<view class="p-empty-state" style="{{(containerStyle)}}"><block wx:if="{{useIcon}}"><view class="p-empty-state__icon-wrapper"><uni-icons vue-id="5cacfd98-1" type="{{iconName}}" size="{{iconSize}}" color="{{iconColor}}" bind:__l="__l"></uni-icons></view></block><block wx:else><image class="p-empty-state__icon" style="{{(iconStyle)}}" src="{{icon||defaultIcon}}" mode="aspectFit"></image></block><text class="p-empty-state__text" style="{{'color:'+(textColor)+';'}}">{{text||'暂无数据'}}</text><block wx:if="{{description}}"><text class="p-empty-state__desc" style="{{'color:'+(descColor)+';'}}">{{description}}</text></block><block wx:if="{{showAction}}"><button data-event-opts="{{[['tap',[['$emit',['action']]]]]}}" class="p-empty-state__action" bindtap="__e">{{actionText}}</button></block></view>