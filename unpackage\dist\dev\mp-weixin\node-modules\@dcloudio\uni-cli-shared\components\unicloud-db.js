(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["node-modules/@dcloudio/uni-cli-shared/components/unicloud-db"],{

/***/ 883:
/*!**************************************************************************!*\
  !*** ./node_modules/@dcloudio/uni-cli-shared/components/unicloud-db.vue ***!
  \**************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _unicloud_db_vue_vue_type_template_id_882a8854___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./unicloud-db.vue?vue&type=template&id=882a8854& */ 884);
/* harmony import */ var _unicloud_db_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./unicloud-db.vue?vue&type=script&lang=js& */ 886);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _unicloud_db_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _unicloud_db_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs




/* normalize component */

var component = Object(_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _unicloud_db_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _unicloud_db_vue_vue_type_template_id_882a8854___WEBPACK_IMPORTED_MODULE_0__["render"],
  _unicloud_db_vue_vue_type_template_id_882a8854___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _unicloud_db_vue_vue_type_template_id_882a8854___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "E:/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/uni-cli-shared/components/unicloud-db.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 884:
/*!*********************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/uni-cli-shared/components/unicloud-db.vue?vue&type=template&id=882a8854& ***!
  \*********************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_webpack_uni_mp_loader_lib_template_js_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_webpack_uni_mp_loader_lib_style_js_unicloud_db_vue_vue_type_template_id_882a8854___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../webpack-uni-mp-loader/lib/template.js!../../vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../webpack-uni-mp-loader/lib/style.js!./unicloud-db.vue?vue&type=template&id=882a8854& */ 885);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_webpack_uni_mp_loader_lib_template_js_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_webpack_uni_mp_loader_lib_style_js_unicloud_db_vue_vue_type_template_id_882a8854___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_webpack_uni_mp_loader_lib_template_js_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_webpack_uni_mp_loader_lib_style_js_unicloud_db_vue_vue_type_template_id_882a8854___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_webpack_uni_mp_loader_lib_template_js_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_webpack_uni_mp_loader_lib_style_js_unicloud_db_vue_vue_type_template_id_882a8854___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_webpack_uni_mp_loader_lib_template_js_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_webpack_uni_mp_loader_lib_style_js_unicloud_db_vue_vue_type_template_id_882a8854___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 885:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./node_modules/@dcloudio/uni-cli-shared/components/unicloud-db.vue?vue&type=template&id=882a8854& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  _vm.$initSSP()
  if (_vm.$scope.data.scopedSlotsCompiler === "augmented") {
    _vm.$setSSP("default", {
      options: _vm.options,
      data: _vm.dataList,
      pagination: _vm.paginationInternal,
      loading: _vm.loading,
      hasMore: _vm.hasMore,
      error: _vm.errorMessage,
    })
  }
  _vm.$callSSP()
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 886:
/*!***************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/uni-cli-shared/components/unicloud-db.vue?vue&type=script&lang=js& ***!
  \***************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_loader_lib_index_js_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_webpack_uni_mp_loader_lib_script_js_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_webpack_uni_mp_loader_lib_style_js_unicloud_db_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../babel-loader/lib!../../vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../webpack-uni-mp-loader/lib/script.js!../../vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../webpack-uni-mp-loader/lib/style.js!./unicloud-db.vue?vue&type=script&lang=js& */ 887);
/* harmony import */ var _babel_loader_lib_index_js_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_webpack_uni_mp_loader_lib_script_js_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_webpack_uni_mp_loader_lib_style_js_unicloud_db_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_loader_lib_index_js_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_webpack_uni_mp_loader_lib_script_js_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_webpack_uni_mp_loader_lib_style_js_unicloud_db_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _babel_loader_lib_index_js_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_webpack_uni_mp_loader_lib_script_js_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_webpack_uni_mp_loader_lib_style_js_unicloud_db_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _babel_loader_lib_index_js_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_webpack_uni_mp_loader_lib_script_js_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_webpack_uni_mp_loader_lib_style_js_unicloud_db_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_babel_loader_lib_index_js_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_webpack_uni_mp_loader_lib_script_js_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_webpack_uni_mp_loader_lib_style_js_unicloud_db_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 887:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./node_modules/@dcloudio/uni-cli-shared/components/unicloud-db.vue?vue&type=script&lang=js& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni, uniCloud) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
var _uniI18n = __webpack_require__(/*! @dcloudio/uni-i18n */ 22);
var _index = _interopRequireDefault(__webpack_require__(/*! ./i18n/index */ 888));
//
//
//
//
//
//
//

var _initVueI18n = (0, _uniI18n.initVueI18n)(_index.default),
  t = _initVueI18n.t;
var events = {
  load: 'load',
  error: 'error'
};
var pageMode = {
  add: 'add',
  replace: 'replace'
};
var loadMode = {
  auto: 'auto',
  onready: 'onready',
  manual: 'manual'
};
var attrs = ['pageCurrent', 'pageSize', 'spaceInfo', 'collection', 'action', 'field', 'getcount', 'orderby', 'where', 'groupby', 'groupField', 'distinct'];
var _default2 = {
  name: 'UniClouddb',
  props: {
    options: {
      type: [Object, Array],
      default: function _default() {
        return {};
      }
    },
    spaceInfo: {
      type: Object,
      default: function _default() {
        return {};
      }
    },
    collection: {
      type: [String, Array],
      default: ''
    },
    action: {
      type: String,
      default: ''
    },
    field: {
      type: String,
      default: ''
    },
    orderby: {
      type: String,
      default: ''
    },
    where: {
      type: [String, Object],
      default: ''
    },
    pageData: {
      type: String,
      default: 'add'
    },
    pageCurrent: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 20
    },
    getcount: {
      type: [Boolean, String],
      default: false
    },
    getone: {
      type: [Boolean, String],
      default: false
    },
    gettree: {
      type: [Boolean, String, Object],
      default: false
    },
    gettreepath: {
      type: [Boolean, String],
      default: false
    },
    startwith: {
      type: String,
      default: ''
    },
    limitlevel: {
      type: Number,
      default: 10
    },
    groupby: {
      type: String,
      default: ''
    },
    groupField: {
      type: String,
      default: ''
    },
    distinct: {
      type: [Boolean, String],
      default: false
    },
    pageIndistinct: {
      type: [Boolean, String],
      default: false
    },
    foreignKey: {
      type: String,
      default: ''
    },
    loadtime: {
      type: String,
      default: 'auto'
    },
    manual: {
      type: Boolean,
      default: false
    }
  },
  data: function data() {
    return {
      loading: false,
      hasMore: false,
      dataList: this.getone ? undefined : [],
      paginationInternal: {},
      errorMessage: ''
    };
  },
  computed: {
    collectionArgs: function collectionArgs() {
      return Array.isArray(this.collection) ? this.collection : [this.collection];
    },
    isLookup: function isLookup() {
      return Array.isArray(this.collection) && this.collection.length > 1 || typeof this.collection === 'string' && this.collection.indexOf(',') > -1;
    }
  },
  created: function created() {
    var _this = this;
    this._isEnded = false;
    this.paginationInternal = {
      current: this.pageCurrent,
      size: this.pageSize,
      count: 0
    };
    this.$watch(function () {
      var al = [];
      attrs.forEach(function (key) {
        al.push(_this[key]);
      });
      return al;
    }, function (newValue, oldValue) {
      _this.paginationInternal.size = _this.pageSize;
      if (newValue[0] !== oldValue[0]) {
        _this.paginationInternal.current = _this.pageCurrent;
      }
      if (_this.loadtime === loadMode.manual) {
        return;
      }
      var needReset = false;
      for (var i = 2; i < newValue.length; i++) {
        if (newValue[i] !== oldValue[i]) {
          needReset = true;
          break;
        }
      }
      if (needReset) {
        _this.clear();
        _this.reset();
      }
      _this._execLoadData();
    });
    if (!this.manual && this.loadtime === loadMode.auto) {
      this.loadData();
    }
  },
  methods: {
    loadData: function loadData(args1, args2) {
      var callback = null;
      var clear = false;
      if ((0, _typeof2.default)(args1) === 'object') {
        if (args1.clear) {
          if (this.pageData === pageMode.replace) {
            this.clear();
          } else {
            clear = args1.clear;
          }
          this.reset();
        }
        if (args1.current !== undefined) {
          this.paginationInternal.current = args1.current;
        }
        if (typeof args2 === 'function') {
          callback = args2;
        }
      } else if (typeof args1 === 'function') {
        callback = args1;
      }
      this._execLoadData(callback, clear);
    },
    loadMore: function loadMore() {
      if (this._isEnded || this.loading) {
        return;
      }
      if (this.pageData === pageMode.add) {
        this.paginationInternal.current++;
      }
      this._execLoadData();
    },
    refresh: function refresh() {
      this.clear();
      this._execLoadData();
    },
    clear: function clear() {
      this._isEnded = false;
      this.dataList = [];
    },
    reset: function reset() {
      this.paginationInternal.current = 1;
    },
    add: function add(value) {
      var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},
        action = _ref.action,
        _ref$showToast = _ref.showToast,
        showToast = _ref$showToast === void 0 ? true : _ref$showToast,
        toastTitle = _ref.toastTitle,
        success = _ref.success,
        fail = _ref.fail,
        complete = _ref.complete,
        _ref$needConfirm = _ref.needConfirm,
        needConfirm = _ref$needConfirm === void 0 ? true : _ref$needConfirm,
        _ref$needLoading = _ref.needLoading,
        needLoading = _ref$needLoading === void 0 ? true : _ref$needLoading,
        _ref$loadingTitle = _ref.loadingTitle,
        loadingTitle = _ref$loadingTitle === void 0 ? '' : _ref$loadingTitle;
      if (needLoading) {
        uni.showLoading({
          title: loadingTitle
        });
      }
      /* eslint-disable no-undef */
      var db = uniCloud.database(this.spaceInfo);
      if (action) {
        db = db.action(action);
      }
      db.collection(this.getMainCollection()).add(value).then(function (res) {
        success && success(res);
        if (showToast) {
          uni.showToast({
            title: toastTitle || t('uniCloud.component.add.success')
          });
        }
      }).catch(function (err) {
        fail && fail(err);
        if (needConfirm) {
          uni.showModal({
            content: err.message,
            showCancel: false
          });
        }
      }).finally(function () {
        if (needLoading) {
          uni.hideLoading();
        }
        complete && complete();
      });
    },
    remove: function remove(id) {
      var _this2 = this;
      var _ref2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},
        action = _ref2.action,
        _success = _ref2.success,
        fail = _ref2.fail,
        complete = _ref2.complete,
        confirmTitle = _ref2.confirmTitle,
        confirmContent = _ref2.confirmContent,
        _ref2$needConfirm = _ref2.needConfirm,
        needConfirm = _ref2$needConfirm === void 0 ? true : _ref2$needConfirm,
        _ref2$needLoading = _ref2.needLoading,
        needLoading = _ref2$needLoading === void 0 ? true : _ref2$needLoading,
        _ref2$loadingTitle = _ref2.loadingTitle,
        loadingTitle = _ref2$loadingTitle === void 0 ? '' : _ref2$loadingTitle;
      if (!id || !id.length) {
        return;
      }
      if (!needConfirm) {
        this._execRemove(id, action, _success, fail, complete, needConfirm, needLoading, loadingTitle);
        return;
      }
      uni.showModal({
        title: confirmTitle || t('uniCloud.component.remove.showModal.title'),
        content: confirmContent || t('uniCloud.component.remove.showModal.content'),
        showCancel: true,
        success: function success(res) {
          if (!res.confirm) {
            return;
          }
          _this2._execRemove(id, action, _success, fail, complete, needConfirm, needLoading, loadingTitle);
        }
      });
    },
    update: function update(id, value) {
      var _this3 = this;
      var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      var fixOptions = Object.assign({
        action: null,
        showToast: true,
        toastTitle: null,
        success: null,
        fail: null,
        complete: null,
        confirmTitle: null,
        confirmContent: null,
        needConfirm: true,
        needLoading: true,
        loadingTitle: ''
      }, options);
      if (options.needConfirm) {
        uni.showModal({
          title: options.confirmTitle || t('uniCloud.component.update.showModal.title'),
          content: options.confirmContent || t('uniCloud.component.update.showModal.content'),
          showCancel: true,
          success: function success(res) {
            if (res.confirm) {
              _this3._doUpdate(id, value, fixOptions);
            }
          }
        });
      } else {
        this._doUpdate(id, value, fixOptions);
      }
    },
    _doUpdate: function _doUpdate(id, value, options) {
      var action = options.action,
        success = options.success,
        fail = options.fail,
        complete = options.complete,
        showToast = options.showToast,
        toastTitle = options.toastTitle,
        needConfirm = options.needConfirm,
        needLoading = options.needLoading,
        loadingTitle = options.loadingTitle;
      if (needLoading) {
        uni.showLoading({
          title: loadingTitle
        });
      }
      /* eslint-disable no-undef */
      var db = uniCloud.database(this.spaceInfo);
      if (action) {
        db = db.action(action);
      }
      db.collection(this.getMainCollection()).doc(id).update(value).then(function (res) {
        success && success(res);
        if (showToast) {
          uni.showToast({
            title: toastTitle || t('uniCloud.component.update.success')
          });
        }
      }).catch(function (err) {
        fail && fail(err);
        if (needConfirm) {
          uni.showModal({
            content: err.message,
            showCancel: false
          });
        }
      }).finally(function () {
        if (needLoading) {
          uni.hideLoading();
        }
        complete && complete();
      });
    },
    getMainCollection: function getMainCollection() {
      if (typeof this.collection === 'string') {
        return this.collection.split(',')[0];
      }
      var mainQuery = JSON.parse(JSON.stringify(this.collection[0]));
      return mainQuery.$db[0].$param[0];
    },
    getTemp: function getTemp() {
      var _db;
      var isTemp = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;
      /* eslint-disable no-undef */
      var db = uniCloud.database(this.spaceInfo);
      if (this.action) {
        db = db.action(this.action);
      }
      db = (_db = db).collection.apply(_db, (0, _toConsumableArray2.default)(this.collectionArgs));
      if (this.foreignKey) {
        db = db.foreignKey(this.foreignKey);
      }
      if (!(!this.where || !Object.keys(this.where).length)) {
        db = db.where(this.where);
      }
      if (this.field) {
        db = db.field(this.field);
      }
      if (this.groupby) {
        db = db.groupBy(this.groupby);
      }
      if (this.groupField) {
        db = db.groupField(this.groupField);
      }
      if (this.distinct === true) {
        db = db.distinct();
      }
      if (this.orderby) {
        db = db.orderBy(this.orderby);
      }
      var _this$paginationInter = this.paginationInternal,
        current = _this$paginationInter.current,
        size = _this$paginationInter.size;
      var getOptions = {};
      if (this.getcount) {
        getOptions.getCount = this.getcount;
      }
      var treeOptions = {
        limitLevel: this.limitlevel,
        startWith: this.startwith
      };
      if (this.gettree) {
        getOptions.getTree = treeOptions;
      }
      if (this.gettreepath) {
        getOptions.getTreePath = treeOptions;
      }
      db = db.skip(size * (current - 1)).limit(size);
      if (isTemp) {
        db = db.getTemp(getOptions);
        db.udb = this;
      } else {
        db = db.get(getOptions);
      }
      return db;
    },
    setResult: function setResult(result) {
      if (result.code === 0) {
        this._execLoadDataSuccess(result);
      } else {
        this._execLoadDataFail(new Error(result.message));
      }
    },
    _execLoadData: function _execLoadData(callback, clear) {
      var _this4 = this;
      if (this.loading) {
        return;
      }
      this.loading = true;
      this.errorMessage = '';
      this._getExec().then(function (res) {
        _this4.loading = false;
        _this4._execLoadDataSuccess(res.result, callback, clear);
      }).catch(function (err) {
        _this4.loading = false;
        _this4._execLoadDataFail(err, callback);
      });
    },
    _execLoadDataSuccess: function _execLoadDataSuccess(result, callback, clear) {
      var data = result.data,
        count = result.count;
      this._isEnded = count !== undefined ? this.paginationInternal.current * this.paginationInternal.size >= count : data.length < this.pageSize;
      this.hasMore = !this._isEnded;
      var data2 = this.getone ? data.length ? data[0] : undefined : data;
      if (this.getcount) {
        this.paginationInternal.count = count;
      }
      callback && callback(data2, this._isEnded, this.paginationInternal);
      this._dispatchEvent(events.load, data2);
      if (this.getone || this.pageData === pageMode.replace) {
        this.dataList = data2;
      } else {
        if (clear) {
          this.dataList = data2;
        } else {
          var _this$dataList;
          (_this$dataList = this.dataList).push.apply(_this$dataList, (0, _toConsumableArray2.default)(data2));
        }
      }
    },
    _execLoadDataFail: function _execLoadDataFail(err, callback) {
      this.errorMessage = err;
      callback && callback();
      this.$emit(events.error, err);
      if (true) {
        console.error(err);
      }
    },
    _getExec: function _getExec() {
      return this.getTemp(false);
    },
    _execRemove: function _execRemove(id, action, success, fail, complete, needConfirm, needLoading, loadingTitle) {
      var _this5 = this;
      if (!this.collection || !id) {
        return;
      }
      var ids = Array.isArray(id) ? id : [id];
      if (!ids.length) {
        return;
      }
      if (needLoading) {
        uni.showLoading({
          mask: true,
          title: loadingTitle
        });
      }

      /* eslint-disable no-undef */
      var db = uniCloud.database(this.spaceInfo);
      var dbCmd = db.command;
      var exec = db;
      if (action) {
        exec = exec.action(action);
      }
      exec.collection(this.getMainCollection()).where({
        _id: dbCmd.in(ids)
      }).remove().then(function (res) {
        success && success(res.result);
        if (_this5.pageData === pageMode.replace) {
          _this5.refresh();
        } else {
          _this5.removeData(ids);
        }
      }).catch(function (err) {
        fail && fail(err);
        if (needConfirm) {
          uni.showModal({
            content: err.message,
            showCancel: false
          });
        }
      }).finally(function () {
        if (needLoading) {
          uni.hideLoading();
        }
        complete && complete();
      });
    },
    removeData: function removeData(ids) {
      var il = ids.slice(0);
      var dl = this.dataList;
      for (var i = dl.length - 1; i >= 0; i--) {
        var index = il.indexOf(dl[i]._id);
        if (index >= 0) {
          dl.splice(i, 1);
          il.splice(index, 1);
        }
      }
    },
    _dispatchEvent: function _dispatchEvent(type, data) {
      if (this._changeDataFunction) {
        this._changeDataFunction(data, this._isEnded, this.paginationInternal);
      } else {
        this.$emit(type, data, this._isEnded, this.paginationInternal);
      }
    }
  }
};
exports.default = _default2;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27)["uniCloud"]))

/***/ })

}]);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/node-modules/@dcloudio/uni-cli-shared/components/unicloud-db.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'node-modules/@dcloudio/uni-cli-shared/components/unicloud-db-create-component',
    {
        'node-modules/@dcloudio/uni-cli-shared/components/unicloud-db-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(883))
        })
    },
    [['node-modules/@dcloudio/uni-cli-shared/components/unicloud-db-create-component']]
]);
