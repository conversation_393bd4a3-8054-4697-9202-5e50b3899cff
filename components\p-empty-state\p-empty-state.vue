<template>
  <view class="p-empty-state" :style="containerStyle">
    <!-- 图标模式 -->
    <view v-if="useIcon" class="p-empty-state__icon-wrapper">
      <uni-icons :type="iconName" :size="iconSize" :color="iconColor" />
    </view>
    
    <!-- 图片模式 -->
    <image v-else :src="icon || defaultIcon" class="p-empty-state__icon" :style="iconStyle" mode="aspectFit"></image>
    
    <text class="p-empty-state__text" :style="{ color: textColor }">{{ text || '暂无数据' }}</text>
    <text v-if="description" class="p-empty-state__desc" :style="{ color: descColor }">{{ description }}</text>
    <button v-if="showAction" class="p-empty-state__action" @click="$emit('action')">{{ actionText }}</button>
  </view>
</template>

<script>
export default {
  name: 'p-empty-state',
  props: {
    /**
     * 图标地址
     */
    icon: {
      type: String,
      default: ''
    },
    /**
     * 提示文本
     */
    text: {
      type: String,
      default: '暂无数据'
    },
    /**
     * 描述文本
     */
    description: {
      type: String,
      default: ''
    },
    /**
     * 图标类型
     */
    type: {
      type: String,
      default: 'default' // default, task, record, search, data, notification, todo, assignment, area, schedule
    },
    /**
     * 图标尺寸
     */
    size: {
      type: String,
      default: 'medium' // small, medium, large
    },
    /**
     * 文字颜色
     */
    textColor: {
      type: String,
      default: '#999'
    },
    /**
     * 描述文字颜色
     */
    descColor: {
      type: String,
      default: '#B0B0B0'
    },
    /**
     * 容器样式
     */
    containerStyle: {
      type: Object,
      default: () => ({})
    },
    /**
     * 是否显示操作按钮
     */
    showAction: {
      type: Boolean,
      default: false
    },
    /**
     * 操作按钮文本
     */
    actionText: {
      type: String,
      default: '点击操作'
    },
    /**
     * 是否使用图标模式（而不是图片）
     */
    useIcon: {
      type: Boolean,
      default: false
    },
    /**
     * 图标名称（当useIcon为true时使用）
     */
    iconName: {
      type: String,
      default: 'info'
    },
    /**
     * 图标颜色
     */
    iconColor: {
      type: String,
      default: '#8E8E93'
    }
  },
  computed: {
    defaultIcon() {
      const iconMap = {
        'default': '/static/empty/empty.png',
        'task': '/static/empty/empty_task.png',
        'record': '/static/empty/empty_record.png',
        'search': '/static/empty/empty-search.png',
        'data': '/static/empty/empty_data.png',
        'todo': '/static/empty/empty_todo.png',
        'assignment': '/static/empty/empty_data.png',
        'area': '/static/empty/empty_data.png',
        'schedule': '/static/empty/empty_data.png'
      };
      return iconMap[this.type] || iconMap.default;
    },
    iconStyle() {
      const sizeMap = {
        'small': '80rpx',
        'medium': '120rpx',
        'large': '180rpx'
      };
      const size = sizeMap[this.size] || sizeMap.medium;
      return {
        width: size,
        height: size
      };
    },
    iconSize() {
      const sizeMap = {
        'small': 20,
        'medium': 24,
        'large': 32
      };
      return sizeMap[this.size] || sizeMap.medium;
    }
  }
}
</script>

<style lang="scss">
.p-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  box-sizing: border-box;
  
  &__icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20rpx;
  }
  
  &__icon {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 20rpx;
  }
  
  &__text {
    font-size: 28rpx;
    color: #999;
    text-align: center;
    margin-bottom: 12rpx;
  }
  
  &__desc {
    font-size: 24rpx;
    color: #B0B0B0;
    text-align: center;
    margin-bottom: 20rpx;
    line-height: 1.4;
  }
  
  &__action {
    margin-top: 20rpx;
    background-color: #1677FF;
    color: white;
    font-size: 28rpx;
    border-radius: 40rpx;
    padding: 10rpx 30rpx;
    border: none;
    
    &:active {
      opacity: 0.8;
    }
  }
}
</style> 