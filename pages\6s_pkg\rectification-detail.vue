<template>
  <view class="page-container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载整改任务详情中...</text>
      </view>
    </view>
    
    <!-- 错误状态 -->
    <view v-else-if="loadError" class="error-container">
      <view class="error-content">
        <uni-icons type="info" size="48" color="#FF3B30"></uni-icons>
        <text class="error-text">{{ loadError }}</text>
        <button class="retry-button" @click="retryLoad">重新加载</button>
      </view>
    </view>
    
    <!-- 正常内容 -->
    <template v-else>
      <!-- 1. 整改任务基本信息 -->
      <view class="card">
        <view class="card-header">
          <view class="header-content">
            <view class="card-title">整改任务详情</view>
            <view class="card-subtitle">{{ taskInfo.area }} - {{ taskInfo.isPublic ? '公共责任区' : '固定责任区' }}</view>
          </view>
        </view>
        <view class="card-body">
          <view class="task-info">
            <view class="info-item">
              <text class="info-label">状态：</text>
              <view class="status-badge" :class="['status-' + taskInfo.status]">{{ computedData.statusText }}</view>
            </view>
            <view class="info-item">
              <text class="info-label">问题类别：</text>
              <text class="info-value">{{ computedData.categoryText }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">问题描述：</text>
              <text class="info-value">{{ taskInfo.problemDescription }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">发现时间：</text>
              <text class="info-value">{{ formatDateTimeOptimized(taskInfo.issueFoundDate) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">检查员：</text>
              <text class="info-value">{{ taskInfo.inspector }}</text>
            </view>
            <view v-if="taskInfo.assignee_name" class="info-item">
              <text class="info-label">负责人：</text>
              <text class="info-value">{{ taskInfo.assignee_name }}</text>
            </view>
            <!-- 根据任务状态显示不同的时间信息 -->
            <view v-if="computedData.isCompleted && taskInfo.completedDate" class="info-item">
              <text class="info-label">完成时间：</text>
              <text class="info-value">{{ formatDateTimeOptimized(taskInfo.completedDate) }}</text>
            </view>
            <view v-else-if="!computedData.isCompleted && taskInfo.deadline" class="info-item">
              <text class="info-label">截止时间：</text>
              <text class="info-value">{{ formatDateTimeOptimized(taskInfo.deadline) }}</text>
            </view>
            <!-- 只在进行中的任务显示开始时间 -->
            <view v-if="taskInfo.status === 'in_progress' && taskInfo.startDate" class="info-item">
              <text class="info-label">开始时间：</text>
              <text class="info-value">{{ formatDateTimeOptimized(taskInfo.startDate) }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 2. 原始问题照片 - 紧跟问题描述后显示 -->
      <view v-if="taskInfo.photos && taskInfo.photos.length > 0" class="card">
        <view class="card-header">
          <view class="card-title">问题照片</view>
          <text class="photo-count">{{ taskInfo.photos.length }}张</text>
        </view>
        <view class="card-body">
          <view class="photo-grid">
            <view v-for="(photo, index) in taskInfo.photos" :key="index" class="photo-item" @click="previewPhoto(index, 'problem')">
              <image :src="photo.url || photo" mode="aspectFill"></image>
              <view class="photo-label">问题</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 3. 整改说明 - 整改过程的说明 -->
      <view v-if="taskInfo.rectificationDescription" class="card">
        <view class="card-header">
          <view class="card-title">整改说明</view>
        </view>
        <view class="card-body">
          <text class="description-text">{{ taskInfo.rectificationDescription }}</text>
        </view>
      </view>

      <!-- 4. 整改完成照片 - 整改结果展示 -->
      <view v-if="taskInfo.completionPhotos && taskInfo.completionPhotos.length > 0" class="card">
        <view class="card-header">
          <view class="card-title">整改后照片</view>
          <text class="photo-count">{{ taskInfo.completionPhotos.length }}张</text>
        </view>
        <view class="card-body">
          <view class="photo-grid">
            <view v-for="(photo, index) in taskInfo.completionPhotos" :key="index" class="photo-item" @click="previewPhoto(index, 'completion')">
              <image :src="photo.url || photo" mode="aspectFill"></image>
              <view class="photo-label completion">整改后</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 5. 复查信息 - 最后的审核结果 -->
      <view v-if="taskInfo.reviewResult || taskInfo.reviewComments" class="card">
        <view class="card-header">
          <view class="card-title">复查信息</view>
        </view>
        <view class="card-body">
          <view class="review-info">
            <view v-if="taskInfo.reviewResult" class="info-item">
              <text class="info-label">复查结果：</text>
              <view class="review-result-badge" :class="['review-' + taskInfo.reviewResult]">{{ computedData.reviewResultText }}</view>
            </view>
            <view v-if="taskInfo.reviewerName" class="info-item">
              <text class="info-label">复查人：</text>
              <text class="info-value">{{ taskInfo.reviewerName }}</text>
            </view>
            <view v-if="taskInfo.reviewDate" class="info-item">
              <text class="info-label">复查时间：</text>
              <text class="info-value">{{ formatDateTimeOptimized(taskInfo.reviewDate) }}</text>
            </view>
            <view v-if="taskInfo.reviewComments" class="info-item">
              <text class="info-label">复查意见：</text>
              <text class="info-value">{{ taskInfo.reviewComments }}</text>
            </view>
          </view>
          
          <!-- 复查照片 -->
          <view v-if="taskInfo.reviewPhotos && taskInfo.reviewPhotos.length > 0" class="review-photos-section">
            <view class="review-photos-header">
              <text class="review-photos-title">复查照片</text>
              <text class="photo-count">{{ taskInfo.reviewPhotos.length }}张</text>
            </view>
            <view class="photo-grid">
              <view v-for="(photo, index) in taskInfo.reviewPhotos" :key="index" class="photo-item" @click="previewPhoto(index, 'review')">
                <image :src="photo.url || photo" mode="aspectFill"></image>
                <view class="photo-label review">复查</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </template>
    
    <view class="button-container">
      <button class="primary-button" @click="goBack">返回</button>
    </view>
  </view>
</template>

<script>
import { callCloudFunction } from '@/utils/auth.js';

export default {
  name: 'RectificationDetail',
  data() {
    return {
      taskInfo: {},
      loading: false,
      loadError: '',
      taskId: '',
      
      // 性能优化缓存
      processCache: null, // 数据处理缓存
      dataLoaded: false, // 数据是否已加载
      
      // 预计算的状态信息
      computedData: {
        isCompleted: false,
        statusText: '',
        categoryText: '',
        reviewResultText: ''
      }
    }
  },
  onLoad(options) {
    this.taskId = options.taskId;
    this.loadTaskDetail(options.taskId);
    
    // 监听整改记录更新事件
    uni.$on('rectificationRecordUpdated', this.handleRecordUpdated);
  },
  onUnload() {
    // 移除事件监听
    uni.$off('rectificationRecordUpdated', this.handleRecordUpdated);
  },
  
  methods: {
    // 初始化数据处理缓存
    initProcessCache() {
      if (!this.processCache) {
        this.processCache = {
          // 状态映射缓存
          statusMap: {
            'pending': '待整改',
            'pending_rectification': '待整改',
            'pending_review': '待复查',
            'completed': '已完成',
            'overdue': '已逾期',
            'in_progress': '整改中',
            'rejected': '整改不达标',
            'verified': '整改合格'
          },
          // 类别映射缓存
          categoryMap: {
            'cleanliness': '清洁问题',
            'safety': '安全问题', 
            'equipment': '设备问题',
            'environment': '环境问题',
            'organization': '整理问题',
            'standardization': '标识问题',
            'other': '其他问题'
          },
          // 审核结果映射缓存
          reviewResultMap: {
            'approved': '复查通过',
            'rejected': '需重新整改',
            'needs_improvement': '需改进'
          },
          // 已完成状态列表
          completedStatuses: ['completed', 'verified', 'approved'],
          // 日期格式化器
          dateFormatter: this.createOptimizedDateFormatter()
        };
      }
    },

    // 创建优化的日期格式化器
    createOptimizedDateFormatter() {
      return (dateString) => {
        if (!dateString) return '--';
        
        try {
          let date;
          if (typeof dateString === 'string') {
            if (dateString.includes('T') || dateString.includes('Z')) {
              date = new Date(dateString);
            } else {
              date = new Date(dateString.replace(/-/g, '/'));
            }
          } else {
            date = new Date(dateString);
          }
          
          if (isNaN(date.getTime())) {
            return '--';
          }
          
          return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
        } catch (error) {
          return '--';
        }
      };
    },

    // 优化的任务详情加载
    async loadTaskDetailOptimized(taskId) {
      if (!taskId) {
        this.loadError = '整改任务ID不能为空';
        return;
      }

      this.loading = true;
      this.loadError = '';

      try {
        // 初始化缓存
        this.initProcessCache();

        const result = await callCloudFunction('hygiene-rectification', {
          action: 'getRectificationDetail',
          data: { id: taskId }
        });

        if (result && result.success && result.data) {
          const task = result.data;
          
          // 使用缓存快速处理数据
          this.taskInfo = this.processTaskDataOptimized(task);
          
          // 预计算状态信息
          this.precomputeTaskStates();
          
          this.dataLoaded = true;
        } else {
          throw new Error(result?.message || '获取整改任务详情失败');
        }
      } catch (error) {

        this.loadError = error.message || '加载失败，请稍后重试';
      } finally {
        this.loading = false;
      }
    },

    // 优化的任务数据处理
    processTaskDataOptimized(task) {
      const formatter = this.processCache.dateFormatter;
      
      return {
        id: task._id || task.id,
        area: task.area_name || '未知责任区',
        areaName: task.area_name || '未知责任区',
        isPublic: task.area_type === 'public',
        status: task.status || 'pending',
        problemDescription: task.issue_description || task.description || '无问题描述',
        issueFoundDate: task.created_at || task.issue_found_date,
        inspector: task.inspector_name || (task.issue && task.issue.inspector_name) || task.created_by_name || task.assigned_by_name || '未知检查员',
        rectificationDescription: task.rectification_description || task.completion_description || '',
        photos: this.processPhotos(task.photos || []),
        completionPhotos: this.processPhotos(task.completion_photos || []),
        completedDate: task.completed_at,
        rectificationSubmitTime: task.submitted_at || task.updated_at,
        assignee_name: task.assigned_to_name || task.cleaner_name || task.area_assignee_name || '未分配',
        category: task.category || '',
        deadline: task.deadline,
        startDate: task.start_date,
        reviewResult: task.review_result || '',
        reviewComments: task.review_comments || '',
        reviewDate: task.review_date,
        reviewerName: task.reviewer_name || '',
        reviewPhotos: this.processPhotos(task.review_photos || [])
      };
    },

    // 优化的照片处理
    processPhotos(photos) {
      if (!Array.isArray(photos)) return [];
      
      return photos.map(photo => {
        if (typeof photo === 'string') {
          return { url: photo };
        } else if (photo && typeof photo === 'object') {
          return { url: photo.url || photo };
        }
        return { url: '' };
      }).filter(photo => photo.url);
    },

    // 预计算任务状态
    precomputeTaskStates() {
      const cache = this.processCache;
      
      this.computedData = {
        isCompleted: cache.completedStatuses.includes(this.taskInfo.status),
        statusText: cache.statusMap[this.taskInfo.status] || '未知状态',
        categoryText: cache.categoryMap[this.taskInfo.category] || this.taskInfo.category || '其他',
        reviewResultText: cache.reviewResultMap[this.taskInfo.reviewResult] || this.taskInfo.reviewResult
      };
    },

    // 根据taskId加载任务详情 - 保留原方法作为备用
    async loadTaskDetail(taskId) {
      // 使用优化版本
      return this.loadTaskDetailOptimized(taskId);
    },

    // 根据taskId加载任务详情 - 原始版本（备用）
    async loadTaskDetailOriginal(taskId) {
      if (!taskId) {
        this.loadError = '整改任务ID不能为空';
        return;
      }

      this.loading = true;
      this.loadError = '';

      try {
        const result = await callCloudFunction('hygiene-rectification', {
          action: 'getRectificationDetail',
          data: { id: taskId }
        });

        if (result && result.success && result.data) {
          const task = result.data;
          
          this.taskInfo = {
            id: task._id || task.id,
            area: task.area_name || '未知责任区',
            areaName: task.area_name || '未知责任区',
            isPublic: task.area_type === 'public',
            status: task.status || 'pending',
            problemDescription: task.issue_description || task.description || '无问题描述',
            issueFoundDate: task.created_at || task.issue_found_date,
            inspector: task.inspector_name || (task.issue && task.issue.inspector_name) || task.created_by_name || task.assigned_by_name || '未知检查员',
            rectificationDescription: task.rectification_description || task.completion_description || '',
            photos: task.photos || [], // 原始问题照片
            completionPhotos: task.completion_photos || [], // 整改完成照片
            completedDate: task.completed_at,
            rectificationSubmitTime: task.submitted_at || task.updated_at,
            assignee_name: task.assigned_to_name || task.cleaner_name || task.area_assignee_name || '未分配',
            // 新增字段
            category: task.category || '',
            deadline: task.deadline,
            startDate: task.start_date,
            // 审核信息
            reviewResult: task.review_result || '',
            reviewComments: task.review_comments || '',
            reviewDate: task.review_date,
            reviewerName: task.reviewer_name || ''
          };
        } else {
          throw new Error(result?.message || '获取整改任务详情失败');
        }
      } catch (error) {

        this.loadError = error.message || '加载失败，请稍后重试';
      } finally {
        this.loading = false;
      }
    },

    // 重新加载
    retryLoad() {
      this.loadTaskDetail(this.taskId);
    },

    // 优化的日期时间格式化
    formatDateTimeOptimized(dateString) {
      if (!this.processCache || !this.processCache.dateFormatter) {
        // 如果缓存未初始化，回退到原方法
        return this.formatDateTime(dateString);
      }
      return this.processCache.dateFormatter(dateString);
    },

    // 格式化日期时间 - 保留原方法作为备用
    formatDateTime(dateString) {
      if (!dateString) return '--';
      
      try {
        let date;
        if (typeof dateString === 'string') {
          if (dateString.includes('T') || dateString.includes('Z')) {
            date = new Date(dateString);
          } else {
            date = new Date(dateString.replace(/-/g, '/'));
          }
        } else {
          date = new Date(dateString);
        }
        
        if (isNaN(date.getTime())) {
          return '--';
        }
        
        return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      } catch (error) {

        return '--';
      }
    },

    // 获取状态文本 - 保留用于向后兼容
    getStatusText(status) {
      if (this.processCache && this.processCache.statusMap) {
        return this.processCache.statusMap[status] || '未知状态';
      }
      // 降级处理
      const statusMap = {
        'pending': '待整改',
        'pending_rectification': '待整改',
        'pending_review': '待复查',
        'completed': '已完成',
        'overdue': '已逾期',
        'in_progress': '整改中',
        'rejected': '整改不达标',
        'verified': '整改合格'
      };
      return statusMap[status] || '未知状态';
    },

    // 获取类别文本 - 保留用于向后兼容
    getCategoryText(category) {
      if (this.processCache && this.processCache.categoryMap) {
        return this.processCache.categoryMap[category] || category || '其他';
      }
      // 降级处理
      const categoryMap = {
        'cleanliness': '清洁问题',
        'safety': '安全问题', 
        'equipment': '设备问题',
        'environment': '环境问题',
        'organization': '整理问题',
        'standardization': '标识问题',
        'other': '其他问题'
      };
      return categoryMap[category] || category || '其他';
    },

    // 判断任务是否已完成 - 保留用于向后兼容
    isTaskCompleted() {
      if (this.computedData && typeof this.computedData.isCompleted === 'boolean') {
        return this.computedData.isCompleted;
      }
      // 降级处理
      const completedStatuses = ['completed', 'verified', 'approved'];
      return completedStatuses.includes(this.taskInfo.status);
    },

    // 获取审核结果文本 - 保留用于向后兼容
    getReviewResultText(result) {
      if (this.processCache && this.processCache.reviewResultMap) {
        return this.processCache.reviewResultMap[result] || result;
      }
      // 降级处理
      const resultMap = {
        'approved': '复查通过',
        'rejected': '需重新整改',
        'needs_improvement': '需改进'
      };
      return resultMap[result] || result;
    },

    // 优化的照片预览
    previewPhoto(index, type = 'problem') {
      let photos, urls;
      
      if (type === 'problem') {
        photos = this.taskInfo.photos || [];
      } else if (type === 'completion') {
        photos = this.taskInfo.completionPhotos || [];
      } else if (type === 'review') {
        photos = this.taskInfo.reviewPhotos || [];
      } else {
        return;
      }
      
      // 使用缓存的URL处理，避免重复map操作
      if (photos._cachedUrls) {
        urls = photos._cachedUrls;
      } else {
        urls = photos.map(photo => photo.url || photo).filter(url => url);
        // 缓存处理结果
        photos._cachedUrls = urls;
      }
      
      if (urls.length === 0) return;
      
      uni.previewImage({
        urls: urls,
        current: Math.min(index, urls.length - 1)
      });
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 处理整改记录更新事件
    handleRecordUpdated(data) {
      // 如果更新的是当前任务，重新加载数据
      if (data.taskId === this.taskId) {
        this.loadTaskDetail(this.taskId);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
  padding: 24rpx;
}

.card {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 32rpx 32rpx 16rpx 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
}

.card-body {
  padding: 32rpx;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-subtitle {
  font-size: 26rpx;
  color: #8E8E93;
}

.photo-count {
  font-size: 24rpx;
  color: #8E8E93;
}

.task-info {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.info-label {
  font-size: 28rpx;
  color: #8E8E93;
  width: 140rpx;
  flex-shrink: 0;
  text-align: left;
}

.info-value {
  font-size: 28rpx;
  color: #1C1C1E;
  flex: 1;
  line-height: 1.4;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 600;
  
  &.status-pending,
  &.status-pending_rectification {
    background: #FFF4E6;
    color: #FF9500;
  }
  
  &.status-pending_review {
    background: #E6F3FF;
    color: #007AFF;
  }
  
  &.status-completed {
    background: #E8F5E8;
    color: #34C759;
  }
  
  &.status-overdue {
    background: #FFE6E6;
    color: #FF3B30;
  }
  
  &.status-in_progress {
    background: #F0F0F5;
    color: #8E8E93;
  }

  &.status-rejected {
    background: #FFE6E6;
    color: #FF3B30;
  }

  &.status-verified {
    background: #E8F5E8;
    color: #34C759;
  }
}





/* 照片标签 */
.photo-label {
  position: absolute;
  bottom: 8rpx;
  left: 8rpx;
  background: rgba(255, 59, 48, 0.8);
  color: white;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
  font-weight: 600;
  
  &.completion {
    background: rgba(52, 199, 89, 0.8);
  }
  
  &.review {
    background: rgba(0, 122, 255, 0.8);
  }
}

.photo-item {
  position: relative;
}

/* 审核信息 */
.review-info {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.review-result-badge {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 600;
  
  &.review-approved {
    background: #E8F5E8;
    color: #34C759;
  }
  
  &.review-rejected {
    background: #FFE6E6;
    color: #FF3B30;
  }
  
  &.review-needs_improvement {
    background: #FFF4E6;
    color: #FF9500;
  }
}

/* 复查照片部分 */
.review-photos-section {
  margin-top: 32rpx;
  padding-top: 32rpx;
  border-top: 1rpx solid #F2F2F7;
}

.review-photos-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.review-photos-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
}

.description-text {
  font-size: 28rpx;
  color: #1C1C1E;
  line-height: 1.6;
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.photo-item {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #F8F9FA;
}

.photo-item image {
  width: 100%;
  height: 100%;
}

.button-container {
  padding: 32rpx 0;
}

.primary-button {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  width: 100%;
}

/* 加载状态 */
.loading-container {
  padding: 120rpx 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #E5E7EB;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
  text-align: center;
}

/* 错误状态 */
.error-container {
  padding: 120rpx 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  text-align: center;
}

.error-text {
  font-size: 28rpx;
  color: #8E8E93;
  line-height: 1.4;
}

.retry-button {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
}
</style>