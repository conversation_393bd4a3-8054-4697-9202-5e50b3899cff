<template>
  <view class="page-container">
    <!-- 顶部统计 -->
    <view class="stats-section">
      <view class="stats-card">
        <view class="stats-item">
          <view class="stats-number">{{ publishedCount }}</view>
          <view class="stats-label">已发布</view>
        </view>
        <view class="stats-item">
          <view class="stats-number">{{ draftCount }}</view>
          <view class="stats-label">草稿</view>
        </view>
        <view class="stats-item">
          <view class="stats-number">{{ totalCount }}</view>
          <view class="stats-label">总计</view>
        </view>
      </view>
    </view>

    <!-- 分类切换 -->
    <view class="tab-section">
      <view class="tab-container">
        <view 
          class="tab-item" 
          :class="{ 'active': currentTab === 'published' }"
          @click="switchTab('published')"
        >
          <text>已发布 ({{ publishedCount }})</text>
        </view>
        <view 
          class="tab-item" 
          :class="{ 'active': currentTab === 'draft' }"
          @click="switchTab('draft')"
        >
          <text>草稿 ({{ draftCount }})</text>
        </view>
      </view>
    </view>

    <!-- 问题列表 -->
    <view class="list-section">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <uni-icons type="spinner-cycle" size="32" color="#007AFF"></uni-icons>
        <text class="loading-text">加载中...</text>
      </view>
      
      <template v-else-if="currentList.length > 0">
        <view 
          v-for="(issue, index) in currentList" 
          :key="index"
          class="issue-item"
          @click="handleItemClick(issue)"
        >
          <view class="issue-header">
            <view class="issue-title-section">
              <view class="issue-number" v-if="issue.number">#{{ getIssueNumber(issue.number) }}</view>
            <view class="issue-title">{{ issue.title }}</view>
            </view>
            <view class="issue-status" :class="'status-' + issue.status">
              {{ getStatusText(issue.status) }}
            </view>
          </view>
          <view class="issue-content">
            <view class="issue-info">
              <view class="info-item">
                <uni-icons type="location" size="14" color="#8E8E93" />
                <text>{{ issue.location || '未设置' }}</text>
              </view>
              <view class="info-item">
                <uni-icons type="person" size="14" color="#8E8E93" />
                <text>{{ issue.responsible || '未指定' }}</text>
              </view>
            </view>
            <view class="issue-time">
              {{ formatTime(issue.createdAt || issue.updatedAt) }}
            </view>
          </view>
          <!-- 操作按钮 -->
          <view v-if="issue.status === 'draft'" class="issue-actions">
            <view class="action-btn edit" @click.stop="editDraft(issue)">
              <uni-icons type="compose" size="14" color="#007AFF" />
              <text>编辑</text>
            </view>
            <view class="action-btn publish" @click.stop="publishDraft(issue)">
              <uni-icons type="upload" size="14" color="#34C759" />
              <text>发布</text>
            </view>
            <view class="action-btn delete" @click.stop="deleteDraft(issue)">
              <uni-icons type="trash" size="14" color="#FF3B30" />
              <text>删除</text>
            </view>
          </view>
          
          <!-- 已发布问题操作按钮 -->
          <view v-else class="issue-actions">
            <view class="action-btn view" @click.stop="viewIssueDetail(issue)">
              <uni-icons type="eye" size="14" color="#007AFF" />
              <text>查看详情</text>
            </view>
            <view class="action-btn edit" @click.stop="editPublishedIssue(issue)" v-if="issue.status !== 'approved'">
              <uni-icons type="compose" size="14" color="#34C759" />
              <text>编辑</text>
            </view>
            <view class="action-btn status" @click.stop="changeIssueStatus(issue)">
              <uni-icons type="loop" size="14" color="#FF9500" />
              <text>{{ issue.status === 'approved' ? '重新打开' : '状态' }}</text>
            </view>
            <!-- 移除关闭按钮，检查通过就是自然结束 -->
          </view>
        </view>
      </template>
      
      <!-- 空状态 -->
      <template v-else>
        <p-empty-state
          type="data"
          :text="currentTab === 'draft' ? '暂无草稿' : '暂无已发布问题'"
          :description="currentTab === 'draft' ? '在问题录入时点击保存草稿即可保存' : '点击右下角按钮新增问题'"
        ></p-empty-state>
      </template>
    </view>

    <!-- 添加按钮 -->
    <view class="fab-container">
      <view class="fab-btn" @click="addIssue">
        <uni-icons type="plus" size="32" color="#ffffff" />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'IssueList',
  data() {
    return {
      currentTab: 'published',
      publishedIssues: [],
      draftIssues: [],
      loading: false,
      dataLoaded: false, // 标记数据是否已加载
      needsRefresh: false // 标记是否需要刷新数据
    }
  },
  computed: {
    currentList() {
      return this.currentTab === 'published' ? this.publishedIssues : this.draftIssues;
    },
    publishedCount() {
      return this.publishedIssues.length;
    },
    draftCount() {
      return this.draftIssues.length;
    },
    totalCount() {
      return this.publishedCount + this.draftCount;
    }
  },
  onLoad() {
    this.loadData();
    
    // 监听问题数据更新事件
    uni.$on('monthlyIssueUpdated', this.handleIssueUpdated);
    uni.$on('issueDraftUpdated', this.handleDraftUpdated);
  },
  onUnload() {
    // 移除事件监听
    uni.$off('monthlyIssueUpdated', this.handleIssueUpdated);
    uni.$off('issueDraftUpdated', this.handleDraftUpdated);
  },
  onShow() {
    // 页面重新显示时，只在确实需要刷新数据的情况下才刷新
    // 使用静默刷新，避免显示加载动画
    if (this.dataLoaded && !this.loading && this.needsRefresh) {
      this.silentRefreshData();
      this.needsRefresh = false; // 重置刷新标记
    }
  },
  methods: {
    // 加载数据
    loadData() {
      this.loadPublishedIssues();
      this.loadDraftIssues();
    },
    
    // 加载已发布问题
    async loadPublishedIssues() {
      try {
        this.loading = true;
        
        const { callCloudFunction } = require('@/utils/auth.js');
        
        // 调用真实API获取问题列表（不限制状态，获取所有数据）
        const result = await callCloudFunction('hygiene-monthly-inspection', {
          action: 'getMonthlyIssues',
          data: {
            pageSize: 50,
            page: 1
          }
        });
        
        if (result && result.success && result.data && result.data.list) {
          this.publishedIssues = result.data.list.map(item => this.formatIssueItem(item));
          
          // 缓存数据
          try {
            uni.setStorageSync('cached_published_issues', this.publishedIssues);
          } catch (cacheError) {
            // 缓存失败，静默处理
          }
        } else {
          this.publishedIssues = [];
        }
        
      } catch (error) {
        
        // 如果API失败，尝试从缓存加载
        try {
          const cachedData = uni.getStorageSync('cached_published_issues');
          if (cachedData && Array.isArray(cachedData)) {
            this.publishedIssues = cachedData;
            uni.showToast({
              title: '已加载缓存数据',
              icon: 'none',
              duration: 2000
            });
          } else {
            this.publishedIssues = [];
          }
        } catch (cacheError) {
          this.publishedIssues = [];
        }
      } finally {
        this.loading = false;
        this.dataLoaded = true; // 标记数据已加载
      }
    },

    // 格式化问题项数据
    formatIssueItem(data) {
      return {
        id: data._id || data.id,
        number: data.issue_number || data.number,
        title: data.title || '未知问题',
        description: data.description || '',
        location: data.location_info?.location_name || '未设置',
        responsible: data.assigned_to_name || '未指定',
        responsible_id: data.assigned_to,
        deadline: this.formatDate(data.expected_completion_date),
        priority: data.priority || 'normal',
        status: data.status || 'pending',
        category: data.category,
        severity: data.severity,
        images: data.photos || [],
        createdAt: data.created_at || new Date().toISOString(),
        updatedAt: data.updated_at || new Date().toISOString()
      };
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '';
      try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) return '';
        return date.toISOString().split('T')[0];
      } catch (error) {
        return '';
      }
    },
    
    // 加载草稿
    async loadDraftIssues() {
      try {
        const drafts = uni.getStorageSync('issue_drafts') || [];
        
        // 解析草稿中的负责人信息
        if (drafts.length > 0) {
          for (let draft of drafts) {
            if (draft.responsible && !draft.responsible_name) {
              // 如果responsible是用户ID，尝试获取用户名
              const userName = await this.getUserNameById(draft.responsible);
              if (userName) {
                draft.responsible_name = userName;
                draft.responsible_id = draft.responsible; // 保存原始用户ID
                draft.responsible = userName; // 更新显示字段
              }
            }
          }
        }
        
        this.draftIssues = drafts;
      } catch (error) {
        this.draftIssues = [];
      }
    },
    
    // 根据用户ID获取用户名
    async getUserNameById(userId) {
      try {
        const { callCloudFunction } = require('@/utils/auth.js');
        
        const result = await callCloudFunction('hygiene-monthly-inspection', {
          action: 'getUserInfo',
          data: {
            userId: userId
          }
        });
        
        if (result && result.success && result.data) {
          return result.data.nickname || result.data.username || '未知用户';
        }
      } catch (error) {
        // 获取用户信息失败，静默处理
      }
      
      return null;
    },
    
    // 切换tab
    switchTab(tab) {
      this.currentTab = tab;
    },
    
    // 处理列表项点击
    handleItemClick(issue) {
      if (issue.status === 'draft') {
        this.editDraft(issue);
      } else {
        this.viewIssueDetail(issue);
      }
    },
    
    // 查看问题详情
    viewIssueDetail(issue) {
        uni.navigateTo({
          url: `/pages/6s_pkg/issue-detail?id=${issue.id}`
        });
    },
    
    // 编辑已发布问题
    editPublishedIssue(issue) {
      // 直接跳转到编辑页面，传递问题数据
      uni.navigateTo({
        url: `/pages/6s_pkg/issue-add?editId=${issue.id}&editData=${encodeURIComponent(JSON.stringify(issue))}`
      });
    },
    
    // 更改问题状态 - 与issue-detail.vue保持一致
    changeIssueStatus(issue) {
      // 根据当前状态和业务逻辑确定可选择的状态
      let statusOptions = [];
      
      const currentStatus = issue.status;
      const hasResponsible = issue.responsible && issue.responsible !== '未指定';
      
      // 如果没有负责人，只能选择分配相关的状态
      if (!hasResponsible) {
        statusOptions = [
          { value: 'assigned', label: '已分配' }
        ];
      } else {
        // 有负责人的情况下，根据当前状态提供合理的选项
        if (currentStatus === 'assigned') {
          statusOptions = [
            { value: 'pending', label: '待整改' },
            { value: 'in_progress', label: '整改中' },
            { value: 'pending_review', label: '待检查' }
          ];
        } else if (currentStatus === 'pending') {
          // 待整改状态，不再提供"已分配"选项，因为负责人已经分配了
          statusOptions = [
            { value: 'in_progress', label: '整改中' },
            { value: 'pending_review', label: '待检查' }
          ];
        } else if (currentStatus === 'in_progress') {
          statusOptions = [
            { value: 'pending_review', label: '待检查' },
            { value: 'pending', label: '待整改' } // 允许回退
          ];
        } else if (currentStatus === 'pending_review') {
          statusOptions = [
            { value: 'approved', label: '检查通过' },
            { value: 'rejected', label: '重新整改' }
          ];
        } else if (currentStatus === 'approved') {
          // 检查通过后，可以重新打开或发现新问题
          statusOptions = [
            { value: 'pending', label: '重新打开' },
            { value: 'rejected', label: '发现新问题，重新整改' }
          ];
        } else if (currentStatus === 'rejected') {
          statusOptions = [
            { value: 'pending', label: '重新开始整改' },
            { value: 'in_progress', label: '整改中' }
          ];
        } else {
          // 其他状态提供基础选项（移除已关闭，检查通过就是自然结束）
          statusOptions = [
            { value: 'pending', label: '待整改' },
            { value: 'in_progress', label: '整改中' },
            { value: 'pending_review', label: '待检查' },
            { value: 'approved', label: '检查通过' }
            // 移除 'closed' 和 'completed'，统一使用 pending_review
          ];
        }
      }
      
      uni.showActionSheet({
        itemList: statusOptions.map(opt => opt.label),
        success: async (res) => {
          const selectedStatus = statusOptions[res.tapIndex];
          if (selectedStatus && selectedStatus.value !== issue.status) {
            await this.updateIssueStatus(issue.id, selectedStatus.value);
          }
        }
      });
    },
    
    // 更新问题状态
    async updateIssueStatus(issueId, newStatus) {
      try {
        // 检查参数有效性
        if (!issueId) {
          uni.showToast({
            title: '问题ID不能为空',
            icon: 'error'
          });
          return;
        }
        
        if (!newStatus) {
          uni.showToast({
            title: '状态参数无效',
            icon: 'error'
          });
          return;
        }
        
        uni.showLoading({ title: '更新中...' });
        
        const { callCloudFunction } = require('@/utils/auth.js');
        
        // 处理特殊状态映射
        let actualStatus = newStatus;
        if (newStatus === 'rejected') {
          actualStatus = 'rejected'; // 重新整改状态
        }
        
        const result = await callCloudFunction('hygiene-monthly-inspection', {
          action: 'updateMonthlyIssue',
          data: {
            issue_id: issueId,
            status: actualStatus,
            action_type: 'admin_update'
          }
        });
        
        if (result && result.success) {
          uni.hideLoading();
          uni.showToast({
            title: '状态更新成功',
            icon: 'success'
          });
          
          // 重新加载数据
          this.loadData();
        } else {
          throw new Error(result?.message || '更新失败');
        }
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: error.message || '更新失败',
          icon: 'error'
        });
      }
    },
    

    
    // 编辑草稿
    editDraft(issue) {
      // 跳转到编辑页面，传递草稿数据
      uni.navigateTo({
        url: `/pages/6s_pkg/issue-add?draftId=${issue.id}`
      });
    },
    
    // 发布草稿
    async publishDraft(issue) {
      // 检查必填字段
      if (!issue.description || !issue.location || !issue.responsible || !issue.deadline) {
        uni.showModal({
          title: '信息不完整',
          content: '草稿信息不完整，需要先编辑补充必要信息后才能发布',
          showCancel: false,
          confirmText: '去编辑',
          success: () => {
            this.editDraft(issue);
          }
        });
        return;
      }
      
      uni.showModal({
        title: '确认发布',
        content: '确定要发布这个问题吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              uni.showLoading({ title: '发布中...' });
              
              const { callCloudFunction } = require('@/utils/auth.js');
              
              // 确保获取正确的负责人ID
              let responsibleId = issue.responsible_id;
              let responsibleName = issue.responsible_name || issue.responsible;
              
              // 如果没有responsible_id，尝试通过responsible字段获取
              if (!responsibleId && issue.responsible) {
                // 如果responsible看起来是用户ID（24位字符），直接使用
                if (typeof issue.responsible === 'string' && issue.responsible.length === 24) {
                  responsibleId = issue.responsible;
                }
              }
              
              
              
              // 构建提交数据
              const submitData = {
                title: issue.title.trim(),
                description: issue.description.trim(),
                location_info: {
                  location_type: 'custom',
                  location_name: issue.location.trim(),
                  location_description: ''
                },
                inspection_info: {
                  inspection_date: new Date().toISOString(),
                  inspection_type: 'monthly_routine'
                },
                category: 'safety',
                severity: issue.priority === 'urgent' ? 'high' : 'medium',
                priority: issue.priority || 'normal',
                assigned_to: responsibleId,
                assigned_to_name: responsibleName,
                photos: issue.images || [],
                tags: [],
                expected_completion_date: issue.deadline
              };
              
              // 调用发布API
              const result = await callCloudFunction('hygiene-monthly-inspection', {
                action: 'createMonthlyIssue',
                data: submitData
              });
              
              if (result && result.success) {
              // 从草稿中移除
              this.removeDraftFromLocal(issue.id);
              this.loadDraftIssues();
                
                // 重新加载已发布列表
                await this.loadPublishedIssues();
              
              uni.hideLoading();
              uni.showToast({
                title: '发布成功',
                icon: 'success'
              });
                
                // 通知其他页面数据已更新
                uni.$emit('monthlyIssueUpdated', {
                  action: 'create',
                  issueId: result.data?.id || result.data?._id,
                  fromDraft: true // 标识来自草稿发布
                });
                
                // 通知草稿列表数据已更新（草稿被删除）
                uni.$emit('issueDraftUpdated', {
                  action: 'delete',
                  draftId: issue.id
                });
              } else {
                throw new Error(result?.message || '发布失败');
              }
              
            } catch (error) {
              uni.hideLoading();
              uni.showToast({
                title: error.message || '发布失败',
                icon: 'error'
              });
              // 发布草稿失败，已有Toast提示
            }
          }
        }
      });
    },
    
    // 删除草稿
    deleteDraft(issue) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个草稿吗？',
        success: (res) => {
          if (res.confirm) {
            this.removeDraftFromLocal(issue.id);
            this.loadDraftIssues();
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
          }
        }
      });
    },
    
    // 从本地缓存移除草稿
    removeDraftFromLocal(draftId) {
      try {
        const drafts = uni.getStorageSync('issue_drafts') || [];
        const filteredDrafts = drafts.filter(draft => draft.id !== draftId);
        uni.setStorageSync('issue_drafts', filteredDrafts);
      } catch (error) {
        // 删除草稿失败，静默处理
      }
    },
    
    // 新增问题
    addIssue() {
      uni.navigateTo({
        url: '/pages/6s_pkg/issue-add'
      });
    },
    
    // 获取问题编号显示
    getIssueNumber(number) {
      if (!number) return '';
      
      const numberStr = String(number);
      
      // 处理 YD 开头的长编号，提取后面的简短编号
      if (numberStr.startsWith('YD') && numberStr.length > 10) {
        const shortNumber = numberStr.slice(-3);
        return `YD${shortNumber}`;
      }
      
      // 处理其他长编号，保留前缀+后6位
      if (numberStr.length > 8) {
        const prefix = numberStr.substring(0, 2);
        const suffix = numberStr.slice(-6);
        return `${prefix}${suffix}`;
      }
      
      return numberStr;
    },
    
    // 获取状态文本 - 与其他页面保持一致
    getStatusText(status) {
      const statusMap = {
        'draft': '草稿',
        'assigned': '已分配',
        'pending': '待整改',
        'in_progress': '整改中',
        'pending_review': '待检查',

        'approved': '检查通过',
        'rejected': '已驳回',
        'overdue': '已逾期',
        'reopened': '重新打开',
        // 保留其他状态以兼容旧数据
        'open': '待处理',
        'resolved': '已解决',
        'reviewing': '审核中',
        'verified': '已验证',
        'cancelled': '已取消',
        'suspended': '已暂停',
        'new': '新建',
        'active': '进行中',
        'inactive': '非活跃',
        'expired': '已过期'
      };
      return statusMap[status] || '未知';
    },
    
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '';
      
      const time = new Date(timeStr);
      const now = new Date();
      const diff = now - time;
      
      if (diff < 60000) { // 1分钟内
        return '刚刚';
      } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前';
      } else if (diff < 86400000) { // 1天内
        return Math.floor(diff / 3600000) + '小时前';
      } else {
        return time.getMonth() + 1 + '月' + time.getDate() + '日';
      }
    },
    
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    },
    
    // 处理问题更新事件
    handleIssueUpdated(data) {
      // 根据操作类型决定刷新策略
      if (data.action === 'create' || data.action === 'update' || data.action === 'delete') {
        // 已发布问题变更，静默刷新已发布数据
        this.silentRefreshPublishedData();
      }
      
      // 如果是从草稿发布，草稿刷新交给 handleDraftUpdated 处理，避免重复刷新
      // 不在这里刷新草稿数据，避免与 handleDraftUpdated 冲突
    },
    
    // 处理草稿更新事件
    handleDraftUpdated(data) {
      // 草稿变更，使用与直接发布相同的成功模式
      setTimeout(() => {
        this.loadDraftIssues(); // 使用与直接发布相同的方法
      }, 100); // 稍微增加延迟确保本地存储操作完成
    },
    
    // 静默刷新数据（不显示加载状态）
    async silentRefreshData() {
      if (this.loading) return; // 如果正在加载，跳过
      
      try {
        // 静默重新加载数据
        await Promise.all([
          this.silentRefreshPublishedData(),
          this.silentRefreshDraftData()
        ]);
      } catch (error) {
        // 静默处理错误，不显示错误提示
      }
    },
    
    // 静默刷新已发布数据
    async silentRefreshPublishedData() {
      try {
        const { callCloudFunction } = require('@/utils/auth.js');
        
        // 调用真实API获取问题列表
        const result = await callCloudFunction('hygiene-monthly-inspection', {
          action: 'getMonthlyIssues',
          data: {
            pageSize: 50,
            page: 1
          }
        });
        
        if (result && result.success && result.data && result.data.list) {
          this.publishedIssues = result.data.list.map(item => this.formatIssueItem(item));
          
          // 更新缓存
          try {
            uni.setStorageSync('cached_published_issues', this.publishedIssues);
          } catch (cacheError) {
            // 缓存失败，静默处理
          }
        }
      } catch (error) {
        // 静默处理错误
      }
    },
    
    // 静默刷新草稿数据
    async silentRefreshDraftData() {
      try {
        const drafts = uni.getStorageSync('issue_drafts') || [];
        
        // 解析草稿中的负责人信息
        if (drafts.length > 0) {
          for (let draft of drafts) {
            if (draft.responsible && !draft.responsible_name) {
              // 如果responsible是用户ID，尝试获取用户名
              const userName = await this.getUserNameById(draft.responsible);
              if (userName) {
                draft.responsible_name = userName;
                draft.responsible_id = draft.responsible; // 保存原始用户ID
                draft.responsible = userName; // 更新显示字段
              }
            }
          }
        }
        
        this.draftIssues = drafts;
      } catch (error) {
        // 静默处理错误
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
  /* 滚动性能优化 */
  -webkit-overflow-scrolling: touch;
  transform: translateZ(0);
  padding-bottom: 100rpx;
}

/* 统计区域 */
.stats-section {
  padding: 32rpx;
  background: transparent;
}

.stats-card {
  display: flex;
  justify-content: space-around;
  padding: 32rpx 24rpx;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  border-radius: 24rpx;
  color: #ffffff;
  box-shadow: 0 12rpx 32rpx rgba(0, 122, 255, 0.4),
              0 8rpx 16rpx rgba(0, 122, 255, 0.3),
              0 4rpx 8rpx rgba(0, 122, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  /* 性能优化 */
  transform: translateZ(0);
  will-change: transform;
}

.stats-item {
  text-align: center;
}

.stats-number {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* Tab切换 */
.tab-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid rgba(229, 229, 229, 0.5);
  margin: 0 32rpx;
  border-radius: 24rpx 24rpx 0 0;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08), 
              0 2rpx 6rpx rgba(0, 0, 0, 0.04);
}

.tab-container {
  display: flex;
  padding: 0 32rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 32rpx 0;
  font-size: 28rpx;
  color: #8E8E93;
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #007AFF;
  border-bottom-color: #007AFF;
  font-weight: 600;
}

/* 列表区域 */
.list-section {
  padding: 0 32rpx;
  margin-top: 24rpx;
}

.issue-item {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08), 
              0 2rpx 6rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* 性能优化 */
  transform: translateZ(0);
  will-change: transform;
}

.issue-item:active {
  background: #F8F8F8;
  transform: scale(0.98);
}

.issue-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.issue-title-section {
  flex: 1;
  margin-right: 16rpx;
}

.issue-number {
  font-size: 22rpx;
  font-weight: 700;
  color: #007AFF;
  font-family: 'Monaco', 'Menlo', monospace;
  margin-bottom: 4rpx;
  opacity: 0.8;
}

.issue-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  line-height: 1.3;
}

.issue-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 600;
  white-space: nowrap;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.08);
}

.status-draft {
  background: #F2F2F7;
  color: #8E8E93;
}

.status-pending {
  background: linear-gradient(135deg, #E6F3FF, #F0F7FF);
  color: #007AFF;
  border: 2rpx solid rgba(0, 122, 255, 0.2);
}

.status-assigned {
  background: linear-gradient(135deg, #ECFEFF, #F0FDFF);
  color: #0891B2;
  border: 2rpx solid rgba(8, 145, 178, 0.2);
}

.status-in_progress {
  background: linear-gradient(135deg, #FFF4E6, #FFFBF0);
  color: #FF9500;
  border: 2rpx solid rgba(255, 149, 0, 0.2);
}

.status-pending_review {
  background: linear-gradient(135deg, #F0EFFF, #F8F7FF);
  color: #5856D6;
  border: 2rpx solid rgba(88, 86, 214, 0.2);
}



.status-approved {
  background: linear-gradient(135deg, #E8F5E8, #F0FFF0);
  color: #34C759;
  border: 2rpx solid rgba(52, 199, 89, 0.2);
}

.status-rejected {
  background: linear-gradient(135deg, #F2F2F7, #F8F8F8);
  color: #8E8E93;
  border: 2rpx solid rgba(142, 142, 147, 0.2);
}



.status-overdue {
  background: linear-gradient(135deg, #FFE6E6, #FFF0F0);
  color: #FF3B30;
  border: 2rpx solid rgba(255, 59, 48, 0.2);
}

/* 兼容性状态映射到8色方案 */
.status-open {
  background: linear-gradient(135deg, #E6F3FF, #F0F7FF);
  color: #007AFF;
  border: 2rpx solid rgba(0, 122, 255, 0.2);
}

.status-resolved {
  background: linear-gradient(135deg, #DCFCE7, #F0FDF4);
  color: #16A34A;
  border: 2rpx solid rgba(22, 163, 74, 0.2);
}

.status-reviewing {
  background: linear-gradient(135deg, #FFF4E6, #FFFBF0);
  color: #FF9500;
  border: 2rpx solid rgba(255, 149, 0, 0.2);
}



.status-verified {
  background: linear-gradient(135deg, #DCFCE7, #F0FDF4);
  color: #16A34A;
  border: 2rpx solid rgba(22, 163, 74, 0.2);
}

.status-cancelled {
  background: linear-gradient(135deg, #F2F2F7, #F8F8F8);
  color: #8E8E93;
  border: 2rpx solid rgba(142, 142, 147, 0.2);
}

.status-suspended {
  background: linear-gradient(135deg, #F2F2F7, #F8F8F8);
  color: #8E8E93;
  border: 2rpx solid rgba(142, 142, 147, 0.2);
}

.status-reopened {
  background: linear-gradient(135deg, #FFF4E6, #FFFBF0);
  color: #FF9500;
  border: 2rpx solid rgba(255, 149, 0, 0.2);
}

.status-new {
  background: linear-gradient(135deg, #ECFEFF, #F0FDFF);
  color: #0891B2;
  border: 2rpx solid rgba(8, 145, 178, 0.2);
}

.status-active {
  background: linear-gradient(135deg, #FFF4E6, #FFFBF0);
  color: #FF9500;
  border: 2rpx solid rgba(255, 149, 0, 0.2);
}

.status-inactive {
  background: linear-gradient(135deg, #F2F2F7, #F8F8F8);
  color: #8E8E93;
  border: 2rpx solid rgba(142, 142, 147, 0.2);
}

.status-expired {
  background: linear-gradient(135deg, #FFE6E6, #FFF0F0);
  color: #FF3B30;
  border: 2rpx solid rgba(255, 59, 48, 0.2);
}

/* 保持原有的processing状态以兼容旧代码 */
.status-processing {
  background: #D1ECF1;
  color: #0C5460;
}

.issue-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.issue-info {
  display: flex;
  gap: 24rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  color: #8E8E93;
}

.issue-time {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 操作按钮 */
.issue-actions {
  display: flex;
  gap: 16rpx;
  border-top: 1rpx solid #F2F2F7;
  padding-top: 16rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.edit {
  background: #F0F8FF;
  color: #007AFF;
}

.action-btn.publish {
  background: #F0FFF4;
  color: #34C759;
}

.action-btn.delete {
  background: #FFF5F5;
  color: #FF3B30;
}

.action-btn.view {
  background: #F0F8FF;
  color: #007AFF;
}

.action-btn.status {
  background: #FFF8E1;
  color: #FF9500;
}

.action-btn.close {
  background: #FFF5F5;
  color: #FF3B30;
}

.action-btn:active {
  opacity: 0.7;
  transform: scale(0.95);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
  gap: 24rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
  text-align: center;
}

/* 悬浮按钮 */
.fab-container {
  position: fixed;
  right: 32rpx;
  bottom: 32rpx;
  z-index: 10;
}

.fab-btn {
  width: 112rpx;
  height: 112rpx;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 12rpx 32rpx rgba(0, 122, 255, 0.4),
              0 8rpx 16rpx rgba(0, 122, 255, 0.3),
              0 4rpx 8rpx rgba(0, 122, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.fab-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.fab-btn:active::before {
  left: 100%;
}

.fab-btn:active {
  transform: scale(0.92);
  box-shadow: 0 6rpx 16rpx rgba(0, 122, 255, 0.3),
              0 3rpx 8rpx rgba(0, 122, 255, 0.2);
}
</style> 