@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container.data-v-9f2cd4fe {
  min-height: 100vh;
  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
  width: 100%;
  overflow-x: hidden;
  /* 防止水平滚动 */
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
}
/* 页面头部 */
.page-header.data-v-9f2cd4fe {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  padding: 32rpx 32rpx 40rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  position: relative;
  z-index: 1000;
}
.header-content.data-v-9f2cd4fe {
  flex: 1;
}
.header-title.data-v-9f2cd4fe {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 8rpx;
}
.header-subtitle.data-v-9f2cd4fe {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}
.status-badge-enhanced.data-v-9f2cd4fe {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 400;
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
.status-badge-enhanced.status-not-cleaned.data-v-9f2cd4fe {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
.status-badge-enhanced.status-pending.data-v-9f2cd4fe {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
.status-badge-enhanced.status-pending-rectification.data-v-9f2cd4fe {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
.status-badge-enhanced.status-completed.data-v-9f2cd4fe {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
.status-badge-enhanced.status-missed.data-v-9f2cd4fe {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
.status-badge-enhanced.status-cancelled.data-v-9f2cd4fe {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
/* 卡片样式 */
.card.data-v-9f2cd4fe {
  background: white;
  border-radius: 16rpx;
  margin: 24rpx 32rpx 0 32rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.card-header.data-v-9f2cd4fe {
  padding: 32rpx 32rpx 16rpx 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.card-title.data-v-9f2cd4fe {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 4rpx;
}
.card-subtitle.data-v-9f2cd4fe {
  font-size: 26rpx;
  color: #8E8E93;
}
.card-subtitle-enhanced.data-v-9f2cd4fe {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #8E8E93;
}
.card-body.data-v-9f2cd4fe {
  padding: 32rpx;
}
/* 信息列表 */
.info-list.data-v-9f2cd4fe {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.info-item.data-v-9f2cd4fe {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.info-label.data-v-9f2cd4fe {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  color: #1C1C1E;
}
.info-value.data-v-9f2cd4fe {
  font-size: 28rpx;
  color: #8E8E93;
  text-align: right;
}
.info-value.rating-value.data-v-9f2cd4fe {
  display: flex;
  align-items: center;
  gap: 4rpx;
  color: #FFD700;
}
/* 增强信息网格 */
.info-grid-enhanced.data-v-9f2cd4fe {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));
  gap: 24rpx;
  margin-top: 24rpx;
}
.info-item-enhanced.data-v-9f2cd4fe {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 24rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.info-icon.data-v-9f2cd4fe {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #007AFF;
  /* 默认图标背景色 */
  color: white;
  font-size: 32rpx;
}
.info-icon.icon-cleaning.data-v-9f2cd4fe {
  background-color: #34C759;
  /* 清理图标背景色 */
}
.info-icon.icon-inspection.data-v-9f2cd4fe {
  background-color: #FF9500;
  /* 检查图标背景色 */
}
.info-icon.icon-time.data-v-9f2cd4fe {
  background-color: #FF3B30;
  /* 时间图标背景色 */
}
.info-icon.icon-cycle.data-v-9f2cd4fe {
  background-color: #5AC8FA;
  /* 周期图标背景色 */
}
.info-icon.icon-rating.data-v-9f2cd4fe {
  background-color: #FFD700;
  /* 评分图标背景色 */
}
.info-icon.icon-review.data-v-9f2cd4fe {
  background-color: #34C759;
  /* 复查图标背景色 */
}
.info-content.data-v-9f2cd4fe {
  flex: 1;
}
.info-label-enhanced.data-v-9f2cd4fe {
  font-size: 28rpx;
  color: #1C1C1E;
  font-weight: 500;
  margin-bottom: 4rpx;
}
.info-value-enhanced.data-v-9f2cd4fe {
  font-size: 28rpx;
  color: #8E8E93;
  font-weight: 600;
}
.rating-item-enhanced.data-v-9f2cd4fe {
  grid-column: span 2;
  /* 评分项跨两列 */
}
.period-item-enhanced.data-v-9f2cd4fe {
  grid-column: span 2;
  /* 所属周期项跨两列 */
}
.rating-display-enhanced.data-v-9f2cd4fe {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-top: 8rpx;
}
.rating-number.data-v-9f2cd4fe {
  font-size: 36rpx;
  font-weight: 700;
  color: #FFD700;
}
.rating-stars.data-v-9f2cd4fe {
  display: flex;
  gap: 4rpx;
}
.rating-desc.data-v-9f2cd4fe {
  font-size: 26rpx;
  color: #8E8E93;
  margin-left: 12rpx;
}
/* 照片网格 */
.no-photos.data-v-9f2cd4fe {
  padding: 40rpx 0;
}
.no-photos-enhanced.data-v-9f2cd4fe {
  padding: 40rpx 0;
}
.photos-grid.data-v-9f2cd4fe {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}
.photos-grid-enhanced.data-v-9f2cd4fe {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  /* 默认3列布局 */
  gap: 16rpx;
}
.photo-item.data-v-9f2cd4fe {
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  background: #F2F2F7;
}
.photo-item-enhanced.data-v-9f2cd4fe {
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  background: #F2F2F7;
  display: flex;
  /* 使用flex布局 */
  flex-direction: column;
  /* 垂直排列内容 */
  justify-content: flex-end;
  /* 底部对齐 */
  align-items: center;
  /* 居中 */
}
.photo-container.data-v-9f2cd4fe {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  overflow: hidden;
}
.photo-image-enhanced.data-v-9f2cd4fe {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}
.photo-overlay.data-v-9f2cd4fe {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.photo-overlay-enhanced.data-v-9f2cd4fe {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12rpx;
  /* 与图片圆角一致 */
}
.photo-item:active .photo-overlay.data-v-9f2cd4fe {
  opacity: 1;
}
.photo-item-enhanced:active .photo-overlay-enhanced.data-v-9f2cd4fe {
  opacity: 1;
}
.overlay-content.data-v-9f2cd4fe {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}
.overlay-text.data-v-9f2cd4fe {
  font-size: 24rpx;
  color: white;
}
/* 检查结果 */
.result-section.data-v-9f2cd4fe {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.result-item.data-v-9f2cd4fe {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.result-label.data-v-9f2cd4fe {
  font-size: 28rpx;
  color: #1C1C1E;
  font-weight: 500;
}
.result-value.data-v-9f2cd4fe {
  font-size: 28rpx;
  font-weight: 600;
}
.result-value.result-passed.data-v-9f2cd4fe {
  color: #34C759;
}
.result-value.result-issues.data-v-9f2cd4fe {
  color: #FF3B30;
}
/* 问题详情 */
.issue-details.data-v-9f2cd4fe {
  background: #FFF5F5;
  border: 1rpx solid #FFE6E6;
  border-radius: 12rpx;
  padding: 24rpx;
}
/* 复查区域样式 */
.review-section.data-v-9f2cd4fe {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #E8F5E8;
  background: #F8FFF8;
  border-radius: 12rpx;
  padding: 24rpx;
}
.review-header.data-v-9f2cd4fe {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #34C759;
  margin-bottom: 16rpx;
}
/* 复查详情区域 */
.review-details-section.data-v-9f2cd4fe {
  margin-bottom: 16rpx;
}
.review-info-row.data-v-9f2cd4fe {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 12rpx;
}
.review-info-item.data-v-9f2cd4fe {
  flex: 1;
  min-width: 200rpx;
  display: flex;
  align-items: center;
}
.review-info-item.rating-item.data-v-9f2cd4fe {
  flex: 100%;
}
.review-label.data-v-9f2cd4fe {
  font-size: 26rpx;
  color: #8E8E93;
  font-weight: 500;
  white-space: nowrap;
}
.review-value.data-v-9f2cd4fe {
  font-size: 26rpx;
  color: #1C1C1E;
  font-weight: 600;
  margin-left: 8rpx;
}
.review-rating-display.data-v-9f2cd4fe {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-left: 8rpx;
}
.rating-score.data-v-9f2cd4fe {
  font-size: 28rpx;
  color: #34C759;
  font-weight: 700;
}
.rating-level.data-v-9f2cd4fe {
  font-size: 24rpx;
  color: #8E8E93;
}
.review-comments.data-v-9f2cd4fe {
  margin-top: 16rpx;
}
.review-comment-content.data-v-9f2cd4fe {
  font-size: 28rpx;
  color: #1C1C1E;
  line-height: 1.5;
  margin-top: 8rpx;
  background: white;
  padding: 16rpx;
  border-radius: 8rpx;
  border: 1rpx solid #E8F5E8;
}
.review-conclusion.data-v-9f2cd4fe {
  text-align: center;
  padding-top: 16rpx;
  border-top: 1rpx solid #E8F5E8;
}
.conclusion-text.data-v-9f2cd4fe {
  font-size: 28rpx;
  color: #34C759;
  font-weight: 600;
}
.review-suffix.data-v-9f2cd4fe {
  font-size: 24rpx;
  color: #8E8E93;
  margin-left: 8rpx;
}
/* 整改任务中的复查信息 */
.review-details.data-v-9f2cd4fe {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(52, 199, 89, 0.2);
}
.review-rating.data-v-9f2cd4fe {
  font-size: 26rpx;
  color: #34C759;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.review-comments-task.data-v-9f2cd4fe {
  font-size: 26rpx;
  color: #1C1C1E;
  line-height: 1.5;
  margin-bottom: 8rpx;
}
/* 整改照片和复查照片样式 */
.rectification-photos.data-v-9f2cd4fe,
.review-photos.data-v-9f2cd4fe {
  margin-top: 24rpx;
  padding: 24rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
  border: 1rpx solid #E5E7EB;
}
.photos-header.data-v-9f2cd4fe {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}
.photos-title.data-v-9f2cd4fe {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
}
.rectification-photos .photos-title.data-v-9f2cd4fe {
  color: #34C759;
}
.review-photos .photos-title.data-v-9f2cd4fe {
  color: #5AC8FA;
}
/* 独立复查结果区域样式 */
.review-section-standalone.data-v-9f2cd4fe {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.review-comments-section.data-v-9f2cd4fe {
  margin-top: 24rpx;
}
.section-title.data-v-9f2cd4fe {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.review-conclusion-standalone.data-v-9f2cd4fe {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 20rpx;
  background: #F0F9F0;
  border: 1rpx solid #34C759;
  border-radius: 12rpx;
  margin-top: 24rpx;
}
.conclusion-icon.data-v-9f2cd4fe {
  display: flex;
  align-items: center;
  justify-content: center;
}
.review-photos-section.data-v-9f2cd4fe {
  margin-top: 24rpx;
}
.issue-header.data-v-9f2cd4fe {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #FF3B30;
  margin-bottom: 16rpx;
}
.issue-time-info.data-v-9f2cd4fe {
  font-size: 26rpx;
  color: #8E8E93;
  font-weight: 500;
  margin-bottom: 4rpx;
}
.issue-content.data-v-9f2cd4fe {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.issue-type.data-v-9f2cd4fe {
  font-size: 26rpx;
  color: #FF3B30;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.issue-description.data-v-9f2cd4fe {
  font-size: 26rpx;
  color: #1C1C1E;
  line-height: 1.5;
  font-weight: 400;
  padding-top: 12rpx;
  border-top: 1rpx solid #FFE6E6;
  margin-top: 8rpx;
  margin-bottom: 4rpx;
}
.remediation-info.data-v-9f2cd4fe {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16rpx;
  border-top: 1rpx solid #FFE6E6;
}
.remediation-label.data-v-9f2cd4fe {
  font-size: 26rpx;
  color: #8E8E93;
}
.remediation-value.data-v-9f2cd4fe {
  font-size: 26rpx;
  font-weight: 500;
}
/* 说明内容 */
.notes-content.data-v-9f2cd4fe {
  font-size: 28rpx;
  color: #1C1C1E;
  line-height: 1.6;
  background: #F8F9FA;
  padding: 24rpx;
  border-radius: 12rpx;
}
/* 整改任务 */
.remediation-task.data-v-9f2cd4fe {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.task-info.data-v-9f2cd4fe {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.task-item.data-v-9f2cd4fe {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.task-label.data-v-9f2cd4fe {
  font-size: 28rpx;
  color: #1C1C1E;
}
.task-value.data-v-9f2cd4fe {
  font-size: 28rpx;
  color: #8E8E93;
}
.task-value.task-status-not-cleaned.data-v-9f2cd4fe {
  color: #8E8E93;
}
.task-value.task-status-pending.data-v-9f2cd4fe {
  color: #FF9500;
}
.task-value.task-status-pending-rectification.data-v-9f2cd4fe {
  color: #FF3B30;
}
.task-value.task-status-completed.data-v-9f2cd4fe {
  color: #34C759;
}
.task-value.task-status-missed.data-v-9f2cd4fe {
  color: #8B5CF6;
}
.task-status-badge.data-v-9f2cd4fe {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  color: white;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}
.task-status-badge.task-badge-not-cleaned.data-v-9f2cd4fe {
  background: #8E8E93;
  color: white;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(142, 142, 147, 0.3);
}
.task-status-badge.task-badge-pending.data-v-9f2cd4fe {
  background: #FF9500;
  color: white;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(255, 149, 0, 0.3);
}
.task-status-badge.task-badge-pending-rectification.data-v-9f2cd4fe {
  background: #FF3B30;
  color: white;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.3);
}
.task-status-badge.task-badge-completed.data-v-9f2cd4fe {
  background: #34C759;
  color: white;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(52, 199, 89, 0.3);
}
.task-status-badge.task-badge-missed.data-v-9f2cd4fe {
  background: #8B5CF6;
  color: white;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(139, 92, 246, 0.3);
}
.completion-info.data-v-9f2cd4fe {
  background: #E8F5E8;
  border: 1rpx solid #34C759;
  border-radius: 12rpx;
  padding: 20rpx;
}
.completion-header.data-v-9f2cd4fe {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 30rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}
/* 根据状态动态设置文字颜色 */
.completion-header text.data-v-9f2cd4fe {
  color: inherit;
  /* 继承父元素颜色 */
}
.completion-details.data-v-9f2cd4fe {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.completion-time.data-v-9f2cd4fe,
.completion-notes.data-v-9f2cd4fe {
  font-size: 26rpx;
  color: #1C1C1E;
  line-height: 1.5;
  margin-bottom: 4rpx;
}
.completion-time.data-v-9f2cd4fe {
  font-weight: 500;
  color: #8E8E93;
}
.completion-notes.data-v-9f2cd4fe {
  color: #1C1C1E;
  font-weight: 400;
  padding-top: 12rpx;
  border-top: 1rpx solid rgba(142, 142, 147, 0.3);
  margin-top: 8rpx;
}
/* 时间线 */
.timeline.data-v-9f2cd4fe {
  position: relative;
  padding-left: 48rpx;
}
.timeline-item.data-v-9f2cd4fe {
  position: relative;
  margin-bottom: 32rpx;
}
.timeline-item.data-v-9f2cd4fe:last-child {
  margin-bottom: 0;
}
.timeline-item.data-v-9f2cd4fe:not(:last-child)::after {
  content: "";
  position: absolute;
  left: -40rpx;
  top: 32rpx;
  width: 2rpx;
  height: calc(100% + 16rpx);
  background: #E5E5EA;
}
.timeline-dot.data-v-9f2cd4fe {
  position: absolute;
  left: -48rpx;
  top: 8rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
}
.timeline-dot.dot-create.data-v-9f2cd4fe {
  background: #007AFF;
}
.timeline-dot.dot-review.data-v-9f2cd4fe {
  background: #34C759;
}
.timeline-dot.dot-issue.data-v-9f2cd4fe {
  background: #FF3B30;
}
.timeline-dot.dot-task.data-v-9f2cd4fe {
  background: #FF9500;
}
.timeline-dot.dot-complete.data-v-9f2cd4fe {
  background: #34C759;
}
.timeline-dot.dot-review.data-v-9f2cd4fe {
  background: #5AC8FA;
}
.timeline-content.data-v-9f2cd4fe {
  background: #F8F9FA;
  padding: 20rpx;
  border-radius: 12rpx;
}
.timeline-title.data-v-9f2cd4fe {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 8rpx;
}
.timeline-desc.data-v-9f2cd4fe {
  font-size: 26rpx;
  color: #8E8E93;
  line-height: 1.4;
  margin-bottom: 8rpx;
}
.timeline-time.data-v-9f2cd4fe {
  font-size: 24rpx;
  color: #C7C7CC;
}
/* 操作按钮 */
.action-section.data-v-9f2cd4fe {
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.action-btn.data-v-9f2cd4fe {
  width: 100%;
  height: 88rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
}
.action-btn.warning.data-v-9f2cd4fe {
  background: #FF9500;
  color: white;
}
.action-btn.primary.data-v-9f2cd4fe {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  color: white;
}
.action-btn.danger.data-v-9f2cd4fe {
  background: #FF3B30;
  color: white;
}
.action-btn.data-v-9f2cd4fe:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.action-btn.btn-loading.data-v-9f2cd4fe {
  opacity: 0.7;
  pointer-events: none;
}
/* 编辑禁用提示 */
.edit-disabled-tip.data-v-9f2cd4fe {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 24rpx;
  background: #FFF3E0;
  border: 1rpx solid #FFB74D;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #F57C00;
  line-height: 1.4;
}
/* 检查结果状态徽章 */
.result-status-badge.data-v-9f2cd4fe {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  color: white;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}
.result-status-badge.badge-passed.data-v-9f2cd4fe {
  background: #34C759;
  color: white;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(52, 199, 89, 0.3);
}
.result-status-badge.badge-issues.data-v-9f2cd4fe {
  background: #FF3B30;
  color: white;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.3);
}
/* 无问题显示 */
.no-issue-display.data-v-9f2cd4fe {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
  gap: 16rpx;
}
.no-issue-icon.data-v-9f2cd4fe {
  flex-shrink: 0;
}
.no-issue-content.data-v-9f2cd4fe {
  flex: 1;
}
.no-issue-title.data-v-9f2cd4fe {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 4rpx;
}
.no-issue-desc.data-v-9f2cd4fe {
  font-size: 28rpx;
  color: #8E8E93;
  line-height: 1.4;
  font-weight: 400;
}
/* 错误状态 */
.error-container.data-v-9f2cd4fe {
  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
}
.retry-btn.data-v-9f2cd4fe {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: #007AFF;
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
}
.retry-btn.data-v-9f2cd4fe:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
/* 照片错误状态 */
.photo-error.data-v-9f2cd4fe {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
.error-text.data-v-9f2cd4fe {
  color: white;
  font-size: 24rpx;
  margin-top: 12rpx;
}
/* 文本展开/收起 */
.text-collapsed.data-v-9f2cd4fe {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  /* 默认显示3行 */
  line-clamp: 3;
  /* 现代浏览器标准属性 */
  overflow: hidden;
  text-overflow: ellipsis;
}
.expand-btn.data-v-9f2cd4fe {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #007AFF;
  margin-top: 12rpx;
  cursor: pointer;
}
/* 底部安全间距 */
.bottom-safe-area.data-v-9f2cd4fe {
  height: 40rpx;
}
/* 内容区域加载状态 */
.content-loading.data-v-9f2cd4fe {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  pointer-events: auto;
}
.loading-content.data-v-9f2cd4fe {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}
.loading-spinner.data-v-9f2cd4fe {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #E5E7EB;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  -webkit-animation: spin-data-v-9f2cd4fe 1s linear infinite;
          animation: spin-data-v-9f2cd4fe 1s linear infinite;
}
@-webkit-keyframes spin-data-v-9f2cd4fe {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin-data-v-9f2cd4fe {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.loading-text.data-v-9f2cd4fe {
  font-size: 28rpx;
  color: #8E8E93;
  text-align: center;
  margin-top: 8rpx;
}
/* 内容区域错误状态 */
.content-error.data-v-9f2cd4fe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
/* H5浏览器端优化 - 条件编译 */
/* 响应式调整 */
@media (max-width: 600rpx) {
.page-header.data-v-9f2cd4fe {
    padding: 24rpx 16rpx;
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
}
.card.data-v-9f2cd4fe {
    margin: 24rpx 16rpx 0 16rpx;
}
.card-header.data-v-9f2cd4fe {
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx;
}
.action-section.data-v-9f2cd4fe {
    padding: 24rpx 16rpx;
}
.info-grid-enhanced.data-v-9f2cd4fe {
    grid-template-columns: 1fr;
    gap: 16rpx;
}
.rating-item-enhanced.data-v-9f2cd4fe,
.period-item-enhanced.data-v-9f2cd4fe {
    grid-column: span 1;
}
.photos-grid-enhanced.data-v-9f2cd4fe {
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
}
}
/* 小屏设备优化 */
@media (max-width: 400rpx) {
.photos-grid-enhanced.data-v-9f2cd4fe {
    grid-template-columns: 1fr;
}
.info-grid-enhanced.data-v-9f2cd4fe {
    grid-template-columns: 1fr;
}
.rating-display-enhanced.data-v-9f2cd4fe {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
}
.status-badge-enhanced.data-v-9f2cd4fe,
.result-status-badge.data-v-9f2cd4fe,
.task-status-badge.data-v-9f2cd4fe {
    font-size: 22rpx;
    padding: 6rpx 12rpx;
}
}
