<template>
  <view class="page-container">

    <!-- 整改任务信息卡片 -->
    <view class="issue-card">
      <view class="issue-header">
        <view class="issue-status">整改中</view>
        <view class="issue-title-header">{{ taskInfo.title }}</view>
      </view>
      <view class="issue-meta">
        <view class="meta-item">
          <uni-icons type="location" size="16" color="#007AFF"></uni-icons>
          <text>{{ taskInfo.areaName }}</text>
        </view>
        <view class="meta-item">
          <uni-icons type="calendar" size="16" color="#FF9500"></uni-icons>
          <text>截止：{{ taskInfo.deadline }}</text>
        </view>
      </view>
      <view v-if="taskInfo.description" class="issue-description">
        <text class="desc-label">问题描述：</text>
        <text class="desc-content">{{ taskInfo.description }}</text>
      </view>

    </view>

    <!-- 原始问题图片 -->
    <view class="section-card">
      <view class="section-header">
        <view class="section-title">问题图片</view>
        <view class="section-subtitle">发现时拍摄</view>
      </view>
      <p-empty-state
        v-if="taskInfo.issuePhotos.length === 0"
        type="data"
        text="暂无问题图片"
        description="检查员未上传问题照片"
      ></p-empty-state>
      
      <view v-else class="image-grid original-images">
        <view 
          v-for="(image, index) in taskInfo.issuePhotos" 
          :key="index"
          class="image-item"
          @click="previewOriginalImage(index)"
        >
          <image :src="image" mode="aspectFill" class="issue-image"></image>
          <view class="image-label">问题图片</view>
        </view>
      </view>
    </view>

    <!-- 整改照片上传 -->
    <view class="section-card">
      <view class="section-header">
        <view class="section-header-main">
          <view class="section-title">整改照片</view>
          <view class="section-subtitle">请上传整改后的照片</view>
        </view>
        <view class="auto-upload-toggle" @click="toggleAutoUpload">
          <view class="toggle-label">自动上传</view>
          <view class="toggle-switch" :class="{ active: autoUpload }">
            <view class="toggle-circle"></view>
          </view>
        </view>
      </view>
      <view class="upload-area">
        <view class="image-grid">
          <view 
            v-for="(photo, index) in rectificationPhotos" 
            :key="index"
            class="image-item photo-item"
          >
            <image :src="photo.url" mode="aspectFill" class="uploaded-image" @click="previewPhoto(index)"></image>
            
            <!-- 上传状态指示器 -->
            <view v-if="photo.uploading" class="photo-uploading">
              <view class="upload-spinner"></view>
            </view>
            <view v-else-if="photo.uploaded" class="photo-uploaded">
              <uni-icons type="checkmarkempty" size="16" color="white"></uni-icons>
            </view>
            <view v-else class="photo-failed" @click="uploadSinglePhoto(photo)">
              <uni-icons type="refreshempty" size="16" color="white"></uni-icons>
            </view>
            
            <!-- 删除按钮 -->
            <view class="photo-delete" @click="deletePhoto(index)">
              <uni-icons type="close" size="18" color="white"></uni-icons>
            </view>
          </view>
          <view 
            v-if="rectificationPhotos.length < maxPhotos" 
            class="image-item add-photo"
            @click="chooseImage"
          >
            <view class="add-icon">
              <uni-icons type="camera-filled" size="32" color="#8E8E93"></uni-icons>
            </view>
            <text class="add-text">添加照片</text>
            <text class="add-count">{{ rectificationPhotos.length }}/{{ maxPhotos }}</text>
          </view>
        </view>
        <view class="upload-tips">
          <text class="tip-text">• 建议拍摄整改后的实际效果照片</text>
          <text class="tip-text">• 可上传 {{ maxPhotos }} 张照片，支持多角度拍摄</text>
        </view>
      </view>
    </view>

    <!-- 整改说明 -->
    <view class="section-card">
      <view class="section-header">
        <view class="section-title">整改说明</view>
        <view class="section-subtitle">详细描述整改措施和效果</view>
      </view>
      <view class="textarea-container">
        <view class="description-input-container">
          <textarea 
            v-model="rectificationDescription"
            class="description-input"
            placeholder="请详细说明采取的整改措施、解决方案和预期效果"
            :maxlength="200"
            auto-height
            @input="handleDescriptionInput"
          ></textarea>
          <view class="char-count-overlay">{{ descriptionLength }}/200</view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="button-container">
      <button 
        class="primary-button" 
        @click="submitRectification"
        :disabled="submitting"
        :class="{ loading: submitting }"
      >
        <view v-if="submitting" class="button-loading">
          <view class="loading-spinner"></view>
          <text>提交中...</text>
        </view>
        <text v-else>提交整改</text>
      </button>
    </view>

    <!-- 加载遮罩 -->
    <view v-if="loading" class="loading-mask">
      <view class="loading-content">
        <uni-icons type="spinner-cycle" size="32" color="#007AFF"></uni-icons>
        <text class="loading-text">{{ loadingText }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { callCloudFunction } from '@/utils/auth.js';
import uploadUtils from '@/utils/upload-utils.js'

export default {
  name: 'RectificationSubmit',
  data() {
    return {
      taskId: null, // 整改任务ID或月检问题ID
      isFromIssue: false, // 是否来自月检问题
      loading: false,
      loadingText: '加载中...',
      submitting: false,
      maxPhotos: 6,
      autoUpload: true, // 自动上传开关
      
      // 整改任务信息
      taskInfo: {
        id: null,
        title: '',
        areaName: '',
        areaId: '',
        deadline: '',
        description: '',
        issuePhotos: [], // 问题照片
        inspectorName: '',
        createdAt: '',
        status: 'pending_rectification'
      },
      
      // 整改照片
      rectificationPhotos: [],
      
      // 整改说明
      rectificationDescription: ''
    }
  },
  
  computed: {
    // 计算描述长度，确保响应式更新
    descriptionLength() {
      return this.rectificationDescription ? this.rectificationDescription.length : 0;
    },
    
    canSubmit() {
      const hasPhotos = this.rectificationPhotos.length > 0;
      const hasDescription = this.rectificationDescription.trim().length >= 2;
      const allPhotosUploaded = this.rectificationPhotos.every(photo => photo.uploaded);
      
      return hasPhotos && hasDescription && allPhotosUploaded;
    }
  },
  
  onLoad(options) {
    // 支持两种参数：taskId（整改任务ID）或 issueId（月检问题ID）
    this.taskId = options.taskId || options.issueId;
    this.isFromIssue = !!options.issueId; // 标记是否来自月检问题
    
    if (!this.taskId) {
      uni.showToast({
        title: '缺少参数',
        icon: 'error'
      });
      setTimeout(() => uni.navigateBack(), 1500);
      return;
    }
    
    this.loadTaskInfo();
    
    // 设置导航栏标题
    uni.setNavigationBarTitle({
      title: '整改提交'
    });
  },

  methods: {
    // 处理描述输入，确保字符限制和响应式更新
    handleDescriptionInput(e) {
      let value = e.detail.value || '';
      
      // 强制限制字符数量
      if (value.length > 200) {
        value = value.substring(0, 200);
        // 如果超出限制，显示提示
        uni.showToast({
          title: '整改说明不能超过200个字符',
          icon: 'none',
          duration: 1500
        });
      }
      
      // 更新数据
      this.rectificationDescription = value;
      
      // 强制触发视图更新
      this.$forceUpdate();
    },
    
    async loadTaskInfo() {
      this.loading = true;
      this.loadingText = this.isFromIssue ? '加载问题信息...' : '加载整改任务信息...';
      
      try {
        let result;
        
        if (this.isFromIssue) {
          // 来自月检问题，直接获取问题详情并格式化为任务数据
          result = await callCloudFunction('hygiene-monthly-inspection', {
            action: 'getIssueDetail',
            data: { issue_id: this.taskId }
          });
          
          if (result && result.success && result.data) {
            const issue = result.data;
            this.taskInfo = this.formatIssueAsTask(issue);
          } else {
            throw new Error('获取问题信息失败');
          }
        } else {
          // 直接加载整改任务
          result = await callCloudFunction('hygiene-rectification', {
            action: 'getRectificationDetail',
            data: { id: this.taskId }
          });

          if (result && result.success && result.data) {
            this.taskInfo = this.formatTaskData(result.data);
          } else {
            throw new Error('获取整改任务信息失败');
          }
        }
        
      } catch (error) {
        console.error('加载失败:', error);
        
        // 根据错误类型提供更友好的提示
        let errorMessage = '加载失败';
        if (error.message?.includes('未知的操作类型')) {
          errorMessage = 'API接口暂时不可用，请稍后重试';
        } else if (error.message?.includes('问题信息失败')) {
          errorMessage = '问题不存在或已被删除';
        } else if (error.message?.includes('整改任务信息失败')) {
          errorMessage = '整改任务不存在或已被删除';
        } else if (error.message) {
          errorMessage = error.message;
        }
        
        uni.showToast({
          title: errorMessage,
          icon: 'error',
          duration: 3000
        });
        
        setTimeout(() => uni.navigateBack(), 2000);
      } finally {
        this.loading = false;
      }
    },

    // 格式化整改任务数据
    formatTaskData(task) {
      return {
        id: task._id || task.id,
        title: this.generateTaskTitle(task),
        areaName: task.area_name || '未知责任区',
        areaId: task.area_id,
        deadline: this.formatDeadline(task.deadline || task.created_at),
        description: task.issue_description || task.description || '',
        issuePhotos: this.formatIssuePhotos(task.issue_photos || []),
        inspectorName: task.inspector_name || '检查员',
        createdAt: task.created_at,
        status: task.status || 'pending_rectification'
      };
    },

    // 将月检问题格式化为任务数据
    formatIssueAsTask(issue) {
      return {
        id: issue._id || issue.id,
        title: issue.title || issue.issue_title || '整改任务',
        areaName: issue.location_info?.location_name || issue.location || '未知区域',
        areaId: issue.area_id || issue.location_info?.area_id || '',
        deadline: this.formatDeadline(issue.expected_completion_date || issue.deadline),
        description: issue.description || issue.issue_description || '',
        issuePhotos: this.formatIssuePhotos(issue.photos || issue.images || issue.issue_photos || []),
        inspectorName: issue.inspector_name || '检查员',
        createdAt: issue.created_at,
        status: 'pending_rectification'
      };
    },

    // 生成任务标题
    generateTaskTitle(task) {
      const issueType = task.issue_type || '清理问题';
      const number = task.number || Math.floor(Math.random() * 999) + 1;
      return `${issueType} #${String(number).padStart(3, '0')}`;
    },

    // 格式化截止时间
    formatDeadline(dateString) {
      if (!dateString) return '待定';
      const date = new Date(dateString);
      const now = new Date();
      
      // 默认给48小时完成时间
      const deadline = new Date(date.getTime() + 48 * 60 * 60 * 1000);      
      return `${deadline.getMonth() + 1}月${deadline.getDate()}日 ${deadline.getHours().toString().padStart(2, '0')}:${deadline.getMinutes().toString().padStart(2, '0')}`;
    },

    // 格式化问题照片
    formatIssuePhotos(photos) {
      if (!Array.isArray(photos)) return [];
      return photos.map(photo => {
        if (typeof photo === 'string') return photo;
        return photo.url || photo.path || photo;
      });
    },
    
    async chooseImage() {
      if (this.rectificationPhotos.length >= this.maxPhotos) {
        uni.showToast({
          title: `最多只能上传${this.maxPhotos}张照片`,
          icon: 'none'
        });
        return;
      }

      try {
        const res = await uni.chooseImage({
          count: this.maxPhotos - this.rectificationPhotos.length,
          sizeType: ['compressed'], // 只使用压缩版本以节省内存和上传时间
          sourceType: ['camera', 'album']
        });
        
        // 处理新选择的照片
        const newPhotos = res.tempFilePaths.map(path => ({
          url: path,
          uploaded: false,
          cloudUrl: '',
          cloudPath: '',
          uploading: false,
          uploadTime: new Date().toISOString()
        }));
        
        // 批量添加到照片列表
        this.rectificationPhotos.push(...newPhotos);
        
        // 如果开启自动上传，立即上传新选择的照片
        if (this.autoUpload) {
          this.autoUploadNewPhotos(newPhotos);
        }
        
      } catch (error) {
        console.error('选择图片失败:', error);
        uni.showToast({
          title: '选择照片失败',
          icon: 'none'
        });
      }
    },

    // 自动上传新选择的照片
    async autoUploadNewPhotos(newPhotos) {
      for (let i = 0; i < newPhotos.length; i++) {
        const photo = newPhotos[i];
        if (!photo.uploaded && !photo.uploading) {
          this.uploadSinglePhoto(photo);
        }
      }
    },

    // 上传单张照片
    async uploadSinglePhoto(photo) {
      if (photo.uploading || photo.uploaded) return;
      
      photo.uploading = true;
      this.$forceUpdate(); // 触发视图更新
      
      try {
        const cloudPath = this.generateCloudPath();
        
        // 使用 uploadToCloud 方法上传照片
        const uploadResult = await uploadUtils.uploadToCloud(photo.url, cloudPath);
        
        if (uploadResult?.fileID) {
          // 获取访问URL
          const fileInfo = await uploadUtils.getFileInfo(uploadResult.fileID);
          
          // 更新照片信息
          Object.assign(photo, {
            uploaded: true,
            uploading: false,
            cloudUrl: fileInfo.tempFileURL || uploadResult.fileID,
            cloudPath: uploadResult.fileID,
            size: uploadResult.actualSize
          });
        } else {
          throw new Error('上传返回结果异常');
        }
      } catch (error) {
        console.error('照片上传失败:', error);
        photo.uploading = false;
        photo.uploaded = false;
        
        uni.showToast({
          title: '照片上传失败',
          icon: 'none'
        });
      } finally {
        this.$forceUpdate(); // 触发视图更新
      }
    },

    // 生成云存储路径
    generateCloudPath() {
      const timestamp = Date.now();
      const random = Math.random().toString(36).substring(2, 8);
      // 优先使用 areaId，其次使用 taskId，最后才是 unknown
      const areaPath = this.taskInfo.areaId || this.taskId || 'temp';
      return `6s/rectification/${areaPath}/${timestamp}_${random}.jpg`;
    },

    // 切换自动上传状态
    toggleAutoUpload() {
      this.autoUpload = !this.autoUpload;
      uni.showToast({
        title: this.autoUpload ? '已开启自动上传' : '已关闭自动上传',
        icon: 'none'
      });
    },
    
    previewPhoto(index) {
      const urls = this.rectificationPhotos.map(photo => photo.url);
      uni.previewImage({
        urls: urls,
        current: index
      });
    },
    
    previewOriginalImage(index) {
      uni.previewImage({
        urls: this.taskInfo.issuePhotos,
        current: index
      });
    },
    
    async deletePhoto(index) {
      if (index < 0 || index >= this.rectificationPhotos.length) {
        return;
      }

      const photo = this.rectificationPhotos[index];
      
      // 如果照片已经上传到云端，需要删除云端文件
      if (photo.uploaded && photo.cloudPath) {
        try {
          uni.showLoading({ title: '删除照片中...' });
          
          // 调用删除云文件的云函数
          await uniCloud.callFunction({
            name: 'delete-file',
            data: {
              fileList: [this.extractFileId(photo.cloudPath)]
            }
          });
        } catch (error) {
          console.error('删除云文件失败:', error);
          uni.showToast({
            title: '删除失败',
            icon: 'none'
          });
          return;
        } finally {
          uni.hideLoading();
        }
      }
      
      // 从数组中移除照片
      this.rectificationPhotos.splice(index, 1);
    },

    // 提取文件ID（用于删除云文件）
    extractFileId(cloudPath) {
      return cloudPath.replace(/^cloud:\/\//, '');
    },
    
    async submitRectification() {
      // 检查照片
      if (this.rectificationPhotos.length === 0) {
        uni.showToast({
          title: '请至少上传一张整改照片',
          icon: 'none'
        });
        return;
      }
      
      // 检查整改说明
      if (this.rectificationDescription.trim().length < 2) {
        uni.showToast({
          title: '请输入整改说明（至少2个字符）',
          icon: 'none'
        });
        return;
      }
      
      // 检查照片是否都已上传成功
      const unUploadedPhotos = this.rectificationPhotos.filter(photo => !photo.uploaded);
      if (unUploadedPhotos.length > 0) {
        uni.showToast({
          title: '请等待照片上传完成或重新上传失败的照片',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      // 检查是否有照片正在上传
      const uploadingPhotos = this.rectificationPhotos.filter(photo => photo.uploading);
      if (uploadingPhotos.length > 0) {
        uni.showToast({
          title: '照片正在上传中，请稍候...',
          icon: 'none'
        });
        return;
      }

      // 检查是否有照片上传失败
      const failedPhotos = this.rectificationPhotos.filter(photo => !photo.uploaded);
      if (failedPhotos.length > 0) {
        uni.showModal({
          title: '提示',
          content: '有照片未上传成功，是否重新上传？',
          success: (res) => {
            if (res.confirm) {
              this.retryFailedUploads();
            }
          }
        });
        return;
      }
      
      this.submitting = true;
      this.loading = true;
      this.loadingText = '提交整改中...';
      
      try {
        // 准备提交数据
        const submitData = {
          id: this.taskId,
          rectification_photos: this.rectificationPhotos.map(photo => ({
            url: photo.cloudUrl || photo.cloudPath,
            type: 'rectification',
            description: ''
          })),
          rectification_description: this.rectificationDescription.trim(),
          submitted_at: new Date().toISOString(),
          status: 'pending_review'
        };

        // 根据来源选择对应的API
        let result;
        if (this.isFromIssue) {
          // 来自月检问题，更新问题状态为已完成
          result = await callCloudFunction('hygiene-monthly-inspection', {
            action: 'updateMonthlyIssue',
            data: {
              issue_id: this.taskId,
              status: 'pending_review',
              action_type: 'submit_rectification',
              rectification_description: submitData.rectification_description,
              rectification_photos: submitData.rectification_photos,
              completed_at: submitData.submitted_at
            }
          });
        } else {
          // 标准整改任务提交
          result = await callCloudFunction('hygiene-rectification', {
            action: 'completeRectification',
            data: {
              id: this.taskId,
              completion_description: submitData.rectification_description,
              completion_photos: submitData.rectification_photos
            }
          });
        }

        if (result && result.success) {
          // 发送更新事件
          if (this.isFromIssue) {
            uni.$emit('monthlyIssueUpdated', {
              action: 'submit_rectification',
              issueId: this.taskId,
              status: 'pending_review'
            });
          } else {
            uni.$emit('rectificationRecordUpdated', {
              taskId: this.taskId,
              areaId: this.taskInfo.areaId,
              status: 'pending_review'
            });
          }
          
          uni.showToast({
            title: '整改提交成功',
            icon: 'success'
          });
          
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          throw new Error(result?.message || '提交失败');
        }
        
      } catch (error) {
        console.error('提交整改失败:', error);
        uni.showToast({
          title: error.message || '提交失败',
          icon: 'error'
        });
      } finally {
        this.submitting = false;
        this.loading = false;
      }
    },

    // 重试失败的上传
    async retryFailedUploads() {
      const failedPhotos = this.rectificationPhotos.filter(photo => !photo.uploaded);
      for (let photo of failedPhotos) {
        await this.uploadSinglePhoto(photo);
      }
    },
    
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
	min-height: 100vh;
	background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
	width: 100%;
	overflow-x: hidden; /* 防止水平滚动 */
	box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
}

/* 问题信息卡片 */
.issue-card {
  background: #FFFFFF;
  margin: 24rpx 32rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.issue-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.issue-status {
  background: #FF9500;
  color: #FFFFFF;
  font-size: 22rpx;
  font-weight: 600;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  white-space: nowrap;
}

.issue-title-header {
  flex: 1;
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-left: 16rpx;
  line-height: 1.4;
  word-wrap: break-word;
}

.issue-meta {
  display: flex;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  color: #8E8E93;
}

.issue-description {
  background: #F8F9FA;
  padding: 20rpx;
  border-radius: 12rpx;
}

.desc-label {
  font-size: 24rpx;
  color: #8E8E93;
  margin-right: 8rpx;
}

.desc-content {
  font-size: 26rpx;
  color: #1C1C1E;
  line-height: 1.5;
}

/* 通用卡片样式 */
.section-card {
  background: #FFFFFF;
  margin: 24rpx 32rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.section-header-main {
  flex: 1;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 8rpx;
}

.section-subtitle {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 自动上传开关 */
.auto-upload-toggle {
  display: flex;
  align-items: center;
  gap: 12rpx;
  cursor: pointer;
}

.toggle-label {
  font-size: 24rpx;
  color: #1C1C1E;
  font-weight: 500;
}

.toggle-switch {
  width: 80rpx;
  height: 48rpx;
  background: #E5E5EA;
  border-radius: 24rpx;
  position: relative;
  transition: background-color 0.3s ease;
}

.toggle-switch.active {
  background: #34C759;
}

.toggle-circle {
  width: 40rpx;
  height: 40rpx;
  background: #FFFFFF;
  border-radius: 50%;
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  transition: transform 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.toggle-switch.active .toggle-circle {
  transform: translateX(32rpx);
}

/* 图片网格 */
.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.image-item {
  width: 170rpx;
  height: 170rpx;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  background: #F5F5F5;
}

.issue-image, .uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-label {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  color: #FFFFFF;
  font-size: 20rpx;
  text-align: center;
  padding: 6rpx;
}



/* 照片状态指示器 */
.photo-uploading,
.photo-uploaded,
.photo-failed {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
}

.photo-uploading {
  background: rgba(0, 0, 0, 0.6);
}

.photo-uploaded {
  background: #34C759;
}

.photo-failed {
  background: rgba(255, 59, 48, 0.9);
  cursor: pointer;
}

.upload-spinner {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-top: 2rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.photo-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
}



.add-photo {
  width: 170rpx;
  height: 170rpx;
  border: 2rpx dashed #D1D1D6;
  background: #FAFBFC;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.add-icon {
  margin-bottom: 8rpx;
}

.add-text {
  font-size: 24rpx;
  color: #8E8E93;
  font-weight: 500;
}

.add-count {
  font-size: 20rpx;
  color: #8E8E93;
}

.upload-tips {
  margin-top: 16rpx;
  padding: 16rpx;
  background: #F8F9FA;
  border-radius: 8rpx;
}

.tip-text {
  display: block;
  font-size: 22rpx;
  color: #8E8E93;
  line-height: 1.5;
  margin-bottom: 4rpx;
}

/* 文本输入 */
.textarea-container {
  position: relative;
}

.description-input-container {
  position: relative;
  width: 100%;
}

.description-input {
  width: 100%;
  min-height: 180rpx;
  padding: 20rpx 16rpx 40rpx 16rpx;
  border: 1rpx solid #E5E5EA;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  background: #F8F9FA;
  box-sizing: border-box;
  resize: none;
  line-height: 1.6;
}

.char-count-overlay {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  font-size: 22rpx;
  color: #8E8E93;
  background: rgba(248, 249, 250, 0.9);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  pointer-events: none;
  z-index: 2;
  backdrop-filter: blur(4rpx);
}

/* 按钮容器 */
.button-container {
  padding: 32rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
}

.primary-button {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 88rpx;
}

.primary-button:disabled {
  background: #C7C7CC;
  color: #8E8E93;
  opacity: 0.7;
}

.primary-button.loading {
  background: #0056D6;
  opacity: 0.9;
}

.button-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  color: white;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 加载遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: #FFFFFF;
  padding: 48rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #1C1C1E;
}

/* H5平台图片尺寸优化 */
/* #ifdef H5 */
@media screen and (min-width: 600px) {
  .image-grid {
    max-width: 600px;
  }
  
  .image-item, .add-photo {
    width: 130px;
    height: 130px;
    max-width: 130px;
    max-height: 130px;
  }
}

@media screen and (min-width: 900px) {
  .image-grid {
    max-width: 800px;
  }
  
  .image-item, .add-photo {
    width: 140px;
    height: 140px;
    max-width: 140px;
    max-height: 140px;
  }
}
/* #endif */
</style> 