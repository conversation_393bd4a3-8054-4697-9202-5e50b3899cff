@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container.data-v-4ede77af {
  min-height: 100vh;
  background: #F8F9FA;
  padding-bottom: 40rpx;
}
.header.data-v-4ede77af {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  padding: 60rpx 32rpx 40rpx;
  color: white;
}
.header-title.data-v-4ede77af {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}
.header-subtitle.data-v-4ede77af {
  font-size: 24rpx;
  opacity: 0.8;
}
.week-overview.data-v-4ede77af {
  background: white;
  margin: 24rpx 32rpx;
  padding: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.overview-title.data-v-4ede77af {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 24rpx;
}
.week-scroll.data-v-4ede77af {
  width: 100%;
}
/* 隐藏水平滚动条 */
.week-scroll.data-v-4ede77af::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  background: transparent;
}
.week-scroll.data-v-4ede77af {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}
.week-grid.data-v-4ede77af {
  display: flex;
  gap: 12rpx;
  padding: 0 4rpx;
}
.day-item.data-v-4ede77af {
  flex-shrink: 0;
  width: 120rpx;
  text-align: center;
  padding: 16rpx 12rpx;
  border-radius: 12rpx;
  background: #F8F9FA;
  transition: all 0.3s ease;
}
.day-item.today.data-v-4ede77af {
  background: rgba(0, 122, 255, 0.1);
  border: 2rpx solid #007AFF;
}
.day-item.has-schedule.data-v-4ede77af {
  background: rgba(52, 199, 89, 0.1);
}
.day-item.today.has-schedule.data-v-4ede77af {
  background: rgba(0, 122, 255, 0.15);
}
.day-header.data-v-4ede77af {
  margin-bottom: 8rpx;
}
.day-name.data-v-4ede77af {
  font-size: 22rpx;
  color: #8E8E93;
  display: block;
}
.day-date.data-v-4ede77af {
  font-size: 26rpx;
  font-weight: 600;
  color: #1C1C1E;
  display: block;
  margin-top: 4rpx;
}
.day-content.data-v-4ede77af {
  min-height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.area-count.data-v-4ede77af {
  font-size: 20rpx;
  color: #34C759;
  font-weight: 500;
}
.no-schedule.data-v-4ede77af {
  font-size: 20rpx;
  color: #C7C7CC;
}
.filter-bar.data-v-4ede77af {
  padding: 0 32rpx 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16rpx;
}
.filter-picker.data-v-4ede77af {
  flex: 1;
  min-width: 0;
}
.filter-value.data-v-4ede77af {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 56rpx;
  background: white;
  border-radius: 12rpx;
  padding: 0 16rpx;
  font-size: 24rpx;
  color: #1C1C1E;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.area-list.data-v-4ede77af {
  padding: 0 32rpx;
}
.area-item.data-v-4ede77af {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.area-main.data-v-4ede77af {
  flex: 1;
}
.area-header.data-v-4ede77af {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.area-name.data-v-4ede77af {
  font-size: 30rpx;
  font-weight: 600;
  color: #1C1C1E;
}
.schedule-status.data-v-4ede77af {
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}
.schedule-status.scheduled.data-v-4ede77af {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}
.schedule-status.unscheduled.data-v-4ede77af {
  background: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}
.area-info.data-v-4ede77af {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 12rpx;
}
.info-item.data-v-4ede77af {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  color: #8E8E93;
  flex-shrink: 0;
  min-width: -webkit-fit-content;
  min-width: fit-content;
}
.info-item .today.data-v-4ede77af {
  color: #007AFF;
  font-weight: 600;
}
.area-actions.data-v-4ede77af {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.action-btn.data-v-4ede77af {
  height: 56rpx;
  padding: 0 16rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  font-size: 22rpx;
  border: none;
  transition: all 0.3s ease;
  min-width: 100rpx;
  white-space: nowrap;
}
.action-btn.set.data-v-4ede77af {
  background: #007AFF;
  color: white;
}
.action-btn.clear.data-v-4ede77af {
  background: #8E8E93;
  color: white;
}
.action-btn.data-v-4ede77af:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.schedule-popup.data-v-4ede77af, .quick-set-popup.data-v-4ede77af {
  width: 92vw;
  max-width: 650rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}
.popup-header.data-v-4ede77af {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
}
.popup-title.data-v-4ede77af {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-left: 5rpx;
}
.close-btn.data-v-4ede77af {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F2F2F7;
  border: none;
  margin-right: 5rpx;
}
.popup-content.data-v-4ede77af {
  flex: 1;
  overflow-y: auto;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
/* 微信小程序隐藏滚动条 */
.popup-content.data-v-4ede77af::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  background: transparent;
}
.popup-content.data-v-4ede77af {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}
/* 区域信息卡片 */
.area-info-card.data-v-4ede77af {
  background: linear-gradient(180deg, #FFFFFF 0%, #FAFBFC 100%);
  border: 1rpx solid #E5E5EA;
  border-radius: 16rpx;
  padding: 26rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}
.area-info-header.data-v-4ede77af {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.area-info-name.data-v-4ede77af {
  font-size: 34rpx;
  font-weight: 600;
  color: #1C1C1E;
  letter-spacing: 0.5rpx;
}
.area-status-badge.data-v-4ede77af {
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}
.area-info-location.data-v-4ede77af {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #8E8E93;
}
/* 设置表单 */
.setting-form.data-v-4ede77af {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 32rpx;
}
.form-group.data-v-4ede77af {
  display: flex;
  flex-direction: column;
}
.form-label.data-v-4ede77af {
  font-size: 28rpx;
  color: #1C1C1E;
  margin-bottom: 12rpx;
  font-weight: 600;
  position: relative;
  padding-left: 16rpx;
}
.form-label.data-v-4ede77af::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 4rpx;
  height: 18rpx;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  border-radius: 2rpx;
}
.form-picker.data-v-4ede77af {
  width: 100%;
}
.form-input.data-v-4ede77af {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 88rpx;
  background: #FFFFFF;
  border: 2rpx solid #E5E5EA;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  box-sizing: border-box;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  position: relative;
}
.form-input.data-v-4ede77af:active {
  border-color: #007AFF;
  background: #F8F9FA;
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.15);
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
}
.dropdown-icon.data-v-4ede77af, .input-icon.data-v-4ede77af {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  border-radius: 6rpx;
  background: rgba(0, 122, 255, 0.08);
  transition: all 0.3s ease;
}
/* 设置预览 */
.setting-preview.data-v-4ede77af {
  background: rgba(0, 122, 255, 0.04);
  border: 2rpx solid rgba(0, 122, 255, 0.12);
  border-radius: 16rpx;
  padding: 24rpx;
}
.preview-header.data-v-4ede77af {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}
.preview-header text.data-v-4ede77af {
  font-size: 26rpx;
  font-weight: 600;
  color: #007AFF;
}
.preview-summary.data-v-4ede77af {
  font-size: 26rpx;
  color: #1C1C1E;
  line-height: 1.6;
}
.preview-summary .highlight-date.data-v-4ede77af {
  color: #007AFF;
  font-weight: 600;
  background: rgba(0, 122, 255, 0.08);
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
}
.preview-summary .highlight-day.data-v-4ede77af {
  color: #34C759;
  font-weight: 600;
  background: rgba(52, 199, 89, 0.08);
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
}
.preview-summary.complete.data-v-4ede77af {
  color: #1C1C1E;
}
.preview-summary.incomplete.data-v-4ede77af {
  color: #8E8E93;
}
.preview-item.data-v-4ede77af {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4rpx;
  line-height: 1.6;
}
.missing-info.data-v-4ede77af {
  color: #FF9500;
  font-weight: 500;
}
.preview-summary.clear.data-v-4ede77af {
  color: #FF3B30;
  font-weight: 600;
  background: rgba(255, 59, 48, 0.08);
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
}
.clear-info.data-v-4ede77af {
  color: #FF3B30;
  font-weight: 600;
}
.popup-footer.data-v-4ede77af {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #F2F2F7;
}
.popup-btn.data-v-4ede77af {
  flex: 1;
  height: 76rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  border: none;
  transition: all 0.3s ease;
}
.popup-btn.cancel.data-v-4ede77af {
  background: #F2F2F7;
  color: #8E8E93;
}
.popup-btn.submit.data-v-4ede77af {
  background: #007AFF;
  color: white;
}
.popup-btn.data-v-4ede77af:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.list-loading.data-v-4ede77af {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  background: #F8F9FA;
}
.loading-content.data-v-4ede77af {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}
.loading-text.data-v-4ede77af {
  font-size: 28rpx;
  color: #8E8E93;
}
