{"bsonType": "object", "required": ["title", "description", "reporter_id", "area_id"], "permission": {"read": true, "create": true, "update": "doc.reporter_id == auth.uid || doc.assigned_to == auth.uid || auth.role.includes('admin')", "delete": "auth.role.includes('admin')"}, "properties": {"_id": {"description": "ID，系统自动生成"}, "title": {"bsonType": "string", "title": "问题标题", "description": "问题的简要描述", "minLength": 5, "maxLength": 100, "errorMessage": {"required": "问题标题不能为空", "minLength": "问题标题长度不能小于 {minLength} 个字符", "maxLength": "问题标题长度不能大于 {maxLength} 个字符"}}, "description": {"bsonType": "string", "title": "问题描述", "description": "问题的详细描述", "minLength": 10, "maxLength": 1000, "errorMessage": {"required": "问题描述不能为空", "minLength": "问题描述长度不能小于 {minLength} 个字符", "maxLength": "问题描述长度不能大于 {maxLength} 个字符"}}, "category": {"bsonType": "string", "title": "问题类别", "description": "问题的分类", "enum": ["cleanliness", "organization", "safety", "equipment", "environment", "other"], "default": "cleanliness"}, "severity": {"bsonType": "string", "title": "严重程度", "description": "问题的严重程度", "enum": ["low", "medium", "high", "critical"], "default": "medium"}, "area_id": {"bsonType": "string", "title": "责任区ID", "description": "问题所在的责任区ID", "foreignKey": "hygiene-areas._id", "errorMessage": {"required": "责任区不能为空"}}, "area_name": {"bsonType": "string", "title": "责任区名称", "description": "冗余存储的责任区名称", "maxLength": 50}, "reporter_id": {"bsonType": "string", "title": "举报人ID", "description": "发现并举报问题的用户ID", "foreignKey": "uni-id-users._id", "errorMessage": {"required": "举报人不能为空"}}, "reporter_name": {"bsonType": "string", "title": "举报人姓名", "description": "冗余存储的举报人姓名", "maxLength": 50}, "assigned_to": {"bsonType": "string", "title": "分配给", "description": "负责解决该问题的用户ID", "foreignKey": "uni-id-users._id"}, "assigned_to_name": {"bsonType": "string", "title": "负责人姓名", "description": "冗余存储的负责人姓名", "maxLength": 50}, "photos": {"bsonType": "array", "title": "问题照片", "description": "展示问题的照片列表", "items": {"bsonType": "object", "properties": {"url": {"bsonType": "string", "title": "图片URL", "pattern": "^cloud://"}, "description": {"bsonType": "string", "title": "照片说明", "maxLength": 100}, "timestamp": {"bsonType": "timestamp", "title": "拍摄时间"}}}, "maxItems": 10}, "status": {"bsonType": "string", "title": "处理状态", "description": "问题的当前处理状态", "enum": ["draft", "submitted", "assigned", "in_progress", "resolved", "verified", "closed", "rejected"], "default": "draft"}, "priority": {"bsonType": "string", "title": "优先级", "description": "问题处理的优先级", "enum": ["low", "normal", "high", "urgent"], "default": "normal"}, "expected_completion_date": {"bsonType": "timestamp", "title": "期望完成日期", "description": "期望问题解决的日期"}, "actual_completion_date": {"bsonType": "timestamp", "title": "实际完成日期", "description": "问题实际解决的日期"}, "resolution_description": {"bsonType": "string", "title": "解决方案描述", "description": "问题的解决方案详细描述", "maxLength": 1000}, "resolution_photos": {"bsonType": "array", "title": "解决后照片", "description": "问题解决后的照片", "items": {"bsonType": "string", "pattern": "^cloud://"}, "maxItems": 10}, "follow_up_required": {"bsonType": "bool", "title": "需要跟进", "description": "是否需要后续跟进", "default": false}, "follow_up_date": {"bsonType": "timestamp", "title": "跟进日期", "description": "计划跟进的日期"}, "cost_estimate": {"bsonType": "number", "title": "成本估算", "description": "解决问题的成本估算（元）", "minimum": 0, "maximum": 999999}, "tags": {"bsonType": "array", "title": "标签", "description": "问题的标签，便于分类和搜索", "items": {"bsonType": "string", "maxLength": 20}, "maxItems": 10}, "comments": {"bsonType": "array", "title": "评论记录", "description": "问题处理过程中的评论和交流", "items": {"bsonType": "object", "properties": {"user_id": {"bsonType": "string", "foreignKey": "uni-id-users._id"}, "user_name": {"bsonType": "string", "maxLength": 50}, "content": {"bsonType": "string", "maxLength": 500}, "timestamp": {"bsonType": "timestamp"}}}, "maxItems": 50}, "created_at": {"bsonType": "timestamp", "title": "创建时间", "description": "记录创建时间", "forceDefaultValue": {"$env": "now"}}, "updated_at": {"bsonType": "timestamp", "title": "更新时间", "description": "记录最后更新时间", "forceDefaultValue": {"$env": "now"}}}}