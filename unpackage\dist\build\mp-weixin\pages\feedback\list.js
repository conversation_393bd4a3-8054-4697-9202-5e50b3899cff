(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/feedback/list"],{1353:function(e,t,n){},2196:function(e,t,n){"use strict";var a=n("7eed"),i=n.n(a);i.a},"27a0":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return s})),n.d(t,"a",(function(){return a}));var a={uniCollapse:function(){return n.e("uni_modules/uni-collapse/components/uni-collapse/uni-collapse").then(n.bind(null,"5e8a"))},uniCollapseItem:function(){return n.e("uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item").then(n.bind(null,"b4cf"))},uniEasyinput:function(){return n.e("uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput").then(n.bind(null,"6cf4"))},uniDatetimePicker:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker")]).then(n.bind(null,"4051"))},uniTable:function(){return n.e("uni_modules/uni-table/components/uni-table/uni-table").then(n.bind(null,"e977"))},uniTr:function(){return n.e("uni_modules/uni-table/components/uni-tr/uni-tr").then(n.bind(null,"7fe0"))},uniTh:function(){return n.e("uni_modules/uni-table/components/uni-th/uni-th").then(n.bind(null,"fd18"))},uniTd:function(){return n.e("uni_modules/uni-table/components/uni-td/uni-td").then(n.bind(null,"e7e2"))},uniPagination:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-pagination/components/uni-pagination/uni-pagination")]).then(n.bind(null,"1b39"))},uniLoadMore:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-load-more/components/uni-load-more/uni-load-more")]).then(n.bind(null,"3282"))},pEmptyState:function(){return n.e("components/p-empty-state/p-empty-state").then(n.bind(null,"9b76"))},uniPopup:function(){return n.e("uni_modules/uni-popup/components/uni-popup/uni-popup").then(n.bind(null,"a2b7"))}},i=function(){var e=this,t=e.$createElement,n=(e._self._c,e.getStatusText(e.searchParams.status)||"工作流状态"),a=e.getUrgencyText(e.searchParams.urgency)||"紧急程度",i=e.getProjectText(e.searchParams.project)||"找茬项目",s=e.getResponsibleText(e.searchParams.responsible)||"责任人",r=e.feedbackList.length,o=r>0?e.__map(e.feedbackList,(function(t,n){var a=e.__get_orig(t),i=t.description&&t.description.length>45,s=t.images&&t.images.length>0,r=s?t.images.length:null,o=s?t.images.length:null,u=s&&o>1?t.images.length:null,c=s&&o>1&&u>2?t.images.length:null,l=e.formatTime(t.createTime,t),d=t.responsibleInfo&&t.responsibleInfo.assignedTime?e.formatTime(t.responsibleInfo.assignedTime,t):null,f=e.getReasonDisplay(t),h="-"!==f?e.formatReasonText(e.getReasonDisplay(t)):null,p=e.hasOperationPermission?e.hasEditPermission&&t.availableActions.includes("edit"):null,m=e.hasOperationPermission?t.availableActions.includes("submit_completion"):null;return{$orig:a,g1:i,g2:s,g3:r,g4:o,g5:u,g6:c,m4:l,m5:d,m6:f,m7:h,g7:p,g8:m}})):null;e.$mp.data=Object.assign({},{$root:{m0:n,m1:a,m2:i,m3:s,g0:r,l0:o}})},s=[]},"572e":function(e,t,n){"use strict";n.r(t);var a=n("ba39"),i=n.n(a);for(var s in a)["default"].indexOf(s)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(s);t["default"]=i.a},"726d":function(e,t,n){"use strict";n.r(t);var a=n("27a0"),i=n("572e");for(var s in i)["default"].indexOf(s)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(s);n("2196"),n("c061");var r=n("828b"),o=Object(r["a"])(i["default"],a["b"],a["c"],!1,null,"4e53a64a",null,!1,a["a"],void 0);t["default"]=o.exports},"7eed":function(e,t,n){},ba39:function(e,t,n){"use strict";(function(e,a){var i=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s,r=i(n("7eb4")),o=i(n("7ca3")),u=i(n("af34")),c=i(n("ee10")),l=n("eddf"),d=e.database(),f=(d.command,{components:{PEmptyState:function(){n.e("components/p-empty-state/p-empty-state").then(function(){return resolve(n("9b76"))}.bind(null,n)).catch(n.oe)}},data:function(){return{responsibleOptions:[],responsibleMap:{},createDateRange:[],searchParams:{keyword:"",project:"",responsible:"",status:"",urgency:""},projectOptions:[{text:"全部",value:""},{text:"安全找茬",value:"安全找茬"},{text:"设备找茬",value:"设备找茬"},{text:"其他找茬",value:"其他找茬"}],isLoading:!1,isTokenValid:!0,userRoles:[],statusOptions:[],urgencyOptions:[],feedbackList:[],totalCount:0,currentPage:1,pageSize:20,currentPageStart:0,searchTimer:null,hasInitialized:!1,lastRefreshTime:0,isPageVisible:!0,needsRefreshOnShow:!1}},computed:{hasOperationPermission:function(){if(!this.isTokenValid)return!1;var e=["responsible","supervisor","GM","PM","admin","manager"];return this.userRoles.some((function(t){return e.includes(t)}))},hasEditPermission:function(){return!!this.isTokenValid&&this.userRoles.some((function(e){return["admin","manager"].includes(e)}))},hasDeletePermission:function(){return!!this.isTokenValid&&this.userRoles.some((function(e){return["admin","manager","GM"].includes(e)}))}},created:function(){var e=this;this.isLoading=!0,a.$on("cross-device-update-detected",(function(t){if(t.silent){var n=e.shouldRefreshOnCrossDeviceUpdate(t);n&&(console.log("反馈列表页面收到跨设备更新通知，静默刷新数据"),e.silentRefresh())}}))},onLoad:function(){var e=this;return(0,c.default)(r.default.mark((function t(){return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,e.hasInitialized=!1,t.prev=2,e.checkAndSetTokenStatus(),t.next=6,e.initializeWorkflowOptions();case 6:return t.next=8,e.loadResponsibleMap();case 8:return t.next=10,e.loadFeedbackList();case 10:e.setupRequestInterceptor(),e.setupTokenEventListeners(),e.setupFeedbackEventListeners(),t.next=20;break;case 15:t.prev=15,t.t0=t["catch"](2),a.showToast({title:"页面加载失败",icon:"none"}),e.hasInitialized=!0,e.isLoading=!1;case 20:case"end":return t.stop()}}),t,null,[[2,15]])})))()},onReady:function(){},onPullDownRefresh:function(){var e=this;this.loadFeedbackList().finally((function(){a.stopPullDownRefresh(),e.hasInitialized=!0}))},onShow:function(){this.isPageVisible=!0;var e=this.isTokenValid;if(this.checkAndSetTokenStatus(),e!==this.isTokenValid)return this.loadResponsibleMap(),this.loadFeedbackList(),void(this.needsRefreshOnShow=!1);if(this.needsRefreshOnShow)return this.loadFeedbackList(),this.lastRefreshTime=Date.now(),void(this.needsRefreshOnShow=!1);var t=Date.now(),n=this.lastRefreshTime||0,a=t-n;this.hasInitialized?a>3e5&&this.silentRefresh():(this.loadFeedbackList(),this.lastRefreshTime=t)},onHide:function(){this.isPageVisible=!1},onUnload:function(){this.removeTokenEventListeners(),this.removeFeedbackEventListeners(),a.$off("cross-device-update-detected")},methods:(s={handleError:function(e,t){a.showToast({title:e,icon:"none"})},silentRefresh:function(){var e=this;return(0,c.default)(r.default.mark((function t(){return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.loadFeedbackList(!0);case 3:t.next=8;break;case 5:t.prev=5,t.t0=t["catch"](0),console.error("❌ 静默刷新失败:",t.t0);case 8:case"end":return t.stop()}}),t,null,[[0,5]])})))()},loadResponsibleMap:function(){var t=this;return(0,c.default)(r.default.mark((function n(){var a;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,t.responsibleMap=l.cacheManager.getResponsibleMap(),n.next=4,l.cacheManager.getResponsibleList((0,c.default)(r.default.mark((function t(){var n,a;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.callFunction({name:"feedback-list",data:{action:"getResponsibleUsers"}});case 2:if(n=t.sent,n&&n.result&&0===n.result.code){t.next=5;break}throw new Error((null===(a=n.result)||void 0===a?void 0:a.message)||"获取责任人数据失败");case 5:return t.abrupt("return",n.result.data||[]);case 6:case"end":return t.stop()}}),t)}))));case 4:a=n.sent,t.responsibleOptions=[{text:"全部",value:""}].concat((0,u.default)(a.map((function(e){return{value:e._id,text:e.nickname||e.username||"-"}})))),t.responsibleMap=a.reduce((function(e,t){return e[t._id]=t.nickname||t.username||"-",e}),{}),n.next=15;break;case 9:n.prev=9,n.t0=n["catch"](0),console.warn("⚠️ 负责人数据加载失败:",n.t0),t.responsibleOptions=[{text:"全部",value:""}],t.responsibleMap={},t.isTokenValid&&n.t0.message&&n.t0.message.includes("权限")&&t.handleError("获取责任人数据失败",n.t0);case 15:case"end":return n.stop()}}),n,null,[[0,9]])})))()},getResponsibleName:function(e){return e&&this.responsibleMap[e]||"-"},previewImage:function(e,t){if(e&&Array.isArray(e)&&0!==e.length)try{a.previewImage({urls:e,current:t||0,fail:function(e){}})}catch(n){}else this.handleError("无图片可预览")},handleImageError:function(e){e.target.src="/static/empty/default-image.png"},onPageChange:function(e){this.currentPage=e.current,this.loadFeedbackList()},onProjectChange:function(e){this.searchParams.project=e,this.currentPage=1,this.loadFeedbackList()},onResponsibleChange:function(e){this.searchParams.responsible=e,this.currentPage=1,this.loadFeedbackList()},onCreateDateChange:function(e){this.createDateRange=e,this.currentPage=1,this.loadFeedbackList()},validateResponse:function(e){return!!e&&(!!e.result&&!!e.result.data)},checkAndSetTokenStatus:function(){var e=a.getStorageSync("uni_id_token");if(e){var t=a.getStorageSync("uni_id_token_expired");t&&t<Date.now()?(this.isTokenValid=!1,this.handleTokenInvalid()):this.isTokenValid=!0}else this.isTokenValid=!1},checkTokenStatus:function(){var e=this;return(0,c.default)(r.default.mark((function t(){var n,i;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,n=a.getStorageSync("uni_id_token"),n){t.next=5;break}return e.handleTokenInvalid(),t.abrupt("return");case 5:i=a.getStorageSync("uni_id_token_expired"),i<Date.now()&&e.handleTokenInvalid(),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](0),e.handleError("验证登录状态失败",t.t0);case 12:case"end":return t.stop()}}),t,null,[[0,9]])})))()},handleTokenInvalid:function(){this.isTokenValid=!1,a.removeStorageSync("uni_id_token"),a.removeStorageSync("uni_id_token_expired"),a.removeStorageSync("uni-id-pages-userInfo"),l.cacheManager.clearUserRelatedCache(),this.loadFeedbackList()},setupRequestInterceptor:function(){var t=this,n=e.database();n.interceptorAdd("callFunction",{invoke:function(e){},success:function(e){return e.result&&"TOKEN_INVALID"===e.result.code&&t.handleTokenInvalid(),e},fail:function(e){return e},complete:function(e){return e}})},setupTokenEventListeners:function(){a.$on("token-expired",this.handleGlobalTokenExpired),a.$on("token-invalid",this.handleGlobalTokenExpired)},removeTokenEventListeners:function(){a.$off("token-expired",this.handleGlobalTokenExpired),a.$off("token-invalid",this.handleGlobalTokenExpired)},handleGlobalTokenExpired:function(){this.isTokenValid=!1,a.removeStorageSync("uni_id_token"),a.removeStorageSync("uni_id_token_expired"),a.removeStorageSync("uni-id-pages-userInfo"),l.cacheManager.clearUserRelatedCache(),this.loadResponsibleMap(),this.loadFeedbackList(),this.$forceUpdate()},setupFeedbackEventListeners:function(){a.$on("feedback-submitted",this.handleFeedbackSubmitted),a.$on("feedback-updated",this.handleFeedbackUpdated),a.$on("task-completed",this.handleTaskCompleted),a.$on("task-status-changed",this.handleTaskStatusChanged)},removeFeedbackEventListeners:function(){a.$off("feedback-submitted",this.handleFeedbackSubmitted),a.$off("feedback-updated",this.handleFeedbackUpdated),a.$off("task-completed",this.handleTaskCompleted),a.$off("task-status-changed",this.handleTaskStatusChanged)},handleFeedbackSubmitted:function(e){this.currentPage=1,this.loadFeedbackList(),this.lastRefreshTime=Date.now(),this.needsRefreshOnShow=!1,this.isPageVisible&&a.showToast({title:"列表已更新",icon:"success",duration:1500})},handleFeedbackUpdated:function(e){this.isPageVisible?(this.loadFeedbackList(),this.lastRefreshTime=Date.now()):this.needsRefreshOnShow=!0},handleTaskCompleted:function(e){this.loadFeedbackList(),this.lastRefreshTime=Date.now(),a.showToast({title:"任务状态已更新",icon:"success",duration:1500})},handleTaskStatusChanged:function(e){this.isPageVisible?this.silentRefresh():this.needsRefreshOnShow=!0},initializeWorkflowOptions:function(){var e=this;return(0,c.default)(r.default.mark((function t(){return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.statusOptions=l.cacheManager.getStatusOptions(),e.urgencyOptions=l.cacheManager.getUrgencyOptions(),t.next=4,l.cacheManager.getProjectOptions();case 4:e.projectOptions=t.sent;case 5:case"end":return t.stop()}}),t)})))()},loadFeedbackList:function(){var t=arguments,n=this;return(0,c.default)(r.default.mark((function i(){var s,o,u,c,l,d,f,h,p,m,g,b,k;return r.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return s=t.length>0&&void 0!==t[0]&&t[0],s||(n.isLoading=!0),i.prev=2,o={action:"getList",pageSize:n.pageSize,pageNum:n.currentPage,project:n.searchParams.project||"",status:n.searchParams.status||"",keyword:n.searchParams.keyword||"",urgency:n.searchParams.urgency||"",responsible:n.searchParams.responsible||""},n.createDateRange&&2===n.createDateRange.length&&(u=n.createDateRange[0],c=n.createDateRange[1],l=u.split("-"),d=c.split("-"),f=new Date(parseInt(l[0]),parseInt(l[1])-1,parseInt(l[2]),0,0,0,0),h=new Date(parseInt(d[0]),parseInt(d[1])-1,parseInt(d[2]),23,59,59,999),o.dateRange={start:f.getTime(),end:h.getTime()}),i.next=7,e.callFunction({name:"feedback-list",data:o});case 7:if(p=i.sent,!p.result||0!==p.result.code){i.next=16;break}m=p.result.data,g=m.list,b=m.pagination,k=m.userInfo,n.feedbackList=g||[],n.totalCount=b?b.total:0,n.currentPageStart=b?(b.pageNum-1)*b.pageSize:0,k&&k.roles?n.userRoles=k.roles:n.userRoles=[],i.next=17;break;case 16:throw new Error(p.result?p.result.message:"未知错误");case 17:i.next=24;break;case 19:i.prev=19,i.t0=i["catch"](2),n.feedbackList=[],n.totalCount=0,a.showToast({title:"加载数据失败: "+(i.t0.message||i.t0),icon:"none",duration:3e3});case 24:return i.prev=24,s||(n.isLoading=!1),n.hasInitialized=!0,n.lastRefreshTime=Date.now(),i.finish(24);case 29:case"end":return i.stop()}}),i,null,[[2,19,24,29]])})))()},onStatusChange:function(e){this.searchParams.status=e,this.currentPage=1,this.loadFeedbackList()},onUrgencyChange:function(e){this.searchParams.urgency=e,this.currentPage=1,this.loadFeedbackList()},onKeywordSearch:function(){var e=this;clearTimeout(this.searchTimer),this.searchTimer=setTimeout((function(){e.currentPage=1,e.loadFeedbackList()}),300)},viewDetail:function(e){a.navigateTo({url:"/pages/feedback_pkg/examine?id=".concat(e._id,"&readonly=true")})},editItem:function(e){a.navigateTo({url:"/pages/feedback_pkg/edit?id=".concat(e._id)})},submitCompletion:function(e){return(0,c.default)(r.default.mark((function t(){return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:a.navigateTo({url:"/pages/ucenter_pkg/complete-task?id=".concat(e._id),fail:function(e){a.showToast({title:"跳转失败",icon:"none"})}});case 1:case"end":return t.stop()}}),t)})))()},deleteItem:function(e){var t=this;return(0,c.default)(r.default.mark((function n(){return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:a.showModal({title:"确认删除",content:'确定要删除"'.concat(e.name,'"的问题反馈吗？此操作不可恢复！'),confirmText:"删除",confirmColor:"#ff4444",success:function(){var n=(0,c.default)(r.default.mark((function n(a){return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!a.confirm){n.next=3;break}return n.next=3,t.performDelete(e);case 3:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()});case 1:case"end":return n.stop()}}),n)})))()},performDelete:function(t){var n=this;return(0,c.default)(r.default.mark((function i(){var s;return r.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return a.showLoading({title:"删除中...",mask:!0}),i.prev=1,i.next=4,e.callFunction({name:"feedback-workflow",data:{action:"delete",id:t._id}});case 4:if(s=i.sent,0!==s.result.code){i.next=12;break}return a.showToast({title:"删除成功",icon:"success"}),a.$emit("feedback-updated",{action:"delete",id:t._id,timestamp:Date.now()}),i.next=10,n.loadFeedbackList();case 10:i.next=13;break;case 12:throw new Error(s.result.message||"删除失败");case 13:i.next=18;break;case 15:i.prev=15,i.t0=i["catch"](1),a.showToast({title:i.t0.message||"删除失败，请重试",icon:"none"});case 18:return i.prev=18,a.hideLoading(),i.finish(18);case 21:case"end":return i.stop()}}),i,null,[[1,15,18,21]])})))()}},(0,o.default)(s,"onPageChange",(function(e){this.currentPage=e.current,this.loadFeedbackList()})),(0,o.default)(s,"formatTime",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!e)return"-";if(t&&t.createTimeFormatted&&e===t.createTime)return t.createTimeFormatted;if("string"===typeof e&&/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(e))return e;try{var n=new Date(e);if(isNaN(n.getTime()))return"-";var a=n.getFullYear(),i=String(n.getMonth()+1).padStart(2,"0"),s=String(n.getDate()).padStart(2,"0"),r=String(n.getHours()).padStart(2,"0"),o=String(n.getMinutes()).padStart(2,"0"),u=String(n.getSeconds()).padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(s," ").concat(r,":").concat(o,":").concat(u)}catch(c){return console.error("时间格式化失败:",c,e),"-"}})),(0,o.default)(s,"toggleExpand",(function(e){this.$set(this.feedbackList[e],"isExpanded",!this.feedbackList[e].isExpanded)})),(0,o.default)(s,"showStatusPicker",(function(){this.$refs.statusPopup.open()})),(0,o.default)(s,"closeStatusPicker",(function(){this.$refs.statusPopup.close()})),(0,o.default)(s,"selectStatus",(function(e){this.searchParams.status=e,this.currentPage=1,this.loadFeedbackList(),this.closeStatusPicker()})),(0,o.default)(s,"showUrgencyPicker",(function(){this.$refs.urgencyPopup.open()})),(0,o.default)(s,"closeUrgencyPicker",(function(){this.$refs.urgencyPopup.close()})),(0,o.default)(s,"selectUrgency",(function(e){this.searchParams.urgency=e,this.currentPage=1,this.loadFeedbackList(),this.closeUrgencyPicker()})),(0,o.default)(s,"showProjectPicker",(function(){this.$refs.projectPopup.open()})),(0,o.default)(s,"closeProjectPicker",(function(){this.$refs.projectPopup.close()})),(0,o.default)(s,"selectProject",(function(e){this.searchParams.project=e,this.currentPage=1,this.loadFeedbackList(),this.closeProjectPicker()})),(0,o.default)(s,"showResponsiblePicker",(function(){this.$refs.responsiblePopup.open()})),(0,o.default)(s,"closeResponsiblePicker",(function(){this.$refs.responsiblePopup.close()})),(0,o.default)(s,"selectResponsible",(function(e){this.searchParams.responsible=e,this.currentPage=1,this.loadFeedbackList(),this.closeResponsiblePicker()})),(0,o.default)(s,"getStatusText",(function(e){if(!e)return"";var t=this.statusOptions.find((function(t){return t.value===e}));return t?t.text:""})),(0,o.default)(s,"getUrgencyText",(function(e){if(!e)return"";var t=this.urgencyOptions.find((function(t){return t.value===e}));return t?t.text:""})),(0,o.default)(s,"getProjectText",(function(e){if(!e)return"";var t=this.projectOptions.find((function(t){return t.value===e}));return t?t.text:""})),(0,o.default)(s,"getResponsibleText",(function(e){if(!e)return"";var t=this.responsibleOptions.find((function(t){return t.value===e}));return t?t.text:""})),(0,o.default)(s,"getReasonDisplay",(function(e){e.workflowStatus;var t=[];if(e.actionHistory&&e.actionHistory.length>0){var n=e.actionHistory.filter((function(e){return["supervisor_approve","supervisor_reject","supervisor_meeting","pm_approve","pm_reject","gm_approve","gm_reject"].includes(e.action)}));n.sort((function(e,t){return e.timestamp-t.timestamp})).forEach((function(e){var n={supervisor_approve:"主管",supervisor_reject:"主管",supervisor_meeting:"主管",pm_approve:"副厂长",pm_reject:"副厂长",gm_approve:"厂长",gm_reject:"厂长"}[e.action];n&&e.reason&&t.push("".concat(n,"：").concat(e.reason))}))}return t.length>0?t.join("\n"):"-"})),(0,o.default)(s,"formatReasonText",(function(e){if(!e||"-"===e)return"";var t=e.split("\n"),n=t.map((function(e){return'<div class="reason-item">'.concat(e,"</div>")}));return n.join("")})),(0,o.default)(s,"shouldRefreshOnCrossDeviceUpdate",(function(e){if(!this.isPageVisible)return this.needsRefreshOnShow=!0,!1;var t=Date.now()-(this.lastRefreshTime||0);if(t<3e3&&e.updateTypes){var n=["feedback_submitted","feedback_deleted","workflow_status_changed"],a=e.updateTypes.some((function(e){return n.includes(e)}));if(!a)return!1}if(e.updateTypes&&e.updateTypes.length>0){var i=["workflow_status_changed","feedback_submitted","feedback_deleted"],s=e.updateTypes.some((function(e){return i.includes(e)}));if(s)return!0}return e.updateCount>2||(!e.updateTypes||0===e.updateTypes.length)})),s),beforeDestroy:function(){a.$off("cross-device-update-detected")}});t.default=f}).call(this,n("861b")["uniCloud"],n("df3c")["default"])},c061:function(e,t,n){"use strict";var a=n("1353"),i=n.n(a);i.a},ec54:function(e,t,n){"use strict";(function(e,t){var a=n("47a9");n("357b"),n("861b");a(n("3240"));var i=a(n("726d"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(i.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])}},[["ec54","common/runtime","common/vendor"]]]);