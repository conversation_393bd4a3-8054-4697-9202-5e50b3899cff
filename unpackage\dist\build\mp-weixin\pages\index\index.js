(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/index/index"],{"2d87":function(e,t,a){"use strict";a.r(t);var n=a("c681"),r=a("607a");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("3eba");var i=a("828b"),s=Object(i["a"])(r["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);t["default"]=s.exports},"3eba":function(e,t,a){"use strict";var n=a("5527"),r=a.n(n);r.a},5527:function(e,t,a){},"607a":function(e,t,a){"use strict";a.r(t);var n=a("af63"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},af63:function(e,t,a){"use strict";(function(e,n){var r=a("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(a("7eb4")),i=r(a("7ca3")),s=r(a("ee10")),c=r(a("af34"));function u(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function l(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?u(Object(a),!0).forEach((function(t){(0,i.default)(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}var f={data:function(){return{ready:!1,formData:{name:"",project:"",description:""},images:[],uploadedFileIDs:[],projectOptions:["安全找茬","设备找茬","其他找茬"]}},onLoad:function(){this.getUserInfo()},onShow:function(){this.getUserInfo()},onReady:function(){var e=this;this.$nextTick((function(){setTimeout((function(){e.ready=!0}),50)}))},methods:{getUserInfo:function(){try{var t=e.getStorageSync("uni-id-pages-userInfo")||{},a=e.getStorageSync("uni_id_token")||"";if(!a)return void(this.formData.name="");var n=t.nickname||t.username||"";n&&!n.startsWith("匿名")&&"admin"!==t.username?this.formData.name=n:this.formData.name=""}catch(r){this.formData.name=""}},bindProjectChange:function(e){this.formData.project=this.projectOptions[e.detail.value]},chooseImage:function(){var t=this;e.chooseImage({count:5-this.images.length,sizeType:["compressed"],sourceType:["album","camera"],success:function(e){var a=e.tempFilePaths.map((function(t,a){return{url:t,progress:0,fileID:"",name:e.tempFiles&&e.tempFiles[a].name?e.tempFiles[a].name:t.substring(t.lastIndexOf("/")+1)}}));t.images=[].concat((0,c.default)(t.images),(0,c.default)(a)),a.forEach((function(e,n){var r=t.images.length-a.length+n;t.uploadImage(e.url,r)}))}})},uploadImage:function(t,a){var r=this;this.$set(this.images[a],"progress",0);var o=0,i=setInterval((function(){o<40?o+=10*Math.random():o<70?o+=5*Math.random():o<95&&(o+=1.5*Math.random()),o>95&&(o=95,clearInterval(i)),r.$set(r.images[a],"progress",Math.floor(o))}),300),s=this.images[a],c=s.name||t.substring(t.lastIndexOf("/")+1),u=c.includes(".")?c.substring(c.lastIndexOf(".")):".jpg",l=c.replace(/\.[^/.]+$/,"").replace(/[^a-zA-Z0-9]/g,"_"),f="".concat(Date.now(),"_").concat(l).concat(u),d=new Date,m=d.getFullYear(),p=String(d.getMonth()+1).padStart(2,"0"),g=String(d.getDate()).padStart(2,"0"),h="".concat(m).concat(p).concat(g),v=n.uploadFile({filePath:t,cloudPath:"feedback/".concat(h,"/").concat(f),cloudPathAsRealPath:!0,onUploadProgress:function(e){if(e.totalBytesSent&&e.totalBytesExpectedToSend){var t=Math.round(e.totalBytesSent/e.totalBytesExpectedToSend*100);t>95&&(clearInterval(i),r.$set(r.images[a],"progress",t))}}});v.then((function(t){clearInterval(i),r.$set(r.images[a],"fileID",t.fileID),r.$set(r.images[a],"progress",100),r.uploadedFileIDs.push(t.fileID),e.showToast({title:"上传成功",icon:"success",duration:1e3})})).catch((function(t){clearInterval(i),r.$set(r.images[a],"progress",void 0),e.showToast({title:"图片上传失败",icon:"none"})}))},previewImages:function(t){var a=this.images.map((function(e){return e.url||e}));e.previewImage({urls:a,current:t})},deleteImage:function(t){var a=this,r=this.images[t],o=r.fileID||r;o&&"string"===typeof o&&(o.startsWith("cloud://")||o.startsWith("https://"))?(e.showLoading({title:"删除中...",mask:!0}),n.callFunction({name:"delete-file",data:{fileList:[o]}}).then((function(n){var r=a.uploadedFileIDs.indexOf(o);-1!==r&&a.uploadedFileIDs.splice(r,1),a.images.splice(t,1),e.showToast({title:"删除成功",icon:"success"})})).catch((function(n){a.images.splice(t,1),e.showToast({title:"已从列表移除",icon:"success"})})).finally((function(){e.hideLoading()}))):(this.images.splice(t,1),e.showToast({title:"已移除",icon:"success"}))},submitForm:function(){var t=this;return(0,s.default)(o.default.mark((function a(){var r,i,s,c,u,f;return o.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t.formData.name&&t.formData.project&&t.formData.description){a.next=3;break}return e.showToast({title:"请填写完整信息",icon:"none"}),a.abrupt("return");case 3:if(e.showLoading({title:"提交中..."}),a.prev=4,r=t.images.every((function(e){return!e.progress||100===e.progress})),r){a.next=8;break}throw new Error("请等待图片上传完成");case 8:return i=t.images.map((function(e){return e.fileID||e})).filter((function(e){return e&&"string"===typeof e})),a.next=11,n.callFunction({name:"feedback-workflow",data:l(l({action:"submit"},t.formData),{},{images:i})});case 11:if(s=a.sent,e.hideLoading(),0!==s.result.code){a.next=24;break}e.showToast({title:"提交成功",icon:"success"}),u=getApp(),u&&u.todoBadgeManager&&u.todoBadgeManager.updateTodoCountImmediately(),e.$emit("feedback-submitted",{id:null===(c=s.result.data)||void 0===c?void 0:c.id,timestamp:Date.now()}),f=t.formData.name,t.formData={name:f,project:"",description:""},t.images=[],t.uploadedFileIDs=[],a.next=25;break;case 24:throw new Error(s.result.message||"提交失败");case 25:a.next=31;break;case 27:a.prev=27,a.t0=a["catch"](4),e.hideLoading(),e.showToast({title:a.t0.message||"提交失败，请重试",icon:"none"});case 31:case"end":return a.stop()}}),a,null,[[4,27]])})))()}}};t.default=f}).call(this,a("df3c")["default"],a("861b")["uniCloud"])},b129:function(e,t,a){"use strict";(function(e,t){var n=a("47a9");a("357b"),a("861b");n(a("3240"));var r=n(a("2d87"));e.__webpack_require_UNI_MP_PLUGIN__=a,t(r.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},c681:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){}));var n=function(){var e=this.$createElement,t=(this._self._c,this.images.length);this.$mp.data=Object.assign({},{$root:{g0:t}})},r=[]}},[["b129","common/runtime","common/vendor"]]]);