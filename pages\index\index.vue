<template>
	<view class="content">
		<view class="form-container">
			<view class="form-item">
				<text class="label">姓名：</text>
				<input class="input" v-model="formData.name" :placeholder="ready ? '请输入您的姓名' : ''" placeholder-class="ph" />
			</view>
			
			<view class="form-item">
				<text class="label">找茬项目：</text>
				<picker class="picker" mode="selector" :range="projectOptions" @change="bindProjectChange">
					<view :class="formData.project ? 'picker-text-selected' : 'picker-text'">{{formData.project || '请选择找茬项目'}}</view>
				</picker>
			</view>
			
			<view class="form-item">
				<text class="label">问题描述：</text>
				<textarea class="textarea" v-model="formData.description" :placeholder="ready ? '请描述您发现的问题' : ''" placeholder-class="ph" />
			</view>
			
			<view class="form-item">
				<text class="label">上传图片（最多5张）：</text>
				<view class="image-uploader">
					<view class="image-preview" v-for="(image, index) in images" :key="index">
						<image class="preview-image" :src="image.url || image" mode="aspectFill" @click="previewImages(index)"></image>
						<view class="delete-btn" @click="deleteImage(index)">×</view>
						<!-- 上传进度显示 -->
						<view class="upload-progress" v-if="image.progress !== undefined && image.progress < 100">
							<view class="progress-text">{{image.progress}}%</view>
						</view>
					</view>
					<view class="upload-btn" @click="chooseImage" v-if="images.length < 5">
						<text class="plus-icon">+</text>
					</view>
				</view>
			</view>
			
			<button class="submit-btn" @click="submitForm">提交反馈</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				ready: false,
				formData: {
					name: '',
					project: '',
					description: ''
				},
				images: [],
				uploadedFileIDs: [], // 存储已上传文件的fileID
				projectOptions: ['安全找茬', '设备找茬', '其他找茬']
			}
		},
		onLoad() {
			// 页面加载时获取用户信息
			this.getUserInfo();
		},
		onShow() {
			// 页面每次显示时也获取用户信息，确保自动填充
			this.getUserInfo();
		},
		onReady() {
			// 页面渲染完成后再显示 placeholder，避免首帧竖排闪现
			this.$nextTick(() => {
				setTimeout(() => {
					this.ready = true;
				}, 50);
			});
		},
		methods: {
			
			getUserInfo() {
				try {
					const userInfo = uni.getStorageSync('uni-id-pages-userInfo') || {};
					const token = uni.getStorageSync('uni_id_token') || '';
					if (!token) {
						this.formData.name = '';
						return;
					}
					const name = userInfo.nickname || userInfo.username || '';
					if (name && !name.startsWith('匿名') && userInfo.username !== 'admin') {
						this.formData.name = name;
					} else {
						this.formData.name = '';
					}
				} catch (e) {
					this.formData.name = '';
				}
			},
			bindProjectChange(e) {
				this.formData.project = this.projectOptions[e.detail.value]
			},
			chooseImage() {
				uni.chooseImage({
					count: 5 - this.images.length,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						const newImages = res.tempFilePaths.map((path, idx) => {
							return {
								url: path,
								progress: 0,
								fileID: '',
								name: res.tempFiles && res.tempFiles[idx].name 
									? res.tempFiles[idx].name 
									: path.substring(path.lastIndexOf('/') + 1)
							}
						});
						
						this.images = [...this.images, ...newImages];
						
						newImages.forEach((image, index) => {
							const totalIndex = this.images.length - newImages.length + index;
							this.uploadImage(image.url, totalIndex);
						});
					}
				})
			},
			uploadImage(filePath, index) {
				this.$set(this.images[index], 'progress', 0);
				
				let simulatedProgress = 0;
				const progressInterval = setInterval(() => {
					if (simulatedProgress < 40) {
						simulatedProgress += Math.random() * 10;
					} else if (simulatedProgress < 70) {
						simulatedProgress += Math.random() * 5;
					} else if (simulatedProgress < 95) {
						simulatedProgress += Math.random() * 1.5;
					}
					
					if (simulatedProgress > 95) {
						simulatedProgress = 95;
						clearInterval(progressInterval);
					}
					
					this.$set(this.images[index], 'progress', Math.floor(simulatedProgress));
				}, 300);
				
				
				const imageObj = this.images[index];
				const fileName = imageObj.name || filePath.substring(filePath.lastIndexOf('/') + 1);
				const fileExt = fileName.includes('.') ? fileName.substring(fileName.lastIndexOf('.')) : '.jpg';
				const safeFileName = fileName.replace(/\.[^/.]+$/, "").replace(/[^a-zA-Z0-9]/g, '_');
				const uniqueFileName = `${Date.now()}_${safeFileName}${fileExt}`;
				
				const now = new Date();
				const year = now.getFullYear();
				const month = String(now.getMonth() + 1).padStart(2, '0');
				const day = String(now.getDate()).padStart(2, '0');
				const dateFolder = `${year}${month}${day}`;
				
				
				const uploadTask = uniCloud.uploadFile({
					filePath: filePath,
					cloudPath: `feedback/${dateFolder}/${uniqueFileName}`,
					cloudPathAsRealPath: true,
					onUploadProgress: (progressEvent) => {
						if (progressEvent.totalBytesSent && progressEvent.totalBytesExpectedToSend) {
							const realProgress = Math.round(progressEvent.totalBytesSent / progressEvent.totalBytesExpectedToSend * 100);
							if (realProgress > 95) {
								clearInterval(progressInterval);
								this.$set(this.images[index], 'progress', realProgress);
							}
						}
					}
				});
				
				uploadTask.then(res => {
					clearInterval(progressInterval);
					this.$set(this.images[index], 'fileID', res.fileID);
					this.$set(this.images[index], 'progress', 100);
					this.uploadedFileIDs.push(res.fileID);
					
					uni.showToast({
						title: '上传成功',
						icon: 'success',
						duration: 1000
					});
				}).catch(err => {
					clearInterval(progressInterval);
					this.$set(this.images[index], 'progress', undefined);
					uni.showToast({
						title: '图片上传失败',
						icon: 'none'
					});
				});
			},
			previewImages(index) {
				const urls = this.images.map(img => img.url || img);
				uni.previewImage({
					urls: urls,
					current: index
				});
			},
			deleteImage(index) {
				const image = this.images[index];
				const fileID = image.fileID || image;
				
				if (fileID && typeof fileID === 'string' && (fileID.startsWith('cloud://') || fileID.startsWith('https://'))) {
					uni.showLoading({
						title: '删除中...',
						mask: true
					});
					
					uniCloud.callFunction({
						name: 'delete-file',
						data: {
							fileList: [fileID]
						}
					}).then(res => {
						const idIndex = this.uploadedFileIDs.indexOf(fileID);
						if (idIndex !== -1) {
							this.uploadedFileIDs.splice(idIndex, 1);
						}
						this.images.splice(index, 1);
						
						uni.showToast({
							title: '删除成功',
							icon: 'success'
						});
					}).catch(err => {
						this.images.splice(index, 1);
						uni.showToast({
							title: '已从列表移除',
							icon: 'success'
						});
					}).finally(() => {
						uni.hideLoading();
					});
				} else {
					this.images.splice(index, 1);
					uni.showToast({
						title: '已移除',
						icon: 'success'
					});
				}
			},
			async submitForm() {
				if (!this.formData.name || !this.formData.project || !this.formData.description) {
					uni.showToast({
						title: '请填写完整信息',
						icon: 'none'
					})
					return
				}

				uni.showLoading({
					title: '提交中...'
				})

				try {
					const allUploaded = this.images.every(img => !img.progress || img.progress === 100);
					if (!allUploaded) {
						throw new Error('请等待图片上传完成');
					}
					
					const uploadedImages = this.images.map(img => img.fileID || img).filter(id => id && typeof id === 'string');

					const res = await uniCloud.callFunction({
						name: 'feedback-workflow',
						data: {
							action: 'submit',
							...this.formData,
							images: uploadedImages
						}
					})

					uni.hideLoading()

					if (res.result.code === 0) {
						uni.showToast({
							title: '提交成功',
							icon: 'success'
						})

						// 更新角标
						const app = getApp();
						if (app && app.todoBadgeManager) {
							app.todoBadgeManager.updateTodoCountImmediately();
						}

						// 发送全局事件通知列表页刷新数据
						uni.$emit('feedback-submitted', {
							id: res.result.data?.id,
							timestamp: Date.now()
						});

						// 清空表单（保留用户名）
						const currentName = this.formData.name; // 保存当前用户名
						this.formData = {
							name: currentName, // 保持用户名不变
							project: '',
							description: ''
						}
						this.images = []
						this.uploadedFileIDs = []
					} else {
						throw new Error(res.result.message || '提交失败')
					}
				} catch (err) {
					uni.hideLoading()
					uni.showToast({
						title: err.message || '提交失败，请重试',
						icon: 'none'
					})
				}
			}
		}
	}
</script>

<style>
	/* placeholder 样式优化 */
	.ph {
		color: #A0AEC0;
		font-size: 15px;
		line-height: 1.5;
		text-align: left;
	}
	
	.content {
    	padding: 20px;
		box-sizing: border-box;
		background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		overflow: hidden;
		min-height: 100vh;
  	}
	
  .form-container {
		width: 100%;
		max-width: 90%;
		margin: 0 auto;
		padding: 40rpx;
		background-color: #fff;
		border-radius: 20rpx;
		box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
	}
  
	.form-item {
		margin-bottom: 30px;
		position: relative;
	}
	
	.label {
		display: block;
		margin-bottom: 12px;
		font-size: 16px;
		color: #2d3748;
		font-weight: 600;
		position: relative;
		padding-left: 12px;
	}
	
	.label::before {
		content: '';
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 4px;
		height: 16px;
		background: #4299e1;
		border-radius: 2px;
	}
	
	.input, .textarea, .picker {
		width: 100%;
		padding: 14px;
		border: 2px solid #e2e8f0;
		border-radius: 12px;
		box-sizing: border-box;
		background-color: #f8fafc;
		transition: all 0.3s ease;
		font-size: 15px;
		color: #4a5568;
		line-height: 1.5;
		height: auto;
		min-height: 48px;
		vertical-align: middle;
	}
	
	.input:hover, .textarea:hover, .picker:hover {
		border-color: #cbd5e0;
		background-color: #f7fafc;
	}
	
	.input:focus, .textarea:focus {
		border-color: #4299e1;
		box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.15);
		background-color: #fff;
		outline: none;
	}
	
	.textarea {
		height: 120px;
		resize: none;
		line-height: 1.6;
	}
	
	.picker-text {
		color: #A0AEC0; /* 与 placeholder 保持一致的颜色 */
		font-size: 15px;
		line-height: 1.5; /* 与 placeholder 保持一致的行高 */
	}

	.picker-text-selected {
		color: #4a5568; /* 已选择状态的正常文字颜色，与输入框文字颜色一致 */
		font-size: 15px;
		line-height: 1.5;
	}
	
	.image-uploader {
		display: flex;
		flex-wrap: wrap;
		gap: 20px;
	}
	
	.image-preview {
		position: relative;
		width: 100px;
		height: 100px;
		border-radius: 12px;
		overflow: hidden;
	}
	
	.preview-image {
		width: 100%;
		height: 100%;
	}
	
	.delete-btn {
		position: absolute;
		right: 5px;
		top: 5px;
		width: 20px;
		height: 20px;
		background-color: rgba(0, 0, 0, 0.5);
		color: #fff;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 16px;
		z-index: 10;
	}
	
	.upload-btn {
		width: 100px;
		height: 100px;
		border: 2px dashed #e2e8f0;
		border-radius: 12px;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #f8fafc;
	}
	
	.plus-icon {
		font-size: 60px;
		color: #718096;
	}
	
	.upload-text {
		font-size: 12px;
		color: #718096;
	}
	
	.upload-progress {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.6);
		backdrop-filter: blur(2px);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}
	
	.progress-text {
		color: #fff;
		font-size: 14px;
		font-weight: 600;
		text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
	}
	
	.submit-btn {
		background: linear-gradient(135deg, #67B7E6 0%, #4A9FD1 100%);
		color: #fff;
		font-size: 17px;
		padding: 16px;
		border-radius: 12px;
		margin-top: 30px;
		width: 100%;
		font-weight: 600;
		border: none;
		box-shadow: 0 4px 10px rgba(74, 159, 209, 0.25);
		transition: all 0.3s ease;
		letter-spacing: 0.5px;
		position: relative;
		overflow: hidden;
	}
	
	.submit-btn::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(
			to right, 
			rgba(255, 255, 255, 0) 0%, 
			rgba(255, 255, 255, 0.1) 20%, 
			rgba(255, 255, 255, 0.15) 50%, 
			rgba(255, 255, 255, 0.1) 80%, 
			rgba(255, 255, 255, 0) 100%
		);
		transform: skewX(-15deg);
		animation: shimmer 5s ease-in-out infinite;
	}
	
	@keyframes shimmer {
		0% { left: -100%; }
		45% { left: 100%; }
		100% { left: 100%; }
	}
	
	.submit-btn:hover {
		transform: translateY(-2px);
		box-shadow: 0 6px 15px rgba(74, 159, 209, 0.35);
		background: linear-gradient(135deg, #78C3EE 0%, #5AAAD9 100%);
	}
	
	.submit-btn:active {
		transform: translateY(0);
		box-shadow: 0 4px 6px rgba(28, 124, 184, 0.25);
	}
</style>
