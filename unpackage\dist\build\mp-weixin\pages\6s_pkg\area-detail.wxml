<view class="page-container data-v-76f23d42"><block wx:if="{{loading}}"><view class="loading-container data-v-76f23d42"><view class="loading-content data-v-76f23d42"><view class="loading-spinner data-v-76f23d42"></view><text class="loading-text data-v-76f23d42">加载区域详情中...</text></view></view></block><block wx:else><block wx:if="{{loadError}}"><view class="error-container data-v-76f23d42"><p-empty-state vue-id="61600ee1-1" type="error" title="加载失败" description="网络异常，请检查网络连接" show-button="{{true}}" button-text="重新加载" data-event-opts="{{[['^buttonClick',[['loadPageData']]]]}}" bind:buttonClick="__e" class="data-v-76f23d42" bind:__l="__l"></p-empty-state></view></block><block wx:else><view class="card data-v-76f23d42"><view class="card-header data-v-76f23d42"><view class="header-content data-v-76f23d42"><view class="card-title data-v-76f23d42">{{areaInfo.name}}</view><view class="card-subtitle data-v-76f23d42">{{$root.m0}}</view></view><view class="{{['status-badge','data-v-76f23d42','status-'+areaInfo.status]}}">{{''+$root.m1+''}}</view></view></view><view class="card data-v-76f23d42"><view class="card-header data-v-76f23d42"><view class="card-title data-v-76f23d42">基本信息</view></view><view class="card-body data-v-76f23d42"><view class="info-list data-v-76f23d42"><view class="info-item data-v-76f23d42"><view class="info-label data-v-76f23d42"><uni-icons vue-id="61600ee1-2" type="location" size="16" color="#8E8E93" class="data-v-76f23d42" bind:__l="__l"></uni-icons><text class="data-v-76f23d42">责任区位置</text></view><view class="info-value data-v-76f23d42">{{areaInfo.location}}</view></view><view class="info-item data-v-76f23d42"><view class="info-label data-v-76f23d42"><uni-icons vue-id="61600ee1-3" type="person" size="16" color="#8E8E93" class="data-v-76f23d42" bind:__l="__l"></uni-icons><text class="data-v-76f23d42">负责人</text></view><view class="info-value data-v-76f23d42">{{areaInfo.assignedEmployee}}</view></view><view class="info-item data-v-76f23d42"><view class="info-label data-v-76f23d42"><uni-icons vue-id="61600ee1-4" type="reload" size="16" color="#8E8E93" class="data-v-76f23d42" bind:__l="__l"></uni-icons><text class="data-v-76f23d42">清理频率</text></view><view class="info-value data-v-76f23d42">{{areaInfo.cleaningFrequency}}</view></view><view class="info-item data-v-76f23d42"><view class="info-label data-v-76f23d42"><uni-icons vue-id="61600ee1-5" type="calendar" size="16" color="#8E8E93" class="data-v-76f23d42" bind:__l="__l"></uni-icons><text class="data-v-76f23d42">下次清理</text></view><view class="info-value data-v-76f23d42">{{areaInfo.nextCleaningTime}}</view></view></view><block wx:if="{{areaInfo.description}}"><view class="description-section data-v-76f23d42"><view class="description-title data-v-76f23d42">区域说明</view><view class="description-text data-v-76f23d42">{{areaInfo.description}}</view></view></block></view></view><view class="card data-v-76f23d42"><view class="card-header data-v-76f23d42"><view class="card-title data-v-76f23d42">{{isHistoricalView?'历史状态':'本周状态'}}</view><view class="card-subtitle data-v-76f23d42">{{currentWeekText}}</view></view><view class="card-body data-v-76f23d42"><block wx:if="{{currentWeekRecord}}"><view class="week-status completed data-v-76f23d42"><view class="status-icon data-v-76f23d42"><uni-icons vue-id="61600ee1-6" type="checkmarkempty" size="24" color="#34C759" class="data-v-76f23d42" bind:__l="__l"></uni-icons></view><view class="status-content data-v-76f23d42"><view class="status-title data-v-76f23d42">{{isHistoricalView?'已完成清理':'本周已完成清理'}}</view><view class="status-desc data-v-76f23d42">{{currentWeekRecord.submitTime}}</view><view data-event-opts="{{[['tap',[['viewWeekPhotos',['$event']]]]]}}" class="status-photos data-v-76f23d42" bindtap="__e"><text class="data-v-76f23d42">{{"查看清理照片 ("+$root.g0+"张)"}}</text><uni-icons vue-id="61600ee1-7" type="right" size="14" color="#007AFF" class="data-v-76f23d42" bind:__l="__l"></uni-icons></view></view></view></block><block wx:else><view class="week-status pending data-v-76f23d42"><view class="status-icon data-v-76f23d42"><uni-icons vue-id="61600ee1-8" type="clock" size="24" color="#FF9500" class="data-v-76f23d42" bind:__l="__l"></uni-icons></view><view class="status-content data-v-76f23d42"><view class="status-title data-v-76f23d42">{{isHistoricalView?'当时未清理':'本周待清理'}}</view><view class="status-desc data-v-76f23d42">{{isHistoricalView?'该时间段内未进行清理':$root.m2}}</view></view></view></block></view></view><block wx:if="{{!isHistoricalView&&!currentWeekRecord&&areaInfo.canClean}}"><view class="action-section data-v-76f23d42"><view data-event-opts="{{[['tap',[['goToCleaning',['$event']]]]]}}" class="action-btn primary data-v-76f23d42" bindtap="__e"><uni-icons vue-id="61600ee1-9" type="camera" size="20" color="white" class="data-v-76f23d42" bind:__l="__l"></uni-icons><text class="data-v-76f23d42">立即清理</text></view></view></block><view class="card data-v-76f23d42"><view class="card-header data-v-76f23d42"><view class="header-content data-v-76f23d42"><view class="card-title data-v-76f23d42">清理历史</view><view class="card-subtitle data-v-76f23d42">最近的清理记录</view></view><view data-event-opts="{{[['tap',[['showTimeFilter',['$event']]]]]}}" class="time-filter data-v-76f23d42" bindtap="__e"><text class="data-v-76f23d42">{{selectedTimeFilter}}</text><uni-icons vue-id="61600ee1-10" type="down" size="12" color="#007AFF" class="data-v-76f23d42" bind:__l="__l"></uni-icons></view></view><block wx:if="{{$root.g1===0}}"><p-empty-state vue-id="61600ee1-11" type="data" text="暂无清理记录" description="还没有清理记录，开始第一次清理吧" class="data-v-76f23d42" bind:__l="__l"></p-empty-state></block><block wx:else><view class="history-list data-v-76f23d42"><block wx:for="{{$root.l1}}" wx:for-item="monthGroup" wx:for-index="__i0__" wx:key="month"><view class="month-group data-v-76f23d42"><view data-event-opts="{{[['tap',[['toggleMonth',['$0'],[[['groupedHistory','month',monthGroup.$orig.month,'month']]]]]]]}}" class="month-header data-v-76f23d42" bindtap="__e"><view class="month-info data-v-76f23d42"><text class="month-title data-v-76f23d42">{{"📅 "+monthGroup.$orig.monthText}}</text><text class="month-count data-v-76f23d42">{{"("+monthGroup.g2+"条记录)"}}</text></view><uni-icons vue-id="{{'61600ee1-12-'+__i0__}}" type="{{monthGroup.g3?'down':'right'}}" size="16" color="#8E8E93" class="data-v-76f23d42" bind:__l="__l"></uni-icons></view><block wx:if="{{monthGroup.g4}}"><view class="month-records data-v-76f23d42"><block wx:for="{{monthGroup.l0}}" wx:for-item="record" wx:for-index="recordIndex" wx:key="recordIndex"><view data-event-opts="{{[['tap',[['viewRecordDetail',['$0'],[[['groupedHistory','month',monthGroup.$orig.month],['records','',recordIndex]]]]]]]}}" class="history-item data-v-76f23d42" bindtap="__e"><view class="{{['history-icon','data-v-76f23d42','icon-bg-'+record.$orig.status]}}"><uni-icons vue-id="{{'61600ee1-13-'+__i0__+'-'+recordIndex}}" type="{{record.$orig.icon}}" size="16" color="{{record.m3}}" class="data-v-76f23d42" bind:__l="__l"></uni-icons></view><view class="history-content data-v-76f23d42"><view class="history-title data-v-76f23d42">{{record.$orig.weekText}}</view><view class="history-subtitle data-v-76f23d42">{{record.$orig.subtitle}}</view></view><view class="history-right data-v-76f23d42"><view class="photo-count data-v-76f23d42">{{record.$orig.photoCount+"张照片"}}</view><uni-icons vue-id="{{'61600ee1-14-'+__i0__+'-'+recordIndex}}" type="right" size="14" color="#C7C7CC" class="data-v-76f23d42" bind:__l="__l"></uni-icons></view></view></block></view></block></view></block></view></block></view><view class="card data-v-76f23d42"><view class="card-header data-v-76f23d42"><view class="card-title data-v-76f23d42">检查记录</view><view class="card-subtitle data-v-76f23d42">管理层抽查记录</view></view><block wx:if="{{$root.g5===0}}"><p-empty-state vue-id="61600ee1-15" type="data" text="暂无检查记录" description="还没有管理层检查记录" class="data-v-76f23d42" bind:__l="__l"></p-empty-state></block><block wx:else><view class="history-list data-v-76f23d42"><block wx:for="{{$root.l2}}" wx:for-item="record" wx:for-index="recordIndex" wx:key="recordIndex"><view data-event-opts="{{[['tap',[['viewInspectionDetail',['$0'],[[['inspectionHistory','',recordIndex]]]]]]]}}" class="history-item data-v-76f23d42" bindtap="__e"><view class="{{['history-icon','data-v-76f23d42','icon-bg-'+record.$orig.result]}}"><uni-icons vue-id="{{'61600ee1-16-'+recordIndex}}" type="{{record.$orig.icon}}" size="18" color="{{record.m4}}" class="data-v-76f23d42" bind:__l="__l"></uni-icons></view><view class="history-content data-v-76f23d42"><view class="history-title data-v-76f23d42">{{record.$orig.inspectorName+" · "+record.$orig.time}}</view><view class="history-subtitle data-v-76f23d42">{{record.$orig.subtitle}}</view></view><view class="history-right data-v-76f23d42"><block wx:if="{{record.$orig.rating}}"><view class="rating-stars data-v-76f23d42"><text class="data-v-76f23d42">{{record.$orig.rating+"/5"}}</text><uni-icons vue-id="{{'61600ee1-17-'+recordIndex}}" type="star-filled" size="12" color="#FFD700" class="data-v-76f23d42" bind:__l="__l"></uni-icons></view></block><block wx:else><block wx:if="{{record.$orig.statusIndicator}}"><view class="status-indicator data-v-76f23d42" style="{{'color:'+(record.$orig.statusIndicator.color)+';'}}"><text class="indicator-text data-v-76f23d42">{{record.$orig.statusIndicator.text}}</text><text class="indicator-desc data-v-76f23d42">{{record.$orig.statusIndicator.desc}}</text></view></block></block><uni-icons vue-id="{{'61600ee1-18-'+recordIndex}}" type="right" size="14" color="#C7C7CC" class="data-v-76f23d42" bind:__l="__l"></uni-icons></view></view></block></view></block></view><view class="bottom-safe-area data-v-76f23d42"></view></block></block></view>