@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container.data-v-2ae3b0bd {
  min-height: 100vh;
  background: #F8F9FA;
  padding-bottom: 40rpx;
}
.header.data-v-2ae3b0bd {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  padding: 60rpx 32rpx 40rpx;
  color: white;
}
.header-title.data-v-2ae3b0bd {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}
.header-subtitle.data-v-2ae3b0bd {
  font-size: 24rpx;
  opacity: 0.8;
}
.stats-card.data-v-2ae3b0bd {
  background: white;
  margin: 24rpx 32rpx;
  padding: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}
.stat-item.data-v-2ae3b0bd {
  text-align: center;
  padding: 24rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
}
.stat-number.data-v-2ae3b0bd {
  font-size: 48rpx;
  font-weight: 600;
  color: #007AFF;
  margin-bottom: 8rpx;
}
.stat-label.data-v-2ae3b0bd {
  font-size: 24rpx;
  color: #8E8E93;
}
.action-bar.data-v-2ae3b0bd {
  padding: 0 32rpx 24rpx;
  display: flex;
  gap: 16rpx;
}
.action-btn.data-v-2ae3b0bd {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 26rpx;
  border: none;
  transition: all 0.3s ease;
}
.action-btn.primary.data-v-2ae3b0bd {
  background: #007AFF;
  color: white;
}
.action-btn.secondary.data-v-2ae3b0bd {
  background: white;
  color: #007AFF;
  border: 2rpx solid #007AFF;
}
.action-btn.data-v-2ae3b0bd:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.area-list.data-v-2ae3b0bd {
  padding: 0 32rpx;
}
.area-item.data-v-2ae3b0bd {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.area-main.data-v-2ae3b0bd {
  flex: 1;
}
.area-header.data-v-2ae3b0bd {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.area-name.data-v-2ae3b0bd {
  font-size: 30rpx;
  font-weight: 600;
  color: #1C1C1E;
}
.area-status.data-v-2ae3b0bd {
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}
.area-status.status-active.data-v-2ae3b0bd {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}
.area-status.status-inactive.data-v-2ae3b0bd {
  background: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}
.area-info.data-v-2ae3b0bd {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 12rpx;
}
.info-item.data-v-2ae3b0bd {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  color: #8E8E93;
}
.unscheduled-text.data-v-2ae3b0bd {
  color: #C7C7CC;
}
.area-description.data-v-2ae3b0bd {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 12rpx;
}
.area-schedule.data-v-2ae3b0bd {
  background: #F8F9FA;
  border-radius: 8rpx;
  padding: 12rpx;
}
.schedule-info.data-v-2ae3b0bd {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.next-clean.data-v-2ae3b0bd, .last-clean.data-v-2ae3b0bd {
  display: flex;
  align-items: center;
  gap: 6rpx;
}
.next-label.data-v-2ae3b0bd, .last-label.data-v-2ae3b0bd {
  font-size: 22rpx;
  color: #8E8E93;
}
.next-date.data-v-2ae3b0bd, .last-date.data-v-2ae3b0bd {
  font-size: 22rpx;
  font-weight: 500;
  color: #34C759;
}
.next-date.today.data-v-2ae3b0bd {
  color: #FF3B30;
  font-weight: 600;
}
.no-schedule-info.data-v-2ae3b0bd {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 22rpx;
  color: #C7C7CC;
  font-style: italic;
}
.area-actions.data-v-2ae3b0bd {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.action-icon-btn.data-v-2ae3b0bd {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  transition: all 0.3s ease;
}
.action-icon-btn.schedule.data-v-2ae3b0bd {
  background: rgba(52, 199, 89, 0.1);
}
.action-icon-btn.edit.data-v-2ae3b0bd {
  background: rgba(0, 122, 255, 0.1);
}
.action-icon-btn.delete.data-v-2ae3b0bd {
  background: rgba(255, 59, 48, 0.1);
}
.action-icon-btn.data-v-2ae3b0bd:active {
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}
.form-popup.data-v-2ae3b0bd {
  width: 92vw;
  max-width: 550rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}
.form-header.data-v-2ae3b0bd {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
}
.form-title.data-v-2ae3b0bd {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-left: 5rpx;
}
.close-btn.data-v-2ae3b0bd {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F2F2F7;
  border: none;
  margin-right: 5rpx;
}
.form-content.data-v-2ae3b0bd {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}
/* 微信小程序隐藏滚动条 */
.form-content.data-v-2ae3b0bd::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  background: transparent;
}
.form-content.data-v-2ae3b0bd {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}
.form-item.data-v-2ae3b0bd {
  margin-bottom: 32rpx;
}
.form-label.data-v-2ae3b0bd {
  display: block;
  font-size: 28rpx;
  color: #1C1C1E;
  margin-bottom: 12rpx;
  font-weight: 500;
}
.form-label.required.data-v-2ae3b0bd::before {
  content: "*";
  color: #FF3B30;
  margin-right: 6rpx;
}
.form-input.data-v-2ae3b0bd, .form-textarea.data-v-2ae3b0bd {
  width: 100%;
  background: #F2F2F7;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  box-sizing: border-box;
}
.form-textarea.data-v-2ae3b0bd {
  min-height: 120rpx;
  padding: 16rpx;
  line-height: 1.5;
  resize: none;
}
.form-input.data-v-2ae3b0bd {
  height: 80rpx;
  padding: 0 16rpx;
  line-height: 80rpx;
  text-align: left;
}
/* 强制占位符样式保持一致 */
.form-input.data-v-2ae3b0bd::-webkit-input-placeholder {
  line-height: 80rpx;
  color: #C7C7CC;
}
.form-input.data-v-2ae3b0bd::placeholder {
  line-height: 80rpx;
  color: #C7C7CC;
}
/* 兼容WebKit浏览器 */
.form-input.data-v-2ae3b0bd::-webkit-input-placeholder {
  line-height: 80rpx;
  color: #C7C7CC;
}
/* 兼容Firefox */
.form-input.data-v-2ae3b0bd::-moz-placeholder {
  line-height: 80rpx;
  color: #C7C7CC;
  opacity: 1;
}
/* 兼容IE/Edge */
.form-input.data-v-2ae3b0bd:-ms-input-placeholder {
  line-height: 80rpx;
  color: #C7C7CC;
}
.form-picker.data-v-2ae3b0bd {
  width: 100%;
}
.picker-value.data-v-2ae3b0bd {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 80rpx;
  background: #F2F2F7;
  border-radius: 12rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  box-sizing: border-box;
}
.form-footer.data-v-2ae3b0bd {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #F2F2F7;
}
.form-btn.data-v-2ae3b0bd {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  border: none;
  transition: all 0.3s ease;
}
.form-btn.cancel.data-v-2ae3b0bd {
  background: #F2F2F7;
  color: #8E8E93;
}
.form-btn.submit.data-v-2ae3b0bd {
  background: #007AFF;
  color: white;
}
.form-btn.data-v-2ae3b0bd:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.schedule-popup.data-v-2ae3b0bd {
  width: 92vw;
  max-width: 650rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}
.schedule-header.data-v-2ae3b0bd {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
}
.schedule-title.data-v-2ae3b0bd {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-left: 5rpx;
}
.schedule-content.data-v-2ae3b0bd {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}
/* 微信小程序隐藏滚动条 */
.schedule-content.data-v-2ae3b0bd::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  background: transparent;
}
.schedule-content.data-v-2ae3b0bd {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}
.area-name-display.data-v-2ae3b0bd {
  font-size: 30rpx;
  font-weight: 600;
  color: #007AFF;
  text-align: center;
  margin-bottom: 32rpx;
  padding: 16rpx;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 12rpx;
}
.schedule-item.data-v-2ae3b0bd {
  margin-bottom: 24rpx;
}
.schedule-label.data-v-2ae3b0bd {
  display: block;
  font-size: 28rpx;
  color: #1C1C1E;
  margin-bottom: 12rpx;
  font-weight: 500;
}
.schedule-picker.data-v-2ae3b0bd {
  width: 100%;
}
.schedule-picker-value.data-v-2ae3b0bd {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 80rpx;
  background: #F2F2F7;
  border-radius: 12rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  box-sizing: border-box;
}
.schedule-preview.data-v-2ae3b0bd {
  margin-top: 32rpx;
  padding: 24rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
}
.preview-title.data-v-2ae3b0bd {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 16rpx;
}
.preview-info.data-v-2ae3b0bd {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
}
.next-dates.data-v-2ae3b0bd {
  margin-top: 16rpx;
}
.dates-label.data-v-2ae3b0bd {
  font-size: 24rpx;
  color: #8E8E93;
  display: block;
  margin-bottom: 12rpx;
}
.date-list.data-v-2ae3b0bd {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}
.date-item.data-v-2ae3b0bd {
  background: #007AFF;
  color: white;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}
.date-item.today.data-v-2ae3b0bd {
  background: #FF3B30;
}
.schedule-footer.data-v-2ae3b0bd {
  display: flex;
  gap: 12rpx;
  padding: 32rpx;
  border-top: 1rpx solid #F2F2F7;
}
.schedule-btn.data-v-2ae3b0bd {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  border: none;
  transition: all 0.3s ease;
}
.schedule-btn.cancel.data-v-2ae3b0bd {
  background: #F2F2F7;
  color: #8E8E93;
}
.schedule-btn.clear.data-v-2ae3b0bd {
  background: #FF3B30;
  color: white;
}
.schedule-btn.submit.data-v-2ae3b0bd {
  background: #007AFF;
  color: white;
}
.schedule-btn.data-v-2ae3b0bd:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
/* 加载状态样式 */
.stats-loading.data-v-2ae3b0bd {
  grid-column: 1/-1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}
.list-loading.data-v-2ae3b0bd {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
  background: #F8F9FA;
}
.loading-content.data-v-2ae3b0bd {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}
.loading-text.data-v-2ae3b0bd {
  font-size: 28rpx;
  color: #8E8E93;
}
