<template>
	<view class="page-container">
		<!-- 抽查统计 -->
		<view class="card">
			<view class="card-header">
				<view class="header-content">
					<view class="card-title">抽查统计</view>
					<view class="filters-row">
						<view class="time-selector" @click="showTimeSelector">
							<text class="time-text">{{getCurrentTimeRange()}}</text>
							<uni-icons type="down" size="12" color="#007AFF"></uni-icons>
						</view>
						<view v-if="selectedStatus !== 'all'" class="status-filter-indicator" @click="selectStatus('all')">
							<text class="filter-text">{{getStatusFilterText()}}</text>
							<uni-icons type="close" size="12" color="#8E8E93"></uni-icons>
						</view>
					</view>
				</view>
			</view>
			<!-- 统计数据 -->
			<view v-if="loading" class="stats-loading">
				<view class="loading-spinner"></view>
				<view class="loading-text">加载统计数据中...</view>
			</view>
			<view v-else class="stats-grid stats-grid-six">
				<!-- 按优先级重新排序：检查员工作流程优先 -->
				<view class="stats-item"
					:class="{ active: selectedStatus === 'pending_review' }"
					@click="selectStatus('pending_review')">
					<view class="stats-number review">{{getStatsData().pending_review}}</view>
					<view class="stats-label">待复查</view>
				</view>
				<view class="stats-item"
					:class="{ active: selectedStatus === 'pending' }"
					@click="selectStatus('pending')">
										<view class="stats-number warning">{{getStatsData().pending}}</view>
					<view class="stats-label">待检查</view>
				</view>
				<view class="stats-item"
					:class="{ active: selectedStatus === 'pending_rectification' }"
					@click="selectStatus('pending_rectification')">
					<view class="stats-number danger">{{getStatsData().pending_rectification}}</view>
					<view class="stats-label">待整改</view>
				</view>
				<view class="stats-item"
					:class="{ active: selectedStatus === 'missed' }"
					@click="selectStatus('missed')">
					<view class="stats-number missed">{{getStatsData().missed}}</view>
					<view class="stats-label">漏检查</view>
				</view>
				<view class="stats-item"
					:class="{ active: selectedStatus === 'not_cleaned' }"
					@click="selectStatus('not_cleaned')">
					<view class="stats-number not-cleaned">{{getStatsData().not_cleaned}}</view>
					<view class="stats-label">未打扫</view>
				</view>
				<view class="stats-item"
					:class="{ active: selectedStatus === 'completed' }"
					@click="selectStatus('completed')">
					<view class="stats-number success">{{getStatsData().completed}}</view>
					<view class="stats-label">已完成</view>
				</view>
			</view>
		</view>

		<!-- 检查记录列表 -->
		<view class="card">
			<view class="card-header">
				<view class="header-content">
					<view class="card-title">责任区检查</view>
					<view class="card-subtitle">固定和公共责任区检查任务</view>
				</view>
			</view>
			
			<!-- 内容区域 -->
				<!-- 分类标签 -->
				<scroll-view class="category-scroll" scroll-x="true" show-scrollbar="false">
					<view class="category-tabs">
						<view 
							v-for="category in categoryTabs" 
							:key="category.value"
							class="category-tab"
							:class="{ active: selectedCategory === category.value }"
							@click="selectCategory(category.value)"
						>
							<text class="tab-text">{{category.label}}</text>
							<text class="tab-count">{{getCategoryCount(category.value)}}</text>
						</view>
					</view>
				</scroll-view>
				
				<!-- 加载状态 -->
				<view v-if="loading" class="list-loading">
					<view class="loading-spinner"></view>
					<view class="loading-text">加载数据中...</view>
				</view>
				
				<!-- 时间分组的检查记录列表 -->
				<view v-else-if="groupedFilteredRecords.length > 0">
					<view v-for="group in groupedFilteredRecords" :key="group.weekKey" class="time-group">
						<!-- 时间分组标题 -->
						<view class="time-group-header" @click="toggleWeek(group.weekKey)">
							<view class="time-group-title">
								<uni-icons :type="group.expanded ? 'down' : 'right'" size="16" color="#007AFF"></uni-icons>
								<text class="time-title">{{group.title}}</text>
								<view class="time-count">{{group.records.length}}条</view>
							</view>
						</view>
						
						<!-- 分组记录列表 -->
						<view v-if="group.expanded" class="time-group-content">
							<view v-for="(record, recordIndex) in group.records" :key="recordIndex" 
								class="list-item" 
								@click="handleRecordClick(record)">
								<view class="list-item-icon" :class="['icon-bg-' + record.status]">
									<uni-icons :type="record.icon" size="18" :color="getIconColor(record.status)"></uni-icons>
								</view>
								<view class="list-item-content">
									<view class="list-item-title">
										<text>{{record.areaName}}</text>
										<view class="badges-container">
											<!-- 区域类型标签 -->
											<view class="area-type-badge" :class="['type-' + record.type]">
												<text class="badge-text">{{getAreaTypeText(record.type)}}</text>
											</view>
											<!-- 整改任务标签 -->
											<view v-if="record.isRectificationRecheck" class="rectification-badge">
												<text class="badge-text">整改任务</text>
											</view>
										</view>
									</view>
									<view class="list-item-subtitle">{{record.subtitle}}</view>
								</view>
								<view class="list-item-right">
									<view class="status-badge" :class="['status-' + record.status]">{{getStatusText(record.status)}}</view>
									<uni-icons type="right" size="14" color="#C7C7CC"></uni-icons>
								</view>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 无数据状态 -->
				<p-empty-state
					v-else
					useIcon
					iconName="info"
					iconColor="#C7C7CC"
					size="large"
					text="暂无数据"
				></p-empty-state>
		</view>

		<!-- 底部安全间距 -->
		<view class="bottom-safe-area"></view>

		<!-- 日历式时间选择弹窗 -->
		<uni-popup ref="timePopup" type="bottom" border-radius="16rpx 16rpx 0 0">
			<view class="date-picker-popup">
				<view class="popup-header">
					<text class="popup-title">筛选时间</text>
					<view class="popup-close" @click="closeDatePicker">
						<uni-icons type="close" size="18" color="#8E8E93"></uni-icons>
					</view>
				</view>
				
				<!-- 快捷选择模式 -->
				<view v-if="dateFilterMode === 'quick'" class="quick-date-section">
					<view class="section-title">快捷选择</view>
					<view class="quick-options">
						<view 
							v-for="option in quickDateOptions" 
							:key="option.value"
							class="quick-option"
							:class="{ active: selectedQuickFilter === option.value }"
							@click="selectQuickDateOption(option)"
						>
							<text class="quick-text">{{option.label}}</text>
						</view>
					</view>
				</view>
				
				<!-- 日期范围选择模式 -->
				<view v-else class="range-date-section">
					<!-- 简洁的日历选择器 -->
					<view class="calendar-section">
						<view class="calendar-header">
							<text class="calendar-tip">在日历上点击选择开始和结束日期</text>
						</view>
						
						<!-- uni-calendar 日历组件 -->
						<uni-calendar 
							ref="calendar"
							:range="true"
							:date="calendarDate"
							:start-date="calendarStartDate"
							:end-date="calendarEndDate"
							@change="onCalendarChange"
							@monthSwitch="onMonthSwitch"
						></uni-calendar>
						
						<!-- 已选择的日期范围显示 -->
						<view v-if="customDateRange.startDate && customDateRange.endDate" class="selected-range">
							<view class="range-item">
								<text class="range-label">开始日期</text>
								<text class="range-value">{{formatSelectedDate(customDateRange.startDate)}}</text>
							</view>
							<view class="range-separator">→</view>
							<view class="range-item">
								<text class="range-label">结束日期</text>
								<text class="range-value">{{formatSelectedDate(customDateRange.endDate)}}</text>
							</view>
						</view>
					</view>
					
					<view class="mode-switch">
						<text class="switch-text" @click="switchToQuickMode">< 返回快捷选择</text>
					</view>
				</view>
				

			</view>
		</uni-popup>
	</view>
</template>

<script>
import { callCloudFunction } from '@/utils/auth.js';

// 配置常量
const CONFIG = {
	API: {
		AREA_PAGE_SIZE: 100,           // 责任区分页大小
		INSPECTION_PAGE_SIZE: 20,      // 检查记录分页大小
		RECTIFICATION_PAGE_SIZE: 1,    // 整改记录分页大小
		DEFAULT_QUERY_DAYS: 30         // 默认查询天数
	},
	TIME: {
		NIGHT_HOUR: 20,                // 晚上8点后算即将逾期
		TOAST_DURATION: 1500,          // Toast显示时长
		DEBOUNCE_DELAY: 100            // 防抖延迟
	}
};

export default {
	name: 'AreaInspection',
	components: {
		// uni-calendar 组件会自动注册，无需手动导入
	},
	data() {
		return {
			// 日期筛选方式：'quick' 快捷选择, 'range' 日期范围选择
			dateFilterMode: 'quick',
			selectedTimeFilter: 'week',
			selectedCategory: 'all', // 当前选择的分类
			selectedStatus: 'all', // 当前选择的状态筛选
			
			// 自定义日期范围
			customDateRange: {
				startDate: '',
				endDate: ''
			},
			
			// 日历组件相关数据
			calendarDate: '',
			calendarStartDate: '',
			calendarEndDate: '',
			
			// 防重复查询的定时器
			refreshTimer: null,
			calendarChangeTimer: null,
			
			// 快捷日期选项 - 优化为更实用的选项
			quickDateOptions: [
				{ label: '本周', value: 'week' },
				{ label: '上周', value: 'last_week' },
				{ label: '本月', value: 'month' },
				{ label: '自定义', value: 'custom' }
			],
			selectedQuickFilter: 'week',
			

			

			// 分类标签
			categoryTabs: [
				{ label: '全部', value: 'all' },
				{ label: '固定责任区', value: 'fixed' },
				{ label: '公共责任区', value: 'public' }
			],

			// 检查记录数据
			inspectionRecords: [],
			// 加载状态
			loading: false,
			loadError: '',
			// 展开的周次
			expandedWeeks: [], // 将动态设置当前周
			// 数据加载标记
			dataLoaded: false,
			needsRefresh: false, // 标记是否需要刷新数据
			
			// 性能优化缓存
			processCache: {
				timeCalculations: null,
				statusMap: null,
				weekKeyCache: new Map(),
				areaTypeMap: null,
				formattedDatesCache: new Map()
			},
			// 责任区数据缓存
			areasCache: null
		}
	},
	computed: {
		// 根据时间和分类筛选记录
		filteredRecords() {
			let records = this.inspectionRecords;
			
			// 获取当前时间信息用于筛选
			const now = new Date();
			const currentYear = now.getFullYear();
			const currentMonth = now.getMonth() + 1;
			const currentQuarter = Math.ceil(currentMonth / 3);
			const currentWeekKey = this.getCurrentWeekKeyOptimized(now);
			
			// 基于日期范围的直接筛选
			const dateRange = this.getCurrentDateRange();
			if (dateRange) {
				records = records.filter(r => {
					// 直接基于记录的日期信息进行筛选，而不是周次计算
					return this.isRecordInDateRange(r, dateRange);
				});
			}
			
			// 按分类筛选
			if (this.selectedCategory !== 'all') {
				records = records.filter(r => r.type === this.selectedCategory);
			}
			
			// 按状态筛选
			if (this.selectedStatus !== 'all') {
				records = records.filter(r => r.status === this.selectedStatus);
			}
			
			return records;
		},
		
		// 时间分组的记录
		groupedFilteredRecords() {
			const records = this.filteredRecords;
			const grouped = {};
			
			records.forEach(record => {
				const weekKey = record.week;
				if (!grouped[weekKey]) {
					grouped[weekKey] = {
						weekKey,
						title: this.getWeekTitle(weekKey),
						records: [],
						expanded: this.expandedWeeks.includes(weekKey)
					};
				}
				grouped[weekKey].records.push(record);
			});
			
			// 对每个分组内的记录按状态优先级排序
			Object.values(grouped).forEach(group => {
				group.records.sort((a, b) => this.getStatusPriority(a.status) - this.getStatusPriority(b.status));
			});
			
			// 按周次降序排序
			const sortedGroups = Object.values(grouped).sort((a, b) => {
				const weekA = parseInt(a.weekKey.split('-W')[1]);
				const weekB = parseInt(b.weekKey.split('-W')[1]);
				return weekB - weekA;
			});
			
			// 智能展开逻辑：确保第一个（最新的）分组总是展开
			if (sortedGroups.length > 0) {
				const firstWeekKey = sortedGroups[0].weekKey;
				if (!this.expandedWeeks.includes(firstWeekKey)) {
					this.expandedWeeks.push(firstWeekKey);
					sortedGroups[0].expanded = true;
				}
			}
			
			return sortedGroups;
		},


	},
	created() {
		this.initProcessCache();
	},
	onLoad() {
		this.loadPageDataOptimized();
		
		// 监听清理记录更新事件
		uni.$on('cleaningRecordUpdated', this.handleCleaningRecordUpdated);
		// 监听检查记录更新事件
		uni.$on('inspectionRecordUpdated', this.handleInspectionRecordUpdated);
		// 监听整改记录更新事件
		uni.$on('rectificationRecordUpdated', this.handleRectificationRecordUpdated);
	},
	onUnload() {
		// 移除事件监听
		uni.$off('cleaningRecordUpdated', this.handleCleaningRecordUpdated);
		uni.$off('inspectionRecordUpdated', this.handleInspectionRecordUpdated);
		uni.$off('rectificationRecordUpdated', this.handleRectificationRecordUpdated);
		
		// 清理定时器
		if (this.refreshTimer) {
			clearTimeout(this.refreshTimer);
		}
		if (this.calendarChangeTimer) {
			clearTimeout(this.calendarChangeTimer);
		}
	},
	onShow() {
		// 页面重新显示时，只在确实需要刷新数据的情况下才刷新
		// 使用静默刷新，避免显示加载动画
		if (this.dataLoaded && !this.loading && this.needsRefresh) {
			this.silentRefreshData();
			this.needsRefresh = false; // 重置刷新标记
		}
	},
	methods: {
		// 初始化处理缓存
		initProcessCache() {
			if (!this.processCache.areaTypeMap) {
				this.processCache.areaTypeMap = {
					'fixed': '固定',
					'public': '公共'
				};
			}
			
			if (!this.processCache.statusMap) {
				this.processCache.statusMap = {
					'not_cleaned': '未打扫',
					'pending': '待检查', 
					'pending_rectification': '待整改',
					'pending_review': '待复查',
					'completed': '已完成',
					'rejected': '整改不达标',
					'verified': '整改合格',
					'missed': '漏检查'
				};
			}
			
			this.initTimeCalculations();
		},

		// 初始化时间计算缓存
		initTimeCalculations() {
			if (!this.processCache.timeCalculations) {
				const now = new Date();
				this.processCache.timeCalculations = {
					now,
					weekStart: this.getWeekStartOptimized(now),
					weekEnd: this.getWeekEndOptimized(now),
					currentWeekKey: this.getCurrentWeekKeyOptimized(now)
				};
			}
		},

		// 获取区域类型文本（使用缓存）
		getAreaTypeText(type) {
			return this.processCache.areaTypeMap[type] || '固定';
		},

		// 获取状态优先级（数字越小优先级越高）
		getStatusPriority(status) {
			const priorityMap = {
				'pending_review': 1,        // 待复查 - 最高优先级
				'pending': 2,               // 待检查 - 高优先级
				'pending_rectification': 3, // 待整改 - 中高优先级
				'missed': 4,                // 漏检查 - 中优先级
				'not_cleaned': 5,           // 未打扫 - 低优先级
				'completed': 6              // 已完成 - 最低优先级
			};
			return priorityMap[status] || 999; // 未知状态放在最后
		},
		
		// 获取当前周的起始和结束日期
		getCurrentWeekRange(inputDate = new Date()) {
			const date = new Date(inputDate);
			const day = date.getDay();
			const diff = date.getDate() - day + (day === 0 ? -6 : 1);
			
			const monday = new Date(date);
			monday.setDate(diff);
			
			const sunday = new Date(date);
			sunday.setDate(diff + 6);
			
			const startStr = `${monday.getMonth() + 1}月${monday.getDate()}日`;
			const endStr = `${sunday.getMonth() + 1}月${sunday.getDate()}日`;
			
			return {
				start: startStr,
				end: endStr,
				monday: monday,
				sunday: sunday
			};
		},

		// 优化的加载页面数据
		async loadPageDataOptimized() {
			this.loading = true;
			this.loadError = '';

			try {
				// 重新初始化时间计算缓存
				this.processCache.timeCalculations = null;
				this.initTimeCalculations();
				
				// 加载检查记录
				await this.loadInspectionRecordsOptimized();

				// 设置默认展开的周次
				if (this.expandedWeeks.length === 0) {
					const currentWeek = this.processCache.timeCalculations.currentWeekKey;
					this.expandedWeeks = [currentWeek];
					
					// 如果有待复查任务，确保包含待复查任务的周次也展开
					const pendingReviewWeeks = this.inspectionRecords
						.filter(record => record.status === 'pending_review')
						.map(record => record.week)
						.filter((week, index, arr) => arr.indexOf(week) === index); // 去重
					
					pendingReviewWeeks.forEach(week => {
						if (!this.expandedWeeks.includes(week)) {
							this.expandedWeeks.push(week);
						}
					});
				}

			} catch (error) {
				this.loadError = '加载数据失败，请稍后重试';
				this.handleLoadError(error);
			} finally {
				this.loading = false;
				this.dataLoaded = true;
			}
		},

		// 统一的错误处理
		handleLoadError(error) {
			uni.showModal({
				title: '加载失败',
				content: this.loadError,
				showCancel: true,
				cancelText: '返回',
				confirmText: '重试',
				success: (res) => {
					if (res.confirm) {
						this.loadPageDataOptimized();
					}
				}
			});
		},

		// 保留原方法以防其他地方调用
		async loadPageData() {
			return this.loadPageDataOptimized();
		},

		// 优化的加载检查记录
		async loadInspectionRecordsOptimized() {
			try {
				// 获取所有责任区
				const areasResult = await callCloudFunction('hygiene-area-management', {
					action: 'getAreaList',
					data: { pageSize: CONFIG.API.AREA_PAGE_SIZE }
				});

				if (areasResult?.success) {
					const areas = this.extractAreas(areasResult.data);
					
					// 缓存责任区数据，用于后续查询排班日等信息
					this.areasCache = areas;
					
					// 批量获取清理记录和检查记录
					const [cleaningRecordsByWeekArea, inspectionRecordsByWeekArea] = await this.batchLoadRecords(areas);
					
					// 根据日期筛选模式动态生成需要的周次
					const weeksToShow = this.generateWeeksForCurrentFilter();
					
					// 为每个责任区的每个周次生成检查状态
					const inspectionTasks = [];
					
					areas.forEach(area => {
						weeksToShow.forEach(weekKey => {
							const areaId = area._id || area.id;
							const mapKey = `${weekKey}-${areaId}`;
							
							const cleaningRecord = cleaningRecordsByWeekArea.get(mapKey);
							const inspectionRecord = inspectionRecordsByWeekArea.get(mapKey);
							
							const task = this.processAreaInspectionForWeek(
								area, 
								weekKey,
								cleaningRecord,
								inspectionRecord
							);
							
							if (task) {
								inspectionTasks.push(task);
							}
						});
					});

					this.inspectionRecords = inspectionTasks;
				} else {
					throw new Error(areasResult?.message || '获取责任区失败');
				}
			} catch (error) {
				this.inspectionRecords = [];
				throw error;
			}
		},

		// 提取责任区数据
		extractAreas(data) {
			if (!data) return [];
			if (Array.isArray(data)) return data;
			if (data.list && Array.isArray(data.list)) return data.list;
			return [];
		},

		// 批量加载清理和检查记录
		async batchLoadRecords(areas) {
			const areaIds = areas.map(area => area._id || area.id);
			
			// 并行获取所有清理记录和检查记录
			const [cleaningResults, inspectionResults] = await Promise.allSettled([
				this.batchGetCleaningRecords(areaIds),
				this.batchGetInspectionRecords(areaIds)
			]);

			// 按周次和责任区组织数据
			const cleaningRecordsByWeekArea = new Map(); // key: "weekKey-areaId"
			const inspectionRecordsByWeekArea = new Map(); // key: "weekKey-areaId"

			// 处理清理记录结果
			if (cleaningResults.status === 'fulfilled' && cleaningResults.value) {
				cleaningResults.value.forEach(record => {
					const areaId = record.area_id;
					const recordDate = new Date(record.cleaning_date);
					const weekKey = this.getCurrentWeekKeyOptimized(recordDate);
					const mapKey = `${weekKey}-${areaId}`;
					
					// 保留每周每个区域的最新记录
					if (!cleaningRecordsByWeekArea.has(mapKey) || 
						new Date(record.cleaning_date) > new Date(cleaningRecordsByWeekArea.get(mapKey).cleaning_date)) {
						cleaningRecordsByWeekArea.set(mapKey, record);
					}
				});
			}

			// 处理检查记录结果
			if (inspectionResults.status === 'fulfilled' && inspectionResults.value) {
				inspectionResults.value.forEach(record => {
					const areaId = record.area_id;
					const recordDate = new Date(record.inspection_date || record.created_at);
					const weekKey = this.getCurrentWeekKeyOptimized(recordDate);
					const mapKey = `${weekKey}-${areaId}`;
					
					// 保留每周每个区域的最新记录
					if (!inspectionRecordsByWeekArea.has(mapKey) || 
						new Date(record.inspection_date || record.created_at) > new Date(inspectionRecordsByWeekArea.get(mapKey).inspection_date || inspectionRecordsByWeekArea.get(mapKey).created_at)) {
						inspectionRecordsByWeekArea.set(mapKey, record);
					}
				});
			}			
			return [cleaningRecordsByWeekArea, inspectionRecordsByWeekArea];
		},

		// 批量获取清理记录
		async batchGetCleaningRecords(areaIds) {
			try {
				// 智能日期范围查询：基于用户选择的日期范围进行精确查询
				const dateRange = this.getCurrentDateRange();
				
				let startDate, endDate;
				if (dateRange) {
					// 使用用户选择的日期范围
					startDate = new Date(dateRange.start);
					endDate = new Date(dateRange.end);
					
					// 适当扩展边界（前后各2天）确保不遗漏边界数据  
					startDate.setDate(startDate.getDate() - 2);
					endDate.setDate(endDate.getDate() + 2);
				} else {
					// 无选择时默认查询近期天数
					endDate = new Date();
					startDate = new Date();
					startDate.setDate(startDate.getDate() - CONFIG.API.DEFAULT_QUERY_DAYS);
				}
				
				startDate.setHours(0, 0, 0, 0);
				endDate.setHours(23, 59, 59, 999);
				
				const result = await callCloudFunction('hygiene-cleaning', {
					action: 'getBatchCleaningRecords',
					data: {
						area_ids: areaIds,
						start_date: startDate.toISOString(),
						end_date: endDate.toISOString(),
						latest_only: false
					}
				});
				

				return result?.success ? result.data : [];
			} catch (error) {
				console.error('获取清理记录失败:', error);
				return [];
			}
		},

		// 批量获取检查记录
		async batchGetInspectionRecords(areaIds) {
			try {
				const promises = areaIds.map(areaId => 
					callCloudFunction('hygiene-inspection', {
						action: 'getInspectionRecords',
						data: { 
							area_id: areaId, 
							pageSize: CONFIG.API.INSPECTION_PAGE_SIZE
						}
					})
				);
				
				const results = await Promise.allSettled(promises);
				const records = [];
				
				results.forEach((result, index) => {
					if (result.status === 'fulfilled' && result.value?.success && result.value.data?.list?.length > 0) {
						// 获取所有检查记录，而不只是最新的一条
						result.value.data.list.forEach(record => {
							record.area_id = areaIds[index];
							records.push(record);
						});
					}
				});
				
				return records;
			} catch (error) {
				console.error('获取检查记录失败:', error);
				return [];
			}
		},

		// 处理单个责任区的检查状态
		processAreaInspection(area, lastCleaningRecord, lastInspection) {
			const areaId = area._id || area.id;
			const { weekStart, weekEnd } = this.processCache.timeCalculations;
			
			let status = 'not_cleaned';
			let subtitle = '本周未开始打扫';

			if (lastCleaningRecord) {
				const cleaningDate = new Date(lastCleaningRecord.cleaning_date);
				
				if (cleaningDate >= weekStart && cleaningDate <= weekEnd) {
					const missedStatus = this.calculateMissedStatusOptimized(area, lastCleaningRecord, lastInspection);
					if (missedStatus === 'missed') {
						status = 'missed';
						subtitle = `检查员漏检查 · ${this.formatDateTimeOptimized(cleaningDate)}`;
					} else if (lastInspection) {
						const inspectionDate = new Date(lastInspection.inspection_date || lastInspection.created_at);
						
						if (inspectionDate >= cleaningDate) {
							({ status, subtitle } = this.processInspectionStatus(lastInspection, inspectionDate, area, lastCleaningRecord));
						} else {
							status = 'pending';
							subtitle = `员工已提交，待检查 · ${this.formatDateTimeOptimized(cleaningDate)}`;
						}
					} else {
						status = 'pending';
						subtitle = `员工已提交，待检查 · ${this.formatDateTimeOptimized(cleaningDate)}`;
					}
				}
			}

			return {
				id: lastInspection ? (lastInspection._id || lastInspection.id) : areaId,
				areaName: area.name || '未知责任区',
				subtitle,
				status,
				icon: this.getStatusIcon(status),
				type: area.type || 'fixed',
				week: this.processCache.timeCalculations.currentWeekKey,
				inspectionDate: lastInspection ? (lastInspection.inspection_date || lastInspection.created_at) : null,
				isRectificationRecheck: false,
				areaId,
				inspectorName: '检查员',
				inspectionRecordId: lastInspection ? (lastInspection._id || lastInspection.id) : null,
				isInspectionRecord: !!lastInspection,
				// 添加排班日信息，用于判断公共责任区的时效
				scheduledDay: area.scheduled_day || null
			};
		},

		// 处理检查状态
		processInspectionStatus(lastInspection, inspectionDate, area, lastCleaningRecord) {
			let status, subtitle;
			
			if (lastInspection.status === 'pending_rectification') {
				status = 'pending_rectification';
				subtitle = `检查未通过，待整改 · ${this.formatDateTimeOptimized(inspectionDate)}`;
			} else if (lastInspection.status === 'rectification_completed') {
				const rectificationMissed = this.calculateMissedStatusOptimized(area, lastCleaningRecord, lastInspection);
				if (rectificationMissed === 'missed') {
					status = 'missed';
					subtitle = `整改复查已超时 · ${this.formatDateTimeOptimized(inspectionDate)}`;
				} else {
					status = 'pending_review';
					subtitle = `整改已提交，待复查 · ${this.formatDateTimeOptimized(inspectionDate)}`;
				}
			} else if (lastInspection.status === 'verified') {
				status = 'completed';
				subtitle = `整改已确认，检查完成 · ${this.formatDateTimeOptimized(inspectionDate)}`;
			} else if (lastInspection.status === 'completed') {
				status = 'completed';
				subtitle = `已完成检查 · ${this.formatDateTimeOptimized(inspectionDate)}`;
			} else {
				status = 'completed';
				subtitle = `已完成检查 · ${this.formatDateTimeOptimized(inspectionDate)}`;
			}
			
			return { status, subtitle };
		},

		// 保留原方法以防其他地方调用
		async loadInspectionRecords() {
			return this.loadInspectionRecordsOptimized();
		},

		// 优化的获取本周开始时间
		getWeekStartOptimized(date = new Date()) {
			const d = new Date(date);
			const day = d.getDay();
			const diff = d.getDate() - day + (day === 0 ? -6 : 1);
			const monday = new Date(d);
			monday.setDate(diff);
			monday.setHours(0, 0, 0, 0);
			return monday;
		},

		// 优化的获取本周结束时间
		getWeekEndOptimized(date = new Date()) {
			const d = new Date(date);
			const day = d.getDay();
			const diff = d.getDate() - day + (day === 0 ? 0 : 7);
			const sunday = new Date(d);
			sunday.setDate(diff);
			sunday.setHours(23, 59, 59, 999);
			return sunday;
		},

		// 保留原方法以防其他地方调用
		getWeekStart(date = new Date()) {
			return this.getWeekStartOptimized(date);
		},

		getWeekEnd(date = new Date()) {
			return this.getWeekEndOptimized(date);
		},

		// 优化的计算漏检状态
		calculateMissedStatusOptimized(area, lastCleaningRecord, lastInspectionRecord) {
			const { now, weekStart, weekEnd } = this.processCache.timeCalculations;
			
			// 1. 员工已提交清理，但检查员超时未检查
			if (lastCleaningRecord) {
				const cleaningDate = new Date(lastCleaningRecord.cleaning_date);
				
				// 只判断本周期内的清理记录
				if (cleaningDate >= weekStart && cleaningDate <= weekEnd) {
					// 检查是否有对应的检查记录
					let hasValidInspection = false;
					if (lastInspectionRecord) {
						const inspectionDate = new Date(lastInspectionRecord.inspection_date || lastInspectionRecord.created_at);
						// 检查记录必须在清理记录之后
						hasValidInspection = inspectionDate >= cleaningDate;
					}
					
					// 如果没有有效检查记录，判断是否漏检
					if (!hasValidInspection) {
						if (area.type === 'fixed') {
							// 固定责任区：过了周日23:59:59（即到了下周一）才算漏检
							const nextWeekStart = new Date(weekEnd);
							nextWeekStart.setDate(nextWeekStart.getDate() + 1); // 下周一00:00:00
							
							if (now >= nextWeekStart) {
								return 'missed';
							}
						} else if (area.type === 'public') {
							// 公共责任区：过了排班日当天23:59:59才算漏检
							const scheduledEndTime = this.getScheduledDayEndTime(now, area.scheduled_day);
							
							// 如果当前时间已经超过排班日的23:59:59，算漏检
							if (now > scheduledEndTime) {
								return 'missed';
							}
						}
					}
				}
			}
			
			// 2. 员工已提交整改，但检查员超时未复查
			if (lastInspectionRecord && lastInspectionRecord.status === 'rectification_completed') {
				// 查找整改提交时间
				const submitTime = lastInspectionRecord.rectification_submit_time || lastInspectionRecord.updated_at;
				if (submitTime) {
					const submitDate = new Date(submitTime);
					
					// 整改复查也应该遵循周期重置逻辑
					// 如果整改提交在本周内，检查员应该在本周内复查完成
					if (submitDate >= weekStart && submitDate <= weekEnd) {
						// 如果已经到了下周，且整改是在上周提交的，算漏检
						const nextWeekStart = new Date(weekEnd);
						nextWeekStart.setDate(nextWeekStart.getDate() + 1); // 下周一00:00:00
						
						if (now >= nextWeekStart) {
							return 'missed';
						}
					}
				}
			}
			
			return null; // 不是漏检状态
		},

		// 保留原方法以防其他地方调用
		calculateMissedStatus(area, lastCleaningRecord, lastInspectionRecord) {
			return this.calculateMissedStatusOptimized(area, lastCleaningRecord, lastInspectionRecord);
		},

		// 映射检查状态
		mapInspectionStatus(record) {
			// 根据记录的状态和相关信息确定显示状态
			if (record.status === 'completed') {
				return 'completed';
			} else if (record.status === 'pending') {
				return 'pending';
			} else if (record.cleaning_status === 'not_cleaned') {
				return 'not_cleaned';
			} else if (record.rectification_required) {
				return 'pending_rectification';
			} else if (record.status === 'missed' || record.is_overdue) {
				return 'missed';
			}
			return 'pending';
		},

		// 生成记录副标题
		generateRecordSubtitle(record) {
			const status = this.mapInspectionStatus(record);
			const inspectorName = record.inspector_name || record.created_by_name || '未知检查员';
			
			const statusTexts = {
				'completed': '已完成检查',
				'pending': record.is_rectification_recheck ? '整改后待复检' : '员工已提交，待检查',
				'not_cleaned': '本周未开始打扫',
				'pending_rectification': '检查未通过，待整改',
				'missed': '该责任区漏检查'
			};
			
			const dateStr = record.inspection_date ? 
				` · ${this.formatDateTime(record.inspection_date)}` : '';
			
			return `检查员：${inspectorName} · ${statusTexts[status]}${dateStr}`;
		},

		// 获取周次键值
		getWeekKey(dateString) {
			if (!dateString) return this.getCurrentWeekKey();
			const date = new Date(dateString);
			const year = date.getFullYear();
			const weekNum = this.getWeekNumber(date);
			return `${year}-W${weekNum.toString().padStart(2, '0')}`;
		},

		// 优化的获取当前周键值
		getCurrentWeekKeyOptimized(date = new Date()) {
			const cacheKey = date.toDateString();
			if (this.processCache.weekKeyCache.has(cacheKey)) {
				return this.processCache.weekKeyCache.get(cacheKey);
			}
			
			const year = date.getFullYear();
			const weekNum = this.getWeekNumber(date);
			const weekKey = `${year}-W${weekNum.toString().padStart(2, '0')}`;
			
			this.processCache.weekKeyCache.set(cacheKey, weekKey);
			return weekKey;
		},

		// 保留原方法以防其他地方调用
		getCurrentWeekKey() {
			return this.getCurrentWeekKeyOptimized();
		},

		// 获取日期对应的周数
		getWeekNumber(date) {
			const onejan = new Date(date.getFullYear(), 0, 1);
			const millisecsInDay = 86400000;
			return Math.ceil((((date.getTime() - onejan.getTime()) / millisecsInDay) + onejan.getDay() + 1) / 7);
		},

		// 获取当前月的所有周次
		getMonthWeeks(date = new Date()) {
			const year = date.getFullYear();
			const month = date.getMonth();
			
			// 获取当月第一天和最后一天
			const firstDay = new Date(year, month, 1);
			const lastDay = new Date(year, month + 1, 0);
			
			const weeks = [];
			for (let d = new Date(firstDay); d <= lastDay; d.setDate(d.getDate() + 7)) {
				const weekKey = this.getCurrentWeekKeyOptimized(d);
				if (!weeks.includes(weekKey)) {
					weeks.push(weekKey);
				}
			}
			
			return weeks;
		},

		// 获取当前季度的所有周次
		getQuarterWeeks(date = new Date()) {
			const year = date.getFullYear();
			const month = date.getMonth();
			const quarter = Math.ceil((month + 1) / 3);
			
			// 获取季度的第一个月和最后一个月
			const firstMonthOfQuarter = (quarter - 1) * 3;
			const lastMonthOfQuarter = quarter * 3 - 1;
			
			// 获取季度第一天和最后一天
			const firstDay = new Date(year, firstMonthOfQuarter, 1);
			const lastDay = new Date(year, lastMonthOfQuarter + 1, 0);
			
			const weeks = [];
			for (let d = new Date(firstDay); d <= lastDay; d.setDate(d.getDate() + 7)) {
				const weekKey = this.getCurrentWeekKeyOptimized(d);
				if (!weeks.includes(weekKey)) {
					weeks.push(weekKey);
				}
			}
			
			return weeks;
		},

		// 生成需要显示的周次列表
		generateWeeksToShow(weeksCount = 8) {
			const weeks = [];
			const now = new Date();
			
			// 生成从当前周往前推的周次
			for (let i = 0; i < weeksCount; i++) {
				const weekDate = new Date(now);
				weekDate.setDate(weekDate.getDate() - (i * 7));
				const weekKey = this.getCurrentWeekKeyOptimized(weekDate);
				if (!weeks.includes(weekKey)) {
					weeks.push(weekKey);
				}
			}
			
			// 按周次倒序排列（最新的在前）
			return weeks.sort((a, b) => {
				const weekA = parseInt(a.split('-W')[1]);
				const weekB = parseInt(b.split('-W')[1]);
				return weekB - weekA;
			});
		},

		// 根据当前筛选条件生成需要的周次
		generateWeeksForCurrentFilter() {
			const dateRange = this.getCurrentDateRange();
			
			if (!dateRange) {
				// 没有日期范围时，默认生成当前周
				return [this.getCurrentWeekKeyOptimized()];
			}

			// 基于日期范围计算所有涉及的周次
			const weeks = [];
			const start = new Date(dateRange.start);
			const end = new Date(dateRange.end);
			
			// 从开始日期所在的周开始
			let currentDate = new Date(start);
			// 调整到该周的周一
			const dayOfWeek = currentDate.getDay();
			const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
			currentDate.setDate(currentDate.getDate() - daysToMonday);
			
			while (currentDate <= end) {
				const weekKey = this.getCurrentWeekKeyOptimized(currentDate);
				if (!weeks.includes(weekKey)) {
					weeks.push(weekKey);
				}
				// 移动到下一周
				currentDate.setDate(currentDate.getDate() + 7);
			}
			

			
			return weeks.reverse(); // 返回降序（最新的在前）
		},

		// 为特定周次处理责任区检查状态
		processAreaInspectionForWeek(area, weekKey, cleaningRecord, inspectionRecord) {
			const areaId = area._id || area.id;
			
			// 计算该周的起始和结束时间
			const weekTimes = this.getWeekTimesFromKey(weekKey);
			if (!weekTimes) return null;
			
			// 判断是否为当前周
			const currentWeekKey = this.getCurrentWeekKeyOptimized();
			const isCurrentWeek = weekKey === currentWeekKey;
			
			let status = 'not_cleaned';
			let subtitle = '未开始打扫';
			
			// 如果有清理记录，分析状态
			if (cleaningRecord) {
				const cleaningDate = new Date(cleaningRecord.cleaning_date);
				
				if (inspectionRecord) {
					const inspectionDate = new Date(inspectionRecord.inspection_date || inspectionRecord.created_at);
					
					if (inspectionDate >= cleaningDate) {
						({ status, subtitle } = this.processInspectionStatusForWeek(inspectionRecord, inspectionDate, area, cleaningRecord, weekTimes));
					} else {
						status = 'pending';
						subtitle = `员工已提交，待检查 · ${this.formatDateTimeOptimized(cleaningDate)}`;
					}
				} else {
					// 有清理记录但没有检查记录
					const now = new Date();
					if (now > weekTimes.weekEnd) {
						status = 'missed';
						subtitle = `检查员漏检查 · ${this.formatDateTimeOptimized(cleaningDate)}`;
					} else {
						status = 'pending';
						subtitle = `员工已提交，待检查 · ${this.formatDateTimeOptimized(cleaningDate)}`;
					}
				}
			} else {
				// 没有清理记录
				
				const now = new Date();
				if (now > weekTimes.weekEnd) {
					// 过去的周次
					if (isCurrentWeek) {
						status = 'not_cleaned';
						subtitle = '本周未打扫';
					} else {
						status = 'not_cleaned';
						subtitle = `第${this.getWeekDisplayNumber(weekKey)}周未打扫`;
					}
				} else {
					// 当前或未来的周次
					if (isCurrentWeek) {
						status = 'not_cleaned';
						subtitle = '未开始打扫';
					} else {
						status = 'not_cleaned';
						subtitle = '未开始打扫';
					}
				}
			}

			return {
				id: inspectionRecord ? (inspectionRecord._id || inspectionRecord.id) : `${areaId}-${weekKey}`,
				areaName: area.name || '未知责任区',
				subtitle,
				status,
				icon: this.getStatusIcon(status),
				type: area.type || 'fixed',
				week: weekKey,
				inspectionDate: inspectionRecord ? (inspectionRecord.inspection_date || inspectionRecord.created_at) : null,
				isRectificationRecheck: false,
				areaId,
				inspectorName: '检查员',
				inspectionRecordId: inspectionRecord ? (inspectionRecord._id || inspectionRecord.id) : null,
				isInspectionRecord: !!inspectionRecord,
				scheduledDay: area.scheduled_day || null
			};
		},

		// 根据周次键获取周的起始和结束时间
		getWeekTimesFromKey(weekKey) {
			try {
				const match = weekKey.match(/(\d{4})-W(\d{2})/);
				if (!match) return null;
				
				const year = parseInt(match[1]);
				const weekNum = parseInt(match[2]);
				
				// 计算该年第一天
				const jan1 = new Date(year, 0, 1);
				
				// 计算该周的周一
				const daysSinceJan1 = (weekNum - 1) * 7;
				const weekStart = new Date(jan1);
				weekStart.setDate(jan1.getDate() + daysSinceJan1 - jan1.getDay() + 1);
				weekStart.setHours(0, 0, 0, 0);
				
				// 计算该周的周日
				const weekEnd = new Date(weekStart);
				weekEnd.setDate(weekStart.getDate() + 6);
				weekEnd.setHours(23, 59, 59, 999);
				
				return { weekStart, weekEnd };
			} catch (error) {
				return null;
			}
		},

		// 为特定周次处理检查状态
		processInspectionStatusForWeek(inspectionRecord, inspectionDate, area, cleaningRecord, weekTimes) {
			let status, subtitle;
			const now = new Date();
			
			if (inspectionRecord.status === 'pending_rectification') {
				// 检查整改任务是否已超时
				if (now > weekTimes.weekEnd) {
					status = 'missed';
					subtitle = `整改任务已超时 · ${this.formatDateTimeOptimized(inspectionDate)}`;
				} else {
					status = 'pending_rectification';
					subtitle = `检查未通过，待整改 · ${this.formatDateTimeOptimized(inspectionDate)}`;
				}
			} else if (inspectionRecord.status === 'rectification_completed') {
				// 检查整改复查是否已跨周
				const submitTime = inspectionRecord.rectification_submit_time || inspectionRecord.updated_at;
				const submitDate = submitTime ? new Date(submitTime) : inspectionDate;
				
				// 计算复查截止时间（整改提交后的本周结束）
				const submitWeekKey = this.getCurrentWeekKeyOptimized(submitDate);
				const submitWeekTimes = this.getWeekTimesFromKey(submitWeekKey);
				
				if (submitWeekTimes && now > submitWeekTimes.weekEnd) {
					status = 'missed';
					subtitle = `整改复查已超时 · 提交时间: ${this.formatDateTimeOptimized(submitDate)}`;
				} else {
					status = 'pending_review';
					subtitle = `整改已提交，待复查 · ${this.formatDateTimeOptimized(submitDate)}`;
				}
			} else if (inspectionRecord.status === 'verified') {
				status = 'completed';
				subtitle = `整改已确认，检查完成 · ${this.formatDateTimeOptimized(inspectionDate)}`;
			} else if (inspectionRecord.status === 'completed') {
				status = 'completed';
				subtitle = `已完成检查 · ${this.formatDateTimeOptimized(inspectionDate)}`;
			} else {
				status = 'completed';
				subtitle = `已完成检查 · ${this.formatDateTimeOptimized(inspectionDate)}`;
			}
			
			return { status, subtitle };
		},

		// 移除了硬编码的系统上线时间检查
		// 现在基于实际数据进行智能判断，无需手动配置系统上线时间

		// 解析周次键值为年份和周数
		parseWeekKey(weekKey) {
			const match = weekKey.match(/(\d{4})-W(\d{2})/);
			if (!match) return null;
			
			return {
				year: parseInt(match[1]),
				week: parseInt(match[2])
			};
		},

		// 获取周次的显示编号
		getWeekDisplayNumber(weekKey) {
			const parsed = this.parseWeekKey(weekKey);
			return parsed ? parsed.week : '';
		},

		getCurrentTimeRange() {
			if (this.selectedTimeFilter === 'custom' && this.dateFilterMode === 'range') {
				return this.getDateRangeText();
			} else {
				const option = this.quickDateOptions.find(opt => opt.value === this.selectedQuickFilter);
				return option ? option.label : '本周';
			}
		},
		
		// 获取状态筛选文本
		getStatusFilterText() {
			const statusTexts = {
				'not_cleaned': '未打扫',
				'pending': '待检查',
				'pending_rectification': '待整改',
				'pending_review': '待复查',
				'completed': '已完成',
				'missed': '漏检查'
			};
			return statusTexts[this.selectedStatus] || '';
		},

		getStatsData() {
			// 基于实际筛选的记录计算统计 - 5种状态独立统计
			const records = this.filteredRecords;
			const not_cleaned = records.filter(r => r.status === 'not_cleaned').length;
			const pending = records.filter(r => r.status === 'pending').length;
			const pending_rectification = records.filter(r => r.status === 'pending_rectification').length;
			const pending_review = records.filter(r => r.status === 'pending_review').length;
			const completed = records.filter(r => r.status === 'completed').length;
			const missed = records.filter(r => r.status === 'missed').length;
			
			return { not_cleaned, pending, pending_rectification, pending_review, completed, missed };
		},
		// 选择分类
		selectCategory(category) {
			this.selectedCategory = category;
		},
		
		// 选择状态筛选
		selectStatus(status) {
			// 如果点击的是当前选中的状态，则取消筛选（回到全部）
			if (this.selectedStatus === status) {
				this.selectedStatus = 'all';
			} else {
				this.selectedStatus = status;
			}
		},
		// 获取分类数量
		getCategoryCount(category) {
			if (category === 'all') {
				return this.filteredRecords.length;
			}
			return this.filteredRecords.filter(r => r.type === category).length;
		},
		// 获取空状态文本
		getEmptyText() {
			try {
				if (this.selectedCategory === 'all') {
					return '暂无检查记录';
				}
				const category = this.categoryTabs.find(t => t.value === this.selectedCategory);
				const categoryLabel = category ? category.label : '该分类';
				return `暂无${categoryLabel}记录`;
			} catch (error) {
				return '暂无数据';
			}
		},
		// 处理记录点击
		handleRecordClick(record) {
			if (record.status === 'pending') {
				// 待检查 - 可以开始检查
				this.startInspection(record);
			} else if (record.status === 'completed') {
				// 已完成 - 查看检查记录详情
				this.viewRecordDetail(record);
			} else if (record.status === 'not_cleaned') {
				// 未打扫 - 需要根据责任区类型和时效判断
				this.handleNotCleanedClick(record);
			} else if (record.status === 'pending_rectification') {
				// 待整改 - 跳转到检查员查看整改详情页面
				this.viewRectificationDetail(record);
			} else if (record.status === 'pending_review') {
				// 待复查 - 直接查找整改任务ID并跳转到复查页面
				this.reviewRectificationFromRecord(record);
			} else if (record.status === 'missed') {
				// 漏检查 - 历史状态，提示已锁定
				uni.showModal({
					title: '记录已锁定',
					content: '该责任区已漏检查，历史记录已锁定',
					showCancel: false
				});
			}
		},

		// 处理未打扫状态的点击
		handleNotCleanedClick(record) {
			const { title, content } = this.getNotCleanedMessage(record);
			uni.showModal({
				title,
				content,
				showCancel: false
			});
		},

		// 获取未打扫状态的提示信息
		getNotCleanedMessage(record) {
			const now = new Date();
			const currentWeekKey = this.getCurrentWeekKeyOptimized();
			const isCurrentWeek = record.week === currentWeekKey;
			
			// 如果是历史周次，显示相应的历史提示
			if (!isCurrentWeek) {
				const weekNum = this.getWeekDisplayNumber(record.week);
				return {
					title: '历史记录',
					content: `第${weekNum}周该责任区未进行清理，已进入历史记录`
				};
			}
			
			const currentDay = now.getDay(); // 0=周日, 1=周一, ..., 6=周六
			
			if (record.type === 'public') {
				// 公共责任区：根据排班日判断
				const scheduledDay = record.scheduledDay;
				
				if (scheduledDay !== null && scheduledDay !== undefined) {
					// 转换：0=周日转为7，保持1-6不变
					const normalizedScheduledDay = scheduledDay === 0 ? 7 : scheduledDay;
					const normalizedCurrentDay = currentDay === 0 ? 7 : currentDay;
					
					const dayNames = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
					const scheduledDayName = dayNames[normalizedScheduledDay];
					
					if (normalizedCurrentDay > normalizedScheduledDay) {
						// 已过排班日
						return {
							title: '逾期未打扫',
							content: `该公共责任区排班日为${scheduledDayName}，现已逾期，已进入下个周期`
						};
					} else if (normalizedCurrentDay === normalizedScheduledDay) {
						// 当天
						const currentHour = now.getHours();
						if (currentHour >= CONFIG.TIME.NIGHT_HOUR) { // 晚上8点后算即将逾期
							return {
								title: '即将逾期',
								content: `该公共责任区排班日为今天（${scheduledDayName}），员工需尽快完成打扫`
							};
						} else {
							return {
								title: '员工未打扫',
								content: `该公共责任区排班日为今天（${scheduledDayName}），请等待员工完成`
							};
						}
					} else {
						// 还未到排班日
						return {
							title: '员工未打扫',
							content: `该公共责任区排班日为${scheduledDayName}，请等待员工完成`
						};
					}
				} else {
					// 没有排班日信息，使用默认提示
					return {
						title: '员工未打扫',
						content: '该公共责任区员工尚未开始打扫，请等待员工完成'
					};
				}
			} else {
				// 固定责任区：判断是否过了本周日
				const weekEnd = this.getWeekEndOptimized(now);
				
				if (now > weekEnd) {
					// 已过本周日
					return {
						title: '逾期未打扫',
						content: '该固定责任区本周期已结束，已进入下周期'
					};
				} else {
					// 本周内
					const remainingDays = Math.ceil((weekEnd - now) / (24 * 60 * 60 * 1000));
					if (remainingDays <= 1) {
						return {
							title: '即将逾期',
							content: '该固定责任区本周期即将结束，员工需尽快完成打扫'
						};
					} else {
						return {
							title: '员工未打扫',
							content: '该固定责任区员工尚未开始打扫，请等待员工完成'
						};
					}
				}
			}
		},

		// 获取责任区的排班日（这里需要根据实际数据结构调整）
		getAreaScheduledDay(areaId) {
			// 这里应该从责任区数据中获取scheduled_day
			// 暂时返回null，需要在loadInspectionRecordsOptimized中补充这个信息
			const area = this.areasCache?.find(a => (a._id || a.id) === areaId);
			return area?.scheduled_day || null;
		},

		// ======== 新的日期选择器方法 ========
		showTimeSelector() {
			// 初始化日期范围
			this.initializeDateRange();
			this.$refs.timePopup.open();
		},

		closeDatePicker() {
			this.$refs.timePopup.close();
		},

		// 初始化日期范围
		initializeDateRange() {
			if (!this.customDateRange.startDate) {
				const now = new Date();
				// 根据当前快捷选择初始化日期范围
				const range = this.getQuickDateRange(this.selectedQuickFilter);
				this.customDateRange.startDate = this.formatDateForPicker(range.start);
				this.customDateRange.endDate = this.formatDateForPicker(range.end);
			}
			
			// 初始化日历组件的日期
			if (!this.calendarDate) {
				this.calendarDate = this.formatDateForPicker(new Date());
			}
		},

		// 切换到范围选择模式
		switchToRangeMode() {
			this.dateFilterMode = 'range';
		},

		// 切换到快捷选择模式  
		switchToQuickMode() {
			this.dateFilterMode = 'quick';
		},



		// 快捷日期选择
		selectQuickDateOption(option) {
			this.selectedQuickFilter = option.value;
			if (option.value === 'custom') {
				this.switchToRangeMode();
			} else {
				// 快捷选择时，重置展开状态，让系统自动展开第一个分组
				this.resetExpandedState();
				
				// 快捷选择立即生效，关闭弹窗
				this.closeDatePicker();
				
				// 重新加载数据以匹配新的筛选条件
				this.loading = true;
				clearTimeout(this.refreshTimer);
				this.refreshTimer = setTimeout(async () => {
					try {
						await this.loadInspectionRecordsOptimized();
					} finally {
						this.loading = false;
					}
				}, CONFIG.TIME.DEBOUNCE_DELAY);
			}
		},

		// 获取当前日期范围
		getCurrentDateRange() {
			if (this.dateFilterMode === 'quick' && this.selectedQuickFilter !== 'custom') {
				return this.getQuickDateRange(this.selectedQuickFilter);
			} else if (this.customDateRange.startDate && this.customDateRange.endDate) {
				return {
					start: new Date(this.customDateRange.startDate),
					end: new Date(this.customDateRange.endDate)
				};
			}
			return null;
		},

		// 获取快捷日期的范围
		getQuickDateRange(quickValue) {
			const now = new Date();
			const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
			
			switch (quickValue) {
				case 'week':
					// 本周：周一到周日
					const startOfWeek = new Date(today);
					const dayOfWeek = startOfWeek.getDay();
					const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
					startOfWeek.setDate(startOfWeek.getDate() - daysToMonday);
					
					const endOfWeek = new Date(startOfWeek);
					endOfWeek.setDate(endOfWeek.getDate() + 6);
					endOfWeek.setHours(23, 59, 59, 999);
					
					return { start: startOfWeek, end: endOfWeek };
				
				case 'last_week':
					// 上周：上周一到上周日
					const lastWeekStart = new Date(today);
					const currentDayOfWeek = lastWeekStart.getDay();
					const daysToLastMonday = currentDayOfWeek === 0 ? 13 : currentDayOfWeek + 6;
					lastWeekStart.setDate(lastWeekStart.getDate() - daysToLastMonday);
					
					const lastWeekEnd = new Date(lastWeekStart);
					lastWeekEnd.setDate(lastWeekEnd.getDate() + 6);
					lastWeekEnd.setHours(23, 59, 59, 999);
					
					return { start: lastWeekStart, end: lastWeekEnd };
				
				case 'month':
					// 本月：1号到月底
					const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
					const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
					endOfMonth.setHours(23, 59, 59, 999);
					
					return { start: startOfMonth, end: endOfMonth };
				
				default:
					// 默认返回本周
					return this.getQuickDateRange('week');
			}
		},

		// 判断记录是否在日期范围内
		isRecordInDateRange(record, dateRange) {
			// 获取记录对应周的时间范围
			if (!record.week) return false;
			
			const weekTimes = this.getWeekTimesFromKey(record.week);
			if (!weekTimes) return false;
			
			// 判断周的时间范围是否与选择的日期范围有重叠
			// 周开始时间 <= 选择结束时间 && 周结束时间 >= 选择开始时间
			const weekOverlapsRange = weekTimes.weekStart <= dateRange.end && weekTimes.weekEnd >= dateRange.start;
			

			
			return weekOverlapsRange;
		},

		// 获取周的开始日期（周一）
		getWeekStart(date) {
			const result = new Date(date);
			const dayOfWeek = result.getDay();
			const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
			result.setDate(result.getDate() - daysToMonday);
			result.setHours(0, 0, 0, 0);
			return result;
		},

		// 获取公共责任区排班日的截止时间（排班日23:59:59）
		getScheduledDayEndTime(currentDate, scheduledDay) {
			if (scheduledDay === null || scheduledDay === undefined) {
				return null;
			}
			
			// 转换周日值：0转为7
			const normalizedScheduledDay = scheduledDay === 0 ? 7 : scheduledDay;
			const currentDayOfWeek = currentDate.getDay() === 0 ? 7 : currentDate.getDay();
			
			// 计算本周排班日的日期
			const daysFromScheduled = normalizedScheduledDay - currentDayOfWeek;
			const scheduledDate = new Date(currentDate);
			scheduledDate.setDate(currentDate.getDate() + daysFromScheduled);
			scheduledDate.setHours(23, 59, 59, 999);
			
			return scheduledDate;
		},

		// 格式化日期用于picker
		formatDateForPicker(date) {
			if (!date) return '';
			const d = new Date(date);
			const year = d.getFullYear();
			const month = String(d.getMonth() + 1).padStart(2, '0');
			const day = String(d.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`;
		},

		// 获取开始日期显示文本  
		getStartDateText() {
			if (!this.customDateRange.startDate) return '请选择开始日期';
			return this.formatDisplayDate(new Date(this.customDateRange.startDate));
		},

		// 获取结束日期显示文本
		getEndDateText() {
			if (!this.customDateRange.endDate) return '请选择结束日期';
			return this.formatDisplayDate(new Date(this.customDateRange.endDate));
		},

		// 格式化显示日期
		formatDisplayDate(date) {
			const d = new Date(date);
			const month = d.getMonth() + 1;
			const day = d.getDate();
			return `${month}月${day}日`;
		},

		// 获取日期范围显示文本
		getDateRangeText() {
			if (this.dateFilterMode === 'quick') {
				const option = this.quickDateOptions.find(opt => opt.value === this.selectedQuickFilter);
				return option ? option.label : '本周';
			} else {
				const startText = this.getStartDateText();
				const endText = this.getEndDateText();
				if (startText === '请选择' || endText === '请选择') {
					return '选择日期范围';
				}
				return `${startText} - ${endText}`;
			}
		},

		// 日历组件事件处理
		onCalendarChange(event) {
			
			// 防止重复处理
			clearTimeout(this.calendarChangeTimer);
			this.calendarChangeTimer = setTimeout(() => {
				// 处理日期范围选择
				if (event.range) {
					if (event.range.before && event.range.after) {
						// 选择了完整的日期范围
						this.customDateRange.startDate = event.range.before;
						this.customDateRange.endDate = event.range.after;
						
						// 立即生效：关闭弹窗并重新加载数据
						this.selectedTimeFilter = 'custom';
						this.resetExpandedState();
						this.closeDatePicker();
						
						// 重新加载数据
						this.loading = true;
						clearTimeout(this.refreshTimer);
						this.refreshTimer = setTimeout(async () => {
							try {
								await this.loadInspectionRecordsOptimized();
							} finally {
								this.loading = false;
							}
						}, CONFIG.TIME.DEBOUNCE_DELAY);
						
						uni.showToast({
							title: '日期范围选择完成',
							icon: 'success',
							duration: CONFIG.TIME.TOAST_DURATION
						});
					}
				} else if (event.fulldate) {
					// 单个日期选择
					if (!this.customDateRange.startDate || this.customDateRange.endDate) {
						// 选择开始日期或重新选择
						this.customDateRange.startDate = event.fulldate;
						this.customDateRange.endDate = '';

					} else {
						// 选择结束日期
						this.customDateRange.endDate = event.fulldate;
						
						// 确保开始日期不晚于结束日期
						if (new Date(this.customDateRange.startDate) > new Date(this.customDateRange.endDate)) {
							const temp = this.customDateRange.startDate;
							this.customDateRange.startDate = this.customDateRange.endDate;
							this.customDateRange.endDate = temp;
						}
						
						// 选择完成后立即生效：关闭弹窗并重新加载数据
						this.selectedTimeFilter = 'custom';
						this.resetExpandedState();
						this.closeDatePicker();
						
						// 重新加载数据
						this.loading = true;
						clearTimeout(this.refreshTimer);
						this.refreshTimer = setTimeout(async () => {
							try {
								await this.loadInspectionRecordsOptimized();
							} finally {
								this.loading = false;
							}
						}, CONFIG.TIME.DEBOUNCE_DELAY);
						
						uni.showToast({
							title: '日期范围选择完成',
							icon: 'success',
							duration: CONFIG.TIME.TOAST_DURATION
						});
					}
				}
			}, CONFIG.TIME.DEBOUNCE_DELAY);
		},

		// 月份切换事件
		onMonthSwitch(event) {
			// 安全地处理月份切换事件
			if (event && event.current && event.current.fulldate) {
				this.calendarDate = event.current.fulldate;
			} else if (event && event.year && event.month) {
				// 如果没有fulldate，手动构造日期
				const year = event.year;
				const month = String(event.month).padStart(2, '0');
				this.calendarDate = `${year}-${month}-01`;
			}
		},

		// 格式化选中的日期显示
		formatSelectedDate(dateStr) {
			if (!dateStr) return '';
			const date = new Date(dateStr);
			const month = date.getMonth() + 1;
			const day = date.getDate();
			const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
			const dayName = dayNames[date.getDay()];
			return `${month}月${day}日 ${dayName}`;
		},


		
		// 获取状态图标
		getStatusIcon(status) {
			const icons = {
				'completed': 'checkmarkempty',
				'pending': 'calendar',
				'not_cleaned': 'minus',
				'pending_rectification': 'info',
				'pending_review': 'eye',
				'missed': 'close'
			};
			return icons[status] || 'help';
		},
		
		// 获取问题类型中文显示
		getCategoryText(category) {
			const categoryMap = {
				'cleanliness': '清洁问题',
				'safety': '安全问题', 
				'equipment': '设备问题',
				'environment': '环境问题',
				'organization': '整理问题',
				'standardization': '标识问题',
				'other': '其他问题'
			};
			return categoryMap[category] || '其他';
		},
		
		// 获取周标题
		getWeekTitle(weekKey) {
			// 解析weekKey，例如 '2025-W03' -> 2025年第3周
			const match = weekKey.match(/(\d{4})-W(\d{2})/);
			if (!match) return weekKey;
			
			const year = parseInt(match[1]);
			const weekNum = parseInt(match[2]);
			const currentYear = new Date().getFullYear();
			const currentWeek = this.getWeekNumber(new Date());
			
			// 判断是否为当前周
			if (year === currentYear && weekNum === currentWeek) {
				const weekRange = this.getCurrentWeekRange();
				return `本周 (${weekRange.start}-${weekRange.end})`;
			} else if (year === currentYear && weekNum === currentWeek - 1) {
				return `上周 (第${weekNum}周)`;
			} else if (year === currentYear) {
				return `第${weekNum}周 (${year}年)`;
			} else {
				return `第${weekNum}周 (${year}年)`;
			}
		},
		
		// 切换周展开状态
		toggleWeek(weekKey) {
			const index = this.expandedWeeks.indexOf(weekKey);
			if (index > -1) {
				this.expandedWeeks.splice(index, 1);
			} else {
				this.expandedWeeks.push(weekKey);
			}
		},

		// 重置展开状态
		resetExpandedState() {
			this.expandedWeeks = [];
		},
		selectAndConfirmTimeFilter(option) {
			this.selectedTimeFilter = option.value;
			
			// 重置展开状态，让系统自动展开第一个分组
			this.resetExpandedState();
			
			this.$refs.timePopup.close();
			
			// 显示加载状态并重新加载数据
			this.loading = true;
			this.loadPageData().finally(() => {
				this.loading = false;
			});
		},
		// 优化的状态文本获取（使用缓存）
		getStatusText(status) {
			return this.processCache.statusMap[status] || status;
		},

		getIconColor(status) {
			const colorMap = {
				'not_cleaned': '#8E8E93',
				'pending': '#FF9500',
				'pending_rectification': '#FF3B30',
				'pending_review': '#007AFF',
				'completed': '#34C759',
				'missed': '#8B5CF6',
				'passed': '#34C759',
				'issues': '#FF3B30'
			};
			return colorMap[status] || '#8E8E93';
		},
		startInspection(record) {
			// 对于待检查状态，使用责任区ID
			const areaId = record.areaId || record.id;
			// 跳转到检查页面时，标记需要刷新数据
			this.needsRefresh = true;
			uni.navigateTo({
				url: `/pages/6s_pkg/inspection-detail?id=${areaId}&isRectification=${record.isRectificationRecheck || false}`
			});
		},
		viewRecordDetail(record) {
			// 如果有检查记录ID，说明这是基于实际检查记录的状态
			if (record.isInspectionRecord && record.inspectionRecordId) {
				uni.navigateTo({
					url: `/pages/6s_pkg/record-detail?id=${record.inspectionRecordId}&type=inspection`
				});
			} else {
				// 否则这是责任区级别的信息，跳转到责任区详情
				uni.navigateTo({
					url: `/pages/6s_pkg/area-detail?id=${record.areaId}`
				});
			}
		},
		
		// 查看整改详情
		async viewRectificationDetail(record) {
			try {
				uni.showLoading({ title: '加载中...' });
				
				const { callCloudFunction } = require('@/utils/auth.js');
				
				let result;
				
				// 根据记录状态使用不同的查询方式
				if (record.status === 'pending_review') {
									// 对于待复查状态，使用责任区ID查找整改任务
				const areaId = record.areaId || record.id;
					
					result = await callCloudFunction('hygiene-rectification', {
						action: 'getRectifications',
						data: {
							area_id: areaId,
							status: 'pending_review',
							pageSize: CONFIG.API.RECTIFICATION_PAGE_SIZE
						}
					});
					
	
					
					// 如果没找到 pending_review 状态的，尝试查找其他可能需要审核的状态
					let hasValidTask = false;
					if (result && result.success && result.data) {
						if (Array.isArray(result.data) && result.data.length > 0) {
							hasValidTask = true;
						} else if (result.data.list && Array.isArray(result.data.list) && result.data.list.length > 0) {
							hasValidTask = true;
						} else if (result.data.records && Array.isArray(result.data.records) && result.data.records.length > 0) {
							hasValidTask = true;
						}
					}
					
					if (!hasValidTask) {
						const possibleStatuses = ['submitted', 'completed', 'in_progress'];
						for (const status of possibleStatuses) {
							result = await callCloudFunction('hygiene-rectification', {
								action: 'getRectifications',
								data: {
									area_id: record.areaId || record.id,
									status: status,
									pageSize: CONFIG.API.RECTIFICATION_PAGE_SIZE
								}
							});
							
							if (result && result.success && result.data) {
								let task = null;
								if (Array.isArray(result.data) && result.data.length > 0) {
									task = result.data[0];
								} else if (result.data.list && Array.isArray(result.data.list) && result.data.list.length > 0) {
									task = result.data.list[0];
								} else if (result.data.records && Array.isArray(result.data.records) && result.data.records.length > 0) {
									task = result.data.records[0];
								}
								
								if (task) {
									// 检查是否真的需要审核（有提交时间）
									const hasSubmitted = task.submitted_at || task.rectification_submit_time || task.completion_time;
									if (hasSubmitted) {
										break;
									}
								}
							}
						}
					}
				} else {
					// 对于待整改状态，优先通过责任区ID查找最新的整改任务
					const areaId = record.areaId || record.id;
					
					result = await callCloudFunction('hygiene-rectification', {
						action: 'getRectifications', 
						data: {
							area_id: areaId,
							status: 'pending_rectification',
							pageSize: CONFIG.API.RECTIFICATION_PAGE_SIZE
						}
					});
					
					// 如果通过状态查找失败，再尝试通过检查记录ID查找
					if (!result || !result.success || !result.data || 
						(!Array.isArray(result.data) || result.data.length === 0) &&
						(!result.data.list || result.data.list.length === 0) &&
						(!result.data.records || result.data.records.length === 0)) {
						
						result = await callCloudFunction('hygiene-rectification', {
							action: 'getRectifications',
							data: {
								inspection_record_id: record.id,
								pageSize: CONFIG.API.RECTIFICATION_PAGE_SIZE
							}
						});
					}
				}
				
				uni.hideLoading();
				
				if (result && result.success && result.data) {
					let rectificationTask = null;
					
					// 处理不同的数据结构
					if (Array.isArray(result.data) && result.data.length > 0) {
						rectificationTask = result.data[0];
					} else if (result.data.list && Array.isArray(result.data.list) && result.data.list.length > 0) {
						rectificationTask = result.data.list[0];
					} else if (result.data.records && Array.isArray(result.data.records) && result.data.records.length > 0) {
						rectificationTask = result.data.records[0];
					}
					
					if (rectificationTask) {
						const taskId = rectificationTask._id || rectificationTask.id;
						
						if (taskId) {
							// 跳转到检查员专用的整改详情页面
							uni.navigateTo({
								url: `/pages/6s_pkg/inspector-rectification-detail?taskId=${taskId}`
							});
						} else {
							uni.showModal({
								title: '错误',
								content: '整改任务数据异常，缺少ID字段',
								showCancel: false
							});
						}
					} else {
						uni.showModal({
							title: '提示',
							content: '未找到对应的整改任务',
							showCancel: false
						});
					}
				} else {
					uni.showModal({
						title: '提示',
						content: '未找到对应的整改任务',
						showCancel: false
					});
				}
			} catch (error) {
				uni.hideLoading();
				uni.showModal({
					title: '加载失败',
					content: '无法加载整改任务详情，请稍后重试',
					showCancel: false
				});
			}
		},
		
		// 从检查记录审核整改任务
		async reviewRectificationFromRecord(record) {
			try {
				uni.showLoading({ title: '查找整改任务...' });
				
				const { callCloudFunction } = require('@/utils/auth.js');
				const areaId = record.areaId || record.id;
				
				// 查找该责任区的整改任务
				let result = await callCloudFunction('hygiene-rectification', {
					action: 'getRectifications',
					data: {
						area_id: areaId,
						status: 'pending_review',
						pageSize: CONFIG.API.RECTIFICATION_PAGE_SIZE
					}
				});
				
				// 如果没找到 pending_review 状态的，尝试查找其他可能需要审核的状态
				if (!result || !result.success || !result.data || 
					(!Array.isArray(result.data) && (!result.data.list || result.data.list.length === 0) && (!result.data.records || result.data.records.length === 0))) {
					
					const possibleStatuses = ['submitted', 'completed', 'in_progress'];
					for (const status of possibleStatuses) {
						result = await callCloudFunction('hygiene-rectification', {
							action: 'getRectifications',
							data: {
								area_id: areaId,
								status: status,
								pageSize: CONFIG.API.RECTIFICATION_PAGE_SIZE
							}
						});
						
						if (result && result.success && result.data) {
							let task = null;
							if (Array.isArray(result.data) && result.data.length > 0) {
								task = result.data[0];
							} else if (result.data.list && Array.isArray(result.data.list) && result.data.list.length > 0) {
								task = result.data.list[0];
							} else if (result.data.records && Array.isArray(result.data.records) && result.data.records.length > 0) {
								task = result.data.records[0];
							}
							
							if (task) {
								// 检查是否真的需要审核（有提交时间）
								const hasSubmitted = task.submitted_at || task.rectification_submit_time || task.completion_time;
								if (hasSubmitted) {
									break;
								}
							}
						}
					}
				}
				
				uni.hideLoading();
				
				if (result && result.success && result.data) {
					let rectificationTask = null;
					
					// 处理不同的数据结构
					if (Array.isArray(result.data) && result.data.length > 0) {
						rectificationTask = result.data[0];
					} else if (result.data.list && Array.isArray(result.data.list) && result.data.list.length > 0) {
						rectificationTask = result.data.list[0];
					} else if (result.data.records && Array.isArray(result.data.records) && result.data.records.length > 0) {
						rectificationTask = result.data.records[0];
					}
					
									if (rectificationTask) {
					const taskId = rectificationTask._id || rectificationTask.id;
					
					if (taskId) {
						// 跳转到整改复查页面时，标记需要刷新数据
						this.needsRefresh = true;
						uni.navigateTo({
							url: `/pages/6s_pkg/rectification-review?taskId=${taskId}`
						});
					} else {
						uni.showModal({
							title: '错误',
							content: '整改任务数据异常，缺少ID字段',
							showCancel: false
						});
					}
					} else {
						uni.showModal({
							title: '提示',
							content: '未找到对应的整改任务',
							showCancel: false
						});
					}
				} else {
					uni.showModal({
						title: '提示',
						content: '未找到对应的整改任务',
						showCancel: false
					});
				}
					} catch (error) {
			uni.hideLoading();
			uni.showModal({
				title: '加载失败',
				content: '无法加载整改任务，请稍后重试',
				showCancel: false
			});
		}
		},
		
		// 优化的格式化日期时间（使用缓存）
		formatDateTimeOptimized(dateString) {
			if (!dateString) return '--';
			
			const cacheKey = dateString.toString();
			if (this.processCache.formattedDatesCache.has(cacheKey)) {
				return this.processCache.formattedDatesCache.get(cacheKey);
			}
			
			try {
				let date;
				if (typeof dateString === 'string') {
					if (dateString.includes('T') || dateString.includes('Z')) {
						date = new Date(dateString);
					} else {
						date = new Date(dateString.replace(/-/g, '/'));
					}
				} else {
					date = new Date(dateString);
				}
				
				if (isNaN(date.getTime())) {
					this.processCache.formattedDatesCache.set(cacheKey, '--');
					return '--';
				}
				
				const formatted = `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
				this.processCache.formattedDatesCache.set(cacheKey, formatted);
				return formatted;
			} catch (error) {
				this.processCache.formattedDatesCache.set(cacheKey, '--');
				return '--';
			}
		},

		// 保留原方法以防其他地方调用
		formatDateTime(dateString) {
			return this.formatDateTimeOptimized(dateString);
		},

		// 处理清理记录更新事件
		handleCleaningRecordUpdated(data) {
			// 当有清理记录更新时，静默刷新检查任务状态
			this.silentRefreshData();
		},

		// 处理检查记录更新事件
		handleInspectionRecordUpdated(data) {
			// 当有检查记录更新时，静默刷新检查任务状态
			this.silentRefreshData();
		},

		// 处理整改记录更新事件
		handleRectificationRecordUpdated(data) {
			// 当有整改记录更新时，静默刷新检查任务状态
			this.silentRefreshData();
		},

		// 优化的静默刷新数据（不显示加载状态）
		async silentRefreshData() {
			if (this.loading) return; // 如果正在加载，跳过
			
			try {
				// 清除缓存以获取最新数据
				this.processCache.timeCalculations = null;
				this.processCache.weekKeyCache.clear();
				this.processCache.formattedDatesCache.clear();
				
				// 重新初始化时间计算
				this.initTimeCalculations();
				
				// 静默重新加载检查记录，不显示loading状态
				await this.loadInspectionRecordsOptimized();
				// 清除刷新标记，避免用户返回页面时再次显示loading
				this.needsRefresh = false;
			} catch (error) {
				// 静默处理错误，不显示错误提示
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.page-container {
	min-height: 100vh;
	background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
	width: 100%;
	overflow-x: hidden; /* 防止水平滚动 */
	box-sizing: border-box;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
}

.card {
	background: white;
	border-radius: 16rpx;
	margin: 0 32rpx 24rpx 32rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.card:first-child {
	margin-top: 24rpx;
}

.card-header {
	padding: 32rpx 32rpx 16rpx 32rpx;
}

.header-content {
	display: flex;
	flex-direction: row;
	align-items: baseline;
	justify-content: space-between;
}

.filters-row {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.status-filter-indicator {
	display: flex;
	align-items: center;
	gap: 6rpx;
	background: rgba(0, 122, 255, 0.1);
	padding: 12rpx 20rpx;
	border-radius: 20rpx;
	border: 2rpx solid rgba(0, 122, 255, 0.2);
	cursor: pointer;
	transition: all 0.3s ease;
	
	&:active {
		background: rgba(0, 122, 255, 0.15);
		transform: scale(0.98);
	}
}

.filter-text {
	font-size: 24rpx;
	color: #007AFF;
	font-weight: 500;
}

.card-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #1C1C1E;
	margin-bottom: 0;
}

.card-subtitle {
	font-size: 26rpx;
	color: #8E8E93;
}

/* 时间选择器样式 */
.time-selector {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 12rpx 20rpx;
	background: rgba(0, 122, 255, 0.1);
	border-radius: 20rpx;
	border: 2rpx solid rgba(0, 122, 255, 0.2);
	cursor: pointer;
	transition: all 0.3s ease;
	
	&:active {
		background: rgba(0, 122, 255, 0.15);
		transform: scale(0.98);
	}
	
	&.small {
		padding: 8rpx 16rpx;
		border-radius: 16rpx;
		background: rgba(142, 142, 147, 0.1);
		border-color: rgba(142, 142, 147, 0.2);
	}
}

.time-text {
	font-size: 24rpx;
	color: #007AFF;
	font-weight: 500;
	
	.small & {
		font-size: 24rpx;
		color: #8E8E93;
	}
}

/* 弹窗样式 */
.time-popup {
	background: white;
	border-radius: 24rpx 24rpx 0 0;
	padding: 0 0 env(safe-area-inset-bottom) 0;
}

.popup-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx 32rpx 24rpx 32rpx;
	border-bottom: 1rpx solid #F2F2F7;
}

.popup-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #1C1C1E;
}

.popup-close {
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #F2F2F7;
	border-radius: 50%;
}

.time-options {
	padding: 24rpx 32rpx;
}

.time-option {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 0;
	border-bottom: 1rpx solid #F2F2F7;
	position: relative;
	
	&:last-child {
		border-bottom: none;
	}
	
	&.active {
		.option-text {
			color: #007AFF;
			font-weight: 600;
		}
	}
}

.option-left {
	display: flex;
	align-items: center;
	flex: 1;
}

.option-content {
	margin-left: 16rpx;
	flex: 1;
}

.option-title-row {
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-bottom: 4rpx;
}

.option-text {
	font-size: 28rpx;
	color: #1C1C1E;
	flex: 1;
}

.option-desc {
	font-size: 24rpx;
	color: #8E8E93;
	display: block;
}

.smart-badge {
	background: linear-gradient(135deg, #FF6B35, #FF8E53);
	color: white;
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	font-weight: 600;
	animation: pulse 2s infinite;
}

@keyframes pulse {
	0% { opacity: 1; }
	50% { opacity: 0.7; }
	100% { opacity: 1; }
}

.time-option.recommended {
	background: linear-gradient(135deg, rgba(255, 107, 53, 0.05), rgba(255, 142, 83, 0.02));
	border-left: 4rpx solid #FF6B35;
}

/* ======== 新的日历式日期选择器样式 ======== */
.date-picker-popup {
	background: white;
	padding: 0;
	max-height: 80vh;
	overflow-y: auto;
}

.quick-date-section,
.range-date-section {
	padding: 24rpx 32rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #1C1C1E;
	margin-bottom: 24rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}



.quick-options {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
	margin-bottom: 32rpx;
}

.quick-option {
	flex: 1;
	min-width: 120rpx;
	padding: 24rpx 16rpx;
	border: 2rpx solid #E5E5EA;
	border-radius: 16rpx;
	text-align: center;
	transition: all 0.2s;
}

.quick-option.active {
	border-color: #007AFF;
	background: rgba(0, 122, 255, 0.1);
}

.quick-text {
	font-size: 28rpx;
	color: #1C1C1E;
}

.quick-option.active .quick-text {
	color: #007AFF;
	font-weight: 600;
}

.date-inputs {
	margin: 32rpx 0;
}

.date-input-group {
	margin-bottom: 24rpx;
}

.input-label {
	font-size: 26rpx;
	color: #8E8E93;
	margin-bottom: 12rpx;
	display: block;
}

.date-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 24rpx;
	border: 2rpx solid #E5E5EA;
	border-radius: 16rpx;
	background: #F8F9FA;
	transition: all 0.2s;
}

.date-input:active {
	border-color: #007AFF;
	background: rgba(0, 122, 255, 0.05);
}

.date-text {
	font-size: 28rpx;
	color: #1C1C1E;
	flex: 1;
}

.mode-switch {
	text-align: center;
	padding: 16rpx 0;
}

.switch-text {
	font-size: 26rpx;
	color: #007AFF;
	padding: 12rpx 0;
}

.popup-actions {
	display: flex;
	gap: 16rpx;
	padding: 24rpx 32rpx 32rpx;
	border-top: 1rpx solid #E5E5EA;
}

.action-btn {
	flex: 1;
	padding: 20rpx;
	border-radius: 16rpx;
	text-align: center;
	font-size: 28rpx;
	font-weight: 600;
	transition: all 0.2s;
}

.action-btn.cancel {
	background: #F8F9FA;
	color: #8E8E93;
	border: 2rpx solid #E5E5EA;
}

.action-btn.confirm {
	background: linear-gradient(135deg, #007AFF, #5AC8FA);
	color: white;
	box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
	
	&.disabled {
		background: #E5E5EA;
		color: #8E8E93;
		box-shadow: none;
		cursor: not-allowed;
	}
}

.action-btn:active {
	transform: scale(0.95);
}

/* 日历组件样式 */
.calendar-section {
	margin: 24rpx 0;
}

.calendar-header {
	margin-bottom: 24rpx;
}



.calendar-tip {
	font-size: 24rpx;
	color: #8E8E93;
	display: block;
}

.selected-range {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 24rpx;
	padding: 20rpx;
	background: #F8F9FA;
	border-radius: 16rpx;
	gap: 16rpx;
}

.range-item {
	text-align: center;
	flex: 1;
}

.range-label {
	font-size: 22rpx;
	color: #8E8E93;
	display: block;
	margin-bottom: 4rpx;
}

.range-value {
	font-size: 26rpx;
	color: #1C1C1E;
	font-weight: 600;
	display: block;
}

.range-separator {
	font-size: 24rpx;
	color: #007AFF;
	font-weight: 600;
}



.option-check {
	width: 32rpx;
	height: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.popup-footer {
	padding: 24rpx 32rpx;
	border-top: 1rpx solid #F2F2F7;
}

.popup-btn {
	width: 100%;
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 16rpx;
	font-size: 32rpx;
	font-weight: 600;
	
	&.confirm {
		background: #007AFF;
		color: white;
	}
}

.stats-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 32rpx;
	padding: 32rpx;
	text-align: center;
	
	&.stats-grid-four {
		grid-template-columns: repeat(4, 1fr);
		gap: 24rpx;
	}
	
	&.stats-grid-five {
		grid-template-columns: repeat(5, 1fr);
		gap: 16rpx;
		padding: 24rpx 16rpx;
	}
	
	&.stats-grid-six {
		grid-template-columns: repeat(3, 1fr);
		gap: 16rpx;
		padding: 24rpx 16rpx;
		
		@media (min-width: 750rpx) {
			grid-template-columns: repeat(6, 1fr);
		}
	}
}

.stats-item {
	text-align: center;
	cursor: pointer;
	transition: all 0.3s ease;
	border-radius: 12rpx;
	padding: 12rpx 8rpx;
	
	&:hover {
		background: rgba(0, 122, 255, 0.05);
	}
	
	&.active {
		background: rgba(0, 122, 255, 0.1);
		border: 2rpx solid #007AFF;
		transform: scale(1.05);
	}
}

/* 统计区域加载状态 */
.stats-loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx 24rpx;
	min-height: 160rpx;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #007AFF;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 24rpx;
}

.loading-text {
	font-size: 28rpx;
	color: #8E8E93;
	text-align: center;
}

.list-loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 20rpx;
	background: #FFFFFF;
	border-radius: 24rpx;
	margin: 20rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.stats-number {
	font-size: 48rpx;
	font-weight: bold;
	margin-bottom: 8rpx;

	&.not-cleaned { color: #8E8E93; }
	&.warning { color: #FF9500; }
	&.danger { color: #FF3B30; }
	&.review { color: #007AFF; }
	&.success { color: #34C759; }
	&.missed { color: #8B5CF6; }
}

.stats-label {
	font-size: 24rpx;
	color: #8E8E93;
}

.list-item {
	display: flex;
	align-items: center;
	padding: 24rpx 32rpx;
	border-bottom: 1rpx solid #F2F2F7;

	&:last-child {
		border-bottom: none;
	}

}

.list-item-icon {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;

	&.icon-bg-not_cleaned { background: rgba(142, 142, 147, 0.15); }
	&.icon-bg-pending { background: rgba(255, 149, 0, 0.1); }
	&.icon-bg-pending_rectification { background: rgba(255, 59, 48, 0.1); }
	&.icon-bg-pending_review { background: rgba(0, 122, 255, 0.1); }
	&.icon-bg-completed { background: rgba(52, 199, 89, 0.1); }
	&.icon-bg-missed { background: rgba(139, 92, 246, 0.1); }
	&.icon-bg-passed { background: rgba(52, 199, 89, 0.1); }
	&.icon-bg-issues { background: rgba(255, 59, 48, 0.1); }
	&.icon-bg-warning { background: rgba(255, 149, 0, 0.1); }
}

.list-item-content {
	flex: 1;
}

.list-item-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #1C1C1E;
	margin-bottom: 4rpx;
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.list-item-subtitle {
	font-size: 24rpx;
	color: #8E8E93;
}

.list-item-right {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 8rpx;
	font-size: 24rpx;
	font-weight: 600;

	&.status-not_cleaned { background: #F2F2F7; color: #8E8E93; }
	&.status-pending { background: #FFF4E6; color: #FF9500; }
	&.status-pending_rectification { background: #FFE6E6; color: #FF3B30; }
	&.status-pending_review { background: #E6F3FF; color: #007AFF; }
	&.status-completed { background: #E8F5E8; color: #34C759; }
	&.status-missed { background: #F3F0FF; color: #8B5CF6; }
	&.status-passed { background: #E8F5E8; color: #34C759; }
	&.status-issues { background: #FFE6E6; color: #FF3B30; }
	&.status-warning { background: #FFF4E6; color: #FF9500; }
	&.status-rejected { background: #FFE6E6; color: #FF3B30; }
	&.status-verified { background: #E8F5E8; color: #34C759; }
	&.status-approved { background: #E8F5E8; color: #34C759; }
	&.status-in_progress { background: #E6F3FF; color: #007AFF; }
}

.card-body {
	padding: 0 32rpx 32rpx 32rpx;
}

.more-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	padding: 24rpx;
	border: 2rpx dashed #E5E5EA;
	border-radius: 12rpx;
	font-size: 26rpx;
	color: #007AFF;
}

.bottom-safe-area {
	height: 40rpx;
}

// 分类标签样式
.category-scroll {
	width: 100%;
}

.category-tabs {
	display: flex;
	padding: 8rpx 32rpx 16rpx 32rpx;
	gap: 16rpx;
	white-space: nowrap;
}

.category-tab {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 6rpx;
	padding: 12rpx 20rpx;
	border-radius: 20rpx;
	background: #F2F2F7;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
	flex-shrink: 0;
	
	&.active {
		background: #E6F3FF;
		border-color: #007AFF;
	}
}

.tab-text {
	font-size: 24rpx;
	font-weight: 500;
	color: #8E8E93;
	
	.category-tab.active & {
		color: #007AFF;
	}
}

.tab-count {
	font-size: 20rpx;
	color: #C7C7CC;
	background: rgba(199, 199, 204, 0.2);
	border-radius: 10rpx;
	padding: 2rpx 8rpx;
	min-width: 32rpx;
	text-align: center;
	
	.category-tab.active & {
		color: #007AFF;
		background: rgba(0, 122, 255, 0.15);
	}
}

// 标签容器
.badges-container {
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-left: 12rpx;
}

// 区域类型标签样式
.area-type-badge {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	border: 1rpx solid;
	
	&.type-fixed {
		background: rgba(52, 199, 89, 0.1);
		border-color: rgba(52, 199, 89, 0.2);
		
		.badge-text {
			color: #34C759;
		}
	}
	
	&.type-public {
		background: rgba(0, 122, 255, 0.1);
		border-color: rgba(0, 122, 255, 0.2);
		
		.badge-text {
			color: #007AFF;
		}
	}
}

// 整改标识样式
.rectification-badge {
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 149, 0, 0.1);
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	border: 1rpx solid rgba(255, 149, 0, 0.2);
}

.badge-text {
	font-size: 20rpx;
	color: #FF9500;
	font-weight: 500;
	white-space: nowrap;
}

// 时间分组样式
.time-group {
	margin-bottom: 16rpx;
}

.time-group-header {
	padding: 24rpx 32rpx;
	background: #F8F9FA;
	border-radius: 12rpx;
	margin-bottom: 8rpx;
}

.time-group-title {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.time-title {
	font-size: 30rpx;
	font-weight: 500;
	color: #1D1D1F;
	flex: 1;
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.time-count {
	font-size: 24rpx;
	color: #8E8E93;
	background: #E5E7EB;
	padding: 4rpx 12rpx;
	border-radius: 16rpx;
	min-width: 48rpx;
	text-align: center;
}

/* 加载状态样式 */
.loading-container {
	padding: 80rpx 32rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 24rpx;
}

.loading-spinner {
	width: 40rpx;
	height: 40rpx;
	border: 4rpx solid #E5E7EB;
	border-top: 4rpx solid #007AFF;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #8E8E93;
	text-align: center;
}

@media (max-width: 414px) {
	.card {
		margin: 0 16rpx 24rpx 16rpx;
	}
	
	.card:first-child {
		margin-top: 24rpx;
	}
}
</style> 