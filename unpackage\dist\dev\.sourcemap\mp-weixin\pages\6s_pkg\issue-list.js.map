{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s_pkg/issue-list.vue?1f5e", "webpack:///D:/Xwzc/pages/6s_pkg/issue-list.vue?f655", "webpack:///D:/Xwzc/pages/6s_pkg/issue-list.vue?396d", "webpack:///D:/Xwzc/pages/6s_pkg/issue-list.vue?80eb", "uni-app:///pages/6s_pkg/issue-list.vue", "webpack:///D:/Xwzc/pages/6s_pkg/issue-list.vue?58ca", "webpack:///D:/Xwzc/pages/6s_pkg/issue-list.vue?8663"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "currentTab", "publishedIssues", "draftIssues", "loading", "dataLoaded", "needsRefresh", "computed", "currentList", "publishedCount", "draftCount", "totalCount", "onLoad", "uni", "onUnload", "onShow", "methods", "loadData", "loadPublishedIssues", "require", "callCloudFunction", "action", "pageSize", "page", "result", "cachedData", "title", "icon", "duration", "formatIssueItem", "id", "number", "description", "location", "responsible", "responsible_id", "deadline", "priority", "status", "category", "severity", "images", "createdAt", "updatedAt", "formatDate", "loadDraftIssues", "drafts", "draft", "userName", "getUserNameById", "userId", "switchTab", "handleItemClick", "viewIssueDetail", "url", "editPublishedIssue", "changeIssueStatus", "statusOptions", "value", "label", "itemList", "success", "selectedStatus", "updateIssueStatus", "issueId", "newStatus", "actualStatus", "issue_id", "action_type", "editDraft", "publishDraft", "content", "showCancel", "confirmText", "res", "responsibleId", "responsibleName", "submitData", "location_info", "location_type", "location_name", "location_description", "inspection_info", "inspection_date", "inspection_type", "assigned_to", "assigned_to_name", "photos", "tags", "expected_completion_date", "fromDraft", "draftId", "deleteDraft", "removeDraftFromLocal", "addIssue", "getIssueNumber", "getStatusText", "formatTime", "delay", "handleIssueUpdated", "handleDraftUpdated", "setTimeout", "silentRefreshData", "Promise", "silentRefreshPublishedData", "silentRefreshDraftData"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AAAomB,CAAgB,8nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCsIxnB;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;;IAEA;IACAC;IACAA;EACA;EACAC;IACA;IACAD;IACAA;EACA;EACAE;IACA;IACA;IACA;MACA;MACA;IACA;EACA;;EACAC;IACA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBAAA,WAEAC,gGAEA;gBAAA;gBAAA,OACAC;kBACAC;kBACArB;oBACAsB;oBACAC;kBACA;gBACA;cAAA;gBANAC;gBAQA;kBACA;oBAAA;kBAAA;;kBAEA;kBACA;oBACAX;kBACA;oBACA;kBAAA;gBAEA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIA;gBACA;kBACAY;kBACA;oBACA;oBACAZ;sBACAa;sBACAC;sBACAC;oBACA;kBACA;oBACA;kBACA;gBACA;kBACA;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MACA;QACAC;QACAC;QACAL;QACAM;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC,mDAEA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA,uCACAA;gBAAA;gBAAA;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACAD;kBACAA;kBACAA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAKA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAE;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,YAEA9B;gBAAA;gBAAA,OAEAC;kBACAC;kBACArB;oBACAkD;kBACA;gBACA;cAAA;gBALA1B;gBAAA,MAOAA;kBAAA;kBAAA;gBAAA;gBAAA,kCACAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,kCAMA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA2B;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACAxC;QACAyC;MACA;IACA;IAEA;IACAC;MACA;MACA1C;QACAyC;MACA;IACA;IAEA;IACAE;MAAA;MACA;MACA;MAEA;MACA;;MAEA;MACA;QACAC,iBACA;UAAAC;UAAAC;QAAA,EACA;MACA;QACA;QACA;UACAF,iBACA;YAAAC;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,EACA;QACA;UACA;UACAF,iBACA;YAAAC;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,EACA;QACA;UACAF,iBACA;YAAAC;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA;UAAA,CACA;QACA;UACAF,iBACA;YAAAC;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,EACA;QACA;UACA;UACAF,iBACA;YAAAC;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,EACA;QACA;UACAF,iBACA;YAAAC;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,EACA;QACA;UACA;UACAF,iBACA;YAAAC;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA;UACA;UAAA,CACA;QACA;MACA;;MAEA9C;QACA+C;UAAA;QAAA;QACAC;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBACAC;oBAAA,MACAA;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA,OACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAEA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,IAGAC;kBAAA;kBAAA;gBAAA;gBACAnD;kBACAa;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAIAsC;kBAAA;kBAAA;gBAAA;gBACApD;kBACAa;kBACAC;gBACA;gBAAA;cAAA;gBAIAd;kBAAAa;gBAAA;gBAAA,YAEAP,iGAEA;gBACA+C;gBACA;kBACAA;gBACA;gBAAA;gBAAA,OAEA9C;kBACAC;kBACArB;oBACAmE;oBACA7B;oBACA8B;kBACA;gBACA;cAAA;gBAPA5C;gBAAA,MASAA;kBAAA;kBAAA;gBAAA;gBACAX;gBACAA;kBACAa;kBACAC;gBACA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAd;gBACAA;kBACAa;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAIA;IACA0C;MACA;MACAxD;QACAyC;MACA;IACA;IAEA;IACAgB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACAzD;kBACAa;kBACA6C;kBACAC;kBACAC;kBACAZ;oBACA;kBACA;gBACA;gBAAA;cAAA;gBAIAhD;kBACAa;kBACA6C;kBACAV;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAa;gCAAA;gCAAA;8BAAA;8BAAA;8BAEA7D;gCAAAa;8BAAA;8BAAA,YAEAP,iGAEA;8BACAwD;8BACAC,+DAEA;8BACA;gCACA;gCACA;kCACAD;gCACA;8BACA;;8BAIA;8BACAE;gCACAnD;gCACAM;gCACA8C;kCACAC;kCACAC;kCACAC;gCACA;gCACAC;kCACAC;kCACAC;gCACA;gCACA7C;gCACAC;gCACAH;gCACAgD;gCACAC;gCACAC;gCACAC;gCACAC;8BACA,GAEA;8BAAA;8BAAA,OACArE;gCACAC;gCACArB;8BACA;4BAAA;8BAHAwB;8BAAA,MAKAA;gCAAA;gCAAA;8BAAA;8BACA;8BACA;8BACA;;8BAEA;8BAAA;8BAAA,OACA;4BAAA;8BAEAX;8BACAA;gCACAa;gCACAC;8BACA;;8BAEA;8BACAd;gCACAQ;gCACA2C;gCACA0B;8BACA;;8BAEA;8BACA7E;gCACAQ;gCACAsE;8BACA;8BAAA;8BAAA;4BAAA;8BAAA,MAEA;4BAAA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAIA9E;8BACAA;gCACAa;gCACAC;8BACA;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAGA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAiE;MAAA;MACA/E;QACAa;QACA6C;QACAV;UACA;YACA;YACA;YACAhD;cACAa;cACAC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAkE;MACA;QACA;QACA;UAAA;QAAA;QACAhF;MACA;QACA;MAAA;IAEA;IAEA;IACAiF;MACAjF;QACAyC;MACA;IACA;IAEA;IACAyC;MACA;MAEA;;MAEA;MACA;QACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;MACA;MAEA;QAAA;QACA;MACA;QAAA;QACA;MACA;QAAA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACAC;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAIAC,aACA,qCACA,gCACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,YAEArF,iGAEA;gBAAA;gBAAA,OACAC;kBACAC;kBACArB;oBACAsB;oBACAC;kBACA;gBACA;cAAA;gBANAC;gBAQA;kBACA;oBAAA;kBAAA;;kBAEA;kBACA;oBACAX;kBACA;oBACA;kBAAA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IAEA;IACA4F;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA3D,mDAEA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA,wCACAA;gBAAA;gBAAA;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACAD;kBACAA;kBACAA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAKA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClyBA;AAAA;AAAA;AAAA;AAAuqC,CAAgB,6oCAAG,EAAC,C;;;;;;;;;;;ACA3rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/issue-list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/issue-list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./issue-list.vue?vue&type=template&id=ed1d1fe4&scoped=true&\"\nvar renderjs\nimport script from \"./issue-list.vue?vue&type=script&lang=js&\"\nexport * from \"./issue-list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./issue-list.vue?vue&type=style&index=0&id=ed1d1fe4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ed1d1fe4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/issue-list.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./issue-list.vue?vue&type=template&id=ed1d1fe4&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.loading ? _vm.currentList.length : null\n  var l0 =\n    !_vm.loading && g0 > 0\n      ? _vm.__map(_vm.currentList, function (issue, index) {\n          var $orig = _vm.__get_orig(issue)\n          var m0 = issue.number ? _vm.getIssueNumber(issue.number) : null\n          var m1 = _vm.getStatusText(issue.status)\n          var m2 = _vm.formatTime(issue.createdAt || issue.updatedAt)\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            m2: m2,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./issue-list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./issue-list.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page-container\">\n    <!-- 顶部统计 -->\n    <view class=\"stats-section\">\n      <view class=\"stats-card\">\n        <view class=\"stats-item\">\n          <view class=\"stats-number\">{{ publishedCount }}</view>\n          <view class=\"stats-label\">已发布</view>\n        </view>\n        <view class=\"stats-item\">\n          <view class=\"stats-number\">{{ draftCount }}</view>\n          <view class=\"stats-label\">草稿</view>\n        </view>\n        <view class=\"stats-item\">\n          <view class=\"stats-number\">{{ totalCount }}</view>\n          <view class=\"stats-label\">总计</view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 分类切换 -->\n    <view class=\"tab-section\">\n      <view class=\"tab-container\">\n        <view \n          class=\"tab-item\" \n          :class=\"{ 'active': currentTab === 'published' }\"\n          @click=\"switchTab('published')\"\n        >\n          <text>已发布 ({{ publishedCount }})</text>\n        </view>\n        <view \n          class=\"tab-item\" \n          :class=\"{ 'active': currentTab === 'draft' }\"\n          @click=\"switchTab('draft')\"\n        >\n          <text>草稿 ({{ draftCount }})</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 问题列表 -->\n    <view class=\"list-section\">\n      <!-- 加载状态 -->\n      <view v-if=\"loading\" class=\"loading-container\">\n        <uni-icons type=\"spinner-cycle\" size=\"32\" color=\"#007AFF\"></uni-icons>\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n      \n      <template v-else-if=\"currentList.length > 0\">\n        <view \n          v-for=\"(issue, index) in currentList\" \n          :key=\"index\"\n          class=\"issue-item\"\n          @click=\"handleItemClick(issue)\"\n        >\n          <view class=\"issue-header\">\n            <view class=\"issue-title-section\">\n              <view class=\"issue-number\" v-if=\"issue.number\">#{{ getIssueNumber(issue.number) }}</view>\n            <view class=\"issue-title\">{{ issue.title }}</view>\n            </view>\n            <view class=\"issue-status\" :class=\"'status-' + issue.status\">\n              {{ getStatusText(issue.status) }}\n            </view>\n          </view>\n          <view class=\"issue-content\">\n            <view class=\"issue-info\">\n              <view class=\"info-item\">\n                <uni-icons type=\"location\" size=\"14\" color=\"#8E8E93\" />\n                <text>{{ issue.location || '未设置' }}</text>\n              </view>\n              <view class=\"info-item\">\n                <uni-icons type=\"person\" size=\"14\" color=\"#8E8E93\" />\n                <text>{{ issue.responsible || '未指定' }}</text>\n              </view>\n            </view>\n            <view class=\"issue-time\">\n              {{ formatTime(issue.createdAt || issue.updatedAt) }}\n            </view>\n          </view>\n          <!-- 操作按钮 -->\n          <view v-if=\"issue.status === 'draft'\" class=\"issue-actions\">\n            <view class=\"action-btn edit\" @click.stop=\"editDraft(issue)\">\n              <uni-icons type=\"compose\" size=\"14\" color=\"#007AFF\" />\n              <text>编辑</text>\n            </view>\n            <view class=\"action-btn publish\" @click.stop=\"publishDraft(issue)\">\n              <uni-icons type=\"upload\" size=\"14\" color=\"#34C759\" />\n              <text>发布</text>\n            </view>\n            <view class=\"action-btn delete\" @click.stop=\"deleteDraft(issue)\">\n              <uni-icons type=\"trash\" size=\"14\" color=\"#FF3B30\" />\n              <text>删除</text>\n            </view>\n          </view>\n          \n          <!-- 已发布问题操作按钮 -->\n          <view v-else class=\"issue-actions\">\n            <view class=\"action-btn view\" @click.stop=\"viewIssueDetail(issue)\">\n              <uni-icons type=\"eye\" size=\"14\" color=\"#007AFF\" />\n              <text>查看详情</text>\n            </view>\n            <view class=\"action-btn edit\" @click.stop=\"editPublishedIssue(issue)\" v-if=\"issue.status !== 'approved'\">\n              <uni-icons type=\"compose\" size=\"14\" color=\"#34C759\" />\n              <text>编辑</text>\n            </view>\n            <view class=\"action-btn status\" @click.stop=\"changeIssueStatus(issue)\">\n              <uni-icons type=\"loop\" size=\"14\" color=\"#FF9500\" />\n              <text>{{ issue.status === 'approved' ? '重新打开' : '状态' }}</text>\n            </view>\n            <!-- 移除关闭按钮，检查通过就是自然结束 -->\n          </view>\n        </view>\n      </template>\n      \n      <!-- 空状态 -->\n      <template v-else>\n        <p-empty-state\n          type=\"data\"\n          :text=\"currentTab === 'draft' ? '暂无草稿' : '暂无已发布问题'\"\n          :description=\"currentTab === 'draft' ? '在问题录入时点击保存草稿即可保存' : '点击右下角按钮新增问题'\"\n        ></p-empty-state>\n      </template>\n    </view>\n\n    <!-- 添加按钮 -->\n    <view class=\"fab-container\">\n      <view class=\"fab-btn\" @click=\"addIssue\">\n        <uni-icons type=\"plus\" size=\"32\" color=\"#ffffff\" />\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'IssueList',\n  data() {\n    return {\n      currentTab: 'published',\n      publishedIssues: [],\n      draftIssues: [],\n      loading: false,\n      dataLoaded: false, // 标记数据是否已加载\n      needsRefresh: false // 标记是否需要刷新数据\n    }\n  },\n  computed: {\n    currentList() {\n      return this.currentTab === 'published' ? this.publishedIssues : this.draftIssues;\n    },\n    publishedCount() {\n      return this.publishedIssues.length;\n    },\n    draftCount() {\n      return this.draftIssues.length;\n    },\n    totalCount() {\n      return this.publishedCount + this.draftCount;\n    }\n  },\n  onLoad() {\n    this.loadData();\n    \n    // 监听问题数据更新事件\n    uni.$on('monthlyIssueUpdated', this.handleIssueUpdated);\n    uni.$on('issueDraftUpdated', this.handleDraftUpdated);\n  },\n  onUnload() {\n    // 移除事件监听\n    uni.$off('monthlyIssueUpdated', this.handleIssueUpdated);\n    uni.$off('issueDraftUpdated', this.handleDraftUpdated);\n  },\n  onShow() {\n    // 页面重新显示时，只在确实需要刷新数据的情况下才刷新\n    // 使用静默刷新，避免显示加载动画\n    if (this.dataLoaded && !this.loading && this.needsRefresh) {\n      this.silentRefreshData();\n      this.needsRefresh = false; // 重置刷新标记\n    }\n  },\n  methods: {\n    // 加载数据\n    loadData() {\n      this.loadPublishedIssues();\n      this.loadDraftIssues();\n    },\n    \n    // 加载已发布问题\n    async loadPublishedIssues() {\n      try {\n        this.loading = true;\n        \n        const { callCloudFunction } = require('@/utils/auth.js');\n        \n        // 调用真实API获取问题列表（不限制状态，获取所有数据）\n        const result = await callCloudFunction('hygiene-monthly-inspection', {\n          action: 'getMonthlyIssues',\n          data: {\n            pageSize: 50,\n            page: 1\n          }\n        });\n        \n        if (result && result.success && result.data && result.data.list) {\n          this.publishedIssues = result.data.list.map(item => this.formatIssueItem(item));\n          \n          // 缓存数据\n          try {\n            uni.setStorageSync('cached_published_issues', this.publishedIssues);\n          } catch (cacheError) {\n            // 缓存失败，静默处理\n          }\n        } else {\n          this.publishedIssues = [];\n        }\n        \n      } catch (error) {\n        \n        // 如果API失败，尝试从缓存加载\n        try {\n          const cachedData = uni.getStorageSync('cached_published_issues');\n          if (cachedData && Array.isArray(cachedData)) {\n            this.publishedIssues = cachedData;\n            uni.showToast({\n              title: '已加载缓存数据',\n              icon: 'none',\n              duration: 2000\n            });\n          } else {\n            this.publishedIssues = [];\n          }\n        } catch (cacheError) {\n          this.publishedIssues = [];\n        }\n      } finally {\n        this.loading = false;\n        this.dataLoaded = true; // 标记数据已加载\n      }\n    },\n\n    // 格式化问题项数据\n    formatIssueItem(data) {\n      return {\n        id: data._id || data.id,\n        number: data.issue_number || data.number,\n        title: data.title || '未知问题',\n        description: data.description || '',\n        location: data.location_info?.location_name || '未设置',\n        responsible: data.assigned_to_name || '未指定',\n        responsible_id: data.assigned_to,\n        deadline: this.formatDate(data.expected_completion_date),\n        priority: data.priority || 'normal',\n        status: data.status || 'pending',\n        category: data.category,\n        severity: data.severity,\n        images: data.photos || [],\n        createdAt: data.created_at || new Date().toISOString(),\n        updatedAt: data.updated_at || new Date().toISOString()\n      };\n    },\n\n    // 格式化日期\n    formatDate(dateStr) {\n      if (!dateStr) return '';\n      try {\n        const date = new Date(dateStr);\n        if (isNaN(date.getTime())) return '';\n        return date.toISOString().split('T')[0];\n      } catch (error) {\n        return '';\n      }\n    },\n    \n    // 加载草稿\n    async loadDraftIssues() {\n      try {\n        const drafts = uni.getStorageSync('issue_drafts') || [];\n        \n        // 解析草稿中的负责人信息\n        if (drafts.length > 0) {\n          for (let draft of drafts) {\n            if (draft.responsible && !draft.responsible_name) {\n              // 如果responsible是用户ID，尝试获取用户名\n              const userName = await this.getUserNameById(draft.responsible);\n              if (userName) {\n                draft.responsible_name = userName;\n                draft.responsible_id = draft.responsible; // 保存原始用户ID\n                draft.responsible = userName; // 更新显示字段\n              }\n            }\n          }\n        }\n        \n        this.draftIssues = drafts;\n      } catch (error) {\n        this.draftIssues = [];\n      }\n    },\n    \n    // 根据用户ID获取用户名\n    async getUserNameById(userId) {\n      try {\n        const { callCloudFunction } = require('@/utils/auth.js');\n        \n        const result = await callCloudFunction('hygiene-monthly-inspection', {\n          action: 'getUserInfo',\n          data: {\n            userId: userId\n          }\n        });\n        \n        if (result && result.success && result.data) {\n          return result.data.nickname || result.data.username || '未知用户';\n        }\n      } catch (error) {\n        // 获取用户信息失败，静默处理\n      }\n      \n      return null;\n    },\n    \n    // 切换tab\n    switchTab(tab) {\n      this.currentTab = tab;\n    },\n    \n    // 处理列表项点击\n    handleItemClick(issue) {\n      if (issue.status === 'draft') {\n        this.editDraft(issue);\n      } else {\n        this.viewIssueDetail(issue);\n      }\n    },\n    \n    // 查看问题详情\n    viewIssueDetail(issue) {\n        uni.navigateTo({\n          url: `/pages/6s_pkg/issue-detail?id=${issue.id}`\n        });\n    },\n    \n    // 编辑已发布问题\n    editPublishedIssue(issue) {\n      // 直接跳转到编辑页面，传递问题数据\n      uni.navigateTo({\n        url: `/pages/6s_pkg/issue-add?editId=${issue.id}&editData=${encodeURIComponent(JSON.stringify(issue))}`\n      });\n    },\n    \n    // 更改问题状态 - 与issue-detail.vue保持一致\n    changeIssueStatus(issue) {\n      // 根据当前状态和业务逻辑确定可选择的状态\n      let statusOptions = [];\n      \n      const currentStatus = issue.status;\n      const hasResponsible = issue.responsible && issue.responsible !== '未指定';\n      \n      // 如果没有负责人，只能选择分配相关的状态\n      if (!hasResponsible) {\n        statusOptions = [\n          { value: 'assigned', label: '已分配' }\n        ];\n      } else {\n        // 有负责人的情况下，根据当前状态提供合理的选项\n        if (currentStatus === 'assigned') {\n          statusOptions = [\n            { value: 'pending', label: '待整改' },\n            { value: 'in_progress', label: '整改中' },\n            { value: 'pending_review', label: '待检查' }\n          ];\n        } else if (currentStatus === 'pending') {\n          // 待整改状态，不再提供\"已分配\"选项，因为负责人已经分配了\n          statusOptions = [\n            { value: 'in_progress', label: '整改中' },\n            { value: 'pending_review', label: '待检查' }\n          ];\n        } else if (currentStatus === 'in_progress') {\n          statusOptions = [\n            { value: 'pending_review', label: '待检查' },\n            { value: 'pending', label: '待整改' } // 允许回退\n          ];\n        } else if (currentStatus === 'pending_review') {\n          statusOptions = [\n            { value: 'approved', label: '检查通过' },\n            { value: 'rejected', label: '重新整改' }\n          ];\n        } else if (currentStatus === 'approved') {\n          // 检查通过后，可以重新打开或发现新问题\n          statusOptions = [\n            { value: 'pending', label: '重新打开' },\n            { value: 'rejected', label: '发现新问题，重新整改' }\n          ];\n        } else if (currentStatus === 'rejected') {\n          statusOptions = [\n            { value: 'pending', label: '重新开始整改' },\n            { value: 'in_progress', label: '整改中' }\n          ];\n        } else {\n          // 其他状态提供基础选项（移除已关闭，检查通过就是自然结束）\n          statusOptions = [\n            { value: 'pending', label: '待整改' },\n            { value: 'in_progress', label: '整改中' },\n            { value: 'pending_review', label: '待检查' },\n            { value: 'approved', label: '检查通过' }\n            // 移除 'closed' 和 'completed'，统一使用 pending_review\n          ];\n        }\n      }\n      \n      uni.showActionSheet({\n        itemList: statusOptions.map(opt => opt.label),\n        success: async (res) => {\n          const selectedStatus = statusOptions[res.tapIndex];\n          if (selectedStatus && selectedStatus.value !== issue.status) {\n            await this.updateIssueStatus(issue.id, selectedStatus.value);\n          }\n        }\n      });\n    },\n    \n    // 更新问题状态\n    async updateIssueStatus(issueId, newStatus) {\n      try {\n        // 检查参数有效性\n        if (!issueId) {\n          uni.showToast({\n            title: '问题ID不能为空',\n            icon: 'error'\n          });\n          return;\n        }\n        \n        if (!newStatus) {\n          uni.showToast({\n            title: '状态参数无效',\n            icon: 'error'\n          });\n          return;\n        }\n        \n        uni.showLoading({ title: '更新中...' });\n        \n        const { callCloudFunction } = require('@/utils/auth.js');\n        \n        // 处理特殊状态映射\n        let actualStatus = newStatus;\n        if (newStatus === 'rejected') {\n          actualStatus = 'rejected'; // 重新整改状态\n        }\n        \n        const result = await callCloudFunction('hygiene-monthly-inspection', {\n          action: 'updateMonthlyIssue',\n          data: {\n            issue_id: issueId,\n            status: actualStatus,\n            action_type: 'admin_update'\n          }\n        });\n        \n        if (result && result.success) {\n          uni.hideLoading();\n          uni.showToast({\n            title: '状态更新成功',\n            icon: 'success'\n          });\n          \n          // 重新加载数据\n          this.loadData();\n        } else {\n          throw new Error(result?.message || '更新失败');\n        }\n      } catch (error) {\n        uni.hideLoading();\n        uni.showToast({\n          title: error.message || '更新失败',\n          icon: 'error'\n        });\n      }\n    },\n    \n\n    \n    // 编辑草稿\n    editDraft(issue) {\n      // 跳转到编辑页面，传递草稿数据\n      uni.navigateTo({\n        url: `/pages/6s_pkg/issue-add?draftId=${issue.id}`\n      });\n    },\n    \n    // 发布草稿\n    async publishDraft(issue) {\n      // 检查必填字段\n      if (!issue.description || !issue.location || !issue.responsible || !issue.deadline) {\n        uni.showModal({\n          title: '信息不完整',\n          content: '草稿信息不完整，需要先编辑补充必要信息后才能发布',\n          showCancel: false,\n          confirmText: '去编辑',\n          success: () => {\n            this.editDraft(issue);\n          }\n        });\n        return;\n      }\n      \n      uni.showModal({\n        title: '确认发布',\n        content: '确定要发布这个问题吗？',\n        success: async (res) => {\n          if (res.confirm) {\n            try {\n              uni.showLoading({ title: '发布中...' });\n              \n              const { callCloudFunction } = require('@/utils/auth.js');\n              \n              // 确保获取正确的负责人ID\n              let responsibleId = issue.responsible_id;\n              let responsibleName = issue.responsible_name || issue.responsible;\n              \n              // 如果没有responsible_id，尝试通过responsible字段获取\n              if (!responsibleId && issue.responsible) {\n                // 如果responsible看起来是用户ID（24位字符），直接使用\n                if (typeof issue.responsible === 'string' && issue.responsible.length === 24) {\n                  responsibleId = issue.responsible;\n                }\n              }\n              \n              \n              \n              // 构建提交数据\n              const submitData = {\n                title: issue.title.trim(),\n                description: issue.description.trim(),\n                location_info: {\n                  location_type: 'custom',\n                  location_name: issue.location.trim(),\n                  location_description: ''\n                },\n                inspection_info: {\n                  inspection_date: new Date().toISOString(),\n                  inspection_type: 'monthly_routine'\n                },\n                category: 'safety',\n                severity: issue.priority === 'urgent' ? 'high' : 'medium',\n                priority: issue.priority || 'normal',\n                assigned_to: responsibleId,\n                assigned_to_name: responsibleName,\n                photos: issue.images || [],\n                tags: [],\n                expected_completion_date: issue.deadline\n              };\n              \n              // 调用发布API\n              const result = await callCloudFunction('hygiene-monthly-inspection', {\n                action: 'createMonthlyIssue',\n                data: submitData\n              });\n              \n              if (result && result.success) {\n              // 从草稿中移除\n              this.removeDraftFromLocal(issue.id);\n              this.loadDraftIssues();\n                \n                // 重新加载已发布列表\n                await this.loadPublishedIssues();\n              \n              uni.hideLoading();\n              uni.showToast({\n                title: '发布成功',\n                icon: 'success'\n              });\n                \n                // 通知其他页面数据已更新\n                uni.$emit('monthlyIssueUpdated', {\n                  action: 'create',\n                  issueId: result.data?.id || result.data?._id,\n                  fromDraft: true // 标识来自草稿发布\n                });\n                \n                // 通知草稿列表数据已更新（草稿被删除）\n                uni.$emit('issueDraftUpdated', {\n                  action: 'delete',\n                  draftId: issue.id\n                });\n              } else {\n                throw new Error(result?.message || '发布失败');\n              }\n              \n            } catch (error) {\n              uni.hideLoading();\n              uni.showToast({\n                title: error.message || '发布失败',\n                icon: 'error'\n              });\n              // 发布草稿失败，已有Toast提示\n            }\n          }\n        }\n      });\n    },\n    \n    // 删除草稿\n    deleteDraft(issue) {\n      uni.showModal({\n        title: '确认删除',\n        content: '确定要删除这个草稿吗？',\n        success: (res) => {\n          if (res.confirm) {\n            this.removeDraftFromLocal(issue.id);\n            this.loadDraftIssues();\n            uni.showToast({\n              title: '删除成功',\n              icon: 'success'\n            });\n          }\n        }\n      });\n    },\n    \n    // 从本地缓存移除草稿\n    removeDraftFromLocal(draftId) {\n      try {\n        const drafts = uni.getStorageSync('issue_drafts') || [];\n        const filteredDrafts = drafts.filter(draft => draft.id !== draftId);\n        uni.setStorageSync('issue_drafts', filteredDrafts);\n      } catch (error) {\n        // 删除草稿失败，静默处理\n      }\n    },\n    \n    // 新增问题\n    addIssue() {\n      uni.navigateTo({\n        url: '/pages/6s_pkg/issue-add'\n      });\n    },\n    \n    // 获取问题编号显示\n    getIssueNumber(number) {\n      if (!number) return '';\n      \n      const numberStr = String(number);\n      \n      // 处理 YD 开头的长编号，提取后面的简短编号\n      if (numberStr.startsWith('YD') && numberStr.length > 10) {\n        const shortNumber = numberStr.slice(-3);\n        return `YD${shortNumber}`;\n      }\n      \n      // 处理其他长编号，保留前缀+后6位\n      if (numberStr.length > 8) {\n        const prefix = numberStr.substring(0, 2);\n        const suffix = numberStr.slice(-6);\n        return `${prefix}${suffix}`;\n      }\n      \n      return numberStr;\n    },\n    \n    // 获取状态文本 - 与其他页面保持一致\n    getStatusText(status) {\n      const statusMap = {\n        'draft': '草稿',\n        'assigned': '已分配',\n        'pending': '待整改',\n        'in_progress': '整改中',\n        'pending_review': '待检查',\n\n        'approved': '检查通过',\n        'rejected': '已驳回',\n        'overdue': '已逾期',\n        'reopened': '重新打开',\n        // 保留其他状态以兼容旧数据\n        'open': '待处理',\n        'resolved': '已解决',\n        'reviewing': '审核中',\n        'verified': '已验证',\n        'cancelled': '已取消',\n        'suspended': '已暂停',\n        'new': '新建',\n        'active': '进行中',\n        'inactive': '非活跃',\n        'expired': '已过期'\n      };\n      return statusMap[status] || '未知';\n    },\n    \n    // 格式化时间\n    formatTime(timeStr) {\n      if (!timeStr) return '';\n      \n      const time = new Date(timeStr);\n      const now = new Date();\n      const diff = now - time;\n      \n      if (diff < 60000) { // 1分钟内\n        return '刚刚';\n      } else if (diff < 3600000) { // 1小时内\n        return Math.floor(diff / 60000) + '分钟前';\n      } else if (diff < 86400000) { // 1天内\n        return Math.floor(diff / 3600000) + '小时前';\n      } else {\n        return time.getMonth() + 1 + '月' + time.getDate() + '日';\n      }\n    },\n    \n    delay(ms) {\n      return new Promise(resolve => setTimeout(resolve, ms));\n    },\n    \n    // 处理问题更新事件\n    handleIssueUpdated(data) {\n      // 根据操作类型决定刷新策略\n      if (data.action === 'create' || data.action === 'update' || data.action === 'delete') {\n        // 已发布问题变更，静默刷新已发布数据\n        this.silentRefreshPublishedData();\n      }\n      \n      // 如果是从草稿发布，草稿刷新交给 handleDraftUpdated 处理，避免重复刷新\n      // 不在这里刷新草稿数据，避免与 handleDraftUpdated 冲突\n    },\n    \n    // 处理草稿更新事件\n    handleDraftUpdated(data) {\n      // 草稿变更，使用与直接发布相同的成功模式\n      setTimeout(() => {\n        this.loadDraftIssues(); // 使用与直接发布相同的方法\n      }, 100); // 稍微增加延迟确保本地存储操作完成\n    },\n    \n    // 静默刷新数据（不显示加载状态）\n    async silentRefreshData() {\n      if (this.loading) return; // 如果正在加载，跳过\n      \n      try {\n        // 静默重新加载数据\n        await Promise.all([\n          this.silentRefreshPublishedData(),\n          this.silentRefreshDraftData()\n        ]);\n      } catch (error) {\n        // 静默处理错误，不显示错误提示\n      }\n    },\n    \n    // 静默刷新已发布数据\n    async silentRefreshPublishedData() {\n      try {\n        const { callCloudFunction } = require('@/utils/auth.js');\n        \n        // 调用真实API获取问题列表\n        const result = await callCloudFunction('hygiene-monthly-inspection', {\n          action: 'getMonthlyIssues',\n          data: {\n            pageSize: 50,\n            page: 1\n          }\n        });\n        \n        if (result && result.success && result.data && result.data.list) {\n          this.publishedIssues = result.data.list.map(item => this.formatIssueItem(item));\n          \n          // 更新缓存\n          try {\n            uni.setStorageSync('cached_published_issues', this.publishedIssues);\n          } catch (cacheError) {\n            // 缓存失败，静默处理\n          }\n        }\n      } catch (error) {\n        // 静默处理错误\n      }\n    },\n    \n    // 静默刷新草稿数据\n    async silentRefreshDraftData() {\n      try {\n        const drafts = uni.getStorageSync('issue_drafts') || [];\n        \n        // 解析草稿中的负责人信息\n        if (drafts.length > 0) {\n          for (let draft of drafts) {\n            if (draft.responsible && !draft.responsible_name) {\n              // 如果responsible是用户ID，尝试获取用户名\n              const userName = await this.getUserNameById(draft.responsible);\n              if (userName) {\n                draft.responsible_name = userName;\n                draft.responsible_id = draft.responsible; // 保存原始用户ID\n                draft.responsible = userName; // 更新显示字段\n              }\n            }\n          }\n        }\n        \n        this.draftIssues = drafts;\n      } catch (error) {\n        // 静默处理错误\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n  min-height: 100vh;\n  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\n  width: 100%;\n  overflow-x: hidden;\n  box-sizing: border-box;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;\n  /* 滚动性能优化 */\n  -webkit-overflow-scrolling: touch;\n  transform: translateZ(0);\n  padding-bottom: 100rpx;\n}\n\n/* 统计区域 */\n.stats-section {\n  padding: 32rpx;\n  background: transparent;\n}\n\n.stats-card {\n  display: flex;\n  justify-content: space-around;\n  padding: 32rpx 24rpx;\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n  border-radius: 24rpx;\n  color: #ffffff;\n  box-shadow: 0 12rpx 32rpx rgba(0, 122, 255, 0.4),\n              0 8rpx 16rpx rgba(0, 122, 255, 0.3),\n              0 4rpx 8rpx rgba(0, 122, 255, 0.2);\n  border: 1rpx solid rgba(255, 255, 255, 0.3);\n  /* 性能优化 */\n  transform: translateZ(0);\n  will-change: transform;\n}\n\n.stats-item {\n  text-align: center;\n}\n\n.stats-number {\n  font-size: 48rpx;\n  font-weight: bold;\n  margin-bottom: 8rpx;\n}\n\n.stats-label {\n  font-size: 24rpx;\n  opacity: 0.8;\n}\n\n/* Tab切换 */\n.tab-section {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20rpx);\n  border-bottom: 1rpx solid rgba(229, 229, 229, 0.5);\n  margin: 0 32rpx;\n  border-radius: 24rpx 24rpx 0 0;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08), \n              0 2rpx 6rpx rgba(0, 0, 0, 0.04);\n}\n\n.tab-container {\n  display: flex;\n  padding: 0 32rpx;\n}\n\n.tab-item {\n  flex: 1;\n  text-align: center;\n  padding: 32rpx 0;\n  font-size: 28rpx;\n  color: #8E8E93;\n  border-bottom: 4rpx solid transparent;\n  transition: all 0.3s ease;\n}\n\n.tab-item.active {\n  color: #007AFF;\n  border-bottom-color: #007AFF;\n  font-weight: 600;\n}\n\n/* 列表区域 */\n.list-section {\n  padding: 0 32rpx;\n  margin-top: 24rpx;\n}\n\n.issue-item {\n  background: #ffffff;\n  border-radius: 24rpx;\n  padding: 32rpx;\n  margin-bottom: 24rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08), \n              0 2rpx 6rpx rgba(0, 0, 0, 0.04);\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  /* 性能优化 */\n  transform: translateZ(0);\n  will-change: transform;\n}\n\n.issue-item:active {\n  background: #F8F8F8;\n  transform: scale(0.98);\n}\n\n.issue-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 16rpx;\n}\n\n.issue-title-section {\n  flex: 1;\n  margin-right: 16rpx;\n}\n\n.issue-number {\n  font-size: 22rpx;\n  font-weight: 700;\n  color: #007AFF;\n  font-family: 'Monaco', 'Menlo', monospace;\n  margin-bottom: 4rpx;\n  opacity: 0.8;\n}\n\n.issue-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  line-height: 1.3;\n}\n\n.issue-status {\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  font-size: 22rpx;\n  font-weight: 600;\n  white-space: nowrap;\n  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.08);\n}\n\n.status-draft {\n  background: #F2F2F7;\n  color: #8E8E93;\n}\n\n.status-pending {\n  background: linear-gradient(135deg, #E6F3FF, #F0F7FF);\n  color: #007AFF;\n  border: 2rpx solid rgba(0, 122, 255, 0.2);\n}\n\n.status-assigned {\n  background: linear-gradient(135deg, #ECFEFF, #F0FDFF);\n  color: #0891B2;\n  border: 2rpx solid rgba(8, 145, 178, 0.2);\n}\n\n.status-in_progress {\n  background: linear-gradient(135deg, #FFF4E6, #FFFBF0);\n  color: #FF9500;\n  border: 2rpx solid rgba(255, 149, 0, 0.2);\n}\n\n.status-pending_review {\n  background: linear-gradient(135deg, #F0EFFF, #F8F7FF);\n  color: #5856D6;\n  border: 2rpx solid rgba(88, 86, 214, 0.2);\n}\n\n\n\n.status-approved {\n  background: linear-gradient(135deg, #E8F5E8, #F0FFF0);\n  color: #34C759;\n  border: 2rpx solid rgba(52, 199, 89, 0.2);\n}\n\n.status-rejected {\n  background: linear-gradient(135deg, #F2F2F7, #F8F8F8);\n  color: #8E8E93;\n  border: 2rpx solid rgba(142, 142, 147, 0.2);\n}\n\n\n\n.status-overdue {\n  background: linear-gradient(135deg, #FFE6E6, #FFF0F0);\n  color: #FF3B30;\n  border: 2rpx solid rgba(255, 59, 48, 0.2);\n}\n\n/* 兼容性状态映射到8色方案 */\n.status-open {\n  background: linear-gradient(135deg, #E6F3FF, #F0F7FF);\n  color: #007AFF;\n  border: 2rpx solid rgba(0, 122, 255, 0.2);\n}\n\n.status-resolved {\n  background: linear-gradient(135deg, #DCFCE7, #F0FDF4);\n  color: #16A34A;\n  border: 2rpx solid rgba(22, 163, 74, 0.2);\n}\n\n.status-reviewing {\n  background: linear-gradient(135deg, #FFF4E6, #FFFBF0);\n  color: #FF9500;\n  border: 2rpx solid rgba(255, 149, 0, 0.2);\n}\n\n\n\n.status-verified {\n  background: linear-gradient(135deg, #DCFCE7, #F0FDF4);\n  color: #16A34A;\n  border: 2rpx solid rgba(22, 163, 74, 0.2);\n}\n\n.status-cancelled {\n  background: linear-gradient(135deg, #F2F2F7, #F8F8F8);\n  color: #8E8E93;\n  border: 2rpx solid rgba(142, 142, 147, 0.2);\n}\n\n.status-suspended {\n  background: linear-gradient(135deg, #F2F2F7, #F8F8F8);\n  color: #8E8E93;\n  border: 2rpx solid rgba(142, 142, 147, 0.2);\n}\n\n.status-reopened {\n  background: linear-gradient(135deg, #FFF4E6, #FFFBF0);\n  color: #FF9500;\n  border: 2rpx solid rgba(255, 149, 0, 0.2);\n}\n\n.status-new {\n  background: linear-gradient(135deg, #ECFEFF, #F0FDFF);\n  color: #0891B2;\n  border: 2rpx solid rgba(8, 145, 178, 0.2);\n}\n\n.status-active {\n  background: linear-gradient(135deg, #FFF4E6, #FFFBF0);\n  color: #FF9500;\n  border: 2rpx solid rgba(255, 149, 0, 0.2);\n}\n\n.status-inactive {\n  background: linear-gradient(135deg, #F2F2F7, #F8F8F8);\n  color: #8E8E93;\n  border: 2rpx solid rgba(142, 142, 147, 0.2);\n}\n\n.status-expired {\n  background: linear-gradient(135deg, #FFE6E6, #FFF0F0);\n  color: #FF3B30;\n  border: 2rpx solid rgba(255, 59, 48, 0.2);\n}\n\n/* 保持原有的processing状态以兼容旧代码 */\n.status-processing {\n  background: #D1ECF1;\n  color: #0C5460;\n}\n\n.issue-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.issue-info {\n  display: flex;\n  gap: 24rpx;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  gap: 6rpx;\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.issue-time {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n/* 操作按钮 */\n.issue-actions {\n  display: flex;\n  gap: 16rpx;\n  border-top: 1rpx solid #F2F2F7;\n  padding-top: 16rpx;\n}\n\n.action-btn {\n  display: flex;\n  align-items: center;\n  gap: 6rpx;\n  padding: 8rpx 16rpx;\n  border-radius: 12rpx;\n  font-size: 24rpx;\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n.action-btn.edit {\n  background: #F0F8FF;\n  color: #007AFF;\n}\n\n.action-btn.publish {\n  background: #F0FFF4;\n  color: #34C759;\n}\n\n.action-btn.delete {\n  background: #FFF5F5;\n  color: #FF3B30;\n}\n\n.action-btn.view {\n  background: #F0F8FF;\n  color: #007AFF;\n}\n\n.action-btn.status {\n  background: #FFF8E1;\n  color: #FF9500;\n}\n\n.action-btn.close {\n  background: #FFF5F5;\n  color: #FF3B30;\n}\n\n.action-btn:active {\n  opacity: 0.7;\n  transform: scale(0.95);\n}\n\n/* 加载状态 */\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 80rpx 32rpx;\n  gap: 24rpx;\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n  text-align: center;\n}\n\n/* 悬浮按钮 */\n.fab-container {\n  position: fixed;\n  right: 32rpx;\n  bottom: 32rpx;\n  z-index: 10;\n}\n\n.fab-btn {\n  width: 112rpx;\n  height: 112rpx;\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 12rpx 32rpx rgba(0, 122, 255, 0.4),\n              0 8rpx 16rpx rgba(0, 122, 255, 0.3),\n              0 4rpx 8rpx rgba(0, 122, 255, 0.2);\n  border: 2rpx solid rgba(255, 255, 255, 0.3);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  overflow: hidden;\n}\n\n.fab-btn::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.6s;\n}\n\n.fab-btn:active::before {\n  left: 100%;\n}\n\n.fab-btn:active {\n  transform: scale(0.92);\n  box-shadow: 0 6rpx 16rpx rgba(0, 122, 255, 0.3),\n              0 3rpx 8rpx rgba(0, 122, 255, 0.2);\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./issue-list.vue?vue&type=style&index=0&id=ed1d1fe4&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./issue-list.vue?vue&type=style&index=0&id=ed1d1fe4&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775842452\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}