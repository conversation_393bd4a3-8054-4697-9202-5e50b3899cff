require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/inspection-detail"],{"1b42":function(e,t,n){"use strict";n.r(t);var i=n("930e"),r=n("5ea7");for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);n("e750");var s=n("828b"),a=Object(s["a"])(r["default"],i["b"],i["c"],!1,null,"110de69d",null,!1,i["a"],void 0);t["default"]=a.exports},"31ad":function(e,t,n){"use strict";(function(e,i){var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n("7eb4")),s=r(n("34cf")),a=r(n("3b2d")),c=r(n("ee10")),u=r(n("4ea0")),l={name:"InspectionDetail",components:{},data:function(){return{areaId:"",isRectificationCheck:!1,loading:!1,areaInfo:{id:"",name:"",assignedEmployee:"",location:"",cleaningFrequency:"",lastCleaningTime:"",hasRectificationHistory:!1},currentTime:"",sliderTouching:!1,inspectionForm:{rating:0,photos:[],hasIssues:!1,issueType:"",issueDescription:"",remarks:""},isSubmitting:!1,autoUpload:!0,uploading:!1,uploadProgress:0}},computed:{issueDescriptionLength:function(){return this.inspectionForm.issueDescription?this.inspectionForm.issueDescription.length:0},remarksLength:function(){return this.inspectionForm.remarks?this.inspectionForm.remarks.length:0},canSubmit:function(){return!this.isSubmitting&&(this.inspectionForm.hasIssues?!!this.inspectionForm.hasIssues&&(this.inspectionForm.issueDescription.trim().length>0&&this.inspectionForm.photos.length>0):this.inspectionForm.rating>0)},getLastCleaningClass:function(){return"normal"},shouldShowLastCleaning:function(){return this.areaInfo.hasRectificationHistory&&this.areaInfo.lastCleaningTime}},onLoad:function(e){e.id&&(this.areaId=e.id,this.isRectificationCheck="true"===e.isRectification,this.loadAreaInfo()),this.updateCurrentTime()},methods:{updateCurrentTime:function(){var e=new Date,t=e.getFullYear(),n=(e.getMonth()+1).toString().padStart(2,"0"),i=e.getDate().toString().padStart(2,"0"),r=e.getHours().toString().padStart(2,"0"),o=e.getMinutes().toString().padStart(2,"0"),s=e.getSeconds().toString().padStart(2,"0"),a=["日","一","二","三","四","五","六"][e.getDay()];this.currentTime="".concat(t,"年").concat(n,"月").concat(i,"日 星期").concat(a," ").concat(r,":").concat(o,":").concat(s)},loadAreaInfo:function(){var t=this;return(0,c.default)(o.default.mark((function i(){var r,s,a,c;return o.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return t.loading=!0,i.prev=1,r=n("882c"),s=r.callCloudFunction,i.next=5,s("hygiene-area-management",{action:"getAreaDetail",data:{id:t.areaId}});case 5:if(a=i.sent,!(a&&a.success&&a.data)){i.next=14;break}return c=a.data,t.areaInfo={id:c._id||c.id,name:c.name||"未知责任区",assignedEmployee:t.getAssignedEmployee(c),location:t.getLocationDisplay(c.location),cleaningFrequency:t.getCleaningFrequency(c),lastCleaningTime:"",hasRectificationHistory:t.isRectificationCheck},i.next=11,t.getLastCleaningTime(c._id||c.id);case 11:t.areaInfo.lastCleaningTime=i.sent,i.next=15;break;case 14:throw new Error((null===a||void 0===a?void 0:a.message)||"获取责任区信息失败");case 15:i.next=20;break;case 17:i.prev=17,i.t0=i["catch"](1),e.showToast({title:"加载失败，请重试",icon:"none"});case 20:return i.prev=20,t.loading=!1,i.finish(20);case 23:case"end":return i.stop()}}),i,null,[[1,17,20,23]])})))()},getAssignedEmployee:function(e){if(e.assigned_user_details&&e.assigned_user_details.length>0){var t=e.assigned_user_details[0];return t.nickname||t.username||"未知员工"}return e.assigned_users&&e.assigned_users.length>0?"用户".concat(e.assigned_users[0]):"未分配"},getLocationDisplay:function(e){return e?"string"===typeof e?e:"object"===(0,a.default)(e)&&(e.description||e.name||e.address||e.text||e.area)||"未设置":"未设置"},getCleaningFrequency:function(e){if("fixed"===e.type)return"每周一次";if("public"===e.type&&null!==e.scheduled_day){return"每".concat(["周日","周一","周二","周三","周四","周五","周六"][e.scheduled_day])}return"未设置"},getLastCleaningTime:function(e){var t=this;return(0,c.default)(o.default.mark((function i(){var r,s,a,c,u;return o.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.prev=0,r=n("882c"),s=r.callCloudFunction,i.next=4,s("hygiene-cleaning",{action:"getCleaningRecords",data:{area_id:e,pageSize:1}});case 4:if(a=i.sent,!(a&&a.success&&a.data&&a.data.list&&a.data.list.length>0)){i.next=9;break}return c=a.data.list[0],"未分配"===t.areaInfo.assignedEmployee&&(u=[c.user_name,c.cleaner_name,c.created_by_name,c.operator_name].filter((function(e){return e})),u.length>0&&t.$set(t.areaInfo,"assignedEmployee",u[0])),i.abrupt("return",t.formatDateTime(c.cleaning_date));case 9:return i.abrupt("return","");case 12:return i.prev=12,i.t0=i["catch"](0),i.abrupt("return","");case 15:case"end":return i.stop()}}),i,null,[[0,12]])})))()},formatDateTime:function(e){if(!e)return"";try{var t;return t="string"===typeof e?e.includes("T")||e.includes("Z")?new Date(e):new Date(e.replace(/-/g,"/")):new Date(e),isNaN(t.getTime())?"":"".concat(t.getMonth()+1,"月").concat(t.getDate(),"日 ").concat(t.getHours().toString().padStart(2,"0"),":").concat(t.getMinutes().toString().padStart(2,"0"))}catch(n){return""}},onStarRateChange:function(e){this.inspectionForm.rating=e.value},setRatingByMark:function(e){this.inspectionForm.rating=e},onSliderTouchStart:function(e){this.sliderTouching=!0,this.updateRatingFromTouch(e)},onSliderTouchMove:function(e){this.sliderTouching&&this.updateRatingFromTouch(e)},onSliderTouchEnd:function(e){this.sliderTouching=!1},onSliderMouseDown:function(e){this.sliderTouching=!0,this.updateRatingFromMouse(e)},onSliderMouseMove:function(e){this.sliderTouching&&this.updateRatingFromMouse(e)},onSliderMouseUp:function(e){this.sliderTouching=!1},updateRatingFromTouch:function(t){var n=this,i=t.touches[0]||t.changedTouches[0];e.createSelectorQuery().in(this).select(".custom-slider-container").boundingClientRect((function(e){if(e){var t=i.clientX-e.left,r=Math.max(0,Math.min(1,t/e.width)),o=Math.round(5*r*2)/2;n.inspectionForm.rating=Math.max(0,Math.min(5,o))}})).exec()},updateRatingFromMouse:function(t){var n=this;e.createSelectorQuery().in(this).select(".custom-slider-container").boundingClientRect((function(e){if(e){var i=t.clientX-e.left,r=Math.max(0,Math.min(1,i/e.width)),o=Math.round(5*r*2)/2;n.inspectionForm.rating=Math.max(0,Math.min(5,o))}})).exec()},getRatingDescription:function(e){return 0===e?"请评分":e<=1?"较差":e<=2?"一般":e<=3?"良好":e<5?"优秀":5===e?"完美":""},chooseImage:function(){var t=this;this.inspectionForm.photos.length>=6?e.showToast({title:"最多只能上传6张照片",icon:"none"}):e.chooseImage({count:6-this.inspectionForm.photos.length,sizeType:["compressed"],sourceType:["camera","album"],success:function(){var e=(0,c.default)(o.default.mark((function e(n){var i;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=n.tempFilePaths.map((function(e){return{url:e,cloudUrl:"",fileID:"",name:"检查照片".concat(t.inspectionForm.photos.length+1,".jpg"),uploaded:!1,uploading:!1}})),t.inspectionForm.photos=t.inspectionForm.photos.concat(i),t.autoUpload&&t.autoUploadNewPhotos(i);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()})},uploadImages:function(e){var t=this;return(0,c.default)(o.default.mark((function n(){var i,r;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,u.default.batchUpload(e,{pathGenerator:function(){var e=Date.now(),n=Math.random().toString(36).substring(2,8);return"6s/inspection/".concat(t.areaId,"/").concat(e,"_").concat(n,".jpg")},maxConcurrent:2});case 3:if(i=n.sent,!i.success){n.next=12;break}if(i.results.forEach((function(n,i){if(n.success){var r=e[i],o=t.inspectionForm.photos.findIndex((function(e){return e.url===r}));-1!==o&&(t.$set(t.inspectionForm.photos[o],"uploaded",!0),t.$set(t.inspectionForm.photos[o],"cloudUrl",n.url),t.$set(t.inspectionForm.photos[o],"fileID",n.cloudPath),t.$set(t.inspectionForm.photos[o],"uploading",!1))}})),i.results.filter((function(e){return e.success})).length,r=i.results.filter((function(e){return!e.success})).length,!(r>0)){n.next=10;break}throw new Error("".concat(r,"张照片上传失败"));case 10:n.next=13;break;case 12:throw new Error("照片上传失败");case 13:n.next=18;break;case 15:throw n.prev=15,n.t0=n["catch"](0),n.t0;case 18:case"end":return n.stop()}}),n,null,[[0,15]])})))()},autoUploadNewPhotos:function(t){var n=this;return(0,c.default)(o.default.mark((function i(){var r,s,a;return o.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:r=o.default.mark((function i(r){var s,a,c;return o.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(s=t[r],a=n.inspectionForm.photos.findIndex((function(e){return e.url===s.url})),-1!==a){i.next=4;break}return i.abrupt("return","continue");case 4:return i.prev=4,n.$set(n.inspectionForm.photos[a],"uploading",!0),i.next=8,n.uploadSinglePhoto(s);case 8:if(c=i.sent,!c.success){i.next=16;break}n.$set(n.inspectionForm.photos[a],"uploaded",!0),n.$set(n.inspectionForm.photos[a],"cloudUrl",c.url),n.$set(n.inspectionForm.photos[a],"fileID",c.cloudPath),n.$set(n.inspectionForm.photos[a],"uploading",!1),i.next=17;break;case 16:throw new Error(c.error||"上传失败");case 17:i.next=23;break;case 19:i.prev=19,i.t0=i["catch"](4),n.$set(n.inspectionForm.photos[a],"uploading",!1),e.showModal({title:"上传失败",content:"照片".concat(r+1,"上传失败: ").concat(i.t0.message||i.t0),showCancel:!1,confirmText:"知道了"});case 23:case"end":return i.stop()}}),i,null,[[4,19]])})),s=0;case 2:if(!(s<t.length)){i.next=10;break}return i.delegateYield(r(s),"t0",4);case 4:if(a=i.t0,"continue"!==a){i.next=7;break}return i.abrupt("continue",7);case 7:s++,i.next=2;break;case 10:case"end":return i.stop()}}),i)})))()},uploadSinglePhoto:function(e){var t=this;return(0,c.default)(o.default.mark((function n(){var i,r,s,a,c;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,i=Date.now(),r=Math.random().toString(36).substring(2,8),s="6s/inspection/".concat(t.areaId,"/").concat(i,"_").concat(r,".jpg"),n.next=6,u.default.uploadToCloud(e.url,s,{onProgress:function(e){}});case 6:if(a=n.sent,!a||!a.fileID){n.next=14;break}return n.next=10,u.default.getFileInfo(a.fileID);case 10:return c=n.sent,n.abrupt("return",{success:!0,cloudPath:a.fileID,url:c.tempFileURL||a.fileID,size:a.actualSize});case 14:throw new Error("上传返回结果异常");case 15:n.next=20;break;case 17:return n.prev=17,n.t0=n["catch"](0),n.abrupt("return",{success:!1,error:n.t0.message});case 20:case"end":return n.stop()}}),n,null,[[0,17]])})))()},toggleAutoUpload:function(){this.autoUpload=!this.autoUpload,e.showToast({title:this.autoUpload?"已开启自动上传":"已关闭自动上传",icon:"none"})},deletePhoto:function(t){var n=this;return(0,c.default)(o.default.mark((function r(){return o.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:e.showModal({title:"确认删除",content:"确定要删除这张照片吗？",success:function(){var r=(0,c.default)(o.default.mark((function r(s){var a,c;return o.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!s.confirm){r.next=17;break}if(a=n.inspectionForm.photos[t],!a||!a.fileID){r.next=16;break}return r.prev=3,e.showLoading({title:"删除照片中..."}),r.next=7,i.callFunction({name:"delete-file",data:{fileList:[n.extractFileId(a.fileID)]}});case 7:c=r.sent,c.result&&c.result.code,r.next=13;break;case 11:r.prev=11,r.t0=r["catch"](3);case 13:return r.prev=13,e.hideLoading(),r.finish(13);case 16:n.inspectionForm.photos.splice(t,1);case 17:case"end":return r.stop()}}),r,null,[[3,11,13,16]])})));return function(e){return r.apply(this,arguments)}}()});case 1:case"end":return r.stop()}}),r)})))()},extractFileId:function(e){if(e.startsWith("cloud://")){var t=e.split("/");return t[t.length-1]}if(e.includes("tcb-api")){var n=new URL(e);return n.pathname.split("/").pop()}return e},getPhotoDisplayUrl:function(e){var t=e.url||e;return"string"===typeof t&&t.startsWith("http://tmp/"),t},previewPhoto:function(t){var n=this,i=this.inspectionForm.photos.map((function(e){return n.getPhotoDisplayUrl(e)}));e.previewImage({urls:i,current:i[t]})},toggleIssues:function(e){this.inspectionForm.hasIssues=e,e?(this.inspectionForm.rating=0,this.inspectionForm.remarks=""):(this.inspectionForm.issueDescription="",this.inspectionForm.issueType="")},handleIssueDescriptionInput:function(t){var n=t.detail.value||"";n.length>200&&(n=n.substring(0,200),e.showToast({title:"问题描述不能超过200个字符",icon:"none",duration:1500})),this.inspectionForm.issueDescription=n,this.autoDetectIssueType(n),this.$forceUpdate()},handleRemarksInput:function(t){var n=t.detail.value||"";n.length>200&&(n=n.substring(0,200),e.showToast({title:"检查备注不能超过200个字符",icon:"none",duration:1500})),this.inspectionForm.remarks=n,this.$forceUpdate()},autoDetectIssueType:function(e){if(e){for(var t=0,n=Object.entries({"设备问题":["设备","机器","故障","损坏","噪音","异响","漏油","漏气","过热","振动","磨损"],"清洁问题":["脏","污","积尘","垃圾","异味","地面","清洁","卫生","油污","水渍","污渍","不洁","打扫"],"整理问题":["杂乱","摆放","工具","物品","整理","混乱","堆放","乱放","凌乱","无序","杂物"],"安全问题":["安全","隐患","危险","滑倒","积水","漏电","阻塞","堵塞","阻挡","绊倒","碰撞"],"环境问题":["环境","温度","湿度","通风","采光","噪声","粉尘","气味","空气","污染","废气"],"标识问题":["标识","标志","标牌","不清","缺失","模糊","褪色","破损","看不清","无标识"]});t<n.length;t++){var i=(0,s.default)(n[t],2),r=i[0],o=i[1];if(o.some((function(t){return e.includes(t)})))return void(this.inspectionForm.issueType=r)}this.inspectionForm.issueType="其他问题"}},submitInspection:function(){var t=this;return(0,c.default)(o.default.mark((function n(){return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t.canSubmit){n.next=2;break}return n.abrupt("return");case 2:if(!t.inspectionForm.hasIssues||t.inspectionForm.issueDescription.trim()){n.next=5;break}return e.showToast({title:"请填写问题描述",icon:"none"}),n.abrupt("return");case 5:if(!t.inspectionForm.hasIssues||0!==t.inspectionForm.photos.length){n.next=8;break}return e.showToast({title:"发现问题时必须上传照片作为证据",icon:"none"}),n.abrupt("return");case 8:e.showModal({title:"确认提交",content:"确定要提交本次检查记录吗？",success:function(){var e=(0,c.default)(o.default.mark((function e(n){return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!n.confirm){e.next=3;break}return e.next=3,t.doSubmit();case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()});case 9:case"end":return n.stop()}}),n)})))()},doSubmit:function(){var t=this;return(0,c.default)(o.default.mark((function i(){var r,s,a,c,u,l,p,d,h,f;return o.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(t.isSubmitting=!0,i.prev=1,r=n("882c"),s=r.callCloudFunction,t.inspectionForm.hasIssues&&t.inspectionForm.issueDescription&&!t.inspectionForm.issueType&&t.autoDetectIssueType(t.inspectionForm.issueDescription),a=t.inspectionForm.photos.filter((function(e){return!e.uploaded&&!e.uploading})),!(a.length>0)){i.next=13;break}return e.showLoading({title:"正在上传剩余照片..."}),c=a.map((function(e){return e.url})),i.next=10,t.uploadImages(c);case 10:if(u=t.inspectionForm.photos.filter((function(e){return!e.uploaded&&!e.uploading})),!(u.length>0)){i.next=13;break}throw new Error("部分照片上传失败，请重试");case 13:return l=t.inspectionForm.photos.filter((function(e){return e.uploaded&&e.fileID})).map((function(e){return{url:e.fileID,type:"inspection",description:""}})),p={area_id:t.areaId,inspection_date:(new Date).toISOString(),result:t.inspectionForm.hasIssues?"failed":"passed",photos:l},d="",t.inspectionForm.hasIssues&&(d=t.inspectionForm.issueDescription.trim(),d.length>200&&(d=d.substring(0,200),console.warn("问题描述被截断到200字符"))),t.inspectionForm.hasIssues?(p.score=0,p.remarks=d,p.issues=[{type:t.inspectionForm.issueType||"其他问题",description:d,severity:"medium"}]):(p.score=t.inspectionForm.rating,p.remarks=t.inspectionForm.remarks.trim()||"检查通过"),p.is_passed=!t.inspectionForm.hasIssues,p.overall_rating=t.inspectionForm.rating,p.issue_description=t.inspectionForm.hasIssues?d:"",p.is_rectification_recheck=t.isRectificationCheck,p.original_record_id=null,i.next=25,s("hygiene-inspection",{action:"createInspectionRecord",data:p});case 25:if(h=i.sent,!h||!h.success){i.next=34;break}t.isSubmitting=!1,e.showToast({title:"提交成功",icon:"success"}),e.$emit("inspectionRecordUpdated",{areaId:t.areaId,isPassed:!t.inspectionForm.hasIssues,isRectificationCheck:t.isRectificationCheck}),t.inspectionForm.hasIssues&&e.$emit("rectificationRecordUpdated",{areaId:t.areaId,action:"created",isFromInspection:!0}),setTimeout((function(){e.navigateBack()}),1500),i.next=35;break;case 34:throw new Error((null===h||void 0===h?void 0:h.message)||"提交检查记录失败");case 35:i.next=43;break;case 37:i.prev=37,i.t0=i["catch"](1),t.isSubmitting=!1,f="提交失败，请重试",i.t0.message&&(f=i.t0.message.includes("未登录")?"请先登录":i.t0.message.includes("权限")?"您没有权限操作该责任区":i.t0.message),e.showModal({title:"提交失败",content:f,showCancel:!1,confirmText:"知道了"});case 43:case"end":return i.stop()}}),i,null,[[1,37]])})))()}}};t.default=l}).call(this,n("df3c")["default"],n("861b")["uniCloud"])},"3d20":function(e,t,n){},"5ea7":function(e,t,n){"use strict";n.r(t);var i=n("31ad"),r=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);t["default"]=r.a},"930e":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return i}));var i={uniIcons:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(n.bind(null,"6ddf"))},uniRate:function(){return n.e("uni_modules/uni-rate/components/uni-rate/uni-rate").then(n.bind(null,"897a"))}},r=function(){var e=this,t=e.$createElement,n=(e._self._c,e.inspectionForm.hasIssues?null:e.getRatingDescription(e.inspectionForm.rating)),i=e.__map(e.inspectionForm.photos,(function(t,n){var i=e.__get_orig(t),r=e.getPhotoDisplayUrl(t);return{$orig:i,m1:r}})),r=e.inspectionForm.photos.length,o=r<6?e.inspectionForm.photos.length:null;e.$mp.data=Object.assign({},{$root:{m0:n,l0:i,g0:r,g1:o}})},o=[]},acf5:function(e,t,n){"use strict";(function(e,t){var i=n("47a9");n("357b"),n("861b");i(n("3240"));var r=i(n("1b42"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(r.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},e750:function(e,t,n){"use strict";var i=n("3d20"),r=n.n(i);r.a}},[["acf5","common/runtime","common/vendor"]]]);