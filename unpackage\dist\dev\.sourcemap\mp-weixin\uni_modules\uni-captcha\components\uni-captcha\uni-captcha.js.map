{"version": 3, "sources": ["webpack:///D:/Xwzc/uni_modules/uni-captcha/components/uni-captcha/uni-captcha.vue?7749", "webpack:///D:/Xwzc/uni_modules/uni-captcha/components/uni-captcha/uni-captcha.vue?ee2c", "webpack:///D:/Xwzc/uni_modules/uni-captcha/components/uni-captcha/uni-captcha.vue?214e", "webpack:///D:/Xwzc/uni_modules/uni-captcha/components/uni-captcha/uni-captcha.vue?7eda", "uni-app:///uni_modules/uni-captcha/components/uni-captcha/uni-captcha.vue", "webpack:///D:/Xwzc/uni_modules/uni-captcha/components/uni-captcha/uni-captcha.vue?c721", "webpack:///D:/Xwzc/uni_modules/uni-captcha/components/uni-captcha/uni-captcha.vue?2a3f"], "names": ["props", "modelValue", "value", "scene", "type", "default", "focus", "computed", "val", "get", "set", "data", "focusCaptchaInput", "captchaBase64", "loging", "watch", "handler", "uni", "title", "icon", "immediate", "methods", "getImageCaptcha", "customUI", "uniIdCo", "catch"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAqmB,CAAgB,+nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCaznB;EACAA;IACAC;IACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;EACA;EACAE;IACAC;MACAC;QACA;MACA;MACAC;QACA;;QAEA;;QAGA;MAIA;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAZ;MACAa;QACA;UACA;QACA;UACAC;YACAC;YACAC;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MAAA;MACA;MACA;QACA;QACA;MACA;MACA;QACAC;MACA;MACAC;QACArB;MACA;QAEA;MACA,GACAsB;QACAR;UACAC;UACAC;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AChGA;AAAA;AAAA;AAAA;AAAwqC,CAAgB,8oCAAG,EAAC,C;;;;;;;;;;;ACA5rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-captcha/components/uni-captcha/uni-captcha.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-captcha.vue?vue&type=template&id=9e659dae&scoped=true&\"\nvar renderjs\nimport script from \"./uni-captcha.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-captcha.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-captcha.vue?vue&type=style&index=0&id=9e659dae&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9e659dae\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-captcha/components/uni-captcha/uni-captcha.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-captcha.vue?vue&type=template&id=9e659dae&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.focusCaptchaInput = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-captcha.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-captcha.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"captcha-box\">\r\n\t\t<view class=\"captcha-img-box\">\r\n\t\t\t<uni-icons class=\"loding\" size=\"20px\" color=\"#BBB\" v-if=\"loging\" type=\"spinner-cycle\"></uni-icons>\r\n\t\t\t<image class=\"captcha-img\" :class=\"{opacity:loging}\" @click=\"getImageCaptcha\" :src=\"captchaBase64\"\r\n\t\t\t\tmode=\"widthFix\"></image>\r\n\t\t</view>\r\n\t\t<input @blur=\"focusCaptchaInput = false\" :focus=\"focusCaptchaInput\" type=\"text\" class=\"captcha\"\r\n\t\t\t:inputBorder=\"false\" maxlength=\"4\" v-model=\"val\" placeholder=\"请输入验证码\">\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\n\t\tprops: {\n\t\t\tmodelValue:String,\n\t\t\tvalue:String,\n\t\t\tscene: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault () {\n\t\t\t\t\treturn \"\"\n\t\t\t\t}\n\t\t\t},\n\t\t\tfocus: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault () {\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcomputed:{\n\t\t\tval:{\n\t\t\t\tget(){\n\t\t\t\t\treturn this.value||this.modelValue\n\t\t\t\t},\n\t\t\t\tset(value){\n\t\t\t\t\t// TODO 兼容 vue2\n\t\t\t\t\t// #ifdef VUE2\n\t\t\t\t\tthis.$emit('input', value);\n\t\t\t\t\t// #endif\n\t\t\t\t\t\n\t\t\t\t\t// TODO　兼容　vue3\n\t\t\t\t\t// #ifdef VUE3\n\t\t\t\t\tthis.$emit('update:modelValue', value)\n\t\t\t\t\t// #endif\n\t\t\t\t}\n\t\t\t}\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tfocusCaptchaInput: false,\r\n\t\t\t\tcaptchaBase64: \"\",\r\n\t\t\t\tloging: false\r\n\t\t\t};\r\n\t\t},\n\t\twatch: {\r\n\t\t\tscene: {\r\n\t\t\t\thandler(scene) {\r\n\t\t\t\t\tif (scene) {\r\n\t\t\t\t\t\tthis.getImageCaptcha(this.focus)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: 'scene不能为空',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t},\n\t\t\t\timmediate:true\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetImageCaptcha(focus = true) {\r\n\t\t\t\tthis.loging = true\r\n\t\t\t\tif (focus) {\n\t\t\t\t\tthis.val = ''\r\n\t\t\t\t\tthis.focusCaptchaInput = true\r\n\t\t\t\t}\r\n\t\t\t\tconst uniIdCo = uniCloud.importObject(\"uni-captcha-co\", {\r\n\t\t\t\t\tcustomUI: true\r\n\t\t\t\t})\r\n\t\t\t\tuniIdCo.getImageCaptcha({\r\n\t\t\t\t\t\tscene: this.scene\r\n\t\t\t\t\t}).then(result => {\r\n\r\n\t\t\t\t\t\tthis.captchaBase64 = result.captchaBase64\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(e => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: e.message,\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}).finally(e => {\r\n\t\t\t\t\t\tthis.loging = false\r\n\t\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.captcha-box {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: flex-end;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.captcha-img-box,\r\n\t.captcha {\r\n\t\theight: 44px;\r\n\t\tline-height: 44px;\r\n\t}\r\n\r\n\t.captcha-img-box {\r\n\t\tposition: relative;\r\n\t\tbackground-color: #FEFAE7;\r\n\t}\r\n\r\n\t.captcha {\n\t\tbackground-color: #F8F8F8;\r\n\t\tfont-size: 14px;\r\n\t\tflex: 1;\r\n\t\tpadding: 0 20rpx;\r\n\t\tmargin-left: 20rpx;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tbox-sizing: border-box;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.captcha-img-box,\r\n\t.captcha-img,\r\n\t.loding {\n\t\theight: 44px !important;\r\n\t\twidth: 100px;\r\n\t}\n\t\n\t.captcha-img{\n\t\tcursor: pointer;\n\t}\r\n\r\n\t.loding {\r\n\t\tz-index: 9;\r\n\t\tcolor: #bbb;\r\n\t\tposition: absolute;\r\n\t\ttext-align: center;\r\n\t\tline-height: 45px;\r\n\t\tanimation: rotate 1s linear infinite;\r\n\t}\r\n\r\n\t.opacity {\r\n\t\topacity: 0.5;\r\n\t}\r\n\r\n\t@keyframes rotate {\r\n\t\tfrom {\r\n\t\t\ttransform: rotate(0deg)\r\n\t\t}\r\n\r\n\t\tto {\r\n\t\t\ttransform: rotate(360deg)\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-captcha.vue?vue&type=style&index=0&id=9e659dae&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-captcha.vue?vue&type=style&index=0&id=9e659dae&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775854555\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}