<template>
  <view class="page-container" @click="hideSuggestions">
    <view class="form-container">
      <!-- 问题标题 -->
      <view class="form-item">
        <view class="form-label">问题标题 <text class="required">*</text></view>
        <input 
          class="form-input" 
          v-model="formData.title" 
          placeholder="请输入问题标题"
          maxlength="50"
        />
      </view>

      <!-- 问题描述 -->
      <view class="form-item">
        <view class="form-label">问题描述 <text class="required">*</text></view>
        <textarea 
          class="form-textarea"
          v-model="formData.description" 
          placeholder="详细描述发现的问题"
          maxlength="200"
        />
      </view>

      <!-- 问题位置 -->
      <view class="form-item location-form-item">
        <view class="form-label">问题位置 <text class="required">*</text></view>
        <view class="location-selector">
          <!-- 常用位置快选 -->
          <view class="quick-locations" v-if="commonLocations.length > 0">
            <view class="quick-location-label">常用位置</view>
            <view class="location-tags">
              <view 
                v-for="location in commonLocations" 
                :key="location"
                class="location-tag"
                :class="{ 'active': formData.location === location }"
                @click="selectLocation(location)"
              >
                {{ location }}
              </view>
            </view>
          </view>
          
          <!-- 位置输入框 -->
          <view class="location-input-wrapper">
            <input 
              class="form-input location-input"
              v-model="formData.location" 
              placeholder="选择常用位置或手动输入"
              maxlength="30"
              @input="onLocationInput"
              @focus.stop="onLocationFocus"
              @click.stop
            />
            <view 
              v-if="formData.location" 
              class="clear-location" 
              @click.stop="clearLocation"
              @touchstart.stop="clearLocation"
            >
              <uni-icons type="clear" size="16" color="#C7C7CC" />
            </view>
          </view>
          
          <!-- 位置建议列表 -->
          <view 
            v-if="showLocationSuggestions" 
            class="location-suggestions"
            @click.stop
          >
            <!-- 常用位置（带删除功能） -->
            <view v-if="commonLocations.length > 0" class="suggestion-section">
              <view 
                v-for="(location, index) in commonLocations" 
                :key="index"
                class="suggestion-item common-location"
              >
                <view class="location-content" @click="selectLocation(location)">
                  <uni-icons type="star-filled" size="14" color="#FFB800" />
                  <text class="suggestion-text">{{ location }}</text>
                </view>
                <view class="delete-common" @click.stop="removeFromCommon(location, index)">
                  <uni-icons type="clear" size="16" color="#C7C7CC" />
                </view>
              </view>
            </view>
            
            <!-- 分类位置建议 -->
            <view v-if="filteredCategorizedSuggestions.length > 0" class="suggestion-section location-section">
              <view 
                v-for="(category, categoryIndex) in filteredCategorizedSuggestions" 
                :key="categoryIndex"
                class="category-group"
              >

                <view 
                  v-for="(location, itemIndex) in category.items" 
                  :key="itemIndex"
                  class="suggestion-item regular-location"
                >
                  <view class="location-content" @click="selectLocation(location)">
                    <uni-icons type="location" size="14" color="#007AFF" />
                    <text class="suggestion-text">{{ location }}</text>
                  </view>
                  <view class="star-btn" @click.stop="toggleFavorite(location)">
                    <uni-icons 
                      :type="isFavorite(location) ? 'star-filled' : 'star'" 
                      :size="16" 
                      :color="isFavorite(location) ? '#FFD700' : '#CCCCCC'"
                    />
                  </view>
                </view>
              </view>
            </view>
            
            <!-- 无匹配结果 -->
            <view v-if="!commonLocations.length && !filteredCategorizedSuggestions.length" class="no-suggestions">
              <text class="no-suggestions-text">暂无匹配的位置</text>
              <text class="no-suggestions-hint">您可以直接输入位置名称</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 负责人 -->
      <view class="form-item">
        <view class="form-label">负责人 <text class="required">*</text></view>
        <picker 
          :value="selectedResponsibleIndex" 
          :range="responsiblePersons" 
          range-key="text"
          @change="onResponsibleChange"
        >
          <view class="form-picker">
            {{ responsibleDisplayName }}
            <text class="picker-arrow">></text>
          </view>
        </picker>
      </view>

      <!-- 整改期限 -->
      <view class="form-item">
        <view class="form-label">整改期限 <text class="required">*</text></view>
        <picker 
          mode="date" 
          :value="formData.deadline" 
          :start="todayDate"
          @change="onDeadlineChange"
        >
          <view class="form-picker">
            {{ formData.deadline || '请选择整改期限' }}
            <text class="picker-arrow">></text>
          </view>
        </picker>
      </view>

      <!-- 优先级 -->
      <view class="form-item">
        <view class="form-label">优先级</view>
        <view class="priority-buttons">
          <view 
            v-for="(option, index) in priorityOptions" 
            :key="index"
            class="priority-btn"
            :class="{ 
              'priority-btn-active': formData.priority === option.value,
              'priority-btn-urgent': option.value === 'urgent'
            }"
            @click="selectPriority(option.value, index)"
          >
            <uni-icons 
              :type="option.value === 'urgent' ? 'fire' : 'info'" 
              :size="16" 
              :color="formData.priority === option.value ? '#ffffff' : '#666666'"
            />
            <text class="priority-text">{{ option.text }}</text>
          </view>
        </view>
      </view>

      <!-- 照片上传 -->
      <view class="form-item photo-upload-item">
        <view class="upload-section">
          <view class="section-header">
            <view class="form-label">问题照片</view>
            <view class="auto-upload-toggle" @click="toggleAutoUpload">
              <view class="toggle-label">自动上传</view>
              <view class="toggle-switch" :class="{ active: autoUpload }">
                <view class="toggle-circle"></view>
              </view>
            </view>
          </view>
        </view>
        <view class="image-upload photo-upload-content">
          <view class="photo-grid">
            <view 
              v-for="(photo, index) in formData.images" 
              :key="index"
              class="photo-item"
            >
              <image :src="getPhotoDisplayUrl(photo)" mode="aspectFill" @click="previewPhoto(index)" />
              <!-- 上传状态指示器 -->
              <view v-if="photo.uploading" class="photo-uploading">
                <view class="upload-spinner"></view>
              </view>
              <view v-else-if="photo.uploaded" class="photo-uploaded">
                <uni-icons type="checkmarkempty" size="16" color="white"></uni-icons>
              </view>
              <view class="photo-delete" @click="deleteImage(index)">
                <uni-icons type="close" size="18" color="white"></uni-icons>
              </view>
            </view>
            <view 
              v-if="formData.images.length < 6" 
              class="photo-add"
              @click="chooseImage"
              :class="{ disabled: uploading }"
            >
              <uni-icons type="camera" size="32" color="#8E8E93" />
              <text class="add-text">添加图片</text>
            </view>
          </view>
          <!-- 上传进度 -->
          <view v-if="uploading" class="upload-progress">
            <view class="progress-bar">
              <view class="progress-fill" :style="{ width: uploadProgress + '%' }"></view>
            </view>
            <text class="progress-text">正在上传照片... {{ uploadProgress }}%</text>
          </view>
          <view class="photo-tip">最多可上传6张照片，建议拍摄问题的清晰照片</view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <view class="button-group">
        <button 
          class="draft-btn" 
          @click="saveDraft" 
          :disabled="!canSaveDraft"
          :class="{ 'disabled': !canSaveDraft }"
        >
          {{ draftSaving ? '保存中...' : '保存草稿' }}
        </button>
        <button 
          class="submit-btn" 
          @click="submitForm" 
          :disabled="!canSubmit && !submitting"
          :class="{ 'loading': submitting }"
        >
          <view v-if="submitting" class="button-loading">
            <text>{{ isEditingPublished ? '更新中...' : '发布中...' }}</text>
          </view>
          <text v-else>{{ isEditingPublished ? '保存修改' : '立即发布' }}</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import uploadUtils from '@/utils/upload-utils.js'

export default {
  name: 'IssueAdd',
  data() {
    return {
      submitting: false,
      draftSaving: false,
      isEditingDraft: false,
      currentDraftId: null,
      isEditingPublished: false,
      currentEditId: null,
      pendingEditData: null,
      showLocationSuggestions: false,
      uploading: false,
      uploadProgress: 0,
      autoUpload: true, // 自动上传开关
      formData: {
        title: '',
        description: '',
        location: '',
        responsible: '',
        deadline: '',
        priority: 'normal',
        images: [] // 现在存储包含上传状态的对象数组
      },
      selectedResponsibleIndex: 0, // 默认选择第一项"请选择负责人"
      selectedPriorityIndex: 0, // 默认选中一般问题
      responsiblePersons: [],
      priorityOptions: [
        { value: 'normal', text: '一般问题' },
        { value: 'urgent', text: '紧急问题' }
      ],
      // 常用位置列表（基于历史使用频率）
      commonLocations: [],
      // 所有位置建议（动态加载）
      allLocationSuggestions: [],
      // 位置分类数据（用于分类显示）
      locationCategories: [],
      // 位置配置加载状态
      locationConfigLoading: false
    }
  },
  onLoad(options) {
    // 重置编辑状态，确保每次进入页面时状态干净
    this.isEditingPublished = false;
    this.isEditingDraft = false;
    this.currentEditId = null;
    this.currentDraftId = null;
    this.pendingEditData = null;
    
    // 检查是否是编辑草稿
    if (options.draftId) {
      this.loadDraft(options.draftId);
      // 设置草稿编辑模式的导航标题
      uni.setNavigationBarTitle({
        title: '编辑草稿'
      });
    }
    
    // 检查是否是编辑已发布的问题
    if (options.editId && options.editData) {
      this.loadEditData(options.editId, options.editData);
      // 设置编辑模式的导航标题
      uni.setNavigationBarTitle({
        title: '编辑问题'
      });
    }
    
    // 加载位置配置、常用位置和负责人列表
    this.loadLocationConfig();
    this.loadCommonLocations();
    this.loadResponsiblePersons();
  },

  onShow() {
    // 页面显示时刷新常用位置（可能在其他页面有变化）
    this.loadCommonLocations();
    
    // 检查负责人列表是否需要刷新（如果为空或只有"请选择"选项）
    // 但是如果正在编辑（有编辑ID或草稿ID），则不重新加载，避免重置选择器
    const isEditing = this.currentEditId || this.currentDraftId || this.isEditingPublished || this.isEditingDraft;
    
    if (this.responsiblePersons.length <= 1 && !isEditing) {
      this.loadResponsiblePersons();
    }
  },
  computed: {
    todayDate() {
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const day = String(today.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    
    canSubmit() {
      return !this.submitting && 
             this.formData.title.trim() && 
             this.formData.description.trim() && 
             this.formData.location.trim() && 
             this.formData.responsible && 
             this.formData.deadline;
    },
    
    canSaveDraft() {
      // 草稿只需要标题即可保存
      return !this.draftSaving && this.formData.title.trim();
    },
    
    // 负责人显示名称 - 改为computed属性
            responsibleDisplayName() {
          if (this.selectedResponsibleIndex > 0 && this.responsiblePersons[this.selectedResponsibleIndex]) {
            return this.responsiblePersons[this.selectedResponsibleIndex].text;
          }
          return '请选择负责人';
        },
    
    // 过滤后的分类位置建议
    filteredCategorizedSuggestions() {
      if (!this.locationCategories || this.locationCategories.length === 0) {
        return [];
      }
      
      const input = this.formData.location.toLowerCase().trim();
      
      return this.locationCategories
        .map(category => {
          let filteredItems = category.items || [];
          
          // 如果有输入内容，进行筛选
          if (input) {
            filteredItems = filteredItems.filter(location => 
              location.toLowerCase().includes(input)
            );
          }
          
          // 排除已在常用位置中的项目
          filteredItems = filteredItems.filter(location => 
            !this.commonLocations.includes(location)
          );
          
          return {
            ...category,
            items: filteredItems.slice(0, 5) // 每个分类最多显示5个
          };
        })
        .filter(category => category.items.length > 0); // 只显示有内容的分类
    },
    

  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    // 位置相关方法
    async loadLocationConfig() {
      try {
        this.locationConfigLoading = true;
        
        // 1. 尝试从服务器获取最新配置
        await this.fetchLocationConfigFromServer();
        
        // 2. 如果服务器获取失败，使用本地缓存
        if (this.allLocationSuggestions.length === 0) {
          this.loadLocationConfigFromCache();
        }
        
        // 3. 如果本地也没有，显示错误提示
        if (this.allLocationSuggestions.length === 0) {
          this.showLocationConfigError();
        }
        
      } catch (error) {
        this.loadLocationConfigFromCache();
        
        // 如果缓存也失败了，显示错误提示
        if (this.allLocationSuggestions.length === 0) {
          this.showLocationConfigError();
        }
      } finally {
        this.locationConfigLoading = false;
      }
    },
    
    async fetchLocationConfigFromServer() {
      try {
        const { callCloudFunction } = require('@/utils/auth.js');
        
        const result = await callCloudFunction('hygiene-location-management', {
          action: 'getLocationConfig'
        });
        
        if (result && result.success && result.data && result.data.locations) {
          const serverConfig = {
            version: '1.0.0',
            lastUpdated: new Date().toISOString(),
            locations: result.data.locations
          };
          
          // 设置分类数据
          this.locationCategories = serverConfig.locations;
          
          // 展平所有位置
          this.allLocationSuggestions = serverConfig.locations.reduce((acc, category) => {
            return acc.concat(category.items || []);
          }, []);
          
          // 缓存到本地
          uni.setStorageSync('location_config', serverConfig);
          uni.setStorageSync('location_config_timestamp', Date.now());
          

        } else {
          throw new Error('获取位置配置失败');
        }
        
      } catch (error) {
        throw error;
      }
    },
    
    loadLocationConfigFromCache() {
      try {
        const cachedConfig = uni.getStorageSync('location_config');
        const cacheTimestamp = uni.getStorageSync('location_config_timestamp');
        
        // 检查缓存是否过期（7天）
        const cacheAge = Date.now() - (cacheTimestamp || 0);
        const cacheExpired = cacheAge > 7 * 24 * 60 * 60 * 1000;
        
        if (cachedConfig && !cacheExpired) {
          // 设置分类数据
          this.locationCategories = cachedConfig.locations;
          
          this.allLocationSuggestions = cachedConfig.locations.reduce((acc, category) => {
            return acc.concat(category.items);
          }, []);
          

        }
        
      } catch (error) {
        // 缓存加载失败，使用空数据
      }
    },
    
    showLocationConfigError() {
      // 显示位置配置加载失败的错误提示
      uni.showModal({
        title: '位置配置加载失败',
        content: '无法获取位置配置数据，请检查网络连接或联系管理员。',
        showCancel: false,
        confirmText: '重试',
        success: (res) => {
          if (res.confirm) {
            // 用户点击重试，重新加载位置配置
            this.loadLocationConfig();
          }
        }
      });
      
      // 设置空的位置配置，确保页面不崩溃
      this.locationCategories = [];
      this.allLocationSuggestions = [];
    },

    loadCommonLocations() {
      try {
        // 从收藏列表加载常用位置
        const favoriteLocations = uni.getStorageSync('favorite_locations') || [];
        this.commonLocations = favoriteLocations;
      } catch (error) {
        // 加载常用位置失败，使用空数据
      }
    },
    
    selectLocation(location) {
      this.formData.location = location;
      this.showLocationSuggestions = false;
      // 移除自动添加到常用位置的逻辑，改为手动星标收藏
    },
    
    clearLocation() {
      this.formData.location = '';
      this.showLocationSuggestions = false; // 清空时隐藏建议列表
    },
    
    onLocationInput() {
      this.showLocationSuggestions = true;
    },

    onLocationFocus() {
      this.showLocationSuggestions = true;
    },
    
    // 检查位置是否被收藏
    isFavorite(location) {
      return this.commonLocations.includes(location);
    },
    
    // 切换收藏状态
    toggleFavorite(location) {
      try {
        const favoriteLocations = uni.getStorageSync('favorite_locations') || [];
        const index = favoriteLocations.indexOf(location);
        
        if (index > -1) {
          // 取消收藏
          favoriteLocations.splice(index, 1);
          uni.showToast({
            title: '已取消收藏',
            icon: 'none',
            duration: 1500
          });
        } else {
          // 添加收藏
          favoriteLocations.unshift(location); // 添加到开头
          uni.showToast({
            title: '已添加到常用位置',
            icon: 'success',
            duration: 1500
          });
        }
        
        uni.setStorageSync('favorite_locations', favoriteLocations);
        
        // 更新常用位置列表
        this.loadCommonLocations();
      } catch (error) {
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    },
    
    // 从常用位置中删除
    removeFromCommon(location, index) {
      uni.showModal({
        title: '删除常用位置',
        content: `确定要从常用位置中删除"${location}"吗？`,
        success: (res) => {
          if (res.confirm) {
            try {
              // 从收藏列表中删除
              const favoriteLocations = uni.getStorageSync('favorite_locations') || [];
              const favoriteIndex = favoriteLocations.indexOf(location);
              
              if (favoriteIndex > -1) {
                favoriteLocations.splice(favoriteIndex, 1);
                uni.setStorageSync('favorite_locations', favoriteLocations);
              }
              
              // 从当前常用位置列表中删除
              this.commonLocations.splice(index, 1);
              
              uni.showToast({
                title: '已删除',
                icon: 'success',
                duration: 1500
              });
            } catch (error) {
              uni.showToast({
                title: '删除失败',
                icon: 'none'
              });
            }
          }
        }
      });
    },
    
    // 加载负责人列表
    async loadResponsiblePersons() {
      try {
        const { callCloudFunction } = require('@/utils/auth.js');
        
        // 使用月度检查专用云函数获取负责人列表
        const result = await callCloudFunction('hygiene-monthly-inspection', {
          action: 'getResponsibleUsers'
        });
        
        if (result && result.success && result.data) {
          // 过滤掉匿名用户和admin用户，只显示responsible角色的用户
          const filteredUsers = result.data
            .filter(user => {
              // 排除admin用户
              const roles = Array.isArray(user.role) ? user.role : [user.role];
              if (roles.includes('admin')) return false;
              
              // 只显示角色为"responsible"的用户
              if (!roles.includes('responsible')) return false;
              
              // 排除匿名用户（没有姓名的用户）
              const displayName = user.nickname || user.username;
              if (!displayName || displayName.trim() === '') return false;
              
              // 排除以"匿名"开头的用户
              if (displayName.startsWith('匿名')) return false;
              
              return true;
            })
            .map(user => ({
              value: user._id,
              text: user.nickname || user.username // 只显示姓名，不显示角色
            }))
            .filter(item => item.value && item.text); // 再次过滤确保没有空的value或text
          
          // 一次性完成数组的构建和清理，避免多次赋值触发picker重置
          const finalResponsiblePersons = [
            { value: '', text: '请选择负责人' },
            ...filteredUsers
          ].filter((item, index) => {
            if (index === 0) return true; // 保留第一项"请选择负责人"
            return item && item.text && item.text.trim() !== '';
          });
          
          // 只设置一次，避免触发picker组件的重置
          this.responsiblePersons = finalResponsiblePersons;

          
          // 如果有待处理的编辑数据，现在设置选择器状态
          if (this.pendingEditData) {
            this.setEditSelectors(this.pendingEditData);
            this.pendingEditData = null;
          } else {
            // 只有在没有编辑数据时才重置选择器索引
            this.selectedResponsibleIndex = 0;
          }
        } else {
          uni.showToast({
            title: '加载负责人列表失败',
            icon: 'none'
          });
          // 初始化为空列表，只包含"请选择"选项
          this.responsiblePersons = [
            { value: '', text: '请选择负责人' }
          ];
          // 只有在没有编辑数据时才重置选择器索引
          if (!this.pendingEditData) {
            this.selectedResponsibleIndex = 0;
          }
        }
              } catch (error) {
        uni.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
        // 初始化为空列表，只包含"请选择"选项
        this.responsiblePersons = [
          { value: '', text: '请选择负责人' }
        ];
        // 只有在没有编辑数据时才重置选择器索引
        if (!this.pendingEditData) {
          this.selectedResponsibleIndex = 0;
        }
        
        // 即使加载失败，也要处理待处理的编辑数据
        if (this.pendingEditData) {
          this.setEditSelectors(this.pendingEditData);
          this.pendingEditData = null;
        }
      }
    },
    
    // 负责人选择
    onResponsibleChange(e) {
      const index = e.detail.value;
      this.selectedResponsibleIndex = index;
      
      // 安全检查：确保索引有效且对应的选项存在
      if (index >= 0 && index < this.responsiblePersons.length && this.responsiblePersons[index]) {
        if (index === 0) {
          // 选择了"请选择负责人"，清空值
          this.formData.responsible = '';
        } else {
          // 选择了具体的负责人
          this.formData.responsible = this.responsiblePersons[index].value;
        }
      } else {
        // 如果索引无效，清空选择
        this.formData.responsible = '';
        this.selectedResponsibleIndex = 0; // 重置为"请选择负责人"
      }
    },
    
    // 整改期限选择
    onDeadlineChange(e) {
      this.formData.deadline = e.detail.value;
    },
    
    // 优先级选择 - 新的按钮方式
    selectPriority(value, index) {
      this.formData.priority = value;
      this.selectedPriorityIndex = index;
    },
    
    // 优先级选择 - 保留兼容性
    onPriorityChange(e) {
      const index = e.detail.value;
      this.selectedPriorityIndex = index;
      this.formData.priority = this.priorityOptions[index].value;
    },
    
    // 获取优先级文本
    getPriorityText() {
      const option = this.priorityOptions.find(item => item.value === this.formData.priority);
      return option ? option.text : '一般问题';
    },
    
    // 选择图片
    chooseImage() {
      if (this.formData.images.length >= 6) {
        uni.showToast({
          title: '最多只能选择6张图片',
          icon: 'none'
        });
        return;
      }

      uni.chooseImage({
        count: 6 - this.formData.images.length,
        sizeType: ['compressed'], // 只使用压缩版本以节省内存和上传时间
        sourceType: ['camera', 'album'],
        success: (res) => {
          const newPhotos = this.processNewPhotos(res.tempFilePaths);
          
          // 批量添加到照片列表
          this.formData.images.push(...newPhotos);
          
          // 如果开启自动上传，立即上传新选择的照片
          if (this.autoUpload) {
            this.autoUploadNewPhotos(newPhotos);
          }
        },
        fail: () => {
          uni.showToast({
            title: '选择照片失败',
            icon: 'none'
          });
        }
      });
    },

    // 处理新选择的照片
    processNewPhotos(tempFilePaths) {
      return tempFilePaths.map(path => ({
        url: path,
        uploaded: false,
        cloudUrl: '',
        cloudPath: '',
        uploading: false
      }));
    },

    // 自动上传新选择的照片
    async autoUploadNewPhotos(newPhotos) {
      // 使用 Promise.allSettled 并行上传，避免阻塞
      const uploadPromises = newPhotos.map(async (photo, i) => {
        const photoIndex = this.formData.images.findIndex(p => p.url === photo.url);
        
        if (photoIndex === -1) return { success: false, index: i };
        
        try {
          // 标记为正在上传
          this.$set(this.formData.images[photoIndex], 'uploading', true);
          
          // 单张照片上传
          const uploadResult = await this.uploadSinglePhoto(photo);
          
          if (uploadResult.success) {
            // 批量更新照片信息
            Object.assign(this.formData.images[photoIndex], {
              uploaded: true,
              cloudUrl: uploadResult.url,
              cloudPath: uploadResult.cloudPath,
              uploading: false,
              cloudReady: false // 云端图片未准备好
            });
            
            // 预加载云端图片
            this.preloadCloudImage(photoIndex, uploadResult.url);
            return { success: true, index: i };
          } else {
            throw new Error(uploadResult.error || '上传失败');
          }
        } catch (error) {
          this.$set(this.formData.images[photoIndex], 'uploading', false);
          return { success: false, index: i, error: error.message };
        }
      });

      // 等待所有上传完成
      const results = await Promise.allSettled(uploadPromises);
      
      // 统计失败的上传
      const failures = results
        .filter(result => result.status === 'fulfilled' && !result.value.success)
        .map(result => result.value);
      
      if (failures.length > 0) {
        uni.showToast({
          title: `${failures.length}张照片上传失败`,
          icon: 'none',
          duration: 2000
        });
      }
    },
    
    // 上传单张图片（使用 uploadUtils 支持目录上传）
    async uploadSinglePhoto(photo) {
      try {
        const cloudPath = this.generateCloudPath();
        
        // 使用 uploadToCloud 方法上传单张照片
        const uploadResult = await uploadUtils.uploadToCloud(photo.url, cloudPath);
        
        if (uploadResult?.fileID) {
          // 获取访问URL
          const fileInfo = await uploadUtils.getFileInfo(uploadResult.fileID);
          
          return {
            success: true,
            cloudPath: uploadResult.fileID,
            url: fileInfo.tempFileURL || uploadResult.fileID,
            size: uploadResult.actualSize
          };
        } else {
          throw new Error('上传返回结果异常');
        }
      } catch (error) {
        return { success: false, error: error.message };
      }
    },

    // 生成云存储路径
    generateCloudPath() {
      const timestamp = Date.now();
      const random = Math.random().toString(36).substring(2, 8);
      return `6s/issues/${timestamp}_${random}.jpg`;
    },
    
    // 删除图片
    async deleteImage(index) {
      if (index < 0 || index >= this.formData.images.length) {
        return;
      }

      const photo = this.formData.images[index];
      
      // 如果照片已经上传到云端，需要删除云端文件
      if (photo.uploaded && photo.cloudPath) {
        try {
          uni.showLoading({ title: '删除照片中...' });
          
          // 调用云函数删除云端文件
          await uniCloud.callFunction({
            name: 'delete-file',
            data: {
              fileList: [this.extractFileId(photo.cloudPath)]
            }
          });
        } catch (error) {
          uni.showToast({
            title: '删除云端照片失败',
            icon: 'none'
          });
        } finally {
          uni.hideLoading();
        }
      }
      
      // 从本地数组中移除
      this.formData.images.splice(index, 1);
    },

    // 从URL中提取文件ID
    extractFileId(url) {
      if (url.startsWith('cloud://')) {
        const parts = url.split('/');
        return parts[parts.length - 1];
      } else if (url.includes('tcb-api')) {
        const urlObj = new URL(url);
        return urlObj.pathname.split('/').pop();
      }
      return url;
    },

    // 预览照片
    previewPhoto(index) {
      const urls = this.formData.images.map(photo => this.getPhotoDisplayUrl(photo));
      uni.previewImage({
        urls: urls,
        current: index
      });
    },

    // 获取照片显示URL
    getPhotoDisplayUrl(photo) {
      if (typeof photo === 'string') {
        return photo; // 兼容旧的字符串格式
      }
      
      // 只有云端图片预加载完成才切换，避免闪烁
      if (photo.uploaded && photo.cloudUrl && photo.cloudReady) {
        return photo.cloudUrl;
      }
      
      // 默认使用本地图片，直到云端图片准备就绪
      return photo.url || photo.cloudUrl || photo;
    },

    // 预加载云端图片（小程序兼容）
    preloadCloudImage(photoIndex, cloudUrl) {
      uni.getImageInfo({
        src: cloudUrl,
        success: () => {
          if (this.formData.images[photoIndex]) {
            this.$set(this.formData.images[photoIndex], 'cloudReady', true);
          }
        },
        fail: () => {
          // 预加载失败，继续使用本地图片
          if (this.formData.images[photoIndex]) {
            this.$set(this.formData.images[photoIndex], 'cloudReady', false);
          }
        }
      });
    },
    
    // 表单验证
    validateForm() {
      if (!this.formData.title.trim()) {
        uni.showToast({
          title: '请输入问题标题',
          icon: 'none'
        });
        return false;
      }
      
      if (this.formData.title.length < 2 || this.formData.title.length > 50) {
        uni.showToast({
          title: '标题长度在2-50个字符之间',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.description.trim()) {
        uni.showToast({
          title: '请输入问题描述',
          icon: 'none'
        });
        return false;
      }
      
      if (this.formData.description.length < 5 || this.formData.description.length > 200) {
        uni.showToast({
          title: '描述长度在5-200个字符之间',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.location.trim()) {
        uni.showToast({
          title: '请输入问题位置',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.responsible) {
        uni.showToast({
          title: '请选择负责人',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.deadline) {
        uni.showToast({
          title: '请选择整改期限',
          icon: 'none'
        });
        return false;
      }
      
      return true;
    },
    
    async submitForm() {
      if (!this.validateForm()) {
        return;
      }
      
      try {
        this.submitting = true;
        
        const { callCloudFunction } = require('@/utils/auth.js');
        
        // 构建提交数据 - 提交到月度检查系统
        const submitData = {
          title: this.formData.title.trim(),
          description: this.formData.description.trim(),
          location_info: {
            location_type: 'custom',
            location_name: this.formData.location.trim(),
            location_description: ''
          },
          inspection_info: {
            inspection_date: new Date().toISOString(),
            inspection_type: 'monthly_routine'
          },
          category: 'safety', // 默认安全问题
          severity: this.getPriorityToSeverity(this.formData.priority),
          priority: this.formData.priority,
          assigned_to: this.getResponsibleId(),
          assigned_to_name: this.getResponsibleName(),
          photos: this.preparePhotosForSubmit(),
          tags: [],
          expected_completion_date: this.formData.deadline
        };
        
        // 根据模式选择API操作
        const action = this.isEditingPublished ? 'updateMonthlyIssue' : 'createMonthlyIssue';
        const apiData = this.isEditingPublished ? {
          issue_id: this.currentEditId,
          ...submitData
        } : submitData;
        
        const result = await callCloudFunction('hygiene-monthly-inspection', {
          action: action,
          data: apiData
        });
        
        if (result && result.success) {
          uni.showToast({
            title: this.isEditingPublished ? '问题更新成功' : '问题发布成功',
            icon: 'success'
          });
          
          // 通知其他页面数据已更新
          const eventData = {
            action: this.isEditingPublished ? 'update' : 'create',
            issueId: this.isEditingPublished ? this.currentEditId : (result.data?.id || result.data?._id),
            issueNumber: result.data?.issue_number,
            issueData: submitData,
            fromDraft: this.isEditingDraft // 标识是否来自草稿发布
          };
          uni.$emit('monthlyIssueUpdated', eventData);
          
          // 清除草稿（如果有）
          this.clearDraftIfExists();
          
          // 如果是从草稿发布，延迟通知草稿列表更新（确保本地存储操作完成）
          if (this.isEditingDraft && this.currentDraftId) {
            setTimeout(() => {
              const draftEventData = {
                action: 'delete',
                draftId: this.currentDraftId
              };
              uni.$emit('issueDraftUpdated', draftEventData);
            }, 100); // 确保本地存储操作完成
          }
          
          // 返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          throw new Error(result.message || '发布失败');
        }
        
      } catch (error) {
        uni.showToast({
          title: error.message || '发布失败，请重试',
          icon: 'error'
        });
      } finally {
        this.submitting = false;
      }
    },

    // 准备照片数据用于提交
    preparePhotosForSubmit() {
      return this.formData.images
        .filter(photo => photo.uploaded && photo.cloudPath) // 只提交已上传的照片
        .map(photo => ({
          url: photo.cloudPath,
          description: '月度检查发现的问题',
          timestamp: new Date()
        }));
    },
    
    // 优先级转换为严重程度
    getPriorityToSeverity(priority) {
      const priorityMap = {
        'normal': 'medium',
        'urgent': 'high'
      };
      return priorityMap[priority] || 'medium';
    },
    
    // 获取负责人ID
    getResponsibleId() {
      if (this.selectedResponsibleIndex > 0 && this.responsiblePersons[this.selectedResponsibleIndex]) {
        const value = this.responsiblePersons[this.selectedResponsibleIndex].value;
        return value || null; // 如果value为空字符串，返回null
      }
      return null;
    },
    
    // 获取负责人姓名
    getResponsibleName() {
      if (this.selectedResponsibleIndex > 0 && this.responsiblePersons[this.selectedResponsibleIndex]) {
        const text = this.responsiblePersons[this.selectedResponsibleIndex].text;
        // 由于现在只显示姓名，不需要分割
        return text !== '请选择负责人' ? text : null;
      }
      return null;
    },


    
    // 清除草稿
    clearDraftIfExists() {
      if (this.isEditingDraft && this.currentDraftId) {
        try {
          const drafts = uni.getStorageSync('issue_drafts') || [];
          const filteredDrafts = drafts.filter(draft => draft.id !== this.currentDraftId);
          uni.setStorageSync('issue_drafts', filteredDrafts);
        } catch (error) {
          // 清除草稿失败，静默处理
        }
      }
    },
    
    async saveDraft() {
      if (!this.canSaveDraft) {
        return;
      }
      
      try {
        this.draftSaving = true;
        
        // 草稿保存（本地存储）
        
        // 构建草稿数据
        const draftData = {
          ...this.formData,
          // 处理照片数据，优先保存云端URL
          images: this.formData.images.map(photo => {
            if (typeof photo === 'string') {
              return photo; // 兼容旧格式
            }
            // 如果已上传到云端，使用云端数据
            if (photo.uploaded && photo.cloudUrl) {
              return {
                ...photo,
                url: photo.cloudUrl, // 将url字段更新为云端URL
                displayUrl: photo.cloudUrl // 添加显示URL字段
              };
            }
            return photo; // 未上传的保持原样
          }),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          status: 'draft', // 草稿状态
          id: Date.now() // 生成本地草稿唯一标识符
        };
        
        // 存储到本地缓存
        this.saveDraftToLocal(draftData);
        
        uni.showToast({
          title: '草稿保存成功',
          icon: 'success'
        });
        
        // 通知其他页面草稿数据已更新
        uni.$emit('issueDraftUpdated', {
          action: this.isEditingDraft ? 'update' : 'create',
          draftId: this.isEditingDraft ? this.currentDraftId : draftData.id,
          draftData: draftData
        });
        
        // 返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
        
      } catch (error) {
        uni.showToast({
          title: '保存失败，请重试',
          icon: 'error'
        });
      } finally {
        this.draftSaving = false;
      }
    },
    
    // 保存草稿到本地缓存
    saveDraftToLocal(draftData) {
      try {
        const existingDrafts = uni.getStorageSync('issue_drafts') || [];
        
        if (this.isEditingDraft) {
          // 更新现有草稿
          const index = existingDrafts.findIndex(draft => draft.id === this.currentDraftId);
          if (index !== -1) {
            existingDrafts[index] = { ...draftData, id: this.currentDraftId };
          }
        } else {
          // 添加新草稿
          existingDrafts.push(draftData);
        }
        
        // 保存到本地
        uni.setStorageSync('issue_drafts', existingDrafts);
        

      } catch (error) {
        // 保存草稿到本地失败，静默处理
      }
    },
    
    // 加载草稿数据
    loadDraft(draftId) {
      try {
        const drafts = uni.getStorageSync('issue_drafts') || [];
        const draft = drafts.find(d => d.id == draftId);
        
        if (draft) {
          this.isEditingDraft = true;
          this.currentDraftId = draft.id;
          this.formData = { ...draft };
          
          // 保存草稿数据，待负责人列表加载完成后设置
          this.pendingEditData = {
            responsible_id: draft.responsible,
            priority: draft.priority
          };

        }
      } catch (error) {
        // 加载草稿失败，静默处理
      }
    },

    // 加载编辑数据
    loadEditData(editId, editDataStr) {
      try {
        const editData = JSON.parse(decodeURIComponent(editDataStr));
        // 设置为编辑模式
        this.isEditingPublished = true;
        this.currentEditId = editId;
        
        // 填充表单数据
        this.formData = {
          title: editData.title || '',
          description: editData.description || '',
          location: editData.location || '',
          responsible: editData.responsible_id || '',
          deadline: editData.deadline || '',
          priority: editData.priority || 'normal',
          images: this.processEditImages(editData.images || [])
        };
        
        // 保存编辑数据，待负责人列表加载完成后设置
        this.pendingEditData = editData;
        
      } catch (error) {
        uni.showToast({
          title: '加载编辑数据失败',
          icon: 'error'
        });
      }
    },
    
    // 设置编辑时的选择器状态
    setEditSelectors(editData) {
      // 设置负责人选择器
      if (editData.responsible_id && this.responsiblePersons.length > 0) {
        const responsibleIndex = this.responsiblePersons.findIndex(p => p.value === editData.responsible_id);
        
        if (responsibleIndex > -1) {
          this.selectedResponsibleIndex = responsibleIndex;
          this.formData.responsible = editData.responsible_id;
        }
      }
      
      // 设置优先级选择器
      if (editData.priority) {
        const priorityIndex = this.priorityOptions.findIndex(p => p.value === editData.priority);
        if (priorityIndex > -1) {
          this.selectedPriorityIndex = priorityIndex;
        }
      }
    },
    
    // 处理编辑时的图片数据
    processEditImages(images) {
      if (!Array.isArray(images)) return [];
      
      return images.map(img => {
        if (typeof img === 'string') {
          return {
            url: img,
            cloudUrl: img,
            cloudPath: img,
            uploaded: true,
            uploading: false
          };
        } else if (img && img.url) {
          return {
            url: img.url,
            cloudUrl: img.url,
            cloudPath: img.url,
            uploaded: true,
            uploading: false
          };
        }
        return img;
      }).filter(Boolean);
    },

    // 切换自动上传状态
    toggleAutoUpload() {
      this.autoUpload = !this.autoUpload;
      uni.showToast({
        title: this.autoUpload ? '已开启自动上传' : '已关闭自动上传',
        icon: 'none'
      });
    },

    // 点击页面其他地方隐藏建议列表
    hideSuggestions() {
      this.showLocationSuggestions = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
	min-height: 100vh;
	background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
	width: 100%;
	overflow-x: hidden; /* 防止水平滚动 */
	box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
  /* 滚动性能优化 */
  -webkit-overflow-scrolling: touch;
  transform: translateZ(0);
}

/* 表单容器 */
.form-container {
  padding: 32rpx;
}

/* 表单项 - 统一卡片样式 */
.form-item {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08), 
              0 2rpx 6rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  /* 性能优化 */
  transform: translateZ(0);
  will-change: transform;
}

.form-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 16rpx;
}

.required {
  color: #FF3B30;
  margin-left: 4rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid rgba(240, 240, 247, 0.8);
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  background: linear-gradient(135deg, rgba(248, 249, 250, 0.9), rgba(255, 255, 255, 0.8));
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  backdrop-filter: blur(5rpx);
}

.form-input:focus {
  border-color: #007AFF;
  background: #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.15),
              0 2rpx 6rpx rgba(0, 122, 255, 0.1);
  transform: translateY(-1rpx);
}

.form-textarea {
  width: 100%;
  min-height: 160rpx;
  border: 2rpx solid rgba(240, 240, 247, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  background: linear-gradient(135deg, rgba(248, 249, 250, 0.9), rgba(255, 255, 255, 0.8));
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  resize: none;
  line-height: 1.5;
  box-sizing: border-box;
  backdrop-filter: blur(5rpx);
}

.form-textarea:focus {
  border-color: #007AFF;
  background: #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.15),
              0 2rpx 6rpx rgba(0, 122, 255, 0.1);
  transform: translateY(-1rpx);
}

.form-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  border: 2rpx solid rgba(240, 240, 247, 0.8);
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  background: linear-gradient(135deg, rgba(248, 249, 250, 0.9), rgba(255, 255, 255, 0.8));
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  backdrop-filter: blur(5rpx);
  cursor: pointer;
}

.picker-arrow {
  font-size: 24rpx;
  color: #8E8E93;
  transform: rotate(90deg);
}

/* 图片上传 - 仅保留布局，移除视觉样式 */
.upload-section {
  margin: 32rpx 32rpx 16rpx 32rpx; /* 保持与form-item原有内边距一致，减少底部间距 */
  padding: 0; /* 移除内边距 */
  background: transparent; /* 移除背景色 */
  border: none; /* 移除边框 */
  border-radius: 0; /* 移除圆角 */
}

/* 照片上传专用form-item样式 */
.photo-upload-item {
  padding: 0 !important; /* 移除form-item的默认内边距 */
  margin-bottom: 0 !important; /* 移除问题照片容器的下边距 */
}

.photo-upload-item .form-label {
  margin-bottom: 0 !important; /* 移除标题下方间距 */
}

.photo-upload-content {
  margin: 0 32rpx 32rpx 32rpx; /* 与upload-section保持一致的左右边距 */
}

/* 照片上传区域的section-header特殊样式 */
.photo-upload-item .section-header {
  background: transparent !important; /* 移除灰色背景 */
  border-bottom: none !important; /* 移除下边线 */
  padding: 0 !important; /* 移除内边距 */
  margin-bottom: 16rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.auto-upload-toggle {
  display: flex;
  align-items: center;
  gap: 12rpx;
  cursor: pointer;
}

.toggle-label {
  font-size: 24rpx;
  color: #8E8E93;
}

.toggle-switch {
  width: 76rpx;
  height: 44rpx;
  background: linear-gradient(135deg, #E5E5EA, #D1D1D6);
  border-radius: 22rpx;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.toggle-switch.active {
  background: linear-gradient(135deg, #34C759, #30D158);
  box-shadow: 0 4rpx 12rpx rgba(52, 199, 89, 0.3),
              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);
}

.toggle-circle {
  width: 36rpx;
  height: 36rpx;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: 50%;
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15),
              0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.toggle-switch.active .toggle-circle {
  transform: translateX(32rpx);
  background: linear-gradient(135deg, #ffffff, #f0fff0);
  box-shadow: 0 4rpx 16rpx rgba(52, 199, 89, 0.2),
              0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.image-upload {
  margin-top: 16rpx;
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.photo-item {
  position: relative;
  width: 170rpx;
  height: 170rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1),
              0 2rpx 4rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.photo-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.15),
              0 3rpx 8rpx rgba(0, 0, 0, 0.1);
}

.photo-item image {
  width: 100%;
  height: 100%;
}

.photo-delete {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  width: 36rpx;
  height: 36rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.photo-add {
  width: 170rpx;
  height: 170rpx;
  border: 2rpx dashed rgba(0, 122, 255, 0.3);
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(255, 255, 255, 0.9));
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.photo-add:hover {
  border-color: #007AFF;
  background: linear-gradient(135deg, rgba(230, 243, 255, 0.9), rgba(240, 248, 255, 0.95));
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0, 122, 255, 0.15);
}

.photo-add.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.photo-add text {
  font-size: 24rpx;
  color: #8E8E93;
}

.photo-tip {
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.4;
}

.photo-uploading {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-spinner {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-top: 2rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.photo-uploaded {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #34C759;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-progress {
  margin: 16rpx 0;
  padding: 16rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #E5E5EA;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007AFF 0%, #5AC8FA 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #8E8E93;
  text-align: center;
  display: block;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 优先级按钮样式 */
.priority-buttons {
  display: flex;
  gap: 16rpx;
  margin-top: 16rpx;
}

.priority-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 20rpx 16rpx;
  border: 2rpx solid rgba(229, 229, 229, 0.6);
  border-radius: 16rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08),
              0 2rpx 6rpx rgba(0, 0, 0, 0.04),
              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.priority-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1),
              0 1rpx 3rpx rgba(0, 0, 0, 0.06),
              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.6);
}

/* 一般问题激活样式 */
.priority-btn-active {
  border-color: #007AFF;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  box-shadow: 0 6rpx 20rpx rgba(0, 122, 255, 0.3),
              0 4rpx 12rpx rgba(0, 122, 255, 0.2),
              0 2rpx 6rpx rgba(0, 122, 255, 0.1),
              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
}

/* 紧急问题默认样式 */
.priority-btn-urgent {
  border-color: rgba(229, 229, 229, 0.6);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
}

/* 紧急问题激活样式 */
.priority-btn-urgent.priority-btn-active {
  border-color: #FF6B35;
  background: linear-gradient(135deg, #FF6B35 0%, #FF8A50 100%);
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 53, 0.3),
              0 4rpx 12rpx rgba(255, 107, 53, 0.2),
              0 2rpx 6rpx rgba(255, 107, 53, 0.1),
              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
}

/* 文字样式 */
.priority-text {
  font-size: 28rpx;
  color: #666666;
  font-weight: 500;
}

/* 紧急问题未选中时的图标和文字颜色 */
.priority-btn-urgent .priority-text {
  color: #666666;
}

/* 激活状态的文字 - 需要更高权重覆盖上面的样式 */
.priority-btn-active .priority-text {
  color: #ffffff !important;
}

/* 紧急问题激活状态的文字 */
.priority-btn-urgent.priority-btn-active .priority-text {
  color: #ffffff !important;
}

.image-upload {
  margin-top: 16rpx;
}

/* 位置选择器样式 */
.location-selector {
  position: relative;
  z-index: 10000; /* 确保选择器及其子元素在最上层 */
}

.quick-locations {
  margin-bottom: 16rpx;
}

.quick-location-label {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 12rpx;
}

.location-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.location-tag {
  padding: 12rpx 20rpx;
  background: #F2F2F7;
  border: 2rpx solid #F2F2F7;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #1C1C1E;
  transition: all 0.3s ease;
}

.location-tag.active {
  background: #E3F2FD;
  border-color: #007AFF;
  color: #007AFF;
}

.location-tag:active {
  background: #E0E0E0;
  transform: scale(0.95);
}

.location-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.location-input {
  flex: 1;
  margin: 0;
  padding-right: 60rpx; /* 为清空按钮预留空间 */
}

.clear-location {
  position: absolute;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(199, 199, 204, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
  z-index: 10;
}

.clear-location:hover {
  background: rgba(199, 199, 204, 0.2);
}

.clear-location:active {
  background: rgba(199, 199, 204, 0.3);
  transform: translateY(-50%) scale(0.9);
}

/* 位置选择表单项需要允许overflow */
.location-form-item {
  overflow: visible !important;
  z-index: 9999; /* 提高到很高的层级 */
  /* 移除创建层叠上下文的属性 */
  transform: none !important;
  will-change: auto !important;
}

.location-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  border-radius: 24rpx; /* 统一使用24rpx圆角 */
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12), 
              0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  z-index: 999999;
  max-height: 480rpx; /* 增加高度 */
  overflow-y: auto;
  margin-top: 8rpx;
  /* 性能优化 */
  transform: translateZ(0);
  will-change: transform;
  /* 平滑滚动 */
  -webkit-overflow-scrolling: touch;
}

.suggestion-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 24rpx; /* 增加内边距 */

  transition: all 0.2s ease; /* 减少过渡时间，降低闪烁 */
  min-height: 80rpx; /* 增加高度 */
  position: relative;
  overflow: hidden;
}

/* 使用伪元素作为分割线，避免:last-child规则 */
.suggestion-item::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1rpx;
  background: rgba(0, 0, 0, 0.05);
  pointer-events: none;
}

/* 隐藏最后一个分割线 */
.location-suggestions > .suggestion-section:last-child .suggestion-item:last-child::after {
  display: none;
}

.suggestion-item:active {
  transform: scale(0.98);
  background: rgba(0, 0, 0, 0.05);
}

.suggestion-text {
  font-size: 30rpx; /* 稍微增大字体 */
  color: #1C1C1E;
  font-weight: 500; /* 稍微加粗 */
  letter-spacing: 0.5rpx; /* 增加字间距 */
}

/* 位置内容区域 */
.location-content {
  display: flex;
  align-items: center;
  gap: 16rpx; /* 增加图标和文字间距 */
  flex: 1;
  cursor: pointer;
  padding: 4rpx 0; /* 增加垂直点击区域 */
}

/* 星标按钮样式 */
.star-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 52rpx;
  height: 52rpx;
  border-radius: 26rpx;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  margin-left: 12rpx;
}

.star-btn:active {
  background: rgba(255, 255, 255, 0.9);
  transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.15);
}

/* 分区样式 */
.suggestion-section {
  margin-bottom: 1rpx; /* 移除section之间的间距 */
}

.suggestion-section:first-child {
  padding-top: 4rpx;
}

.suggestion-section:last-child {
  margin-bottom: 4rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 20rpx 6rpx;
  border-bottom: 1rpx solid #E5E5E5;
  margin-bottom: 2rpx;
  background: #F8F8F8;
}

.section-title {
  font-size: 26rpx;
  color: #1C1C1E;
  font-weight: 600;
}

.section-count {
  font-size: 22rpx;
  color: #8E8E93;
  background: #F2F2F7;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

/* 常用位置样式 */
.common-location {
  position: relative;
  border-left: 4rpx solid #FF9800;
  justify-content: flex-start;
}

.common-location:active {
  background: rgba(0, 0, 0, 0.05);
  transform: scale(0.98);
}

/* 常用位置的内容区域样式 */
.common-location .location-content {
  flex: 1;
}

/* 普通位置样式 - 蓝色彩带效果 */
.regular-location {
  position: relative;
  border-left: 4rpx solid #007AFF;
  justify-content: flex-start;
}

.regular-location:active {
  background: rgba(0, 0, 0, 0.05);
  transform: scale(0.98);
}

/* 普通位置的内容区域样式 */
.regular-location .location-content {
  flex: 1;
}

.delete-common {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  margin-left: 16rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
}

.delete-common:hover {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.12);
}

.delete-common:active {
  background: rgba(240, 240, 240, 0.9);
  transform: scale(0.92);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 分类组样式 */
.category-group {
  margin-bottom: 8rpx;
}

.category-group:last-child {
  margin-bottom: 0;
}

/* 无建议样式 */
.no-suggestions {
  padding: 48rpx 24rpx;
  text-align: center;
  background: #FAFAFA;
}

.no-suggestions-text {
  display: block;
  font-size: 28rpx;
  color: #8E8E93;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.no-suggestions-hint {
  display: block;
  font-size: 24rpx;
  color: #C7C7CC;
}

/* 提交区域 - 简化为正常文档流 */
.submit-section {
  padding: 24rpx 32rpx 32rpx 32rpx; /* 上24rpx 左右32rpx 下32rpx */
  margin: 0rpx 32rpx 32rpx 32rpx; /* 上间距减少到12rpx，比form-item间距小 */
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(229, 229, 229, 0.5);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  border-radius: 24rpx; /* 添加圆角保持与form-item一致 */
}

.button-group {
  display: flex;
  gap: 20rpx;
}

.draft-btn {
  flex: 1;
  height: 88rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  color: #1C1C1E;
  border: 2rpx solid rgba(0, 122, 255, 0.25);
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(15rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.15),
              0 4rpx 12rpx rgba(0, 122, 255, 0.12),
              0 2rpx 6rpx rgba(0, 122, 255, 0.08),
              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.draft-btn.disabled {
  background: #C7C7CC !important;
  color: #8E8E93 !important;
  border: 2rpx solid #E5E5E5 !important; /* 确保禁用时没有蓝色边框 */
  box-shadow: none !important;
}

.draft-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 122, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

.draft-btn:not(.disabled):active::before {
  left: 100%;
}

.draft-btn:not(.disabled):active {
  background: linear-gradient(135deg, rgba(230, 243, 255, 0.95) 0%, rgba(240, 248, 255, 0.9) 100%);
  transform: translateY(3rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.15),
              0 2rpx 6rpx rgba(0, 122, 255, 0.1),
              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.6);
}

.submit-btn {
  flex: 1;
  height: 88rpx;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 12rpx 32rpx rgba(0, 122, 255, 0.4),
              0 8rpx 16rpx rgba(0, 122, 255, 0.3),
              0 4rpx 8rpx rgba(0, 122, 255, 0.2),
              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  border: 1rpx solid rgba(90, 200, 250, 0.3);
}

.submit-btn[disabled] {
  background: #C7C7CC !important;
  color: #8E8E93 !important;
  box-shadow: none !important;
}

.submit-btn.loading {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%) !important;
}

.button-loading {
  color: white;
}

.submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.submit-btn:not(.disabled):active::before {
  left: 100%;
}

.submit-btn:not(.disabled):active {
  transform: translateY(4rpx);
  box-shadow: 0 6rpx 16rpx rgba(0, 122, 255, 0.3),
              0 3rpx 8rpx rgba(0, 122, 255, 0.2),
              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);
}
</style> 