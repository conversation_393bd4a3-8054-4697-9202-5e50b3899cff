{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/patrol_pkg/task/batch-add.vue?3d1d", "webpack:///D:/Xwzc/pages/patrol_pkg/task/batch-add.vue?7cc3", "webpack:///D:/Xwzc/pages/patrol_pkg/task/batch-add.vue?241d", "webpack:///D:/Xwzc/pages/patrol_pkg/task/batch-add.vue?7de0", "uni-app:///pages/patrol_pkg/task/batch-add.vue", "webpack:///D:/Xwzc/pages/patrol_pkg/task/batch-add.vue?2cd2", "webpack:///D:/Xwzc/pages/patrol_pkg/task/batch-add.vue?fa5f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "submitting", "formData", "namePrefix", "area", "shift_id", "route_id", "user_id", "selectedDates", "shiftOptions", "_id", "name", "rounds", "shiftIndex", "routeOptions", "routeIndex", "userOptions", "nickname", "userIndex", "<PERSON><PERSON><PERSON><PERSON>", "selectedShift", "selected<PERSON>ser", "roleNameMap", "STATUS", "NOT_STARTED", "IN_PROGRESS", "COMPLETED", "EXPIRED", "CANCELLED", "timeZoneIndicator", "createdTaskCount", "totalTaskCount", "currentDate", "startDate", "endDate", "weekdays", "currentYear", "currentMonth", "calendarDays", "minDate", "maxDate", "pointsRefreshed", "searchUserName", "filteredUsers", "showUserSelect", "onLoad", "uni", "title", "Promise", "console", "icon", "watch", "handler", "immediate", "methods", "loadShifts", "PatrolApi", "action", "status", "with_rounds", "with_detail", "res", "shifts", "shift", "across_day", "round", "time", "start_time", "end_time", "day_offset", "duration", "systemInfo", "platform", "isMobile", "loadRoutes", "getCurrentUserId", "loadUsers", "currentUserId", "userid", "params", "pageSize", "field", "result", "avatar", "role", "userList", "filter", "map", "roleNames", "<PERSON><PERSON><PERSON>", "wx_openid", "identities", "loadRoles", "getRoleText", "getSingleRoleText", "getDateRangeText", "onDateChange", "onShiftChange", "onRouteChange", "index", "mask", "with_points", "include_point_details", "pointsData", "pointsDetail", "onUserChange", "validateForm", "calculateTaskStatus", "startTime", "processRoundsData", "route", "routePoints", "point_id", "order", "location", "range", "qrcode_enabled", "qrcode_required", "qrcode_version", "roundHours", "shiftStartHours", "actualDayOffset", "checkDateTime", "checkTime", "point", "roundStatus", "check_time", "points", "stats", "total_points", "completed_points", "missed_points", "completion_rate", "abnormal_count", "calculateOverallStats", "last_checkin_time", "totalPoints", "completedPoints", "abnormalCount", "prepareTaskData", "tasks", "i", "date", "roundsData", "statsData", "enabledRounds", "task", "patrol_date", "route_name", "shift_name", "shift_type", "user_name", "role_id", "role_name", "rounds_detail", "enabled_rounds", "is_urgent", "remark", "overall_stats", "last_check_time", "create_time", "update_time", "auto_closed", "checkBatchDuplicateTasks", "duplicateTasks", "queryParams", "patrolDate", "filters", "userId", "response", "userName", "shiftName", "routeName", "duplicateInfo", "item", "content", "confirmText", "cancelText", "success", "resolve", "submitForm", "shouldC<PERSON><PERSON>ue", "createBatchTasks", "successTasks", "failedTasks", "allTasks", "taskData", "id", "error", "setTimeout", "showCancel", "navigateBack", "removeDate", "clearSelectedDates", "addSelectedDate", "formatDisplayDate", "onStartDateChange", "onEndDateChange", "addDateRange", "generateDatesFromRange", "dates", "generateCalendarDays", "fullDate", "isToday", "disabled", "prevMonth", "nextMonth", "isDateSelected", "isDisabledDate", "toggleDateSelection", "refreshRoutePointsData", "force_refresh", "processPointData", "original", "formatDate", "d", "replace", "calculateRoundTime", "baseDate", "hours", "minutes", "calculateEndTime", "filterUserOptions", "user", "selectUser", "hideUserSelect", "computed", "calendarTitle", "mounted"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5FA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,6nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;ACoRvnB;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;QAAAC;QAAAC;QAAAC;MAAA;MACAC;MACAC;QAAAJ;QAAAC;MAAA;MACAI;MACAC;QAAAN;QAAAO;MAAA;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACA;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACA;;cAEA;cAAA;cAEAC;gBAAAC;cAAA;;cAEA;cAAA;cAAA,OACAC,aACA,oBACA,oBACA,mBACA,kBACA;YAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAC;cACAH;gBACAC;gBACAG;cACA;YAAA;cAAA;cAEAJ;cAAA;YAAA;cAGA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAK;IACA;IACAnC;MACAoC;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;kBACA7C;kBACA8C;kBACAzD;oBACA0D;oBACAC;oBACAC;kBACA;gBACA;cAAA;gBARAC;gBAUA;kBACA;kBACAC;oBAAA,uCACAC;sBACApD;sBACAqD;sBACApD,sDACAmD;wBAAA;sBAAA;wBAAA;0BACAE;0BACAtD;0BACAuD;0BACAC;0BACAC;0BACAC;0BACAC;0BACAZ;wBACA;sBAAA,KACA;oBAAA;kBAAA,CACA,GAEA;kBACAa;kBACAC;kBACAC,kDAEA;kBACAX;oBACA;oBACA;;oBAEA;oBACA;oBACA;kBACA;;kBAEA;kBACA,uBACA;oBAAApD;oBAAAC;oBAAAC;kBAAA,2CACAkD,QACA;gBACA;kBACA;oBAAApD;oBAAAC;oBAAAC;kBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAqC;gBACA;kBAAAvC;kBAAAC;kBAAAC;gBAAA;gBACAkC;kBACAC;kBACAG;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAwB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAlB;kBACA7C;kBACA8C;kBACAzD;oBAAA0D;kBAAA;gBACA;cAAA;gBAJAG;gBAMA;kBACA;kBACA,uBACA;oBAAAnD;oBAAAC;kBAAA,2CACAkD,eACA;gBACA;kBACA;oBAAAnD;oBAAAC;kBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;kBAAAD;kBAAAC;gBAAA;gBACAmC;kBACAC;kBACAG;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAyB;MACA;QACA;QACA;QACA;UACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA,OAGArB;kBACA7C;kBACA8C;kBACAzD;oBACA8E;oBACAC;sBACAC;sBACAC;oBACA;kBACA;gBACA;cAAA;gBAVAC;gBAYA;kBACA;kBACA,sBACA;oBACAxE;oBACAO;oBACAkE;oBACAC;kBACA,EACA;;kBAEA;kBACAC,4BACAC;oBACA;oBACA;oBACA;sBACA;sBACA;oBACA;sBACA;sBACA;oBACA;kBACA,GACAA;oBACA;oBACA;oBACA;kBACA,GACAC;oBACA;oBACA;oBACA;oBACA;oBAEA;sBACA;wBACAC;0BACA;wBACA;sBACA;oBACA;sBACAvC;sBACAuC;oBACA;oBAEA;sBACA9E;sBACAO;sBACAkE;sBACAC;sBACAK;sBACA/B;sBACAgC;sBACAC;oBACA;kBACA,IAEA;kBACA;;kBAEA;kBACA;kBACA;kBACA;kBAEA;oBACA7C;sBACAC;sBACAG;oBACA;kBACA;gBACA;kBACA;oBACAxC;oBACAO;oBACAkE;oBACAC;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAnC;gBACA;kBAAAvC;kBAAAO;gBAAA;gBACA6B;kBACAC;kBACAG;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA0C;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;kBACA;;kBAEA;kBACA;kBACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;gBACA;kBACA3C;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA4C;MAAA;MACA;;MAEA;MACA;QACA;QACA;QACA;UAAA;QAAA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACA;;gBAEA;gBACAhF,wDAEA;gBAAA,MACAgF;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBACA;;gBAEA;gBACArD;kBACAC;kBACAqD;gBACA;gBAAA;gBAAA;gBAAA,OAGA5C;kBACA7C;kBACA8C;kBACAzD;oBACA+E;sBACAzE;oBACA;oBACA+F;oBACAzC;oBACA0C;kBACA;gBACA;cAAA;gBAXAzC;gBAaAf;gBAAA,MAEAe;kBAAA;kBAAA;gBAAA;gBACA0C;gBACA;kBACAA;gBACA;kBACAA;gBACA;gBAEA,qEACA,uBACA1C;kBACA2C;gBAAA,EACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAvD;gBACAH;gBACAA;kBACAC;kBACAG;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAuD;MACA;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA5D;UACAC;UACAG;QACA;QACA;MACA;;MAEA;MACA;QACAJ;UACAC;UACAG;QACA;QACA;MACA;;MAEA;MACA;QACAJ;UACAC;UACAG;QACA;QACA;MACA;;MAEA;MACA;QACAJ;UACAC;UACAG;QACA;QACA;MACA;;MAEA;MACA;QACAJ;UACAC;UACAG;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAyD;MACA;MACA;QACA;MACA;;MAEA;MACA;MACA;QACAC;MACA;QACA3D;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACA4D;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACA5D;gBAAA,kCACA;cAAA;gBAGAc;gBACA+C,8BAEA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA7D;gBAAA,kCACA;cAAA;gBAGA;gBACA8D;kBAAA;oBACAC;oBACArG;oBACAsG;oBACAvD;oBAAA;oBACAwD;oBACAC;oBACA;oBACAC;oBACAC;oBACAC;kBACA;gBAAA,IAEA;gBAAA,kCACAvD;kBACA;kBACA;kBACA;;kBAEA;kBACA;kBACA;oBACA;oBACA;oBACA;sBAAA;sBAAAwD;oBACA;sBAAA;sBAAAC;oBAEA;sBACAC;sBACA;sBACAC;sBACAC;oBACA;kBACA;;kBAEA;kBACA;;kBAEA;kBACA;oBAAA,uCACAC;sBACAlE;oBAAA;kBAAA,CACA;;kBAEA;kBACA;kBACA;;kBAEA;oBACA;oBACAmE;oBACA5E;kBACA;oBACA;oBACA4E;oBACA5E;kBACA;oBACA;oBACA4E;oBACA5E;kBACA;kBAEA;oBACAgB;oBACAtD;oBACAuD;oBACA4D;oBACA3D;oBACAC;oBACAC;oBACAC;oBACAZ;oBAAA;oBACAM;oBACA+D;oBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;QACA;UACAL;UACAC;UACAC;UACAC;UACAC;UACAE;QACA;MACA;;MAEA;MACA;MACA;MACA;MAEA3H;QACA;UACA4H;UACAC;UACAC;QACA;MACA;;MAEA;MACA;MACA;MAEA;QACAT;QACAC;QACAC;QACAC;QACAC;QACAE;MACA;IACA;IAEA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACA7F;kBACAC;kBACAG;gBACA;gBAAA,kCACA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBACAJ;kBACAC;kBACAG;gBACA;gBAAA,kCACA;cAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBACAJ;kBACAC;kBACAG;gBACA;gBAAA,kCACA;cAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBACAJ;kBACAC;kBACAG;gBACA;gBAAA,kCACA;cAAA;gBAGA;gBACAJ;kBACAC;gBACA;gBAAA;gBAGA;gBACA6F,YAEA;gBACAC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACAC,gCAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACA9F;gBAAA;cAAA;gBAIA;gBACA+F,sDAEA;gBACAC;kBAAA;gBAAA,WAEA;gBACAC;kBACAC;kBAAA;kBACA7I;kBACA8I;kBACAhJ;kBAAA;kBACAC;kBACAgJ;kBACAC;kBACAtF;kBACAzD;kBACAgJ;kBACAC;kBACAC;kBACAC;kBACAC;kBAAA;kBACAC;kBACAlG;kBAAA;kBACAmG;kBACA7B;kBACA8B;kBAAA;kBACAC;kBAAA;kBACAC;kBACAC;kBACAC;gBACA;gBAEAtB;cAAA;gBA5CAC;gBAAA;gBAAA;cAAA;gBAAA,kCA+CAD;cAAA;gBAAA;gBAAA;gBAEA3F;gBAAA,kCACA;cAAA;gBAAA;gBAEAH;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAqH;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEArH;kBACAC;kBACAqD;gBACA;gBAEAgE,qBAEA;gBACAvB;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACAC,iCAEA;gBACAuB;kBACAC;kBACAC;oBACAjK;kBACA;kBACAkK;kBACAnK;gBACA,GAEA;gBAAA;gBAAA,OACAmD;cAAA;gBAAAiH;gBAEA;kBACA;kBACAL;oBACAtB;oBACAI;kBACA;gBACA;cAAA;gBAtBAL;gBAAA;gBAAA;cAAA;gBAyBA/F;gBAAA,MAEAsH;kBAAA;kBAAA;gBAAA;gBACA;gBACAM;gBACAC;gBACAC;gBAEAC;kBAAA,wBACAC;gBAAA,EACA;gBAEA;kBACAD;gBACA;gBAAA,kCAEA;kBACA/H;oBACAC;oBACAgI;oBACAC;oBACAC;oBACAC;sBACAC;oBACA;kBACA;gBACA;cAAA;gBAAA,kCAIA;cAAA;gBAAA;gBAAA;gBAEArI;gBACAG;gBACA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAmI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAKA;cAAA;gBAAAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBACAvI;kBACAC;kBACAgI;kBACAG;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACArH;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA,OACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAEA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAyH;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAGA;gBACA;gBACA;;gBAEA;gBACAxI;kBACAC;kBACAqD;gBACA;;gBAEA;gBACAmF;gBACAC,kBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAIA5C;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACA6C;gBACA5C;gBAAA;gBAGA;gBACAhG;kBACAC;kBACAqD;gBACA;;gBAEA;gBACAsF;;gBAEA;gBACA;kBACAA;gBACA;gBAEA;kBACAA,yDACAA;oBACAnD;kBAAA,EACA;gBACA;;gBAEA;gBACAmD;;gBAEA;gBAAA;gBAAA,OACAlI;cAAA;gBAAAiH;gBAEA;gBACA;kBACAc;oBACAzC;oBACAnI;oBACAgL;kBACA;kBAEA;gBACA;kBACAH;oBACA1C;oBACAnI;oBACAiL;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAJ;kBACA1C;kBACAnI;kBACAiL;gBACA;cAAA;gBArDA/C;gBAAA;gBAAA;cAAA;gBAyDA;gBACA/F;;gBAEA;gBACA;kBACAA;oBACAC;oBACAG;oBACAoB;kBACA;;kBAEA;kBACAxB;;kBAEA;kBACA;oBACA+I;sBACA/I;wBACAC;wBACAgI;wBACAe;sBACA;oBACA;kBACA;oBACA;oBACAD;sBACA/I;oBACA;kBACA;gBACA;kBACAA;oBACAC;oBACAgI;oBACAe;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAhJ;gBACAA;kBACAC;kBACAG;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA6I;MACAjJ;IACA;IAEA;IACAkJ;MACA;QAAA;MAAA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;;MAEA;MACAnJ;QACAC;QACAG;QACAoB;MACA;IACA;IACA4H;MACA;QACApJ;UACAC;UACAG;QACA;QACA;MACA;;MAEA;MACA;MACA;QACAJ;UACAC;UACAG;QACA;QACA;MACA;;MAEA;MACA;;MAEA;MACAJ;QACAC;QACAG;MACA;IACA;IACAiJ;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACAxJ;UACAC;UACAG;QACA;QACA;MACA;MAEA;MACA;;MAEA;MACA;QACAJ;UACAC;UACAG;QACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QACAJ;UACAC;UACAgI;UACAG;YACA;cACA;YACA;UACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAqB;MAAA;MACA;MACA;MAEA;QACAC;QACAxK;MACA;;MAEA;MACA;QAAA;MAAA;MAEA;QACAc;UACAC;UACAG;QACA;QACA;MACA;MAEA;MAEAJ;QACAC;QACAG;MACA;;MAEA;MACA;MACA;IACA;IACA;IACAuJ;MACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QAEA;UACA3D;UACA4D;UACArK;UACAsK;UACAC;QACA;MACA;;MAEA;MACA;QACA;QACA;QAEA;UACA9D;UACA4D;UACArK;UACAsK;UACAC;QACA;MACA;;MAEA;MACA;MACA;QACA;QACA;QACA;QACA;QAEA;UACA9D;UACA4D;UACArK;UACAsK;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;MAEA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACApK;kBACAC;kBACAG;gBACA;gBAAA;cAAA;gBAAA;gBAKAJ;kBACAC;kBACAqD;gBACA;gBAAA;gBAAA,OAEA5C;kBACA7C;kBACA8C;kBACAzD;oBACA+E;sBACAzE;sBACA+F;sBACAzC;sBACA0C;sBACA6G;oBACA;kBACA;gBACA;cAAA;gBAZAtJ;gBAcAf;gBAAA,MAEAe;kBAAA;kBAAA;gBAAA;gBACA0C;gBACA;kBACAA;gBACA;kBACAA;gBACA;gBAEA,wDACA;kBACAC;gBAAA,EACA;gBAEA;gBAEA1D;kBACAC;kBACAG;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAJ;gBACAA;kBACAC;kBACAG;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAkK;MACA;QACA;MACA;MAEA;MAEA;QACA;UAAA;YACA1M;YACAC;YACAwG;YACAF;YACAC;YACAE;YACAC;YACAC;YACA+F;UACA;QAAA;MACA;QACA;UAAA;YACA3M;YACAC;YACAwG;YACAF;YACAC;YACAE;YACAC;YACAC;YACA+F;UACA;QAAA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;QACAC;MACA;QACAA;MACA;QACA;QACAA;QACAtK;MACA;;MAEA;MACA;QACAA;QACA;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA,cACAuK,sBACAA,qBACAA,mBACAA,qBACAA,uBACAA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;QACA;QACA;UACAC;QACA;UACA;UACAA;QACA;MACA;QACAA;MACA;QACA;QACAA;QACAzK;MACA;;MAEA;MACAyK;;MAEA;MACA;QACAA;MACA;;MAEA;MACA;QACA;YAAA;UAAA;UAAA;UAAAC;UAAAC;;QAEA;QACA;UACAF;QACA;MACA;MAEA;IACA;IAEA;IACAG;MAAA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;MACA;QACA;UAAA,OACAC,sEACAA;QAAA;QAAA,CACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;QACA;QACA;UAAA;QAAA;QACA;UACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjzDA;AAAA;AAAA;AAAA;AAA8oC,CAAgB,onCAAG,EAAC,C;;;;;;;;;;;ACAlqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/patrol_pkg/task/batch-add.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/patrol_pkg/task/batch-add.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./batch-add.vue?vue&type=template&id=8393c05e&\"\nvar renderjs\nimport script from \"./batch-add.vue?vue&type=script&lang=js&\"\nexport * from \"./batch-add.vue?vue&type=script&lang=js&\"\nimport style0 from \"./batch-add.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/patrol_pkg/task/batch-add.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./batch-add.vue?vue&type=template&id=8393c05e&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.selectedDates.length\n  var l0 = _vm.__map(_vm.calendarDays, function (day, index) {\n    var $orig = _vm.__get_orig(day)\n    var m0 = _vm.isDateSelected(day.fullDate)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var g1 =\n    _vm.selectedRoute &&\n    _vm.selectedRoute.pointsDetail &&\n    _vm.selectedRoute.pointsDetail.length > 0\n  var l1 = _vm.showUserSelect\n    ? _vm.__map(_vm.filteredUsers, function (user, idx) {\n        var $orig = _vm.__get_orig(user)\n        var m1 = user.role && _vm.getRoleText(user.role)\n        var m2 = m1 ? _vm.getRoleText(user.role) : null\n        return {\n          $orig: $orig,\n          m1: m1,\n          m2: m2,\n        }\n      })\n    : null\n  var g2 = _vm.selectedDates.length || 0\n  var g3 = _vm.selectedDates.length || 0\n  var g4 = _vm.selectedDates.length\n  var g5 = _vm.submitting || _vm.selectedDates.length === 0\n  var g6 = _vm.selectedDates.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      _vm.showUserSelect = true\n    }\n    _vm.e1 = function ($event) {\n      $event.stopPropagation()\n      _vm.showUserSelect = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.showUserSelect = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        l1: l1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./batch-add.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./batch-add.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"batch-add-task-container\" @click=\"hideUserSelect\">\n\t\t\n\t\t<view class=\"form-container\">\n\t\t\t<form @submit=\"submitForm\">\n\t\t\t\t<!-- 基本信息 -->\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<text class=\"section-title\">基本信息</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label required\">任务名称</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\ttype=\"text\" \n\t\t\t\t\t\t\tv-model=\"formData.namePrefix\"\n\t\t\t\t\t\t\tplaceholder=\"请输入任务名称\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label required\">选择日期</text>\n\t\t\t\t\t\t<view class=\"date-selection\">\n\t\t\t\t\t\t\t<view class=\"date-selection-header\">\n\t\t\t\t\t\t\t\t<view class=\"date-selection-info\">\n\t\t\t\t\t\t\t\t\t<view class=\"selected-days-count\">已选择 {{ selectedDates.length }} 天</view>\n\t\t\t\t\t\t\t\t\t<view class=\"clear-dates-btn\" @click=\"clearSelectedDates\">清空所选</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"custom-calendar\">\n\t\t\t\t\t\t\t\t<!-- 日历头部 -->\n\t\t\t\t\t\t\t\t<view class=\"calendar-header\">\n\t\t\t\t\t\t\t\t\t<view class=\"month-picker\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"arrow-btn\" @click=\"prevMonth\">\n\t\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"left\" size=\"18\" color=\"#666\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"current-month\">{{calendarTitle}}</text>\n\t\t\t\t\t\t\t\t\t\t<view class=\"arrow-btn\" @click=\"nextMonth\">\n\t\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"18\" color=\"#666\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<!-- 星期标题 -->\n\t\t\t\t\t\t\t\t<view class=\"weekday-header\">\n\t\t\t\t\t\t\t\t\t<text class=\"weekday-item\" v-for=\"(day, index) in weekdays\" :key=\"index\">{{day}}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<!-- 日期网格 -->\n\t\t\t\t\t\t\t\t<view class=\"days-grid\">\n\t\t\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\t\t\tclass=\"day-cell\" \n\t\t\t\t\t\t\t\t\t\tv-for=\"(day, index) in calendarDays\" \n\t\t\t\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t\t\t\t:class=\"{\n\t\t\t\t\t\t\t\t\t\t\t'other-month': !day.currentMonth,\n\t\t\t\t\t\t\t\t\t\t\t'today': day.isToday,\n\t\t\t\t\t\t\t\t\t\t\t'selected': isDateSelected(day.fullDate),\n\t\t\t\t\t\t\t\t\t\t\t'disabled': day.disabled\n\t\t\t\t\t\t\t\t\t\t}\"\n\t\t\t\t\t\t\t\t\t\t@click=\"toggleDateSelection(day)\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<text class=\"day-number\">{{day.date}}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 班次与轮次 -->\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<text class=\"section-title\">班次与轮次</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label required\">班次</text>\n\t\t\t\t\t\t<picker \n\t\t\t\t\t\t\t:range=\"shiftOptions\" \n\t\t\t\t\t\t\trange-key=\"name\" \n\t\t\t\t\t\t\t:value=\"shiftIndex\"\n\t\t\t\t\t\t\t@change=\"onShiftChange\"\n\t\t\t\t\t\t\tclass=\"form-picker\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<view class=\"picker-value\">\n\t\t\t\t\t\t\t\t<view class=\"shift-info\">\n\t\t\t\t\t\t\t\t\t<text>{{ shiftOptions[shiftIndex].name || '请选择班次' }}</text>\n\t\t\t\t\t\t\t\t\t<text v-if=\"selectedShift && selectedShift.across_day\" class=\"cross-day-tag\">跨天班次</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"18\" color=\"#999999\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\" v-if=\"selectedShift && selectedShift.rounds\">\n\t\t\t\t\t\t<text class=\"form-label\">轮次信息</text>\n\t\t\t\t\t\t<view class=\"shift-time-info\" v-if=\"selectedShift\">\n\t\t\t\t\t\t\t<view class=\"time-range\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"14\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t<text>{{ selectedShift.start_time }} - {{ selectedShift.end_time }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"round-tags\">\n\t\t\t\t\t\t\t<view class=\"round-tag\" v-for=\"(round, index) in selectedShift.rounds\" :key=\"index\">\n\t\t\t\t\t\t\t\t轮次{{ round.round }}: {{ round.start_time || round.time }}\n\t\t\t\t\t\t\t\t<text v-if=\"round.day_offset > 0\" class=\"next-day-badge\">次日</text>\n\t\t\t\t\t\t\t\t<text v-if=\"round.duration\">, 有效时长{{ round.duration }}分钟</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 巡检路线 -->\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<text class=\"section-title\">巡检路线</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label required\">路线</text>\n\t\t\t\t\t\t<picker \n\t\t\t\t\t\t\t:range=\"routeOptions\" \n\t\t\t\t\t\t\trange-key=\"name\" \n\t\t\t\t\t\t\t:value=\"routeIndex\"\n\t\t\t\t\t\t\t@change=\"onRouteChange\"\n\t\t\t\t\t\t\tclass=\"form-picker\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<view class=\"picker-value\">\n\t\t\t\t\t\t\t\t<text>{{ routeOptions[routeIndex].name || '请选择巡检路线' }}</text>\n\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"18\" color=\"#999999\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 添加路线点位列表显示 -->\n\t\t\t\t\t<view class=\"form-item\" v-if=\"selectedRoute && selectedRoute.pointsDetail && selectedRoute.pointsDetail.length > 0\">\n\t\t\t\t\t\t<view class=\"points-header\">\n\t\t\t\t\t\t\t<text class=\"form-label\">点位列表</text>\n\t\t\t\t\t\t\t<view class=\"refresh-points-btn\" @click=\"refreshRoutePointsData\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"refresh\" size=\"16\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t<text>刷新点位数据</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"points-info\" v-if=\"pointsRefreshed\">\n\t\t\t\t\t\t\t<uni-icons type=\"info\" size=\"14\" color=\"#52C41A\"></uni-icons>\n\t\t\t\t\t\t\t<text class=\"info-text success\">点位数据已更新至最新</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"points-list\">\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tclass=\"point-item\" \n\t\t\t\t\t\t\t\tv-for=\"(point, index) in selectedRoute.pointsDetail\" \n\t\t\t\t\t\t\t\t:key=\"point._id\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<text class=\"point-index\">{{ index + 1 }}</text>\n\t\t\t\t\t\t\t\t<text class=\"point-name\">{{ point.name }}</text>\n\t\t\t\t\t\t\t\t<text class=\"point-range\">{{ point.range || 50 }}米</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 任务执行人 -->\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<text class=\"section-title\">任务执行人</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label required\">人员选择</text>\n\t\t\t\t\t\t<view class=\"custom-select\" @click.stop=\"showUserSelect = true\">\n\t\t\t\t\t\t\t<view class=\"picker-value\">\n\t\t\t\t\t\t\t\t<text>{{ selectedUser ? selectedUser.nickname : '请选择执行人员' }}</text>\n\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"18\" color=\"#999999\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 自定义下拉选择器 -->\n\t\t\t\t\t\t<view class=\"user-select-modal\" v-if=\"showUserSelect\" @click.stop=\"showUserSelect = false\">\n\t\t\t\t\t\t\t<view class=\"user-select-popup\" @click.stop>\n\t\t\t\t\t\t\t\t<view class=\"user-select-container\">\n\t\t\t\t\t\t\t\t\t<view class=\"user-select-header\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"user-select-title\">请选择执行人员</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"user-search-header\">\n\t\t\t\t\t\t\t\t\t\t<input \n\t\t\t\t\t\t\t\t\t\t\tclass=\"user-search-input\" \n\t\t\t\t\t\t\t\t\t\t\ttype=\"text\" \n\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"输入姓名进行搜索\" \n\t\t\t\t\t\t\t\t\t\t\tv-model=\"searchUserName\"\n\t\t\t\t\t\t\t\t\t\t\t@input=\"filterUserOptions\"\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"search\" size=\"18\" color=\"#999999\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<scroll-view scroll-y class=\"user-select-list\">\n\t\t\t\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\t\t\t\tclass=\"user-select-item\" \n\t\t\t\t\t\t\t\t\t\t\tv-for=\"(user, idx) in filteredUsers\" \n\t\t\t\t\t\t\t\t\t\t\t:key=\"user._id\"\n\t\t\t\t\t\t\t\t\t\t\t@tap=\"selectUser(idx)\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"user-item-content\">\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"user.avatar || '/static/user/default-avatar.png'\" class=\"user-avatar-small\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"user-item-details\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"user-item-name\">{{ user.nickname }}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"user-item-role\" v-if=\"user.role && getRoleText(user.role)\">{{ getRoleText(user.role) }}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"select-radio\" v-if=\"selectedUser && selectedUser._id === user._id\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"checkbox-filled\" size=\"18\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t\t\t\t\t<view class=\"user-select-footer\">\n\t\t\t\t\t\t\t\t\t\t<button class=\"user-select-close\" @tap=\"showUserSelect = false\">关闭</button>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\" v-if=\"selectedUser\">\n\t\t\t\t\t\t<text class=\"form-label\">所选人员</text>\n\t\t\t\t\t\t<view class=\"user-info\">\n\t\t\t\t\t\t\t<image \n\t\t\t\t\t\t\t\t:src=\"selectedUser.avatar || '/static/user/default-avatar.png'\" \n\t\t\t\t\t\t\t\tmode=\"aspectFill\" \n\t\t\t\t\t\t\t\tclass=\"user-avatar\"\n\t\t\t\t\t\t\t></image>\n\t\t\t\t\t\t\t<view class=\"user-details\">\n\t\t\t\t\t\t\t\t<text class=\"user-name\">{{ selectedUser.nickname }}</text>\n\t\t\t\t\t\t\t\t<text class=\"user-role\">{{ selectedUser.roleName || '普通用户' }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 任务预览 -->\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<text class=\"section-title\">批量任务预览</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"task-preview\">\n\t\t\t\t\t\t<view class=\"preview-info\">\n\t\t\t\t\t\t\t<text class=\"preview-label\">总任务数：</text>\n\t\t\t\t\t\t\t<text class=\"preview-value\">{{ selectedDates.length || 0 }}个</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"preview-info\">\n\t\t\t\t\t\t\t<text class=\"preview-label\">批量创建说明：</text>\n\t\t\t\t\t\t\t<text class=\"preview-value\">将为{{ selectedUser ? selectedUser.nickname : '所选人员' }}创建{{ selectedDates.length || 0 }}个任务，每个任务执行日期不同</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 提交按钮 -->\n\t\t\t\t<view class=\"submit-section\">\n\t\t\t\t\t<button \n\t\t\t\t\t\tclass=\"cancel-btn\" \n\t\t\t\t\t\t@click=\"navigateBack\"\n\t\t\t\t\t>取消操作</button>\n\t\t\t\t\t<button \n\t\t\t\t\t\tclass=\"primary-btn\" \n\t\t\t\t\t\tform-type=\"submit\"\n\t\t\t\t\t\t:loading=\"submitting\"\n\t\t\t\t\t\t:disabled=\"submitting || selectedDates.length === 0\"\n\t\t\t\t\t\t:style=\"selectedDates.length === 0 ? 'background-color: #CCCCCC !important;' : 'background-color: #1677FF !important;'\"\n\t\t\t\t\t>批量创建{{ selectedDates.length }}个任务</button>\n\t\t\t\t</view>\n\t\t\t</form>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport PatrolApi from '@/utils/patrol-api.js';\nimport { calculateRoundTime, calculateEndTime, formatDate, detectTimeZone, preprocessDates, getDaysDiff } from '@/utils/date.js';\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tsubmitting: false,\n\t\t\tformData: {\n\t\t\t\tnamePrefix: '',\n\t\t\t\tarea: '',\n\t\t\t\tshift_id: '',\n\t\t\t\troute_id: '',\n\t\t\t\tuser_id: ''\n\t\t\t},\n\t\t\tselectedDates: [],\n\t\t\tshiftOptions: [{ _id: '', name: '请选择班次', rounds: [] }],\n\t\t\tshiftIndex: 0,\n\t\t\trouteOptions: [{ _id: '', name: '请选择路线' }],\n\t\t\trouteIndex: 0,\n\t\t\tuserOptions: [{ _id: '', nickname: '请选择人员' }],\n\t\t\tuserIndex: 0,\n\t\t\tselectedRoute: null,\n\t\t\tselectedShift: null,\n\t\t\tselectedUser: null,\n\t\t\t// 角色映射表\n\t\t\troleNameMap: {},\n\t\t\t// 状态常量\n\t\t\tSTATUS: {\n\t\t\t\tNOT_STARTED: 0, // 未开始\n\t\t\t\tIN_PROGRESS: 1, // 进行中\n\t\t\t\tCOMPLETED: 2,   // 已完成\n\t\t\t\tEXPIRED: 3,     // 已超时\n\t\t\t\tCANCELLED: 4    // 已取消\n\t\t\t},\n\t\t\ttimeZoneIndicator: null, // 用于显示当前时区\n\t\t\tcreatedTaskCount: 0, // 已创建任务数\n\t\t\ttotalTaskCount: 0,   // 总任务数\n\t\t\tcurrentDate: formatDate(new Date(), 'YYYY-MM-DD'),\n\t\t\tstartDate: '',\n\t\t\tendDate: '',\n\t\t\t// 日历相关数据\n\t\t\tweekdays: ['日', '一', '二', '三', '四', '五', '六'],\n\t\t\tcurrentYear: new Date().getFullYear(),\n\t\t\tcurrentMonth: new Date().getMonth(),\n\t\t\tcalendarDays: [],\n\t\t\tminDate: '2020-01-01',\n\t\t\tmaxDate: '2030-12-31',\n\t\t\tpointsRefreshed: false,\n\t\t\t// 用户选择相关\n\t\t\tsearchUserName: '',\n\t\t\tfilteredUsers: [],\n\t\t\tshowUserSelect: false,\n\t\t};\n\t},\n\tasync onLoad() {\n\t\t// 检测并记录当前时区\n\t\tthis.timeZoneIndicator = detectTimeZone();\n\t\t\n\t\t// 🔥 优化：并行加载数据提升用户体验\n\t\ttry {\n\t\t\tuni.showLoading({ title: '加载中...' });\n\t\t\t\n\t\t\t// 并行加载减少用户等待时间\n\t\t\tawait Promise.all([\n\t\t\t\tthis.loadShifts(),\n\t\t\t\tthis.loadRoutes(),\n\t\t\t\tthis.loadRoles(),\n\t\t\t\tthis.loadUsers()\n\t\t\t]);\n\t\t} catch (e) {\n\t\t\tconsole.error('初始化数据加载失败:', e);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '数据加载失败',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t} finally {\n\t\t\tuni.hideLoading();\n\t\t}\n\t\t\n\t\t// 生成初始日历数据\n\t\tthis.generateCalendarDays();\n\t},\n\twatch: {\n\t\t// 监听用户列表变化，初始化过滤后的列表\n\t\tuserOptions: {\n\t\t\thandler(val) {\n\t\t\t\tthis.filteredUsers = [...val];\n\t\t\t},\n\t\t\timmediate: true\n\t\t}\n\t},\n\tmethods: {\n\t\t// 加载班次数据\n\t\tasync loadShifts() {\n\t\t\ttry {\n\t\t\t\tconst res = await PatrolApi.call({\n\t\t\t\t\tname: 'patrol-shift',\n\t\t\t\t\taction: 'getShiftList',\n\t\t\t\t\tdata: { \n\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t\twith_rounds: true,\n\t\t\t\t\t\twith_detail: true\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data && res.data.list) {\n\t\t\t\t\t// 只加载启用的班次\n\t\t\t\t\tlet shifts = res.data.list.map(shift => ({\n\t\t\t\t\t\t...shift,\n\t\t\t\t\t\tname: shift.name,\n\t\t\t\t\t\tacross_day: shift.across_day || false,\n\t\t\t\t\t\trounds: shift.rounds && Array.isArray(shift.rounds) \n\t\t\t\t\t\t\t? shift.rounds.filter(r => r.status !== 0).map(round => ({\n\t\t\t\t\t\t\t\tround: round.round,\n\t\t\t\t\t\t\t\tname: round.name || `轮次${round.round}`,\n\t\t\t\t\t\t\t\ttime: round.time,\n\t\t\t\t\t\t\t\tstart_time: round.start_time || round.time,\n\t\t\t\t\t\t\t\tend_time: round.end_time || round.time,\n\t\t\t\t\t\t\t\tday_offset: round.day_offset || 0,\n\t\t\t\t\t\t\t\tduration: round.duration || 60,\n\t\t\t\t\t\t\t\tstatus: round.status\n\t\t\t\t\t\t\t}))\n\t\t\t\t\t\t\t: []\n\t\t\t\t\t}));\n\n\t\t\t\t\t// 按照班次名称字母顺序排序\n\t\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\t\t\t\tconst platform = systemInfo.platform;\n\t\t\t\t\tconst isMobile = ['android', 'ios'].includes(platform);\n\n\t\t\t\t\t// 对班次进行排序\n\t\t\t\t\tshifts.sort((a, b) => {\n\t\t\t\t\t\tconst nameA = String(a.name || '').trim();\n\t\t\t\t\t\tconst nameB = String(b.name || '').trim();\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 使用 localeCompare 进行中文排序，统一使用 A-Z 排序\n\t\t\t\t\t\tconst compareResult = nameA.localeCompare(nameB, 'zh-CN');\n\t\t\t\t\t\treturn isMobile ? -compareResult : compareResult;\n\t\t\t\t\t});\n\n\t\t\t\t\t// 添加默认选项到排序后的数组前面\n\t\t\t\t\tthis.shiftOptions = [\n\t\t\t\t\t\t{ _id: '', name: '请选择班次', rounds: [] },\n\t\t\t\t\t\t...shifts\n\t\t\t\t\t];\n\t\t\t\t} else {\n\t\t\t\t\tthis.shiftOptions = [{ _id: '', name: '请选择班次', rounds: [] }];\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('加载班次失败:', e);\n\t\t\t\tthis.shiftOptions = [{ _id: '', name: '请选择班次', rounds: [] }];\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载班次失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 加载路线数据\n\t\tasync loadRoutes() {\n\t\t\ttry {\n\t\t\t\tconst res = await PatrolApi.call({\n\t\t\t\t\tname: 'patrol-route',\n\t\t\t\t\taction: 'getRouteList',\n\t\t\t\t\tdata: { status: 1 }\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data && res.data.list) {\n\t\t\t\t\t// 只加载启用的路线\n\t\t\t\t\tthis.routeOptions = [\n\t\t\t\t\t\t{ _id: '', name: '请选择路线' },\n\t\t\t\t\t\t...res.data.list\n\t\t\t\t\t];\n\t\t\t\t} else {\n\t\t\t\t\tthis.routeOptions = [{ _id: '', name: '请选择路线' }];\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tthis.routeOptions = [{ _id: '', name: '请选择路线' }];\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载路线失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 获取当前用户ID\n\t\tgetCurrentUserId() {\n\t\t\ttry {\n\t\t\t\t// 尝试从本地存储获取用户信息\n\t\t\t\tconst userInfo = uni.getStorageSync('uni-id-pages-userInfo');\n\t\t\t\tif (userInfo) {\n\t\t\t\t\treturn userInfo._id || '';\n\t\t\t\t}\n\t\t\t\treturn '';\n\t\t\t} catch (e) {\n\t\t\t\treturn '';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 加载用户列表\n\t\tasync loadUsers() {\n\t\t\ttry {\n\t\t\t\tconst currentUserId = this.getCurrentUserId();\n\t\t\t\tif (!currentUserId) {\n\t\t\t\t\tthrow new Error('未能获取当前用户ID');\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst result = await PatrolApi.call({\n\t\t\t\t\tname: 'patrol-user',\n\t\t\t\t\taction: 'getUsers',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\tuserid: currentUserId,\n\t\t\t\t\t\tparams: {\n\t\t\t\t\t\t\tpageSize: 100,\n\t\t\t\t\t\t\tfield: 'nickname,avatar,role,username,wx_openid,identities' // 请求所需字段\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (result.code === 0 && result.data) {\n\t\t\t\t\t// 添加一个空选项作为第一个选项\n\t\t\t\t\tthis.userOptions = [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t_id: '',\n\t\t\t\t\t\t\tnickname: '请选择执行人',\n\t\t\t\t\t\t\tavatar: '/static/user/default-avatar.png',\n\t\t\t\t\t\t\trole: []\n\t\t\t\t\t\t}\n\t\t\t\t\t];\n\t\t\t\t\t\n\t\t\t\t\t// 处理用户数据，过滤掉admin角色的用户\n\t\t\t\t\tconst userList = result.data.list\n\t\t\t\t\t\t.filter(user => {\n\t\t\t\t\t\t\t// 过滤掉admin角色用户\n\t\t\t\t\t\t\tif (!user.role) return true; // 没有角色的保留\n\t\t\t\t\t\t\tif (Array.isArray(user.role)) {\n\t\t\t\t\t\t\t\t// 如果是数组，检查是否包含admin\n\t\t\t\t\t\t\t\treturn !user.role.includes('admin');\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// 如果是字符串，检查是否为admin\n\t\t\t\t\t\t\t\treturn user.role !== 'admin';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.filter(user => {\n\t\t\t\t\t\t\t// 过滤掉以\"匿名\"开头的用户\n\t\t\t\t\t\t\tconst nickname = user.real_name || user.nickname || user.username || '';\n\t\t\t\t\t\t\treturn !nickname.startsWith('匿名');\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.map(user => {\n\t\t\t\t\t\t\t// 确保role是数组\n\t\t\t\t\t\t\tconst userRoles = Array.isArray(user.role) ? user.role : [];\n\t\t\t\t\t\t\t// 避免在没有roleNameMap的情况下调用map\n\t\t\t\t\t\t\tlet roleNames = '普通用户';\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\tif (userRoles.length > 0 && this.roleNameMap) {\n\t\t\t\t\t\t\t\t\troleNames = userRoles.map(roleId => {\n\t\t\t\t\t\t\t\t\t\treturn this.roleNameMap[roleId] || '未知角色';\n\t\t\t\t\t\t\t\t\t}).join('、');\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\t\tconsole.error('处理用户角色时出错:', e);\n\t\t\t\t\t\t\t\troleNames = '角色解析错误';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t_id: user._id,\n\t\t\t\t\t\t\t\tnickname: user.real_name || user.nickname || user.username || '未命名用户',\n\t\t\t\t\t\t\t\tavatar: user.avatar || '/static/user/default-avatar.png',\n\t\t\t\t\t\t\t\trole: userRoles,\n\t\t\t\t\t\t\t\troleName: roleNames || '普通用户',\n\t\t\t\t\t\t\t\tstatus: user.status,\n\t\t\t\t\t\t\t\twx_openid: user.wx_openid || null,\n\t\t\t\t\t\t\t\tidentities: user.identities || []\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 合并空选项和用户列表\n\t\t\t\t\tthis.userOptions = [...this.userOptions, ...userList];\n\t\t\t\t\t\n\t\t\t\t\t// 初始选择第一项（请选择执行人）\n\t\t\t\t\tthis.userIndex = 0;\n\t\t\t\t\tthis.selectedUser = null;\n\t\t\t\t\tthis.formData.user_id = '';\n\t\t\t\t\t\n\t\t\t\t\tif (this.userOptions.length <= 1) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '暂无可用执行人员',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.userOptions = [{ \n\t\t\t\t\t\t_id: '',\n\t\t\t\t\t\tnickname: '请选择执行人',\n\t\t\t\t\t\tavatar: '/static/user/default-avatar.png',\n\t\t\t\t\t\trole: []\n\t\t\t\t\t}];\n\t\t\t\t\tthis.userIndex = 0;\n\t\t\t\t\tthis.selectedUser = null;\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('加载用户失败:', e);\n\t\t\t\tthis.userOptions = [{ _id: '', nickname: '请选择人员' }];\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载用户数据失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 加载角色数据\n\t\tasync loadRoles() {\n\t\t\ttry {\n\t\t\t\t// 使用更完整的角色映射\n\t\t\t\tthis.roleNameMap = {\n\t\t\t\t\t'admin': '管理员',\n\t\t\t\t\t'responsible': '责任人',\n\t\t\t\t\t'reviser': '发布人',\n\t\t\t\t\t'supervisor': '主管',\n\t\t\t\t\t'PM': '副厂长',\n\t\t\t\t\t'GM': '厂长',\n\t\t\t\t\t'logistics': '后勤员',\n\t\t\t\t\t'dispatch': '调度员',\n\t\t\t\t\t'Integrated': '综合员',\n\t\t\t\t\t'operator': '设备员',\n\t\t\t\t\t'technician': '工艺员',\n\t\t\t\t\t'mechanic': '技术员',\n\t\t\t\t\t'user': '普通员工',\n\t\t\t\t\t'manager': '管理人员',\n\t\t\t\t\t'worker': '普通员工'\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t// 如果后续需要从服务器获取角色，可以使用正确的云函数\n\t\t\t\t// 例如使用用户云函数获取角色信息\n\t\t\t\t/*\n\t\t\t\tconst result = await PatrolApi.call({\n\t\t\t\t\tname: 'uni-id-co', // 使用uni-id云对象获取角色信息\n\t\t\t\t\taction: 'getRoleList'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (result.code === 0 && result.roleList && Array.isArray(result.roleList)) {\n\t\t\t\t\t// 构建角色映射\n\t\t\t\t\tthis.roleNameMap = {};\n\t\t\t\t\tresult.roleList.forEach(role => {\n\t\t\t\t\t\tthis.roleNameMap[role.roleID] = role.roleName || '未知角色';\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t*/\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('角色数据处理错误:', e);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 获取角色文本\n\t\tgetRoleText(role) {\n\t\t\tif (!role) return '普通员工';\n\t\t\t\n\t\t\t// 处理数组形式的角色\n\t\t\tif (Array.isArray(role)) {\n\t\t\t\tif (role.length === 0) return '普通员工';\n\t\t\t\t// 返回所有角色的文本，用逗号分隔\n\t\t\t\treturn role.map(r => this.getSingleRoleText(r)).join(', ');\n\t\t\t}\n\t\t\t\n\t\t\treturn this.getSingleRoleText(role);\n\t\t},\n\t\t\n\t\t// 处理单个角色值\n\t\tgetSingleRoleText(role) {\n\t\t\t// 首先从角色表的映射中查找\n\t\t\tif (this.roleNameMap[role]) {\n\t\t\t\treturn this.roleNameMap[role];\n\t\t\t}\n\t\t\t\n\t\t\t// 如果在角色表中找不到，使用预定义的映射作为备用\n\t\t\tconst roleMap = {\n\t\t\t\t'admin': '管理员',\n\t\t\t\t'responsible': '责任人',\n\t\t\t\t'reviser': '发布人',\n\t\t\t\t'supervisor': '主管',\n\t\t\t\t'PM': '副厂长',\n\t\t\t\t'GM': '厂长',\n\t\t\t\t'logistics': '后勤员',\n\t\t\t\t'dispatch': '调度员',\n\t\t\t\t'Integrated': '综合员',\n\t\t\t\t'operator': '设备员',\n\t\t\t\t'technician': '工艺员',\n\t\t\t\t'mechanic': '技术员',\n\t\t\t\t'user': '普通员工',\n\t\t\t\t'manager': '管理人员'\n\t\t\t};\n\t\t\t\n\t\t\treturn roleMap[role] || '用户 (' + role + ')';\n\t\t},\n\t\t\n\t\t// 获取日期范围显示文本\n\t\tgetDateRangeText() {\n\t\t\tif (this.dateRange && this.dateRange.length === 2) {\n\t\t\t\tconst startDate = this.dateRange[0];\n\t\t\t\tconst endDate = this.dateRange[1];\n\t\t\t\treturn `${startDate} 至 ${endDate}`;\n\t\t\t}\n\t\t\treturn '请选择日期范围';\n\t\t},\n\t\t\n\t\t// 日期选择变化处理\n\t\tonDateChange(e) {\n\t\t\tif (e.detail.value) {\n\t\t\t\tthis.currentDate = e.detail.value;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 班次选择变化\n\t\tonShiftChange(e) {\n\t\t\tconst index = e.detail.value;\n\t\t\tthis.shiftIndex = Number(index);\n\t\t\t\n\t\t\t// 更新选中的班次和班次ID\n\t\t\tthis.selectedShift = this.shiftOptions[this.shiftIndex];\n\t\t\tthis.formData.shift_id = this.selectedShift._id;\n\t\t},\n\t\t\n\t\t// 路线选择变化\n\t\tasync onRouteChange(e) {\n\t\t\tconst index = e.detail.value;\n\t\t\tthis.routeIndex = Number(index);\n\t\t\t\n\t\t\t// 获取选中的路线\n\t\t\tconst selectedRoute = this.routeOptions[this.routeIndex];\n\t\t\t\n\t\t\t// 如果选择的是空选项（第一个选项）\n\t\t\tif (index === 0 || !selectedRoute || !selectedRoute._id) {\n\t\t\t\tthis.selectedRoute = null;\n\t\t\t\tthis.formData.route_id = '';\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 更新选中的路线和路线ID\n\t\t\tthis.selectedRoute = selectedRoute;\n\t\t\tthis.formData.route_id = selectedRoute._id;\n\t\t\t\n\t\t\t// 加载路线详情\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '加载路线点位...',\n\t\t\t\tmask: true\n\t\t\t});\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst res = await PatrolApi.call({\n\t\t\t\t\tname: 'patrol-route',\n\t\t\t\t\taction: 'getRouteDetail',\n\t\t\t\t\tdata: { \n\t\t\t\t\t\tparams: {\n\t\t\t\t\t\t\troute_id: selectedRoute._id\n\t\t\t\t\t\t},\n\t\t\t\t\t\twith_points: true,\n\t\t\t\t\t\twith_detail: true,\n\t\t\t\t\t\tinclude_point_details: true\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tuni.hideLoading();\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\tlet pointsData = [];\n\t\t\t\t\tif (res.data.pointsDetail && res.data.pointsDetail.length > 0) {\n\t\t\t\t\t\tpointsData = this.processPointData(res.data.pointsDetail);\n\t\t\t\t\t} else if (res.data.points && res.data.points.length > 0) {\n\t\t\t\t\t\tpointsData = this.processPointData(res.data.points);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthis.selectedRoute = {\n\t\t\t\t\t\t...this.selectedRoute,\n\t\t\t\t\t\t...res.data,\n\t\t\t\t\t\tpointsDetail: pointsData\n\t\t\t\t\t};\n\t\t\t\t} else {\n\t\t\t\t\tthrow new Error(res.message || '获取路线详情失败');\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('加载路线详情失败:', e);\n\t\t\t\tuni.hideLoading();\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: e.message || '加载路线详情失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 用户选择变化\n\t\tonUserChange(e) {\n\t\t\tconst index = e.detail.value;\n\t\t\tthis.userIndex = Number(index);\n\t\t\t\n\t\t\t// 更新选中的用户和用户ID\n\t\t\tthis.selectedUser = this.userOptions[this.userIndex];\n\t\t\tthis.formData.user_id = this.selectedUser._id;\n\t\t},\n\t\t\n\t\t// 验证表单\n\t\tvalidateForm() {\n\t\t\t// 验证任务名称前缀\n\t\t\tif (!this.formData.namePrefix.trim()) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请输入任务名称前缀',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\t// 验证日期范围\n\t\t\tif (this.selectedDates.length === 0) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请选择日期范围',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\t// 验证班次\n\t\t\tif (!this.formData.shift_id) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请选择班次',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\t// 验证路线\n\t\t\tif (!this.formData.route_id) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请选择巡检路线',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\t// 验证执行人员\n\t\t\tif (!this.formData.user_id) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请选择执行人员',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\treturn true;\n\t\t},\n\t\t\n\t\t// 计算任务开始时间\n\t\tcalculateTaskStatus(startTimeStr) {\n\t\t\t// 如果没有有效的开始时间，默认为未开始\n\t\t\tif (!startTimeStr) {\n\t\t\t\treturn this.STATUS.NOT_STARTED;\n\t\t\t}\n\t\t\t\n\t\t\t// 将字符串转为Date对象\n\t\t\tlet startTime;\n\t\t\ttry {\n\t\t\t\tstartTime = new Date(startTimeStr);\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('解析任务开始时间出错:', e);\n\t\t\t\treturn this.STATUS.NOT_STARTED;\n\t\t\t}\n\t\t\t\n\t\t\t// 获取当前时间\n\t\t\tconst now = new Date();\n\t\t\t\n\t\t\t// 计算任务状态\n\t\t\tif (startTime > now) {\n\t\t\t\t// 开始时间在将来，任务未开始\n\t\t\t\treturn this.STATUS.NOT_STARTED;\n\t\t\t} else {\n\t\t\t\t// 开始时间已过，任务进行中\n\t\t\t\treturn this.STATUS.IN_PROGRESS;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 处理轮次数据\n\t\tasync processRoundsData(date) {\n\t\t\t// 验证前提条件\n\t\t\tif (!this.selectedShift || !this.selectedShift.rounds || !this.selectedRoute) {\n\t\t\t\tconsole.warn('处理轮次数据: 缺少必要条件');\n\t\t\t\treturn [];\n\t\t\t}\n\t\t\t\n\t\t\tconst shift = this.selectedShift;\n\t\t\tconst route = this.selectedRoute;\n\t\t\t\n\t\t\t// 验证路线是否有点位\n\t\t\tif (!route.pointsDetail || !Array.isArray(route.pointsDetail) || route.pointsDetail.length === 0) {\n\t\t\t\tconsole.warn('处理轮次数据: 路线没有点位');\n\t\t\t\treturn [];\n\t\t\t}\n\t\t\t\n\t\t\t// 提取路线点位 - 保持与单个任务创建页面相同的数据结构\n\t\t\tconst routePoints = route.pointsDetail.map((point, index) => ({\n\t\t\t\tpoint_id: point._id,\n\t\t\t\tname: point.name || `点位${index + 1}`,\n\t\t\t\torder: point.order || index,\n\t\t\t\tstatus: 0, // 初始状态：未打卡\n\t\t\t\tlocation: point.location,\n\t\t\t\trange: point.range || 50,\n\t\t\t\t// 修正二维码配置的读取方式\n\t\t\t\tqrcode_enabled: !!point.qrcode_enabled,\n\t\t\t\tqrcode_required: !!point.qrcode_required,\n\t\t\t\tqrcode_version: Number(point.qrcode_version || 0)\n\t\t\t}));\n\t\t\t\n\t\t\t// 处理每个轮次的数据\n\t\t\treturn shift.rounds.map(round => {\n\t\t\t\t// 计算轮次的打卡时间\n\t\t\t\tlet checkDateTime = this.calculateRoundTime(date, round.start_time || round.time, round.day_offset || 0);\n\t\t\t\tlet checkTime = this.formatDate(checkDateTime, 'HH:mm');\n\t\t\t\t\n\t\t\t\t// 计算轮次的实际有效天数偏移\n\t\t\t\tlet actualDayOffset = round.day_offset || 0;\n\t\t\t\tif (shift.across_day) {\n\t\t\t\t\t// 如果是跨天班次，且轮次时间在班次开始时间之前（如班次17:00开始，轮次在第二天8:00）\n\t\t\t\t\t// 需要确保天数偏移至少为1\n\t\t\t\t\tconst [roundHours] = (round.start_time || round.time).split(':').map(Number);\n\t\t\t\t\tconst [shiftStartHours] = (shift.start_time || '00:00').split(':').map(Number);\n\t\t\t\t\t\n\t\t\t\t\tif (roundHours < shiftStartHours && actualDayOffset === 0) {\n\t\t\t\t\t\tactualDayOffset = 1;\n\t\t\t\t\t\t// 重新计算轮次打卡时间\n\t\t\t\t\t\tcheckDateTime = this.calculateRoundTime(date, round.start_time || round.time, actualDayOffset);\n\t\t\t\t\t\tcheckTime = this.formatDate(checkDateTime, 'HH:mm');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 计算轮次结束时间\n\t\t\t\tconst endDateTime = this.calculateEndTime(checkDateTime, round.duration || 60);\n\t\t\t\t\n\t\t\t\t// 为轮次中的每个点位分配相同的初始状态 - 保持与单个任务相同的结构\n\t\t\t\tconst roundPoints = routePoints.map(point => ({\n\t\t\t\t\t...point,\n\t\t\t\t\tstatus: 0 // 初始状态：未打卡\n\t\t\t\t}));\n\t\t\t\t\n\t\t\t\t// 计算轮次状态 - 根据当前时间和轮次预计开始时间\n\t\t\t\tconst now = new Date();\n\t\t\t\tlet roundStatus = 0; // 默认未开始\n\t\t\t\t\n\t\t\t\tif (now > endDateTime) {\n\t\t\t\t\t// 当前时间已超过轮次结束时间，标记为\"已超时\"\n\t\t\t\t\troundStatus = 3;\n\t\t\t\t\tconsole.log(`轮次${round.round}已超时: 当前时间${now.toLocaleString()} > 结束时间${endDateTime.toLocaleString()}`);\n\t\t\t\t} else if (now >= checkDateTime) {\n\t\t\t\t\t// 当前时间在轮次的开始和结束时间之间，标记为\"进行中\"\n\t\t\t\t\troundStatus = 1;\n\t\t\t\t\tconsole.log(`轮次${round.round}进行中: 开始时间${checkDateTime.toLocaleString()} <= 当前时间${now.toLocaleString()} <= 结束时间${endDateTime.toLocaleString()}`);\n\t\t\t\t} else {\n\t\t\t\t\t// 未到开始时间，未开始\n\t\t\t\t\troundStatus = 0;\n\t\t\t\t\tconsole.log(`轮次${round.round}未开始: 当前时间${now.toLocaleString()} < 开始时间${checkDateTime.toLocaleString()}`);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn {\n\t\t\t\t\tround: round.round,\n\t\t\t\t\tname: round.name || `轮次${round.round}`,\n\t\t\t\t\ttime: checkTime,\n\t\t\t\t\tcheck_time: checkDateTime.toISOString(),\n\t\t\t\t\tstart_time: checkDateTime.toISOString(),\n\t\t\t\t\tend_time: endDateTime.toISOString(),\n\t\t\t\t\tday_offset: actualDayOffset,\n\t\t\t\t\tduration: round.duration || 60,\n\t\t\t\t\tstatus: roundStatus,  // 强制设置为未开始状态\n\t\t\t\t\tacross_day: shift.across_day,\n\t\t\t\t\tpoints: roundPoints,\n\t\t\t\t\tstats: {\n\t\t\t\t\t\ttotal_points: roundPoints.length,\n\t\t\t\t\t\tcompleted_points: 0,\n\t\t\t\t\t\tmissed_points: roundPoints.length,\n\t\t\t\t\t\tcompletion_rate: 0,\n\t\t\t\t\t\tabnormal_count: 0\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 计算整体统计数据\n\t\tcalculateOverallStats(rounds) {\n\t\t\t// 如果没有轮次，返回默认统计数据\n\t\t\tif (!rounds || rounds.length === 0) {\n\t\t\t\treturn {\n\t\t\t\t\ttotal_points: 0,\n\t\t\t\t\tcompleted_points: 0,\n\t\t\t\t\tmissed_points: 0,\n\t\t\t\t\tcompletion_rate: 0,\n\t\t\t\t\tabnormal_count: 0,\n\t\t\t\t\tlast_checkin_time: null // 初始无打卡记录\n\t\t\t\t};\n\t\t\t}\n\t\t\t\n\t\t\t// 计算所有轮次的统计数据\n\t\t\tlet totalPoints = 0;\n\t\t\tlet completedPoints = 0;\n\t\t\tlet abnormalCount = 0;\n\t\t\t\n\t\t\trounds.forEach(round => {\n\t\t\t\tif (round.stats) {\n\t\t\t\t\ttotalPoints += round.stats.total_points || 0;\n\t\t\t\t\tcompletedPoints += round.stats.completed_points || 0;\n\t\t\t\t\tabnormalCount += round.stats.abnormal_count || 0;\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\t// 计算未完成点位数和完成率\n\t\t\tconst missedPoints = totalPoints - completedPoints;\n\t\t\tconst completionRate = totalPoints > 0 ? completedPoints / totalPoints : 0;\n\t\t\t\n\t\t\treturn {\n\t\t\t\ttotal_points: totalPoints,\n\t\t\t\tcompleted_points: completedPoints,\n\t\t\t\tmissed_points: missedPoints,\n\t\t\t\tcompletion_rate: completionRate,\n\t\t\t\tabnormal_count: abnormalCount,\n\t\t\t\tlast_checkin_time: null // 初始无打卡记录\n\t\t\t};\n\t\t},\n\t\t\n\t\t// 处理批量任务表单数据\n\t\tasync prepareTaskData() {\n\t\t\t// 必填项验证\n\t\t\tif (!this.selectedDates || this.selectedDates.length === 0) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请选择执行日期',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn null;\n\t\t\t}\n\n\t\t\tif (!this.selectedRoute || !this.selectedRoute._id) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请选择巡检路线',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn null;\n\t\t\t}\n\n\t\t\tif (!this.selectedUser) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请选择执行人员',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn null;\n\t\t\t}\n\n\t\t\tif (!this.selectedShift) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请选择班次',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn null;\n\t\t\t}\n\n\t\t\t// 显示加载提示\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '正在处理数据...'\n\t\t\t});\n\n\t\t\ttry {\n\t\t\t\t// 创建任务基础对象\n\t\t\t\tlet tasks = [];\n\n\t\t\t\t// 处理选中的每一天\n\t\t\t\tfor (let i = 0; i < this.selectedDates.length; i++) {\n\t\t\t\t\tconst date = this.selectedDates[i];\n\t\t\t\t\t\n\t\t\t\t\t// 为当前日期处理轮次数据\n\t\t\t\t\tconst roundsData = await this.processRoundsData(date);\n\t\t\t\t\t\n\t\t\t\t\tif (!roundsData || roundsData.length === 0) {\n\t\t\t\t\t\tconsole.error('处理轮次数据失败:', date);\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 获取当前日期的统计数据\n\t\t\t\t\tconst statsData = this.calculateOverallStats(roundsData);\n\t\t\t\t\t\n\t\t\t\t\t// 获取enabled_rounds（启用的轮次编号数组）\n\t\t\t\t\tconst enabledRounds = this.selectedShift?.rounds?.map(r => r.round) || [];\n\n\t\t\t\t\t// 创建任务对象\n\t\t\t\t\tconst task = {\n\t\t\t\t\t\tpatrol_date: this.formatDate(date), // 格式化为'YYYY-MM-DD'\n\t\t\t\t\t\t\troute_id: this.selectedRoute._id,\n\t\t\t\t\t\t\troute_name: this.selectedRoute.name,\n\t\t\t\t\t\t\tarea: this.selectedRoute.name, // 区域默认使用路线名称\n\t\t\t\t\t\t\tshift_id: this.selectedShift._id,\n\t\t\t\t\t\t\tshift_name: this.selectedShift.name,\n\t\t\t\t\t\t\tshift_type: this.selectedShift.type || 1,\n\t\t\t\t\t\t\tacross_day: !!this.selectedShift.across_day,\n\t\t\t\t\t\t\tuser_id: this.selectedUser._id,\n\t\t\t\t\t\t\tuser_name: this.selectedUser.name,\n\t\t\t\t\t\t\trole_id: this.selectedUser.role || '',\n\t\t\t\t\t\t\trole_name: this.selectedUser.roleName || this.getRoleText(this.selectedUser.role),\n\t\t\t\t\t\t\trounds_detail: roundsData,\n\t\t\t\t\t\t\tenabled_rounds: enabledRounds, // 添加启用的轮次\n\t\t\t\t\t\t\tis_urgent: this.isUrgent ? 1 : 0,\n\t\t\t\t\t\t\tstatus: this.calculateTaskStatus(roundsData[0]?.start_time), // 使用calculateTaskStatus计算正确的状态\n\t\t\t\t\t\t\tremark: this.remark || '',\n\t\t\t\t\t\t\tstats: statsData,\n\t\t\t\t\t\t\toverall_stats: statsData, // 添加overall_stats字段\n\t\t\t\t\t\t\tlast_check_time: new Date().toISOString(), // 添加last_check_time字段\n\t\t\t\t\t\t\tcreate_time: new Date().toISOString(),\n\t\t\t\t\t\t\tupdate_time: new Date().toISOString(),\n\t\t\t\t\t\t\tauto_closed: false\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\ttasks.push(task);\n\t\t\t\t\t}\n\n\t\t\t\t\treturn tasks;\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('准备批量任务数据时出错:', e);\n\t\t\t\t\treturn null;\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\t\t\n\t\t// 检查批量重复任务\n\t\tasync checkBatchDuplicateTasks() {\n\t\t\ttry {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '检查重复任务...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tconst duplicateTasks = [];\n\t\t\t\t\n\t\t\t\t// 检查每个选中日期是否有重复任务\n\t\t\t\tfor (let i = 0; i < this.selectedDates.length; i++) {\n\t\t\t\t\tconst date = this.selectedDates[i];\n\t\t\t\t\t\n\t\t\t\t\t// 构建查询参数 - 使用正确的参数格式\n\t\t\t\t\tconst queryParams = {\n\t\t\t\t\t\tpatrolDate: date,\n\t\t\t\t\t\tfilters: {\n\t\t\t\t\t\t\troute_id: this.formData.route_id\n\t\t\t\t\t\t},\n\t\t\t\t\t\tuserId: this.formData.user_id,\n\t\t\t\t\t\tshift_id: this.formData.shift_id\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\t// 调用API查询是否存在相同条件的任务\n\t\t\t\t\tconst response = await PatrolApi.getTaskList(queryParams);\n\t\t\t\t\t\n\t\t\t\t\tif (response && response.code === 0 && response.data && response.data.list && response.data.list.length > 0) {\n\t\t\t\t\t\t// 找到重复任务\n\t\t\t\t\t\tduplicateTasks.push({\n\t\t\t\t\t\t\tdate: date,\n\t\t\t\t\t\t\ttask: response.data.list[0]\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tuni.hideLoading();\n\t\t\t\t\n\t\t\t\tif (duplicateTasks.length > 0) {\n\t\t\t\t\t// 有重复任务，显示提醒\n\t\t\t\t\tconst userName = this.selectedUser ? this.selectedUser.nickname : '该员工';\n\t\t\t\t\tconst shiftName = this.selectedShift ? this.selectedShift.name : '该班次';\n\t\t\t\t\tconst routeName = this.selectedRoute ? this.selectedRoute.name : '该路线';\n\t\t\t\t\t\n\t\t\t\t\tlet duplicateInfo = duplicateTasks.map(item => \n\t\t\t\t\t\t`• ${item.date}: ${item.task.name}`\n\t\t\t\t\t).join('\\n');\n\t\t\t\t\t\n\t\t\t\t\tif (duplicateInfo.length > 200) {\n\t\t\t\t\t\tduplicateInfo = duplicateInfo.substring(0, 200) + '...';\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\treturn new Promise((resolve) => {\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '发现重复任务',\n\t\t\t\t\t\t\tcontent: `${userName} 在以下日期的 ${shiftName} 班次已有 ${routeName} 的任务：\\n${duplicateInfo}\\n共 ${duplicateTasks.length} 个重复任务，是否仍要继续创建？`,\n\t\t\t\t\t\t\tconfirmText: '继续创建',\n\t\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\tresolve(res.confirm);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 没有找到重复任务\n\t\t\t\treturn true;\n\t\t\t} catch (error) {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tconsole.error('检查重复任务失败:', error);\n\t\t\t\t// 检查失败时允许继续创建\n\t\t\t\treturn true;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 批量创建任务\n\t\tasync submitForm() {\n\t\t\t// 表单验证\n\t\t\tif (!this.validateForm()) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 检查重复任务\n\t\t\tconst shouldContinue = await this.checkBatchDuplicateTasks();\n\t\t\tif (!shouldContinue) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 确认创建多个任务\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '确认创建',\n\t\t\t\tcontent: `您将为 ${this.selectedUser.nickname} 批量创建 ${this.selectedDates.length} 个任务，确定继续吗？`,\n\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tawait this.createBatchTasks();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 执行批量创建任务\n\t\tasync createBatchTasks() {\n\t\t\t// 显示加载状态\n\t\t\tthis.submitting = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 初始化计数器\n\t\t\t\tthis.createdTaskCount = 0;\n\t\t\t\tthis.totalTaskCount = this.selectedDates.length;\n\t\t\t\t\n\t\t\t\t// 显示进度弹窗\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: `创建中(0/${this.totalTaskCount})`,\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 成功和失败的任务列表\n\t\t\t\tconst successTasks = [];\n\t\t\t\tconst failedTasks = [];\n\t\t\t\t\n\t\t\t\t// 预先准备所有任务数据\n\t\t\t\tconst allTasks = await this.prepareTaskData();\n\t\t\t\t\n\t\t\t\tif (!allTasks || allTasks.length === 0) {\n\t\t\t\t\tthrow new Error('准备任务数据失败');\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 逐个创建任务\n\t\t\t\tfor (let i = 0; i < allTasks.length; i++) {\n\t\t\t\t\tconst taskData = allTasks[i];\n\t\t\t\t\tconst date = this.selectedDates[i];\n\t\t\t\t\t\n\t\t\t\t\ttry {\n\t\t\t\t\t\t// 更新进度提示\n\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\ttitle: `创建中(${i+1}/${this.totalTaskCount})`,\n\t\t\t\t\t\t\tmask: true\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 使用纯前缀作为任务名称，不再添加日期\n\t\t\t\t\t\ttaskData.name = this.formData.namePrefix;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 确保必要字段都存在，服务器期望的数据结构\n\t\t\t\t\t\tif (!taskData.last_check_time) {\n\t\t\t\t\t\t\ttaskData.last_check_time = new Date().toISOString();\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (!taskData.overall_stats && taskData.stats) {\n\t\t\t\t\t\t\ttaskData.overall_stats = {\n\t\t\t\t\t\t\t\t...taskData.stats,\n\t\t\t\t\t\t\t\tlast_checkin_time: new Date().toISOString()\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 添加必须的auto_closed字段\n\t\t\t\t\t\ttaskData.auto_closed = false;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 发送请求创建任务\n\t\t\t\t\t\tconst response = await PatrolApi.addTask(taskData);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 处理响应\n\t\t\t\t\t\tif (response && response.code === 0 && response.data && response.data._id) {\n\t\t\t\t\t\t\tsuccessTasks.push({\n\t\t\t\t\t\t\t\tdate,\n\t\t\t\t\t\t\t\tname: taskData.name,\n\t\t\t\t\t\t\t\tid: response.data._id\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tthis.createdTaskCount++;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tfailedTasks.push({\n\t\t\t\t\t\t\t\tdate,\n\t\t\t\t\t\t\t\tname: taskData.name,\n\t\t\t\t\t\t\t\terror: response.message || '未知错误'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tfailedTasks.push({\n\t\t\t\t\t\t\tdate,\n\t\t\t\t\t\t\tname: taskData.name,\n\t\t\t\t\t\t\terror: error.message || '请求异常'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 关闭加载提示\n\t\t\t\tuni.hideLoading();\n\t\t\t\t\n\t\t\t\t// 显示结果\n\t\t\t\tif (successTasks.length > 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: `成功创建${successTasks.length}个任务`,\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 触发刷新任务列表事件\n\t\t\t\t\tuni.$emit('refresh-task-list');\n\t\t\t\t\t\n\t\t\t\t\t// 如果有失败的任务，显示详情\n\t\t\t\t\tif (failedTasks.length > 0) {\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: '部分任务创建失败',\n\t\t\t\t\t\t\t\tcontent: `成功: ${successTasks.length}个, 失败: ${failedTasks.length}个`,\n\t\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 全部成功，返回上一页\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '创建失败',\n\t\t\t\t\t\tcontent: '所有任务创建失败，请检查网络或参数设置',\n\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '批量创建失败: ' + (e.message || '未知错误'),\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.submitting = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 返回上一页\n\t\tnavigateBack() {\n\t\t\tuni.navigateBack();\n\t\t},\n\t\t\n\t\t// 移除单个日期\n\t\tremoveDate(date) {\n\t\t\tconst index = this.selectedDates.findIndex(d => d === date);\n\t\t\tif (index !== -1) {\n\t\t\t\tthis.selectedDates.splice(index, 1);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 清空所有已选日期\n\t\tclearSelectedDates() {\n\t\t\t// 如果没有选择日期，不执行操作\n\t\t\tif (this.selectedDates.length === 0) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 清空选中的日期\n\t\t\tthis.selectedDates = [];\n\t\t\t\n\t\t\t// 重新生成日历日期，确保UI更新\n\t\t\tthis.generateCalendarDays();\n\t\t\t\n\t\t\t// 提示用户\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '已清空所选日期',\n\t\t\t\ticon: 'none',\n\t\t\t\tduration: 1500\n\t\t\t});\n\t\t},\n\t\taddSelectedDate() {\n\t\t\tif (!this.currentDate) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先选择日期',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 检查是否已存在相同日期\n\t\t\tconst exists = this.selectedDates.includes(this.currentDate);\n\t\t\tif (exists) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '该日期已添加',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 添加到已选日期列表\n\t\t\tthis.selectedDates.push(this.currentDate);\n\t\t\t\n\t\t\t// 显示成功提示\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '已添加',\n\t\t\t\ticon: 'success'\n\t\t\t});\n\t\t},\n\t\tformatDisplayDate(dateStr) {\n\t\t\ttry {\n\t\t\t\tconst date = new Date(dateStr);\n\t\t\t\treturn `${date.getMonth() + 1}月${date.getDate()}日`;\n\t\t\t} catch (e) {\n\t\t\t\treturn dateStr;\n\t\t\t}\n\t\t},\n\t\tonStartDateChange(e) {\n\t\t\tif (e.detail.value) {\n\t\t\t\tthis.startDate = e.detail.value;\n\t\t\t}\n\t\t},\n\t\tonEndDateChange(e) {\n\t\t\tif (e.detail.value) {\n\t\t\t\tthis.endDate = e.detail.value;\n\t\t\t}\n\t\t},\n\t\taddDateRange() {\n\t\t\tif (!this.startDate || !this.endDate) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请选择开始和结束日期',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tconst startDate = new Date(this.startDate.replace(/-/g, '/'));\n\t\t\tconst endDate = new Date(this.endDate.replace(/-/g, '/'));\n\t\t\t\n\t\t\t// 验证开始日期不能大于结束日期\n\t\t\tif (startDate > endDate) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '开始日期不能晚于结束日期',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 计算日期差\n\t\t\tconst daysDiff = getDaysDiff(startDate, endDate);\n\t\t\t\n\t\t\t// 日期范围过大时提示\n\t\t\tif (daysDiff > 60) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: `您选择了${daysDiff + 1}天的日期范围，可能会创建大量任务，确定继续吗？`,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tthis.generateDatesFromRange(startDate, endDate);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tthis.generateDatesFromRange(startDate, endDate);\n\t\t\t}\n\t\t},\n\t\t// 从范围生成日期数组\n\t\tgenerateDatesFromRange(startDate, endDate) {\n\t\t\tconst dates = [];\n\t\t\tconst currentDate = new Date(startDate);\n\t\t\t\n\t\t\twhile (currentDate <= endDate) {\n\t\t\t\tdates.push(formatDate(currentDate, 'YYYY-MM-DD'));\n\t\t\t\tcurrentDate.setDate(currentDate.getDate() + 1);\n\t\t\t}\n\t\t\t\n\t\t\t// 合并到已选日期，去重\n\t\t\tconst newDates = dates.filter(date => !this.selectedDates.includes(date));\n\t\t\t\n\t\t\tif (newDates.length === 0) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '所选日期已全部添加',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tthis.selectedDates = [...this.selectedDates, ...newDates];\n\t\t\t\n\t\t\tuni.showToast({\n\t\t\t\ttitle: `已添加${newDates.length}个日期`,\n\t\t\t\ticon: 'success'\n\t\t\t});\n\t\t\t\n\t\t\t// 清空范围选择\n\t\t\t// this.startDate = '';\n\t\t\t// this.endDate = '';\n\t\t},\n\t\t// 生成日历数据\n\t\tgenerateCalendarDays() {\n\t\t\tconst year = this.currentYear;\n\t\t\tconst month = this.currentMonth;\n\t\t\t\n\t\t\t// 获取当月第一天\n\t\t\tconst firstDay = new Date(year, month, 1);\n\t\t\t// 获取当月最后一天\n\t\t\tconst lastDay = new Date(year, month + 1, 0);\n\t\t\t\n\t\t\t// 当月天数\n\t\t\tconst daysInMonth = lastDay.getDate();\n\t\t\t\n\t\t\t// 当月第一天是星期几\n\t\t\tconst firstDayWeek = firstDay.getDay();\n\t\t\t\n\t\t\t// 日历中显示的总天数（前一月尾部 + 当月 + 下一月头部）\n\t\t\tconst totalDays = 42; // 6行7列\n\t\t\t\n\t\t\t// 获取今天日期\n\t\t\tconst today = new Date();\n\t\t\tconst todayFullDate = formatDate(today, 'YYYY-MM-DD');\n\t\t\t\n\t\t\t// 准备日历数据\n\t\t\tthis.calendarDays = [];\n\t\t\t\n\t\t\t// 添加上个月的日期\n\t\t\tconst prevMonthLastDay = new Date(year, month, 0).getDate();\n\t\t\tfor (let i = 0; i < firstDayWeek; i++) {\n\t\t\t\tconst date = prevMonthLastDay - firstDayWeek + i + 1;\n\t\t\t\tconst prevYear = month === 0 ? year - 1 : year;\n\t\t\t\tconst prevMonth = month === 0 ? 11 : month - 1;\n\t\t\t\tconst fullDate = formatDate(new Date(prevYear, prevMonth, date), 'YYYY-MM-DD');\n\t\t\t\tconst disabled = this.isDisabledDate(fullDate);\n\t\t\t\t\n\t\t\t\tthis.calendarDays.push({\n\t\t\t\t\tdate: date,\n\t\t\t\t\tfullDate: fullDate,\n\t\t\t\t\tcurrentMonth: false,\n\t\t\t\t\tisToday: fullDate === todayFullDate,\n\t\t\t\t\tdisabled: disabled\n\t\t\t\t});\n\t\t\t}\n\t\t\t\n\t\t\t// 添加当月日期\n\t\t\tfor (let i = 1; i <= daysInMonth; i++) {\n\t\t\t\tconst fullDate = formatDate(new Date(year, month, i), 'YYYY-MM-DD');\n\t\t\t\tconst disabled = this.isDisabledDate(fullDate);\n\t\t\t\t\n\t\t\t\tthis.calendarDays.push({\n\t\t\t\t\tdate: i,\n\t\t\t\t\tfullDate: fullDate,\n\t\t\t\t\tcurrentMonth: true,\n\t\t\t\t\tisToday: fullDate === todayFullDate,\n\t\t\t\t\tdisabled: disabled\n\t\t\t\t});\n\t\t\t}\n\t\t\t\n\t\t\t// 添加下个月的日期\n\t\t\tconst remainingDays = totalDays - this.calendarDays.length;\n\t\t\tfor (let i = 1; i <= remainingDays; i++) {\n\t\t\t\tconst nextYear = month === 11 ? year + 1 : year;\n\t\t\t\tconst nextMonth = month === 11 ? 0 : month + 1;\n\t\t\t\tconst fullDate = formatDate(new Date(nextYear, nextMonth, i), 'YYYY-MM-DD');\n\t\t\t\tconst disabled = this.isDisabledDate(fullDate);\n\t\t\t\t\n\t\t\t\tthis.calendarDays.push({\n\t\t\t\t\tdate: i,\n\t\t\t\t\tfullDate: fullDate,\n\t\t\t\t\tcurrentMonth: false,\n\t\t\t\t\tisToday: fullDate === todayFullDate,\n\t\t\t\t\tdisabled: disabled\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 切换到上个月\n\t\tprevMonth() {\n\t\t\tif (this.currentMonth === 0) {\n\t\t\t\tthis.currentYear--;\n\t\t\t\tthis.currentMonth = 11;\n\t\t\t} else {\n\t\t\t\tthis.currentMonth--;\n\t\t\t}\n\t\t\tthis.generateCalendarDays();\n\t\t},\n\t\t\n\t\t// 切换到下个月\n\t\tnextMonth() {\n\t\t\tif (this.currentMonth === 11) {\n\t\t\t\tthis.currentYear++;\n\t\t\t\tthis.currentMonth = 0;\n\t\t\t} else {\n\t\t\t\tthis.currentMonth++;\n\t\t\t}\n\t\t\tthis.generateCalendarDays();\n\t\t},\n\t\t\n\t\t// 检查日期是否被选中\n\t\tisDateSelected(date) {\n\t\t\treturn this.selectedDates.includes(date);\n\t\t},\n\t\t\n\t\t// 检查日期是否禁用\n\t\tisDisabledDate(dateStr) {\n\t\t\ttry {\n\t\t\t\tconst date = new Date(dateStr.replace(/-/g, '/'));\n\t\t\t\tconst minDate = new Date(this.minDate.replace(/-/g, '/'));\n\t\t\t\tconst maxDate = new Date(this.maxDate.replace(/-/g, '/'));\n\t\t\t\t\n\t\t\t\treturn date < minDate || date > maxDate;\n\t\t\t} catch(e) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 切换日期选择状态\n\t\ttoggleDateSelection(day) {\n\t\t\tif (day.disabled) return;\n\t\t\t\n\t\t\tconst fullDate = day.fullDate;\n\t\t\tconst index = this.selectedDates.indexOf(fullDate);\n\t\t\t\n\t\t\tif (index === -1) {\n\t\t\t\t// 添加日期\n\t\t\t\tthis.selectedDates.push(fullDate);\n\t\t\t} else {\n\t\t\t\t// 移除日期\n\t\t\t\tthis.selectedDates.splice(index, 1);\n\t\t\t}\n\t\t},\n\t\t// 刷新路线点位数据\n\t\tasync refreshRoutePointsData() {\n\t\t\tif (!this.formData.route_id) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先选择路线',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\ttry {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '刷新中...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tconst res = await PatrolApi.call({\n\t\t\t\t\tname: 'patrol-route',\n\t\t\t\t\taction: 'getRouteDetail',\n\t\t\t\t\tdata: { \n\t\t\t\t\t\tparams: { \n\t\t\t\t\t\t\troute_id: this.formData.route_id,\n\t\t\t\t\t\t\twith_points: true,\n\t\t\t\t\t\t\twith_detail: true,\n\t\t\t\t\t\t\tinclude_point_details: true,\n\t\t\t\t\t\t\tforce_refresh: true\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tuni.hideLoading();\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\tlet pointsData = [];\n\t\t\t\t\tif (res.data.pointsDetail && res.data.pointsDetail.length > 0) {\n\t\t\t\t\t\tpointsData = this.processPointData(res.data.pointsDetail);\n\t\t\t\t\t} else if (res.data.points && res.data.points.length > 0) {\n\t\t\t\t\t\tpointsData = this.processPointData(res.data.points);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthis.selectedRoute = {\n\t\t\t\t\t\t...this.selectedRoute,\n\t\t\t\t\t\tpointsDetail: pointsData\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\tthis.pointsRefreshed = true;\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '点位数据已更新',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tthrow new Error(res.message || '获取最新点位数据失败');\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '刷新点位数据失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t// 处理API返回的点位数据\n\t\tprocessPointData(data) {\n\t\t\tif (!data || !Array.isArray(data) || data.length === 0) {\n\t\t\t\treturn [];\n\t\t\t}\n\t\t\t\n\t\t\tconst hasDetailedInfo = data[0].name !== undefined && data[0].name !== null;\n\t\t\t\n\t\t\tif (hasDetailedInfo) {\n\t\t\t\treturn data.map(point => ({\n\t\t\t\t\t_id: point._id || point.point_id,\n\t\t\t\t\tname: point.name,\n\t\t\t\t\trange: point.range || 50,\n\t\t\t\t\torder: point.order || 0,\n\t\t\t\t\tlocation: point.location || null,\n\t\t\t\t\tqrcode_enabled: !!point.qrcode_enabled,\n\t\t\t\t\tqrcode_required: !!point.qrcode_required,\n\t\t\t\t\tqrcode_version: Number(point.qrcode_version || 0),\n\t\t\t\t\toriginal: point\n\t\t\t\t}));\n\t\t\t} else {\n\t\t\t\treturn data.map((point, index) => ({\n\t\t\t\t\t_id: point._id || point.point_id || `point_${index}`,\n\t\t\t\t\tname: `点位${index + 1}`,\n\t\t\t\t\trange: 50,\n\t\t\t\t\torder: point.order || index,\n\t\t\t\t\tlocation: null,\n\t\t\t\t\tqrcode_enabled: false,\n\t\t\t\t\tqrcode_required: false,\n\t\t\t\t\tqrcode_version: 0,\n\t\t\t\t\toriginal: point\n\t\t\t\t}));\n\t\t\t}\n\t\t},\n\t\t// 日期格式化工具函数\n\t\tformatDate(date, format = 'YYYY-MM-DD') {\n\t\t\t// 确保date是Date对象\n\t\t\tlet d;\n\t\t\tif (typeof date === 'string') {\n\t\t\t\t// 处理ISO字符串\n\t\t\t\td = new Date(date);\n\t\t\t} else if (date instanceof Date) {\n\t\t\t\td = date;\n\t\t\t} else {\n\t\t\t\t// 默认使用当前日期\n\t\t\t\td = new Date();\n\t\t\t\tconsole.warn('格式化日期: 使用当前日期作为默认值');\n\t\t\t}\n\t\t\t\n\t\t\t// 防止无效日期\n\t\t\tif (isNaN(d.getTime())) {\n\t\t\t\tconsole.error('格式化日期: 无效日期', date);\n\t\t\t\treturn '';\n\t\t\t}\n\t\t\t\n\t\t\tconst year = d.getFullYear();\n\t\t\tconst month = String(d.getMonth() + 1).padStart(2, '0');\n\t\t\tconst day = String(d.getDate()).padStart(2, '0');\n\t\t\tconst hours = String(d.getHours()).padStart(2, '0');\n\t\t\tconst minutes = String(d.getMinutes()).padStart(2, '0');\n\t\t\tconst seconds = String(d.getSeconds()).padStart(2, '0');\n\t\t\t\n\t\t\t// 替换格式字符串\n\t\t\treturn format\n\t\t\t\t.replace('YYYY', year)\n\t\t\t\t.replace('MM', month)\n\t\t\t\t.replace('DD', day)\n\t\t\t\t.replace('HH', hours)\n\t\t\t\t.replace('mm', minutes)\n\t\t\t\t.replace('ss', seconds);\n\t\t},\n\t\t\n\t\t// 计算轮次时间工具函数\n\t\tcalculateRoundTime(date, timeStr, dayOffset = 0) {\n\t\t\t// 确保date是Date对象\n\t\t\tlet baseDate;\n\t\t\tif (typeof date === 'string') {\n\t\t\t\t// 处理可能的格式，包括YYYY-MM-DD或带时区的ISO字符串\n\t\t\t\tif (date.includes('T')) {\n\t\t\t\t\tbaseDate = new Date(date);\n\t\t\t\t} else {\n\t\t\t\t\t// 纯日期字符串，添加时间部分\n\t\t\t\t\tbaseDate = new Date(date + 'T00:00:00');\n\t\t\t\t}\n\t\t\t} else if (date instanceof Date) {\n\t\t\t\tbaseDate = new Date(date);\n\t\t\t} else {\n\t\t\t\t// 默认使用当前日期\n\t\t\t\tbaseDate = new Date();\n\t\t\t\tconsole.warn('计算轮次时间: 使用当前日期作为默认值');\n\t\t\t}\n\t\t\t\n\t\t\t// 重置时间部分为00:00:00\n\t\t\tbaseDate.setHours(0, 0, 0, 0);\n\t\t\t\n\t\t\t// 应用天数偏移\n\t\t\tif (dayOffset && !isNaN(dayOffset)) {\n\t\t\t\tbaseDate.setDate(baseDate.getDate() + parseInt(dayOffset));\n\t\t\t}\n\t\t\t\n\t\t\t// 解析时间字符串\n\t\t\tif (timeStr && typeof timeStr === 'string') {\n\t\t\t\tconst [hours, minutes] = timeStr.split(':').map(num => parseInt(num, 10));\n\t\t\t\t\n\t\t\t\t// 应用时间\n\t\t\t\tif (!isNaN(hours) && !isNaN(minutes)) {\n\t\t\t\t\tbaseDate.setHours(hours, minutes, 0, 0);\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\treturn baseDate;\n\t\t},\n\n\t\t// 计算结束时间工具函数\n\t\tcalculateEndTime(startTime, durationMinutes = 60) {\n\t\t\t// 确保startTime是Date对象\n\t\t\tconst start = new Date(startTime);\n\t\t\t// 添加持续时间\n\t\t\treturn new Date(start.getTime() + durationMinutes * 60 * 1000);\n\t\t},\n\t\t\n\t\t// 过滤用户选项\n\t\tfilterUserOptions() {\n\t\t\tconst searchText = this.searchUserName.toLowerCase();\n\t\t\tif (!searchText) {\n\t\t\t\t// 如果搜索框为空，显示所有用户\n\t\t\t\tthis.filteredUsers = [...this.userOptions];\n\t\t\t} else {\n\t\t\t\tthis.filteredUsers = this.userOptions.filter(user => \n\t\t\t\t\tuser.nickname && user.nickname.toLowerCase().includes(searchText) && \n\t\t\t\t\t(user._id === '' || !user.nickname.startsWith('匿名')) // 确保搜索结果也遵循匿名用户过滤规则，但保留默认选项\n\t\t\t\t);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 选择用户\n\t\tselectUser(index) {\n\t\t\tconst user = this.filteredUsers[index];\n\t\t\tif (user && user._id) {\n\t\t\t\tthis.formData.user_id = user._id;\n\t\t\t\tthis.selectedUser = user;\n\t\t\t\t// 更新userIndex以保持与之前逻辑的兼容性\n\t\t\t\tconst originalIndex = this.userOptions.findIndex(u => u._id === user._id);\n\t\t\t\tif (originalIndex !== -1) {\n\t\t\t\t\tthis.userIndex = originalIndex;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tthis.formData.user_id = '';\n\t\t\t\tthis.selectedUser = null;\n\t\t\t}\n\t\t\tthis.showUserSelect = false;\n\t\t},\n\t\t\n\t\t// 隐藏用户选择器\n\t\thideUserSelect() {\n\t\t\tthis.showUserSelect = false;\n\t\t},\n\t},\n\tcomputed: {\n\t\tcalendarTitle() {\n\t\t\treturn `${this.currentYear}年${this.currentMonth + 1}月`;\n\t\t}\n\t},\n\tmounted() {\n\t\tthis.generateCalendarDays();\n\t},\n};\n</script>\n\n<style lang=\"scss\">\n.batch-add-task-container {\n\tbackground-color: #F5F7FA;\n\tmin-height: 100vh;\n\tpadding-bottom: 120rpx;\n}\n\n.form-container {\n\tpadding: 30rpx 20rpx;\n}\n\n.form-section {\n\tbackground-color: #FFFFFF;\n\tborder-radius: 12rpx;\n\tmargin-bottom: 30rpx;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n\toverflow: hidden;\n}\n\n.card-header {\n\tpadding: 24rpx;\n\tborder-bottom: 1rpx solid #F0F0F0;\n}\n\n.section-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #333333;\n\tposition: relative;\n\tpadding-left: 20rpx;\n}\n\n.section-title::before {\n\tcontent: '';\n\tposition: absolute;\n\tleft: 0;\n\ttop: 50%;\n\ttransform: translateY(-50%);\n\twidth: 6rpx;\n\theight: 30rpx;\n\tbackground-color: #1677FF;\n\tborder-radius: 3rpx;\n}\n\n.form-item {\n\tpadding: 24rpx;\n\tborder-bottom: 1rpx solid #F0F0F0;\n}\n\n.form-item:last-child {\n\tborder-bottom: none;\n}\n\n.form-label {\n\tdisplay: block;\n\tfont-size: 28rpx;\n\tcolor: #666666;\n\tmargin-bottom: 16rpx;\n\t\n\t&.required::before {\n\t\tcontent: '*';\n\t\tcolor: #FF4D4F;\n\t\tmargin-right: 8rpx;\n\t}\n}\n\n.form-input {\n\theight: 80rpx;\n\tbackground-color: #F9F9F9;\n\tborder: 1px solid #EEEEEE;\n\tborder-radius: 8rpx;\n\tpadding: 0 20rpx;\n\tfont-size: 28rpx;\n\tcolor: #333333;\n\ttransition: all 0.2s ease;\n\t\n\t&:focus {\n\t\tborder-color: #1677FF;\n\t\tbackground-color: #FFFFFF;\n\t}\n}\n\n.form-picker {\n\twidth: 100%;\n}\n\n.picker-value {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\twidth: 100%;\n\theight: 80rpx;\n\tborder: 1px solid #EEEEEE;\n\tborder-radius: 8rpx;\n\tpadding: 0 20rpx;\n\tfont-size: 28rpx;\n\tbox-sizing: border-box;\n\tbackground-color: #F9F9F9;\n\ttransition: all 0.2s ease;\n}\n\n.shift-info {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.cross-day-tag {\n\tfont-size: 22rpx;\n\tcolor: #FA8C16;\n\tbackground-color: rgba(250, 140, 22, 0.1);\n\tpadding: 2rpx 8rpx;\n\tborder-radius: 4rpx;\n\tmargin-left: 8rpx;\n}\n\n.shift-time-info {\n\tmargin-bottom: 12rpx;\n}\n\n.time-range {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\tfont-size: 26rpx;\n\tcolor: #666666;\n\tpadding: 8rpx 0;\n}\n\n.round-tags {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 12rpx;\n\tmargin-top: 8rpx;\n}\n\n.round-tag {\n\tbackground-color: #E6F7FF;\n\tcolor: #1677FF;\n\tfont-size: 24rpx;\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\tborder: 1px solid rgba(22, 119, 255, 0.2);\n}\n\n.next-day-badge {\n\tdisplay: inline-block;\n\tfont-size: 22rpx;\n\tpadding: 2rpx 8rpx;\n\tbackground-color: #FA8C16;\n\tcolor: #FFFFFF;\n\tborder-radius: 20rpx;\n\tmargin-left: 6rpx;\n}\n\n.user-info {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 16rpx 0;\n}\n\n.user-avatar {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 40rpx;\n\tmargin-right: 24rpx;\n\tbackground-color: #F0F0F0;\n}\n\n.user-details {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.user-name {\n\tfont-size: 30rpx;\n\tcolor: #333333;\n\tfont-weight: 500;\n\tmargin-bottom: 8rpx;\n}\n\n.user-role {\n\tfont-size: 24rpx;\n\tcolor: #666666;\n}\n\n.status-info {\n\tbackground-color: #F5F7FA;\n\tborder-radius: 8rpx;\n\tpadding: 16rpx 24rpx;\n}\n\n.status-desc {\n\tfont-size: 26rpx;\n\tcolor: #666666;\n\tline-height: 1.5;\n}\n\n.timezone-indicator {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-top: 16rpx;\n}\n\n.timezone-indicator text {\n\tfont-size: 24rpx;\n\tcolor: #999999;\n\tmargin-left: 8rpx;\n}\n\n.status-preview {\n\tdisplay: flex;\n\tflex-direction: column;\n\tbackground-color: #F5F7FA;\n\tborder-radius: 8rpx;\n\tpadding: 16rpx 24rpx;\n}\n\n.status-tag {\n\tdisplay: inline-flex;\n\talign-items: center;\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 30rpx;\n\tfont-size: 26rpx;\n\twidth: fit-content;\n}\n\n.status-0 {\n\tbackground-color: #F5F5F5;\n\tcolor: #666666;\n}\n\n.status-1 {\n\tbackground-color: #E6F7FF;\n\tcolor: #1677FF;\n}\n\n.status-2 {\n\tbackground-color: #F6FFED;\n\tcolor: #52C41A;\n}\n\n.status-3 {\n\tbackground-color: #FFF1F0;\n\tcolor: #F5222D;\n}\n\n.status-4 {\n\tbackground-color: #F5F5F5;\n\tcolor: #999999;\n}\n\n.status-tag uni-icons {\n\tmargin-right: 8rpx;\n}\n\n.status-explanation {\n\tmargin-top: 12rpx;\n\tfont-size: 24rpx;\n\tcolor: #8F959E;\n}\n\n.submit-section {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tpadding: 30rpx 20rpx;\n\tbackground-color: #FFFFFF;\n\tbox-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tz-index: 100;\n}\n\n.cancel-btn, .primary-btn {\n\twidth: 320rpx;\n\theight: 88rpx;\n\tline-height: 88rpx;\n\ttext-align: center;\n\tborder-radius: 44rpx;\n\tfont-size: 32rpx;\n}\n\n.cancel-btn {\n\tbackground-color: #F5F7FA;\n\tcolor: #666666;\n\tborder: 1rpx solid #D9D9D9;\n}\n\n.primary-btn {\n\tbackground-color: #1677FF;\n\tcolor: #FFFFFF !important;\n\t\n\t&[loading] {\n\t\topacity: 0.8;\n\t}\n}\n\n/* 确保加载状态按钮的文字和图标为白色 */\nbutton[loading]::before {\n\tcolor: #FFFFFF !important;\n}\n\nbutton[loading]::after {\n\tborder-color: #FFFFFF transparent #FFFFFF transparent !important;\n}\n\n.points-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 16rpx;\n}\n\n.refresh-points-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tfont-size: 26rpx;\n\tcolor: #1677FF;\n}\n\n.refresh-points-btn text {\n\tmargin-left: 8rpx;\n}\n\n.points-info {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 16rpx;\n}\n\n.info-text {\n\tfont-size: 24rpx;\n\tmargin-left: 8rpx;\n}\n\n.success {\n\tcolor: #52C41A;\n}\n\n.points-list {\n\tbackground-color: #F5F7FA;\n\tborder-radius: 8rpx;\n\tpadding: 16rpx;\n\tmax-height: 400rpx;\n\toverflow-y: auto;\n}\n\n.point-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 12rpx 16rpx;\n\tborder-bottom: 1rpx solid #F0F0F0;\n}\n\n.point-item:last-child {\n\tborder-bottom: none;\n}\n\n.point-index {\n\twidth: 40rpx;\n\theight: 40rpx;\n\tbackground-color: #1677FF;\n\tcolor: #FFFFFF;\n\tborder-radius: 20rpx;\n\ttext-align: center;\n\tline-height: 40rpx;\n\tfont-size: 24rpx;\n\tmargin-right: 16rpx;\n}\n\n.point-name {\n\tflex: 1;\n\tfont-size: 28rpx;\n\tcolor: #333333;\n}\n\n.point-range {\n\tfont-size: 24rpx;\n\tcolor: #8F959E;\n}\n\n.date-range-picker {\n\twidth: 100%;\n}\n\n.uni-date-class {\n\twidth: 100%;\n}\n\n.uni-date-editor-class {\n\twidth: 100%;\n\tbackground-color: #F5F7FA !important;\n\tborder-radius: 8rpx;\n}\n\n.selected-dates-container {\n\tmargin-top: 16rpx;\n}\n\n.selected-dates-counter {\n\tfont-size: 28rpx;\n\tcolor: #333333;\n\tmargin-bottom: 12rpx;\n}\n\n.selected-dates-list {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n}\n\n.date-tag {\n\tdisplay: flex;\n\talign-items: center;\n\tposition: relative;\n\tpadding: 6rpx 36rpx 6rpx 16rpx;\n\tbackground-color: #E6F7FF;\n\tcolor: #1677FF;\n\tborder: 1rpx solid #91D5FF;\n\tborder-radius: 30rpx;\n\tfont-size: 24rpx;\n\tmargin-right: 12rpx;\n\tmargin-bottom: 12rpx;\n}\n\n.more-dates {\n\tfont-size: 24rpx;\n\tcolor: #8F959E;\n\tpadding: 6rpx 16rpx;\n}\n\n.task-preview {\n\tbackground-color: #F5F7FA;\n\tborder-radius: 8rpx;\n\tpadding: 24rpx;\n}\n\n.preview-info {\n\tmargin-bottom: 16rpx;\n\tdisplay: flex;\n\talign-items: flex-start;\n}\n\n.preview-info:last-child {\n\tmargin-bottom: 0;\n}\n\n.preview-label {\n\tfont-size: 28rpx;\n\tcolor: #666666;\n\tmargin-right: 16rpx;\n\tmin-width: 160rpx;\n}\n\n.preview-value {\n\tfont-size: 28rpx;\n\tcolor: #333333;\n\tflex: 1;\n}\n\n.calendar-wrapper {\n\twidth: 100%;\n\tborder-radius: 8rpx;\n\toverflow: hidden;\n\tbackground-color: #FFFFFF;\n\tmargin-bottom: 20rpx;\n}\n\n.dates-tip {\n\tfont-size: 26rpx;\n\tcolor: #999999;\n\tpadding: 16rpx;\n\ttext-align: center;\n\tbackground-color: #f9f9f9;\n\tborder-radius: 8rpx;\n}\n\n.date-selection {\n\tdisplay: flex;\n\tflex-direction: column;\n\twidth: 100%;\n}\n\n.date-picker-row {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 16rpx;\n\tgap: 20rpx;\n}\n\n.add-date-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 12rpx 24rpx;\n\tborder-radius: 30rpx;\n\tbackground-color: #E6F7FF;\n\tfont-size: 26rpx;\n\tcolor: #1677FF;\n\tbox-shadow: 0 2rpx 8rpx rgba(22, 119, 255, 0.1);\n}\n\n.add-date-btn text {\n\tmargin-left: 8rpx;\n}\n\n.date-range-row {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-top: 16rpx;\n\tgap: 20rpx;\n}\n\n.date-range-label {\n\tfont-size: 26rpx;\n\tcolor: #666666;\n\twhite-space: nowrap;\n}\n\n.date-range-picker {\n\tflex: 1;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.start-date-picker, .end-date-picker {\n\tflex: 1;\n}\n\n.range-separator {\n\tpadding: 0 16rpx;\n\tcolor: #999999;\n}\n\n.add-range-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 12rpx 24rpx;\n\tborder-radius: 30rpx;\n\tbackground-color: #FFF7E6;\n\tfont-size: 26rpx;\n\tcolor: #FA8C16;\n\tbox-shadow: 0 2rpx 8rpx rgba(250, 140, 22, 0.1);\n}\n\n.add-range-btn text {\n\tmargin-left: 8rpx;\n}\n\n.date-tag::after {\n\tcontent: '×';\n\tposition: absolute;\n\tright: 10rpx;\n\tfont-size: 24rpx;\n\tcolor: #999;\n\twidth: 24rpx;\n\theight: 24rpx;\n\tline-height: 24rpx;\n\ttext-align: center;\n}\n\n.clear-dates {\n\tdisplay: inline-flex;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 6rpx 16rpx;\n\tborder: 1px solid #ddd;\n\tborder-radius: 30rpx;\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tmargin-left: 12rpx;\n\tmargin-bottom: 12rpx;\n}\n\n.custom-calendar {\n\tbackground-color: #FFFFFF;\n\tborder-radius: 12rpx;\n\tborder: 1px solid #ECECEC;\n\toverflow: hidden;\n\twidth: 100%;\n\tmargin-bottom: 20rpx;\n}\n\n.calendar-header {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tpadding: 24rpx 0;\n\tbackground-color: #F5F7FA;\n}\n\n.month-picker {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.arrow-btn {\n\tpadding: 10rpx 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.current-month {\n\tfont-size: 30rpx;\n\tfont-weight: bold;\n\tcolor: #333333;\n\twidth: 160rpx;\n\ttext-align: center;\n}\n\n.weekday-header {\n\tdisplay: flex;\n\tbackground-color: #F9FAFB;\n\tborder-bottom: 1px solid #ECECEC;\n}\n\n.weekday-item {\n\tflex: 1;\n\ttext-align: center;\n\tfont-size: 24rpx;\n\tcolor: #666666;\n\tpadding: 16rpx 0;\n}\n\n.days-grid {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tpadding: 10rpx;\n}\n\n.day-cell {\n\twidth: 14.285%;\n\theight: 70rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tposition: relative;\n}\n\n.day-number {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tline-height: 60rpx;\n\ttext-align: center;\n\tfont-size: 26rpx;\n\tcolor: #333333;\n\tborder-radius: 50%;\n}\n\n.other-month .day-number {\n\tcolor: #CCCCCC;\n}\n\n.today .day-number {\n\tbackground-color: #F0F8FF;\n\tcolor: #1677FF;\n}\n\n.selected .day-number {\n\tbackground-color: #1677FF;\n\tcolor: #FFFFFF;\n}\n\n.disabled {\n\tpointer-events: none;\n}\n\n.disabled .day-number {\n\tcolor: #DDDDDD;\n}\n\n/* 日期选择部分样式优化 */\n.date-selection-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 10rpx;\n}\n\n.date-selection-info {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 20rpx;\n\tmargin-bottom: 10rpx;\n}\n\n.selected-days-count {\n\tfont-size: 26rpx;\n\tcolor: #1677FF;\n\tbackground-color: #E6F7FF;\n\tpadding: 6rpx 16rpx;\n\tborder-radius: 20rpx;\n\tfont-weight: 500;\n}\n\n.clear-dates-btn {\n\tfont-size: 24rpx;\n\tcolor: #FF4D4F;\n\tbackground-color: #FFF1F0;\n\tpadding: 6rpx 16rpx;\n\tborder-radius: 20rpx;\n\tborder: 1px solid #FFE4E4;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n/* 确保日历网格样式正常 */\n.custom-calendar {\n\tbackground-color: #fff;\n\tborder-radius: 10rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n\tmargin-top: 10rpx;\n}\n\n/* 自定义人员选择下拉菜单样式 */\n.custom-select {\n\tposition: relative;\n}\n\n.user-select-modal {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tbackground-color: rgba(0, 0, 0, 0.5);\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tz-index: 9999;\n}\n\n.user-select-popup {\n\twidth: 90%;\n\tmax-width: 650rpx;\n\tbackground-color: #FFFFFF;\n\tborder-radius: 12rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);\n}\n\n/* #ifdef H5 */\n/* H5 端的响应式样式 */\n@media screen and (min-width: 1200px) {\n    .user-select-popup {\n        max-width: 600px;\n    }\n}\n\n@media screen and (min-width: 992px) and (max-width: 1199px) {\n    .user-select-popup {\n        max-width: 560px;\n    }\n}\n\n@media screen and (min-width: 768px) and (max-width: 991px) {\n    .user-select-popup {\n        max-width: 500px;\n    }\n}\n\n@media screen and (min-width: 576px) and (max-width: 767px) {\n    .user-select-popup {\n        max-width: 450px;\n    }\n}\n\n@media screen and (max-width: 575px) {\n    .user-select-popup {\n        width: 92%;\n        max-width: none;\n    }\n}\n/* #endif */\n\n.user-select-container {\n\tdisplay: flex;\n\tflex-direction: column;\n\theight: 100%;\n\tmax-height: 80vh;\n}\n\n.user-select-header {\n\tpadding: 20rpx;\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #2C3E50;\n\tborder-bottom: 1px solid #EEEEEE;\n\ttext-align: center;\n}\n\n.user-search-header {\n\tpadding: 20rpx;\n\tborder-bottom: 1px solid #EEEEEE;\n\tdisplay: flex;\n\talign-items: center;\n\tbackground-color: #F9F9F9;\n}\n\n.user-search-input {\n\tflex: 1;\n\theight: 70rpx;\n\tfont-size: 28rpx;\n\tpadding: 0 10rpx;\n\tbackground-color: transparent;\n}\n\n.user-select-list {\n\tflex: 1;\n\toverflow-y: auto;\n}\n\n/* #ifdef MP-WEIXIN */\n/* 在微信小程序环境下隐藏滚动条 */\n.user-select-list::-webkit-scrollbar {\n\tdisplay: none;\n\twidth: 0;\n\theight: 0;\n\tbackground: transparent;\n}\n\n.user-select-list {\n\tscrollbar-width: none; /* Firefox */\n\t-ms-overflow-style: none; /* IE and Edge */\n}\n/* #endif */\n\n/* #ifndef MP-WEIXIN */\n/* 非微信小程序环境下显示滚动条 */\n.user-select-list::-webkit-scrollbar {\n\twidth: 6px;\n\theight: 6px;\n}\n\n.user-select-list::-webkit-scrollbar-thumb {\n\tbackground: rgba(0, 0, 0, 0.2);\n\tborder-radius: 3px;\n}\n\n.user-select-list::-webkit-scrollbar-thumb:hover {\n\tbackground: rgba(0, 0, 0, 0.3);\n}\n\n.user-select-list {\n\tscrollbar-width: thin;\n\tscrollbar-color: rgba(0, 0, 0, 0.2) transparent;\n}\n/* #endif */\n\n.user-select-item {\n\tpadding: 16rpx 20rpx;\n\tborder-bottom: 1px solid #F5F5F5;\n\tfont-size: 28rpx;\n\tcolor: #333333;\n}\n\n.user-select-item:active {\n\tbackground-color: #F9F9F9;\n}\n\n.user-item-content {\n\tdisplay: flex;\n\talign-items: center;\n\twidth: 100%;\n}\n\n.user-avatar-small {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 50%;\n\tmargin-right: 16rpx;\n\tborder: 1px solid #EEEEEE;\n}\n\n.user-item-details {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.user-item-name {\n\tfont-size: 28rpx;\n\tcolor: #333333;\n\tfont-weight: 500;\n}\n\n.user-item-role {\n\tfont-size: 24rpx;\n\tcolor: #999999;\n\tmargin-top: 4rpx;\n}\n\n.select-radio {\n\tmargin-left: 10rpx;\n}\n\n.user-select-footer {\n\tpadding: 20rpx;\n\tborder-top: 1px solid #EEEEEE;\n}\n\n.user-select-close {\n\theight: 80rpx;\n\tbackground-color: #1677FF;\n\tcolor: #FFFFFF;\n\tborder-radius: 40rpx;\n\tfont-size: 28rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\t\n\t&:after {\n\t\tborder: none;\n\t}\n}\n</style>", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./batch-add.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./batch-add.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775844984\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}