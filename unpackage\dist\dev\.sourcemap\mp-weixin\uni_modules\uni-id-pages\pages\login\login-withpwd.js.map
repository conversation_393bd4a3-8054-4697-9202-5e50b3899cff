{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/pages/login/login-withpwd.vue?4af0", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/pages/login/login-withpwd.vue?7557", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/pages/login/login-withpwd.vue?6ac3", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/pages/login/login-withpwd.vue?442e", "uni-app:///uni_modules/uni-id-pages/pages/login/login-withpwd.vue", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/pages/login/login-withpwd.vue?1736", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/pages/login/login-withpwd.vue?d075"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "errorOptions", "type", "mixins", "data", "onShow", "methods", "toRetrievePwd", "url", "uni", "pwd<PERSON><PERSON><PERSON>", "title", "icon", "duration", "uniIdCo", "toRegister", "fail", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,kRAEN;AACP,KAAK;AACL;AACA,aAAa,6VAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxDA;AAAA;AAAA;AAAA;AAAumB,CAAgB,ioBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACqE3nB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;EACAC;IACAC;EACA;AACA;AAAA,eACA;EACAC;EACAC;IACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAC,2BASA;EACAC;IACA;IACAC;MACA;MACA;MACA;QACAC;MACA;MACAC;QACAD;MACA;IACA;IACA;AACA;AACA;IACAE;MAAA;MACA;QACA;QACA;UACAC;UACAC;UACAC;QACA;MACA;MACA;QACA;QACA;UACAF;UACAC;UACAC;QACA;MACA;MACA;QACA;QACA;UACAF;UACAC;UACAC;QACA;MACA;MAEA;QACA;MACA;MAEA;QACA;QACA;MACA;MAEA;QACAT;MACA;QACAA;MACA;QACAA;MACA;MAEAU;QACA;MACA;QACA;UACA;QACA;UACA;UACA;QACA;MACA;IACA;IACA,UACAC;MACAN;QACAD,uFACA;QACAQ;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClLA;AAAA;AAAA;AAAA;AAA0qC,CAAgB,gpCAAG,EAAC,C;;;;;;;;;;;ACA9rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-id-pages/pages/login/login-withpwd.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './uni_modules/uni-id-pages/pages/login/login-withpwd.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login-withpwd.vue?vue&type=template&id=9dec0b32&scoped=true&\"\nvar renderjs\nimport script from \"./login-withpwd.vue?vue&type=script&lang=js&\"\nexport * from \"./login-withpwd.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login-withpwd.vue?vue&type=style&index=0&id=9dec0b32&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9dec0b32\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-id-pages/pages/login/login-withpwd.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login-withpwd.vue?vue&type=template&id=9dec0b32&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    uniCaptcha: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-captcha/components/uni-captcha/uni-captcha\" */ \"@/uni_modules/uni-captcha/components/uni-captcha/uni-captcha.vue\"\n      )\n    },\n    uniIdPagesAgreements: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements\" */ \"@/uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.focusUsername = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.focusPassword = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login-withpwd.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login-withpwd.vue?vue&type=script&lang=js&\"", "<!-- 账号密码登录页 -->\n<template>\n\t<view class=\"login-container\">\n\t\t<!-- 登录页顶部图形 -->\n\t\t<view class=\"login-header\">\n\t\t\t<view class=\"wave-container\">\n\t\t\t\t<view class=\"wave wave1\"></view>\n\t\t\t\t<view class=\"wave wave2\"></view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 登录内容区 -->\n\t\t<view class=\"login-content\">\n\t\t\t<!-- 应用Logo -->\n\t\t\t<view class=\"logo-container\">\n\t\t\t\t<image class=\"logo-image\" :src=\"logo\" mode=\"aspectFit\"></image>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 登录方式 -->\n\t\t\t<view class=\"login-methods\">\n\t\t\t\t<text class=\"welcome-text\">账号密码登录</text>\n\t\t\t\t\n\t\t\t\t<!-- 登录表单 -->\n\t\t\t\t<view class=\"login-form\">\n\t\t\t\t\t<!-- 用户名输入框 -->\n\t\t\t\t\t<view class=\"input-wrapper\">\n\t\t\t\t\t\t<uni-icons type=\"person\" size=\"20\" color=\"#666\"></uni-icons>\n\t\t\t\t\t\t<uni-easyinput \n\t\t\t\t\t\t\t:focus=\"focusUsername\" \n\t\t\t\t\t\t\t@blur=\"focusUsername = false\" \n\t\t\t\t\t\t\tclass=\"input-box\"\n\t\t\t\t\t\t\t:inputBorder=\"false\" \n\t\t\t\t\t\t\tv-model=\"username\" \n\t\t\t\t\t\t\tplaceholder=\"请输入用户名\" \n\t\t\t\t\t\t\ttrim=\"all\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 密码输入框 -->\n\t\t\t\t\t<view class=\"input-wrapper\">\n\t\t\t\t\t\t<uni-icons type=\"locked\" size=\"20\" color=\"#666\"></uni-icons>\n\t\t\t\t\t\t<uni-easyinput \n\t\t\t\t\t\t\t:focus=\"focusPassword\" \n\t\t\t\t\t\t\t@blur=\"focusPassword = false\" \n\t\t\t\t\t\t\tclass=\"input-box\" \n\t\t\t\t\t\t\tclearable\n\t\t\t\t\t\t\ttype=\"password\" \n\t\t\t\t\t\t\t:inputBorder=\"false\" \n\t\t\t\t\t\t\tv-model=\"password\" \n\t\t\t\t\t\t\tplaceholder=\"请输入密码\" \n\t\t\t\t\t\t\ttrim=\"all\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 验证码 -->\n\t\t\t\t\t<uni-captcha v-if=\"needCaptcha\" focus ref=\"captcha\" scene=\"login-by-pwd\" v-model=\"captcha\" />\n\t\t\t\t\t\n\t\t\t\t\t<!-- 服务协议和隐私政策 -->\n\t\t\t\t\t<view class=\"agreement-box\">\n\t\t\t\t\t\t<uni-id-pages-agreements scope=\"login\" ref=\"agreements\"></uni-id-pages-agreements>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 登录按钮 -->\n\t\t\t\t\t<button class=\"primary-btn\" @click=\"pwdLogin\">登录</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport mixin from '@/uni_modules/uni-id-pages/common/login-page.mixin.js';\n\tconst uniIdCo = uniCloud.importObject(\"uni-id-co\", {\n\t\terrorOptions: {\n\t\t\ttype: 'toast'\n\t\t}\n\t})\n\texport default {\n\t\tmixins: [mixin],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t\"password\": \"\",\n\t\t\t\t\"username\": \"\",\n\t\t\t\t\"captcha\": \"\",\n\t\t\t\t\"needCaptcha\": false,\n\t\t\t\t\"focusUsername\": false,\n\t\t\t\t\"focusPassword\": false,\n\t\t\t\t\"logo\": \"/static/brand/logo.png\"\n\t\t\t}\n\t\t},\n\t\tonShow() {\n\t\t\t// #ifdef H5\n\t\t\tdocument.onkeydown = event => {\n\t\t\t\tvar e = event || window.event;\n\t\t\t\tif (e && e.keyCode == 13) { //回车键的键值为13\n\t\t\t\t\tthis.pwdLogin()\n\t\t\t\t}\n\t\t\t};\n\t\t\t// #endif\n\t\t},\n\t\tmethods: {\n\t\t\t// 页面跳转，找回密码\n\t\t\ttoRetrievePwd() {\n\t\t\t\tlet url = '/uni_modules/uni-id-pages/pages/retrieve/retrieve'\n\t\t\t\t//如果刚好用户名输入框的值为手机号码，就把它传到retrieve页面，根据该手机号找回密码\n\t\t\t\tif (/^1\\d{10}$/.test(this.username)) {\n\t\t\t\t\turl += `?phoneNumber=${this.username}`\n\t\t\t\t}\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl\n\t\t\t\t})\n\t\t\t},\n\t\t\t/**\n\t\t\t * 密码登录\n\t\t\t */\n\t\t\tpwdLogin() {\n\t\t\t\tif (!this.password.length) {\n\t\t\t\t\tthis.focusPassword = true\n\t\t\t\t\treturn uni.showToast({\n\t\t\t\t\t\ttitle: '请输入密码',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tif (!this.username.length) {\n\t\t\t\t\tthis.focusUsername = true\n\t\t\t\t\treturn uni.showToast({\n\t\t\t\t\t\ttitle: '请输入用户名',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tif (this.needCaptcha && this.captcha.length != 4) {\n\t\t\t\t\tthis.$refs.captcha.getImageCaptcha()\n\t\t\t\t\treturn uni.showToast({\n\t\t\t\t\t\ttitle: '请输入验证码',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\tif (this.needAgreements && !this.agree) {\n\t\t\t\t\treturn this.$refs.agreements.popup(this.pwdLogin)\n\t\t\t\t}\n\n\t\t\t\tlet data = {\n\t\t\t\t\t\"password\": this.password,\n\t\t\t\t\t\"captcha\": this.captcha\n\t\t\t\t}\n\n\t\t\t\tif (/^1\\d{10}$/.test(this.username)) {\n\t\t\t\t\tdata.mobile = this.username\n\t\t\t\t} else if (/@/.test(this.username)) {\n\t\t\t\t\tdata.email = this.username\n\t\t\t\t} else {\n\t\t\t\t\tdata.username = this.username\n\t\t\t\t}\n\n\t\t\t\tuniIdCo.login(data).then(e => {\n\t\t\t\t\tthis.loginSuccess(e)\n\t\t\t\t}).catch(e => {\n\t\t\t\t\tif (e.errCode == 'uni-id-captcha-required') {\n\t\t\t\t\t\tthis.needCaptcha = true\n\t\t\t\t\t} else if (this.needCaptcha) {\n\t\t\t\t\t\t//登录失败，自动重新获取验证码\n\t\t\t\t\t\tthis.$refs.captcha.getImageCaptcha()\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\t/* 前往注册 */\n\t\t\ttoRegister() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: this.config.isAdmin ? '/uni_modules/uni-id-pages/pages/register/register-admin' :\n\t\t\t\t\t\t'/uni_modules/uni-id-pages/pages/register/register',\n\t\t\t\t\tfail(e) {\n\t\t\t\t\t\tconsole.error(e);\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.login-container {\n\t\tmin-height: 100vh;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tbackground-color: #f8f9fa;\n\t}\n\t\n\t/* 顶部波浪效果 */\n\t.login-header {\n\t\tposition: relative;\n\t\theight: 220px;\n\t\toverflow: hidden;\n\t}\n\t\n\t.wave-container {\n\t\tposition: absolute;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: linear-gradient(135deg, #3688FF, #5DABFF);\n\t}\n\t\n\t.wave {\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\twidth: 100%;\n\t\theight: 100px;\n\t\tbackground: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 800 88.7'%3E%3Cpath d='M800 56.9c-155.5 0-204.9-50-405.5-49.9-200 0-250 49.9-394.5 49.9v31.8h800v-31.8z' fill='%23ffffff'/%3E%3C/svg%3E\");\n\t\tbackground-position: center;\n\t\tbackground-repeat: repeat-x;\n\t\tbackground-size: 800px 100px;\n\t}\n\t\n\t.wave1 {\n\t\tanimation: wave 15s -3s linear infinite;\n\t\topacity: 1;\n\t\tz-index: 1;\n\t\tbottom: 0;\n\t}\n\t\n\t.wave2 {\n\t\tanimation: wave2 8s linear reverse infinite;\n\t\topacity: 0.5;\n\t\tz-index: 0;\n\t\tbottom: 10px;\n\t}\n\t\n\t@keyframes wave {\n\t\t0% {\n\t\t\tbackground-position-x: 0;\n\t\t}\n\t\t100% {\n\t\t\tbackground-position-x: 800px;\n\t\t}\n\t}\n\t\n\t@keyframes wave2 {\n\t\t0% {\n\t\t\tbackground-position-x: 0;\n\t\t}\n\t\t100% {\n\t\t\tbackground-position-x: 800px;\n\t\t}\n\t}\n\t\n\t/* 登录内容区 */\n\t.login-content {\n\t\tflex: 1;\n\t\tpadding: 0 30px;\n\t\tmargin-top: -50px;\n\t\tz-index: 2;\n\t}\n\t\n\t/* Logo区域 */\n\t.logo-container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tmargin-bottom: 40px;\n\t}\n\t\n\t.logo-image {\n\t\twidth: 100px;\n\t\theight: 100px;\n\t\tborder-radius: 10px;\n\t\tbox-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\n\t}\n\t\n\t.app-name {\n\t\tfont-size: 24px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-top: 15px;\n\t}\n\t\n\t/* 登录方式区域 */\n\t.login-methods {\n\t\tbackground-color: #fff;\n\t\tborder-radius: 16px;\n\t\tpadding: 30px;\n\t\tbox-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.welcome-text {\n\t\tfont-size: 22px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 30px;\n\t\tdisplay: block;\n\t\ttext-align: center;\n\t}\n\t\n\t/* 登录表单 */\n\t.login-form {\n\t\tmargin-bottom: 20px;\n\t}\n\t\n\t/* 输入框样式 */\n\t.input-wrapper {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\n\t\tborder-radius: 25px;\n\t\tpadding: 5px 15px;\n\t\tmargin-bottom: 20px;\n\t\tborder: 1px solid #e2e8f0;\n\t}\n\t\n\t.input-box {\n\t\tflex: 1;\n\t\tpadding-left: 10px;\n\t\t\n\t\t:deep(.uni-easyinput__content) {\n\t\t\tbackground-color: transparent !important;\n\t\t\theight: 40px;\n\t\t}\n\t\t\n\t\t:deep(.uni-easyinput__placeholder-class) {\n\t\t\tfont-size: 14px;\n\t\t}\n\t}\n\t\n\t/* 协议样式 */\n\t.agreement-box {\n\t\tmargin: 20px 0;\n\t}\n\t\n\t/* 主按钮样式 */\n\t.primary-btn {\n\t\tbackground-color: #3688FF;\n\t\tcolor: #fff;\n\t\theight: 50px;\n\t\tborder-radius: 25px;\n\t\tfont-size: 16px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-top: 10px;\n\t\tbox-shadow: 0 4px 10px rgba(54, 136, 255, 0.3);\n\t\tborder: none;\n\t}\n\t\n\t/* 其他登录方式 */\n\t.other-login-methods {\n\t\tmargin-top: 30px;\n\t}\n\t\n\t.divider {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tmargin-bottom: 20px;\n\t}\n\t\n\t.line {\n\t\tflex: 1;\n\t\theight: 1px;\n\t\tbackground-color: #e2e8f0;\n\t}\n\t\n\t.divider-text {\n\t\tpadding: 0 15px;\n\t\tfont-size: 14px;\n\t\tcolor: #999;\n\t}\n\t\n\t/* 底部版权信息 */\n\t.footer {\n\t\tpadding: 20px 0;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t}\n\t\n\t.copyright {\n\t\tfont-size: 12px;\n\t\tcolor: #999;\n\t}\n\t\n\t/* 响应式调整 */\n\t@media screen and (max-width: 375px) {\n\t\t.login-content {\n\t\t\tpadding: 0 20px;\n\t\t}\n\t\t\n\t\t.login-methods {\n\t\t\tpadding: 20px;\n\t\t}\n\t\t\n\t\t.logo-image {\n\t\t\twidth: 80px;\n\t\t\theight: 80px;\n\t\t}\n\t\t\n\t\t.app-name {\n\t\t\tfont-size: 20px;\n\t\t}\n\t\t\n\t\t.welcome-text {\n\t\t\tfont-size: 20px;\n\t\t}\n\t}\n</style>\n", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login-withpwd.vue?vue&type=style&index=0&id=9dec0b32&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login-withpwd.vue?vue&type=style&index=0&id=9dec0b32&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775852426\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}