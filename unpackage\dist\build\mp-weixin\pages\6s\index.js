(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s/index"],{"0996":function(e,t,n){"use strict";var a=n("cb25"),r=n.n(a);r.a},2845:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return a}));var a={uniIcons:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(n.bind(null,"6ddf"))}},r=function(){var e=this.$createElement,t=(this._self._c,this.activitiesLoading?null:this.recentActivities.length),n=this.remindersLoading?null:this.smartReminders.length;this.$mp.data=Object.assign({},{$root:{g0:t,g1:n}})},i=[]},"29f8":function(e,t,n){"use strict";n.r(t);var a=n("2845"),r=n("a191");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("0996");var c=n("828b"),o=Object(c["a"])(r["default"],a["b"],a["c"],!1,null,"339109bc",null,!1,a["a"],void 0);t["default"]=o.exports},"390a":function(e,t,n){"use strict";(function(e,a){var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(n("7eb4")),c=r(n("ee10")),o=r(n("7ca3")),s=n("882c");function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){(0,o.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var l={components:{},data:function(){return{currentTab:0,tabs:[{name:"6S首页",key:"home"}],selectedTimeFilter:"week",loading:!1,dataLoaded:!1,loadError:!1,activitiesLoading:!0,remindersLoading:!0,processCache:null,timeCache:null,stats:{totalAreas:0,completedAreas:0,foundIssues:0,averageRating:0,completionRate:0,urgentIssues:0},recentActivities:[],smartReminders:[]}},computed:{userInfo:function(){try{var t=e.getCurrentUserInfo(),n=a.getStorageSync("uni-id-pages-userInfo")||{};return d(d({},n),t)}catch(r){return console.error("获取用户信息失败:",r),a.getStorageSync("uni-id-pages-userInfo")||{}}},hasInspectionPermission:function(){var e=this.userInfo.role;return["supervisor","PM","GM","admin"].includes(e)},currentTimeRangeText:function(){if(!this.timeCache)return"本周";var e=this.timeCache,t=e.currentYear,n=e.currentMonth,a=e.currentWeekRange;switch(this.selectedTimeFilter){case"today":return"".concat(t,"年").concat(n,"月").concat((new Date).getDate(),"日");case"week":return"".concat(a.start," - ").concat(a.end);case"month":return"".concat(t,"年").concat(n,"月");default:return"".concat(a.start," - ").concat(a.end)}}},onLoad:function(){this.initCaches(),this.loadPageDataOptimized(),a.$on("cleaningRecordUpdated",this.handleDataUpdated),a.$on("rectificationRecordUpdated",this.handleDataUpdated),a.$on("inspectionRecordUpdated",this.handleDataUpdated)},onUnload:function(){a.$off("cleaningRecordUpdated",this.handleDataUpdated),a.$off("rectificationRecordUpdated",this.handleDataUpdated),a.$off("inspectionRecordUpdated",this.handleDataUpdated)},onShow:function(){this.dataLoaded&&!this.loading&&this.silentRefreshData()},methods:{initCaches:function(){this.initProcessCache(),this.initTimeCache()},initProcessCache:function(){this.processCache||(this.processCache={activityTypeMap:{cleaning_completed:{type:"success",iconType:"checkmarkempty",iconColor:"#34C759"},issue_found:{type:"warning",iconType:"info",iconColor:"#FF9500"},inspection_completed:{type:"info",iconType:"search",iconColor:"#007AFF"},rectification_completed:{type:"success",iconType:"checkmarkempty",iconColor:"#34C759"},area_overdue:{type:"danger",iconType:"closeempty",iconColor:"#FF3B30"}},reminderTypeMap:{pending_cleaning:{type:"info",iconSymbol:"i",title:"清理提醒"},overdue_cleaning:{type:"warning",iconSymbol:"⚠",title:"清理逾期"},pending_rectification:{type:"warning",iconSymbol:"!",title:"整改提醒"},overdue_rectification:{type:"danger",iconSymbol:"⚠",title:"整改逾期"},inspection_due:{type:"info",iconSymbol:"○",title:"检查提醒"},missed_inspection:{type:"danger",iconSymbol:"⚠",title:"漏检提醒"},overdue_review:{type:"danger",iconSymbol:"!",title:"复查逾期"},not_cleaned:{type:"warning",iconSymbol:"●",title:"未打扫提醒"},all_good:{type:"success",iconSymbol:"✓",title:"状态良好"},no_areas:{type:"info",iconSymbol:"?",title:"系统提示"}},timeFormatter:this.createTimeFormatter()})},initTimeCache:function(){if(!this.timeCache){var e=new Date;this.timeCache={now:e,currentYear:e.getFullYear(),currentMonth:e.getMonth()+1,currentWeekRange:this.getCurrentWeekRange(e),lastUpdateTime:e.getTime()}}},createTimeFormatter:function(){return function(e){if(!e)return"--";try{var t=new Date(e);if(isNaN(t.getTime()))return"--";var n=new Date,a=Math.floor((n-t)/36e5);if(a<1){var r=Math.floor((n-t)/6e4);return"".concat(r,"分钟前")}if(a<24)return"".concat(a,"小时前");var i=Math.floor(a/24);return 1===i?"昨天":i<7?"".concat(i,"天前"):"".concat(t.getMonth()+1,"月").concat(t.getDate(),"日")}catch(c){return"--"}}},getCurrentWeekRange:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date,t=new Date(e),n=t.getDay(),a=t.getDate()-n+(0===n?-6:1),r=new Date(t);r.setDate(a);var i=new Date(t);return i.setDate(a+6),{start:"".concat(r.getMonth()+1,"月").concat(r.getDate(),"日"),end:"".concat(i.getMonth()+1,"月").concat(i.getDate(),"日"),monday:r,sunday:i}},loadPageDataOptimized:function(){var e=this;return(0,c.default)(i.default.mark((function t(){return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,e.loadError=!1,t.prev=2,t.next=5,e.loadStatsDataOptimized();case 5:e.loading=!1,e.dataLoaded=!0,e.loadSecondaryDataAsync(),t.next=15;break;case 10:t.prev=10,t.t0=t["catch"](2),e.loading=!1,e.loadError=!0,e.handleLoadError(t.t0);case 15:case"end":return t.stop()}}),t,null,[[2,10]])})))()},loadSecondaryDataAsync:function(){var e=this;return(0,c.default)(i.default.mark((function t(){return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Promise.all([e.loadRecentActivitiesOptimized(),e.loadSmartRemindersOptimized()]);case 3:t.next=7;break;case 5:t.prev=5,t.t0=t["catch"](0);case 7:return t.prev=7,e.activitiesLoading=!1,e.remindersLoading=!1,t.finish(7);case 11:case"end":return t.stop()}}),t,null,[[0,5,7,11]])})))()},loadStatsDataOptimized:function(){var e=this;return(0,c.default)(i.default.mark((function t(){var n;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,s.callCloudFunction)("hygiene-area-management",{action:"getOverviewStats",data:{timeFilter:e.selectedTimeFilter,includeDetails:!1}});case 3:if(n=t.sent,!(n&&n.success&&n.data)){t.next=8;break}e.stats={totalAreas:n.data.totalAreas||0,completedAreas:n.data.completedAreas||0,foundIssues:n.data.pendingRectifications||0,averageRating:n.data.averageRating||0,completionRate:n.data.completionRate||0,urgentIssues:n.data.overdueAreas||0},t.next=9;break;case 8:throw new Error("获取统计数据失败");case 9:t.next=15;break;case 11:throw t.prev=11,t.t0=t["catch"](0),e.stats={totalAreas:0,completedAreas:0,foundIssues:0,averageRating:0,completionRate:0,urgentIssues:0},t.t0;case 15:case"end":return t.stop()}}),t,null,[[0,11]])})))()},loadRecentActivitiesOptimized:function(){var e=this;return(0,c.default)(i.default.mark((function t(){var n;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,s.callCloudFunction)("hygiene-area-management",{action:"getRecentActivities",data:{limit:5,timeFilter:"week"}});case 3:n=t.sent,n&&n.success&&n.data&&(e.recentActivities=e.processActivitiesData(n.data)),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0),e.recentActivities=[];case 10:case"end":return t.stop()}}),t,null,[[0,7]])})))()},loadSmartRemindersOptimized:function(){var e=this;return(0,c.default)(i.default.mark((function t(){var n,a;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,n=e.userInfo.role||"employee",t.next=4,(0,s.callCloudFunction)("hygiene-area-management",{action:"getSmartReminders",data:{userRole:n,limit:3}});case 4:a=t.sent,a&&a.success&&a.data&&(e.smartReminders=e.processRemindersData(a.data)),t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](0),e.smartReminders=[];case 11:case"end":return t.stop()}}),t,null,[[0,8]])})))()},processActivitiesData:function(e){var t=this;return Array.isArray(e)?e.map((function(e){var n=t.processCache.activityTypeMap[e.type]||{type:"info",iconType:"info",iconColor:"#8E8E93"};return d(d({},n),{},{title:e.title||"未知活动",time:t.processCache.timeFormatter(e.created_at)||"--"})})):[]},processRemindersData:function(e){var t=this;return Array.isArray(e)?e.map((function(e){var n=t.processCache.reminderTypeMap[e.type]||{type:"info",iconSymbol:"i",title:"系统提醒"};return d(d({},n),{},{description:e.description||"暂无详细信息"})})):[]},silentRefreshData:function(){var e=this;return(0,c.default)(i.default.mark((function t(){return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.loading){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,e.timeCache=null,e.initTimeCache(),t.next=7,e.loadStatsDataOptimized();case 7:e.loadSecondaryDataAsync(),t.next=12;break;case 10:t.prev=10,t.t0=t["catch"](2);case 12:case"end":return t.stop()}}),t,null,[[2,10]])})))()},handleLoadError:function(e){a.showToast({title:"数据加载失败",icon:"none",duration:2e3})},switchTimeFilter:function(e){var t=this;this.selectedTimeFilter!==e&&(this.selectedTimeFilter=e,this.activitiesLoading=!0,this.remindersLoading=!0,this.loadStatsDataOptimized().catch((function(e){t.handleLoadError(e)})),this.loadSecondaryDataAsync())},getTimeRangeTitle:function(){switch(this.selectedTimeFilter){case"today":return"今日";case"week":return"本周";case"month":return"本月";default:return"本周"}},handleDataUpdated:function(e){this.silentRefreshData()},goToMyArea:function(){a.navigateTo({url:"/pages/6s_pkg/my-areas"})},goToPendingTasks:function(){a.navigateTo({url:"/pages/6s_pkg/area-inspection"})},uploadPhoto:function(){a.navigateTo({url:"/pages/6s_pkg/monthly-check"})},manageBaseData:function(){a.navigateTo({url:"/pages/6s_pkg/data-manage"})}}};t.default=l}).call(this,n("861b")["uniCloud"],n("df3c")["default"])},a191:function(e,t,n){"use strict";n.r(t);var a=n("390a"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},cb25:function(e,t,n){},fbdb:function(e,t,n){"use strict";(function(e,t){var a=n("47a9");n("357b"),n("861b");a(n("3240"));var r=a(n("29f8"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(r.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])}},[["fbdb","common/runtime","common/vendor"]]]);