'use strict';

const db = uniCloud.database();
const dbCmd = db.command;
const uniIdCommon = require('uni-id-common');

exports.main = async (event, context) => {
  const { action, data } = event;
  
  // 使用 uni-id-common 进行用户认证
  const uniIdInstance = uniIdCommon.createInstance({
    context
  });
  
  const payload = await uniIdInstance.checkToken(event.uniIdToken);
  if (payload.code !== 0) {
    return {
      success: false,
      message: '用户未登录或token无效'
    };
  }
  
  const { uid, role } = payload;
  console.log('当前用户:', uid, '角色:', role);
  
  try {
    switch (action) {
      case 'createRectification':
        return await createRectification(data, uid, role);
      case 'updateRectification':
        return await updateRectification(data, uid, role);
      case 'deleteRectification':
        return await deleteRectification(data, uid, role);
      case 'getRectifications':
        return await getRectifications(data, uid, role);
      case 'getMyRectifications':
        return await getMyRectifications(data, uid);
      case 'getRectificationDetail':
        return await getRectificationDetail(data, uid, role);
      case 'updateProgress':
        return await updateProgress(data, uid, role);
      case 'completeRectification':
        return await completeRectification(data, uid, role);
      case 'reviewRectification':
        return await reviewRectification(data, uid, role);
      case 'getOverdueTasks':
        return await getOverdueTasks(data, uid, role);
      default:
        return {
          success: false,
          message: '未知的操作类型'
        };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return {
      success: false,
      message: '服务器错误',
      error: error.message
    };
  }
};

// 创建整改任务
async function createRectification(data, uid, role) {
  const {
    issue_id,
    area_id,
    title,
    description,
    assigned_to,
    deadline,
    priority = 'normal',
    estimated_hours
  } = data;
  
  // 验证必填字段
  if (!assigned_to || !deadline) {
    return {
      success: false,
      message: '负责人和整改期限不能为空'
    };
  }
  
  // 权限检查：只有检查员或管理员可以创建整改任务
  if (!role.includes('admin') && !role.includes('inspector') && !role.includes('manager')) {
    return {
      success: false,
      message: '权限不足，只有检查员或管理员可以创建整改任务'
    };
  }
  
  // 获取负责人信息
  const userResult = await db.collection('uni-id-users').doc(assigned_to).get();
  if (!userResult.data.length) {
    return {
      success: false,
      message: '指定的负责人不存在'
    };
  }
  
  const assignedUser = userResult.data[0];
  
  // 获取分配人信息
  const assignerResult = await db.collection('uni-id-users').doc(uid).get();
  const assignerName = assignerResult.data.length > 0 ? assignerResult.data[0].nickname || assignerResult.data[0].username : '';
  
  let rectificationData = {
    assigned_to,
    assigned_to_name: assignedUser.nickname || assignedUser.username,
    assigned_by: uid,
    assigned_by_name: assignerName,
    deadline: new Date(deadline),
    priority,
    status: 'pending',
    progress_percentage: 0,
    created_at: new Date(),
    updated_at: new Date()
  };
  
  // 可选字段
  if (issue_id) rectificationData.issue_id = issue_id;
  if (area_id) rectificationData.area_id = area_id;
  if (title) rectificationData.title = title;
  if (description) rectificationData.description = description;
  if (estimated_hours) rectificationData.estimated_hours = estimated_hours;
  
  // 如果有问题ID，获取问题信息
  if (issue_id) {
    const issueResult = await db.collection('hygiene-issues').doc(issue_id).get();
    if (issueResult.data.length > 0) {
      const issue = issueResult.data[0];
      if (!area_id) rectificationData.area_id = issue.area_id;
      if (!title) rectificationData.title = `整改任务：${issue.title}`;
      if (!description) rectificationData.description = issue.description;
      rectificationData.area_name = issue.area_name;
    }
  }
  
  // 如果有责任区ID但没有名称，获取责任区信息
  if (rectificationData.area_id && !rectificationData.area_name) {
    const areaResult = await db.collection('hygiene-areas').doc(rectificationData.area_id).get();
    if (areaResult.data.length > 0) {
      rectificationData.area_name = areaResult.data[0].name;
    }
  }
  
  const result = await db.collection('hygiene-rectification-records').add(rectificationData);
  
  return {
    success: true,
    message: '整改任务创建成功',
    data: {
      _id: result.id,
      ...rectificationData
    }
  };
}

// 更新整改任务
async function updateRectification(data, uid, role) {
  const {
    id,
    title,
    description,
    deadline,
    priority,
    estimated_hours,
    assigned_to
  } = data;
  
  if (!id) {
    return {
      success: false,
      message: '整改任务ID不能为空'
    };
  }
  
  // 获取整改记录
  const recordResult = await db.collection('hygiene-rectification-records').doc(id).get();
  if (!recordResult.data.length) {
    return {
      success: false,
      message: '整改任务不存在'
    };
  }
  
  const record = recordResult.data[0];
  
  // 权限检查：任务创建者、负责人或管理员可以修改
  if (!role.includes('admin') && !role.includes('manager') && 
      record.assigned_by !== uid && record.assigned_to !== uid) {
    return {
      success: false,
      message: '权限不足'
    };
  }
  
  const updateData = {
    updated_at: new Date()
  };
  
  if (title !== undefined) updateData.title = title;
  if (description !== undefined) updateData.description = description;
  if (priority !== undefined) updateData.priority = priority;
  if (estimated_hours !== undefined) updateData.estimated_hours = estimated_hours;
  if (deadline !== undefined) updateData.deadline = new Date(deadline);
  
  // 如果更换负责人
  if (assigned_to && assigned_to !== record.assigned_to) {
    const userResult = await db.collection('uni-id-users').doc(assigned_to).get();
    if (!userResult.data.length) {
      return {
        success: false,
        message: '指定的负责人不存在'
      };
    }
    const assignedUser = userResult.data[0];
    updateData.assigned_to = assigned_to;
    updateData.assigned_to_name = assignedUser.nickname || assignedUser.username;
  }
  
  await db.collection('hygiene-rectification-records').doc(id).update(updateData);
  
  return {
    success: true,
    message: '整改任务更新成功'
  };
}

// 删除整改任务
async function deleteRectification(data, uid, role) {
  const { id } = data;
  
  if (!id) {
    return {
      success: false,
      message: '整改任务ID不能为空'
    };
  }
  
  // 权限检查：只有管理员可以删除整改任务
  if (!role.includes('admin')) {
    return {
      success: false,
      message: '只有管理员可以删除整改任务'
    };
  }
  
  await db.collection('hygiene-rectification-records').doc(id).remove();
  
  return {
    success: true,
    message: '整改任务删除成功'
  };
}

// 获取整改任务列表
async function getRectifications(data, uid, role) {
  const {
    issue_id,
    area_id,
    inspection_record_id,
    assigned_to,
    assigned_by,
    status,
    priority,
    start_date,
    end_date,
    page = 1,
    pageSize = 20,
    overdue_only = false
  } = data;
  
  let whereCondition = {};
  
  // 问题ID筛选
  if (issue_id) {
    whereCondition.issue_id = issue_id;
  }
  
  // 责任区筛选
  if (area_id) {
    whereCondition.area_id = area_id;
  }
  
  // 检查记录ID筛选
  if (inspection_record_id) {
    whereCondition.inspection_record_id = inspection_record_id;
  }
  
  // 负责人筛选
  if (assigned_to) {
    whereCondition.assigned_to = assigned_to;
  }
  
  // 分配人筛选
  if (assigned_by) {
    whereCondition.assigned_by = assigned_by;
  }
  
  // 状态筛选
  if (status) {
    whereCondition.status = status;
  }
  
  // 优先级筛选
  if (priority) {
    whereCondition.priority = priority;
  }
  
  // 只显示逾期任务
  if (overdue_only) {
    whereCondition.deadline = dbCmd.lt(new Date());
    whereCondition.status = dbCmd.in(['pending', 'in_progress']);
  }
  
  // 日期范围筛选
  if (start_date && end_date) {
    whereCondition.created_at = dbCmd.gte(new Date(start_date)).and(dbCmd.lte(new Date(end_date)));
  } else if (start_date) {
    whereCondition.created_at = dbCmd.gte(new Date(start_date));
  } else if (end_date) {
    whereCondition.created_at = dbCmd.lte(new Date(end_date));
  }
  
  const skip = (page - 1) * pageSize;
  
  const [listResult, countResult] = await Promise.all([
    db.collection('hygiene-rectification-records')
      .where(whereCondition)
      .orderBy('created_at', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get(),
    db.collection('hygiene-rectification-records')
      .where(whereCondition)
      .count()
  ]);
  
  // 检查逾期状态并更新
  const now = new Date();
  for (const record of listResult.data) {
    if (record.deadline < now && (record.status === 'pending' || record.status === 'in_progress')) {
      record.is_overdue = true;
      // 更新数据库中的逾期状态
      await db.collection('hygiene-rectification-records').doc(record._id).update({
        status: 'overdue',
        updated_at: new Date()
      });
      record.status = 'overdue';
    }
  }
  
  return {
    success: true,
    data: {
      list: listResult.data,
      total: countResult.total,
      page,
      pageSize
    }
  };
}

// 获取我的整改任务
async function getMyRectifications(data, uid) {
  const { status, limit = 10, timeFilter } = data;
  
  // 直接查询分配给当前用户的整改任务
  let whereCondition = {
    assigned_to: uid  // 直接查询分配给当前用户的任务
  };
  
  if (status) {
    whereCondition.status = status;
  }
  
  const result = await db.collection('hygiene-rectification-records')
    .where(whereCondition)
    .orderBy('created_at', 'desc')
    .limit(limit)
    .get();
  
  // 检查逾期状态并获取责任区类型信息
  const now = new Date();
  for (const record of result.data) {
    if (record.deadline < now && (record.status === 'pending' || record.status === 'in_progress')) {
      record.is_overdue = true;
    }
    
    // 获取责任区类型信息
    if (record.area_id) {
      try {
        const areaResult = await db.collection('hygiene-areas').doc(record.area_id).get();
        if (areaResult.data && areaResult.data.length > 0) {
          record.area_type = areaResult.data[0].type || 'fixed';
        }
      } catch (error) {
        // 静默处理错误，使用默认值
        record.area_type = 'fixed';
      }
    }
  }
  
  return {
    success: true,
    data: result.data
  };
}

// 获取整改任务详情
async function getRectificationDetail(data, uid, role) {
  const { id } = data;
  
  if (!id) {
    return {
      success: false,
      message: '整改任务ID不能为空'
    };
  }
  
  const recordResult = await db.collection('hygiene-rectification-records').doc(id).get();
  if (!recordResult.data.length) {
    return {
      success: false,
      message: '整改任务不存在'
    };
  }
  
  const record = recordResult.data[0];
  
  // 获取相关问题信息
  let issueInfo = null;
  if (record.issue_id) {
    const issueResult = await db.collection('hygiene-issues').doc(record.issue_id).get();
    if (issueResult.data.length > 0) {
      issueInfo = issueResult.data[0];
    }
  }
  
  // 获取相关检查记录信息（如果有inspection_record_id）
  let inspectionInfo = null;
  if (record.inspection_record_id) {
    try {
      const inspectionResult = await db.collection('hygiene-inspection-records').doc(record.inspection_record_id).get();
      if (inspectionResult.data.length > 0) {
        const inspectionRecord = inspectionResult.data[0];
        inspectionInfo = {
          inspector_id: inspectionRecord.inspector_id,
          inspector_name: inspectionRecord.inspector_name,
          // 包含检查员上传的照片，用于在整改页面显示
          inspection_photos: inspectionRecord.photos || []
        };
      }
    } catch (error) {
      console.error('查询检查记录失败:', error);
    }
  }
  
  // 获取责任区负责人信息
  let areaInfo = null;
  if (record.area_id) {
    try {
      const areaResult = await db.collection('hygiene-areas').doc(record.area_id).get();
      if (areaResult.data.length > 0) {
        const area = areaResult.data[0];
        
        // 如果整改记录中已经有分配的负责人，优先使用
        if (record.assigned_to_name) {
          areaInfo = {
            area_assignee_name: record.assigned_to_name
          };
        }
        // 如果是固定责任区，从责任区分配用户中获取
        else if (area.type === 'fixed' && area.assigned_users && area.assigned_users.length > 0) {
          const userResult = await db.collection('uni-id-users').doc(area.assigned_users[0]).get();
          if (userResult.data.length > 0) {
            const user = userResult.data[0];
            areaInfo = {
              area_assignee_name: user.nickname || user.username || user.mobile || '未知用户'
            };
          }
        }
        // 如果是公共责任区，从清理记录中查找实际清理人员
        else if (area.type === 'public') {
          // 查找与整改记录时间相近的清理记录，确定实际负责人
          const createdDate = new Date(record.created_at);
          const startDate = new Date(createdDate.getTime() - 7 * 24 * 60 * 60 * 1000); // 前7天
          const endDate = new Date(createdDate.getTime() + 1 * 24 * 60 * 60 * 1000);   // 后1天
          
          const cleaningResult = await db.collection('hygiene-cleaning-records')
            .where({
              area_id: record.area_id,
              cleaning_date: db.command.gte(startDate).and(db.command.lte(endDate))
            })
            .orderBy('cleaning_date', 'desc')
            .limit(1)
            .get();
          
          if (cleaningResult.data.length > 0) {
            const cleaningRecord = cleaningResult.data[0];
            areaInfo = {
              area_assignee_name: cleaningRecord.user_name || '未知用户'
            };
          }
        }
      }
    } catch (error) {
      console.error('查询责任区信息失败:', error);
    }
  }
  
  return {
    success: true,
    data: {
      ...record,
      ...inspectionInfo,
      ...areaInfo,
      issue: issueInfo
    }
  };
}

// 更新整改进度
async function updateProgress(data, uid, role) {
  const { id, progress_percentage, description, photos = [] } = data;
  
  if (!id) {
    return {
      success: false,
      message: '整改任务ID不能为空'
    };
  }
  
  // 获取整改记录
  const recordResult = await db.collection('hygiene-rectification-records').doc(id).get();
  if (!recordResult.data.length) {
    return {
      success: false,
      message: '整改任务不存在'
    };
  }
  
  const record = recordResult.data[0];
  
  // 权限检查：只有任务负责人可以更新进度
  if (record.assigned_to !== uid) {
    return {
      success: false,
      message: '只有任务负责人可以更新进度'
    };
  }
  
  const updateData = {
    updated_at: new Date()
  };
  
  if (progress_percentage !== undefined) {
    updateData.progress_percentage = progress_percentage;
    
    // 根据进度自动更新状态
    if (progress_percentage === 100) {
      updateData.status = 'completed';
      updateData.completion_date = new Date();
    } else if (progress_percentage > 0) {
      updateData.status = 'in_progress';
      if (!record.start_date) {
        updateData.start_date = new Date();
      }
    }
  }
  
  // 添加进度更新记录
  const existingUpdates = record.progress_updates || [];
  const newUpdate = {
    timestamp: new Date(),
    progress: progress_percentage || record.progress_percentage,
    description: description || '',
    photos: photos
  };
  existingUpdates.push(newUpdate);
  updateData.progress_updates = existingUpdates;
  
  await db.collection('hygiene-rectification-records').doc(id).update(updateData);
  
  return {
    success: true,
    message: '进度更新成功'
  };
}

// 完成整改任务
async function completeRectification(data, uid, role) {
  const { id, completion_description, completion_photos = [] } = data;
  
  if (!id) {
    return {
      success: false,
      message: '整改任务ID不能为空'
    };
  }
  
  // 获取整改记录
  const recordResult = await db.collection('hygiene-rectification-records').doc(id).get();
  if (!recordResult.data.length) {
    return {
      success: false,
      message: '整改任务不存在'
    };
  }
  
  const record = recordResult.data[0];
  
  // 权限检查：只有任务负责人可以完成任务
  if (record.assigned_to !== uid) {
    return {
      success: false,
      message: '只有任务负责人可以完成任务'
    };
  }
  
  const updateData = {
    status: 'pending_review',  // 提交后等待检查员审核
    progress_percentage: 100,
    submitted_at: new Date(),  // 添加提交时间字段
    completion_description,
    completion_photos: completion_photos.map(photo => ({
      url: photo.url || photo,
      description: photo.description || '',
      timestamp: new Date()
    })),
    updated_at: new Date()
  };
  
  if (!record.start_date) {
    updateData.start_date = new Date();
  }
  
  // 更新整改任务状态
  await db.collection('hygiene-rectification-records').doc(id).update(updateData);
  
  // 🔄 同步更新相关检查记录的状态
  if (record.inspection_record_id) {
    await db.collection('hygiene-inspection-records').doc(record.inspection_record_id).update({
      status: 'rectification_completed', // 整改已完成，等待验收
      updated_at: new Date()
    });
    console.log('整改提交完成，同步更新检查记录状态为: rectification_completed');
  }
  
  // 如果有关联的问题，更新问题状态为已解决
  if (record.issue_id) {
    await db.collection('hygiene-issues').doc(record.issue_id).update({
      status: 'resolved',
      actual_completion_date: new Date(),
      updated_at: new Date()
    });
  }
  
  return {
    success: true,
    message: '整改任务完成成功'
  };
}

// 审核整改结果
async function reviewRectification(data, uid, role) {
  const { id, review_result, review_comments, final_rating, review_rating, review_photos = [] } = data;
  
  if (!id || !review_result) {
    return {
      success: false,
      message: '整改任务ID和审核结果不能为空'
    };
  }
  
  // 如果审核通过，评分是必填的
  if (review_result === 'approved' && !final_rating) {
    return {
      success: false,
      message: '审核通过时必须提供评分'
    };
  }
  
  // 权限检查：只有管理员或检查员可以审核
  if (!role.includes('admin') && !role.includes('inspector') && !role.includes('manager')) {
    return {
      success: false,
      message: '权限不足，只有管理员或检查员可以审核整改结果'
    };
  }
  
  const recordResult = await db.collection('hygiene-rectification-records').doc(id).get();
  if (!recordResult.data.length) {
    return {
      success: false,
      message: '整改任务不存在'
    };
  }
  
  const updateData = {
    reviewer_id: uid,
    review_date: new Date(),
    review_result,
    review_comments,
    updated_at: new Date()
  };
  
  // 处理评分字段
  if (final_rating !== undefined) updateData.final_rating = final_rating;
  if (review_rating !== undefined) updateData.review_rating = review_rating;
  
  // 处理审核照片
  if (review_photos && review_photos.length > 0) {
    updateData.review_photos = review_photos.map(photo => ({
      url: photo.url || photo,
      description: photo.description || '',
      timestamp: new Date()
    }));
  }
  
  // 根据审核结果更新状态
  if (review_result === 'approved') {
    updateData.status = 'verified';
  } else if (review_result === 'rejected') {
    updateData.status = 'pending_rectification'; // 审核不通过，重新回到待整改状态
    updateData.progress_percentage = 0; // 重置进度
    updateData.submitted_at = null; // 清除提交时间，允许重新提交
  }
  
  // 获取审核人信息
  const userResult = await db.collection('uni-id-users').doc(uid).get();
  if (userResult.data.length > 0) {
    updateData.reviewer_name = userResult.data[0].nickname || userResult.data[0].username;
  }
  
  // 更新整改任务状态
  await db.collection('hygiene-rectification-records').doc(id).update(updateData);
  
  // 🔄 关键：同步更新相关检查记录的状态
  const record = recordResult.data[0];
  if (record.inspection_record_id) {
    let inspectionStatus = '';
    if (review_result === 'approved') {
      inspectionStatus = 'verified'; // 整改审核通过，检查记录变为已验收
    } else if (review_result === 'rejected') {
      inspectionStatus = 'pending_rectification'; // 整改被拒绝，检查记录保持待整改
    }
    
    if (inspectionStatus) {
      await db.collection('hygiene-inspection-records').doc(record.inspection_record_id).update({
        status: inspectionStatus,
        updated_at: new Date()
      });
      console.log(`同步更新检查记录状态为: ${inspectionStatus}`);
    }
  }
  
  return {
    success: true,
    message: '整改结果审核成功'
  };
}

// 获取逾期任务
async function getOverdueTasks(data, uid, role) {
  const { assigned_to, area_id } = data;
  
  const now = new Date();
  let whereCondition = {
    deadline: dbCmd.lt(now),
    status: dbCmd.in(['pending', 'in_progress'])
  };
  
  if (assigned_to) {
    whereCondition.assigned_to = assigned_to;
  }
  
  if (area_id) {
    whereCondition.area_id = area_id;
  }
  
  const result = await db.collection('hygiene-rectification-records')
    .where(whereCondition)
    .orderBy('deadline', 'asc')
    .get();
  
  // 批量更新逾期状态
  const updatePromises = result.data.map(record => {
    return db.collection('hygiene-rectification-records').doc(record._id).update({
      status: 'overdue',
      updated_at: new Date()
    });
  });
  
  if (updatePromises.length > 0) {
    await Promise.all(updatePromises);
  }
  
  // 更新返回数据的状态
  result.data.forEach(record => {
    record.status = 'overdue';
    record.is_overdue = true;
  });
  
  return {
    success: true,
    data: result.data
  };
} 