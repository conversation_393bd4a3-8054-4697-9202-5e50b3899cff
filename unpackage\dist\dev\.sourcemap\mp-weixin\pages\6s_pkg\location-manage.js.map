{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s_pkg/location-manage.vue?1f13", "webpack:///D:/Xwzc/pages/6s_pkg/location-manage.vue?1926", "webpack:///D:/Xwzc/pages/6s_pkg/location-manage.vue?f187", "webpack:///D:/Xwzc/pages/6s_pkg/location-manage.vue?2ad8", "uni-app:///pages/6s_pkg/location-manage.vue", "webpack:///D:/Xwzc/pages/6s_pkg/location-manage.vue?aac6", "webpack:///D:/Xwzc/pages/6s_pkg/location-manage.vue?5704"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "loading", "saving", "locationConfig", "lastUpdated", "locations", "onLoad", "computed", "totalLocations", "categoriesCount", "methods", "loadConfig", "action", "result", "category", "editing", "console", "uni", "title", "icon", "getLocationDisplayName", "addCategory", "items", "editCategory", "saveCategory", "duration", "content", "showCancel", "success", "index", "deleteCategory", "addLocation", "editLocation", "location", "saveLocation", "locationName", "deleteLocation", "saveConfig", "configToSave", "handlePageClick"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpEA;AAAA;AAAA;AAAA;AAAymB,CAAgB,moBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC8H7nB;AAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MACA;QACA;MACA;IACA;IAEAC;MACA;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;;gBAEA;gBAAA;gBAAA,OACA;kBACAC;gBACA;cAAA;gBAFAC;gBAIA;kBACA;oBACAT;oBACAC;kBACA;;kBAEA;kBACA;oBACAS;oBACAA;sBACA;wBACA;0BAAAf;0BAAAgB;wBAAA;sBACA;sBACA;wBAAAA;sBAAA;oBACA;kBACA;gBACA;kBACA;kBACA;oBACAX;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAW;gBACAC;kBACAC;kBACAC;gBACA;gBACA;gBACA;kBACAf;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAMA;IACAe;MACA;QACA;MACA;MACA;IACA;IAIA;IACAC;MACA;MACA;QACAP;QACAC;QACAO;MACA;IAGA;IAEAC;MACA;MACA;QACA;MAAA,CACA;IACA;IAEAC;MAAA;MACA;;MAEA;MACA;QACA;QACA;UACA;UACAP;YACAC;YACAC;YACAM;UACA;UACA;QACA;UACA;UACAR;YACAC;YACAQ;YACAC;YACAC;cACA;cACAd;cACA;gBACA;cAAA,CACA;YACA;UACA;UACA;QACA;MACA;;MAEA;MACAA;;MAEA;MACA;QAAA,OACAe;MAAA,EACA;MAEA;QACAZ;UACAC;UACAQ;UACAC;UACAC;YACA;YACAd;YACA;cACA;YAAA,CACA;UACA;QACA;QACA;MACA;MAEAA;IACA;IAEAgB;MAAA;MACAb;QACAC;QACAQ;QACAE;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAG;MACA;MACA;QACAhC;QACAgB;MACA;IAGA;IAEAiB;MACA;MACA;MACA;QACA;UACAjC;UACAgB;QACA;MACA;QACAkB;MACA;IACA;IAEAC;MAAA;MACA;MACA;MACA;MAEA;QACA;QACA;QACAjB;UACAC;UACAC;UACAM;QACA;QACA;MACA;;MAEA;MACAU;;MAEA;MACA;MACA;QACA;QACA;QACA;MACA;MAEA;QACAlB;UACAC;UACAQ;UACAC;UACAC;YACA;YACA;cACA;gBACA7B;gBACAgB;cACA;YACA;cACAkB;YACA;UACA;QACA;QACA;MACA;;MAEA;MACA;QACA;UACAlC;UACAgB;QACA;MACA;QACAkB;QACAA;MACA;IACA;IAEAG;MAAA;MACA;MACA;MACAnB;QACAC;QACAQ;QACAE;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAS;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;;gBAEA;gBACAC;kBACAlC;kBACAC;oBAAA;sBACAS;sBACAQ;wBAAA;sBAAA;wBAAA;sBAAA;oBACA;kBAAA;oBAAA;kBAAA;gBACA,GAEA;gBAAA;gBAAA,OACA;kBACAV;kBACAZ;gBACA;cAAA;gBAEAiB;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAH;gBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAIA;IACAoB;MAAA;MACA;MACA;QACA;UACA;QACA;MACA;;MAEA;MACA;QACA;UACA;UACA;YACA;UACA;QACA;MACA;IACA;EAGA;AACA;AAAA,2B;;;;;;;;;;;;;ACrdA;AAAA;AAAA;AAAA;AAA4qC,CAAgB,kpCAAG,EAAC,C;;;;;;;;;;;ACAhsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/location-manage.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/location-manage.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./location-manage.vue?vue&type=template&id=e8f5409e&scoped=true&\"\nvar renderjs\nimport script from \"./location-manage.vue?vue&type=script&lang=js&\"\nexport * from \"./location-manage.vue?vue&type=script&lang=js&\"\nimport style0 from \"./location-manage.vue?vue&type=style&index=0&id=e8f5409e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"e8f5409e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/location-manage.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location-manage.vue?vue&type=template&id=e8f5409e&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 = !_vm.loading\n    ? _vm.__map(\n        _vm.locationConfig.locations,\n        function (category, categoryIndex) {\n          var $orig = _vm.__get_orig(category)\n          var g0 = category.items.length\n          var l0 = _vm.__map(\n            category.items,\n            function (location, locationIndex) {\n              var $orig = _vm.__get_orig(location)\n              var m0 = !location.editing\n                ? _vm.getLocationDisplayName(location)\n                : null\n              return {\n                $orig: $orig,\n                m0: m0,\n              }\n            }\n          )\n          return {\n            $orig: $orig,\n            g0: g0,\n            l0: l0,\n          }\n        }\n      )\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location-manage.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location-manage.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"page-container\" @click=\"handlePageClick\">\r\n    <!-- 页面头部 -->\r\n    <view class=\"header\">\r\n      <view class=\"header-title\">位置配置管理</view>\r\n      <view class=\"header-subtitle\">管理工厂所有区域位置信息</view>\r\n    </view>\r\n\r\n    <!-- 统计信息 -->\r\n    <view class=\"stats-card\">\r\n      <!-- 加载状态 -->\r\n      <view v-if=\"loading\" class=\"stats-loading\">\r\n        <view class=\"loading-content\">\r\n          <uni-icons type=\"spinner-cycle\" size=\"40\" color=\"#007AFF\"></uni-icons>\r\n          <text class=\"loading-text\">加载位置配置中...</text>\r\n        </view>\r\n      </view>\r\n      <!-- 正常统计 -->\r\n      <template v-else>\r\n        <view class=\"stats-item\">\r\n          <view class=\"stats-number\">{{ totalLocations }}</view>\r\n          <view class=\"stats-label\">总位置数</view>\r\n        </view>\r\n        <view class=\"stats-item\">\r\n          <view class=\"stats-number\">{{ categoriesCount }}</view>\r\n          <view class=\"stats-label\">分类数</view>\r\n        </view>\r\n      </template>\r\n    </view>\r\n\r\n    <!-- 操作按钮 -->\r\n    <view class=\"action-buttons\" @click.stop>\r\n      <button class=\"action-btn primary\" @click=\"addCategory\">\r\n        <uni-icons type=\"plus\" size=\"16\" color=\"#FFFFFF\" />\r\n        <text>新增分类</text>\r\n      </button>\r\n    </view>\r\n\r\n    <!-- 位置分类列表 -->\r\n    <!-- 加载状态 -->\r\n    <view v-if=\"loading\" class=\"list-loading\">\r\n      <view class=\"loading-content\">\r\n        <uni-icons type=\"spinner-cycle\" size=\"40\" color=\"#007AFF\"></uni-icons>\r\n        <text class=\"loading-text\">加载位置数据中...</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 正常列表 -->\r\n    <view v-else class=\"categories-list\">\r\n      <view \r\n        v-for=\"(category, categoryIndex) in locationConfig.locations\" \r\n        :key=\"categoryIndex\"\r\n        class=\"category-card\"\r\n      >\r\n        <!-- 分类头部 -->\r\n        <view class=\"category-header\" @click.stop>\r\n          <view class=\"category-title-group\">\r\n            <input \r\n              v-if=\"category.editing\"\r\n              v-model=\"category.category\"\r\n              class=\"category-title-input\"\r\n              placeholder=\"请输入分类名称\"\r\n              @blur=\"saveCategory(categoryIndex)\"\r\n              @confirm=\"saveCategory(categoryIndex)\"\r\n            />\r\n            <view v-else class=\"category-title\" @click=\"editCategory(categoryIndex)\">\r\n              {{ category.category }}\r\n            </view>\r\n            <view class=\"category-count\">({{ category.items.length }})</view>\r\n          </view>\r\n          \r\n          <view class=\"category-actions\">\r\n            <view class=\"action-icon\" @click=\"editCategory(categoryIndex)\">\r\n              <uni-icons type=\"compose\" size=\"18\" color=\"#007AFF\" />\r\n            </view>\r\n            <view class=\"action-icon\" @click=\"deleteCategory(categoryIndex)\">\r\n              <uni-icons type=\"trash\" size=\"18\" color=\"#FF3B30\" />\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 位置列表 -->\r\n        <view class=\"locations-grid\">\r\n          <view \r\n            v-for=\"(location, locationIndex) in category.items\"\r\n            :key=\"locationIndex\"\r\n            class=\"location-item\"\r\n            @click.stop\r\n          >\r\n            <input \r\n              v-if=\"location.editing\"\r\n              v-model=\"location.name\"\r\n              class=\"location-input\"\r\n              placeholder=\"请输入位置名称\"\r\n              @blur=\"saveLocation(categoryIndex, locationIndex)\"\r\n              @confirm=\"saveLocation(categoryIndex, locationIndex)\"\r\n            />\r\n            <view v-else class=\"location-content\" @click=\"editLocation(categoryIndex, locationIndex)\">\r\n              <text class=\"location-name\">{{ getLocationDisplayName(location) }}</text>\r\n            </view>\r\n            \r\n            <view v-if=\"!location.editing\" class=\"location-delete\" @click=\"deleteLocation(categoryIndex, locationIndex)\">\r\n              <uni-icons type=\"clear\" size=\"16\" color=\"#FF3B30\" />\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 添加位置按钮 -->\r\n          <view class=\"add-location-btn\" @click.stop=\"addLocation(categoryIndex)\">\r\n            <uni-icons type=\"plus\" size=\"20\" color=\"#C7C7CC\" />\r\n            <text class=\"add-text\">添加位置</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 保存按钮 -->\r\n    <view class=\"save-section\" @click.stop>\r\n      <button class=\"save-btn\" @click=\"saveConfig\" :disabled=\"saving\">\r\n        <uni-icons v-if=\"saving\" type=\"spinner-cycle\" size=\"16\" color=\"#FFFFFF\" />\r\n        <text>{{ saving ? '保存中...' : '保存配置' }}</text>\r\n      </button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { callCloudFunction } from '@/utils/auth.js';\r\n\r\nexport default {\r\n  name: 'LocationManage',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      saving: false,\r\n      locationConfig: {\r\n        lastUpdated: new Date().toISOString(),\r\n        locations: []\r\n      }\r\n    }\r\n  },\r\n  \r\n      onLoad() {\r\n    this.loadConfig();\r\n  },\r\n  \r\n  computed: {\r\n    totalLocations() {\r\n      return this.locationConfig.locations.reduce((sum, category) => {\r\n        return sum + category.items.length;\r\n      }, 0);\r\n    },\r\n    \r\n    categoriesCount() {\r\n      return this.locationConfig.locations.length;\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    // 加载配置\r\n    async loadConfig() {\r\n      try {\r\n        this.loading = true;\r\n        \r\n        // 使用云函数获取位置配置数据\r\n        const result = await callCloudFunction('hygiene-location-management', {\r\n          action: 'getLocationConfig'\r\n        });\r\n\r\n        if (result.data && result.data.locations) {\r\n          this.locationConfig = {\r\n            lastUpdated: result.data.lastUpdated || new Date().toISOString(),\r\n            locations: result.data.locations || []\r\n          };\r\n          \r\n          // 确保每个位置都有编辑状态\r\n          this.locationConfig.locations.forEach(category => {\r\n            category.editing = false;\r\n            category.items = category.items.map(item => {\r\n              if (typeof item === 'string') {\r\n                return { name: item, editing: false };\r\n              }\r\n              return { ...item, editing: false };\r\n            });\r\n          });\r\n        } else {\r\n          // 初始化空配置，让用户自己创建\r\n          this.locationConfig = {\r\n            lastUpdated: new Date().toISOString(),\r\n            locations: []\r\n          };\r\n        }\r\n        \r\n      } catch (error) {\r\n        console.error('加载配置失败：', error);\r\n        uni.showToast({\r\n          title: '加载失败',\r\n          icon: 'none'\r\n        });\r\n        // 初始化空配置\r\n        this.locationConfig = {\r\n          lastUpdated: new Date().toISOString(),\r\n          locations: []\r\n        };\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    \r\n\r\n    \r\n\r\n    \r\n    // 获取位置显示名称\r\n    getLocationDisplayName(location) {\r\n      if (typeof location === 'string') {\r\n        return location;\r\n      }\r\n      return location.name || '未命名位置';\r\n    },\r\n    \r\n\r\n    \r\n    // 分类管理\r\n    addCategory() {\r\n      const newCategoryIndex = this.locationConfig.locations.length;\r\n      this.locationConfig.locations.push({\r\n        category: '新分类',\r\n        editing: true,\r\n        items: []\r\n      });\r\n      \r\n\r\n    },\r\n    \r\n    editCategory(categoryIndex) {\r\n      this.locationConfig.locations[categoryIndex].editing = true;\r\n      this.$nextTick(() => {\r\n        // 小程序环境下暂不支持自动聚焦\r\n      });\r\n    },\r\n    \r\n    saveCategory(categoryIndex) {\r\n      const category = this.locationConfig.locations[categoryIndex];\r\n      \r\n      // 验证分类名称是否为空\r\n      if (!category.category || !category.category.trim()) {\r\n        // 如果是新建的分类且名称为空，直接删除\r\n        if (category.items.length === 0) {\r\n          this.locationConfig.locations.splice(categoryIndex, 1);\r\n          uni.showToast({\r\n            title: '分类名称不能为空',\r\n            icon: 'none',\r\n            duration: 2000\r\n          });\r\n          return;\r\n        } else {\r\n          // 如果分类下已有位置，提示用户\r\n          uni.showModal({\r\n            title: '提示',\r\n            content: '分类名称不能为空，请输入分类名称',\r\n            showCancel: false,\r\n            success: () => {\r\n              // 重新进入编辑状态\r\n              category.editing = true;\r\n              this.$nextTick(() => {\r\n                // 小程序环境下暂不支持自动聚焦\r\n              });\r\n            }\r\n          });\r\n          return;\r\n        }\r\n      }\r\n      \r\n      // 去除首尾空格\r\n      category.category = category.category.trim();\r\n      \r\n      // 检查是否有重复的分类名称\r\n      const duplicateIndex = this.locationConfig.locations.findIndex((cat, index) => \r\n        index !== categoryIndex && cat.category === category.category\r\n      );\r\n      \r\n      if (duplicateIndex !== -1) {\r\n        uni.showModal({\r\n          title: '提示',\r\n          content: '分类名称已存在，请使用其他名称',\r\n          showCancel: false,\r\n          success: () => {\r\n            // 重新进入编辑状态\r\n            category.editing = true;\r\n            this.$nextTick(() => {\r\n              // 小程序环境下暂不支持自动聚焦\r\n            });\r\n          }\r\n        });\r\n        return;\r\n      }\r\n      \r\n      category.editing = false;\r\n    },\r\n    \r\n    deleteCategory(categoryIndex) {\r\n      uni.showModal({\r\n        title: '确认删除',\r\n        content: `确定要删除\"${this.locationConfig.locations[categoryIndex].category}\"分类及其所有位置吗？`,\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            this.locationConfig.locations.splice(categoryIndex, 1);\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 位置管理\r\n    addLocation(categoryIndex) {\r\n      const newLocationIndex = this.locationConfig.locations[categoryIndex].items.length;\r\n      this.locationConfig.locations[categoryIndex].items.push({\r\n        name: '',\r\n        editing: true\r\n      });\r\n      \r\n\r\n    },\r\n    \r\n    editLocation(categoryIndex, locationIndex) {\r\n      const location = this.locationConfig.locations[categoryIndex].items[locationIndex];\r\n      // 如果是字符串格式，转换为对象格式\r\n      if (typeof location === 'string') {\r\n        this.locationConfig.locations[categoryIndex].items[locationIndex] = {\r\n          name: location,\r\n          editing: true\r\n        };\r\n      } else {\r\n        location.editing = true;\r\n      }\r\n    },\r\n    \r\n    saveLocation(categoryIndex, locationIndex) {\r\n      const location = this.locationConfig.locations[categoryIndex].items[locationIndex];\r\n      // 处理不同的数据结构（字符串或对象）\r\n      let locationName = typeof location === 'string' ? location : (location.name || '');\r\n      \r\n      if (!locationName.trim()) {\r\n        // 如果名称为空，删除该项\r\n        this.locationConfig.locations[categoryIndex].items.splice(locationIndex, 1);\r\n        uni.showToast({\r\n          title: '位置名称不能为空',\r\n          icon: 'none',\r\n          duration: 2000\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 去除首尾空格\r\n      locationName = locationName.trim();\r\n      \r\n      // 检查同一分类下是否有重复的位置名称\r\n      const categoryItems = this.locationConfig.locations[categoryIndex].items;\r\n      const duplicateIndex = categoryItems.findIndex((item, index) => {\r\n        if (index === locationIndex) return false;\r\n        const itemName = typeof item === 'string' ? item : (item.name || '');\r\n        return itemName === locationName;\r\n      });\r\n      \r\n      if (duplicateIndex !== -1) {\r\n        uni.showModal({\r\n          title: '提示',\r\n          content: '该分类下已存在相同的位置名称，请使用其他名称',\r\n          showCancel: false,\r\n          success: () => {\r\n            // 重新进入编辑状态\r\n            if (typeof location === 'string') {\r\n              this.locationConfig.locations[categoryIndex].items[locationIndex] = {\r\n                name: location,\r\n                editing: true\r\n              };\r\n            } else {\r\n              location.editing = true;\r\n            }\r\n          }\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 确保location是对象格式\r\n      if (typeof location === 'string') {\r\n        this.locationConfig.locations[categoryIndex].items[locationIndex] = {\r\n          name: locationName,\r\n          editing: false\r\n        };\r\n      } else {\r\n        location.name = locationName;\r\n        location.editing = false;\r\n      }\r\n    },\r\n    \r\n    deleteLocation(categoryIndex, locationIndex) {\r\n      const location = this.locationConfig.locations[categoryIndex].items[locationIndex];\r\n      const locationName = location.name || location || '未命名位置';\r\n      uni.showModal({\r\n        title: '确认删除',\r\n        content: `确定要删除位置\"${locationName}\"吗？`,\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            this.locationConfig.locations[categoryIndex].items.splice(locationIndex, 1);\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 配置管理\r\n    async saveConfig() {\r\n      try {\r\n        this.saving = true;\r\n        \r\n        // 清理数据格式\r\n        const configToSave = {\r\n          lastUpdated: new Date().toISOString(),\r\n          locations: this.locationConfig.locations.map(category => ({\r\n            category: category.category,\r\n            items: category.items.map(item => item.name || item).filter(name => name.trim())\r\n          })).filter(category => category.items.length > 0)\r\n        };\r\n        \r\n        // 使用云函数保存位置配置\r\n        await callCloudFunction('hygiene-location-management', {\r\n          action: 'updateLocationConfig',\r\n          data: configToSave\r\n        });\r\n        \r\n        uni.showToast({\r\n          title: '配置保存成功',\r\n          icon: 'success'\r\n        });\r\n        \r\n      } catch (error) {\r\n        console.error('保存配置失败：', error);\r\n        uni.showToast({\r\n          title: error.message || '保存失败',\r\n          icon: 'none'\r\n        });\r\n      } finally {\r\n        this.saving = false;\r\n      }\r\n    },\r\n    \r\n\r\n    \r\n    // 处理页面点击事件\r\n    handlePageClick() {\r\n      // 保存所有正在编辑的分类\r\n      this.locationConfig.locations.forEach((category, categoryIndex) => {\r\n        if (category.editing) {\r\n          this.saveCategory(categoryIndex);\r\n        }\r\n      });\r\n      \r\n      // 保存所有正在编辑的位置\r\n      this.locationConfig.locations.forEach((category, categoryIndex) => {\r\n        for (let i = category.items.length - 1; i >= 0; i--) {\r\n          const location = category.items[i];\r\n          if (location.editing) {\r\n            this.saveLocation(categoryIndex, i);\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-container {\r\n  min-height: 100vh;\r\n  background: #F8F9FA;\r\n  padding-bottom: 200rpx;\r\n}\r\n\r\n.header {\r\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\r\n  padding: 60rpx 32rpx 40rpx;\r\n  color: white;\r\n}\r\n\r\n.header-title {\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.header-subtitle {\r\n  font-size: 24rpx;\r\n  opacity: 0.8;\r\n}\r\n\r\n.stats-card {\r\n  background: white;\r\n  margin: 24rpx 32rpx;\r\n  padding: 32rpx;\r\n  border-radius: 16rpx;\r\n  display: flex;\r\n  justify-content: space-around;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.stats-item {\r\n  text-align: center;\r\n}\r\n\r\n.stats-number {\r\n  font-size: 48rpx;\r\n  font-weight: 600;\r\n  color: #007AFF;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.stats-label {\r\n  font-size: 24rpx;\r\n  color: #8E8E93;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 16rpx;\r\n  padding: 0 32rpx;\r\n  margin-bottom: 24rpx;\r\n}\r\n\r\n.action-btn {\r\n  flex: 1;\r\n  height: 72rpx;\r\n  border-radius: 12rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8rpx;\r\n  font-size: 26rpx;\r\n  border: none;\r\n}\r\n\r\n.action-btn.primary {\r\n  background: #007AFF;\r\n  color: white;\r\n}\r\n\r\n.action-btn.secondary {\r\n  background: white;\r\n  color: #007AFF;\r\n  border: 2rpx solid #007AFF;\r\n}\r\n\r\n.categories-list {\r\n  padding: 0 32rpx;\r\n}\r\n\r\n.category-card {\r\n  background: white;\r\n  border-radius: 16rpx;\r\n  margin-bottom: 24rpx;\r\n  padding: 32rpx;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.category-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24rpx;\r\n  padding-bottom: 16rpx;\r\n  border-bottom: 2rpx solid #F2F2F7;\r\n}\r\n\r\n.category-title-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12rpx;\r\n}\r\n\r\n.category-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #1C1C1E;\r\n}\r\n\r\n.category-title-input {\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  color: #1C1C1E;\r\n  border: 2rpx solid #007AFF;\r\n  border-radius: 8rpx;\r\n  padding: 8rpx 12rpx;\r\n  background: white;\r\n  min-width: 200rpx;\r\n}\r\n\r\n/* 分类标题输入框占位符样式 */\r\n.category-title-input::placeholder {\r\n  color: #C7C7CC;\r\n  font-size: 26rpx;\r\n  font-weight: 400;\r\n}\r\n\r\n.category-title-input::-webkit-input-placeholder {\r\n  color: #C7C7CC;\r\n  font-size: 26rpx;\r\n  font-weight: 400;\r\n}\r\n\r\n.category-title-input::-moz-placeholder {\r\n  color: #C7C7CC;\r\n  font-size: 26rpx;\r\n  font-weight: 400;\r\n  opacity: 1;\r\n}\r\n\r\n.category-title-input:-ms-input-placeholder {\r\n  color: #C7C7CC;\r\n  font-size: 26rpx;\r\n  font-weight: 400;\r\n}\r\n\r\n.category-count {\r\n  font-size: 24rpx;\r\n  color: #8E8E93;\r\n}\r\n\r\n.category-actions {\r\n  display: flex;\r\n  gap: 16rpx;\r\n}\r\n\r\n.action-icon {\r\n  padding: 8rpx;\r\n  border-radius: 8rpx;\r\n  background: #F2F2F7;\r\n}\r\n\r\n.locations-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 16rpx;\r\n}\r\n\r\n.location-item {\r\n  position: relative;\r\n  background: #F8F9FA;\r\n  border-radius: 12rpx;\r\n  border: 2rpx solid #F2F2F7;\r\n  overflow: hidden;\r\n}\r\n\r\n.location-content {\r\n  padding: 20rpx 50rpx 20rpx 20rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  min-height: 20rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.location-name {\r\n  font-size: 26rpx;\r\n  color: #1C1C1E;\r\n  flex: 1;\r\n  line-height: 40rpx;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n\r\n\r\n.location-input {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 20rpx;\r\n  font-size: 24rpx;\r\n  color: #1C1C1E;\r\n  border: none;\r\n  background: white;\r\n  box-sizing: border-box;\r\n  line-height: 24rpx;\r\n  text-align: left;\r\n  border-radius: 12rpx;\r\n}\r\n\r\n/* 位置输入框占位符样式 */\r\n.location-input::placeholder {\r\n  color: #C7C7CC;\r\n  font-size: 22rpx;\r\n  font-weight: 400;\r\n}\r\n\r\n.location-input::-webkit-input-placeholder {\r\n  color: #C7C7CC;\r\n  font-size: 22rpx;\r\n  font-weight: 400;\r\n}\r\n\r\n.location-input::-moz-placeholder {\r\n  color: #C7C7CC;\r\n  font-size: 22rpx;\r\n  font-weight: 400;\r\n  opacity: 1;\r\n}\r\n\r\n.location-input:-ms-input-placeholder {\r\n  color: #C7C7CC;\r\n  font-size: 22rpx;\r\n  font-weight: 400;\r\n}\r\n\r\n.location-delete {\r\n  position: absolute;\r\n  top: 50%;\r\n  right: 10rpx;\r\n  transform: translateY(-50%);\r\n  width: 36rpx;\r\n  height: 36rpx;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.add-location-btn {\r\n  background: #F2F2F7;\r\n  border: 2rpx dashed #C7C7CC;\r\n  border-radius: 12rpx;\r\n  padding: 20rpx;\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8rpx;\r\n}\r\n\r\n.add-text {\r\n  font-size: 24rpx;\r\n  color: #8E8E93;\r\n}\r\n\r\n.save-section {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  padding: 32rpx;\r\n  background: white;\r\n  border-top: 2rpx solid #F2F2F7;\r\n  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));\r\n}\r\n\r\n.save-btn {\r\n  width: 100%;\r\n  height: 88rpx;\r\n  background: #007AFF;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 12rpx;\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8rpx;\r\n}\r\n\r\n.save-btn[disabled] {\r\n  background: #C7C7CC;\r\n}\r\n\r\n/* 加载状态样式 */\r\n.stats-loading {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 60rpx 0;\r\n  width: 100%;\r\n}\r\n\r\n.list-loading {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 80rpx 0;\r\n  background: #F8F9FA;\r\n}\r\n\r\n.loading-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16rpx;\r\n}\r\n\r\n.loading-text {\r\n  font-size: 28rpx;\r\n  color: #8E8E93;\r\n}\r\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location-manage.vue?vue&type=style&index=0&id=e8f5409e&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location-manage.vue?vue&type=style&index=0&id=e8f5409e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775841929\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}