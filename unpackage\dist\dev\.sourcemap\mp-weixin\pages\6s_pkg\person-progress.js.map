{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s_pkg/person-progress.vue?bcbd", "webpack:///D:/Xwzc/pages/6s_pkg/person-progress.vue?1770", "webpack:///D:/Xwzc/pages/6s_pkg/person-progress.vue?9c97", "webpack:///D:/Xwzc/pages/6s_pkg/person-progress.vue?5416", "uni-app:///pages/6s_pkg/person-progress.vue", "webpack:///D:/Xwzc/pages/6s_pkg/person-progress.vue?e743", "webpack:///D:/Xwzc/pages/6s_pkg/person-progress.vue?0f1b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "loading", "loadingText", "personId", "personName", "currentFilter", "hasManagePermission", "displayLimit", "displayStep", "personData", "id", "role", "total", "completed", "inProgress", "overdue", "issues", "statusFilters", "label", "value", "dataCache", "cacheExpireTime", "lastLoadTime", "computed", "completionRate", "filteredIssues", "displayedIssues", "has<PERSON><PERSON><PERSON><PERSON><PERSON>", "remainingIssuesCount", "onLoad", "uni", "onUnload", "onShow", "methods", "loadPersonData", "require", "Promise", "issuesResult", "userResult", "userInfo", "console", "title", "icon", "checkUserPermissions", "adminRoles", "smartRefresh", "now", "timeSinceLastLoad", "loadPersonDataSilently", "loadPersonIssues", "callCloudFunction", "action", "responsiblePersonId", "page", "pageSize", "result", "number", "location", "deadline", "status", "priority", "description", "loadPersonInfo", "userId", "mapIssueStatus", "getDefaultDeadline", "date", "formatLocation", "getRoleDisplayName", "updateFilterCounts", "changeFilter", "getFilterTitle", "getStatusText", "generateIssueHistory", "history", "time", "operator", "formatHistoryTime", "viewIssueDetail", "issue", "responsible", "createdAt", "updatedAt", "images", "url", "showMoreIssues", "showLoading", "hideLoading", "delay", "handleDataUpdated", "setTimeout", "refreshDataSilently", "refreshData"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnEA;AAAA;AAAA;AAAA;AAAymB,CAAgB,moBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCqI7nB;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QACAX;QACAY;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC,gBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACA;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;QACA;MACA;MACA;QAAA;MAAA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;IAEA;IACA;;IAEA;IACAC;IACAA;EACA;EAEAC;IACA;IACAD;IACAA;EACA;EAEAE;IACA;IACA;EACA;EACAC;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA,WAEAC,gGAEA;gBAAA;gBAAA,OACAC,aACA,2BACA,wBACA;cAAA;gBAAA;gBAAA;gBAHAC;gBAAAC;gBAKAtB;gBACAuB;kBAAAxC;kBAAAY;gBAAA,GAEA;gBACAC;gBACAC;kBAAA;gBAAA;gBACAC;kBAAA;gBAAA;gBACAC;kBAAA;gBAAA;gBAEA;kBACAL;kBACAX;kBACAY;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAwB;;gBAEA;gBACA;kBACA9B;kBACAX;kBACAY;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBAEA;gBAEAc;kBACAW;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACAJ;kBACA5B,4BAEA;kBACAiC;kBACA;oBAAA;kBAAA;gBAEA;kBACAJ;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAC,+CAEA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,YAEAb;gBAAA;gBAAA,OAEAC,aACA,2BACA,wBACA;cAAA;gBAAA;gBAAA;gBAHAC;gBAAAC;gBAKA;kBACAtB;kBACAuB;oBAAAxC;oBAAAY;kBAAA,GAEA;kBACAC;kBACAC;oBAAA;kBAAA;kBACAC;oBAAA;kBAAA;kBACAC;oBAAA;kBAAA;kBAEA,oDACA;oBACAH;oBACAC;oBACAC;oBACAC;oBACAC;kBAAA,EACA;kBAEA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IAEA;IACAiC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,YACAd;gBAAA;gBAAA;gBAAA,OAIAe;kBACAC;kBACAnD;oBACAoD;oBACAC;oBACAC;kBACA;gBACA;cAAA;gBAPAC;gBAAA,MASAA;kBAAA;kBAAA;gBAAA;gBAAA,kCACAA;kBAAA;oBACA7C;oBACA8C;oBACAf;oBACAgB;oBACAC,2CACA,uEACA;oBACAC;oBACAC;oBACAC;kBACA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGArB;cAAA;gBAAA,kCAGA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAsB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,YACA3B;gBAAA;gBAAA;gBAAA,OAIAe;kBACAC;kBACAnD;oBACA+D;kBACA;gBACA;cAAA;gBALAR;gBAAA,MAOAA;kBAAA;kBAAA;gBAAA;gBAAA,kCACA;kBACAxD;kBACAY;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA6B;cAAA;gBAAA,kCAGA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAwB;MACA;QACA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAC;MACA;IACA;IAEA;IACAC;MACA;MAEA;;MAEA;MACA;QACAV;MACA;MAEA;IACA;IAEA;IACAW;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;;MAEA;MACA;IACA;IAEAC;MACA;IAAA,CACA;IAEAC;MACA;MACA;MACA;;MAEA;MACA;QACA;MAAA,CACA;IACA;IAEAC;MAAA;MACA;QAAA;MAAA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;QACAC;UACAvB;UACAU;UACAc;UACAC;QACA;MACA;;MAEA;MACA;QACAF;UACAvB;UACAU;UACAc;UACAC;QACA;MACA;;MAEA;MACA;QACAF;UACAvB;UACAU;UACAc;UACAC;QACA;MACA;QACAF,aACA;UACAvB;UACAU;UACAc;UACAC;QACA,GACA;UACAzB;UACAU;UACAc;UACAC;QACA,EACA;;QAEA;QACA;UACAF,aACA;YACAvB;YACAU;YACAc;YACAC;UACA,GACA;YACAzB;YACAU;YACAc;YACAC;UACA,EACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MAEA;QACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA,oDACAC;QACAC;QACArE;QACAsE;QACAC;QACAC;QACAT;MAAA,EACA;MAEA5C;MACAA;QACAsD;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MAAA;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACA;MACA;;MAEA;MACAC;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAvD,aACA,4BACA,yBACA;cAAA;gBAAA;gBAAA;gBAHAC;gBAAAC;gBAKA;kBACAtB;kBACAuB;oBAAAxC;oBAAAY;kBAAA,GAEA;kBACAC;kBACAC;oBAAA,OACAkE;kBAAA,EACA;kBACAjE;oBAAA;kBAAA;kBACAC;oBAAA;kBAAA;kBAEA,qDACA;oBACAH;oBACAC;oBACAC;oBACAC;oBACAC;kBAAA,EACA;kBAEA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IAEA;IACA4E;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvqBA;AAAA;AAAA;AAAA;AAA4qC,CAAgB,kpCAAG,EAAC,C;;;;;;;;;;;ACAhsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/person-progress.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/person-progress.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./person-progress.vue?vue&type=template&id=1ef03a4e&scoped=true&\"\nvar renderjs\nimport script from \"./person-progress.vue?vue&type=script&lang=js&\"\nexport * from \"./person-progress.vue?vue&type=script&lang=js&\"\nimport style0 from \"./person-progress.vue?vue&type=style&index=0&id=1ef03a4e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1ef03a4e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/person-progress.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./person-progress.vue?vue&type=template&id=1ef03a4e&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getFilterTitle()\n  var g0 = _vm.filteredIssues.length\n  var g1 = _vm.filteredIssues.length\n  var l0 =\n    g1 > 0\n      ? _vm.__map(_vm.displayedIssues, function (issue, index) {\n          var $orig = _vm.__get_orig(issue)\n          var g2 = String(issue.number).padStart(2, \"0\")\n          var m1 = _vm.getStatusText(issue.status)\n          return {\n            $orig: $orig,\n            g2: g2,\n            m1: m1,\n          }\n        })\n      : null\n  var m2 = !(g1 > 0) ? _vm.getFilterTitle() || \"数据\" : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./person-progress.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./person-progress.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page-container\">\n    <view class=\"content\">\n      <!-- 负责人信息卡片 -->\n      <view class=\"person-card\">\n        <view class=\"person-header\">\n          <view class=\"person-avatar\">\n            <uni-icons type=\"person\" size=\"32\" color=\"#007AFF\"></uni-icons>\n          </view>\n          <view class=\"person-info\">\n            <view class=\"person-name\">{{ personData.name }}</view>\n            <view class=\"person-role\">{{ personData.role }}</view>\n          </view>\n        </view>\n        \n        <view class=\"progress-summary\">\n          <view class=\"summary-item\">\n            <view class=\"summary-number primary\">{{ personData.total }}</view>\n            <view class=\"summary-label\">总问题</view>\n          </view>\n          <view class=\"summary-item\">\n            <view class=\"summary-number success\">{{ personData.completed }}</view>\n            <view class=\"summary-label\">已完成</view>\n          </view>\n          <view class=\"summary-item\">\n            <view class=\"summary-number warning\">{{ personData.inProgress }}</view>\n            <view class=\"summary-label\">进行中</view>\n          </view>\n          <view class=\"summary-item\">\n            <view class=\"summary-number danger\">{{ personData.overdue }}</view>\n            <view class=\"summary-label\">已逾期</view>\n          </view>\n        </view>\n        \n        <view class=\"completion-rate\">\n          <view class=\"rate-info\">\n            <text class=\"rate-label\">完成率</text>\n            <text class=\"rate-value\">{{ completionRate }}%</text>\n          </view>\n          <view class=\"rate-bar\">\n            <view class=\"rate-fill\" :style=\"{ width: completionRate + '%' }\"></view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 问题筛选 -->\n      <view class=\"filter-card\">\n        <view class=\"card-header\">\n          <scroll-view class=\"filter-tabs\" scroll-x=\"true\" show-scrollbar=\"false\">\n            <view \n              v-for=\"filter in statusFilters\" \n              :key=\"filter.value\"\n              class=\"filter-tab\"\n              :class=\"{ active: currentFilter === filter.value }\"\n              @click=\"changeFilter(filter.value)\"\n            >\n              {{ filter.label }}\n            </view>\n          </scroll-view>\n        </view>\n      </view>\n\n      <!-- 问题列表 -->\n      <view class=\"issues-card\">\n        <view class=\"card-header\">\n          <view class=\"card-title\">{{ getFilterTitle() }}</view>\n          <view class=\"card-subtitle\">共{{ filteredIssues.length }}个问题</view>\n        </view>\n        \n        <view v-if=\"filteredIssues.length > 0\" class=\"issues-list\">\n          <view \n            v-for=\"(issue, index) in displayedIssues\" \n            :key=\"index\"\n            class=\"issue-item\"\n            @click=\"viewIssueDetail(issue)\"\n          >\n            <view class=\"issue-icon\" :class=\"['icon-bg-' + issue.status]\">\n              <text class=\"issue-number\">{{ String(issue.number).padStart(2, '0') }}</text>\n            </view>\n            <view class=\"issue-content\">\n              <view class=\"issue-title\">{{ issue.title }}</view>\n              <view class=\"issue-location\">\n                <uni-icons type=\"location\" size=\"14\" color=\"#8E8E93\"></uni-icons>\n                <text>{{ issue.location }}</text>\n              </view>\n              <view class=\"issue-deadline\">\n                <uni-icons type=\"calendar\" size=\"14\" color=\"#8E8E93\"></uni-icons>\n                <text>截止：{{ issue.deadline }}</text>\n              </view>\n            </view>\n            <view class=\"issue-right\">\n              <view class=\"issue-status\" :class=\"['status-' + issue.status]\">\n                {{ getStatusText(issue.status) }}\n              </view>\n              <uni-icons type=\"right\" size=\"14\" color=\"#C7C7CC\"></uni-icons>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 空状态 -->\n        <p-empty-state\n          v-else\n          useIcon\n          iconName=\"info\"\n          iconColor=\"#C7C7CC\"\n          size=\"large\"\n          :text=\"'暂无' + (getFilterTitle() || '数据')\"\n        ></p-empty-state>\n        \n        <!-- 查看更多按钮 -->\n        <view v-if=\"hasMoreIssues\" class=\"more-section\">\n          <view class=\"more-btn\" @click=\"showMoreIssues\">\n            <uni-icons type=\"down\" size=\"16\" color=\"#007AFF\"></uni-icons>\n            <text>查看更多问题 ({{ remainingIssuesCount }})</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 底部安全间距 -->\n      <view class=\"bottom-safe-area\"></view>\n    </view>\n\n    <!-- 加载状态 - 自定义遮罩 -->\n    <view v-if=\"loading\" class=\"custom-loading-mask\">\n      <view class=\"loading-container-enhanced\">\n        <uni-icons type=\"spinner-cycle\" size=\"48\" color=\"#007AFF\"></uni-icons>\n        <text class=\"loading-text-enhanced\">{{ loadingText }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'PersonProgress',\n  data() {\n    return {\n      loading: false,\n      loadingText: '加载中...',\n      personId: null,\n      personName: '',\n      currentFilter: 'all',\n      hasManagePermission: false,\n      // 分页控制\n      displayLimit: 6, // 初始显示数量\n      displayStep: 4,   // 每次加载更多的数量\n      personData: {\n        id: '',\n        name: '',\n        role: '',\n        total: 0,\n        completed: 0,\n        inProgress: 0,\n        overdue: 0,\n        issues: []\n      },\n      statusFilters: [\n        { label: '全部', value: 'all' },\n        { label: '已分配', value: 'assigned' },\n        { label: '待整改', value: 'pending' },\n        { label: '整改中', value: 'in_progress' },\n        { label: '待检查', value: 'pending_review' },\n        { label: '检查通过', value: 'approved' }\n      ],\n      // 数据缓存（性能优化）\n      dataCache: new Map(),\n      cacheExpireTime: 3 * 60 * 1000, // 3分钟缓存\n      lastLoadTime: 0\n    }\n  },\n  computed: {\n    completionRate() {\n      if (this.personData.total === 0) return 0\n      return Math.round((this.personData.completed / this.personData.total) * 100)\n    },\n    filteredIssues() {\n      if (this.currentFilter === 'all') {\n        return this.personData.issues\n      }\n      return this.personData.issues.filter(issue => issue.status === this.currentFilter)\n    },\n    displayedIssues() {\n      return this.filteredIssues.slice(0, this.displayLimit)\n    },\n    hasMoreIssues() {\n      return this.filteredIssues.length > this.displayLimit\n    },\n    remainingIssuesCount() {\n      return this.filteredIssues.length - this.displayLimit\n    }\n  },\n  onLoad(options) {\n    this.personId = options.id;\n    this.personName = decodeURIComponent(options.name || '负责人');\n    \n    this.loadPersonData();\n    this.checkUserPermissions();\n    \n    // 监听数据更新事件\n    uni.$on('monthlyIssueUpdated', this.handleDataUpdated);\n    uni.$on('issueDraftUpdated', this.handleDataUpdated);\n  },\n\n  onUnload() {\n    // 移除事件监听\n    uni.$off('monthlyIssueUpdated', this.handleDataUpdated);\n    uni.$off('issueDraftUpdated', this.handleDataUpdated);\n  },\n\n  onShow() {\n    // 智能刷新：检查缓存是否过期\n    this.smartRefresh();\n  },\n  methods: {\n\n    async loadPersonData() {\n      this.showLoading('加载个人进度数据...')\n      try {\n        const { callCloudFunction } = require('@/utils/auth.js');\n        \n        // 并行调用获取个人问题数据和用户信息\n        const [issuesResult, userResult] = await Promise.all([\n          this.loadPersonIssues(),\n          this.loadPersonInfo()\n        ]);\n        \n        const issues = issuesResult || [];\n        const userInfo = userResult || { name: this.personName, role: '负责人' };\n        \n        // 计算统计数据\n        const total = issues.length;\n        const completed = issues.filter(issue => issue.status === 'approved').length;\n        const inProgress = issues.filter(issue => issue.status === 'in_progress').length;\n        const overdue = issues.filter(issue => issue.status === 'overdue').length;\n        \n        this.personData = {\n          id: this.personId,\n          name: userInfo.name || this.personName,\n          role: userInfo.role || '负责人',\n          total,\n          completed,\n          inProgress,\n          overdue,\n          issues\n        };\n        \n        this.updateFilterCounts();\n        \n      } catch (error) {\n        console.error('加载个人进度数据失败:', error);\n        \n        // 使用默认空数据\n        this.personData = {\n          id: this.personId,\n          name: this.personName,\n          role: '负责人',\n          total: 0,\n          completed: 0,\n          inProgress: 0,\n          overdue: 0,\n          issues: []\n        };\n        \n        this.updateFilterCounts();\n        \n        uni.showToast({\n          title: '加载数据失败',\n          icon: 'none'\n        });\n      } finally {\n        this.hideLoading();\n        this.lastLoadTime = Date.now();\n      }\n    },\n\n    // 检查用户权限 - 与其他页面保持一致\n    async checkUserPermissions() {\n      try {\n        const userInfo = uni.getStorageSync('uni-id-pages-userInfo') || {};\n        const role = userInfo.role || [];\n        \n        // 管理权限：管理员、厂长、6S专员\n        const adminRoles = ['admin', 'GM', 'Integrated', 'reviser'];\n        this.hasManagePermission = role.some(r => adminRoles.includes(r));\n        \n      } catch (error) {\n        console.error('获取用户权限失败:', error);\n        this.hasManagePermission = false;\n      }\n    },\n\n    // 智能刷新（性能优化）\n    async smartRefresh() {\n      const now = Date.now();\n      const timeSinceLastLoad = now - this.lastLoadTime;\n      \n      // 如果距离上次加载超过3分钟，或者数据为空，则静默刷新\n      if (timeSinceLastLoad > this.cacheExpireTime || this.personData.issues.length === 0) {\n        await this.loadPersonDataSilently();\n      }\n    },\n\n    // 静默加载个人数据\n    async loadPersonDataSilently() {\n      try {\n        const { callCloudFunction } = require('@/utils/auth.js');\n        \n        const [issuesResult, userResult] = await Promise.all([\n          this.loadPersonIssues(),\n          this.loadPersonInfo()\n        ]);\n        \n        if (issuesResult && issuesResult.length > 0) {\n          const issues = issuesResult;\n          const userInfo = userResult || { name: this.personName, role: '负责人' };\n          \n          // 计算统计数据\n          const total = issues.length;\n          const completed = issues.filter(issue => issue.status === 'approved').length;\n          const inProgress = issues.filter(issue => issue.status === 'in_progress').length;\n          const overdue = issues.filter(issue => issue.status === 'overdue').length;\n          \n          this.personData = {\n            ...this.personData,\n            total,\n            completed,\n            inProgress,\n            overdue,\n            issues\n          };\n          \n          this.updateFilterCounts();\n          this.lastLoadTime = Date.now();\n        }\n      } catch (error) {\n        // 静默处理刷新失败\n      }\n    },\n\n    // 加载个人问题数据\n    async loadPersonIssues() {\n      const { callCloudFunction } = require('@/utils/auth.js');\n      \n      try {\n        // 调用新的月度检查云函数获取分配给该人员的问题\n        const result = await callCloudFunction('hygiene-monthly-inspection', {\n          action: 'getPersonIssues',\n          data: {\n            responsiblePersonId: this.personId,\n            page: 1,\n            pageSize: 50\n          }\n        });\n        \n        if (result && result.success && result.data && result.data.list) {\n          return result.data.list.map((issue, index) => ({\n            id: issue._id || issue.id,\n            number: index + 1,\n            title: issue.title || issue.description,\n            location: this.formatLocation(issue.location_info) || issue.location || '未知位置',\n            deadline: issue.expected_completion_date ? \n              new Date(issue.expected_completion_date).toISOString().split('T')[0] : \n              this.getDefaultDeadline(),\n            status: this.mapIssueStatus(issue.status),\n            priority: issue.priority || 'normal',\n            description: issue.description\n          }));\n        }\n      } catch (error) {\n        console.error('获取个人问题失败:', error);\n      }\n      \n      return [];\n    },\n\n    // 加载个人信息\n    async loadPersonInfo() {\n      const { callCloudFunction } = require('@/utils/auth.js');\n      \n      try {\n        // 通过新的月度检查云函数获取用户信息\n        const result = await callCloudFunction('hygiene-monthly-inspection', {\n          action: 'getUserInfo',\n          data: {\n            userId: this.personId\n          }\n        });\n        \n        if (result && result.success && result.data) {\n          return {\n            name: result.data.nickname || result.data.username || this.personName,\n            role: this.getRoleDisplayName(result.data.role || [])\n          };\n        }\n      } catch (error) {\n        console.error('获取个人信息失败:', error);\n      }\n      \n      return null;\n    },\n\n    // 映射问题状态 - 与其他页面保持一致\n    mapIssueStatus(apiStatus) {\n      const statusMap = {\n        'submitted': 'pending',\n        'assigned': 'assigned',     // 已分配\n        'pending': 'pending',       // 待整改\n        'in_progress': 'in_progress', // 整改中\n        'pending_review': 'pending_review', // 待检查\n        'approved': 'approved',     // 检查通过\n        'closed': 'closed',         // 已关闭\n        'resolved': 'approved',     // API的resolved映射为approved\n        'overdue': 'overdue',\n        'rejected': 'rejected',     // 已驳回\n        'reopened': 'pending',      // 重新打开映射为待整改\n        'draft': 'pending'\n      };\n      return statusMap[apiStatus] || 'pending';\n    },\n\n    // 获取默认截止日期（7天后）\n    getDefaultDeadline() {\n      const date = new Date();\n      date.setDate(date.getDate() + 7);\n      return date.toISOString().split('T')[0];\n    },\n\n    // 格式化位置信息 - 与monthly-check.vue保持一致\n    formatLocation(locationInfo) {\n      if (!locationInfo) return null;\n      \n      let location = locationInfo.location_name || locationInfo.name || null;\n      \n      // 如果有分类信息，添加到前面\n      if (locationInfo.location_category) {\n        location = `${locationInfo.location_category} - ${location}`;\n      }\n      \n      return location;\n    },\n\n    // 获取角色显示名称 - 与其他页面保持统一\n    getRoleDisplayName(roles) {\n      if (!roles || !Array.isArray(roles) || roles.length === 0) {\n        return '普通员工';\n      }\n      \n      // 统一的角色映射表 - 与用户中心、员工分配等页面保持一致\n      const roleMap = {\n        'admin': '管理员',\n        'responsible': '负责人',\n        'reviser': '发布人',\n        'supervisor': '主管',\n        'PM': '副厂长',\n        'GM': '厂长',\n        'logistics': '后勤员',\n        'dispatch': '调度员',\n        'Integrated': '综合员',\n        'operator': '设备员',\n        'technician': '工艺员',\n        'mechanic': '技术员',\n        'user': '普通员工'\n      };\n      \n      // 如果有多个角色，显示第一个主要角色\n      return roleMap[roles[0]] || '普通员工';\n    },\n\n    updateFilterCounts() {\n      // 统计数字已移除，保留方法以避免调用错误\n    },\n\n    changeFilter(value) {\n      this.currentFilter = value;\n      // 切换筛选条件时重置显示限制\n      this.displayLimit = 6;\n      \n      // 性能优化：延迟更新筛选结果，减少频繁计算\n      this.$nextTick(() => {\n        // 筛选操作在计算属性中完成，这里只需要触发重新渲染\n      });\n    },\n\n    getFilterTitle() {\n      const filter = this.statusFilters.find(f => f.value === this.currentFilter)\n      return filter ? filter.label + '问题' : '问题'\n    },\n\n    getStatusText(status) {\n      const statusMap = {\n        'assigned': '已分配',\n        'pending': '待整改',\n        'in_progress': '整改中',\n        'pending_review': '待检查',\n        'approved': '检查通过',\n        'rejected': '已驳回',\n        'overdue': '已逾期',\n        'closed': '已关闭',\n        'reopened': '重新打开'\n      }\n      return statusMap[status] || '未知'\n    },\n\n    // 生成问题历史记录（优化版，基于实际数据）\n    generateIssueHistory(issue) {\n      const history = [];\n      \n      // 创建问题\n      if (issue.created_at) {\n        history.push({\n          action: '创建问题',\n          description: '6S检查员发现问题并记录',\n          time: this.formatHistoryTime(issue.created_at),\n          operator: issue.reporter_name || '检查员'\n        });\n      }\n      \n      // 分配负责人\n      if (issue.assigned_to_name) {\n        history.push({\n          action: '分配负责人',\n          description: `将问题分配给${issue.assigned_to_name}处理`,\n          time: this.formatHistoryTime(issue.updated_at || issue.created_at),\n          operator: '管理员'\n        });\n      }\n      \n      // 根据状态添加相应的历史记录\n      if (issue.status === 'in_progress') {\n        history.push({\n          action: '开始整改',\n          description: '负责人已开始处理此问题',\n          time: this.formatHistoryTime(issue.updated_at),\n          operator: issue.assigned_to_name || this.personData.name\n        });\n      } else if (issue.status === 'approved') {\n        history.push(\n          {\n            action: '开始整改',\n            description: '负责人已开始处理此问题',\n            time: this.formatHistoryTime(issue.updated_at),\n            operator: issue.assigned_to_name || this.personData.name\n          },\n          {\n            action: '整改完成',\n            description: '整改措施已实施完成',\n            time: this.formatHistoryTime(issue.actual_completion_date || issue.updated_at),\n            operator: issue.assigned_to_name || this.personData.name\n          }\n        );\n        \n        // 如果有解决日期，表示已经通过检查\n        if (issue.actual_completion_date) {\n          history.push(\n            {\n              action: '开始检查',\n              description: '检查员开始验证整改效果',\n              time: this.formatHistoryTime(issue.actual_completion_date),\n              operator: '检查员'\n            },\n            {\n              action: '检查通过',\n              description: '检查员确认整改合格',\n              time: this.formatHistoryTime(issue.actual_completion_date),\n              operator: '检查员'\n            }\n          );\n        }\n      }\n      \n      return history;\n    },\n\n    // 格式化历史记录时间\n    formatHistoryTime(dateString) {\n      if (!dateString) return '--';\n      \n      try {\n        const date = new Date(dateString);\n        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n      } catch (error) {\n        return '--';\n      }\n    },\n\n    viewIssueDetail(issue) {\n      // 补充完整的问题数据并存储\n      const completeIssue = {\n        ...issue,\n        responsible: this.personData.name,\n        role: this.personData.role,\n        createdAt: '2024-01-15',\n        updatedAt: '2024-01-15',\n        images: ['/static/empty/default-image.png'],\n        history: this.generateIssueHistory(issue)\n      };\n      \n      uni.setStorageSync(`issue_detail_${issue.id}`, completeIssue);\n      uni.navigateTo({\n        url: `/pages/6s_pkg/issue-detail?id=${issue.id}`\n      })\n    },\n\n    showMoreIssues() {\n      // 增加显示数量\n      this.displayLimit += this.displayStep;\n    },\n\n    showLoading(text = '加载中...') {\n      this.loading = true\n      this.loadingText = text\n    },\n\n    hideLoading() {\n      this.loading = false\n    },\n\n    delay(ms) {\n      return new Promise(resolve => setTimeout(resolve, ms));\n    },\n\n    // 处理数据更新事件\n    handleDataUpdated(data) {\n      // 清除缓存，确保获取最新数据\n      if (this.dataCache) {\n        this.dataCache.clear();\n      }\n      \n      // 延迟一下再刷新，确保提交操作完全完成\n      setTimeout(() => {\n        this.refreshDataSilently();\n      }, 200);\n    },\n\n    // 静默刷新数据（不显示加载状态）\n    async refreshDataSilently() {\n      if (this.loading) return; // 如果正在加载，跳过\n      \n      try {\n        const [issuesResult, userResult] = await Promise.all([\n          this.loadPersonIssues(),\n          this.loadPersonInfo()\n        ]);\n        \n        if (issuesResult && issuesResult.length > 0) {\n          const issues = issuesResult;\n          const userInfo = userResult || { name: this.personName, role: '负责人' };\n          \n          // 计算统计数据\n          const total = issues.length;\n          const completed = issues.filter(issue => \n            issue.status === 'approved'\n          ).length;\n          const inProgress = issues.filter(issue => issue.status === 'in_progress').length;\n          const overdue = issues.filter(issue => issue.status === 'overdue').length;\n          \n          this.personData = {\n            ...this.personData,\n            total,\n            completed,\n            inProgress,\n            overdue,\n            issues\n          };\n          \n          this.updateFilterCounts();\n          this.lastLoadTime = Date.now();\n        }\n      } catch (error) {\n        // 静默处理刷新失败\n      }\n    },\n\n    // 数据刷新方法（供外部调用）\n    async refreshData() {\n      this.dataCache.clear();\n      await this.loadPersonData();\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\n\twidth: 100%;\n\toverflow-x: hidden; /* 防止水平滚动 */\n\tbox-sizing: border-box;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;\n}\n\n.content {\n  padding: 32rpx;\n  padding-bottom: 120rpx;\n}\n\n/* 卡片通用样式 */\n.person-card,\n.issues-card,\n.chart-card {\n  background: #ffffff;\n  border-radius: 24rpx;\n  margin-bottom: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\n}\n\n/* 筛选卡片特殊样式 - 控制溢出行为 */\n.filter-card {\n  background: #ffffff;\n  border-radius: 24rpx;\n  margin-bottom: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\n}\n\n/* 负责人信息卡片 */\n.person-card {\n  padding: 32rpx;\n}\n\n.person-header {\n  display: flex;\n  align-items: center;\n  gap: 24rpx;\n  margin-bottom: 32rpx;\n}\n\n.person-avatar {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 50%;\n  background: rgba(0, 122, 255, 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.person-info {\n  flex: 1;\n}\n\n.person-name {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 8rpx;\n}\n\n.person-role {\n  font-size: 26rpx;\n  color: #8E8E93;\n}\n\n.progress-summary {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 32rpx;\n  margin-bottom: 32rpx;\n  text-align: center;\n}\n\n.summary-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.summary-number {\n  font-size: 40rpx;\n  font-weight: bold;\n  margin-bottom: 8rpx;\n\n  &.primary {\n    color: #007AFF;\n  }\n\n  &.success {\n    color: #34C759;\n  }\n\n  &.warning {\n    color: #FF9500;\n  }\n\n  &.danger {\n    color: #FF3B30;\n  }\n}\n\n.summary-label {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.completion-rate {\n  padding-top: 32rpx;\n  border-top: 1rpx solid #F0F0F0;\n}\n\n.rate-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.rate-label {\n  font-size: 28rpx;\n  color: #1C1C1E;\n  font-weight: 500;\n}\n\n.rate-value {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #007AFF;\n}\n\n.rate-bar {\n  height: 12rpx;\n  background: #F0F0F0;\n  border-radius: 6rpx;\n  overflow: hidden;\n}\n\n.rate-fill {\n  height: 100%;\n  background: linear-gradient(90deg, #007AFF 0%, #34C759 100%);\n  border-radius: 6rpx;\n  transition: width 0.5s ease;\n}\n\n/* 筛选卡片 */\n.filter-card {\n  overflow: hidden;\n}\n\n.filter-card .card-header {\n  padding: 32rpx 32rpx 24rpx 32rpx;\n  border-bottom: 1rpx solid #F0F0F0;\n}\n\n.filter-tabs {\n  white-space: nowrap;\n  padding: 12rpx 0 0 0;\n  margin-top: 0rpx;\n}\n\n.filter-tabs::-webkit-scrollbar {\n  display: none;\n}\n\n.filter-tab {\n  display: inline-block;\n  padding: 12rpx 20rpx;\n  border: 2rpx solid #E5E5E5;\n  background: #ffffff;\n  border-radius: 24rpx;\n  font-size: 24rpx;\n  color: #8E8E93;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  font-weight: 500;\n  letter-spacing: 0.3rpx;\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);\n  white-space: nowrap;\n  margin: 4rpx 12rpx 4rpx 0;\n\n  &.active {\n    border-color: #007AFF;\n    background: linear-gradient(135deg, #007AFF, #5AC8FA);\n    color: #ffffff;\n    transform: translateY(-2rpx);\n    box-shadow: 0 6rpx 16rpx rgba(0, 122, 255, 0.3);\n  }\n  \n  &:hover:not(.active) {\n    border-color: #B3D9FF;\n    background: #F8FBFF;\n    transform: translateY(-1rpx);\n    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\n  }\n}\n\n\n\n/* 问题列表卡片 */\n.issues-card {\n  overflow: hidden;\n}\n\n.card-header {\n  padding: 32rpx;\n  border-bottom: 1rpx solid #F0F0F0;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 4rpx;\n}\n\n.card-subtitle {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.issues-list {\n  padding: 0;\n}\n\n.issue-item {\n  display: flex;\n  align-items: center;\n  padding: 24rpx 32rpx;\n  border-bottom: 1rpx solid #F0F0F0;\n\n  &:last-child {\n    border-bottom: none;\n  }\n}\n\n.issue-icon {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 16rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 24rpx;\n  flex-shrink: 0;\n\n  &.icon-bg-approved {\n    background: rgba(52, 199, 89, 0.1);\n  }\n\n  &.icon-bg-in_progress {\n    background: rgba(255, 149, 0, 0.1);\n  }\n\n  &.icon-bg-overdue {\n    background: rgba(255, 59, 48, 0.1);\n  }\n\n  &.icon-bg-pending {\n    background: rgba(0, 122, 255, 0.1);\n  }\n\n  &.icon-bg-assigned {\n    background: rgba(0, 122, 255, 0.1);\n  }\n\n  &.icon-bg-pending_review {\n    background: rgba(88, 86, 214, 0.1);\n  }\n\n  &.icon-bg-approved {\n    background: rgba(52, 199, 89, 0.1);\n  }\n\n  &.icon-bg-rejected {\n    background: rgba(255, 149, 0, 0.1);\n  }\n}\n\n.issue-number {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #1C1C1E;\n}\n\n.issue-icon.icon-bg-approved .issue-number {\n  color: #34C759;\n}\n\n.issue-icon.icon-bg-in_progress .issue-number {\n  color: #FF9500;\n}\n\n.issue-icon.icon-bg-overdue .issue-number {\n  color: #FF3B30;\n}\n\n.issue-icon.icon-bg-pending .issue-number {\n  color: #007AFF;\n}\n\n.issue-icon.icon-bg-assigned .issue-number {\n  color: #007AFF;\n}\n\n.issue-icon.icon-bg-pending_review .issue-number {\n  color: #5856D6;\n}\n\n.issue-icon.icon-bg-approved .issue-number {\n  color: #34C759;\n}\n\n.issue-icon.icon-bg-rejected .issue-number {\n  color: #FF9500;\n}\n\n.issue-content {\n  flex: 1;\n}\n\n.issue-title {\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #1C1C1E;\n  margin-bottom: 8rpx;\n}\n\n.issue-location,\n.issue-deadline {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  font-size: 24rpx;\n  color: #8E8E93;\n  margin-bottom: 4rpx;\n}\n\n.issue-right {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n}\n\n.issue-status {\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  font-size: 22rpx;\n  font-weight: 600;\n  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.08);\n\n  &.status-approved {\n    background: linear-gradient(135deg, #E8F5E8, #F0FFF0);\n    color: #34C759;\n    border: 2rpx solid rgba(52, 199, 89, 0.2);\n  }\n\n  &.status-in_progress {\n    background: linear-gradient(135deg, #FFF4E6, #FFFBF0);\n    color: #FF9500;\n    border: 2rpx solid rgba(255, 149, 0, 0.2);\n  }\n\n  &.status-overdue {\n    background: linear-gradient(135deg, #FFE6E6, #FFF0F0);\n    color: #FF3B30;\n    border: 2rpx solid rgba(255, 59, 48, 0.2);\n  }\n\n  &.status-pending {\n    background: linear-gradient(135deg, #E6F3FF, #F0F7FF);\n    color: #007AFF;\n    border: 2rpx solid rgba(0, 122, 255, 0.2);\n  }\n\n  &.status-assigned {\n    background: linear-gradient(135deg, #ECFEFF, #F0FDFF);\n    color: #0891B2;\n    border: 2rpx solid rgba(8, 145, 178, 0.2);\n  }\n\n  &.status-pending_review {\n    background: linear-gradient(135deg, #F0EFFF, #F8F7FF);\n    color: #5856D6;\n    border: 2rpx solid rgba(88, 86, 214, 0.2);\n  }\n\n  &.status-approved {\n    background: linear-gradient(135deg, #E8F5E8, #F0FFF0);\n    color: #34C759;\n    border: 2rpx solid rgba(52, 199, 89, 0.2);\n  }\n\n  &.status-rejected {\n    background: linear-gradient(135deg, #F2F2F7, #F8F8F8);\n    color: #8E8E93;\n    border: 2rpx solid rgba(142, 142, 147, 0.2);\n  }\n}\n\n/* 查看更多按钮样式 */\n.more-section {\n  padding: 28rpx;\n  border-top: 1rpx solid #F0F0F0;\n}\n\n.more-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12rpx;\n  padding: 28rpx;\n  font-size: 30rpx;\n  color: #007AFF;\n  border: 2rpx solid rgba(0, 122, 255, 0.2);\n  border-radius: 16rpx;\n  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(255, 255, 255, 0.9));\n  font-weight: 600;\n  letter-spacing: 0.5rpx;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.more-btn:active {\n  transform: translateY(2rpx);\n  background: linear-gradient(135deg, rgba(230, 243, 255, 0.9), rgba(240, 247, 255, 0.95));\n  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.15);\n  transition: all 0.1s ease;\n}\n\n.more-btn:not(:active) {\n  transform: translateY(0);\n}\n\n/* 底部安全间距 */\n.bottom-safe-area {\n  height: 40rpx;\n}\n\n/* 加载状态 - 自定义遮罩 */\n.custom-loading-mask {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(0, 0, 0, 0.4);\n  backdrop-filter: blur(8rpx);\n  z-index: 9999;\n  animation: maskFadeIn 0.3s ease-out;\n  /* 确保相对于视口定位，而不是父容器 */\n  margin: 0;\n  padding: 0;\n}\n\n@keyframes maskFadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n.loading-container-enhanced {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 32rpx;\n  padding: 80rpx 60rpx;\n  background: rgba(255, 255, 255, 0.98);\n  backdrop-filter: blur(20rpx);\n  border-radius: 32rpx;\n  min-width: 320rpx;\n  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.25), \n              0 4rpx 16rpx rgba(0, 0, 0, 0.15);\n  border: 1rpx solid rgba(255, 255, 255, 0.4);\n  animation: containerSlideIn 0.4s ease-out 0.1s both;\n}\n\n@keyframes containerSlideIn {\n  from {\n    opacity: 0;\n    transform: translateY(20rpx) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n.loading-text-enhanced {\n  font-size: 32rpx;\n  color: #1C1C1E;\n  font-weight: 600;\n  margin-top: 8rpx;\n  text-align: center;\n  line-height: 1.3;\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./person-progress.vue?vue&type=style&index=0&id=1ef03a4e&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./person-progress.vue?vue&type=style&index=0&id=1ef03a4e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775844038\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}