<template>
  <view class="page-container">
    <view class="card">
      <view class="card-header">
        <view class="header-content">
          <view class="card-title">{{ areaInfo.name || '责任区清理' }}</view>
          <view class="card-subtitle">{{ mode === 'edit' ? '修改清理记录' : getAreaTypeText(areaInfo.type) }}</view>
        </view>
      </view>
      <view class="card-body">
        <view class="upload-section">
          <view class="section-header">
            <view class="section-title">上传清理照片</view>
            <view class="auto-upload-toggle" @click="toggleAutoUpload">
              <view class="toggle-label">自动上传</view>
              <view class="toggle-switch" :class="{ active: autoUpload }">
                <view class="toggle-circle"></view>
              </view>
            </view>
          </view>
          <view class="photo-grid">
            <view v-for="(photo, index) in photos" :key="index" class="photo-item">
              <image :src="getPhotoDisplayUrl(photo)" mode="aspectFill" @click="previewPhoto(index)"></image>
              <!-- 上传状态指示器 -->
              <view v-if="photo.uploading" class="photo-uploading">
                <view class="upload-spinner"></view>
              </view>
              <view v-else-if="photo.uploaded" class="photo-uploaded">
                <uni-icons type="checkmarkempty" size="16" color="white"></uni-icons>
              </view>
              <view class="photo-delete" @click="deletePhoto(index)">
                <uni-icons type="close" size="18" color="white"></uni-icons>
              </view>
            </view>
            <view v-if="photos.length < 12" class="photo-add" @click="addPhoto" :class="{ disabled: uploading }">
              <uni-icons type="camera" size="32" color="#8E8E93"></uni-icons>
              <text>添加照片</text>
            </view>
          </view>
          <!-- 上传进度 -->
          <view v-if="uploading" class="upload-progress">
            <view class="progress-bar">
              <view class="progress-fill" :style="{ width: uploadProgress + '%' }"></view>
            </view>
            <text class="progress-text">正在上传照片... {{ uploadProgress }}%</text>
          </view>
          <view class="photo-tip">最多可上传12张照片，建议拍摄清理前后对比照片</view>
        </view>
        
        <view class="remarks-section">
          <view class="section-title">清理备注（可选）</view>
          <view class="remarks-input-container">
            <textarea 
              v-model="remarks" 
              placeholder="请描述清理过程中发现的问题或需要注意的事项..."
              maxlength="200"
              class="remarks-input"
              @input="handleRemarksInput"
            ></textarea>
            <view class="char-count-overlay">{{ remarksLength }}/200</view>
          </view>
        </view>
      </view>
    </view>
    
    <view class="button-container">
      <button 
        class="primary-button" 
        @click="submitCleaning" 
        :disabled="photos.length === 0 || loading || uploading"
        :class="{ loading: loading || uploading }"
      >
        <view v-if="loading || uploading" class="button-loading">
          <view class="loading-spinner"></view>
          <text>{{ uploading ? '上传中...' : '提交中...' }}</text>
        </view>
        <text v-else>{{ mode === 'edit' ? '保存修改' : '提交清理记录' }}</text>
      </button>
    </view>
  </view>
</template>

<script>
import uploadUtils from '@/utils/upload-utils.js'
import HygieneAPI from '@/utils/api/hygiene.js'

export default {
  name: 'CleaningUpload',
  data() {
    return {
      mode: 'create', // 'create' 或 'edit'
      recordId: '', // 编辑模式下的记录ID
      areaInfo: {},
      photos: [],
      remarks: '',
      loading: false,
      uploading: false,
      uploadProgress: 0,
      autoUpload: true, // 自动上传开关
      uploadQueue: [], // 上传队列
      
      // 性能优化缓存
      processCache: {
        areaTypeMap: null,
        photoUrlCache: new Map() // 缓存照片URL处理结果
      }
    }
  },
  computed: {
    // 计算备注长度，确保响应式更新
    remarksLength() {
      return this.remarks ? this.remarks.length : 0;
    }
  },
  onLoad(options) {
    // 获取参数
    this.mode = options.mode || 'create';
    this.recordId = options.recordId || '';
    const areaId = options.areaId || options.area_id || options.id;
    const areaType = options.type || 'fixed'; // 获取区域类型参数
    
    // 初始化缓存
    this.initProcessCache();
    
    // 如果有区域类型参数，先设置到 areaInfo 中（防止在 API 调用完成前访问时出错）
    if (areaType) {
      this.areaInfo.type = areaType;
    }
    
    // 加载数据
    this.loadAreaInfoOptimized(areaId);
    
    // 如果是编辑模式，加载记录数据
    if (this.mode === 'edit' && this.recordId) {
      this.loadRecordDataOptimized();
    }
  },
  methods: {
    // 初始化处理缓存
    initProcessCache() {
      if (!this.processCache.areaTypeMap) {
        this.processCache.areaTypeMap = {
          'fixed': '固定责任区清理',
          'public': '公共责任区清理'
        };
      }
    },

    // 处理备注输入，确保字符限制和响应式更新
    handleRemarksInput(e) {
      let value = e.detail.value || '';
      
      // 强制限制字符数量
      if (value.length > 200) {
        value = value.substring(0, 200);
        // 如果超出限制，显示提示
        uni.showToast({
          title: '备注不能超过200个字符',
          icon: 'none',
          duration: 1500
        });
      }
      
      // 更新数据
      this.remarks = value;
      
      // 强制触发视图更新
      this.$forceUpdate();
    },

    // 获取责任区信息（优化版）
    async loadAreaInfoOptimized(areaId) {
      if (!areaId) {
        uni.showToast({
          title: '责任区ID不能为空',
          icon: 'none'
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
        return;
      }

      try {
        const areaDetail = await HygieneAPI.getAreaDetail(areaId);
        // 保留之前从 URL 参数设置的 type（如果 API 返回的数据中没有 type 字段）
        this.areaInfo = {
          ...areaDetail,
          type: areaDetail.type || this.areaInfo.type || 'fixed'
        };
      } catch (error) {
        this.handleLoadError(error, () => this.loadAreaInfoOptimized(areaId));
      }
    },

    // 统一错误处理
    handleLoadError(error, retryCallback) {
      let errorMessage = '获取数据失败';
      if (error.message.includes('未登录')) {
        errorMessage = '请先登录';
      } else if (error.message.includes('权限')) {
        errorMessage = '您没有权限查看该责任区';
      } else if (error.message.includes('不存在')) {
        errorMessage = '责任区不存在或已删除';
      } else {
        errorMessage = error.message || '网络连接失败，请稍后重试';
      }
      
      uni.showModal({
        title: '获取数据失败',
        content: errorMessage,
        showCancel: true,
        cancelText: '返回',
        confirmText: '重试',
        success: (res) => {
          if (res.confirm) {
            retryCallback();
          } else {
            uni.navigateBack();
          }
        }
      });
    },

    // 获取责任区信息（保留原方法以防其他地方调用）
    async loadAreaInfo(areaId) {
      return this.loadAreaInfoOptimized(areaId);
    },

    // 加载记录数据（优化版）
    async loadRecordDataOptimized() {
      if (!this.recordId) {
        return;
      }

      try {
        uni.showLoading({ title: '加载记录数据...' });
        
        const result = await uniCloud.callFunction({
          name: 'hygiene-cleaning',
          data: {
            action: 'getCleaningRecordDetail',
            data: { record_id: this.recordId }
          }
        });

        if (result.result?.success && result.result.data) {
          const record = result.result.data;
          this.processRecordData(record);
        } else {
          throw new Error(result.result?.message || '获取记录数据失败');
        }
      } catch (error) {
        this.handleLoadError(error, () => this.loadRecordDataOptimized());
      } finally {
        uni.hideLoading();
      }
    },

    // 处理记录数据
    processRecordData(record) {
      // 填充备注
      this.remarks = record.remark || '';
      
      // 填充照片并预处理URL
      if (record.photos?.length > 0) {
        this.photos = record.photos.map(photo => {
          const photoData = {
            url: photo.url || photo,
            uploaded: true,
            cloudUrl: photo.url || photo,
            cloudPath: photo.url || photo,
            uploading: false,
            belongsToRecord: true
          };
          
          // 预缓存照片URL
          const cacheKey = photoData.url;
          if (!this.processCache.photoUrlCache.has(cacheKey)) {
            this.processCache.photoUrlCache.set(cacheKey, this.processPhotoUrl(photoData.url));
          }
          
          return photoData;
        });
      }
    },

    // 照片URL处理
    processPhotoUrl(url) {
      if (typeof url === 'string' && url.startsWith('http://tmp/')) {
        return url; // 微信小程序本地临时文件路径
      }
      return url;
    },

    // 原方法保留以防其他地方调用
    async loadRecordData() {
      return this.loadRecordDataOptimized();
    },



    // 优化的添加照片方法
    addPhoto() {
      if (this.photos.length >= 12) {
        uni.showToast({
          title: '最多只能上传12张照片',
          icon: 'none'
        });
        return;
      }

      uni.chooseImage({
        count: 12 - this.photos.length,
        sizeType: ['compressed'], // 只使用压缩版本以节省内存和上传时间
        sourceType: ['camera', 'album'],
        success: (res) => {
          const newPhotos = this.processNewPhotos(res.tempFilePaths);
          
          // 批量添加到照片列表
          this.photos.push(...newPhotos);
          
          // 如果开启自动上传，立即上传新选择的照片
          if (this.autoUpload) {
            this.autoUploadNewPhotosOptimized(newPhotos);
          }
        },
        fail: () => {
          uni.showToast({
            title: '选择照片失败',
            icon: 'none'
          });
        }
      });
    },

    // 处理新选择的照片
    processNewPhotos(tempFilePaths) {
      return tempFilePaths.map(path => {
        const photoData = {
          url: path,
          uploaded: false,
          cloudUrl: '',
          cloudPath: '',
          uploading: false,
          belongsToRecord: false
        };
        
        // 预缓存照片URL
        const cacheKey = path;
        if (!this.processCache.photoUrlCache.has(cacheKey)) {
          this.processCache.photoUrlCache.set(cacheKey, this.processPhotoUrl(path));
        }
        
        return photoData;
      });
    },
    
    async deletePhoto(index) {
      if (index < 0 || index >= this.photos.length) {
        return;
      }

      const photo = this.photos[index];
      
      // 如果照片已经上传到云端，需要删除云端文件
      if (photo.uploaded && photo.cloudPath) {
        try {
          uni.showLoading({ title: '删除照片中...' });
          
          // 区分已保存到记录的照片和新上传的照片
          if (this.mode === 'edit' && this.recordId && photo.belongsToRecord !== false) {
            // 编辑模式下，属于记录的照片通过API删除
            await HygieneAPI.deleteCleaningPhotos(this.recordId, [photo.cloudPath]);
          } else {
            // 新上传但未保存的照片，直接调用删除云文件的云函数
            await uniCloud.callFunction({
              name: 'delete-file',
              data: {
                fileList: [this.extractFileId(photo.cloudPath)]
              }
            });
          }
        } catch (error) {
          uni.showToast({
            title: '删除云端照片失败',
            icon: 'none'
          });
        } finally {
          uni.hideLoading();
        }
      }
      
      // 从本地数组中移除
      this.photos.splice(index, 1);
    },

    // 从URL中提取文件ID
    extractFileId(url) {
      if (url.startsWith('cloud://')) {
        const parts = url.split('/');
        return parts[parts.length - 1];
      } else if (url.includes('tcb-api')) {
        const urlObj = new URL(url);
        return urlObj.pathname.split('/').pop();
      }
      return url;
    },
    
    previewPhoto(index) {
      const urls = this.photos.map(photo => this.getPhotoDisplayUrl(photo));
      uni.previewImage({
        urls: urls,
        current: index
      });
    },

    // 优化的自动上传新选择的照片
    async autoUploadNewPhotosOptimized(newPhotos) {
      // 使用 Promise.allSettled 并行上传，避免阻塞
      const uploadPromises = newPhotos.map(async (photo, i) => {
        const photoIndex = this.photos.findIndex(p => p.url === photo.url);
        
        if (photoIndex === -1) return { success: false, index: i };
        
        try {
          // 标记为正在上传
          this.$set(this.photos[photoIndex], 'uploading', true);
          
          // 单张照片上传
          const uploadResult = await this.uploadSinglePhotoOptimized(photo);
          
          if (uploadResult.success) {
            // 批量更新照片信息
            Object.assign(this.photos[photoIndex], {
              uploaded: true,
              cloudUrl: uploadResult.url,
              cloudPath: uploadResult.cloudPath,
              uploading: false
            });
            return { success: true, index: i };
          } else {
            throw new Error(uploadResult.error || '上传失败');
          }
        } catch (error) {
          this.$set(this.photos[photoIndex], 'uploading', false);
          return { success: false, index: i, error: error.message };
        }
      });

      // 等待所有上传完成
      const results = await Promise.allSettled(uploadPromises);
      
      // 统计失败的上传
      const failures = results
        .filter(result => result.status === 'fulfilled' && !result.value.success)
        .map(result => result.value);
      
      if (failures.length > 0) {
        uni.showToast({
          title: `${failures.length}张照片上传失败`,
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 保留原方法以防其他地方调用
    async autoUploadNewPhotos(newPhotos) {
      return this.autoUploadNewPhotosOptimized(newPhotos);
    },

    // 优化的单张照片上传
    async uploadSinglePhotoOptimized(photo) {
      try {
        const cloudPath = this.generateCloudPath();
        
        // 使用 uploadToCloud 方法上传单张照片
        const uploadResult = await uploadUtils.uploadToCloud(photo.url, cloudPath);
        
        if (uploadResult?.fileID) {
          // 获取访问URL
          const fileInfo = await uploadUtils.getFileInfo(uploadResult.fileID);
          
          return {
            success: true,
            cloudPath: uploadResult.fileID,
            url: fileInfo.tempFileURL || uploadResult.fileID,
            size: uploadResult.actualSize
          };
        } else {
          throw new Error('上传返回结果异常');
        }
      } catch (error) {
        return { success: false, error: error.message };
      }
    },

    // 生成云存储路径
    generateCloudPath() {
      const timestamp = Date.now();
      const random = Math.random().toString(36).substring(2, 8);
      return `6s/cleaning/${this.areaInfo._id}/${timestamp}_${random}.jpg`;
    },

    // 保留原方法以防其他地方调用
    async uploadSinglePhoto(photo) {
      return this.uploadSinglePhotoOptimized(photo);
    },

    // 切换自动上传状态
    toggleAutoUpload() {
      this.autoUpload = !this.autoUpload;
      uni.showToast({
        title: this.autoUpload ? '已开启自动上传' : '已关闭自动上传',
        icon: 'none'
      });
    },
    
    // 优化的上传照片到云存储
    async uploadPhotosOptimized() {
      if (this.photos.length === 0) {
        return [];
      }

      this.uploading = true;
      this.uploadProgress = 0;

      try {
        // 只上传未上传的照片
        const unuploadedPhotos = this.photos.filter(photo => !photo.uploaded);
        if (unuploadedPhotos.length === 0) {
          // 所有照片都已上传，直接返回已上传的照片信息
          return this.photos
            .filter(photo => photo.uploaded)
            .map(photo => ({
              url: photo.cloudPath,
              type: 'cleaning',
              description: ''
            }));
        }

        const photoPaths = unuploadedPhotos.map(photo => photo.url);
        
        const uploadResult = await uploadUtils.batchUpload(photoPaths, {
          onProgress: (progress) => {
            this.uploadProgress = progress.progress;
          },
          pathGenerator: () => this.generateCloudPath(),
          maxConcurrent: 3 // 增加并发数以提高上传速度
        });

        if (uploadResult.success) {
          // 更新照片信息
          uploadResult.results.forEach((result, index) => {
            const originalIndex = this.photos.findIndex(p => p.url === unuploadedPhotos[index].url);
            if (result.success && originalIndex !== -1) {
              Object.assign(this.photos[originalIndex], {
                uploaded: true,
                cloudUrl: result.url,
                cloudPath: result.cloudPath
              });
            }
          });

          // 返回所有已上传的照片信息
          return this.photos
            .filter(photo => photo.uploaded)
            .map(photo => ({
              url: photo.cloudPath,
              type: 'cleaning',
              description: ''
            }));
        } else {
          throw new Error('照片上传失败');
        }
      } catch (error) {
        throw error;
      } finally {
        this.uploading = false;
        this.uploadProgress = 0;
      }
    },

    // 保留原方法以防其他地方调用
    async uploadPhotos() {
      return this.uploadPhotosOptimized();
    },
    
    // 优化的提交清理记录
    async submitCleaning() {
      if (this.photos.length === 0) {
        uni.showToast({
          title: '请至少上传一张照片',
          icon: 'none'
        });
        return;
      }

      if (this.loading) return;

      this.loading = true;
      
      try {
        // 准备上传的照片数据
        const uploadedPhotos = await this.preparePhotosForSubmit();
        
        if (uploadedPhotos.length === 0) {
          throw new Error('没有可用的照片，请重新上传');
        }

        // 提交数据
        const submissionData = this.prepareSubmissionData(uploadedPhotos);
        
        uni.showLoading({
          title: '提交清理记录...'
        });

        // 根据模式调用不同的API
        if (this.mode === 'edit') {
          await HygieneAPI.updateCleaningRecord(this.recordId, submissionData);
        } else {
          await HygieneAPI.createCleaningRecord(submissionData);
        }

        this.handleSubmitSuccess();
      } catch (error) {
        this.handleSubmitError(error);
      } finally {
        this.loading = false;
      }
    },

    // 准备照片数据用于提交
    async preparePhotosForSubmit() {
      const unuploadedPhotos = this.photos.filter(photo => !photo.uploaded && !photo.uploading);
      
      if (unuploadedPhotos.length > 0) {
        uni.showLoading({ title: '正在上传剩余照片...' });
        return await this.uploadPhotosOptimized();
      } else {
        // 所有照片都已上传，直接使用已上传的照片
        return this.photos
          .filter(photo => photo.uploaded)
          .map(photo => ({
            url: photo.cloudPath,
            type: 'cleaning',
            description: ''
          }));
      }
    },

    // 准备提交数据
    prepareSubmissionData(uploadedPhotos) {
      // 确保备注不超过限制
      let finalRemarks = this.remarks.trim();
      if (finalRemarks.length > 200) {
        finalRemarks = finalRemarks.substring(0, 200);
        console.warn('备注被截断到200字符');
      }
      
      const baseData = {
        photos: uploadedPhotos,
        remark: finalRemarks
      };

      if (this.mode === 'create') {
        return {
          ...baseData,
          area_id: this.areaInfo._id,
          cleaning_date: new Date().toISOString(),
          completion_status: 'completed'
        };
      }

      return baseData;
    },

    // 处理提交成功
    handleSubmitSuccess() {
      uni.hideLoading();
      uni.showToast({
        title: this.mode === 'edit' ? '修改成功' : '提交成功',
        icon: 'success'
      });
      
      // 通知其他页面数据已更新
      uni.$emit('cleaningRecordUpdated', {
        mode: this.mode,
        recordId: this.recordId,
        areaId: this.areaInfo._id
      });
      
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    },

    // 处理提交错误
    handleSubmitError(error) {
      uni.hideLoading();
      
      let errorMessage = '提交失败，请重试';
      if (error.message) {
        if (error.message.includes('未登录')) {
          errorMessage = '请先登录';
        } else if (error.message.includes('权限')) {
          errorMessage = '您没有权限操作该责任区';
        } else {
          errorMessage = error.message;
        }
      }
      
      uni.showModal({
        title: '提交失败',
        content: errorMessage,
        showCancel: false,
        confirmText: '知道了'
      });
    },

    // 获取责任区类型文本（使用缓存）
    getAreaTypeText(type) {
      // 确保缓存已初始化
      if (!this.processCache.areaTypeMap) {
        this.initProcessCache();
      }
      
      // 安全访问缓存
      return (this.processCache.areaTypeMap && this.processCache.areaTypeMap[type]) || '责任区清理';
    },

    // 获取照片显示URL（优化版，使用缓存）
    getPhotoDisplayUrl(photo) {
      const url = photo.url || photo;
      const cacheKey = url;
      
      // 确保缓存已初始化
      if (!this.processCache.photoUrlCache) {
        this.initProcessCache();
      }
      
      // 先检查缓存
      if (this.processCache.photoUrlCache.has(cacheKey)) {
        return this.processCache.photoUrlCache.get(cacheKey);
      }
      
      // 处理URL并缓存
      const processedUrl = this.processPhotoUrl(url);
      this.processCache.photoUrlCache.set(cacheKey, processedUrl);
      
      return processedUrl;
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
	min-height: 100vh;
	background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
	width: 100%;
	overflow-x: hidden; /* 防止水平滚动 */
	box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
  min-height: 100vh;
  padding: 24rpx;
}

.card {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 32rpx 32rpx 16rpx 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
}

.card-body {
  padding: 32rpx;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
}

.card-subtitle {
  font-size: 26rpx;
  color: #8E8E93;
}

.upload-section, .remarks-section {
  margin-bottom: 32rpx;
}

.remarks-section .section-title {
  margin-bottom: 16rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
}

.auto-upload-toggle {
  display: flex;
  align-items: center;
  gap: 12rpx;
  cursor: pointer;
}

.toggle-label {
  font-size: 24rpx;
  color: #8E8E93;
}

.toggle-switch {
  width: 76rpx;
  height: 44rpx;
  background: #E5E5EA;
  border-radius: 22rpx;
  position: relative;
  transition: background-color 0.3s ease;
}

.toggle-switch.active {
  background: #34C759;
}

.toggle-circle {
  width: 36rpx;
  height: 36rpx;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  transition: transform 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.toggle-switch.active .toggle-circle {
  transform: translateX(32rpx);
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.photo-item {
  position: relative;
  width: 170rpx;
  height: 170rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.photo-item image {
  width: 100%;
  height: 100%;
}

.photo-delete {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  width: 36rpx;
  height: 36rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.photo-add {
  width: 170rpx;
  height: 170rpx;
  border: 2rpx dashed #E5E5EA;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  background: #F8F9FA;
}

.photo-add text {
  font-size: 24rpx;
  color: #8E8E93;
}

.photo-tip {
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.4;
}

.remarks-input-container {
  position: relative;
  width: 100%;
}

.remarks-input {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx 16rpx 32rpx 16rpx; /* 恢复正常的右侧padding，让文字可以占用全宽 */
  border: 1rpx solid #E5E5EA;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  background: #F8F9FA;
  box-sizing: border-box;
  resize: none; /* 防止拖拽改变大小 */
  line-height: 1.5;
}

.char-count-overlay {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  font-size: 22rpx;
  color: #8E8E93;
  background: rgba(248, 249, 250, 0.9); /* 半透明背景 */
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  pointer-events: none; /* 防止遮挡输入 */
  z-index: 2;
  backdrop-filter: blur(4rpx); /* 添加模糊效果 */
}

.button-container {
  padding: 32rpx 0;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.primary-button {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  width: 100%;
}

.primary-button[disabled] {
  background: #C7C7CC;
  color: #8E8E93;
}

.primary-button.loading {
  background: #0056D6;
  opacity: 0.9;
}

.button-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  color: white;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.photo-uploading {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-spinner {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-top: 2rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.photo-uploaded {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #34C759;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.photo-add.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.upload-progress {
  margin: 16rpx 0;
  padding: 16rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #E5E5EA;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007AFF 0%, #5AC8FA 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #8E8E93;
  text-align: center;
  display: block;
}

/* H5端优化 - 支持更多照片展示 */
/* #ifdef H5 */
@media screen and (min-width: 600px) {
  .photo-grid {
    max-width: 600px;
  }
  
  .photo-item, .photo-add {
    width: 130px;
    height: 130px;
    max-width: 130px;
    max-height: 130px;
  }
}

@media screen and (min-width: 900px) {
  .photo-grid {
    max-width: 800px;
  }
  
  .photo-item, .photo-add {
    width: 140px;
    height: 140px;
    max-width: 140px;
    max-height: 140px;
  }
}
/* #endif */

</style>