{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-detail.vue?8eb3", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-detail.vue?c23a", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-detail.vue?be03", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-detail.vue?24b3", "uni-app:///pages/6s_pkg/rectification-detail.vue", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-detail.vue?33df", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-detail.vue?f630"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "taskInfo", "loading", "loadError", "taskId", "processCache", "dataLoaded", "computedData", "isCompleted", "statusText", "categoryText", "reviewResultText", "onLoad", "uni", "onUnload", "methods", "initProcessCache", "statusMap", "categoryMap", "reviewResultMap", "completedStatuses", "dateF<PERSON><PERSON><PERSON>", "createOptimizedDateFormatter", "date", "loadTaskDetailOptimized", "action", "id", "result", "task", "processTaskDataOptimized", "area", "areaName", "isPublic", "status", "problemDescription", "issueFoundDate", "inspector", "rectificationDescription", "photos", "completionPhotos", "completedDate", "rectificationSubmitTime", "assignee_name", "category", "deadline", "startDate", "reviewResult", "reviewComments", "reviewDate", "reviewerName", "reviewPhotos", "processPhotos", "url", "precomputeTaskStates", "loadTaskDetail", "loadTaskDetailOriginal", "retryLoad", "formatDateTimeOptimized", "formatDateTime", "getStatusText", "getCategoryText", "isTaskCompleted", "getReviewResultText", "previewPhoto", "urls", "current", "goBack", "handleRecordUpdated"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,4BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6I;AAC7I;AACwE;AACL;AACsC;;;AAGzG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,0FAAM;AACR,EAAE,2GAAM;AACR,EAAE,oHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChHA;AAAA;AAAA;AAAA;AAA8mB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACoKloB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;;IAEA;IACAC;EACA;EACAC;IACA;IACAD;EACA;EAEAE;IACA;IACAC;MACA;QACA;UACA;UACAC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA;UACA;UACAC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA;UACA;UACAC;YACA;YACA;YACA;UACA;UACA;UACAC;UACA;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QAEA;UACA;UACA;YACA;cACAC;YACA;cACAA;YACA;UACA;YACAA;UACA;UAEA;YACA;UACA;UAEA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACApB;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBAAA;gBAGA;gBACA;gBAAA;gBAAA,OAEA;kBACAqB;kBACAzB;oBAAA0B;kBAAA;gBACA;cAAA;gBAHAC;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACAC,oBAEA;gBACA;;gBAEA;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;MAEA;QACAH;QACAI;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;UACA;YAAAC;UAAA;QACA;UACA;YAAAA;UAAA;QACA;QACA;UAAAA;QAAA;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MAEA;QACA7C;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACA2C;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACAnD;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBAAA;gBAAA;gBAAA,OAGA;kBACAqB;kBACAzB;oBAAA0B;kBAAA;gBACA;cAAA;gBAHAC;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACAC;gBAEA;kBACAF;kBACAI;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBAAA;kBACAC;kBAAA;kBACAC;kBACAC;kBACAC;kBACA;kBACAC;kBACAC;kBACAC;kBACA;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAO;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;QACA;UACA;YACAnC;UACA;YACAA;UACA;QACA;UACAA;QACA;QAEA;UACA;QACA;QAEA;MACA;QAEA;MACA;IACA;IAEA;IACAoC;MACA;QACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MACA;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEA;QACAzB;MACA;QACAA;MACA;QACAA;MACA;QACA;MACA;;MAEA;MACA;QACA0B;MACA;QACAA;UAAA;QAAA;UAAA;QAAA;QACA;QACA1B;MACA;MAEA;MAEAzB;QACAmD;QACAC;MACA;IACA;IAEA;IACAC;MACArD;IACA;IAEA;IACAsD;MACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3jBA;AAAA;AAAA;AAAA;AAAirC,CAAgB,upCAAG,EAAC,C;;;;;;;;;;;ACArsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/rectification-detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/rectification-detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./rectification-detail.vue?vue&type=template&id=ccc26d00&scoped=true&\"\nvar renderjs\nimport script from \"./rectification-detail.vue?vue&type=script&lang=js&\"\nexport * from \"./rectification-detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./rectification-detail.vue?vue&type=style&index=0&id=ccc26d00&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ccc26d00\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/rectification-detail.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rectification-detail.vue?vue&type=template&id=ccc26d00&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    !_vm.loading && !_vm.loadError\n      ? _vm.formatDateTimeOptimized(_vm.taskInfo.issueFoundDate)\n      : null\n  var m1 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    _vm.computedData.isCompleted &&\n    _vm.taskInfo.completedDate\n      ? _vm.formatDateTimeOptimized(_vm.taskInfo.completedDate)\n      : null\n  var m2 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    !(_vm.computedData.isCompleted && _vm.taskInfo.completedDate) &&\n    !_vm.computedData.isCompleted &&\n    _vm.taskInfo.deadline\n      ? _vm.formatDateTimeOptimized(_vm.taskInfo.deadline)\n      : null\n  var m3 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    _vm.taskInfo.status === \"in_progress\" &&\n    _vm.taskInfo.startDate\n      ? _vm.formatDateTimeOptimized(_vm.taskInfo.startDate)\n      : null\n  var g0 =\n    !_vm.loading && !_vm.loadError\n      ? _vm.taskInfo.photos && _vm.taskInfo.photos.length > 0\n      : null\n  var g1 =\n    !_vm.loading && !_vm.loadError && g0 ? _vm.taskInfo.photos.length : null\n  var g2 =\n    !_vm.loading && !_vm.loadError\n      ? _vm.taskInfo.completionPhotos &&\n        _vm.taskInfo.completionPhotos.length > 0\n      : null\n  var g3 =\n    !_vm.loading && !_vm.loadError && g2\n      ? _vm.taskInfo.completionPhotos.length\n      : null\n  var m4 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    (_vm.taskInfo.reviewResult || _vm.taskInfo.reviewComments) &&\n    _vm.taskInfo.reviewDate\n      ? _vm.formatDateTimeOptimized(_vm.taskInfo.reviewDate)\n      : null\n  var g4 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    (_vm.taskInfo.reviewResult || _vm.taskInfo.reviewComments)\n      ? _vm.taskInfo.reviewPhotos && _vm.taskInfo.reviewPhotos.length > 0\n      : null\n  var g5 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    (_vm.taskInfo.reviewResult || _vm.taskInfo.reviewComments) &&\n    g4\n      ? _vm.taskInfo.reviewPhotos.length\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        m4: m4,\n        g4: g4,\n        g5: g5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rectification-detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rectification-detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page-container\">\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-container\">\n      <view class=\"loading-content\">\n        <view class=\"loading-spinner\"></view>\n        <text class=\"loading-text\">加载整改任务详情中...</text>\n      </view>\n    </view>\n    \n    <!-- 错误状态 -->\n    <view v-else-if=\"loadError\" class=\"error-container\">\n      <view class=\"error-content\">\n        <uni-icons type=\"info\" size=\"48\" color=\"#FF3B30\"></uni-icons>\n        <text class=\"error-text\">{{ loadError }}</text>\n        <button class=\"retry-button\" @click=\"retryLoad\">重新加载</button>\n      </view>\n    </view>\n    \n    <!-- 正常内容 -->\n    <template v-else>\n      <!-- 1. 整改任务基本信息 -->\n      <view class=\"card\">\n        <view class=\"card-header\">\n          <view class=\"header-content\">\n            <view class=\"card-title\">整改任务详情</view>\n            <view class=\"card-subtitle\">{{ taskInfo.area }} - {{ taskInfo.isPublic ? '公共责任区' : '固定责任区' }}</view>\n          </view>\n        </view>\n        <view class=\"card-body\">\n          <view class=\"task-info\">\n            <view class=\"info-item\">\n              <text class=\"info-label\">状态：</text>\n              <view class=\"status-badge\" :class=\"['status-' + taskInfo.status]\">{{ computedData.statusText }}</view>\n            </view>\n            <view class=\"info-item\">\n              <text class=\"info-label\">问题类别：</text>\n              <text class=\"info-value\">{{ computedData.categoryText }}</text>\n            </view>\n            <view class=\"info-item\">\n              <text class=\"info-label\">问题描述：</text>\n              <text class=\"info-value\">{{ taskInfo.problemDescription }}</text>\n            </view>\n            <view class=\"info-item\">\n              <text class=\"info-label\">发现时间：</text>\n              <text class=\"info-value\">{{ formatDateTimeOptimized(taskInfo.issueFoundDate) }}</text>\n            </view>\n            <view class=\"info-item\">\n              <text class=\"info-label\">检查员：</text>\n              <text class=\"info-value\">{{ taskInfo.inspector }}</text>\n            </view>\n            <view v-if=\"taskInfo.assignee_name\" class=\"info-item\">\n              <text class=\"info-label\">负责人：</text>\n              <text class=\"info-value\">{{ taskInfo.assignee_name }}</text>\n            </view>\n            <!-- 根据任务状态显示不同的时间信息 -->\n            <view v-if=\"computedData.isCompleted && taskInfo.completedDate\" class=\"info-item\">\n              <text class=\"info-label\">完成时间：</text>\n              <text class=\"info-value\">{{ formatDateTimeOptimized(taskInfo.completedDate) }}</text>\n            </view>\n            <view v-else-if=\"!computedData.isCompleted && taskInfo.deadline\" class=\"info-item\">\n              <text class=\"info-label\">截止时间：</text>\n              <text class=\"info-value\">{{ formatDateTimeOptimized(taskInfo.deadline) }}</text>\n            </view>\n            <!-- 只在进行中的任务显示开始时间 -->\n            <view v-if=\"taskInfo.status === 'in_progress' && taskInfo.startDate\" class=\"info-item\">\n              <text class=\"info-label\">开始时间：</text>\n              <text class=\"info-value\">{{ formatDateTimeOptimized(taskInfo.startDate) }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 2. 原始问题照片 - 紧跟问题描述后显示 -->\n      <view v-if=\"taskInfo.photos && taskInfo.photos.length > 0\" class=\"card\">\n        <view class=\"card-header\">\n          <view class=\"card-title\">问题照片</view>\n          <text class=\"photo-count\">{{ taskInfo.photos.length }}张</text>\n        </view>\n        <view class=\"card-body\">\n          <view class=\"photo-grid\">\n            <view v-for=\"(photo, index) in taskInfo.photos\" :key=\"index\" class=\"photo-item\" @click=\"previewPhoto(index, 'problem')\">\n              <image :src=\"photo.url || photo\" mode=\"aspectFill\"></image>\n              <view class=\"photo-label\">问题</view>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 3. 整改说明 - 整改过程的说明 -->\n      <view v-if=\"taskInfo.rectificationDescription\" class=\"card\">\n        <view class=\"card-header\">\n          <view class=\"card-title\">整改说明</view>\n        </view>\n        <view class=\"card-body\">\n          <text class=\"description-text\">{{ taskInfo.rectificationDescription }}</text>\n        </view>\n      </view>\n\n      <!-- 4. 整改完成照片 - 整改结果展示 -->\n      <view v-if=\"taskInfo.completionPhotos && taskInfo.completionPhotos.length > 0\" class=\"card\">\n        <view class=\"card-header\">\n          <view class=\"card-title\">整改后照片</view>\n          <text class=\"photo-count\">{{ taskInfo.completionPhotos.length }}张</text>\n        </view>\n        <view class=\"card-body\">\n          <view class=\"photo-grid\">\n            <view v-for=\"(photo, index) in taskInfo.completionPhotos\" :key=\"index\" class=\"photo-item\" @click=\"previewPhoto(index, 'completion')\">\n              <image :src=\"photo.url || photo\" mode=\"aspectFill\"></image>\n              <view class=\"photo-label completion\">整改后</view>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 5. 复查信息 - 最后的审核结果 -->\n      <view v-if=\"taskInfo.reviewResult || taskInfo.reviewComments\" class=\"card\">\n        <view class=\"card-header\">\n          <view class=\"card-title\">复查信息</view>\n        </view>\n        <view class=\"card-body\">\n          <view class=\"review-info\">\n            <view v-if=\"taskInfo.reviewResult\" class=\"info-item\">\n              <text class=\"info-label\">复查结果：</text>\n              <view class=\"review-result-badge\" :class=\"['review-' + taskInfo.reviewResult]\">{{ computedData.reviewResultText }}</view>\n            </view>\n            <view v-if=\"taskInfo.reviewerName\" class=\"info-item\">\n              <text class=\"info-label\">复查人：</text>\n              <text class=\"info-value\">{{ taskInfo.reviewerName }}</text>\n            </view>\n            <view v-if=\"taskInfo.reviewDate\" class=\"info-item\">\n              <text class=\"info-label\">复查时间：</text>\n              <text class=\"info-value\">{{ formatDateTimeOptimized(taskInfo.reviewDate) }}</text>\n            </view>\n            <view v-if=\"taskInfo.reviewComments\" class=\"info-item\">\n              <text class=\"info-label\">复查意见：</text>\n              <text class=\"info-value\">{{ taskInfo.reviewComments }}</text>\n            </view>\n          </view>\n          \n          <!-- 复查照片 -->\n          <view v-if=\"taskInfo.reviewPhotos && taskInfo.reviewPhotos.length > 0\" class=\"review-photos-section\">\n            <view class=\"review-photos-header\">\n              <text class=\"review-photos-title\">复查照片</text>\n              <text class=\"photo-count\">{{ taskInfo.reviewPhotos.length }}张</text>\n            </view>\n            <view class=\"photo-grid\">\n              <view v-for=\"(photo, index) in taskInfo.reviewPhotos\" :key=\"index\" class=\"photo-item\" @click=\"previewPhoto(index, 'review')\">\n                <image :src=\"photo.url || photo\" mode=\"aspectFill\"></image>\n                <view class=\"photo-label review\">复查</view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </template>\n    \n    <view class=\"button-container\">\n      <button class=\"primary-button\" @click=\"goBack\">返回</button>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { callCloudFunction } from '@/utils/auth.js';\n\nexport default {\n  name: 'RectificationDetail',\n  data() {\n    return {\n      taskInfo: {},\n      loading: false,\n      loadError: '',\n      taskId: '',\n      \n      // 性能优化缓存\n      processCache: null, // 数据处理缓存\n      dataLoaded: false, // 数据是否已加载\n      \n      // 预计算的状态信息\n      computedData: {\n        isCompleted: false,\n        statusText: '',\n        categoryText: '',\n        reviewResultText: ''\n      }\n    }\n  },\n  onLoad(options) {\n    this.taskId = options.taskId;\n    this.loadTaskDetail(options.taskId);\n    \n    // 监听整改记录更新事件\n    uni.$on('rectificationRecordUpdated', this.handleRecordUpdated);\n  },\n  onUnload() {\n    // 移除事件监听\n    uni.$off('rectificationRecordUpdated', this.handleRecordUpdated);\n  },\n  \n  methods: {\n    // 初始化数据处理缓存\n    initProcessCache() {\n      if (!this.processCache) {\n        this.processCache = {\n          // 状态映射缓存\n          statusMap: {\n            'pending': '待整改',\n            'pending_rectification': '待整改',\n            'pending_review': '待复查',\n            'completed': '已完成',\n            'overdue': '已逾期',\n            'in_progress': '整改中',\n            'rejected': '整改不达标',\n            'verified': '整改合格'\n          },\n          // 类别映射缓存\n          categoryMap: {\n            'cleanliness': '清洁问题',\n            'safety': '安全问题', \n            'equipment': '设备问题',\n            'environment': '环境问题',\n            'organization': '整理问题',\n            'standardization': '标识问题',\n            'other': '其他问题'\n          },\n          // 审核结果映射缓存\n          reviewResultMap: {\n            'approved': '复查通过',\n            'rejected': '需重新整改',\n            'needs_improvement': '需改进'\n          },\n          // 已完成状态列表\n          completedStatuses: ['completed', 'verified', 'approved'],\n          // 日期格式化器\n          dateFormatter: this.createOptimizedDateFormatter()\n        };\n      }\n    },\n\n    // 创建优化的日期格式化器\n    createOptimizedDateFormatter() {\n      return (dateString) => {\n        if (!dateString) return '--';\n        \n        try {\n          let date;\n          if (typeof dateString === 'string') {\n            if (dateString.includes('T') || dateString.includes('Z')) {\n              date = new Date(dateString);\n            } else {\n              date = new Date(dateString.replace(/-/g, '/'));\n            }\n          } else {\n            date = new Date(dateString);\n          }\n          \n          if (isNaN(date.getTime())) {\n            return '--';\n          }\n          \n          return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;\n        } catch (error) {\n          return '--';\n        }\n      };\n    },\n\n    // 优化的任务详情加载\n    async loadTaskDetailOptimized(taskId) {\n      if (!taskId) {\n        this.loadError = '整改任务ID不能为空';\n        return;\n      }\n\n      this.loading = true;\n      this.loadError = '';\n\n      try {\n        // 初始化缓存\n        this.initProcessCache();\n\n        const result = await callCloudFunction('hygiene-rectification', {\n          action: 'getRectificationDetail',\n          data: { id: taskId }\n        });\n\n        if (result && result.success && result.data) {\n          const task = result.data;\n          \n          // 使用缓存快速处理数据\n          this.taskInfo = this.processTaskDataOptimized(task);\n          \n          // 预计算状态信息\n          this.precomputeTaskStates();\n          \n          this.dataLoaded = true;\n        } else {\n          throw new Error(result?.message || '获取整改任务详情失败');\n        }\n      } catch (error) {\n\n        this.loadError = error.message || '加载失败，请稍后重试';\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 优化的任务数据处理\n    processTaskDataOptimized(task) {\n      const formatter = this.processCache.dateFormatter;\n      \n      return {\n        id: task._id || task.id,\n        area: task.area_name || '未知责任区',\n        areaName: task.area_name || '未知责任区',\n        isPublic: task.area_type === 'public',\n        status: task.status || 'pending',\n        problemDescription: task.issue_description || task.description || '无问题描述',\n        issueFoundDate: task.created_at || task.issue_found_date,\n        inspector: task.inspector_name || (task.issue && task.issue.inspector_name) || task.created_by_name || task.assigned_by_name || '未知检查员',\n        rectificationDescription: task.rectification_description || task.completion_description || '',\n        photos: this.processPhotos(task.photos || []),\n        completionPhotos: this.processPhotos(task.completion_photos || []),\n        completedDate: task.completed_at,\n        rectificationSubmitTime: task.submitted_at || task.updated_at,\n        assignee_name: task.assigned_to_name || task.cleaner_name || task.area_assignee_name || '未分配',\n        category: task.category || '',\n        deadline: task.deadline,\n        startDate: task.start_date,\n        reviewResult: task.review_result || '',\n        reviewComments: task.review_comments || '',\n        reviewDate: task.review_date,\n        reviewerName: task.reviewer_name || '',\n        reviewPhotos: this.processPhotos(task.review_photos || [])\n      };\n    },\n\n    // 优化的照片处理\n    processPhotos(photos) {\n      if (!Array.isArray(photos)) return [];\n      \n      return photos.map(photo => {\n        if (typeof photo === 'string') {\n          return { url: photo };\n        } else if (photo && typeof photo === 'object') {\n          return { url: photo.url || photo };\n        }\n        return { url: '' };\n      }).filter(photo => photo.url);\n    },\n\n    // 预计算任务状态\n    precomputeTaskStates() {\n      const cache = this.processCache;\n      \n      this.computedData = {\n        isCompleted: cache.completedStatuses.includes(this.taskInfo.status),\n        statusText: cache.statusMap[this.taskInfo.status] || '未知状态',\n        categoryText: cache.categoryMap[this.taskInfo.category] || this.taskInfo.category || '其他',\n        reviewResultText: cache.reviewResultMap[this.taskInfo.reviewResult] || this.taskInfo.reviewResult\n      };\n    },\n\n    // 根据taskId加载任务详情 - 保留原方法作为备用\n    async loadTaskDetail(taskId) {\n      // 使用优化版本\n      return this.loadTaskDetailOptimized(taskId);\n    },\n\n    // 根据taskId加载任务详情 - 原始版本（备用）\n    async loadTaskDetailOriginal(taskId) {\n      if (!taskId) {\n        this.loadError = '整改任务ID不能为空';\n        return;\n      }\n\n      this.loading = true;\n      this.loadError = '';\n\n      try {\n        const result = await callCloudFunction('hygiene-rectification', {\n          action: 'getRectificationDetail',\n          data: { id: taskId }\n        });\n\n        if (result && result.success && result.data) {\n          const task = result.data;\n          \n          this.taskInfo = {\n            id: task._id || task.id,\n            area: task.area_name || '未知责任区',\n            areaName: task.area_name || '未知责任区',\n            isPublic: task.area_type === 'public',\n            status: task.status || 'pending',\n            problemDescription: task.issue_description || task.description || '无问题描述',\n            issueFoundDate: task.created_at || task.issue_found_date,\n            inspector: task.inspector_name || (task.issue && task.issue.inspector_name) || task.created_by_name || task.assigned_by_name || '未知检查员',\n            rectificationDescription: task.rectification_description || task.completion_description || '',\n            photos: task.photos || [], // 原始问题照片\n            completionPhotos: task.completion_photos || [], // 整改完成照片\n            completedDate: task.completed_at,\n            rectificationSubmitTime: task.submitted_at || task.updated_at,\n            assignee_name: task.assigned_to_name || task.cleaner_name || task.area_assignee_name || '未分配',\n            // 新增字段\n            category: task.category || '',\n            deadline: task.deadline,\n            startDate: task.start_date,\n            // 审核信息\n            reviewResult: task.review_result || '',\n            reviewComments: task.review_comments || '',\n            reviewDate: task.review_date,\n            reviewerName: task.reviewer_name || ''\n          };\n        } else {\n          throw new Error(result?.message || '获取整改任务详情失败');\n        }\n      } catch (error) {\n\n        this.loadError = error.message || '加载失败，请稍后重试';\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 重新加载\n    retryLoad() {\n      this.loadTaskDetail(this.taskId);\n    },\n\n    // 优化的日期时间格式化\n    formatDateTimeOptimized(dateString) {\n      if (!this.processCache || !this.processCache.dateFormatter) {\n        // 如果缓存未初始化，回退到原方法\n        return this.formatDateTime(dateString);\n      }\n      return this.processCache.dateFormatter(dateString);\n    },\n\n    // 格式化日期时间 - 保留原方法作为备用\n    formatDateTime(dateString) {\n      if (!dateString) return '--';\n      \n      try {\n        let date;\n        if (typeof dateString === 'string') {\n          if (dateString.includes('T') || dateString.includes('Z')) {\n            date = new Date(dateString);\n          } else {\n            date = new Date(dateString.replace(/-/g, '/'));\n          }\n        } else {\n          date = new Date(dateString);\n        }\n        \n        if (isNaN(date.getTime())) {\n          return '--';\n        }\n        \n        return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;\n      } catch (error) {\n\n        return '--';\n      }\n    },\n\n    // 获取状态文本 - 保留用于向后兼容\n    getStatusText(status) {\n      if (this.processCache && this.processCache.statusMap) {\n        return this.processCache.statusMap[status] || '未知状态';\n      }\n      // 降级处理\n      const statusMap = {\n        'pending': '待整改',\n        'pending_rectification': '待整改',\n        'pending_review': '待复查',\n        'completed': '已完成',\n        'overdue': '已逾期',\n        'in_progress': '整改中',\n        'rejected': '整改不达标',\n        'verified': '整改合格'\n      };\n      return statusMap[status] || '未知状态';\n    },\n\n    // 获取类别文本 - 保留用于向后兼容\n    getCategoryText(category) {\n      if (this.processCache && this.processCache.categoryMap) {\n        return this.processCache.categoryMap[category] || category || '其他';\n      }\n      // 降级处理\n      const categoryMap = {\n        'cleanliness': '清洁问题',\n        'safety': '安全问题', \n        'equipment': '设备问题',\n        'environment': '环境问题',\n        'organization': '整理问题',\n        'standardization': '标识问题',\n        'other': '其他问题'\n      };\n      return categoryMap[category] || category || '其他';\n    },\n\n    // 判断任务是否已完成 - 保留用于向后兼容\n    isTaskCompleted() {\n      if (this.computedData && typeof this.computedData.isCompleted === 'boolean') {\n        return this.computedData.isCompleted;\n      }\n      // 降级处理\n      const completedStatuses = ['completed', 'verified', 'approved'];\n      return completedStatuses.includes(this.taskInfo.status);\n    },\n\n    // 获取审核结果文本 - 保留用于向后兼容\n    getReviewResultText(result) {\n      if (this.processCache && this.processCache.reviewResultMap) {\n        return this.processCache.reviewResultMap[result] || result;\n      }\n      // 降级处理\n      const resultMap = {\n        'approved': '复查通过',\n        'rejected': '需重新整改',\n        'needs_improvement': '需改进'\n      };\n      return resultMap[result] || result;\n    },\n\n    // 优化的照片预览\n    previewPhoto(index, type = 'problem') {\n      let photos, urls;\n      \n      if (type === 'problem') {\n        photos = this.taskInfo.photos || [];\n      } else if (type === 'completion') {\n        photos = this.taskInfo.completionPhotos || [];\n      } else if (type === 'review') {\n        photos = this.taskInfo.reviewPhotos || [];\n      } else {\n        return;\n      }\n      \n      // 使用缓存的URL处理，避免重复map操作\n      if (photos._cachedUrls) {\n        urls = photos._cachedUrls;\n      } else {\n        urls = photos.map(photo => photo.url || photo).filter(url => url);\n        // 缓存处理结果\n        photos._cachedUrls = urls;\n      }\n      \n      if (urls.length === 0) return;\n      \n      uni.previewImage({\n        urls: urls,\n        current: Math.min(index, urls.length - 1)\n      });\n    },\n\n    // 返回上一页\n    goBack() {\n      uni.navigateBack();\n    },\n\n    // 处理整改记录更新事件\n    handleRecordUpdated(data) {\n      // 如果更新的是当前任务，重新加载数据\n      if (data.taskId === this.taskId) {\n        this.loadTaskDetail(this.taskId);\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n  min-height: 100vh;\n  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\n  width: 100%;\n  overflow-x: hidden;\n  box-sizing: border-box;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;\n  padding: 24rpx;\n}\n\n.card {\n  background: #ffffff;\n  border-radius: 16rpx;\n  margin-bottom: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.card-header {\n  padding: 32rpx 32rpx 16rpx 32rpx;\n  border-bottom: 1rpx solid #F2F2F7;\n}\n\n.card-body {\n  padding: 32rpx;\n}\n\n.header-content {\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.card-subtitle {\n  font-size: 26rpx;\n  color: #8E8E93;\n}\n\n.photo-count {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.task-info {\n  display: flex;\n  flex-direction: column;\n  gap: 24rpx;\n}\n\n.info-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 16rpx;\n}\n\n.info-label {\n  font-size: 28rpx;\n  color: #8E8E93;\n  width: 140rpx;\n  flex-shrink: 0;\n  text-align: left;\n}\n\n.info-value {\n  font-size: 28rpx;\n  color: #1C1C1E;\n  flex: 1;\n  line-height: 1.4;\n}\n\n.status-badge {\n  padding: 8rpx 16rpx;\n  border-radius: 8rpx;\n  font-size: 24rpx;\n  font-weight: 600;\n  \n  &.status-pending,\n  &.status-pending_rectification {\n    background: #FFF4E6;\n    color: #FF9500;\n  }\n  \n  &.status-pending_review {\n    background: #E6F3FF;\n    color: #007AFF;\n  }\n  \n  &.status-completed {\n    background: #E8F5E8;\n    color: #34C759;\n  }\n  \n  &.status-overdue {\n    background: #FFE6E6;\n    color: #FF3B30;\n  }\n  \n  &.status-in_progress {\n    background: #F0F0F5;\n    color: #8E8E93;\n  }\n\n  &.status-rejected {\n    background: #FFE6E6;\n    color: #FF3B30;\n  }\n\n  &.status-verified {\n    background: #E8F5E8;\n    color: #34C759;\n  }\n}\n\n\n\n\n\n/* 照片标签 */\n.photo-label {\n  position: absolute;\n  bottom: 8rpx;\n  left: 8rpx;\n  background: rgba(255, 59, 48, 0.8);\n  color: white;\n  padding: 4rpx 8rpx;\n  border-radius: 4rpx;\n  font-size: 20rpx;\n  font-weight: 600;\n  \n  &.completion {\n    background: rgba(52, 199, 89, 0.8);\n  }\n  \n  &.review {\n    background: rgba(0, 122, 255, 0.8);\n  }\n}\n\n.photo-item {\n  position: relative;\n}\n\n/* 审核信息 */\n.review-info {\n  display: flex;\n  flex-direction: column;\n  gap: 24rpx;\n}\n\n.review-result-badge {\n  padding: 8rpx 16rpx;\n  border-radius: 8rpx;\n  font-size: 24rpx;\n  font-weight: 600;\n  \n  &.review-approved {\n    background: #E8F5E8;\n    color: #34C759;\n  }\n  \n  &.review-rejected {\n    background: #FFE6E6;\n    color: #FF3B30;\n  }\n  \n  &.review-needs_improvement {\n    background: #FFF4E6;\n    color: #FF9500;\n  }\n}\n\n/* 复查照片部分 */\n.review-photos-section {\n  margin-top: 32rpx;\n  padding-top: 32rpx;\n  border-top: 1rpx solid #F2F2F7;\n}\n\n.review-photos-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24rpx;\n}\n\n.review-photos-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n}\n\n.description-text {\n  font-size: 28rpx;\n  color: #1C1C1E;\n  line-height: 1.6;\n}\n\n.photo-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12rpx;\n}\n\n.photo-item {\n  width: 200rpx;\n  height: 200rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n  background: #F8F9FA;\n}\n\n.photo-item image {\n  width: 100%;\n  height: 100%;\n}\n\n.button-container {\n  padding: 32rpx 0;\n}\n\n.primary-button {\n  background: #007AFF;\n  color: white;\n  border: none;\n  border-radius: 12rpx;\n  padding: 24rpx;\n  font-size: 32rpx;\n  font-weight: 600;\n  width: 100%;\n}\n\n/* 加载状态 */\n.loading-container {\n  padding: 120rpx 32rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.loading-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 24rpx;\n}\n\n.loading-spinner {\n  width: 40rpx;\n  height: 40rpx;\n  border: 4rpx solid #E5E7EB;\n  border-top: 4rpx solid #007AFF;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n  text-align: center;\n}\n\n/* 错误状态 */\n.error-container {\n  padding: 120rpx 32rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.error-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 24rpx;\n  text-align: center;\n}\n\n.error-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n  line-height: 1.4;\n}\n\n.retry-button {\n  background: #007AFF;\n  color: white;\n  border: none;\n  border-radius: 8rpx;\n  padding: 16rpx 32rpx;\n  font-size: 26rpx;\n}\n</style>", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rectification-detail.vue?vue&type=style&index=0&id=ccc26d00&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rectification-detail.vue?vue&type=style&index=0&id=ccc26d00&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775842253\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}