<view class="page-container data-v-110de69d"><view class="page-header data-v-110de69d"><view class="header-content data-v-110de69d"><view class="header-title data-v-110de69d">责任区检查</view><view class="header-subtitle data-v-110de69d"><block wx:if="{{loading}}"><text style="color:white !important;animation:pulse 1.5s ease-in-out infinite;" class="data-v-110de69d">加载中...</text></block><block wx:else><text class="data-v-110de69d">{{areaInfo.name||'未知责任区'}}</text></block></view></view><view class="check-time data-v-110de69d"><text class="data-v-110de69d">{{currentTime}}</text></view></view><view class="card data-v-110de69d"><view class="card-header data-v-110de69d"><view class="card-title data-v-110de69d">责任区信息</view></view><view class="card-body data-v-110de69d"><block wx:if="{{loading}}"><view class="loading-container data-v-110de69d"><view class="loading-content data-v-110de69d"><view class="loading-spinner data-v-110de69d"></view><text class="loading-text data-v-110de69d">加载责任区信息中...</text></view></view></block><block wx:else><view class="info-grid-enhanced data-v-110de69d"><view class="info-item-enhanced data-v-110de69d"><view class="info-icon data-v-110de69d"><uni-icons vue-id="0e8b6370-1" type="person" size="18" color="#007AFF" class="data-v-110de69d" bind:__l="__l"></uni-icons></view><view class="info-content data-v-110de69d"><view class="info-label-enhanced data-v-110de69d">负责人</view><view class="info-value-enhanced data-v-110de69d">{{areaInfo.assignedEmployee||'加载中...'}}</view></view></view><view class="info-item-enhanced data-v-110de69d"><view class="info-icon data-v-110de69d"><uni-icons vue-id="0e8b6370-2" type="location" size="18" color="#FF9500" class="data-v-110de69d" bind:__l="__l"></uni-icons></view><view class="info-content data-v-110de69d"><view class="info-label-enhanced data-v-110de69d">区域位置</view><view class="info-value-enhanced data-v-110de69d">{{areaInfo.location||'加载中...'}}</view></view></view><view class="info-item-enhanced data-v-110de69d"><view class="info-icon data-v-110de69d"><uni-icons vue-id="0e8b6370-3" type="calendar" size="18" color="#5856D6" class="data-v-110de69d" bind:__l="__l"></uni-icons></view><view class="info-content data-v-110de69d"><view class="info-label-enhanced data-v-110de69d">清理频率</view><view class="info-value-enhanced data-v-110de69d">{{areaInfo.cleaningFrequency||'加载中...'}}</view></view></view><block wx:if="{{shouldShowLastCleaning}}"><view class="info-item-enhanced data-v-110de69d"><view class="info-icon data-v-110de69d"><uni-icons vue-id="0e8b6370-4" type="checkmarkempty" size="18" color="#34C759" class="data-v-110de69d" bind:__l="__l"></uni-icons></view><view class="info-content data-v-110de69d"><view class="info-label-enhanced data-v-110de69d">上次清理</view><view class="{{['info-value-enhanced','data-v-110de69d',getLastCleaningClass]}}">{{areaInfo.lastCleaningTime}}</view></view></view></block></view></block></view></view><block wx:if="{{!inspectionForm.hasIssues}}"><view class="card data-v-110de69d"><view class="card-header data-v-110de69d"><view class="card-title data-v-110de69d">检查评分</view><view class="card-subtitle data-v-110de69d">检查通过后，对责任区状况进行客观评价</view></view><view class="card-body data-v-110de69d"><view class="rating-section data-v-110de69d"><view class="rating-display-large data-v-110de69d"><view class="rating-number-container data-v-110de69d"><text class="rating-number data-v-110de69d">{{inspectionForm.rating}}</text><text class="rating-unit data-v-110de69d">/5</text></view><view class="rating-desc data-v-110de69d">{{$root.m0}}</view></view><view class="star-rating data-v-110de69d"><uni-rate vue-id="0e8b6370-5" value="{{inspectionForm.rating}}" allow-half="{{true}}" size="{{24}}" active-color="#FFD700" inactive-color="#E5E5EA" touchable="{{true}}" margin="{{8}}" data-event-opts="{{[['^change',[['onStarRateChange']]]]}}" bind:change="__e" class="data-v-110de69d" bind:__l="__l"></uni-rate></view><view class="custom-slider-rating data-v-110de69d"><view data-event-opts="{{[['touchstart',[['onSliderTouchStart',['$event']]]],['touchmove',[['onSliderTouchMove',['$event']]]],['touchend',[['onSliderTouchEnd',['$event']]]],['mousedown',[['onSliderMouseDown',['$event']]]],['mousemove',[['onSliderMouseMove',['$event']]]],['mouseup',[['onSliderMouseUp',['$event']]]],['mouseleave',[['onSliderMouseUp',['$event']]]]]}}" class="custom-slider-container data-v-110de69d" bindtouchstart="__e" bindtouchmove="__e" bindtouchend="__e" bindmousedown="__e" bindmousemove="__e" bindmouseup="__e" bindmouseleave="__e"><view class="slider-track data-v-110de69d"><view class="slider-track-active data-v-110de69d" style="{{'width:'+(inspectionForm.rating/5*100+'%')+';'}}"></view></view><view class="slider-thumb data-v-110de69d" style="{{'left:'+(inspectionForm.rating/5*100+'%')+';'}}"></view><view class="slider-marks data-v-110de69d"><block wx:for="{{6}}" wx:for-item="mark" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['setRatingByMark',[index]]]]]}}" class="{{['slider-mark','data-v-110de69d',(index<=inspectionForm.rating)?'slider-mark-active':'']}}" style="{{'left:'+(index/5*100+'%')+';'}}" bindtap="__e"></view></block></view></view><view class="slider-labels-external data-v-110de69d"><block wx:for="{{['0','1','2','3','4','5']}}" wx:for-item="label" wx:for-index="labelIndex" wx:key="labelIndex"><text class="{{['slider-label-external','data-v-110de69d',(labelIndex<=inspectionForm.rating)?'slider-label-active':'']}}" style="{{'left:'+(labelIndex/5*100+'%')+';'}}">{{label}}</text></block></view></view><view class="rating-tips data-v-110de69d"><text class="data-v-110de69d">请对本次检查结果进行客观评分（支持半星评分）</text></view></view></view></view></block><view class="card data-v-110de69d"><view class="card-header data-v-110de69d"><view class="header-content data-v-110de69d"><view class="card-title data-v-110de69d">检查照片</view><view class="card-subtitle data-v-110de69d"><block wx:if="{{!inspectionForm.hasIssues}}"><text class="data-v-110de69d">上传现场检查照片（可选）</text></block><block wx:else><text class="data-v-110de69d">上传现场检查照片（至少上传1张照片）</text></block></view></view><view data-event-opts="{{[['tap',[['toggleAutoUpload',['$event']]]]]}}" class="auto-upload-toggle data-v-110de69d" bindtap="__e"><view class="toggle-label data-v-110de69d">自动上传</view><view class="{{['toggle-switch','data-v-110de69d',(autoUpload)?'active':'']}}"><view class="toggle-circle data-v-110de69d"></view></view></view></view><view class="card-body data-v-110de69d"><view class="upload-grid data-v-110de69d"><block wx:for="{{$root.l0}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['previewPhoto',[index]]]]]}}" class="upload-item has-photo data-v-110de69d" bindtap="__e"><image class="upload-image data-v-110de69d" src="{{photo.m1}}" mode="aspectFill"></image><block wx:if="{{photo.$orig.uploading}}"><view class="photo-uploading data-v-110de69d"><view class="upload-spinner data-v-110de69d"></view></view></block><block wx:else><block wx:if="{{photo.$orig.uploaded}}"><view class="photo-uploaded data-v-110de69d"><uni-icons vue-id="{{'0e8b6370-6-'+index}}" type="checkmarkempty" size="16" color="white" class="data-v-110de69d" bind:__l="__l"></uni-icons></view></block></block><view data-event-opts="{{[['tap',[['deletePhoto',[index]]]]]}}" class="photo-delete data-v-110de69d" catchtap="__e"><uni-icons vue-id="{{'0e8b6370-7-'+index}}" type="close" size="18" color="white" class="data-v-110de69d" bind:__l="__l"></uni-icons></view></view></block><block wx:if="{{$root.g0<6}}"><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="upload-item add-photo data-v-110de69d" bindtap="__e"><uni-icons vue-id="0e8b6370-8" type="camera" size="28" color="#8E8E93" class="data-v-110de69d" bind:__l="__l"></uni-icons><text class="upload-text data-v-110de69d">添加照片</text><text class="upload-count data-v-110de69d">{{$root.g1+"/6"}}</text></view></block></view></view></view><view class="card data-v-110de69d"><view class="card-header data-v-110de69d"><view class="card-title data-v-110de69d">检查结果</view><view class="card-subtitle data-v-110de69d">记录检查发现的问题</view></view><view class="card-body data-v-110de69d"><view class="issue-toggle data-v-110de69d"><view class="toggle-group data-v-110de69d"><view data-event-opts="{{[['tap',[['toggleIssues',[false]]]]]}}" class="{{['toggle-item','enhanced','data-v-110de69d',(!inspectionForm.hasIssues)?'active':'']}}" bindtap="__e"><view class="toggle-icon data-v-110de69d"><uni-icons vue-id="0e8b6370-9" type="checkmarkempty" size="18" color="{{!inspectionForm.hasIssues?'#007AFF':'#8E8E93'}}" class="data-v-110de69d" bind:__l="__l"></uni-icons></view><view class="toggle-content data-v-110de69d"><text class="toggle-title data-v-110de69d">检查通过</text><text class="toggle-desc data-v-110de69d">可对责任区进行评分</text></view></view><view data-event-opts="{{[['tap',[['toggleIssues',[true]]]]]}}" class="{{['toggle-item','enhanced','data-v-110de69d',(inspectionForm.hasIssues)?'active':'']}}" bindtap="__e"><view class="toggle-icon data-v-110de69d"><uni-icons vue-id="0e8b6370-10" type="close" size="18" color="{{inspectionForm.hasIssues?'#FF3B30':'#8E8E93'}}" class="data-v-110de69d" bind:__l="__l"></uni-icons></view><view class="toggle-content data-v-110de69d"><text class="toggle-title data-v-110de69d">发现问题</text><text class="toggle-desc data-v-110de69d">需要记录并整改</text></view></view></view></view><block wx:if="{{!inspectionForm.hasIssues}}"><view class="remarks-form data-v-110de69d"><view class="form-section data-v-110de69d"><view class="form-label data-v-110de69d">检查备注（可选）</view><view class="textarea-container data-v-110de69d"><textarea class="remarks-textarea-enhanced data-v-110de69d" placeholder="可以记录检查过程中的注意事项、建议等..." maxlength="200" data-event-opts="{{[['input',[['__set_model',['$0','remarks','$event',[]],['inspectionForm']],['handleRemarksInput',['$event']]]]]}}" value="{{inspectionForm.remarks}}" bindinput="__e"></textarea><view class="char-count-overlay data-v-110de69d">{{remarksLength+"/200"}}</view></view></view></view></block><block wx:if="{{inspectionForm.hasIssues}}"><view class="issue-form data-v-110de69d"><view class="form-section data-v-110de69d"><view class="form-label data-v-110de69d">问题描述</view><view class="textarea-container data-v-110de69d"><textarea class="issue-textarea-enhanced data-v-110de69d" placeholder="请详细描述发现的问题..." maxlength="200" data-event-opts="{{[['input',[['__set_model',['$0','issueDescription','$event',[]],['inspectionForm']],['handleIssueDescriptionInput',['$event']]]]]}}" value="{{inspectionForm.issueDescription}}" bindinput="__e"></textarea><view class="char-count-overlay data-v-110de69d">{{issueDescriptionLength+"/200"}}</view><block wx:if="{{inspectionForm.issueDescription&&inspectionForm.issueType}}"><view class="auto-type-hint-floating data-v-110de69d"><uni-icons vue-id="0e8b6370-11" type="checkmarkempty" size="14" color="#52C41A" class="data-v-110de69d" bind:__l="__l"></uni-icons><text class="data-v-110de69d">{{"系统推断："+inspectionForm.issueType}}</text></view></block></view></view></view></block></view></view><view class="button-container data-v-110de69d"><button class="{{['primary-button','data-v-110de69d',(isSubmitting)?'loading':'']}}" disabled="{{!canSubmit||isSubmitting}}" data-event-opts="{{[['tap',[['submitInspection',['$event']]]]]}}" bindtap="__e"><block wx:if="{{isSubmitting}}"><view class="button-loading data-v-110de69d"><view class="loading-spinner data-v-110de69d"></view><text class="data-v-110de69d">提交中...</text></view></block><block wx:else><text class="data-v-110de69d">提交检查记录</text></block></button></view><view class="bottom-safe-area data-v-110de69d"></view></view>