<template>
  <view class="page-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="header-content">
        <view class="header-title">责任区检查</view>
        <view class="header-subtitle">
          <text v-if="loading" style="color: white !important; animation: pulse 1.5s ease-in-out infinite;">加载中...</text>
          <text v-else>{{ areaInfo.name || '未知责任区' }}</text>
        </view>
      </view>
      <view class="check-time">
        <text>{{ currentTime }}</text>
      </view>
    </view>

    <!-- 责任区信息 -->
    <view class="card">
      <view class="card-header">
        <view class="card-title">责任区信息</view>
      </view>
      <view class="card-body">
        <!-- 加载状态 -->
        <view v-if="loading" class="loading-container">
          <view class="loading-content">
            <view class="loading-spinner"></view>
            <text class="loading-text">加载责任区信息中...</text>
          </view>
        </view>
        
        <!-- 正常内容 -->
        <view v-else class="info-grid-enhanced">
          <view class="info-item-enhanced">
            <view class="info-icon">
              <uni-icons type="person" size="18" color="#007AFF"></uni-icons>
            </view>
            <view class="info-content">
              <view class="info-label-enhanced">负责人</view>
              <view class="info-value-enhanced">{{ areaInfo.assignedEmployee || '加载中...' }}</view>
            </view>
          </view>
          <view class="info-item-enhanced">
            <view class="info-icon">
              <uni-icons type="location" size="18" color="#FF9500"></uni-icons>
            </view>
            <view class="info-content">
              <view class="info-label-enhanced">区域位置</view>
              <view class="info-value-enhanced">{{ areaInfo.location || '加载中...' }}</view>
            </view>
          </view>
          <view class="info-item-enhanced">
            <view class="info-icon">
              <uni-icons type="calendar" size="18" color="#5856D6"></uni-icons>
            </view>
            <view class="info-content">
              <view class="info-label-enhanced">清理频率</view>
              <view class="info-value-enhanced">{{ areaInfo.cleaningFrequency || '加载中...' }}</view>
            </view>
          </view>
          <view v-if="shouldShowLastCleaning" class="info-item-enhanced">
            <view class="info-icon">
              <uni-icons type="checkmarkempty" size="18" color="#34C759"></uni-icons>
            </view>
            <view class="info-content">
              <view class="info-label-enhanced">上次清理</view>
              <view class="info-value-enhanced" :class="getLastCleaningClass">{{ areaInfo.lastCleaningTime }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 本次检查评分 - 只在检查通过时显示 -->
    <view v-if="!inspectionForm.hasIssues" class="card">
      <view class="card-header">
        <view class="card-title">检查评分</view>
        <view class="card-subtitle">检查通过后，对责任区状况进行客观评价</view>
      </view>
      <view class="card-body">
        <view class="rating-section">
          <view class="rating-display-large">
            <view class="rating-number-container">
              <text class="rating-number">{{ inspectionForm.rating }}</text>
              <text class="rating-unit">/5</text>
            </view>
            <view class="rating-desc">{{ getRatingDescription(inspectionForm.rating) }}</view>
          </view>
          
          <!-- 星星评分（使用官方uni-rate组件） -->
          <view class="star-rating">
            <uni-rate 
              :value="inspectionForm.rating" 
              @change="onStarRateChange"
              allow-half
              :size="24"
              active-color="#FFD700"
              inactive-color="#E5E5EA"
              :touchable="true"
              :margin="8"
            />
          </view>
          
          <!-- 自定义滑动条评分 -->
          <view class="custom-slider-rating">
            <view class="custom-slider-container" 
                  @touchstart="onSliderTouchStart" 
                  @touchmove="onSliderTouchMove" 
                  @touchend="onSliderTouchEnd"
                  @mousedown="onSliderMouseDown"
                  @mousemove="onSliderMouseMove"
                  @mouseup="onSliderMouseUp"
                  @mouseleave="onSliderMouseUp">
              <!-- 滑动轨道 -->
              <view class="slider-track">
                <view class="slider-track-active" :style="{ width: (inspectionForm.rating / 5 * 100) + '%' }"></view>
              </view>
              <!-- 滑块 -->
              <view class="slider-thumb" :style="{ left: (inspectionForm.rating / 5 * 100) + '%' }"></view>
              <!-- 刻度点 -->
              <view class="slider-marks">
                <view 
                  v-for="(mark, index) in 6" 
                  :key="index" 
                  class="slider-mark"
                  :class="{ 'slider-mark-active': index <= inspectionForm.rating }"
                  :style="{ left: (index / 5 * 100) + '%' }"
                  @click="setRatingByMark(index)"
                ></view>
              </view>
            </view>
            <!-- 标签 - 放在滑动条容器外面 -->
            <view class="slider-labels-external">
              <text 
                v-for="(label, labelIndex) in ['0','1','2','3','4','5']" 
                :key="labelIndex"
                class="slider-label-external"
                :class="{ 'slider-label-active': labelIndex <= inspectionForm.rating }"
                :style="{ left: (labelIndex / 5 * 100) + '%' }"
              >{{ label }}</text>
            </view>
          </view>
          
          <view class="rating-tips">
            <text>请对本次检查结果进行客观评分（支持半星评分）</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 检查照片 -->
    <view class="card">
      <view class="card-header">
        <view class="header-content">
          <view class="card-title">检查照片</view>
          <view class="card-subtitle">
            <text v-if="!inspectionForm.hasIssues">上传现场检查照片（可选）</text>
            <text v-else>上传现场检查照片（至少上传1张照片）</text>
          </view>
        </view>
        <view class="auto-upload-toggle" @click="toggleAutoUpload">
          <view class="toggle-label">自动上传</view>
          <view class="toggle-switch" :class="{ active: autoUpload }">
            <view class="toggle-circle"></view>
          </view>
        </view>
      </view>
      <view class="card-body">
        <view class="upload-grid">
          <view 
            v-for="(photo, index) in inspectionForm.photos" 
            :key="index" 
            class="upload-item has-photo"
            @click="previewPhoto(index)"
          >
            <image :src="getPhotoDisplayUrl(photo)" mode="aspectFill" class="upload-image"></image>
            <!-- 上传状态指示器 -->
            <view v-if="photo.uploading" class="photo-uploading">
              <view class="upload-spinner"></view>
            </view>
            <view v-else-if="photo.uploaded" class="photo-uploaded">
              <uni-icons type="checkmarkempty" size="16" color="white"></uni-icons>
            </view>
            <view class="photo-delete" @click.stop="deletePhoto(index)">
              <uni-icons type="close" size="18" color="white"></uni-icons>
            </view>
          </view>
          
          <view 
            v-if="inspectionForm.photos.length < 6" 
            class="upload-item add-photo"
            @click="chooseImage"
          >
            <uni-icons type="camera" size="28" color="#8E8E93"></uni-icons>
            <text class="upload-text">添加照片</text>
            <text class="upload-count">{{ inspectionForm.photos.length }}/6</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 问题检查 -->
    <view class="card">
      <view class="card-header">
        <view class="card-title">检查结果</view>
        <view class="card-subtitle">记录检查发现的问题</view>
      </view>
      <view class="card-body">
        <view class="issue-toggle">
          <view class="toggle-group">
            <view 
              class="toggle-item enhanced"
              :class="{ active: !inspectionForm.hasIssues }"
              @click="toggleIssues(false)"
            >
              <view class="toggle-icon">
                <uni-icons type="checkmarkempty" size="18" :color="!inspectionForm.hasIssues ? '#007AFF' : '#8E8E93'"></uni-icons>
              </view>
              <view class="toggle-content">
                <text class="toggle-title">检查通过</text>
                <text class="toggle-desc">可对责任区进行评分</text>
              </view>
            </view>
            <view 
              class="toggle-item enhanced"
              :class="{ active: inspectionForm.hasIssues }"
              @click="toggleIssues(true)"
            >
              <view class="toggle-icon">
                <uni-icons type="close" size="18" :color="inspectionForm.hasIssues ? '#FF3B30' : '#8E8E93'"></uni-icons>
              </view>
              <view class="toggle-content">
                <text class="toggle-title">发现问题</text>
                <text class="toggle-desc">需要记录并整改</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 检查通过时的备注 -->
        <view v-if="!inspectionForm.hasIssues" class="remarks-form">
          <view class="form-section">
            <view class="form-label">检查备注（可选）</view>
            <view class="textarea-container">
              <textarea 
                v-model="inspectionForm.remarks"
                @input="handleRemarksInput"
                placeholder="可以记录检查过程中的注意事项、建议等..."
                maxlength="200"
                class="remarks-textarea-enhanced"
              ></textarea>
              <!-- 字数统计 -->
              <view class="char-count-overlay">{{ remarksLength }}/200</view>
            </view>
          </view>
        </view>

        <!-- 发现问题时的描述 -->
        <view v-if="inspectionForm.hasIssues" class="issue-form">
          <view class="form-section">
            <view class="form-label">问题描述</view>
            <view class="textarea-container">
              <textarea 
                v-model="inspectionForm.issueDescription"
                @input="handleIssueDescriptionInput"
                placeholder="请详细描述发现的问题..."
                maxlength="200"
                class="issue-textarea-enhanced"
              ></textarea>
              <!-- 字数统计 - 始终显示在右下角 -->
              <view class="char-count-overlay">{{ issueDescriptionLength }}/200</view>
              
              <!-- 系统推断提示 - 只在有推断时显示 -->
              <view v-if="inspectionForm.issueDescription && inspectionForm.issueType" class="auto-type-hint-floating">
                <uni-icons type="checkmarkempty" size="14" color="#52C41A"></uni-icons>
                <text>系统推断：{{ inspectionForm.issueType }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="button-container">
      <button 
        class="primary-button" 
        @click="submitInspection" 
        :disabled="!canSubmit || isSubmitting"
        :class="{ loading: isSubmitting }"
      >
        <view v-if="isSubmitting" class="button-loading">
          <view class="loading-spinner"></view>
          <text>提交中...</text>
        </view>
        <text v-else>提交检查记录</text>
      </button>
    </view>

    <!-- 底部安全间距 -->
    <view class="bottom-safe-area"></view>
  </view>
</template>

<script>
import uploadUtils from '@/utils/upload-utils.js'

export default {
  name: 'InspectionDetail',
  components: {
    // uni-rate组件会自动注册，无需手动引入
  },
  data() {
    return {
      areaId: '',
      isRectificationCheck: false, // 是否是整改复检
      loading: false,
      areaInfo: {
        id: '',
        name: '',
        assignedEmployee: '',
        location: '',
        cleaningFrequency: '',
        lastCleaningTime: '',
        hasRectificationHistory: false // 是否有整改历史
      },
      currentTime: '',
      sliderTouching: false,
      inspectionForm: {
        rating: 0,
        photos: [],
        hasIssues: false,
        issueType: '',
        issueDescription: '',
        remarks: '',

      },

      isSubmitting: false,
      autoUpload: true, // 自动上传开关
      uploading: false, // 是否正在上传
      uploadProgress: 0 // 上传进度
    }
  },
  computed: {
    // 计算问题描述长度，确保响应式更新
    issueDescriptionLength() {
      return this.inspectionForm.issueDescription ? this.inspectionForm.issueDescription.length : 0;
    },
    
    // 计算备注长度，确保响应式更新
    remarksLength() {
      return this.inspectionForm.remarks ? this.inspectionForm.remarks.length : 0;
    },
    
    canSubmit() {
      if (this.isSubmitting) return false;
      
      // 如果检查通过，需要评分
      if (!this.inspectionForm.hasIssues) {
        return this.inspectionForm.rating > 0;
      }
      
      // 如果发现问题，需要问题描述和至少一张照片
      if (this.inspectionForm.hasIssues) {
        return this.inspectionForm.issueDescription.trim().length > 0 && 
               this.inspectionForm.photos.length > 0;
      }
      
      return false;
    },
    
    // 获取上次清理时间样式
    getLastCleaningClass() {
      // 可以根据时间判断是否逾期
      return 'normal';
    },
    
    // 是否应该显示"上次清理"时间
    shouldShowLastCleaning() {
      // 只有当该责任区有整改历史时才显示"上次清理"时间
      // 这表示员工已经进行过二次清理（整改后的清理）
      return this.areaInfo.hasRectificationHistory && this.areaInfo.lastCleaningTime;
    }
  },
  onLoad(options) {

    if (options.id) {
      this.areaId = options.id;
      this.isRectificationCheck = options.isRectification === 'true'; // 是否是整改复检
      this.loadAreaInfo();
    }
    this.updateCurrentTime();
  },
  methods: {
    // 更新当前时间
    updateCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const day = now.getDate().toString().padStart(2, '0');
      const hour = now.getHours().toString().padStart(2, '0');
      const minute = now.getMinutes().toString().padStart(2, '0');
      const second = now.getSeconds().toString().padStart(2, '0');
      
      // 获取星期几
      const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
      const weekDay = weekDays[now.getDay()];
      
      // 格式化为中文日期时间格式
      this.currentTime = `${year}年${month}月${day}日 星期${weekDay} ${hour}:${minute}:${second}`;
    },

    // 加载责任区信息
    async loadAreaInfo() {
      this.loading = true;
      
      try {
        const { callCloudFunction } = require('@/utils/auth.js');
        
        const result = await callCloudFunction('hygiene-area-management', {
          action: 'getAreaDetail',
          data: { id: this.areaId }
        });

        if (result && result.success && result.data) {
          const area = result.data;
          
          // 先设置基本信息
          this.areaInfo = {
            id: area._id || area.id,
            name: area.name || '未知责任区',
            assignedEmployee: this.getAssignedEmployee(area),
            location: this.getLocationDisplay(area.location),
            cleaningFrequency: this.getCleaningFrequency(area),
            lastCleaningTime: '',
            hasRectificationHistory: this.isRectificationCheck
          };
          
          // 然后获取清理时间（这个过程中可能会更新负责人）
          this.areaInfo.lastCleaningTime = await this.getLastCleaningTime(area._id || area.id);
        } else {
          throw new Error(result?.message || '获取责任区信息失败');
        }
      } catch (error) {

        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 获取分配的员工
    getAssignedEmployee(area) {
      if (area.assigned_user_details && area.assigned_user_details.length > 0) {
        const user = area.assigned_user_details[0];
        return user.nickname || user.username || '未知员工';
      } else if (area.assigned_users && area.assigned_users.length > 0) {
        return `用户${area.assigned_users[0]}`;
      }
      return '未分配';
    },

    // 获取位置显示
    getLocationDisplay(location) {
      if (!location) return '未设置';
      
      // 如果是字符串，直接返回
      if (typeof location === 'string') {
        return location;
      }
      
      // 如果是对象，尝试获取描述字段
      if (typeof location === 'object') {
        return location.description || location.name || location.address || location.text || location.area || '未设置';
      }
      
      return '未设置';
    },

    // 获取清理频率
    getCleaningFrequency(area) {
      if (area.type === 'fixed') {
        return '每周一次';
      } else if (area.type === 'public' && area.scheduled_day !== null) {
        const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        return `每${weekDays[area.scheduled_day]}`;
      }
      return '未设置';
    },

    // 获取最后清理时间
    async getLastCleaningTime(areaId) {
      try {
        const { callCloudFunction } = require('@/utils/auth.js');
        
        const result = await callCloudFunction('hygiene-cleaning', {
          action: 'getCleaningRecords',
          data: {
            area_id: areaId,
            pageSize: 1
          }
        });

        if (result && result.success && result.data && result.data.list && result.data.list.length > 0) {
          const lastRecord = result.data.list[0];
          
          // 如果责任区没有分配负责人，尝试从清理记录中获取
          if (this.areaInfo.assignedEmployee === '未分配') {
            const possibleNames = [
              lastRecord.user_name,
              lastRecord.cleaner_name,
              lastRecord.created_by_name,
              lastRecord.operator_name
            ].filter(name => name);
            
            if (possibleNames.length > 0) {
              this.$set(this.areaInfo, 'assignedEmployee', possibleNames[0]);
            }
          }
          
          return this.formatDateTime(lastRecord.cleaning_date);
        }
        return '';
      } catch (error) {

        return '';
      }
    },

    // 格式化日期时间
    formatDateTime(dateString) {
      if (!dateString) return '';
      
      try {
        let date;
        if (typeof dateString === 'string') {
          if (dateString.includes('T') || dateString.includes('Z')) {
            date = new Date(dateString);
          } else {
            date = new Date(dateString.replace(/-/g, '/'));
          }
        } else {
          date = new Date(dateString);
        }
        
        if (isNaN(date.getTime())) {
          return '';
        }
        
        return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      } catch (error) {

        return '';
      }
    },

    // 星星评分变化
    onStarRateChange(e) {
      this.inspectionForm.rating = e.value;
    },

    // 点击刻度设置评分
    setRatingByMark(rating) {
      this.inspectionForm.rating = rating;
    },

    // 自定义滑动条触摸开始
    onSliderTouchStart(e) {
      this.sliderTouching = true;
      this.updateRatingFromTouch(e);
    },

    // 自定义滑动条触摸移动
    onSliderTouchMove(e) {
      if (this.sliderTouching) {
        this.updateRatingFromTouch(e);
      }
    },

    // 自定义滑动条触摸结束
    onSliderTouchEnd(e) {
      this.sliderTouching = false;
    },

    // 鼠标事件处理（H5端支持）
    onSliderMouseDown(e) {
      this.sliderTouching = true;
      this.updateRatingFromMouse(e);
    },

    onSliderMouseMove(e) {
      if (this.sliderTouching) {
        this.updateRatingFromMouse(e);
      }
    },

    onSliderMouseUp(e) {
      this.sliderTouching = false;
    },

    // 根据触摸位置更新评分
    updateRatingFromTouch(e) {
      const touch = e.touches[0] || e.changedTouches[0];
      // 获取滑动条容器的位置信息
      uni.createSelectorQuery().in(this).select('.custom-slider-container').boundingClientRect((rect) => {
        if (rect) {
          const x = touch.clientX - rect.left;
          const percentage = Math.max(0, Math.min(1, x / rect.width));
          const rating = Math.round(percentage * 5 * 2) / 2; // 精确到0.5
          this.inspectionForm.rating = Math.max(0, Math.min(5, rating));
        }
      }).exec();
    },

    // 根据鼠标位置更新评分（H5端）
    updateRatingFromMouse(e) {
      // 获取滑动条容器的位置信息
      uni.createSelectorQuery().in(this).select('.custom-slider-container').boundingClientRect((rect) => {
        if (rect) {
          const x = e.clientX - rect.left;
          const percentage = Math.max(0, Math.min(1, x / rect.width));
          const rating = Math.round(percentage * 5 * 2) / 2; // 精确到0.5
          this.inspectionForm.rating = Math.max(0, Math.min(5, rating));
        }
      }).exec();
    },

    // 获取评分描述
    getRatingDescription(rating) {
      if (rating === 0) return '请评分';
      if (rating <= 1) return '较差';
      if (rating <= 2) return '一般';
      if (rating <= 3) return '良好';
      if (rating < 5) return '优秀';  // 4-4.5分都是优秀
      if (rating === 5) return '完美';
      return '';
    },

    // 选择图片
    chooseImage() {
      if (this.inspectionForm.photos.length >= 6) {
        uni.showToast({
          title: '最多只能上传6张照片',
          icon: 'none'
        });
        return;
      }

      uni.chooseImage({
        count: 6 - this.inspectionForm.photos.length,
        sizeType: ['compressed'],
        sourceType: ['camera', 'album'],
        success: async (res) => {
          // 先添加照片到列表（显示本地预览）
          const newPhotos = res.tempFilePaths.map(path => ({
            url: path, // 保持本地路径用于显示
            cloudUrl: '', // 云端访问URL
            fileID: '', // 云端文件ID
            name: `检查照片${this.inspectionForm.photos.length + 1}.jpg`,
            uploaded: false,
            uploading: false
          }));
          
          this.inspectionForm.photos = this.inspectionForm.photos.concat(newPhotos);
          
          // 如果开启自动上传，立即上传新选择的照片
          if (this.autoUpload) {
            this.autoUploadNewPhotos(newPhotos);
          }
        }
      });
    },

    // 上传图片
    async uploadImages(tempFilePaths) {
      try {
        
        const uploadResult = await uploadUtils.batchUpload(tempFilePaths, {
          pathGenerator: () => {
            const timestamp = Date.now();
            const random = Math.random().toString(36).substring(2, 8);
            return `6s/inspection/${this.areaId}/${timestamp}_${random}.jpg`;
          },
          maxConcurrent: 2
        });

        if (uploadResult.success) {
          // 更新现有照片的状态，而不是添加新照片
          uploadResult.results.forEach((result, index) => {
            if (result.success) {
              const tempFilePath = tempFilePaths[index];
              const photoIndex = this.inspectionForm.photos.findIndex(photo => photo.url === tempFilePath);
              
              if (photoIndex !== -1) {
                this.$set(this.inspectionForm.photos[photoIndex], 'uploaded', true);
                this.$set(this.inspectionForm.photos[photoIndex], 'cloudUrl', result.url);
                this.$set(this.inspectionForm.photos[photoIndex], 'fileID', result.cloudPath);
                this.$set(this.inspectionForm.photos[photoIndex], 'uploading', false);
                // 保持原始 url 不变，用于显示本地预览
              }
            }
          });

          const successCount = uploadResult.results.filter(r => r.success).length;
          const failCount = uploadResult.results.filter(r => !r.success).length;

          if (failCount > 0) {
            throw new Error(`${failCount}张照片上传失败`);
          }
        } else {
          throw new Error('照片上传失败');
        }
      } catch (error) {
        throw error;
      }
    },

    // 自动上传新选择的照片
    async autoUploadNewPhotos(newPhotos) {
      for (let i = 0; i < newPhotos.length; i++) {
        const photo = newPhotos[i];
        const photoIndex = this.inspectionForm.photos.findIndex(p => p.url === photo.url);
        
        if (photoIndex === -1) continue;
        
        try {
          // 标记为正在上传
          this.$set(this.inspectionForm.photos[photoIndex], 'uploading', true);
          
          // 单张照片上传
          const uploadResult = await this.uploadSinglePhoto(photo);
          
          if (uploadResult.success) {
            // 更新照片信息
            this.$set(this.inspectionForm.photos[photoIndex], 'uploaded', true);
            this.$set(this.inspectionForm.photos[photoIndex], 'cloudUrl', uploadResult.url);
            this.$set(this.inspectionForm.photos[photoIndex], 'fileID', uploadResult.cloudPath);
            this.$set(this.inspectionForm.photos[photoIndex], 'uploading', false);
            // 保持原始 url 不变，用于显示本地预览
          } else {
            throw new Error(uploadResult.error || '上传失败');
          }
        } catch (error) {
          this.$set(this.inspectionForm.photos[photoIndex], 'uploading', false);
          
          // 显示上传失败提示
          uni.showModal({
            title: '上传失败',
            content: `照片${i + 1}上传失败: ${error.message || error}`,
            showCancel: false,
            confirmText: '知道了'
          });
        }
      }
    },

    // 单张照片上传
    async uploadSinglePhoto(photo) {
      try {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);
        const cloudPath = `6s/inspection/${this.areaId}/${timestamp}_${random}.jpg`;
        
        // 使用 uploadToCloud 方法上传单张照片
        const uploadResult = await uploadUtils.uploadToCloud(photo.url, cloudPath, {
          onProgress: (progress) => {
            // 可以在这里更新单张照片的上传进度
          }
        });
        
        if (uploadResult && uploadResult.fileID) {
          // 获取访问URL
          const fileInfo = await uploadUtils.getFileInfo(uploadResult.fileID);
          
          return {
            success: true,
            cloudPath: uploadResult.fileID,
            url: fileInfo.tempFileURL || uploadResult.fileID,
            size: uploadResult.actualSize
          };
        } else {
          throw new Error('上传返回结果异常');
        }
      } catch (error) {
        return { success: false, error: error.message };
      }
    },

    // 切换自动上传状态
    toggleAutoUpload() {
      this.autoUpload = !this.autoUpload;
      uni.showToast({
        title: this.autoUpload ? '已开启自动上传' : '已关闭自动上传',
        icon: 'none'
      });
    },

    // 删除照片
    async deletePhoto(index) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这张照片吗？',
        success: async (res) => {
          if (res.confirm) {
            const photo = this.inspectionForm.photos[index];
            
            // 如果照片已经上传到云存储，尝试删除云存储文件
            if (photo && photo.fileID) {
              try {
                uni.showLoading({ title: '删除照片中...' });
                
                const result = await uniCloud.callFunction({
                  name: 'delete-file',
                  data: {
                    fileList: [this.extractFileId(photo.fileID)]
                  }
                });
                
                if (result.result && result.result.code === 0) {
                  // 删除成功
                } else {
                  // 删除失败但继续删除本地引用
                }
              } catch (error) {
                // 即使删除云存储文件失败，也继续删除本地引用
              } finally {
                uni.hideLoading();
              }
            }
            
            // 删除本地引用
            this.inspectionForm.photos.splice(index, 1);
          }
        }
      });
    },

    // 从URL中提取文件ID
    extractFileId(url) {
      if (url.startsWith('cloud://')) {
        const parts = url.split('/');
        return parts[parts.length - 1];
      } else if (url.includes('tcb-api')) {
        const urlObj = new URL(url);
        return urlObj.pathname.split('/').pop();
      }
      return url;
    },

    // 获取照片显示URL（处理HTTP协议警告）
    getPhotoDisplayUrl(photo) {
      const url = photo.url || photo;
      
      // 如果是本地临时文件路径，在小程序环境下可能会有HTTP协议警告
      // 但这是正常的本地文件路径，直接返回即可
      if (typeof url === 'string' && url.startsWith('http://tmp/')) {
        // 这是微信小程序的本地临时文件路径，直接使用
        return url;
      }
      
      // 优先使用本地URL，如果已上传则可以选择使用云端URL（但为了避免闪烁，保持使用本地URL）
      return url;
    },

    // 预览照片
    previewPhoto(index) {
      const urls = this.inspectionForm.photos.map(photo => this.getPhotoDisplayUrl(photo));
      uni.previewImage({
        urls: urls,
        current: urls[index]
      });
    },

    // 切换问题状态
    toggleIssues(hasIssues) {
      this.inspectionForm.hasIssues = hasIssues;
      if (!hasIssues) {
        // 切换到"检查通过"：清空问题相关信息
        this.inspectionForm.issueDescription = '';
        this.inspectionForm.issueType = '';
      } else {
        // 切换到"发现问题"：重置评分和备注
        this.inspectionForm.rating = 0;
        this.inspectionForm.remarks = '';
      }
    },

    // 处理问题描述输入，确保字符限制和响应式更新
    handleIssueDescriptionInput(e) {
      let value = e.detail.value || '';
      
      // 强制限制字符数量
      if (value.length > 200) {
        value = value.substring(0, 200);
        // 如果超出限制，显示提示
        uni.showToast({
          title: '问题描述不能超过200个字符',
          icon: 'none',
          duration: 1500
        });
      }
      
      // 更新数据
      this.inspectionForm.issueDescription = value;
      
      // 自动推断问题类型
      this.autoDetectIssueType(value);
      
      // 强制触发视图更新
      this.$forceUpdate();
    },

    // 处理备注输入，确保字符限制和响应式更新
    handleRemarksInput(e) {
      let value = e.detail.value || '';
      
      // 强制限制字符数量
      if (value.length > 200) {
        value = value.substring(0, 200);
        // 如果超出限制，显示提示
        uni.showToast({
          title: '检查备注不能超过200个字符',
          icon: 'none',
          duration: 1500
        });
      }
      
      // 更新数据
      this.inspectionForm.remarks = value;
      
      // 强制触发视图更新
      this.$forceUpdate();
    },

    // 根据问题描述自动推断问题类型
    autoDetectIssueType(description) {
      if (!description) return;
      
      const keywords = {
        '设备问题': ['设备', '机器', '故障', '损坏', '噪音', '异响', '漏油', '漏气', '过热', '振动', '磨损'],
        '清洁问题': ['脏', '污', '积尘', '垃圾', '异味', '地面', '清洁', '卫生', '油污', '水渍', '污渍', '不洁', '打扫'],
        '整理问题': ['杂乱', '摆放', '工具', '物品', '整理', '混乱', '堆放', '乱放', '凌乱', '无序', '杂物'],
        '安全问题': ['安全', '隐患', '危险', '滑倒', '积水', '漏电', '阻塞', '堵塞', '阻挡', '绊倒', '碰撞'],
        '环境问题': ['环境', '温度', '湿度', '通风', '采光', '噪声', '粉尘', '气味', '空气', '污染', '废气'],
        '标识问题': ['标识', '标志', '标牌', '不清', '缺失', '模糊', '褪色', '破损', '看不清', '无标识'],
      };
      
      for (let [type, words] of Object.entries(keywords)) {
        if (words.some(word => description.includes(word))) {
          this.inspectionForm.issueType = type;
          return;
        }
      }
      
      // 默认为其他问题
      this.inspectionForm.issueType = '其他问题';
    },

    // 提交检查记录
    async submitInspection() {
      if (!this.canSubmit) return;

      if (this.inspectionForm.hasIssues && !this.inspectionForm.issueDescription.trim()) {
        uni.showToast({
          title: '请填写问题描述',
          icon: 'none'
        });
        return;
      }

      if (this.inspectionForm.hasIssues && this.inspectionForm.photos.length === 0) {
        uni.showToast({
          title: '发现问题时必须上传照片作为证据',
          icon: 'none'
        });
        return;
      }

      uni.showModal({
        title: '确认提交',
        content: '确定要提交本次检查记录吗？',
        success: async (res) => {
          if (res.confirm) {
            await this.doSubmit();
          }
        }
      });
    },

    // 执行提交
    async doSubmit() {
      this.isSubmitting = true;

      try {
        const { callCloudFunction } = require('@/utils/auth.js');
        
        // 如果有问题但没有设置类型，自动推断
        if (this.inspectionForm.hasIssues && this.inspectionForm.issueDescription && !this.inspectionForm.issueType) {
          this.autoDetectIssueType(this.inspectionForm.issueDescription);
        }

        // 检查是否有未上传的照片
        const unuploadedPhotos = this.inspectionForm.photos.filter(photo => !photo.uploaded && !photo.uploading);
        
        if (unuploadedPhotos.length > 0) {
          uni.showLoading({
            title: '正在上传剩余照片...'
          });
          
          // 上传剩余未上传的照片
          const tempFilePaths = unuploadedPhotos.map(photo => photo.url);
          await this.uploadImages(tempFilePaths);
          
          // 重新检查是否还有未上传的照片
          const stillUnuploaded = this.inspectionForm.photos.filter(photo => !photo.uploaded && !photo.uploading);
          if (stillUnuploaded.length > 0) {
            throw new Error('部分照片上传失败，请重试');
          }
        }

        // 准备照片数据（只包含已上传的照片）
        const photos = this.inspectionForm.photos
          .filter(photo => photo.uploaded && photo.fileID)
          .map(photo => ({
            url: photo.fileID,
            type: 'inspection',
            description: ''
          }));

        const inspectionData = {
          area_id: this.areaId,
          inspection_date: new Date().toISOString(),
          result: !this.inspectionForm.hasIssues ? 'passed' : 'failed', // 云函数必需字段
          photos: photos
        };

        // 处理问题描述（移到条件判断外面）
        let finalDescription = '';
        if (this.inspectionForm.hasIssues) {
          finalDescription = this.inspectionForm.issueDescription.trim();
          if (finalDescription.length > 200) {
            finalDescription = finalDescription.substring(0, 200);
            console.warn('问题描述被截断到200字符');
          }
        }

        // 根据检查结果添加相应数据
        if (!this.inspectionForm.hasIssues) {
          // 检查通过：添加评分和备注
          inspectionData.score = this.inspectionForm.rating;
          inspectionData.remarks = this.inspectionForm.remarks.trim() || '检查通过';
        } else {
          // 发现问题：添加问题信息
          inspectionData.score = 0;
          inspectionData.remarks = finalDescription;
          inspectionData.issues = [{
            type: this.inspectionForm.issueType || '其他问题',
            description: finalDescription,
            severity: 'medium'
          }];
        }

        // 保留您原有的字段（如果其他地方需要用到）
        inspectionData.is_passed = !this.inspectionForm.hasIssues;
        inspectionData.overall_rating = this.inspectionForm.rating;
        inspectionData.issue_description = this.inspectionForm.hasIssues ? finalDescription : '';
        inspectionData.is_rectification_recheck = this.isRectificationCheck;
        inspectionData.original_record_id = null;

        // 调用云函数提交检查记录
        const result = await callCloudFunction('hygiene-inspection', {
          action: 'createInspectionRecord',
          data: inspectionData
        });

        if (result && result.success) {
          this.isSubmitting = false;

          uni.showToast({
            title: '提交成功',
            icon: 'success'
          });

          // 通知其他页面数据已更新
          uni.$emit('inspectionRecordUpdated', {
            areaId: this.areaId,
            isPassed: !this.inspectionForm.hasIssues,
            isRectificationCheck: this.isRectificationCheck
          });
          
          // 如果发现问题（检查未通过），还需要通知整改任务创建
          if (this.inspectionForm.hasIssues) {
            uni.$emit('rectificationRecordUpdated', {
              areaId: this.areaId,
              action: 'created',
              isFromInspection: true
            });
          }

          // 2秒后返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          throw new Error(result?.message || '提交检查记录失败');
        }

      } catch (error) {
        this.isSubmitting = false;

        
        let errorMessage = '提交失败，请重试';
        if (error.message) {
          if (error.message.includes('未登录')) {
            errorMessage = '请先登录';
          } else if (error.message.includes('权限')) {
            errorMessage = '您没有权限操作该责任区';
          } else {
            errorMessage = error.message;
          }
        }
        
        uni.showModal({
          title: '提交失败',
          content: errorMessage,
          showCancel: false,
          confirmText: '知道了'
        });
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
	min-height: 100vh;
	background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
	width: 100%;
	overflow-x: hidden; /* 防止水平滚动 */
	box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  flex: 1;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}

.check-time {
  background: rgba(255, 255, 255, 0.2);
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  font-size: 24rpx;
  color: white;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 16rpx;
  margin: 24rpx 32rpx 0 32rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 32rpx 32rpx 16rpx 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  flex: 1;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 4rpx;
}

.card-subtitle {
  font-size: 26rpx;
  color: #8E8E93;
}

/* 自动上传开关样式 */
.auto-upload-toggle {
  display: flex;
  align-items: center;
  gap: 12rpx;
  cursor: pointer;
}

.toggle-label {
  font-size: 24rpx;
  color: #8E8E93;
}

.toggle-switch {
  width: 76rpx;
  height: 44rpx;
  background: #E5E5EA;
  border-radius: 22rpx;
  position: relative;
  transition: background-color 0.3s ease;
}

.toggle-switch.active {
  background: #34C759;
}

.toggle-circle {
  width: 36rpx;
  height: 36rpx;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  transition: transform 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.toggle-switch.active .toggle-circle {
  transform: translateX(32rpx);
}

.card-body {
  padding: 32rpx;
}

/* 增强的信息网格 - 兼容性优化 */
.info-grid-enhanced {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

/* 为不支持CSS Grid的旧版本提供fallback */
.info-grid-enhanced {
  display: flex;
  flex-wrap: wrap;
  margin: -10rpx;
}

.info-grid-enhanced .info-item-enhanced {
  width: calc(50% - 20rpx);
  margin: 10rpx;
}

/* 现代浏览器使用Grid布局 */
@supports (display: grid) {
  .info-grid-enhanced {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
    margin: 0;
  }
  
  .info-grid-enhanced .info-item-enhanced {
    width: auto;
    margin: 0;
  }
}

/* 小屏设备适配 */
@media (max-width: 600rpx) {
  .info-grid-enhanced {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }
  
  /* Flexbox fallback for small screens */
  .info-grid-enhanced .info-item-enhanced {
    width: calc(100% - 20rpx);
  }
  
  @supports (display: grid) {
    .info-grid-enhanced .info-item-enhanced {
      width: auto;
    }
  }
}

.info-item-enhanced {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  padding: 20rpx;
  background: #FAFAFA;
  border-radius: 16rpx;
  border: 2rpx solid #F2F2F7;
  transition: all 0.3s ease;
}

.info-item-enhanced:hover {
  background: white;
  border-color: #E5E5EA;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.info-icon {
  flex-shrink: 0;
  width: 36rpx;
  height: 36rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 2rpx;
}

.info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.info-label-enhanced {
  font-size: 24rpx;
  color: #8E8E93;
  font-weight: 500;
  line-height: 1.2;
}

.info-value-enhanced {
  font-size: 28rpx;
  color: #1C1C1E;
  font-weight: 600;
  line-height: 1.3;
}

/* 评分区域 */
.rating-section {
  text-align: center;
}

.rating-display-large {
  margin-bottom: 32rpx;
}

.rating-number-container {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 6rpx;
  margin-bottom: 8rpx;
}

.rating-number {
  font-size: 80rpx;
  font-weight: 700;
  color: #007AFF;
  line-height: 1;
}

.rating-unit {
  font-size: 40rpx;
  color: #8E8E93;
  font-weight: 500;
}

.star-rating {
  display: flex;
  justify-content: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.rating-tips {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 上传区域 */
.upload-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.upload-item {
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  
  &.has-photo {
    background: #F2F2F7;
  }
  
  &.add-photo {
    border: 2rpx dashed #C7C7CC;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    background: #FAFAFA;
  }
}

.upload-image {
  width: 100%;
  height: 100%;
}

.photo-delete {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  width: 36rpx;
  height: 36rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 照片状态指示器 */
.photo-uploading {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-spinner {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-top: 2rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.photo-uploaded {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #34C759;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-text {
  font-size: 22rpx;
  color: #8E8E93;
}

.upload-count {
  font-size: 20rpx;
  color: #C7C7CC;
  margin-top: 4rpx;
}

/* 问题记录 */
.issue-toggle {
  margin-bottom: 32rpx;
}

.toggle-group {
  display: flex;
  gap: 16rpx;
}

.toggle-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
  
  &.active {
    background: white;
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  }
}

.issue-form,
.remarks-form {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.form-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 16rpx;
}

/* 增强的问题描述输入框 */
.textarea-container {
  position: relative;
  border-radius: 16rpx;
  border: 2rpx solid #F2F2F7;
  background: #FAFAFA;
  overflow: hidden;
  transition: all 0.3s ease;
}



.textarea-container:focus-within {
  border-color: #007AFF;
  background: white;
  box-shadow: 0 2rpx 12rpx rgba(0, 122, 255, 0.15);
}

.issue-textarea-enhanced {
  width: 100%;
  min-height: 160rpx;
  background: transparent;
  padding: 24rpx 20rpx 16rpx 20rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  line-height: 1.5;
  border: none;
  outline: none;
  resize: none;
  box-sizing: border-box;
}

.issue-textarea-enhanced::placeholder {
  color: #C7C7CC;
  font-size: 28rpx;
}

.remarks-textarea-enhanced {
  width: 100%;
  min-height: 120rpx;
  background: transparent;
  padding: 24rpx 20rpx 16rpx 20rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  line-height: 1.5;
  border: none;
  outline: none;
  resize: none;
  box-sizing: border-box;
}

.remarks-textarea-enhanced::placeholder {
  color: #C7C7CC;
  font-size: 28rpx;
}

.char-count-overlay {
  position: absolute;
  bottom: 16rpx;
  right: 20rpx;
  font-size: 24rpx;
  color: #C7C7CC;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.9);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  backdrop-filter: blur(4rpx);
  z-index: 2;
}

/* 简洁的按钮样式 */
.button-container {
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.primary-button {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  width: 100%;
}

.primary-button[disabled] {
  background: #C7C7CC;
  color: #8E8E93;
}

.primary-button.loading {
  background: #0056D6;
  opacity: 0.9;
}

.button-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  color: white;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.submit-tips-enhanced {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 20rpx;
  background: rgba(0, 122, 255, 0.06);
  border-radius: 16rpx;
  border-left: 4rpx solid #007AFF;
}

.tips-icon {
  flex-shrink: 0;
  width: 20rpx;
  height: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tips-text {
  flex: 1;
  font-size: 26rpx;
  color: #0056CC;
  line-height: 1.4;
  font-weight: 500;
}

/* 底部安全间距 */
.bottom-safe-area {
  height: 40rpx;
}

/* 响应式调整 - 使用rpx单位更好的跨平台兼容性 */
@media (max-width: 600rpx) {
  .page-header {
    padding: 24rpx 16rpx;
  }
  
  .card {
    margin: 24rpx 16rpx 0 16rpx;
  }
  
  .button-container {
    padding: 24rpx 16rpx;
  }
}

.auto-type-hint-floating {
  position: absolute;
  bottom: 16rpx;
  left: 20rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  background: #F6FFED;
  border-radius: 12rpx;
  font-size: 24rpx;
  color: #52C41A;
  border: 1rpx solid #B7EB8F;
  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.15);
  z-index: 2;
}

/* 评分描述 */
.rating-desc {
  font-size: 24rpx;
  color: #8E8E93;
  margin-top: 8rpx;
  font-weight: 500;
}

/* 星星评分样式 */
.star-rating {
  display: flex;
  justify-content: center;
  margin: 24rpx 0;
}

/* 自定义滑动条评分样式 */
.custom-slider-rating {
  margin: 32rpx 0 24rpx 0;
}

.custom-slider-container {
  position: relative;
  height: 40rpx;
  margin: 0 24rpx;
  display: flex;
  align-items: center;
  cursor: pointer; /* H5端显示手型光标 */
  user-select: none; /* 防止选中文本 */
}

.slider-track {
  position: absolute;
  width: 100%;
  height: 4rpx;
  background: #E5E5EA;
  border-radius: 2rpx;
  left: 0;
}

.slider-track-active {
  height: 100%;
  background: #FFD700;
  border-radius: 2rpx;
  transition: width 0.2s ease;
}

.slider-thumb {
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  background: #FFD700;
  border-radius: 50%;
  transform: translateX(-50%);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  transition: left 0.2s ease;
  z-index: 2;
  cursor: grab; /* 拖拽光标 */
}

.slider-thumb:active {
  cursor: grabbing; /* 拖拽中光标 */
}

.slider-marks {
  position: absolute;
  width: 100%;
  height: 100%;
  top: -12rpx;
}

.slider-mark {
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background: #E5E5EA;
  border-radius: 50%;
  transform: translateX(-50%);
  cursor: pointer;
  z-index: 1;
  transition: all 0.2s ease;
}

.slider-mark-active {
  background: #FFD700;
  width: 16rpx;
  height: 16rpx;
  box-shadow: 0 2rpx 6rpx rgba(255, 215, 0, 0.3);
}

.slider-labels-external {
  position: relative;
  margin-top: 20rpx;
  margin-left: 24rpx;
  margin-right: 24rpx;
  height: 40rpx;
}

.slider-label-external {
  position: absolute;
  font-size: 24rpx;
  color: #8E8E93;
  font-weight: 500;
  transform: translateX(-50%);
  text-align: center;
  transition: all 0.2s ease;
}

.slider-label-active {
  color: #FFD700;
  font-weight: 600;
  font-size: 26rpx;
}

/* 增强的切换选项样式 */
.toggle-item.enhanced {
  flex: 1;
  display: flex;
  align-items: flex-start;
  padding: 24rpx 20rpx;
  border-radius: 16rpx;
  border: 2rpx solid #F2F2F7;
  background: #FAFAFA;
  transition: all 0.3s ease;
  gap: 12rpx;
}



.toggle-item.enhanced.active {
  border-color: currentColor;
  border-width: 3rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.toggle-item.enhanced:first-child.active {
  border-color: #007AFF;
  background: rgba(0, 122, 255, 0.08);
}

.toggle-item.enhanced:last-child.active {
  border-color: #FF3B30;
  background: rgba(255, 59, 48, 0.08);
}

.toggle-icon {
  flex-shrink: 0;
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 2rpx;
}

.toggle-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.toggle-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1C1C1E;
  line-height: 1.2;
}

.toggle-desc {
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.3;
}

/* 加载状态样式 */
.loading-container {
  padding: 60rpx 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #F2F2F7;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
  text-align: center;
}



@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 0.3; }
}

/* H5浏览器端优化 - 条件编译 */
/* #ifdef H5 */
@media screen and (min-width: 600px) {
  .upload-grid {
    max-width: 480px;
  }
  
  .upload-item {
    max-width: 150px;
    max-height: 150px;
  }
}
/* #endif */
</style> 