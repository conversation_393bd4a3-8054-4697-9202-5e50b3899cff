{"version": 3, "sources": ["webpack:///D:/Xwzc/uni_modules/sp-editor/components/sp-editor/fab-tool.vue?8be9", "webpack:///D:/Xwzc/uni_modules/sp-editor/components/sp-editor/fab-tool.vue?33ff", "webpack:///D:/Xwzc/uni_modules/sp-editor/components/sp-editor/fab-tool.vue?0281", "webpack:///D:/Xwzc/uni_modules/sp-editor/components/sp-editor/fab-tool.vue?66d9", "uni-app:///uni_modules/sp-editor/components/sp-editor/fab-tool.vue", "webpack:///D:/Xwzc/uni_modules/sp-editor/components/sp-editor/fab-tool.vue?495a", "webpack:///D:/Xwzc/uni_modules/sp-editor/components/sp-editor/fab-tool.vue?9c1e"], "names": ["props", "visible", "type", "default", "placement", "data", "placementHeight", "placementType", "watch", "screenWidth", "uni", "createSelectorQuery", "in", "select", "boundingClientRect", "<PERSON><PERSON><PERSON><PERSON>", "exec", "mounted", "computed", "placementStyle", "position", "top", "left", "transform", "right", "bottom", "methods"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,4nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCYtnB;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAP;MAAA;MACA;QACA;UAAAQ;QAEA;UACA;UACAC,IACAC,sBACAC,UACAC,wBACAC;YACA;YACAC;UACA,GACAC;UACA;UACA;YACAN,IACAC,sBACAC,UACAC,mBACAC;cACA;cACA;cACA;gBACA;cACA;gBACA;cACA;gBACA;cACA;YACA,GACAE;UACA;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;UACAC;YACAC;YACAC;UACA;UACA;QACA;UACAF;YACAC;YACAC;YACAC;UACA;UACA;QACA;UACAH;YACAC;YACAG;UACA;UACA;QACA;UACAJ;YACAK;YACAH;UACA;UACA;QACA;UACAF;YACAK;YACAH;YACAC;UACA;UACA;QACA;UACAH;YACAK;YACAD;UACA;UACA;QACA;UACA;MAAA;MAEA;IACA;EACA;EACAE;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;AC1HA;AAAA;AAAA;AAAA;AAA6oC,CAAgB,mnCAAG,EAAC,C;;;;;;;;;;;ACAjqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/sp-editor/components/sp-editor/fab-tool.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./fab-tool.vue?vue&type=template&id=b2da5cfc&\"\nvar renderjs\nimport script from \"./fab-tool.vue?vue&type=script&lang=js&\"\nexport * from \"./fab-tool.vue?vue&type=script&lang=js&\"\nimport style0 from \"./fab-tool.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/sp-editor/components/sp-editor/fab-tool.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fab-tool.vue?vue&type=template&id=b2da5cfc&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fab-tool.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fab-tool.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"fab-tool\">\n    <view id=\"toolfab\">\n      <slot></slot>\n    </view>\n    <view class=\"fab-tool-content\" :style=\"placementStyle\" id=\"placementfab\">\n      <slot name=\"content\" v-if=\"visible\"></slot>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    placement: {\n      type: String,\n      default: 'auto' // 'auto' | 'top-start' | 'top-center' | 'top-end' | 'bottom-start' | 'bottom-center' | 'bottom-end'\n    }\n  },\n  data() {\n    return {\n      placementHeight: '0',\n      placementType: ''\n    }\n  },\n  watch: {\n    visible(newVal) {\n      if (newVal) {\n        const { screenWidth } = uni.getSystemInfoSync()\n\n        this.$nextTick(() => {\n          let placementWidth = 0\n          uni\n            .createSelectorQuery()\n            .in(this)\n            .select('#placementfab')\n            .boundingClientRect((res) => {\n              this.placementHeight = -res.height + 'px'\n              placementWidth = res.width\n            })\n            .exec()\n          // 开启自动模式后\n          if (this.placement == 'auto') {\n            uni\n              .createSelectorQuery()\n              .in(this)\n              .select('#toolfab')\n              .boundingClientRect((res) => {\n                let leftRemain = res.left\n                let rightRemain = screenWidth - leftRemain\n                if (rightRemain > placementWidth) {\n                  this.placementType = 'bottom-start'\n                } else if (leftRemain > placementWidth) {\n                  this.placementType = 'bottom-end'\n                } else {\n                  this.placementType = 'bottom-center'\n                }\n              })\n              .exec()\n          }\n        })\n      }\n    }\n  },\n  mounted() {\n    this.placementType = this.placement\n  },\n  computed: {\n    placementStyle() {\n      let position = {}\n      switch (this.placementType) {\n        case 'top-start':\n          position = {\n            top: this.placementHeight,\n            left: 0\n          }\n          break\n        case 'top-center':\n          position = {\n            top: this.placementHeight,\n            left: '50%',\n            transform: 'translateX(-50%)'\n          }\n          break\n        case 'top-end':\n          position = {\n            top: this.placementHeight,\n            right: 0\n          }\n          break\n        case 'bottom-start':\n          position = {\n            bottom: this.placementHeight,\n            left: 0\n          }\n          break\n        case 'bottom-center':\n          position = {\n            bottom: this.placementHeight,\n            left: '50%',\n            transform: 'translateX(-50%)'\n          }\n          break\n        case 'bottom-end':\n          position = {\n            bottom: this.placementHeight,\n            right: 0\n          }\n          break\n        default:\n          break\n      }\n      return position\n    }\n  },\n  methods: {\n    //\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.fab-tool {\n  position: relative;\n\n  .fab-tool-content {\n    position: absolute;\n    z-index: 999;\n\n    background-color: #ffffff;\n    box-shadow: -2px -2px 4px rgba(0, 0, 0, 0.05), 2px 2px 4px rgba(0, 0, 0, 0.05);\n    border-radius: 12rpx;\n    box-sizing: border-box;\n  }\n}\n</style>\n", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fab-tool.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fab-tool.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775854787\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}