<template>
  <view class="page-container">
    <!-- 月度汇总 -->
    <view class="card">
      <view class="card-header">
        <view class="header-content">
          <view class="card-title">{{ getSummaryTitle() }}</view>
          <view class="time-selector" @click="showTimeSelector">
            <text class="time-text">{{ getCurrentTimeRange() }}</text>
            <uni-icons type="down" size="12" color="#007AFF"></uni-icons>
          </view>
        </view>
      </view>
      <view class="card-body">
        <view class="stats-grid">
          <view class="stats-item">
            <view class="stats-number primary">{{ totalIssues }}</view>
            <view class="stats-label">发现问题</view>
          </view>
          <view class="stats-item">
            <view class="stats-number success">{{ completedIssues }}</view>
            <view class="stats-label">已整改</view>
          </view>
          <view class="stats-item">
            <view class="stats-number warning">{{ inProgressIssues }}</view>
            <view class="stats-label">整改中</view>
          </view>
          <view class="stats-item">
            <view class="stats-number danger">{{ overdueIssues }}</view>
            <view class="stats-label">逾期未改</view>
          </view>
        </view>
        
        <!-- 整改率显示 -->
        <view class="completion-rate">
          <view class="rate-value">{{ completionRate }}%</view>
          <view class="rate-bar">
            <view class="rate-fill" :style="{ width: completionRate + '%' }"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 月度管理 -->
    <view class="card" v-if="hasManagePermission">
      <view class="card-header">
        <view class="header-content">
          <view class="management-title-group">
            <view class="management-icon">
              <uni-icons type="gear-filled" size="20" color="#007AFF"></uni-icons>
            </view>
            <view class="management-text">
              <view class="card-title">月度管理</view>
              <view class="card-subtitle">管理员专属操作面板</view>
            </view>
          </view>
        </view>
      </view>
      <view class="card-body">
        <view class="management-actions">
          <view class="action-btn primary" @click="addNewIssue">
            <uni-icons type="plus" size="20" color="#ffffff"></uni-icons>
            <text>新增问题</text>
          </view>
          <view class="action-btn secondary" @click="manageIssues">
            <uni-icons type="list" size="20" color="#007AFF"></uni-icons>
            <text>问题管理</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 月度问题跟踪 -->
    <view class="card">
      <view class="card-header">
        <view class="header-content">
          <view class="card-title">{{ getIssueTitle() }}</view>
          <view class="card-subtitle">全厂安全卫生检查发现的问题</view>
        </view>
        <!-- 筛选按钮 -->
        <scroll-view class="filter-tabs" scroll-x="true" show-scrollbar="false">
          <view 
            v-for="filter in statusFilters" 
            :key="filter.value"
            class="filter-tab"
            :class="{ active: currentStatusFilter === filter.value }"
            @click="changeStatusFilter(filter.value)"
          >
            {{ filter.label }}
          </view>
        </scroll-view>
      </view>

      <!-- 问题列表 -->
      <view v-if="filteredIssues.length > 0">
        <view v-for="(issue, index) in displayedIssues" :key="index" class="list-item" :class="{ 'no-animations': disableAnimations }" @click="viewIssueDetail(issue)">
          <view class="list-item-icon" :class="['icon-bg-' + issue.status]">
            <text class="issue-number">{{ String(index + 1).padStart(2, '0') }}</text>
          </view>
          <view class="list-item-content">
            <view class="list-item-title">{{ issue.title }}</view>
            <view class="issue-meta">
              <text class="meta-item">{{ issue.location }}</text>
              <text class="meta-separator">·</text>
              <text class="meta-item">{{ issue.responsible }}</text>
              <text class="meta-separator">·</text>
              <text class="meta-item">{{ formatDeadline(issue.deadline) }}</text>
            </view>
          </view>
          <view class="list-item-right">
            <view class="status-badge" :class="['status-' + issue.status]">{{ getIssueStatusText(issue.status) }}</view>
            <uni-icons type="right" size="14" color="#C7C7CC"></uni-icons>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <p-empty-state
        v-else
        useIcon
        iconName="info"
        iconColor="#C7C7CC"
        size="large"
        :text="'暂无' + (getStatusFilterText(currentStatusFilter) || '') + '问题'"
      ></p-empty-state>

      <!-- 查看更多按钮 -->
      <view class="card-body" v-if="hasMoreIssues">
        <view class="more-btn" @click="showMoreIssues">
          <uni-icons type="down" size="16" color="#007AFF"></uni-icons>
          <text>查看更多问题 ({{ remainingIssuesCount }})</text>
        </view>
      </view>
    </view>

    <!-- 整改进度跟踪 -->
    <view class="card">
      <view class="card-header">
        <view class="header-content">
          <view class="card-title">整改进度跟踪</view>
          <view class="card-subtitle">按负责人分组显示</view>
        </view>
      </view>

      <!-- 有负责人数据时显示 -->
      <view v-if="responsiblePersons.length > 0">
      <view v-for="(person, personIndex) in responsiblePersons" :key="personIndex" class="progress-item" @click="viewPersonProgress(person)">
        <view class="progress-header">
          <view class="person-info">
            <view class="person-avatar">
              <uni-icons type="person" size="16" color="#007AFF"></uni-icons>
            </view>
            <view class="person-details">
              <text class="person-name">{{ person.name }}</text>
              <text class="person-role">{{ person.role }}</text>
            </view>
          </view>
          <view class="progress-stats">
            <text class="progress-text">{{ person.completed }}/{{ person.total }}</text>
            <text class="progress-percent">{{ getProgressPercentage(person) }}%</text>
          </view>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" :class="{ 'approved': getProgressPercentage(person) === 100, 'empty': getProgressPercentage(person) === 0 }" :style="{ width: getProgressWidth(person) }"></view>
        </view>
        <!-- 责任问题列表预览 -->
        <view class="person-issues" v-if="person.issues && person.issues.length > 0">
          <view v-for="(issue, issueIndex) in person.issues.slice(0, 2)" :key="issueIndex" class="person-issue">
            <view class="issue-dot" :class="['dot-' + issue.status]"></view>
            <text class="issue-title">{{ issue.title }}</text>
          </view>
          <view v-if="person.issues.length > 2" class="more-issues">
            <text>还有{{ person.issues.length - 2 }}个问题...</text>
          </view>
        </view>
      </view>
      </view>
      
      <!-- 空状态显示 -->
      <p-empty-state
        v-else
        useIcon
        iconName="person"
        iconColor="#C7C7CC"
        size="large"
        text="暂无负责人进度数据"
        subText="当有月度检查问题分配给负责人时，这里会显示进度统计"
      ></p-empty-state>
    </view>
    
    <!-- 底部安全间距 -->
    <view class="bottom-safe-area"></view>

    <!-- 时间选择弹窗 -->
    <uni-popup ref="timePopup" type="bottom" border-radius="16rpx 16rpx 0 0">
      <view class="time-popup">
        <view class="popup-header">
          <text class="popup-title">选择时间范围</text>
          <view class="popup-close" @click="closeTimeSelector">
            <uni-icons type="close" size="18" color="#8E8E93"></uni-icons>
          </view>
        </view>
        <view class="time-options">
          <view 
            v-for="option in timeOptions" 
            :key="option.value"
            class="time-option"
            :class="{ active: selectedTimeFilter === option.value }"
            @click="selectTimeFilter(option)"
          >
            <view class="option-left">
              <uni-icons :type="option.icon" size="18" :color="selectedTimeFilter === option.value ? '#007AFF' : '#8E8E93'"></uni-icons>
              <view class="option-content">
                <text class="option-text">{{ option.label }}</text>
                <text class="option-desc">{{ option.desc }}</text>
              </view>
            </view>
            <view v-if="selectedTimeFilter === option.value" class="option-check">
              <uni-icons type="checkmarkempty" size="16" color="#007AFF"></uni-icons>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 加载状态 - 自定义遮罩 -->
    <view v-if="loading" class="custom-loading-mask">
      <view class="loading-container-enhanced">
        <uni-icons type="spinner-cycle" size="48" color="#007AFF"></uni-icons>
        <text class="loading-text-enhanced">{{ loadingText }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'MonthlyCheck',
  data() {
    return {
      loading: false,
      loadingText: '加载中...',
selectedTimeFilter: 'current',
      currentStatusFilter: 'all',
      displayLimit: 4,
      disableAnimations: false,
      timeOptions: [],
      statusFilters: [
        { label: '全部', value: 'all' },
        { label: '已分配', value: 'assigned' },
        { label: '待整改', value: 'pending' },
        { label: '整改中', value: 'in_progress' },
        { label: '待检查', value: 'pending_review' },
        { label: '检查通过', value: 'approved' }
      ],
      // 当前用户权限
      hasManagePermission: false,
      // 月度问题数据
      monthlyIssues: [],
      // 月度问题总数（用于分页和统计）
      monthlyIssuesTotal: 0,
      // 负责人进度数据
      responsiblePersons: [],
      // 数据缓存
      dataCache: new Map(),
      cacheExpireTime: 5 * 60 * 1000, // 5分钟缓存
      needsRefresh: false, // 标记是否需要刷新数据
      // 配置项
      config: {
        maxPageSize: 1000, // API单次最大获取数据量
        defaultDeadlineDays: 7, // 默认截止日期（天数）
        adminUsernames: ['admin'] // 管理员用户名列表
      }
    }
  },
  computed: {
    totalIssues() {
      return this.monthlyIssues.length
    },
    completedIssues() {
      // 已完成：检查通过
      return this.monthlyIssues.filter(issue => 
        issue.status === 'approved'
      ).length
    },
    inProgressIssues() {
      // 整改中：只统计正在进行整改的问题
      return this.monthlyIssues.filter(issue => issue.status === 'in_progress').length
    },
    overdueIssues() {
      return this.monthlyIssues.filter(issue => issue.status === 'overdue').length
    },
    pendingIssues() {
      // 待处理问题：已分配、待整改、被驳回
      return this.monthlyIssues.filter(issue => 
        issue.status === 'assigned' || issue.status === 'pending' || issue.status === 'rejected'
      ).length
    },
    completionRate() {
      if (this.totalIssues === 0) return 0
      // 完成率基于检查通过的问题
      const approvedIssues = this.monthlyIssues.filter(issue => issue.status === 'approved').length
      return Math.round((approvedIssues / this.totalIssues) * 100)
    },
    filteredIssues() {
      if (this.currentStatusFilter === 'all') {
        return this.monthlyIssues
      }
      return this.monthlyIssues.filter(issue => issue.status === this.currentStatusFilter)
    },
    displayedIssues() {
      return this.filteredIssues.slice(0, this.displayLimit)
    },
    hasMoreIssues() {
      return this.filteredIssues.length > this.displayLimit
    },
    remainingIssuesCount() {
      return this.filteredIssues.length - this.displayLimit
    }
  },
  onLoad() {
    this.initPage()
    this.checkUserPermissions()
    
    // 监听数据更新事件
    uni.$on('cleaningRecordUpdated', this.handleDataUpdated);
    uni.$on('rectificationRecordUpdated', this.handleDataUpdated);
    uni.$on('monthlyIssueUpdated', this.handleDataUpdated);
    uni.$on('issueDraftUpdated', this.handleDataUpdated);
  },

  onUnload() {
    // 移除事件监听
    uni.$off('cleaningRecordUpdated', this.handleDataUpdated);
    uni.$off('rectificationRecordUpdated', this.handleDataUpdated);
    uni.$off('monthlyIssueUpdated', this.handleDataUpdated);
    uni.$off('issueDraftUpdated', this.handleDataUpdated);
  },
  onShow() {
    // 页面显示时检查权限和刷新必要数据
    this.checkUserPermissions();
    
    // 静默加载数据（如果数据为空或缓存过期）
    if (this.monthlyIssues.length === 0 && !this.loading) {
      this.loadDataSilently();
    }
  },
  methods: {
    async initPage() {
      this.showLoading('初始化页面...')
      try {
        await this.loadData()
        this.updateTimeOptions()
      } catch (error) {
        this.showError('页面初始化失败')
      } finally {
        this.hideLoading()
      }
    },

    async loadData() {
      try {
        const cacheKey = `monthly_data_${this.selectedTimeFilter}`;
        const cachedData = this.dataCache.get(cacheKey);
        
        // 检查缓存是否有效
        if (cachedData && (Date.now() - cachedData.timestamp) < this.cacheExpireTime) {
          this.monthlyIssues = cachedData.issues;
          this.monthlyIssuesTotal = cachedData.issuesTotal || cachedData.issues.length;
          this.responsiblePersons = cachedData.responsiblePersons;
          return;
        }
        
        // 并行调用多个API获取月度数据
        const [issuesResult, statsResult] = await Promise.all([
          this.loadMonthlyIssues(),
          this.loadMonthlyStats()
        ]);
        
        this.monthlyIssues = issuesResult || [];
        // 根据实际数据更新统计信息
        if (statsResult) {
          this.updateStatsFromAPI(statsResult);
        }
        
        // 对问题数据进行去重（基于id）
        this.removeDuplicateIssues();
        
        // 计算负责人进度数据
        this.calculateResponsiblePersons();
        
        // 缓存数据
        this.dataCache.set(cacheKey, {
          issues: this.monthlyIssues,
          issuesTotal: this.monthlyIssuesTotal,
          responsiblePersons: this.responsiblePersons,
          timestamp: Date.now()
        });
        
      } catch (error) {
        // API失败时使用空数据，让用户看到空状态
        this.monthlyIssues = [];
        this.responsiblePersons = [];
        // 显示错误提示
        this.showError('无法加载月度数据，请检查网络连接');
      }
    },

    // 加载月度问题数据
    async loadMonthlyIssues() {
      const { callCloudFunction } = require('@/utils/auth.js');
      
      const timeRange = this.getTimeRangeForAPI();
      
      const result = await callCloudFunction('hygiene-monthly-inspection', {
        action: 'getMonthlyIssues',
        data: {
          start_date: timeRange.start,
          end_date: timeRange.end,
          page: 1,
          pageSize: this.config.maxPageSize
        }
      });      
      
      if (result && result.success && result.data && result.data.list) {
        const mappedIssues = result.data.list.map((issue, index) => ({
          id: issue._id || issue.id,
          number: index + 1,
          title: issue.title,
          status: this.mapIssueStatus(issue.status),
          location: this.formatLocation(issue.location_info),
          responsible: issue.assigned_to_name || '未分配',
          responsibleId: issue.assigned_to || null, // 保存真实的用户ID
          role: '负责人',
          deadline: issue.expected_completion_date ? 
            new Date(issue.expected_completion_date).toISOString().split('T')[0] : 
            this.getDefaultDeadline(),
          priority: issue.priority || 'normal',
          description: issue.description,
          createdAt: issue.created_at,
          updatedAt: issue.updated_at,
          images: issue.photos ? issue.photos.map(p => p.url || p) : [],
          history: this.generateIssueHistory(issue)
        }));
        
        // 保存总数信息
        this.monthlyIssuesTotal = result.data.total || mappedIssues.length;
        
        return mappedIssues;
      }
      
      return [];
    },

    // 加载月度统计数据
    async loadMonthlyStats() {
      const { callCloudFunction } = require('@/utils/auth.js');
      
      try {
        const timeRange = this.getTimeRangeForAPI();
        
        const result = await callCloudFunction('hygiene-monthly-inspection', {
          action: 'getMonthlyStatistics',
          data: {
            start_date: timeRange.start,
            end_date: timeRange.end
          }
        });
        
        if (result && result.success && result.data) {
          return result.data;
        }
      } catch (error) {
        // 获取月度统计数据失败
      }
      
      return null;
    },

    // 获取API调用的时间范围
    getTimeRangeForAPI() {
      const now = new Date();
      let start, end;
      
      switch (this.selectedTimeFilter) {
        case 'current':
          // 当月第一天 00:00:00
          start = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);
          // 当月最后一天 23:59:59
          end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
          break;
        case 'last':
          // 上月第一天 00:00:00
          const lastMonthYear = now.getMonth() === 0 ? now.getFullYear() - 1 : now.getFullYear();
          const lastMonth = now.getMonth() === 0 ? 11 : now.getMonth() - 1;
          start = new Date(lastMonthYear, lastMonth, 1, 0, 0, 0, 0);
          // 上月最后一天 23:59:59
          end = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59, 999);
          break;
        case 'quarter':
          // 当前季度第一天 00:00:00
          const quarter = Math.floor(now.getMonth() / 3);
          start = new Date(now.getFullYear(), quarter * 3, 1, 0, 0, 0, 0);
          // 当前季度最后一天 23:59:59
          end = new Date(now.getFullYear(), quarter * 3 + 3, 0, 23, 59, 59, 999);
          break;
        case 'year':
          // 当年第一天 00:00:00
          start = new Date(now.getFullYear(), 0, 1, 0, 0, 0, 0);
          // 当年最后一天 23:59:59
          end = new Date(now.getFullYear(), 11, 31, 23, 59, 59, 999);
          break;
        default:
          // 默认为当月
          start = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);
          end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
      }
      
      // 转换为本地日期字符串（避免时区问题）
      const startDate = this.formatLocalDate(start);
      const endDate = this.formatLocalDate(end);
      
      return {
        start: startDate,
        end: endDate
      };
    },

    // 映射问题状态 - 与issue-detail.vue保持一致
    mapIssueStatus(apiStatus) {
      const statusMap = {
        'submitted': 'pending',
        'assigned': 'assigned',     // 修复：已分配状态应该保持
        'pending': 'pending',       // 待整改
        'in_progress': 'in_progress', // 整改中
        'pending_review': 'pending_review', // 待检查
        'approved': 'approved',     // 检查通过
        'closed': 'closed',         // 已关闭
        'resolved': 'approved',     // API的resolved映射为approved
        'overdue': 'overdue',
        'rejected': 'rejected',     // 修复：被驳回状态应该保持
        'reopened': 'pending',      // 重新打开映射为待整改
        'draft': 'pending'
      };
      return statusMap[apiStatus] || 'pending';
    },

    // 获取默认截止日期
    getDefaultDeadline() {
      const date = new Date();
      date.setDate(date.getDate() + this.config.defaultDeadlineDays);
      return date.toISOString().split('T')[0];
    },

    // 格式化位置信息
    formatLocation(locationInfo) {
      if (!locationInfo) return '未知位置';
      
      let location = locationInfo.location_name || '未知位置';
      
      // 如果有分类信息，添加到前面
      if (locationInfo.location_category) {
        location = `${locationInfo.location_category} - ${location}`;
      }
      
      return location;
    },

    // 根据API数据更新统计信息
    updateStatsFromAPI(apiStats) {
      // 如果API返回了统计数据，使用API数据而不是计算值
      // 这样可以保持页面原有的显示逻辑
    },

    // 生成问题历史记录
    generateIssueHistory(issue) {
      const history = [];
      
      if (issue.created_at) {
        history.push({
          action: '创建问题',
          description: '6S检查员发现问题并记录',
          time: this.formatHistoryTime(issue.created_at),
          operator: issue.reporter_name || '检查员'
        });
      }
      
      if (issue.assigned_to_name) {
        history.push({
          action: '分配负责人',
          description: `将问题分配给${issue.assigned_to_name}处理`,
          time: this.formatHistoryTime(issue.updated_at || issue.created_at),
          operator: '管理员'
        });
      }
      
      // 根据状态添加相应历史
      if (issue.status === 'resolved' || issue.status === 'approved') {
        history.push(
            {
              action: '开始整改',
            description: '负责人已开始处理此问题',
            time: this.formatHistoryTime(issue.updated_at),
            operator: issue.assigned_to_name || '负责人'
            },
            {
              action: '整改完成',
            description: '整改措施已实施完成',
            time: this.formatHistoryTime(issue.actual_completion_date || issue.updated_at),
            operator: issue.assigned_to_name || '负责人'
          }
        );
      } else if (issue.status === 'in_progress') {
        history.push({
          action: '开始整改',
          description: '负责人已开始处理此问题',
          time: this.formatHistoryTime(issue.updated_at),
          operator: issue.assigned_to_name || '负责人'
        });
      }
      
      return history;
    },

    // 格式化历史记录时间
    formatHistoryTime(dateString) {
      if (!dateString) return '--';
      
      try {
        const date = new Date(dateString);
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
      } catch (error) {
        return '--';
      }
    },

    removeDuplicateIssues() {
      // 使用Map根据id去重
      const issueMap = new Map()
      this.monthlyIssues.forEach(issue => {
        issueMap.set(issue.id, issue)
      })
      this.monthlyIssues = Array.from(issueMap.values())
    },

    calculateResponsiblePersons() {
      const personMap = new Map()
      
      this.monthlyIssues.forEach(issue => {
        // 只统计有负责人的问题
        if (!issue.responsibleId || !issue.responsible || issue.responsible === '未分配') {
          return;
        }
        
        const key = issue.responsibleId // 使用用户ID作为key
        if (!personMap.has(key)) {
          personMap.set(key, {
            id: issue.responsibleId, // 使用真实的用户ID
            name: issue.responsible, // 显示姓名
            role: '负责人',
            total: 0,
            completed: 0,
            issues: []
          })
        }
        
        const person = personMap.get(key)
        person.total++
        person.issues.push(issue)
        // 统计已完成的问题：检查通过才算完成
        if (issue.status === 'approved') {
          person.completed++
        }
      })
      
      this.responsiblePersons = Array.from(personMap.values())
    },

showTimeSelector() {
      this.$refs.timePopup.open()
    },

    closeTimeSelector() {
      this.$refs.timePopup.close()
    },

async selectTimeFilter(option) {
      if (this.selectedTimeFilter !== option.value) {
        this.selectedTimeFilter = option.value
        this.$refs.timePopup.close()
        await this.refreshData()
      } else {
        this.$refs.timePopup.close()
      }
    },

    async refreshData() {
      this.showLoading('更新数据...')
      try {
        // 清除缓存，强制重新加载
        this.dataCache.clear();
        await this.loadData()
        uni.showToast({
          title: '数据已更新',
          icon: 'success'
        })
      } catch (error) {
        this.showError('数据更新失败')
      } finally {
        this.hideLoading()
      }
    },

    async loadDataSilently() {
      // 静默加载数据，不显示任何提醒
      try {
        await this.loadData()
      } catch (error) {
        // 静默处理错误，不显示提醒
      }
    },

    changeStatusFilter(status) {
      if (this.currentStatusFilter === status) return; // 避免重复操作
      
      this.currentStatusFilter = status
      this.displayLimit = 4 // 重置显示限制
    },

    getCurrentTimeRange() {
      const ranges = {
        current: '本月',
        last: '上月',
quarter: '本季度',
        year: '本年度'
      }
      return ranges[this.selectedTimeFilter] || '本月'
    },

    getSummaryTitle() {
      const now = new Date()
      const year = now.getFullYear()
      const month = now.getMonth() + 1
      
      const filterMap = {
        'current': `${year}年${month}月汇总`,
        'last': `${month > 1 ? year : year - 1}年${month > 1 ? month - 1 : 12}月汇总`,
'quarter': `${year}年Q${Math.ceil(month / 3)}汇总`,
        'year': `${year}年度汇总`
      }
      return filterMap[this.selectedTimeFilter] || '月度汇总'
    },

    getIssueTitle() {
      const filterMap = {
        'current': '本月问题跟踪',
        'last': '上月问题跟踪',
        'quarter': '本季度问题跟踪',
        'year': '本年度问题跟踪'
      }
      return filterMap[this.selectedTimeFilter] || '问题跟踪'
    },

    getIssueStatusText(status) {
      const statusMap = {
        'assigned': '已分配',
        'pending': '待整改',
        'in_progress': '整改中', 
        'pending_review': '待检查',
        'approved': '检查通过',
        'rejected': '已驳回',
        'overdue': '已逾期',
        'closed': '已关闭',
        'reopened': '重新打开'
      }
      return statusMap[status] || '未知'
    },

    getStatusFilterText(status) {
      const filterMap = {
        'all': '',
        'assigned': '已分配',
        'pending': '待整改',
        'in_progress': '整改中',
        'pending_review': '待检查',
        'approved': '检查通过',
        'rejected': '已驳回',
        'overdue': '已逾期'
      }
      return filterMap[status] || ''
    },

    getProgressWidth(person) {
      const percentage = person.total > 0 ? (person.completed / person.total) * 100 : 0
      // 如果进度为0%，则不显示进度条
      return `${percentage}%`
    },

    getProgressPercentage(person) {
      return person.total > 0 ? Math.round((person.completed / person.total) * 100) : 0
    },

    addNewIssue() {
      uni.navigateTo({
        url: '/pages/6s_pkg/issue-add'
      })
    },

    manageIssues() {
      uni.navigateTo({
        url: '/pages/6s_pkg/issue-list'
      })
    },

    async exportReport() {
      try {
      this.showLoading('生成报告...')
        
        const { callCloudFunction } = require('@/utils/auth.js');
        const timeRange = this.getTimeRangeForAPI();
        
        const result = await callCloudFunction('hygiene-monthly-inspection', {
          action: 'exportMonthlyReport',
          data: {
            start_date: timeRange.start,
            end_date: timeRange.end,
            time_filter: this.selectedTimeFilter
          }
        });
        
        if (result && result.success) {
        uni.showToast({
          title: '报告已生成',
          icon: 'success'
          });
          
          // 如果有下载链接，可以触发下载
          if (result.data && result.data.downloadUrl) {
            // 可以在这里处理文件下载
          }
        } else {
          throw new Error(result.message || '报告生成失败');
        }
        
      } catch (error) {
        uni.showToast({
          title: '报告生成失败',
          icon: 'error'
        });
      } finally {
        this.hideLoading();
      }
    },

    viewIssueDetail(issue) {
      // 将完整的问题数据存储到本地，供详情页使用
      uni.setStorageSync(`issue_detail_${issue.id}`, issue);
      uni.navigateTo({
        url: `/pages/6s_pkg/issue-detail?id=${issue.id}`
      })
    },

    viewPersonProgress(person) {
      uni.navigateTo({
        url: `/pages/6s_pkg/person-progress?id=${encodeURIComponent(person.id)}&name=${encodeURIComponent(person.name)}`
      })
    },

    showMoreIssues() {
      // 防止重复点击
      if (this.disableAnimations) return;
      
      // 添加一个临时的CSS类来禁用动画效果
      this.disableAnimations = true;
      
      this.displayLimit += 4;
      
      // 短暂延迟后恢复动画效果
      setTimeout(() => {
        this.disableAnimations = false;
      }, 100);
    },

    // 工具方法
    
    // 处理数据更新事件
    handleDataUpdated(data) {
      // 清除缓存，确保获取最新数据
      if (this.dataCache) {
        this.dataCache.clear();
      }
      
      // 延迟一下再刷新，确保提交操作完全完成
      setTimeout(() => {
        this.refreshDataSilently();
      }, 200);
    },

    // 静默刷新数据（不显示加载状态）
    async refreshDataSilently() {
      if (this.loading) return; // 如果正在加载，跳过
      
      try {
        // 清除缓存，确保获取最新数据
        this.dataCache.clear();
        
        // 静默重新加载数据，不显示loading状态
        // 使用 setTimeout 避免热更新时的模块引用问题
        await new Promise(resolve => {
          setTimeout(async () => {
            try {
              // 并行获取数据
              const [issuesResult, statsResult] = await Promise.all([
                this.loadMonthlyIssues(),
                this.loadMonthlyStats()
              ]);
              
              // 更新数据
              this.monthlyIssues = issuesResult || [];
              
              // 如果没有问题，重置总数
              if (!issuesResult || issuesResult.length === 0) {
                this.monthlyIssuesTotal = 0;
              }
              
              // 根据实际数据更新统计信息
              if (statsResult) {
                this.updateStatsFromAPI(statsResult);
              }
              
              // 对问题数据进行去重（基于id）
              this.removeDuplicateIssues();
              
              // 计算负责人进度数据
              this.calculateResponsiblePersons();
              
              // 缓存数据
              const cacheKey = `monthly_data_${this.selectedTimeFilter}`;
              this.dataCache.set(cacheKey, {
                issues: this.monthlyIssues,
                issuesTotal: this.monthlyIssuesTotal,
                responsiblePersons: this.responsiblePersons,
                timestamp: Date.now()
              });
              
              resolve();
            } catch (error) {
              resolve();
            }
          }, 100);
        });
        
        // 清除刷新标记，避免用户返回页面时再次显示loading
        this.needsRefresh = false;
      } catch (error) {
        // 静默处理错误，不显示错误提示
      }
    },
    
    // 检查用户权限
    async checkUserPermissions() {
      try {
        const { getCacheKey, CACHE_KEYS } = require('@/utils/cache.js');
        
        // 获取当前用户信息
        const userInfo = uni.getStorageSync('uni-id-pages-userInfo') || {};
        
        // 获取用户角色信息（从专门的角色缓存中获取）
        let userRoleStr = uni.getStorageSync(getCacheKey(CACHE_KEYS.USER_ROLE)) || '{}';
        let userRole = {};
        
        try {
          if (typeof userRoleStr === 'string') {
            userRole = JSON.parse(userRoleStr);
          } else {
            userRole = userRoleStr;
          }
        } catch (e) {
          userRole = {};
        }
        
        // 从userRole.value.userRole获取角色数组
        let roles = [];
        if (userRole && userRole.value && userRole.value.userRole) {
          roles = Array.isArray(userRole.value.userRole) ? userRole.value.userRole : [userRole.value.userRole];
        } else if (userInfo.role) {
          // 备用方案：从用户信息中获取角色
          roles = Array.isArray(userInfo.role) ? userInfo.role : [userInfo.role];
        } else if (this.config.adminUsernames.includes(userInfo.username)) {
          // 特殊处理：如果用户名在管理员列表中，给予admin角色
          roles = ['admin'];
        }
        
        // 月度检查管理权限：管理员或检查员
        this.hasManagePermission = roles.includes('admin') || roles.includes('Integrated') || roles.includes('reviser');
        
      } catch (error) {
        this.hasManagePermission = false;
      }
    },

    updateTimeOptions() {
      const now = new Date()
      const year = now.getFullYear()
      const month = now.getMonth() + 1
      const quarter = Math.ceil(month / 3)
      
      this.timeOptions = [
        { 
          label: '本月', 
          value: 'current', 
          icon: 'calendar',
          desc: `${year}年${month}月`
        },
        { 
          label: '上月', 
          value: 'last', 
          icon: 'calendar',
          desc: `${month > 1 ? year : year - 1}年${month > 1 ? month - 1 : 12}月`
        },
        { 
          label: '本季度', 
          value: 'quarter', 
          icon: 'calendar',
          desc: `${year}年Q${quarter}`
        },
        { 
          label: '本年度', 
          value: 'year', 
          icon: 'calendar',
          desc: `${year}年`
        }
      ]
    },

    showLoading(text = '加载中...') {
      this.loading = true
      this.loadingText = text

    },

    hideLoading() {
      this.loading = false

    },

    showError(message) {
      uni.showToast({
        title: message,
        icon: 'none',
        duration: 3000
      })
    },

    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    },

    formatDeadline(deadline) {
      const date = new Date(deadline);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 格式化本地日期为YYYY-MM-DD格式（避免时区问题）
    formatLocalDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
  }
}
</script>

<style lang="scss" scoped>
.page-container {
	min-height: 100vh;
	background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
	width: 100%;
	overflow-x: hidden;
	box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
}

/* 卡片样式 */
.card {
  background: #ffffff;
  border-radius: 24rpx;
  margin: 0 32rpx 24rpx 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08), 
              0 2rpx 6rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.card:first-child {
  margin-top: 24rpx;
}

.card-header {
  padding: 32rpx 32rpx 24rpx 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.card-body {
  padding: 28rpx;
}

.header-content {
  display: flex;
  flex-direction: row;
  align-items: baseline;
  justify-content: space-between;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 0;
}

.card-subtitle {
  font-size: 26rpx;
  color: #8E8E93;
}

.management-title-group {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.management-icon {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(90, 200, 250, 0.05));
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid rgba(0, 122, 255, 0.1);
}

.management-text {
  display: flex;
  flex-direction: column;
}

.time-filter-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.filter-title {
  font-size: 28rpx;
  color: #1C1C1E;
  font-weight: 500;
}

.time-filter-buttons {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.time-filter-btn {
  padding: 16rpx 32rpx;
  border: 1rpx solid #E5E5E5;
  background: white;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #8E8E93;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8rpx;

  &.active {
    border-color: #007AFF;
    background: #007AFF;
    color: white;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 28rpx;
  text-align: center;
  padding: 4rpx;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 20rpx;
  border-radius: 20rpx;
  background: linear-gradient(145deg, rgba(248, 249, 250, 0.9), rgba(255, 255, 255, 0.8));
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.stats-item:hover {
  transform: translateY(-3rpx);
  background: linear-gradient(145deg, rgba(248, 249, 250, 1), rgba(255, 255, 255, 0.95));
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
}

.stats-number {
  font-size: 52rpx;
  font-weight: 700;
  letter-spacing: -1rpx;
  margin-bottom: 8rpx;

  &.primary {
    background: linear-gradient(135deg, #007AFF, #5AC8FA);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }

  &.success {
    background: linear-gradient(135deg, #34C759, #30D158);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }

  &.warning {
    background: linear-gradient(135deg, #FF9500, #FF9F0A);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }

  &.danger {
    background: linear-gradient(135deg, #FF3B30, #FF453A);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }
}

.stats-label {
  font-size: 26rpx;
  color: #6C7B8A;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

.completion-rate {
  margin-top: 24rpx;
  padding: 28rpx 24rpx;
  border-top: 1rpx solid rgba(240, 240, 240, 0.8);
  background: linear-gradient(135deg, rgba(248, 249, 250, 0.8), rgba(255, 255, 255, 0.9));
  border-radius: 20rpx;
  position: relative;
  overflow: hidden;
}

.completion-rate::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, #007AFF, #5AC8FA, #34C759);
  opacity: 0.6;
}

.rate-label {
  font-size: 28rpx;
  color: #1C1C1E;
  font-weight: 600;
  margin-bottom: 16rpx;
  text-align: center;
}

.rate-value {
  font-size: 56rpx;
  font-weight: 800;
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  margin-bottom: 16rpx;
  text-align: center;
  letter-spacing: -2rpx;
}

.rate-bar {
  height: 16rpx;
  background: rgba(240, 240, 240, 0.8);
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.rate-fill {
  height: 100%;
  background: linear-gradient(90deg, #007AFF 0%, #5AC8FA 50%, #34C759 100%);
  border-radius: 8rpx;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
  position: relative;
}

.rate-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.3), transparent);
  border-radius: 8rpx 8rpx 0 0;
}

.management-actions {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 28rpx 32rpx;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex: 1;
  position: relative;
  overflow: hidden;

  &.primary {
    background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
    color: #ffffff;
    box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3),
                0 4rpx 8rpx rgba(0, 122, 255, 0.2);
    border: none;
  }
  
  &.primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
  }
  
  &.primary:active::before {
    left: 100%;
  }
  
  &.primary:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3),
                0 2rpx 4rpx rgba(0, 122, 255, 0.2);
  }

  &.secondary {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    color: #007AFF;
    border: 2rpx solid rgba(0, 122, 255, 0.25);
    backdrop-filter: blur(15rpx);
    box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.12),
                0 2rpx 4rpx rgba(0, 122, 255, 0.08);
    position: relative;
    overflow: hidden;
  }
  
  &.secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 122, 255, 0.1), transparent);
    transition: left 0.6s ease;
  }
  
  &.secondary:active::before {
    left: 100%;
  }
  
  &.secondary:active {
    background: linear-gradient(135deg, rgba(230, 243, 255, 0.95) 0%, rgba(240, 248, 255, 0.9) 100%);
    transform: translateY(1rpx);
    box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.15),
                0 1rpx 2rpx rgba(0, 122, 255, 0.1);
  }
}

.filter-tabs {
  white-space: nowrap;
  padding: 12rpx 0 0 0;
  margin-top: 16rpx;
}

.filter-tabs::-webkit-scrollbar {
  display: none;
}

.filter-tab {
  display: inline-block;
  padding: 12rpx 20rpx;
  border: 2rpx solid #E5E5E5;
  background: #ffffff;
  border-radius: 24rpx;
  font-size: 24rpx;
  color: #8E8E93;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  letter-spacing: 0.3rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  white-space: nowrap;
  margin: 4rpx 12rpx 4rpx 0;

  &.active {
    border-color: #007AFF;
    background: linear-gradient(135deg, #007AFF, #5AC8FA);
    color: #ffffff;
    transform: translateY(-2rpx);
    box-shadow: 0 6rpx 16rpx rgba(0, 122, 255, 0.3);
  }
  
  &:hover:not(.active) {
    border-color: #B3D9FF;
    background: #F8FBFF;
    transform: translateY(-1rpx);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  }
}

.list-item {
  display: flex;
  align-items: center;
  padding: 28rpx 24rpx 28rpx 32rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #F0F0F0;
  transition: background 0.2s ease, transform 0.2s ease, border-bottom 0s ease 0.1s;
  cursor: pointer;
}

.list-item:hover {
  background: #F8F9FA;
  transform: translateX(2rpx);
}

.list-item:active {
  transform: translateX(2rpx) scale(0.98);
  transition: all 0.1s ease;
}

.list-item.no-animations {
  pointer-events: none;
  transition: none !important;
}

.list-item.no-animations:hover,
.list-item.no-animations:active {
  transform: none !important;
  background: #ffffff !important;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-icon {
  width: 88rpx;
  height: 88rpx;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;

  &.icon-bg-approved {
    background: linear-gradient(135deg, rgba(52, 199, 89, 0.15), rgba(48, 209, 88, 0.08));
    border: 2rpx solid rgba(52, 199, 89, 0.3);
  }

  &.icon-bg-pending_review {
    background: linear-gradient(135deg, rgba(88, 86, 214, 0.15), rgba(88, 86, 214, 0.08));
    border: 2rpx solid rgba(88, 86, 214, 0.3);
  }

  &.icon-bg-in_progress {
    background: linear-gradient(135deg, rgba(255, 149, 0, 0.15), rgba(255, 159, 10, 0.08));
    border: 2rpx solid rgba(255, 149, 0, 0.3);
  }

  &.icon-bg-overdue {
    background: linear-gradient(135deg, rgba(255, 59, 48, 0.15), rgba(255, 69, 58, 0.08));
    border: 2rpx solid rgba(255, 59, 48, 0.3);
  }

  &.icon-bg-pending {
    background: linear-gradient(135deg, rgba(0, 122, 255, 0.15), rgba(90, 200, 250, 0.08));
    border: 2rpx solid rgba(0, 122, 255, 0.3);
  }

  &.icon-bg-assigned {
    background: linear-gradient(135deg, rgba(8, 145, 178, 0.15), rgba(6, 182, 212, 0.08));
    border: 2rpx solid rgba(8, 145, 178, 0.3);
  }

  &.icon-bg-approved {
    background: linear-gradient(135deg, rgba(22, 163, 74, 0.15), rgba(34, 197, 94, 0.08));
    border: 2rpx solid rgba(22, 163, 74, 0.3);
  }

  &.icon-bg-rejected {
    background: linear-gradient(135deg, rgba(142, 142, 147, 0.15), rgba(142, 142, 147, 0.08));
    border: 2rpx solid rgba(142, 142, 147, 0.3);
  }
}

.issue-number {
  font-size: 30rpx;
  font-weight: 800;
  letter-spacing: -0.5rpx;
}

.list-item-icon.icon-bg-approved .issue-number {
  color: #34C759;
}

.list-item-icon.icon-bg-pending_review .issue-number {
  color: #5856D6;
}

.list-item-icon.icon-bg-in_progress .issue-number {
  color: #FF9500;
}

.list-item-icon.icon-bg-overdue .issue-number {
  color: #FF3B30;
}

.list-item-icon.icon-bg-pending .issue-number {
  color: #007AFF;
}

.list-item-icon.icon-bg-assigned .issue-number {
  color: #0891B2;
}

.list-item-icon.icon-bg-approved .issue-number {
  color: #16A34A;
}

.list-item-icon.icon-bg-rejected .issue-number {
  color: #8E8E93;
}

.list-item-content {
  flex: 1;
  min-width: 0;
  margin-right: 12rpx;
}

.list-item-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 12rpx;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-clamp: 2;
}

.issue-meta {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #C7C7CC;
  flex-wrap: wrap;
}

.meta-item {
  color: #8E8E93;
}

.meta-separator {
  color: #C7C7CC;
}

.list-item-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-left: 16rpx;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 600;
  letter-spacing: 0.3rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  &.status-approved {
    background: linear-gradient(135deg, #E8F5E8, #F0FFF0);
    color: #34C759;
    border: 2rpx solid rgba(52, 199, 89, 0.2);
  }

  &.status-pending_review {
    background: linear-gradient(135deg, #F0EFFF, #F8F7FF);
    color: #5856D6;
    border: 2rpx solid rgba(88, 86, 214, 0.2);
  }

  &.status-in_progress {
    background: linear-gradient(135deg, #FFF4E6, #FFFBF0);
    color: #FF9500;
    border: 2rpx solid rgba(255, 149, 0, 0.2);
  }

  &.status-overdue {
    background: linear-gradient(135deg, #FFE6E6, #FFF0F0);
    color: #FF3B30;
    border: 2rpx solid rgba(255, 59, 48, 0.2);
  }

  &.status-pending {
    background: linear-gradient(135deg, #E6F3FF, #F0F7FF);
    color: #007AFF;
    border: 2rpx solid rgba(0, 122, 255, 0.2);
  }

  &.status-assigned {
    background: linear-gradient(135deg, #ECFEFF, #F0FDFF);
    color: #0891B2;
    border: 2rpx solid rgba(8, 145, 178, 0.2);
  }

  &.status-approved {
    background: linear-gradient(135deg, #E8F5E8, #F0FFF0);
    color: #34C759;
    border: 2rpx solid rgba(52, 199, 89, 0.2);
  }

  &.status-rejected {
    background: linear-gradient(135deg, #F2F2F7, #F8F8F8);
    color: #8E8E93;
    border: 2rpx solid rgba(142, 142, 147, 0.2);
  }
}

.progress-item {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.progress-item:last-child {
  border-bottom: none;
}

.progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.person-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.person-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: rgba(0, 122, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.person-details {
  display: flex;
  flex-direction: column;
}

.person-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #1C1C1E;
  margin-bottom: 6rpx;
  line-height: 1.2;
}

.person-role {
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.2;
}

.progress-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4rpx;
}

.progress-text {
  font-size: 26rpx;
  color: #007AFF;
  font-weight: 500;
}

.progress-percent {
  font-size: 22rpx;
  color: #8E8E93;
}

.person-issues {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #F5F5F5;
}

.person-issue {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.issue-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  flex-shrink: 0;

  &.dot-approved {
    background: #34C759;
  }

  &.dot-pending_review {
    background: #5856D6;
  }

  &.dot-in_progress {
    background: #FF9500;
  }

  &.dot-overdue {
    background: #FF3B30;
  }

  &.dot-pending {
    background: #007AFF;
  }

  &.dot-assigned {
    background: #0891B2;
  }

  &.dot-approved {
    background: #16A34A;
  }

  &.dot-rejected {
    background: #8E8E93;
  }
}

.person-issue .issue-title {
  font-size: 24rpx;
  color: #666;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.more-issues {
  font-size: 22rpx;
  color: #8E8E93;
  margin-top: 8rpx;
}

.progress-bar {
  height: 8rpx;
  background: #F0F0F0;
  border-radius: 4rpx;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  background: linear-gradient(90deg, #007AFF 0%, #5AC8FA 100%);
  
  &.approved {
    background: linear-gradient(90deg, #34C759 0%, #30D158 100%);
  }
  
  &.empty {
    background: linear-gradient(90deg, #FF9500 0%, #FF9F0A 100%);
    opacity: 0.6;
  }
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.3), transparent);
    border-radius: 4rpx 4rpx 0 0;
  }
}

.more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 28rpx;
  font-size: 30rpx;
  color: #007AFF;
  border: 2rpx solid rgba(0, 122, 255, 0.2);
  border-radius: 16rpx;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(255, 255, 255, 0.9));
  font-weight: 600;
  letter-spacing: 0.5rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.more-btn:active {
  transform: translateY(2rpx);
  background: linear-gradient(135deg, rgba(230, 243, 255, 0.9), rgba(240, 247, 255, 0.95));
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.15);
  transition: all 0.1s ease;
}

.more-btn:not(:active) {
  transform: translateY(0);
}

.bottom-safe-area {
  height: 40rpx;
}

.time-popup {
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  padding: 0 0 env(safe-area-inset-bottom) 0;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
}

.popup-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F2F2F7;
  border-radius: 50%;
}

.time-options {
  padding: 24rpx 32rpx;
}

.time-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F2F2F7;
  position: relative;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.active {
    .option-text {
      color: #007AFF;
      font-weight: 600;
    }
  }
}

.option-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.option-content {
  margin-left: 16rpx;
  flex: 1;
}

.option-text {
  font-size: 28rpx;
  color: #1C1C1E;
  display: block;
  margin-bottom: 4rpx;
}

.option-desc {
  font-size: 24rpx;
  color: #8E8E93;
  display: block;
}

.option-check {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(8rpx);
  z-index: 9999;
  animation: maskFadeIn 0.3s ease-out;
  margin: 0;
  padding: 0;
}

@keyframes maskFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.loading-container-enhanced {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 32rpx;
  padding: 80rpx 60rpx;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  min-width: 320rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.25), 
              0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
  animation: containerSlideIn 0.4s ease-out 0.1s both;
}

@keyframes containerSlideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.loading-text-enhanced {
  font-size: 32rpx;
  color: #1C1C1E;
  font-weight: 600;
  margin-top: 8rpx;
  text-align: center;
  line-height: 1.3;
}

/* 时间选择器通用样式 */
.time-selector {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 10rpx 18rpx;
  background: linear-gradient(135deg, #E6F3FF, #F0F7FF);
  border-radius: 24rpx;
  border: 1rpx solid rgba(0, 122, 255, 0.2);
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.1);
  cursor: pointer;
}

.time-selector:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, #D0E8FF, #E6F3FF);
}

.time-text {
  font-size: 26rpx;
  color: #007AFF;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

/* H5端样式 */
/* #ifdef H5 */
@media (max-width: 414px) {
  .card {
    margin: 0 16rpx 24rpx 16rpx;
  }

  .card:first-child {
    margin-top: 24rpx;
  }
  
  .action-btn {
    padding: 24rpx 28rpx;
    font-size: 28rpx;
  }
}
/* #endif */
</style> 