{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/Xwzc/pages/6s_pkg/my-areas.vue?630c", "webpack:///D:/Xwzc/pages/6s_pkg/my-areas.vue?501e", "webpack:///D:/Xwzc/pages/6s_pkg/my-areas.vue?23b9", "uni-app:///pages/6s_pkg/my-areas.vue", "webpack:///D:/Xwzc/pages/6s_pkg/my-areas.vue?814a", "webpack:///D:/Xwzc/pages/6s_pkg/my-areas.vue?2136"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "dateFilterMode", "selected<PERSON><PERSON><PERSON><PERSON>er", "customDateRange", "startDate", "endDate", "calendarDate", "calendarStartDate", "calendarEndDate", "refreshTimer", "calendarChangeTimer", "quickDateOptions", "label", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "my<PERSON><PERSON>s", "rectificationTasks", "cleaningRecords", "list", "pageSize", "hasMore", "loading", "allCleaningRecords", "dataLoaded", "needsRefresh", "expandedRectificationWeeks", "currentWeekCache", "areasCache", "computed", "myFixedAreas", "sourceLength", "fixed", "public", "myPublicAreas", "completedCount", "task", "console", "pendingCount", "area", "overdueCount", "filteredRectificationTasks", "tasks", "groupedRectificationTasks", "grouped", "<PERSON><PERSON><PERSON>", "title", "expanded", "yearA", "weekA", "yearB", "weekB", "sortedGroups", "group", "created", "onLoad", "uni", "onUnload", "clearTimeout", "onShow", "methods", "getThisWeekRectificationTasks", "isRectificationOverdue", "loadMyAreas", "Promise", "icon", "duration", "loadUserAssignments", "action", "result", "assignments", "areaIds", "areaDataMap", "assignment", "id", "type", "weeklyRequired", "location", "description", "last_clean_date", "areaData", "subtitle", "status", "lastCleaningDate", "loadLatestCleaningRecords", "now", "weekStart", "weekEnd", "area_ids", "start_date", "end_date", "latest_only", "promises", "area_id", "latestRecord", "loadMyCleaningRecords", "requestData", "date<PERSON><PERSON><PERSON>", "records", "formattedRecords", "areaId", "areaName", "areaType", "cleaningDate", "formattedDate", "photos", "remark", "week", "loadMoreCleaningRecords", "currentDisplayed", "nextPageSize", "nextPageEnd", "nextPageData", "loadUserRectificationTasks", "originalStatus", "issueFoundDate", "completedDate", "assignee_id", "assignee_name", "created_at", "deadline", "generateAreaSubtitle", "calculateAreaStatus", "nextWeekStart", "scheduledDate", "scheduledStart", "scheduledEnd", "getTaskIcon", "getAreaIcon", "toBeijingTime", "getFullYear", "getMonth", "getDate", "getDay", "getHours", "getMinutes", "getSeconds", "getTime", "toISOString", "valueOf", "getWeekKey", "year", "weekNum", "dateString", "getWeekNumber", "month", "day", "dayOfWeek", "thursday", "firstThursday", "getWeekStart", "getWeekEnd", "formatDate", "date", "formatDateTime", "getStatusTitle", "getCurrentTimeRange", "getWeekTitle", "getWeekRangeByWeekKey", "start", "end", "targetThursday", "monday", "sunday", "getCurrentWeekNumber", "calculateRectificationStatus", "setDefaultExpandedWeek", "targetWeek", "toggleRectificationWeek", "resetExpandedState", "smartResetExpandedState", "setTimeout", "getStatusText", "getCleaningRecordStatusText", "formatDescription", "replace", "trim", "formatted", "handleRecordUpdated", "silentRefreshData", "getIconColor", "handleOverdueStatus", "content", "showCancel", "confirmText", "openAreaDetail", "url", "openPublicAreaDetail", "openRectificationDetail", "openCleaningRecordDetail", "showTimeSelector", "closeDatePicker", "initializeDateRange", "switchToRangeMode", "switchToQuickMode", "selectQuickDateOption", "getCurrentDateRange", "getQuickDateRange", "startOfWeek", "endOfWeek", "lastWeekStart", "lastWeekEnd", "endOfMonth", "formatDateForPicker", "getDateRangeText", "getStartDateText", "getEndDateText", "formatDisplayDate", "onCalendarChange", "onMonthSwitch", "formatSelectedDate"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,iVAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvJA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,4nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;ACsUtnB;AAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACA;MACA;MACAC;MACAC;MAEA;MACAC;QACAC;QACAC;MACA;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC,mBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;;MACAC;MAAA;;MAEA;MACAD;MACAE;MAAA;MACAC;MAAA;;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACA;UACAC;UACAC;YAAA;UAAA;UACAC;YAAA;UAAA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;UACAH;UACAC;YAAA;UAAA;UACAC;YAAA;UAAA;QACA;MACA;MACA;IACA;IAEAE;MACA;QACA;QACA;UACA;YAAA;UAAA;UACA;YAAA,OACAC;UAAA,EACA;UACA;QACA;UACA;UACA;YAAA,OACAA;UAAA,EACA;UACA;QACA;MACA;QACAC;QACA;MACA;IACA;IACAC;MACA;QACA;QACA;UACA;UACA;YAAA,OACAC;UAAA;UAAA,CACA;UACA;UACA;YAAA,OACAH;UAAA;UAAA,CACA;UACA;QACA;UACA;UACA;YAAA,OACAA;UAAA;UAAA,CACA;UACA;QACA;MACA;QACAC;QACA;MACA;IACA;IACAG;MAAA;MACA;QACA;QACA;UACA;YAAA;UAAA;UACA;UACA;YAAA,OACAJ;UAAA,EACA;UACA;QACA;UACA;UACA;YAAA,OACAA;UAAA,EACA;UACA;QACA;MACA;QACAC;QACA;MACA;IACA;IACA;IACAI;MACA;;MAEA;MACA;MACA;QACAC;UACA;UACA;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MAEAD;QACA;QACA;QACA;QACA;UACAE;YACAC;YACAC;YACAJ;YACAK;UACA;QACA;QACAH;MACA;;MAEA;MACA;QACA;QACA;YAAA;UAAA;UAAA;UAAAI;UAAAC;QACA;YAAA;UAAA;UAAA;UAAAC;UAAAC;QAEA;UACA;QACA;;QACA;MACA;;MAEA;MACAC;QACAC;MACA;MAIA;IACA;EACA;EACAC,6BACA;EACAC;IACA;;IAEA;IACAC;IACA;IACAA;EACA;EACAC;IACA;IACAD;IACAA;;IAEA;IACA;MACAE;IACA;IACA;MACAA;IACA;EACA;EACAC;IACA;IACA;IACA;MACA;MACA;IACA;EACA;;EACAC;IAGA;IACAC;MACA;QACA;QACA;QAEA;UACAxB;UACA;QACA;QAEA;QACA;QAEA;UACAA;UACA;QACA;QAEA;UACA;QACA;QAEA;UACA;UACA;YACA;YACA;YACA;UACA;YACAA;YACA;UACA;QACA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACAyB;MACA;QACA;MACA;;MAEA;QACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAGAC,aACA,8BACA,qCACA,+BACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAR;kBACAV;kBACAmB;kBACAC;gBACA;gBACA;gBACA;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;kBACAC;kBACAnE;gBACA;cAAA;gBAHAoE;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACAC,iCAEA;gBACA;;gBAEA;gBACAC;gBACAC;gBAEAF;kBACA;oBACAG;sBACA;sBACAF;;sBAEA;sBACA;wBACAG;wBACA1E;wBACAiE;wBACAU;wBACAC;wBACAC;wBACAC;wBACAC;sBACA;;sBAEA;sBACA;wBACAC;wBACAA;sBACA;sBAEAR;oBACA;kBACA;gBACA;;gBAEA;gBAAA,MACAD;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAGA;gBACA;kBACA;kBACA,uCACAhC;oBACA0C;oBACAC;oBACAjB;oBACAkB;kBAAA;gBAEA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACA;kBACAC;kBACA;oBACAA;oBACAC;oBACAC;kBACA;gBACA;gBAAA,wBACA,+GAEA;gBAAA;gBAAA;gBAAA,OAEA;kBACAnB;kBACAnE;oBACAuF;oBACAC;oBACAC;oBACAC;kBACA;gBACA;cAAA;gBARAtB;gBAUA;kBACA;kBACAA;oBACA;sBACAG;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBAEAoB;kBAAA;oBAAA;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;4BAAA;4BAAA,OAEA;8BACAxB;8BACAnE;gCACA4F;gCACAJ;gCACAC;gCACAtE;8BACA;4BACA;0BAAA;4BARAiD;4BAUA;8BACAyB;8BACA;gCACAtB;8BACA;4BACA;4BAAA;4BAAA;0BAAA;4BAAA;4BAAA;0BAAA;0BAAA;4BAAA;wBAAA;sBAAA;oBAAA;kBAAA,CAIA;kBAAA;oBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAR;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAMA;IAEA;IACA+B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACA;;gBAEA;gBACAC;kBACA5E;gBACA,GAEA;gBACA6E;gBACA;kBACAD;kBACAA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;kBACA5B;kBACAnE;gBACA;cAAA;gBAHAoE;gBAKA;kBACA6B,6BAEA;kBACAC;oBACA;sBACAzB;sBACA0B;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAxB;sBAAA;sBACAjB;sBACA0C;oBACA;kBACA,IAEA;kBACA;;kBAEA;kBACA;kBACA;;kBAEA;kBACA;oBAAA,OACA;kBAAA,EACA;gBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBAEAC;gBACAC;gBACAC,+CAEA;gBACA;kBACAC,+EAEA;kBACA;;kBAEA;kBACA;gBACA;kBACA;gBACA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAjB,kBAEA;gBACAC;gBACA;kBACAD;kBACAA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;kBACA5B;kBACAnE;gBACA;cAAA;gBAHAoE;gBAKA;kBACA3B,2BAEA;kBACA;oBACA;;oBAEA;oBACA;;oBAEA;oBACA;oBACA;sBACA;oBACA;;oBAEA;sBACAgC;sBACA0B;sBACAC;sBACApB;sBACAC;sBACAgC;sBAAA;sBACAjD;sBACAqC;sBACAK;sBACAQ;sBACAC;sBACAC;sBACAC;sBACAC;sBAAA;sBACAC;oBACA;kBACA;oBAAA;kBAAA;;kBAEA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;QACA;UACA;UACA;UACA;UACA;;UAEA;UACA;YACA;YACA;UACA;YACA;UACA;QACA;UACA;QACA;MACA;QACA;QACA;UACA;YACA;YACA;UACA;YACA;YACA;;YAEA;YACA;cACA;YACA;cACA;YACA;cACA;YACA;cACA;YACA;UACA;QACA;UACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;UACArC;UACAC;UACAC;QACA;MACA;MACA;QAAAF;QAAAC;QAAAC;MAEA;QACA;QACA;UACA;UACA;YACA;UACA;QACA;;QAEA;QACA;QACA;QACA;QACAoC;;QAEA;UAAA;UACA;UACA;YACA;UACA;;UAEA;UACA;YACA;UACA;QACA;;QAEA;QACA;MACA;QACA;QACA;UACA;YACA;YACA;cACA;YACA;UACA;;UAEA;UACA;UACA;;UAEA;UACA;UACA;UAEA;YACA;YACAC;UACA;YACA;YACAA;UACA;UAEA;UACAC;;UAEA;UACAC;;UAEA;;UAEA;UACA;YACA;UACA;UACA;UAAA,KACA;YACA;UACA;UACA;UAAA,KACA;YACA;UACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QAAA;QACA;QACA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;;MACA;IACA;IAEA;IACAC;MACA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MAEA;QACA;QACA;UACA;QACA;;QAEA;QACA;QACA;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;UACAC;YAAA;UAAA;UACAC;YAAA;UAAA;UACAC;YAAA;UAAA;UACAC;YAAA;UAAA;UACAC;YAAA;UAAA;UACAC;YAAA;UAAA;UACAC;YAAA;UAAA;UACAC;YAAA;UAAA;UACAC;YAAA;UAAA;UACA;UACAC;YAAA;UAAA;QACA;MACA;QACAtG;QACA;MACA;IACA;IAEA;IACAuG;MACA;QACA;QAEA;QACA;UACAvG;UACA;QACA;QAEA;QACA;QAEA;UACAA;YAAAwG;YAAAC;YAAAC;UAAA;UACA;QACA;QAEA;QAEA;MACA;QACA1G;QACA;MACA;IACA;IAEA;IACA2G;MACA;QACA;UACA;QACA;;QAEA;QACA;QAEA;UACA;UACAH;UACAI;UACAC;UACAC;QACA;UACA;UACA;UACA;YACA;UACA;;UACAN;UACAI;UACAC;UACAC;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;QACA;UACA;QACA;;QAEA;QACA;;QAEA;QACA;QACAC;QAEA;UACA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;;QAEA;QACAC;QAEA;UACA;QACA;;QAEA;QACA;QAEA;MACA;QACAhH;QACA;MACA;IACA;IAEA;IACAiH;MACA;QACA;MACA;MAEA;QACA;QACA;QAEA;UACA;UACAT;UACAI;UACAC;UACAC;QACA;UACA;UACA;UACA;YACA;UACA;UACAN;UACAI;UACAC;UACAC;QACA;;QAEA;QACA;UACA;QACA;QAEA;;QAEA;QACA;QAEA;UACA;QACA;QAEA;MACA;QACA9G;QACA;MACA;IACA;IAEA;IACAkH;MACA;QACA;MACA;MAEA;QACA;QACA;QAEA;UACA;UACAV;UACAI;UACAC;UACAC;QACA;UACA;UACA;UACA;YACA;UACA;UACAN;UACAI;UACAC;UACAC;QACA;;QAEA;QACA;UACA;QACA;QAEA;;QAEA;QACA;QAEA;UACA;QACA;QAEA;MACA;QACA9G;QACA;MACA;IACA;IAEA;IACAmH;MACA;MAEA;QACA;QACA;QACA;UACA;UACA;YACAC;UACA;YACA;YACAA;UACA;QACA;UACAA;QACA;;QAEA;QACA;UACA;QACA;QAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;QACA;QACA;UACA;UACA;YACAD;UACA;YACA;YACAA;UACA;QACA;UACAA;QACA;;QAEA;QACA;UACA;QACA;QAEA;MACA;QACA;MACA;IACA;IAEAE;MAAA;MACA;QACA;MACA;QACA;UAAA;QAAA;QACA;MACA;QACA;MACA;MACA;IACA;IAEAC;MAAA;MACA;QACA;MACA;QACA;UAAA;QAAA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;QACA;QACA;UACA;QACA;UACA;QACA;UACA;UACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UACA;YAAAC;YAAAC;UAAA;QACA;QAEA;QACA;UAAAD;UAAAC;QAAA;QAEA;QACA;QAEA;UACA;YAAAD;YAAAC;UAAA;QACA;;QAEA;QACA;QACA;UACA;YAAAD;YAAAC;UAAA;QACA;QAEA;QACA;;QAEA;QACAX;;QAEA;QACA;QACAY;QAEA;UACA;YAAAF;YAAAC;UAAA;QACA;;QAEA;QACA;QACAE;;QAEA;QACA;QACAC;;QAEA;UACA;YAAAJ;YAAAC;UAAA;QACA;QAEA;UACAD;UACAC;UACAE;UACAC;QACA;MACA;QACA9H;QACA;UAAA0H;UAAAC;QAAA;MACA;IACA;IAEA;IACAI;MACA;QACA;QACA;QAEA;UACA/H;UACA;QACA;;QAEA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACAgI;MACA;;MAIA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;UACA;UACA;YACA;UACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QAEA;UACA;UACA;UACA;;UAEA;UACA;YACA;UACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;MACA;QAAA;MAAA;MAEA;QACA;QACA;QACA;QACA;QAEA;;QAEA;QACA;UACA;UACAC;QACA;UACA;UACA;YACA;gBAAA;cAAA;cAAA;cAAAvH;cAAAC;YACA;gBAAA;cAAA;cAAA;cAAAC;cAAAC;YAEA;cACA;YACA;;YACA;UACA;;UAEAoH;QACA;;QAEA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;QACAC;UACA;QACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;QACA;QAAA;QACA;QACA;QAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA,4BACAC;MAAA,CACAA;MAAA,CACAA;MAAA,CACAC;;MAEA;MACA;QACAC;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGA;gBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACAnH,aACA,+BACA,sCACA,gCACA;cAAA;gBAEA;gBACA;kBACA;gBACA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IAEAoH;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;;MACA;IACA;IAEA;IACAC;MACA;MACA,yBACA,kCACA;MAEA7H;QACAV;QACAwI;QACAC;QACAC;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;MACA;MAEA;QACA;QACAjI;UACAkI;QACA;MACA;QACA;QACAlI;UACAkI;QACA;MACA;IACA;IAEAC;MACA;QACA;QACA;MACA;MAEA;QACA;QACA;QACA;QACAnI;UACAV;UACAwI;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACA;QACAhI;UACAkI;QACA;MACA;QACA;QACAlI;UACAkI;QACA;MACA;IACA;IAEAE;MACA;MACA;MAEA;QACApI;UACAV;UACAmB;QACA;QACA;MACA;MAEA;QACAT;UACAV;UACAwI;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACA;QACAhI;UACAkI;QACA;MACA;QACA;QACAlI;UACAkI;QACA;MACA;QACA;QACAlI;UACAkI;QACA;MACA;IACA;IAEA;IACAG;MACA;MACA;MAEArI;QACAkI;MACA;IACA;IAEA;IACAI;MACA;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACA;MACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;QACAzI;QACA;UAAA;YAAA;cAAA;gBAAA;kBAAA;kBAAA;kBAAA,OAEA;gBAAA;kBACA;kBACA;oBACA;kBACA;gBAAA;kBAAA;kBAEA;kBAAA;gBAAA;gBAAA;kBAAA;cAAA;YAAA;UAAA;QAAA,CAEA;MACA;IACA;IAEA;IACA0I;MACA;QACA;MACA;QACA;UACArC;UACAC;QACA;MACA;MACA;IACA;IAEA;IACAqC;MACA;MACA;MAEA;QACA;UACA;UACA;UACA;UACA;UACAC;UAEA;UACAC;UACAA;UAEA;YAAAxC;YAAAC;UAAA;QAEA;UACA;UACA;UACA;UACA;UACAwC;UAEA;UACAC;UACAA;UAEA;YAAA1C;YAAAC;UAAA;QAEA;UACA;UACA;UACA;UACA0C;UAEA;YAAA3C;YAAAC;UAAA;QAEA;UACA;UACA;MAAA;IAEA;IAEA;IACA2C;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;UAAA;QAAA;QACA;MACA;QACA;QACA;QACA;UACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACAtJ;MACA;QACA;QACA;UACA;YACA;YACA;YACA;;YAEA;YACA;YACA;YACA;;YAEA;YACA;YACAA;YACA;cAAA;gBAAA;kBAAA;oBAAA;sBAAA;sBAAA;sBAAA,OAEA;oBAAA;sBACA;sBACA;wBACA;sBACA;oBAAA;sBAAA;sBAEA;sBAAA;oBAAA;oBAAA;sBAAA;kBAAA;gBAAA;cAAA;YAAA,CAEA;YAEAF;cACAV;cACAmB;cACAC;YACA;UACA;QACA;UACA;UACA;YACA;YACA;YACA;UACA;YACA;YACA;;YAEA;YACA;cACA;cACA;cACA;YACA;;YAEA;YACA;YACA;YACA;;YAEA;YACA;YACAR;YACA;cAAA;gBAAA;kBAAA;oBAAA;sBAAA;sBAAA;sBAAA,OAEA;oBAAA;sBACA;sBACA;wBACA;sBACA;oBAAA;sBAAA;sBAEA;sBAAA;oBAAA;oBAAA;sBAAA;kBAAA;gBAAA;cAAA;YAAA,CAEA;YAEAF;cACAV;cACAmB;cACAC;YACA;UACA;QACA;MACA;IACA;IAEA;IACA+I;MACA;MACA;QACA;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACroEA;AAAA;AAAA;AAAA;AAAqqC,CAAgB,2oCAAG,EAAC,C;;;;;;;;;;;ACAzrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/my-areas.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/my-areas.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./my-areas.vue?vue&type=template&id=4c3d3ebe&scoped=true&\"\nvar renderjs\nimport script from \"./my-areas.vue?vue&type=script&lang=js&\"\nexport * from \"./my-areas.vue?vue&type=script&lang=js&\"\nimport style0 from \"./my-areas.vue?vue&type=style&index=0&id=4c3d3ebe&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4c3d3ebe\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/my-areas.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my-areas.vue?vue&type=template&id=4c3d3ebe&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniCalendar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-calendar/components/uni-calendar/uni-calendar\" */ \"@/uni_modules/uni-calendar/components/uni-calendar/uni-calendar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getStatusTitle()\n  var m1 = _vm.getCurrentTimeRange()\n  var g0 = !_vm.loading ? _vm.myFixedAreas.length : null\n  var l0 = !_vm.loading\n    ? _vm.__map(_vm.myFixedAreas, function (area, index) {\n        var $orig = _vm.__get_orig(area)\n        var m2 = _vm.getIconColor(area.status)\n        var m3 = _vm.getStatusText(area.status)\n        return {\n          $orig: $orig,\n          m2: m2,\n          m3: m3,\n        }\n      })\n    : null\n  var g1 = _vm.myPublicAreas.length > 0 || (!_vm.loading && _vm.dataLoaded)\n  var g2 = g1 && !_vm.loading ? _vm.myPublicAreas.length : null\n  var l1 =\n    g1 && !_vm.loading\n      ? _vm.__map(_vm.myPublicAreas, function (area, index) {\n          var $orig = _vm.__get_orig(area)\n          var m4 = _vm.getIconColor(area.status)\n          var m5 = _vm.getStatusText(area.status)\n          return {\n            $orig: $orig,\n            m4: m4,\n            m5: m5,\n          }\n        })\n      : null\n  var g3 = !_vm.loading ? _vm.groupedRectificationTasks.length : null\n  var l3 = !_vm.loading\n    ? _vm.__map(_vm.groupedRectificationTasks, function (group, __i0__) {\n        var $orig = _vm.__get_orig(group)\n        var g4 = group.tasks.length\n        var l2 = group.expanded\n          ? _vm.__map(group.tasks, function (task, index) {\n              var $orig = _vm.__get_orig(task)\n              var m6 = _vm.getIconColor(task.status)\n              var m7 = _vm.getStatusText(task.status)\n              return {\n                $orig: $orig,\n                m6: m6,\n                m7: m7,\n              }\n            })\n          : null\n        return {\n          $orig: $orig,\n          g4: g4,\n          l2: l2,\n        }\n      })\n    : null\n  var g5 = !_vm.loading ? _vm.cleaningRecords.list.length : null\n  var l4 = !_vm.loading\n    ? _vm.__map(_vm.cleaningRecords.list, function (record, index) {\n        var $orig = _vm.__get_orig(record)\n        var m8 = _vm.getIconColor(record.status)\n        var g6 = record.remark ? record.remark.substring(0, 15) : null\n        var g7 = record.remark ? record.remark.length : null\n        var m9 = _vm.getCleaningRecordStatusText(record.status)\n        return {\n          $orig: $orig,\n          m8: m8,\n          g6: g6,\n          g7: g7,\n          m9: m9,\n        }\n      })\n    : null\n  var m10 =\n    !(_vm.dateFilterMode === \"quick\") &&\n    _vm.customDateRange.startDate &&\n    _vm.customDateRange.endDate\n      ? _vm.formatSelectedDate(_vm.customDateRange.startDate)\n      : null\n  var m11 =\n    !(_vm.dateFilterMode === \"quick\") &&\n    _vm.customDateRange.startDate &&\n    _vm.customDateRange.endDate\n      ? _vm.formatSelectedDate(_vm.customDateRange.endDate)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        g2: g2,\n        l1: l1,\n        g3: g3,\n        l3: l3,\n        g5: g5,\n        l4: l4,\n        m10: m10,\n        m11: m11,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my-areas.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my-areas.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page-container\">\n    <!-- 本周清理状态 -->\n    <view class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"header-content\">\n          <view class=\"card-title\">{{ getStatusTitle() }}</view>\n          <view class=\"time-selector\" @click=\"showTimeSelector\">\n            <text class=\"time-text\">{{ getCurrentTimeRange() }}</text>\n            <uni-icons type=\"down\" size=\"12\" color=\"#007AFF\"></uni-icons>\n          </view>\n        </view>\n      </view>\n      <view class=\"card-body\">\n        <!-- 统计数据加载中状态 -->\n        <view v-if=\"loading\" class=\"stats-loading\">\n          <view class=\"loading-content\">\n            <view class=\"loading-spinner\"></view>\n            <text class=\"loading-text\">统计数据加载中...</text>\n          </view>\n        </view>\n        \n        <!-- 统计数据 -->\n        <view v-else class=\"stats-grid\">\n          <view class=\"stats-item\">\n            <view class=\"stats-number success\">{{ completedCount }}</view>\n            <view class=\"stats-label\">{{ selectedQuickFilter === 'week' ? '已完成' : '已完成整改' }}</view>\n          </view>\n          <view class=\"stats-item\">\n            <view class=\"stats-number warning\">{{ pendingCount }}</view>\n            <view class=\"stats-label\">{{ selectedQuickFilter === 'week' ? '待处理' : '待整改' }}</view>\n          </view>\n          <view class=\"stats-item\">\n            <view class=\"stats-number danger\">{{ overdueCount }}</view>\n            <view class=\"stats-label\">已逾期</view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 我的固定责任区 (每人一个) -->\n    <view class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"header-content\">\n          <view class=\"card-title\">固定责任区</view>\n          <view class=\"card-subtitle\">每人分配固定责任区进行清理</view>\n        </view>\n      </view>\n      \n      <!-- 加载中状态 -->\n      <view v-if=\"loading\" class=\"list-loading\">\n        <view class=\"loading-content\">\n          <view class=\"loading-spinner\"></view>\n          <text class=\"loading-text\">加载责任区数据中...</text>\n        </view>\n      </view>\n      \n      <template v-else>\n        <p-empty-state\n          v-if=\"myFixedAreas.length === 0\"\n          useIcon\n          iconName=\"info\"\n          iconColor=\"#8E8E93\"\n          text=\"暂未分配固定责任区\"\n          description=\"请联系管理员在责任区检查中进行分配\"\n        ></p-empty-state>\n        \n        <view v-for=\"(area, index) in myFixedAreas\" :key=\"index\" class=\"list-item\" @click=\"openAreaDetail(area)\">\n        <view class=\"list-item-icon\" :class=\"['icon-bg-' + area.status]\">\n          <uni-icons :type=\"area.icon\" size=\"18\" :color=\"getIconColor(area.status)\"></uni-icons>\n        </view>\n        <view class=\"list-item-content\">\n          <view class=\"list-item-title\">\n            {{ area.name }}\n            <text class=\"fixed-tag\">固定</text>\n          </view>\n          <view class=\"list-item-subtitle\">{{ area.subtitle }}</view>\n        </view>\n        <view class=\"list-item-right\">\n          <view class=\"status-badge\" :class=\"['status-' + area.status]\">{{ getStatusText(area.status) }}</view>\n          <uni-icons type=\"right\" size=\"14\" color=\"#C7C7CC\"></uni-icons>\n        </view>\n      </view>\n      </template>\n    </view>\n\n    <!-- 公共责任区 (所有员工共同维护) -->\n    <view class=\"card\" v-if=\"myPublicAreas.length > 0 || (!loading && dataLoaded)\">\n      <view class=\"card-header\">\n        <view class=\"header-content\">\n          <view class=\"card-title\">公共责任区</view>\n          <view class=\"card-subtitle\">每周轮班公共责任区进行清理</view>\n        </view>\n      </view>\n      \n      <!-- 加载中状态与固定责任区共享 -->\n      <view v-if=\"loading\" class=\"list-loading\">\n        <view class=\"loading-content\">\n          <view class=\"loading-spinner\"></view>\n          <text class=\"loading-text\">加载责任区数据中...</text>\n        </view>\n      </view>\n      \n      <template v-else>\n        <p-empty-state\n          v-if=\"myPublicAreas.length === 0\"\n          useIcon\n          iconName=\"home\"\n          iconColor=\"#8E8E93\"\n          text=\"暂无公共责任区\"\n          description=\"管理员可在责任区检查中添加公共责任区\"\n        ></p-empty-state>\n      \n      <view v-for=\"(area, index) in myPublicAreas\" :key=\"index\" class=\"list-item\" @click=\"openPublicAreaDetail(area)\">\n        <view class=\"list-item-icon\" :class=\"['icon-bg-' + area.status]\">\n          <uni-icons :type=\"area.icon\" size=\"18\" :color=\"getIconColor(area.status)\"></uni-icons>\n        </view>\n        <view class=\"list-item-content\">\n          <view class=\"list-item-title\">\n            {{ area.name }}\n            <text class=\"public-tag\">公共</text>\n          </view>\n          <view class=\"list-item-subtitle\">{{ area.subtitle }}</view>\n        </view>\n        <view class=\"list-item-right\">\n          <view class=\"status-badge\" :class=\"['status-' + area.status]\">{{ getStatusText(area.status) }}</view>\n          <uni-icons type=\"right\" size=\"14\" color=\"#C7C7CC\"></uni-icons>\n        </view>\n      </view>\n      </template>\n    </view>\n\n    <!-- 我的整改记录 -->\n    <view class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"header-content\">\n          <view class=\"card-title\">我的整改记录</view>\n          <view class=\"card-subtitle\">我的责任区整改记录</view>\n        </view>\n      </view>\n      \n      <!-- 加载中状态 -->\n      <view v-if=\"loading\" class=\"list-loading\">\n        <view class=\"loading-content\">\n          <view class=\"loading-spinner\"></view>\n          <text class=\"loading-text\">加载整改记录中...</text>\n        </view>\n      </view>\n      \n      <template v-else>\n        <p-empty-state\n          v-if=\"groupedRectificationTasks.length === 0\"\n          useIcon\n          iconName=\"checkmarkempty\"\n          iconColor=\"#34C759\"\n          text=\"暂无整改任务\"\n          description=\"当责任区检查发现问题时，会在此显示需要整改的任务\"\n        ></p-empty-state>\n        \n        <!-- 时间分组的整改记录列表 -->\n        <view v-for=\"group in groupedRectificationTasks\" :key=\"group.weekKey\" class=\"time-group\">\n        <!-- 时间分组标题 -->\n        <view class=\"time-group-header\" @click=\"toggleRectificationWeek(group.weekKey)\">\n          <view class=\"time-group-title\">\n            <uni-icons :type=\"group.expanded ? 'down' : 'right'\" size=\"16\" color=\"#007AFF\"></uni-icons>\n            <text class=\"time-title\">{{ group.title }}</text>\n            <view class=\"time-count\">{{ group.tasks.length }}条</view>\n          </view>\n        </view>\n        \n        <!-- 分组任务列表 -->\n        <view v-if=\"group.expanded\" class=\"time-group-content\">\n          <view v-for=\"(task, index) in group.tasks\" :key=\"index\" class=\"list-item\" @click=\"openRectificationDetail(task)\">\n            <view class=\"list-item-icon\" :class=\"['icon-bg-' + task.status]\">\n              <uni-icons :type=\"task.icon\" size=\"18\" :color=\"getIconColor(task.status)\"></uni-icons>\n            </view>\n            <view class=\"list-item-content\">\n              <view class=\"list-item-title\">\n                {{ task.areaName }}\n                <text v-if=\"task.areaType === 'public'\" class=\"public-tag\">公共</text>\n                <text v-else class=\"fixed-tag\">固定</text>\n              </view>\n              <view class=\"list-item-subtitle\">{{ task.subtitle }}</view>\n              <view class=\"task-time-info\">\n                <text class=\"time-label\">发现时间：</text>\n                <text class=\"time-value\">{{ task.issueFoundDate }}</text>\n                <text v-if=\"task.completedDate\" class=\"time-label\"> · 完成时间：</text>\n                <text v-if=\"task.completedDate\" class=\"time-value\">{{ task.completedDate }}</text>\n              </view>\n            </view>\n            <view class=\"list-item-right\">\n              <view class=\"status-badge\" :class=\"['status-' + task.status]\">{{ getStatusText(task.status) }}</view>\n              <uni-icons type=\"right\" size=\"14\" color=\"#C7C7CC\"></uni-icons>\n            </view>\n          </view>\n        </view>\n      </view>\n      </template>\n    </view>\n\n    <!-- 我的清理记录 -->\n    <view class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"header-content\">\n          <view class=\"card-title\">我的清理记录</view>\n          <view class=\"card-subtitle\">最近的清理工作记录</view>\n        </view>\n      </view>\n      \n      <!-- 加载中状态 -->\n      <view v-if=\"loading\" class=\"loading-container\">\n        <view class=\"loading-content\">\n          <view class=\"loading-spinner\"></view>\n          <text class=\"loading-text\">加载清理记录中...</text>\n        </view>\n      </view>\n      \n      <template v-else>\n        <p-empty-state\n          v-if=\"cleaningRecords.list.length === 0\"\n          useIcon\n          iconName=\"checkmarkempty\"\n          iconColor=\"#34C759\"\n          text=\"暂无清理记录\"\n          description=\"完成清理任务后，记录会在此显示\"\n        ></p-empty-state>\n        \n        <view v-for=\"(record, index) in cleaningRecords.list\" :key=\"index\" class=\"list-item\" @click=\"openCleaningRecordDetail(record)\">\n          <view class=\"list-item-icon\" :class=\"['icon-bg-' + record.status]\">\n            <uni-icons :type=\"record.icon\" size=\"18\" :color=\"getIconColor(record.status)\"></uni-icons>\n          </view>\n          <view class=\"list-item-content\">\n            <view class=\"list-item-title\">\n              {{ record.areaName }}\n              <text v-if=\"record.areaType === 'public'\" class=\"public-tag\">公共</text>\n              <text v-else class=\"fixed-tag\">固定</text>\n            </view>\n            <view class=\"list-item-subtitle\">{{ record.formattedDate }}</view>\n            <view class=\"cleaning-record-meta\">\n              <text class=\"meta-label\">照片：</text>\n              <text class=\"meta-value\">{{ record.photos }}张</text>\n              <text v-if=\"record.remark\" class=\"meta-label\"> · 备注：</text>\n              <text v-if=\"record.remark\" class=\"meta-value meta-remark\">{{ record.remark.substring(0, 15) }}{{ record.remark.length > 15 ? '...' : '' }}</text>\n            </view>\n          </view>\n          <view class=\"list-item-right\">\n            <view class=\"status-badge\" :class=\"['status-' + record.status]\">{{ getCleaningRecordStatusText(record.status) }}</view>\n            <uni-icons type=\"right\" size=\"14\" color=\"#C7C7CC\"></uni-icons>\n          </view>\n        </view>\n        \n        <!-- 分页加载更多按钮 -->\n        <view v-if=\"cleaningRecords.hasMore\" class=\"load-more-section\">\n          <button \n            class=\"load-more-btn\" \n            :disabled=\"cleaningRecords.loading\"\n            @click=\"loadMoreCleaningRecords\"\n          >\n            <view \n              v-if=\"cleaningRecords.loading\" \n              class=\"btn-loading-spinner\"\n            ></view>\n            <text>{{ cleaningRecords.loading ? '加载中...' : '加载更多' }}</text>\n          </button>\n        </view>\n      </template>\n    </view>\n    \n    <!-- 底部安全间距 -->\n    <view class=\"bottom-safe-area\"></view>\n\n    <!-- 日历式时间选择弹窗 -->\n    <uni-popup ref=\"timePopup\" type=\"bottom\" border-radius=\"16rpx 16rpx 0 0\">\n      <view class=\"date-picker-popup\">\n        <view class=\"popup-header\">\n          <text class=\"popup-title\">筛选时间</text>\n          <view class=\"popup-close\" @click=\"closeDatePicker\">\n            <uni-icons type=\"close\" size=\"18\" color=\"#8E8E93\"></uni-icons>\n          </view>\n        </view>\n\n        <!-- 快捷选择模式 -->\n        <view v-if=\"dateFilterMode === 'quick'\" class=\"quick-date-section\">\n          <view class=\"section-title\">快捷选择</view>\n          <view class=\"quick-options\">\n            <view v-for=\"option in quickDateOptions\" :key=\"option.value\" class=\"quick-option\"\n              :class=\"{ active: selectedQuickFilter === option.value }\"\n              @click=\"selectQuickDateOption(option)\">\n              <text class=\"quick-text\">{{ option.label }}</text>\n              </view>\n            </view>\n            </view>\n\n        <!-- 日期范围选择模式 -->\n        <view v-else class=\"range-date-section\">\n          <!-- 简洁的日历选择器 -->\n          <view class=\"calendar-section\">\n            <view class=\"calendar-header\">\n              <text class=\"calendar-tip\">在日历上点击选择开始和结束日期</text>\n          </view>\n\n            <!-- uni-calendar 日历组件 -->\n            <uni-calendar ref=\"calendar\" :range=\"true\" :date=\"calendarDate\" :start-date=\"calendarStartDate\"\n              :end-date=\"calendarEndDate\" @change=\"onCalendarChange\"\n              @monthSwitch=\"onMonthSwitch\"></uni-calendar>\n\n            <!-- 已选择的日期范围显示 -->\n            <view v-if=\"customDateRange.startDate && customDateRange.endDate\" class=\"selected-range\">\n              <view class=\"range-item\">\n                <text class=\"range-label\">开始日期</text>\n                <text class=\"range-value\">{{ formatSelectedDate(customDateRange.startDate) }}</text>\n              </view>\n              <view class=\"range-separator\">→</view>\n              <view class=\"range-item\">\n                <text class=\"range-label\">结束日期</text>\n                <text class=\"range-value\">{{ formatSelectedDate(customDateRange.endDate) }}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\nimport { callCloudFunction } from '@/utils/auth.js';\n\nexport default {\n  name: 'MyAreas',\n  data() {\n    return {\n      // 日期筛选方式：'quick' 快捷选择, 'range' 日期范围选择\n      dateFilterMode: 'quick',\n      selectedTimeFilter: 'week',\n\n      // 自定义日期范围\n      customDateRange: {\n        startDate: '',\n        endDate: ''\n      },\n\n      // 日历组件相关数据\n      calendarDate: '',\n      calendarStartDate: '',\n      calendarEndDate: '',\n\n      // 防重复查询的定时器\n      refreshTimer: null,\n      calendarChangeTimer: null,\n\n      // 快捷日期选项\n      quickDateOptions: [\n        { label: '本周', value: 'week' },\n        { label: '上周', value: 'last_week' },\n        { label: '本月', value: 'month' },\n        { label: '自定义', value: 'custom' }\n      ],\n      selectedQuickFilter: 'week',\n      \n      // 我的责任区（从API加载）\n      myAreas: [],\n      \n      // 整改任务记录（从API加载）\n      rectificationTasks: [],\n      \n      // 我的清理记录（分页结构）\n      cleaningRecords: {\n        list: [],           // 当前显示的记录列表\n        pageSize: 10,       // 每页显示数量\n        hasMore: true,      // 是否还有更多数据\n        loading: false      // 加载状态\n      },\n      allCleaningRecords: [], // 所有清理记录（用于分页）\n      \n      // 加载状态\n      loading: false,\n      dataLoaded: false, // 标记数据是否已加载\n      needsRefresh: false, // 标记是否需要刷新数据\n      \n      // 展开的整改记录周次（动态设置，默认展开最新的）\n      expandedRectificationWeeks: [],\n      \n      // 时间计算缓存（避免重复计算）\n      currentWeekCache: null,\n      \n      // 区域分类缓存（避免重复过滤）\n      areasCache: null\n    }\n  },\n  computed: {\n    // 固定责任区（永远显示本周状态，不受时间筛选影响）\n    myFixedAreas() {\n      // 使用缓存避免重复计算\n      if (!this.areasCache || this.areasCache.sourceLength !== this.myAreas.length) {\n        this.areasCache = {\n          sourceLength: this.myAreas.length,\n          fixed: this.myAreas.filter(area => area.type === 'fixed'),\n          public: this.myAreas.filter(area => area.type === 'public')\n        };\n      }\n      return this.areasCache.fixed;\n    },\n    // 公共责任区（永远显示本周状态，不受时间筛选影响）\n    myPublicAreas() {\n      // 使用缓存避免重复计算\n      if (!this.areasCache || this.areasCache.sourceLength !== this.myAreas.length) {\n        this.areasCache = {\n          sourceLength: this.myAreas.length,\n          fixed: this.myAreas.filter(area => area.type === 'fixed'),\n          public: this.myAreas.filter(area => area.type === 'public')\n        };\n      }\n      return this.areasCache.public;\n    },\n    \n              completedCount() {\n        try {\n          // 如果选择的是本周，统计本周已完成的责任区 + 本周已完成的整改记录\n          if (this.selectedQuickFilter === 'week') {\n            const completedAreas = this.myAreas.filter(area => area.status === 'completed').length || 0;\n            const completedRectifications = this.getThisWeekRectificationTasks().filter(task => \n              task.status === 'completed' || task.status === 'verified'\n            ).length || 0;\n            return completedAreas + completedRectifications;\n          } else {\n            // 历史时间范围：只统计该时间范围内的整改记录（责任区状态是当前状态，不应该按历史统计）\n            const count = this.filteredRectificationTasks.filter(task => \n              task.status === 'completed' || task.status === 'verified'\n            ).length || 0;\n            return count;\n          }\n        } catch (error) {\n          console.error('计算已完成数量出错:', error);\n          return 0;\n        }\n      },\n    pendingCount() {\n      try {\n        // 如果选择的是本周，统计本周真正需要员工处理的任务\n        if (this.selectedQuickFilter === 'week') {\n          // 责任区：只统计真正可以清理的（排除\"未到时间\"的公共责任区）\n          const pendingAreas = this.myAreas.filter(area => \n            area.status === 'pending' // 只统计真正待清理的，不包括scheduled(未到时间)\n          ).length || 0;\n          // 整改记录：只统计真正需要员工操作的（排除\"待复查\"）\n          const pendingRectifications = this.getThisWeekRectificationTasks().filter(task => \n            task.status === 'pending_rectification' // 只统计待整改，不包括pending_review(待复查)\n          ).length || 0;\n          return pendingAreas + pendingRectifications;\n        } else {\n          // 历史时间范围：只统计该时间范围内需要员工操作的整改记录\n          const count = this.filteredRectificationTasks.filter(task => \n            task.status === 'pending_rectification' // 只统计待整改\n          ).length || 0;\n          return count;\n        }\n      } catch (error) {\n        console.error('计算待处理数量出错:', error);\n        return 0;\n      }\n    },\n    overdueCount() {\n      try {\n        // 如果选择的是本周，统计本周已逾期的责任区 + 逾期整改记录\n        if (this.selectedQuickFilter === 'week') {\n          const overdueAreas = this.myAreas.filter(area => area.status === 'overdue').length || 0;\n          // 整改记录逾期：使用更精确的逾期判断逻辑\n          const overdueRectifications = this.getThisWeekRectificationTasks().filter(task => \n            task.status === 'overdue' || this.isRectificationOverdue(task)\n          ).length || 0;\n          return overdueAreas + overdueRectifications;\n        } else {\n          // 历史时间范围：只统计该时间范围内的逾期整改记录\n          const count = this.filteredRectificationTasks.filter(task => \n            task.status === 'overdue' || this.isRectificationOverdue(task)\n          ).length || 0;\n          return count;\n        }\n      } catch (error) {\n        console.error('计算逾期数量出错:', error);\n        return 0;\n      }\n    },\n    // 根据时间筛选的整改任务记录\n    filteredRectificationTasks() {\n      let tasks = this.rectificationTasks;\n      \n      // 基于日期范围的直接筛选\n      const dateRange = this.getCurrentDateRange();\n      if (dateRange) {\n        tasks = tasks.filter(task => {\n          if (!task.created_at) return false;\n          const taskDate = new Date(task.created_at);\n          return taskDate >= dateRange.start && taskDate <= dateRange.end;\n        });\n      }\n      \n      return tasks;\n    },\n    \n    // 时间分组的整改任务记录\n    groupedRectificationTasks() {\n      const tasks = this.filteredRectificationTasks;\n      const grouped = {};\n      \n      tasks.forEach(task => {\n        // 重新计算周键以确保使用最新的时区逻辑\n        // 使用原始的created_at而不是格式化后的issueFoundDate\n        const weekKey = this.getWeekKey(task.created_at);\n        if (!grouped[weekKey]) {\n          grouped[weekKey] = {\n            weekKey,\n            title: this.getWeekTitle(weekKey),\n            tasks: [],\n            expanded: this.expandedRectificationWeeks.includes(weekKey)\n          };\n        }\n        grouped[weekKey].tasks.push(task);\n      });\n      \n      // 按周次降序排序\n      const sortedGroups = Object.values(grouped).sort((a, b) => {\n        // 更精确的排序：先按年份，再按周数\n        const [yearA, weekA] = a.weekKey.split('-W').map(x => parseInt(x));\n        const [yearB, weekB] = b.weekKey.split('-W').map(x => parseInt(x));\n        \n        if (yearA !== yearB) {\n          return yearB - yearA; // 年份降序\n        }\n        return weekB - weekA; // 同年内周数降序\n      });\n      \n      // 同步展开状态：完全基于用户手动操作的状态\n      sortedGroups.forEach(group => {\n        group.expanded = this.expandedRectificationWeeks.includes(group.weekKey);\n      });\n      \n\n      \n      return sortedGroups;\n    }\n  },\n  created() {\n  },\n  onLoad() {\n    this.loadMyAreas();\n    \n    // 监听清理记录更新事件\n    uni.$on('cleaningRecordUpdated', this.handleRecordUpdated);\n    // 监听整改记录更新事件\n    uni.$on('rectificationRecordUpdated', this.handleRecordUpdated);\n  },\n  onUnload() {\n    // 移除事件监听\n    uni.$off('cleaningRecordUpdated', this.handleRecordUpdated);\n    uni.$off('rectificationRecordUpdated', this.handleRecordUpdated);\n    \n    // 清理定时器\n    if (this.refreshTimer) {\n      clearTimeout(this.refreshTimer);\n    }\n    if (this.calendarChangeTimer) {\n      clearTimeout(this.calendarChangeTimer);\n    }\n  },\n  onShow() {\n    // 页面重新显示时，只在确实需要刷新数据的情况下才刷新\n    // 使用静默刷新，避免显示加载动画\n    if (this.dataLoaded && !this.loading && this.needsRefresh) {\n      this.silentRefreshData();\n      this.needsRefresh = false; // 重置刷新标记\n    }\n  },\n  methods: {\n\n    \n    // 获取本周的整改任务\n    getThisWeekRectificationTasks() {\n      try {\n        const now = new Date();\n        const beijingNow = this.toBeijingTime(now);\n        \n        if (!beijingNow) {\n          console.error('无法获取北京时间');\n          return [];\n        }\n        \n        const weekStart = this.getWeekStart(beijingNow);\n        const weekEnd = this.getWeekEnd(beijingNow);\n        \n        if (!weekStart || !weekEnd) {\n          console.error('无法计算本周时间范围');\n          return [];\n        }\n        \n        if (!Array.isArray(this.rectificationTasks)) {\n          return [];\n        }\n        \n        return this.rectificationTasks.filter(task => {\n          if (!task || !task.created_at) return false;\n          try {\n            const taskDate = new Date(task.created_at);\n            if (isNaN(taskDate.getTime())) return false;\n            return taskDate >= weekStart && taskDate <= weekEnd;\n          } catch (error) {\n            console.error('处理任务日期出错:', error, task);\n            return false;\n          }\n        });\n      } catch (error) {\n        console.error('获取本周整改任务出错:', error);\n        return [];\n      }\n    },\n\n    // 判断整改任务是否逾期（严格按周计算）\n    isRectificationOverdue(task) {\n      if (!task || task.status === 'completed' || task.status === 'verified') {\n        return false; // 已完成的任务不算逾期\n      }\n      \n      if (!task.created_at) {\n        return false;\n      }\n      \n      // 只有待整改和待分配的任务才能算逾期\n      if (task.status !== 'pending_rectification' && task.status !== 'pending_assignment') {\n        return false;\n      }\n      \n      const now = new Date();\n      const beijingNow = this.toBeijingTime(now);\n      \n      // 获取任务创建时间所在周的结束时间（周日23:59:59）\n      const taskCreatedTime = new Date(task.created_at);\n      const beijingCreatedTime = this.toBeijingTime(taskCreatedTime);\n      const taskWeekEnd = this.getWeekEnd(beijingCreatedTime);\n      \n      // 如果当前时间超过了任务创建周的周日23:59:59，则为逾期\n      return beijingNow.getTime() > taskWeekEnd.getTime();\n    },\n\n    // 加载用户责任区数据\n    async loadMyAreas() {\n      this.loading = true;\n      try {\n        // 并行加载所有数据，等待全部完成后统一显示\n        await Promise.all([\n          this.loadUserAssignments(),\n          this.loadUserRectificationTasks(),\n          this.loadMyCleaningRecords()\n        ]);\n        \n      } catch (error) {\n        uni.showToast({\n          title: '加载数据失败',\n          icon: 'none',\n          duration: 2000\n        });\n        // 设置为空数组以显示空状态\n        this.myAreas = [];\n        this.rectificationTasks = [];\n        this.cleaningRecords.list = [];\n      } finally {\n        this.loading = false;\n        this.dataLoaded = true; // 标记数据已加载\n      }\n    },\n\n    // 加载用户的责任区分配\n    async loadUserAssignments() {\n      try {\n        // 调用云函数获取当前用户的责任区分配\n        const result = await callCloudFunction('hygiene-assignments', {\n          action: 'getUserAssignments',\n          data: {}\n        });\n\n        if (result && result.success) {\n          const assignments = result.data || [];\n          \n          // 将分配记录转换为页面需要的格式\n          this.myAreas = [];\n          \n          // 收集所有责任区ID，用于批量查询清理记录\n          const areaIds = [];\n          const areaDataMap = new Map();\n          \n          assignments.forEach((assignment) => {\n            if (assignment.areas_info && Array.isArray(assignment.areas_info)) {\n              assignment.areas_info.forEach((area) => {\n                const areaId = area._id || area.id;\n                areaIds.push(areaId);\n                \n                // 先创建基础数据结构\n                const areaData = {\n                  id: areaId,\n                  name: area.name,\n                  icon: area.type === 'fixed' ? 'gear' : 'videocam',\n                  type: area.type || 'fixed',\n                  weeklyRequired: area.type === 'fixed',\n                  location: area.location,\n                  description: area.description,\n                  last_clean_date: null // 将通过清理记录查询获得真实值\n                };\n                \n                // 如果是公共责任区，添加排班信息\n                if (area.type === 'public' && area.scheduled_day !== null) {\n                  areaData.scheduledDay = area.scheduled_day;\n                  areaData.scheduled_day = area.scheduled_day;\n                }\n                \n                areaDataMap.set(areaId, areaData);\n              });\n            }\n          });\n          \n          // 查询这些责任区的最新清理记录\n          if (areaIds.length > 0) {\n            await this.loadLatestCleaningRecords(areaIds, areaDataMap);\n          }\n          \n          // 转换为最终格式\n          this.myAreas = Array.from(areaDataMap.values()).map(area => {\n            const status = this.calculateAreaStatus(area);\n            return {\n              ...area,\n              subtitle: this.generateAreaSubtitle(area),\n              status: status,\n              icon: this.getAreaIcon(status),\n              lastCleaningDate: area.last_clean_date\n            };\n          });\n          \n          // 清理缓存，确保计算属性重新计算\n          this.areasCache = null;\n        } else {\n          this.myAreas = [];\n        }\n      } catch (error) {\n        this.myAreas = [];\n      }\n    },\n\n    // 加载责任区的最新清理记录\n    async loadLatestCleaningRecords(areaIds, areaDataMap) {\n      try {\n        // 获取本周时间范围（缓存计算结果）\n        if (!this.currentWeekCache) {\n          const now = new Date();\n          this.currentWeekCache = {\n            now,\n            weekStart: this.getWeekStart(now),\n            weekEnd: this.getWeekEnd(now)\n          };\n        }\n        const { weekStart, weekEnd } = this.currentWeekCache;\n        \n        // 批量查询所有责任区的清理记录（一次API调用替代多次）\n        try {\n          const result = await callCloudFunction('hygiene-cleaning', {\n            action: 'getBatchCleaningRecords',\n            data: {\n              area_ids: areaIds,\n              start_date: weekStart.toISOString(),\n              end_date: weekEnd.toISOString(),\n              latest_only: true // 只返回每个区域的最新记录\n            }\n          });\n\n          if (result && result.success && result.data) {\n            // 处理批量返回的记录\n            result.data.forEach(record => {\n              if (areaDataMap.has(record.area_id)) {\n                areaDataMap.get(record.area_id).last_clean_date = record.cleaning_date;\n              }\n            });\n          }\n        } catch (error) {\n          // 如果批量查询失败，回退到单独查询\n\n          const promises = areaIds.map(async (areaId) => {\n            try {\n              const result = await callCloudFunction('hygiene-cleaning', {\n                action: 'getCleaningRecords',\n                data: {\n                  area_id: areaId,\n                  start_date: weekStart.toISOString(),\n                  end_date: weekEnd.toISOString(),\n                  pageSize: 1 // 只需要最新的1条记录\n                }\n              });\n\n              if (result && result.success && result.data && result.data.list && result.data.list.length > 0) {\n                const latestRecord = result.data.list[0];\n                if (areaDataMap.has(areaId)) {\n                  areaDataMap.get(areaId).last_clean_date = latestRecord.cleaning_date;\n                }\n              }\n            } catch (error) {\n              // 静默处理单个责任区查询失败\n            }\n          });\n          await Promise.all(promises);\n        }\n        \n      } catch (error) {\n        // 即使加载失败也不影响主流程，只是状态可能不准确\n      }\n    },\n\n    // 加载用户的清理记录\n    async loadMyCleaningRecords() {\n      try {\n        // 设置加载状态\n        this.cleaningRecords.loading = true;\n        \n        // 构建请求参数，包含日期范围筛选\n        const requestData = {\n          pageSize: 20 // 显示最近20条记录\n        };\n        \n        // 如果有日期范围筛选，添加到请求参数中\n        const dateRange = this.getCurrentDateRange();\n        if (dateRange) {\n          requestData.start_date = dateRange.start.toISOString();\n          requestData.end_date = dateRange.end.toISOString();\n        }\n        \n        // 调用云函数获取当前用户的清理记录\n        const result = await callCloudFunction('hygiene-cleaning', {\n          action: 'getMyCleaningRecords',\n          data: requestData\n        });\n\n        if (result && result.success) {\n          const records = result.data || [];\n          \n          // 将清理记录转换为页面需要的格式\n          const formattedRecords = records.map(record => {\n            return {\n              id: record._id || record.id,\n              areaId: record.area_id,\n              areaName: record.area_name || '未知责任区',\n              areaType: record.area_type || 'fixed',\n              cleaningDate: record.cleaning_date,\n              formattedDate: this.formatDateTime(record.cleaning_date),\n              photos: record.photos ? record.photos.length : 0,\n              remark: record.remark || '',\n              status: 'completed', // 所有记录都是已完成\n              icon: 'checkmarkempty',\n              week: this.getWeekKey(record.cleaning_date)\n            };\n          });\n          \n          // 保存所有记录用于分页\n          this.allCleaningRecords = formattedRecords;\n          \n          // 设置首页数据\n          this.cleaningRecords.list = formattedRecords.slice(0, this.cleaningRecords.pageSize);\n          this.cleaningRecords.hasMore = records.length > this.cleaningRecords.pageSize;\n          \n          // 按清理时间降序排序\n          this.cleaningRecords.list.sort((a, b) => \n            new Date(b.cleaningDate) - new Date(a.cleaningDate)\n          );\n        } else {\n          this.cleaningRecords.list = [];\n          this.cleaningRecords.hasMore = false;\n        }\n      } catch (error) {\n        this.cleaningRecords.list = [];\n        this.cleaningRecords.hasMore = false;\n      } finally {\n        // 重置加载状态\n        this.cleaningRecords.loading = false;\n      }\n    },\n\n    // 加载更多清理记录（从本地缓存的数据中分页显示）\n    async loadMoreCleaningRecords() {\n      if (this.cleaningRecords.loading || !this.cleaningRecords.hasMore) {\n        return;\n      }\n      \n      this.cleaningRecords.loading = true;\n      \n      const currentDisplayed = this.cleaningRecords.list.length;\n      const nextPageSize = this.cleaningRecords.pageSize;\n      const nextPageEnd = currentDisplayed + nextPageSize;\n      \n      // 从所有记录中获取下一页数据\n      if (this.allCleaningRecords && this.allCleaningRecords.length > currentDisplayed) {\n        const nextPageData = this.allCleaningRecords.slice(currentDisplayed, nextPageEnd);\n        \n        // 追加新数据\n        this.cleaningRecords.list = [...this.cleaningRecords.list, ...nextPageData];\n        \n        // 检查是否还有更多数据\n        this.cleaningRecords.hasMore = this.cleaningRecords.list.length < this.allCleaningRecords.length;\n      } else {\n        this.cleaningRecords.hasMore = false;\n      }\n      \n      this.cleaningRecords.loading = false;\n    },\n\n    // 加载用户的整改任务记录\n    async loadUserRectificationTasks() {\n      try {\n        // 构建请求参数，包含日期范围筛选\n        const requestData = {};\n        \n        // 如果有日期范围筛选，添加到请求参数中\n        const dateRange = this.getCurrentDateRange();\n        if (dateRange) {\n          requestData.start_date = dateRange.start.toISOString();\n          requestData.end_date = dateRange.end.toISOString();\n        }\n        \n        // 调用云函数获取当前用户的整改记录\n        const result = await callCloudFunction('hygiene-rectification', {\n          action: 'getMyRectifications',\n          data: requestData\n        });\n\n        if (result && result.success) {\n          const tasks = result.data || [];\n          \n          // 将整改记录转换为页面需要的格式\n          this.rectificationTasks = tasks.map(task => {\n            const weekKey = this.getWeekKey(task.created_at);\n            \n            // 计算实际状态（考虑时间逾期）\n            const actualStatus = this.calculateRectificationStatus(task);\n            \n            // 确保有有效的ID（支持多种可能的ID字段）\n            const taskId = task._id || task.id || task.issue_id || task.task_id;\n            if (!taskId) {\n              return null; // 跳过无效的任务数据\n            }\n            \n            return {\n              id: taskId,\n              areaId: task.area_id,\n              areaName: task.area_name,\n              subtitle: this.formatDescription(task.issue_description || task.description),\n              status: actualStatus,\n              originalStatus: task.status, // 保留数据库原始状态\n              icon: this.getTaskIcon(actualStatus),\n              areaType: task.area_type || 'fixed',\n              week: weekKey,\n              issueFoundDate: this.formatDate(task.created_at),\n              completedDate: task.completed_at ? this.formatDate(task.completed_at) : null,\n              assignee_id: task.assignee_id,\n              assignee_name: task.assignee_name,\n              created_at: task.created_at, // 保留原始创建时间用于筛选\n              deadline: task.deadline // 保留截止时间用于逾期判断\n            };\n          }).filter(task => task && task.id); // 过滤掉null和没有ID的任务\n          \n          // 设置默认展开最新的周\n          this.setDefaultExpandedWeek();\n        } else {\n          this.rectificationTasks = [];\n        }\n      } catch (error) {\n        this.rectificationTasks = [];\n      }\n    },\n\n    // 生成责任区副标题\n    generateAreaSubtitle(area) {\n      if (area.type === 'fixed') {\n        // 固定责任区：显示本周状态\n        if (area.last_clean_date) {\n          const now = new Date();\n          const weekStart = this.getWeekStart(now);\n          const weekEnd = this.getWeekEnd(now);\n          const cleanDate = new Date(area.last_clean_date);\n          \n          // 检查清理日期是否在本周内\n          if (cleanDate >= weekStart && cleanDate <= weekEnd) {\n            const formattedDate = this.formatDate(area.last_clean_date);\n            return `本周已清理 · ${formattedDate}`;\n          } else {\n            return '本周待清理 · 每周完成一次';\n          }\n        } else {\n          return '本周待清理 · 每周完成一次';\n        }\n      } else if (area.type === 'public') {\n        // 公共责任区：根据排班和状态显示\n        if (area.scheduled_day !== null) {\n          if (area.last_clean_date) {\n            const cleanDate = this.formatDate(area.last_clean_date);\n            return `本周已清理 · ${cleanDate}`;\n          } else {\n            const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\n            const scheduledDay = area.scheduled_day === 0 ? 0 : area.scheduled_day;\n            \n            // 根据状态显示不同的副标题\n            if (area.status === 'scheduled') {\n              return `计划${weekDays[scheduledDay]}清理 · 未到时间`;\n            } else if (area.status === 'pending') {\n              return `计划${weekDays[scheduledDay]}清理 · 可以清理`;\n            } else if (area.status === 'overdue') {\n              return `计划${weekDays[scheduledDay]}清理 · 已逾期`;\n            } else {\n              return `计划${weekDays[scheduledDay]}清理`;\n            }\n          }\n        } else {\n          return '未设置清理日程';\n        }\n      }\n      return '待处理';\n    },\n\n    // 计算责任区状态\n    calculateAreaStatus(area) {\n      // 使用缓存的时间计算结果\n      if (!this.currentWeekCache) {\n        const now = new Date();\n        this.currentWeekCache = {\n          now,\n          weekStart: this.getWeekStart(now),\n          weekEnd: this.getWeekEnd(now)\n        };\n      }\n      const { now, weekStart, weekEnd } = this.currentWeekCache;\n      \n      if (area.type === 'fixed') {\n        // 固定责任区：每周循环，从周一到周日\n        if (area.last_clean_date) {\n          const cleanDate = new Date(area.last_clean_date);\n          if (cleanDate >= weekStart && cleanDate <= weekEnd) {\n            return 'completed';\n          }\n        }\n        \n        // 固定责任区逾期逻辑：只有过了本周且本周没有清理才算逾期\n        // 本周内（周一到周日）都应该显示\"待清理\"，不算逾期\n        // 逾期判断：已经是下周了，但上周没有完成清理\n        const nextWeekStart = new Date(weekEnd);\n        nextWeekStart.setDate(nextWeekStart.getDate() + 1); // 下周一\n        \n        if (now >= nextWeekStart) { // 已经到了下周\n          // 检查上周（当前计算的这一周）是否有清理记录\n          if (!area.last_clean_date) {\n            return 'overdue'; // 没有任何清理记录\n          }\n          \n          const cleanDate = new Date(area.last_clean_date);\n          if (!(cleanDate >= weekStart && cleanDate <= weekEnd)) {\n            return 'overdue'; // 上周没有清理记录\n          }\n        }\n        \n        // 默认为待清理状态\n        return 'pending';\n      } else if (area.type === 'public') {\n        // 公共责任区：根据排班计算状态\n        if (area.scheduled_day !== null) {\n          if (area.last_clean_date) {\n            const cleanDate = new Date(area.last_clean_date);\n            if (cleanDate >= weekStart && cleanDate <= weekEnd) {\n              return 'completed';\n            }\n          }\n          \n          // 公共责任区状态逻辑：严格按排班日的0点到23:59分\n          const beijingNow = this.toBeijingTime(now);\n          const scheduledDay = area.scheduled_day === 0 ? 0 : area.scheduled_day; // 0=周日, 1=周一, ..., 6=周六\n          \n          // 计算本周排班日的开始和结束时间\n          const beijingWeekStart = this.getWeekStart(beijingNow);\n          const scheduledDate = new Date(beijingWeekStart);\n          \n          if (scheduledDay === 0) {\n            // 周日：本周的最后一天\n            scheduledDate.setDate(beijingWeekStart.getDate() + 6);\n          } else {\n            // 周一到周六：相应调整天数\n            scheduledDate.setDate(beijingWeekStart.getDate() + scheduledDay - 1);\n          }\n          \n          const scheduledStart = new Date(scheduledDate);\n          scheduledStart.setHours(0, 0, 0, 0); // 排班日的0点\n          \n          const scheduledEnd = new Date(scheduledDate);\n          scheduledEnd.setHours(23, 59, 59, 999); // 排班日的23:59:59\n          \n          const currentTime = beijingNow.getTime();\n          \n          // 如果当前时间在排班日内（0点到23:59分），可以清理\n          if (currentTime >= scheduledStart.getTime() && currentTime <= scheduledEnd.getTime()) {\n            return 'pending';\n          }\n          // 如果当前时间超过了排班日的23:59分，则逾期\n          else if (currentTime > scheduledEnd.getTime()) {\n            return 'overdue';\n          }\n          // 如果当前时间还未到排班日的0点，显示为\"未到时间\"\n          else {\n            return 'scheduled';\n          }\n        }\n        return 'pending';\n      }\n      return 'pending';\n    },\n\n    // 获取任务图标\n    getTaskIcon(status) {\n      const iconMap = {\n        'completed': 'checkmarkempty',\n        'pending_rectification': 'refreshempty',\n        'pending_assignment': 'refreshempty',  // 待分配，使用刷新图标\n        'pending_review': 'eye',\n        'overdue': 'closeempty',\n        'rejected': 'closeempty',  // 整改不达标，使用关闭图标\n        'verified': 'checkmarkempty',  // 整改合格，使用对勾图标\n        'approved': 'checkmarkempty',  // 复查通过，使用对勾图标\n        'in_progress': 'gear'  // 整改中，使用齿轮图标\n      };\n      return iconMap[status] || 'help';\n    },\n\n    // 获取责任区图标\n    getAreaIcon(status) {\n      const iconMap = {\n        'completed': 'checkmarkempty',    // 已完成 - 对勾\n        'pending': 'minus',              // 待清理 - 减号\n        'overdue': 'closeempty',         // 已逾期 - 关闭\n        'scheduled': 'calendar'          // 未到时间 - 日历\n      };\n      return iconMap[status] || 'gear';  // 默认齿轮图标\n    },\n\n    // 转换为北京时间的工具函数\n    toBeijingTime(dateInput) {\n      if (!dateInput) {\n        return null;\n      }\n      \n      try {\n        const inputDate = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;\n        if (isNaN(inputDate.getTime())) {\n          return null;\n        }\n        \n        // 小程序环境中 toLocaleString 可能不稳定，使用更兼容的方式\n        // 手动计算北京时间（UTC+8）\n        const utcTime = inputDate.getTime();\n        const beijingTime = new Date(utcTime + 8 * 60 * 60 * 1000);\n        \n        // 检查计算结果是否有效\n        if (isNaN(beijingTime.getTime())) {\n          return null;\n        }\n        \n        // 返回标准的Date对象，直接使用正确的北京时间\n        return {\n          getFullYear: () => beijingTime.getUTCFullYear(),\n          getMonth: () => beijingTime.getUTCMonth(),\n          getDate: () => beijingTime.getUTCDate(),\n          getDay: () => beijingTime.getUTCDay(),\n          getHours: () => beijingTime.getUTCHours(),\n          getMinutes: () => beijingTime.getUTCMinutes(),\n          getSeconds: () => beijingTime.getUTCSeconds(),\n          getTime: () => beijingTime.getTime(),\n          toISOString: () => beijingTime.toISOString(),\n          // 兼容Date对象的比较\n          valueOf: () => beijingTime.getTime()\n        };\n      } catch (error) {\n        console.error('北京时间转换失败:', error, dateInput);\n        return null;\n      }\n    },\n\n    // 获取周次键值（统一使用北京时间）\n    getWeekKey(dateString) {\n      try {\n        if (!dateString) return '';\n        \n        const beijingDate = this.toBeijingTime(dateString);\n        if (!beijingDate) {\n          console.error('无法转换为北京时间:', dateString);\n          return '';\n        }\n        \n        const year = beijingDate.getFullYear();\n        const weekNum = this.getWeekNumber(beijingDate);\n        \n        if (isNaN(year) || isNaN(weekNum) || year < 1900 || year > 2100) {\n          console.error('日期参数异常:', { year, weekNum, dateString });\n          return '';\n        }\n        \n        const weekKey = `${year}-W${weekNum.toString().padStart(2, '0')}`;\n        \n        return weekKey;\n      } catch (error) {\n        console.error('生成周次键值出错:', error, dateString);\n        return '';\n      }\n    },\n\n    // 获取日期对应的周数（ISO 8601标准，周一为一周开始）\n    getWeekNumber(dateObj) {\n      try {\n        if (!dateObj) {\n          return 1; // 返回默认值\n        }\n        \n        // 兼容自定义日期对象和原生Date对象\n        let year, month, day, dayOfWeek;\n        \n        if (typeof dateObj.getFullYear === 'function') {\n          // 自定义日期对象或原生Date对象\n          year = dateObj.getFullYear();\n          month = dateObj.getMonth(); // 0-11\n          day = dateObj.getDate(); // 1-31\n          dayOfWeek = dateObj.getDay(); // 0=周日, 1=周一\n        } else {\n          // 备用：原生Date对象\n          const d = new Date(dateObj);\n          if (isNaN(d.getTime())) {\n            return 1; // 返回默认值\n          }\n          year = d.getFullYear();\n          month = d.getMonth();\n          day = d.getDate();\n          dayOfWeek = d.getDay();\n        }\n        \n        // 检查获取的值是否有效\n        if (isNaN(year) || isNaN(month) || isNaN(day) || isNaN(dayOfWeek)) {\n          return 1;\n        }\n        \n        // 创建该日期的Date对象用于计算\n        const d = new Date(year, month, day, 12, 0, 0, 0);\n        if (isNaN(d.getTime())) {\n          return 1; // 返回默认值\n        }\n        \n        // 转换为ISO标准（1=周一, 2=周二, ..., 7=周日）\n        const isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;\n        \n        // 找到这一周的周四（ISO 8601标准：包含周四的周属于该年）\n        const thursday = new Date(d);\n        thursday.setDate(d.getDate() + 4 - isoDayOfWeek);\n        \n        if (isNaN(thursday.getTime())) {\n          return 1; // 返回默认值\n        }\n        \n        // 获取周四所在的年份\n        const thursdayYear = thursday.getFullYear();\n        \n        // 计算该年第一个周四\n        const firstThursday = new Date(thursdayYear, 0, 4);\n        const firstThursdayDayOfWeek = firstThursday.getDay();\n        const firstThursdayIsoDay = firstThursdayDayOfWeek === 0 ? 7 : firstThursdayDayOfWeek;\n        \n        // 调整到第一个周四\n        firstThursday.setDate(4 + (4 - firstThursdayIsoDay));\n        \n        if (isNaN(firstThursday.getTime())) {\n          return 1; // 返回默认值\n        }\n        \n        // 计算周数\n        const weekNum = Math.floor((thursday - firstThursday) / (7 * 24 * 60 * 60 * 1000)) + 1;\n        \n        return isNaN(weekNum) ? 1 : weekNum;\n      } catch (error) {\n        console.error('计算周数出错:', error, dateObj);\n        return 1; // 返回默认值\n      }\n    },\n\n    // 获取一周的开始日期（周一 00:00:00）- 与getWeekNumber保持一致\n    getWeekStart(dateObj) {\n      if (!dateObj) {\n        return null;\n      }\n      \n      try {\n        // 兼容自定义日期对象和原生Date对象\n        let year, month, day, dayOfWeek;\n        \n        if (typeof dateObj.getFullYear === 'function') {\n          // 自定义日期对象或原生Date对象\n          year = dateObj.getFullYear();\n          month = dateObj.getMonth(); // 0-11\n          day = dateObj.getDate(); // 1-31\n          dayOfWeek = dateObj.getDay(); // 0=周日, 1=周一\n        } else {\n          // 备用：原生Date对象\n          const d = new Date(dateObj);\n          if (isNaN(d.getTime())) {\n            return null;\n          }\n          year = d.getFullYear();\n          month = d.getMonth();\n          day = d.getDate();\n          dayOfWeek = d.getDay();\n        }\n        \n        // 检查获取的值是否有效\n        if (isNaN(year) || isNaN(month) || isNaN(day) || isNaN(dayOfWeek)) {\n          return null;\n        }\n        \n        const isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek; // 转换为ISO标准\n        \n        // 计算周一\n        const monday = new Date(year, month, day - isoDayOfWeek + 1, 0, 0, 0, 0);\n        \n        if (isNaN(monday.getTime())) {\n          return null;\n        }\n        \n        return monday;\n      } catch (error) {\n        console.error('计算周开始时间出错:', error, dateObj);\n        return null;\n      }\n    },\n\n    // 获取一周的结束日期（周日 23:59:59）- 与getWeekNumber保持一致  \n    getWeekEnd(dateObj) {\n      if (!dateObj) {\n        return null;\n      }\n      \n      try {\n        // 兼容自定义日期对象和原生Date对象\n        let year, month, day, dayOfWeek;\n        \n        if (typeof dateObj.getFullYear === 'function') {\n          // 自定义日期对象或原生Date对象\n          year = dateObj.getFullYear();\n          month = dateObj.getMonth(); // 0-11\n          day = dateObj.getDate(); // 1-31\n          dayOfWeek = dateObj.getDay(); // 0=周日, 1=周一\n        } else {\n          // 备用：原生Date对象\n          const d = new Date(dateObj);\n          if (isNaN(d.getTime())) {\n            return null;\n          }\n          year = d.getFullYear();\n          month = d.getMonth();\n          day = d.getDate();\n          dayOfWeek = d.getDay();\n        }\n        \n        // 检查获取的值是否有效\n        if (isNaN(year) || isNaN(month) || isNaN(day) || isNaN(dayOfWeek)) {\n          return null;\n        }\n        \n        const isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek; // 转换为ISO标准\n        \n        // 计算周日\n        const sunday = new Date(year, month, day - isoDayOfWeek + 7, 23, 59, 59, 999);\n        \n        if (isNaN(sunday.getTime())) {\n          return null;\n        }\n        \n        return sunday;\n      } catch (error) {\n        console.error('计算周结束时间出错:', error, dateObj);\n        return null;\n      }\n    },\n    \n    // 格式化日期显示（只显示日期）\n    formatDate(dateString) {\n      if (!dateString) return '--';\n      \n      try {\n        // 兼容多种日期格式\n        let date;\n        if (typeof dateString === 'string') {\n          // ISO格式直接解析\n          if (dateString.includes('T') || dateString.includes('Z')) {\n            date = new Date(dateString);\n          } else {\n            // 其他格式尝试替换处理\n            date = new Date(dateString.replace(/-/g, '/'));\n          }\n        } else {\n          date = new Date(dateString);\n        }\n        \n        // 检查日期是否有效\n        if (isNaN(date.getTime())) {\n          return '--';\n        }\n        \n        return `${date.getMonth() + 1}月${date.getDate()}日`;\n      } catch (error) {\n        return '--';\n      }\n    },\n\n    // 格式化日期时间显示\n    formatDateTime(dateString) {\n      if (!dateString) return '--';\n      \n      try {\n        // 兼容多种日期格式\n        let date;\n        if (typeof dateString === 'string') {\n          // ISO格式直接解析\n          if (dateString.includes('T') || dateString.includes('Z')) {\n            date = new Date(dateString);\n          } else {\n            // 其他格式尝试替换处理\n            date = new Date(dateString.replace(/-/g, '/'));\n          }\n        } else {\n          date = new Date(dateString);\n        }\n        \n        // 检查日期是否有效\n        if (isNaN(date.getTime())) {\n          return '--';\n        }\n        \n        return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;\n      } catch (error) {\n        return '--';\n      }\n    },\n\n    getStatusTitle() {\n      if (this.selectedQuickFilter === 'week') {\n        return '本周清理状态';\n      } else if (this.dateFilterMode === 'quick' && this.selectedQuickFilter !== 'custom') {\n        const option = this.quickDateOptions.find(opt => opt.value === this.selectedQuickFilter);\n        return option ? `${option.label}整改记录统计` : '整改记录统计';\n      } else if (this.customDateRange.startDate && this.customDateRange.endDate) {\n        return '自定义时间段整改记录统计';\n      }\n      return '清理状态';\n    },\n\n    getCurrentTimeRange() {\n      if (this.selectedTimeFilter === 'custom' && this.dateFilterMode === 'range') {\n        return this.getDateRangeText();\n      } else {\n        const option = this.quickDateOptions.find(opt => opt.value === this.selectedQuickFilter);\n        return option ? option.label : '本周';\n      }\n    },\n\n    // 获取周标题\n    getWeekTitle(weekKey) {\n      // 解析weekKey，例如 '2025-W03' -> 2025年第3周\n      const match = weekKey.match(/(\\d{4})-W(\\d{2})/);\n      if (!match) return weekKey;\n      \n      const year = parseInt(match[1]);\n      const weekNum = parseInt(match[2]);\n      const beijingNow = this.toBeijingTime(new Date());\n      const currentYear = beijingNow.getFullYear();\n      const currentWeek = this.getCurrentWeekNumber();\n      \n      // 计算该周的日期范围\n      const weekRange = this.getWeekRangeByWeekKey(weekKey);\n      \n      // 判断是否为当前周\n      if (year === currentYear && weekNum === currentWeek) {\n        return `本周 (${weekRange.start}-${weekRange.end})`;\n      } else if (year === currentYear && weekNum === currentWeek - 1) {\n        return `上周 (${weekRange.start}-${weekRange.end})`;\n      } else if (year === currentYear && weekNum === currentWeek - 2) {\n        return `前周 (${weekRange.start}-${weekRange.end})`;\n      } else if (year === currentYear) {\n        // 计算周数差\n        const weekDiff = currentWeek - weekNum;\n        if (weekDiff > 0 && weekDiff <= 4) {\n          return `${weekDiff}周前 (${weekRange.start}-${weekRange.end})`;\n        } else if (weekDiff > 4) {\n          return `${year}年${weekRange.start}-${weekRange.end}`;\n        } else {\n          // 未来的周\n          return `第${weekNum}周 (${weekRange.start}-${weekRange.end})`;\n        }\n      } else {\n        return `${year}年${weekRange.start}-${weekRange.end}`;\n      }\n    },\n\n    // 根据weekKey获取该周的日期范围\n    getWeekRangeByWeekKey(weekKey) {\n      try {\n        if (!weekKey) {\n          return { start: '', end: '' };\n        }\n        \n        const match = weekKey.match(/(\\d{4})-W(\\d{2})/);\n        if (!match) return { start: '', end: '' };\n        \n        const year = parseInt(match[1]);\n        const weekNum = parseInt(match[2]);\n        \n        if (isNaN(year) || isNaN(weekNum) || year < 1900 || year > 2100 || weekNum < 1 || weekNum > 53) {\n          return { start: '', end: '' };\n        }\n        \n        // 计算该年第一个周四\n        const firstThursday = new Date(year, 0, 4);\n        if (isNaN(firstThursday.getTime())) {\n          return { start: '', end: '' };\n        }\n        \n        const firstThursdayDayOfWeek = firstThursday.getDay();\n        const firstThursdayIsoDay = firstThursdayDayOfWeek === 0 ? 7 : firstThursdayDayOfWeek;\n        \n        // 调整到第一个周四\n        firstThursday.setDate(4 + (4 - firstThursdayIsoDay));\n        \n        // 计算目标周的周四\n        const targetThursday = new Date(firstThursday);\n        targetThursday.setDate(firstThursday.getDate() + (weekNum - 1) * 7);\n        \n        if (isNaN(targetThursday.getTime())) {\n          return { start: '', end: '' };\n        }\n        \n        // 计算该周的周一（开始）\n        const monday = new Date(targetThursday);\n        monday.setDate(targetThursday.getDate() - 3); // 周四往前3天是周一\n        \n        // 计算该周的周日（结束）\n        const sunday = new Date(targetThursday);\n        sunday.setDate(targetThursday.getDate() + 3); // 周四往后3天是周日\n        \n        if (isNaN(monday.getTime()) || isNaN(sunday.getTime())) {\n          return { start: '', end: '' };\n        }\n        \n        return {\n          start: `${monday.getMonth() + 1}月${monday.getDate()}日`,\n          end: `${sunday.getMonth() + 1}月${sunday.getDate()}日`,\n          monday: monday,\n          sunday: sunday\n        };\n      } catch (error) {\n        console.error('计算周次日期范围出错:', error, weekKey);\n        return { start: '', end: '' };\n      }\n    },\n\n    // 获取当前是一年中的第几周（北京时间）\n    getCurrentWeekNumber() {\n      try {\n        const now = new Date();\n        const beijingNow = this.toBeijingTime(now);\n        \n        if (!beijingNow) {\n          console.error('无法获取北京时间');\n          return 1; // 返回默认值\n        }\n        \n        return this.getWeekNumber(beijingNow);\n      } catch (error) {\n        console.error('获取当前周数出错:', error);\n        return 1; // 返回默认值\n      }\n    },\n\n    // 计算整改任务的实际状态（考虑时间逾期）\n    calculateRectificationStatus(task) {\n      const now = new Date();\n      \n\n      \n      // 如果任务已经完成，状态不变\n      if (task.status === 'completed') {\n        return 'completed';\n      }\n      \n      // 检查是否有deadline字段进行逾期判断\n      if (task.deadline) {\n        const deadline = new Date(task.deadline);\n        if (!isNaN(deadline.getTime()) && now > deadline) {\n          // 超过截止时间且未完成，判定为逾期\n          if (task.status === 'pending_rectification' || task.status === 'pending_assignment') {\n            return 'overdue';\n          }\n        }\n      }\n      \n      // 业务逻辑：整改任务应该在创建当周内完成，超过当周就算逾期\n      if (task.created_at) {\n        const beijingCreatedDate = this.toBeijingTime(task.created_at);\n        const beijingNow = this.toBeijingTime(now);\n        \n        if (beijingCreatedDate && beijingNow) {\n          // 计算创建日期所在的周（基于北京时间）\n          const createdWeekStart = this.getWeekStart(beijingCreatedDate);\n          const createdWeekEnd = this.getWeekEnd(beijingCreatedDate);\n          \n          // 如果当前北京时间已经超过创建周的结束时间，且任务未完成，判定为逾期\n          if (beijingNow.getTime() > createdWeekEnd.getTime() && (task.status === 'pending_rectification' || task.status === 'pending_assignment')) {\n            return 'overdue';\n          }\n        }\n      }\n      \n      // 返回原始状态\n      return task.status;\n    },\n\n    // 智能设置默认展开的周（优先本周，没有则展开最新周）\n    setDefaultExpandedWeek() {\n      // 获取当前筛选数据中的所有唯一周次\n      const allWeeks = [...new Set(this.filteredRectificationTasks.map(task => task.week))];\n      \n      if (allWeeks.length > 0) {\n        // 获取当前周的周次键\n        const now = new Date();\n        const beijingNow = this.toBeijingTime(now);\n        const currentWeekKey = this.getWeekKey(beijingNow);\n        \n        let targetWeek;\n        \n        // 智能选择展开的周次：优先本周，没有则选最新周\n        if (allWeeks.includes(currentWeekKey)) {\n          // 如果筛选结果中包含本周，优先展开本周\n          targetWeek = currentWeekKey;\n        } else {\n          // 如果没有本周数据，按周次排序找到最新的周\n          const sortedWeeks = allWeeks.sort((a, b) => {\n            const [yearA, weekA] = a.split('-W').map(x => parseInt(x));\n            const [yearB, weekB] = b.split('-W').map(x => parseInt(x));\n            \n            if (yearA !== yearB) {\n              return yearB - yearA; // 年份降序\n            }\n            return weekB - weekA; // 同年内周数降序\n          });\n          \n          targetWeek = sortedWeeks[0];\n        }\n        \n        // 确保目标周在展开列表中\n        if (targetWeek && !this.expandedRectificationWeeks.includes(targetWeek)) {\n          this.expandedRectificationWeeks.push(targetWeek);\n        }\n      }\n    },\n\n    // 切换整改记录周展开状态\n    toggleRectificationWeek(weekKey) {\n      const index = this.expandedRectificationWeeks.indexOf(weekKey);\n      if (index > -1) {\n        this.expandedRectificationWeeks.splice(index, 1);\n      } else {\n        this.expandedRectificationWeeks.push(weekKey);\n      }\n    },\n\n    // 重置展开状态\n    resetExpandedState() {\n      this.expandedRectificationWeeks = [];\n    },\n    \n    // 智能重置展开状态（清空后等数据重新加载）\n    smartResetExpandedState() {\n      this.expandedRectificationWeeks = [];\n      // 等待下一个tick和少量延时后设置默认展开，确保数据已更新\n      this.$nextTick(() => {\n        setTimeout(() => {\n          this.setDefaultExpandedWeek();\n        }, 50);\n      });\n    },\n    \n    getStatusText(status) {\n      const statusMap = {\n        'pending': '待清理',\n        'pending_review': '待复查', \n        'completed': '已完成',\n        'pending_rectification': '待整改',\n        'pending_assignment': '待分配',  // 待分配状态\n        'overdue': '已逾期',\n        'scheduled': '未到时间',  // 公共责任区未到排班日\n        'rejected': '整改不达标',\n        'verified': '整改合格',\n        'approved': '复查通过',\n        'in_progress': '整改中',\n        'rectification_completed': '整改已完成'\n      }\n      return statusMap[status] || '未知'\n    },\n\n    // 获取清理记录状态文本\n    getCleaningRecordStatusText(status) {\n      return '已完成'; // 所有清理记录都是已完成状态\n    },\n\n    // 格式化描述文本，智能处理换行和长度\n    formatDescription(description) {\n      if (!description) return '';\n      \n      // 智能清理文本\n      let formatted = description\n        .replace(/\\n/g, ' ')        // 将换行符替换为空格\n        .replace(/\\r/g, ' ')        // 将回车符替换为空格\n        .replace(/\\s+/g, ' ')       // 将多个连续空格合并为一个\n        .trim();                    // 去除首尾空格\n      \n      // 限制长度，避免显示过长导致换行\n      if (formatted.length > 16) {\n        formatted = formatted.substring(0, 16) + '...';\n      }\n      \n      return formatted;\n    },\n\n    // 处理清理记录更新事件\n    handleRecordUpdated(data) {\n      // 通过事件已经收到更新通知，进行静默刷新\n      this.silentRefreshData();\n    },\n\n    // 静默刷新数据（不显示加载状态）\n    async silentRefreshData() {\n      if (this.loading) return; // 如果正在加载，跳过\n      \n      try {\n        // 清理所有缓存\n        this.currentWeekCache = null;\n        this.areasCache = null;\n        \n        // 静默重新加载数据，不显示loading状态\n        await Promise.all([\n          this.loadUserAssignments(),\n          this.loadUserRectificationTasks(),\n          this.loadMyCleaningRecords()\n        ]);\n        \n        // 数据加载完成后，确保第一个分组展开\n        this.$nextTick(() => {\n          this.setDefaultExpandedWeek();\n        });\n        \n        // 清除刷新标记，避免用户返回页面时再次显示loading\n        this.needsRefresh = false;\n      } catch (error) {\n        // 静默处理错误，不显示错误提示\n      }\n    },\n\n    getIconColor(status) {\n      const colorMap = {\n        'completed': '#34C759',\n        'pending': '#FF9500',\n        'overdue': '#FF3B30',\n        'pending_review': '#007AFF',\n        'pending_rectification': '#FF6B35',\n        'pending_assignment': '#FF9500',  // 待分配，使用橙色\n        'scheduled': '#8E8E93',  // 未到时间，使用灰色\n        'rejected': '#FF3B30',  // 整改不达标，使用红色\n        'verified': '#34C759',  // 整改合格，使用绿色\n        'approved': '#34C759',  // 复查通过，使用绿色\n        'in_progress': '#007AFF',  // 整改中，使用蓝色\n        'rectification_completed': '#FF9500'  // 整改已完成，使用橙色\n      }\n      return colorMap[status] || '#8E8E93'\n    },\n\n    // 处理逾期状态的通用方法\n    handleOverdueStatus(area) {\n      const isPublic = area.type === 'public';\n      const content = isPublic \n        ? '排班清理时间已过，无法再进行清理操作。请等待下周重新分配。'\n        : '本周清理时间已过，无法再进行清理操作。请等待下周重新分配。';\n      \n      uni.showModal({\n        title: '清理任务已逾期',\n        content,\n        showCancel: false,\n        confirmText: '知道了'\n      });\n      return true; // 返回true表示已处理逾期状态\n    },\n    \n    openAreaDetail(area) {\n      if (area.status === 'overdue') {\n        this.handleOverdueStatus(area);\n        return;\n      }\n      \n      if (area.status === 'pending') {\n        // 跳转到清理页面，由事件监听机制处理数据更新，无需手动刷新\n        uni.navigateTo({\n          url: `/pages/6s_pkg/cleaning-upload?areaId=${area._id || area.id}&type=fixed`\n        })\n      } else {\n        // 跳转到详情页面不需要刷新数据\n        uni.navigateTo({\n          url: `/pages/6s_pkg/area-detail?areaId=${area._id || area.id}&type=fixed`\n        })\n      }\n    },\n    \n    openPublicAreaDetail(area) {\n      if (area.status === 'overdue') {\n        this.handleOverdueStatus(area);\n        return;\n      }\n      \n      if (area.status === 'scheduled') {\n        // 未到排班日，不允许清理\n        const weekDays = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];\n        const scheduledDay = area.scheduled_day === 0 ? 7 : area.scheduled_day;\n        uni.showModal({\n          title: '未到清理时间',\n          content: `该公共责任区安排在${weekDays[scheduledDay]}进行清理，当前时间无需清理。`,\n          showCancel: false,\n          confirmText: '知道了'\n        });\n        return;\n      }\n      \n      if (area.status === 'pending') {\n        // 到了排班日，可以进行清理，由事件监听机制处理数据更新，无需手动刷新\n        uni.navigateTo({\n          url: `/pages/6s_pkg/cleaning-upload?areaId=${area._id || area.id}&type=public`\n        })\n      } else {\n        // 跳转到详情页面不需要刷新数据\n        uni.navigateTo({\n          url: `/pages/6s_pkg/area-detail?areaId=${area._id || area.id}&type=public`\n        })\n      }\n    },\n    \n    openRectificationDetail(task) {\n      // 检查任务ID是否有效\n      const taskId = task.id || task._id || task.issue_id || task.task_id;\n      \n      if (!taskId) {\n        uni.showToast({\n          title: '任务数据异常',\n          icon: 'error'\n        });\n        return;\n      }\n      \n      if (task.status === 'overdue') {\n        uni.showModal({\n          title: '整改任务已逾期',\n          content: '该整改任务已逾期，记录已锁定',\n          showCancel: false,\n          confirmText: '知道了'\n        });\n        return;\n      }\n      \n      if (task.status === 'pending_rectification') {\n        // 待整改任务，直接跳转到整改上传页面，由事件监听机制处理数据更新，无需手动刷新\n        uni.navigateTo({\n          url: `/pages/6s_pkg/rectification-upload?taskId=${taskId}&areaType=${task.areaType}`\n        });\n      } else if (task.status === 'pending_review') {\n        // 待复查任务，员工只能查看详情，不需要刷新数据\n        uni.navigateTo({\n          url: `/pages/6s_pkg/rectification-detail?taskId=${taskId}`\n        });\n      } else {\n        // 已完成任务，跳转到整改详情页面，不需要刷新数据\n        uni.navigateTo({\n          url: `/pages/6s_pkg/rectification-detail?taskId=${taskId}`\n        });\n      }\n    },\n\n    // 打开清理记录详情\n    openCleaningRecordDetail(record) {\n      // 将整个 record 对象转换为 JSON 字符串进行传递\n      const recordStr = encodeURIComponent(JSON.stringify(record));\n      \n      uni.navigateTo({\n        url: `/pages/6s_pkg/area-detail?record=${recordStr}`\n      });\n    },\n\n    // ======== 日期选择器方法 ========\n    showTimeSelector() {\n      // 初始化日期范围\n      this.initializeDateRange();\n      this.$refs.timePopup.open();\n    },\n\n    closeDatePicker() {\n      this.$refs.timePopup.close();\n    },\n\n    // 初始化日期范围\n    initializeDateRange() {\n      if (!this.customDateRange.startDate) {\n        const now = new Date();\n        // 根据当前快捷选择初始化日期范围\n        const range = this.getQuickDateRange(this.selectedQuickFilter);\n        this.customDateRange.startDate = this.formatDateForPicker(range.start);\n        this.customDateRange.endDate = this.formatDateForPicker(range.end);\n      }\n\n      // 初始化日历组件的日期\n      if (!this.calendarDate) {\n        this.calendarDate = this.formatDateForPicker(new Date());\n      }\n    },\n\n    // 切换到范围选择模式\n    switchToRangeMode() {\n      this.dateFilterMode = 'range';\n    },\n\n    // 切换到快捷选择模式  \n    switchToQuickMode() {\n      this.dateFilterMode = 'quick';\n    },\n\n    // 快捷日期选择\n    selectQuickDateOption(option) {\n      this.selectedQuickFilter = option.value;\n      if (option.value === 'custom') {\n        this.switchToRangeMode();\n      } else {\n        // 快捷选择时，智能重置展开状态\n        this.smartResetExpandedState();\n\n        // 快捷选择立即生效，关闭弹窗\n        this.closeDatePicker();\n\n        // 重新加载数据以匹配新的筛选条件\n        this.loading = true;\n        clearTimeout(this.refreshTimer);\n        this.refreshTimer = setTimeout(async () => {\n          try {\n            await this.loadMyAreas();\n            // 数据加载完成后，确保第一个分组展开\n            this.$nextTick(() => {\n              this.setDefaultExpandedWeek();\n            });\n          } finally {\n            this.loading = false;\n          }\n        }, 100);\n      }\n    },\n\n    // 获取当前日期范围\n    getCurrentDateRange() {\n      if (this.dateFilterMode === 'quick' && this.selectedQuickFilter !== 'custom') {\n        return this.getQuickDateRange(this.selectedQuickFilter);\n      } else if (this.customDateRange.startDate && this.customDateRange.endDate) {\n        return {\n          start: new Date(this.customDateRange.startDate),\n          end: new Date(this.customDateRange.endDate)\n        };\n      }\n      return null;\n    },\n\n    // 获取快捷日期的范围\n    getQuickDateRange(quickValue) {\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n\n      switch (quickValue) {\n        case 'week':\n          // 本周：周一到周日\n          const startOfWeek = new Date(today);\n          const dayOfWeek = startOfWeek.getDay();\n          const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;\n          startOfWeek.setDate(startOfWeek.getDate() - daysToMonday);\n\n          const endOfWeek = new Date(startOfWeek);\n          endOfWeek.setDate(endOfWeek.getDate() + 6);\n          endOfWeek.setHours(23, 59, 59, 999);\n\n          return { start: startOfWeek, end: endOfWeek };\n\n        case 'last_week':\n          // 上周：上周一到上周日\n          const lastWeekStart = new Date(today);\n          const currentDayOfWeek = lastWeekStart.getDay();\n          const daysToLastMonday = currentDayOfWeek === 0 ? 13 : currentDayOfWeek + 6;\n          lastWeekStart.setDate(lastWeekStart.getDate() - daysToLastMonday);\n\n          const lastWeekEnd = new Date(lastWeekStart);\n          lastWeekEnd.setDate(lastWeekEnd.getDate() + 6);\n          lastWeekEnd.setHours(23, 59, 59, 999);\n\n          return { start: lastWeekStart, end: lastWeekEnd };\n\n        case 'month':\n          // 本月：1号到月底\n          const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);\n          const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);\n          endOfMonth.setHours(23, 59, 59, 999);\n\n          return { start: startOfMonth, end: endOfMonth };\n\n        default:\n          // 默认返回本周\n          return this.getQuickDateRange('week');\n      }\n    },\n\n    // 格式化日期用于picker\n    formatDateForPicker(date) {\n      if (!date) return '';\n      const d = new Date(date);\n      const year = d.getFullYear();\n      const month = String(d.getMonth() + 1).padStart(2, '0');\n      const day = String(d.getDate()).padStart(2, '0');\n      return `${year}-${month}-${day}`;\n    },\n\n    // 获取日期范围显示文本\n    getDateRangeText() {\n      if (this.dateFilterMode === 'quick') {\n        const option = this.quickDateOptions.find(opt => opt.value === this.selectedQuickFilter);\n        return option ? option.label : '本周';\n      } else {\n        const startText = this.getStartDateText();\n        const endText = this.getEndDateText();\n        if (startText === '请选择' || endText === '请选择') {\n          return '选择日期范围';\n        }\n        return `${startText} - ${endText}`;\n      }\n    },\n\n    // 获取开始日期显示文本  \n    getStartDateText() {\n      if (!this.customDateRange.startDate) return '请选择开始日期';\n      return this.formatDisplayDate(new Date(this.customDateRange.startDate));\n    },\n\n    // 获取结束日期显示文本\n    getEndDateText() {\n      if (!this.customDateRange.endDate) return '请选择结束日期';\n      return this.formatDisplayDate(new Date(this.customDateRange.endDate));\n    },\n\n    // 格式化显示日期\n    formatDisplayDate(date) {\n      const d = new Date(date);\n      const month = d.getMonth() + 1;\n      const day = d.getDate();\n      return `${month}月${day}日`;\n    },\n\n    // 日历组件事件处理\n    onCalendarChange(event) {\n      // 防止重复处理\n      clearTimeout(this.calendarChangeTimer);\n      this.calendarChangeTimer = setTimeout(() => {\n        // 处理日期范围选择\n        if (event.range) {\n          if (event.range.before && event.range.after) {\n            // 选择了完整的日期范围\n            this.customDateRange.startDate = event.range.before;\n            this.customDateRange.endDate = event.range.after;\n\n            // 立即生效：关闭弹窗并重新加载数据\n            this.selectedTimeFilter = 'custom';\n            this.smartResetExpandedState();\n            this.closeDatePicker();\n\n            // 重新加载数据\n            this.loading = true;\n            clearTimeout(this.refreshTimer);\n            this.refreshTimer = setTimeout(async () => {\n              try {\n                await this.loadMyAreas();\n                // 数据加载完成后，确保第一个分组展开\n                this.$nextTick(() => {\n                  this.setDefaultExpandedWeek();\n                });\n              } finally {\n                this.loading = false;\n              }\n            }, 100);\n\n            uni.showToast({\n              title: '日期范围选择完成',\n              icon: 'success',\n              duration: 1500\n            });\n          }\n        } else if (event.fulldate) {\n          // 单个日期选择\n          if (!this.customDateRange.startDate || this.customDateRange.endDate) {\n            // 选择开始日期或重新选择\n            this.customDateRange.startDate = event.fulldate;\n            this.customDateRange.endDate = '';\n          } else {\n            // 选择结束日期\n            this.customDateRange.endDate = event.fulldate;\n\n            // 确保开始日期不晚于结束日期\n            if (new Date(this.customDateRange.startDate) > new Date(this.customDateRange.endDate)) {\n              const temp = this.customDateRange.startDate;\n              this.customDateRange.startDate = this.customDateRange.endDate;\n              this.customDateRange.endDate = temp;\n            }\n\n            // 选择完成后立即生效：关闭弹窗并重新加载数据\n            this.selectedTimeFilter = 'custom';\n            this.smartResetExpandedState();\n            this.closeDatePicker();\n\n            // 重新加载数据\n            this.loading = true;\n            clearTimeout(this.refreshTimer);\n            this.refreshTimer = setTimeout(async () => {\n              try {\n                await this.loadMyAreas();\n                // 数据加载完成后，确保第一个分组展开\n                this.$nextTick(() => {\n                  this.setDefaultExpandedWeek();\n                });\n              } finally {\n                this.loading = false;\n              }\n            }, 100);\n\n            uni.showToast({\n              title: '日期范围选择完成',\n              icon: 'success',\n              duration: 1500\n            });\n          }\n        }\n      }, 100);\n    },\n\n    // 月份切换事件\n    onMonthSwitch(event) {\n      // 安全地处理月份切换事件\n      if (event && event.current && event.current.fulldate) {\n        this.calendarDate = event.current.fulldate;\n      } else if (event && event.year && event.month) {\n        // 如果没有fulldate，手动构造日期\n        const year = event.year;\n        const month = String(event.month).padStart(2, '0');\n        this.calendarDate = `${year}-${month}-01`;\n      }\n    },\n\n    // 格式化选中的日期显示\n    formatSelectedDate(dateStr) {\n      if (!dateStr) return '';\n      const date = new Date(dateStr);\n      const month = date.getMonth() + 1;\n      const day = date.getDate();\n      const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\n      const dayName = dayNames[date.getDay()];\n      return `${month}月${day}日 ${dayName}`;\n    },\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\n\twidth: 100%;\n\toverflow-x: hidden; /* 防止水平滚动 */\n\tbox-sizing: border-box;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;\n}\n\n/* 时间选择器样式 */\n.time-selector {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  padding: 12rpx 20rpx;\n  background: rgba(0, 122, 255, 0.1);\n  border-radius: 20rpx;\n  border: 2rpx solid rgba(0, 122, 255, 0.2);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:active {\n    background: rgba(0, 122, 255, 0.15);\n    transform: scale(0.98);\n  }\n}\n\n.time-text {\n  font-size: 24rpx;\n  color: #007AFF;\n  font-weight: 500;\n}\n\n/* ======== 新的日历式日期选择器样式 ======== */\n.date-picker-popup {\n  background: white;\n  padding: 0;\n  max-height: 80vh;\n  overflow-y: auto;\n}\n\n.popup-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 32rpx 32rpx 24rpx 32rpx;\n  border-bottom: 1rpx solid #F2F2F7;\n}\n\n.popup-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n}\n\n.popup-close {\n  width: 48rpx;\n  height: 48rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #F2F2F7;\n  border-radius: 50%;\n}\n\n.quick-date-section,\n.range-date-section {\n  padding: 24rpx 32rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 24rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.quick-options {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n  margin-bottom: 32rpx;\n}\n\n.quick-option {\n  flex: 1;\n  min-width: 120rpx;\n  padding: 24rpx 16rpx;\n  border: 2rpx solid #E5E5EA;\n  border-radius: 16rpx;\n  text-align: center;\n  transition: all 0.2s;\n}\n\n.quick-option.active {\n  border-color: #007AFF;\n  background: rgba(0, 122, 255, 0.1);\n}\n\n.quick-text {\n  font-size: 28rpx;\n  color: #1C1C1E;\n}\n\n.quick-option.active .quick-text {\n      color: #007AFF;\n      font-weight: 600;\n    }\n\n/* 日历组件样式 */\n.calendar-section {\n  margin: 24rpx 0;\n}\n\n.calendar-header {\n  margin-bottom: 24rpx;\n}\n\n.calendar-tip {\n  font-size: 24rpx;\n  color: #8E8E93;\n  display: block;\n}\n\n.selected-range {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-top: 24rpx;\n  padding: 20rpx;\n  background: #F8F9FA;\n  border-radius: 16rpx;\n  gap: 16rpx;\n}\n\n.range-item {\n  text-align: center;\n  flex: 1;\n}\n\n.range-label {\n  font-size: 22rpx;\n  color: #8E8E93;\n  display: block;\n  margin-bottom: 4rpx;\n}\n\n.range-value {\n  font-size: 26rpx;\n  color: #1C1C1E;\n  font-weight: 600;\n  display: block;\n}\n\n.range-separator {\n  font-size: 24rpx;\n  color: #007AFF;\n  font-weight: 600;\n}\n\n/* 卡片样式 */\n.card {\n  background: #ffffff;\n  border-radius: 16rpx;\n  margin: 24rpx 32rpx 0 32rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.card:first-child {\n  margin-top: 24rpx;\n}\n\n.card-header {\n  padding: 32rpx 32rpx 16rpx 32rpx;\n  border-bottom: 1rpx solid #F2F2F7;\n}\n\n.card-body {\n  padding: 32rpx;\n}\n\n.header-content {\n  display: flex;\n  flex-direction: row;\n  align-items: baseline;\n  justify-content: space-between;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 0;\n}\n\n.card-subtitle {\n  font-size: 26rpx;\n  color: #8E8E93;\n}\n\n/* 时间选择器 */\n.time-selector {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.time-selector-header {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n}\n\n.time-selector-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n}\n\n.time-selector-picker {\n  background: rgba(0, 122, 255, 0.1);\n  border: 1rpx solid rgba(0, 122, 255, 0.2);\n  border-radius: 20rpx;\n  padding: 12rpx 20rpx;\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  font-size: 26rpx;\n  color: #007AFF;\n  font-weight: 500;\n  transition: all 0.2s ease;\n  \n  &:active {\n    background: rgba(0, 122, 255, 0.15);\n    transform: scale(0.98);\n  }\n}\n\n\n\n/* 统计网格 */\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 32rpx;\n  text-align: center;\n}\n\n.stats-grid-two {\n  grid-template-columns: repeat(2, 1fr);\n}\n\n.stats-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.stats-number {\n  font-size: 48rpx;\n  font-weight: bold;\n  margin-bottom: 8rpx;\n  \n  &.success {\n    color: #34C759;\n  }\n  \n  &.warning {\n    color: #FF9500;\n  }\n  \n  &.info {\n    color: #007AFF;\n  }\n  \n  &.danger {\n    color: #FF3B30;\n  }\n}\n\n.stats-label {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n/* 列表项样式 */\n.list-item {\n  display: flex;\n  align-items: center;\n  padding: 24rpx 32rpx;\n  background: #ffffff;\n  border-bottom: 1rpx solid #F2F2F7;\n}\n\n.list-item:last-child {\n  border-bottom: none;\n}\n\n.list-item-icon {\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 24rpx;\n  flex-shrink: 0;\n  \n  &.icon-bg-completed {\n    background: rgba(52, 199, 89, 0.1);\n  }\n  \n  &.icon-bg-pending {\n    background: rgba(255, 149, 0, 0.1);\n  }\n  \n  &.icon-bg-overdue {\n    background: rgba(255, 59, 48, 0.1);\n  }\n  \n  &.icon-bg-pending_review {\n    background: rgba(0, 122, 255, 0.1);\n  }\n  \n  &.icon-bg-pending_rectification {\n    background: rgba(255, 107, 53, 0.1);\n  }\n  \n  &.icon-bg-pending_assignment {\n    background: rgba(255, 149, 0, 0.1);\n  }\n  \n  &.icon-bg-scheduled {\n    background: rgba(142, 142, 147, 0.1);\n  }\n  \n  &.icon-bg-rejected {\n    background: rgba(255, 59, 48, 0.1);\n  }\n  \n  &.icon-bg-verified {\n    background: rgba(52, 199, 89, 0.1);\n  }\n  \n  &.icon-bg-approved {\n    background: rgba(52, 199, 89, 0.1);\n  }\n  \n  &.icon-bg-in_progress {\n    background: rgba(0, 122, 255, 0.1);\n  }\n}\n\n.list-item-content {\n  flex: 1;\n}\n\n.list-item-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 4rpx;\n}\n\n.list-item-subtitle {\n  font-size: 24rpx;\n  color: #8E8E93;\n  line-height: 1.4;\n}\n\n.list-item-right {\n  display: flex;\n  align-items: center;\n  gap: 16rpx;\n}\n\n/* 状态标签 */\n.status-badge {\n  padding: 8rpx 16rpx;\n  border-radius: 8rpx;\n  font-size: 24rpx;\n  font-weight: 600;\n  \n  &.status-completed {\n    background: #E8F5E8;\n    color: #34C759;\n  }\n  \n  &.status-pending {\n    background: #FFF4E6;\n    color: #FF9500;\n  }\n  \n  &.status-overdue {\n    background: #FFE6E6;\n    color: #FF3B30;\n  }\n  \n  &.status-pending_review {\n    background: #E6F3FF;\n    color: #007AFF;\n  }\n  \n  &.status-pending_rectification {\n    background: #FFF0E6;\n    color: #FF6B35;\n  }\n  \n  &.status-pending_assignment {\n    background: #FFF4E6;\n    color: #FF9500;\n  }\n  \n  &.status-scheduled {\n    background: #F2F2F7;\n    color: #8E8E93;\n  }\n  \n  &.status-rejected {\n    background: #FFE6E6;\n    color: #FF3B30;\n  }\n  \n  &.status-verified {\n    background: #E8F5E8;\n    color: #34C759;\n  }\n  \n  &.status-approved {\n    background: #E8F5E8;\n    color: #34C759;\n  }\n  \n  &.status-in_progress {\n    background: #E6F3FF;\n    color: #007AFF;\n  }\n  \n  &.status-rectification_completed {\n    background: #FFF4E6;\n    color: #FF9500;\n  }\n}\n\n/* 公共区域标签 */\n.public-tag {\n  display: inline-block;\n  background: #E6F3FF;\n  color: #007AFF;\n  font-size: 22rpx;\n  padding: 4rpx 12rpx;\n  border-radius: 16rpx;\n  margin-left: 12rpx;\n  font-weight: 400;\n}\n\n/* 固定区域标签 */\n.fixed-tag {\n  display: inline-block;\n  background: #E8F5E8;\n  color: #34C759;\n  font-size: 22rpx;\n  padding: 4rpx 12rpx;\n  border-radius: 16rpx;\n  margin-left: 12rpx;\n  font-weight: 400;\n}\n\n/* 整改任务时间信息 */\n.task-time-info {\n  display: flex;\n  align-items: center;\n  margin-top: 8rpx;\n  font-size: 22rpx;\n  color: #8E8E93;\n}\n\n.time-label {\n  margin-right: 8rpx;\n}\n\n.time-value {\n  font-weight: 500;\n  color: #1C1C1E;\n}\n\n/* 清理记录元信息 */\n.cleaning-record-meta {\n  display: flex;\n  align-items: center;\n  margin-top: 8rpx;\n  font-size: 22rpx;\n  color: #8E8E93;\n}\n\n.meta-label {\n  margin-right: 4rpx;\n}\n\n.meta-value {\n  font-weight: 500;\n  color: #1C1C1E;\n  margin-right: 12rpx;\n}\n\n.meta-remark {\n  max-width: 200rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n/* 时间分组样式 */\n.time-group {\n  margin-bottom: 16rpx;\n}\n\n.time-group-header {\n  padding: 24rpx 32rpx;\n  background: #F8F9FA;\n  border-radius: 12rpx;\n  margin-bottom: 8rpx;\n}\n\n.time-group-title {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n}\n\n.time-title {\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #1D1D1F;\n  flex: 1;\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n}\n\n.time-count {\n  font-size: 24rpx;\n  color: #8E8E93;\n  background: #E5E7EB;\n  padding: 4rpx 12rpx;\n  border-radius: 16rpx;\n  min-width: 48rpx;\n  text-align: center;\n}\n\n/* 加载状态样式 */\n.loading-container {\n  padding: 80rpx 32rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: #FFFFFF;\n  border-radius: 24rpx;\n  margin: 20rpx;\n}\n\n.loading-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 32rpx;\n}\n\n/* 统计区域加载状态 */\n.stats-loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 80rpx 24rpx;\n  min-height: 200rpx;\n}\n\n/* 列表加载状态 */\n.list-loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 20rpx;\n  background: #FFFFFF;\n  border-radius: 24rpx;\n  margin: 20rpx;\n  border: 1rpx solid #F2F2F7;\n}\n\n.loading-spinner {\n  width: 60rpx;\n  height: 60rpx;\n  border: 6rpx solid #F0F4F8;\n  border-top: 6rpx solid #007AFF;\n  border-radius: 50%;\n  animation: spin 1.2s linear infinite;\n  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);\n}\n\n/* 按钮内的加载动画 */\n.btn-loading-spinner {\n  width: 32rpx;\n  height: 32rpx;\n  border: 4rpx solid rgba(255, 255, 255, 0.3);\n  border-top: 4rpx solid #FFFFFF;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-right: 12rpx;\n}\n\n@keyframes spin {\n  0% { \n    transform: rotate(0deg);\n  }\n  100% { \n    transform: rotate(360deg);\n  }\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #6B7280;\n  text-align: center;\n  font-weight: 500;\n  letter-spacing: 0.5rpx;\n}\n\n\n\n.list-loading .loading-text {\n  color: #374151;\n}\n\n/* 加载更多按钮 */\n.load-more-section {\n  padding: 32rpx;\n  display: flex;\n  justify-content: center;\n}\n\n.load-more-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12rpx;\n  padding: 24rpx 48rpx;\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n  color: white;\n  border: none;\n  border-radius: 25rpx;\n  font-size: 28rpx;\n  font-weight: 500;\n  min-width: 200rpx;\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n  }\n  \n  &:active:not(:disabled) {\n    transform: scale(0.95);\n  }\n}\n\n/* 底部安全间距 */\n.bottom-safe-area {\n  height: 40rpx;\n}\n\n/* 响应式调整 */\n@media (max-width: 414px) {\n  .page-header {\n    padding: 24rpx 16rpx;\n  }\n  \n  .card {\n    margin: 24rpx 16rpx 0 16rpx;\n  }\n  \n  .card:first-child {\n    margin-top: 24rpx;\n  }\n  \n  .stats-grid {\n    grid-template-columns: repeat(3, 1fr);\n    gap: 24rpx;\n  }\n}\n</style>", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my-areas.vue?vue&type=style&index=0&id=4c3d3ebe&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my-areas.vue?vue&type=style&index=0&id=4c3d3ebe&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755779815927\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}