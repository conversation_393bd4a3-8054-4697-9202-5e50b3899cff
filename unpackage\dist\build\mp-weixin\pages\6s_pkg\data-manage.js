require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/data-manage"],{"1f76":function(e,t,a){"use strict";var n=a("937a"),r=a.n(n);r.a},"4a91":function(e,t,a){"use strict";a.r(t);var n=a("71d8"),r=a("5421");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("1f76");var s=a("828b"),o=Object(s["a"])(r["default"],n["b"],n["c"],!1,null,"fb27bff8",null,!1,n["a"],void 0);t["default"]=o.exports},5421:function(e,t,a){"use strict";a.r(t);var n=a("7e04"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"71d8":function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return n}));var n={uniIcons:function(){return Promise.all([a.e("common/vendor"),a.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(a.bind(null,"6ddf"))}},r=function(){var e=this.$createElement;this._self._c},i=[]},"7e04":function(e,t,a){"use strict";(function(e){var n=a("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("7eb4")),i=n(a("7ca3")),s=n(a("3b2d")),o=n(a("ee10")),u=a("882c");function c(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}var l={name:"DataManage",data:function(){return{loading:!1,loadError:!1,dataLoaded:!1,showPermissionModal:!1,overview:{fixedAreas:0,publicAreas:0,locations:0,employees:0,assignments:0},processCache:{overviewData:null,cacheTimestamp:null,cacheExpiry:3e5}}},onLoad:function(){this.checkUserPermission(),this.loadOverviewDataOptimized()},onShow:function(){this.dataLoaded&&this.isCacheExpired()&&this.silentRefreshData()},methods:{checkUserPermission:function(){try{var t=a("eddf"),n=t.getCacheKey,r=t.CACHE_KEYS,i=e.getStorageSync("uni-id-pages-userInfo")||{},s=e.getStorageSync(n(r.USER_ROLE))||"{}",o={};try{o="string"===typeof s?JSON.parse(s):s}catch(d){o={}}var u=[];o&&o.value&&o.value.userRole?u=Array.isArray(o.value.userRole)?o.value.userRole:[o.value.userRole]:i.role?u=Array.isArray(i.role)?i.role:[i.role]:"admin"===i.username&&(u=["admin"]);var c=["admin","GM","PM","Integrated","reviser"],l=u.some((function(e){return c.includes(e)}));return!!l||(this.showPermissionModal=!0,!1)}catch(p){return console.error("权限检查失败:",p),e.showToast({title:"权限验证失败",icon:"none",duration:2e3}),setTimeout((function(){e.switchTab({url:"/pages/6s/index"})}),2e3),!1}},closePermissionModal:function(){this.showPermissionModal=!1,e.switchTab({url:"/pages/6s/index"})},loadOverviewDataOptimized:function(){var e=this;return(0,o.default)(r.default.mark((function t(){var a,n;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,e.loading=!0,e.loadError=!1,!e.isCacheValid()){t.next=8;break}return e.overview=e.processCache.overviewData,e.loading=!1,e.dataLoaded=!0,t.abrupt("return");case 8:return t.next=10,Promise.allSettled([e.loadAreasCountOptimized("fixed"),e.loadAreasCountOptimized("public"),e.loadLocationsCountOptimized(),e.loadEmployeeAssignmentCountOptimized()]);case 10:a=t.sent,n={fixedAreas:"fulfilled"===a[0].status?a[0].value:0,publicAreas:"fulfilled"===a[1].status?a[1].value:0,locations:"fulfilled"===a[2].status?a[2].value:0,employees:"fulfilled"===a[3].status?a[3].value.employees:0,assignments:"fulfilled"===a[3].status?a[3].value.assignments:0},e.overview=n,e.updateCache(n),t.next=19;break;case 16:t.prev=16,t.t0=t["catch"](0),e.handleLoadError(t.t0);case 19:return t.prev=19,e.loading=!1,e.dataLoaded=!0,t.finish(19);case 23:case"end":return t.stop()}}),t,null,[[0,16,19,23]])})))()},silentRefreshData:function(){var e=this;return(0,o.default)(r.default.mark((function t(){var a,n;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.loading){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,e.clearCache(),t.next=6,Promise.allSettled([e.loadAreasCountOptimized("fixed"),e.loadAreasCountOptimized("public"),e.loadLocationsCountOptimized(),e.loadEmployeeAssignmentCountOptimized()]);case 6:a=t.sent,n={fixedAreas:"fulfilled"===a[0].status?a[0].value:e.overview.fixedAreas,publicAreas:"fulfilled"===a[1].status?a[1].value:e.overview.publicAreas,locations:"fulfilled"===a[2].status?a[2].value:e.overview.locations,employees:"fulfilled"===a[3].status?a[3].value.employees:e.overview.employees,assignments:"fulfilled"===a[3].status?a[3].value.assignments:e.overview.assignments},e.overview=n,e.updateCache(n),t.next=14;break;case 12:t.prev=12,t.t0=t["catch"](2);case 14:case"end":return t.stop()}}),t,null,[[2,12]])})))()},loadAreasCountOptimized:function(e){var t=this;return(0,o.default)(r.default.mark((function a(){var n;return r.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,(0,u.callCloudFunction)("hygiene-area-management",{action:"getAreaList",data:{type:e,pageSize:1e3}});case 3:if(n=a.sent,!n.success||!n.data){a.next=6;break}return a.abrupt("return",t.extractDataCount(n.data));case 6:return a.abrupt("return",0);case 9:return a.prev=9,a.t0=a["catch"](0),a.abrupt("return",0);case 12:case"end":return a.stop()}}),a,null,[[0,9]])})))()},extractDataCount:function(e){return Array.isArray(e)?e.length:e.data&&Array.isArray(e.data)?e.data.length:"object"===(0,s.default)(e)&&void 0!==e.total?e.total:0},loadLocationsCountOptimized:function(){return(0,o.default)(r.default.mark((function e(){var t;return r.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,u.callCloudFunction)("hygiene-location-management",{action:"getLocationConfig"});case 3:if(t=e.sent,!(t.success&&t.data&&t.data.locations)){e.next=6;break}return e.abrupt("return",t.data.locations.reduce((function(e,t){return e+(t.items?t.items.length:0)}),0));case 6:return e.abrupt("return",0);case 9:return e.prev=9,e.t0=e["catch"](0),e.abrupt("return",0);case 12:case"end":return e.stop()}}),e,null,[[0,9]])})))()},loadEmployeeAssignmentCountOptimized:function(){var e=this;return(0,o.default)(r.default.mark((function t(){var a;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,u.callCloudFunction)("hygiene-assignments",{action:"getAssignmentList",data:{pageSize:1e3}});case 3:if(a=t.sent,!a.success||!Array.isArray(a.data)){t.next=6;break}return t.abrupt("return",e.processAssignmentData(a.data));case 6:return t.abrupt("return",{employees:0,assignments:0});case 9:return t.prev=9,t.t0=t["catch"](0),t.abrupt("return",{employees:0,assignments:0});case 12:case"end":return t.stop()}}),t,null,[[0,9]])})))()},processAssignmentData:function(e){var t=e.map((function(e){var t,a;return e.employee_id||(null===(t=e.employee)||void 0===t?void 0:t.id)||(null===(a=e.employee)||void 0===a?void 0:a._id)})).filter(Boolean);return{employees:new Set(t).size,assignments:e.length}},manageFixedAreas:function(){e.navigateTo({url:"/pages/6s_pkg/fixed-area-manage"})},managePublicAreas:function(){e.navigateTo({url:"/pages/6s_pkg/public-area-manage"})},manageLocations:function(){e.navigateTo({url:"/pages/6s_pkg/location-manage"})},manageAssignment:function(){e.navigateTo({url:"/pages/6s_pkg/employee-assignment"})},managePublicSchedule:function(){e.navigateTo({url:"/pages/6s_pkg/public-schedule"})},isCacheValid:function(){return!(!this.processCache.overviewData||!this.processCache.cacheTimestamp)&&Date.now()-this.processCache.cacheTimestamp<this.processCache.cacheExpiry},isCacheExpired:function(){return!this.processCache.cacheTimestamp||Date.now()-this.processCache.cacheTimestamp>=this.processCache.cacheExpiry},updateCache:function(e){this.processCache.overviewData=function(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?c(Object(a),!0).forEach((function(t){(0,i.default)(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):c(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}({},e),this.processCache.cacheTimestamp=Date.now()},clearCache:function(){this.processCache.overviewData=null,this.processCache.cacheTimestamp=null},handleLoadError:function(t){this.loadError=!0,e.showToast({title:"加载数据失败",icon:"none",duration:2e3})},retryLoad:function(){this.clearCache(),this.loadOverviewDataOptimized()}}};t.default=l}).call(this,a("df3c")["default"])},"937a":function(e,t,a){},"99b1":function(e,t,a){"use strict";(function(e,t){var n=a("47a9");a("357b"),a("861b");n(a("3240"));var r=n(a("4a91"));e.__webpack_require_UNI_MP_PLUGIN__=a,t(r.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])}},[["99b1","common/runtime","common/vendor"]]]);