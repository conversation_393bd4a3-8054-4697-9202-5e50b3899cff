{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s_pkg/monthly-check.vue?c4cc", "webpack:///D:/Xwzc/pages/6s_pkg/monthly-check.vue?643c", "webpack:///D:/Xwzc/pages/6s_pkg/monthly-check.vue?a274", "webpack:///D:/Xwzc/pages/6s_pkg/monthly-check.vue?913b", "uni-app:///pages/6s_pkg/monthly-check.vue", "webpack:///D:/Xwzc/pages/6s_pkg/monthly-check.vue?ca29", "webpack:///D:/Xwzc/pages/6s_pkg/monthly-check.vue?b334"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "loading", "loadingText", "selected<PERSON><PERSON><PERSON><PERSON>er", "currentStatusFilter", "displayLimit", "disableAnimations", "timeOptions", "statusFilters", "label", "value", "hasManagePermission", "monthlyIssues", "monthlyIssuesTotal", "<PERSON><PERSON><PERSON><PERSON>", "dataCache", "cacheExpireTime", "needsRefresh", "config", "maxPageSize", "defaultDeadlineDays", "adminUsernames", "computed", "totalIssues", "completedIssues", "issue", "inProgressIssues", "overdueIssues", "pendingIssues", "completionRate", "filteredIssues", "displayedIssues", "has<PERSON><PERSON><PERSON><PERSON><PERSON>", "remainingIssuesCount", "onLoad", "uni", "onUnload", "onShow", "methods", "initPage", "loadData", "cache<PERSON>ey", "cachedData", "Promise", "issuesResult", "statsResult", "issues", "issuesTotal", "timestamp", "loadMonthlyIssues", "require", "timeRange", "callCloudFunction", "action", "start_date", "end_date", "page", "pageSize", "result", "mappedIssues", "id", "number", "title", "status", "location", "responsible", "responsibleId", "role", "deadline", "priority", "description", "createdAt", "updatedAt", "images", "history", "loadMonthlyStats", "getTimeRangeForAPI", "start", "end", "mapIssueStatus", "getDefaultDeadline", "date", "formatLocation", "updateStatsFromAPI", "generateIssueHistory", "time", "operator", "formatHistoryTime", "removeDuplicateIssues", "issueMap", "calculateResponsiblePersons", "personMap", "total", "completed", "person", "showTimeSelector", "closeTimeSelector", "selectTimeFilter", "refreshData", "icon", "loadDataSilently", "changeStatusFilter", "getCurrentTimeRange", "current", "last", "quarter", "year", "getSummaryTitle", "getIssueTitle", "getIssueStatusText", "getStatusFilterText", "getProgressWidth", "getProgressPercentage", "addNewIssue", "url", "manageIssues", "exportReport", "time_filter", "viewIssueDetail", "viewPersonProgress", "showMoreIssues", "setTimeout", "handleDataUpdated", "refreshDataSilently", "resolve", "checkUserPermissions", "userInfo", "userRoleStr", "userRole", "roles", "updateTimeOptions", "desc", "showLoading", "hideLoading", "showError", "duration", "delay", "formatDeadline", "formatLocalDate"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1GA;AAAA;AAAA;AAAA;AAAumB,CAAgB,ioBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC4O3nB;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,gBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;IACA;EACA;;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;QAAA,OACAC;MAAA,EACA;IACA;IACAC;MACA;MACA;QAAA;MAAA;IACA;IACAC;MACA;QAAA;MAAA;IACA;IACAC;MACA;MACA;QAAA,OACAH;MAAA,EACA;IACA;IACAI;MACA;MACA;MACA;QAAA;MAAA;MACA;IACA;IACAC;MAAA;MACA;QACA;MACA;MACA;QAAA;MAAA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;;IAEA;IACAC;IACAA;IACAA;IACAA;EACA;EAEAC;IACA;IACAD;IACAA;IACAA;IACAA;EACA;EACAE;IACA;IACA;;IAEA;IACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBACAC,6CAEA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAKAC,aACA,4BACA,0BACA;cAAA;gBAAA;gBAAA;gBAHAC;gBAAAC;gBAKA;gBACA;gBACA;kBACA;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;kBACAC;kBACAC;kBACAjC;kBACAkC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;gBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,WACAC;gBAEAC;gBAAA;gBAAA,OAEAC;kBACAC;kBACArD;oBACAsD;oBACAC;oBACAC;oBACAC;kBACA;gBACA;cAAA;gBARAC;gBAAA,MAUAA;kBAAA;kBAAA;gBAAA;gBACAC;kBAAA;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBAAA;oBACAC;oBACAC,2CACA,uEACA;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;sBAAA;oBAAA;oBACAC;kBACA;gBAAA,IAEA;gBACA;gBAAA,kCAEAf;cAAA;gBAAA,kCAGA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAgB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,YACAzB;gBAAA;gBAGAC;gBAAA;gBAAA,OAEAC;kBACAC;kBACArD;oBACAsD;oBACAC;kBACA;gBACA;cAAA;gBANAG;gBAAA,MAQAA;kBAAA;kBAAA;gBAAA;gBAAA,kCACAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,kCAMA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAkB;MACA;MACA;MAEA;QACA;UACA;UACAC;UACA;UACAC;UACA;QACA;UACA;UACA;UACA;UACAD;UACA;UACAC;UACA;QACA;UACA;UACA;UACAD;UACA;UACAC;UACA;QACA;UACA;UACAD;UACA;UACAC;UACA;QACA;UACA;UACAD;UACAC;MAAA;;MAGA;MACA;MACA;MAEA;QACAD;QACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAC;MACA;IACA;IAEA;IACAC;MACA;MAEA;;MAEA;MACA;QACAlB;MACA;MAEA;IACA;IAEA;IACAmB;MACA;MACA;IAAA,CACA;IAEA;IACAC;MACA;MAEA;QACAV;UACArB;UACAiB;UACAe;UACAC;QACA;MACA;MAEA;QACAZ;UACArB;UACAiB;UACAe;UACAC;QACA;MACA;;MAEA;MACA;QACAZ,aACA;UACArB;UACAiB;UACAe;UACAC;QACA,GACA;UACAjC;UACAiB;UACAe;UACAC;QACA,EACA;MACA;QACAZ;UACArB;UACAiB;UACAe;UACAC;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MAEA;QACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;QACAC;MACA;MACA;IACA;IAEAC;MACA;MAEA;QACA;QACA;UACA;QACA;QAEA;QACA;UACAC;YACA/B;YAAA;YACA7D;YAAA;YACAoE;YACAyB;YACAC;YACA/C;UACA;QACA;QAEA;QACAgD;QACAA;QACA;QACA;UACAA;QACA;MACA;MAEA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAEA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBACA/D;kBACA2B;kBACAqC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IAEAC;MACA;;MAEA;MACA;IACA;IAEAC;MACA;QACAC;QACAC;QACAC;QACAC;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MAEA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA9E;QACA+E;MACA;IACA;IAEAC;MACAhF;QACA+E;MACA;IACA;IAEAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBAAA,YAEAlE;gBACAC;gBAAA;gBAAA,OAEAC;kBACAC;kBACArD;oBACAsD;oBACAC;oBACA8D;kBACA;gBACA;cAAA;gBAPA3D;gBAAA,MASAA;kBAAA;kBAAA;gBAAA;gBACAvB;kBACA2B;kBACAqC;gBACA;;gBAEA;gBACA;kBACA;gBAAA;gBACA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAhE;kBACA2B;kBACAqC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAmB;MACA;MACAnF;MACAA;QACA+E;MACA;IACA;IAEAK;MACApF;QACA+E;MACA;IACA;IAEAM;MAAA;MACA;MACA;;MAEA;MACA;MAEA;;MAEA;MACAC;QACA;MACA;IACA;IAEA;IAEA;IACAC;MAAA;MACA;MACA;QACA;MACA;;MAEA;MACAD;QACA;MACA;IACA;IAEA;IACAE;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGA;gBACA;;gBAEA;gBACA;gBAAA;gBAAA,OACA;kBACAF;oBAAA;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;4BAAA;4BAAA,OAGA9E,aACA,6BACA,2BACA;0BAAA;4BAAA;4BAAA;4BAHAC;4BAAAC;4BAKA;4BACA;;4BAEA;4BACA;8BACA;4BACA;;4BAEA;4BACA;8BACA;4BACA;;4BAEA;4BACA;;4BAEA;4BACA;;4BAEA;4BACAJ;4BACA;8BACAK;8BACAC;8BACAjC;8BACAkC;4BACA;4BAEA4E;4BAAA;4BAAA;0BAAA;4BAAA;4BAAA;4BAEAA;0BAAA;0BAAA;4BAAA;wBAAA;sBAAA;oBAAA;kBAAA,CAEA;gBACA;cAAA;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBAAA,YACA3E,yHAEA;kBACA4E,8DAEA;kBACAC;kBACAC;kBAEA;oBACA;sBACAA;oBACA;sBACAA;oBACA;kBACA;oBACAA;kBACA;;kBAEA;kBACAC;kBACA;oBACAA;kBACA;oBACA;oBACAA;kBACA;oBACA;oBACAA;kBACA;;kBAEA;kBACA;gBAEA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAC;MACA;MACA;MACA;MACA;MAEA,oBACA;QACAzH;QACAC;QACAyF;QACAgC;MACA,GACA;QACA1H;QACAC;QACAyF;QACAgC;MACA,GACA;QACA1H;QACAC;QACAyF;QACAgC;MACA,GACA;QACA1H;QACAC;QACAyF;QACAgC;MACA,EACA;IACA;IAEAC;MAAA;MACA;MACA;IAEA;IAEAC;MACA;IAEA;IAEAC;MACAnG;QACA2B;QACAqC;QACAoC;MACA;IACA;IAEAC;MACA;QAAA;MAAA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACljCA;AAAA;AAAA;AAAA;AAA0qC,CAAgB,gpCAAG,EAAC,C;;;;;;;;;;;ACA9rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/monthly-check.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/monthly-check.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./monthly-check.vue?vue&type=template&id=cd5e3c08&scoped=true&\"\nvar renderjs\nimport script from \"./monthly-check.vue?vue&type=script&lang=js&\"\nexport * from \"./monthly-check.vue?vue&type=script&lang=js&\"\nimport style0 from \"./monthly-check.vue?vue&type=style&index=0&id=cd5e3c08&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"cd5e3c08\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/monthly-check.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./monthly-check.vue?vue&type=template&id=cd5e3c08&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getSummaryTitle()\n  var m1 = _vm.getCurrentTimeRange()\n  var m2 = _vm.getIssueTitle()\n  var g0 = _vm.filteredIssues.length\n  var l0 =\n    g0 > 0\n      ? _vm.__map(_vm.displayedIssues, function (issue, index) {\n          var $orig = _vm.__get_orig(issue)\n          var g1 = String(index + 1).padStart(2, \"0\")\n          var m3 = _vm.formatDeadline(issue.deadline)\n          var m4 = _vm.getIssueStatusText(issue.status)\n          return {\n            $orig: $orig,\n            g1: g1,\n            m3: m3,\n            m4: m4,\n          }\n        })\n      : null\n  var m5 = !(g0 > 0)\n    ? _vm.getStatusFilterText(_vm.currentStatusFilter) || \"\"\n    : null\n  var g2 = _vm.responsiblePersons.length\n  var l2 =\n    g2 > 0\n      ? _vm.__map(_vm.responsiblePersons, function (person, personIndex) {\n          var $orig = _vm.__get_orig(person)\n          var m6 = _vm.getProgressPercentage(person)\n          var m7 = _vm.getProgressPercentage(person)\n          var m8 = _vm.getProgressPercentage(person)\n          var m9 = _vm.getProgressWidth(person)\n          var g3 = person.issues && person.issues.length > 0\n          var l1 = g3 ? person.issues.slice(0, 2) : null\n          var g4 = g3 ? person.issues.length : null\n          var g5 = g3 && g4 > 2 ? person.issues.length : null\n          return {\n            $orig: $orig,\n            m6: m6,\n            m7: m7,\n            m8: m8,\n            m9: m9,\n            g3: g3,\n            l1: l1,\n            g4: g4,\n            g5: g5,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        g0: g0,\n        l0: l0,\n        m5: m5,\n        g2: g2,\n        l2: l2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./monthly-check.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./monthly-check.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page-container\">\n    <!-- 月度汇总 -->\n    <view class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"header-content\">\n          <view class=\"card-title\">{{ getSummaryTitle() }}</view>\n          <view class=\"time-selector\" @click=\"showTimeSelector\">\n            <text class=\"time-text\">{{ getCurrentTimeRange() }}</text>\n            <uni-icons type=\"down\" size=\"12\" color=\"#007AFF\"></uni-icons>\n          </view>\n        </view>\n      </view>\n      <view class=\"card-body\">\n        <view class=\"stats-grid\">\n          <view class=\"stats-item\">\n            <view class=\"stats-number primary\">{{ totalIssues }}</view>\n            <view class=\"stats-label\">发现问题</view>\n          </view>\n          <view class=\"stats-item\">\n            <view class=\"stats-number success\">{{ completedIssues }}</view>\n            <view class=\"stats-label\">已整改</view>\n          </view>\n          <view class=\"stats-item\">\n            <view class=\"stats-number warning\">{{ inProgressIssues }}</view>\n            <view class=\"stats-label\">整改中</view>\n          </view>\n          <view class=\"stats-item\">\n            <view class=\"stats-number danger\">{{ overdueIssues }}</view>\n            <view class=\"stats-label\">逾期未改</view>\n          </view>\n        </view>\n        \n        <!-- 整改率显示 -->\n        <view class=\"completion-rate\">\n          <view class=\"rate-value\">{{ completionRate }}%</view>\n          <view class=\"rate-bar\">\n            <view class=\"rate-fill\" :style=\"{ width: completionRate + '%' }\"></view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 月度管理 -->\n    <view class=\"card\" v-if=\"hasManagePermission\">\n      <view class=\"card-header\">\n        <view class=\"header-content\">\n          <view class=\"management-title-group\">\n            <view class=\"management-icon\">\n              <uni-icons type=\"gear-filled\" size=\"20\" color=\"#007AFF\"></uni-icons>\n            </view>\n            <view class=\"management-text\">\n              <view class=\"card-title\">月度管理</view>\n              <view class=\"card-subtitle\">管理员专属操作面板</view>\n            </view>\n          </view>\n        </view>\n      </view>\n      <view class=\"card-body\">\n        <view class=\"management-actions\">\n          <view class=\"action-btn primary\" @click=\"addNewIssue\">\n            <uni-icons type=\"plus\" size=\"20\" color=\"#ffffff\"></uni-icons>\n            <text>新增问题</text>\n          </view>\n          <view class=\"action-btn secondary\" @click=\"manageIssues\">\n            <uni-icons type=\"list\" size=\"20\" color=\"#007AFF\"></uni-icons>\n            <text>问题管理</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 月度问题跟踪 -->\n    <view class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"header-content\">\n          <view class=\"card-title\">{{ getIssueTitle() }}</view>\n          <view class=\"card-subtitle\">全厂安全卫生检查发现的问题</view>\n        </view>\n        <!-- 筛选按钮 -->\n        <scroll-view class=\"filter-tabs\" scroll-x=\"true\" show-scrollbar=\"false\">\n          <view \n            v-for=\"filter in statusFilters\" \n            :key=\"filter.value\"\n            class=\"filter-tab\"\n            :class=\"{ active: currentStatusFilter === filter.value }\"\n            @click=\"changeStatusFilter(filter.value)\"\n          >\n            {{ filter.label }}\n          </view>\n        </scroll-view>\n      </view>\n\n      <!-- 问题列表 -->\n      <view v-if=\"filteredIssues.length > 0\">\n        <view v-for=\"(issue, index) in displayedIssues\" :key=\"index\" class=\"list-item\" :class=\"{ 'no-animations': disableAnimations }\" @click=\"viewIssueDetail(issue)\">\n          <view class=\"list-item-icon\" :class=\"['icon-bg-' + issue.status]\">\n            <text class=\"issue-number\">{{ String(index + 1).padStart(2, '0') }}</text>\n          </view>\n          <view class=\"list-item-content\">\n            <view class=\"list-item-title\">{{ issue.title }}</view>\n            <view class=\"issue-meta\">\n              <text class=\"meta-item\">{{ issue.location }}</text>\n              <text class=\"meta-separator\">·</text>\n              <text class=\"meta-item\">{{ issue.responsible }}</text>\n              <text class=\"meta-separator\">·</text>\n              <text class=\"meta-item\">{{ formatDeadline(issue.deadline) }}</text>\n            </view>\n          </view>\n          <view class=\"list-item-right\">\n            <view class=\"status-badge\" :class=\"['status-' + issue.status]\">{{ getIssueStatusText(issue.status) }}</view>\n            <uni-icons type=\"right\" size=\"14\" color=\"#C7C7CC\"></uni-icons>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 空状态 -->\n      <p-empty-state\n        v-else\n        useIcon\n        iconName=\"info\"\n        iconColor=\"#C7C7CC\"\n        size=\"large\"\n        :text=\"'暂无' + (getStatusFilterText(currentStatusFilter) || '') + '问题'\"\n      ></p-empty-state>\n\n      <!-- 查看更多按钮 -->\n      <view class=\"card-body\" v-if=\"hasMoreIssues\">\n        <view class=\"more-btn\" @click=\"showMoreIssues\">\n          <uni-icons type=\"down\" size=\"16\" color=\"#007AFF\"></uni-icons>\n          <text>查看更多问题 ({{ remainingIssuesCount }})</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 整改进度跟踪 -->\n    <view class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"header-content\">\n          <view class=\"card-title\">整改进度跟踪</view>\n          <view class=\"card-subtitle\">按负责人分组显示</view>\n        </view>\n      </view>\n\n      <!-- 有负责人数据时显示 -->\n      <view v-if=\"responsiblePersons.length > 0\">\n      <view v-for=\"(person, personIndex) in responsiblePersons\" :key=\"personIndex\" class=\"progress-item\" @click=\"viewPersonProgress(person)\">\n        <view class=\"progress-header\">\n          <view class=\"person-info\">\n            <view class=\"person-avatar\">\n              <uni-icons type=\"person\" size=\"16\" color=\"#007AFF\"></uni-icons>\n            </view>\n            <view class=\"person-details\">\n              <text class=\"person-name\">{{ person.name }}</text>\n              <text class=\"person-role\">{{ person.role }}</text>\n            </view>\n          </view>\n          <view class=\"progress-stats\">\n            <text class=\"progress-text\">{{ person.completed }}/{{ person.total }}</text>\n            <text class=\"progress-percent\">{{ getProgressPercentage(person) }}%</text>\n          </view>\n        </view>\n        <view class=\"progress-bar\">\n          <view class=\"progress-fill\" :class=\"{ 'approved': getProgressPercentage(person) === 100, 'empty': getProgressPercentage(person) === 0 }\" :style=\"{ width: getProgressWidth(person) }\"></view>\n        </view>\n        <!-- 责任问题列表预览 -->\n        <view class=\"person-issues\" v-if=\"person.issues && person.issues.length > 0\">\n          <view v-for=\"(issue, issueIndex) in person.issues.slice(0, 2)\" :key=\"issueIndex\" class=\"person-issue\">\n            <view class=\"issue-dot\" :class=\"['dot-' + issue.status]\"></view>\n            <text class=\"issue-title\">{{ issue.title }}</text>\n          </view>\n          <view v-if=\"person.issues.length > 2\" class=\"more-issues\">\n            <text>还有{{ person.issues.length - 2 }}个问题...</text>\n          </view>\n        </view>\n      </view>\n      </view>\n      \n      <!-- 空状态显示 -->\n      <p-empty-state\n        v-else\n        useIcon\n        iconName=\"person\"\n        iconColor=\"#C7C7CC\"\n        size=\"large\"\n        text=\"暂无负责人进度数据\"\n        subText=\"当有月度检查问题分配给负责人时，这里会显示进度统计\"\n      ></p-empty-state>\n    </view>\n    \n    <!-- 底部安全间距 -->\n    <view class=\"bottom-safe-area\"></view>\n\n    <!-- 时间选择弹窗 -->\n    <uni-popup ref=\"timePopup\" type=\"bottom\" border-radius=\"16rpx 16rpx 0 0\">\n      <view class=\"time-popup\">\n        <view class=\"popup-header\">\n          <text class=\"popup-title\">选择时间范围</text>\n          <view class=\"popup-close\" @click=\"closeTimeSelector\">\n            <uni-icons type=\"close\" size=\"18\" color=\"#8E8E93\"></uni-icons>\n          </view>\n        </view>\n        <view class=\"time-options\">\n          <view \n            v-for=\"option in timeOptions\" \n            :key=\"option.value\"\n            class=\"time-option\"\n            :class=\"{ active: selectedTimeFilter === option.value }\"\n            @click=\"selectTimeFilter(option)\"\n          >\n            <view class=\"option-left\">\n              <uni-icons :type=\"option.icon\" size=\"18\" :color=\"selectedTimeFilter === option.value ? '#007AFF' : '#8E8E93'\"></uni-icons>\n              <view class=\"option-content\">\n                <text class=\"option-text\">{{ option.label }}</text>\n                <text class=\"option-desc\">{{ option.desc }}</text>\n              </view>\n            </view>\n            <view v-if=\"selectedTimeFilter === option.value\" class=\"option-check\">\n              <uni-icons type=\"checkmarkempty\" size=\"16\" color=\"#007AFF\"></uni-icons>\n            </view>\n          </view>\n        </view>\n      </view>\n    </uni-popup>\n\n    <!-- 加载状态 - 自定义遮罩 -->\n    <view v-if=\"loading\" class=\"custom-loading-mask\">\n      <view class=\"loading-container-enhanced\">\n        <uni-icons type=\"spinner-cycle\" size=\"48\" color=\"#007AFF\"></uni-icons>\n        <text class=\"loading-text-enhanced\">{{ loadingText }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'MonthlyCheck',\n  data() {\n    return {\n      loading: false,\n      loadingText: '加载中...',\nselectedTimeFilter: 'current',\n      currentStatusFilter: 'all',\n      displayLimit: 4,\n      disableAnimations: false,\n      timeOptions: [],\n      statusFilters: [\n        { label: '全部', value: 'all' },\n        { label: '已分配', value: 'assigned' },\n        { label: '待整改', value: 'pending' },\n        { label: '整改中', value: 'in_progress' },\n        { label: '待检查', value: 'pending_review' },\n        { label: '检查通过', value: 'approved' }\n      ],\n      // 当前用户权限\n      hasManagePermission: false,\n      // 月度问题数据\n      monthlyIssues: [],\n      // 月度问题总数（用于分页和统计）\n      monthlyIssuesTotal: 0,\n      // 负责人进度数据\n      responsiblePersons: [],\n      // 数据缓存\n      dataCache: new Map(),\n      cacheExpireTime: 5 * 60 * 1000, // 5分钟缓存\n      needsRefresh: false, // 标记是否需要刷新数据\n      // 配置项\n      config: {\n        maxPageSize: 1000, // API单次最大获取数据量\n        defaultDeadlineDays: 7, // 默认截止日期（天数）\n        adminUsernames: ['admin'] // 管理员用户名列表\n      }\n    }\n  },\n  computed: {\n    totalIssues() {\n      return this.monthlyIssues.length\n    },\n    completedIssues() {\n      // 已完成：检查通过\n      return this.monthlyIssues.filter(issue => \n        issue.status === 'approved'\n      ).length\n    },\n    inProgressIssues() {\n      // 整改中：只统计正在进行整改的问题\n      return this.monthlyIssues.filter(issue => issue.status === 'in_progress').length\n    },\n    overdueIssues() {\n      return this.monthlyIssues.filter(issue => issue.status === 'overdue').length\n    },\n    pendingIssues() {\n      // 待处理问题：已分配、待整改、被驳回\n      return this.monthlyIssues.filter(issue => \n        issue.status === 'assigned' || issue.status === 'pending' || issue.status === 'rejected'\n      ).length\n    },\n    completionRate() {\n      if (this.totalIssues === 0) return 0\n      // 完成率基于检查通过的问题\n      const approvedIssues = this.monthlyIssues.filter(issue => issue.status === 'approved').length\n      return Math.round((approvedIssues / this.totalIssues) * 100)\n    },\n    filteredIssues() {\n      if (this.currentStatusFilter === 'all') {\n        return this.monthlyIssues\n      }\n      return this.monthlyIssues.filter(issue => issue.status === this.currentStatusFilter)\n    },\n    displayedIssues() {\n      return this.filteredIssues.slice(0, this.displayLimit)\n    },\n    hasMoreIssues() {\n      return this.filteredIssues.length > this.displayLimit\n    },\n    remainingIssuesCount() {\n      return this.filteredIssues.length - this.displayLimit\n    }\n  },\n  onLoad() {\n    this.initPage()\n    this.checkUserPermissions()\n    \n    // 监听数据更新事件\n    uni.$on('cleaningRecordUpdated', this.handleDataUpdated);\n    uni.$on('rectificationRecordUpdated', this.handleDataUpdated);\n    uni.$on('monthlyIssueUpdated', this.handleDataUpdated);\n    uni.$on('issueDraftUpdated', this.handleDataUpdated);\n  },\n\n  onUnload() {\n    // 移除事件监听\n    uni.$off('cleaningRecordUpdated', this.handleDataUpdated);\n    uni.$off('rectificationRecordUpdated', this.handleDataUpdated);\n    uni.$off('monthlyIssueUpdated', this.handleDataUpdated);\n    uni.$off('issueDraftUpdated', this.handleDataUpdated);\n  },\n  onShow() {\n    // 页面显示时检查权限和刷新必要数据\n    this.checkUserPermissions();\n    \n    // 静默加载数据（如果数据为空或缓存过期）\n    if (this.monthlyIssues.length === 0 && !this.loading) {\n      this.loadDataSilently();\n    }\n  },\n  methods: {\n    async initPage() {\n      this.showLoading('初始化页面...')\n      try {\n        await this.loadData()\n        this.updateTimeOptions()\n      } catch (error) {\n        this.showError('页面初始化失败')\n      } finally {\n        this.hideLoading()\n      }\n    },\n\n    async loadData() {\n      try {\n        const cacheKey = `monthly_data_${this.selectedTimeFilter}`;\n        const cachedData = this.dataCache.get(cacheKey);\n        \n        // 检查缓存是否有效\n        if (cachedData && (Date.now() - cachedData.timestamp) < this.cacheExpireTime) {\n          this.monthlyIssues = cachedData.issues;\n          this.monthlyIssuesTotal = cachedData.issuesTotal || cachedData.issues.length;\n          this.responsiblePersons = cachedData.responsiblePersons;\n          return;\n        }\n        \n        // 并行调用多个API获取月度数据\n        const [issuesResult, statsResult] = await Promise.all([\n          this.loadMonthlyIssues(),\n          this.loadMonthlyStats()\n        ]);\n        \n        this.monthlyIssues = issuesResult || [];\n        // 根据实际数据更新统计信息\n        if (statsResult) {\n          this.updateStatsFromAPI(statsResult);\n        }\n        \n        // 对问题数据进行去重（基于id）\n        this.removeDuplicateIssues();\n        \n        // 计算负责人进度数据\n        this.calculateResponsiblePersons();\n        \n        // 缓存数据\n        this.dataCache.set(cacheKey, {\n          issues: this.monthlyIssues,\n          issuesTotal: this.monthlyIssuesTotal,\n          responsiblePersons: this.responsiblePersons,\n          timestamp: Date.now()\n        });\n        \n      } catch (error) {\n        // API失败时使用空数据，让用户看到空状态\n        this.monthlyIssues = [];\n        this.responsiblePersons = [];\n        // 显示错误提示\n        this.showError('无法加载月度数据，请检查网络连接');\n      }\n    },\n\n    // 加载月度问题数据\n    async loadMonthlyIssues() {\n      const { callCloudFunction } = require('@/utils/auth.js');\n      \n      const timeRange = this.getTimeRangeForAPI();\n      \n      const result = await callCloudFunction('hygiene-monthly-inspection', {\n        action: 'getMonthlyIssues',\n        data: {\n          start_date: timeRange.start,\n          end_date: timeRange.end,\n          page: 1,\n          pageSize: this.config.maxPageSize\n        }\n      });      \n      \n      if (result && result.success && result.data && result.data.list) {\n        const mappedIssues = result.data.list.map((issue, index) => ({\n          id: issue._id || issue.id,\n          number: index + 1,\n          title: issue.title,\n          status: this.mapIssueStatus(issue.status),\n          location: this.formatLocation(issue.location_info),\n          responsible: issue.assigned_to_name || '未分配',\n          responsibleId: issue.assigned_to || null, // 保存真实的用户ID\n          role: '负责人',\n          deadline: issue.expected_completion_date ? \n            new Date(issue.expected_completion_date).toISOString().split('T')[0] : \n            this.getDefaultDeadline(),\n          priority: issue.priority || 'normal',\n          description: issue.description,\n          createdAt: issue.created_at,\n          updatedAt: issue.updated_at,\n          images: issue.photos ? issue.photos.map(p => p.url || p) : [],\n          history: this.generateIssueHistory(issue)\n        }));\n        \n        // 保存总数信息\n        this.monthlyIssuesTotal = result.data.total || mappedIssues.length;\n        \n        return mappedIssues;\n      }\n      \n      return [];\n    },\n\n    // 加载月度统计数据\n    async loadMonthlyStats() {\n      const { callCloudFunction } = require('@/utils/auth.js');\n      \n      try {\n        const timeRange = this.getTimeRangeForAPI();\n        \n        const result = await callCloudFunction('hygiene-monthly-inspection', {\n          action: 'getMonthlyStatistics',\n          data: {\n            start_date: timeRange.start,\n            end_date: timeRange.end\n          }\n        });\n        \n        if (result && result.success && result.data) {\n          return result.data;\n        }\n      } catch (error) {\n        // 获取月度统计数据失败\n      }\n      \n      return null;\n    },\n\n    // 获取API调用的时间范围\n    getTimeRangeForAPI() {\n      const now = new Date();\n      let start, end;\n      \n      switch (this.selectedTimeFilter) {\n        case 'current':\n          // 当月第一天 00:00:00\n          start = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);\n          // 当月最后一天 23:59:59\n          end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);\n          break;\n        case 'last':\n          // 上月第一天 00:00:00\n          const lastMonthYear = now.getMonth() === 0 ? now.getFullYear() - 1 : now.getFullYear();\n          const lastMonth = now.getMonth() === 0 ? 11 : now.getMonth() - 1;\n          start = new Date(lastMonthYear, lastMonth, 1, 0, 0, 0, 0);\n          // 上月最后一天 23:59:59\n          end = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59, 999);\n          break;\n        case 'quarter':\n          // 当前季度第一天 00:00:00\n          const quarter = Math.floor(now.getMonth() / 3);\n          start = new Date(now.getFullYear(), quarter * 3, 1, 0, 0, 0, 0);\n          // 当前季度最后一天 23:59:59\n          end = new Date(now.getFullYear(), quarter * 3 + 3, 0, 23, 59, 59, 999);\n          break;\n        case 'year':\n          // 当年第一天 00:00:00\n          start = new Date(now.getFullYear(), 0, 1, 0, 0, 0, 0);\n          // 当年最后一天 23:59:59\n          end = new Date(now.getFullYear(), 11, 31, 23, 59, 59, 999);\n          break;\n        default:\n          // 默认为当月\n          start = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);\n          end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);\n      }\n      \n      // 转换为本地日期字符串（避免时区问题）\n      const startDate = this.formatLocalDate(start);\n      const endDate = this.formatLocalDate(end);\n      \n      return {\n        start: startDate,\n        end: endDate\n      };\n    },\n\n    // 映射问题状态 - 与issue-detail.vue保持一致\n    mapIssueStatus(apiStatus) {\n      const statusMap = {\n        'submitted': 'pending',\n        'assigned': 'assigned',     // 修复：已分配状态应该保持\n        'pending': 'pending',       // 待整改\n        'in_progress': 'in_progress', // 整改中\n        'pending_review': 'pending_review', // 待检查\n        'approved': 'approved',     // 检查通过\n        'closed': 'closed',         // 已关闭\n        'resolved': 'approved',     // API的resolved映射为approved\n        'overdue': 'overdue',\n        'rejected': 'rejected',     // 修复：被驳回状态应该保持\n        'reopened': 'pending',      // 重新打开映射为待整改\n        'draft': 'pending'\n      };\n      return statusMap[apiStatus] || 'pending';\n    },\n\n    // 获取默认截止日期\n    getDefaultDeadline() {\n      const date = new Date();\n      date.setDate(date.getDate() + this.config.defaultDeadlineDays);\n      return date.toISOString().split('T')[0];\n    },\n\n    // 格式化位置信息\n    formatLocation(locationInfo) {\n      if (!locationInfo) return '未知位置';\n      \n      let location = locationInfo.location_name || '未知位置';\n      \n      // 如果有分类信息，添加到前面\n      if (locationInfo.location_category) {\n        location = `${locationInfo.location_category} - ${location}`;\n      }\n      \n      return location;\n    },\n\n    // 根据API数据更新统计信息\n    updateStatsFromAPI(apiStats) {\n      // 如果API返回了统计数据，使用API数据而不是计算值\n      // 这样可以保持页面原有的显示逻辑\n    },\n\n    // 生成问题历史记录\n    generateIssueHistory(issue) {\n      const history = [];\n      \n      if (issue.created_at) {\n        history.push({\n          action: '创建问题',\n          description: '6S检查员发现问题并记录',\n          time: this.formatHistoryTime(issue.created_at),\n          operator: issue.reporter_name || '检查员'\n        });\n      }\n      \n      if (issue.assigned_to_name) {\n        history.push({\n          action: '分配负责人',\n          description: `将问题分配给${issue.assigned_to_name}处理`,\n          time: this.formatHistoryTime(issue.updated_at || issue.created_at),\n          operator: '管理员'\n        });\n      }\n      \n      // 根据状态添加相应历史\n      if (issue.status === 'resolved' || issue.status === 'approved') {\n        history.push(\n            {\n              action: '开始整改',\n            description: '负责人已开始处理此问题',\n            time: this.formatHistoryTime(issue.updated_at),\n            operator: issue.assigned_to_name || '负责人'\n            },\n            {\n              action: '整改完成',\n            description: '整改措施已实施完成',\n            time: this.formatHistoryTime(issue.actual_completion_date || issue.updated_at),\n            operator: issue.assigned_to_name || '负责人'\n          }\n        );\n      } else if (issue.status === 'in_progress') {\n        history.push({\n          action: '开始整改',\n          description: '负责人已开始处理此问题',\n          time: this.formatHistoryTime(issue.updated_at),\n          operator: issue.assigned_to_name || '负责人'\n        });\n      }\n      \n      return history;\n    },\n\n    // 格式化历史记录时间\n    formatHistoryTime(dateString) {\n      if (!dateString) return '--';\n      \n      try {\n        const date = new Date(dateString);\n        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n      } catch (error) {\n        return '--';\n      }\n    },\n\n    removeDuplicateIssues() {\n      // 使用Map根据id去重\n      const issueMap = new Map()\n      this.monthlyIssues.forEach(issue => {\n        issueMap.set(issue.id, issue)\n      })\n      this.monthlyIssues = Array.from(issueMap.values())\n    },\n\n    calculateResponsiblePersons() {\n      const personMap = new Map()\n      \n      this.monthlyIssues.forEach(issue => {\n        // 只统计有负责人的问题\n        if (!issue.responsibleId || !issue.responsible || issue.responsible === '未分配') {\n          return;\n        }\n        \n        const key = issue.responsibleId // 使用用户ID作为key\n        if (!personMap.has(key)) {\n          personMap.set(key, {\n            id: issue.responsibleId, // 使用真实的用户ID\n            name: issue.responsible, // 显示姓名\n            role: '负责人',\n            total: 0,\n            completed: 0,\n            issues: []\n          })\n        }\n        \n        const person = personMap.get(key)\n        person.total++\n        person.issues.push(issue)\n        // 统计已完成的问题：检查通过才算完成\n        if (issue.status === 'approved') {\n          person.completed++\n        }\n      })\n      \n      this.responsiblePersons = Array.from(personMap.values())\n    },\n\nshowTimeSelector() {\n      this.$refs.timePopup.open()\n    },\n\n    closeTimeSelector() {\n      this.$refs.timePopup.close()\n    },\n\nasync selectTimeFilter(option) {\n      if (this.selectedTimeFilter !== option.value) {\n        this.selectedTimeFilter = option.value\n        this.$refs.timePopup.close()\n        await this.refreshData()\n      } else {\n        this.$refs.timePopup.close()\n      }\n    },\n\n    async refreshData() {\n      this.showLoading('更新数据...')\n      try {\n        // 清除缓存，强制重新加载\n        this.dataCache.clear();\n        await this.loadData()\n        uni.showToast({\n          title: '数据已更新',\n          icon: 'success'\n        })\n      } catch (error) {\n        this.showError('数据更新失败')\n      } finally {\n        this.hideLoading()\n      }\n    },\n\n    async loadDataSilently() {\n      // 静默加载数据，不显示任何提醒\n      try {\n        await this.loadData()\n      } catch (error) {\n        // 静默处理错误，不显示提醒\n      }\n    },\n\n    changeStatusFilter(status) {\n      if (this.currentStatusFilter === status) return; // 避免重复操作\n      \n      this.currentStatusFilter = status\n      this.displayLimit = 4 // 重置显示限制\n    },\n\n    getCurrentTimeRange() {\n      const ranges = {\n        current: '本月',\n        last: '上月',\nquarter: '本季度',\n        year: '本年度'\n      }\n      return ranges[this.selectedTimeFilter] || '本月'\n    },\n\n    getSummaryTitle() {\n      const now = new Date()\n      const year = now.getFullYear()\n      const month = now.getMonth() + 1\n      \n      const filterMap = {\n        'current': `${year}年${month}月汇总`,\n        'last': `${month > 1 ? year : year - 1}年${month > 1 ? month - 1 : 12}月汇总`,\n'quarter': `${year}年Q${Math.ceil(month / 3)}汇总`,\n        'year': `${year}年度汇总`\n      }\n      return filterMap[this.selectedTimeFilter] || '月度汇总'\n    },\n\n    getIssueTitle() {\n      const filterMap = {\n        'current': '本月问题跟踪',\n        'last': '上月问题跟踪',\n        'quarter': '本季度问题跟踪',\n        'year': '本年度问题跟踪'\n      }\n      return filterMap[this.selectedTimeFilter] || '问题跟踪'\n    },\n\n    getIssueStatusText(status) {\n      const statusMap = {\n        'assigned': '已分配',\n        'pending': '待整改',\n        'in_progress': '整改中', \n        'pending_review': '待检查',\n        'approved': '检查通过',\n        'rejected': '已驳回',\n        'overdue': '已逾期',\n        'closed': '已关闭',\n        'reopened': '重新打开'\n      }\n      return statusMap[status] || '未知'\n    },\n\n    getStatusFilterText(status) {\n      const filterMap = {\n        'all': '',\n        'assigned': '已分配',\n        'pending': '待整改',\n        'in_progress': '整改中',\n        'pending_review': '待检查',\n        'approved': '检查通过',\n        'rejected': '已驳回',\n        'overdue': '已逾期'\n      }\n      return filterMap[status] || ''\n    },\n\n    getProgressWidth(person) {\n      const percentage = person.total > 0 ? (person.completed / person.total) * 100 : 0\n      // 如果进度为0%，则不显示进度条\n      return `${percentage}%`\n    },\n\n    getProgressPercentage(person) {\n      return person.total > 0 ? Math.round((person.completed / person.total) * 100) : 0\n    },\n\n    addNewIssue() {\n      uni.navigateTo({\n        url: '/pages/6s_pkg/issue-add'\n      })\n    },\n\n    manageIssues() {\n      uni.navigateTo({\n        url: '/pages/6s_pkg/issue-list'\n      })\n    },\n\n    async exportReport() {\n      try {\n      this.showLoading('生成报告...')\n        \n        const { callCloudFunction } = require('@/utils/auth.js');\n        const timeRange = this.getTimeRangeForAPI();\n        \n        const result = await callCloudFunction('hygiene-monthly-inspection', {\n          action: 'exportMonthlyReport',\n          data: {\n            start_date: timeRange.start,\n            end_date: timeRange.end,\n            time_filter: this.selectedTimeFilter\n          }\n        });\n        \n        if (result && result.success) {\n        uni.showToast({\n          title: '报告已生成',\n          icon: 'success'\n          });\n          \n          // 如果有下载链接，可以触发下载\n          if (result.data && result.data.downloadUrl) {\n            // 可以在这里处理文件下载\n          }\n        } else {\n          throw new Error(result.message || '报告生成失败');\n        }\n        \n      } catch (error) {\n        uni.showToast({\n          title: '报告生成失败',\n          icon: 'error'\n        });\n      } finally {\n        this.hideLoading();\n      }\n    },\n\n    viewIssueDetail(issue) {\n      // 将完整的问题数据存储到本地，供详情页使用\n      uni.setStorageSync(`issue_detail_${issue.id}`, issue);\n      uni.navigateTo({\n        url: `/pages/6s_pkg/issue-detail?id=${issue.id}`\n      })\n    },\n\n    viewPersonProgress(person) {\n      uni.navigateTo({\n        url: `/pages/6s_pkg/person-progress?id=${encodeURIComponent(person.id)}&name=${encodeURIComponent(person.name)}`\n      })\n    },\n\n    showMoreIssues() {\n      // 防止重复点击\n      if (this.disableAnimations) return;\n      \n      // 添加一个临时的CSS类来禁用动画效果\n      this.disableAnimations = true;\n      \n      this.displayLimit += 4;\n      \n      // 短暂延迟后恢复动画效果\n      setTimeout(() => {\n        this.disableAnimations = false;\n      }, 100);\n    },\n\n    // 工具方法\n    \n    // 处理数据更新事件\n    handleDataUpdated(data) {\n      // 清除缓存，确保获取最新数据\n      if (this.dataCache) {\n        this.dataCache.clear();\n      }\n      \n      // 延迟一下再刷新，确保提交操作完全完成\n      setTimeout(() => {\n        this.refreshDataSilently();\n      }, 200);\n    },\n\n    // 静默刷新数据（不显示加载状态）\n    async refreshDataSilently() {\n      if (this.loading) return; // 如果正在加载，跳过\n      \n      try {\n        // 清除缓存，确保获取最新数据\n        this.dataCache.clear();\n        \n        // 静默重新加载数据，不显示loading状态\n        // 使用 setTimeout 避免热更新时的模块引用问题\n        await new Promise(resolve => {\n          setTimeout(async () => {\n            try {\n              // 并行获取数据\n              const [issuesResult, statsResult] = await Promise.all([\n                this.loadMonthlyIssues(),\n                this.loadMonthlyStats()\n              ]);\n              \n              // 更新数据\n              this.monthlyIssues = issuesResult || [];\n              \n              // 如果没有问题，重置总数\n              if (!issuesResult || issuesResult.length === 0) {\n                this.monthlyIssuesTotal = 0;\n              }\n              \n              // 根据实际数据更新统计信息\n              if (statsResult) {\n                this.updateStatsFromAPI(statsResult);\n              }\n              \n              // 对问题数据进行去重（基于id）\n              this.removeDuplicateIssues();\n              \n              // 计算负责人进度数据\n              this.calculateResponsiblePersons();\n              \n              // 缓存数据\n              const cacheKey = `monthly_data_${this.selectedTimeFilter}`;\n              this.dataCache.set(cacheKey, {\n                issues: this.monthlyIssues,\n                issuesTotal: this.monthlyIssuesTotal,\n                responsiblePersons: this.responsiblePersons,\n                timestamp: Date.now()\n              });\n              \n              resolve();\n            } catch (error) {\n              resolve();\n            }\n          }, 100);\n        });\n        \n        // 清除刷新标记，避免用户返回页面时再次显示loading\n        this.needsRefresh = false;\n      } catch (error) {\n        // 静默处理错误，不显示错误提示\n      }\n    },\n    \n    // 检查用户权限\n    async checkUserPermissions() {\n      try {\n        const { getCacheKey, CACHE_KEYS } = require('@/utils/cache.js');\n        \n        // 获取当前用户信息\n        const userInfo = uni.getStorageSync('uni-id-pages-userInfo') || {};\n        \n        // 获取用户角色信息（从专门的角色缓存中获取）\n        let userRoleStr = uni.getStorageSync(getCacheKey(CACHE_KEYS.USER_ROLE)) || '{}';\n        let userRole = {};\n        \n        try {\n          if (typeof userRoleStr === 'string') {\n            userRole = JSON.parse(userRoleStr);\n          } else {\n            userRole = userRoleStr;\n          }\n        } catch (e) {\n          userRole = {};\n        }\n        \n        // 从userRole.value.userRole获取角色数组\n        let roles = [];\n        if (userRole && userRole.value && userRole.value.userRole) {\n          roles = Array.isArray(userRole.value.userRole) ? userRole.value.userRole : [userRole.value.userRole];\n        } else if (userInfo.role) {\n          // 备用方案：从用户信息中获取角色\n          roles = Array.isArray(userInfo.role) ? userInfo.role : [userInfo.role];\n        } else if (this.config.adminUsernames.includes(userInfo.username)) {\n          // 特殊处理：如果用户名在管理员列表中，给予admin角色\n          roles = ['admin'];\n        }\n        \n        // 月度检查管理权限：管理员或检查员\n        this.hasManagePermission = roles.includes('admin') || roles.includes('Integrated') || roles.includes('reviser');\n        \n      } catch (error) {\n        this.hasManagePermission = false;\n      }\n    },\n\n    updateTimeOptions() {\n      const now = new Date()\n      const year = now.getFullYear()\n      const month = now.getMonth() + 1\n      const quarter = Math.ceil(month / 3)\n      \n      this.timeOptions = [\n        { \n          label: '本月', \n          value: 'current', \n          icon: 'calendar',\n          desc: `${year}年${month}月`\n        },\n        { \n          label: '上月', \n          value: 'last', \n          icon: 'calendar',\n          desc: `${month > 1 ? year : year - 1}年${month > 1 ? month - 1 : 12}月`\n        },\n        { \n          label: '本季度', \n          value: 'quarter', \n          icon: 'calendar',\n          desc: `${year}年Q${quarter}`\n        },\n        { \n          label: '本年度', \n          value: 'year', \n          icon: 'calendar',\n          desc: `${year}年`\n        }\n      ]\n    },\n\n    showLoading(text = '加载中...') {\n      this.loading = true\n      this.loadingText = text\n\n    },\n\n    hideLoading() {\n      this.loading = false\n\n    },\n\n    showError(message) {\n      uni.showToast({\n        title: message,\n        icon: 'none',\n        duration: 3000\n      })\n    },\n\n    delay(ms) {\n      return new Promise(resolve => setTimeout(resolve, ms))\n    },\n\n    formatDeadline(deadline) {\n      const date = new Date(deadline);\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      return `${year}-${month}-${day}`;\n    },\n\n    // 格式化本地日期为YYYY-MM-DD格式（避免时区问题）\n    formatLocalDate(date) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      return `${year}-${month}-${day}`;\n    },\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\n\twidth: 100%;\n\toverflow-x: hidden;\n\tbox-sizing: border-box;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;\n}\n\n/* 卡片样式 */\n.card {\n  background: #ffffff;\n  border-radius: 24rpx;\n  margin: 0 32rpx 24rpx 32rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08), \n              0 2rpx 6rpx rgba(0, 0, 0, 0.04);\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\n}\n\n.card:first-child {\n  margin-top: 24rpx;\n}\n\n.card-header {\n  padding: 32rpx 32rpx 24rpx 32rpx;\n  border-bottom: 1rpx solid #F0F0F0;\n}\n\n.card-body {\n  padding: 28rpx;\n}\n\n.header-content {\n  display: flex;\n  flex-direction: row;\n  align-items: baseline;\n  justify-content: space-between;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 0;\n}\n\n.card-subtitle {\n  font-size: 26rpx;\n  color: #8E8E93;\n}\n\n.management-title-group {\n  display: flex;\n  align-items: center;\n  gap: 16rpx;\n}\n\n.management-icon {\n  width: 72rpx;\n  height: 72rpx;\n  border-radius: 50%;\n  background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(90, 200, 250, 0.05));\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2rpx solid rgba(0, 122, 255, 0.1);\n}\n\n.management-text {\n  display: flex;\n  flex-direction: column;\n}\n\n.time-filter-header {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n  margin-bottom: 24rpx;\n}\n\n.filter-title {\n  font-size: 28rpx;\n  color: #1C1C1E;\n  font-weight: 500;\n}\n\n.time-filter-buttons {\n  display: flex;\n  gap: 16rpx;\n  flex-wrap: wrap;\n}\n\n.time-filter-btn {\n  padding: 16rpx 32rpx;\n  border: 1rpx solid #E5E5E5;\n  background: white;\n  border-radius: 12rpx;\n  font-size: 26rpx;\n  color: #8E8E93;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n\n  &.active {\n    border-color: #007AFF;\n    background: #007AFF;\n    color: white;\n  }\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 28rpx;\n  text-align: center;\n  padding: 4rpx;\n}\n\n.stats-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 24rpx 20rpx;\n  border-radius: 20rpx;\n  background: linear-gradient(145deg, rgba(248, 249, 250, 0.9), rgba(255, 255, 255, 0.8));\n  border: 1rpx solid rgba(0, 0, 0, 0.04);\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);\n  transition: transform 0.2s ease;\n}\n\n.stats-item:hover {\n  transform: translateY(-3rpx);\n  background: linear-gradient(145deg, rgba(248, 249, 250, 1), rgba(255, 255, 255, 0.95));\n  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);\n}\n\n.stats-number {\n  font-size: 52rpx;\n  font-weight: 700;\n  letter-spacing: -1rpx;\n  margin-bottom: 8rpx;\n\n  &.primary {\n    background: linear-gradient(135deg, #007AFF, #5AC8FA);\n    background-clip: text;\n    -webkit-background-clip: text;\n    color: transparent;\n  }\n\n  &.success {\n    background: linear-gradient(135deg, #34C759, #30D158);\n    background-clip: text;\n    -webkit-background-clip: text;\n    color: transparent;\n  }\n\n  &.warning {\n    background: linear-gradient(135deg, #FF9500, #FF9F0A);\n    background-clip: text;\n    -webkit-background-clip: text;\n    color: transparent;\n  }\n\n  &.danger {\n    background: linear-gradient(135deg, #FF3B30, #FF453A);\n    background-clip: text;\n    -webkit-background-clip: text;\n    color: transparent;\n  }\n}\n\n.stats-label {\n  font-size: 26rpx;\n  color: #6C7B8A;\n  font-weight: 500;\n  letter-spacing: 0.5rpx;\n}\n\n.completion-rate {\n  margin-top: 24rpx;\n  padding: 28rpx 24rpx;\n  border-top: 1rpx solid rgba(240, 240, 240, 0.8);\n  background: linear-gradient(135deg, rgba(248, 249, 250, 0.8), rgba(255, 255, 255, 0.9));\n  border-radius: 20rpx;\n  position: relative;\n  overflow: hidden;\n}\n\n.completion-rate::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 2rpx;\n  background: linear-gradient(90deg, #007AFF, #5AC8FA, #34C759);\n  opacity: 0.6;\n}\n\n.rate-label {\n  font-size: 28rpx;\n  color: #1C1C1E;\n  font-weight: 600;\n  margin-bottom: 16rpx;\n  text-align: center;\n}\n\n.rate-value {\n  font-size: 56rpx;\n  font-weight: 800;\n  background: linear-gradient(135deg, #007AFF, #5AC8FA);\n  background-clip: text;\n  -webkit-background-clip: text;\n  color: transparent;\n  margin-bottom: 16rpx;\n  text-align: center;\n  letter-spacing: -2rpx;\n}\n\n.rate-bar {\n  height: 16rpx;\n  background: rgba(240, 240, 240, 0.8);\n  border-radius: 8rpx;\n  overflow: hidden;\n  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n}\n\n.rate-fill {\n  height: 100%;\n  background: linear-gradient(90deg, #007AFF 0%, #5AC8FA 50%, #34C759 100%);\n  border-radius: 8rpx;\n  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);\n  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);\n  position: relative;\n}\n\n.rate-fill::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 50%;\n  background: linear-gradient(180deg, rgba(255, 255, 255, 0.3), transparent);\n  border-radius: 8rpx 8rpx 0 0;\n}\n\n.management-actions {\n  display: flex;\n  gap: 16rpx;\n  flex-wrap: wrap;\n}\n\n.action-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12rpx;\n  padding: 28rpx 32rpx;\n  border-radius: 16rpx;\n  font-size: 30rpx;\n  font-weight: 600;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  flex: 1;\n  position: relative;\n  overflow: hidden;\n\n  &.primary {\n    background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n    color: #ffffff;\n    box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3),\n                0 4rpx 8rpx rgba(0, 122, 255, 0.2);\n    border: none;\n  }\n  \n  &.primary::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n    transition: left 0.6s;\n  }\n  \n  &.primary:active::before {\n    left: 100%;\n  }\n  \n  &.primary:active {\n    transform: translateY(2rpx);\n    box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3),\n                0 2rpx 4rpx rgba(0, 122, 255, 0.2);\n  }\n\n  &.secondary {\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);\n    color: #007AFF;\n    border: 2rpx solid rgba(0, 122, 255, 0.25);\n    backdrop-filter: blur(15rpx);\n    box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.12),\n                0 2rpx 4rpx rgba(0, 122, 255, 0.08);\n    position: relative;\n    overflow: hidden;\n  }\n  \n  &.secondary::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(90deg, transparent, rgba(0, 122, 255, 0.1), transparent);\n    transition: left 0.6s ease;\n  }\n  \n  &.secondary:active::before {\n    left: 100%;\n  }\n  \n  &.secondary:active {\n    background: linear-gradient(135deg, rgba(230, 243, 255, 0.95) 0%, rgba(240, 248, 255, 0.9) 100%);\n    transform: translateY(1rpx);\n    box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.15),\n                0 1rpx 2rpx rgba(0, 122, 255, 0.1);\n  }\n}\n\n.filter-tabs {\n  white-space: nowrap;\n  padding: 12rpx 0 0 0;\n  margin-top: 16rpx;\n}\n\n.filter-tabs::-webkit-scrollbar {\n  display: none;\n}\n\n.filter-tab {\n  display: inline-block;\n  padding: 12rpx 20rpx;\n  border: 2rpx solid #E5E5E5;\n  background: #ffffff;\n  border-radius: 24rpx;\n  font-size: 24rpx;\n  color: #8E8E93;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  font-weight: 500;\n  letter-spacing: 0.3rpx;\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);\n  white-space: nowrap;\n  margin: 4rpx 12rpx 4rpx 0;\n\n  &.active {\n    border-color: #007AFF;\n    background: linear-gradient(135deg, #007AFF, #5AC8FA);\n    color: #ffffff;\n    transform: translateY(-2rpx);\n    box-shadow: 0 6rpx 16rpx rgba(0, 122, 255, 0.3);\n  }\n  \n  &:hover:not(.active) {\n    border-color: #B3D9FF;\n    background: #F8FBFF;\n    transform: translateY(-1rpx);\n    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\n  }\n}\n\n.list-item {\n  display: flex;\n  align-items: center;\n  padding: 28rpx 24rpx 28rpx 32rpx;\n  background: #ffffff;\n  border-bottom: 1rpx solid #F0F0F0;\n  transition: background 0.2s ease, transform 0.2s ease, border-bottom 0s ease 0.1s;\n  cursor: pointer;\n}\n\n.list-item:hover {\n  background: #F8F9FA;\n  transform: translateX(2rpx);\n}\n\n.list-item:active {\n  transform: translateX(2rpx) scale(0.98);\n  transition: all 0.1s ease;\n}\n\n.list-item.no-animations {\n  pointer-events: none;\n  transition: none !important;\n}\n\n.list-item.no-animations:hover,\n.list-item.no-animations:active {\n  transform: none !important;\n  background: #ffffff !important;\n}\n\n.list-item:last-child {\n  border-bottom: none;\n}\n\n.list-item-icon {\n  width: 88rpx;\n  height: 88rpx;\n  border-radius: 18rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 24rpx;\n  flex-shrink: 0;\n  position: relative;\n  overflow: hidden;\n\n  &.icon-bg-approved {\n    background: linear-gradient(135deg, rgba(52, 199, 89, 0.15), rgba(48, 209, 88, 0.08));\n    border: 2rpx solid rgba(52, 199, 89, 0.3);\n  }\n\n  &.icon-bg-pending_review {\n    background: linear-gradient(135deg, rgba(88, 86, 214, 0.15), rgba(88, 86, 214, 0.08));\n    border: 2rpx solid rgba(88, 86, 214, 0.3);\n  }\n\n  &.icon-bg-in_progress {\n    background: linear-gradient(135deg, rgba(255, 149, 0, 0.15), rgba(255, 159, 10, 0.08));\n    border: 2rpx solid rgba(255, 149, 0, 0.3);\n  }\n\n  &.icon-bg-overdue {\n    background: linear-gradient(135deg, rgba(255, 59, 48, 0.15), rgba(255, 69, 58, 0.08));\n    border: 2rpx solid rgba(255, 59, 48, 0.3);\n  }\n\n  &.icon-bg-pending {\n    background: linear-gradient(135deg, rgba(0, 122, 255, 0.15), rgba(90, 200, 250, 0.08));\n    border: 2rpx solid rgba(0, 122, 255, 0.3);\n  }\n\n  &.icon-bg-assigned {\n    background: linear-gradient(135deg, rgba(8, 145, 178, 0.15), rgba(6, 182, 212, 0.08));\n    border: 2rpx solid rgba(8, 145, 178, 0.3);\n  }\n\n  &.icon-bg-approved {\n    background: linear-gradient(135deg, rgba(22, 163, 74, 0.15), rgba(34, 197, 94, 0.08));\n    border: 2rpx solid rgba(22, 163, 74, 0.3);\n  }\n\n  &.icon-bg-rejected {\n    background: linear-gradient(135deg, rgba(142, 142, 147, 0.15), rgba(142, 142, 147, 0.08));\n    border: 2rpx solid rgba(142, 142, 147, 0.3);\n  }\n}\n\n.issue-number {\n  font-size: 30rpx;\n  font-weight: 800;\n  letter-spacing: -0.5rpx;\n}\n\n.list-item-icon.icon-bg-approved .issue-number {\n  color: #34C759;\n}\n\n.list-item-icon.icon-bg-pending_review .issue-number {\n  color: #5856D6;\n}\n\n.list-item-icon.icon-bg-in_progress .issue-number {\n  color: #FF9500;\n}\n\n.list-item-icon.icon-bg-overdue .issue-number {\n  color: #FF3B30;\n}\n\n.list-item-icon.icon-bg-pending .issue-number {\n  color: #007AFF;\n}\n\n.list-item-icon.icon-bg-assigned .issue-number {\n  color: #0891B2;\n}\n\n.list-item-icon.icon-bg-approved .issue-number {\n  color: #16A34A;\n}\n\n.list-item-icon.icon-bg-rejected .issue-number {\n  color: #8E8E93;\n}\n\n.list-item-content {\n  flex: 1;\n  min-width: 0;\n  margin-right: 12rpx;\n}\n\n.list-item-title {\n  font-size: 34rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 12rpx;\n  line-height: 1.3;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  line-clamp: 2;\n}\n\n.issue-meta {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  font-size: 24rpx;\n  color: #C7C7CC;\n  flex-wrap: wrap;\n}\n\n.meta-item {\n  color: #8E8E93;\n}\n\n.meta-separator {\n  color: #C7C7CC;\n}\n\n.list-item-right {\n  display: flex;\n  align-items: center;\n  gap: 16rpx;\n  margin-left: 16rpx;\n}\n\n.status-badge {\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  font-size: 22rpx;\n  font-weight: 600;\n  letter-spacing: 0.3rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\n  &.status-approved {\n    background: linear-gradient(135deg, #E8F5E8, #F0FFF0);\n    color: #34C759;\n    border: 2rpx solid rgba(52, 199, 89, 0.2);\n  }\n\n  &.status-pending_review {\n    background: linear-gradient(135deg, #F0EFFF, #F8F7FF);\n    color: #5856D6;\n    border: 2rpx solid rgba(88, 86, 214, 0.2);\n  }\n\n  &.status-in_progress {\n    background: linear-gradient(135deg, #FFF4E6, #FFFBF0);\n    color: #FF9500;\n    border: 2rpx solid rgba(255, 149, 0, 0.2);\n  }\n\n  &.status-overdue {\n    background: linear-gradient(135deg, #FFE6E6, #FFF0F0);\n    color: #FF3B30;\n    border: 2rpx solid rgba(255, 59, 48, 0.2);\n  }\n\n  &.status-pending {\n    background: linear-gradient(135deg, #E6F3FF, #F0F7FF);\n    color: #007AFF;\n    border: 2rpx solid rgba(0, 122, 255, 0.2);\n  }\n\n  &.status-assigned {\n    background: linear-gradient(135deg, #ECFEFF, #F0FDFF);\n    color: #0891B2;\n    border: 2rpx solid rgba(8, 145, 178, 0.2);\n  }\n\n  &.status-approved {\n    background: linear-gradient(135deg, #E8F5E8, #F0FFF0);\n    color: #34C759;\n    border: 2rpx solid rgba(52, 199, 89, 0.2);\n  }\n\n  &.status-rejected {\n    background: linear-gradient(135deg, #F2F2F7, #F8F8F8);\n    color: #8E8E93;\n    border: 2rpx solid rgba(142, 142, 147, 0.2);\n  }\n}\n\n.progress-item {\n  padding: 24rpx 32rpx;\n  border-bottom: 1rpx solid #F0F0F0;\n}\n\n.progress-item:last-child {\n  border-bottom: none;\n}\n\n.progress-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 16rpx;\n}\n\n.person-info {\n  display: flex;\n  align-items: center;\n  gap: 20rpx;\n}\n\n.person-avatar {\n  width: 64rpx;\n  height: 64rpx;\n  border-radius: 50%;\n  background: rgba(0, 122, 255, 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.person-details {\n  display: flex;\n  flex-direction: column;\n}\n\n.person-name {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #1C1C1E;\n  margin-bottom: 6rpx;\n  line-height: 1.2;\n}\n\n.person-role {\n  font-size: 24rpx;\n  color: #8E8E93;\n  line-height: 1.2;\n}\n\n.progress-stats {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  gap: 4rpx;\n}\n\n.progress-text {\n  font-size: 26rpx;\n  color: #007AFF;\n  font-weight: 500;\n}\n\n.progress-percent {\n  font-size: 22rpx;\n  color: #8E8E93;\n}\n\n.person-issues {\n  margin-top: 16rpx;\n  padding-top: 16rpx;\n  border-top: 1rpx solid #F5F5F5;\n}\n\n.person-issue {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n  margin-bottom: 8rpx;\n}\n\n.issue-dot {\n  width: 12rpx;\n  height: 12rpx;\n  border-radius: 50%;\n  flex-shrink: 0;\n\n  &.dot-approved {\n    background: #34C759;\n  }\n\n  &.dot-pending_review {\n    background: #5856D6;\n  }\n\n  &.dot-in_progress {\n    background: #FF9500;\n  }\n\n  &.dot-overdue {\n    background: #FF3B30;\n  }\n\n  &.dot-pending {\n    background: #007AFF;\n  }\n\n  &.dot-assigned {\n    background: #0891B2;\n  }\n\n  &.dot-approved {\n    background: #16A34A;\n  }\n\n  &.dot-rejected {\n    background: #8E8E93;\n  }\n}\n\n.person-issue .issue-title {\n  font-size: 24rpx;\n  color: #666;\n  flex: 1;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.more-issues {\n  font-size: 22rpx;\n  color: #8E8E93;\n  margin-top: 8rpx;\n}\n\n.progress-bar {\n  height: 8rpx;\n  background: #F0F0F0;\n  border-radius: 4rpx;\n  overflow: hidden;\n  position: relative;\n}\n\n.progress-fill {\n  height: 100%;\n  border-radius: 4rpx;\n  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  background: linear-gradient(90deg, #007AFF 0%, #5AC8FA 100%);\n  \n  &.approved {\n    background: linear-gradient(90deg, #34C759 0%, #30D158 100%);\n  }\n  \n  &.empty {\n    background: linear-gradient(90deg, #FF9500 0%, #FF9F0A 100%);\n    opacity: 0.6;\n  }\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 50%;\n    background: linear-gradient(180deg, rgba(255, 255, 255, 0.3), transparent);\n    border-radius: 4rpx 4rpx 0 0;\n  }\n}\n\n.more-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12rpx;\n  padding: 28rpx;\n  font-size: 30rpx;\n  color: #007AFF;\n  border: 2rpx solid rgba(0, 122, 255, 0.2);\n  border-radius: 16rpx;\n  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(255, 255, 255, 0.9));\n  font-weight: 600;\n  letter-spacing: 0.5rpx;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.more-btn:active {\n  transform: translateY(2rpx);\n  background: linear-gradient(135deg, rgba(230, 243, 255, 0.9), rgba(240, 247, 255, 0.95));\n  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.15);\n  transition: all 0.1s ease;\n}\n\n.more-btn:not(:active) {\n  transform: translateY(0);\n}\n\n.bottom-safe-area {\n  height: 40rpx;\n}\n\n.time-popup {\n  background: white;\n  border-radius: 24rpx 24rpx 0 0;\n  padding: 0 0 env(safe-area-inset-bottom) 0;\n}\n\n.popup-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 32rpx 32rpx 24rpx 32rpx;\n  border-bottom: 1rpx solid #F2F2F7;\n}\n\n.popup-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n}\n\n.popup-close {\n  width: 48rpx;\n  height: 48rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #F2F2F7;\n  border-radius: 50%;\n}\n\n.time-options {\n  padding: 24rpx 32rpx;\n}\n\n.time-option {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 24rpx 0;\n  border-bottom: 1rpx solid #F2F2F7;\n  position: relative;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n  \n  &.active {\n    .option-text {\n      color: #007AFF;\n      font-weight: 600;\n    }\n  }\n}\n\n.option-left {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.option-content {\n  margin-left: 16rpx;\n  flex: 1;\n}\n\n.option-text {\n  font-size: 28rpx;\n  color: #1C1C1E;\n  display: block;\n  margin-bottom: 4rpx;\n}\n\n.option-desc {\n  font-size: 24rpx;\n  color: #8E8E93;\n  display: block;\n}\n\n.option-check {\n  width: 32rpx;\n  height: 32rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.custom-loading-mask {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(0, 0, 0, 0.4);\n  backdrop-filter: blur(8rpx);\n  z-index: 9999;\n  animation: maskFadeIn 0.3s ease-out;\n  margin: 0;\n  padding: 0;\n}\n\n@keyframes maskFadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n.loading-container-enhanced {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 32rpx;\n  padding: 80rpx 60rpx;\n  background: rgba(255, 255, 255, 0.98);\n  backdrop-filter: blur(20rpx);\n  border-radius: 32rpx;\n  min-width: 320rpx;\n  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.25), \n              0 4rpx 16rpx rgba(0, 0, 0, 0.15);\n  border: 1rpx solid rgba(255, 255, 255, 0.4);\n  animation: containerSlideIn 0.4s ease-out 0.1s both;\n}\n\n@keyframes containerSlideIn {\n  from {\n    opacity: 0;\n    transform: translateY(20rpx) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n.loading-text-enhanced {\n  font-size: 32rpx;\n  color: #1C1C1E;\n  font-weight: 600;\n  margin-top: 8rpx;\n  text-align: center;\n  line-height: 1.3;\n}\n\n/* 时间选择器通用样式 */\n.time-selector {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  padding: 10rpx 18rpx;\n  background: linear-gradient(135deg, #E6F3FF, #F0F7FF);\n  border-radius: 24rpx;\n  border: 1rpx solid rgba(0, 122, 255, 0.2);\n  transition: all 0.2s ease;\n  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.1);\n  cursor: pointer;\n}\n\n.time-selector:active {\n  transform: scale(0.98);\n  background: linear-gradient(135deg, #D0E8FF, #E6F3FF);\n}\n\n.time-text {\n  font-size: 26rpx;\n  color: #007AFF;\n  font-weight: 600;\n  letter-spacing: 0.5rpx;\n}\n\n/* H5端样式 */\n/* #ifdef H5 */\n@media (max-width: 414px) {\n  .card {\n    margin: 0 16rpx 24rpx 16rpx;\n  }\n\n  .card:first-child {\n    margin-top: 24rpx;\n  }\n  \n  .action-btn {\n    padding: 24rpx 28rpx;\n    font-size: 28rpx;\n  }\n}\n/* #endif */\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./monthly-check.vue?vue&type=style&index=0&id=cd5e3c08&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./monthly-check.vue?vue&type=style&index=0&id=cd5e3c08&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775845103\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}