{"bsonType": "object", "required": ["locations", "created_at", "created_by"], "permission": {"read": true, "create": "auth.role && (auth.role.includes('admin') || auth.role.includes('GM') || auth.role.includes('Integrated') || auth.role.includes('reviser'))", "update": "auth.role && (auth.role.includes('admin') || auth.role.includes('GM') || auth.role.includes('Integrated') || auth.role.includes('reviser'))", "delete": "auth.role && auth.role.includes('admin')"}, "properties": {"_id": {"description": "ID，系统自动生成"}, "locations": {"bsonType": "array", "title": "位置配置", "description": "位置分类和项目的配置数组", "items": {"bsonType": "object", "required": ["category", "items"], "properties": {"category": {"bsonType": "string", "title": "位置分类", "description": "位置分类名称，如：工业厂、办公区等", "maxLength": 50}, "items": {"bsonType": "array", "title": "位置项目", "description": "该分类下的具体位置项目", "items": {"bsonType": "string", "title": "位置名称", "description": "具体位置名称", "maxLength": 100}}}}}, "last_updated": {"bsonType": "timestamp", "title": "最后更新时间", "description": "配置的最后更新时间戳"}, "created_at": {"bsonType": "timestamp", "title": "创建时间", "description": "配置创建时间"}, "updated_at": {"bsonType": "timestamp", "title": "更新时间", "description": "最后更新时间"}, "created_by": {"bsonType": "string", "title": "创建者", "description": "创建该配置的用户ID", "foreignKey": "uni-id-users._id"}, "updated_by": {"bsonType": "string", "title": "更新者", "description": "最后更新该配置的用户ID", "foreignKey": "uni-id-users._id"}}}