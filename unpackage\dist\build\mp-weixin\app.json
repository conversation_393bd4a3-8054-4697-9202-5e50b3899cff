{"pages": ["pages/index/index", "pages/patrol/index", "pages/feedback/list", "pages/6s/index", "pages/ucenter/ucenter"], "subPackages": [{"root": "pages/feedback_pkg", "pages": ["edit", "examine"]}, {"root": "pages/ucenter_pkg", "pages": ["todo", "export-excel", "user-management", "responsible-tasks", "gm-supervision", "complete-task", "study-center"]}, {"root": "pages/notice", "pages": ["add", "edit", "list", "detail"]}, {"root": "uni_modules/uni-id-pages/pages", "pages": ["login/login-withoutpwd", "login/login-withpwd", "userinfo/change_pwd/change_pwd", "userinfo/userinfo"]}, {"root": "pages/honor_pkg", "pages": ["gallery/index", "admin/index", "admin/add-record", "admin/batch-manager", "admin/type-manager"]}, {"root": "pages/info_pkg", "pages": ["user-guide", "privacy"]}, {"root": "pages/patrol_pkg", "pages": ["checkin/index", "point/index", "point/add", "point/edit", "point/detail", "route/index", "route/add", "route/edit", "route/detail", "shift/index", "shift/add", "shift/edit", "shift/detail", "task/index", "task/add", "task/batch-add", "task/detail", "task/edit", "record/index", "record/detail", "record/route-detail", "point/qrcode", "point/qrcode-batch"]}, {"root": "pages/6s_pkg", "pages": ["my-areas", "area-inspection", "monthly-check", "data-manage", "cleaning-upload", "area-detail", "inspection-detail", "record-detail", "issue-list", "location-manage", "issue-add", "issue-detail", "person-progress", "rectification-upload", "rectification-detail", "rectification-review", "rectification-submit", "inspector-rectification-detail", "fixed-area-manage", "employee-assignment", "public-area-manage", "public-schedule"]}], "window": {"navigationBarTextStyle": "black", "navigationBarTitleText": "株水小智", "navigationBarBackgroundColor": "#F8F8F8", "backgroundColor": "#F8F8F8"}, "tabBar": {"color": "#7A7E83", "selectedColor": "#1677FF", "borderStyle": "black", "backgroundColor": "#ffffff", "list": [{"pagePath": "pages/index/index", "iconPath": "static/tabbar/home.png", "selectedIconPath": "static/tabbar/home_active.png", "text": "找茬"}, {"pagePath": "pages/feedback/list", "iconPath": "static/tabbar/feedback.png", "selectedIconPath": "static/tabbar/feedback_active.png", "text": "反馈"}, {"pagePath": "pages/patrol/index", "iconPath": "static/tabbar/patrol.png", "selectedIconPath": "static/tabbar/patrol_active.png", "text": "巡视"}, {"pagePath": "pages/6s/index", "iconPath": "static/tabbar/6s.png", "selectedIconPath": "static/tabbar/6s_active.png", "text": "检查"}, {"pagePath": "pages/ucenter/ucenter", "iconPath": "static/tabbar/user.png", "selectedIconPath": "static/tabbar/user_active.png", "text": "我的"}]}, "lazyCodeLoading": "requiredComponents", "requiredPrivateInfos": ["getLocation", "chooseLocation", "onLocationChange", "startLocationUpdate"], "permission": {"scope.userLocation": {"desc": "您的位置信息将用于巡视打卡功能"}}, "usingComponents": {}}