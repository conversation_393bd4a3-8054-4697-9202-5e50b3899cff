<view class="page-container data-v-6b5fa68e"><view class="card data-v-6b5fa68e"><view class="card-header data-v-6b5fa68e"><view class="header-content data-v-6b5fa68e"><view class="card-title data-v-6b5fa68e">{{$root.m0}}</view><view data-event-opts="{{[['tap',[['showTimeSelector',['$event']]]]]}}" class="time-selector data-v-6b5fa68e" bindtap="__e"><text class="time-text data-v-6b5fa68e">{{$root.m1}}</text><uni-icons vue-id="de6842ec-1" type="down" size="12" color="#007AFF" class="data-v-6b5fa68e" bind:__l="__l"></uni-icons></view></view></view><view class="card-body data-v-6b5fa68e"><view class="stats-grid data-v-6b5fa68e"><view class="stats-item data-v-6b5fa68e"><view class="stats-number primary data-v-6b5fa68e">{{totalIssues}}</view><view class="stats-label data-v-6b5fa68e">发现问题</view></view><view class="stats-item data-v-6b5fa68e"><view class="stats-number success data-v-6b5fa68e">{{completedIssues}}</view><view class="stats-label data-v-6b5fa68e">已整改</view></view><view class="stats-item data-v-6b5fa68e"><view class="stats-number warning data-v-6b5fa68e">{{inProgressIssues}}</view><view class="stats-label data-v-6b5fa68e">整改中</view></view><view class="stats-item data-v-6b5fa68e"><view class="stats-number danger data-v-6b5fa68e">{{overdueIssues}}</view><view class="stats-label data-v-6b5fa68e">逾期未改</view></view></view><view class="completion-rate data-v-6b5fa68e"><view class="rate-value data-v-6b5fa68e">{{completionRate+"%"}}</view><view class="rate-bar data-v-6b5fa68e"><view class="rate-fill data-v-6b5fa68e" style="{{'width:'+(completionRate+'%')+';'}}"></view></view></view></view></view><block wx:if="{{hasManagePermission}}"><view class="card data-v-6b5fa68e"><view class="card-header data-v-6b5fa68e"><view class="header-content data-v-6b5fa68e"><view class="management-title-group data-v-6b5fa68e"><view class="management-icon data-v-6b5fa68e"><uni-icons vue-id="de6842ec-2" type="gear-filled" size="20" color="#007AFF" class="data-v-6b5fa68e" bind:__l="__l"></uni-icons></view><view class="management-text data-v-6b5fa68e"><view class="card-title data-v-6b5fa68e">月度管理</view><view class="card-subtitle data-v-6b5fa68e">管理员专属操作面板</view></view></view></view></view><view class="card-body data-v-6b5fa68e"><view class="management-actions data-v-6b5fa68e"><view data-event-opts="{{[['tap',[['addNewIssue',['$event']]]]]}}" class="action-btn primary data-v-6b5fa68e" bindtap="__e"><uni-icons vue-id="de6842ec-3" type="plus" size="20" color="#ffffff" class="data-v-6b5fa68e" bind:__l="__l"></uni-icons><text class="data-v-6b5fa68e">新增问题</text></view><view data-event-opts="{{[['tap',[['manageIssues',['$event']]]]]}}" class="action-btn secondary data-v-6b5fa68e" bindtap="__e"><uni-icons vue-id="de6842ec-4" type="list" size="20" color="#007AFF" class="data-v-6b5fa68e" bind:__l="__l"></uni-icons><text class="data-v-6b5fa68e">问题管理</text></view></view></view></view></block><view class="card data-v-6b5fa68e"><view class="card-header data-v-6b5fa68e"><view class="header-content data-v-6b5fa68e"><view class="card-title data-v-6b5fa68e">{{$root.m2}}</view><view class="card-subtitle data-v-6b5fa68e">全厂安全卫生检查发现的问题</view></view><scroll-view class="filter-tabs data-v-6b5fa68e" scroll-x="true" show-scrollbar="false"><block wx:for="{{statusFilters}}" wx:for-item="filter" wx:for-index="__i0__" wx:key="value"><view data-event-opts="{{[['tap',[['changeStatusFilter',['$0'],[[['statusFilters','value',filter.value,'value']]]]]]]}}" class="{{['filter-tab','data-v-6b5fa68e',(currentStatusFilter===filter.value)?'active':'']}}" bindtap="__e">{{''+filter.label+''}}</view></block></scroll-view></view><block wx:if="{{$root.g0>0}}"><view class="data-v-6b5fa68e"><block wx:for="{{$root.l0}}" wx:for-item="issue" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['viewIssueDetail',['$0'],[[['displayedIssues','',index]]]]]]]}}" class="{{['list-item','data-v-6b5fa68e',(disableAnimations)?'no-animations':'']}}" bindtap="__e"><view class="{{['list-item-icon','data-v-6b5fa68e','icon-bg-'+issue.$orig.status]}}"><text class="issue-number data-v-6b5fa68e">{{issue.g1}}</text></view><view class="list-item-content data-v-6b5fa68e"><view class="list-item-title data-v-6b5fa68e">{{issue.$orig.title}}</view><view class="issue-meta data-v-6b5fa68e"><text class="meta-item data-v-6b5fa68e">{{issue.$orig.location}}</text><text class="meta-separator data-v-6b5fa68e">·</text><text class="meta-item data-v-6b5fa68e">{{issue.$orig.responsible}}</text><text class="meta-separator data-v-6b5fa68e">·</text><text class="meta-item data-v-6b5fa68e">{{issue.m3}}</text></view></view><view class="list-item-right data-v-6b5fa68e"><view class="{{['status-badge','data-v-6b5fa68e','status-'+issue.$orig.status]}}">{{issue.m4}}</view><uni-icons vue-id="{{'de6842ec-5-'+index}}" type="right" size="14" color="#C7C7CC" class="data-v-6b5fa68e" bind:__l="__l"></uni-icons></view></view></block></view></block><block wx:else><p-empty-state vue-id="de6842ec-6" useIcon="{{true}}" iconName="info" iconColor="#C7C7CC" size="large" text="{{'暂无'+$root.m5+'问题'}}" class="data-v-6b5fa68e" bind:__l="__l"></p-empty-state></block><block wx:if="{{hasMoreIssues}}"><view class="card-body data-v-6b5fa68e"><view data-event-opts="{{[['tap',[['showMoreIssues',['$event']]]]]}}" class="more-btn data-v-6b5fa68e" bindtap="__e"><uni-icons vue-id="de6842ec-7" type="down" size="16" color="#007AFF" class="data-v-6b5fa68e" bind:__l="__l"></uni-icons><text class="data-v-6b5fa68e">{{"查看更多问题 ("+remainingIssuesCount+")"}}</text></view></view></block></view><view class="card data-v-6b5fa68e"><view class="card-header data-v-6b5fa68e"><view class="header-content data-v-6b5fa68e"><view class="card-title data-v-6b5fa68e">整改进度跟踪</view><view class="card-subtitle data-v-6b5fa68e">按负责人分组显示</view></view></view><block wx:if="{{$root.g2>0}}"><view class="data-v-6b5fa68e"><block wx:for="{{$root.l2}}" wx:for-item="person" wx:for-index="personIndex" wx:key="personIndex"><view data-event-opts="{{[['tap',[['viewPersonProgress',['$0'],[[['responsiblePersons','',personIndex]]]]]]]}}" class="progress-item data-v-6b5fa68e" bindtap="__e"><view class="progress-header data-v-6b5fa68e"><view class="person-info data-v-6b5fa68e"><view class="person-avatar data-v-6b5fa68e"><uni-icons vue-id="{{'de6842ec-8-'+personIndex}}" type="person" size="16" color="#007AFF" class="data-v-6b5fa68e" bind:__l="__l"></uni-icons></view><view class="person-details data-v-6b5fa68e"><text class="person-name data-v-6b5fa68e">{{person.$orig.name}}</text><text class="person-role data-v-6b5fa68e">{{person.$orig.role}}</text></view></view><view class="progress-stats data-v-6b5fa68e"><text class="progress-text data-v-6b5fa68e">{{person.$orig.completed+"/"+person.$orig.total}}</text><text class="progress-percent data-v-6b5fa68e">{{person.m6+"%"}}</text></view></view><view class="progress-bar data-v-6b5fa68e"><view class="{{['progress-fill','data-v-6b5fa68e',(person.m7===100)?'approved':'',(person.m8===0)?'empty':'']}}" style="{{'width:'+(person.m9)+';'}}"></view></view><block wx:if="{{person.g3}}"><view class="person-issues data-v-6b5fa68e"><block wx:for="{{person.l1}}" wx:for-item="issue" wx:for-index="issueIndex" wx:key="issueIndex"><view class="person-issue data-v-6b5fa68e"><view class="{{['issue-dot','data-v-6b5fa68e','dot-'+issue.status]}}"></view><text class="issue-title data-v-6b5fa68e">{{issue.title}}</text></view></block><block wx:if="{{person.g4>2}}"><view class="more-issues data-v-6b5fa68e"><text class="data-v-6b5fa68e">{{"还有"+(person.g5-2)+"个问题..."}}</text></view></block></view></block></view></block></view></block><block wx:else><p-empty-state vue-id="de6842ec-9" useIcon="{{true}}" iconName="person" iconColor="#C7C7CC" size="large" text="暂无负责人进度数据" subText="当有月度检查问题分配给负责人时，这里会显示进度统计" class="data-v-6b5fa68e" bind:__l="__l"></p-empty-state></block></view><view class="bottom-safe-area data-v-6b5fa68e"></view><uni-popup vue-id="de6842ec-10" type="bottom" border-radius="16rpx 16rpx 0 0" data-ref="timePopup" class="data-v-6b5fa68e vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="time-popup data-v-6b5fa68e"><view class="popup-header data-v-6b5fa68e"><text class="popup-title data-v-6b5fa68e">选择时间范围</text><view data-event-opts="{{[['tap',[['closeTimeSelector',['$event']]]]]}}" class="popup-close data-v-6b5fa68e" bindtap="__e"><uni-icons vue-id="{{('de6842ec-11')+','+('de6842ec-10')}}" type="close" size="18" color="#8E8E93" class="data-v-6b5fa68e" bind:__l="__l"></uni-icons></view></view><view class="time-options data-v-6b5fa68e"><block wx:for="{{timeOptions}}" wx:for-item="option" wx:for-index="__i1__" wx:key="value"><view data-event-opts="{{[['tap',[['selectTimeFilter',['$0'],[[['timeOptions','value',option.value]]]]]]]}}" class="{{['time-option','data-v-6b5fa68e',(selectedTimeFilter===option.value)?'active':'']}}" bindtap="__e"><view class="option-left data-v-6b5fa68e"><uni-icons vue-id="{{('de6842ec-12-'+__i1__)+','+('de6842ec-10')}}" type="{{option.icon}}" size="18" color="{{selectedTimeFilter===option.value?'#007AFF':'#8E8E93'}}" class="data-v-6b5fa68e" bind:__l="__l"></uni-icons><view class="option-content data-v-6b5fa68e"><text class="option-text data-v-6b5fa68e">{{option.label}}</text><text class="option-desc data-v-6b5fa68e">{{option.desc}}</text></view></view><block wx:if="{{selectedTimeFilter===option.value}}"><view class="option-check data-v-6b5fa68e"><uni-icons vue-id="{{('de6842ec-13-'+__i1__)+','+('de6842ec-10')}}" type="checkmarkempty" size="16" color="#007AFF" class="data-v-6b5fa68e" bind:__l="__l"></uni-icons></view></block></view></block></view></view></uni-popup><block wx:if="{{loading}}"><view class="custom-loading-mask data-v-6b5fa68e"><view class="loading-container-enhanced data-v-6b5fa68e"><uni-icons vue-id="de6842ec-14" type="spinner-cycle" size="48" color="#007AFF" class="data-v-6b5fa68e" bind:__l="__l"></uni-icons><text class="loading-text-enhanced data-v-6b5fa68e">{{loadingText}}</text></view></view></block></view>