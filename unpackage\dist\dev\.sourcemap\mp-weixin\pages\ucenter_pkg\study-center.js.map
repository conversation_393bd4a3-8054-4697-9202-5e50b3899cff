{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/ucenter_pkg/study-center.vue?0624", "webpack:///D:/Xwzc/pages/ucenter_pkg/study-center.vue?a613", "webpack:///D:/Xwzc/pages/ucenter_pkg/study-center.vue?014a", "webpack:///D:/Xwzc/pages/ucenter_pkg/study-center.vue?990a", "uni-app:///pages/ucenter_pkg/study-center.vue", "webpack:///D:/Xwzc/pages/ucenter_pkg/study-center.vue?02a9", "webpack:///D:/Xwzc/pages/ucenter_pkg/study-center.vue?c936"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "searchKeyword", "searchTimer", "categories", "categoryOptions", "selectedCategoryId", "isCategoryManagerVisible", "editingCategory", "newCategory", "description", "documents", "loading", "hasInitialized", "page", "pageSize", "hasMore", "loadMoreStatus", "canUpload", "canManageCategory", "userRole", "currentUserId", "ROLE_CONSTANTS", "ADMIN_ROLES", "UPLOAD_ROLES", "MANAGE_ROLES", "uploading", "isSubmitting", "uploadForm", "title", "category_id", "type", "file_name", "file_url", "file_type", "file_size", "files", "isEditMode", "editingDocumentId", "editForm", "currentCollection", "currentPreviewDocument", "downloadingFiles", "downloadedFiles", "computed", "selectedCategoryName", "documentTitle", "get", "set", "documentDescription", "editingCategoryName", "editingCategoryDescription", "canSubmit", "canSubmitEdit", "currentCollectionFiles", "currentCollectionFileCount", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "initPage", "Promise", "console", "uni", "icon", "callStudyCenterAPI", "token", "uniCloud", "action", "uniIdToken", "res", "handleAPIError", "checkPermission", "userInfo", "loadCategories", "result", "loadDocuments", "isLoadMore", "startTime", "params", "onSearchInput", "clearTimeout", "onSearch", "selectCategory", "loadMore", "refreshData", "viewDocument", "showDocumentPreviewModal", "closeDocumentPreviewModal", "downloadDocument", "document", "id", "url", "success", "download_count", "filePath", "showMenu", "fail", "content", "showCancel", "showCollectionDownloadModal", "closeCollectionDownloadModal", "downloadCollectionFile", "mask", "complete", "downloadIndex", "incrementCollectionDownload", "index", "downloadAllFiles", "totalFiles", "downloadedCount", "i", "file", "resolve", "reject", "editDocument", "getCurrentCategoryName", "getCurrentEditingDocument", "getCurrentEditingDocumentFileName", "removeFileFromCollectionEdit", "doc", "isLastFile", "deleteRes", "addFilesToCollection", "count", "fileInfo", "order", "cloudPath", "uploadRes", "duration", "submitEdit", "updateData", "deleteDocument", "isCollection", "extractFileIdFromUrl", "canEditDocument", "canDeleteDocument", "showUploadModal", "closeUploadModal", "resetUploadForm", "resetEditForm", "openCategorySelector", "closeCategorySelector", "handleCategorySelect", "showCategoryManager", "closeCategoryManager", "createCategory", "editCategory", "setTimeout", "showCategoryEditModal", "closeCategoryEditModal", "submitCategoryEdit", "confirmDeleteCategory", "deleteCategory", "chooseFile", "removeFile", "selectDocumentType", "chooseMultipleFiles", "removeFileFromCollection", "chooseFileForEdit", "removeFileInEdit", "uploadFileToCloudForEdit", "cloudPathAsRealPath", "uploadMultipleFilesToCloud", "uploadPromises", "results", "successFiles", "uploadFileToCloud", "submitUpload", "appConfig", "fileTypes", "pdf", "color", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "zip", "rar", "png", "jpg", "jpeg", "gif", "pagination", "defaultPageSize", "maxPageSize", "ui", "toastDuration", "loadingDelay", "getFileIconClass", "getFileIconColor", "deleteCloudFile", "fileIds", "filter", "map", "fileList", "handleError", "showSuccess", "formatFileSize", "size", "formatTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mWAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrQA;AAAA;AAAA;AAAA;AAAsmB,CAAgB,goBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC4nB1nB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACA;MACA;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;QACAT;QACAU;MACA;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;MACAC;MAAA;MACAC;QACAC;QACAC;QACApB;QACAqB;QAAA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;;MAEA;MACAC;MACAC;MACAC;QACAV;QACAC;QACApB;QACAsB;QACAC;QACAC;QACAC;MACA;MAEA;MACAK;MACAC;MACAC;MACAC;MACAP;IACA;EACA;EACAQ,0CACA;IACAC;MAAA;MACA;QACA;UAAA;QAAA;QACA;MACA;MACA;IACA;IACAC;MACAC;QACA;MACA;MACAC;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACAF;QACA;MACA;MACAC;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAE;MACAH;QACA;MACA;MACAC;QACA;UACA;QACA;MACA;IACA;IAEAG;MACAJ;QACA;MACA;MACAC;QACA;UACA;QACA;MACA;IACA;IACAI;MACA;QACA,gCACA,+BACA;MACA;QACA,gCACA,+BACA,yBACA;MACA;MACA;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;EAAA,EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAA;gBAAA,OAGAC,aACA,yBACA,uBACA;cAAA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACAC;kBACAlC;kBACAmC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAIA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAhE;gBAAA;gBAEAiE;gBAAA;gBAAA,OACAC;kBACAnE;kBACAC;oBACAmE;oBACAnE;oBACAoE;kBACA;gBACA;cAAA;gBAPAC;gBAAA,kCAQAA;cAAA;gBAAA;gBAAA;gBAEAR;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACAS;MAAA;MACAT;MACA;MACAC;QACAlC;QACAmC;MACA;IACA;IAEA;IACAQ;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACAC;kBACA;oBACA;oBACA;;oBAEA;oBACA;sBAAA;oBAAA;;oBAEA;oBACA;sBAAA;oBAAA;kBACA;gBACA;kBACAX;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAY;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBAEA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAb;gBACAC;kBACAlC;kBACAmC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAY;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACAC;gBAEA;gBACA;kBACA;kBACA;gBACA;gBAAA;gBAGAC;kBACAjE;kBACAC;gBACA;gBAEA;kBACAgE;gBACA;gBAEA;kBACAA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAJ;gBAEA;kBAAA,eACAA;kBAEA;oBACA;kBACA;oBACA;kBACA;kBAEA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAb;gBACAC;kBACAlC;kBACAmC;gBACA;cAAA;gBAAA;gBAEA;gBACAD;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiB;MAAA;MACA;QACAC;MACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;cAAA;gBAAA;gBAKA3B;kBACAlC;gBACA;;gBAEA;gBAAA;gBAAA,OACAsC;kBACAnE;kBACAC;oBACAmE;oBACAnE;sBAAA0F;oBAAA;kBACA;gBACA;cAAA;gBAEA;gBACA5B;kBACA6B;kBACAC;oBACA9B;oBACA;sBACAA;wBACAlC;wBACAmC;sBACA;sBACA;sBACA;wBAAA;sBAAA;sBACA;wBACA;0BAAA8B;wBAAA;sBACA;sBACA;sBACA;wBACA;sBACA;sBACA;sBACA/B;wBACAgC;wBACAC;wBACAC;0BACAlC;4BACAlC;4BACAqE;4BACAC;0BACA;wBACA;sBACA;oBACA;sBACApC;wBACAlC;wBACAmC;sBACA;oBACA;kBACA;kBACAiC;oBACAlC;oBACAD;oBACAC;sBACAlC;sBACAmC;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;gBACAD;gBACAC;kBACAlC;kBACAmC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAoC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBAEA;kBACA;kBACAvC;oBACAlC;oBACA0E;kBACA;;kBAEA;kBACAxC;oBACA6B;oBACAC;sBACA9B;sBACA;wBACA;wBACA;0BACA;wBACA;wBAEAA;0BACAlC;0BACAmC;wBACA;;wBAEA;wBACAD;0BACAgC;0BACAC;0BACAC;4BACAlC;8BACAlC;8BACAqE;8BACAC;4BACA;0BACA;wBACA;;wBAEA;wBACA;sBACA;wBACApC;0BACAlC;0BACAmC;wBACA;sBACA;oBACA;oBACAiC;sBACAlC;sBACAD;sBACAC;wBACAlC;wBACAmC;sBACA;oBACA;oBACAwC;sBACA;sBACA;sBACA;wBACA;sBACA;oBACA;kBACA;gBACA;kBACAzC;kBACAD;kBACAC;oBACAlC;oBACAmC;kBACA;;kBAEA;kBACAyC;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAvC;kBACAnE;kBACAC;oBACAmE;oBACAnE;sBAAA0F;oBAAA;kBACA;gBACA;cAAA;gBAEA;gBACAgB;kBAAA;gBAAA;gBACA;kBACA,uEACA;oBACAb;kBAAA,GACA;gBACA;gBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAhC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA8C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIAxE;gBACAyE;gBACAC;gBAEA/C;kBACAlC;kBACAqE;kBACAL;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAvB;gCAAA;gCAAA;8BAAA;8BACAP;gCACAlC;gCACA0E;8BACA;;8BAEA;8BAAA,8DACAQ;gCAAA;gCAAA;kCAAA;oCAAA;sCAAA;wCACAC;wCACAL;wCAAA;wCAGA;wCACA;;wCAEA;wCAAA;wCAAA,OACA;0CACA5C;4CACA6B;4CACAC;8CACA;gDACAiB;;gDAEA;gDACA;kDACA;gDACA;;gDAEA;gDACA/C;kDACAgC;kDACAC;kDACAC,0BACA;gDACA;gDAEAgB;8CACA;gDACAC;8CACA;4CACA;4CACAjB;8CACAnC;8CACAoD;4CACA;0CACA;wCACA;sCAAA;wCAAA;wCAAA,OAGA;0CAAA;wCAAA;sCAAA;wCAAA;wCAAA;sCAAA;wCAAA;wCAAA;wCAGApD;wCACA;sCAAA;wCAAA;wCAEA;wCACA2C;wCACA;0CACA;wCACA;wCAAA;sCAAA;sCAAA;wCAAA;oCAAA;kCAAA;gCAAA;8BAAA;8BApDAM;4BAAA;8BAAA;gCAAA;gCAAA;8BAAA;8BAAA;4BAAA;8BAAAA;8BAAA;8BAAA;4BAAA;8BAwDAhD;;8BAEA;8BAAA;8BAAA,OACA;4BAAA;8BAEA;8BACA;gCACAA;kCACAlC;kCACAmC;gCACA;8BACA;gCACAD;kCACAlC;kCACAqE;kCACAC;gCACA;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAEA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAgB;MACA;MACA;MACA;QACAtF;QACAC;QACApB;MACA;MACA;MACA;IACA;IAEA;IACA0G;MACA;MACA;QACA;UAAA;QAAA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;UAAA;QAAA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MAEA;QACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAR,yBAEA;gBACAS;gBAAA,KAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA1D;kBACAlC;kBACAqE;kBACAL;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAvB;gCAAA;gCAAA;8BAAA;8BAAA;8BAEAP;gCACAlC;8BACA;;8BAEA;8BAAA,MACAmF;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA;8BAAA,OAEA;4BAAA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAEAlD;8BACAC;gCACAlC;gCACAmC;8BACA;8BACAD;8BAAA;4BAAA;8BAAA;8BAAA,OAMAI;gCACAnE;gCACAC;kCACAmE;kCACAnE;oCAAA0F;kCAAA;gCACA;8BACA;4BAAA;8BANA+B;8BAQA3D;8BAEA;gCACAA;kCACAlC;kCACAmC;gCACA;gCACA;gCACA;gCACA;8BACA;gCACAD;kCACAlC;kCACAmC;gCACA;8BACA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAEAD;8BACAD;8BACAC;gCACAlC;gCACAmC;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAGA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAGAgD;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACAjD;kBACAlC;kBACAmC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAF;gBACAC;kBACAlC;kBACAmC;gBACA;gBAAA;cAAA;gBAIAwD;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKA5D;kBACA6D;kBAAA;kBACA7F;gBACA;cAAA;gBAHAuC;gBAeAkD;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA,uCACAlD;gBAAA;gBAAA;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA0C;gBACAa;kBACA7F;kBACAG;kBACAD;kBACAD;kBAAA;kBACA6F;gBACA,GAEA;gBAAA;gBAAA;gBAAA,OAEA3D;kBACA4B;kBACAgC;gBACA;cAAA;gBAHAC;gBAIAH;;gBAEA;gBACA;gBACAL;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA1D;gBACAC;kBACAlC;kBACAmC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBACA;gBAEAD;kBACAlC;kBACAmC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAF;gBAEAC;kBACAlC;kBACAmC;kBACAiE;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAUA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBACA;gBAAA;gBAGAnE;kBACAlC;kBACA0E;gBACA;gBAEAiB;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAzD;gBACAA;kBACAlC;kBACAmC;gBACA;gBAAA;cAAA;gBAIA;gBACAmE;kBACAxC;kBACA9D;kBACAC;kBACApB;gBACA,GAEA;gBACA0D;gBACA;kBACAA;kBACA;kBACA;oBACA+D;kBACA;gBACA;kBACA;kBACA;oBACAA;oBACAA;oBACAA;oBACAA;kBACA;gBACA;gBAAA;gBAAA,OAEAhE;kBACAnE;kBACAC;oBACAmE;oBACAnE;kBACA;gBACA;cAAA;gBANAqE;gBAQAP;gBAEA;kBACAA;oBACAlC;oBACAmC;kBACA;kBACA;kBACA;gBACA;kBACAD;oBACAlC;oBACAmC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;gBACAD;gBACAC;kBACAlC;kBACAmC;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAoE;MAAA;MACA;MACA;MAEArE;QACAlC;QACAqE;QACAL;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACAvB;sBAAA;sBAAA;oBAAA;oBAAA;oBAEAP;sBACAlC;oBACA;oBAAA,KAIAwG;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA,OAEAlE;sBACAnE;sBACAC;wBACAmE;wBACAnE;0BAAA0F;wBAAA;sBACA;oBACA;kBAAA;oBANA+B;oBAAA;oBAAA;kBAAA;oBAAA,KAUAhC;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA;oBAAA,OAGA;kBAAA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAEA5B;oBACAC;sBACAlC;sBACAmC;oBACA;oBACAD;oBAAA;kBAAA;oBAAA;oBAAA,OAMAI;sBACAnE;sBACAC;wBACAmE;wBACAnE;0BAAA0F;wBAAA;sBACA;oBACA;kBAAA;oBANA+B;kBAAA;oBASA3D;oBAEA;sBACAA;wBACAlC;wBACAmC;sBACA;sBACA;oBACA;sBACAD;wBACAlC;wBACAmC;sBACA;oBACA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAEAD;oBACAD;oBACAC;sBACAlC;sBACAmC;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAGA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACAsE;MACA;QACA;QACA;QACA;UACA;QACA;QACA;QACA;QACA;MACA;QACAxE;QACA;MACA;IACA;IAEA;IACAyE;MAAA;MACA;QAAA;MAAA,MACA7C;IACA;IAEA;IACA8C;MAAA;MACA;QAAA;MAAA,MACA9C;IACA;IAEA;IACA+C;MACA;QACA1E;UACAlC;UACAmC;QACA;QACA;MACA;MACA;MACA;IACA;IAEA;IACA0E;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA9G;QACAC;QACApB;QACAqB;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAwG;MACA;MACA;MACA;QACA/G;QACAC;QACApB;QACAsB;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACA0G;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACAjF;UACAlC;UACAmC;QACA;QACA;MACA;MACA;IACA;IAEA;IACAiF;MACA;MACA;QAAAjJ;QAAAU;MAAA;IACA;IAEA;IACAwI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAnF;kBACAlC;kBACAmC;gBACA;gBAAA;cAAA;gBAAA,KAKA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBACA;gBAAA;gBAGA;gBACAE;gBAAA;gBAAA,OAEAC;kBACAnE;kBACAC;oBACAmE;oBACAnE;oBACAoE;kBACA;gBACA;cAAA;gBAPAC;gBAAA,MASAA;kBAAA;kBAAA;gBAAA;gBACAP;kBACAlC;kBACAmC;gBACA;gBACA;kBAAAhE;kBAAAU;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAEAqD;kBACAlC;kBACAmC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAF;gBACAC;kBACAlC;kBACAmC;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAmF;MAAA;MACA;MACA;;MAEA;MACA;;MAEA;MACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAxF;kBACAlC;kBACAmC;gBACA;gBAAA;cAAA;gBAAA,KAKA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBACA;gBAAA;gBAGAD;kBACAlC;gBACA;gBAEAqC;gBAAA;gBAAA,OACAC;kBACAnE;kBACAC;oBACAmE;oBACAnE;sBACA0F;sBACA3F;sBACAU;oBACA;kBACA;kBACA2D;gBACA;cAAA;gBAXAM;gBAaAZ;gBAAA,MAEAY;kBAAA;kBAAA;gBAAA;gBACAZ;kBACAlC;kBACAmC;gBACA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAEAD;kBACAlC;kBACAmC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAF;gBACAC;gBACAA;kBACAlC;kBACAmC;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAwF;MAAA;MACA;MACA;;MAEA;MACAJ;QACArF;UACAlC;UACAqE;UACAL;YACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACA4D;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA1F;kBACAlC;gBACA;gBAEAqC;gBAAA;gBAAA,OACAC;kBACAnE;kBACAC;oBACAmE;oBACAnE;sBACA0F;oBACA;kBACA;kBACAtB;gBACA;cAAA;gBATAM;gBAWAZ;gBAAA,MAEAY;kBAAA;kBAAA;gBAAA;gBACAZ;kBACAlC;kBACAmC;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAEAD;kBACAlC;kBACAmC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAF;gBACAC;gBACAA;kBACAlC;kBACAmC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA0F;MAAA;MAEA;MACA3F;QACA6D;QACA7F;QACA8D;UACA;UACA;UACA;UACA;;UAEA;UACA;YACA;UACA;;UAEA;UACA;QACA;QACAI;UACAnC;UACAC;YACAlC;YACAmC;YACAiE;UACA;QACA;MACA;IA+BA;IAEA;IACA0B;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBACA5F;kBACAlC;kBACAmC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAF;gBACAC;kBACAlC;kBACAmC;gBACA;gBAAA;cAAA;gBAKA;gBACA;gBACA;gBACA;gBACA;gBACA;;gBAEA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA4F;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAEA;MACA9F;QACA6D;QAAA;QACA7F;QACA8D;UACA;UACA;UACA;YACA9B;cACAlC;cACAmC;YACA;YACA;UACA;;UAEA;UACA;YACA;UACA;;UAEA;UACA;QACA;QACAiC;UACAnC;UACAC;YACAlC;YACAmC;YACAiE;UACA;QACA;MACA;IAoCA;IAEA;IACA6B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA9C,wCAEA;gBACAS;gBAAA,KAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA1D;kBACAlC;kBACAqE;kBACAL;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAvB;gCAAA;gCAAA;8BAAA;8BAAA,MAEA0C;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA;8BAAA,OAEA;4BAAA;8BACAjD;gCACAlC;gCACAmC;8BACA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAEAF;8BACAC;gCACAlC;gCACAmC;8BACA;8BAAA;4BAAA;8BAIA;8BACA;8BACA;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAEA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAGAgD;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACAjD;kBACAlC;kBACAmC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAF;gBACAC;kBACAlC;kBACAmC;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA+F;MAAA;MAEA;MACAhG;QACA6D;QACA7F;QACA8D;UACA;UACA;UACA;UACA;;UAEA;UACA;QACA;QACAI;UACAnC;UACAC;YACAlC;YACAmC;YACAiE;UACA;QACA;MACA;IA0BA;IAEA;IACA+B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAxC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAzD;kBACAlC;kBACAmC;gBACA;gBAAA;cAAA;gBAIA;gBACAD;kBACAlC;kBACAqE;kBACAL;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAvB;gCAAA;gCAAA;8BAAA;8BAAA;8BAEAP;gCACAlC;8BACA;;8BAEA;8BAAA,KACA2F;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA;8BAAA,OAGA;4BAAA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAEA1D;8BACAC;gCACAlC;gCACAmC;8BACA;8BACAD;8BAAA;4BAAA;8BAAA;8BAAA,OAMAI;gCACAnE;gCACAC;kCACAmE;kCACAnE;oCAAA0F;kCAAA;gCACA;8BACA;4BAAA;8BANA+B;8BAQA3D;8BAEA;gCACAA;kCACAlC;kCACAmC;gCACA;gCACA;gCACA;gCACA;8BACA;gCACAD;kCACAlC;kCACAmC;gCACA;8BACA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAEAD;8BACAD;8BACAC;gCACAlC;gCACAmC;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAGA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAiG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAGA9F;kBACA4B;kBACAgC;kBACAmC;gBACA;cAAA;gBAJAvF;gBAMA;gBAEAZ;kBACAlC;kBACAmC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAF;gBACAC;kBACAlC;kBACAmC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAmG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAGAC;kBAAA;oBAAA;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;4BAAA;4BAAA,OAEAjG;8BACA4B;8BACAgC;8BACAmC;4BACA;0BAAA;4BAJAvF;4BAAA,mCAQA;8BACA3C;8BACAG;8BACAD;8BACAD;8BACA6F;4BACA;0BAAA;4BAAA;4BAAA;4BAEAhE;4BAAA,mCACA;0BAAA;0BAAA;4BAAA;wBAAA;sBAAA;oBAAA;kBAAA,CAEA;kBAAA;oBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEAD;cAAA;gBAAAwG;gBACAC;kBAAA;gBAAA;gBAEA;kBACA;kBACAvG;oBACAlC;oBACAmC;kBACA;gBACA;kBACAD;oBACAlC;oBACAmC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAF;gBACAC;kBACAlC;kBACAmC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAuG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAGApG;kBACA4B;kBACAgC;kBACAmC;gBACA;cAAA;gBAJAvF;gBAMA;gBACA;gBAEAZ;kBACAlC;kBACAmC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAF;gBACAC;kBACAlC;kBACAmC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAwG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAzG;kBACAlC;kBACAmC;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBAAA;gBAGA;gBACAE;gBAGA;kBACAE;kBACAnE;gBACA;kBACAmE;kBACAnE;gBACA;gBAAA;gBAAA,OAEAkE;kBACAnE;kBACAC;oBACAmE;oBACAnE;oBACAoE;kBACA;gBACA;cAAA;gBAPAC;gBASA;kBACAP;oBACAlC;oBACAmC;kBACA;kBACA;kBACA;gBACA;kBACAD;oBACAlC;oBACAmC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAF;gBACAC;kBACAlC;kBACAmC;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAyG;MACA;QACA;QACAC;UACAC;YAAA3G;YAAA4G;UAAA;UACApD;YAAAxD;YAAA4G;UAAA;UACAC;YAAA7G;YAAA4G;UAAA;UACAE;YAAA9G;YAAA4G;UAAA;UACAG;YAAA/G;YAAA4G;UAAA;UACAI;YAAAhH;YAAA4G;UAAA;UACAK;YAAAjH;YAAA4G;UAAA;UACAM;YAAAlH;YAAA4G;UAAA;UACAO;YAAAnH;YAAA4G;UAAA;UACAQ;YAAApH;YAAA4G;UAAA;UACAS;YAAArH;YAAA4G;UAAA;UACAU;YAAAtH;YAAA4G;UAAA;UACAW;YAAAvH;YAAA4G;UAAA;UACAY;YAAAxH;YAAA4G;UAAA;QACA;QACA;QACAa;UACAC;UACAC;QACA;QACA;QACAC;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;QACApB;QACAnD;QACAqD;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;IACA;IAEA;IACAQ;MACA;;MAEA;MACA;QACArB;QACAnD;QACAqD;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;IACA;IAIA;IACAS;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC,mBACAC;kBAAA;gBAAA,GACAC;kBACA;kBACA;oBACA;kBACA;kBACA;kBACA;oBACA;oBACA;sBACA;oBACA;oBACA;kBACA;kBACA;kBACA;gBACA,GACAD;kBAAA;gBAAA;gBAAA,MAEAD;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA/H;kBACAnE;kBACAC;oBAAAoM;kBAAA;gBACA;cAAA;gBAHA1H;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA,MAIAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAKAb;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACAwI;MAAA;MAAA;MACAxI;MACA;QACAC;UACAlC;UACAmC;UACAiE;QACA;MACA;MACA;IACA;IAEA;IACAsE;MACAxI;QACAlC;QACAmC;QACAiE;MACA;IACA;IAEA;IACAuE;MACA;MACA;MACA;MACA;QACAC;QACA9F;MACA;MACA;IACA;IAEA;IACA+F;MACA;QACA;MACA;MACA;MAEA;QACA;MACA;;MAEA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACztFA;AAAA;AAAA;AAAA;AAAyqC,CAAgB,+oCAAG,EAAC,C;;;;;;;;;;;ACA7rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/ucenter_pkg/study-center.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/ucenter_pkg/study-center.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./study-center.vue?vue&type=template&id=93367c6c&scoped=true&\"\nvar renderjs\nimport script from \"./study-center.vue?vue&type=script&lang=js&\"\nexport * from \"./study-center.vue?vue&type=script&lang=js&\"\nimport style0 from \"./study-center.vue?vue&type=style&index=0&id=93367c6c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"93367c6c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/ucenter_pkg/study-center.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./study-center.vue?vue&type=template&id=93367c6c&scoped=true&\"", "var components\ntry {\n  components = {\n    uniSearchBar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar\" */ \"@/uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-load-more/components/uni-load-more/uni-load-more\" */ \"@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.loading || (_vm.documents.length === 0 && !_vm.hasInitialized)\n  var g1 = !g0 ? _vm.documents.length : null\n  var l0 =\n    !g0 && !(g1 === 0)\n      ? _vm.__map(_vm.documents, function (document, __i1__) {\n          var $orig = _vm.__get_orig(document)\n          var m0 = !(document.type === \"collection\")\n            ? _vm.getFileIconClass(document.file_type)\n            : null\n          var m1 = !(document.type === \"collection\")\n            ? _vm.getFileIconColor(document.file_type)\n            : null\n          var g2 =\n            document.type === \"collection\" && document.files\n              ? document.files.length\n              : null\n          var m2 = !(document.type === \"collection\")\n            ? _vm.formatFileSize(document.file_size)\n            : null\n          var m3 = _vm.formatTime(document.create_time)\n          var m4 = _vm.canEditDocument(document)\n          var m5 = _vm.canDeleteDocument(document)\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            g2: g2,\n            m2: m2,\n            m3: m3,\n            m4: m4,\n            m5: m5,\n          }\n        })\n      : null\n  var g3 = _vm.documents.length\n  var g4 = g3 > 0 && !_vm.hasMore ? _vm.documents.length : null\n  var m6 = _vm.getCurrentCategoryName() || \"请选择分类\"\n  var m7 = _vm.isEditMode\n    ? _vm.getCurrentEditingDocument() &&\n      _vm.getCurrentEditingDocument().type === \"single\" &&\n      _vm.getCurrentEditingDocument().file_name\n    : null\n  var m8 = _vm.isEditMode && m7 ? _vm.getCurrentEditingDocumentFileName() : null\n  var m9 = _vm.isEditMode\n    ? _vm.getCurrentEditingDocument() &&\n      _vm.getCurrentEditingDocument().type === \"collection\"\n    : null\n  var m10 = _vm.isEditMode && m9 ? _vm.getCurrentEditingDocument() : null\n  var m11 =\n    _vm.isEditMode && m9\n      ? _vm.getCurrentEditingDocument().files &&\n        _vm.getCurrentEditingDocument().files.length > 0\n      : null\n  var l1 =\n    _vm.isEditMode && m9 && m11\n      ? _vm.__map(\n          _vm.getCurrentEditingDocument().files,\n          function (file, index) {\n            var $orig = _vm.__get_orig(file)\n            var m12 = _vm.getFileIconClass(file.file_type)\n            var m13 = _vm.getFileIconColor(file.file_type)\n            var m14 = _vm.formatFileSize(file.file_size)\n            return {\n              $orig: $orig,\n              m12: m12,\n              m13: m13,\n              m14: m14,\n            }\n          }\n        )\n      : null\n  var m15 =\n    !_vm.isEditMode &&\n    _vm.uploadForm.type === \"single\" &&\n    _vm.uploadForm.file_name\n      ? _vm.getFileIconClass(_vm.uploadForm.file_type)\n      : null\n  var m16 =\n    !_vm.isEditMode &&\n    _vm.uploadForm.type === \"single\" &&\n    _vm.uploadForm.file_name\n      ? _vm.getFileIconColor(_vm.uploadForm.file_type)\n      : null\n  var m17 =\n    !_vm.isEditMode &&\n    _vm.uploadForm.type === \"single\" &&\n    _vm.uploadForm.file_name\n      ? _vm.formatFileSize(_vm.uploadForm.file_size)\n      : null\n  var g5 =\n    !_vm.isEditMode && _vm.uploadForm.type === \"collection\"\n      ? _vm.uploadForm.files && _vm.uploadForm.files.length > 0\n      : null\n  var l2 =\n    !_vm.isEditMode && _vm.uploadForm.type === \"collection\" && g5\n      ? _vm.__map(_vm.uploadForm.files, function (file, index) {\n          var $orig = _vm.__get_orig(file)\n          var m18 = _vm.getFileIconClass(file.file_type)\n          var m19 = _vm.getFileIconColor(file.file_type)\n          var m20 = _vm.formatFileSize(file.file_size)\n          return {\n            $orig: $orig,\n            m18: m18,\n            m19: m19,\n            m20: m20,\n          }\n        })\n      : null\n  var g6 = _vm.categoryOptions.length\n  var g7 = _vm.isCategoryManagerVisible\n    ? !_vm.newCategory.name.trim() || _vm.isSubmitting\n    : null\n  var g8 = _vm.isCategoryManagerVisible ? _vm.categories.length : null\n  var l3 = _vm.__map(_vm.currentCollectionFiles, function (file, index) {\n    var $orig = _vm.__get_orig(file)\n    var g9 =\n      _vm.currentCollectionFileCount > 0\n        ? _vm.downloadingFiles.includes(index)\n        : null\n    var m21 =\n      _vm.currentCollectionFileCount > 0\n        ? _vm.getFileIconClass(file.file_type)\n        : null\n    var m22 =\n      _vm.currentCollectionFileCount > 0\n        ? _vm.getFileIconColor(file.file_type)\n        : null\n    var m23 =\n      _vm.currentCollectionFileCount > 0\n        ? _vm.formatFileSize(file.file_size)\n        : null\n    var g10 =\n      _vm.currentCollectionFileCount > 0\n        ? _vm.downloadedFiles.includes(index)\n        : null\n    var g11 =\n      _vm.currentCollectionFileCount > 0 && !g10\n        ? _vm.downloadingFiles.includes(index)\n        : null\n    return {\n      $orig: $orig,\n      g9: g9,\n      m21: m21,\n      m22: m22,\n      m23: m23,\n      g10: g10,\n      g11: g11,\n    }\n  })\n  var g12 =\n    _vm.currentCollectionFileCount > 0 ? _vm.downloadingFiles.length : null\n  var m24 = _vm.getFileIconClass(\n    _vm.currentPreviewDocument ? _vm.currentPreviewDocument.file_type : \"\"\n  )\n  var m25 = _vm.getFileIconColor(\n    _vm.currentPreviewDocument ? _vm.currentPreviewDocument.file_type : \"\"\n  )\n  var m26 = _vm.currentPreviewDocument\n    ? _vm.formatFileSize(_vm.currentPreviewDocument.file_size)\n    : null\n  var m27 = _vm.currentPreviewDocument\n    ? _vm.formatTime(_vm.currentPreviewDocument.create_time)\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, index) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        index = _temp2.index\n      var _temp, _temp2\n      return _vm.removeFileFromCollectionEdit(index)\n    }\n    _vm.e1 = function ($event) {\n      _vm.isEditMode ? _vm.submitEdit() : _vm.submitUpload()\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        g3: g3,\n        g4: g4,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        l1: l1,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        g5: g5,\n        l2: l2,\n        g6: g6,\n        g7: g7,\n        g8: g8,\n        l3: l3,\n        g12: g12,\n        m24: m24,\n        m25: m25,\n        m26: m26,\n        m27: m27,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./study-center.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./study-center.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"study-center-container\">\n\t\t<!-- 搜索栏 -->\n\t\t<view class=\"search-bar\">\n\t\t\t<uni-search-bar \n\t\t\t\tv-model=\"searchKeyword\" \n\t\t\t\t@confirm=\"onSearch\" \n\t\t\t\t@input=\"onSearchInput\"\n\t\t\t\tplaceholder=\"搜索文档标题、描述或文件名\"\n\t\t\t\t:focus=\"false\"\n\t\t\t\t:show-action=\"false\"\n\t\t\t\tbg-color=\"#f5f5f5\"\n\t\t\t></uni-search-bar>\n\t\t</view>\n\t\t\n\t\t<!-- 分类筛选 -->\n\t\t<view class=\"category-filter\">\n\t\t\t<scroll-view scroll-x=\"true\" class=\"category-scroll\">\n\t\t\t\t<view class=\"category-list\">\n\t\t\t\t\t<view \n\t\t\t\t\t\tclass=\"category-item\" \n\t\t\t\t\t\t:class=\"{ active: selectedCategoryId === '' }\"\n\t\t\t\t\t\t@click=\"selectCategory('')\"\n\t\t\t\t\t>\n\t\t\t\t\t\t全部\n\t\t\t\t\t</view>\n\t\t\t\t\t<view \n\t\t\t\t\t\tv-for=\"category in categories\" \n\t\t\t\t\t\t:key=\"category._id\"\n\t\t\t\t\t\tclass=\"category-item\"\n\t\t\t\t\t\t:class=\"{ active: selectedCategoryId === category._id }\"\n\t\t\t\t\t\t@click=\"selectCategory(category._id)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t{{ category.name }}\n\t\t\t\t\t\t<text class=\"category-count\">({{ category.document_count }})</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<!-- 分类管理按钮 -->\n\t\t\t\t\t<view \n\t\t\t\t\t\tv-if=\"canManageCategory\"\n\t\t\t\t\t\tclass=\"category-manage-btn\"\n\t\t\t\t\t\t@click=\"showCategoryManager\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<uni-icons type=\"gear\" size=\"18\" color=\"#666\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</view>\n\t\t\n\t\t<!-- 文档列表 -->\n\t\t<view class=\"document-list\">\n\t\t\t<view v-if=\"loading || documents.length === 0 && !hasInitialized\" class=\"loading-container\">\n\t\t\t\t<uni-load-more status=\"loading\"></uni-load-more>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view v-else-if=\"documents.length === 0\" class=\"empty-container\">\n\t\t\t\t<p-empty-state \n\t\t\t\t\timage=\"/static/empty/empty_data.png\"\n\t\t\t\t\ttitle=\"暂无文档\"\n\t\t\t\t\tdescription=\"还没有上传任何学习资料\"\n\t\t\t\t></p-empty-state>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view v-else>\n\t\t\t\t<view \n\t\t\t\t\tv-for=\"document in documents\" \n\t\t\t\t\t:key=\"document._id\"\n\t\t\t\t\tclass=\"document-item\"\n\t\t\t\t\t@click=\"viewDocument(document)\"\n\t\t\t\t>\n\t\t\t\t\t<view class=\"document-icon\">\n\t\t\t\t\t\t<uni-icons \n\t\t\t\t\t\t\t:type=\"document.type === 'collection' ? 'paperclip' : getFileIconClass(document.file_type)\" \n\t\t\t\t\t\t\tsize=\"24\" \n\t\t\t\t\t\t\t:color=\"document.type === 'collection' ? '#ff9800' : getFileIconColor(document.file_type)\"\n\t\t\t\t\t\t></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"document-content\">\n\t\t\t\t\t\t<view class=\"document-title\">\n\t\t\t\t\t\t\t{{ document.title }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"document-info\">\n\t\t\t\t\t\t\t<text class=\"category-tag\">{{ document.category_name }}</text>\n\t\t\t\t\t\t\t<text v-if=\"document.type === 'collection'\" class=\"file-count\">{{ document.files ? document.files.length : 0 }}个文件</text>\n\t\t\t\t\t\t\t<text v-else class=\"file-size\">{{ formatFileSize(document.file_size) }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"document-meta\">\n\t\t\t\t\t\t\t<view class=\"meta-item\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"person\" size=\"14\" color=\"#666\"></uni-icons>\n\t\t\t\t\t\t\t\t<text class=\"uploader\">{{ document.uploader_name }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"meta-item\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"14\" color=\"#666\"></uni-icons>\n\t\t\t\t\t\t\t\t<text class=\"upload-time\">{{ formatTime(document.create_time) }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"meta-item\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"download\" size=\"14\" color=\"#666\"></uni-icons>\n\t\t\t\t\t\t\t\t<text class=\"download-count\">下载 {{ document.download_count }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-if=\"document.description\" class=\"document-desc\">\n\t\t\t\t\t\t\t{{ document.description }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"document-actions\">\n\t\t\t\t\t\t<view class=\"action-btn download-btn\" @click.stop=\"downloadDocument(document)\">\n\t\t\t\t\t\t\t<uni-icons type=\"download\" size=\"18\" color=\"#4caf50\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tv-if=\"canEditDocument(document)\"\n\t\t\t\t\t\t\tclass=\"action-btn edit-btn\" \n\t\t\t\t\t\t\************=\"editDocument(document)\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<uni-icons type=\"compose\" size=\"18\" color=\"#ff9800\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tv-if=\"canDeleteDocument(document)\"\n\t\t\t\t\t\t\tclass=\"action-btn delete-btn\" \n\t\t\t\t\t\t\************=\"deleteDocument(document)\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<uni-icons type=\"trash\" size=\"18\" color=\"#f44336\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 加载更多 -->\n\t\t\t<view v-if=\"documents.length > 0\" class=\"load-more\">\n\t\t\t\t<view v-if=\"hasMore\" class=\"load-more-content\">\n\t\t\t\t\t<uni-load-more \n\t\t\t\t\t\t:status=\"loadMoreStatus\" \n\t\t\t\t\t\t@clickLoadMore=\"loadMore\"\n\t\t\t\t\t></uni-load-more>\n\t\t\t\t</view>\n\t\t\t\t<view v-else class=\"no-more-content\">\n\t\t\t\t\t<text class=\"no-more-text\">已显示全部 {{ documents.length }} 个文档</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 悬浮按钮 -->\n\t\t<view v-if=\"canUpload\" class=\"fab-container\">\n\t\t\t<view class=\"fab-btn\" @click=\"showUploadModal\">\n\t\t\t\t<uni-icons type=\"plus\" size=\"24\" color=\"#fff\"></uni-icons>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 上传弹窗 -->\n\t\t<uni-popup ref=\"uploadPopup\" type=\"center\" :mask-click=\"false\" :animation=\"false\" class=\"popup-high-level\">\n\t\t\t<view class=\"upload-modal\">\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<text class=\"modal-title\">{{ isEditMode ? '编辑文档' : '上传文档' }}</text>\n\t\t\t\t\t<view class=\"modal-close\" @click=\"closeUploadModal\">×</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"modal-content\">\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">文档标题</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tv-model=\"documentTitle\" \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\tplaceholder=\"请输入文档标题\"\n\t\t\t\t\t\t\tmaxlength=\"100\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">选择分类</text>\n\t\t\t\t\t\t<view class=\"category-selector\" @click=\"openCategorySelector\">\n\t\t\t\t\t\t\t<view class=\"picker-input\">\n\t\t\t\t\t\t\t\t{{ getCurrentCategoryName() || '请选择分类' }}\n\t\t\t\t\t\t\t\t<text class=\"picker-arrow\">></text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">文档描述</text>\n\t\t\t\t\t\t<textarea \n\t\t\t\t\t\t\tv-model=\"documentDescription\" \n\t\t\t\t\t\t\tclass=\"form-textarea\" \n\t\t\t\t\t\t\tplaceholder=\"请输入文档描述（可选）\"\n\t\t\t\t\t\t\tmaxlength=\"500\"\n\t\t\t\t\t\t\tauto-height\n\t\t\t\t\t\t></textarea>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"!isEditMode\" class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">文档类型</text>\n\t\t\t\t\t\t<view class=\"type-selector\">\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tclass=\"type-option\" \n\t\t\t\t\t\t\t\t:class=\"{ active: uploadForm.type === 'single' }\"\n\t\t\t\t\t\t\t\t@click=\"selectDocumentType('single')\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<uni-icons type=\"paperclip\" size=\"20\" :color=\"uploadForm.type === 'single' ? '#007aff' : '#999'\"></uni-icons>\n\t\t\t\t\t\t\t\t<text class=\"type-text\" :class=\"{ active: uploadForm.type === 'single' }\">单个文档</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tclass=\"type-option\" \n\t\t\t\t\t\t\t\t:class=\"{ active: uploadForm.type === 'collection' }\"\n\t\t\t\t\t\t\t\t@click=\"selectDocumentType('collection')\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<uni-icons type=\"folder-add\" size=\"20\" :color=\"uploadForm.type === 'collection' ? '#007aff' : '#999'\"></uni-icons>\n\t\t\t\t\t\t\t\t<text class=\"type-text\" :class=\"{ active: uploadForm.type === 'collection' }\">文档集合</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"isEditMode\" class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">文档文件</text>\n\t\t\t\t\t\t<!-- 单个文档编辑 -->\n\t\t\t\t\t\t<view v-if=\"getCurrentEditingDocument() && getCurrentEditingDocument().type === 'single' && getCurrentEditingDocument().file_name\" class=\"current-document\">\n\t\t\t\t\t\t\t<view class=\"doc-icon\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"paperclip\" size=\"24\" color=\"#007aff\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"doc-info\">\n\t\t\t\t\t\t\t\t<text class=\"doc-name\">{{ getCurrentEditingDocumentFileName() }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"doc-actions\">\n\t\t\t\t\t\t\t\t<view class=\"action-btn remove-btn\" @click=\"removeFileInEdit\" title=\"删除文档\">\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"trash\" size=\"14\" color=\"#f44336\"></uni-icons>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 集合文档编辑 -->\n\t\t\t\t\t\t<view v-if=\"getCurrentEditingDocument() && getCurrentEditingDocument().type === 'collection'\" class=\"collection-edit\">\n\t\t\t\t\t\t\t<view class=\"collection-header\">\n\t\t\t\t\t\t\t\t<view class=\"collection-icon\">\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"folder-add\" size=\"24\" color=\"#007aff\"></uni-icons>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"collection-info\">\n\t\t\t\t\t\t\t\t\t<text class=\"collection-title\">{{ getCurrentEditingDocument().title }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 显示集合中的文件列表 -->\n\t\t\t\t\t\t\t<view v-if=\"getCurrentEditingDocument().files && getCurrentEditingDocument().files.length > 0\" class=\"collection-files\">\n\t\t\t\t\t\t\t\t<view v-for=\"(file, index) in getCurrentEditingDocument().files\" :key=\"index\" class=\"collection-file-item\">\n\t\t\t\t\t\t\t\t\t<view class=\"file-icon\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons :type=\"getFileIconClass(file.file_type)\" size=\"20\" :color=\"getFileIconColor(file.file_type)\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"file-details\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"file-name\">{{ file.file_name }}</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"file-size\">{{ formatFileSize(file.file_size) }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"file-actions\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"action-btn remove-btn\" @click=\"removeFileFromCollectionEdit(index)\" title=\"删除文件\">\n\t\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"trash\" size=\"14\" color=\"#f44336\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 添加新文件的按钮 -->\n\t\t\t\t\t\t\t<view class=\"collection-actions\">\n\t\t\t\t\t\t\t\t<view class=\"collection-add-btn\" @click=\"addFilesToCollection\">\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"plus\" size=\"18\" color=\"#333\"></uni-icons>\n\t\t\t\t\t\t\t\t\t<text>添加文件</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"!isEditMode\" class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">{{ uploadForm.type === 'collection' ? '选择文件（可多选）' : '选择文件' }}</text>\n\t\t\t\t\t\t<view class=\"file-upload-container\">\n\t\t\t\t\t\t\t<!-- 单文件上传 -->\n\t\t\t\t\t\t\t<template v-if=\"uploadForm.type === 'single'\">\n\t\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\t\tv-if=\"!uploadForm.file_name\" \n\t\t\t\t\t\t\t\t\tclass=\"upload-area\" \n\t\t\t\t\t\t\t\t\t@click=\"chooseFile\"\n\t\t\t\t\t\t\t\t\t:class=\"{ 'uploading': uploading }\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<view class=\"upload-icon\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"cloud-upload\" size=\"48\" color=\"#007aff\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<text class=\"upload-text\">{{ uploading ? '上传中...' : '点击选择文件' }}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"upload-hint\">支持 PDF、Word、Excel、PPT 等格式</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<view v-if=\"uploadForm.file_name\" class=\"file-selected\">\n\t\t\t\t\t\t\t\t\t<view class=\"file-icon\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons :type=\"getFileIconClass(uploadForm.file_type)\" size=\"32\" :color=\"getFileIconColor(uploadForm.file_type)\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"file-details\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"file-name\">{{ uploadForm.file_name }}</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"file-size\">{{ formatFileSize(uploadForm.file_size) }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"file-actions\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"action-btn remove-btn\" @click=\"removeFile\" title=\"删除文件\">\n\t\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"trash\" size=\"16\" color=\"#f44336\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 多文件上传 -->\n\t\t\t\t\t\t\t<template v-if=\"uploadForm.type === 'collection'\">\n\t\t\t\t\t\t\t\t<view class=\"multi-file-upload\">\n\t\t\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\t\t\tclass=\"upload-area multi-upload\" \n\t\t\t\t\t\t\t\t\t\t@click=\"chooseMultipleFiles\"\n\t\t\t\t\t\t\t\t\t\t:class=\"{ 'uploading': uploading }\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<view class=\"upload-icon\">\n\t\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"cloud-upload\" size=\"48\" color=\"#007aff\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"upload-text\">{{ uploading ? '上传中...' : '点击选择多个文件' }}</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"upload-hint\">支持同时选择多个文件组成集合</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t<view v-if=\"uploadForm.files && uploadForm.files.length > 0\" class=\"files-list\">\n\t\t\t\t\t\t\t\t\t\t<view v-for=\"(file, index) in uploadForm.files\" :key=\"index\" class=\"file-item\">\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"file-icon\">\n\t\t\t\t\t\t\t\t\t\t\t\t<uni-icons :type=\"getFileIconClass(file.file_type)\" size=\"24\" :color=\"getFileIconColor(file.file_type)\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"file-details\">\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"file-name\">{{ file.file_name }}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"file-size\">{{ formatFileSize(file.file_size) }}</text>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"file-actions\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"action-btn remove-btn\" @click=\"removeFileFromCollection(index)\" title=\"删除文件\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"trash\" size=\"16\" color=\"#f44336\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"modal-footer\">\n\t\t\t\t\t<button \n\t\t\t\t\t\tclass=\"btn btn-cancel\" \n\t\t\t\t\t\t@click=\"closeUploadModal\"\n\t\t\t\t\t\t:disabled=\"isSubmitting || uploading\"\n\t\t\t\t\t>\n\t\t\t\t\t\t{{ isSubmitting || uploading ? '处理中...' : '取消' }}\n\t\t\t\t\t</button>\n\t\t\t\t\t<button \n\t\t\t\t\t\tclass=\"btn btn-primary\" \n\t\t\t\t\t\t@click=\"isEditMode ? submitEdit() : submitUpload()\"\n\t\t\t\t\t\t:disabled=\"isEditMode ? (!canSubmitEdit || isSubmitting) : (!canSubmit || uploading || isSubmitting)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t{{ isEditMode ? (isSubmitting ? '保存中...' : '确定修改') : (uploading ? '上传中...' : (isSubmitting ? '提交中...' : '确定上传')) }}\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t\t\n\t\t<!-- 分类选择弹窗 -->\n\t\t<uni-popup ref=\"categorySelectorPopup\" type=\"bottom\" :mask-click=\"false\" :animation=\"false\" class=\"popup-high-level\">\n\t\t\t<view class=\"selector-modal-content\">\n\t\t\t\t<view class=\"selector-modal-header\">\n\t\t\t\t\t<text class=\"selector-modal-title\">选择分类</text>\n\t\t\t\t\t<view class=\"selector-modal-close\" @click=\"closeCategorySelector\">\n\t\t\t\t\t\t<uni-icons type=\"close\" size=\"22\" color=\"#999\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<scroll-view scroll-y class=\"selector-list-container\">\n\t\t\t\t\t<view v-if=\"categoryOptions.length === 0\" class=\"empty-category-hint\">\n\t\t\t\t\t\t<text class=\"empty-text\">暂无分类</text>\n\t\t\t\t\t\t<text v-if=\"canManageCategory\" class=\"empty-desc\">请先到分类管理中添加</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view \n\t\t\t\t\t\tclass=\"selector-item\" \n\t\t\t\t\t\tv-for=\"category in categoryOptions\" \n\t\t\t\t\t\t:key=\"category._id\"\n\t\t\t\t\t\t:class=\"{ 'selected': (isEditMode ? editForm.category_id : uploadForm.category_id) === category._id }\"\n\t\t\t\t\t\t@click=\"handleCategorySelect(category)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<view class=\"selector-item-content\">\n\t\t\t\t\t\t\t<text class=\"selector-name\">{{ category.name }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"selector-item-left\">\n\t\t\t\t\t\t\t<uni-icons v-if=\"(isEditMode ? editForm.category_id : uploadForm.category_id) === category._id\" type=\"checkmarkempty\" size=\"24\" color=\"#007aff\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t\t\n\t\t<!-- 分类编辑弹窗 -->\n\t\t<uni-popup ref=\"categoryEditPopup\" type=\"center\" :mask-click=\"false\" :animation=\"false\" class=\"popup-high-level\">\n\t\t\t<view class=\"category-edit-modal\">\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<text class=\"modal-title\">编辑分类</text>\n\t\t\t\t\t<view class=\"modal-close\" @click=\"closeCategoryEditModal\">×</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"modal-content\">\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">分类名称</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tv-model=\"editingCategoryName\" \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\tplaceholder=\"请输入分类名称\"\n\t\t\t\t\t\t\tmaxlength=\"50\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">分类描述</text>\n\t\t\t\t\t\t<textarea \n\t\t\t\t\t\t\tv-model=\"editingCategoryDescription\" \n\t\t\t\t\t\t\tclass=\"form-textarea\" \n\t\t\t\t\t\t\tplaceholder=\"请输入分类描述（可选）\"\n\t\t\t\t\t\t\tmaxlength=\"200\"\n\t\t\t\t\t\t\tauto-height\n\t\t\t\t\t\t></textarea>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"modal-footer\">\n\t\t\t\t\t<button \n\t\t\t\t\t\tclass=\"btn btn-cancel\" \n\t\t\t\t\t\t@click=\"closeCategoryEditModal\"\n\t\t\t\t\t\t:disabled=\"isSubmitting\"\n\t\t\t\t\t>\n\t\t\t\t\t\t{{ isSubmitting ? '处理中...' : '取消' }}\n\t\t\t\t\t</button>\n\t\t\t\t\t<button class=\"btn btn-primary\" @click=\"submitCategoryEdit\">确定修改</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t\t\n\t\t<!-- 分类管理弹窗 -->\n\t\t<view v-if=\"isCategoryManagerVisible\" class=\"category-manager-overlay\">\n\t\t\t<view class=\"category-manager-content\" @click.stop>\n\t\t\t\t<view class=\"manager-header\">\n\t\t\t\t\t<text class=\"manager-title\">分类管理</text>\n\t\t\t\t\t<view class=\"manager-close\" @click=\"closeCategoryManager\">\n\t\t\t\t\t\t<uni-icons type=\"close\" size=\"18\" color=\"#8a94a6\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 添加分类 -->\n\t\t\t\t<view class=\"manager-add-section\">\n\t\t\t\t\t<view class=\"add-form\">\n\t\t\t\t\t\t<view class=\"form-row\">\n\t\t\t\t\t\t\t<input \n\t\t\t\t\t\t\t\tv-model=\"newCategory.name\" \n\t\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\t\tplaceholder=\"分类名称\"\n\t\t\t\t\t\t\t\tmaxlength=\"50\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t<input \n\t\t\t\t\t\t\t\tv-model=\"newCategory.description\" \n\t\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\t\tplaceholder=\"分类描述（可选）\"\n\t\t\t\t\t\t\t\tmaxlength=\"200\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<button class=\"add-btn\" @click=\"createCategory\" :disabled=\"!newCategory.name.trim() || isSubmitting\">\n\t\t\t\t\t\t\t<uni-icons type=\"plus\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t\t<text>添加</text>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 分类列表 -->\n\t\t\t\t<scroll-view scroll-y class=\"manager-list\">\n\t\t\t\t\t<view v-if=\"categories.length === 0\" class=\"empty-state\">\n\t\t\t\t\t\t<uni-icons type=\"folder\" size=\"40\" color=\"#c4c4c4\"></uni-icons>\n\t\t\t\t\t\t<text class=\"empty-text\">暂无分类</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view \n\t\t\t\t\t\tv-for=\"category in categories\" \n\t\t\t\t\t\t:key=\"category._id\"\n\t\t\t\t\t\tclass=\"manager-item\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<view class=\"item-info\">\n\t\t\t\t\t\t\t<text class=\"item-name\">{{ category.name }}</text>\n\t\t\t\t\t\t\t<text class=\"item-desc\" v-if=\"category.description\">{{ category.description }}</text>\n\t\t\t\t\t\t\t<text class=\"item-count\">{{ category.document_count || 0 }}个文档</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"item-actions\">\n\t\t\t\t\t\t\t<view class=\"manager-action-btn edit-btn\" @click=\"editCategory(category)\">\n\t\t\t\t\t\t\t<uni-icons type=\"compose\" size=\"16\" color=\"#3a86ff\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"manager-action-btn delete-btn\" @click=\"confirmDeleteCategory(category)\">\n\t\t\t\t\t\t\t<uni-icons type=\"trash\" size=\"16\" color=\"#ef4444\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 文档集合下载弹窗 -->\n\t\t<uni-popup ref=\"collectionDownloadPopup\" type=\"center\" :mask-click=\"false\" :animation=\"false\" class=\"popup-high-level\">\n\t\t\t<view class=\"collection-download-modal\">\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<view class=\"header-content\">\n\t\t\t\t\t\t<view class=\"collection-info\">\n\t\t\t\t\t\t\t<uni-icons type=\"paperclip\" size=\"24\" color=\"#007aff\"></uni-icons>\n\t\t\t\t\t\t\t<text class=\"collection-title\">{{ currentCollection ? currentCollection.title : '文档集合' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"collection-desc\">{{ currentCollection ? currentCollection.description : '' }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"modal-close\" @click=\"closeCollectionDownloadModal\">\n\t\t\t\t\t\t<uni-icons type=\"close\" size=\"20\" color=\"#999\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"modal-content\">\n\t\t\t\t\t<view class=\"files-section\">\n\t\t\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t\t\t<text class=\"section-title\">选择要下载的文件</text>\n\t\t\t\t\t\t\t<text class=\"file-count\">{{ currentCollectionFileCount }} 个文件</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<scroll-view scroll-y class=\"files-list\">\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tv-if=\"currentCollectionFileCount > 0\" \n\t\t\t\t\t\t\t\tv-for=\"(file, index) in currentCollectionFiles\" \n\t\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t\tclass=\"file-item\"\n\t\t\t\t\t\t\t\t:class=\"{ 'downloading': downloadingFiles.includes(index) }\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<view class=\"file-info\">\n\t\t\t\t\t\t\t\t\t<view class=\"file-icon\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons \n\t\t\t\t\t\t\t\t\t\t\t:type=\"getFileIconClass(file.file_type)\" \n\t\t\t\t\t\t\t\t\t\t\tsize=\"20\" \n\t\t\t\t\t\t\t\t\t\t\t:color=\"getFileIconColor(file.file_type)\"\n\t\t\t\t\t\t\t\t\t\t></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"file-details\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"file-name\">{{ file.file_name }}</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"file-size\">{{ formatFileSize(file.file_size) }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<view class=\"file-actions\">\n\t\t\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\t\t\tv-if=\"downloadedFiles.includes(index)\"\n\t\t\t\t\t\t\t\t\t\tclass=\"download-success\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"checkmarkempty\" size=\"16\" color=\"#4caf50\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\t\t\tv-else-if=\"downloadingFiles.includes(index)\"\n\t\t\t\t\t\t\t\t\t\tclass=\"download-progress\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"spinner-cycle\" size=\"16\" color=\"#007aff\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\t\t\tv-else\n\t\t\t\t\t\t\t\t\t\tclass=\"download-btn\"\n\t\t\t\t\t\t\t\t\t\t@click=\"downloadCollectionFile(file, index)\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"download\" size=\"16\" color=\"#007aff\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<view v-if=\"currentCollectionFileCount === 0\" class=\"empty-files\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"paperclip\" size=\"48\" color=\"#ccc\"></uni-icons>\n\t\t\t\t\t\t\t\t<text class=\"empty-text\">暂无文件</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"modal-footer\">\n\t\t\t\t\t<button class=\"btn btn-cancel\" @click=\"closeCollectionDownloadModal\">关闭</button>\n\t\t\t\t\t<button \n\t\t\t\t\t\tv-if=\"currentCollectionFileCount > 0\"\n\t\t\t\t\t\tclass=\"btn btn-primary\" \n\t\t\t\t\t\t@click=\"downloadAllFiles\"\n\t\t\t\t\t\t:disabled=\"downloadingFiles.length > 0\"\n\t\t\t\t\t>\n\t\t\t\t\t\t下载全部\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t\t\n\t\t<!-- 文档预览弹窗 -->\n\t\t<uni-popup ref=\"documentPreviewPopup\" type=\"center\" :mask-click=\"false\" :animation=\"false\">\n\t\t\t<view class=\"document-preview-modal\">\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<view class=\"header-content\">\n\t\t\t\t\t\t<view class=\"document-info\">\n\t\t\t\t\t\t\t<uni-icons \n\t\t\t\t\t\t\t\t:type=\"getFileIconClass(currentPreviewDocument ? currentPreviewDocument.file_type : '')\" \n\t\t\t\t\t\t\t\tsize=\"24\" \n\t\t\t\t\t\t\t\t:color=\"getFileIconColor(currentPreviewDocument ? currentPreviewDocument.file_type : '')\"\n\t\t\t\t\t\t\t></uni-icons>\n\t\t\t\t\t\t\t<text class=\"document-title\">{{ currentPreviewDocument ? currentPreviewDocument.title : '文档预览' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"document-desc\">{{ currentPreviewDocument ? currentPreviewDocument.description : '' }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"modal-close\" @click=\"closeDocumentPreviewModal\">\n\t\t\t\t\t\t<uni-icons type=\"close\" size=\"20\" color=\"#999\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"modal-content\">\n\t\t\t\t\t<view class=\"preview-section\">\n\t\t\t\t\t\t<view class=\"preview-item\">\n\t\t\t\t\t\t\t<text class=\"preview-label\">文件名</text>\n\t\t\t\t\t\t\t<text class=\"preview-value\">{{ currentPreviewDocument ? currentPreviewDocument.file_name : '' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"preview-item\">\n\t\t\t\t\t\t\t<text class=\"preview-label\">文件大小</text>\n\t\t\t\t\t\t\t<text class=\"preview-value\">{{ currentPreviewDocument ? formatFileSize(currentPreviewDocument.file_size) : '' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"preview-item\">\n\t\t\t\t\t\t\t<text class=\"preview-label\">上传者</text>\n\t\t\t\t\t\t\t<text class=\"preview-value\">{{ currentPreviewDocument ? currentPreviewDocument.uploader_name : '' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"preview-item\">\n\t\t\t\t\t\t\t<text class=\"preview-label\">上传时间</text>\n\t\t\t\t\t\t\t<text class=\"preview-value\">{{ currentPreviewDocument ? formatTime(currentPreviewDocument.create_time) : '' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"preview-item\">\n\t\t\t\t\t\t\t<text class=\"preview-label\">下载次数</text>\n\t\t\t\t\t\t\t<text class=\"preview-value\">{{ currentPreviewDocument ? currentPreviewDocument.download_count : 0 }} 次</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"modal-footer\">\n\t\t\t\t\t<button class=\"btn btn-cancel\" @click=\"closeDocumentPreviewModal\">关闭</button>\n\t\t\t\t\t<button class=\"btn btn-primary\" @click=\"downloadDocument(currentPreviewDocument)\">\n\t\t\t\t\t\t<uni-icons type=\"download\" size=\"16\" color=\"#fff\"></uni-icons>\n\t\t\t\t\t\t<text>下载文档</text>\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t</view>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex';\n\nexport default {\n\tname: 'StudyCenter',\n\tdata() {\n\t\treturn {\n\t\t\t// 搜索相关\n\t\t\tsearchKeyword: '',\n\t\t\tsearchTimer: null,\n\t\t\t\n\t\t\t// 分类相关\n\t\t\tcategories: [],\n\t\t\tcategoryOptions: [],\n\t\t\tselectedCategoryId: '',\n\t\t\tisCategoryManagerVisible: false,\n\t\t\teditingCategory: null, // 正在编辑的分类\n\t\t\tnewCategory: {\n\t\t\t\tname: '',\n\t\t\t\tdescription: ''\n\t\t\t},\n\t\t\t\n\t\t\t// 文档列表\n\t\t\tdocuments: [],\n\t\t\tloading: false,\n\t\t\thasInitialized: false,\n\t\t\tpage: 1,\n\t\t\tpageSize: 20, // 在这里修改每页显示数量\n\t\t\thasMore: true,\n\t\t\tloadMoreStatus: 'more',\n\t\t\t\n\t\t\t// 权限相关\n\t\t\tcanUpload: false,\n\t\t\tcanManageCategory: false,\n\t\t\tuserRole: [],\n\t\t\tcurrentUserId: '',\n\t\t\t\n\t\t\t// 权限角色常量\n\t\t\tROLE_CONSTANTS: {\n\t\t\t\tADMIN_ROLES: ['admin', 'supervisor', 'PM', 'GM'],\n\t\t\t\tUPLOAD_ROLES: ['admin', 'supervisor', 'PM', 'GM', 'technician', 'mechanic', 'operator'],\n\t\t\t\tMANAGE_ROLES: ['admin', 'supervisor', 'PM', 'GM']\n\t\t\t},\n\t\t\t\n\t\t\t// 上传相关\n\t\t\tuploading: false,\n\t\t\tisSubmitting: false, // 防抖状态\n\t\t\tuploadForm: {\n\t\t\t\ttitle: '',\n\t\t\t\tcategory_id: '',\n\t\t\t\tdescription: '',\n\t\t\t\ttype: 'single', // 文档类型：single-单文档，collection-文档集合\n\t\t\t\tfile_name: '',\n\t\t\t\tfile_url: '',\n\t\t\t\tfile_type: '',\n\t\t\t\tfile_size: 0,\n\t\t\t\tfiles: [] // 多文件集合\n\t\t\t},\n\t\t\t\n\t\t\t// 编辑相关\n\t\t\tisEditMode: false,\n\t\t\teditingDocumentId: '',\n\t\t\teditForm: {\n\t\t\t\ttitle: '',\n\t\t\t\tcategory_id: '',\n\t\t\t\tdescription: '',\n\t\t\t\tfile_name: '',\n\t\t\t\tfile_url: '',\n\t\t\t\tfile_type: '',\n\t\t\t\tfile_size: 0\n\t\t\t},\n\t\t\t\n\t\t\t// 下载相关\n\t\t\tcurrentCollection: null,\n\t\t\tcurrentPreviewDocument: null,\n\t\t\tdownloadingFiles: [],\n\t\t\tdownloadedFiles: [],\n\t\t\tfiles: []\n\t\t};\n\t},\n\tcomputed: {\n\t\t...mapGetters('user', ['userInfo']),\n\t\tselectedCategoryName() {\n\t\t\tif (this.uploadForm.category_id) {\n\t\t\t\tconst category = this.categoryOptions.find(cat => cat._id === this.uploadForm.category_id);\n\t\t\t\treturn category ? category.name : '';\n\t\t\t}\n\t\t\treturn '';\n\t\t},\n\t\tdocumentTitle: {\n\t\t\tget() {\n\t\t\t\treturn this.isEditMode ? this.editForm.title : this.uploadForm.title;\n\t\t\t},\n\t\t\tset(value) {\n\t\t\t\tif (this.isEditMode) {\n\t\t\t\t\tthis.editForm.title = value;\n\t\t\t\t} else {\n\t\t\t\t\tthis.uploadForm.title = value;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tdocumentDescription: {\n\t\t\tget() {\n\t\t\t\treturn this.isEditMode ? this.editForm.description : this.uploadForm.description;\n\t\t\t},\n\t\t\tset(value) {\n\t\t\t\tif (this.isEditMode) {\n\t\t\t\t\tthis.editForm.description = value;\n\t\t\t\t} else {\n\t\t\t\t\tthis.uploadForm.description = value;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 分类编辑相关的计算属性\n\t\teditingCategoryName: {\n\t\t\tget() {\n\t\t\t\treturn this.editingCategory ? this.editingCategory.name : '';\n\t\t\t},\n\t\t\tset(value) {\n\t\t\t\tif (this.editingCategory) {\n\t\t\t\t\tthis.editingCategory.name = value;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\n\t\teditingCategoryDescription: {\n\t\t\tget() {\n\t\t\t\treturn this.editingCategory ? this.editingCategory.description : '';\n\t\t\t},\n\t\t\tset(value) {\n\t\t\t\tif (this.editingCategory) {\n\t\t\t\t\tthis.editingCategory.description = value;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcanSubmit() {\n\t\t\tif (this.uploadForm.type === 'single') {\n\t\t\t\treturn this.uploadForm.title && \n\t\t\t\t\t   this.uploadForm.category_id && \n\t\t\t\t\t   this.uploadForm.file_url;\n\t\t\t} else if (this.uploadForm.type === 'collection') {\n\t\t\t\treturn this.uploadForm.title && \n\t\t\t\t\t   this.uploadForm.category_id && \n\t\t\t\t\t   this.uploadForm.files && \n\t\t\t\t\t   this.uploadForm.files.length > 0;\n\t\t\t}\n\t\t\treturn false;\n\t\t},\n\t\tcanSubmitEdit() {\n\t\t\treturn this.editForm.title && this.editForm.category_id;\n\t\t},\n\t\t\n\t\t// 安全获取当前集合的文件列表\n\t\tcurrentCollectionFiles() {\n\t\t\treturn this.currentCollection && this.currentCollection.files ? this.currentCollection.files : [];\n\t\t},\n\t\t\n\t\t// 安全获取当前集合的文件数量\n\t\tcurrentCollectionFileCount() {\n\t\t\treturn this.currentCollectionFiles.length;\n\t\t}\n\t},\n\tonLoad() {\n\t\tthis.initPage();\n\t},\n\tonPullDownRefresh() {\n\t\tthis.refreshData();\n\t},\n\tonReachBottom() {\n\t\tif (this.hasMore && !this.loading) {\n\t\t\tthis.loadMore();\n\t\t}\n\t},\n\tmethods: {\n\t\tasync initPage() {\n\t\t\t// 优化：并行执行权限检查和数据加载，提升页面加载速度\n\t\t\ttry {\n\t\t\t\t// 先检查权限\n\t\t\t\tawait this.checkPermission();\n\t\t\t\t\n\t\t\t\t// 并行加载分类和文档数据\n\t\t\t\tawait Promise.all([\n\t\t\t\t\tthis.loadCategories(),\n\t\t\t\t\tthis.loadDocuments()\n\t\t\t\t]);\n\t\t\t\t\n\t\t\t\tthis.hasInitialized = true;\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('页面初始化失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '页面加载失败，请重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\n\t\t\n\t\t// 通用API调用方法\n\t\tasync callStudyCenterAPI(action, data = {}) {\n\t\t\ttry {\n\t\t\t\tconst token = uni.getStorageSync('uni_id_token') || this.$store.getters['user/token'];\n\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\tname: 'study-center',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\taction,\n\t\t\t\t\t\tdata,\n\t\t\t\t\t\tuniIdToken: token\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\treturn res.result;\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(`调用study-center API失败 (${action}):`, error);\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 通用错误处理方法\n\t\thandleAPIError(error, defaultMessage = '操作失败') {\n\t\t\tconsole.error('API错误:', error);\n\t\t\tconst message = error.message || defaultMessage;\n\t\t\tuni.showToast({\n\t\t\t\ttitle: message,\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 检查权限\n\t\tasync checkPermission() {\n\t\t\ttry {\n\t\t\t\tconst userInfo = uniCloud.getCurrentUserInfo();\n\t\t\t\tif (userInfo && userInfo.uid) {\n\t\t\t\t\tthis.currentUserId = userInfo.uid;\n\t\t\t\t\tthis.userRole = userInfo.role || [];\n\t\t\t\t\t\n\t\t\t\t\t// 检查上传权限 - 包括管理员、技术类员工等\n\t\t\t\t\tthis.canUpload = this.ROLE_CONSTANTS.UPLOAD_ROLES.some(role => this.userRole.includes(role));\n\t\t\t\t\t\n\t\t\t\t\t// 检查分类管理权限\n\t\t\t\t\tthis.canManageCategory = this.ROLE_CONSTANTS.MANAGE_ROLES.some(role => this.userRole.includes(role));\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('检查权限失败:', error);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 加载分类\n\t\tasync loadCategories() {\n\t\t\ttry {\n\t\t\t\tconst result = await this.callStudyCenterAPI('getCategories');\n\t\t\t\t\n\t\t\t\tif (result.code === 0) {\n\t\t\t\t\tthis.categories = result.data;\n\t\t\t\t\tthis.categoryOptions = [...this.categories];\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载分类失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载分类失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 加载文档列表\n\t\tasync loadDocuments(isLoadMore = false) {\n\t\t\tif (this.loading) return;\n\t\t\t\n\t\t\t// 性能监控：记录开始时间\n\t\t\tconst startTime = Date.now();\n\t\t\t\n\t\t\tthis.loading = true;\n\t\t\tif (!isLoadMore) {\n\t\t\t\tthis.page = 1;\n\t\t\t\tthis.documents = [];\n\t\t\t}\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst params = {\n\t\t\t\t\tpage: this.page,\n\t\t\t\t\tpageSize: this.pageSize\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tif (this.selectedCategoryId) {\n\t\t\t\t\tparams.category_id = this.selectedCategoryId;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (this.searchKeyword) {\n\t\t\t\t\tparams.keyword = this.searchKeyword;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 使用通用API调用方法，减少重复代码\n\t\t\t\tconst result = await this.callStudyCenterAPI('getDocuments', params);\n\t\t\t\t\n\t\t\t\tif (result.code === 0) {\n\t\t\t\t\tconst { list, total } = result.data;\n\t\t\t\t\t\n\t\t\t\t\tif (isLoadMore) {\n\t\t\t\t\t\tthis.documents = [...this.documents, ...list];\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.documents = list;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthis.hasMore = list.length === this.pageSize && this.documents.length < total;\n\t\t\t\t\tthis.loadMoreStatus = this.hasMore ? 'more' : 'noMore';\n\t\t\t\t} else {\n\t\t\t\t\tthis.handleAPIError(new Error(result.message), '加载文档失败');\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载文档失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载文档失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 搜索输入\n\t\tonSearchInput(value) {\n\t\t\tif (this.searchTimer) {\n\t\t\t\tclearTimeout(this.searchTimer);\n\t\t\t}\n\t\t\tthis.searchTimer = setTimeout(() => {\n\t\t\t\tthis.onSearch(value);\n\t\t\t}, 500);\n\t\t},\n\t\t\n\t\t// 搜索确认\n\t\tonSearch(keyword) {\n\t\t\tthis.searchKeyword = keyword;\n\t\t\tthis.loadDocuments();\n\t\t},\n\t\t\n\t\t// 选择分类（筛选）\n\t\tselectCategory(categoryId) {\n\t\t\tthis.selectedCategoryId = categoryId;\n\t\t\tthis.loadDocuments();\n\t\t},\n\t\t\n\t\t// 加载更多\n\t\tloadMore() {\n\t\t\tif (this.hasMore && !this.loading) {\n\t\t\t\tthis.page++;\n\t\t\t\tthis.loadDocuments(true);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 刷新数据\n\t\tasync refreshData() {\n\t\t\tawait this.loadCategories();\n\t\t\tawait this.loadDocuments();\n\t\t},\n\t\t\n\t\t// 查看文档\n\t\tviewDocument(document) {\n\t\t\t// 对于集合类型，直接打开下载弹窗，提供更好的用户体验\n\t\t\tif (document.type === 'collection') {\n\t\t\t\tthis.showCollectionDownloadModal(document);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 对于单个文档，显示快速预览，然后提供下载选项\n\t\t\tthis.showDocumentPreviewModal(document);\n\t\t},\n\t\t\n\t\t// 显示文档预览弹窗\n\t\tshowDocumentPreviewModal(document) {\n\t\t\tthis.currentPreviewDocument = document;\n\t\t\tthis.$refs.documentPreviewPopup.open();\n\t\t},\n\t\t\n\t\t// 关闭文档预览弹窗\n\t\tcloseDocumentPreviewModal() {\n\t\t\tthis.$refs.documentPreviewPopup.close();\n\t\t\tthis.currentPreviewDocument = null;\n\t\t},\n\t\t\n\t\t// 下载文档\n\t\tasync downloadDocument(document) {\n\t\t\tif (document.type === 'collection') {\n\t\t\t\t// 显示现代化的文档集合下载界面\n\t\t\t\tthis.showCollectionDownloadModal(document);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\ttry {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '下载中...'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 增加下载次数\n\t\t\t\tawait uniCloud.callFunction({\n\t\t\t\t\tname: 'study-center',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\taction: 'incrementDownload',\n\t\t\t\t\t\tdata: { id: document._id }\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 下载文件\n\t\t\t\tuni.downloadFile({\n\t\t\t\t\turl: document.file_url,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tif (res.statusCode === 200) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '下载成功',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t// 更新下载次数显示\n\t\t\t\t\t\t\tconst index = this.documents.findIndex(item => item._id === document._id);\n\t\t\t\t\t\t\tif (index !== -1) {\n\t\t\t\t\t\t\t\tthis.$set(this.documents, index, { ...this.documents[index], download_count: this.documents[index].download_count + 1 });\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// 同步更新预览弹窗中的下载次数\n\t\t\t\t\t\t\tif (this.currentPreviewDocument && this.currentPreviewDocument._id === document._id) {\n\t\t\t\t\t\t\t\tthis.currentPreviewDocument.download_count = (this.currentPreviewDocument.download_count || 0) + 1;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// 尝试打开文件\n\t\t\t\t\t\t\tuni.openDocument({\n\t\t\t\t\t\t\t\tfilePath: res.tempFilePath,\n\t\t\t\t\t\t\t\tshowMenu: true,\n\t\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\t\t\tcontent: '文件已下载，但无法直接打开。请在文件管理器中查看。',\n\t\t\t\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '下载失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tconsole.error('下载失败:', err);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '下载失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} catch (error) {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tconsole.error('下载文档失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '下载失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 显示文档集合下载弹窗\n\t\tshowCollectionDownloadModal(document) {\n\t\t\tthis.currentCollection = document;\n\t\t\tthis.downloadingFiles = [];\n\t\t\tthis.downloadedFiles = [];\n\t\t\tthis.$refs.collectionDownloadPopup.open();\n\t\t},\n\t\t\n\t\t// 关闭文档集合下载弹窗\n\t\tcloseCollectionDownloadModal() {\n\t\t\tthis.$refs.collectionDownloadPopup.close();\n\t\t\tthis.currentCollection = null;\n\t\t\tthis.downloadingFiles = [];\n\t\t\tthis.downloadedFiles = [];\n\t\t},\n\t\t\n\t\t// 下载集合中的单个文件\n\t\tasync downloadCollectionFile(file, index) {\n\t\t\tif (this.downloadingFiles.includes(index)) {\n\t\t\t\treturn; // 正在下载中，防止重复点击\n\t\t\t}\n\t\t\t\n\t\t\tthis.downloadingFiles.push(index);\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 显示下载进度\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '下载中...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 下载文件\n\t\t\t\tuni.downloadFile({\n\t\t\t\t\turl: file.file_url,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tif (res.statusCode === 200) {\n\t\t\t\t\t\t\t// 添加到已下载列表\n\t\t\t\t\t\t\tif (!this.downloadedFiles.includes(index)) {\n\t\t\t\t\t\t\t\tthis.downloadedFiles.push(index);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '下载成功',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 尝试打开文件\n\t\t\t\t\t\t\tuni.openDocument({\n\t\t\t\t\t\t\t\tfilePath: res.tempFilePath,\n\t\t\t\t\t\t\t\tshowMenu: true,\n\t\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\t\t\tcontent: '文件已下载，但无法直接打开。请在文件管理器中查看。',\n\t\t\t\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 增加集合下载次数\n\t\t\t\t\t\t\tthis.incrementCollectionDownload();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '下载失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tconsole.error('下载失败:', err);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '下载失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t\tcomplete: () => {\n\t\t\t\t\t\t// 从下载列表中移除\n\t\t\t\t\t\tconst downloadIndex = this.downloadingFiles.indexOf(index);\n\t\t\t\t\t\tif (downloadIndex > -1) {\n\t\t\t\t\t\t\tthis.downloadingFiles.splice(downloadIndex, 1);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} catch (error) {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tconsole.error('下载文件失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '下载失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 从下载列表中移除\n\t\t\t\tconst downloadIndex = this.downloadingFiles.indexOf(index);\n\t\t\t\tif (downloadIndex > -1) {\n\t\t\t\t\tthis.downloadingFiles.splice(downloadIndex, 1);\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 增加集合下载次数\n\t\tasync incrementCollectionDownload() {\n\t\t\tif (!this.currentCollection) return;\n\t\t\t\n\t\t\ttry {\n\t\t\t\tawait uniCloud.callFunction({\n\t\t\t\t\tname: 'study-center',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\taction: 'incrementCollectionDownload',\n\t\t\t\t\t\tdata: { id: this.currentCollection._id }\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 更新本地显示\n\t\t\t\tconst index = this.documents.findIndex(item => item._id === this.currentCollection._id);\n\t\t\t\tif (index !== -1) {\n\t\t\t\t\tthis.$set(this.documents, index, { \n\t\t\t\t\t\t...this.documents[index], \n\t\t\t\t\t\tdownload_count: this.documents[index].download_count + 1 \n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t// 同步更新集合下载弹窗中的下载次数\n\t\t\t\tif (this.currentCollection) {\n\t\t\t\t\tthis.currentCollection.download_count = (this.currentCollection.download_count || 0) + 1;\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('更新集合下载次数失败:', error);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 下载全部文件\n\t\tasync downloadAllFiles() {\n\t\t\tif (!this.currentCollection || this.currentCollectionFileCount === 0) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tconst files = this.currentCollectionFiles;\n\t\t\tconst totalFiles = files.length;\n\t\t\tlet downloadedCount = 0;\n\t\t\t\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '批量下载',\n\t\t\t\tcontent: `确定要下载全部 ${totalFiles} 个文件吗？`,\n\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\ttitle: '准备下载...',\n\t\t\t\t\t\t\tmask: true\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 逐个下载文件\n\t\t\t\t\t\tfor (let i = 0; i < files.length; i++) {\n\t\t\t\t\t\t\tconst file = files[i];\n\t\t\t\t\t\t\tconst index = i;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t// 添加到下载列表\n\t\t\t\t\t\t\t\tthis.downloadingFiles.push(index);\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 下载文件\n\t\t\t\t\t\t\t\tawait new Promise((resolve, reject) => {\n\t\t\t\t\t\t\t\t\tuni.downloadFile({\n\t\t\t\t\t\t\t\t\t\turl: file.file_url,\n\t\t\t\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\t\t\t\tif (res.statusCode === 200) {\n\t\t\t\t\t\t\t\t\t\t\t\tdownloadedCount++;\n\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t// 添加到已下载列表\n\t\t\t\t\t\t\t\t\t\t\t\tif (!this.downloadedFiles.includes(index)) {\n\t\t\t\t\t\t\t\t\t\t\t\t\tthis.downloadedFiles.push(index);\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t// 尝试打开文件\n\t\t\t\t\t\t\t\t\t\t\t\tuni.openDocument({\n\t\t\t\t\t\t\t\t\t\t\t\t\tfilePath: res.tempFilePath,\n\t\t\t\t\t\t\t\t\t\t\t\t\tshowMenu: true,\n\t\t\t\t\t\t\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\tresolve();\n\t\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\t\treject(new Error('下载失败'));\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\t\t\t\tconsole.error('下载失败:', err);\n\t\t\t\t\t\t\t\t\t\t\treject(err);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 短暂延迟，避免同时下载过多文件\n\t\t\t\t\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 500));\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\t\tconsole.error(`下载文件 ${file.file_name} 失败:`, error);\n\t\t\t\t\t\t\t\t// 继续下载其他文件\n\t\t\t\t\t\t\t} finally {\n\t\t\t\t\t\t\t\t// 从下载列表中移除\n\t\t\t\t\t\t\t\tconst downloadIndex = this.downloadingFiles.indexOf(index);\n\t\t\t\t\t\t\t\tif (downloadIndex > -1) {\n\t\t\t\t\t\t\t\t\tthis.downloadingFiles.splice(downloadIndex, 1);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 增加集合下载次数\n\t\t\t\t\t\tawait this.incrementCollectionDownload();\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 显示下载结果\n\t\t\t\t\t\tif (downloadedCount === totalFiles) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '全部下载成功',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: '下载完成',\n\t\t\t\t\t\t\t\tcontent: `成功下载 ${downloadedCount}/${totalFiles} 个文件`,\n\t\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 编辑文档\n\t\teditDocument(document) {\n\t\t\tthis.isEditMode = true;\n\t\t\tthis.editingDocumentId = document._id;\n\t\t\tthis.editForm = {\n\t\t\t\ttitle: document.title,\n\t\t\t\tcategory_id: document.category_id,\n\t\t\t\tdescription: document.description || ''\n\t\t\t};\n\t\t\t// 重用上传弹窗显示编辑表单\n\t\t\tthis.$refs.uploadPopup.open();\n\t\t},\n\t\t\n\t\t// 获取当前选中的分类名称\n\t\tgetCurrentCategoryName() {\n\t\t\tconst categoryId = this.isEditMode ? this.editForm.category_id : this.uploadForm.category_id;\n\t\t\tif (categoryId) {\n\t\t\t\tconst category = this.categoryOptions.find(cat => cat._id === categoryId);\n\t\t\t\treturn category ? category.name : '';\n\t\t\t}\n\t\t\treturn '';\n\t\t},\n\t\t\n\t\t// 获取当前正在编辑的文档\n\t\tgetCurrentEditingDocument() {\n\t\t\tif (this.isEditMode && this.editingDocumentId) {\n\t\t\t\treturn this.documents.find(doc => doc._id === this.editingDocumentId);\n\t\t\t}\n\t\t\treturn null;\n\t\t},\n\t\t\n\t\t// 获取当前编辑文档的文件名显示\n\t\tgetCurrentEditingDocumentFileName() {\n\t\t\tconst doc = this.getCurrentEditingDocument();\n\t\t\tif (!doc) return '未知文档';\n\t\t\t\n\t\t\tif (doc.type === 'collection') {\n\t\t\t\t// 集合类型：显示集合标题和文件数量\n\t\t\t\tconst fileCount = doc.files ? doc.files.length : 0;\n\t\t\t\treturn `${doc.title} (${fileCount}个文件)`;\n\t\t\t} else {\n\t\t\t\t// 单个文档：显示文件名，如果没有文件名则不显示\n\t\t\t\treturn doc.file_name || '';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 从集合编辑中移除文件\n\t\tasync removeFileFromCollectionEdit(index) {\n\t\t\tconst doc = this.getCurrentEditingDocument();\n\t\t\tif (doc && doc.type === 'collection' && doc.files) {\n\t\t\t\tconst file = doc.files[index];\n\t\t\t\t\n\t\t\t\t// 检查是否是最后一个文件\n\t\t\t\tconst isLastFile = doc.files.length === 1;\n\t\t\t\t\n\t\t\t\tif (isLastFile) {\n\t\t\t\t\t// 删除最后一个文件时，删除整个集合\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '确认删除',\n\t\t\t\t\t\tcontent: `删除最后一个文件将同时删除整个集合「${doc.title}」，确定继续吗？`,\n\t\t\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\t\t\t\ttitle: '删除中...'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 如果有云端文件，先删除云端文件\n\t\t\t\t\t\t\t\t\tif (file && file.file_url) {\n\t\t\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\t\t\tawait this.deleteCloudFile([file.file_url]);\n\t\t\t\t\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\t\t\t\t\tconsole.error('删除云存储文件失败:', error);\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '删除云存储文件失败，操作已取消',\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 删除整个集合\n\t\t\t\t\t\t\t\t\tconst deleteRes = await uniCloud.callFunction({\n\t\t\t\t\t\t\t\t\t\tname: 'study-center',\n\t\t\t\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\t\t\t\taction: 'deleteCollection',\n\t\t\t\t\t\t\t\t\t\t\tdata: { id: doc._id }\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tif (deleteRes.result.code === 0) {\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: '集合删除成功',\n\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t// 关闭编辑弹窗并刷新数据\n\t\t\t\t\t\t\t\t\t\tthis.closeUploadModal();\n\t\t\t\t\t\t\t\t\t\tthis.refreshData();\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: deleteRes.result.message || '删除失败',\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\tconsole.error('删除集合失败:', error);\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '删除失败',\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\t// 删除非最后一个文件\n\t\t\t\t\tif (file && file.file_url) {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tawait this.deleteCloudFile([file.file_url]);\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '文件已从云端删除',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tconsole.error('删除云端文件失败:', error);\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '删除云端文件失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\treturn; // 删除失败时不继续\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tdoc.files.splice(index, 1);\n\t\t\t\t\t// 触发视图更新\n\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 向集合中添加文件\n\t\tasync addFilesToCollection() {\n\t\t\ttry {\n\t\t\t\tlet res;\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\t// 微信小程序使用 chooseMessageFile\n\t\t\tres = await uni.chooseMessageFile({\n\t\t\t\tcount: 9, // 最多选择9个文件\n\t\t\t\ttype: 'file'\n\t\t\t});\n\t\t\t// #endif\n\t\t\t\n\t\t\t// #ifndef MP-WEIXIN\n\t\t\t// 其他平台使用 chooseFile\n\t\t\tres = await uni.chooseFile({\n\t\t\t\tcount: 9, // 最多选择9个文件\n\t\t\t\textension: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'zip', 'rar'],\n\t\t\t\tmultiple: true\n\t\t\t});\n\t\t\t// #endif\n\t\t\t\t\n\t\t\t\tconst doc = this.getCurrentEditingDocument();\n\t\t\t\tif (doc && doc.type === 'collection') {\n\t\t\t\t\t// 处理选择的文件\n\t\t\t\t\tfor (const file of res.tempFiles) {\n\t\t\t\t\t\tconst fileInfo = {\n\t\t\t\t\t\t\tfile_name: file.name,\n\t\t\t\t\t\t\tfile_size: file.size,\n\t\t\t\t\t\t\tfile_type: file.name.split('.').pop().toLowerCase(),\n\t\t\t\t\t\t\tfile_url: '', // 需要上传到云存储\n\t\t\t\t\t\t\torder: doc.files ? doc.files.length : 0\n\t\t\t\t\t\t};\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 上传文件到云存储\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst uploadRes = await uniCloud.uploadFile({\n\t\t\t\t\t\t\t\tfilePath: file.path,\n\t\t\t\t\t\t\t\tcloudPath: `study-center/${Date.now()}_${file.name}`\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tfileInfo.file_url = uploadRes.fileID;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 添加到集合文件列表\n\t\t\t\t\t\t\tif (!doc.files) doc.files = [];\n\t\t\t\t\t\t\tdoc.files.push(fileInfo);\n\t\t\t\t\t\t} catch (uploadError) {\n\t\t\t\t\t\t\tconsole.error('上传文件失败:', uploadError);\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '文件上传失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 触发视图更新\n\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '文件添加成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('选择文件失败:', error);\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请从聊天记录中选择文件',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 3000\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t\t\n\t\t\t\t// #ifndef MP-WEIXIN\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '选择文件失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 提交编辑\n\t\tasync submitEdit() {\n\t\t\t// 防抖检查\n\t\t\tif (this.isSubmitting) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 设置防抖状态\n\t\t\tthis.isSubmitting = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '保存中...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tconst doc = this.getCurrentEditingDocument();\n\t\t\t\tif (!doc) {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '文档不存在',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 构建更新数据\n\t\t\t\tconst updateData = {\n\t\t\t\t\tid: this.editingDocumentId,\n\t\t\t\t\ttitle: this.editForm.title,\n\t\t\t\t\tcategory_id: this.editForm.category_id,\n\t\t\t\t\tdescription: this.editForm.description\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t// 根据文档类型选择不同的更新方式\n\t\t\t\tlet action = 'updateDocument';\n\t\t\t\tif (doc.type === 'collection') {\n\t\t\t\t\taction = 'updateCollection';\n\t\t\t\t\t// 集合类型：更新文件列表\n\t\t\t\t\tif (doc.files) {\n\t\t\t\t\t\tupdateData.files = doc.files;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// 单个文档：如果用户更换了文件，添加新的文件信息\n\t\t\t\t\tif (this.editForm.file_url) {\n\t\t\t\t\t\tupdateData.file_name = this.editForm.file_name;\n\t\t\t\t\t\tupdateData.file_url = this.editForm.file_url;\n\t\t\t\t\t\tupdateData.file_type = this.editForm.file_type;\n\t\t\t\t\t\tupdateData.file_size = this.editForm.file_size;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\tname: 'study-center',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\taction: action,\n\t\t\t\t\t\tdata: updateData\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tuni.hideLoading();\n\t\t\t\t\n\t\t\t\tif (res.result.code === 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '修改成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\tthis.closeUploadModal();\n\t\t\t\t\tthis.refreshData();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.result.message || '修改失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tconsole.error('编辑文档失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '修改失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\t// 重置防抖状态\n\t\t\t\tthis.isSubmitting = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 删除文档\n\t\tdeleteDocument(document) {\n\t\t\tconst isCollection = document.type === 'collection';\n\t\t\tconst actionText = isCollection ? '集合' : '文档';\n\t\t\t\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '确认删除',\n\t\t\t\tcontent: `确定要删除${actionText}「${document.title}」吗？此操作不可恢复。`,\n\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\t\ttitle: '删除中...'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tlet deleteRes;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tif (isCollection) {\n\t\t\t\t\t\t\t\t// 删除集合\n\t\t\t\t\t\t\t\tdeleteRes = await uniCloud.callFunction({\n\t\t\t\t\t\t\t\t\tname: 'study-center',\n\t\t\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\t\t\taction: 'deleteCollection',\n\t\t\t\t\t\t\t\t\t\tdata: { id: document._id }\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// 删除单个文档\n\t\t\t\t\t\t\t\t// 先删除云存储文件\n\t\t\t\t\t\t\t\tif (document.file_url) {\n\t\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\t\t// 直接使用file_url作为文件ID\n\t\t\t\t\t\t\t\t\t\tawait this.deleteCloudFile([document.file_url]);\n\t\t\t\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\t\t\t\tconsole.error('删除云存储文件失败:', error);\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: '删除云存储文件失败，操作已取消',\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\t\treturn; // 云存储文件删除失败，不继续删除数据库记录\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 删除数据库记录\n\t\t\t\t\t\t\t\tdeleteRes = await uniCloud.callFunction({\n\t\t\t\t\t\t\t\t\tname: 'study-center',\n\t\t\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\t\t\taction: 'deleteDocument',\n\t\t\t\t\t\t\t\t\t\tdata: { id: document._id }\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tif (deleteRes.result.code === 0) {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '删除成功',\n\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tthis.refreshData();\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: deleteRes.result.message || '删除失败',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tconsole.error('删除文档失败:', error);\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '删除失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 从URL中提取文件ID\n\t\textractFileIdFromUrl(url) {\n\t\t\ttry {\n\t\t\t\t// uniCloud的fileID就是完整的文件标识符\n\t\t\t\t// 如果url就是fileID格式，直接返回\n\t\t\t\tif (url && (url.startsWith('cloud://') || url.includes('tcb-api'))) {\n\t\t\t\t\treturn url;\n\t\t\t\t}\n\t\t\t\t// 如果是HTTP URL，尝试提取文件ID\n\t\t\t\tconst matches = url.match(/\\/([a-f0-9]{24,})/i);\n\t\t\t\treturn matches ? matches[1] : url;\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('提取文件ID失败:', error);\n\t\t\t\treturn url; // 如果提取失败，返回原URL\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 检查是否可以编辑文档\n\t\tcanEditDocument(document) {\n\t\t\treturn this.ROLE_CONSTANTS.ADMIN_ROLES.some(role => this.userRole.includes(role)) || \n\t\t\t\t   document.uploader_id === this.currentUserId;\n\t\t},\n\t\t\n\t\t// 检查是否可以删除文档\n\t\tcanDeleteDocument(document) {\n\t\t\treturn this.ROLE_CONSTANTS.ADMIN_ROLES.some(role => this.userRole.includes(role)) || \n\t\t\t\t   document.uploader_id === this.currentUserId;\n\t\t},\n\t\t\n\t\t// 显示上传弹窗\n\t\tshowUploadModal() {\n\t\t\tif (!this.canUpload) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '权限不足',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.resetUploadForm();\n\t\t\tthis.$refs.uploadPopup.open();\n\t\t},\n\t\t\n\t\t// 关闭上传弹窗\n\t\tcloseUploadModal() {\n\t\t\tthis.$refs.uploadPopup.close();\n\t\t\tif (this.isEditMode) {\n\t\t\t\tthis.resetEditForm();\n\t\t\t} else {\n\t\t\t\tthis.resetUploadForm();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 重置上传表单\n\t\tresetUploadForm() {\n\t\t\tthis.uploadForm = {\n\t\t\t\ttitle: '',\n\t\t\t\tcategory_id: '',\n\t\t\t\tdescription: '',\n\t\t\t\ttype: 'single',\n\t\t\t\tfile_name: '',\n\t\t\t\tfile_url: '',\n\t\t\t\tfile_type: '',\n\t\t\t\tfile_size: 0,\n\t\t\t\tfiles: []\n\t\t\t};\n\t\t},\n\t\t\n\t\t// 重置编辑表单\n\t\tresetEditForm() {\n\t\t\tthis.isEditMode = false;\n\t\t\tthis.editingDocumentId = '';\n\t\t\tthis.editForm = {\n\t\t\t\ttitle: '',\n\t\t\t\tcategory_id: '',\n\t\t\t\tdescription: '',\n\t\t\t\tfile_name: '',\n\t\t\t\tfile_url: '',\n\t\t\t\tfile_type: '',\n\t\t\t\tfile_size: 0\n\t\t\t};\n\t\t},\n\t\t\n\t\t// 打开分类选择器\n\t\topenCategorySelector() {\n\t\t\tthis.$refs.categorySelectorPopup.open();\n\t\t},\n\t\t\n\t\t// 关闭分类选择器\n\t\tcloseCategorySelector() {\n\t\t\tthis.$refs.categorySelectorPopup.close();\n\t\t},\n\t\t\n\t\t// 选择分类（上传时和编辑时）\n\t\thandleCategorySelect(category) {\n\t\t\tif (this.isEditMode) {\n\t\t\t\tthis.editForm.category_id = category._id;\n\t\t\t} else {\n\t\t\t\tthis.uploadForm.category_id = category._id;\n\t\t\t}\n\t\t\tthis.closeCategorySelector();\n\t\t},\n\t\t\n\t\t// 显示分类管理器\n\t\tshowCategoryManager() {\n\t\t\tif (!this.canManageCategory) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '权限不足',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.isCategoryManagerVisible = true;\n\t\t},\n\t\t\n\t\t// 关闭分类管理器\n\t\tcloseCategoryManager() {\n\t\t\tthis.isCategoryManagerVisible = false;\n\t\t\tthis.newCategory = { name: '', description: '' };\n\t\t},\n\t\t\n\t\t// 创建分类\n\t\tasync createCategory() {\n\t\t\tif (!this.newCategory.name.trim()) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请输入分类名称',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 防抖检查\n\t\t\tif (this.isSubmitting) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 设置防抖状态\n\t\t\tthis.isSubmitting = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 获取token\n\t\t\t\tconst token = uni.getStorageSync('uni_id_token') || this.$store.getters['user/token'];\n\t\t\t\t\n\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\tname: 'study-center',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\taction: 'createCategory',\n\t\t\t\t\t\tdata: this.newCategory,\n\t\t\t\t\t\tuniIdToken: token\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.result.code === 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '创建成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\tthis.newCategory = { name: '', description: '' };\n\t\t\t\t\tawait this.loadCategories();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.result.message || '创建失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('创建分类失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '创建失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\t// 重置防抖状态\n\t\t\t\tthis.isSubmitting = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 编辑分类\n\t\teditCategory(category) {\n\t\t\t// 先关闭分类管理弹窗，避免层级冲突\n\t\t\tthis.closeCategoryManager();\n\t\t\t\n\t\t\t// 设置编辑模式\n\t\t\tthis.editingCategory = { ...category };\n\t\t\t\n\t\t\t// 使用 setTimeout 确保弹窗在下一个事件循环中显示\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.showCategoryEditModal();\n\t\t\t}, 200);\n\t\t},\n\t\t\n\t\t// 显示分类编辑弹窗\n\t\tshowCategoryEditModal() {\n\t\t\tthis.$refs.categoryEditPopup.open();\n\t\t},\n\t\t\n\t\t// 关闭分类编辑弹窗\n\t\tcloseCategoryEditModal() {\n\t\t\tthis.$refs.categoryEditPopup.close();\n\t\t\tthis.editingCategory = null;\n\t\t},\n\t\t\n\t\t// 提交分类编辑\n\t\tasync submitCategoryEdit() {\n\t\t\tif (!this.editingCategory || !this.editingCategoryName.trim()) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请输入分类名称',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 防抖检查\n\t\t\tif (this.isSubmitting) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 设置防抖状态\n\t\t\tthis.isSubmitting = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '保存中...'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tconst token = uni.getStorageSync('uni_id_token') || this.$store.getters['user/token'];\n\t\t\t\tconst result = await uniCloud.callFunction({\n\t\t\t\t\tname: 'study-center',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\taction: 'updateCategory',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tid: this.editingCategory._id,\n\t\t\t\t\t\t\tname: this.editingCategoryName.trim(),\n\t\t\t\t\t\t\tdescription: this.editingCategoryDescription || ''\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tuniIdToken: token\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tuni.hideLoading();\n\t\t\t\t\n\t\t\t\tif (result.result.code === 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '修改成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\tthis.closeCategoryEditModal();\n\t\t\t\t\t// 刷新分类列表\n\t\t\t\t\tawait this.loadCategories();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: result.result.message || '修改失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('修改分类失败:', error);\n\t\t\t\tuni.hideLoading();\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '修改失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\t// 重置防抖状态\n\t\t\t\tthis.isSubmitting = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 确认删除分类\n\t\tconfirmDeleteCategory(category) {\n\t\t\t// 先关闭分类管理弹窗，避免层级冲突\n\t\t\tthis.closeCategoryManager();\n\t\t\t\n\t\t\t// 使用 setTimeout 确保弹窗在下一个事件循环中显示\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认删除',\n\t\t\t\t\tcontent: `确定要删除分类\"${category.name}\"吗？删除后无法恢复。`,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tthis.deleteCategory(category);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}, 200);\n\t\t},\n\t\t\n\t\t// 删除分类\n\t\tasync deleteCategory(category) {\n\t\t\ttry {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '删除中...'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tconst token = uni.getStorageSync('uni_id_token') || this.$store.getters['user/token'];\n\t\t\t\tconst result = await uniCloud.callFunction({\n\t\t\t\t\tname: 'study-center',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\taction: 'deleteCategory',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tid: category._id\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tuniIdToken: token\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tuni.hideLoading();\n\t\t\t\t\n\t\t\t\tif (result.result.code === 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '删除成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 刷新分类列表\n\t\t\t\t\tawait this.loadCategories();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: result.result.message || '删除失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('删除分类失败:', error);\n\t\t\t\tuni.hideLoading();\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '删除失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 选择文件\n\t\tchooseFile() {\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\t// 微信小程序使用 chooseMessageFile\n\t\t\tuni.chooseMessageFile({\n\t\t\t\tcount: 1,\n\t\t\t\ttype: 'file',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconst file = res.tempFiles[0];\n\t\t\t\t\tthis.uploadForm.file_name = file.name;\n\t\t\t\t\tthis.uploadForm.file_size = file.size;\n\t\t\t\t\tthis.uploadForm.file_type = file.name.split('.').pop().toLowerCase();\n\t\t\t\t\t\n\t\t\t\t\t// 如果标题为空，使用文件名（去掉扩展名）\n\t\t\t\t\tif (!this.uploadForm.title) {\n\t\t\t\t\t\tthis.uploadForm.title = file.name.replace(/\\.[^/.]+$/, '');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 上传文件到云存储\n\t\t\t\t\tthis.uploadFileToCloud(file.path);\n\t\t\t\t},\n\t\t\t\tfail: (error) => {\n\t\t\t\t\tconsole.error('选择文件失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请从聊天记录中选择文件',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t\t// #endif\n\t\t\t\n\t\t\t// #ifndef MP-WEIXIN\n\t\t\t// 其他平台使用 chooseFile\n\t\t\tuni.chooseFile({\n\t\t\t\tcount: 1,\n\t\t\t\textension: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'zip', 'rar'],\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconst file = res.tempFiles[0];\n\t\t\t\t\tthis.uploadForm.file_name = file.name;\n\t\t\t\t\tthis.uploadForm.file_size = file.size;\n\t\t\t\t\tthis.uploadForm.file_type = file.name.split('.').pop().toLowerCase();\n\t\t\t\t\t\n\t\t\t\t\t// 如果标题为空，使用文件名（去掉扩展名）\n\t\t\t\t\tif (!this.uploadForm.title) {\n\t\t\t\t\t\tthis.uploadForm.title = file.name.replace(/\\.[^/.]+$/, '');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 上传文件到云存储\n\t\t\t\t\tthis.uploadFileToCloud(file.path);\n\t\t\t\t},\n\t\t\t\tfail: (error) => {\n\t\t\t\t\tconsole.error('选择文件失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '选择文件失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t\t// #endif\n\t\t},\n\t\t\n\t\t// 移除文件\n\t\tasync removeFile() {\n\t\t\t// 如果有云端文件，先删除云端文件\n\t\t\tif (this.uploadForm.file_url) {\n\t\t\t\ttry {\n\t\t\t\t\t// 直接使用file_url作为文件ID\n\t\t\t\t\tawait this.deleteCloudFile([this.uploadForm.file_url]);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '文件已从云端删除',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('删除云端文件失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '删除云端文件失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn; // 删除失败时不继续\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 清空文件信息\n\t\t\tthis.uploadForm.file_name = '';\n\t\t\tthis.uploadForm.file_size = 0;\n\t\t\tthis.uploadForm.file_type = '';\n\t\t\tthis.uploadForm.file_url = '';\n\t\t\tthis.tempFilePath = '';\n\t\t\t\n\t\t\t// 清空标题和描述\n\t\t\tthis.uploadForm.title = '';\n\t\t\tthis.uploadForm.description = '';\n\t\t},\n\t\t\n\t\t// 选择文档类型\n\t\tselectDocumentType(type) {\n\t\t\tthis.uploadForm.type = type;\n\t\t\t// 切换类型时清空文件\n\t\t\tthis.uploadForm.files = [];\n\t\t\tthis.removeFile();\n\t\t},\n\t\t\n\t\t// 选择多个文件\n\t\tchooseMultipleFiles() {\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\t// 微信小程序使用 chooseMessageFile\n\t\t\tuni.chooseMessageFile({\n\t\t\t\tcount: 9, // 最多选择9个文件\n\t\t\t\ttype: 'file',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconst files = res.tempFiles;\n\t\t\t\t\t// 检查文件数量限制\n\t\t\t\t\tif (this.uploadForm.files.length + files.length > 9) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '最多只能选择9个文件',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 如果标题为空，使用第一个文件名（去掉扩展名）\n\t\t\t\t\tif (!this.uploadForm.title && files.length > 0) {\n\t\t\t\t\t\tthis.uploadForm.title = files[0].name.replace(/\\.[^/.]+$/, '') + '等文档集合';\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 批量上传文件到云存储\n\t\t\t\t\tthis.uploadMultipleFilesToCloud(files);\n\t\t\t\t},\n\t\t\t\tfail: (error) => {\n\t\t\t\t\tconsole.error('选择文件失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请从聊天记录中选择文件',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t\t// #endif\n\t\t\t\n\t\t\t// #ifndef MP-WEIXIN\n\t\t\t// 其他平台使用 chooseFile\n\t\t\tuni.chooseFile({\n\t\t\t\tcount: 9, // 最多选择9个文件\n\t\t\t\textension: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'zip', 'rar'],\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconst files = res.tempFiles;\n\t\t\t\t\t// 检查文件数量限制\n\t\t\t\t\tif (this.uploadForm.files.length + files.length > 9) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '最多只能选择9个文件',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 如果标题为空，使用第一个文件名（去掉扩展名）\n\t\t\t\t\tif (!this.uploadForm.title && files.length > 0) {\n\t\t\t\t\t\tthis.uploadForm.title = files[0].name.replace(/\\.[^/.]+$/, '') + '等文档集合';\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 批量上传文件到云存储\n\t\t\t\t\tthis.uploadMultipleFilesToCloud(files);\n\t\t\t\t},\n\t\t\t\tfail: (error) => {\n\t\t\t\t\tconsole.error('选择文件失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '选择文件失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t\t// #endif\n\t\t},\n\t\t\n\t\t// 从集合中移除文件\n\t\tasync removeFileFromCollection(index) {\n\t\t\tconst file = this.uploadForm.files[index];\n\t\t\t\n\t\t\t// 检查是否是最后一个文件\n\t\t\tconst isLastFile = this.uploadForm.files.length === 1;\n\t\t\t\n\t\t\tif (isLastFile) {\n\t\t\t\t// 删除最后一个文件时，清空整个集合\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认删除',\n\t\t\t\t\tcontent: '删除最后一个文件将清空整个集合，确定继续吗？',\n\t\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t// 如果有云端文件，先删除云端文件\n\t\t\t\t\t\t\tif (file && file.file_url) {\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\tawait this.deleteCloudFile([file.file_url]);\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '文件已从云端删除',\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\t\t\tconsole.error('删除云端文件失败:', error);\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '删除云端文件失败',\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\treturn; // 删除失败时不继续\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.uploadForm.files.splice(index, 1);\n\t\t\t\t\t\t\t// 清空标题和描述\n\t\t\t\t\t\t\tthis.uploadForm.title = '';\n\t\t\t\t\t\t\tthis.uploadForm.description = '';\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\t// 删除非最后一个文件\n\t\t\t\tif (file && file.file_url) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tawait this.deleteCloudFile([file.file_url]);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '文件已从云端删除',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.error('删除云端文件失败:', error);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '删除云端文件失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn; // 删除失败时不继续\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.uploadForm.files.splice(index, 1);\n\t\t\t\t// 如果删除后没有文件了，清空标题和描述\n\t\t\t\tif (this.uploadForm.files.length === 0) {\n\t\t\t\t\tthis.uploadForm.title = '';\n\t\t\t\t\tthis.uploadForm.description = '';\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 编辑模式下选择文件\n\t\tchooseFileForEdit() {\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\t// 微信小程序使用 chooseMessageFile\n\t\t\tuni.chooseMessageFile({\n\t\t\t\tcount: 1,\n\t\t\t\ttype: 'file',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconst file = res.tempFiles[0];\n\t\t\t\t\tthis.editForm.file_name = file.name;\n\t\t\t\t\tthis.editForm.file_size = file.size;\n\t\t\t\t\tthis.editForm.file_type = file.name.split('.').pop().toLowerCase();\n\t\t\t\t\t\n\t\t\t\t\t// 上传文件到云存储\n\t\t\t\t\tthis.uploadFileToCloudForEdit(file.path);\n\t\t\t\t},\n\t\t\t\tfail: (error) => {\n\t\t\t\t\tconsole.error('选择文件失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请从聊天记录中选择文件',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t\t// #endif\n\t\t\t\n\t\t\t// #ifndef MP-WEIXIN\n\t\t\t// 其他平台使用 chooseFile\n\t\t\tuni.chooseFile({\n\t\t\t\tcount: 1,\n\t\t\t\textension: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'zip', 'rar'],\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconst file = res.tempFiles[0];\n\t\t\t\t\tthis.editForm.file_name = file.name;\n\t\t\t\t\tthis.editForm.file_size = file.size;\n\t\t\t\t\tthis.editForm.file_type = file.name.split('.').pop().toLowerCase();\n\t\t\t\t\t\n\t\t\t\t\t// 上传文件到云存储\n\t\t\t\t\tthis.uploadFileToCloudForEdit(file.path);\n\t\t\t\t},\n\t\t\t\tfail: (error) => {\n\t\t\t\t\tconsole.error('选择文件失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '选择文件失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t\t// #endif\n\t\t},\n\t\t\n\t\t// 编辑模式下移除文件\n\t\tasync removeFileInEdit() {\n\t\t\tconst doc = this.getCurrentEditingDocument();\n\t\t\tif (!doc) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '未找到要编辑的文档',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 单个文档删除文件时，直接删除整个文档\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '确认删除',\n\t\t\t\tcontent: `确定要删除文档「${doc.title}」吗？此操作不可恢复。`,\n\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\t\ttitle: '删除中...'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 如果有云端文件，先删除云端文件\n\t\t\t\t\t\t\tif (doc.file_url) {\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\t// 直接使用file_url作为文件ID\n\t\t\t\t\t\t\t\t\tawait this.deleteCloudFile([doc.file_url]);\n\t\t\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\t\t\tconsole.error('删除云存储文件失败:', error);\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '删除云存储文件失败，操作已取消',\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\treturn; // 云存储文件删除失败，不继续删除数据库记录\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 删除数据库记录\n\t\t\t\t\t\t\tconst deleteRes = await uniCloud.callFunction({\n\t\t\t\t\t\t\t\tname: 'study-center',\n\t\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\t\taction: 'deleteDocument',\n\t\t\t\t\t\t\t\t\tdata: { id: doc._id }\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tif (deleteRes.result.code === 0) {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '删除成功',\n\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t// 关闭编辑弹窗并刷新数据\n\t\t\t\t\t\t\t\tthis.closeUploadModal();\n\t\t\t\t\t\t\t\tthis.refreshData();\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: deleteRes.result.message || '删除失败',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tconsole.error('删除文档失败:', error);\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '删除失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 编辑模式下上传文件到云存储\n\t\tasync uploadFileToCloudForEdit(filePath) {\n\t\t\tthis.uploading = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst result = await uniCloud.uploadFile({\n\t\t\t\t\tfilePath: filePath,\n\t\t\t\t\tcloudPath: `study/${Date.now()}_${this.editForm.file_name}`,\n\t\t\t\t\tcloudPathAsRealPath: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tthis.editForm.file_url = result.fileID;\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '文件上传成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('文件上传失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '文件上传失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.uploading = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 批量上传文件到云存储\n\t\tasync uploadMultipleFilesToCloud(files) {\n\t\t\tthis.uploading = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst uploadPromises = files.map(async (file, index) => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst result = await uniCloud.uploadFile({\n\t\t\t\t\t\t\tfilePath: file.path,\n\t\t\t\t\t\t\tcloudPath: `study/${Date.now()}_${index}_${file.name}`,\n\t\t\t\t\t\t\tcloudPathAsRealPath: true\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t// 批量上传文件结果\n\t\t\t\t\t\t\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tfile_name: file.name,\n\t\t\t\t\t\t\tfile_size: file.size,\n\t\t\t\t\t\t\tfile_type: file.name.split('.').pop().toLowerCase(),\n\t\t\t\t\t\t\tfile_url: result.fileID,\n\t\t\t\t\t\t\torder: index\n\t\t\t\t\t\t};\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.error(`文件 ${file.name} 上传失败:`, error);\n\t\t\t\t\t\treturn null;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tconst results = await Promise.all(uploadPromises);\n\t\t\t\tconst successFiles = results.filter(result => result !== null);\n\t\t\t\t\n\t\t\t\tif (successFiles.length > 0) {\n\t\t\t\t\tthis.uploadForm.files = [...this.uploadForm.files, ...successFiles];\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: `成功上传 ${successFiles.length} 个文件`,\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '文件上传失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('批量上传文件失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '文件上传失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.uploading = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 上传文件到云存储\n\t\tasync uploadFileToCloud(filePath) {\n\t\t\tthis.uploading = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst result = await uniCloud.uploadFile({\n\t\t\t\t\tfilePath: filePath,\n\t\t\t\t\tcloudPath: `study/${Date.now()}_${this.uploadForm.file_name}`,\n\t\t\t\t\tcloudPathAsRealPath: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 上传文件结果\n\t\t\t\tthis.uploadForm.file_url = result.fileID;\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '文件上传成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('文件上传失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '文件上传失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.uploading = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 提交上传\n\t\tasync submitUpload() {\n\t\t\t// 防抖检查\n\t\t\tif (this.isSubmitting) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tif (!this.canSubmit) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请完善信息',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 设置防抖状态\n\t\t\tthis.isSubmitting = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 获取token\n\t\t\t\tconst token = uni.getStorageSync('uni_id_token') || this.$store.getters['user/token'];\n\t\t\t\t\n\t\t\t\tlet action, data;\n\t\t\t\tif (this.uploadForm.type === 'single') {\n\t\t\t\t\taction = 'uploadDocument';\n\t\t\t\t\tdata = this.uploadForm;\n\t\t\t\t} else if (this.uploadForm.type === 'collection') {\n\t\t\t\t\taction = 'uploadCollection';\n\t\t\t\t\tdata = this.uploadForm;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\tname: 'study-center',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\taction: action,\n\t\t\t\t\t\tdata: data,\n\t\t\t\t\t\tuniIdToken: token\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.result.code === 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '上传成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\tthis.closeUploadModal();\n\t\t\t\t\tthis.refreshData();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.result.message || '上传失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('上传文档失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '上传失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\t// 重置防抖状态\n\t\t\t\tthis.isSubmitting = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 应用配置常量\n\t\tappConfig() {\n\t\t\treturn {\n\t\t\t\t// 文件类型配置\n\t\t\t\tfileTypes: {\n\t\t\t\t\tpdf: { icon: 'paperclip', color: '#ff4757' },\n\t\t\t\t\tdoc: { icon: 'paperclip', color: '#2e86de' },\n\t\t\t\t\tdocx: { icon: 'paperclip', color: '#2e86de' },\n\t\t\t\t\txls: { icon: 'paperclip', color: '#10ac84' },\n\t\t\t\t\txlsx: { icon: 'paperclip', color: '#10ac84' },\n\t\t\t\t\tppt: { icon: 'paperclip', color: '#ff6348' },\n\t\t\t\t\tpptx: { icon: 'paperclip', color: '#ff6348' },\n\t\t\t\t\ttxt: { icon: 'paperclip', color: '#747d8c' },\n\t\t\t\t\tzip: { icon: 'paperclip', color: '#ffa502' },\n\t\t\t\t\trar: { icon: 'paperclip', color: '#ffa502' },\n\t\t\t\t\tpng: { icon: 'image', color: '#3a86ff' },\n\t\t\t\t\tjpg: { icon: 'image', color: '#3a86ff' },\n\t\t\t\t\tjpeg: { icon: 'image', color: '#3a86ff' },\n\t\t\t\t\tgif: { icon: 'image', color: '#3a86ff' },\n\t\t\t\t},\n\t\t\t\t// 页面配置\n\t\t\t\tpagination: {\n\t\t\t\t\tdefaultPageSize: 10,\n\t\t\t\t\tmaxPageSize: 50\n\t\t\t\t},\n\t\t\t\t// UI配置\n\t\t\t\tui: {\n\t\t\t\t\ttoastDuration: 2000,\n\t\t\t\t\tloadingDelay: 300\n\t\t\t\t}\n\t\t\t};\n\t\t},\n\t\t\n\t\t// 获取文件图标类名\n\t\tgetFileIconClass(fileType) {\n\t\t\tif (!fileType) return 'folder';\n\t\t\t\n\t\t\t// 直接返回固定的图标映射，不依赖computed属性\n\t\t\tconst iconMap = {\n\t\t\t\tpdf: 'paperclip',\n\t\t\t\tdoc: 'paperclip',\n\t\t\t\tdocx: 'paperclip',\n\t\t\t\txls: 'paperclip',\n\t\t\t\txlsx: 'paperclip',\n\t\t\t\tppt: 'paperclip',\n\t\t\t\tpptx: 'paperclip',\n\t\t\t\ttxt: 'paperclip',\n\t\t\t\tzip: 'paperclip',\n\t\t\t\trar: 'paperclip',\n\t\t\t\tpng: 'image',\n\t\t\t\tjpg: 'image',\n\t\t\t\tjpeg: 'image',\n\t\t\t\tgif: 'image'\n\t\t\t};\n\t\t\t\n\t\t\treturn iconMap[fileType] || 'folder';\n\t\t},\n\n\t\t// 获取文件图标颜色\n\t\tgetFileIconColor(fileType) {\n\t\t\tif (!fileType) return '#666';\n\t\t\t\n\t\t\t// 直接返回固定的颜色映射，不依赖computed属性\n\t\t\tconst colorMap = {\n\t\t\t\tpdf: '#ff4757',\n\t\t\t\tdoc: '#2e86de',\n\t\t\t\tdocx: '#2e86de',\n\t\t\t\txls: '#10ac84',\n\t\t\t\txlsx: '#10ac84',\n\t\t\t\tppt: '#ff6348',\n\t\t\t\tpptx: '#ff6348',\n\t\t\t\ttxt: '#747d8c',\n\t\t\t\tzip: '#ffa502',\n\t\t\t\trar: '#ffa502',\n\t\t\t\tpng: '#3a86ff',\n\t\t\t\tjpg: '#3a86ff',\n\t\t\t\tjpeg: '#3a86ff',\n\t\t\t\tgif: '#3a86ff'\n\t\t\t};\n\t\t\t\n\t\t\treturn colorMap[fileType] || '#666';\n\t\t},\n\t\t\n\n\t\t\n\t\t// 删除云端文件\n\t\tasync deleteCloudFile(fileUrls) {\n\t\t\ttry {\n\t\t\t\t// 处理文件ID列表 - 支持fileID和CDN URL\n\t\t\t\tconst fileIds = fileUrls\n\t\t\t\t\t.filter(url => url && url.trim())\n\t\t\t\t\t.map(url => {\n\t\t\t\t\t\t// 如果是完整的fileID，直接使用\n\t\t\t\t\t\tif (url.startsWith('cloud://')) {\n\t\t\t\t\t\t\treturn url;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 如果是CDN链接，尝试提取文件ID\n\t\t\t\t\t\tif (url.includes('cdn.bspapp.com')) {\n\t\t\t\t\t\t\tconst matches = url.match(/\\/([a-f0-9]{24,})/i);\n\t\t\t\t\t\t\tif (matches && matches[1]) {\n\t\t\t\t\t\t\t\treturn matches[1];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn url;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 如果是其他格式的URL，尝试直接使用\n\t\t\t\t\t\treturn url;\n\t\t\t\t\t})\n\t\t\t\t\t.filter(id => id);\n\t\t\t\t\n\t\t\t\tif (fileIds.length > 0) {\n\t\t\t\t\tconst result = await uniCloud.callFunction({\n\t\t\t\t\t\tname: 'delete-file',\n\t\t\t\t\t\tdata: { fileList: fileIds }\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (result.result && result.result.code === 0) {\n\t\t\t\t\t\t// 删除成功\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 检查是否有部分成功的情况\n\t\t\t\t\t\tif (result.result && result.result.successCount > 0) {\n\t\t\t\t\t\t\t// 如果有成功的，就不抛出错误\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthrow new Error(result.result?.message || '删除失败');\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('删除云端文件失败:', error);\n\t\t\t\tthrow error; // 重新抛出错误，让调用方处理\n\t\t\t}\n\t\t},\n\t\t\n// 通用错误处理方法\n\t\thandleError(error, operation = '操作', showToast = true) {\n\t\t\tconsole.error(`${operation}失败:`, error);\n\t\t\tif (showToast) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: `${operation}失败`,\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: this.appConfig.ui.toastDuration || 2000\n\t\t\t\t});\n\t\t\t}\n\t\t\treturn false;\n\t\t},\n\t\t\n\t\t// 通用成功提示方法\n\t\tshowSuccess(message, duration) {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: message,\n\t\t\t\ticon: 'success',\n\t\t\t\tduration: duration || this.appConfig.ui.toastDuration || 2000\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 格式化文件大小\n\t\tformatFileSize(size) {\n\t\t\tif (!size) return '0B';\n\t\t\tconst units = ['B', 'KB', 'MB', 'GB'];\n\t\t\tlet index = 0;\n\t\t\twhile (size >= 1024 && index < units.length - 1) {\n\t\t\t\tsize /= 1024;\n\t\t\t\tindex++;\n\t\t\t}\n\t\t\treturn Math.round(size * 100) / 100 + units[index];\n\t\t},\n\t\t\n\t\t// 格式化时间\n\t\tformatTime(timestamp) {\n\t\t\tif (!timestamp) {\n\t\t\t\treturn '';\n\t\t\t}\n\t\t\tconst date = new Date(timestamp);\n\t\t\t\n\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\treturn '';\n\t\t\t}\n\t\t\t\n\t\t\t// 始终显示年月日格式\n\t\t\tconst year = date.getFullYear();\n\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\n\t\t\tconst day = String(date.getDate()).padStart(2, '0');\n\t\t\treturn `${year}-${month}-${day}`;\n\t\t}\n\t}\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.study-center-container {\n\tbackground-color: #f5f5f5;\n\tpadding-bottom: 20rpx;\n\tmin-height: 100vh;\n}\n\n.search-bar {\n\tpadding: 20rpx;\n\tbackground-color: #fff;\n\tborder-bottom: 1rpx solid #eee;\n}\n\n.category-filter {\n\tbackground-color: #fff;\n\tborder-bottom: 1rpx solid #eee;\n\tpadding: 20rpx 0;\n}\n\n.category-scroll {\n\twhite-space: nowrap;\n\t/* 确保滚动边界处理更好 */\n\toverflow-x: auto;\n\t-webkit-overflow-scrolling: touch;\n\t/* 隐藏滚动条但保持功能 */\n\tscrollbar-width: none;\n\t-ms-overflow-style: none;\n\t\n\t&::-webkit-scrollbar {\n\t\tdisplay: none;\n\t}\n}\n\n.category-list {\n\tdisplay: flex;\n\tpadding: 0 20rpx;\n\talign-items: center;\n\t/* 确保小齿轮图标有足够的右边距，防止被裁剪 */\n\tpadding-right: 40rpx;\n}\n\n.category-item {\n\tpadding: 12rpx 24rpx;\n\tmargin-right: 20rpx;\n\tborder-radius: 30rpx;\n\tbackground-color: #f8f8f8;\n\tcolor: #666;\n\tfont-size: 28rpx;\n\twhite-space: nowrap;\n\ttransition: none;\n\t\n\t&.active {\n\t\tbackground-color: #007aff;\n\t\tcolor: #fff;\n\t}\n}\n\n.category-count {\n\tfont-size: 24rpx;\n\tmargin-left: 8rpx;\n}\n\n.category-manage-btn {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground-color: #f0f0f0;\n\tborder-radius: 50%;\n\tmargin-left: 20rpx;\n\t/* 确保图标不会被裁剪 */\n\tflex-shrink: 0;\n\t/* 添加最小宽度保护 */\n\tmin-width: 60rpx;\n\ttransition: none;\n\t\n\t&:active {\n\t\ttransform: scale(0.9);\n\t\tbackground-color: #e0e0e0;\n\t}\n}\n\n.document-list {\n\tpadding: 20rpx;\n}\n\n.loading-container,\n.empty-container {\n\tpadding: 100rpx 0;\n\ttext-align: center;\n}\n\n.document-item {\n\tdisplay: flex;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n\tbackground-color: #fff;\n\tborder-radius: 16rpx;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\n\ttransition: all 0.3s ease;\n\tposition: relative;\n\t\n\t&:active {\n\t\tbox-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.1);\n\t\ttransform: translateY(1rpx);\n\t}\n\t\n\t/* 悬停效果 */\n\t&:hover {\n\t\tbox-shadow: 0 3rpx 12rpx rgba(0, 0, 0, 0.08);\n\t}\n}\n\n.document-icon {\n\twidth: 88rpx;\n\theight: 88rpx;\n\tmargin-right: 24rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder-radius: 20rpx;\n\tposition: relative;\n\toverflow: hidden;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);\n\ttransition: all 0.3s ease;\n\t\n\t/* 悬停效果 */\n\t&:hover {\n\t\ttransform: translateY(-1rpx);\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12);\n\t}\n\t\n\t/* 点击效果 */\n\t&:active {\n\t\ttransform: translateY(0);\n\t\tbox-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08);\n\t}\n}\n\n.document-content {\n\tflex: 1;\n\tmin-width: 0;\n}\n\n/* 现代化图标样式 - 苹果设计风格 */\n.document-icon.collection {\n\tbackground: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);\n}\n\n.document-icon.pdf {\n\tbackground: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);\n}\n\n.document-icon.doc, .document-icon.docx {\n\tbackground: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);\n}\n\n.document-icon.xls, .document-icon.xlsx {\n\tbackground: linear-gradient(135deg, #a8e6cf 0%, #7fcdcd 100%);\n}\n\n.document-icon.ppt, .document-icon.pptx {\n\tbackground: linear-gradient(135deg, #ffd93d 0%, #ff6b6b 100%);\n}\n\n.document-icon.txt {\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.document-icon.zip, .document-icon.rar {\n\tbackground: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.document-icon.default {\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n/* 图标内部的图标样式增强 */\n.document-icon uni-icons {\n\tposition: relative;\n\tz-index: 2;\n}\n\n.document-item .document-title {\n\tfont-size: 30rpx;\n\tfont-weight: 600;\n\tcolor: #1a1a1a;\n\tmargin-bottom: 12rpx;\n\tline-height: 1.4;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\tdisplay: -webkit-box;\n\t-webkit-line-clamp: 2;\n\t-webkit-box-orient: vertical;\n\tword-break: break-all;\n}\n\n\n\n.document-item .document-info {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 16rpx;\n\tgap: 12rpx;\n}\n\n.document-item .category-tag {\n\tpadding: 4rpx 12rpx;\n\tbackground-color: #e3f2fd;\n\tcolor: #1976d2;\n\tfont-size: 22rpx;\n\tborder-radius: 8rpx;\n}\n\n.document-item .file-size, .document-item .file-count {\n\tfont-size: 24rpx;\n\tcolor: #8a94a6;\n\tfont-weight: 400;\n}\n\n\n\n.document-item .document-meta {\n\tdisplay: flex;\n\talign-items: center;\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tmargin-bottom: 12rpx;\n\tgap: 20rpx;\n}\n\n.meta-item {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 6rpx;\n}\n\n.document-desc {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tline-height: 1.5;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\tdisplay: -webkit-box;\n\t-webkit-line-clamp: 2;\n\t-webkit-box-orient: vertical;\n}\n\n.document-actions {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tmargin-left: 20rpx;\n}\n\n.action-btn {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder-radius: 50%;\n\tmargin-bottom: 12rpx;\n\ttransition: all 0.3s;\n\t\n\t&.download-btn {\n\t\tbackground-color: #e8f5e8;\n\t\tcolor: #4caf50;\n\t}\n\t\n\t&.edit-btn {\n\t\tbackground-color: #e3f2fd;\n\t\tcolor: #1976d2;\n\t}\n\t\n\t&.delete-btn {\n\t\twidth: 48rpx;\n\t\theight: 48rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder-radius: 50%;\n\t\ttransition: all 0.3s;\n\t\tcursor: pointer;\n\t\tbackground-color: #ffebee;\n\t\t\n\t\t&:hover {\n\t\t\tbackground-color: #ffcdd2;\n\t\t}\n\t}\n\t\n\t&:active {\n\t\ttransform: scale(0.9);\n\t}\n}\n\n.load-more {\n\tpadding: 40rpx 0;\n\tbackground-color: #fff;\n\tborder-top: 1rpx solid #f0f0f0;\n}\n\n.load-more-content {\n\tdisplay: flex;\n\tjustify-content: center;\n}\n\n.no-more-content {\n\tdisplay: flex;\n\tjustify-content: center;\n}\n\n.no-more-text {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.fab-container {\n\tposition: fixed;\n\tbottom: 120rpx;\n\tright: 40rpx;\n\tz-index: 99;\n}\n\n.fab-btn {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tbackground-color: #007aff;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);\n\ttransition: all 0.3s;\n\n\t&:active {\n\t\ttransform: scale(0.9);\n\t}\n}\n\n// 弹窗样式\n.upload-modal {\n\twidth: 640rpx;\n\tmax-height: 85vh;\n\tmin-height: 400rpx;\n\tbackground-color: #fff;\n\tborder-radius: 16rpx;\n\toverflow: hidden;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n/* 通用模态框头部样式 */\n.modal-header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 40rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n\tbackground: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\n\tflex-shrink: 0;\n\t\n\t&.flex-start {\n\t\talign-items: flex-start;\n\t}\n}\n\n.modal-title {\n\tfont-size: 36rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n\n.modal-close {\n\twidth: 48rpx;\n\theight: 48rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 36rpx;\n\tcolor: #999;\n\tborder-radius: 50%;\n\tbackground-color: #f5f5f5;\n\ttransition: none;\n\tcursor: pointer;\n}\n\n.modal-close:active {\n\tbackground-color: #e0e0e0;\n\ttransform: scale(0.95);\n}\n\n.modal-content {\n\tpadding: 32rpx 32rpx 8rpx 32rpx;\n\tflex: 1;\n\toverflow-y: auto;\n\t-webkit-overflow-scrolling: touch;\n}\n\n.form-item {\n\tmargin-bottom: 24rpx;\n}\n\n.form-label {\n\tdisplay: block;\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tmargin-bottom: 16rpx;\n}\n\n.form-input,\n.form-textarea {\n\twidth: 100%;\n\tpadding: 20rpx;\n\tborder: 1rpx solid #ddd;\n\tborder-radius: 8rpx;\n\tfont-size: 28rpx;\n\tbox-sizing: border-box;\n}\n\n.form-textarea {\n\tmin-height: 120rpx;\n\tresize: none;\n}\n\n.category-selector {\n\tcursor: pointer;\n}\n\n.picker-input {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 20rpx;\n\tborder: 1rpx solid #ddd;\n\tborder-radius: 8rpx;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.picker-arrow {\n\tcolor: #999;\n}\n\n.current-document {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 20rpx;\n\tbackground-color: #f0f8ff;\n\tborder: 1rpx solid #e3f2fd;\n\tborder-radius: 8rpx;\n}\n\n/* 集合编辑样式 */\n.collection-edit {\n\tmargin-top: 10rpx;\n}\n\n.collection-header {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 20rpx;\n\tbackground-color: #f8f9fa;\n\tborder: 2rpx solid #e9ecef;\n\tborder-radius: 12rpx;\n\tmargin-bottom: 16rpx;\n}\n\n.collection-icon {\n\tmargin-right: 16rpx;\n}\n\n.collection-info {\n\tflex: 1;\n}\n\n.collection-title {\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n\tdisplay: block;\n\tmargin-bottom: 4rpx;\n}\n\n.collection-hint {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.collection-files {\n\tmargin-bottom: 16rpx;\n\tmax-height: 300rpx;\n\toverflow-y: auto;\n\t-webkit-overflow-scrolling: touch;\n\tpadding: 20rpx 0;\n\tposition: relative;\n}\n\n.collection-file-item {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 12rpx;\n\tpadding: 16rpx;\n\tbackground-color: #fafbfc;\n\tborder: 1rpx solid #e0e0e0;\n\tborder-radius: 12rpx;\n\tmargin-bottom: 12rpx;\n\tmin-height: 60rpx;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n\ttransition: all 0.3s ease;\n\tposition: relative;\n\toverflow: hidden;\n\ttransform: translateY(0);\n\t\n\t&:hover {\n\t\tbackground-color: #f0f8ff;\n\t\tborder-color: #007aff;\n\t\ttransform: translateY(-2rpx);\n\t\tbox-shadow: 0 6rpx 16rpx rgba(0, 122, 255, 0.2);\n\t}\n\t\n\t&:first-child {\n\t\tmargin-top: 2rpx;\n\t}\n\t\n\t&:last-child {\n\t\tmargin-bottom: 0;\n\t}\n}\n\n.collection-file-item .file-icon {\n\twidth: 40rpx;\n\theight: 40rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder-radius: 8rpx;\n\tbackground-color: #fff;\n\tflex-shrink: 0;\n\tbox-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\n}\n\n.collection-file-item .file-details {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 4rpx;\n\tmin-width: 0;\n\toverflow: hidden;\n}\n\n.collection-file-item .file-name {\n\tfont-size: 26rpx;\n\tcolor: #333;\n\tfont-weight: 500;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\twhite-space: nowrap;\n\tline-height: 1.4;\n}\n\n.collection-file-item .file-size {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.collection-actions {\n\tdisplay: flex;\n\tjustify-content: center;\n\tmargin-top: 16rpx;\n\tmargin-bottom: 16rpx;\n}\n\n.collection-add-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 20rpx 40rpx;\n\tbackground-color: #fafafa;\n\tcolor: #333;\n\tborder: 2rpx dashed #ddd;\n\tborder-radius: 12rpx !important;\n\tfont-size: 30rpx;\n\tgap: 16rpx;\n\ttransition: all 0.3s ease;\n\tcursor: pointer;\n\twidth: 100%;\n\theight: auto !important;\n\t\n\t&:hover {\n\t\tborder-color: #007aff;\n\t\tbackground-color: #f0f8ff;\n\t\tcolor: #007aff;\n\t\t\n\t\t.uni-icons {\n\t\t\tcolor: #007aff !important;\n\t\t}\n\t}\n\t\n\t&:active {\n\t\ttransform: scale(0.98);\n\t}\n}\n\n.add-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 12rpx 24rpx;\n\tbackground-color: #007aff;\n\tcolor: #fff;\n\tborder-radius: 8rpx;\n\tfont-size: 26rpx;\n\tgap: 8rpx;\n}\n\n/* 分类编辑弹窗样式 */\n.category-edit-modal {\n\twidth: 600rpx;\n\tbackground-color: #fff;\n\tborder-radius: 20rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);\n}\n\n\n.category-edit-modal .modal-content {\n\tpadding: 40rpx;\n}\n\n.category-edit-modal .form-item {\n\tmargin-bottom: 30rpx;\n}\n\n.category-edit-modal .form-label {\n\tdisplay: block;\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tfont-weight: 500;\n\tmargin-bottom: 12rpx;\n}\n\n.category-edit-modal .form-input {\n\twidth: 100%;\n\tpadding: 20rpx;\n\tborder: 2rpx solid #e9ecef;\n\tborder-radius: 12rpx;\n\tfont-size: 28rpx;\n\tbackground-color: #fff;\n\ttransition: all 0.3s;\n\t\n\t&:focus {\n\t\tborder-color: #007aff;\n\t\tbox-shadow: 0 0 0 3rpx rgba(0, 122, 255, 0.1);\n\t}\n}\n\n.category-edit-modal .form-textarea {\n\twidth: 100%;\n\tmin-height: 120rpx;\n\tpadding: 20rpx;\n\tborder: 2rpx solid #e9ecef;\n\tborder-radius: 12rpx;\n\tfont-size: 28rpx;\n\tbackground-color: #fff;\n\tresize: none;\n\ttransition: all 0.3s;\n\t\n\t&:focus {\n\t\tborder-color: #007aff;\n\t\tbox-shadow: 0 0 0 3rpx rgba(0, 122, 255, 0.1);\n\t}\n}\n\n.category-edit-modal .modal-footer {\n\tdisplay: flex;\n\tgap: 16rpx;\n\tpadding: 32rpx;\n\tborder-top: 1rpx solid #f0f0f0;\n}\n\n.category-edit-modal .btn {\n\tflex: 1;\n\tpadding: 20rpx;\n\tborder-radius: 8rpx;\n\tfont-size: 26rpx;\n\tfont-weight: 500;\n\tborder: none;\n\tcursor: pointer;\n\ttransition: none;\n}\n\n.doc-icon {\n\twidth: 48rpx;\n\theight: 48rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground-color: #fff;\n\tborder-radius: 6rpx;\n\tmargin-right: 16rpx;\n\tbox-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);\n}\n\n.doc-info {\n\tflex: 1;\n}\n\n.doc-name {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tfont-weight: 500;\n\tdisplay: block;\n\tmargin-bottom: 6rpx;\n}\n\n.doc-hint {\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n.file-upload-container {\n\twidth: 100%;\n}\n\n.upload-area {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 60rpx 40rpx;\n\tborder: 2rpx dashed #ddd;\n\tborder-radius: 12rpx;\n\tbackground-color: #fafafa;\n\tcursor: pointer;\n\t\n\t&:hover {\n\t\tborder-color: #007aff;\n\t\tbackground-color: #f0f8ff;\n\t}\n\t\n\t&.uploading {\n\t\tborder-color: #007aff;\n\t\tbackground-color: #f0f8ff;\n\t\tpointer-events: none;\n\t}\n}\n\n.upload-icon {\n\tmargin-bottom: 20rpx;\n}\n\n.upload-text {\n\tfont-size: 32rpx;\n\tcolor: #333;\n\tfont-weight: 500;\n\tmargin-bottom: 12rpx;\n}\n\n.upload-hint {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\ttext-align: center;\n}\n\n.file-selected {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 24rpx;\n\tbackground-color: #f0f8ff;\n\tborder: 1rpx solid #e3f2fd;\n\tborder-radius: 12rpx;\n}\n\n.file-icon {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground-color: #fff;\n\tborder-radius: 8rpx;\n\tmargin-right: 20rpx;\n\tbox-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n}\n\n.file-details {\n\tflex: 1;\n\tmin-width: 0;\n}\n\n.file-name {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tfont-weight: 500;\n\tdisplay: block;\n\tmargin-bottom: 8rpx;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\twhite-space: nowrap;\n}\n\n.file-size {\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n.file-actions {\n\tdisplay: flex;\n\tgap: 12rpx;\n}\n\n.remove-btn {\n\twidth: 48rpx;\n\theight: 48rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder-radius: 50%;\n\ttransition: all 0.3s;\n\tcursor: pointer;\n\tbackground-color: #ffebee;\n\t\n\t&:hover {\n\t\tbackground-color: #ffcdd2;\n\t}\n}\n\n/* 通用模态框底部样式 */\n.modal-footer {\n\tdisplay: flex;\n\tpadding: 24rpx 32rpx;\n\tborder-top: 1rpx solid #f0f0f0;\n\tgap: 16rpx;\n\tbackground-color: #fafafa;\n\tflex-shrink: 0;\n}\n\n.btn {\n\tflex: 1;\n\tpadding: 20rpx;\n\tborder: none;\n\tborder-radius: 8rpx;\n\tfont-size: 26rpx;\n\tfont-weight: 500;\n\ttransition: none;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tgap: 8rpx;\n\t\n\t&.btn-cancel {\n\t\tbackground-color: #f8f9fa;\n\t\tcolor: #666;\n\t\tborder: 1rpx solid #e9ecef;\n\t\t\n\t\t&:active {\n\t\t\tbackground-color: #e9ecef;\n\t\t\ttransform: scale(0.98);\n\t\t}\n\t}\n\t\n\t&.btn-primary {\n\t\tbackground-color: #007aff;\n\t\tcolor: #fff;\n\t\t\n\t\t&:active {\n\t\t\ttransform: scale(0.98);\n\t\t}\n\t\t\n\t\t&[disabled] {\n\t\t\tbackground-color: #ccc;\n\t\t\ttransform: none;\n\t\t}\n\t}\n}\n\n// 选择器弹窗样式\n.category-manager-overlay {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground-color: rgba(0, 0, 0, 0.5);\n\tz-index: 1001;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.selector-modal-content {\n\tbackground-color: #fff;\n\tborder-top-left-radius: 20rpx;\n\tborder-top-right-radius: 20rpx;\n}\n\n.category-manager-content {\n\twidth: 90%;\n\tmax-width: 500px;\n\tmax-height: 80vh;\n\tbackground-color: #fff;\n\tborder-radius: 16rpx;\n\toverflow: hidden;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.selector-modal-header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 30rpx 40rpx;\n\tborder-bottom: 1rpx solid #f5f5f5;\n}\n\n.manager-header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 40rpx;\n\tborder-bottom: 1rpx solid #eee;\n\tflex-shrink: 0;\n}\n\n.selector-modal-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n\n.manager-title {\n\tfont-size: 36rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n\n.selector-modal-close,\n.manager-close {\n\tcursor: pointer;\n\twidth: 48rpx;\n\theight: 48rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder-radius: 50%;\n\tbackground-color: #f5f5f5;\n\ttransition: none;\n}\n\n.selector-modal-close:active,\n.manager-close:active {\n\tbackground-color: #e0e0e0;\n\ttransform: scale(0.95);\n}\n\n.manager-add-section {\n\tpadding: 20rpx 30rpx;\n\tborder-top: 1rpx solid #f0f0f0;\n}\n\n.add-form {\n\tdisplay: flex;\n\talign-items: flex-start;\n\tgap: 20rpx;\n}\n\n.form-row {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 10rpx;\n}\n\n.form-input {\n\twidth: 100%;\n\tpadding: 16rpx 20rpx;\n\tborder: 1rpx solid #e0e0e0;\n\tborder-radius: 8rpx;\n\tfont-size: 28rpx;\n\tbackground-color: #fff;\n\tbox-sizing: border-box;\n\ttransition: all 0.3s ease;\n\toutline: none;\n\tmin-height: 80rpx;\n\t\n\t/* #ifdef MP-WEIXIN */\n\t/* 小程序特殊样式 */\n\tborder: 2rpx solid #e0e0e0;\n\tpadding: 20rpx 24rpx;\n\tfont-size: 30rpx;\n\tline-height: 1.4;\n\t/* #endif */\n\t\n\t&:focus {\n\t\tborder-color: #007aff;\n\t\tbox-shadow: 0 0 0 2rpx rgba(0, 122, 255, 0.1);\n\t\t\n\t\t/* #ifdef MP-WEIXIN */\n\t\tborder-color: #007aff;\n\t\tbackground-color: #fafbfc;\n\t\t/* #endif */\n\t}\n\t\n\t&::placeholder {\n\t\tcolor: #c0c4cc;\n\t\tfont-size: 26rpx;\n\t\t\n\t\t/* #ifdef MP-WEIXIN */\n\t\tcolor: #999;\n\t\tfont-size: 28rpx;\n\t\t/* #endif */\n\t}\n}\n\n.add-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 0 30rpx;\n\theight: 112rpx;\n\tbackground-color: #007aff;\n\tcolor: #fff;\n\tborder: none;\n\tborder-radius: 8rpx;\n\tfont-size: 28rpx;\n\twhite-space: nowrap;\n\ttransition: all 0.3s ease;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);\n\tmin-width: 120rpx;\n\t\n\t/* #ifdef MP-WEIXIN */\n\t/* 小程序按钮优化 */\n\theight: 80rpx;\n\tpadding: 0 24rpx;\n\tfont-size: 30rpx;\n\tborder-radius: 6rpx;\n\tbox-shadow: 0 2rpx 6rpx rgba(0, 122, 255, 0.2);\n\t/* #endif */\n\t\n\t&:active {\n\t\ttransform: translateY(1rpx);\n\t\tbox-shadow: 0 1rpx 4rpx rgba(0, 122, 255, 0.3);\n\t\t\n\t\t/* #ifdef MP-WEIXIN */\n\t\tbackground-color: #0056d6;\n\t\ttransform: scale(0.98);\n\t\t/* #endif */\n\t}\n\t\n\t&[disabled] {\n\t\tbackground-color: #c8c9cc;\n\t\tbox-shadow: none;\n\t\ttransform: none;\n\t}\n\t\n\t.uni-icons {\n\t\tmargin-right: 8rpx;\n\t}\n}\n\n.selector-list-container {\n\tmax-height: 60vh;\n\tpadding: 20rpx 0;\n}\n\n.manager-list {\n\tflex-grow: 1;\n\toverflow-y: auto;\n}\n\n/* 通用空状态样式 */\n.empty-category-hint,\n.empty-state,\n.empty-files {\n\tpadding: 80rpx 40rpx;\n\ttext-align: center;\n\tcolor: #999;\n}\n\n.empty-text {\n\tfont-size: 28rpx;\n\tcolor: #999;\n\tdisplay: block;\n\tmargin: 20rpx 0 10rpx;\n}\n\n.empty-desc {\n\tfont-size: 24rpx;\n\tcolor: #ccc;\n}\n\n.selector-item {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 30rpx 40rpx;\n\ttransition: background-color 0.2s;\n\t\n\t&.selected {\n\t\t.selector-name {\n\t\t\tcolor: #007aff;\n\t\t\tfont-weight: 600;\n\t\t}\n\t}\n\t\n\t&:active {\n\t\tbackground-color: #f5f5f5;\n\t}\n}\n\n.manager-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 30rpx 40rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n\ttransition: all 0.3s;\n\t\n\t&:active {\n\t\tbackground-color: #f5f5f5;\n\t}\n}\n\n.selector-item-left {\n\twidth: 40rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.selector-item-content {\n\tflex: 1;\n}\n\n.item-info {\n\tflex: 1;\n\tmin-width: 0;\n\tmargin-right: 20rpx;\n}\n\n.selector-name {\n\tfont-size: 30rpx;\n\tcolor: #333;\n}\n\n.item-name {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tdisplay: block;\n\tmargin-bottom: 8rpx;\n}\n\n.item-desc {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tdisplay: block;\n}\n\n.item-count {\n\tfont-size: 22rpx;\n\tcolor: #666;\n\tdisplay: block;\n\tmargin-top: 4rpx;\n}\n\n.item-actions {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 16rpx;\n}\n\n/* 通用动作按钮样式 */\n.action-btn, .manager-action-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder-radius: 50%;\n\ttransition: all 0.3s;\n\tcursor: pointer;\n\t\n\t&.edit-btn {\n\t\tbackground-color: #e3f2fd;\n\t\tcolor: #1976d2;\n\t}\n\t\n\t&.delete-btn {\n\t\twidth: 48rpx;\n\t\theight: 48rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder-radius: 50%;\n\t\ttransition: all 0.3s;\n\t\tcursor: pointer;\n\t\tbackground-color: #ffebee;\n\t\t\n\t\t&:hover {\n\t\t\tbackground-color: #ffcdd2;\n\t\t}\n\t}\n\t\n\t&:active {\n\t\ttransform: scale(0.9);\n\t}\n}\n\n.action-btn {\n\twidth: 48rpx;\n\theight: 48rpx;\n}\n\n.manager-action-btn {\n\twidth: 60rpx;\n\theight: 60rpx;\n}\n\n/* 文档类型选择器样式 */\n.type-selector {\n\tdisplay: flex;\n\tgap: 20rpx;\n\tmargin-top: 10rpx;\n}\n\n.type-option {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 10rpx;\n\tpadding: 20rpx 30rpx;\n\tborder: 2rpx solid #e0e0e0;\n\tborder-radius: 12rpx;\n\tbackground-color: #fff;\n\tflex: 1;\n\tjustify-content: center;\n\tcursor: pointer;\n\ttransition: all 0.2s ease;\n\t\n\t&.active {\n\t\tborder-color: #007aff;\n\t\tbackground-color: #f0f8ff;\n\t}\n\t\n\t&:hover {\n\t\tborder-color: #007aff;\n\t\tbackground-color: #f8fbff;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.15);\n\t\ttransform: translateY(-1rpx);\n\t}\n\t\n\t&.active:hover {\n\t\tbackground-color: #e6f3ff;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.2);\n\t\ttransform: translateY(-1rpx);\n\t}\n\t\n\t&:active {\n\t\ttransform: translateY(0);\n\t\tbox-shadow: 0 1rpx 4rpx rgba(0, 122, 255, 0.2);\n\t}\n}\n\n.type-text {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\ttransition: color 0.2s ease;\n\t\n\t&.active {\n\t\tcolor: #007aff;\n\t\tfont-weight: 500;\n\t}\n}\n\n.type-option:hover .type-text {\n\tcolor: #007aff;\n}\n\n.type-option:hover .uni-icons {\n\tcolor: #007aff !important;\n}\n\n.type-option.active:hover .type-text {\n\tcolor: #007aff;\n}\n\n.type-option.active:hover .uni-icons {\n\tcolor: #007aff !important;\n}\n\n/* 多文件上传样式 */\n.multi-file-upload {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 20rpx;\n}\n\n.upload-area.multi-upload {\n\tborder: 2rpx dashed #ddd;\n\tbackground-color: #fafafa;\n\t\n\t&:hover {\n\t\tborder-color: #007aff;\n\t\tbackground-color: #f8fbff;\n\t}\n}\n\n.files-list {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 15rpx;\n\tmax-height: 300rpx;\n\toverflow-y: auto;\n\t-webkit-overflow-scrolling: touch;\n}\n\n.file-item {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 12rpx;\n\tpadding: 16rpx;\n\tborder: 1rpx solid #e0e0e0;\n\tborder-radius: 8rpx;\n\tbackground-color: #fff;\n\tmin-height: 60rpx;\n\t\n\t&:hover {\n\t\tborder-color: #007aff;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.1);\n\t}\n}\n\n.file-item .file-icon {\n\twidth: 40rpx;\n\theight: 40rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder-radius: 6rpx;\n\tbackground-color: #f5f5f5;\n\tflex-shrink: 0;\n}\n\n.file-item .file-details {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 4rpx;\n\tmin-width: 0;\n\toverflow: hidden;\n}\n\n.file-item .file-name {\n\tfont-size: 26rpx;\n\tcolor: #333;\n\tfont-weight: 500;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\twhite-space: nowrap;\n\tline-height: 1.4;\n}\n\n.file-item .file-size {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.file-item .file-actions {\n\tdisplay: flex;\n\tgap: 8rpx;\n\tflex-shrink: 0;\n\talign-items: center;\n}\n\n/* 文档集合下载弹窗样式 */\n.collection-download-modal {\n\twidth: 680rpx;\n\tmax-height: 80vh;\n\tbackground-color: #fff;\n\tborder-radius: 20rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);\n}\n\n/* 集合下载模态框头部样式已整合到通用模态框样式中 */\n\n.header-content {\n\tflex: 1;\n\tmargin-right: 20rpx;\n}\n\n.collection-info {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 12rpx;\n}\n\n.collection-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #1a1a1a;\n\tmargin-left: 12rpx;\n}\n\n.collection-desc {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tline-height: 1.4;\n}\n\n.modal-close {\n\twidth: 48rpx;\n\theight: 48rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder-radius: 50%;\n\tbackground-color: #f5f5f5;\n\ttransition: none;\n}\n\n.modal-close:active {\n\tbackground-color: #e0e0e0;\n\ttransform: scale(0.95);\n}\n\n.collection-download-modal .modal-content {\n\tpadding: 0;\n\tmax-height: 60vh;\n}\n\n.files-section {\n\tpadding: 0 40rpx;\n}\n\n.section-header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 30rpx 0 20rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.section-title {\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tcolor: #1a1a1a;\n}\n\n.file-count {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tbackground-color: #f0f8ff;\n\tpadding: 6rpx 12rpx;\n\tborder-radius: 12rpx;\n}\n\n.files-list {\n\tmax-height: 400rpx;\n\tpadding: 20rpx 0;\n}\n\n.file-item {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 32rpx 24rpx;\n\tmargin-bottom: 16rpx;\n\tbackground-color: #fafbfc;\n\tborder-radius: 16rpx;\n\tborder: 1rpx solid #f0f0f0;\n\ttransition: none;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n\tposition: relative;\n\toverflow: hidden;\n\ttransform: translateY(0);\n}\n\n.file-item:last-child {\n\tmargin-bottom: 0;\n}\n\n.file-item.downloading {\n\tbackground-color: #f0f8ff;\n\tborder-color: #e3f2fd;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.1);\n}\n\n/* 优化第一个文件卡片的悬停效果 */\n.files-list .file-item:first-child:hover {\n\tbackground-color: #f0f8ff;\n\tborder-color: #007aff;\n\ttransform: translateY(-2rpx);\n\tbox-shadow: 0 6rpx 16rpx rgba(0, 122, 255, 0.2);\n}\n\n.file-item:hover {\n\tbackground-color: #f0f8ff;\n\tborder-color: #007aff;\n\ttransform: translateY(-2rpx);\n\tbox-shadow: 0 6rpx 16rpx rgba(0, 122, 255, 0.2);\n}\n\n/* 修复悬停时的空白问题 */\n.files-list {\n\tmax-height: 300rpx;\n\tpadding: 20rpx 0;\n\toverflow-y: auto;\n\tposition: relative;\n\t/* 优化滚动条样式 */\n\t-webkit-overflow-scrolling: touch;\n}\n\n.files-list .file-item:first-child {\n\tmargin-top: 2rpx;\n}\n\n.files-list .file-item:last-child {\n\tmargin-bottom: 0;\n}\n\n/* 确保文件项在容器内正确显示 */\n.files-section {\n\tpadding: 0 40rpx;\n\tposition: relative;\n}\n\n.file-info {\n\tdisplay: flex;\n\talign-items: center;\n\tflex: 1;\n\tmin-width: 0;\n}\n\n.file-icon {\n\twidth: 56rpx;\n\theight: 56rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground-color: #fff;\n\tborder-radius: 12rpx;\n\tmargin-right: 20rpx;\n\tbox-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\n\tborder: 1rpx solid #f0f0f0;\n}\n\n.file-details {\n\tflex: 1;\n\tmin-width: 0;\n}\n\n.file-name {\n\tfont-size: 28rpx;\n\tcolor: #1a1a1a;\n\tfont-weight: 500;\n\tdisplay: block;\n\tmargin-bottom: 6rpx;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\twhite-space: nowrap;\n}\n\n.file-size {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.file-actions {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-left: 20rpx;\n}\n\n.download-btn {\n\twidth: 56rpx;\n\theight: 56rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground-color: #f0f8ff;\n\tborder-radius: 50%;\n\ttransition: all 0.3s ease;\n}\n\n.download-btn:active {\n\tbackground-color: #e3f2fd;\n\ttransform: scale(0.95);\n}\n\n.download-progress {\n\twidth: 56rpx;\n\theight: 56rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n\n\n.download-success {\n\twidth: 56rpx;\n\theight: 56rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground-color: #e8f5e8;\n\tborder-radius: 50%;\n}\n\n/* 文档预览弹窗样式 */\n.document-preview-modal {\n\twidth: 680rpx;\n\tmax-height: 80vh;\n\tbackground-color: #fff;\n\tborder-radius: 20rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);\n}\n\n.document-info {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 12rpx;\n}\n\n.document-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #1a1a1a;\n\tmargin-left: 12rpx;\n}\n\n.document-desc {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tline-height: 1.4;\n}\n\n.preview-section {\n\tpadding: 20rpx 0;\n}\n\n.preview-item {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 24rpx 0;\n\tborder-bottom: 1rpx solid #f8f8f8;\n}\n\n.preview-item:last-child {\n\tborder-bottom: none;\n}\n\n.preview-label {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tfont-weight: 500;\n}\n\n.preview-value {\n\tfont-size: 28rpx;\n\tcolor: #1a1a1a;\n\tfont-weight: 500;\n}\n\n\n/* 弹窗高层级样式 - 确保弹窗在浮动按钮之上 */\n.popup-high-level {\n\tz-index: 100 !important;\n}\n\n</style>", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./study-center.vue?vue&type=style&index=0&id=93367c6c&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./study-center.vue?vue&type=style&index=0&id=93367c6c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775852493\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}