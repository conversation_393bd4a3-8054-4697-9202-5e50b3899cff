{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s_pkg/public-schedule.vue?fdff", "webpack:///D:/Xwzc/pages/6s_pkg/public-schedule.vue?2536", "webpack:///D:/Xwzc/pages/6s_pkg/public-schedule.vue?c218", "webpack:///D:/Xwzc/pages/6s_pkg/public-schedule.vue?a305", "uni-app:///pages/6s_pkg/public-schedule.vue", "webpack:///D:/Xwzc/pages/6s_pkg/public-schedule.vue?c7e1", "webpack:///D:/Xwzc/pages/6s_pkg/public-schedule.vue?9a87"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "areaList", "loading", "saving", "currentArea", "scheduleData", "scheduled_day", "start_date", "filterOptions", "value", "label", "weekDayOptions", "filterIndex", "scheduleWeekIndex", "computed", "filtered<PERSON><PERSON>s", "weekDays", "startOfWeek", "i", "currentDay", "days", "date", "isToday", "areas", "onLoad", "methods", "getPreviewState", "hasValidSchedule", "area", "loadData", "action", "type", "result", "filter", "map", "next_clean_date", "console", "uni", "title", "icon", "calculateNextCleanDate", "startDate", "nextDate", "getNextMonday", "nextMonday", "onFilterChange", "setSchedule", "clearSchedule", "content", "confirmText", "confirmColor", "success", "performClearSchedule", "id", "index", "updatedArea", "updated_at", "onScheduleWeekChange", "onStartDateChange", "submitSchedule", "successMessage", "closeSchedulePopup", "setTimeout", "resetPopupState", "getWeekDayText", "formatDate", "tomorrow", "formatFullDate", "getEmptyText", "getEmptyDesc"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5HA;AAAA;AAAA;AAAA;AAAymB,CAAgB,moBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACmO7nB;AAAA;AAAA;AAAA,eAEA;EACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;MAEAC,gBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC,iBACA;QAAAF;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MAEAE;MACAC;IACA;EACA;EAEAC;IACAC;MACA;MACA;QACA;UACA;YAAA;UAAA;QACA;UACA;YAAA;UAAA;QACA;UACA;UACA;YAAA;UAAA;QACA;UACA;MAAA;IAEA;IAEAC;MAAA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACAC;MACA;MAAA,2BACAC;QACA;QACAC;QAEA;QACA;;QAEAC;UACArB;UACAsB;UACAC;UACAC;YAAA;UAAA;QACA;MAAA;MAZA;QAAA;MAaA;MAEA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MACA,8DACA,iDACA;MACA;MAEA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA,sCACAC,oCACA,0CACAA,2BACAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;;gBAEA;gBAAA;gBAAA,OACA;kBACAC;kBACA9B;oBACA+B;kBACA;gBACA;cAAA;gBALAC;gBAOA,2CACAC;kBAAA;gBAAA,GACAC;kBAAA,uCACAN;oBACAO;kBAAA;gBAAA,CACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;MACA;MAEA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;QACA;UACAC;QACA;QACA;MACA;;MAEA;MACA;MACA;QACAC;MACA;;MAEA;MACA;QACA;UAAA;UACAA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACAxC;QACAC;MACA;;MAEA;MACA;QAAA;MAAA;MACA;MACA;IACA;IAEA;IACAwC;MAAA;MACAV;QACAC;QACAU;QACAC;QACAC;QACAC;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;kBACAtB;kBACA9B;oBACAqD;oBACA/C;oBACAC;kBACA;gBACA;cAAA;gBAEA;gBACA+C;kBAAA;gBAAA;gBACA;kBACAC,8CACA;oBACAjD;oBACAC;oBACA4B;oBACAqB;kBAAA,IAGA;kBACA;gBACA;gBAEAnB;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAH;gBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAkB;MACA;MACA;MACA;QACA;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACAtB;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAKA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAKA;;gBAEA;gBAAA;gBAAA,OACA;kBACAT;kBACA9B;oBACAqD;oBACA/C;oBACAC;kBACA;gBACA;cAAA;gBAEA;gBACA+C;kBAAA;gBAAA;gBACA;kBACAC,8CACA;oBACAjD;oBACAC;oBACA4B,+EACA;sBACA7B;sBACAC;oBAAA,GACA;oBACAiD;kBAAA,IAGA;kBACA;gBACA;;gBAEA;gBACAI;gBAEAvB;kBACAC;kBACAC;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAH;gBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAsB;MAAA;MACA;;MAEA;MACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAzD;QACAC;MACA;MACA;IACA;IAEA;IACAyD;MACA;QACA;MACA;MAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACA1C;MACA;MACA;MACA;MACA;IACA;IAEA;IACA2C;MACA;MAEA;QACA;;QAEA;QACA;UACA;QACA;QAEA;QACA;QACAC;QACA;QACA;QACA;QAEA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA9B;QACA;MACA;IACA;IAEA;IACA+B;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzrBA;AAAA;AAAA;AAAA;AAA4qC,CAAgB,kpCAAG,EAAC,C;;;;;;;;;;;ACAhsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/public-schedule.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/public-schedule.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./public-schedule.vue?vue&type=template&id=4ede77af&scoped=true&\"\nvar renderjs\nimport script from \"./public-schedule.vue?vue&type=script&lang=js&\"\nexport * from \"./public-schedule.vue?vue&type=script&lang=js&\"\nimport style0 from \"./public-schedule.vue?vue&type=style&index=0&id=4ede77af&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4ede77af\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/public-schedule.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./public-schedule.vue?vue&type=template&id=4ede77af&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.weekDays, function (day, index) {\n    var $orig = _vm.__get_orig(day)\n    var g0 = day.areas.length\n    var g1 = day.areas.length\n    var g2 = g1 > 0 ? day.areas.length : null\n    return {\n      $orig: $orig,\n      g0: g0,\n      g1: g1,\n      g2: g2,\n    }\n  })\n  var g3 = !_vm.loading ? _vm.filteredAreas.length : null\n  var l1 =\n    !_vm.loading && g3 > 0\n      ? _vm.__map(_vm.filteredAreas, function (area, index) {\n          var $orig = _vm.__get_orig(area)\n          var m0 = _vm.hasValidSchedule(area)\n          var m1 = m0 ? _vm.getWeekDayText(area.scheduled_day) : null\n          var m2 = _vm.hasValidSchedule(area)\n          var m3 = m2 ? _vm.isToday(area.next_clean_date) : null\n          var m4 = m2 ? _vm.formatDate(area.next_clean_date) : null\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            m2: m2,\n            m3: m3,\n            m4: m4,\n          }\n        })\n      : null\n  var g4 =\n    !_vm.loading && !(g3 > 0)\n      ? !_vm.loading &&\n        _vm.areaList.length > 0 &&\n        _vm.filteredAreas.length === 0\n      : null\n  var g5 =\n    !_vm.loading && !(g3 > 0) && !g4\n      ? !_vm.loading && _vm.areaList.length === 0\n      : null\n  var m5 = !_vm.loading && !(g3 > 0) && !g4 && g5 ? _vm.getEmptyText() : null\n  var m6 = !_vm.loading && !(g3 > 0) && !g4 && g5 ? _vm.getEmptyDesc() : null\n  var m7 = _vm.currentArea ? _vm.getPreviewState() : null\n  var m8 =\n    _vm.currentArea && m7 === \"complete\"\n      ? _vm.formatFullDate(_vm.scheduleData.start_date)\n      : null\n  var m9 =\n    _vm.currentArea && m7 === \"complete\"\n      ? _vm.getWeekDayText(_vm.scheduleData.scheduled_day)\n      : null\n  var m10 =\n    _vm.currentArea && !(m7 === \"complete\") ? _vm.getPreviewState() : null\n  var m11 =\n    _vm.currentArea &&\n    !(m7 === \"complete\") &&\n    !(m10 === \"clear\") &&\n    _vm.scheduleData.start_date\n      ? _vm.formatFullDate(_vm.scheduleData.start_date)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g3: g3,\n        l1: l1,\n        g4: g4,\n        g5: g5,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./public-schedule.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./public-schedule.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page-container\">\n    <!-- 页面头部 -->\n    <view class=\"header\">\n      <view class=\"header-title\">公共区清扫日设置</view>\n      <view class=\"header-subtitle\">设置公共责任区固定清扫日程安排</view>\n    </view>\n\n    <!-- 本周清扫概览 -->\n    <view class=\"week-overview\">\n      <view class=\"overview-title\">本周清扫安排</view>\n      <scroll-view scroll-x class=\"week-scroll\" show-scrollbar=\"false\">\n        <view class=\"week-grid\">\n          <view \n            class=\"day-item\"\n            v-for=\"(day, index) in weekDays\" \n            :key=\"index\"\n            :class=\"{ 'today': day.isToday, 'has-schedule': day.areas.length > 0 }\"\n          >\n            <view class=\"day-header\">\n              <text class=\"day-name\">{{ day.name }}</text>\n              <text class=\"day-date\">{{ day.date }}</text>\n            </view>\n            <view class=\"day-content\">\n              <view class=\"area-count\" v-if=\"day.areas.length > 0\">\n                {{ day.areas.length }}个区域\n              </view>\n              <view class=\"no-schedule\" v-else>\n                无安排\n              </view>\n            </view>\n          </view>\n        </view>\n      </scroll-view>\n    </view>\n\n    <!-- 筛选栏 -->\n    <view class=\"filter-bar\">\n      <picker \n        :range=\"filterOptions\" \n        range-key=\"label\" \n        :value=\"filterIndex\"\n        @change=\"onFilterChange\"\n        class=\"filter-picker\"\n      >\n        <view class=\"filter-value\">\n          <text>{{ filterOptions[filterIndex].label }}</text>\n          <uni-icons type=\"down\" size=\"14\" color=\"#8E8E93\" />\n        </view>\n      </picker>\n    </view>\n\n    <!-- 公共区列表 -->\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"list-loading\">\n      <view class=\"loading-content\">\n        <uni-icons type=\"spinner-cycle\" size=\"40\" color=\"#007AFF\"></uni-icons>\n        <text class=\"loading-text\">加载公共区数据中...</text>\n      </view>\n    </view>\n    \n    <!-- 正常列表 -->\n    <view v-else-if=\"filteredAreas.length > 0\" class=\"area-list\">\n      <view \n        class=\"area-item\"\n        v-for=\"(area, index) in filteredAreas\" \n        :key=\"index\"\n      >\n        <view class=\"area-main\">\n          <view class=\"area-header\">\n            <view class=\"area-name\">{{ area.name }}</view>\n            <view class=\"schedule-status\" :class=\"[area.scheduled_day !== null ? 'scheduled' : 'unscheduled']\">\n          {{ area.scheduled_day !== null ? '已排班' : '未排班' }}\n            </view>\n          </view>\n          <view class=\"area-info\">\n            <view class=\"info-item\">\n              <uni-icons type=\"location\" size=\"14\" color=\"#8E8E93\" />\n              <text>{{ (area.location && area.location.area) || area.location || '未设置位置' }}</text>\n            </view>\n            <view class=\"info-item\" v-if=\"hasValidSchedule(area)\">\n              <uni-icons type=\"calendar\" size=\"14\" color=\"#8E8E93\" />\n              <text>{{ getWeekDayText(area.scheduled_day) }}清扫</text>\n            </view>\n            <view class=\"info-item\" v-else>\n              <uni-icons type=\"calendar\" size=\"14\" color=\"#8E8E93\" />\n              <text>未设置清扫日程</text>\n            </view>\n            <view class=\"info-item\" v-if=\"hasValidSchedule(area)\">\n              <uni-icons type=\"time\" size=\"14\" color=\"#8E8E93\" />\n              <text :class=\"{ 'today': isToday(area.next_clean_date) }\">{{ formatDate(area.next_clean_date) }}</text>\n            </view>\n          </view>\n        </view>\n        <view class=\"area-actions\">\n          <button class=\"action-btn set\" @click=\"setSchedule(area)\">\n            <uni-icons type=\"calendar\" size=\"14\" color=\"#FFFFFF\" />\n            <text>{{ area.scheduled_day !== null ? '调整' : '设置' }}</text>\n        </button>\n        <button class=\"action-btn clear\" @click=\"clearSchedule(area)\" v-if=\"area.scheduled_day !== null\">\n            <uni-icons type=\"close\" size=\"14\" color=\"#FFFFFF\" />\n            <text>清除</text>\n          </button>\n        </view>\n      </view>\n    </view>\n\n    <!-- 筛选后无结果 -->\n    <view v-else-if=\"!loading && areaList.length > 0 && filteredAreas.length === 0\" class=\"filter-empty\">\n      <p-empty-state\n        type=\"search\"\n        text=\"当前筛选条件下暂无数据\"\n        description=\"可以尝试切换其他筛选条件\"\n      ></p-empty-state>\n    </view>\n\n    <!-- 真正的空状态 -->\n    <p-empty-state\n      v-else-if=\"!loading && areaList.length === 0\"\n      type=\"schedule\"\n      :text=\"getEmptyText()\"\n      :description=\"getEmptyDesc()\"\n    ></p-empty-state>\n\n    <!-- 排班设置弹窗 -->\n    <uni-popup ref=\"schedulePopup\" type=\"center\" :mask-click=\"false\">\n      <view class=\"schedule-popup\">\n        <view class=\"popup-header\">\n          <view class=\"popup-title\">设置清扫日程</view>\n          <button class=\"close-btn\" @click=\"closeSchedulePopup\">\n            <uni-icons type=\"close\" size=\"20\" color=\"#8E8E93\" />\n          </button>\n        </view>\n        \n        <view class=\"popup-content\" v-if=\"currentArea\">\n          <!-- 区域信息 -->\n          <view class=\"area-info-card\">\n            <view class=\"area-info-header\">\n              <view class=\"area-info-name\">{{ currentArea.name }}</view>\n              <view class=\"area-status-badge\">公共区域</view>\n            </view>\n            <view class=\"area-info-location\">\n              <uni-icons type=\"location\" size=\"14\" color=\"#8E8E93\" />\n              <text>{{ (currentArea.location && currentArea.location.area) || currentArea.location || '未设置位置' }}</text>\n            </view>\n          </view>\n          \n          <!-- 设置表单 -->\n          <view class=\"setting-form\">\n            <view class=\"form-group\">\n              <view class=\"form-label\">清扫日期</view>\n              <picker \n                :range=\"weekDayOptions\" \n                range-key=\"label\" \n                :value=\"scheduleWeekIndex\"\n                @change=\"onScheduleWeekChange\"\n                class=\"form-picker\"\n              >\n                <view class=\"form-input\">\n                  <text>{{ weekDayOptions[scheduleWeekIndex] ? weekDayOptions[scheduleWeekIndex].label : '请选择' }}</text>\n                  <view class=\"dropdown-icon\">\n                    <uni-icons type=\"bottom\" size=\"12\" color=\"#8E8E93\" />\n                  </view>\n                </view>\n              </picker>\n            </view>\n            \n            <view class=\"form-group\">\n              <view class=\"form-label\">开始日期</view>\n              <picker \n                mode=\"date\" \n                :value=\"scheduleData.start_date\" \n                @change=\"onStartDateChange\"\n                class=\"form-picker\"\n              >\n                <view class=\"form-input\">\n                  <text>{{ scheduleData.start_date || '请选择开始日期' }}</text>\n                  <view class=\"input-icon\">\n                    <uni-icons type=\"calendar\" size=\"14\" color=\"#007AFF\" />\n                  </view>\n                </view>\n              </picker>\n            </view>\n          </view>\n          \n          <!-- 设置预览 -->\n          <view class=\"setting-preview\">\n            <view class=\"preview-header\">\n              <uni-icons type=\"eye\" size=\"16\" color=\"#007AFF\" />\n              <text>设置预览</text>\n            </view>\n            \n            <!-- 完整设置预览 -->\n            <view v-if=\"getPreviewState() === 'complete'\" class=\"preview-summary complete\">\n              从 <text class=\"highlight-date\">{{ formatFullDate(scheduleData.start_date) }}</text> 开始，每 <text class=\"highlight-day\">{{ getWeekDayText(scheduleData.scheduled_day) }}</text> 清扫\n            </view>\n            \n            <!-- 清除排班预览 -->\n            <view v-else-if=\"getPreviewState() === 'clear'\" class=\"preview-summary clear\">\n              <text class=\"clear-info\">该区域将不设置固定清扫日程</text>\n            </view>\n            \n            <!-- 部分设置预览 -->\n            <view v-else class=\"preview-summary incomplete\">\n              <view v-if=\"scheduleData.start_date\" class=\"preview-item\">\n                已选择开始日期：<text class=\"highlight-date\">{{ formatFullDate(scheduleData.start_date) }}</text>\n                <text class=\"missing-info\">，请选择清扫日期</text>\n              </view>\n              <view v-else class=\"preview-item\">\n                <text class=\"missing-info\">请选择清扫日期和开始日期</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"popup-footer\">\n          <button class=\"popup-btn cancel\" @click=\"closeSchedulePopup\">取消</button>\n          <button class=\"popup-btn submit\" @click=\"submitSchedule\" :loading=\"saving\">\n            确定设置\n          </button>\n        </view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\nimport { callCloudFunction } from '@/utils/auth.js';\n\nexport default {\n  name: 'PublicSchedule',\n  // 公共区清扫日设置页面\n  data() {\n    return {\n      areaList: [],\n      loading: false,\n      saving: false,\n      currentArea: null,\n      scheduleData: {\n        scheduled_day: null,\n        start_date: ''\n      },\n\n      filterOptions: [\n        { value: 'all', label: '全部区域' },\n        { value: 'scheduled', label: '已排班' },\n        { value: 'unscheduled', label: '未排班' },\n        { value: 'today', label: '今日清扫' }\n      ],\n      weekDayOptions: [\n        { value: null, label: '请选择清扫日期' },\n        { value: 1, label: '每周一' },\n        { value: 2, label: '每周二' },\n        { value: 3, label: '每周三' },\n        { value: 4, label: '每周四' },\n        { value: 5, label: '每周五' },\n        { value: 6, label: '每周六' },\n        { value: 0, label: '每周日' }\n      ],\n\n      filterIndex: 0,\n      scheduleWeekIndex: 0\n    }\n  },\n  \n  computed: {\n    filteredAreas() {\n      const filter = this.filterOptions[this.filterIndex].value;\n      switch (filter) {\n        case 'scheduled':\n                  return this.areaList.filter(area => area.scheduled_day !== null);\n      case 'unscheduled':\n        return this.areaList.filter(area => area.scheduled_day === null);\n      case 'today':\n        const today = new Date().getDay();\n        return this.areaList.filter(area => area.scheduled_day === today);\n        default:\n          return this.areaList;\n      }\n    },\n\n    weekDays() {\n      const today = new Date();\n      const days = [];\n      \n      // 获取本周的周一\n      const startOfWeek = new Date(today);\n      const dayOfWeek = today.getDay();\n      const diffToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;\n      startOfWeek.setDate(today.getDate() + diffToMonday);      \n      const weekNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];      \n      for (let i = 0; i < 7; i++) {\n        const currentDay = new Date(startOfWeek);\n        currentDay.setDate(startOfWeek.getDate() + i);\n        \n        const dayOfWeek = currentDay.getDay();\n        const adjustedDay = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // 调整为周一=0，周日=6\n        \n        days.push({\n          name: weekNames[dayOfWeek],\n          date: currentDay.getDate(),\n          isToday: currentDay.toDateString() === today.toDateString(),\n          areas: this.areaList.filter(area => area.scheduled_day === dayOfWeek)\n        });\n      }\n      \n      return days;\n    }\n  },\n  \n  onLoad() {\n    this.loadData();\n  },\n  \n  methods: {\n    // 获取预览状态\n    getPreviewState() {\n      const hasValidDay = this.scheduleData.scheduled_day !== null && \n                         this.scheduleData.scheduled_day !== undefined && \n                         typeof this.scheduleData.scheduled_day === 'number';\n      const hasStartDate = this.scheduleData.start_date && this.scheduleData.start_date.trim();\n      \n      if (hasValidDay && hasStartDate) {\n        return 'complete';\n      } else {\n        return 'incomplete';\n      }\n    },\n    \n    // 判断区域是否有有效的排班设置\n    hasValidSchedule(area) {\n      return area.scheduled_day !== null && \n             area.scheduled_day !== undefined && \n             typeof area.scheduled_day === 'number' &&\n             area.scheduled_day >= 0 && \n             area.scheduled_day <= 6;\n    },\n\n    // 加载数据\n    async loadData() {\n      try {\n        this.loading = true;\n        \n        // 使用云函数获取公共责任区数据\n        const result = await callCloudFunction('hygiene-area-management', {\n          action: 'getAreaList',\n          data: {\n            type: 'public' // 只获取公共责任区\n          }\n        });\n\n        this.areaList = (result.data.list || [])\n          .filter(area => area.status === 'active')\n          .map(area => ({\n            ...area,\n            next_clean_date: this.calculateNextCleanDate(area)\n          }));\n        \n      } catch (error) {\n        console.error('加载数据失败：', error);\n        uni.showToast({\n          title: '加载失败',\n          icon: 'none'\n        });\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    // 计算下次清扫日期\n    calculateNextCleanDate(area) {\n      if (area.scheduled_day === null || area.scheduled_day === undefined) {\n        return null;\n      }\n      \n      const today = new Date();\n      const targetWeekDay = area.scheduled_day;\n      \n      // 如果有开始日期，则从开始日期计算，否则从今天开始\n      let startDate = area.start_date ? new Date(area.start_date) : new Date(today);\n      \n      // 如果开始日期是未来的日期，则从开始日期开始找\n      if (area.start_date && startDate > today) {\n        // 从开始日期找第一个目标星期几\n        while (startDate.getDay() !== targetWeekDay) {\n          startDate.setDate(startDate.getDate() + 1);\n        }\n        return startDate.toISOString().split('T')[0];\n      }\n      \n      // 从今天开始找下一个目标星期几\n      let nextDate = new Date(today);\n      while (nextDate.getDay() !== targetWeekDay) {\n        nextDate.setDate(nextDate.getDate() + 1);\n      }\n      \n      // 如果今天就是目标星期几，但已经是下午了，则找下周的同一天\n      if (nextDate.getDay() === targetWeekDay && nextDate.toDateString() === today.toDateString()) {\n        if (today.getHours() >= 12) { // 假设12点后不进行清扫\n          nextDate.setDate(nextDate.getDate() + 7);\n        }\n      }      \n      return nextDate.toISOString().split('T')[0];\n    },\n    \n    // 获取下周一的日期\n    getNextMonday() {\n      const today = new Date();\n      const nextMonday = new Date(today);\n      const daysUntilMonday = (8 - today.getDay()) % 7;\n      nextMonday.setDate(today.getDate() + (daysUntilMonday === 0 ? 7 : daysUntilMonday));\n      return nextMonday.toISOString().split('T')[0];\n    },\n    \n    // 筛选变更\n    onFilterChange(e) {\n      this.filterIndex = e.detail.value;\n    },\n    \n    // 设置排班\n    setSchedule(area) {\n      this.currentArea = area;\n      \n      this.scheduleData = {\n        scheduled_day: area.scheduled_day,\n        start_date: area.start_date || this.getNextMonday()\n      };\n      \n      // 设置星期选择器的索引\n      const foundIndex = this.weekDayOptions.findIndex(option => option.value === area.scheduled_day);\n      this.scheduleWeekIndex = foundIndex >= 0 ? foundIndex : 0; // 如果找不到匹配项，默认选择\"请选择清扫日期\"\n      this.$refs.schedulePopup.open();\n    },\n    \n    // 清除排班\n    clearSchedule(area) {\n      uni.showModal({\n        title: '确认清除',\n        content: `确定要清除\"${area.name}\"的排班设置吗？`,\n        confirmText: '确定',\n        confirmColor: '#FF3B30',\n        success: (res) => {\n          if (res.confirm) {\n            this.performClearSchedule(area);\n          }\n        }\n      });\n    },\n    \n    // 执行清除排班\n    async performClearSchedule(area) {\n      try {\n        // 使用云函数清除排班\n        await callCloudFunction('hygiene-area-management', {\n          action: 'updateArea',\n          data: {\n            id: area._id || area.id,\n            scheduled_day: null,\n            start_date: null\n          }\n        });\n\n        // 更新本地数据\n        const index = this.areaList.findIndex(item => (item._id || item.id) === (area._id || area.id));\n        if (index > -1) {\n          const updatedArea = {\n            ...this.areaList[index],\n            scheduled_day: null,\n            start_date: null,\n            next_clean_date: null,\n            updated_at: new Date().toISOString()\n          };\n          \n          // 使用 Vue.set 来确保响应式更新\n          this.$set(this.areaList, index, updatedArea);\n        }\n        \n        uni.showToast({\n          title: '排班已清除',\n          icon: 'success'\n        });\n        \n      } catch (error) {\n        console.error('清除排班失败：', error);\n        uni.showToast({\n          title: error.message || '操作失败',\n          icon: 'none'\n        });\n      }\n    },\n    \n    // 排班星期变更\n    onScheduleWeekChange(e) {\n      this.scheduleWeekIndex = parseInt(e.detail.value);\n      const selectedOption = this.weekDayOptions[this.scheduleWeekIndex];\n      if (selectedOption) {\n        this.scheduleData.scheduled_day = selectedOption.value;\n      } else {\n        // 如果选择的索引无效，默认选择第一个选项（请选择清扫日期）\n        this.scheduleWeekIndex = 0;\n        this.scheduleData.scheduled_day = null;\n      }\n    },\n    \n    // 开始日期变更\n    onStartDateChange(e) {\n      this.scheduleData.start_date = e.detail.value;\n    },\n    \n    // 提交排班\n    async submitSchedule() {\n      // 验证清扫日期\n      if (this.scheduleData.scheduled_day === null || this.scheduleData.scheduled_day === undefined) {\n        uni.showToast({\n          title: '请选择清扫日期',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 验证开始日期\n      if (!this.scheduleData.start_date) {\n        uni.showToast({\n          title: '请选择开始日期',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      try {\n        this.saving = true;\n        \n        // 使用云函数设置排班\n        await callCloudFunction('hygiene-area-management', {\n          action: 'updateArea',\n          data: {\n            id: this.currentArea._id || this.currentArea.id,\n            scheduled_day: this.scheduleData.scheduled_day,\n            start_date: this.scheduleData.start_date\n          }\n        });\n\n        // 更新本地数据\n        const index = this.areaList.findIndex(item => (item._id || item.id) === (this.currentArea._id || this.currentArea.id));\n        if (index > -1) {\n          const updatedArea = {\n            ...this.areaList[index],\n            scheduled_day: this.scheduleData.scheduled_day,\n            start_date: this.scheduleData.start_date,\n            next_clean_date: this.calculateNextCleanDate({\n              ...this.areaList[index],\n              scheduled_day: this.scheduleData.scheduled_day,\n              start_date: this.scheduleData.start_date\n            }),\n            updated_at: new Date().toISOString()\n          };\n          \n          // 使用 Vue.set 或者替换整个数组来确保响应式更新\n          this.$set(this.areaList, index, updatedArea);\n        }\n        \n        // 显示成功消息\n        const successMessage = '排班设置成功';\n        \n        uni.showToast({\n          title: successMessage,\n          icon: 'success'\n        });\n        \n        this.closeSchedulePopup();\n        \n      } catch (error) {\n        console.error('排班设置失败：', error);\n        uni.showToast({\n          title: error.message || '设置失败',\n          icon: 'none'\n        });\n      } finally {\n        this.saving = false;\n      }\n    },\n    \n    // 关闭排班弹窗\n    closeSchedulePopup() {\n      this.$refs.schedulePopup.close();\n      \n      // 使用更长的延迟来确保弹窗关闭动画完全结束后再重置状态\n      setTimeout(() => {\n        this.resetPopupState();\n      }, 350); // 增加延迟时间以避免动画闪烁\n    },\n\n    // 重置弹窗状态\n    resetPopupState() {\n      this.currentArea = null;\n      this.scheduleData = {\n        scheduled_day: null,\n        start_date: ''\n      };\n      this.scheduleWeekIndex = 0; // 默认选择第一个选项（请选择清扫日期）\n    },\n    \n    // 获取星期文本\n    getWeekDayText(weekDay) {\n      if (weekDay === null || weekDay === undefined || typeof weekDay !== 'number') {\n        return '未知';\n      }\n      \n      const weekDayMap = {\n        0: '周日',\n        1: '周一',\n        2: '周二',\n        3: '周三',\n        4: '周四',\n        5: '周五',\n        6: '周六'\n      };\n      return weekDayMap[weekDay] || '未知';\n    },\n    \n    // 判断是否是今天\n    isToday(dateString) {\n      if (!dateString) return false;\n      const today = new Date().toISOString().split('T')[0];\n      const date = typeof dateString === 'string' ? dateString.split('T')[0] : dateString.toISOString().split('T')[0];\n      return today === date;\n    },\n    \n    // 格式化日期\n    formatDate(dateString) {\n      if (!dateString) return '';\n      \n      try {\n        const date = new Date(dateString);\n        \n        // 检查日期是否有效\n        if (isNaN(date.getTime())) {\n          return '';\n        }\n        \n        const today = new Date();\n        const tomorrow = new Date(today);\n        tomorrow.setDate(today.getDate() + 1);      \n        const dateStr = date.toISOString().split('T')[0];\n        const todayStr = today.toISOString().split('T')[0];\n        const tomorrowStr = tomorrow.toISOString().split('T')[0];\n        \n        if (dateStr === todayStr) {\n          return '今天';\n        } else if (dateStr === tomorrowStr) {\n          return '明天';\n        } else {\n          return `${date.getMonth() + 1}-${String(date.getDate()).padStart(2, '0')}`;\n        }\n      } catch (error) {\n        console.error('日期格式化错误：', error);\n        return '';\n      }\n    },\n    \n    // 格式化完整日期（用于设置说明）\n    formatFullDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;\n    },\n    \n    // 获取空状态文本\n    getEmptyText() {\n      const filter = this.filterOptions[this.filterIndex].value;\n      switch (filter) {\n        case 'scheduled':\n          return '暂无已排班区域';\n        case 'unscheduled':\n          return '暂无未排班区域';\n        case 'today':\n          return '今日无清扫安排';\n        default:\n          return '暂无公共责任区';\n      }\n    },\n    \n    // 获取空状态描述\n    getEmptyDesc() {\n      const filter = this.filterOptions[this.filterIndex].value;\n      switch (filter) {\n        case 'scheduled':\n          return '可以切换到\"未排班\"查看需要设置的区域';\n        case 'unscheduled':\n          return '所有公共责任区都已完成排班设置';\n        case 'today':\n          return '可以查看其他日期的清扫安排';\n        default:\n          return '请先在公共责任区管理中创建区域';\n      }\n    },\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n  min-height: 100vh;\n  background: #F8F9FA;\n  padding-bottom: 40rpx;\n}\n\n.header {\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n  padding: 60rpx 32rpx 40rpx;\n  color: white;\n}\n\n.header-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  margin-bottom: 8rpx;\n}\n\n.header-subtitle {\n  font-size: 24rpx;\n  opacity: 0.8;\n}\n\n.week-overview {\n  background: white;\n  margin: 24rpx 32rpx;\n  padding: 32rpx;\n  border-radius: 16rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.overview-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 24rpx;\n}\n\n.week-scroll {\n  width: 100%;\n}\n\n/* 隐藏水平滚动条 */\n.week-scroll::-webkit-scrollbar {\n  display: none;\n  width: 0;\n  height: 0;\n  background: transparent;\n}\n\n.week-scroll {\n  scrollbar-width: none; /* Firefox */\n  -ms-overflow-style: none; /* IE and Edge */\n}\n\n.week-grid {\n  display: flex;\n  gap: 12rpx;\n  padding: 0 4rpx;\n}\n\n.day-item {\n  flex-shrink: 0;\n  width: 120rpx;\n  text-align: center;\n  padding: 16rpx 12rpx;\n  border-radius: 12rpx;\n  background: #F8F9FA;\n  transition: all 0.3s ease;\n}\n\n.day-item.today {\n  background: rgba(0, 122, 255, 0.1);\n  border: 2rpx solid #007AFF;\n}\n\n.day-item.has-schedule {\n  background: rgba(52, 199, 89, 0.1);\n}\n\n.day-item.today.has-schedule {\n  background: rgba(0, 122, 255, 0.15);\n}\n\n.day-header {\n  margin-bottom: 8rpx;\n}\n\n.day-name {\n  font-size: 22rpx;\n  color: #8E8E93;\n  display: block;\n}\n\n.day-date {\n  font-size: 26rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  display: block;\n  margin-top: 4rpx;\n}\n\n.day-content {\n  min-height: 40rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.area-count {\n  font-size: 20rpx;\n  color: #34C759;\n  font-weight: 500;\n}\n\n.no-schedule {\n  font-size: 20rpx;\n  color: #C7C7CC;\n}\n\n.filter-bar {\n  padding: 0 32rpx 24rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 16rpx;\n}\n\n.filter-picker {\n  flex: 1;\n  min-width: 0;\n}\n\n.filter-value {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 56rpx;\n  background: white;\n  border-radius: 12rpx;\n  padding: 0 16rpx;\n  font-size: 24rpx;\n  color: #1C1C1E;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.area-list {\n  padding: 0 32rpx;\n}\n\n.area-item {\n  background: white;\n  border-radius: 16rpx;\n  margin-bottom: 16rpx;\n  padding: 24rpx;\n  display: flex;\n  align-items: flex-start;\n  gap: 16rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.area-main {\n  flex: 1;\n}\n\n.area-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12rpx;\n}\n\n.area-name {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n}\n\n.schedule-status {\n  font-size: 22rpx;\n  padding: 6rpx 12rpx;\n  border-radius: 12rpx;\n  font-weight: 500;\n}\n\n.schedule-status.scheduled {\n  background: rgba(52, 199, 89, 0.1);\n  color: #34C759;\n}\n\n.schedule-status.unscheduled {\n  background: rgba(255, 149, 0, 0.1);\n  color: #FF9500;\n}\n\n.area-info {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n  margin-bottom: 12rpx;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  gap: 6rpx;\n  font-size: 24rpx;\n  color: #8E8E93;\n  flex-shrink: 0;\n  min-width: fit-content;\n}\n\n.info-item .today {\n  color: #007AFF;\n  font-weight: 600;\n}\n\n.area-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n}\n\n.action-btn {\n  height: 56rpx;\n  padding: 0 16rpx;\n  border-radius: 8rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6rpx;\n  font-size: 22rpx;\n  border: none;\n  transition: all 0.3s ease;\n  min-width: 100rpx;\n  white-space: nowrap;\n}\n\n.action-btn.set {\n  background: #007AFF;\n  color: white;\n}\n\n.action-btn.clear {\n  background: #8E8E93;\n  color: white;\n}\n\n.action-btn:active {\n  transform: scale(0.95);\n}\n\n.schedule-popup, .quick-set-popup {\n  width: 92vw;\n  max-width: 650rpx;\n  background: white;\n  border-radius: 16rpx;\n  overflow: hidden;\n  max-height: 80vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 32rpx;\n  border-bottom: 1rpx solid #F2F2F7;\n}\n\n.popup-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-left: 5rpx;\n}\n\n.close-btn {\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 12rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #F2F2F7;\n  border: none;\n  margin-right: 5rpx;\n}\n\n.popup-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 32rpx;\n  display: flex;\n  flex-direction: column;\n  gap: 24rpx;\n}\n\n/* #ifdef MP-WEIXIN */\n/* 微信小程序隐藏滚动条 */\n.popup-content::-webkit-scrollbar {\n  display: none;\n  width: 0;\n  height: 0;\n  background: transparent;\n}\n\n.popup-content {\n  scrollbar-width: none; /* Firefox */\n  -ms-overflow-style: none; /* IE and Edge */\n}\n/* #endif */\n\n/* #ifndef MP-WEIXIN */\n/* 非微信小程序环境下也隐藏滚动条 */\n.popup-content::-webkit-scrollbar {\n  display: none;\n  width: 0;\n  height: 0;\n  background: transparent;\n}\n\n.popup-content {\n  scrollbar-width: none; /* Firefox */\n  -ms-overflow-style: none; /* IE and Edge */\n}\n/* #endif */\n\n/* 区域信息卡片 */\n.area-info-card {\n  background: linear-gradient(180deg, #FFFFFF 0%, #FAFBFC 100%);\n  border: 1rpx solid #E5E5EA;\n  border-radius: 16rpx;\n  padding: 26rpx;\n  margin-bottom: 32rpx;\n  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);\n}\n\n.area-info-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12rpx;\n}\n\n.area-info-name {\n  font-size: 34rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  letter-spacing: 0.5rpx;\n}\n\n.area-status-badge {\n  background: rgba(0, 122, 255, 0.1);\n  color: #007AFF;\n  font-size: 20rpx;\n  padding: 6rpx 12rpx;\n  border-radius: 12rpx;\n  font-weight: 500;\n}\n\n.area-info-location {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n/* 设置表单 */\n.setting-form {\n  display: flex;\n  flex-direction: column;\n  gap: 24rpx;\n  margin-bottom: 32rpx;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.form-label {\n  font-size: 28rpx;\n  color: #1C1C1E;\n  margin-bottom: 12rpx;\n  font-weight: 600;\n  position: relative;\n  padding-left: 16rpx;\n}\n\n.form-label::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 4rpx;\n  height: 18rpx;\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n  border-radius: 2rpx;\n}\n\n.form-picker {\n  width: 100%;\n}\n\n.form-input {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n  height: 88rpx;\n  background: #FFFFFF;\n  border: 2rpx solid #E5E5EA;\n  border-radius: 10rpx;\n  padding: 0 20rpx;\n  font-size: 28rpx;\n  color: #1C1C1E;\n  box-sizing: border-box;\n  transition: all 0.3s ease;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);\n  position: relative;\n}\n\n.form-input:active {\n  border-color: #007AFF;\n  background: #F8F9FA;\n  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.15);\n  transform: translateY(-2rpx);\n}\n\n.dropdown-icon, .input-icon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 32rpx;\n  height: 32rpx;\n  border-radius: 6rpx;\n  background: rgba(0, 122, 255, 0.08);\n  transition: all 0.3s ease;\n}\n\n\n\n/* 设置预览 */\n.setting-preview {\n  background: rgba(0, 122, 255, 0.04);\n  border: 2rpx solid rgba(0, 122, 255, 0.12);\n  border-radius: 16rpx;\n  padding: 24rpx;\n}\n\n.preview-header {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  margin-bottom: 16rpx;\n}\n\n.preview-header text {\n  font-size: 26rpx;\n  font-weight: 600;\n  color: #007AFF;\n}\n\n.preview-summary {\n  font-size: 26rpx;\n  color: #1C1C1E;\n  line-height: 1.6;\n}\n\n.preview-summary .highlight-date {\n  color: #007AFF;\n  font-weight: 600;\n  background: rgba(0, 122, 255, 0.08);\n  padding: 2rpx 8rpx;\n  border-radius: 6rpx;\n}\n\n.preview-summary .highlight-day {\n  color: #34C759;\n  font-weight: 600;\n  background: rgba(52, 199, 89, 0.08);\n  padding: 2rpx 8rpx;\n  border-radius: 6rpx;\n}\n\n.preview-summary.complete {\n  color: #1C1C1E;\n}\n\n.preview-summary.incomplete {\n  color: #8E8E93;\n}\n\n.preview-item {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  gap: 4rpx;\n  line-height: 1.6;\n}\n\n.missing-info {\n  color: #FF9500;\n  font-weight: 500;\n}\n\n.preview-summary.clear {\n  color: #FF3B30;\n  font-weight: 600;\n  background: rgba(255, 59, 48, 0.08);\n  padding: 2rpx 8rpx;\n  border-radius: 6rpx;\n}\n\n.clear-info {\n  color: #FF3B30;\n  font-weight: 600;\n}\n\n\n.popup-footer {\n  display: flex;\n  gap: 16rpx;\n  padding: 32rpx;\n  border-top: 1rpx solid #F2F2F7;\n}\n\n.popup-btn {\n  flex: 1;\n  height: 76rpx;\n  border-radius: 12rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  border: none;\n  transition: all 0.3s ease;\n}\n\n.popup-btn.cancel {\n  background: #F2F2F7;\n  color: #8E8E93;\n}\n\n.popup-btn.submit {\n  background: #007AFF;\n  color: white;\n}\n\n.popup-btn:active {\n  transform: scale(0.95);\n}\n\n.list-loading {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 40rpx 0;\n  background: #F8F9FA;\n}\n\n.loading-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 12rpx;\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./public-schedule.vue?vue&type=style&index=0&id=4ede77af&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./public-schedule.vue?vue&type=style&index=0&id=4ede77af&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775844079\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}