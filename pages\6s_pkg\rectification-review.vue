<template>
  <view class="page-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="header-content">
        <view class="header-title">整改复查</view>
        <view class="header-subtitle">{{ loading ? '加载中...' : taskInfo.areaName }}</view>
      </view>
      <view v-if="!loading" class="status-badge-enhanced" :class="['status-' + taskInfo.status]">
        <uni-icons 
          :type="taskInfo.status === 'pending_review' ? 'info' : taskInfo.status === 'approved' ? 'checkmarkempty' : taskInfo.status === 'rejected' ? 'close' : taskInfo.status === 'completed' ? 'checkmarkempty' : 'info'"
          size="16" 
          color="rgba(255, 255, 255, 0.95)"
        ></uni-icons>
        <text>{{ getStatusText(taskInfo.status) }}</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="page-loading">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载整改详情...</text>
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="loadError" class="page-error">
      <p-empty-state 
        type="error"
        title="加载失败"
        description="网络异常，请检查网络连接"
        :show-button="true"
        button-text="重新加载"
        @button-click="retryLoad"
      ></p-empty-state>
    </view>

    <!-- 正常内容 -->
    <view v-else-if="dataLoaded">
      <!-- 原始问题信息 -->
      <view class="card">
        <view class="card-header">
          <view class="card-title">原始问题</view>
          <view class="card-subtitle">{{ formatDateTime(taskInfo.issueFoundDate) }}</view>
        </view>
        <view class="card-body">
          <!-- 问题照片 -->
          <view v-if="taskInfo.issuePhotos && taskInfo.issuePhotos.length > 0" class="photo-section">
            <view class="section-title">问题照片</view>
            <view class="photo-grid">
              <view 
                v-for="(photo, index) in taskInfo.issuePhotos" 
                :key="index" 
                class="photo-item"
                @click="previewIssuePhoto(index)"
              >
                <image :src="photo.url || photo" mode="aspectFill" @error="onPhotoError(index, 'issuePhotos')"></image>
                <view v-if="photo.loadError" class="photo-error" @click.stop="retryPhotoLoad(index, 'issuePhotos')">
                  <uni-icons type="reload" size="24" color="#8E8E93"></uni-icons>
                  <text class="error-text">点击重试</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 问题说明 -->
          <view class="description-section">
            <view class="section-title">问题说明</view>
            <view class="description-content">
              {{ taskInfo.issueDescription }}
            </view>
            <view class="issue-meta">
              <text class="meta-label">检查员：</text>
              <text class="meta-value">{{ taskInfo.inspector }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 员工整改记录 -->
      <view class="card">
        <view class="card-header">
          <view class="card-title">员工整改记录</view>
          <view class="card-subtitle">{{ formatDateTime(taskInfo.rectificationSubmitTime) }}</view>
        </view>
        <view class="card-body">
          <!-- 整改照片 -->
          <view class="photo-section">
            <view class="section-title">整改照片</view>
            <!-- 页面还在加载时显示整体加载 -->
            <view v-if="!dataLoaded" class="photos-loading-placeholder">
              <view class="loading-center">
                <view class="loading-spinner-small"></view>
                <view class="photos-loading-text">加载照片中...</view>
              </view>
            </view>
            <!-- 页面已加载，但照片列表为空 -->
            <view v-else-if="taskInfo.rectificationPhotos.length === 0" class="no-photos">
              <p-empty-state 
                type="no-image"
                title="暂无照片"
                description="员工未上传整改照片"
              ></p-empty-state>
            </view>
            <!-- 显示照片 -->
            <view v-else class="photo-grid">
              <view 
                v-for="(photo, index) in taskInfo.rectificationPhotos" 
                :key="index" 
                class="photo-item"
                @click="previewPhoto(index)"
              >
                <image :src="photo.url || photo" mode="aspectFill" @error="onPhotoError(index)"></image>
                <view v-if="photo.loadError" class="photo-error" @click.stop="retryPhotoLoad(index)">
                  <uni-icons type="reload" size="24" color="#8E8E93"></uni-icons>
                  <text class="error-text">点击重试</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 整改说明 -->
          <view class="description-section">
            <view class="section-title">整改说明</view>
            <view class="description-content">
              {{ taskInfo.rectificationDescription }}
            </view>
            <view class="issue-meta">
              <text class="meta-label">负责人：</text>
              <text class="meta-value">{{ taskInfo.employeeName }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 审核结果 -->
      <view class="card">
        <view class="card-header">
          <view class="card-title">复查结果</view>
          <view class="card-subtitle">请对整改效果进行评价</view>
        </view>
        <view class="card-body">
          <!-- 审核选择 -->
          <view class="review-options">
            <view 
              class="review-option"
              :class="{ active: reviewForm.result === 'approved' }"
              @click="selectReviewResult('approved')"
            >
              <view class="option-icon approved">
                <uni-icons type="checkmarkempty" size="20" color="white"></uni-icons>
              </view>
              <view class="option-content">
                <view class="option-title">复查通过</view>
                <view class="option-desc">整改效果良好，问题已解决</view>
              </view>
            </view>

            <view 
              class="review-option"
              :class="{ active: reviewForm.result === 'rejected' }"
              @click="selectReviewResult('rejected')"
            >
              <view class="option-icon rejected">
                <uni-icons type="close" size="20" color="white"></uni-icons>
              </view>
              <view class="option-content">
                <view class="option-title">需要重新整改</view>
                <view class="option-desc">整改不到位，需要继续处理</view>
              </view>
            </view>
          </view>

          <!-- 审核通过时的评分 -->
          <view v-if="reviewForm.result === 'approved'" class="rating-section-enhanced">
            <view class="section-title">整改后评分</view>
            <view class="rating-container-enhanced">
              <!-- 大数字显示 -->
              <view class="rating-display-large">
                <view class="rating-number-container">
                  <text class="rating-number">{{ reviewForm.rating || 0 }}</text>
                  <text class="rating-unit">/5</text>
                </view>
                <view class="rating-desc">{{ getRatingDescription(reviewForm.rating) }}</view>
              </view>
              
              <!-- 星星评分（使用官方uni-rate组件） -->
              <view class="star-rating">
                <uni-rate 
                  :value="reviewForm.rating" 
                  @change="onStarRateChange"
                  allow-half
                  :size="24"
                  active-color="#FFD700"
                  inactive-color="#E5E5EA"
                  :touchable="true"
                  :margin="8"
                />
              </view>
              
              <!-- 自定义滑动条评分 -->
              <view class="custom-slider-rating">
                <view class="custom-slider-container" 
                      @touchstart="onSliderTouchStart" 
                      @touchmove="onSliderTouchMove" 
                      @touchend="onSliderTouchEnd"
                      @mousedown="onSliderMouseDown"
                      @mousemove="onSliderMouseMove"
                      @mouseup="onSliderMouseUp"
                      @mouseleave="onSliderMouseUp">
                  <!-- 滑动轨道 -->
                  <view class="slider-track">
                    <view class="slider-track-active" :style="{ width: (reviewForm.rating / 5 * 100) + '%' }"></view>
                  </view>
                  <!-- 滑块 -->
                  <view class="slider-thumb" :style="{ left: (reviewForm.rating / 5 * 100) + '%' }"></view>
                  <!-- 刻度点 -->
                  <view class="slider-marks">
                    <view 
                      v-for="(mark, index) in 6" 
                      :key="index" 
                      class="slider-mark"
                      :class="{ 'slider-mark-active': index <= reviewForm.rating }"
                      :style="{ left: (index / 5 * 100) + '%' }"
                      @click="setRatingByMark(index)"
                    ></view>
                  </view>
                </view>
                <!-- 标签 - 放在滑动条容器外面 -->
                <view class="slider-labels-external">
                  <text 
                    v-for="(label, labelIndex) in ['0','1','2','3','4','5']" 
                    :key="labelIndex"
                    class="slider-label-external"
                    :class="{ 'slider-label-active': labelIndex <= reviewForm.rating }"
                    :style="{ left: (labelIndex / 5 * 100) + '%' }"
                  >{{ label }}</text>
                </view>
              </view>
              
              <view class="rating-tips">
                <text>请根据整改后的实际效果进行客观评分（支持半星评分）</text>
              </view>
            </view>
          </view>

          <!-- 审核意见 -->
          <view class="review-comment-section">
            <view class="section-title">
              复查意见
              <text class="required-mark" v-if="reviewForm.result === 'rejected'">*</text>
            </view>
            <view class="textarea-container-enhanced">
              <textarea 
                v-model="reviewForm.comment"
                :placeholder="commentPlaceholder"
                maxlength="200"
                class="comment-textarea-enhanced"
                @input="handleCommentInput"
              ></textarea>
              <!-- 字数统计 - 悬浮在右下角 -->
              <view class="char-count-overlay">
                {{ commentLength }}/200
              </view>
            </view>
          </view>

          <!-- 审核照片（可选） -->
          <view class="review-photo-section">
            <view class="section-header">
              <view class="section-title">
                复查照片
                <text v-if="reviewForm.result === 'rejected'">（必填）</text>
                <text v-else>（可选）</text>
                <text class="required-mark" v-if="reviewForm.result === 'rejected'">*</text>
              </view>
              <view class="auto-upload-toggle" @click="toggleAutoUpload">
                <view class="toggle-label">自动上传</view>
                <view class="toggle-switch" :class="{ active: autoUpload }">
                  <view class="toggle-circle"></view>
                </view>
              </view>
            </view>
            <view class="photo-grid">
              <view 
                v-for="(photo, index) in reviewForm.photos" 
                :key="index" 
                class="photo-item"
              >
                <image :src="getPhotoDisplayUrl(photo)" mode="aspectFill" @click="previewReviewPhoto(index)" @error="onPhotoError(index)"></image>
                
                <!-- 上传状态指示器 -->
                <view v-if="photo.uploading" class="photo-uploading">
                  <view class="upload-spinner"></view>
                </view>
                <view v-else-if="photo.uploaded" class="photo-uploaded">
                  <uni-icons type="checkmarkempty" size="16" color="white"></uni-icons>
                </view>
                
                <!-- 删除按钮 - 垂直居中 -->
                <view class="photo-delete" @click.stop="deleteReviewPhoto(index)">
                  <uni-icons type="close" size="18" color="white"></uni-icons>
                </view>
              </view>
              <view 
                v-if="reviewForm.photos.length < 6" 
                class="photo-add"
                @click="addReviewPhoto"
              >
                <uni-icons type="camera" size="28" color="#8E8E93"></uni-icons>
                <text>添加照片</text>
              </view>
            </view>
            <view class="photo-tip">
              <text v-if="reviewForm.result === 'rejected'">需要重新整改时必须上传照片作为证据，最多6张</text>
              <text v-else>最多可上传6张复查照片</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="button-container">
        <button 
          class="primary-button" 
          @click="submitReview" 
          :disabled="!canSubmit"
          :class="{ loading: isSubmitting }"
        >
          <view v-if="isSubmitting" class="button-loading">
            <view class="loading-spinner"></view>
            <text>提交中...</text>
          </view>
          <text v-else>提交复查结果</text>
        </button>
      </view>
    </view>

    <!-- 底部安全间距 -->
    <view class="bottom-safe-area"></view>
  </view>
</template>

<script>
import { callCloudFunction } from '@/utils/auth.js';
import uploadUtils from '@/utils/upload-utils.js';

export default {
  name: 'RectificationReview',
  data() {
    return {
      taskId: '',
      loading: false,
      dataLoaded: false,
      loadError: false,
      isSubmitting: false,
      taskInfo: {
        id: '',
        areaName: '',
        areaType: '',
        status: 'pending_review',
        issueDescription: '',
        issueFoundDate: '',
        inspector: '',
        issuePhotos: [],
        rectificationDescription: '',
        rectificationPhotos: [],
        rectificationSubmitTime: '',
        employeeName: ''
      },
      reviewForm: {
        result: '', // 'approved' | 'rejected'
        comment: '',
        photos: [],
        rating: 0 // 整改后评分 (1-5)
      },
      autoUpload: true, // 自动上传开关（默认开启）
      sliderTouching: false // 滑动条触摸状态
    }
  },
  computed: {
    // 计算评论长度，确保响应式更新
    commentLength() {
      return this.reviewForm.comment ? this.reviewForm.comment.length : 0;
    },
    
    canSubmit() {
      if (!this.reviewForm.result) return false;
      if (this.reviewForm.result === 'rejected' && !this.reviewForm.comment.trim()) return false;
      if (this.reviewForm.result === 'rejected' && this.reviewForm.photos.length === 0) return false;
      if (this.reviewForm.result === 'approved' && !this.reviewForm.rating) return false;
      
      // 检查是否有照片正在上传
      const hasUploadingPhotos = this.reviewForm.photos.some(photo => 
        typeof photo === 'object' && photo.uploading
      );
      if (hasUploadingPhotos) return false;
      
      return !this.isSubmitting;
    },
    commentPlaceholder() {
      if (this.reviewForm.result === 'approved') {
        return '请对整改效果进行评价（可选）';
      } else if (this.reviewForm.result === 'rejected') {
        return '请说明需要重新整改的原因';
      }
      return '请输入复查意见';
    }
  },
  onLoad(options) {
    if (options.taskId) {
      this.taskId = options.taskId;
      this.loadTaskInfo();
    } else {
      this.loadError = true;
      uni.showModal({
        title: '参数错误',
        content: '缺少整改任务ID参数',
        showCancel: true,
        cancelText: '返回',
        confirmText: '确定',
        success: (res) => {
          uni.navigateBack();
        }
      });
    }
  },
  methods: {
    // 处理评论输入，确保字符限制和响应式更新
    handleCommentInput(e) {
      let value = e.detail.value || '';
      
      // 强制限制字符数量
      if (value.length > 200) {
        value = value.substring(0, 200);
        // 如果超出限制，显示提示
        uni.showToast({
          title: '复查意见不能超过200个字符',
          icon: 'none',
          duration: 1500
        });
      }
      
      // 更新数据
      this.reviewForm.comment = value;
      
      // 强制触发视图更新
      this.$forceUpdate();
    },
    
    // 加载整改任务信息
    async loadTaskInfo() {
      if (!this.taskId) {
        this.loadError = true;
        uni.showModal({
          title: '参数错误',
          content: '整改任务ID不能为空',
          showCancel: false,
          confirmText: '返回',
          success: () => {
            uni.navigateBack();
          }
        });
        return;
      }

      this.loading = true;
      this.dataLoaded = false;
      this.loadError = false;
      
      try {
        const result = await callCloudFunction('hygiene-rectification', {
          action: 'getRectificationDetail',
          data: { id: this.taskId }
        });

        if (result && result.success && result.data) {
          const task = result.data;
          
          this.taskInfo = {
            id: task._id || task.id,
            areaName: task.area_name || '未知责任区',
            areaType: task.area_type || 'fixed',
            status: task.status || 'pending_review',
            issueDescription: task.issue_description || task.description || '无问题描述',
            issueFoundDate: task.created_at || task.issue_found_date,
            inspector: task.inspector_name || task.assigned_by_name || task.created_by_name || (task.issue && task.issue.inspector_name) || '未知检查员',
            issuePhotos: this.processPhotos(task.photos || []),
            rectificationDescription: task.rectification_description || task.completion_description || '',
            rectificationPhotos: this.processPhotos(task.completion_photos || []),
            rectificationSubmitTime: task.submitted_at || task.updated_at,
            employeeName: task.assigned_to_name || task.assignee_name || '未分配',
            assignee_id: task.assignee_id
          };
        } else {
          throw new Error(result?.message || '获取整改任务详情失败');
        }
        
        this.dataLoaded = true;
        
      } catch (error) {
        console.error('加载整改任务信息失败:', error);
        this.loadError = true;
        
        uni.showModal({
          title: '加载失败',
          content: error.message || '网络异常，请稍后重试',
          showCancel: true,
          cancelText: '返回',
          confirmText: '重试',
          success: (res) => {
            if (res.confirm) {
              this.loadTaskInfo();
            } else {
              uni.navigateBack();
            }
          }
        });
      } finally {
        this.loading = false;
      }
    },

    // 重新加载数据
    retryLoad() {
      this.loadTaskInfo();
    },

    // 处理照片数据
    processPhotos(photos) {
      if (!photos || !Array.isArray(photos)) return [];
      
      return photos.map(photo => {
        if (typeof photo === 'string') {
          return photo;
        } else if (photo && photo.url) {
          return photo.url;
        }
        return '';
      }).filter(url => url);
    },

    // 选择审核结果
    selectReviewResult(result) {
      this.reviewForm.result = result;
      if (result === 'approved') {
        // 通过时清空之前可能的拒绝原因
        if (this.reviewForm.comment.includes('需要') || this.reviewForm.comment.includes('不够')) {
          this.reviewForm.comment = '';
        }
        // 设置默认评分
        if (!this.reviewForm.rating) {
          this.reviewForm.rating = 3; // 默认3分
        }
      } else {
        // 拒绝时清空评分
        this.reviewForm.rating = 0;
      }
    },

    // 设置评分
    setRating(rating) {
      this.reviewForm.rating = rating;
    },

    // 获取评分描述
    getRatingDesc(rating) {
      const descriptions = {
        1: '需要改进',
        2: '基本合格', 
        3: '良好',
        4: '优秀',
        5: '卓越'
      };
      return descriptions[rating] || '请评分';
    },

    // 获取评分描述（更详细版本，与inspection-detail.vue保持一致）
    getRatingDescription(rating) {
      if (rating === 0) return '请评分';
      if (rating <= 1) return '较差';
      if (rating <= 2) return '一般';
      if (rating <= 3) return '良好';
      if (rating < 5) return '优秀';  // 4-4.5分都是优秀
      if (rating === 5) return '完美';
      return '';
    },

    // 星星评分变化
    onStarRateChange(e) {
      this.reviewForm.rating = e.value;
    },

    // 点击刻度设置评分
    setRatingByMark(rating) {
      this.reviewForm.rating = rating;
    },

    // 自定义滑动条触摸开始
    onSliderTouchStart(e) {
      this.sliderTouching = true;
      this.updateRatingFromTouch(e);
    },

    // 自定义滑动条触摸移动
    onSliderTouchMove(e) {
      if (this.sliderTouching) {
        this.updateRatingFromTouch(e);
      }
    },

    // 自定义滑动条触摸结束
    onSliderTouchEnd(e) {
      this.sliderTouching = false;
    },

    // 鼠标事件处理（H5端支持）
    onSliderMouseDown(e) {
      this.sliderTouching = true;
      this.updateRatingFromMouse(e);
    },

    onSliderMouseMove(e) {
      if (this.sliderTouching) {
        this.updateRatingFromMouse(e);
      }
    },

    onSliderMouseUp(e) {
      this.sliderTouching = false;
    },

    // 根据触摸位置更新评分
    updateRatingFromTouch(e) {
      const touch = e.touches[0] || e.changedTouches[0];
      // 获取滑动条容器的位置信息
      uni.createSelectorQuery().in(this).select('.custom-slider-container').boundingClientRect((rect) => {
        if (rect) {
          const x = touch.clientX - rect.left;
          const percentage = Math.max(0, Math.min(1, x / rect.width));
          const rating = Math.round(percentage * 5 * 2) / 2; // 精确到0.5
          this.reviewForm.rating = Math.max(0, Math.min(5, rating));
        }
      }).exec();
    },

    // 根据鼠标位置更新评分（H5端）
    updateRatingFromMouse(e) {
      // 获取滑动条容器的位置信息
      uni.createSelectorQuery().in(this).select('.custom-slider-container').boundingClientRect((rect) => {
        if (rect) {
          const x = e.clientX - rect.left;
          const percentage = Math.max(0, Math.min(1, x / rect.width));
          const rating = Math.round(percentage * 5 * 2) / 2; // 精确到0.5
          this.reviewForm.rating = Math.max(0, Math.min(5, rating));
        }
      }).exec();
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'pending_review': '待复查',
        'approved': '复查通过',
        'rejected': '整改不达标',
        'completed': '已完成'
      };
      return statusMap[status] || status;
    },

    // 格式化日期时间
    formatDateTime(dateString) {
      if (!dateString) return '--';
      
      try {
        let date;
        if (typeof dateString === 'string') {
          if (dateString.includes('T') || dateString.includes('Z')) {
            // ISO 8601 格式
            date = new Date(dateString);
          } else {
            // 传统格式
            date = new Date(dateString.replace(/-/g, '/'));
          }
        } else {
          date = new Date(dateString);
        }
        
        if (isNaN(date.getTime())) {
          return '--';
        }
        
        return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      } catch (error) {
        return '--';
      }
    },

    // 预览整改照片
    previewPhoto(index) {
      const urls = this.taskInfo.rectificationPhotos.map(photo => 
        typeof photo === 'object' ? photo.url : photo
      );
      uni.previewImage({
        urls: urls,
        current: index
      });
    },

    // 预览问题照片
    previewIssuePhoto(index) {
      const urls = this.taskInfo.issuePhotos.map(photo => 
        typeof photo === 'object' ? photo.url : photo
      );
      uni.previewImage({
        urls: urls,
        current: index
      });
    },

    // 预览审核照片
    previewReviewPhoto(index) {
      const urls = this.reviewForm.photos.map(photo => this.getPhotoDisplayUrl(photo));
      uni.previewImage({
        urls: urls,
        current: index
      });
    },

    // 获取照片显示URL
    getPhotoDisplayUrl(photo) {
      return photo.url || photo;
    },

    // 添加审核照片
    async addReviewPhoto() {
      try {
        const res = await new Promise((resolve, reject) => {
          uni.chooseImage({
            count: 6 - this.reviewForm.photos.length,
            sizeType: ['compressed'],
            sourceType: ['camera', 'album'],
            success: resolve,
            fail: reject
          });
        });
        
        // 添加临时路径到照片列表
        const newPhotos = res.tempFilePaths.map(path => ({
          url: path,
          uploaded: false,
          cloudUrl: '',
          cloudPath: '',
          uploading: false,
          belongsToRecord: false // 标记为新上传的照片
        }));
        

        
        // 添加到照片列表
        this.reviewForm.photos = this.reviewForm.photos.concat(newPhotos);
        
        // 如果开启自动上传，立即上传新选择的照片
        if (this.autoUpload) {
          this.autoUploadNewPhotos(newPhotos);
        }
      } catch (error) {
        console.error('选择照片失败:', error);
        uni.showToast({
          title: '选择照片失败',
          icon: 'none'
        });
      }
    },

    // 删除审核照片
    async deleteReviewPhoto(index) {
      if (index < 0 || index >= this.reviewForm.photos.length) {
        return;
      }

      const photo = this.reviewForm.photos[index];
      
      // 如果照片已经上传到云端，需要删除云端文件
      if (photo.uploaded && photo.cloudPath) {
        try {
          uni.showLoading({ title: '删除照片中...' });
          
          // 审核照片都是新上传的，直接调用删除云文件的云函数
          await uniCloud.callFunction({
            name: 'delete-file',
            data: {
              fileList: [this.extractFileId(photo.cloudPath)]
            }
          });
          

        } catch (error) {
          uni.showToast({
            title: '删除云端照片失败',
            icon: 'none'
          });
        } finally {
          uni.hideLoading();
        }
      }
      
      // 从本地数组中移除
      this.reviewForm.photos.splice(index, 1);
    },

    // 从URL中提取文件ID（与cleaning-upload.vue保持一致）
    extractFileId(url) {
      if (url.startsWith('cloud://')) {
        const parts = url.split('/');
        return parts[parts.length - 1];
      } else if (url.includes('tcb-api')) {
        const urlObj = new URL(url);
        return urlObj.pathname.split('/').pop();
      }
      return url;
    },

    // 自动上传新选择的照片（与cleaning-upload.vue保持一致）
    async autoUploadNewPhotos(newPhotos) {
      
      for (let i = 0; i < newPhotos.length; i++) {
        const photo = newPhotos[i];
        const photoIndex = this.reviewForm.photos.findIndex(p => p.url === photo.url);
        
        if (photoIndex === -1) continue;
        
        try {
          // 标记为正在上传
          this.$set(this.reviewForm.photos[photoIndex], 'uploading', true);
          
          // 单张照片上传
          const uploadResult = await this.uploadSinglePhoto(photo);
          
          if (uploadResult.success) {
            // 更新照片信息（与cleaning-upload.vue保持一致，不更新photo.url）
            this.$set(this.reviewForm.photos[photoIndex], 'uploaded', true);
            this.$set(this.reviewForm.photos[photoIndex], 'cloudUrl', uploadResult.url);
            this.$set(this.reviewForm.photos[photoIndex], 'cloudPath', uploadResult.cloudPath);
            this.$set(this.reviewForm.photos[photoIndex], 'uploading', false);
            

          } else {
            throw new Error(uploadResult.error || '上传失败');
          }
        } catch (error) {
          this.$set(this.reviewForm.photos[photoIndex], 'uploading', false);
          
          // 显示上传失败提示
          uni.showToast({
            title: `审核照片${i + 1}上传失败`,
            icon: 'none',
            duration: 2000
          });
        }
      }
    },

    // 上传审核照片
    async uploadReviewPhotos() {
      const uploadedPhotos = [];
      
      for (let i = 0; i < this.reviewForm.photos.length; i++) {
        const photo = this.reviewForm.photos[i];
        
        if (photo.uploaded) {
          // 已上传的照片，直接使用云端URL
          uploadedPhotos.push({
            url: photo.cloudPath,
            description: '审核照片',
            timestamp: new Date()
          });
          continue;
        }

        try {
          // 设置上传状态
          this.$set(photo, 'uploading', true);
          
          // 使用统一的上传工具上传单张照片
          const uploadResult = await this.uploadSinglePhoto(photo);
          
          if (uploadResult.success) {
            // 更新照片状态（与cleaning-upload.vue保持一致，不更新photo.url）
            this.$set(photo, 'uploaded', true);
            this.$set(photo, 'cloudUrl', uploadResult.url);
            this.$set(photo, 'cloudPath', uploadResult.cloudPath);
            this.$set(photo, 'uploading', false);

            uploadedPhotos.push({
              url: uploadResult.cloudPath,
              description: '审核照片',
              timestamp: new Date()
            });
            

          } else {
            throw new Error(uploadResult.error || '上传失败');
          }
        } catch (error) {
          this.$set(photo, 'uploading', false);
          
          uni.showModal({
            title: '上传失败',
            content: `审核照片 ${i + 1} 上传失败，请重试`,
            showCancel: false
          });
          
          throw error; // 中断整个提交流程
        }
      }

      return uploadedPhotos;
    },

    // 单张照片上传（统一方法）
    async uploadSinglePhoto(photo) {
      try {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);
        const cloudPath = `6s/review/${this.taskInfo.id || 'unknown'}/${timestamp}_${random}.jpg`;
        
        // 使用统一的 uploadToCloud 方法
        const uploadResult = await uploadUtils.uploadToCloud(photo.url, cloudPath);
        
        if (uploadResult && uploadResult.fileID) {
          // 获取访问URL
          const fileInfo = await uploadUtils.getFileInfo(uploadResult.fileID);
          
          return {
            success: true,
            cloudPath: uploadResult.fileID,
            url: fileInfo.tempFileURL || uploadResult.fileID,
            size: uploadResult.actualSize
          };
        } else {
          throw new Error('上传返回结果异常');
        }
      } catch (error) {
        return { success: false, error: error.message };
      }
    },

    // 提交审核结果
    async submitReview() {
      if (!this.canSubmit) return;

      const resultText = this.reviewForm.result === 'approved' ? '复查通过' : '需重新整改';
      
      uni.showModal({
        title: '确认提交',
        content: `确定审核结果为"${resultText}"吗？`,
        success: async (res) => {
          if (res.confirm) {
            await this.doSubmit();
          }
        }
      });
    },

    // 执行提交
    async doSubmit() {
      this.isSubmitting = true;

      try {
        // 上传审核照片
        let uploadedPhotos = [];
        if (this.reviewForm.photos.length > 0) {
          uploadedPhotos = await this.uploadReviewPhotos();
        }

        const reviewData = {
          id: this.taskId, // 修正参数名
          review_result: this.reviewForm.result, // 'approved' 或 'rejected'
          review_comments: this.reviewForm.comment.trim() || '',
          review_photos: uploadedPhotos, // 添加审核照片
          review_date: new Date().toISOString(),
          final_rating: this.reviewForm.result === 'approved' ? this.reviewForm.rating : 0 // 最终评分
        };



        // 调用云函数提交审核结果
        const result = await callCloudFunction('hygiene-rectification', {
          action: 'reviewRectification',
          data: reviewData
        });

        if (result && result.success) {
          const resultText = this.reviewForm.result === 'approved' ? '复查通过' : '需重新整改';
          
          uni.showToast({
            title: `审核${resultText}`,
            icon: 'success'
          });

          // 通知其他页面数据已更新
          uni.$emit('rectificationReviewUpdated', {
            taskId: this.taskId,
            result: this.reviewForm.result,
            areaId: this.taskInfo.id,
            areaName: this.taskInfo.areaName
          });

          // 同时触发整改记录更新事件，供其他页面监听
          uni.$emit('rectificationRecordUpdated', {
            taskId: this.taskId,
            areaId: this.taskInfo.id,
            mode: 'review',
            result: this.reviewForm.result
          });

          // 返回上一页并刷新
          setTimeout(() => {
            uni.navigateBack({
              delta: 1
            });
          }, 1500);
        } else {
          throw new Error(result?.message || '提交审核结果失败');
        }

      } catch (error) {
        
        let errorMessage = '提交失败，请重试';
        if (error.message) {
          if (error.message.includes('未登录')) {
            errorMessage = '请先登录';
          } else if (error.message.includes('权限')) {
            errorMessage = '您没有权限审核该任务';
          } else {
            errorMessage = error.message;
          }
        }
        
        uni.showModal({
          title: '提交失败',
          content: errorMessage,
          showCancel: false,
          confirmText: '知道了'
        });
      } finally {
        this.isSubmitting = false;
      }
    },

    // 处理照片加载错误（通用方法）
    onPhotoError(index, photoArray = 'rectificationPhotos') {
      let photos;
      if (photoArray === 'rectificationPhotos') {
        photos = this.taskInfo.rectificationPhotos;
      } else if (photoArray === 'issuePhotos') {
        photos = this.taskInfo.issuePhotos;
      } else {
        photos = this.reviewForm.photos;
      }
      
      const photo = photos[index];
      
      if (typeof photo === 'object') {
        this.$set(photo, 'loadError', true);
      } else {
        const photoObj = (photoArray === 'rectificationPhotos' || photoArray === 'issuePhotos')
          ? { url: photo, loadError: true }
          : { url: photo, uploaded: false, cloudUrl: '', cloudPath: '', uploading: false, belongsToRecord: false, loadError: true };
        this.$set(photos, index, photoObj);
      }
    },

    // 重试加载照片（通用方法）
    retryPhotoLoad(index, photoArray = 'rectificationPhotos') {
      let photos;
      if (photoArray === 'rectificationPhotos') {
        photos = this.taskInfo.rectificationPhotos;
      } else if (photoArray === 'issuePhotos') {
        photos = this.taskInfo.issuePhotos;
      } else {
        photos = this.reviewForm.photos;
      }
      
      const photo = photos[index];
      
      if (typeof photo === 'object') {
        this.$set(photo, 'loadError', false);
      }
    },



    // 切换自动上传状态
    toggleAutoUpload() {
      this.autoUpload = !this.autoUpload;
      uni.showToast({
        title: this.autoUpload ? '已开启自动上传' : '已关闭自动上传',
        icon: 'none',
        duration: 1500
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
	min-height: 100vh;
	background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
	width: 100%;
	overflow-x: hidden; /* 防止水平滚动 */
	box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
}

.page-header {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  padding: 32rpx 32rpx 40rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.header-content {
  flex: 1;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

.status-badge-enhanced {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 400;
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  white-space: nowrap;

  &.status-pending_review {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.95);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
  }

  &.status-approved {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.95);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
  }

  &.status-rejected {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.95);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
  }

  &.status-completed {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.95);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
  }
}

.page-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
  padding: 60rpx 32rpx;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #E5E7EB;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner-small {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid #E5E7EB;
  border-top: 3rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
  margin-top: 8rpx;
}

.page-error {
  padding: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300rpx; /* Adjust as needed */
}

.card {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 24rpx 32rpx 0 32rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 32rpx 32rpx 16rpx 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.card-subtitle {
  font-size: 24rpx;
  color: #8E8E93;
}

.card-body {
  padding: 32rpx;
}

.issue-info {
  .issue-description {
    font-size: 28rpx;
    color: #1D1D1F;
    line-height: 1.6;
    margin-bottom: 16rpx;
  }

  .issue-meta {
    .meta-label {
      font-size: 24rpx;
      color: #8E8E93;
    }

    .meta-value {
      font-size: 24rpx;
      color: #007AFF;
      margin-left: 8rpx;
    }
  }
}

/* 问题说明中的检查员信息样式 */
.description-section .issue-meta {
  margin-top: 16rpx;
  padding-top: 12rpx;
  border-top: 1rpx solid #F2F2F7;

  .meta-label {
    font-size: 24rpx;
    color: #8E8E93;
  }

  .meta-value {
    font-size: 24rpx;
    color: #007AFF;
    margin-left: 8rpx;
  }
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1D1D1F;
  margin-bottom: 16rpx;

  .required-mark {
    color: #FF3B30;
    margin-left: 4rpx;
  }
}

.photo-section {
  margin-bottom: 32rpx;
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.photo-item {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;

  image {
    width: 100%;
    height: 100%;
  }
}

.photo-add {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #C7C7CC;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #F9F9F9;

  text {
    font-size: 24rpx;
    color: #8E8E93;
    margin-top: 8rpx;
  }
}

.photo-delete {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  width: 36rpx;
  height: 36rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.photo-uploading {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-spinner {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-top: 2rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.photo-uploaded {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #34C759;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.photo-tip {
  font-size: 24rpx;
  color: #8E8E93;
  margin-top: 16rpx;
}

.description-content {
  font-size: 28rpx;
  color: #1D1D1F;
  line-height: 1.6;
  background: #F9F9F9;
  padding: 24rpx;
  border-radius: 12rpx;
}

.review-options {
  margin-bottom: 32rpx;
}

.review-option {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #E5E5EA;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;

  &.active {
    border-color: #007AFF;
    background: rgba(0, 122, 255, 0.05);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.option-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;

  &.approved {
    background: #34C759;
  }

  &.rejected {
    background: #FF3B30;
  }
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1D1D1F;
  margin-bottom: 4rpx;
}

.option-desc {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 增强的评分区域样式 */
.rating-section-enhanced {
  margin-top: 32rpx;
  padding: 32rpx;
  background: #F8F9FA;
  border-radius: 16rpx;
  border: 2rpx solid #E8F5E8;
}

.rating-container-enhanced {
  text-align: center;
}

.rating-display-large {
  margin-bottom: 32rpx;
}

.rating-number-container {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 6rpx;
  margin-bottom: 8rpx;
}

.rating-number {
  font-size: 80rpx;
  font-weight: 700;
  color: #007AFF;
  line-height: 1;
}

.rating-unit {
  font-size: 40rpx;
  color: #8E8E93;
  font-weight: 500;
}

.rating-desc {
  font-size: 24rpx;
  color: #8E8E93;
  margin-top: 8rpx;
  font-weight: 500;
}

/* 星星评分样式 */
.star-rating {
  display: flex;
  justify-content: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

/* 自定义滑动条评分样式 */
.custom-slider-rating {
  margin: 32rpx 0 24rpx 0;
}

.custom-slider-container {
  position: relative;
  height: 40rpx;
  margin: 0 24rpx;
  display: flex;
  align-items: center;
  user-select: none;
}

.slider-track {
  position: absolute;
  width: 100%;
  height: 4rpx;
  background: #E5E5EA;
  border-radius: 2rpx;
  left: 0;
}

.slider-track-active {
  height: 100%;
  background: #FFD700;
  border-radius: 2rpx;
  transition: width 0.2s ease;
}

.slider-thumb {
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  background: #FFD700;
  border-radius: 50%;
  transform: translateX(-50%);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  transition: left 0.2s ease;
  z-index: 2;
}

.slider-marks {
  position: absolute;
  width: 100%;
  height: 100%;
  top: -12rpx;
}

.slider-mark {
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background: #E5E5EA;
  border-radius: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.slider-mark-active {
  background: #FFD700;
  width: 16rpx;
  height: 16rpx;
}

.slider-labels-external {
  position: relative;
  margin-top: 20rpx;
  margin-left: 24rpx;
  margin-right: 24rpx;
  height: 40rpx;
}

.slider-label-external {
  position: absolute;
  font-size: 24rpx;
  color: #8E8E93;
  font-weight: 500;
  transform: translateX(-50%);
  text-align: center;
}

.slider-label-active {
  color: #FFD700;
  font-weight: 600;
}

.rating-tips {
  font-size: 24rpx;
  color: #8E8E93;
}

.review-comment-section,
.review-photo-section {
  margin-top: 32rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.auto-upload-toggle {
  display: flex;
  align-items: center;
  gap: 12rpx;
  cursor: pointer;
}

.toggle-label {
  font-size: 24rpx;
  color: #8E8E93;
}

.toggle-switch {
  width: 76rpx;
  height: 44rpx;
  background: #E5E5EA;
  border-radius: 22rpx;
  position: relative;
  transition: background-color 0.3s ease;
}

.toggle-switch.active {
  background: #34C759;
}

.toggle-circle {
  width: 36rpx;
  height: 36rpx;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  transition: transform 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.toggle-switch.active .toggle-circle {
  transform: translateX(32rpx);
}

.textarea-container {
  position: relative;
}

.textarea-container-enhanced {
  position: relative;
  border-radius: 16rpx;
  border: 2rpx solid #F2F2F7;
  background: #FAFAFA;
  overflow: hidden;
  transition: all 0.3s ease;

  &:focus-within {
    border-color: #007AFF;
    background: white;
    box-shadow: 0 2rpx 12rpx rgba(0, 122, 255, 0.15);
  }
}

.comment-textarea-enhanced {
  width: 100%;
  min-height: 160rpx;
  background: transparent;
  padding: 24rpx 20rpx 16rpx 20rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  line-height: 1.5;
  border: none;
  outline: none;
  resize: none;
  box-sizing: border-box;

  &::placeholder {
    color: #C7C7CC;
    font-size: 28rpx;
  }
}

.char-count-overlay {
  position: absolute;
  bottom: 16rpx;
  right: 20rpx;
  font-size: 24rpx;
  color: #C7C7CC !important;
  font-weight: 500;
  z-index: 2;
}

.button-container {
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.primary-button {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  width: 100%;
}

.primary-button[disabled] {
  background: #C7C7CC;
  color: #8E8E93;
}

.primary-button.loading {
  background: #0056D6;
  opacity: 0.9;
}

.button-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  color: white;
}

.button-loading .loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.bottom-safe-area {
  height: 40rpx;
}

.photos-loading-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 160rpx; /* Adjust as needed */
  background: #f0f0f0;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.loading-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.photos-loading-text {
  font-size: 28rpx;
  color: #8E8E93;
  margin-top: 12rpx;
  text-align: center;
}

.no-photos {
  padding: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 160rpx; /* Adjust as needed */
}

.photo-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8E8E93;
  font-size: 24rpx;
  z-index: 10;
}

.error-text {
  margin-top: 8rpx;
}
</style> 