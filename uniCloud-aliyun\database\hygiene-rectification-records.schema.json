{"bsonType": "object", "required": ["issue_id", "assigned_to", "deadline"], "permission": {"read": true, "create": "auth.role.includes('Integrated') || auth.role.includes('reviser') || auth.role.includes('admin')", "update": "doc.assigned_to == auth.uid || doc.reviewer_id == auth.uid || auth.role.includes('admin')", "delete": "auth.role.includes('admin')"}, "properties": {"_id": {"description": "ID，系统自动生成"}, "issue_id": {"bsonType": "string", "title": "问题ID", "description": "关联的问题记录ID", "foreignKey": "hygiene-issues._id", "errorMessage": {"required": "问题ID不能为空"}}, "area_id": {"bsonType": "string", "title": "责任区ID", "description": "整改涉及的责任区ID", "foreignKey": "hygiene-areas._id"}, "area_name": {"bsonType": "string", "title": "责任区名称", "description": "冗余存储的责任区名称", "maxLength": 50}, "title": {"bsonType": "string", "title": "整改任务标题", "description": "整改任务的标题", "maxLength": 100}, "description": {"bsonType": "string", "title": "整改要求", "description": "详细的整改要求和标准", "maxLength": 1000}, "assigned_to": {"bsonType": "string", "title": "整改负责人", "description": "负责执行整改的用户ID", "foreignKey": "uni-id-users._id", "errorMessage": {"required": "整改负责人不能为空"}}, "assigned_to_name": {"bsonType": "string", "title": "负责人姓名", "description": "冗余存储的负责人姓名", "maxLength": 50}, "assigned_by": {"bsonType": "string", "title": "分配人", "description": "分配整改任务的用户ID", "foreignKey": "uni-id-users._id"}, "assigned_by_name": {"bsonType": "string", "title": "分配人姓名", "description": "冗余存储的分配人姓名", "maxLength": 50}, "priority": {"bsonType": "string", "title": "优先级", "description": "整改任务的优先级", "enum": ["low", "normal", "high", "urgent"], "default": "normal"}, "deadline": {"bsonType": "timestamp", "title": "整改期限", "description": "要求完成整改的截止时间", "errorMessage": {"required": "整改期限不能为空"}}, "estimated_hours": {"bsonType": "number", "title": "预计工时", "description": "预计完成整改所需的工时", "minimum": 0.1, "maximum": 100}, "status": {"bsonType": "string", "title": "整改状态", "description": "整改任务的当前状态", "enum": ["pending_assignment", "pending_rectification", "in_progress", "pending_review", "completed", "verified", "rejected", "overdue"], "default": "pending_assignment"}, "progress_percentage": {"bsonType": "number", "title": "完成进度", "description": "整改完成的百分比", "minimum": 0, "maximum": 100, "default": 0}, "start_date": {"bsonType": "timestamp", "title": "开始时间", "description": "开始执行整改的时间"}, "completion_date": {"bsonType": "timestamp", "title": "完成时间", "description": "整改完成的时间"}, "completion_description": {"bsonType": "string", "title": "完成说明", "description": "整改完成后的详细说明", "maxLength": 1000}, "completion_photos": {"bsonType": "array", "title": "完成照片", "description": "整改完成后的照片证明", "items": {"bsonType": "object", "properties": {"url": {"bsonType": "string", "title": "图片URL", "pattern": "^cloud://"}, "description": {"bsonType": "string", "title": "照片说明", "maxLength": 100}, "timestamp": {"bsonType": "timestamp", "title": "拍摄时间"}}}, "maxItems": 10}, "progress_updates": {"bsonType": "array", "title": "进度更新", "description": "整改过程中的进度更新记录", "items": {"bsonType": "object", "properties": {"timestamp": {"bsonType": "timestamp", "title": "更新时间"}, "progress": {"bsonType": "number", "title": "进度百分比", "minimum": 0, "maximum": 100}, "description": {"bsonType": "string", "title": "进度说明", "maxLength": 500}, "photos": {"bsonType": "array", "title": "进度照片", "items": {"bsonType": "string", "pattern": "^cloud://"}, "maxItems": 5}}}, "maxItems": 20}, "reviewer_id": {"bsonType": "string", "title": "审核人ID", "description": "审核整改结果的用户ID", "foreignKey": "uni-id-users._id"}, "reviewer_name": {"bsonType": "string", "title": "审核人姓名", "description": "冗余存储的审核人姓名", "maxLength": 50}, "review_date": {"bsonType": "timestamp", "title": "审核时间", "description": "审核的时间"}, "review_result": {"bsonType": "string", "title": "审核结果", "description": "整改审核的结果", "enum": ["approved", "rejected", "needs_improvement"]}, "review_comments": {"bsonType": "string", "title": "审核意见", "description": "审核的详细意见和建议", "maxLength": 500}, "review_rating": {"bsonType": "number", "title": "审核评分", "description": "整改结果的质量评分（1-5分）", "minimum": 1, "maximum": 5}, "final_rating": {"bsonType": "number", "title": "最终评分", "description": "整改任务的最终质量评分（1-5分）", "minimum": 1, "maximum": 5}, "review_photos": {"bsonType": "array", "title": "审核照片", "description": "审核时拍摄的照片", "items": {"bsonType": "object", "properties": {"url": {"bsonType": "string", "title": "图片URL", "pattern": "^cloud://"}, "description": {"bsonType": "string", "title": "照片说明", "maxLength": 100}, "timestamp": {"bsonType": "timestamp", "title": "拍摄时间"}}}, "maxItems": 10}, "cost_actual": {"bsonType": "number", "title": "实际成本", "description": "整改实际发生的成本（元）", "minimum": 0, "maximum": 999999}, "follow_up_required": {"bsonType": "bool", "title": "需要跟进", "description": "是否需要后续跟进检查", "default": false}, "follow_up_date": {"bsonType": "timestamp", "title": "跟进日期", "description": "计划跟进检查的日期"}, "tags": {"bsonType": "array", "title": "标签", "description": "整改任务的标签", "items": {"bsonType": "string", "maxLength": 20}, "maxItems": 10}, "created_at": {"bsonType": "timestamp", "title": "创建时间", "description": "记录创建时间", "forceDefaultValue": {"$env": "now"}}, "updated_at": {"bsonType": "timestamp", "title": "更新时间", "description": "记录最后更新时间", "forceDefaultValue": {"$env": "now"}}}}