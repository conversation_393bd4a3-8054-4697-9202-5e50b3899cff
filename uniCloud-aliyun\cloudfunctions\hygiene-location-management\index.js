'use strict';

const db = uniCloud.database();
const dbCmd = db.command;

// 6S权限角色常量 - 基于实际业务场景
const HYGIENE_ROLES = {
  // 最高管理权限：系统管理员、厂长（可以创建、修改、删除，但主要看结果）
  ADMIN_ROLES: ['admin', 'GM'],
  // 6S专员权限：综合员、发布人（专门负责6S的核心工作人员）
  SPECIALIST_ROLES: ['Integrated', 'reviser'],
  // 位置配置权限：管理员和6S专员可以配置位置信息
  CONFIG_ROLES: ['admin', 'GM', 'Integrated', 'reviser'],
  // 数据查看权限：所有角色
  VIEW_ROLES: ['admin', 'GM', 'supervisor', 'PM', 'responsible', 'logistics', 'dispatch', 'Integrated', 'operator', 'technician', 'mechanic', 'user', 'reviser']
};

exports.main = async (event, context) => {
  console.log('6S位置管理云函数被调用:', event);
  
  const { action, data, uniIdToken } = event;
  let uid = '';
  let role = [];
  
  // 获取用户信息
  if (uniIdToken) {
    const { createInstance } = require('uni-id-common');
    const uniID = createInstance({ context });
    const payload = await uniID.checkToken(uniIdToken);
    
    if (payload.code === 0) {
      uid = payload.uid;
      
      // 从数据库获取用户角色信息
      try {
        const userRes = await db.collection('uni-id-users')
          .doc(uid)
          .field({ role: true })
          .get();
        
        if (userRes.data && userRes.data[0] && userRes.data[0].role) {
          role = Array.isArray(userRes.data[0].role) ? userRes.data[0].role : [userRes.data[0].role];
        } else {
          role = ['user'];
        }
      } catch (error) {
        console.error('获取用户角色失败:', error);
        role = ['user'];
      }
    } else {
      return {
        success: false,
        message: payload.errMsg || '用户未登录或登录已过期',
        data: null,
        code: payload.code || 401
      };
    }
  } else {
    return {
      success: false,
      message: '缺少用户认证信息',
      data: null,
      code: 401
    };
  }

  console.log('当前用户:', uid, '角色:', role);

  
  try {
    switch (action) {
      case 'getLocationConfig':
        return await getLocationConfig(uid, role);
      case 'updateLocationConfig':
        return await updateLocationConfig(data, uid, role);
      default:
        throw new Error(`未知的操作类型: ${action}`);
    }
  } catch (error) {
    console.error('6S位置管理云函数执行失败:', error);
    return {
      success: false,
      message: error.message || '操作失败',
      data: null
    };
  }
};

// 获取位置配置
async function getLocationConfig(uid, role) {
  try {
    // 权限检查：所有登录用户都可以查看位置配置
    if (!role.some(r => HYGIENE_ROLES.VIEW_ROLES.includes(r))) {
      return {
        success: false,
        message: '您没有权限查看位置配置',
        data: null,
        code: 403
      };
    }
    
    // 查找位置配置
    const configResult = await db.collection('hygiene_location_config')
      .orderBy('created_at', 'desc')
      .limit(1)
      .get();

    let locationConfig;
    
    if (configResult.data.length === 0) {
      // 返回空配置，让用户自己创建
      locationConfig = {
        last_updated: new Date().toISOString(),
        locations: []
      };
    } else {
      locationConfig = configResult.data[0];
    }

    return {
      success: true,
      message: '获取位置配置成功',
      data: {
        lastUpdated: locationConfig.last_updated,
        locations: locationConfig.locations || []
      }
    };
  } catch (error) {
    console.error('获取位置配置失败:', error);
    throw new Error('获取位置配置失败: ' + error.message);
  }
}

// 更新位置配置
async function updateLocationConfig(configData, uid, role) {
  try {
    // 权限检查：只有管理员和6S专员可以修改位置配置
    if (!role.some(r => HYGIENE_ROLES.CONFIG_ROLES.includes(r))) {
      return {
        success: false,
        message: '您没有权限修改位置配置，请联系管理员或6S专员',
        data: null,
        code: 403
      };
    }
    
    const updateData = {
      last_updated: new Date().toISOString(),
      locations: configData.locations || [],
      updated_at: new Date().toISOString(),
      updated_by: uid // 记录操作者
    };

    // 查找现有配置
    const existingConfig = await db.collection('hygiene_location_config')
      .orderBy('created_at', 'desc')
      .limit(1)
      .get();

    if (existingConfig.data.length > 0) {
      // 更新现有配置
      await db.collection('hygiene_location_config')
        .doc(existingConfig.data[0]._id)
        .update(updateData);
    } else {
      // 创建新配置
      updateData.created_at = new Date().toISOString();
      updateData.created_by = uid; // 记录创建者
      await db.collection('hygiene_location_config').add(updateData);
    }

    return {
      success: true,
      message: '位置配置保存成功',
      data: updateData
    };
  } catch (error) {
    console.error('更新位置配置失败:', error);
    throw new Error('更新位置配置失败: ' + error.message);
  }
}



 