'use strict';

const db = uniCloud.database();
const dbCmd = db.command;

// 6S权限角色常量 - 基于实际业务场景
const HYGIENE_ROLES = {
  // 最高管理权限：系统管理员、厂长（可以创建、修改、删除，但主要看结果）
  ADMIN_ROLES: ['admin', 'GM'],
  // 6S专员权限：综合员、发布人（专门负责6S的核心工作人员）
  SPECIALIST_ROLES: ['Integrated', 'reviser'],
  // 清洁员权限：所有人都有责任区，都需要做清洁工作
  CLEANER_ROLES: ['admin', 'GM', 'supervisor', 'PM', 'responsible', 'logistics', 'dispatch', 'Integrated', 'operator', 'technician', 'mechanic', 'user', 'reviser'],
  // 检查权限：只有6S专员负责检查（包括卫生检查、问题发现、任务指派）
  INSPECTOR_ROLES: ['Integrated', 'reviser'],
  // 数据查看权限：所有角色
  VIEW_ROLES: ['admin', 'GM', 'supervisor', 'PM', 'responsible', 'logistics', 'dispatch', 'Integrated', 'operator', 'technician', 'mechanic', 'user', 'reviser']
};

exports.main = async (event, context) => {
  const { action, data, uniIdToken } = event;
  let uid = '';
  let role = [];
  
  // 获取用户信息
  if (uniIdToken) {
    const { createInstance } = require('uni-id-common');
    const uniID = createInstance({ context });
    const payload = await uniID.checkToken(uniIdToken);
    if (payload.code === 0) {
      uid = payload.uid;
      
      // 从数据库获取用户角色信息
      try {
        const userRes = await db.collection('uni-id-users')
          .doc(uid)
          .field({ role: true })
          .get();
        
        if (userRes.data && userRes.data[0] && userRes.data[0].role) {
          role = Array.isArray(userRes.data[0].role) ? userRes.data[0].role : [userRes.data[0].role];
        } else {
          role = ['user'];
        }
      } catch (error) {
        console.error('获取用户角色失败:', error);
        role = ['user'];
      }
    }
  }
  
  // 权限检查
  if (!uid) {
    return {
      success: false,
      message: '用户未登录'
    };
  }
  
  try {
    switch (action) {
      case 'createIssue':
        return await createIssue(data, uid, role);
      case 'updateIssue':
        return await updateIssue(data, uid, role);
      case 'deleteIssue':
        return await deleteIssue(data, uid, role);
      case 'getIssues':
        return await getIssues(data, uid, role);
      case 'getMyIssues':
        return await getMyIssues(data, uid);
      case 'getIssueDetail':
        return await getIssueDetail(data, uid, role);
      case 'assignIssue':
        return await assignIssue(data, uid, role);
      case 'updateIssueStatus':
        return await updateIssueStatus(data, uid, role);
      case 'addComment':
        return await addComment(data, uid, role);
      case 'saveDraft':
        return await saveDraft(data, uid, role);
      case 'publishIssue':
        return await publishIssue(data, uid, role);
      default:
        return {
          success: false,
          message: '未知的操作类型'
        };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return {
      success: false,
      message: '服务器错误',
      error: error.message
    };
  }
};

// 创建问题
async function createIssue(data, uid, role) {
  const {
    title,
    description,
    category = 'cleanliness',
    severity = 'medium',
    area_id,
    photos = [],
    priority = 'normal',
    expected_completion_date,
    tags = []
  } = data;
  
  // 验证必填字段
  if (!title || !description || !area_id) {
    return {
      success: false,
      message: '问题标题、描述和责任区不能为空'
    };
  }
  
  // 获取责任区信息
  const areaResult = await db.collection('hygiene-areas').doc(area_id).get();
  if (!areaResult.data.length) {
    return {
      success: false,
      message: '责任区不存在'
    };
  }
  
  const area = areaResult.data[0];
  
  // 获取举报人信息
  const userResult = await db.collection('uni-id-users').doc(uid).get();
  const userName = userResult.data.length > 0 ? userResult.data[0].nickname || userResult.data[0].username : '';
  
  const issueData = {
    title,
    description,
    category,
    severity,
    area_id,
    area_name: area.name,
    reporter_id: uid,
    reporter_name: userName,
    photos: photos.map(photo => ({
      url: photo.url || photo,
      description: photo.description || '',
      timestamp: new Date()
    })),
    status: 'submitted',
    priority,
    tags,
    follow_up_required: false,
    comments: [],
    created_at: new Date(),
    updated_at: new Date()
  };
  
  if (expected_completion_date) {
    issueData.expected_completion_date = new Date(expected_completion_date);
  }
  
  const result = await db.collection('hygiene-issues').add(issueData);
  
  return {
    success: true,
    message: '问题创建成功',
    data: {
      _id: result.id,
      ...issueData
    }
  };
}

// 更新问题
async function updateIssue(data, uid, role) {
  const {
    id,
    title,
    description,
    category,
    severity,
    photos,
    priority,
    expected_completion_date,
    tags,
    resolution_description,
    resolution_photos,
    follow_up_required,
    follow_up_date
  } = data;
  
  if (!id) {
    return {
      success: false,
      message: '问题ID不能为空'
    };
  }
  
  // 获取问题记录
  const issueResult = await db.collection('hygiene-issues').doc(id).get();
  if (!issueResult.data.length) {
    return {
      success: false,
      message: '问题不存在'
    };
  }
  
  const issue = issueResult.data[0];
  
  // 权限检查：举报人、负责人或管理员可以修改
  if (!role.includes('admin') && !role.includes('manager') && 
      issue.reporter_id !== uid && issue.assigned_to !== uid) {
    return {
      success: false,
      message: '权限不足'
    };
  }
  
  const updateData = {
    updated_at: new Date()
  };
  
  if (title !== undefined) updateData.title = title;
  if (description !== undefined) updateData.description = description;
  if (category !== undefined) updateData.category = category;
  if (severity !== undefined) updateData.severity = severity;
  if (priority !== undefined) updateData.priority = priority;
  if (tags !== undefined) updateData.tags = tags;
  if (follow_up_required !== undefined) updateData.follow_up_required = follow_up_required;
  
  if (photos !== undefined) {
    updateData.photos = photos.map(photo => ({
      url: photo.url || photo,
      description: photo.description || '',
      timestamp: new Date()
    }));
  }
  
  if (expected_completion_date !== undefined) {
    updateData.expected_completion_date = expected_completion_date ? new Date(expected_completion_date) : null;
  }
  
  if (follow_up_date !== undefined) {
    updateData.follow_up_date = follow_up_date ? new Date(follow_up_date) : null;
  }
  
  if (resolution_description !== undefined) updateData.resolution_description = resolution_description;
  if (resolution_photos !== undefined) updateData.resolution_photos = resolution_photos;
  
  await db.collection('hygiene-issues').doc(id).update(updateData);
  
  return {
    success: true,
    message: '问题更新成功'
  };
}

// 删除问题
async function deleteIssue(data, uid, role) {
  const { id } = data;
  
  if (!id) {
    return {
      success: false,
      message: '问题ID不能为空'
    };
  }
  
  // 获取问题记录
  const issueResult = await db.collection('hygiene-issues').doc(id).get();
  if (!issueResult.data.length) {
    return {
      success: false,
      message: '问题不存在'
    };
  }
  
  const issue = issueResult.data[0];
  
  // 权限检查：只有管理员或举报人可以删除（且问题状态为草稿）
  if (!role.includes('admin') && 
      !(issue.reporter_id === uid && issue.status === 'draft')) {
    return {
      success: false,
      message: '权限不足，只有管理员或草稿状态的举报人可以删除问题'
    };
  }
  
  // 检查是否存在相关的整改记录
  const rectificationResult = await db.collection('hygiene-rectification-records')
    .where({ issue_id: id })
    .count();
  
  if (rectificationResult.total > 0) {
    return {
      success: false,
      message: '该问题存在相关整改记录，无法删除'
    };
  }
  
  await db.collection('hygiene-issues').doc(id).remove();
  
  return {
    success: true,
    message: '问题删除成功'
  };
}

// 获取问题列表
async function getIssues(data, uid, role) {
  const {
    area_id,
    reporter_id,
    assigned_to,
    status,
    category,
    severity,
    priority,
    start_date,
    end_date,
    page = 1,
    pageSize = 20,
    keyword
  } = data;
  
  let whereCondition = {};
  
  // 责任区筛选
  if (area_id) {
    whereCondition.area_id = area_id;
  }
  
  // 举报人筛选
  if (reporter_id) {
    whereCondition.reporter_id = reporter_id;
  }
  
  // 负责人筛选
  if (assigned_to) {
    whereCondition.assigned_to = assigned_to;
  }
  
  // 状态筛选
  if (status) {
    whereCondition.status = status;
  }
  
  // 分类筛选
  if (category) {
    whereCondition.category = category;
  }
  
  // 严重程度筛选
  if (severity) {
    whereCondition.severity = severity;
  }
  
  // 优先级筛选
  if (priority) {
    whereCondition.priority = priority;
  }
  
  // 关键词搜索
  if (keyword) {
    whereCondition.title = new RegExp(keyword, 'i');
  }
  
  // 日期范围筛选
  if (start_date && end_date) {
    whereCondition.created_at = dbCmd.gte(new Date(start_date)).and(dbCmd.lte(new Date(end_date)));
  } else if (start_date) {
    whereCondition.created_at = dbCmd.gte(new Date(start_date));
  } else if (end_date) {
    whereCondition.created_at = dbCmd.lte(new Date(end_date));
  }
  
  const skip = (page - 1) * pageSize;
  
  const [listResult, countResult] = await Promise.all([
    db.collection('hygiene-issues')
      .where(whereCondition)
      .orderBy('created_at', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get(),
    db.collection('hygiene-issues')
      .where(whereCondition)
      .count()
  ]);
  
  return {
    success: true,
    data: {
      list: listResult.data,
      total: countResult.total,
      page,
      pageSize
    }
  };
}

// 获取我的问题
async function getMyIssues(data, uid) {
  const { type = 'reported', status, limit = 10 } = data;
  
  let whereCondition = {};
  
  if (type === 'reported') {
    whereCondition.reporter_id = uid;
  } else if (type === 'assigned') {
    whereCondition.assigned_to = uid;
  }
  
  if (status) {
    whereCondition.status = status;
  }
  
  const result = await db.collection('hygiene-issues')
    .where(whereCondition)
    .orderBy('created_at', 'desc')
    .limit(limit)
    .get();
  
  return {
    success: true,
    data: result.data
  };
}

// 获取问题详情
async function getIssueDetail(data, uid, role) {
  const { id } = data;
  
  if (!id) {
    return {
      success: false,
      message: '问题ID不能为空'
    };
  }
  
  const issueResult = await db.collection('hygiene-issues').doc(id).get();
  if (!issueResult.data.length) {
    return {
      success: false,
      message: '问题不存在'
    };
  }
  
  const issue = issueResult.data[0];
  
  // 获取相关的整改记录
  const rectificationResult = await db.collection('hygiene-rectification-records')
    .where({ issue_id: id })
    .orderBy('created_at', 'desc')
    .get();
  
  return {
    success: true,
    data: {
      ...issue,
      rectifications: rectificationResult.data
    }
  };
}

// 分配问题
async function assignIssue(data, uid, role) {
  const { id, assigned_to, assignment_reason } = data;
  
  if (!id || !assigned_to) {
    return {
      success: false,
      message: '问题ID和负责人不能为空'
    };
  }
  
  // 权限检查：只有管理员可以分配问题
  if (!role.includes('admin') && !role.includes('manager')) {
    return {
      success: false,
      message: '权限不足，只有管理员可以分配问题'
    };
  }
  
  // 获取负责人信息
  const userResult = await db.collection('uni-id-users').doc(assigned_to).get();
  if (!userResult.data.length) {
    return {
      success: false,
      message: '指定的负责人不存在'
    };
  }
  
  const assignedUser = userResult.data[0];
  
  const updateData = {
    assigned_to,
    assigned_to_name: assignedUser.nickname || assignedUser.username,
    status: 'assigned',
    updated_at: new Date()
  };
  
  if (assignment_reason) updateData.assignment_reason = assignment_reason;
  
  await db.collection('hygiene-issues').doc(id).update(updateData);
  
  return {
    success: true,
    message: '问题分配成功'
  };
}

// 更新问题状态
async function updateIssueStatus(data, uid, role) {
  const { id, status, resolution_description, actual_completion_date } = data;
  
  if (!id || !status) {
    return {
      success: false,
      message: '问题ID和状态不能为空'
    };
  }
  
  // 获取问题记录
  const issueResult = await db.collection('hygiene-issues').doc(id).get();
  if (!issueResult.data.length) {
    return {
      success: false,
      message: '问题不存在'
    };
  }
  
  const issue = issueResult.data[0];
  
  // 权限检查：负责人、举报人或管理员可以更新状态
  if (!role.includes('admin') && !role.includes('manager') && 
      issue.assigned_to !== uid && issue.reporter_id !== uid) {
    return {
      success: false,
      message: '权限不足'
    };
  }
  
  const updateData = {
    status,
    updated_at: new Date()
  };
  
  if (resolution_description) updateData.resolution_description = resolution_description;
  if (actual_completion_date) updateData.actual_completion_date = new Date(actual_completion_date);
  
  // 如果状态更新为已解决，记录完成时间
  if (status === 'resolved' && !actual_completion_date) {
    updateData.actual_completion_date = new Date();
  }
  
  await db.collection('hygiene-issues').doc(id).update(updateData);
  
  return {
    success: true,
    message: '问题状态更新成功'
  };
}

// 添加评论
async function addComment(data, uid, role) {
  const { id, content } = data;
  
  if (!id || !content) {
    return {
      success: false,
      message: '问题ID和评论内容不能为空'
    };
  }
  
  // 获取问题记录
  const issueResult = await db.collection('hygiene-issues').doc(id).get();
  if (!issueResult.data.length) {
    return {
      success: false,
      message: '问题不存在'
    };
  }
  
  const issue = issueResult.data[0];
  
  // 获取用户信息
  const userResult = await db.collection('uni-id-users').doc(uid).get();
  const userName = userResult.data.length > 0 ? userResult.data[0].nickname || userResult.data[0].username : '';
  
  const newComment = {
    user_id: uid,
    user_name: userName,
    content,
    timestamp: new Date()
  };
  
  const existingComments = issue.comments || [];
  existingComments.push(newComment);
  
  await db.collection('hygiene-issues').doc(id).update({
    comments: existingComments,
    updated_at: new Date()
  });
  
  return {
    success: true,
    message: '评论添加成功',
    data: newComment
  };
}

// 保存草稿
async function saveDraft(data, uid, role) {
  const {
    id,
    title,
    description,
    category = 'cleanliness',
    severity = 'medium',
    area_id,
    photos = [],
    priority = 'normal',
    tags = []
  } = data;
  
  // 如果有ID，更新现有草稿；否则创建新草稿
  if (id) {
    // 更新草稿
    const issueResult = await db.collection('hygiene-issues').doc(id).get();
    if (!issueResult.data.length) {
      return {
        success: false,
        message: '草稿不存在'
      };
    }
    
    const issue = issueResult.data[0];
    
    // 权限检查：只有草稿创建者可以修改
    if (issue.reporter_id !== uid) {
      return {
        success: false,
        message: '只能修改自己的草稿'
      };
    }
    
    const updateData = {
      title: title || issue.title,
      description: description || issue.description,
      category: category || issue.category,
      severity: severity || issue.severity,
      priority: priority || issue.priority,
      tags: tags || issue.tags,
      updated_at: new Date()
    };
    
    if (area_id) updateData.area_id = area_id;
    if (photos.length > 0) {
      updateData.photos = photos.map(photo => ({
        url: photo.url || photo,
        description: photo.description || '',
        timestamp: new Date()
      }));
    }
    
    await db.collection('hygiene-issues').doc(id).update(updateData);
    
    return {
      success: true,
      message: '草稿保存成功',
      data: { id }
    };
  } else {
    // 创建新草稿
    if (!title || !description) {
      return {
        success: false,
        message: '标题和描述不能为空'
      };
    }
    
    // 获取用户信息
    const userResult = await db.collection('uni-id-users').doc(uid).get();
    const userName = userResult.data.length > 0 ? userResult.data[0].nickname || userResult.data[0].username : '';
    
    const draftData = {
      title,
      description,
      category,
      severity,
      area_id: area_id || '',
      area_name: '',
      reporter_id: uid,
      reporter_name: userName,
      photos: photos.map(photo => ({
        url: photo.url || photo,
        description: photo.description || '',
        timestamp: new Date()
      })),
      status: 'draft',
      priority,
      tags,
      follow_up_required: false,
      comments: [],
      created_at: new Date(),
      updated_at: new Date()
    };
    
    // 如果有责任区ID，获取责任区名称
    if (area_id) {
      const areaResult = await db.collection('hygiene-areas').doc(area_id).get();
      if (areaResult.data.length > 0) {
        draftData.area_name = areaResult.data[0].name;
      }
    }
    
    const result = await db.collection('hygiene-issues').add(draftData);
    
    return {
      success: true,
      message: '草稿创建成功',
      data: {
        _id: result.id,
        ...draftData
      }
    };
  }
}

// 发布问题（从草稿状态）
async function publishIssue(data, uid, role) {
  const { id } = data;
  
  if (!id) {
    return {
      success: false,
      message: '问题ID不能为空'
    };
  }
  
  // 获取草稿
  const issueResult = await db.collection('hygiene-issues').doc(id).get();
  if (!issueResult.data.length) {
    return {
      success: false,
      message: '草稿不存在'
    };
  }
  
  const issue = issueResult.data[0];
  
  // 权限检查：只有草稿创建者可以发布
  if (issue.reporter_id !== uid) {
    return {
      success: false,
      message: '只能发布自己的草稿'
    };
  }
  
  // 检查必填字段
  if (!issue.title || !issue.description || !issue.area_id) {
    return {
      success: false,
      message: '标题、描述和责任区不能为空'
    };
  }
  
  // 如果责任区名称为空，获取责任区信息
  let updateData = {
    status: 'submitted',
    updated_at: new Date()
  };
  
  if (!issue.area_name && issue.area_id) {
    const areaResult = await db.collection('hygiene-areas').doc(issue.area_id).get();
    if (areaResult.data.length > 0) {
      updateData.area_name = areaResult.data[0].name;
    }
  }
  
  await db.collection('hygiene-issues').doc(id).update(updateData);
  
  return {
    success: true,
    message: '问题发布成功'
  };
} 