<template>
  <view class="page-container">
    <!-- 内容区域加载状态 -->
    <view v-if="loading" class="content-loading">
      <view class="loading-content">
        <uni-icons type="spinner-cycle" size="40" color="#007AFF"></uni-icons>
        <text class="loading-text">加载中...</text>
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="loadError" class="content-error">
      <p-empty-state 
        type="error"
        title="加载失败"
        description="网络异常，请检查网络连接"
        :show-button="true"
        button-text="重新加载"
        @button-click="retryLoad"
      ></p-empty-state>
    </view>

    <!-- 正常内容 -->
    <view v-else-if="dataLoaded && issue" class="content">
      <!-- 问题状态卡片 -->
      <view class="status-card">
        <!-- 顶部状态栏 -->
        <view class="status-bar">
          <view class="issue-id">#{{ getIssueNumber() }}</view>
          <view class="status-badge" :class="['status-' + issue.status]">
            {{ getStatusText(issue.status) }}
          </view>
        </view>
        
        <!-- 问题标题 -->
        <view class="issue-title">{{ issue.title }}</view>
        
        <!-- 关键信息网格 -->
        <view class="key-info">
          <view class="info-item">
            <view class="info-label">
              <uni-icons type="person" size="14" color="#007AFF"></uni-icons>
              <text>负责人</text>
            </view>
            <view class="info-value">
              <text class="main-value">{{ issue.responsible }}</text>
            </view>
          </view>
          <view class="info-item">
            <view class="info-label">
              <uni-icons type="calendar" size="14" color="#FF9500"></uni-icons>
              <text>截止时间</text>
            </view>
            <view class="info-value-with-badge">
              <text class="main-value">{{ issue.deadline }}</text>
              <view 
                v-if="issue.deadline" 
                class="countdown-badge" 
                :class="countdownClass"
              >
                {{ deadlineCountdown || '计算中...' }}
              </view>
            </view>
          </view>
        </view>
        
        <!-- 位置信息 -->
        <view class="location-info">
          <view class="location-section">
            <uni-icons type="location" size="16" color="#007AFF"></uni-icons>
            <text>{{ issue.location }}</text>
          </view>
          <view class="priority-badge" :class="'priority-' + (issue && issue.priority ? issue.priority : 'normal')">
            <view class="priority-dot"></view>
            <text>{{ getPriorityText(issue && issue.priority ? issue.priority : 'normal') }}</text>
          </view>
        </view>
      </view>

      <!-- 问题描述 -->
      <view class="description-card">
        <view class="card-title">问题描述</view>
        <view class="description-text">{{ issue.description }}</view>
      </view>

      <!-- 问题图片 -->
      <view v-if="issue.images && issue.images.length > 0" class="images-card">
        <view class="card-title">问题图片</view>
        <view class="images-grid">
          <view 
            v-for="(image, index) in issue.images" 
            :key="index"
            class="image-item"
            @click="previewImage(index)"
          >
            <image :src="image" mode="aspectFill" class="issue-image" />
          </view>
        </view>
      </view>

      <!-- 整改内容 -->
      <view v-if="issue.rectification_description || (issue.rectification_photos && issue.rectification_photos.length > 0)" class="rectification-card">
        <view class="card-title">整改内容</view>
        
        <!-- 整改说明 -->
        <view v-if="issue.rectification_description" class="rectification-description">
          <view class="rectification-label">整改说明</view>
          <view class="rectification-text">{{ issue.rectification_description }}</view>
        </view>
        
        <!-- 整改照片 -->
        <view v-if="issue.rectification_photos && issue.rectification_photos.length > 0" class="rectification-images">
          <view class="rectification-label">整改照片</view>
          <view class="images-grid">
            <view 
              v-for="(photo, index) in issue.rectification_photos" 
              :key="index"
              class="image-item"
              @click="previewRectificationImage(index)"
            >
              <image :src="photo.url || photo" mode="aspectFill" class="rectification-image" />
            </view>
          </view>
        </view>
        
        <!-- 整改完成时间 -->
        <view v-if="issue.completed_at" class="rectification-time">
          <view class="rectification-label">完成时间</view>
          <view class="rectification-time-text">{{ formatDateTime(issue.completed_at) }}</view>
        </view>
      </view>

      <!-- 处理进展 -->
      <view class="timeline-card">
        <view class="card-title">处理进展</view>
        <view class="unified-timeline">
          <view 
            v-for="(item, index) in unifiedTimeline" 
            :key="index"
            class="timeline-item"
            :class="{ 
              completed: item.status === 'completed', 
              current: item.status === 'current',
              pending: item.status === 'pending',
              overdue: item.status === 'overdue'
            }"
          >
            <view class="timeline-dot">
              <uni-icons 
                v-if="item.status === 'completed'" 
                type="checkmarkempty" 
                size="16" 
                color="#ffffff"
              ></uni-icons>
              <uni-icons 
                v-else-if="item.status === 'current'" 
                type="gear" 
                size="16" 
                color="#ffffff"
              ></uni-icons>
              <uni-icons 
                v-else-if="item.status === 'overdue'" 
                type="closeempty" 
                size="16" 
                color="#ffffff"
              ></uni-icons>
              <view 
                v-else 
                class="dot-placeholder"
              ></view>
            </view>
            <view class="timeline-content">
              <view class="timeline-header">
                <view class="step-title">{{ item.title }}</view>
                <view class="step-time" v-if="item.time">{{ item.time }}</view>
              </view>
              <view class="step-desc" v-if="item.description">{{ item.description }}</view>
              <view class="step-operator" v-if="item.operator">操作人：{{ item.operator }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部安全间距 -->
      <view class="bottom-safe-area"></view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-bar" v-if="dataLoaded && issue && shouldShowActionButton">
      <view class="action-buttons">
        <button 
          class="action-btn primary full" 
          :disabled="isButtonDisabled"
          @click="handleMainAction"
        >
          {{ getButtonText() }}
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'IssueDetail',
  data() {
    return {
      issueId: null,
      issue: null,
      loading: false,
      loadError: false,
      dataLoaded: false,
      unifiedTimeline: [],
      currentUserRole: null, // 'admin' | 'inspector' | 'responsible' | 'viewer'
      currentUserId: null, // 当前用户ID
      issueSubmittedForInspection: false // 整改内容是否已提交检查
    }
  },
  computed: {
    // 是否显示操作按钮
    shouldShowActionButton() {
      // 数据未加载时不显示按钮
      if (!this.issue || !this.dataLoaded) {
        return false;
      }
      
      // 只有负责人、检查员或管理员在特定状态下才显示按钮
      if (this.currentUserRole === 'viewer') {
        return false;
      }
      
      const status = this.issue.status;
      
      if (this.currentUserRole === 'responsible') {
        // 负责人：已分配、待整改、整改中且未提交检查、被驳回（逾期问题不能操作）
        return status === 'assigned' || status === 'pending' || status === 'rejected' || (status === 'in_progress' && !this.issueSubmittedForInspection);
      } else if (this.currentUserRole === 'admin') {
        // 管理员：检查通过后可以重新打开，其他状态都可以操作
        return true;
      } else if (this.currentUserRole === 'inspector') {
        // 检查员：整改已提交检查 或 待检查状态
        return this.issueSubmittedForInspection || status === 'pending_review';
      }
      
      return false;
    },
    
    // 按钮是否禁用
    isButtonDisabled() {
      return false; // 可以根据具体业务逻辑调整
    },
    
    // 截止时间倒计时文本
    deadlineCountdown() {
      if (!this.issue || !this.issue.deadline) return '';
      return this.getDeadlineCountdown(this.issue.deadline);
    },
    
    // 倒计时样式类
    countdownClass() {
      if (!this.issue || !this.issue.deadline) return '';
      return this.getCountdownClass(this.issue.deadline);
    }
  },
  onLoad(options) {
    this.issueId = options.id
    this.initializeUserInfo()
    this.loadIssueDetail()
    
    // 监听数据更新事件
    uni.$on('monthlyIssueUpdated', this.handleIssueUpdated);
  },
  
  onUnload() {
    // 移除事件监听
    uni.$off('monthlyIssueUpdated', this.handleIssueUpdated);
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    // 初始化用户信息
    async initializeUserInfo() {
      try {
        // 获取当前用户信息（与其他6S页面保持一致的实现）
        const userInfo = uni.getStorageSync('uni-id-pages-userInfo') || {};
        
        // 尝试获取用户角色信息（从专门的角色缓存中获取）
        let roles = [];
        try {
          const { getCacheKey, CACHE_KEYS } = require('@/utils/cache.js');
          let userRoleStr = uni.getStorageSync(getCacheKey(CACHE_KEYS.USER_ROLE)) || '{}';
          let userRole = {};
          
          if (typeof userRoleStr === 'string') {
            userRole = JSON.parse(userRoleStr);
          } else {
            userRole = userRoleStr;
          }
          
          // 从userRole.value.userRole获取角色数组
          if (userRole && userRole.value && userRole.value.userRole) {
            roles = Array.isArray(userRole.value.userRole) ? userRole.value.userRole : [userRole.value.userRole];
          }
        } catch (cacheError) {
          // 无法获取角色缓存，使用备用方案
        }
        
        // 备用方案：从用户信息中获取角色
        if (roles.length === 0 && userInfo.role) {
          roles = Array.isArray(userInfo.role) ? userInfo.role : [userInfo.role];
        } else if (roles.length === 0 && userInfo.username === 'admin') {
          // 特殊处理：如果用户名是admin，给予admin角色
          roles = ['admin'];
        }
        
        // 用户权限初始化
        
        if (userInfo && (userInfo._id || userInfo.id)) {
          this.currentUserId = userInfo._id || userInfo.id;
          
          // 根据用户角色确定权限
          if (roles.includes('admin')) {
            this.currentUserRole = 'admin';
          } else if (roles.includes('inspector') || roles.includes('Integrated') || roles.includes('reviser')) {
            this.currentUserRole = 'inspector';
          } else if (roles.includes('responsible')) {
            this.currentUserRole = 'responsible';
          } else {
            this.currentUserRole = 'viewer';
          }          

        } else {
          this.currentUserRole = 'viewer';
          this.currentUserId = null;
        }

        
      } catch (error) {
        console.log('初始化用户信息失败:', error);
        this.currentUserRole = 'viewer';
      }
    },

    async loadIssueDetail() {
      try {
        this.loading = true;
        this.loadError = false;
        
        const { callCloudFunction } = require('@/utils/auth.js');
        
        // 调用真实API获取问题详情
        const result = await callCloudFunction('hygiene-monthly-inspection', {
          action: 'getIssueDetail',
          data: {
            issue_id: this.issueId
          }
        });
        
        if (result && result.success) {
          // 检查返回的数据结构
          if (!result.data) {
            throw new Error('未找到对应的问题记录');
          }
          
          // 检查状态值是否有效
          if (result.data.status && !['pending', 'in_progress', 'pending_review', 'approved', 'overdue', 'rejected', 'open', 'assigned', 'resolved', 'reviewing', 'submitted', 'verified', 'cancelled', 'suspended', 'reopened', 'draft', 'new', 'active', 'inactive', 'expired'].includes(result.data.status)) {
            console.warn('未识别的状态值:', result.data.status, '完整数据:', result.data);
          }
          
          this.issue = this.formatIssueData(result.data);
          // 检查是否为负责人
          this.checkIfResponsible();
          // 检查整改提交状态
          this.checkSubmissionStatus();
          this.generateUnifiedTimeline();
          this.dataLoaded = true;
        } else {
          const errorMsg = result?.message || result?.error || '获取问题详情失败';
          throw new Error(errorMsg);
        }
        
      } catch (error) {
        
        // 如果是网络错误或API不可用，尝试从本地存储获取
        try {
          const storageKey = `issue_detail_${this.issueId}`;
          const cachedData = uni.getStorageSync(storageKey);
          
          if (cachedData && typeof cachedData === 'object') {
            this.issue = this.formatIssueData(cachedData);
            this.checkIfResponsible();
            this.checkSubmissionStatus();
            this.generateUnifiedTimeline();
            this.dataLoaded = true;
            
            uni.showToast({
              title: '已加载缓存数据',
              icon: 'none',
              duration: 2000
            });
            return;
          }
        } catch (cacheError) {
          // 缓存数据读取失败
        }
        
        this.loadError = true;
        uni.showToast({
          title: error.message || '加载失败，请检查网络连接',
          icon: 'error'
        });
      } finally {
        this.loading = false;
      }
    },

    retryLoad() {
      this.loadError = false;
      this.dataLoaded = false;
      this.loadIssueDetail();
    },
    
    // 检查当前用户是否为负责人
    checkIfResponsible() {
      if (this.issue && this.currentUserId) {
        const assignedToId = this.issue.assigned_to || this.issue.responsible_id;
        if (assignedToId === this.currentUserId) {
          this.currentUserRole = 'responsible';
        }
      }
    },
    
    // 检查整改提交状态
    checkSubmissionStatus() {
      if (this.issue) {
        // 检查历史记录中是否有提交检查的记录
        const hasSubmissionRecord = this.issue.history && this.issue.history.some(item => 
          item.action === '提交检查' || item.action === 'submit_for_inspection' || item.action === 'submit_rectification'
        );
        
        // 如果状态是pending_review且没有提交记录，说明刚刚提交
        const isJustCompleted = this.issue.status === 'pending_review';
        
        this.issueSubmittedForInspection = hasSubmissionRecord || isJustCompleted;
      }
    },
    
    // 格式化API返回的问题数据
    formatIssueData(data) {
      // 防护性检查，确保data存在
      if (!data || typeof data !== 'object') {
        throw new Error('问题数据格式错误');
      }
      
      return {
        id: data._id || data.id || this.issueId,
        number: data.issue_number || data.number || data.issue_id || this.issueId,
        title: data.title || data.issue_title || data.name || data.subject || '待确认问题',
        description: data.description || data.issue_description || '',
        status: data.status || data.issue_status || 'pending',
        location: data.location_info?.location_name || data.location_info?.name || data.location || data.location_name || '待确认',
        responsible: data.assigned_to_name || data.responsible || data.responsible_name || '未分配',
        role: data.assigned_to_role || data.role || data.responsible_role || '负责人',
        deadline: this.formatDate(data.expected_completion_date || data.deadline || data.due_date || data.target_date),
        priority: data.priority || data.priority_level || 'normal',
        createdAt: this.formatDate(data.created_at || data.createdAt || data.create_time),
        updatedAt: this.formatDate(data.updated_at || data.updatedAt || data.update_time),
        images: this.formatImages(data.photos || data.images || data.issue_photos || []),
        history: this.formatHistory(data.history || data.progress_logs || data.logs || []),
        // 整改相关数据
        rectification_description: data.rectification_description || '',
        rectification_photos: this.formatImages(data.rectification_photos || []),
        completed_at: data.completed_at,
        // 保存原始数据以备后用
        assigned_to: data.assigned_to,
        responsible_id: data.responsible_id
      };
    },
    
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '';
      try {
        // 处理iOS兼容性问题
        let normalizedDateStr = dateStr;
        if (typeof dateStr === 'string' && dateStr.includes('-') && !dateStr.includes('T')) {
          // 将 "YYYY-MM-DD" 转换为 "YYYY/MM/DD" 以兼容iOS
          normalizedDateStr = dateStr.replace(/-/g, '/');
        }
        
        const date = new Date(normalizedDateStr);
        if (isNaN(date.getTime())) {
          return dateStr;
        }
        
        // 返回 YYYY-MM-DD 格式，便于显示和计算
        return date.toISOString().split('T')[0];
      } catch (error) {
        return dateStr;
      }
    },
    
    // 格式化图片数据
    formatImages(images) {
      if (!Array.isArray(images)) return [];
      return images.map(img => {
        if (typeof img === 'string') return img;
        return img.url || img.cloudPath || img;
      }).filter(Boolean);
    },
    
    // 格式化历史记录
    formatHistory(history) {
      if (!Array.isArray(history)) return [];
      return history.map(item => ({
        action: item.action || item.type || '操作',
        description: item.description || item.content || '',
        time: this.formatDateTime(item.time || item.timestamp || item.created_at),
        operator: item.operator || item.user_name || item.created_by || '系统'
      }));
    },

    generateUnifiedTimeline() {
      const timeline = [];
      const status = this.issue.status;
      
      // 定义标准的时间轴步骤
      const standardSteps = [
        { key: 'create', title: '创建问题', action: '创建问题' },
        { key: 'assign', title: '分配负责人', action: '分配负责人' },
        { key: 'start', title: '开始整改', action: '开始整改' },
        { key: 'complete', title: '提交整改', action: '整改完成' },
        { key: 'inspect', title: '检查问题', action: '检查问题' },
        { key: 'approve', title: '检查通过', action: '检查通过' }
      ];
      
      // 为每个标准步骤生成时间轴项
      standardSteps.forEach(step => {
        let stepData = {
          title: step.title,
          description: '',
          time: '',
          operator: '',
          status: 'pending'
        };
        
        // 从历史记录中查找对应的步骤
        if (this.issue.history && this.issue.history.length > 0) {
          const historyItem = this.issue.history.find(h => h.action === step.action);
          if (historyItem) {
            stepData.description = historyItem.description;
            stepData.time = historyItem.time;
            stepData.operator = historyItem.operator;
            stepData.status = 'completed';
          }
        }
        
         // 根据问题状态确定步骤状态
         if (stepData.status !== 'completed') {
           // 如果历史记录中没有找到，根据当前状态推断
           if (step.key === 'create' || step.key === 'assign') {
             // 创建问题和分配负责人总是已完成的
             stepData.status = 'completed';
             stepData.description = step.key === 'create' ? '问题已创建并记录' : '负责人已分配';
             stepData.operator = step.key === 'create' ? '检查员' : '管理员';
             // 生成默认时间
             const baseDate = new Date(this.issue.createdAt || '2024-01-15');
             if (step.key === 'assign') {
               baseDate.setHours(baseDate.getHours() + 1); // 分配在创建1小时后
             }
             stepData.time = this.formatDateTime(baseDate);
           } else if (step.key === 'start') {
             if (status === 'pending' || status === 'assigned') {
               stepData.status = 'current';
               stepData.description = '等待负责人开始整改';
             } else if (status === 'reopened') {
               stepData.status = 'current';
               stepData.description = '问题已重新打开，等待开始整改';
             } else if (status === 'overdue') {
               // 逾期：在"开始整改"这一步卡住了
               stepData.status = 'overdue';
               stepData.description = '未能在截止时间前开始整改，问题已逾期';
             } else {
               stepData.status = 'completed';
               stepData.description = '负责人已开始整改';
               stepData.operator = this.issue.responsible || '负责人';
               stepData.time = this.formatDateTime(new Date(2024, 0, 16, 9, 30));
             }
           } else if (step.key === 'complete') {
             if (status === 'pending' || status === 'assigned') {
               stepData.status = 'pending';
               stepData.description = '等待开始整改';
             } else if (status === 'reopened') {
               stepData.status = 'pending';
               stepData.description = '等待重新开始整改';
             } else if (status === 'overdue') {
               // 逾期：后续步骤都无法执行
               stepData.status = 'pending';
               stepData.description = '因问题逾期，此步骤无法执行';
             } else if (status === 'in_progress' || status === 'active') {
               stepData.status = 'current';
               stepData.description = '负责人正在进行整改';
                         } else if (status === 'rejected') {
              stepData.status = 'current';
              stepData.description = '检查未通过，需要重新整改';
            } else if (status === 'pending_review') {
              stepData.status = 'completed';
              stepData.description = '整改工作已完成，等待检查';
              stepData.operator = this.issue.responsible || '负责人';
              stepData.time = this.formatDateTime(new Date());
            } else {
              stepData.status = 'completed';
              stepData.description = '整改工作已完成';
              stepData.operator = this.issue.responsible || '负责人';
              stepData.time = this.formatDateTime(new Date(2024, 0, 18, 15, 20));
            }
                     } else if (step.key === 'inspect') {
            if (status === 'pending' || status === 'assigned' || status === 'in_progress' || status === 'active') {
              stepData.status = 'pending';
              stepData.description = '等待整改完成';
            } else if (status === 'reopened') {
              stepData.status = 'pending';
              stepData.description = '等待重新整改完成';
            } else if (status === 'overdue') {
              // 逾期：后续步骤都无法执行
              stepData.status = 'pending';
              stepData.description = '因问题逾期，此步骤无法执行';
            } else if (status === 'pending_review') {
              stepData.status = 'current';
              stepData.description = '等待检查员检查整改效果';
            } else if (status === 'reviewing' || status === 'submitted') {
              stepData.status = 'current';
              stepData.description = '检查员正在检查';
            } else {
              // approved, resolved, verified等状态：检查已经开始并完成
              stepData.status = 'completed';
              stepData.description = '检查员已验收整改效果';
              stepData.operator = '检查员';
              stepData.time = this.formatDateTime(new Date(2024, 0, 19, 10, 15));
            }
                     } else if (step.key === 'approve') {
            if (status === 'pending' || status === 'assigned' || status === 'in_progress' || status === 'active') {
              stepData.status = 'pending';
              stepData.description = '等待检查完成';
            } else if (status === 'reopened') {
              stepData.status = 'pending';
              stepData.description = '等待重新检查完成';
            } else if (status === 'overdue') {
              // 逾期：后续步骤都无法执行
              stepData.status = 'pending';
              stepData.description = '因问题逾期，此步骤无法执行';
            } else if (status === 'pending_review') {
              stepData.status = 'current';
              stepData.description = '等待检查员确认';
            } else if (status === 'reviewing' || status === 'submitted') {
              stepData.status = 'pending';
              stepData.description = '等待检查完成';
            } else if (status === 'approved' || status === 'resolved' || status === 'verified') {
              stepData.status = 'completed';
              stepData.description = '检查员确认整改合格';
              stepData.operator = '检查员';
              stepData.time = this.formatDateTime(new Date(2024, 0, 19, 14, 30));
            } else {
              stepData.status = 'completed';
              stepData.description = '检查员确认整改合格';
              stepData.operator = '检查员';
              stepData.time = this.formatDateTime(new Date(2024, 0, 19, 14, 30));
            }
           }
         }
        
        timeline.push(stepData);
      });
      
      // 确保时间轴的逻辑连贯性
      this.validateTimelineLogic(timeline);
      this.unifiedTimeline = timeline;
    },

    // 验证和修正时间轴逻辑
    validateTimelineLogic(timeline) {
      const status = this.issue.status;
      let hasCurrentStep = false;
      
      // 根据状态确定应该到达的步骤和当前状态
      const statusStepMap = {
        'assigned': { targetStep: 1, currentStep: 1 },     // 已分配：刚完成分配，当前在"开始整改"
        'pending': { targetStep: 2, currentStep: 2 },      // 待整改：分配已完成，当前在"开始整改"
        'in_progress': { targetStep: 3, currentStep: 3 },  // 整改中：当前在"提交整改"
        // 移除 completed 状态，统一使用 pending_review
        'pending_review': { targetStep: 4, currentStep: 4 }, // 待检查：整改已完成，当前在"检查问题"
        'approved': { targetStep: 6, currentStep: -1 },    // 已通过：流程结束，但可重新打开
        'overdue': { targetStep: 2, currentStep: 2 },      // 逾期：卡在"开始整改"
        'rejected': { targetStep: 3, currentStep: 3 },     // 已驳回：回到"提交整改"
        'reopened': { targetStep: 2, currentStep: 2 },     // 重新打开：回到"开始整改"
        'reviewing': { targetStep: 4, currentStep: 4 },    // 审核中：当前在"检查问题"
        'submitted': { targetStep: 4, currentStep: 4 }     // 已提交：当前在"检查问题"
      };
      
      const statusMapping = statusStepMap[status] || { targetStep: 2, currentStep: 2 };
      
      timeline.forEach((step, index) => {
        if (index < statusMapping.targetStep) {
          // 前面的步骤应该已完成
          step.status = 'completed';
          step.time = step.time || this.generateStepTime(index);
          step.operator = step.operator || this.getDefaultOperator(index);
          step.description = step.description || this.getDefaultDescription(index, 'completed');
        } else if (statusMapping.currentStep === -1) {
          // 流程已结束（approved），所有步骤都应该完成
          step.status = 'completed';
          step.time = step.time || this.generateStepTime(index);
          step.operator = step.operator || this.getDefaultOperator(index);
          step.description = step.description || this.getDefaultDescription(index, 'completed');
        } else if (index === statusMapping.currentStep) {
          // 当前步骤
          if (status === 'overdue') {
            step.status = 'overdue';
            step.description = '未能在截止时间前完成，问题已逾期';
          } else if (status === 'rejected') {
            step.status = 'current';
            step.description = '检查未通过，需要重新整改';
          } else if (status === 'reopened') {
            step.status = 'current';
            step.description = '问题已重新打开，等待处理';
          } else {
            step.status = 'current';
            step.description = step.description || this.getDefaultDescription(index, 'current');
          }
        } else {
          // 后续步骤应该是待处理
          step.status = 'pending';
          step.time = '';
          step.operator = '';
          step.description = this.getDefaultDescription(index, 'pending');
        }
      });
    },

    // 生成步骤时间
    generateStepTime(stepIndex) {
      const baseTime = new Date(this.issue.createdAt || Date.now());
      const stepTime = new Date(baseTime.getTime() + stepIndex * 2 * 60 * 60 * 1000); // 每步间隔2小时
      return this.formatDateTime(stepTime);
    },

    // 获取默认操作者
    getDefaultOperator(stepIndex) {
      const operators = ['检查员', '管理员', this.issue.responsible || '负责人', this.issue.responsible || '负责人', '检查员', '检查员'];
      return operators[stepIndex] || '系统';
    },

    // 获取默认描述
    getDefaultDescription(stepIndex, stepStatus) {
      const stepKeys = ['create', 'assign', 'start', 'complete', 'inspect', 'approve'];
      const stepKey = stepKeys[stepIndex];
      
      // 获取关键信息用于更详细的描述
      const responsible = this.issue.responsible || '负责人';
      const deadline = this.issue.deadline ? `（截止：${this.formatDate(this.issue.deadline)}）` : '';
      
      const descriptions = {
        'create': {
          'completed': `检查员发现问题并记录在案${deadline ? '，已设定整改期限' : ''}`,
          'current': '正在创建问题记录',
          'pending': '等待创建问题'
        },
        'assign': {
          'completed': `已指派${responsible}负责整改${deadline}`,
          'current': '正在分配负责人',
          'pending': '等待分配负责人'
        },
        'start': {
          'completed': `${responsible}已接收任务并开始整改`,
          'current': `等待${responsible}开始整改${deadline}`,
          'pending': '等待开始整改',
          'overdue': `整改已逾期${deadline}，需要跟进处理`,
          'reopened': '问题已重新打开，需要重新开始整改'
        },
        'complete': {
          'completed': `${responsible}已完成整改并提交，等待检查员验收`,
          'current': `${responsible}正在进行整改工作${deadline}`,
          'pending': '等待负责人提交整改结果'
        },
        'inspect': {
          'completed': '检查员已开始现场检查整改效果',
          'current': '等待检查员安排现场检查',
          'pending': '等待检查验收'
        },
        'approve': {
          'completed': '整改效果符合要求，问题已彻底解决',
          'current': '检查员正在现场验收整改效果',
          'pending': '等待检查验收结果'
        }
      };
      
      return descriptions[stepKey]?.[stepStatus] || '处理中';
    },

    // 检查是否有历史记录
    hasHistoryRecord(stepIndex) {
      const stepKeys = ['create', 'assign', 'start', 'complete', 'inspect', 'approve'];
      const actions = ['创建问题', '分配负责人', '开始整改', '整改完成', '检查通过'];
      const targetAction = actions[stepIndex];
      
      return this.issue.history && this.issue.history.some(h => h.action === targetAction);
    },

    getStatusText(status) {
      const statusMap = {
        'pending': '待整改',
        'in_progress': '整改中',
        'pending_review': '待检查',
        'approved': '检查通过',
        'overdue': '逾期',
        'rejected': '已驳回',
        // 添加更多可能的状态值
        'open': '待处理',
        'assigned': '已分配',
        'resolved': '已解决',
        'reviewing': '审核中',
        'submitted': '已提交',
        'verified': '已验证',
        'cancelled': '已取消',
        'suspended': '已暂停',
        'reopened': '重新打开',
        'draft': '草稿',
        'new': '新建',
        'active': '进行中',
        'inactive': '非活跃',
        'expired': '已过期'
      }
      return statusMap[status] || (status ? `${status}(待定义)` : '待确认')
    },

    getPriorityText(priority) {
      if (!priority || priority === 'normal') return '一般问题';
      const priorityMap = {
        'normal': '一般问题',
        'urgent': '紧急问题'
      }
      return priorityMap[priority] || '一般问题'
    },

    // 获取问题编号显示
    getIssueNumber() {
      const number = this.issue?.number || this.issue?.issue_number || this.issueId;
      
      if (number && String(number).length > 0) {
        const numberStr = String(number);
        
        // 处理 YD 开头的长编号，提取后面的简短编号
        if (numberStr.startsWith('YD') && numberStr.length > 10) {
          // YD202508025008002 -> 只取最后3位序号
          const shortNumber = numberStr.slice(-3);
          return `YD${shortNumber}`;
        }
        
        // 处理其他长编号，保留前缀+后6位
        if (numberStr.length > 8) {
          const prefix = numberStr.substring(0, 2); // 取前2位作为前缀
          const suffix = numberStr.slice(-6); // 取后6位
          return `${prefix}${suffix}`;
        }
        
        // 短编号直接返回
        return numberStr;
      }
      
      // 默认编号：使用时间戳生成简单编号
      return String(Date.now()).slice(-8);
    },

    // 计算截止时间倒计时
    getDeadlineCountdown(deadline) {
      if (!deadline) return '';
      
      try {
        // 直接使用原始日期字符串，Date构造函数会自动处理
        const deadlineDate = new Date(deadline);
        const currentDate = new Date();
        
        // 检查日期是否有效
        if (isNaN(deadlineDate.getTime())) {
          // 尝试替换格式再解析一次
          const normalizedDate = new Date(deadline.replace(/-/g, '/'));
          if (isNaN(normalizedDate.getTime())) {
            return '';
          }
          deadlineDate.setTime(normalizedDate.getTime());
        }
        
        // 只比较日期，忽略时间
        const deadlineDay = new Date(deadlineDate.getFullYear(), deadlineDate.getMonth(), deadlineDate.getDate());
        const currentDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());
        
        const timeDiff = deadlineDay.getTime() - currentDay.getTime();
        const daysDiff = Math.round(timeDiff / (1000 * 60 * 60 * 24));
        
        if (daysDiff < 0) {
          return `已逾期${Math.abs(daysDiff)}天`;
        } else if (daysDiff === 0) {
          return '今日到期';
        } else if (daysDiff === 1) {
          return '明日到期';
        } else {
          return `${daysDiff}天后到期`;
        }
      } catch (error) {
        return '';
      }
    },

    // 获取倒计时样式类
    getCountdownClass(deadline) {
      if (!deadline) return '';
      
      try {
        // 使用与 getDeadlineCountdown 相同的逻辑
        const deadlineDate = new Date(deadline);
        const currentDate = new Date();
        
        // 检查日期是否有效
        if (isNaN(deadlineDate.getTime())) {
          const normalizedDate = new Date(deadline.replace(/-/g, '/'));
          if (isNaN(normalizedDate.getTime())) {
            return 'normal';
          }
          deadlineDate.setTime(normalizedDate.getTime());
        }
        
        // 只比较日期，忽略时间
        const deadlineDay = new Date(deadlineDate.getFullYear(), deadlineDate.getMonth(), deadlineDate.getDate());
        const currentDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());
        
        const timeDiff = deadlineDay.getTime() - currentDay.getTime();
        const daysDiff = Math.round(timeDiff / (1000 * 60 * 60 * 24));
        
        if (daysDiff < 0) {
          return 'overdue'; // 已逾期
        } else if (daysDiff <= 1) {
          return 'urgent'; // 紧急（今天或明天）
        } else if (daysDiff <= 3) {
          return 'warning'; // 警告（3天内）
        } else {
          return 'normal'; // 正常
        }
      } catch (error) {
        return 'normal';
      }
    },

    // 获取按钮文字
    getButtonText() {
      if (!this.issue) return '操作';
      
      const status = this.issue.status;
      if (this.currentUserRole === 'responsible') {
        if (status === 'assigned' || status === 'pending') {
          return '开始整改';
        } else if (status === 'rejected') {
          return '重新整改';
        } else if (status === 'in_progress' && !this.issueSubmittedForInspection) {
          return '继续整改';
        }
      } else if (this.currentUserRole === 'admin') {
        // 管理员根据状态显示不同操作
        if (status === 'assigned' || status === 'pending') {
          return '更改状态';
        } else if (status === 'in_progress') {
          return '更改状态';
        } else if (status === 'pending_review') {
          return '检查问题';
        } else if (status === 'approved') {
          return '重新打开';
        } else if (this.issueSubmittedForInspection) {
          return '检查问题';
        } else {
          return '更改状态';
        }
      } else if (this.currentUserRole === 'inspector') {
        if (this.issueSubmittedForInspection) {
          return '检查问题';
        } else if (status === 'pending_review') {
          return '开始检查';
        }
      }
      return '操作';
    },

    // 处理主要操作
    handleMainAction() {
      if (!this.issue) return;
      
      const status = this.issue.status;
      if (this.currentUserRole === 'responsible') {
        if (status === 'assigned' || status === 'pending') {
          this.startRectification();
        } else if (status === 'rejected') {
          this.continueRectification(); // 被驳回后继续整改
        } else if (status === 'in_progress') {
          this.continueRectification();
        }
      } else if (this.currentUserRole === 'admin') {
        // 管理员操作：根据状态执行不同操作
        if (status === 'assigned' || status === 'pending') {
          this.adminManageIssue();
        } else if (status === 'in_progress') {
          this.adminManageIssue(); // 整改中状态也用管理问题菜单
        } else if (status === 'pending_review') {
          this.adminAcceptIssue();
        } else if (this.issueSubmittedForInspection) {
          // 开始检查功能已移除，直接进行状态更改
          this.changeIssueStatus();
        } else {
          this.adminManageIssue();
        }
      } else if (this.currentUserRole === 'inspector') {
        // 开始检查功能已移除，直接进行状态更改
        this.changeIssueStatus();
      }
    },

    // 开始整改
    async startRectification() {
      try {
        const { callCloudFunction } = require('@/utils/auth.js');
        
        // 更新问题状态为整改中
        const result = await callCloudFunction('hygiene-monthly-inspection', {
          action: 'updateMonthlyIssue',
          data: {
            issue_id: this.issueId,
            status: 'in_progress',
            action_type: 'start_rectification'
          }
        });
        
        if (result && result.success) {
          uni.navigateTo({
            url: `/pages/6s_pkg/rectification-submit?issueId=${this.issueId}`
          });
        } else {
          throw new Error(result?.message || '开始整改失败');
        }
      } catch (error) {
        uni.showToast({
          title: error.message || '操作失败',
          icon: 'error'
        });
      }
    },

    // 继续整改
    continueRectification() {
      uni.navigateTo({
        url: `/pages/6s_pkg/rectification-submit?issueId=${this.issueId}`
      });
    },



    // 管理员管理问题 - 直接进行状态操作
    adminManageIssue() {
      this.changeIssueStatus();
    },

    // 管理员检查进度
    adminCheckProgress() {
      uni.showActionSheet({
        itemList: ['更改状态', '联系负责人'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.changeIssueStatus();
              break;
            case 1:
              // 联系负责人功能
              uni.showToast({
                title: '功能开发中',
                icon: 'none'
              });
              break;
          }
        }
      });
    },

    // 管理员检查问题
    adminAcceptIssue() {
      uni.showActionSheet({
        itemList: ['检查通过', '重新整改'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.approveIssue();
              break;
            case 1:
              this.rejectIssue();
              break;
          }
        }
      });
    },

    // 检查通过
    async approveIssue() {
      uni.showModal({
        title: '检查通过',
        content: '确定检查通过此问题吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              const { callCloudFunction } = require('@/utils/auth.js');
              const result = await callCloudFunction('hygiene-monthly-inspection', {
                action: 'updateMonthlyIssue',
                data: {
                  issue_id: this.issueId,
                  status: 'approved',
                  action_type: 'approve'
                }
              });
              
              if (result && result.success) {
                this.issue.status = 'approved';
                this.generateUnifiedTimeline();
                uni.showToast({
                  title: '检查通过',
                  icon: 'success'
                });
                
                // 通知其他页面数据已更新
                uni.$emit('monthlyIssueUpdated', {
                  action: 'approve',
                  issueId: this.issueId,
                  issueData: this.issue
                });
              }
            } catch (error) {
              uni.showToast({
                title: '操作失败',
                icon: 'error'
              });
            }
          }
        }
      });
    },

    // 重新整改
    async rejectIssue() {
      uni.showModal({
        title: '重新整改',
        content: '确定要求重新整改此问题吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              const { callCloudFunction } = require('@/utils/auth.js');
              const result = await callCloudFunction('hygiene-monthly-inspection', {
                action: 'updateMonthlyIssue',
                data: {
                  issue_id: this.issueId,
                  status: 'rejected',
                  action_type: 'reject'
                }
              });
              
              if (result && result.success) {
                this.issue.status = 'rejected';
                this.generateUnifiedTimeline();
                uni.showToast({
                  title: '已要求重新整改',
                  icon: 'success'
                });
                
                // 通知其他页面数据已更新
                uni.$emit('monthlyIssueUpdated', {
                  action: 'reject',
                  issueId: this.issueId,
                  issueData: this.issue
                });
              }
            } catch (error) {
              uni.showToast({
                title: '操作失败',
                icon: 'error'
              });
            }
          }
        }
      });
    },

    // 更改问题状态
    changeIssueStatus() {
      // 根据当前状态和逻辑确定可选择的状态
      let statusOptions = [];
      
      const currentStatus = this.issue.status;
      const hasResponsible = this.issue.responsible || this.issue.assigned_to;
      
      // 如果没有负责人，只能选择分配相关的状态
      if (!hasResponsible) {
        statusOptions = [
          { value: 'assigned', label: '已分配' }
        ];
      } else {
        // 有负责人的情况下，根据当前状态提供合理的选项
        if (currentStatus === 'assigned') {
          statusOptions = [
            { value: 'pending', label: '待整改' },
            { value: 'in_progress', label: '整改中' },
            { value: 'pending_review', label: '待检查' }
          ];
        } else if (currentStatus === 'pending') {
          // 待整改状态，不再提供"已分配"选项，因为负责人已经分配了
          statusOptions = [
            { value: 'in_progress', label: '整改中' },
            { value: 'pending_review', label: '待检查' }
          ];
        } else if (currentStatus === 'in_progress') {
          statusOptions = [
            { value: 'pending_review', label: '待检查' },
            { value: 'pending', label: '待整改' } // 允许回退
          ];
        } else if (currentStatus === 'pending_review') {
          statusOptions = [
            { value: 'approved', label: '检查通过' },
            { value: 'rejected', label: '重新整改' }
          ];
        } else if (currentStatus === 'approved') {
          // 检查通过后，可以重新打开或发现新问题
          statusOptions = [
            { value: 'pending', label: '重新打开' },
            { value: 'rejected', label: '发现新问题，重新整改' }
          ];
        } else {
          // 其他状态提供基础选项（移除已关闭，检查通过就是自然结束）
          statusOptions = [
            { value: 'pending', label: '待整改' },
            { value: 'in_progress', label: '整改中' },
            { value: 'pending_review', label: '待检查' },
            { value: 'approved', label: '检查通过' }
            // 移除 'closed' 和 'completed'，统一使用 pending_review
          ];
        }
      }
      
      uni.showActionSheet({
        itemList: statusOptions.map(opt => opt.label),
        success: async (res) => {
          const selectedStatus = statusOptions[res.tapIndex];
          if (selectedStatus && selectedStatus.value !== this.issue.status) {
            await this.updateIssueStatus(selectedStatus.value);
          }
        }
      });
    },

    // 更新问题状态
    async updateIssueStatus(newStatus) {
      try {
        const { callCloudFunction } = require('@/utils/auth.js');
        
        // 处理特殊状态映射
        let actualStatus = newStatus;
        if (newStatus === 'rejected') {
          actualStatus = 'rejected'; // 重新整改状态
        }
        
        // 使用updateMonthlyIssue操作来更新状态
        const result = await callCloudFunction('hygiene-monthly-inspection', {
          action: 'updateMonthlyIssue',
          data: {
            issue_id: this.issueId,
            status: actualStatus,
            action_type: 'admin_update'
          }
        });
        
        if (result && result.success) {
          this.issue.status = newStatus;
          this.generateUnifiedTimeline();
          uni.showToast({
            title: '状态更新成功',
            icon: 'success'
          });
          
          // 通知其他页面数据已更新
          uni.$emit('monthlyIssueUpdated', {
            action: 'update_status',
            issueId: this.issueId,
            newStatus: newStatus,
            issueData: this.issue
          });
        } else {
          throw new Error(result?.message || result?.error || '状态更新失败');
        }
      } catch (error) {
        console.error('更新问题状态失败:', error);
        uni.showToast({
          title: error.message || '更新失败',
          icon: 'error'
        });
      }
    },



    goToRectificationPage() {
      uni.navigateTo({
        url: `/pages/6s_pkg/rectification-submit?issueId=${this.issueId}`
      });
    },
    


    previewImage(index) {
      uni.previewImage({
        urls: this.issue.images,
        current: index
      })
    },
    
    // 预览整改照片
    previewRectificationImage(index) {
      const urls = this.issue.rectification_photos.map(photo => photo.url || photo);
      uni.previewImage({
        urls: urls,
        current: index
      })
    },

    // 重新打开问题
    async reopenIssue() {
      uni.showModal({
        title: '确认重新打开',
        content: '确定要重新打开这个问题吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              const { callCloudFunction } = require('@/utils/auth.js');
              
              const result = await callCloudFunction('hygiene-monthly-inspection', {
                action: 'updateMonthlyIssue',
                data: {
                  issue_id: this.issueId,
                  status: 'in_progress',
                  action_type: 'reopen'
                }
              });
              
              if (result && result.success) {
                this.issue.status = 'in_progress';
                this.generateUnifiedTimeline();
                uni.showToast({
                  title: '已重新打开',
                  icon: 'success'
                });
                
                // 通知其他页面数据已更新
                uni.$emit('monthlyIssueUpdated', {
                  action: 'reopen',
                  issueId: this.issueId,
                  issueData: this.issue
                });
              } else {
                throw new Error(result?.message || '重新打开失败');
              }
            } catch (error) {
              uni.showToast({
                title: error.message || '操作失败',
                icon: 'error'
              });
            }
          }
        }
      })
    },

    editIssue() {
      // 编辑已发布的问题，传递问题数据到 issue-add 页面
      const editData = {
        title: this.issue.title,
        description: this.issue.description,
        location: this.issue.location,
        deadline: this.issue.deadline,
        responsible: this.issue.responsible,
        responsible_id: this.issue.assigned_to || this.issue.responsible_id, // 传递负责人ID
        priority: this.issue.priority,
        images: this.issue.images || []
      };
      
      uni.navigateTo({
        url: `/pages/6s_pkg/issue-add?editId=${this.issueId}&editData=${encodeURIComponent(JSON.stringify(editData))}`
      });
    },

    formatDateTime(dateInput) {
      if (!dateInput) return '';
      try {
        let date;
        if (typeof dateInput === 'string') {
          // 处理iOS兼容性问题：将 "YYYY-MM-DD HH:mm" 格式转换为 "YYYY/MM/DD HH:mm:ss"
          let normalizedDateStr = dateInput;
          if (dateInput.includes('-') && dateInput.includes(' ')) {
            // "2025-08-16 09:57" -> "2025/08/16 09:57:00"
            normalizedDateStr = dateInput.replace(/-/g, '/');
            if (!normalizedDateStr.includes(':00', normalizedDateStr.length - 3)) {
              normalizedDateStr += ':00';
            }
          } else if (dateInput.includes('-') && !dateInput.includes('T')) {
            // "2025-08-16" -> "2025/08/16"
            normalizedDateStr = dateInput.replace(/-/g, '/');
          }
          date = new Date(normalizedDateStr);
        } else {
          date = dateInput;
        }
        
        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          return dateInput?.toString() || '';
        }
        
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        return dateInput?.toString() || '';
      }
    },
    
    // 处理问题更新事件
    handleIssueUpdated(eventData) {
      console.log('issue-detail 收到更新事件:', eventData);
      
      // 检查是否是当前问题的更新
      if (eventData && eventData.issueId && eventData.issueId === this.issueId) {
        console.log('当前问题需要更新，开始刷新数据...');
        
        // 根据不同的操作类型进行相应的处理
        if (eventData.action === 'submit_rectification') {
          // 整改提交：更新状态为待检查
          if (this.issue) {
            this.issue.status = eventData.status || 'pending_review';
            
            // 添加整改提交的历史记录（如果不存在）
            if (!this.issue.history) {
              this.issue.history = [];
            }
            
            const hasSubmitRecord = this.issue.history.some(item => 
              item.action === 'submit_rectification' || item.action === '整改完成'
            );
            
            if (!hasSubmitRecord) {
              this.issue.history.push({
                action: 'submit_rectification',
                description: '负责人已完成整改并提交检查',
                time: this.formatDateTime(new Date()),
                operator: this.issue.responsible || '负责人'
              });
            }
            
            this.checkSubmissionStatus(); // 重新检查提交状态
            this.generateUnifiedTimeline(); // 重新生成时间轴
            
            // 显示成功提示
            uni.showToast({
              title: '整改已提交',
              icon: 'success',
              duration: 2000
            });
          }
        } else {
          // 其他更新：重新加载完整数据
          this.loadIssueDetail();
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
	min-height: 100vh;
	background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
	width: 100%;
	overflow-x: hidden; /* 防止水平滚动 */
	box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
  padding-bottom: 160rpx;
}

.content-loading,
.content-error {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f0f4f8;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
}

.content {
  padding: 32rpx;
}

/* 卡片通用样式 - 统一卡片样式 */
.status-card,
.description-card,
.images-card,
.rectification-card,
.timeline-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08), 
              0 2rpx 6rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  /* 性能优化 */
  transform: translateZ(0);
  will-change: transform;
}

/* 状态卡片 */
.status-card {
  padding: 0;
  overflow: hidden;
}

/* 顶部状态栏 */
.status-bar {
  background: #FFFFFF;
  padding: 24rpx 32rpx 20rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #F0F0F0;
}

.issue-id {
  font-size: 26rpx;
  font-weight: 700;
  color: #007AFF;
  font-family: 'Monaco', 'Menlo', monospace;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 600;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.08);

  &.status-pending {
    background: linear-gradient(135deg, #E6F3FF, #F0F7FF);
    color: #007AFF;
    border: 2rpx solid rgba(0, 122, 255, 0.2);
  }

  &.status-in_progress {
    background: linear-gradient(135deg, #FFF4E6, #FFFBF0);
    color: #FF9500;
    border: 2rpx solid rgba(255, 149, 0, 0.2);
  }



  &.status-pending_review {
    background: linear-gradient(135deg, #F0EFFF, #F8F7FF);
    color: #5856D6;
    border: 2rpx solid rgba(88, 86, 214, 0.2);
  }

  &.status-approved {
    background: linear-gradient(135deg, #E8F5E8, #F0FFF0);
    color: #34C759;
    border: 2rpx solid rgba(52, 199, 89, 0.2);
  }

  &.status-overdue {
    background: linear-gradient(135deg, #FFE6E6, #FFF0F0);
    color: #FF3B30;
    border: 2rpx solid rgba(255, 59, 48, 0.2);
  }

  &.status-rejected {
    background: linear-gradient(135deg, #F2F2F7, #F8F8F8);
    color: #8E8E93;
    border: 2rpx solid rgba(142, 142, 147, 0.2);
  }

  // 兼容性状态映射到8色方案
  &.status-open {
    background: linear-gradient(135deg, #E6F3FF, #F0F7FF);
    color: #007AFF;
    border: 2rpx solid rgba(0, 122, 255, 0.2);
  }

  &.status-assigned {
    background: linear-gradient(135deg, #ECFEFF, #F0FDFF);
    color: #0891B2;
    border: 2rpx solid rgba(8, 145, 178, 0.2);
  }

  &.status-resolved {
    background: linear-gradient(135deg, #DCFCE7, #F0FDF4);
    color: #16A34A;
    border: 2rpx solid rgba(22, 163, 74, 0.2);
  }

  &.status-reviewing {
    background: linear-gradient(135deg, #FFF4E6, #FFFBF0);
    color: #FF9500;
    border: 2rpx solid rgba(255, 149, 0, 0.2);
  }

  &.status-submitted {
    background: linear-gradient(135deg, #F0EFFF, #F8F7FF);
    color: #5856D6;
    border: 2rpx solid rgba(88, 86, 214, 0.2);
  }

  &.status-verified {
    background: linear-gradient(135deg, #DCFCE7, #F0FDF4);
    color: #16A34A;
    border: 2rpx solid rgba(22, 163, 74, 0.2);
  }

  &.status-cancelled {
    background: linear-gradient(135deg, #F2F2F7, #F8F8F8);
    color: #8E8E93;
    border: 2rpx solid rgba(142, 142, 147, 0.2);
  }

  &.status-suspended {
    background: linear-gradient(135deg, #F2F2F7, #F8F8F8);
    color: #8E8E93;
    border: 2rpx solid rgba(142, 142, 147, 0.2);
  }

  &.status-reopened {
    background: linear-gradient(135deg, #FFF4E6, #FFFBF0);
    color: #FF9500;
    border: 2rpx solid rgba(255, 149, 0, 0.2);
  }

  &.status-draft {
    background: linear-gradient(135deg, #F2F2F7, #F8F8F8);
    color: #8E8E93;
    border: 2rpx solid rgba(142, 142, 147, 0.2);
  }

  &.status-new {
    background: linear-gradient(135deg, #ECFEFF, #F0FDFF);
    color: #0891B2;
    border: 2rpx solid rgba(8, 145, 178, 0.2);
  }

  &.status-active {
    background: linear-gradient(135deg, #FFF4E6, #FFFBF0);
    color: #FF9500;
    border: 2rpx solid rgba(255, 149, 0, 0.2);
  }

  &.status-inactive {
    background: linear-gradient(135deg, #F2F2F7, #F8F8F8);
    color: #8E8E93;
    border: 2rpx solid rgba(142, 142, 147, 0.2);
  }

  &.status-expired {
    background: linear-gradient(135deg, #FFE6E6, #FFF0F0);
    color: #FF3B30;
    border: 2rpx solid rgba(255, 59, 48, 0.2);
  }
}

/* 问题标题 */
.issue-title {
  font-size: 38rpx;
  font-weight: 700;
  color: #1C1C1E;
  line-height: 1.3;
  padding: 32rpx 32rpx 24rpx 32rpx;
}

/* 关键信息网格 */
.key-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  padding: 0 32rpx 24rpx 32rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #8E8E93;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.info-value-with-badge {
  display: flex;
  align-items: center;
  gap: 12rpx;
  min-height: 44rpx;
}

.main-value {
  font-size: 28rpx;
  color: #1C1C1E;
  font-weight: 600;
  line-height: 1.2;
  display: flex;
  align-items: center;
}

.role-badge {
  background: #F0F9FF;
  color: #0284C7;
  font-size: 22rpx;
  font-weight: 500;
  padding: 8rpx 10rpx;
  border-radius: 8rpx;
  line-height: 1;
  display: flex;
  align-items: center;
  height: 32rpx;
}

.countdown-badge {
  font-size: 22rpx;
  font-weight: 500;
  padding: 8rpx 10rpx;
  border-radius: 8rpx;
  line-height: 1;
  display: flex;
  align-items: center;
  height: 32rpx;
  
  &.overdue {
    background: #FEF2F2;
    color: #DC2626;
  }
  
  &.urgent {
    background: #FFF7ED;
    color: #EA580C;
  }
  
  &.warning {
    background: #FFFBEB;
    color: #D97706;
  }
  
  &.normal {
    background: #F0F9FF;
    color: #0284C7;
  }
}

/* 位置信息 */
.location-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 32rpx 32rpx 32rpx;
  background: #FFFFFF;
  border-top: 1rpx solid #F0F0F0;
}

.location-section {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #007AFF;
}

.priority-badge {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 22rpx;
  font-weight: 500;
  padding: 6rpx 10rpx;
  border-radius: 8rpx;
  
  &.priority-normal {
    background: #F8FAFC;
    color: #64748B;
  }
  
  &.priority-urgent {
    background: #FFF0F0;
    color: #DC2626;
  }
}

.priority-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: currentColor;
  opacity: 0.9;
}

/* 描述卡片 */
.description-card {
  padding: 32rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 24rpx;
}

.description-text {
  font-size: 28rpx;
  color: #1C1C1E;
  line-height: 1.6;
}

/* 图片卡片 */
.images-card {
  padding: 32rpx;
}

/* 整改内容卡片 */
.rectification-card {
  padding: 32rpx;
}

.rectification-description,
.rectification-images,
.rectification-time {
  margin-bottom: 24rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.rectification-label {
  font-size: 26rpx;
  font-weight: 600;
  color: #16a34a;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  
  &::before {
    content: '';
    width: 6rpx;
    height: 6rpx;
    border-radius: 50%;
    background: #16a34a;
  }
}

.rectification-text {
  font-size: 28rpx;
  color: #1C1C1E;
  line-height: 1.6;
  background: rgba(255, 255, 255, 0.8);
  padding: 20rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(34, 197, 94, 0.1);
}

.rectification-time-text {
  font-size: 28rpx;
  color: #1C1C1E;
  line-height: 1.6;
  background: rgba(255, 255, 255, 0.8);
  padding: 20rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(34, 197, 94, 0.1);
}

.rectification-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.image-item {
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
  background: #F5F5F5;
  transition: transform 0.2s ease;
  cursor: pointer;
  
  &:active {
    transform: scale(0.98);
  }
}

.issue-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}

/* 时间轴卡片 */
.timeline-card {
  padding: 32rpx;
  margin-bottom: 40rpx; /* 增加与底部的间距 */
}

.unified-timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
  margin-bottom: 40rpx;
  position: relative;
  padding: 16rpx 0;

  &:last-child {
    margin-bottom: 0;
  }

  &:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 31rpx;
    top: 72rpx;
    bottom: -56rpx;
    width: 3rpx;
    background: #E5E5E5;
    border-radius: 2rpx;
  }

  &.completed::after {
    background: linear-gradient(180deg, #34C759 0%, #30D158 100%);
  }
  
  &.current::after {
    background: linear-gradient(180deg, #FF9500 0%, #FF9F0A 100%);
  }
  
  &.overdue::after {
    background: linear-gradient(180deg, #FF3B30 0%, #FF453A 100%);
  }
}

.timeline-dot {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  background: #E5E5E5;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  z-index: 1;
  position: relative;
  border: 4rpx solid #FFFFFF;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.timeline-item.completed .timeline-dot {
  background: linear-gradient(135deg, #34C759 0%, #30D158 100%);
}

.timeline-item.current .timeline-dot {
  background: linear-gradient(135deg, #FF9500 0%, #FF9F0A 100%);
  animation: pulse 2s infinite;
}

.timeline-item.pending .timeline-dot {
  background: linear-gradient(135deg, #E5E5E5 0%, #D1D1D6 100%);
}

.timeline-item.overdue .timeline-dot {
  background: linear-gradient(135deg, #FF3B30 0%, #FF453A 100%);
}

.dot-placeholder {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #FFFFFF;
  opacity: 0.8;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

.timeline-content {
  flex: 1;
  padding: 16rpx 20rpx;
  background: linear-gradient(135deg, rgba(248, 249, 250, 0.9), rgba(255, 255, 255, 0.8));
  border-radius: 16rpx;
  border: 2rpx solid rgba(240, 240, 247, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(5rpx);
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.step-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #1C1C1E;
  flex: 1;
}

.timeline-item.pending .step-title {
  color: #8E8E93;
}

.timeline-item.completed .timeline-content {
  background: linear-gradient(135deg, rgba(52, 199, 89, 0.1), rgba(48, 209, 88, 0.08));
  border-color: rgba(52, 199, 89, 0.3);
}

.timeline-item.current .timeline-content {
  background: linear-gradient(135deg, rgba(255, 149, 0, 0.1), rgba(255, 159, 10, 0.08));
  border-color: rgba(255, 149, 0, 0.3);
}

.timeline-item.pending .timeline-content {
  background: linear-gradient(135deg, rgba(229, 229, 229, 0.4), rgba(209, 209, 214, 0.3));
  border-color: rgba(229, 229, 229, 0.6);
}

.timeline-item.overdue .timeline-content {
  background: linear-gradient(135deg, rgba(255, 59, 48, 0.1), rgba(255, 69, 58, 0.08));
  border-color: rgba(255, 59, 48, 0.3);
}

.timeline-item.overdue .step-title {
  color: #FF3B30;
}

.timeline-item.overdue .step-desc {
  color: #FF3B30;
  font-weight: 500;
}

.step-time {
  font-size: 20rpx;
  color: #8E8E93;
  margin-left: 16rpx;
  flex-shrink: 0;
  white-space: nowrap;
}

.step-desc {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 4rpx;
  line-height: 1.4;
}

.timeline-item.pending .step-desc {
  color: #C7C7CC;
}

.step-operator {
  font-size: 20rpx;
  color: #007AFF;
  background: rgba(0, 122, 255, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
  display: inline-block;
  margin-top: 4rpx;
}



/* 操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 32rpx 32rpx 32rpx;
  margin: 0rpx 32rpx 32rpx 32rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(229, 229, 229, 0.5);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  border-radius: 24rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  z-index: 999;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &.primary {
    background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
    color: #ffffff;
    border: 1rpx solid rgba(90, 200, 250, 0.3);
    box-shadow: 0 12rpx 32rpx rgba(0, 122, 255, 0.4),
                0 8rpx 16rpx rgba(0, 122, 255, 0.3),
                0 4rpx 8rpx rgba(0, 122, 255, 0.2),
                inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
  }

  &.primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
  }

  &.primary:active::before {
    left: 100%;
  }

  &.primary:active {
    transform: translateY(4rpx);
    box-shadow: 0 6rpx 16rpx rgba(0, 122, 255, 0.3),
                0 3rpx 8rpx rgba(0, 122, 255, 0.2),
                inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);
  }

  &.secondary {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    color: #1C1C1E;
    border: 2rpx solid rgba(0, 122, 255, 0.25);
    backdrop-filter: blur(15rpx);
    box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.15),
                0 4rpx 12rpx rgba(0, 122, 255, 0.12),
                0 2rpx 6rpx rgba(0, 122, 255, 0.08),
                inset 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
  }

  &.full {
    flex: none;
    width: 100%;
  }
}

/* 底部安全间距 */
.bottom-safe-area {
  height: 40rpx;
}

/* H5平台适配 - 仅优化图片显示大小 */
/* #ifdef H5 */
@media screen and (min-width: 768px) {
  .image-item {
    max-width: 200rpx;
    max-height: 200rpx;
  }
  
  .issue-image {
    max-width: 200rpx;
    max-height: 200rpx;
  }
}
/* #endif */


</style> 