<template>
  <view class="page-container">
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-title">公共责任区管理</view>
      <view class="header-subtitle">管理需要轮班清洁的公共区域</view>
    </view>

    <!-- 统计卡片 -->
    <view class="stats-card">
      <!-- 加载状态 -->
      <view v-if="loading" class="stats-loading">
        <view class="loading-content">
          <uni-icons type="spinner-cycle" size="40" color="#007AFF"></uni-icons>
          <text class="loading-text">加载统计数据中...</text>
        </view>
      </view>
      <!-- 正常统计 -->
      <template v-else>
        <view class="stat-item">
          <view class="stat-number">{{ areaList.length }}</view>
          <view class="stat-label">总公共区</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{ enabledAreas.length }}</view>
          <view class="stat-label">启用中</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{ scheduledAreas.length }}</view>
          <view class="stat-label">已排班</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{ todayAreas.length }}</view>
          <view class="stat-label">今日清扫</view>
        </view>
      </template>
    </view>

    <!-- 操作按钮组 -->
    <view class="action-bar">
      <button class="action-btn primary" @click="showAddArea">
        <uni-icons type="plus" size="16" color="#FFFFFF" />
        <text>新增公共区</text>
      </button>
    </view>

    <!-- 公共区列表 -->
    <!-- 加载状态 -->
    <view v-if="loading" class="list-loading">
      <view class="loading-content">
        <uni-icons type="spinner-cycle" size="40" color="#007AFF"></uni-icons>
        <text class="loading-text">加载公共区数据中...</text>
      </view>
    </view>
    
    <!-- 正常列表 -->
    <view v-else-if="areaList.length > 0" class="area-list">
      <view 
        class="area-item"
        v-for="(area, index) in areaList" 
        :key="index"
      >
        <view class="area-main">
          <view class="area-header">
            <view class="area-name">{{ area.name }}</view>
            <view class="area-status" :class="[`status-${area.status}`]">
              {{ getStatusText(area.status) }}
            </view>
          </view>
          <view class="area-info">
            <view class="info-item">
              <uni-icons type="location" size="14" color="#8E8E93" />
              <text>{{ (area.location && area.location.area) || area.location || '未设置位置' }}</text>
            </view>
            <view class="info-item">
              <uni-icons type="calendar" size="14" color="#8E8E93" />
              <text v-if="hasValidSchedule(area)">每{{ getWeekDayText(area.scheduled_day) }}清扫</text>
              <text v-else class="unscheduled-text">未设置清扫日程</text>
            </view>
          </view>
          <view class="area-description" v-if="area.description">
            {{ area.description }}
          </view>
          <view class="area-schedule">
            <view v-if="hasValidSchedule(area)" class="schedule-info">
              <view class="next-clean">
                <text class="next-label">下次清扫：</text>
                <text class="next-date" :class="{ 'today': isToday(area.next_clean_date) }">
                  {{ formatDate(area.next_clean_date) }}
                </text>
              </view>
              <view class="last-clean" v-if="area.last_clean_date">
                <text class="last-label">上次完成：</text>
                <text class="last-date">{{ formatDate(area.last_clean_date) }}</text>
              </view>
            </view>
            <view v-else class="no-schedule-info">
              <uni-icons type="calendar" size="16" color="#C7C7CC" />
              <text>暂未设置清扫日程</text>
            </view>
          </view>
        </view>
        <view class="area-actions">
          <button class="action-icon-btn schedule" @click.stop="setSchedule(area)">
            <uni-icons type="calendar" size="16" color="#34C759" />
          </button>
          <button class="action-icon-btn edit" @click.stop="editArea(area)">
            <uni-icons type="compose" size="16" color="#007AFF" />
          </button>
          <button class="action-icon-btn delete" @click.stop="deleteArea(area)">
            <uni-icons type="trash" size="16" color="#FF3B30" />
          </button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <p-empty-state
      v-else-if="!loading && areaList.length === 0"
      type="area"
      text="暂无公共责任区"
      description="点击上方按钮创建第一个公共责任区"
    ></p-empty-state>

    <!-- 新增/编辑弹窗 -->
    <uni-popup ref="areaFormPopup" type="center" :mask-click="false">
      <view class="form-popup">
        <view class="form-header">
          <view class="form-title">{{ isEditing ? '编辑' : '新增' }}公共责任区</view>
          <button class="close-btn" @click="closeForm">
            <uni-icons type="close" size="20" color="#8E8E93" />
          </button>
        </view>
        
        <view class="form-content">
          <view class="form-item">
            <text class="form-label required">责任区名称</text>
            <input 
              class="form-input" 
              type="text" 
              v-model="formData.name"
              placeholder="请输入公共责任区名称"
              maxlength="50"
            />
          </view>
          
          <view class="form-item">
            <text class="form-label">所在位置</text>
            <input 
              class="form-input" 
              type="text" 
              v-model="formData.location"
              placeholder="请输入具体位置"
              maxlength="100"
            />
          </view>
          
          <view class="form-item">
            <text class="form-label">责任区描述</text>
            <textarea 
              class="form-textarea" 
              v-model="formData.description"
              placeholder="请输入责任区详细描述"
              maxlength="200"
              :show-count="true"
            ></textarea>
          </view>
          
          <view class="form-item">
            <text class="form-label">状态</text>
            <picker 
              :range="statusOptions" 
              range-key="label" 
              :value="statusIndex"
              @change="onStatusChange"
              class="form-picker"
            >
              <view class="picker-value">
                <text>{{ statusOptions[statusIndex].label }}</text>
                <uni-icons type="right" size="16" color="#C7C7CC" />
              </view>
            </picker>
          </view>
        </view>
        
        <view class="form-footer">
          <button class="form-btn cancel" @click="closeForm">取消</button>
          <button class="form-btn submit" @click="submitForm" :loading="saving">
            {{ isEditing ? '保存' : '创建' }}
          </button>
        </view>
      </view>
    </uni-popup>

    <!-- 排班设置弹窗 -->
    <uni-popup ref="schedulePopup" type="center" :mask-click="false">
      <view class="schedule-popup">
        <view class="schedule-header">
          <view class="schedule-title">设置清扫日程</view>
          <button class="close-btn" @click="closeSchedulePopup">
            <uni-icons type="close" size="20" color="#8E8E93" />
          </button>
        </view>
        
        <view class="schedule-content" v-if="currentScheduleArea">
          <view class="area-name-display">{{ currentScheduleArea.name }}</view>
          
          <view class="schedule-item">
            <text class="schedule-label">清扫日期</text>
            <picker 
              :range="weekDayOptions" 
              range-key="label" 
              :value="scheduleWeekIndex"
              @change="onScheduleWeekChange"
              class="schedule-picker"
            >
              <view class="schedule-picker-value">
                <text>{{ weekDayOptions[scheduleWeekIndex].label }}</text>
                <uni-icons type="right" size="16" color="#C7C7CC" />
              </view>
            </picker>
          </view>
          
          <view class="schedule-item">
            <text class="schedule-label">开始日期</text>
            <picker 
              mode="date" 
              :value="scheduleData.start_date" 
              @change="onStartDateChange"
              class="schedule-picker"
            >
              <view class="schedule-picker-value">
                <text>{{ scheduleData.start_date || '请选择开始日期' }}</text>
                <uni-icons type="calendar" size="16" color="#C7C7CC" />
              </view>
            </picker>
          </view>
          
          <view class="schedule-preview" v-if="scheduleData.scheduled_day !== null && scheduleData.start_date">
            <view class="preview-title">预览</view>
            <view class="preview-info">
              <text>从 {{ scheduleData.start_date }} 开始，每{{ getWeekDayText(scheduleData.scheduled_day) }}进行清扫</text>
            </view>
          </view>
        </view>
        
        <view class="schedule-footer">
          <button class="schedule-btn cancel" @click="closeSchedulePopup">取消</button>
          <button class="schedule-btn clear" @click="clearSchedule" v-if="currentScheduleArea && currentScheduleArea.scheduled_day !== null">
            清除排班
          </button>
          <button class="schedule-btn submit" @click="submitSchedule" :loading="scheduleSaving">
            保存排班
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { callCloudFunction } from '@/utils/auth.js';

export default {
  name: 'PublicAreaManage',
  data() {
    return {
      areaList: [],
      loading: false,
      saving: false,
      scheduleSaving: false,
      isEditing: false,
      currentArea: null,
      currentScheduleArea: null,
      formData: {
        name: '',
        location: '',
        description: '',
        status: 'active'
      },
      scheduleData: {
        scheduled_day: null,
        start_date: ''
      },
      statusOptions: [
        { value: 'active', label: '启用' },
        { value: 'inactive', label: '禁用' }
      ],
      weekDayOptions: [
        { value: null, label: '请选择清扫日期' },
        { value: 1, label: '每周一' },
        { value: 2, label: '每周二' },
        { value: 3, label: '每周三' },
        { value: 4, label: '每周四' },
        { value: 5, label: '每周五' },
        { value: 6, label: '每周六' },
        { value: 0, label: '每周日' }
      ],
      statusIndex: 0,
      scheduleWeekIndex: 0
    }
  },
  
  computed: {
    enabledAreas() {
      return this.areaList.filter(area => area.status === 'active');
    },
    scheduledAreas() {
      return this.areaList.filter(area => area.scheduled_day !== null);
    },
    todayAreas() {
      const today = new Date();
      const todayWeekDay = today.getDay();
      return this.areaList.filter(area => area.scheduled_day === todayWeekDay);
    },

  },
  
  onLoad() {
    this.loadAreaList();
  },
  
  methods: {
    // 加载公共责任区列表
    async loadAreaList() {
      try {
        this.loading = true;
        
        // 使用认证工具调用云函数获取公共责任区列表
        const result = await callCloudFunction('hygiene-area-management', {
          action: 'getAreaList',
          data: {
            type: 'public' // 只获取公共责任区
          }
        });

        // 处理返回的数据，计算下次清扫日期
        this.areaList = (result.data.list || []).map(area => ({
          ...area,
          // 确保 scheduled_day 是正确的数字类型或 null
          scheduled_day: area.scheduled_day !== null && area.scheduled_day !== undefined 
            ? Number(area.scheduled_day) 
            : null,
          next_clean_date: this.calculateNextCleanDate({
            ...area,
            scheduled_day: area.scheduled_day !== null && area.scheduled_day !== undefined 
              ? Number(area.scheduled_day) 
              : null
          })
        }));
        
      } catch (error) {
        console.error('加载公共责任区列表失败：', error);
        // 认证工具已经处理了登录相关错误
        if (!error.message.includes('未登录') && !error.message.includes('登录')) {
          uni.showToast({
            title: error.message || '加载失败',
            icon: 'none',
            duration: 3000
          });
        }
      } finally {
        this.loading = false;
      }
    },
    
    // 创建默认数据 - 保留用于首次使用时的演示
    createDefaultAreas() {
      const areas = [
        {
          id: 'public_001',
          name: '主入口大厅',
          location: { area: '办公楼1层' },
          description: '包括接待台、休息区、展示区域',
          status: 'active',
          type: 'public',
          scheduled_day: 1, // 周一
          start_date: '2024-01-15',
          last_clean_date: '2024-01-15',
          created_at: new Date('2024-01-10').toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 'public_002',
          name: '员工餐厅',
          location: { area: '办公楼2层' },
          description: '用餐区域、后厨清洁区域',
          status: 'active',
          type: 'public',
          scheduled_day: 3, // 周三
          start_date: '2024-01-17',
          last_clean_date: '2024-01-17',
          created_at: new Date('2024-01-12').toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 'public_003',
          name: '停车场区域',
          location: { area: '厂区东侧' },
          description: '室外停车区域和通道',
          status: 'active',
          type: 'public',
          scheduled_day: 5, // 周五
          start_date: '2024-01-19',
          last_clean_date: null,
          created_at: new Date('2024-01-14').toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 'public_004',
          name: '会议室区域',
          location: { area: '办公楼3层' },
          description: '大中小会议室及茶水间',
          status: 'active',
          type: 'public',
          scheduled_day: null, // 未设置
          start_date: null,
          last_clean_date: null,
          created_at: new Date('2024-01-16').toISOString(),
          updated_at: new Date().toISOString()
        }
      ];
      
      // 计算下次清扫日期
      return areas.map(area => ({
        ...area,
        next_clean_date: this.calculateNextCleanDate(area)
      }));
    },
    
    // 计算下次清扫日期
    calculateNextCleanDate(area) {
      if (area.scheduled_day === null || !area.start_date) {
        return null;
      }
      
      const today = new Date();
      const targetWeekDay = area.scheduled_day;
      
      // 从今天开始找下一个目标星期几
      let nextDate = new Date(today);
      while (nextDate.getDay() !== targetWeekDay) {
        nextDate.setDate(nextDate.getDate() + 1);
      }
      
      // 如果今天就是目标星期几，但已经是下午了，则找下周的同一天
      if (nextDate.getDay() === targetWeekDay && nextDate.toDateString() === today.toDateString()) {
        if (today.getHours() >= 12) { // 假设12点后不进行清扫
          nextDate.setDate(nextDate.getDate() + 7);
        }
      }
      
      return nextDate.toISOString().split('T')[0];
    },
    
    // 显示新增表单
    showAddArea() {
      this.isEditing = false;
      this.currentArea = null;
      this.resetForm();
      this.$refs.areaFormPopup.open();
    },
    
    // 编辑公共区
    editArea(area) {
      this.isEditing = true;
      this.currentArea = area;
      this.formData = {
        name: area.name || '',
        location: (area.location && area.location.area) || area.location || '',
        description: area.description || '',
        status: area.status || 'active'
      };
      
      // 设置状态选择器的索引
      this.statusIndex = this.statusOptions.findIndex(option => option.value === area.status) || 0;
      
      this.$refs.areaFormPopup.open();
    },
    
    // 删除公共区
    deleteArea(area) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除公共责任区"${area.name}"吗？删除后不可恢复。`,
        confirmText: '删除',
        confirmColor: '#FF3B30',
        success: (res) => {
          if (res.confirm) {
            this.performDelete(area);
          }
        }
      });
    },
    
    // 执行删除
    async performDelete(area) {
      try {
        // 使用认证工具调用云函数删除责任区
        await callCloudFunction('hygiene-area-management', {
          action: 'deleteArea',
          data: {
            id: area._id || area.id
          }
        });

        // 从本地列表中移除
        const index = this.areaList.findIndex(item => (item._id || item.id) === (area._id || area.id));
        if (index > -1) {
          this.areaList.splice(index, 1);
        }
        
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('删除失败：', error);
        // 认证工具已经处理了登录相关错误
        if (!error.message.includes('未登录') && !error.message.includes('登录')) {
          const errorMessage = error.message || '删除失败';
          if (errorMessage.includes('权限不足')) {
            uni.showModal({
              title: '权限不足',
              content: errorMessage,
              showCancel: false,
              confirmText: '我知道了'
            });
          } else {
            uni.showToast({
              title: errorMessage,
              icon: 'none',
              duration: 3000,
              mask: true
            });
          }
        }
      }
    },
    
    // 设置排班
    setSchedule(area) {
      this.currentScheduleArea = area;
      
      // 确保 scheduled_day 是正确的数据类型
      let scheduledDay = area.scheduled_day;
      if (scheduledDay !== null && scheduledDay !== undefined) {
        scheduledDay = Number(scheduledDay);
        // 如果转换后不是有效数字，则设为null
        if (isNaN(scheduledDay) || scheduledDay < 0 || scheduledDay > 6) {
          scheduledDay = null;
        }
      }
      
      this.scheduleData = {
        scheduled_day: scheduledDay,
        start_date: area.start_date || this.getNextMonday()
      };
      
      // 设置星期选择器的索引
      const foundIndex = this.weekDayOptions.findIndex(option => option.value === scheduledDay);
      this.scheduleWeekIndex = foundIndex >= 0 ? foundIndex : 0;
      
      this.$refs.schedulePopup.open();
    },
    

    
    // 获取下周一的日期
    getNextMonday() {
      const today = new Date();
      const nextMonday = new Date(today);
      const daysUntilMonday = (8 - today.getDay()) % 7;
      nextMonday.setDate(today.getDate() + (daysUntilMonday === 0 ? 7 : daysUntilMonday));
      return nextMonday.toISOString().split('T')[0];
    },
    

    
    // 状态变更
    onStatusChange(e) {
      this.statusIndex = e.detail.value;
      this.formData.status = this.statusOptions[this.statusIndex].value;
    },
    
    // 排班星期变更
    onScheduleWeekChange(e) {
      this.scheduleWeekIndex = parseInt(e.detail.value);
      const selectedOption = this.weekDayOptions[this.scheduleWeekIndex];
      if (selectedOption) {
        this.scheduleData.scheduled_day = selectedOption.value;
      } else {
        // 如果选择的索引无效，默认设为null（不设置固定日期）
        this.scheduleData.scheduled_day = null;
        this.scheduleWeekIndex = 0;
      }
    },
    
    // 开始日期变更
    onStartDateChange(e) {
      this.scheduleData.start_date = e.detail.value;
    },
    
    // 提交表单
    async submitForm() {
      if (!this.validateForm()) {
        return;
      }
      
      try {
        this.saving = true;
        
        const requestData = {
          name: this.formData.name,
          location: { area: this.formData.location },
          description: this.formData.description,
          status: this.formData.status,
          type: 'public' // 标记为公共责任区
        };

        let result;
        if (this.isEditing) {
          // 编辑模式 - 使用认证工具
          result = await callCloudFunction('hygiene-area-management', {
            action: 'updateArea',
            data: {
              id: this.currentArea._id || this.currentArea.id,
              ...requestData
            }
          });
        } else {
          // 新增模式 - 使用认证工具
          result = await callCloudFunction('hygiene-area-management', {
            action: 'createArea',
            data: requestData
          });
        }

        if (this.isEditing) {
          // 更新本地列表中的数据
          const index = this.areaList.findIndex(item => (item._id || item.id) === (this.currentArea._id || this.currentArea.id));
          if (index > -1) {
            this.areaList[index] = {
              ...this.areaList[index],
              ...(result.data || {}),
              next_clean_date: this.calculateNextCleanDate(result.data || {})
            };
          }
        } else {
          // 添加新数据到列表开头
          const newArea = {
            ...(result.data || {}),
            next_clean_date: this.calculateNextCleanDate(result.data || {})
          };
          this.areaList.unshift(newArea);
        }
        
        uni.showToast({
          title: this.isEditing ? '保存成功' : '创建成功',
          icon: 'success'
        });
        
        this.closeForm();
        
      } catch (error) {
        console.error('保存失败：', error);
        // 认证工具已经处理了登录相关错误
        if (!error.message.includes('未登录') && !error.message.includes('登录')) {
          const errorMessage = error.message || '保存失败';
          if (errorMessage.includes('权限不足')) {
            uni.showModal({
              title: '权限不足',
              content: errorMessage,
              showCancel: false,
              confirmText: '我知道了'
            });
          } else {
            uni.showToast({
              title: errorMessage,
              icon: 'none',
              duration: 3000,
              mask: true
            });
          }
        }
      } finally {
        this.saving = false;
      }
    },
    
    // 提交排班
    async submitSchedule() {
      // 验证输入
      if (this.scheduleData.scheduled_day === null || this.scheduleData.scheduled_day === undefined) {
        uni.showToast({
          title: '请选择清扫日期',
          icon: 'none'
        });
        return;
      }
      
      if (!this.scheduleData.start_date) {
        uni.showToast({
          title: '请选择开始日期',
          icon: 'none'
        });
        return;
      }
      
      // 确保 scheduled_day 是有效的数字
      const scheduledDay = Number(this.scheduleData.scheduled_day);
      if (isNaN(scheduledDay) || scheduledDay < 0 || scheduledDay > 6) {
        uni.showToast({
          title: '请选择有效的清扫日期',
          icon: 'none'
        });
        return;
      }
      
      try {
        this.scheduleSaving = true;
        
        // 使用认证工具调用云函数更新排班信息
        await callCloudFunction('hygiene-area-management', {
          action: 'updateArea',
          data: {
            id: this.currentScheduleArea._id || this.currentScheduleArea.id,
            scheduled_day: scheduledDay,  // 使用验证过的数值
            start_date: this.scheduleData.start_date
          }
        });

        // 更新本地列表中的数据
        const index = this.areaList.findIndex(item => (item._id || item.id) === (this.currentScheduleArea._id || this.currentScheduleArea.id));
        if (index > -1) {
          const updatedArea = {
            ...this.areaList[index],
            scheduled_day: scheduledDay,  // 使用验证过的数值
            start_date: this.scheduleData.start_date,
            updated_at: new Date().toISOString()
          };
          updatedArea.next_clean_date = this.calculateNextCleanDate(updatedArea);
          this.areaList[index] = updatedArea;
        }
        
        uni.showToast({
          title: '排班设置成功',
          icon: 'success'
        });
        
        this.closeSchedulePopup();
        
      } catch (error) {
        console.error('排班设置失败：', error);
        // 认证工具已经处理了登录相关错误
        if (!error.message.includes('未登录') && !error.message.includes('登录')) {
          const errorMessage = error.message || '设置失败';
          if (errorMessage.includes('权限不足')) {
            uni.showModal({
              title: '权限不足',
              content: errorMessage,
              showCancel: false,
              confirmText: '我知道了'
            });
          } else {
            uni.showToast({
              title: errorMessage,
              icon: 'none',
              duration: 3000
            });
          }
        }
      } finally {
        this.scheduleSaving = false;
      }
    },
    
    // 清除排班
    async clearSchedule() {
      try {
        // 使用认证工具调用云函数清除排班信息
        await callCloudFunction('hygiene-area-management', {
          action: 'updateArea',
          data: {
            id: this.currentScheduleArea._id || this.currentScheduleArea.id,
            scheduled_day: null,
            start_date: null
          }
        });

        // 更新本地列表中的数据
        const index = this.areaList.findIndex(item => (item._id || item.id) === (this.currentScheduleArea._id || this.currentScheduleArea.id));
        if (index > -1) {
          this.areaList[index] = {
            ...this.areaList[index],
            scheduled_day: null,
            start_date: null,
            next_clean_date: null,
            updated_at: new Date().toISOString()
          };
        }
        
        uni.showToast({
          title: '排班已清除',
          icon: 'success'
        });
        
        this.closeSchedulePopup();
        
      } catch (error) {
        console.error('清除排班失败：', error);
        // 认证工具已经处理了登录相关错误
        if (!error.message.includes('未登录') && !error.message.includes('登录')) {
          const errorMessage = error.message || '操作失败';
          if (errorMessage.includes('权限不足')) {
            uni.showModal({
              title: '权限不足',
              content: errorMessage,
              showCancel: false,
              confirmText: '我知道了'
            });
          } else {
            uni.showToast({
              title: errorMessage,
              icon: 'none',
              duration: 3000
            });
          }
        }
      }
    },
    
    // 表单验证
    validateForm() {
      if (!this.formData.name.trim()) {
        uni.showToast({
          title: '请输入责任区名称',
          icon: 'none'
        });
        return false;
      }
      
      if (this.formData.name.length > 50) {
        uni.showToast({
          title: '责任区名称不能超过50个字符',
          icon: 'none'
        });
        return false;
      }
      return true;
    },
    
    // 重置表单
    resetForm() {
      this.formData = {
        name: '',
        location: '',
        description: '',
        status: 'active'
      };
      this.statusIndex = 0;
    },
    
    // 关闭表单
    closeForm() {
      this.$refs.areaFormPopup.close();
      this.resetForm();
    },
    
    // 关闭排班弹窗
    closeSchedulePopup() {
      this.$refs.schedulePopup.close();
      this.currentScheduleArea = null;
      // 重置排班数据
      this.scheduleData = {
        scheduled_day: null,
        start_date: ''
      };
      this.scheduleWeekIndex = 0;
      // 确保保存状态重置
      this.scheduleSaving = false;
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'active': '启用',
        'inactive': '禁用'
      };
      return statusMap[status] || '未知';
    },
    
    // 判断区域是否有有效的排班设置
    hasValidSchedule(area) {
      return area.scheduled_day !== null && 
             area.scheduled_day !== undefined && 
             typeof area.scheduled_day === 'number' &&
             area.scheduled_day >= 0 && 
             area.scheduled_day <= 6;
    },
    
    // 获取星期文本
    getWeekDayText(weekDay) {
      const weekDayMap = {
        0: '周日',
        1: '周一',
        2: '周二',
        3: '周三',
        4: '周四',
        5: '周五',
        6: '周六'
      };
      return weekDayMap[weekDay] || '';
    },
    
    // 判断是否是今天
    isToday(dateString) {
      if (!dateString) return false;
      const today = new Date().toISOString().split('T')[0];
      const date = typeof dateString === 'string' ? dateString.split('T')[0] : dateString.toISOString().split('T')[0];
      return today === date;
    },
    
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      const today = new Date();
      const tomorrow = new Date(today);
      tomorrow.setDate(today.getDate() + 1);
      
      const dateStr = date.toISOString().split('T')[0];
      const todayStr = today.toISOString().split('T')[0];
      const tomorrowStr = tomorrow.toISOString().split('T')[0];
      
      if (dateStr === todayStr) {
        return '今天';
      } else if (dateStr === tomorrowStr) {
        return '明天';
      } else {
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: #F8F9FA;
  padding-bottom: 40rpx;
}

.header {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  padding: 60rpx 32rpx 40rpx;
  color: white;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 24rpx;
  opacity: 0.8;
}

.stats-card {
  background: white;
  margin: 24rpx 32rpx;
  padding: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.stat-item {
  text-align: center;
  padding: 24rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 600;
  color: #007AFF;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #8E8E93;
}

.action-bar {
  padding: 0 32rpx 24rpx;
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 26rpx;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: #007AFF;
  color: white;
}

.action-btn.secondary {
  background: white;
  color: #007AFF;
  border: 2rpx solid #007AFF;
}

.action-btn:active {
  transform: scale(0.95);
}

.area-list {
  padding: 0 32rpx;
}

.area-item {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.area-main {
  flex: 1;
}

.area-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.area-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #1C1C1E;
}

.area-status {
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.area-status.status-active {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.area-status.status-inactive {
  background: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}

.area-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 12rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  color: #8E8E93;
}

.unscheduled-text {
  color: #C7C7CC;
}

.area-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.area-schedule {
  background: #F8F9FA;
  border-radius: 8rpx;
  padding: 12rpx;
}

.schedule-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.next-clean, .last-clean {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.next-label, .last-label {
  font-size: 22rpx;
  color: #8E8E93;
}

.next-date, .last-date {
  font-size: 22rpx;
  font-weight: 500;
  color: #34C759;
}

.next-date.today {
  color: #FF3B30;
  font-weight: 600;
}

.no-schedule-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 22rpx;
  color: #C7C7CC;
  font-style: italic;
}

.area-actions {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.action-icon-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  transition: all 0.3s ease;
}

.action-icon-btn.schedule {
  background: rgba(52, 199, 89, 0.1);
}

.action-icon-btn.edit {
  background: rgba(0, 122, 255, 0.1);
}

.action-icon-btn.delete {
  background: rgba(255, 59, 48, 0.1);
}

.action-icon-btn:active {
  transform: scale(0.9);
}

.form-popup {
  width: 92vw;
  max-width: 550rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
}

.form-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-left: 5rpx;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F2F2F7;
  border: none;
  margin-right: 5rpx;
}

.form-content {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

/* #ifdef MP-WEIXIN */
/* 微信小程序隐藏滚动条 */
.form-content::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  background: transparent;
}

.form-content {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
/* #endif */

/* #ifndef MP-WEIXIN */
/* 非微信小程序环境下显示细滚动条 */
.form-content::-webkit-scrollbar {
  width: 4rpx;
  height: 4rpx;
}

.form-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2rpx;
}

.form-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}

.form-content {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
}
/* #endif */

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #1C1C1E;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.form-label.required::before {
  content: '*';
  color: #FF3B30;
  margin-right: 6rpx;
}

.form-input, .form-textarea {
  width: 100%;
  background: #F2F2F7;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  box-sizing: border-box;
}

.form-textarea {
  min-height: 120rpx;
  padding: 16rpx;
  line-height: 1.5;
  resize: none;
}

.form-input {
  height: 80rpx;
  padding: 0 16rpx;
  line-height: 80rpx;
  text-align: left;
}

/* 强制占位符样式保持一致 */
.form-input::placeholder {
  line-height: 80rpx;
  color: #C7C7CC;
}

/* 兼容WebKit浏览器 */
.form-input::-webkit-input-placeholder {
  line-height: 80rpx;
  color: #C7C7CC;
}

/* 兼容Firefox */
.form-input::-moz-placeholder {
  line-height: 80rpx;
  color: #C7C7CC;
  opacity: 1;
}

/* 兼容IE/Edge */
.form-input:-ms-input-placeholder {
  line-height: 80rpx;
  color: #C7C7CC;
}

.form-picker {
  width: 100%;
}

.picker-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 80rpx;
  background: #F2F2F7;
  border-radius: 12rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  box-sizing: border-box;
}

.form-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #F2F2F7;
}

.form-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  border: none;
  transition: all 0.3s ease;
}

.form-btn.cancel {
  background: #F2F2F7;
  color: #8E8E93;
}

.form-btn.submit {
  background: #007AFF;
  color: white;
}

.form-btn:active {
  transform: scale(0.95);
}

.schedule-popup {
  width: 92vw;
  max-width: 650rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
}

.schedule-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-left: 5rpx;
}

.schedule-content {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

/* #ifdef MP-WEIXIN */
/* 微信小程序隐藏滚动条 */
.schedule-content::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  background: transparent;
}

.schedule-content {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
/* #endif */

/* #ifndef MP-WEIXIN */
/* 非微信小程序环境下显示细滚动条 */
.schedule-content::-webkit-scrollbar {
  width: 4rpx;
  height: 4rpx;
}

.schedule-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2rpx;
}

.schedule-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}

.schedule-content {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
}
/* #endif */

.area-name-display {
  font-size: 30rpx;
  font-weight: 600;
  color: #007AFF;
  text-align: center;
  margin-bottom: 32rpx;
  padding: 16rpx;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 12rpx;
}

.schedule-item {
  margin-bottom: 24rpx;
}

.schedule-label {
  display: block;
  font-size: 28rpx;
  color: #1C1C1E;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.schedule-picker {
  width: 100%;
}

.schedule-picker-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 80rpx;
  background: #F2F2F7;
  border-radius: 12rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  box-sizing: border-box;
}

.schedule-preview {
  margin-top: 32rpx;
  padding: 24rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
}

.preview-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 16rpx;
}

.preview-info {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.next-dates {
  margin-top: 16rpx;
}

.dates-label {
  font-size: 24rpx;
  color: #8E8E93;
  display: block;
  margin-bottom: 12rpx;
}

.date-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.date-item {
  background: #007AFF;
  color: white;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.date-item.today {
  background: #FF3B30;
}

.schedule-footer {
  display: flex;
  gap: 12rpx;
  padding: 32rpx;
  border-top: 1rpx solid #F2F2F7;
}

.schedule-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  border: none;
  transition: all 0.3s ease;
}

.schedule-btn.cancel {
  background: #F2F2F7;
  color: #8E8E93;
}

.schedule-btn.clear {
  background: #FF3B30;
  color: white;
}

.schedule-btn.submit {
  background: #007AFF;
  color: white;
}

.schedule-btn:active {
  transform: scale(0.95);
}

/* 加载状态样式 */
.stats-loading {
  grid-column: 1 / -1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}

.list-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
  background: #F8F9FA;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
}
</style> 