{"bsonType": "object", "required": ["name", "type", "creator_id"], "permission": {"read": true, "create": "doc.creator_id == auth.uid || auth.role.includes('admin') || auth.role.includes('Integrated') || auth.role.includes('reviser')", "update": "doc.creator_id == auth.uid || auth.role.includes('admin') || auth.role.includes('Integrated') || auth.role.includes('reviser')", "delete": "auth.role.includes('admin')"}, "properties": {"_id": {"description": "ID，系统自动生成"}, "name": {"bsonType": "string", "title": "责任区名称", "description": "责任区的名称，如'生产车间A区东侧'", "minLength": 2, "maxLength": 50, "errorMessage": {"required": "责任区名称不能为空", "minLength": "责任区名称长度不能小于 {minLength} 个字符", "maxLength": "责任区名称长度不能大于 {maxLength} 个字符"}}, "type": {"bsonType": "string", "title": "责任区类型", "description": "固定责任区或公共责任区", "enum": ["fixed", "public"], "errorMessage": {"required": "请选择责任区类型", "enum": "请选择有效的责任区类型"}}, "description": {"bsonType": "string", "title": "区域描述", "description": "详细描述该责任区的具体范围和要求", "maxLength": 500, "errorMessage": {"maxLength": "区域描述不能超过 {maxLength} 个字符"}}, "location": {"bsonType": "object", "title": "位置信息", "description": "责任区的具体位置信息", "properties": {"building": {"bsonType": "string", "title": "所属建筑", "maxLength": 50}, "floor": {"bsonType": "string", "title": "楼层", "maxLength": 20}, "area": {"bsonType": "string", "title": "具体区域", "maxLength": 100}}}, "assigned_users": {"bsonType": "array", "title": "分配的用户", "description": "分配给该责任区的用户ID列表", "items": {"bsonType": "string", "foreignKey": "uni-id-users._id"}, "maxItems": 10}, "cleaning_requirements": {"bsonType": "array", "title": "清洁要求", "description": "该责任区的具体清洁要求列表", "items": {"bsonType": "string", "maxLength": 200}, "maxItems": 20}, "cleaning_frequency": {"bsonType": "string", "title": "清洁频率", "description": "清洁的频率要求", "enum": ["daily", "weekly", "monthly", "as_needed"], "default": "weekly"}, "scheduled_day": {"bsonType": "number", "title": "预定清洁日", "description": "公共责任区的固定清洁日（1-7表示周一到周日）", "minimum": 1, "maximum": 7}, "status": {"bsonType": "string", "title": "状态", "description": "责任区的当前状态", "enum": ["active", "inactive", "maintenance"], "default": "active"}, "creator_id": {"bsonType": "string", "title": "创建人", "description": "创建该责任区的用户ID", "foreignKey": "uni-id-users._id"}, "created_at": {"bsonType": "timestamp", "title": "创建时间", "description": "记录创建时间", "forceDefaultValue": {"$env": "now"}}, "updated_at": {"bsonType": "timestamp", "title": "更新时间", "description": "记录最后更新时间", "forceDefaultValue": {"$env": "now"}}}}