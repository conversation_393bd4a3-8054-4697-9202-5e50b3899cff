@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container.data-v-4c3d3ebe {
  min-height: 100vh;
  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
  width: 100%;
  overflow-x: hidden;
  /* 防止水平滚动 */
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
}
/* 时间选择器样式 */
.time-selector.data-v-4c3d3ebe {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 20rpx;
  border: 2rpx solid rgba(0, 122, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
}
.time-selector.data-v-4c3d3ebe:active {
  background: rgba(0, 122, 255, 0.15);
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.time-text.data-v-4c3d3ebe {
  font-size: 24rpx;
  color: #007AFF;
  font-weight: 500;
}
/* ======== 新的日历式日期选择器样式 ======== */
.date-picker-popup.data-v-4c3d3ebe {
  background: white;
  padding: 0;
  max-height: 80vh;
  overflow-y: auto;
}
.popup-header.data-v-4c3d3ebe {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
}
.popup-title.data-v-4c3d3ebe {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
}
.popup-close.data-v-4c3d3ebe {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F2F2F7;
  border-radius: 50%;
}
.quick-date-section.data-v-4c3d3ebe,
.range-date-section.data-v-4c3d3ebe {
  padding: 24rpx 32rpx;
}
.section-title.data-v-4c3d3ebe {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.quick-options.data-v-4c3d3ebe {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 32rpx;
}
.quick-option.data-v-4c3d3ebe {
  flex: 1;
  min-width: 120rpx;
  padding: 24rpx 16rpx;
  border: 2rpx solid #E5E5EA;
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.2s;
}
.quick-option.active.data-v-4c3d3ebe {
  border-color: #007AFF;
  background: rgba(0, 122, 255, 0.1);
}
.quick-text.data-v-4c3d3ebe {
  font-size: 28rpx;
  color: #1C1C1E;
}
.quick-option.active .quick-text.data-v-4c3d3ebe {
  color: #007AFF;
  font-weight: 600;
}
/* 日历组件样式 */
.calendar-section.data-v-4c3d3ebe {
  margin: 24rpx 0;
}
.calendar-header.data-v-4c3d3ebe {
  margin-bottom: 24rpx;
}
.calendar-tip.data-v-4c3d3ebe {
  font-size: 24rpx;
  color: #8E8E93;
  display: block;
}
.selected-range.data-v-4c3d3ebe {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 24rpx;
  padding: 20rpx;
  background: #F8F9FA;
  border-radius: 16rpx;
  gap: 16rpx;
}
.range-item.data-v-4c3d3ebe {
  text-align: center;
  flex: 1;
}
.range-label.data-v-4c3d3ebe {
  font-size: 22rpx;
  color: #8E8E93;
  display: block;
  margin-bottom: 4rpx;
}
.range-value.data-v-4c3d3ebe {
  font-size: 26rpx;
  color: #1C1C1E;
  font-weight: 600;
  display: block;
}
.range-separator.data-v-4c3d3ebe {
  font-size: 24rpx;
  color: #007AFF;
  font-weight: 600;
}
/* 卡片样式 */
.card.data-v-4c3d3ebe {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 24rpx 32rpx 0 32rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.card.data-v-4c3d3ebe:first-child {
  margin-top: 24rpx;
}
.card-header.data-v-4c3d3ebe {
  padding: 32rpx 32rpx 16rpx 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
}
.card-body.data-v-4c3d3ebe {
  padding: 32rpx;
}
.header-content.data-v-4c3d3ebe {
  display: flex;
  flex-direction: row;
  align-items: baseline;
  justify-content: space-between;
}
.card-title.data-v-4c3d3ebe {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 0;
}
.card-subtitle.data-v-4c3d3ebe {
  font-size: 26rpx;
  color: #8E8E93;
}
/* 时间选择器 */
.time-selector.data-v-4c3d3ebe {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.time-selector-header.data-v-4c3d3ebe {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.time-selector-title.data-v-4c3d3ebe {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
}
.time-selector-picker.data-v-4c3d3ebe {
  background: rgba(0, 122, 255, 0.1);
  border: 1rpx solid rgba(0, 122, 255, 0.2);
  border-radius: 20rpx;
  padding: 12rpx 20rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #007AFF;
  font-weight: 500;
  transition: all 0.2s ease;
}
.time-selector-picker.data-v-4c3d3ebe:active {
  background: rgba(0, 122, 255, 0.15);
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
/* 统计网格 */
.stats-grid.data-v-4c3d3ebe {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
  text-align: center;
}
.stats-grid-two.data-v-4c3d3ebe {
  grid-template-columns: repeat(2, 1fr);
}
.stats-item.data-v-4c3d3ebe {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stats-number.data-v-4c3d3ebe {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.stats-number.success.data-v-4c3d3ebe {
  color: #34C759;
}
.stats-number.warning.data-v-4c3d3ebe {
  color: #FF9500;
}
.stats-number.info.data-v-4c3d3ebe {
  color: #007AFF;
}
.stats-number.danger.data-v-4c3d3ebe {
  color: #FF3B30;
}
.stats-label.data-v-4c3d3ebe {
  font-size: 24rpx;
  color: #8E8E93;
}
/* 列表项样式 */
.list-item.data-v-4c3d3ebe {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #F2F2F7;
}
.list-item.data-v-4c3d3ebe:last-child {
  border-bottom: none;
}
.list-item-icon.data-v-4c3d3ebe {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}
.list-item-icon.icon-bg-completed.data-v-4c3d3ebe {
  background: rgba(52, 199, 89, 0.1);
}
.list-item-icon.icon-bg-pending.data-v-4c3d3ebe {
  background: rgba(255, 149, 0, 0.1);
}
.list-item-icon.icon-bg-overdue.data-v-4c3d3ebe {
  background: rgba(255, 59, 48, 0.1);
}
.list-item-icon.icon-bg-pending_review.data-v-4c3d3ebe {
  background: rgba(0, 122, 255, 0.1);
}
.list-item-icon.icon-bg-pending_rectification.data-v-4c3d3ebe {
  background: rgba(255, 107, 53, 0.1);
}
.list-item-icon.icon-bg-pending_assignment.data-v-4c3d3ebe {
  background: rgba(255, 149, 0, 0.1);
}
.list-item-icon.icon-bg-scheduled.data-v-4c3d3ebe {
  background: rgba(142, 142, 147, 0.1);
}
.list-item-icon.icon-bg-rejected.data-v-4c3d3ebe {
  background: rgba(255, 59, 48, 0.1);
}
.list-item-icon.icon-bg-verified.data-v-4c3d3ebe {
  background: rgba(52, 199, 89, 0.1);
}
.list-item-icon.icon-bg-approved.data-v-4c3d3ebe {
  background: rgba(52, 199, 89, 0.1);
}
.list-item-icon.icon-bg-in_progress.data-v-4c3d3ebe {
  background: rgba(0, 122, 255, 0.1);
}
.list-item-content.data-v-4c3d3ebe {
  flex: 1;
}
.list-item-title.data-v-4c3d3ebe {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 4rpx;
}
.list-item-subtitle.data-v-4c3d3ebe {
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.4;
}
.list-item-right.data-v-4c3d3ebe {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
/* 状态标签 */
.status-badge.data-v-4c3d3ebe {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 600;
}
.status-badge.status-completed.data-v-4c3d3ebe {
  background: #E8F5E8;
  color: #34C759;
}
.status-badge.status-pending.data-v-4c3d3ebe {
  background: #FFF4E6;
  color: #FF9500;
}
.status-badge.status-overdue.data-v-4c3d3ebe {
  background: #FFE6E6;
  color: #FF3B30;
}
.status-badge.status-pending_review.data-v-4c3d3ebe {
  background: #E6F3FF;
  color: #007AFF;
}
.status-badge.status-pending_rectification.data-v-4c3d3ebe {
  background: #FFF0E6;
  color: #FF6B35;
}
.status-badge.status-pending_assignment.data-v-4c3d3ebe {
  background: #FFF4E6;
  color: #FF9500;
}
.status-badge.status-scheduled.data-v-4c3d3ebe {
  background: #F2F2F7;
  color: #8E8E93;
}
.status-badge.status-rejected.data-v-4c3d3ebe {
  background: #FFE6E6;
  color: #FF3B30;
}
.status-badge.status-verified.data-v-4c3d3ebe {
  background: #E8F5E8;
  color: #34C759;
}
.status-badge.status-approved.data-v-4c3d3ebe {
  background: #E8F5E8;
  color: #34C759;
}
.status-badge.status-in_progress.data-v-4c3d3ebe {
  background: #E6F3FF;
  color: #007AFF;
}
.status-badge.status-rectification_completed.data-v-4c3d3ebe {
  background: #FFF4E6;
  color: #FF9500;
}
/* 公共区域标签 */
.public-tag.data-v-4c3d3ebe {
  display: inline-block;
  background: #E6F3FF;
  color: #007AFF;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  margin-left: 12rpx;
  font-weight: 400;
}
/* 固定区域标签 */
.fixed-tag.data-v-4c3d3ebe {
  display: inline-block;
  background: #E8F5E8;
  color: #34C759;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  margin-left: 12rpx;
  font-weight: 400;
}
/* 整改任务时间信息 */
.task-time-info.data-v-4c3d3ebe {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
  font-size: 22rpx;
  color: #8E8E93;
}
.time-label.data-v-4c3d3ebe {
  margin-right: 8rpx;
}
.time-value.data-v-4c3d3ebe {
  font-weight: 500;
  color: #1C1C1E;
}
/* 清理记录元信息 */
.cleaning-record-meta.data-v-4c3d3ebe {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
  font-size: 22rpx;
  color: #8E8E93;
}
.meta-label.data-v-4c3d3ebe {
  margin-right: 4rpx;
}
.meta-value.data-v-4c3d3ebe {
  font-weight: 500;
  color: #1C1C1E;
  margin-right: 12rpx;
}
.meta-remark.data-v-4c3d3ebe {
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
/* 时间分组样式 */
.time-group.data-v-4c3d3ebe {
  margin-bottom: 16rpx;
}
.time-group-header.data-v-4c3d3ebe {
  padding: 24rpx 32rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
  margin-bottom: 8rpx;
}
.time-group-title.data-v-4c3d3ebe {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.time-title.data-v-4c3d3ebe {
  font-size: 30rpx;
  font-weight: 500;
  color: #1D1D1F;
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.time-count.data-v-4c3d3ebe {
  font-size: 24rpx;
  color: #8E8E93;
  background: #E5E7EB;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  min-width: 48rpx;
  text-align: center;
}
/* 加载状态样式 */
.loading-container.data-v-4c3d3ebe {
  padding: 80rpx 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #FFFFFF;
  border-radius: 24rpx;
  margin: 20rpx;
}
.loading-content.data-v-4c3d3ebe {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
}
/* 统计区域加载状态 */
.stats-loading.data-v-4c3d3ebe {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 24rpx;
  min-height: 200rpx;
}
/* 列表加载状态 */
.list-loading.data-v-4c3d3ebe {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 20rpx;
  background: #FFFFFF;
  border-radius: 24rpx;
  margin: 20rpx;
  border: 1rpx solid #F2F2F7;
}
.loading-spinner.data-v-4c3d3ebe {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #F0F4F8;
  border-top: 6rpx solid #007AFF;
  border-radius: 50%;
  -webkit-animation: spin-data-v-4c3d3ebe 1.2s linear infinite;
          animation: spin-data-v-4c3d3ebe 1.2s linear infinite;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}
/* 按钮内的加载动画 */
.btn-loading-spinner.data-v-4c3d3ebe {
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #FFFFFF;
  border-radius: 50%;
  -webkit-animation: spin-data-v-4c3d3ebe 1s linear infinite;
          animation: spin-data-v-4c3d3ebe 1s linear infinite;
  margin-right: 12rpx;
}
@-webkit-keyframes spin-data-v-4c3d3ebe {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin-data-v-4c3d3ebe {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.loading-text.data-v-4c3d3ebe {
  font-size: 28rpx;
  color: #6B7280;
  text-align: center;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}
.list-loading .loading-text.data-v-4c3d3ebe {
  color: #374151;
}
/* 加载更多按钮 */
.load-more-section.data-v-4c3d3ebe {
  padding: 32rpx;
  display: flex;
  justify-content: center;
}
.load-more-btn.data-v-4c3d3ebe {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx 48rpx;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  color: white;
  border: none;
  border-radius: 25rpx;
  font-size: 28rpx;
  font-weight: 500;
  min-width: 200rpx;
}
.load-more-btn.data-v-4c3d3ebe:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.load-more-btn.data-v-4c3d3ebe:active:not(:disabled) {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
/* 底部安全间距 */
.bottom-safe-area.data-v-4c3d3ebe {
  height: 40rpx;
}
/* 响应式调整 */
@media (max-width: 414px) {
.page-header.data-v-4c3d3ebe {
    padding: 24rpx 16rpx;
}
.card.data-v-4c3d3ebe {
    margin: 24rpx 16rpx 0 16rpx;
}
.card.data-v-4c3d3ebe:first-child {
    margin-top: 24rpx;
}
.stats-grid.data-v-4c3d3ebe {
    grid-template-columns: repeat(3, 1fr);
    gap: 24rpx;
}
}
