<template>
	<view class="page-container">
		<!-- 抽查统计 -->
		<view class="card">
			<view class="card-header">
				<view class="header-content">
					<view class="card-title">抽查统计</view>
					<view class="filters-row">
						<view class="time-selector" @click="showTimeSelector">
							<text class="time-text">{{ getCurrentTimeRange() }}</text>
							<uni-icons type="down" size="12" color="#007AFF"></uni-icons>
						</view>
						<view v-if="selectedStatus !== 'all'" class="status-filter-indicator" @click="selectStatus('all')">
							<text class="filter-text">{{ getStatusFilterText() }}</text>
							<uni-icons type="close" size="12" color="#8E8E93"></uni-icons>
						</view>
					</view>
				</view>
			</view>
			<!-- 统计数据 -->
			<view v-if="loading" class="stats-loading">
				<view class="loading-spinner"></view>
				<view class="loading-text">加载统计数据中...</view>
			</view>
			<view v-else class="stats-grid stats-grid-six">
				<!-- 按优先级重新排序：检查员工作流程优先 -->
				<view class="stats-item"
					:class="{ active: selectedStatus === 'pending_review' }"
					@click="selectStatus('pending_review')">
					<view class="stats-number review">{{ getStatsData().pending_review }}</view>
					<view class="stats-label">待复查</view>
				</view>
				<view class="stats-item"
					:class="{ active: selectedStatus === 'pending' }"
					@click="selectStatus('pending')">
					<view class="stats-number warning">{{ getStatsData().pending }}</view>
					<view class="stats-label">待检查</view>
				</view>
				<view class="stats-item"
					:class="{ active: selectedStatus === 'pending_rectification' }"
					@click="selectStatus('pending_rectification')">
					<view class="stats-number danger">{{ getStatsData().pending_rectification }}</view>
					<view class="stats-label">待整改</view>
				</view>
				<view class="stats-item"
					:class="{ active: selectedStatus === 'missed' }"
					@click="selectStatus('missed')">
					<view class="stats-number missed">{{ getStatsData().missed }}</view>
					<view class="stats-label">漏检查</view>
				</view>
				<view class="stats-item" 
					:class="{ active: selectedStatus === 'not_cleaned' }"
					@click="selectStatus('not_cleaned')">
					<view class="stats-number not-cleaned">{{ getStatsData().not_cleaned }}</view>
					<view class="stats-label">未打扫</view>
				</view>
				<view class="stats-item"
					:class="{ active: selectedStatus === 'completed' }"
					@click="selectStatus('completed')">
					<view class="stats-number success">{{ getStatsData().completed }}</view>
					<view class="stats-label">已完成</view>
				</view>
			</view>
		</view>

		<!-- 检查记录列表 -->
		<view class="card">
			<view class="card-header">
				<view class="header-content">
					<view class="card-title">责任区检查</view>
					<view class="card-subtitle">固定和公共责任区检查任务</view>
				</view>
			</view>
			
			<!-- 加载状态 -->
			<view v-if="loading" class="loading-container">
				<view class="loading-content">
					<view class="loading-spinner"></view>
					<text class="loading-text">加载检查记录中...</text>
				</view>
			</view>
			
			<!-- 正常内容 -->
			<template v-else>
				<!-- 分类标签 -->
				<scroll-view class="category-scroll" scroll-x="true" show-scrollbar="false">
					<view class="category-tabs">
						<view 
							v-for="category in categoryTabs" 
							:key="category.value"
							class="category-tab"
							:class="{ active: selectedCategory === category.value }"
							@click="selectCategory(category.value)"
						>
							<text class="tab-text">{{ category.label }}</text>
							<text class="tab-count">{{ getCategoryCount(category.value) }}</text>
						</view>
					</view>
				</scroll-view>
				
				<!-- 时间分组的检查记录列表 -->
				<view v-if="groupedFilteredRecords.length > 0">
					<view v-for="group in groupedFilteredRecords" :key="group.weekKey" class="time-group">
						<!-- 时间分组标题 -->
						<view class="time-group-header" @click="toggleWeek(group.weekKey)">
							<view class="time-group-title">
								<uni-icons :type="group.expanded ? 'down' : 'right'" size="16" color="#007AFF"></uni-icons>
								<text class="time-title">{{ group.title }}</text>
								<view class="time-count">{{ group.records.length }}条</view>
							</view>
						</view>
						
						<!-- 分组记录列表 -->
						<view v-if="group.expanded" class="time-group-content">
							<view v-for="(record, recordIndex) in group.records" :key="recordIndex" 
								class="list-item" 
								@click="handleRecordClick(record)">
								<view class="list-item-icon" :class="['icon-bg-' + record.status]">
									<uni-icons :type="record.icon" size="18" :color="getIconColor(record.status)"></uni-icons>
								</view>
								<view class="list-item-content">
									<view class="list-item-title">
										<text>{{ record.areaName }}</text>
										<view class="badges-container">
											<!-- 区域类型标签 -->
											<view class="area-type-badge" :class="['type-' + record.type]">
												<text class="badge-text">{{ getAreaTypeText(record.type) }}</text>
											</view>
											<!-- 整改任务标签 -->
											<view v-if="record.isRectificationRecheck" class="rectification-badge">
												<text class="badge-text">整改任务</text>
											</view>
										</view>
									</view>
									<view class="list-item-subtitle">{{ record.subtitle }}</view>
								</view>
								<view class="list-item-right">
									<view class="status-badge" :class="['status-' + record.status]">{{ getStatusText(record.status) }}</view>
									<uni-icons type="right" size="14" color="#C7C7CC"></uni-icons>
								</view>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 无数据状态 -->
				<p-empty-state
					v-else
					useIcon
					iconName="info"
					iconColor="#C7C7CC"
					size="large"
					text="暂无数据"
				></p-empty-state>
			</template>
		</view>

		<!-- 底部安全间距 -->
		<view class="bottom-safe-area"></view>

		<!-- 时间选择弹窗 -->
		<uni-popup ref="timePopup" type="bottom" border-radius="16rpx 16rpx 0 0">
			<view class="time-popup">
				<view class="popup-header">
					<text class="popup-title">选择时间范围</text>
					<view class="popup-close" @click="closeTimeSelector">
						<uni-icons type="close" size="18" color="#8E8E93"></uni-icons>
					</view>
				</view>
				<view class="time-options">
					<view 
						v-for="option in timeOptions" 
						:key="option.value"
						class="time-option"
						:class="{ active: selectedTimeFilter === option.value }"
						@click="selectAndConfirmTimeFilter(option)"
					>
						<view class="option-left">
							<uni-icons :type="option.icon" size="18" :color="selectedTimeFilter === option.value ? '#007AFF' : '#8E8E93'"></uni-icons>
							<view class="option-content">
								<text class="option-text">{{ option.label }}</text>
								<text class="option-desc">{{ option.desc }}</text>
							</view>
						</view>
						<view v-if="selectedTimeFilter === option.value" class="option-check">
							<uni-icons type="checkmarkempty" size="16" color="#007AFF"></uni-icons>
						</view>
					</view>
				</view>

			</view>
		</uni-popup>

		<!-- 自定义日期选择弹窗 -->
		<uni-popup v-if="showDatePicker" type="center" :maskClick="false">
			<view class="date-picker-popup">
				<view class="popup-header">
					<text class="popup-title">选择日期范围</text>
				</view>
				<view class="date-picker-content">
					<view class="date-item">
						<text class="date-label">开始日期</text>
						<picker mode="date" :value="customStartDate" @change="(e) => customStartDate = e.detail.value">
							<view class="date-input">
								<text>{{ customStartDate || '请选择开始日期' }}</text>
								<uni-icons type="calendar" size="16" color="#8E8E93"></uni-icons>
							</view>
						</picker>
					</view>
					<view class="date-item">
						<text class="date-label">结束日期</text>
						<picker mode="date" :value="customEndDate" @change="(e) => customEndDate = e.detail.value">
							<view class="date-input">
								<text>{{ customEndDate || '请选择结束日期' }}</text>
								<uni-icons type="calendar" size="16" color="#8E8E93"></uni-icons>
							</view>
						</picker>
					</view>
				</view>
				<view class="popup-footer">
					<button class="cancel-btn" @click="cancelCustomDate">取消</button>
					<button class="confirm-btn" @click="confirmCustomDate">确定</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import { callCloudFunction } from '@/utils/auth.js';

export default {
	name: 'AreaInspection',
	data() {
		return {
			selectedTimeFilter: 'week',
			selectedCategory: 'all', // 当前选择的分类
			selectedStatus: 'all', // 当前选择的状态筛选
			timeOptions: [], // 将在created中动态生成
			// 自定义日期选择
			showDatePicker: false,
			customStartDate: '',
			customEndDate: '',
			// 分类标签
			categoryTabs: [
				{ label: '全部', value: 'all' },
				{ label: '固定责任区', value: 'fixed' },
				{ label: '公共责任区', value: 'public' }
			],

			// 检查记录数据
			inspectionRecords: [],
			// 加载状态
			loading: false,
			loadError: '',
			// 展开的周次
			expandedWeeks: [], // 将动态设置当前周
			// 数据加载标记
			dataLoaded: false,
			needsRefresh: false, // 标记是否需要刷新数据
			
			// 性能优化缓存
			processCache: {
				timeCalculations: null,
				statusMap: null,
				weekKeyCache: new Map(),
				areaTypeMap: null,
				formattedDatesCache: new Map()
			},
			// 责任区数据缓存
			areasCache: null
		}
	},
	computed: {
		// 根据时间和分类筛选记录
		filteredRecords() {
			let records = this.inspectionRecords;
			
			// 按时间筛选
			if (this.selectedTimeFilter === 'week') {
				// 本周：只显示当前周
				const currentWeekKey = this.getCurrentWeekKeyOptimized();
				records = records.filter(r => r.week === currentWeekKey);
			} else if (this.selectedTimeFilter === 'last_week') {
				// 上周：只显示上周
				const now = new Date();
				const lastWeek = new Date(now);
				lastWeek.setDate(now.getDate() - 7);
				const lastWeekKey = this.getWeekKeyByDate(lastWeek);
				records = records.filter(r => r.week === lastWeekKey);
			} else if (this.selectedTimeFilter === 'month') {
				// 本月：显示当前月份的所有周
				const now = new Date();
				const currentYear = now.getFullYear();
				const currentMonth = now.getMonth() + 1;
				records = records.filter(r => {
					if (!r.week) return false;
					const match = r.week.match(/(\d{4})-W(\d{2})/);
					if (!match) return false;
					
					const year = parseInt(match[1]);
					const weekNum = parseInt(match[2]);
					
					// 计算该周所属的月份
					const weekDate = this.getDateFromWeek(year, weekNum);
					const weekMonth = weekDate.getMonth() + 1;
					
					return year === currentYear && weekMonth === currentMonth;
				});
			} else if (this.selectedTimeFilter === 'quarter') {
				// 本季度：显示当前季度的所有记录
				const now = new Date();
				const currentYear = now.getFullYear();
				const currentQuarter = Math.ceil((now.getMonth() + 1) / 3);
				
				records = records.filter(r => {
					if (!r.week) return false;
					const match = r.week.match(/(\d{4})-W(\d{2})/);
					if (!match) return false;
					
					const year = parseInt(match[1]);
					const weekNum = parseInt(match[2]);
					
					// 计算该周所属的季度
					const weekDate = this.getDateFromWeek(year, weekNum);
					const weekQuarter = Math.ceil((weekDate.getMonth() + 1) / 3);
					return year === currentYear && weekQuarter === currentQuarter;
				});
			} else if (this.selectedTimeFilter === 'custom') {
				// 自定义：根据选择的日期范围筛选
				if (this.customStartDate && this.customEndDate) {
					const startDate = new Date(this.customStartDate);
					const endDate = new Date(this.customEndDate);
					
					records = records.filter(r => {
						if (!r.week) return false;
						const weekStart = this.getWeekStartByWeekKey(r.week);
						const weekEnd = this.getWeekEndByWeekKey(r.week);
						
						// 周与选择的日期范围有交集就包含
						return weekStart <= endDate && weekEnd >= startDate;
					});
				}
			}
			
			// 按分类筛选
			if (this.selectedCategory !== 'all') {
				records = records.filter(r => r.type === this.selectedCategory);
			}
			
			// 按状态筛选 - 包含新状态的映射
			if (this.selectedStatus !== 'all') {
				records = records.filter(r => {
					if (this.selectedStatus === 'not_cleaned') {
						return r.status === 'not_cleaned' || r.status === 'expired_not_cleaned';
					} else if (this.selectedStatus === 'missed') {
						return r.status === 'missed' || r.status === 'inspector_missed';
					} else {
						return r.status === this.selectedStatus;
					}
				});
			}
			
			return records;
		},
		
		// 时间分组的记录
		groupedFilteredRecords() {
			const records = this.filteredRecords;
			const grouped = {};
			
			records.forEach(record => {
				const weekKey = record.week;
				if (!grouped[weekKey]) {
					grouped[weekKey] = {
						weekKey,
						title: this.getWeekTitle(weekKey),
						records: [],
						expanded: this.expandedWeeks.includes(weekKey)
					};
				}
				grouped[weekKey].records.push(record);
			});
			
			// 对每个分组内的记录按状态优先级排序
			Object.values(grouped).forEach(group => {
				group.records.sort((a, b) => this.getStatusPriority(a.status) - this.getStatusPriority(b.status));
			});
			
			// 按周次降序排序
			return Object.values(grouped).sort((a, b) => {
				const weekA = parseInt(a.weekKey.split('-W')[1]);
				const weekB = parseInt(b.weekKey.split('-W')[1]);
				return weekB - weekA;
			});
		}
	},
	created() {
		this.generateTimeOptions();
		this.initProcessCache();
	},
	onLoad() {
		this.loadPageDataOptimized();
		
		// 监听清理记录更新事件
		uni.$on('cleaningRecordUpdated', this.handleCleaningRecordUpdated);
		// 监听检查记录更新事件
		uni.$on('inspectionRecordUpdated', this.handleInspectionRecordUpdated);
		// 监听整改记录更新事件
		uni.$on('rectificationRecordUpdated', this.handleRectificationRecordUpdated);
	},
	onUnload() {
		// 移除事件监听
		uni.$off('cleaningRecordUpdated', this.handleCleaningRecordUpdated);
		uni.$off('inspectionRecordUpdated', this.handleInspectionRecordUpdated);
		uni.$off('rectificationRecordUpdated', this.handleRectificationRecordUpdated);
	},
	onShow() {
		// 页面重新显示时，只在确实需要刷新数据的情况下才刷新
		// 使用静默刷新，避免显示加载动画
		if (this.dataLoaded && !this.loading && this.needsRefresh) {
			this.silentRefreshData();
			this.needsRefresh = false; // 重置刷新标记
		}
	},
	methods: {
		// 初始化处理缓存
		initProcessCache() {
			if (!this.processCache.areaTypeMap) {
				this.processCache.areaTypeMap = {
					'fixed': '固定',
					'public': '公共'
				};
			}
			
			if (!this.processCache.statusMap) {
				this.processCache.statusMap = {
					'not_cleaned': '未打扫',
					'expired_not_cleaned': '已过期',
					'pending': '待检查', 
					'pending_rectification': '待整改',
					'pending_review': '待复查',
					'completed': '已完成',
					'rejected': '整改不达标',
					'verified': '整改合格',
					'missed': '漏检查',
					'inspector_missed': '检查员漏检',
					'passed': '检查通过',
					'failed': '检查未通过'
				};
			}
			
			this.initTimeCalculations();
		},

		// 初始化时间计算缓存
		initTimeCalculations() {
			if (!this.processCache.timeCalculations) {
				const now = new Date();
				this.processCache.timeCalculations = {
					now,
					weekStart: this.getWeekStartOptimized(now),
					weekEnd: this.getWeekEndOptimized(now),
					currentWeekKey: this.getCurrentWeekKeyOptimized(now)
				};
			}
		},

		// 获取区域类型文本（使用缓存）
		getAreaTypeText(type) {
			return this.processCache.areaTypeMap[type] || '固定';
		},

		// 获取状态优先级（数字越小优先级越高）
		getStatusPriority(status) {
			const priorityMap = {
				'pending_review': 1,        // 待复查 - 最高优先级
				'pending': 2,               // 待检查 - 高优先级
				'pending_rectification': 3, // 待整改 - 中高优先级
				'missed': 4,                // 漏检查 - 中优先级
				'not_cleaned': 5,           // 未打扫 - 低优先级
				'completed': 6              // 已完成 - 最低优先级
			};
			return priorityMap[status] || 999; // 未知状态放在最后
		},
		
		// 动态生成时间选项
		generateTimeOptions() {
			const now = new Date();
			const year = now.getFullYear();
			const month = now.getMonth() + 1;
			
			// 获取当前周的起始和结束日期
			const weekRange = this.getCurrentWeekRange(now);
			
			// 获取上周的起始和结束日期
			const lastWeek = new Date(now);
			lastWeek.setDate(now.getDate() - 7);
			const lastWeekRange = this.getCurrentWeekRange(lastWeek);
			
			this.timeOptions = [
				{ 
					label: '本周', 
					value: 'week', 
					icon: 'calendar',
					desc: `${weekRange.start} - ${weekRange.end}`
				},
				{ 
					label: '上周', 
					value: 'last_week', 
					icon: 'calendar',
					desc: `${lastWeekRange.start} - ${lastWeekRange.end}`
				},
				{ 
					label: '本月', 
					value: 'month', 
					icon: 'calendar',
					desc: `${year}年${month}月`
				},
				{ 
					label: '自定义', 
					value: 'custom', 
					icon: 'calendar',
					desc: '选择日期范围'
				}
			];
		},

		// 获取当前周的起始和结束日期
		getCurrentWeekRange(inputDate = new Date()) {
			const date = new Date(inputDate);
			const day = date.getDay();
			const diff = date.getDate() - day + (day === 0 ? -6 : 1);
			
			const monday = new Date(date);
			monday.setDate(diff);
			
			const sunday = new Date(date);
			sunday.setDate(diff + 6);
			
			const startStr = `${monday.getMonth() + 1}月${monday.getDate()}日`;
			const endStr = `${sunday.getMonth() + 1}月${sunday.getDate()}日`;
			
			return {
				start: startStr,
				end: endStr,
				monday: monday,
				sunday: sunday
			};
		},

		// 优化的加载页面数据
		async loadPageDataOptimized() {
			this.loading = true;
			this.loadError = '';

			try {
				// 重新初始化时间计算缓存
				this.processCache.timeCalculations = null;
				this.initTimeCalculations();
				
				// 加载检查记录
				await this.loadInspectionRecordsOptimized();

				// 设置默认展开的周次
				if (this.expandedWeeks.length === 0) {
					const currentWeek = this.processCache.timeCalculations.currentWeekKey;
					
					// 默认展开当前周和有待处理任务的周次
					this.expandedWeeks = [currentWeek];
					
					// 如果有待复查任务，确保包含待复查任务的周次也展开
					const pendingReviewWeeks = this.inspectionRecords
						.filter(record => record.status === 'pending_review')
						.map(record => record.week)
						.filter((week, index, arr) => arr.indexOf(week) === index); // 去重
					
					// 如果有待整改任务，也展开对应周次
					const pendingRectificationWeeks = this.inspectionRecords
						.filter(record => record.status === 'pending_rectification')
						.map(record => record.week)
						.filter((week, index, arr) => arr.indexOf(week) === index); // 去重
					
					// 合并所有需要展开的周次
					const allPendingWeeks = [...pendingReviewWeeks, ...pendingRectificationWeeks];
					allPendingWeeks.forEach(week => {
						if (!this.expandedWeeks.includes(week)) {
							this.expandedWeeks.push(week);
						}
					});
				}

			} catch (error) {
				this.loadError = '加载数据失败，请稍后重试';
				this.handleLoadError(error);
			} finally {
				this.loading = false;
				this.dataLoaded = true;
			}
		},

		// 统一的错误处理
		handleLoadError(error) {
			uni.showModal({
				title: '加载失败',
				content: this.loadError,
				showCancel: true,
				cancelText: '返回',
				confirmText: '重试',
				success: (res) => {
					if (res.confirm) {
						this.loadPageDataOptimized();
					}
				}
			});
		},

		// 保留原方法以防其他地方调用
		async loadPageData() {
			return this.loadPageDataOptimized();
		},

		// 优化的加载检查记录
		async loadInspectionRecordsOptimized() {
			try {
				// 获取所有责任区
				const areasResult = await callCloudFunction('hygiene-area-management', {
					action: 'getAreaList',
					data: { pageSize: 100 }
				});

				if (areasResult?.success) {
					const areas = this.extractAreas(areasResult.data);
					
					// 缓存责任区数据，用于后续查询排班日等信息
					this.areasCache = areas;
					
					// 批量获取清理记录和检查记录
					const [cleaningRecordsMap, inspectionRecordsMap] = await this.batchLoadRecords(areas);
					
					// 为每个责任区生成检查状态（现在每个责任区可能返回多条记录）
					const inspectionTasks = [];
					areas.forEach(area => {
						const areaRecords = this.processAreaInspection(
							area, 
							cleaningRecordsMap.get(area._id || area.id),
							inspectionRecordsMap.get(area._id || area.id)
						);
						inspectionTasks.push(...areaRecords); // 展开数组
					});

					this.inspectionRecords = inspectionTasks;
				} else {
					throw new Error(areasResult?.message || '获取责任区失败');
				}
			} catch (error) {
				this.inspectionRecords = [];
				throw error;
			}
		},

		// 提取责任区数据
		extractAreas(data) {
			if (!data) return [];
			if (Array.isArray(data)) return data;
			if (data.list && Array.isArray(data.list)) return data.list;
			return [];
		},

		// 批量加载清理和检查记录
		async batchLoadRecords(areas) {
			const areaIds = areas.map(area => area._id || area.id);
			
			// 并行获取所有清理记录和检查记录
			const [cleaningResults, inspectionResults] = await Promise.allSettled([
				this.batchGetCleaningRecords(areaIds),
				this.batchGetInspectionRecords(areaIds)
			]);

			// 存储所有记录，不只是最新的
			const cleaningRecordsMap = new Map();
			const inspectionRecordsMap = new Map();

			// 处理清理记录结果 - 存储所有记录
			if (cleaningResults.status === 'fulfilled' && cleaningResults.value) {
				cleaningResults.value.forEach(record => {
					const areaId = record.area_id;
					if (!cleaningRecordsMap.has(areaId)) {
						cleaningRecordsMap.set(areaId, []);
					}
					cleaningRecordsMap.get(areaId).push(record);
				});
				
				// 对每个责任区的记录按时间排序
				cleaningRecordsMap.forEach((records, areaId) => {
					records.sort((a, b) => new Date(b.cleaning_date) - new Date(a.cleaning_date));
				});
			}

			// 处理检查记录结果 - 存储所有记录
			if (inspectionResults.status === 'fulfilled' && inspectionResults.value) {
				inspectionResults.value.forEach(record => {
					const areaId = record.area_id;
					if (!inspectionRecordsMap.has(areaId)) {
						inspectionRecordsMap.set(areaId, []);
					}
					inspectionRecordsMap.get(areaId).push(record);
				});
				
				// 对每个责任区的记录按时间排序
				inspectionRecordsMap.forEach((records, areaId) => {
					records.sort((a, b) => new Date(b.inspection_date || b.created_at) - new Date(a.inspection_date || a.created_at));
				});
			}

			return [cleaningRecordsMap, inspectionRecordsMap];
		},

		// 批量获取清理记录
		async batchGetCleaningRecords(areaIds) {
			try {
				const timeRange = this.getTimeRangeForAPI();
				const result = await callCloudFunction('hygiene-cleaning', {
					action: 'getBatchCleaningRecords',
					data: {
						area_ids: areaIds,
						start_date: timeRange.start_date,
						end_date: timeRange.end_date,
						latest_only: false
					}
				});
				return result?.success ? result.data : [];
			} catch (error) {
				return [];
			}
		},

		// 批量获取检查记录
		async batchGetInspectionRecords(areaIds) {
			try {
				const timeRange = this.getTimeRangeForAPI();
				const promises = areaIds.map(areaId => 
					callCloudFunction('hygiene-inspection', {
						action: 'getInspectionRecords',
						data: { 
							area_id: areaId, 
							start_date: timeRange.start_date,
							end_date: timeRange.end_date,
							pageSize: 10 // 获取更多记录以支持时间范围查询
						}
					})
				);
				
				const results = await Promise.allSettled(promises);
				const records = [];
				
				results.forEach((result, index) => {
					if (result.status === 'fulfilled' && result.value?.success && result.value.data?.list?.length > 0) {
						// 获取时间范围内的所有记录
						result.value.data.list.forEach(record => {
							record.area_id = areaIds[index];
							records.push(record);
						});
					}
				});
				
				return records;
			} catch (error) {
				return [];
			}
		},

		// 根据当前时间筛选器获取API查询的时间范围
		getTimeRangeForAPI() {
			const now = new Date();
			let start_date, end_date;

			switch (this.selectedTimeFilter) {
				case 'week':
					start_date = this.getWeekStartOptimized(now);
					end_date = this.getWeekEndOptimized(now);
					break;
				case 'last_week':
					// 上周：上周的周一到周日
					const lastWeek = new Date(now);
					lastWeek.setDate(now.getDate() - 7);
					start_date = this.getWeekStartOptimized(lastWeek);
					end_date = this.getWeekEndOptimized(lastWeek);
					break;
				case 'month':
					start_date = new Date(now.getFullYear(), now.getMonth(), 1);
					end_date = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
					break;
				case 'custom':
					// 自定义：使用选择的日期范围
					if (this.customStartDate && this.customEndDate) {
						start_date = new Date(this.customStartDate);
						start_date.setHours(0, 0, 0, 0);
						end_date = new Date(this.customEndDate);
						end_date.setHours(23, 59, 59, 999);
					} else {
						// 默认本周
						start_date = this.getWeekStartOptimized(now);
						end_date = this.getWeekEndOptimized(now);
					}
					break;
				default:
					start_date = this.getWeekStartOptimized(now);
					end_date = this.getWeekEndOptimized(now);
			}

			console.log(`时间筛选器: ${this.selectedTimeFilter}`);
			console.log(`API查询时间范围: ${start_date.toISOString()} 到 ${end_date.toISOString()}`);
			console.log(`查询月份: ${start_date.getMonth() + 1}月1日 到 ${end_date.getMonth() + 1}月${end_date.getDate()}日`);

			return {
				start_date: start_date.toISOString(),
				end_date: end_date.toISOString()
			};
		},

		// 处理单个责任区的检查状态 - 生成多条历史记录
		processAreaInspection(area, cleaningRecords, inspectionRecords) {
			const areaId = area._id || area.id;
			const allRecords = [];
			
			// 获取所有清理记录和检查记录，按时间组织
			const cleaningList = cleaningRecords || [];
			const inspectionList = inspectionRecords || [];
			
			console.log(`处理责任区 ${area.name}:`);
			console.log(`- 清理记录数量: ${cleaningList.length}`);
			console.log(`- 检查记录数量: ${inspectionList.length}`);
			if (cleaningList.length > 0) {
				console.log(`- 清理记录时间: ${cleaningList.map(r => r.cleaning_date).join(', ')}`);
			}
			if (inspectionList.length > 0) {
				console.log(`- 检查记录时间: ${inspectionList.map(r => r.inspection_date || r.created_at).join(', ')}`);
			}
			
			// 根据时间筛选器决定要生成哪些周次的记录
			const weeksToGenerate = this.getWeeksToGenerate();
			console.log(`  - 需要生成的周次: ${weeksToGenerate.join(', ')}`);
			
			// 为每个周次生成记录
			weeksToGenerate.forEach(weekKey => {
				// 查找该周的清理记录
				const weekCleaningRecords = cleaningList.filter(record => {
					const recordWeek = this.getWeekKey(record.cleaning_date);
					return recordWeek === weekKey;
				});
				
				// 查找该周的检查记录
				const weekInspectionRecords = inspectionList.filter(record => {
					const recordWeek = this.getWeekKey(record.inspection_date || record.created_at);
					return recordWeek === weekKey;
				});
				
				// 生成该周的记录
				const weekRecord = this.generateWeekRecord(area, weekKey, weekCleaningRecords, weekInspectionRecords);
				if (weekRecord) {
					allRecords.push(weekRecord);
				}
			});
			
			console.log(`  - 生成记录数量: ${allRecords.length}`);
			if (allRecords.length > 0) {
				console.log(`  - 记录周次: ${allRecords.map(r => r.week).join(', ')}`);
			}
			return allRecords;
		},

		// 根据时间筛选器获取需要生成的周次
		getWeeksToGenerate() {
			const now = new Date();
			const currentWeek = this.getWeekNumber(now);
			const currentYear = now.getFullYear();
			const weeks = new Set(); // 使用Set防止重复

			switch (this.selectedTimeFilter) {
				case 'week':
					// 本周：只生成当前周
					weeks.add(`${currentYear}-W${currentWeek.toString().padStart(2, '0')}`);
					break;
				case 'last_week':
					// 上周：生成上周
					const lastWeek = new Date(now);
					lastWeek.setDate(now.getDate() - 7);
					const lastWeekNum = this.getWeekNumber(lastWeek);
					const lastWeekYear = lastWeek.getFullYear();
					weeks.add(`${lastWeekYear}-W${lastWeekNum.toString().padStart(2, '0')}`);
					break;
				case 'month':
					// 本月：生成包含本月任何一天的所有周次
					const monthStart = new Date(currentYear, now.getMonth(), 1);
					const monthEnd = new Date(currentYear, now.getMonth() + 1, 0);
					
					console.log(`月份范围: ${monthStart.toLocaleString()} - ${monthEnd.toLocaleString()}`);
					
					// 遍历本月每一天，找出所有涉及的周次
					const currentDate = new Date(monthStart);
					while (currentDate <= monthEnd) {
						const weekKey = this.getWeekKeyByDate(currentDate);
						weeks.add(weekKey);
						currentDate.setDate(currentDate.getDate() + 1);
					}
					
					// 额外检查月初和月末所在周是否完整包含
					// 如果月初的周一在上个月，也要包含那一周
					const monthStartWeekKey = this.getWeekKeyByDate(monthStart);
					const monthStartWeekStart = this.getWeekStartByWeekKey(monthStartWeekKey);
					if (monthStartWeekStart < monthStart) {
						weeks.add(monthStartWeekKey);
					}
					
					// 如果月末的周日在下个月，也要包含那一周
					const monthEndWeekKey = this.getWeekKeyByDate(monthEnd);
					const monthEndWeekEnd = this.getWeekEndByWeekKey(monthEndWeekKey);
					if (monthEndWeekEnd > monthEnd) {
						weeks.add(monthEndWeekKey);
					}
					
					console.log(`本月包含周次: ${Array.from(weeks).join(', ')}`);
					break;
				case 'quarter':
					// 本季度：生成本季度的所有周次
					const quarter = Math.ceil((now.getMonth() + 1) / 3);
					const quarterStart = new Date(currentYear, (quarter - 1) * 3, 1);
					const quarterEnd = new Date(currentYear, quarter * 3, 0);
					
					const qCurrentDate = new Date(quarterStart);
					while (qCurrentDate <= quarterEnd) {
						const weekKey = this.getWeekKeyByDate(qCurrentDate);
						weeks.add(weekKey);
						qCurrentDate.setDate(qCurrentDate.getDate() + 7); // 每周递增
					}
					break;
				case 'year':
					// 本年度：生成本年度的所有周次
					for (let week = 1; week <= 53; week++) { // 某些年份有53周
						const weekKey = `${currentYear}-W${week.toString().padStart(2, '0')}`;
						// 验证这个周次是否存在
						try {
							this.getWeekStartByWeekKey(weekKey);
							weeks.add(weekKey);
						} catch (e) {
							// 第53周不存在就跳过
							break;
						}
					}
					break;
				case 'custom':
					// 自定义：根据选择的日期范围生成周次
					if (this.customStartDate && this.customEndDate) {
						const startDate = new Date(this.customStartDate);
						const endDate = new Date(this.customEndDate);
						
						const currentDate = new Date(startDate);
						while (currentDate <= endDate) {
							const weekKey = this.getWeekKeyByDate(currentDate);
							weeks.add(weekKey);
							currentDate.setDate(currentDate.getDate() + 1);
						}
					} else {
						// 如果没有选择日期，默认本周
						weeks.add(`${currentYear}-W${currentWeek.toString().padStart(2, '0')}`);
					}
					break;
				default:
					weeks.add(`${currentYear}-W${currentWeek.toString().padStart(2, '0')}`);
			}

			return Array.from(weeks).sort(); // 转为数组并排序
		},

		// 为指定周次生成记录
		generateWeekRecord(area, weekKey, cleaningRecords, inspectionRecords) {
			const areaId = area._id || area.id;
			const now = new Date();
			
			// 计算该周次对于该责任区的实际时间范围
			const { periodStart, periodEnd } = this.getAreaPeriodRange(area, weekKey);
			
			if (cleaningRecords.length > 0 || inspectionRecords.length > 0) {
				// 有数据的周次：基于实际记录生成
				if (cleaningRecords.length > 0) {
					// 使用最新的清理记录
					const latestCleaning = cleaningRecords[0];
					const relatedInspection = inspectionRecords.find(inspection => {
						const inspectionDate = new Date(inspection.inspection_date || inspection.created_at);
						const cleaningDate = new Date(latestCleaning.cleaning_date);
						return inspectionDate >= cleaningDate;
					});
					
					return this.generateRecordFromCleaning(area, latestCleaning, relatedInspection, weekKey);
				} else if (inspectionRecords.length > 0) {
					// 只有检查记录的情况
					return this.generateRecordFromInspection(area, inspectionRecords[0], weekKey);
				}
			} else {
				// 没有数据的周次：根据时间和责任区类型生成正确的状态
				return this.generateDefaultRecord(area, weekKey, periodStart, periodEnd, now);
			}
		},

		// 获取责任区在指定周次的实际时间范围
		getAreaPeriodRange(area, weekKey) {
			const weekStart = this.getWeekStartByWeekKey(weekKey);
			const weekEnd = this.getWeekEndByWeekKey(weekKey);
			
			if (area.type === 'fixed') {
				// 固定责任区：周一0点到周日23:59
				console.log(`固定责任区 ${area.name} 周期: ${weekStart.toLocaleString()} - ${weekEnd.toLocaleString()}`);
				return {
					periodStart: weekStart,
					periodEnd: weekEnd
				};
			} else if (area.type === 'public') {
				// 公共责任区：根据scheduled_day设置的具体某天0点到23:59
				const scheduledDay = area.scheduled_day || 1; // 默认周一
				
				// 计算该周内排班日的时间范围
				const periodStart = new Date(weekStart);
				const dayOffset = scheduledDay === 0 ? 6 : scheduledDay - 1; // 0=周日转为6，1-6保持不变
				periodStart.setDate(weekStart.getDate() + dayOffset);
				periodStart.setHours(0, 0, 0, 0);
				
				const periodEnd = new Date(periodStart);
				periodEnd.setHours(23, 59, 59, 999);
				
				const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
				console.log(`公共责任区 ${area.name} 排班${dayNames[scheduledDay]}周期: ${periodStart.toLocaleString()} - ${periodEnd.toLocaleString()}`);
				
				return {
					periodStart,
					periodEnd
				};
			}
			
			// 默认使用周期范围
			return {
				periodStart: weekStart,
				periodEnd: weekEnd
			};
		},

		// 生成默认记录（无数据时）
		generateDefaultRecord(area, weekKey, periodStart, periodEnd, now) {
			const areaId = area._id || area.id;
			let status, subtitle, clickable = true;
			
			console.log(`生成默认记录 ${area.name} - 周期: ${periodStart.toLocaleString()} 到 ${periodEnd.toLocaleString()}, 当前时间: ${now.toLocaleString()}`);
			
			if (now > periodEnd) {
				// 已过期：该时间段已结束
				clickable = false; // 历史记录不可点击
				if (area.type === 'fixed') {
					status = 'expired_not_cleaned';
					subtitle = '该周期未打扫（已过期）';
				} else {
					const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
					const scheduledDayName = dayNames[area.scheduled_day || 1];
					status = 'expired_not_cleaned';
					subtitle = `${scheduledDayName}未打扫（已过期）`;
				}
			} else if (now >= periodStart && now <= periodEnd) {
				// 进行中：当前在该时间段内
				if (area.type === 'fixed') {
					status = 'not_cleaned';
					subtitle = '本周未开始打扫';
				} else {
					const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
					const scheduledDayName = dayNames[area.scheduled_day || 1];
					status = 'not_cleaned';
					subtitle = `今日(${scheduledDayName})未开始打扫`;
				}
			} else {
				// 未开始：该时间段还未到
				if (area.type === 'fixed') {
					status = 'not_cleaned';
					subtitle = '该周期尚未开始';
				} else {
					const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
					const scheduledDayName = dayNames[area.scheduled_day || 1];
					status = 'not_cleaned';
					subtitle = `等待${scheduledDayName}打扫`;
				}
			}
			
			return {
				id: `${areaId}_${weekKey}`,
				areaName: area.name || '未知责任区',
				subtitle,
				status,
				icon: this.getStatusIcon(status),
				type: area.type || 'fixed',
				week: weekKey,
				inspectionDate: null,
				isRectificationRecheck: false,
				areaId,
				inspectorName: '检查员',
				inspectionRecordId: null,
				isInspectionRecord: false,
				scheduledDay: area.scheduled_day || null,
				clickable: clickable // 添加可点击状态
			};
		},

		// 根据周次键值获取周开始时间
		getWeekStartByWeekKey(weekKey) {
			const match = weekKey.match(/(\d{4})-W(\d{2})/);
			if (!match) return new Date();
			
			const year = parseInt(match[1]);
			const week = parseInt(match[2]);
			
			const weekStart = this.getDateFromWeek(year, week);
			console.log(`解析周次 ${weekKey}: 年=${year}, 周=${week}, 开始时间=${weekStart.toLocaleString()}`);
			return weekStart;
		},

		// 根据周次键值获取周结束时间
		getWeekEndByWeekKey(weekKey) {
			const weekStart = this.getWeekStartByWeekKey(weekKey);
			const weekEnd = new Date(weekStart);
			weekEnd.setDate(weekEnd.getDate() + 6);
			weekEnd.setHours(23, 59, 59, 999);
			return weekEnd;
		},

		// 根据日期获取周次键值
		getWeekKeyByDate(date) {
			const year = date.getFullYear();
			const weekNum = this.getWeekNumber(date);
			return `${year}-W${weekNum.toString().padStart(2, '0')}`;
		},

		// 基于清理记录生成检查记录
		generateRecordFromCleaning(area, cleaningRecord, inspectionRecord, weekKey) {
			const areaId = area._id || area.id;
			const cleaningDate = new Date(cleaningRecord.cleaning_date);
			const now = new Date();
			
			// 获取该责任区在该周次的时间范围
			const { periodStart, periodEnd } = this.getAreaPeriodRange(area, weekKey);
			
			let status, subtitle;
			
			if (inspectionRecord) {
				// 有对应的检查记录
				const inspectionDate = new Date(inspectionRecord.inspection_date || inspectionRecord.created_at);
				({ status, subtitle } = this.processInspectionStatus(inspectionRecord, inspectionDate, area, cleaningRecord));
			} else {
				// 没有检查记录，需要根据时间判断是否超时
				console.log(`检查员检查判断 ${area.name} - 清理时间: ${cleaningDate.toLocaleString()}, 周期结束: ${periodEnd.toLocaleString()}, 当前时间: ${now.toLocaleString()}`);
				
				if (area.type === 'fixed') {
					// 固定责任区：超过周日23:59算漏检查
					if (now > periodEnd) {
						status = 'inspector_missed';
						subtitle = `检查员漏检查 · ${this.formatDateTimeOptimized(cleaningDate)}`;
					} else {
						status = 'pending';
						subtitle = `员工已提交，待检查 · ${this.formatDateTimeOptimized(cleaningDate)}`;
					}
				} else {
					// 公共责任区：超过排班日23:59算漏检查
					if (now > periodEnd) {
						const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
						const scheduledDayName = dayNames[area.scheduled_day || 1];
						status = 'inspector_missed';
						subtitle = `检查员漏检查(${scheduledDayName}) · ${this.formatDateTimeOptimized(cleaningDate)}`;
					} else {
						status = 'pending';
						subtitle = `员工已提交，待检查 · ${this.formatDateTimeOptimized(cleaningDate)}`;
					}
				}
			}
			
			return {
				id: inspectionRecord ? (inspectionRecord._id || inspectionRecord.id) : `${areaId}_${weekKey}`,
				areaName: area.name || '未知责任区',
				subtitle,
				status,
				icon: this.getStatusIcon(status),
				type: area.type || 'fixed',
				week: weekKey,
				inspectionDate: inspectionRecord ? (inspectionRecord.inspection_date || inspectionRecord.created_at) : null,
				isRectificationRecheck: status === 'pending_review',
				areaId,
				inspectorName: '检查员',
				inspectionRecordId: inspectionRecord ? (inspectionRecord._id || inspectionRecord.id) : null,
				isInspectionRecord: !!inspectionRecord,
				scheduledDay: area.scheduled_day || null
			};
		},

		// 基于检查记录生成检查记录（独立的检查记录）
		generateRecordFromInspection(area, inspectionRecord, weekKey) {
			const areaId = area._id || area.id;
			const inspectionDate = new Date(inspectionRecord.inspection_date || inspectionRecord.created_at);
			
			const { status, subtitle } = this.processInspectionStatus(inspectionRecord, inspectionDate, area, null);
			
			return {
				id: inspectionRecord._id || inspectionRecord.id,
				areaName: area.name || '未知责任区',
				subtitle,
				status,
				icon: this.getStatusIcon(status),
				type: area.type || 'fixed',
				week: weekKey,
				inspectionDate: inspectionRecord.inspection_date || inspectionRecord.created_at,
				isRectificationRecheck: status === 'pending_review',
				areaId,
				inspectorName: '检查员',
				inspectionRecordId: inspectionRecord._id || inspectionRecord.id,
				isInspectionRecord: true,
				scheduledDay: area.scheduled_day || null
			};
		},



		// 处理检查状态
		processInspectionStatus(lastInspection, inspectionDate, area, lastCleaningRecord) {
			let status, subtitle;
			const now = new Date();
			
			// 获取该记录所在周次
			const weekKey = this.getWeekKeyByDate(inspectionDate);
			const { periodEnd } = this.getAreaPeriodRange(area, weekKey);
			
			console.log(`处理检查状态 ${area.name} - 检查时间: ${inspectionDate.toLocaleString()}, 周期结束: ${periodEnd.toLocaleString()}, 检查状态: ${lastInspection.status}`);
			
			if (lastInspection.status === 'pending_rectification') {
				status = 'pending_rectification';
				subtitle = `检查未通过，待整改 · ${this.formatDateTimeOptimized(inspectionDate)}`;
			} else if (lastInspection.status === 'rectification_completed') {
				// 员工已提交整改，判断检查员是否及时复查
				if (now > periodEnd) {
					// 超过该周期，检查员漏检查
					status = 'inspector_missed';
					subtitle = `检查员漏复查 · ${this.formatDateTimeOptimized(inspectionDate)}`;
				} else {
					// 在周期内，待复查
					status = 'pending_review';
					subtitle = `整改已提交，待复查 · ${this.formatDateTimeOptimized(inspectionDate)}`;
				}
			} else if (lastInspection.status === 'verified') {
				status = 'completed';
				subtitle = `整改已确认，检查完成 · ${this.formatDateTimeOptimized(inspectionDate)}`;
			} else if (lastInspection.status === 'completed') {
				status = 'completed';
				subtitle = `已完成检查 · ${this.formatDateTimeOptimized(inspectionDate)}`;
			} else {
				status = 'completed';
				subtitle = `已完成检查 · ${this.formatDateTimeOptimized(inspectionDate)}`;
			}
			
			return { status, subtitle };
		},

		// 保留原方法以防其他地方调用
		async loadInspectionRecords() {
			return this.loadInspectionRecordsOptimized();
		},

		// 优化的获取本周开始时间
		getWeekStartOptimized(date = new Date()) {
			const d = new Date(date);
			const day = d.getDay();
			const diff = d.getDate() - day + (day === 0 ? -6 : 1);
			const monday = new Date(d);
			monday.setDate(diff);
			monday.setHours(0, 0, 0, 0);
			return monday;
		},

		// 优化的获取本周结束时间
		getWeekEndOptimized(date = new Date()) {
			const d = new Date(date);
			const day = d.getDay();
			const diff = d.getDate() - day + (day === 0 ? 0 : 7);
			const sunday = new Date(d);
			sunday.setDate(diff);
			sunday.setHours(23, 59, 59, 999);
			return sunday;
		},

		// 保留原方法以防其他地方调用
		getWeekStart(date = new Date()) {
			return this.getWeekStartOptimized(date);
		},

		getWeekEnd(date = new Date()) {
			return this.getWeekEndOptimized(date);
		},

		// 优化的计算漏检状态
		calculateMissedStatusOptimized(area, lastCleaningRecord, lastInspectionRecord) {
			const { now, weekStart, weekEnd } = this.processCache.timeCalculations;
			
			// 1. 员工已提交清理，但检查员超时未检查
			if (lastCleaningRecord) {
				const cleaningDate = new Date(lastCleaningRecord.cleaning_date);
				
				// 只判断本周期内的清理记录
				if (cleaningDate >= weekStart && cleaningDate <= weekEnd) {
					// 检查是否有对应的检查记录
					let hasValidInspection = false;
					if (lastInspectionRecord) {
						const inspectionDate = new Date(lastInspectionRecord.inspection_date || lastInspectionRecord.created_at);
						// 检查记录必须在清理记录之后
						hasValidInspection = inspectionDate >= cleaningDate;
					}
					
					// 如果没有有效检查记录，判断是否漏检
					if (!hasValidInspection) {
						if (area.type === 'fixed') {
							// 固定责任区：过了周日23:59:59（即到了下周一）才算漏检
							const nextWeekStart = new Date(weekEnd);
							nextWeekStart.setDate(nextWeekStart.getDate() + 1); // 下周一00:00:00
							
							if (now >= nextWeekStart) {
								return 'missed';
							}
						} else if (area.type === 'public') {
							// 公共责任区：过了排班日当天23:59:59才算漏检
							const scheduledDay = area.scheduled_day === 0 ? 7 : (area.scheduled_day || 1);
							const currentDay = now.getDay() === 0 ? 7 : now.getDay();
							
							// 如果当前日期已经超过排班日，算漏检
							if (currentDay > scheduledDay) {
								return 'missed';
							}
						}
					}
				}
			}
			
			// 2. 员工已提交整改，但检查员超时未复查
			if (lastInspectionRecord && lastInspectionRecord.status === 'rectification_completed') {
				// 查找整改提交时间
				const submitTime = lastInspectionRecord.rectification_submit_time || lastInspectionRecord.updated_at;
				if (submitTime) {
					const submitDate = new Date(submitTime);
					
					// 整改复查也应该遵循周期重置逻辑
					// 如果整改提交在本周内，检查员应该在本周内复查完成
					if (submitDate >= weekStart && submitDate <= weekEnd) {
						// 如果已经到了下周，且整改是在上周提交的，算漏检
						const nextWeekStart = new Date(weekEnd);
						nextWeekStart.setDate(nextWeekStart.getDate() + 1); // 下周一00:00:00
						
						if (now >= nextWeekStart) {
							return 'missed';
						}
					}
				}
			}
			
			return null; // 不是漏检状态
		},

		// 保留原方法以防其他地方调用
		calculateMissedStatus(area, lastCleaningRecord, lastInspectionRecord) {
			return this.calculateMissedStatusOptimized(area, lastCleaningRecord, lastInspectionRecord);
		},

		// 映射检查状态
		mapInspectionStatus(record) {
			// 根据记录的状态和相关信息确定显示状态
			if (record.status === 'completed') {
				return 'completed';
			} else if (record.status === 'pending') {
				return 'pending';
			} else if (record.cleaning_status === 'not_cleaned') {
				return 'not_cleaned';
			} else if (record.rectification_required) {
				return 'pending_rectification';
			} else if (record.status === 'missed' || record.is_overdue) {
				return 'missed';
			}
			return 'pending';
		},

		// 生成记录副标题
		generateRecordSubtitle(record) {
			const status = this.mapInspectionStatus(record);
			const inspectorName = record.inspector_name || record.created_by_name || '未知检查员';
			
			const statusTexts = {
				'completed': '已完成检查',
				'pending': record.is_rectification_recheck ? '整改后待复检' : '员工已提交，待检查',
				'not_cleaned': '本周未开始打扫',
				'pending_rectification': '检查未通过，待整改',
				'missed': '该责任区漏检查'
			};
			
			const dateStr = record.inspection_date ? 
				` · ${this.formatDateTime(record.inspection_date)}` : '';
			
			return `检查员：${inspectorName} · ${statusTexts[status]}${dateStr}`;
		},

		// 获取周次键值
		getWeekKey(dateString) {
			if (!dateString) return this.getCurrentWeekKey();
			const date = new Date(dateString);
			const year = date.getFullYear();
			const weekNum = this.getWeekNumber(date);
			return `${year}-W${weekNum.toString().padStart(2, '0')}`;
		},

		// 优化的获取当前周键值
		getCurrentWeekKeyOptimized(date = new Date()) {
			const cacheKey = date.toDateString();
			if (this.processCache.weekKeyCache.has(cacheKey)) {
				return this.processCache.weekKeyCache.get(cacheKey);
			}
			
			const year = date.getFullYear();
			const weekNum = this.getWeekNumber(date);
			const weekKey = `${year}-W${weekNum.toString().padStart(2, '0')}`;
			
			this.processCache.weekKeyCache.set(cacheKey, weekKey);
			return weekKey;
		},

		// 保留原方法以防其他地方调用
		getCurrentWeekKey() {
			return this.getCurrentWeekKeyOptimized();
		},

		// 获取日期对应的周数（ISO 8601标准）
		getWeekNumber(date) {
			// 使用ISO 8601标准的周次计算
			const target = new Date(date.valueOf());
			const dayNumber = (date.getDay() + 6) % 7; // 周一为0，周日为6
			target.setDate(target.getDate() - dayNumber + 3); // 移到该周的周四
			const firstThursday = target.valueOf();
			target.setMonth(0, 1); // 移到该年的1月1日
			if (target.getDay() !== 4) {
				target.setMonth(0, 1 + ((4 - target.getDay()) + 7) % 7); // 移到该年的第一个周四
			}
			return 1 + Math.ceil((firstThursday - target) / (7 * 24 * 60 * 60 * 1000));
		},

		// 根据年份和周次获取该周的日期
		getDateFromWeek(year, week) {
			// ISO 8601 标准：第一周包含该年第4天（1月4日）
			// 先找到1月4日，然后找到它所在周的周一
			const jan4 = new Date(year, 0, 4);
			const jan4Day = jan4.getDay();
			
			// 计算1月4日所在周的周一
			const firstMonday = new Date(jan4);
			const daysToSubtract = jan4Day === 0 ? 6 : jan4Day - 1; // 0=周日需要减6天
			firstMonday.setDate(jan4.getDate() - daysToSubtract);
			
			// 计算目标周的周一
			const targetWeek = new Date(firstMonday);
			targetWeek.setDate(firstMonday.getDate() + (week - 1) * 7);
			targetWeek.setHours(0, 0, 0, 0);
			
			console.log(`ISO8601计算: ${year}年第${week}周 = ${targetWeek.toLocaleString()}`);
			return targetWeek;
		},

		getCurrentTimeRange() {
			if (this.selectedTimeFilter === 'custom' && this.customStartDate && this.customEndDate) {
				const startMonth = new Date(this.customStartDate).getMonth() + 1;
				const startDay = new Date(this.customStartDate).getDate();
				const endMonth = new Date(this.customEndDate).getMonth() + 1;
				const endDay = new Date(this.customEndDate).getDate();
				return `${startMonth}/${startDay} - ${endMonth}/${endDay}`;
			}
			const option = this.timeOptions.find(opt => opt.value === this.selectedTimeFilter);
			return option ? option.label : '本周';
		},
		
		// 获取状态筛选文本
		getStatusFilterText() {
			const statusTexts = {
				'not_cleaned': '未打扫',
				'pending': '待检查',
				'pending_rectification': '待整改',
				'pending_review': '待复查',
				'completed': '已完成',
				'missed': '漏检查'
			};
			return statusTexts[this.selectedStatus] || '';
		},

		getStatsData() {
			// 基于实际筛选的记录计算统计 - 包含新状态
			const records = this.filteredRecords;
			const not_cleaned = records.filter(r => r.status === 'not_cleaned' || r.status === 'expired_not_cleaned').length;
			const pending = records.filter(r => r.status === 'pending').length;
			const pending_rectification = records.filter(r => r.status === 'pending_rectification').length;
			const pending_review = records.filter(r => r.status === 'pending_review').length;
			const completed = records.filter(r => r.status === 'completed').length;
			const missed = records.filter(r => r.status === 'missed' || r.status === 'inspector_missed').length;
			
			console.log('统计数据:', { not_cleaned, pending, pending_rectification, pending_review, completed, missed });
			console.log('记录详情:', records.map(r => ({ name: r.areaName, status: r.status, week: r.week })));
			
			return { not_cleaned, pending, pending_rectification, pending_review, completed, missed };
		},
		// 选择分类
		selectCategory(category) {
			this.selectedCategory = category;
		},
		
		// 选择状态筛选
		selectStatus(status) {
			// 如果点击的是当前选中的状态，则取消筛选（回到全部）
			if (this.selectedStatus === status) {
				this.selectedStatus = 'all';
			} else {
				this.selectedStatus = status;
			}
		},
		// 获取分类数量
		getCategoryCount(category) {
			if (category === 'all') {
				return this.filteredRecords.length;
			}
			return this.filteredRecords.filter(r => r.type === category).length;
		},
		// 获取空状态文本
		getEmptyText() {
			try {
				if (this.selectedCategory === 'all') {
					return '暂无检查记录';
				}
				const category = this.categoryTabs.find(t => t.value === this.selectedCategory);
				const categoryLabel = category ? category.label : '该分类';
				return `暂无${categoryLabel}记录`;
			} catch (error) {
				return '暂无数据';
			}
		},
		// 处理记录点击
		handleRecordClick(record) {
			// 检查记录是否可点击
			if (record.clickable === false || record.status === 'expired_not_cleaned') {
				// 历史过期记录不可点击
				uni.showModal({
					title: '历史记录',
					content: '该记录已过期，无法进行操作',
					showCancel: false
				});
				return;
			}
			
			if (record.status === 'pending') {
				// 待检查 - 可以开始检查
				this.startInspection(record);
			} else if (record.status === 'completed') {
				// 已完成 - 查看检查记录详情
				this.viewRecordDetail(record);
			} else if (record.status === 'not_cleaned') {
				// 未打扫 - 需要根据责任区类型和时效判断
				this.handleNotCleanedClick(record);
			} else if (record.status === 'pending_rectification') {
				// 待整改 - 跳转到检查员查看整改详情页面
				this.viewRectificationDetail(record);
			} else if (record.status === 'pending_review') {
				// 待复查 - 直接查找整改任务ID并跳转到复查页面
				this.reviewRectificationFromRecord(record);
			} else if (record.status === 'missed') {
				// 漏检查 - 历史状态，提示已锁定
				uni.showModal({
					title: '记录已锁定',
					content: '该责任区已漏检查，历史记录已锁定',
					showCancel: false
				});
			} else if (record.status === 'inspector_missed') {
				// 检查员漏检查 - 历史状态，提示已锁定
				uni.showModal({
					title: '检查员漏检查',
					content: '检查员未及时检查，历史记录已锁定',
					showCancel: false
				});
			}
		},

		// 处理未打扫状态的点击
		handleNotCleanedClick(record) {
			const { title, content } = this.getNotCleanedMessage(record);
			uni.showModal({
				title,
				content,
				showCancel: false
			});
		},

		// 获取未打扫状态的提示信息
		getNotCleanedMessage(record) {
			const now = new Date();
			const currentDay = now.getDay(); // 0=周日, 1=周一, ..., 6=周六
			
			if (record.type === 'public') {
				// 公共责任区：根据排班日判断
				const scheduledDay = record.scheduledDay;
				
				if (scheduledDay !== null && scheduledDay !== undefined) {
					// 转换：0=周日转为7，保持1-6不变
					const normalizedScheduledDay = scheduledDay === 0 ? 7 : scheduledDay;
					const normalizedCurrentDay = currentDay === 0 ? 7 : currentDay;
					
					const dayNames = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
					const scheduledDayName = dayNames[normalizedScheduledDay];
					
					if (normalizedCurrentDay > normalizedScheduledDay) {
						// 已过排班日
						return {
							title: '逾期未打扫',
							content: `该公共责任区排班日为${scheduledDayName}，现已逾期，已进入下个周期`
						};
					} else if (normalizedCurrentDay === normalizedScheduledDay) {
						// 当天
						const currentHour = now.getHours();
						if (currentHour >= 20) { // 晚上8点后算即将逾期
							return {
								title: '即将逾期',
								content: `该公共责任区排班日为今天（${scheduledDayName}），员工需尽快完成打扫`
							};
						} else {
							return {
								title: '员工未打扫',
								content: `该公共责任区排班日为今天（${scheduledDayName}），请等待员工完成`
							};
						}
					} else {
						// 还未到排班日
						return {
							title: '员工未打扫',
							content: `该公共责任区排班日为${scheduledDayName}，请等待员工完成`
						};
					}
				} else {
					// 没有排班日信息，使用默认提示
					return {
						title: '员工未打扫',
						content: '该公共责任区员工尚未开始打扫，请等待员工完成'
					};
				}
			} else {
				// 固定责任区：判断是否过了本周日
				const weekEnd = this.getWeekEndOptimized(now);
				
				if (now > weekEnd) {
					// 已过本周日
					return {
						title: '逾期未打扫',
						content: '该固定责任区本周期已结束，已进入下周期'
					};
				} else {
					// 本周内
					const remainingDays = Math.ceil((weekEnd - now) / (24 * 60 * 60 * 1000));
					if (remainingDays <= 1) {
						return {
							title: '即将逾期',
							content: '该固定责任区本周期即将结束，员工需尽快完成打扫'
						};
					} else {
						return {
							title: '员工未打扫',
							content: '该固定责任区员工尚未开始打扫，请等待员工完成'
						};
					}
				}
			}
		},

		// 获取责任区的排班日（这里需要根据实际数据结构调整）
		getAreaScheduledDay(areaId) {
			// 这里应该从责任区数据中获取scheduled_day
			// 暂时返回null，需要在loadInspectionRecordsOptimized中补充这个信息
			const area = this.areasCache?.find(a => (a._id || a.id) === areaId);
			return area?.scheduled_day || null;
		},

		showTimeSelector() {
			this.$refs.timePopup.open();
		},

		closeTimeSelector() {
			this.$refs.timePopup.close();
		},
		
		// 获取状态图标
		getStatusIcon(status) {
			const icons = {
				'completed': 'checkmarkempty',
				'pending': 'calendar',
				'not_cleaned': 'minus',
				'expired_not_cleaned': 'minus-circled', // 已过期未打扫
				'pending_rectification': 'info',
				'pending_review': 'eye',
				'missed': 'close',
				'inspector_missed': 'alert', // 检查员漏检查
				'passed': 'checkmarkempty',
				'failed': 'close'
			};
			return icons[status] || 'help';
		},
		
		// 获取问题类型中文显示
		getCategoryText(category) {
			const categoryMap = {
				'cleanliness': '清洁问题',
				'safety': '安全问题', 
				'equipment': '设备问题',
				'environment': '环境问题',
				'organization': '整理问题',
				'standardization': '标识问题',
				'other': '其他问题'
			};
			return categoryMap[category] || '其他';
		},
		
		// 获取周标题
		getWeekTitle(weekKey) {
			// 解析weekKey，例如 '2025-W03' -> 2025年第3周
			const match = weekKey.match(/(\d{4})-W(\d{2})/);
			if (!match) return weekKey;
			
			const year = parseInt(match[1]);
			const weekNum = parseInt(match[2]);
			const currentYear = new Date().getFullYear();
			const currentWeek = this.getWeekNumber(new Date());
			
			// 判断是否为当前周
			if (year === currentYear && weekNum === currentWeek) {
				const weekRange = this.getCurrentWeekRange();
				return `本周 (${weekRange.start}-${weekRange.end})`;
			} else if (year === currentYear && weekNum === currentWeek - 1) {
				return `上周 (第${weekNum}周)`;
			} else if (year === currentYear) {
				return `第${weekNum}周 (${year}年)`;
			} else {
				return `第${weekNum}周 (${year}年)`;
			}
		},
		
		// 切换周展开状态
		toggleWeek(weekKey) {
			const index = this.expandedWeeks.indexOf(weekKey);
			if (index > -1) {
				this.expandedWeeks.splice(index, 1);
			} else {
				this.expandedWeeks.push(weekKey);
			}
		},
		selectAndConfirmTimeFilter(option) {
			if (option.value === 'custom') {
				// 如果选择自定义，显示日期选择器
				this.showCustomDatePicker();
			} else {
				this.selectedTimeFilter = option.value;
				this.$refs.timePopup.close();
				// 重新加载数据
				this.loadPageDataOptimized();
			}
		},

		// 显示自定义日期选择器
		showCustomDatePicker() {
			this.$refs.timePopup.close();
			this.showDatePicker = true;
		},

		// 确认自定义日期选择
		confirmCustomDate() {
			if (this.customStartDate && this.customEndDate) {
				const startDate = new Date(this.customStartDate);
				const endDate = new Date(this.customEndDate);
				
				if (startDate > endDate) {
					uni.showToast({
						title: '开始日期不能大于结束日期',
						icon: 'none'
					});
					return;
				}
				
				this.selectedTimeFilter = 'custom';
				this.showDatePicker = false;
				this.loadPageDataOptimized();
			} else {
				uni.showToast({
					title: '请选择完整的日期范围',
					icon: 'none'
				});
			}
		},

		// 取消自定义日期选择
		cancelCustomDate() {
			this.showDatePicker = false;
			this.customStartDate = '';
			this.customEndDate = '';
		},
		// 优化的状态文本获取（使用缓存）
		getStatusText(status) {
			return this.processCache.statusMap[status] || status;
		},

		getIconColor(status) {
			const colorMap = {
				'not_cleaned': '#8E8E93',
				'expired_not_cleaned': '#323232',
				'pending': '#FF9500',
				'pending_rectification': '#FF3B30',
				'pending_review': '#007AFF',
				'completed': '#34C759',
				'missed': '#8B5CF6',
				'inspector_missed': '#FFC107',
				'passed': '#34C759',
				'failed': '#FF3B30',
				'issues': '#FF3B30'
			};
			return colorMap[status] || '#8E8E93';
		},
		startInspection(record) {
			// 对于待检查状态，使用责任区ID
			const areaId = record.areaId || record.id;
			// 跳转到检查页面时，标记需要刷新数据
			this.needsRefresh = true;
			uni.navigateTo({
				url: `/pages/6s_pkg/inspection-detail?id=${areaId}&isRectification=${record.isRectificationRecheck || false}`
			});
		},
		viewRecordDetail(record) {
			// 如果有检查记录ID，说明这是基于实际检查记录的状态
			if (record.isInspectionRecord && record.inspectionRecordId) {
				uni.navigateTo({
					url: `/pages/6s_pkg/record-detail?id=${record.inspectionRecordId}&type=inspection`
				});
			} else {
				// 否则这是责任区级别的信息，跳转到责任区详情
				uni.navigateTo({
					url: `/pages/6s_pkg/area-detail?id=${record.areaId}`
				});
			}
		},
		
		// 查看整改详情
		async viewRectificationDetail(record) {
			try {
				uni.showLoading({ title: '加载中...' });
				
				const { callCloudFunction } = require('@/utils/auth.js');
				
				let result;
				
				// 根据记录状态使用不同的查询方式
				if (record.status === 'pending_review') {
									// 对于待复查状态，使用责任区ID查找整改任务
				const areaId = record.areaId || record.id;
					
					result = await callCloudFunction('hygiene-rectification', {
						action: 'getRectifications',
						data: {
							area_id: areaId,
							status: 'pending_review',
							pageSize: 1
						}
					});
					
	
					
					// 如果没找到 pending_review 状态的，尝试查找其他可能需要审核的状态
					let hasValidTask = false;
					if (result && result.success && result.data) {
						if (Array.isArray(result.data) && result.data.length > 0) {
							hasValidTask = true;
						} else if (result.data.list && Array.isArray(result.data.list) && result.data.list.length > 0) {
							hasValidTask = true;
						} else if (result.data.records && Array.isArray(result.data.records) && result.data.records.length > 0) {
							hasValidTask = true;
						}
					}
					
					if (!hasValidTask) {
						const possibleStatuses = ['submitted', 'completed', 'in_progress'];
						for (const status of possibleStatuses) {
							result = await callCloudFunction('hygiene-rectification', {
								action: 'getRectifications',
								data: {
									area_id: record.areaId || record.id,
									status: status,
									pageSize: 1
								}
							});
							
							if (result && result.success && result.data) {
								let task = null;
								if (Array.isArray(result.data) && result.data.length > 0) {
									task = result.data[0];
								} else if (result.data.list && Array.isArray(result.data.list) && result.data.list.length > 0) {
									task = result.data.list[0];
								} else if (result.data.records && Array.isArray(result.data.records) && result.data.records.length > 0) {
									task = result.data.records[0];
								}
								
								if (task) {
									// 检查是否真的需要审核（有提交时间）
									const hasSubmitted = task.submitted_at || task.rectification_submit_time || task.completion_time;
									if (hasSubmitted) {
										break;
									}
								}
							}
						}
					}
				} else {
					// 对于待整改状态，优先通过责任区ID查找最新的整改任务
					const areaId = record.areaId || record.id;
					
					result = await callCloudFunction('hygiene-rectification', {
						action: 'getRectifications', 
						data: {
							area_id: areaId,
							status: 'pending_rectification',
							pageSize: 1
						}
					});
					
					// 如果通过状态查找失败，再尝试通过检查记录ID查找
					if (!result || !result.success || !result.data || 
						(!Array.isArray(result.data) || result.data.length === 0) &&
						(!result.data.list || result.data.list.length === 0) &&
						(!result.data.records || result.data.records.length === 0)) {
						
						result = await callCloudFunction('hygiene-rectification', {
							action: 'getRectifications',
							data: {
								inspection_record_id: record.id,
								pageSize: 1
							}
						});
					}
				}
				
				uni.hideLoading();
				
				if (result && result.success && result.data) {
					let rectificationTask = null;
					
					// 处理不同的数据结构
					if (Array.isArray(result.data) && result.data.length > 0) {
						rectificationTask = result.data[0];
					} else if (result.data.list && Array.isArray(result.data.list) && result.data.list.length > 0) {
						rectificationTask = result.data.list[0];
					} else if (result.data.records && Array.isArray(result.data.records) && result.data.records.length > 0) {
						rectificationTask = result.data.records[0];
					}
					
					if (rectificationTask) {
						const taskId = rectificationTask._id || rectificationTask.id;
						
						if (taskId) {
							// 跳转到检查员专用的整改详情页面
							uni.navigateTo({
								url: `/pages/6s_pkg/inspector-rectification-detail?taskId=${taskId}`
							});
						} else {
							uni.showModal({
								title: '错误',
								content: '整改任务数据异常，缺少ID字段',
								showCancel: false
							});
						}
					} else {
						uni.showModal({
							title: '提示',
							content: '未找到对应的整改任务',
							showCancel: false
						});
					}
				} else {
					uni.showModal({
						title: '提示',
						content: '未找到对应的整改任务',
						showCancel: false
					});
				}
			} catch (error) {
				uni.hideLoading();
				uni.showModal({
					title: '加载失败',
					content: '无法加载整改任务详情，请稍后重试',
					showCancel: false
				});
			}
		},
		
		// 从检查记录审核整改任务
		async reviewRectificationFromRecord(record) {
			try {
				uni.showLoading({ title: '查找整改任务...' });
				
				const { callCloudFunction } = require('@/utils/auth.js');
				const areaId = record.areaId || record.id;
				
				// 查找该责任区的整改任务
				let result = await callCloudFunction('hygiene-rectification', {
					action: 'getRectifications',
					data: {
						area_id: areaId,
						status: 'pending_review',
						pageSize: 1
					}
				});
				
				// 如果没找到 pending_review 状态的，尝试查找其他可能需要审核的状态
				if (!result || !result.success || !result.data || 
					(!Array.isArray(result.data) && (!result.data.list || result.data.list.length === 0) && (!result.data.records || result.data.records.length === 0))) {
					
					const possibleStatuses = ['submitted', 'completed', 'in_progress'];
					for (const status of possibleStatuses) {
						result = await callCloudFunction('hygiene-rectification', {
							action: 'getRectifications',
							data: {
								area_id: areaId,
								status: status,
								pageSize: 1
							}
						});
						
						if (result && result.success && result.data) {
							let task = null;
							if (Array.isArray(result.data) && result.data.length > 0) {
								task = result.data[0];
							} else if (result.data.list && Array.isArray(result.data.list) && result.data.list.length > 0) {
								task = result.data.list[0];
							} else if (result.data.records && Array.isArray(result.data.records) && result.data.records.length > 0) {
								task = result.data.records[0];
							}
							
							if (task) {
								// 检查是否真的需要审核（有提交时间）
								const hasSubmitted = task.submitted_at || task.rectification_submit_time || task.completion_time;
								if (hasSubmitted) {
									break;
								}
							}
						}
					}
				}
				
				uni.hideLoading();
				
				if (result && result.success && result.data) {
					let rectificationTask = null;
					
					// 处理不同的数据结构
					if (Array.isArray(result.data) && result.data.length > 0) {
						rectificationTask = result.data[0];
					} else if (result.data.list && Array.isArray(result.data.list) && result.data.list.length > 0) {
						rectificationTask = result.data.list[0];
					} else if (result.data.records && Array.isArray(result.data.records) && result.data.records.length > 0) {
						rectificationTask = result.data.records[0];
					}
					
									if (rectificationTask) {
					const taskId = rectificationTask._id || rectificationTask.id;
					
					if (taskId) {
						// 跳转到整改复查页面时，标记需要刷新数据
						this.needsRefresh = true;
						uni.navigateTo({
							url: `/pages/6s_pkg/rectification-review?taskId=${taskId}`
						});
					} else {
						uni.showModal({
							title: '错误',
							content: '整改任务数据异常，缺少ID字段',
							showCancel: false
						});
					}
					} else {
						uni.showModal({
							title: '提示',
							content: '未找到对应的整改任务',
							showCancel: false
						});
					}
				} else {
					uni.showModal({
						title: '提示',
						content: '未找到对应的整改任务',
						showCancel: false
					});
				}
					} catch (error) {
			uni.hideLoading();
			uni.showModal({
				title: '加载失败',
				content: '无法加载整改任务，请稍后重试',
				showCancel: false
			});
		}
		},
		
		// 优化的格式化日期时间（使用缓存）
		formatDateTimeOptimized(dateString) {
			if (!dateString) return '--';
			
			const cacheKey = dateString.toString();
			if (this.processCache.formattedDatesCache.has(cacheKey)) {
				return this.processCache.formattedDatesCache.get(cacheKey);
			}
			
			try {
				let date;
				if (typeof dateString === 'string') {
					if (dateString.includes('T') || dateString.includes('Z')) {
						date = new Date(dateString);
					} else {
						date = new Date(dateString.replace(/-/g, '/'));
					}
				} else {
					date = new Date(dateString);
				}
				
				if (isNaN(date.getTime())) {
					this.processCache.formattedDatesCache.set(cacheKey, '--');
					return '--';
				}
				
				const formatted = `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
				this.processCache.formattedDatesCache.set(cacheKey, formatted);
				return formatted;
			} catch (error) {
				this.processCache.formattedDatesCache.set(cacheKey, '--');
				return '--';
			}
		},

		// 保留原方法以防其他地方调用
		formatDateTime(dateString) {
			return this.formatDateTimeOptimized(dateString);
		},

		// 处理清理记录更新事件
		handleCleaningRecordUpdated(data) {
			// 当有清理记录更新时，静默刷新检查任务状态
			this.silentRefreshData();
		},

		// 处理检查记录更新事件
		handleInspectionRecordUpdated(data) {
			// 当有检查记录更新时，静默刷新检查任务状态
			this.silentRefreshData();
		},

		// 处理整改记录更新事件
		handleRectificationRecordUpdated(data) {
			// 当有整改记录更新时，静默刷新检查任务状态
			this.silentRefreshData();
		},

		// 优化的静默刷新数据（不显示加载状态）
		async silentRefreshData() {
			if (this.loading) return; // 如果正在加载，跳过
			
			try {
				// 清除缓存以获取最新数据
				this.processCache.timeCalculations = null;
				this.processCache.weekKeyCache.clear();
				this.processCache.formattedDatesCache.clear();
				
				// 重新初始化时间计算
				this.initTimeCalculations();
				
				// 静默重新加载检查记录，不显示loading状态
				await this.loadInspectionRecordsOptimized();
				// 清除刷新标记，避免用户返回页面时再次显示loading
				this.needsRefresh = false;
			} catch (error) {
				// 静默处理错误，不显示错误提示
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.page-container {
	min-height: 100vh;
	background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
	width: 100%;
	overflow-x: hidden; /* 防止水平滚动 */
	box-sizing: border-box;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
}

.card {
	background: white;
	border-radius: 16rpx;
	margin: 0 32rpx 24rpx 32rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.card:first-child {
	margin-top: 24rpx;
}

.card-header {
	padding: 32rpx 32rpx 16rpx 32rpx;
}

.header-content {
	display: flex;
	flex-direction: row;
	align-items: baseline;
	justify-content: space-between;
}

.filters-row {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.status-filter-indicator {
	display: flex;
	align-items: center;
	gap: 6rpx;
	background: rgba(0, 122, 255, 0.1);
	padding: 12rpx 20rpx;
	border-radius: 20rpx;
	border: 2rpx solid rgba(0, 122, 255, 0.2);
	cursor: pointer;
	transition: all 0.3s ease;
	
	&:active {
		background: rgba(0, 122, 255, 0.15);
		transform: scale(0.98);
	}
}

.filter-text {
	font-size: 24rpx;
	color: #007AFF;
	font-weight: 500;
}

.card-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #1C1C1E;
	margin-bottom: 0;
}

.card-subtitle {
	font-size: 26rpx;
	color: #8E8E93;
}

/* 时间选择器样式 */
.time-selector {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 12rpx 20rpx;
	background: rgba(0, 122, 255, 0.1);
	border-radius: 20rpx;
	border: 2rpx solid rgba(0, 122, 255, 0.2);
	cursor: pointer;
	transition: all 0.3s ease;
	
	&:active {
		background: rgba(0, 122, 255, 0.15);
		transform: scale(0.98);
	}
	
	&.small {
		padding: 8rpx 16rpx;
		border-radius: 16rpx;
		background: rgba(142, 142, 147, 0.1);
		border-color: rgba(142, 142, 147, 0.2);
	}
}

.time-text {
	font-size: 24rpx;
	color: #007AFF;
	font-weight: 500;
	
	.small & {
		font-size: 24rpx;
		color: #8E8E93;
	}
}

/* 弹窗样式 */
.time-popup {
	background: white;
	border-radius: 24rpx 24rpx 0 0;
	padding: 0 0 env(safe-area-inset-bottom) 0;
}

.popup-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx 32rpx 24rpx 32rpx;
	border-bottom: 1rpx solid #F2F2F7;
}

.popup-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #1C1C1E;
}

.popup-close {
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #F2F2F7;
	border-radius: 50%;
}

.time-options {
	padding: 24rpx 32rpx;
}

.time-option {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 0;
	border-bottom: 1rpx solid #F2F2F7;
	position: relative;
	
	&:last-child {
		border-bottom: none;
	}
	
	&.active {
		.option-text {
			color: #007AFF;
			font-weight: 600;
		}
	}
}

.option-left {
	display: flex;
	align-items: center;
	flex: 1;
}

.option-content {
	margin-left: 16rpx;
	flex: 1;
}

.option-text {
	font-size: 28rpx;
	color: #1C1C1E;
	display: block;
	margin-bottom: 4rpx;
}

.option-desc {
	font-size: 24rpx;
	color: #8E8E93;
	display: block;
}

.option-check {
	width: 32rpx;
	height: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.popup-footer {
	padding: 24rpx 32rpx;
	border-top: 1rpx solid #F2F2F7;
}

.popup-btn {
	width: 100%;
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 16rpx;
	font-size: 32rpx;
	font-weight: 600;
	
	&.confirm {
		background: #007AFF;
		color: white;
	}
}

.stats-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 32rpx;
	padding: 32rpx;
	text-align: center;
	
	&.stats-grid-four {
		grid-template-columns: repeat(4, 1fr);
		gap: 24rpx;
	}
	
	&.stats-grid-five {
		grid-template-columns: repeat(5, 1fr);
		gap: 16rpx;
		padding: 24rpx 16rpx;
	}
	
	&.stats-grid-six {
		grid-template-columns: repeat(3, 1fr);
		gap: 16rpx;
		padding: 24rpx 16rpx;
		
		@media (min-width: 750rpx) {
			grid-template-columns: repeat(6, 1fr);
		}
	}
}

.stats-item {
	text-align: center;
	cursor: pointer;
	transition: all 0.3s ease;
	border-radius: 12rpx;
	padding: 12rpx 8rpx;
	
	&:hover {
		background: rgba(0, 122, 255, 0.05);
	}
	
	&.active {
		background: rgba(0, 122, 255, 0.1);
		border: 2rpx solid #007AFF;
		transform: scale(1.05);
	}
}

/* 统计区域加载状态 */
.stats-loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx 24rpx;
	min-height: 160rpx;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #007AFF;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 24rpx;
}

.loading-text {
	font-size: 28rpx;
	color: #8E8E93;
	text-align: center;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.stats-number {
	font-size: 48rpx;
	font-weight: bold;
	margin-bottom: 8rpx;

	&.not-cleaned { color: #8E8E93; }
	&.warning { color: #FF9500; }
	&.danger { color: #FF3B30; }
	&.review { color: #007AFF; }
	&.success { color: #34C759; }
	&.missed { color: #8B5CF6; }
}

.stats-label {
	font-size: 24rpx;
	color: #8E8E93;
}

.list-item {
	display: flex;
	align-items: center;
	padding: 24rpx 32rpx;
	border-bottom: 1rpx solid #F2F2F7;

	&:last-child {
		border-bottom: none;
	}

}

.list-item-icon {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;

	&.icon-bg-not_cleaned { background: rgba(142, 142, 147, 0.15); }
	&.icon-bg-expired_not_cleaned { background: rgba(50, 50, 50, 0.15); } // 已过期未打扫
	&.icon-bg-pending { background: rgba(255, 149, 0, 0.1); }
	&.icon-bg-pending_rectification { background: rgba(255, 59, 48, 0.1); }
	&.icon-bg-pending_review { background: rgba(0, 122, 255, 0.1); }
	&.icon-bg-completed { background: rgba(52, 199, 89, 0.1); }
	&.icon-bg-missed { background: rgba(139, 92, 246, 0.1); }
	&.icon-bg-inspector_missed { background: rgba(255, 193, 7, 0.1); } // 检查员漏检查
	&.icon-bg-passed { background: rgba(52, 199, 89, 0.1); }
	&.icon-bg-issues { background: rgba(255, 59, 48, 0.1); }
	&.icon-bg-warning { background: rgba(255, 149, 0, 0.1); }
}

.list-item-content {
	flex: 1;
}

.list-item-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #1C1C1E;
	margin-bottom: 4rpx;
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.list-item-subtitle {
	font-size: 24rpx;
	color: #8E8E93;
}

.list-item-right {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 8rpx;
	font-size: 24rpx;
	font-weight: 600;

	&.status-not_cleaned { background: #F2F2F7; color: #8E8E93; }
	&.status-expired_not_cleaned { background: #E0E0E0; color: #323232; }
	&.status-pending { background: #FFF4E6; color: #FF9500; }
	&.status-pending_rectification { background: #FFE6E6; color: #FF3B30; }
	&.status-pending_review { background: #E6F3FF; color: #007AFF; }
	&.status-completed { background: #E8F5E8; color: #34C759; }
	&.status-missed { background: #F3F0FF; color: #8B5CF6; }
	&.status-inspector_missed { background: #FFF3CD; color: #FFC107; }
	&.status-passed { background: #E8F5E8; color: #34C759; }
	&.status-failed { background: #FFE6E6; color: #FF3B30; }
	&.status-issues { background: #FFE6E6; color: #FF3B30; }
	&.status-warning { background: #FFF4E6; color: #FF9500; }
	&.status-rejected { background: #FFE6E6; color: #FF3B30; }
	&.status-verified { background: #E8F5E8; color: #34C759; }
	&.status-approved { background: #E8F5E8; color: #34C759; }
	&.status-in_progress { background: #E6F3FF; color: #007AFF; }
}

.card-body {
	padding: 0 32rpx 32rpx 32rpx;
}

.more-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	padding: 24rpx;
	border: 2rpx dashed #E5E5EA;
	border-radius: 12rpx;
	font-size: 26rpx;
	color: #007AFF;
}

.bottom-safe-area {
	height: 40rpx;
}

// 分类标签样式
.category-scroll {
	width: 100%;
}

.category-tabs {
	display: flex;
	padding: 8rpx 32rpx 16rpx 32rpx;
	gap: 16rpx;
	white-space: nowrap;
}

.category-tab {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 6rpx;
	padding: 12rpx 20rpx;
	border-radius: 20rpx;
	background: #F2F2F7;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
	flex-shrink: 0;
	
	&.active {
		background: #E6F3FF;
		border-color: #007AFF;
	}
}

.tab-text {
	font-size: 24rpx;
	font-weight: 500;
	color: #8E8E93;
	
	.category-tab.active & {
		color: #007AFF;
	}
}

.tab-count {
	font-size: 20rpx;
	color: #C7C7CC;
	background: rgba(199, 199, 204, 0.2);
	border-radius: 10rpx;
	padding: 2rpx 8rpx;
	min-width: 32rpx;
	text-align: center;
	
	.category-tab.active & {
		color: #007AFF;
		background: rgba(0, 122, 255, 0.15);
	}
}

// 标签容器
.badges-container {
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-left: 12rpx;
}

// 区域类型标签样式
.area-type-badge {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	border: 1rpx solid;
	
	&.type-fixed {
		background: rgba(52, 199, 89, 0.1);
		border-color: rgba(52, 199, 89, 0.2);
		
		.badge-text {
			color: #34C759;
		}
	}
	
	&.type-public {
		background: rgba(0, 122, 255, 0.1);
		border-color: rgba(0, 122, 255, 0.2);
		
		.badge-text {
			color: #007AFF;
		}
	}
}

// 整改标识样式
.rectification-badge {
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 149, 0, 0.1);
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	border: 1rpx solid rgba(255, 149, 0, 0.2);
}

.badge-text {
	font-size: 20rpx;
	color: #FF9500;
	font-weight: 500;
	white-space: nowrap;
}



// 时间分组样式
.time-group {
	margin-bottom: 16rpx;
}

.time-group-header {
	padding: 24rpx 32rpx;
	background: #F8F9FA;
	border-radius: 12rpx;
	margin-bottom: 8rpx;
}

.time-group-title {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.time-title {
	font-size: 30rpx;
	font-weight: 500;
	color: #1D1D1F;
	flex: 1;
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.time-count {
	font-size: 24rpx;
	color: #8E8E93;
	background: #E5E7EB;
	padding: 4rpx 12rpx;
	border-radius: 16rpx;
	min-width: 48rpx;
	text-align: center;
}

/* 加载状态样式 */
.loading-container {
	padding: 80rpx 32rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 24rpx;
}

.loading-spinner {
	width: 40rpx;
	height: 40rpx;
	border: 4rpx solid #E5E7EB;
	border-top: 4rpx solid #007AFF;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #8E8E93;
	text-align: center;
}

@media (max-width: 414px) {
	.card {
		margin: 0 16rpx 24rpx 16rpx;
	}
	
	.card:first-child {
		margin-top: 24rpx;
	}
}

/* 自定义日期选择器样式 */
.date-picker-popup {
	background: white;
	border-radius: 16rpx;
	width: 600rpx;
	max-width: 90vw;
}

.date-picker-content {
	padding: 32rpx;
}

.date-item {
	margin-bottom: 32rpx;
}

.date-label {
	display: block;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 16rpx;
	font-weight: 500;
}

.date-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: #F8F9FA;
	border: 2rpx solid #E5E5E7;
	border-radius: 12rpx;
	padding: 24rpx 20rpx;
	font-size: 28rpx;
	color: #333;
}

.date-input text {
	flex: 1;
	color: #333;
}

.popup-footer {
	display: flex;
	border-top: 2rpx solid #F0F0F0;
}

.cancel-btn, .confirm-btn {
	flex: 1;
	padding: 32rpx;
	font-size: 32rpx;
	border: none;
	background: none;
	border-radius: 0;
}

.cancel-btn {
	color: #8E8E93;
}

.confirm-btn {
	color: #007AFF;
	font-weight: 500;
	border-left: 2rpx solid #F0F0F0;
}

.cancel-btn:active {
	background: rgba(0, 0, 0, 0.05);
}

.confirm-btn:active {
	background: rgba(0, 122, 255, 0.05);
}
</style> 