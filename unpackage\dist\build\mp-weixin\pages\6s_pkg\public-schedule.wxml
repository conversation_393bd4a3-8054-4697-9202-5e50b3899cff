<view class="page-container data-v-23f29288"><view class="header data-v-23f29288"><view class="header-title data-v-23f29288">公共区清扫日设置</view><view class="header-subtitle data-v-23f29288">设置公共责任区固定清扫日程安排</view></view><view class="week-overview data-v-23f29288"><view class="overview-title data-v-23f29288">本周清扫安排</view><scroll-view class="week-scroll data-v-23f29288" scroll-x="{{true}}" show-scrollbar="false"><view class="week-grid data-v-23f29288"><block wx:for="{{$root.l0}}" wx:for-item="day" wx:for-index="index" wx:key="index"><view class="{{['day-item','data-v-23f29288',(day.$orig.isToday)?'today':'',(day.g0>0)?'has-schedule':'']}}"><view class="day-header data-v-23f29288"><text class="day-name data-v-23f29288">{{day.$orig.name}}</text><text class="day-date data-v-23f29288">{{day.$orig.date}}</text></view><view class="day-content data-v-23f29288"><block wx:if="{{day.g1>0}}"><view class="area-count data-v-23f29288">{{''+day.g2+'个区域'}}</view></block><block wx:else><view class="no-schedule data-v-23f29288">无安排</view></block></view></view></block></view></scroll-view></view><view class="filter-bar data-v-23f29288"><picker class="filter-picker data-v-23f29288" range="{{filterOptions}}" range-key="label" value="{{filterIndex}}" data-event-opts="{{[['change',[['onFilterChange',['$event']]]]]}}" bindchange="__e"><view class="filter-value data-v-23f29288"><text class="data-v-23f29288">{{filterOptions[filterIndex].label}}</text><uni-icons vue-id="e61e0192-1" type="down" size="14" color="#8E8E93" class="data-v-23f29288" bind:__l="__l"></uni-icons></view></picker></view><block wx:if="{{loading}}"><view class="list-loading data-v-23f29288"><view class="loading-content data-v-23f29288"><uni-icons vue-id="e61e0192-2" type="spinner-cycle" size="40" color="#007AFF" class="data-v-23f29288" bind:__l="__l"></uni-icons><text class="loading-text data-v-23f29288">加载公共区数据中...</text></view></view></block><block wx:else><block wx:if="{{$root.g3>0}}"><view class="area-list data-v-23f29288"><block wx:for="{{$root.l1}}" wx:for-item="area" wx:for-index="index" wx:key="index"><view class="area-item data-v-23f29288"><view class="area-main data-v-23f29288"><view class="area-header data-v-23f29288"><view class="area-name data-v-23f29288">{{area.$orig.name}}</view><view class="{{['schedule-status','data-v-23f29288',area.$orig.scheduled_day!==null?'scheduled':'unscheduled']}}">{{''+(area.$orig.scheduled_day!==null?'已排班':'未排班')+''}}</view></view><view class="area-info data-v-23f29288"><view class="info-item data-v-23f29288"><uni-icons vue-id="{{'e61e0192-3-'+index}}" type="location" size="14" color="#8E8E93" class="data-v-23f29288" bind:__l="__l"></uni-icons><text class="data-v-23f29288">{{area.$orig.location&&area.$orig.location.area||area.$orig.location||'未设置位置'}}</text></view><block wx:if="{{area.m0}}"><view class="info-item data-v-23f29288"><uni-icons vue-id="{{'e61e0192-4-'+index}}" type="calendar" size="14" color="#8E8E93" class="data-v-23f29288" bind:__l="__l"></uni-icons><text class="data-v-23f29288">{{area.m1+"清扫"}}</text></view></block><block wx:else><view class="info-item data-v-23f29288"><uni-icons vue-id="{{'e61e0192-5-'+index}}" type="calendar" size="14" color="#8E8E93" class="data-v-23f29288" bind:__l="__l"></uni-icons><text class="data-v-23f29288">未设置清扫日程</text></view></block><block wx:if="{{area.m2}}"><view class="info-item data-v-23f29288"><uni-icons vue-id="{{'e61e0192-6-'+index}}" type="time" size="14" color="#8E8E93" class="data-v-23f29288" bind:__l="__l"></uni-icons><text class="{{['data-v-23f29288',(area.m3)?'today':'']}}">{{area.m4}}</text></view></block></view></view><view class="area-actions data-v-23f29288"><button data-event-opts="{{[['tap',[['setSchedule',['$0'],[[['filteredAreas','',index]]]]]]]}}" class="action-btn set data-v-23f29288" bindtap="__e"><uni-icons vue-id="{{'e61e0192-7-'+index}}" type="calendar" size="14" color="#FFFFFF" class="data-v-23f29288" bind:__l="__l"></uni-icons><text class="data-v-23f29288">{{area.$orig.scheduled_day!==null?'调整':'设置'}}</text></button><block wx:if="{{area.$orig.scheduled_day!==null}}"><button data-event-opts="{{[['tap',[['clearSchedule',['$0'],[[['filteredAreas','',index]]]]]]]}}" class="action-btn clear data-v-23f29288" bindtap="__e"><uni-icons vue-id="{{'e61e0192-8-'+index}}" type="close" size="14" color="#FFFFFF" class="data-v-23f29288" bind:__l="__l"></uni-icons><text class="data-v-23f29288">清除</text></button></block></view></view></block></view></block><block wx:else><block wx:if="{{$root.g4}}"><view class="filter-empty data-v-23f29288"><p-empty-state vue-id="e61e0192-9" type="search" text="当前筛选条件下暂无数据" description="可以尝试切换其他筛选条件" class="data-v-23f29288" bind:__l="__l"></p-empty-state></view></block><block wx:else><block wx:if="{{$root.g5}}"><p-empty-state vue-id="e61e0192-10" type="schedule" text="{{$root.m5}}" description="{{$root.m6}}" class="data-v-23f29288" bind:__l="__l"></p-empty-state></block></block></block></block><uni-popup vue-id="e61e0192-11" type="center" mask-click="{{false}}" data-ref="schedulePopup" class="data-v-23f29288 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="schedule-popup data-v-23f29288"><view class="popup-header data-v-23f29288"><view class="popup-title data-v-23f29288">设置清扫日程</view><button data-event-opts="{{[['tap',[['closeSchedulePopup',['$event']]]]]}}" class="close-btn data-v-23f29288" bindtap="__e"><uni-icons vue-id="{{('e61e0192-12')+','+('e61e0192-11')}}" type="close" size="20" color="#8E8E93" class="data-v-23f29288" bind:__l="__l"></uni-icons></button></view><block wx:if="{{currentArea}}"><view class="popup-content data-v-23f29288"><view class="area-info-card data-v-23f29288"><view class="area-info-header data-v-23f29288"><view class="area-info-name data-v-23f29288">{{currentArea.name}}</view><view class="area-status-badge data-v-23f29288">公共区域</view></view><view class="area-info-location data-v-23f29288"><uni-icons vue-id="{{('e61e0192-13')+','+('e61e0192-11')}}" type="location" size="14" color="#8E8E93" class="data-v-23f29288" bind:__l="__l"></uni-icons><text class="data-v-23f29288">{{currentArea.location&&currentArea.location.area||currentArea.location||'未设置位置'}}</text></view></view><view class="setting-form data-v-23f29288"><view class="form-group data-v-23f29288"><view class="form-label data-v-23f29288">清扫日期</view><picker class="form-picker data-v-23f29288" range="{{weekDayOptions}}" range-key="label" value="{{scheduleWeekIndex}}" data-event-opts="{{[['change',[['onScheduleWeekChange',['$event']]]]]}}" bindchange="__e"><view class="form-input data-v-23f29288"><text class="data-v-23f29288">{{weekDayOptions[scheduleWeekIndex]?weekDayOptions[scheduleWeekIndex].label:'请选择'}}</text><view class="dropdown-icon data-v-23f29288"><uni-icons vue-id="{{('e61e0192-14')+','+('e61e0192-11')}}" type="bottom" size="12" color="#8E8E93" class="data-v-23f29288" bind:__l="__l"></uni-icons></view></view></picker></view><view class="form-group data-v-23f29288"><view class="form-label data-v-23f29288">开始日期</view><picker class="form-picker data-v-23f29288" mode="date" value="{{scheduleData.start_date}}" data-event-opts="{{[['change',[['onStartDateChange',['$event']]]]]}}" bindchange="__e"><view class="form-input data-v-23f29288"><text class="data-v-23f29288">{{scheduleData.start_date||'请选择开始日期'}}</text><view class="input-icon data-v-23f29288"><uni-icons vue-id="{{('e61e0192-15')+','+('e61e0192-11')}}" type="calendar" size="14" color="#007AFF" class="data-v-23f29288" bind:__l="__l"></uni-icons></view></view></picker></view></view><view class="setting-preview data-v-23f29288"><view class="preview-header data-v-23f29288"><uni-icons vue-id="{{('e61e0192-16')+','+('e61e0192-11')}}" type="eye" size="16" color="#007AFF" class="data-v-23f29288" bind:__l="__l"></uni-icons><text class="data-v-23f29288">设置预览</text></view><block wx:if="{{$root.m7==='complete'}}"><view class="preview-summary complete data-v-23f29288">从<text class="highlight-date data-v-23f29288">{{$root.m8}}</text>开始，每<text class="highlight-day data-v-23f29288">{{$root.m9}}</text>清扫</view></block><block wx:else><block wx:if="{{$root.m10==='clear'}}"><view class="preview-summary clear data-v-23f29288"><text class="clear-info data-v-23f29288">该区域将不设置固定清扫日程</text></view></block><block wx:else><view class="preview-summary incomplete data-v-23f29288"><block wx:if="{{scheduleData.start_date}}"><view class="preview-item data-v-23f29288">已选择开始日期：<text class="highlight-date data-v-23f29288">{{$root.m11}}</text><text class="missing-info data-v-23f29288">，请选择清扫日期</text></view></block><block wx:else><view class="preview-item data-v-23f29288"><text class="missing-info data-v-23f29288">请选择清扫日期和开始日期</text></view></block></view></block></block></view></view></block><view class="popup-footer data-v-23f29288"><button data-event-opts="{{[['tap',[['closeSchedulePopup',['$event']]]]]}}" class="popup-btn cancel data-v-23f29288" bindtap="__e">取消</button><button class="popup-btn submit data-v-23f29288" loading="{{saving}}" data-event-opts="{{[['tap',[['submitSchedule',['$event']]]]]}}" bindtap="__e">确定设置</button></view></view></uni-popup></view>