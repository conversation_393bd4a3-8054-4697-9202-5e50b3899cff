'use strict';

const db = uniCloud.database();
const dbCmd = db.command;

exports.main = async (event, context) => {
  const { action, data } = event;
  const { uid, role } = context.getClientInfo();
  
  // 权限检查
  if (!uid) {
    return {
      success: false,
      message: '用户未登录'
    };
  }
  
  try {
    switch (action) {
      case 'exportFixedAreaData':
        return await exportFixedAreaData(data, uid, role);
      case 'exportPublicAreaData':
        return await exportPublicAreaData(data, uid, role);
      case 'exportMonthlyData':
        return await exportMonthlyData(data, uid, role);
      case 'exportCleaningRecords':
        return await exportCleaningRecords(data, uid, role);
      case 'exportInspectionRecords':
        return await exportInspectionRecords(data, uid, role);
      case 'exportIssuesData':
        return await exportIssuesData(data, uid, role);
      case 'exportRectificationData':
        return await exportRectificationData(data, uid, role);
      case 'getExportHistory':
        return await getExportHistory(data, uid, role);
      default:
        return {
          success: false,
          message: '未知的操作类型'
        };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return {
      success: false,
      message: '服务器错误',
      error: error.message
    };
  }
};

// 导出固定责任区数据
async function exportFixedAreaData(data, uid, role) {
  const { start_date, end_date, area_ids = [] } = data;
  
  // 权限检查
  if (!role.includes('admin') && !role.includes('manager')) {
    return {
      success: false,
      message: '权限不足，只有管理员可以导出数据'
    };
  }
  
  // 获取固定责任区列表
  let whereCondition = { type: 'fixed', status: 'active' };
  if (area_ids.length > 0) {
    whereCondition._id = dbCmd.in(area_ids);
  }
  
  const areasResult = await db.collection('hygiene-areas')
    .where(whereCondition)
    .get();
  
  const exportData = [];
  
  for (const area of areasResult.data) {
    // 获取该责任区的清洁记录
    let cleaningCondition = { area_id: area._id };
    if (start_date) cleaningCondition.cleaning_date = dbCmd.gte(new Date(start_date));
    if (end_date) {
      if (cleaningCondition.cleaning_date) {
        cleaningCondition.cleaning_date = cleaningCondition.cleaning_date.and(dbCmd.lte(new Date(end_date)));
      } else {
        cleaningCondition.cleaning_date = dbCmd.lte(new Date(end_date));
      }
    }
    
    const [cleaningRecords, inspectionRecords, issues] = await Promise.all([
      db.collection('hygiene-cleaning-records').where(cleaningCondition).get(),
      db.collection('hygiene-inspection-records').where({ area_id: area._id }).get(),
      db.collection('hygiene-issues').where({ area_id: area._id }).get()
    ]);
    
    // 计算统计数据
    const avgQualityScore = cleaningRecords.data.length > 0
      ? cleaningRecords.data
          .filter(r => r.quality_score)
          .reduce((sum, r) => sum + r.quality_score, 0) / cleaningRecords.data.filter(r => r.quality_score).length
      : 0;
    
    const passedInspections = inspectionRecords.data.filter(r => ['excellent', 'good', 'fair'].includes(r.result)).length;
    const passRate = inspectionRecords.data.length > 0 ? (passedInspections / inspectionRecords.data.length * 100) : 0;
    
    exportData.push({
      责任区名称: area.name,
      责任区类型: '固定责任区',
      分配人员: (area.assigned_users || []).length,
      清洁次数: cleaningRecords.data.length,
      平均质量评分: Math.round(avgQualityScore * 10) / 10,
      检查次数: inspectionRecords.data.length,
      检查通过率: Math.round(passRate * 10) / 10 + '%',
      问题数量: issues.data.length,
      状态: area.status === 'active' ? '活跃' : '非活跃',
      创建时间: formatDate(area.created_at)
    });
  }
  
  // 记录导出日志
  await logExport('fixed_area_data', uid, exportData.length);
  
  return {
    success: true,
    message: '固定责任区数据导出成功',
    data: {
      filename: `固定责任区数据_${formatDate(new Date(), 'YYYYMMDD')}.xlsx`,
      records: exportData,
      total: exportData.length
    }
  };
}

// 导出公共责任区数据
async function exportPublicAreaData(data, uid, role) {
  const { start_date, end_date, area_ids = [] } = data;
  
  // 权限检查
  if (!role.includes('admin') && !role.includes('manager')) {
    return {
      success: false,
      message: '权限不足，只有管理员可以导出数据'
    };
  }
  
  // 获取公共责任区列表
  let whereCondition = { type: 'public', status: 'active' };
  if (area_ids.length > 0) {
    whereCondition._id = dbCmd.in(area_ids);
  }
  
  const areasResult = await db.collection('hygiene-areas')
    .where(whereCondition)
    .get();
  
  const exportData = [];
  
  for (const area of areasResult.data) {
    // 获取该责任区的清洁记录
    let cleaningCondition = { area_id: area._id };
    if (start_date) cleaningCondition.cleaning_date = dbCmd.gte(new Date(start_date));
    if (end_date) {
      if (cleaningCondition.cleaning_date) {
        cleaningCondition.cleaning_date = cleaningCondition.cleaning_date.and(dbCmd.lte(new Date(end_date)));
      } else {
        cleaningCondition.cleaning_date = dbCmd.lte(new Date(end_date));
      }
    }
    
    const [cleaningRecords, inspectionRecords] = await Promise.all([
      db.collection('hygiene-cleaning-records').where(cleaningCondition).get(),
      db.collection('hygiene-inspection-records').where({ area_id: area._id }).get()
    ]);
    
    // 获取本周清洁情况
    const weekStart = getWeekStart(new Date());
    const weekEnd = getWeekEnd(new Date());
    const thisWeekCleaning = await db.collection('hygiene-cleaning-records')
      .where({
        area_id: area._id,
        cleaning_date: dbCmd.gte(weekStart).and(dbCmd.lte(weekEnd))
      })
      .get();
    
    const scheduledDay = area.scheduled_day ? ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'][area.scheduled_day] : '未设置';
    
    exportData.push({
      责任区名称: area.name,
      责任区类型: '公共责任区',
      预定清洁日: scheduledDay,
      清洁频率: getFrequencyText(area.cleaning_frequency),
      本周清洁状态: thisWeekCleaning.data.length > 0 ? '已完成' : '未完成',
      总清洁次数: cleaningRecords.data.length,
      检查次数: inspectionRecords.data.length,
      状态: area.status === 'active' ? '活跃' : '非活跃',
      创建时间: formatDate(area.created_at)
    });
  }
  
  // 记录导出日志
  await logExport('public_area_data', uid, exportData.length);
  
  return {
    success: true,
    message: '公共责任区数据导出成功',
    data: {
      filename: `公共责任区数据_${formatDate(new Date(), 'YYYYMMDD')}.xlsx`,
      records: exportData,
      total: exportData.length
    }
  };
}

// 导出月度汇总数据
async function exportMonthlyData(data, uid, role) {
  const { year, month } = data;
  const targetYear = year || new Date().getFullYear();
  const targetMonth = month || new Date().getMonth() + 1;
  
  // 权限检查
  if (!role.includes('admin') && !role.includes('manager')) {
    return {
      success: false,
      message: '权限不足，只有管理员可以导出数据'
    };
  }
  
  const monthStart = new Date(targetYear, targetMonth - 1, 1);
  const monthEnd = new Date(targetYear, targetMonth, 0);
  
  // 获取月度数据
  const [
    cleaningRecords,
    inspectionRecords,
    issues,
    rectificationRecords,
    areas
  ] = await Promise.all([
    db.collection('hygiene-cleaning-records')
      .where({
        cleaning_date: dbCmd.gte(monthStart).and(dbCmd.lte(monthEnd))
      })
      .get(),
    db.collection('hygiene-inspection-records')
      .where({
        inspection_date: dbCmd.gte(monthStart).and(dbCmd.lte(monthEnd))
      })
      .get(),
    db.collection('hygiene-issues')
      .where({
        created_at: dbCmd.gte(monthStart).and(dbCmd.lte(monthEnd))
      })
      .get(),
    db.collection('hygiene-rectification-records')
      .where({
        created_at: dbCmd.gte(monthStart).and(dbCmd.lte(monthEnd))
      })
      .get(),
    db.collection('hygiene-areas').where({ status: 'active' }).get()
  ]);
  
  // 按责任区统计
  const areaStats = {};
  areas.data.forEach(area => {
    areaStats[area._id] = {
      name: area.name,
      type: area.type === 'fixed' ? '固定责任区' : '公共责任区',
      cleaningCount: 0,
      inspectionCount: 0,
      issueCount: 0,
      rectificationCount: 0,
      avgQualityScore: 0,
      passRate: 0
    };
  });
  
  // 统计清洁数据
  cleaningRecords.data.forEach(record => {
    if (areaStats[record.area_id]) {
      areaStats[record.area_id].cleaningCount++;
      if (record.quality_score) {
        areaStats[record.area_id].avgQualityScore += record.quality_score;
      }
    }
  });
  
  // 统计检查数据
  inspectionRecords.data.forEach(record => {
    if (areaStats[record.area_id]) {
      areaStats[record.area_id].inspectionCount++;
      if (['excellent', 'good', 'fair'].includes(record.result)) {
        areaStats[record.area_id].passRate++;
      }
    }
  });
  
  // 统计问题数据
  issues.data.forEach(record => {
    if (areaStats[record.area_id]) {
      areaStats[record.area_id].issueCount++;
    }
  });
  
  // 统计整改数据
  rectificationRecords.data.forEach(record => {
    if (areaStats[record.area_id]) {
      areaStats[record.area_id].rectificationCount++;
    }
  });
  
  // 计算平均值和百分比
  const exportData = Object.values(areaStats).map(stat => {
    const avgScore = stat.cleaningCount > 0 ? stat.avgQualityScore / stat.cleaningCount : 0;
    const passRate = stat.inspectionCount > 0 ? (stat.passRate / stat.inspectionCount * 100) : 0;
    
    return {
      责任区名称: stat.name,
      责任区类型: stat.type,
      清洁次数: stat.cleaningCount,
      平均质量评分: Math.round(avgScore * 10) / 10,
      检查次数: stat.inspectionCount,
      检查通过率: Math.round(passRate * 10) / 10 + '%',
      问题数量: stat.issueCount,
      整改任务数: stat.rectificationCount
    };
  });
  
  // 记录导出日志
  await logExport('monthly_data', uid, exportData.length);
  
  return {
    success: true,
    message: '月度汇总数据导出成功',
    data: {
      filename: `月度汇总数据_${targetYear}年${targetMonth}月.xlsx`,
      records: exportData,
      total: exportData.length,
      period: `${targetYear}年${targetMonth}月`
    }
  };
}

// 导出清洁记录
async function exportCleaningRecords(data, uid, role) {
  const { start_date, end_date, area_id, user_id } = data;
  
  let whereCondition = {};
  
  if (start_date) whereCondition.cleaning_date = dbCmd.gte(new Date(start_date));
  if (end_date) {
    if (whereCondition.cleaning_date) {
      whereCondition.cleaning_date = whereCondition.cleaning_date.and(dbCmd.lte(new Date(end_date)));
    } else {
      whereCondition.cleaning_date = dbCmd.lte(new Date(end_date));
    }
  }
  if (area_id) whereCondition.area_id = area_id;
  if (user_id) whereCondition.user_id = user_id;
  
  const result = await db.collection('hygiene-cleaning-records')
    .where(whereCondition)
    .orderBy('cleaning_date', 'desc')
    .get();
  
  const exportData = result.data.map(record => ({
    责任区名称: record.area_name,
    清洁人员: record.user_name,
    清洁日期: formatDate(record.cleaning_date),
    周次: record.week_number,
    完成状态: getStatusText(record.completion_status),
    质量评分: record.quality_score || '未评分',
    清洁用时: record.duration_minutes ? `${record.duration_minutes}分钟` : '未记录',
    照片数量: (record.photos || []).length,
    备注: record.remark || '无',
    创建时间: formatDate(record.created_at)
  }));
  
  await logExport('cleaning_records', uid, exportData.length);
  
  return {
    success: true,
    message: '清洁记录导出成功',
    data: {
      filename: `清洁记录_${formatDate(new Date(), 'YYYYMMDD')}.xlsx`,
      records: exportData,
      total: exportData.length
    }
  };
}

// 导出检查记录
async function exportInspectionRecords(data, uid, role) {
  const { start_date, end_date, area_id, inspector_id } = data;
  
  let whereCondition = {};
  
  if (start_date) whereCondition.inspection_date = dbCmd.gte(new Date(start_date));
  if (end_date) {
    if (whereCondition.inspection_date) {
      whereCondition.inspection_date = whereCondition.inspection_date.and(dbCmd.lte(new Date(end_date)));
    } else {
      whereCondition.inspection_date = dbCmd.lte(new Date(end_date));
    }
  }
  if (area_id) whereCondition.area_id = area_id;
  if (inspector_id) whereCondition.inspector_id = inspector_id;
  
  const result = await db.collection('hygiene-inspection-records')
    .where(whereCondition)
    .orderBy('inspection_date', 'desc')
    .get();
  
  const exportData = result.data.map(record => ({
    责任区名称: record.area_name,
    检查员: record.inspector_name,
    检查日期: formatDate(record.inspection_date),
    周次: record.week_number,
    检查结果: getResultText(record.result),
    检查评分: record.score || '未评分',
    是否有问题: record.has_issues ? '是' : '否',
    问题数量: (record.issues || []).length,
    照片数量: (record.photos || []).length,
    建议数量: (record.recommendations || []).length,
    状态: getStatusText(record.status),
    备注: record.remarks || '无',
    创建时间: formatDate(record.created_at)
  }));
  
  await logExport('inspection_records', uid, exportData.length);
  
  return {
    success: true,
    message: '检查记录导出成功',
    data: {
      filename: `检查记录_${formatDate(new Date(), 'YYYYMMDD')}.xlsx`,
      records: exportData,
      total: exportData.length
    }
  };
}

// 导出问题数据
async function exportIssuesData(data, uid, role) {
  const { start_date, end_date, area_id, status } = data;
  
  let whereCondition = {};
  
  if (start_date) whereCondition.created_at = dbCmd.gte(new Date(start_date));
  if (end_date) {
    if (whereCondition.created_at) {
      whereCondition.created_at = whereCondition.created_at.and(dbCmd.lte(new Date(end_date)));
    } else {
      whereCondition.created_at = dbCmd.lte(new Date(end_date));
    }
  }
  if (area_id) whereCondition.area_id = area_id;
  if (status) whereCondition.status = status;
  
  const result = await db.collection('hygiene-issues')
    .where(whereCondition)
    .orderBy('created_at', 'desc')
    .get();
  
  const exportData = result.data.map(record => ({
    问题标题: record.title,
    责任区名称: record.area_name,
    举报人: record.reporter_name,
    负责人: record.assigned_to_name || '未分配',
    问题类别: getCategoryText(record.category),
    严重程度: getSeverityText(record.severity),
    优先级: getPriorityText(record.priority),
    状态: getIssueStatusText(record.status),
    期望完成日期: record.expected_completion_date ? formatDate(record.expected_completion_date) : '未设置',
    实际完成日期: record.actual_completion_date ? formatDate(record.actual_completion_date) : '未完成',
    照片数量: (record.photos || []).length,
    评论数量: (record.comments || []).length,
    需要跟进: record.follow_up_required ? '是' : '否',
    标签: (record.tags || []).join(', '),
    创建时间: formatDate(record.created_at)
  }));
  
  await logExport('issues_data', uid, exportData.length);
  
  return {
    success: true,
    message: '问题数据导出成功',
    data: {
      filename: `问题数据_${formatDate(new Date(), 'YYYYMMDD')}.xlsx`,
      records: exportData,
      total: exportData.length
    }
  };
}

// 导出整改数据
async function exportRectificationData(data, uid, role) {
  const { start_date, end_date, assigned_to, status } = data;
  
  let whereCondition = {};
  
  if (start_date) whereCondition.created_at = dbCmd.gte(new Date(start_date));
  if (end_date) {
    if (whereCondition.created_at) {
      whereCondition.created_at = whereCondition.created_at.and(dbCmd.lte(new Date(end_date)));
    } else {
      whereCondition.created_at = dbCmd.lte(new Date(end_date));
    }
  }
  if (assigned_to) whereCondition.assigned_to = assigned_to;
  if (status) whereCondition.status = status;
  
  const result = await db.collection('hygiene-rectification-records')
    .where(whereCondition)
    .orderBy('created_at', 'desc')
    .get();
  
  const exportData = result.data.map(record => {
    const isOverdue = new Date() > new Date(record.deadline) && !['completed', 'verified'].includes(record.status);
    
    return {
      任务标题: record.title || '未设置',
      责任区名称: record.area_name || '未关联',
      负责人: record.assigned_to_name,
      分配人: record.assigned_by_name,
      优先级: getPriorityText(record.priority),
      状态: getRectificationStatusText(record.status),
      完成进度: `${record.progress_percentage || 0}%`,
      预计工时: record.estimated_hours ? `${record.estimated_hours}小时` : '未设置',
      整改期限: formatDate(record.deadline),
      开始时间: record.start_date ? formatDate(record.start_date) : '未开始',
      完成时间: record.completion_date ? formatDate(record.completion_date) : '未完成',
      是否逾期: isOverdue ? '是' : '否',
      审核结果: record.review_result || '未审核',
      实际成本: record.cost_actual || '未记录',
      创建时间: formatDate(record.created_at)
    };
  });
  
  await logExport('rectification_data', uid, exportData.length);
  
  return {
    success: true,
    message: '整改数据导出成功',
    data: {
      filename: `整改数据_${formatDate(new Date(), 'YYYYMMDD')}.xlsx`,
      records: exportData,
      total: exportData.length
    }
  };
}

// 获取导出历史
async function getExportHistory(data, uid, role) {
  const { page = 1, pageSize = 20 } = data;
  
  let whereCondition = {};
  
  // 非管理员只能查看自己的导出记录
  if (!role.includes('admin') && !role.includes('manager')) {
    whereCondition.user_id = uid;
  }
  
  const skip = (page - 1) * pageSize;
  
  const [listResult, countResult] = await Promise.all([
    db.collection('export-logs')
      .where(whereCondition)
      .orderBy('created_at', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get(),
    db.collection('export-logs')
      .where(whereCondition)
      .count()
  ]);
  
  return {
    success: true,
    data: {
      list: listResult.data,
      total: countResult.total,
      page,
      pageSize
    }
  };
}

// 记录导出日志
async function logExport(exportType, userId, recordCount) {
  try {
    const userResult = await db.collection('uni-id-users').doc(userId).get();
    const userName = userResult.data.length > 0 ? userResult.data[0].nickname || userResult.data[0].username : '';
    
    await db.collection('export-logs').add({
      export_type: exportType,
      user_id: userId,
      user_name: userName,
      record_count: recordCount,
      created_at: new Date()
    });
  } catch (error) {
    console.error('记录导出日志失败:', error);
  }
}

// 辅助函数
function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return '';
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hour = String(d.getHours()).padStart(2, '0');
  const minute = String(d.getMinutes()).padStart(2, '0');
  const second = String(d.getSeconds()).padStart(2, '0');
  
  if (format === 'YYYYMMDD') {
    return `${year}${month}${day}`;
  }
  
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
}

function getWeekStart(date) {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day + (day === 0 ? -6 : 1);
  return new Date(d.setDate(diff));
}

function getWeekEnd(date) {
  const d = getWeekStart(date);
  d.setDate(d.getDate() + 6);
  return d;
}

function getFrequencyText(frequency) {
  const map = {
    daily: '每日',
    weekly: '每周',
    monthly: '每月',
    as_needed: '按需'
  };
  return map[frequency] || frequency;
}

function getStatusText(status) {
  const map = {
    completed: '已完成',
    partial: '部分完成',
    delayed: '延迟',
    skipped: '跳过',
    pending: '待处理',
    in_progress: '进行中',
    reviewed: '已审核',
    closed: '已关闭'
  };
  return map[status] || status;
}

function getResultText(result) {
  const map = {
    excellent: '优秀',
    good: '良好',
    fair: '合格',
    poor: '较差',
    failed: '不合格'
  };
  return map[result] || result;
}

function getCategoryText(category) {
  const map = {
    cleanliness: '清洁卫生',
    organization: '整理整顿',
    safety: '安全',
    equipment: '设备',
    environment: '环境',
    other: '其他'
  };
  return map[category] || category;
}

function getSeverityText(severity) {
  const map = {
    low: '轻微',
    medium: '中等',
    high: '严重',
    critical: '紧急'
  };
  return map[severity] || severity;
}

function getPriorityText(priority) {
  const map = {
    low: '低',
    normal: '普通',
    high: '高',
    urgent: '紧急'
  };
  return map[priority] || priority;
}

function getIssueStatusText(status) {
  const map = {
    draft: '草稿',
    submitted: '已提交',
    assigned: '已分配',
    in_progress: '处理中',
    resolved: '已解决',
    verified: '已验证',
    closed: '已关闭',
    rejected: '已拒绝'
  };
  return map[status] || status;
}

function getRectificationStatusText(status) {
  const map = {
    pending: '待处理',
    in_progress: '进行中',
    completed: '已完成',
    verified: '已验证',
    rejected: '已拒绝',
    overdue: '已逾期'
  };
  return map[status] || status;
} 