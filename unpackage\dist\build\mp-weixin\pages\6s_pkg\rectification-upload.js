require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/rectification-upload"],{"2a91":function(t,e,r){},3662:function(t,e,r){"use strict";r.r(e);var n=r("9ec4"),o=r("8dd0");for(var a in o)["default"].indexOf(a)<0&&function(t){r.d(e,t,(function(){return o[t]}))}(a);r("99c9");var i=r("828b"),s=Object(i["a"])(o["default"],n["b"],n["c"],!1,null,"bb11638e",null,!1,n["a"],void 0);e["default"]=s.exports},"865a":function(t,e,r){"use strict";(function(t,e){var n=r("47a9");r("357b"),r("861b");n(r("3240"));var o=n(r("3662"));t.__webpack_require_UNI_MP_PLUGIN__=r,e(o.default)}).call(this,r("3223")["default"],r("df3c")["createPage"])},"8dd0":function(t,e,r){"use strict";r.r(e);var n=r("aa32"),o=r.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){r.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},"99c9":function(t,e,r){"use strict";var n=r("2a91"),o=r.n(n);o.a},"9ec4":function(t,e,r){"use strict";r.d(e,"b",(function(){return o})),r.d(e,"c",(function(){return a})),r.d(e,"a",(function(){return n}));var n={uniIcons:function(){return Promise.all([r.e("common/vendor"),r.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(r.bind(null,"6ddf"))}},o=function(){var t=this,e=t.$createElement,r=(t._self._c,t.loading||t.loadError?null:t.formatDateTime(t.taskInfo.issueFoundDate)),n=t.loading||t.loadError||!t.taskInfo.reviewComments?null:t.formatDateTime(t.taskInfo.reviewDate),o=t.loading||t.loadError?null:t.taskInfo.inspectionPhotos&&t.taskInfo.inspectionPhotos.length>0,a=t.loading||t.loadError||!o?null:t.__map(t.taskInfo.inspectionPhotos,(function(e,r){var n=t.__get_orig(e),o=t.getCloudPhotoUrl(e);return{$orig:n,m2:o}})),i=t.loading||t.loadError?null:t.taskInfo.previousPhotos&&t.taskInfo.previousPhotos.length>0,s=t.loading||t.loadError||!i?null:t.__map(t.taskInfo.previousPhotos,(function(e,r){var n=t.__get_orig(e),o=t.formatDateTime(e.timestamp);return{$orig:n,m3:o}})),c=t.loading||t.loadError?null:t.__map(t.photos,(function(e,r){var n=t.__get_orig(e),o=t.getPhotoDisplayUrl(e);return{$orig:n,m4:o}})),u=t.loading||t.loadError?null:t.photos.length,l=0===t.photos.length||!t.remarks.trim()||t.submitting;t.$mp.data=Object.assign({},{$root:{m0:r,m1:n,g0:o,l0:a,g1:i,l1:s,l2:c,g2:u,g3:l}})},a=[]},aa32:function(t,e,r){"use strict";(function(t,n){var o=r("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=o(r("7eb4")),i=o(r("af34")),s=o(r("ee10")),c=r("882c"),u=o(r("4ea0")),l={name:"RectificationUpload",data:function(){return{taskInfo:{},photos:[],remarks:"",loading:!1,loadError:"",submitting:!1,autoUpload:!0,taskId:"",processCache:{dateFormatter:null,photoUrlCache:new Map,inspectionUrlCache:new Map}}},computed:{remarksLength:function(){return this.remarks?this.remarks.length:0}},onLoad:function(t){this.taskId=t.taskId,this.initProcessCache(),this.loadTaskInfoOptimized(t.taskId)},methods:{initProcessCache:function(){this.processCache.dateFormatter=function(t){if(!t)return"--";try{var e;return e="string"===typeof t?t.includes("T")||t.includes("Z")?new Date(t):new Date(t.replace(/-/g,"/")):new Date(t),isNaN(e.getTime())?"--":"".concat(e.getMonth()+1,"月").concat(e.getDate(),"日 ").concat(e.getHours().toString().padStart(2,"0"),":").concat(e.getMinutes().toString().padStart(2,"0"))}catch(r){return"--"}}},handleRemarksInput:function(e){var r=e.detail.value||"";r.length>200&&(r=r.substring(0,200),t.showToast({title:"整改说明不能超过200个字符",icon:"none",duration:1500})),this.remarks=r,this.$forceUpdate()},loadTaskInfoOptimized:function(t){var e=this;return(0,s.default)(a.default.mark((function r(){var n,o;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(t){r.next=3;break}return e.loadError="整改任务ID不能为空",r.abrupt("return");case 3:return e.loading=!0,e.loadError="",r.prev=5,r.next=8,(0,c.callCloudFunction)("hygiene-rectification",{action:"getRectificationDetail",data:{id:t}});case 8:if(n=r.sent,!(n&&n.success&&n.data)){r.next=15;break}o=n.data,e.taskInfo=e.processTaskDataOptimized(o),e.preprocessPhotos(),r.next=16;break;case 15:throw new Error((null===n||void 0===n?void 0:n.message)||"获取整改任务信息失败");case 16:r.next=22;break;case 18:r.prev=18,r.t0=r["catch"](5),console.error("加载整改任务信息失败:",r.t0),e.loadError=r.t0.message||"加载失败，请稍后重试";case 22:return r.prev=22,e.loading=!1,r.finish(22);case 25:case"end":return r.stop()}}),r,null,[[5,18,22,25]])})))()},processTaskDataOptimized:function(t){return{id:t._id||t.id,area:t.area_name||"未知责任区",areaId:t.area_id,isPublic:"public"===t.area_type,status:t.status||"pending",problemDescription:t.issue_description||t.description||"无问题描述",issueFoundDate:t.created_at||t.issue_found_date,inspector:t.inspector_name||t.created_by_name||t.assigned_by_name||"未知检查员",reviewComments:t.review_comments||"",reviewDate:t.review_date||"",reviewer:t.reviewer_name||"未知审核员",reviewResult:t.review_result||"",inspectionPhotos:t.inspection_photos||[],previousPhotos:t.completion_photos||[]}},preprocessPhotos:function(){var t=this;this.taskInfo.inspectionPhotos&&this.taskInfo.inspectionPhotos.forEach((function(e,r){var n=t.getCloudPhotoUrl(e);t.processCache.inspectionUrlCache.set(r,n)}))},loadTaskInfo:function(t){var e=this;return(0,s.default)(a.default.mark((function r(){var n,o;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(t){r.next=3;break}return e.loadError="整改任务ID不能为空",r.abrupt("return");case 3:return e.loading=!0,e.loadError="",r.prev=5,r.next=8,(0,c.callCloudFunction)("hygiene-rectification",{action:"getRectificationDetail",data:{id:t}});case 8:if(n=r.sent,!(n&&n.success&&n.data)){r.next=14;break}o=n.data,e.taskInfo={id:o._id||o.id,area:o.area_name||"未知责任区",areaId:o.area_id,isPublic:"public"===o.area_type,status:o.status||"pending",problemDescription:o.issue_description||o.description||"无问题描述",issueFoundDate:o.created_at||o.issue_found_date,inspector:o.inspector_name||o.created_by_name||o.assigned_by_name||"未知检查员",reviewComments:o.review_comments||"",reviewDate:o.review_date||"",reviewer:o.reviewer_name||"未知审核员",reviewResult:o.review_result||"",inspectionPhotos:o.inspection_photos||[],previousPhotos:o.completion_photos||[]},r.next=15;break;case 14:throw new Error((null===n||void 0===n?void 0:n.message)||"获取整改任务信息失败");case 15:r.next=21;break;case 17:r.prev=17,r.t0=r["catch"](5),console.error("加载整改任务信息失败:",r.t0),e.loadError=r.t0.message||"加载失败，请稍后重试";case 21:return r.prev=21,e.loading=!1,r.finish(21);case 24:case"end":return r.stop()}}),r,null,[[5,17,21,24]])})))()},retryLoad:function(){this.loadTaskInfo(this.taskId)},formatDateTime:function(t){if(this.processCache.dateFormatter||this.initProcessCache(),this.processCache.dateFormatter&&"function"===typeof this.processCache.dateFormatter)return this.processCache.dateFormatter(t);if(!t)return"--";try{var e;return e="string"===typeof t?t.includes("T")||t.includes("Z")?new Date(t):new Date(t.replace(/-/g,"/")):new Date(t),isNaN(e.getTime())?"--":"".concat(e.getMonth()+1,"月").concat(e.getDate(),"日 ").concat(e.getHours().toString().padStart(2,"0"),":").concat(e.getMinutes().toString().padStart(2,"0"))}catch(r){return"--"}},addPhoto:function(){var e=this;this.photos.length>=12?t.showToast({title:"最多只能上传12张照片",icon:"none"}):t.chooseImage({count:12-this.photos.length,sizeType:["compressed"],sourceType:["camera","album"],success:function(){var t=(0,s.default)(a.default.mark((function t(r){var n;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:n=e.processNewPhotos(r.tempFilePaths),e.photos=e.photos.concat(n),e.autoUpload&&e.autoUploadNewPhotosOptimized(n);case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),fail:function(e){console.error("选择照片失败:",e),t.showToast({title:"选择照片失败",icon:"none"})}})},processNewPhotos:function(t){var e=this;return t.map((function(t){var r={url:t,uploaded:!1,cloudUrl:"",cloudPath:"",uploading:!1};return e.processCache.photoUrlCache.set(t,e.getPhotoDisplayUrl(r)),r}))},autoUploadNewPhotosOptimized:function(e){var r=this;return(0,s.default)(a.default.mark((function n(){var o,s,c,u,l,d,p;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(r.autoUpload&&0!==e.length){n.next=2;break}return n.abrupt("return");case 2:for(3,o=[],s=0;s<e.length;s+=3)c=e.slice(s,s+3),u=c.map((function(t){return r.uploadPhotoWithIndex(t)})),o.push.apply(o,(0,i.default)(u));return n.next=7,Promise.allSettled(o);case 7:l=n.sent,d=l.filter((function(t){return"fulfilled"===t.status})).length,p=l.filter((function(t){return"rejected"===t.status})).length,p>0&&t.showToast({title:"".concat(d,"张上传成功，").concat(p,"张失败"),icon:"none",duration:2e3});case 11:case"end":return n.stop()}}),n)})))()},uploadPhotoWithIndex:function(t){var e=this;return(0,s.default)(a.default.mark((function r(){var n,o;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(n=e.photos.findIndex((function(e){return e.url===t.url})),-1!==n){r.next=3;break}throw new Error("照片不存在");case 3:return r.prev=3,e.$set(e.photos[n],"uploading",!0),r.next=7,e.uploadSinglePhotoOptimized(t);case 7:if(o=r.sent,!o.success){r.next=16;break}return e.$set(e.photos[n],"uploaded",!0),e.$set(e.photos[n],"cloudUrl",o.url),e.$set(e.photos[n],"cloudPath",o.cloudPath),e.$set(e.photos[n],"uploading",!1),r.abrupt("return",o);case 16:throw new Error(o.error||"上传失败");case 17:r.next=23;break;case 19:throw r.prev=19,r.t0=r["catch"](3),e.$set(e.photos[n],"uploading",!1),r.t0;case 23:case"end":return r.stop()}}),r,null,[[3,19]])})))()},autoUploadNewPhotos:function(t){var e=this;return(0,s.default)(a.default.mark((function r(){return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.abrupt("return",e.autoUploadNewPhotosOptimized(t));case 1:case"end":return r.stop()}}),r)})))()},uploadSinglePhotoOptimized:function(t){var e=this;return(0,s.default)(a.default.mark((function r(){var n,o,i;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.prev=0,n=e.generateCloudPath(),r.next=4,u.default.uploadToCloud(t.url,n);case 4:if(o=r.sent,!o||!o.fileID){r.next=12;break}return r.next=8,u.default.getFileInfo(o.fileID);case 8:return i=r.sent,r.abrupt("return",{success:!0,cloudPath:o.fileID,url:i.tempFileURL||o.fileID,size:o.actualSize});case 12:throw new Error("上传返回结果异常");case 13:r.next=19;break;case 15:return r.prev=15,r.t0=r["catch"](0),console.error("单张照片上传失败:",r.t0),r.abrupt("return",{success:!1,error:r.t0.message});case 19:case"end":return r.stop()}}),r,null,[[0,15]])})))()},generateCloudPath:function(){var t=Date.now(),e=Math.random().toString(36).substring(2,8);return"6s/rectification/".concat(this.taskInfo.areaId,"/").concat(t,"_").concat(e,".jpg")},uploadSinglePhoto:function(t){var e=this;return(0,s.default)(a.default.mark((function r(){return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.abrupt("return",e.uploadSinglePhotoOptimized(t));case 1:case"end":return r.stop()}}),r)})))()},deletePhoto:function(e){var r=this;return(0,s.default)(a.default.mark((function o(){var i;return a.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(!(e<0||e>=r.photos.length)){o.next=2;break}return o.abrupt("return");case 2:if(i=r.photos[e],!i.uploaded||!i.cloudPath){o.next=17;break}return o.prev=4,t.showLoading({title:"删除照片中..."}),o.next=8,n.callFunction({name:"delete-file",data:{fileList:[r.extractFileId(i.cloudPath)]}});case 8:o.next=14;break;case 10:o.prev=10,o.t0=o["catch"](4),console.error("删除云端照片失败:",o.t0),t.showToast({title:"删除云端照片失败",icon:"none"});case 14:return o.prev=14,t.hideLoading(),o.finish(14);case 17:r.photos.splice(e,1);case 18:case"end":return o.stop()}}),o,null,[[4,10,14,17]])})))()},extractFileId:function(t){if(t.startsWith("cloud://")){var e=t.split("/");return e[e.length-1]}if(t.includes("tcb-api")){var r=new URL(t);return r.pathname.split("/").pop()}return t},getPhotoDisplayUrl:function(t){var e=t.url||t;if(this.processCache.photoUrlCache||this.initProcessCache(),this.processCache.photoUrlCache&&this.processCache.photoUrlCache.has(e))return this.processCache.photoUrlCache.get(e);var r=e;return"string"===typeof e&&e.startsWith("http://tmp/")&&(r=e),this.processCache.photoUrlCache&&this.processCache.photoUrlCache.set(e,r),r},toggleAutoUpload:function(){this.autoUpload=!this.autoUpload,t.showToast({title:this.autoUpload?"已开启自动上传":"已关闭自动上传",icon:"none"})},previewPhoto:function(e){var r=this;this._cachedPhotoUrls&&this._cachedPhotoUrls.length===this.photos.length||(this._cachedPhotoUrls=this.photos.map((function(t){return r.getPhotoDisplayUrl(t)}))),t.previewImage({urls:this._cachedPhotoUrls,current:e})},getCloudPhotoUrl:function(t){return"string"===typeof t?t:t.url||t},previewInspectionPhoto:function(e){var r=this;this._cachedInspectionUrls||(this._cachedInspectionUrls=this.taskInfo.inspectionPhotos.map((function(t,e){return r.processCache.inspectionUrlCache.get(e)||r.getCloudPhotoUrl(t)}))),t.previewImage({urls:this._cachedInspectionUrls,current:e})},previewPreviousPhoto:function(e){var r=this.taskInfo.previousPhotos.map((function(t){return t.url}));t.previewImage({urls:r,current:e})},submitRectification:function(){var e=this;return(0,s.default)(a.default.mark((function r(){var n,o,i,s;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(0!==e.photos.length){r.next=3;break}return t.showToast({title:"请至少上传一张照片",icon:"none"}),r.abrupt("return");case 3:if(e.remarks.trim()){r.next=6;break}return t.showToast({title:"请填写整改说明",icon:"none"}),r.abrupt("return");case 6:if(!e.submitting){r.next=8;break}return r.abrupt("return");case 8:return e.submitting=!0,r.prev=9,r.next=12,e.preparePhotosForSubmit();case 12:return n=r.sent,o=e.remarks.trim(),o.length>200&&(o=o.substring(0,200),console.warn("整改说明被截断到200字符")),i={id:e.taskId,completion_description:o,completion_photos:n,completion_status:"completed"},t.showLoading({title:"提交整改记录..."}),r.next=19,(0,c.callCloudFunction)("hygiene-rectification",{action:"completeRectification",data:i});case 19:if(s=r.sent,!s||!s.success){r.next=25;break}return r.next=23,e.handleSubmitSuccess();case 23:r.next=26;break;case 25:throw new Error((null===s||void 0===s?void 0:s.message)||"提交整改记录失败");case 26:r.next=33;break;case 28:return r.prev=28,r.t0=r["catch"](9),console.error("提交整改记录失败:",r.t0),r.next=33,e.handleSubmitError(r.t0);case 33:return r.prev=33,e.submitting=!1,r.finish(33);case 36:case"end":return r.stop()}}),r,null,[[9,28,33,36]])})))()},preparePhotosForSubmit:function(){var e=this;return(0,s.default)(a.default.mark((function r(){var n,o,s,c,u,l;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(n=e.photos.filter((function(t){return!t.uploaded&&!t.uploading})),!(n.length>0)){r.next=16;break}return t.showLoading({title:"正在上传剩余照片..."}),o=n.map((function(t){return e.uploadSinglePhotoOptimized(t)})),r.next=6,Promise.allSettled(o);case 6:if(s=r.sent,c=s.filter((function(t){return"fulfilled"===t.status&&t.value.success})).map((function(t){return t.value})),0!==c.length){r.next=11;break}throw t.hideLoading(),new Error("照片上传失败，请重试");case 11:return u=e.photos.filter((function(t){return t.uploaded})).map((function(t){return{url:t.cloudPath,type:"rectification",description:""}})),l=c.map((function(t){return{url:t.cloudPath,type:"rectification",description:""}})),r.abrupt("return",[].concat((0,i.default)(u),(0,i.default)(l)));case 16:return r.abrupt("return",e.photos.filter((function(t){return t.uploaded})).map((function(t){return{url:t.cloudPath,type:"rectification",description:""}})));case 17:case"end":return r.stop()}}),r)})))()},prepareSubmissionData:function(t){return{id:this.taskId,completion_description:this.remarks.trim(),completion_photos:t}},handleSubmitSuccess:function(){var e=this;return(0,s.default)(a.default.mark((function r(){return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:t.hideLoading(),t.$emit("rectificationRecordUpdated",{taskId:e.taskId,areaId:e.taskInfo.areaId,type:"completed"}),t.showToast({title:"提交成功",icon:"success"}),setTimeout((function(){t.navigateBack()}),1500);case 4:case"end":return r.stop()}}),r)})))()},handleSubmitError:function(e){return(0,s.default)(a.default.mark((function r(){var n;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.hideLoading(),n="提交失败，请重试",e.message&&(n=e.message.includes("未登录")?"请先登录":e.message.includes("权限")?"您没有权限操作该整改任务":e.message),r.next=5,t.showModal({title:"提交失败",content:n,showCancel:!1,confirmText:"知道了"});case 5:case"end":return r.stop()}}),r)})))()}}};e.default=l}).call(this,r("df3c")["default"],r("861b")["uniCloud"])}},[["865a","common/runtime","common/vendor"]]]);