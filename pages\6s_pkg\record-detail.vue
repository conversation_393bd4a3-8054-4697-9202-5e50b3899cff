<template>
  <view class="page-container">
    <!-- 页面头部 - 始终显示 -->
    <view class="page-header">
      <view class="header-content">
        <view class="header-title">{{ getPageTitle() }}</view>
        <view class="header-subtitle">{{ loading ? '加载中...' : recordInfo.areaName }}</view>
      </view>
      <view v-if="!loading" class="status-badge-enhanced" :class="statusBadgeClass">
        <uni-icons 
          :type="recordInfo.status === 'completed' ? 'checkmarkempty' : recordInfo.status === 'pending' ? 'info' : recordInfo.status === 'pending_rectification' ? 'flag' : recordInfo.status === 'missed' ? 'close' : recordInfo.status === 'cancelled' ? 'close' : 'info'"
          size="16" 
          color="white"
        ></uni-icons>
        <text>{{ getStatusText(recordInfo.status) }}</text>
      </view>
    </view>

    <!-- 内容区域加载状态 -->
    <view v-if="loading" class="content-loading">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载记录详情...</text>
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="loadError" class="content-error">
      <p-empty-state 
        type="error"
        title="加载失败"
        description="网络异常，请检查网络连接"
        :show-button="true"
        button-text="重新加载"
        @button-click="retryLoad"
      ></p-empty-state>
    </view>

    <!-- 正常内容 -->
    <view v-else-if="dataLoaded">
    <!-- 基本信息 -->
    <view class="card">
      <view class="card-header">
        <view class="card-title">基本信息</view>
      </view>
      <view class="card-body">
        <view class="info-grid-enhanced">
          <view class="info-item-enhanced">
            <view class="info-icon" :class="recordType === 'cleaning' ? 'icon-cleaning' : 'icon-inspection'">
              <uni-icons type="person" size="18" color="white"></uni-icons>
            </view>
            <view class="info-content">
              <view class="info-label-enhanced">{{ recordType === 'cleaning' ? '清理人员' : '检查人员' }}</view>
              <view class="info-value-enhanced">{{ recordInfo.operatorName }}</view>
            </view>
          </view>
          
          <view class="info-item-enhanced">
            <view class="info-icon icon-time">
              <uni-icons type="calendar" size="18" color="white"></uni-icons>
            </view>
            <view class="info-content">
              <view class="info-label-enhanced">{{ recordType === 'cleaning' ? '清理时间' : '检查时间' }}</view>
              <view class="info-value-enhanced">{{ recordInfo.operationTime }}</view>
            </view>
          </view>
          
          <view v-if="recordType === 'cleaning'" class="info-item-enhanced period-item-enhanced">
            <view class="info-icon icon-cycle">
              <uni-icons type="reload" size="18" color="white"></uni-icons>
            </view>
            <view class="info-content">
              <view class="info-label-enhanced">所属周期</view>
              <view class="info-value-enhanced">{{ recordInfo.weekPeriod }}</view>
            </view>
          </view>
          
          <view v-if="recordType === 'inspection' && recordInfo.rating > 0 && !recordInfo.hasIssues" class="info-item-enhanced rating-item-enhanced">
            <view class="info-icon icon-rating">
              <uni-icons type="star-filled" size="18" color="white"></uni-icons>
            </view>
            <view class="info-content">
              <view class="info-label-enhanced">检查评分</view>
              <view class="rating-display-enhanced">
                <view class="rating-number">{{ recordInfo.rating }}</view>
                <view class="rating-stars">
                  <uni-icons 
                    v-for="star in 5" 
                    :key="star"
                    type="star-filled" 
                    size="14" 
                    :color="star <= recordInfo.rating ? '#FFD700' : '#E5E5EA'"
                  ></uni-icons>
                </view>
                <view class="rating-desc">{{ getRatingDescription(recordInfo.rating) }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 照片展示 -->
    <view class="card">
      <view class="card-header">
        <view class="card-title">{{ recordType === 'cleaning' ? '清理照片' : '检查照片' }}</view>
        <view class="card-subtitle-enhanced">
          <uni-icons type="image" size="14" color="#8E8E93"></uni-icons>
          <text>{{ recordInfo.photos.length }}张照片</text>
        </view>
      </view>
      <view class="card-body">
        <view v-if="recordInfo.photos.length === 0" class="no-photos-enhanced">
          <p-empty-state 
            type="no-image"
            title="暂无照片"
            description="该记录没有上传照片"
          ></p-empty-state>
        </view>
        <view v-else class="photos-grid-enhanced">
          <view 
            v-for="(photo, index) in recordInfo.photos" 
            :key="index" 
            class="photo-item-enhanced"
            @click="previewPhoto(index)"
          >
            <!-- 照片容器 -->
            <view class="photo-container">
              <!-- 照片图片 -->
              <image 
                :src="photo.url" 
                mode="aspectFill" 
                class="photo-image-enhanced"
                @error="onPhotoError(index)"
              ></image>
              
              <!-- 照片加载失败覆盖层 -->
              <view v-if="photo.loadError" class="photo-error" @click.stop="retryPhotoLoad(index)">
                <uni-icons type="reload" size="24" color="#8E8E93"></uni-icons>
                <text class="error-text">点击重试</text>
              </view>
              
              <!-- 正常状态的覆盖层 -->
              <view v-else class="photo-overlay-enhanced">
                <view class="overlay-content">
                  <uni-icons type="eye" size="24" color="white"></uni-icons>
                  <text class="overlay-text">预览</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 检查结果（仅检查记录显示） -->
    <view v-if="recordType === 'inspection'" class="card">
      <view class="card-header">
        <view class="card-title">检查结果</view>
        <view class="result-status-badge" :class="resultBadgeClass">
          <uni-icons 
            :type="recordInfo.result === 'passed' ? 'checkmarkempty' : 'info'"
            size="14" 
            color="white"
          ></uni-icons>
          <text>{{ getResultText(recordInfo.result) }}</text>
        </view>
      </view>
      <view class="card-body">
        <view class="result-section">
          <view v-if="recordInfo.hasIssues" class="issue-details">
            <view class="issue-header">
              <uni-icons type="info" size="16" color="#FF3B30"></uni-icons>
              <text>问题类型：{{ recordInfo.issueType }}</text>
            </view>
            <view class="issue-time-info">
              检查时间：{{ recordInfo.operationTime }}
            </view>
            <view class="issue-content">
              <view class="issue-description" :class="{ 'text-collapsed': !issueDescExpanded && recordInfo.issueDescription.length > 100 }">
                问题描述：{{ recordInfo.issueDescription }}
              </view>
              <view v-if="recordInfo.issueDescription.length > 100" class="expand-btn" @click="issueDescExpanded = !issueDescExpanded">
                <text>{{ issueDescExpanded ? '收起' : '展开' }}</text>
                <uni-icons :type="issueDescExpanded ? 'up' : 'down'" size="14" color="#007AFF"></uni-icons>
              </view>
            </view>
          </view>
          
          <view v-else class="no-issue-display">
            <view class="no-issue-icon">
              <uni-icons type="checkmarkempty" size="32" color="#34C759"></uni-icons>
            </view>
            <view class="no-issue-content">
              <view class="no-issue-title">检查通过</view>
              <view class="no-issue-desc">
                {{ recordInfo.rating > 0 
                   ? `该责任区状况${getRatingDescription(recordInfo.rating).toLowerCase()}，无发现问题` 
                   : '该责任区状况良好，无发现问题' }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 清理说明 -->
    <view v-if="recordType === 'cleaning' && recordInfo.notes" class="card">
      <view class="card-header">
        <view class="card-title">清理说明</view>
      </view>
      <view class="card-body">
        <view class="notes-content">
          {{ recordInfo.notes }}
        </view>
      </view>
    </view>

    <!-- 整改任务（如有） -->
    <view v-if="recordType === 'inspection' && recordInfo.remediationTask" class="card">
      <view class="card-header">
        <view class="card-title">整改任务</view>
        <view class="task-status-badge" :class="taskBadgeClass">
          <uni-icons 
            :type="getTaskStatusIcon(recordInfo.remediationTask.status)"
            size="14" 
            color="white"
          ></uni-icons>
          <text>{{ getTaskStatusText(recordInfo.remediationTask.status) }}</text>
        </view>
      </view>
      <view class="card-body">
        <view class="remediation-task">
          <view class="task-info">
            <view class="task-item">
              <view class="task-label">负责人</view>
              <view class="task-value">{{ recordInfo.remediationTask.assignee }}</view>
            </view>
          </view>
          
          <view v-if="recordInfo.remediationTask.status === 'verified' || recordInfo.remediationTask.status === 'completed' || recordInfo.remediationTask.status === 'pending_review'" class="completion-info">
            <view class="completion-header" :style="{ color: recordInfo.remediationTask.status === 'verified' ? '#2E7D32' : '#F57C00' }">
              <uni-icons 
                :type="recordInfo.remediationTask.status === 'verified' ? 'checkmarkempty' : 'eye'"
                size="16" 
                :color="recordInfo.remediationTask.status === 'verified' ? '#2E7D32' : '#F57C00'"
              ></uni-icons>
              <text>{{ recordInfo.remediationTask.status === 'verified' ? '整改完成' : '整改已提交' }}</text>
            </view>
            <view class="completion-details">
              <view v-if="recordInfo.remediationTask.completionTime && recordInfo.remediationTask.completionTime !== '--' && recordInfo.remediationTask.completionTime !== 'null'" class="completion-time">
                完成时间：{{ recordInfo.remediationTask.completionTime }}
              </view>
              
              <view v-if="recordInfo.remediationTask.completionNotes" class="completion-notes">
                整改说明：{{ recordInfo.remediationTask.completionNotes }}
              </view>
            </view>
          </view>
          
          <!-- 整改照片展示 -->
          <view v-if="recordInfo.remediationTask.completionPhotos && recordInfo.remediationTask.completionPhotos.length > 0" class="rectification-photos">
            <view class="photos-header">
              <uni-icons type="image" size="16" color="#34C759"></uni-icons>
              <text class="photos-title">整改照片 ({{ recordInfo.remediationTask.completionPhotos.length }}张)</text>
        </view>
            <view class="photos-grid-enhanced">
              <view 
                v-for="(photo, index) in recordInfo.remediationTask.completionPhotos" 
                :key="index" 
                class="photo-item-enhanced"
                @click="previewRectificationPhoto(index)"
              >
                <view class="photo-container">
                  <image 
                    :src="photo.url" 
                    mode="aspectFill" 
                    class="photo-image-enhanced"
                    @error="onRectificationPhotoError(index)"
                  ></image>
                  
                  <view v-if="photo.loadError" class="photo-error" @click.stop="retryRectificationPhotoLoad(index)">
                    <uni-icons type="reload" size="24" color="#8E8E93"></uni-icons>
                    <text class="error-text">点击重试</text>
                  </view>
                  
                  <view v-else class="photo-overlay-enhanced">
                    <view class="overlay-content">
                      <uni-icons type="eye" size="24" color="white"></uni-icons>
                      <text class="overlay-text">预览</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 复查结果（如有） -->
    <view v-if="recordType === 'inspection' && recordInfo.reviewInfo && recordInfo.reviewInfo.hasReview" class="card">
      <view class="card-header">
        <view class="card-title">复查结果</view>
        <view class="result-status-badge badge-passed">
          <uni-icons type="checkmarkempty" size="14" color="white"></uni-icons>
          <text>复查通过</text>
        </view>
      </view>
      <view class="card-body">
        <view class="review-section-standalone">
          <!-- 复查基本信息 -->
          <view class="info-grid-enhanced">
            <view class="info-item-enhanced">
              <view class="info-icon icon-review">
                <uni-icons type="person" size="18" color="white"></uni-icons>
              </view>
              <view class="info-content">
                <view class="info-label-enhanced">复查人员</view>
                <view class="info-value-enhanced">{{ recordInfo.reviewInfo.reviewerName }}</view>
              </view>
            </view>
            
            <view class="info-item-enhanced">
              <view class="info-icon icon-review">
                <uni-icons type="calendar" size="18" color="white"></uni-icons>
              </view>
              <view class="info-content">
                <view class="info-label-enhanced">复查时间</view>
                <view class="info-value-enhanced">{{ recordInfo.reviewInfo.reviewTime }}</view>
              </view>
            </view>
            
            <view v-if="recordInfo.reviewInfo.reviewRating > 0" class="info-item-enhanced rating-item-enhanced">
              <view class="info-icon icon-rating">
                <uni-icons type="star-filled" size="18" color="white"></uni-icons>
              </view>
              <view class="info-content">
                <view class="info-label-enhanced">复查评分</view>
                <view class="rating-display-enhanced">
                  <view class="rating-number">{{ recordInfo.reviewInfo.reviewRating }}</view>
                  <view class="rating-stars">
                    <uni-icons 
                      v-for="star in 5" 
                      :key="star"
                      type="star-filled" 
                      size="14" 
                      :color="star <= recordInfo.reviewInfo.reviewRating ? '#FFD700' : '#E5E5EA'"
                    ></uni-icons>
                  </view>
                  <view class="rating-desc">{{ getRatingDescription(recordInfo.reviewInfo.reviewRating) }}</view>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 复查意见 -->
          <view v-if="recordInfo.reviewInfo.reviewComments" class="review-comments-section">
            <view class="section-title">复查意见</view>
            <view class="review-comment-content">
              {{ recordInfo.reviewInfo.reviewComments }}
            </view>
          </view>
          
          <!-- 最终结论 -->
          <view class="review-conclusion-standalone">
            <view class="conclusion-icon">
              <uni-icons type="checkmarkempty" size="24" color="#34C759"></uni-icons>
            </view>
            <text class="conclusion-text">整改合格，问题已解决</text>
          </view>
          
          <!-- 复查照片 -->
          <view v-if="recordInfo.reviewInfo.reviewPhotos && recordInfo.reviewInfo.reviewPhotos.length > 0" class="review-photos-section">
            <view class="section-title">
              <uni-icons type="image" size="16" color="#5AC8FA"></uni-icons>
              <text>复查照片 ({{ recordInfo.reviewInfo.reviewPhotos.length }}张)</text>
            </view>
            <view class="photos-grid-enhanced">
              <view 
                v-for="(photo, index) in recordInfo.reviewInfo.reviewPhotos" 
                :key="index" 
                class="photo-item-enhanced"
                @click="previewReviewPhoto(index)"
              >
                <view class="photo-container">
                  <image 
                    :src="photo.url" 
                    mode="aspectFill" 
                    class="photo-image-enhanced"
                    @error="onReviewPhotoError(index)"
                  ></image>
                  
                  <view v-if="photo.loadError" class="photo-error" @click.stop="retryReviewPhotoLoad(index)">
                    <uni-icons type="reload" size="24" color="#8E8E93"></uni-icons>
                    <text class="error-text">点击重试</text>
                  </view>
                  
                  <view v-else class="photo-overlay-enhanced">
                    <view class="overlay-content">
                      <uni-icons type="eye" size="24" color="white"></uni-icons>
                      <text class="overlay-text">预览</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作历史 -->
    <view class="card">
      <view class="card-header">
        <view class="card-title">操作历史</view>
      </view>
      <view class="card-body">
        <view class="timeline">
          <view 
            v-for="(event, index) in recordInfo.history" 
            :key="index" 
            class="timeline-item"
          >
            <view class="timeline-dot" :class="['dot-' + event.type]"></view>
            <view class="timeline-content">
              <view class="timeline-title">{{ event.title }}</view>
              <view class="timeline-desc">{{ event.description }}</view>
              <view class="timeline-time">{{ event.time }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view v-if="showEditSection" class="action-section">
      <!-- 修改清理记录按钮 -->
      <view v-if="recordType === 'cleaning' && editPermission.canEdit" 
            class="action-btn primary" 
            @click="editRecord">
        <uni-icons type="compose" size="20" color="white"></uni-icons>
        <text>修改记录</text>
      </view>
      
      <!-- 不可修改提示 -->
      <view v-if="recordType === 'cleaning' && !editPermission.canEdit && editPermission.message" 
            class="edit-disabled-tip">
        <uni-icons type="info-filled" size="16" color="#FF9500"></uni-icons>
        <text>{{ editPermission.message }}</text>
      </view>
    </view>

    <!-- 底部安全间距 -->
    <view class="bottom-safe-area"></view>
    </view>
  </view>
</template>

<script>
import { callCloudFunction } from '@/utils/auth.js';

export default {
  name: 'RecordDetail',
  data() {
    return {
      recordId: '',
      recordType: 'cleaning', // 'cleaning' 或 'inspection'
      loading: false,
      loadError: false,
      dataLoaded: false, // 数据是否已加载完成
      editPermission: { canEdit: false, message: '', checking: true }, // 编辑权限
      issueDescExpanded: false, // 问题描述是否展开
      
      // 历史记录相关参数
      isHistoricalRecord: false, // 是否为历史记录
      recordWeek: null, // 记录所属周次
      cleaningDate: null, // 清理日期
      
      // 性能优化缓存
      processCache: null, // 数据处理缓存
      basicDataLoaded: false, // 基本数据是否已加载
      
      recordInfo: {
        id: '',
        areaId: '',
        areaName: '',
        operatorName: '',
        operationTime: '',
        operationTimestamp: '',
        status: '',
        photos: [],
        notes: '',
        // 清理记录特有字段
        weekPeriod: '',
        // 检查记录特有字段
        rating: 0,
        result: '',
        hasIssues: false,
        issueType: '',
        issueDescription: '',
        summary: '',
        remediationTask: null,
        // 操作历史
        history: []
      }
    }
  },
  computed: {
    // 是否显示编辑区域
    showEditSection() {
      // 只有权限检查完成后才显示编辑区域
      return this.recordType === 'cleaning' && !this.editPermission.checking && (this.editPermission.canEdit || this.editPermission.message);
    },
    
    // 状态徽章类名
    statusBadgeClass() {
      return {
        'status-completed': this.recordInfo.status === 'completed' || this.recordInfo.status === 'verified' || this.recordInfo.status === 'reviewed',
        'status-pending': this.recordInfo.status === 'pending',
        'status-pending-rectification': this.recordInfo.status === 'pending_rectification' || this.recordInfo.status === 'rectification_completed',
        'status-cancelled': this.recordInfo.status === 'cancelled'
      };
    },
    
    // 检查结果徽章类名
    resultBadgeClass() {
      if (this.recordInfo.result === 'passed') {
        return 'badge-passed';
      } else if (this.recordInfo.result === 'issues') {
        return 'badge-issues';
      }
      return '';
    },
    
    // 整改任务徽章类名
    taskBadgeClass() {
      if (!this.recordInfo.remediationTask) return '';
      const status = this.recordInfo.remediationTask.status;
      if (status === 'pending_assignment') {
        return 'task-badge-pending';
      } else if (status === 'pending_rectification') {
        return 'task-badge-pending-rectification';
      } else if (status === 'in_progress') {
        return 'task-badge-pending';
      } else if (status === 'pending_review') {
        return 'task-badge-pending';
      } else if (status === 'verified') {
        return 'task-badge-completed';
      } else if (status === 'rejected') {
        return 'task-badge-missed';
      } else if (status === 'overdue') {
        return 'task-badge-missed';
      }
      return 'task-badge-pending';
    },
    
    // 任务状态值类名
    taskStatusValueClass() {
      if (!this.recordInfo.remediationTask) return '';
      const status = this.recordInfo.remediationTask.status;
      if (status === 'not_cleaned') {
        return 'task-status-not-cleaned';
      } else if (status === 'pending') {
        return 'task-status-pending';
      } else if (status === 'pending_rectification') {
        return 'task-status-pending-rectification';
      } else if (status === 'completed') {
        return 'task-status-completed';
      } else if (status === 'missed') {
        return 'task-status-missed';
      }
      return '';
    }
  },
  onLoad(options) {
    if (options.id && options.type) {
      this.recordId = options.id;
      this.recordType = options.type;
      
      // 接收历史记录相关参数
      this.isHistoricalRecord = options.isHistorical === 'true' || false;
      this.recordWeek = options.week || null;
      this.cleaningDate = options.cleaningDate || null;
      
      this.loadRecordDetail();
    }
    
    // 监听清理记录更新事件
    uni.$on('cleaningRecordUpdated', this.handleRecordUpdated);
  },
  onUnload() {
    // 移除事件监听
    uni.$off('cleaningRecordUpdated', this.handleRecordUpdated);
  },
  onShow() {
    // 页面显示时不需要自动刷新，依赖事件驱动的刷新机制
    // 只有当记录真正被更新时，通过 uni.$emit('cleaningRecordUpdated') 事件来刷新
  },
  methods: {
    // 获取页面标题
    getPageTitle() {
      return this.recordType === 'cleaning' ? '清理记录详情' : '检查记录详情';
    },

    // 加载记录详情 - 性能优化版本
    async loadRecordDetail() {
      try {
        this.loadError = false;
        this.loading = true;
        
        // 初始化处理缓存
        this.initProcessCache();
        
        if (this.recordType === 'cleaning') {
          await this.loadCleaningRecordDetailOptimized();
        } else {
          await this.loadInspectionRecordDetailOptimized();
        }
        
        this.dataLoaded = true;        
      } catch (error) {
        this.loadError = true;
      } finally {
        this.loading = false;
      }
    },

    // 初始化数据处理缓存
    initProcessCache() {
      if (!this.processCache) {
        this.processCache = {
          // 问题类型映射缓存
          categoryMap: {
            'equipment': '设备问题',
            'cleanliness': '清洁问题', 
            'organization': '整理问题',
            'safety': '安全问题',
            'environment': '环境问题',
            'standardization': '标识问题',
            'other': '其他问题'
          },
          // 状态映射缓存
          statusMaps: {
            record: {
              'completed': '已完成',
              'pending_rectification': '待整改',
              'rectification_completed': '整改已提交',
              'verified': '已确认',
              'reviewed': '已审核',
              'pending': '待处理',
              'cancelled': '已取消'
            },
            result: {
              'passed': '检查通过',
              'issues': '发现问题'
            },
            task: {
              'pending_assignment': '待分配',
              'pending_rectification': '待整改',
              'in_progress': '整改中',
              'pending_review': '待复查',
              'completed': '已完成',
              'verified': '已确认',
              'rejected': '已拒绝',
              'overdue': '已逾期'
            },
            taskIcon: {
              'pending_assignment': 'info',
              'pending_rectification': 'flag',
              'in_progress': 'reload',
              'pending_review': 'eye',
              'completed': 'checkmarkempty',
              'verified': 'checkmarkempty',
              'rejected': 'close',
              'overdue': 'close'
            }
          },
          // 常用日期格式化函数
          formatters: {
            dateTime: this.createDateTimeFormatter(),
            date: this.createDateFormatter()
          }
        };
      }
    },

    // 创建优化的日期时间格式化器
    createDateTimeFormatter() {
      return (dateString) => {
        if (!dateString || dateString === 'null' || dateString === 'undefined') return '--';
        
        try {
          const date = new Date(dateString);
          if (isNaN(date.getTime())) return '--';
          
          return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
        } catch (error) {
          return '--';
        }
      };
    },

    // 创建优化的日期格式化器
    createDateFormatter() {
      return (dateString) => {
        if (!dateString || dateString === 'null' || dateString === 'undefined') return '--';
        
        try {
          const date = new Date(dateString);
          if (isNaN(date.getTime())) return '--';
          
          return `${date.getMonth() + 1}月${date.getDate()}日`;
        } catch (error) {
          return '--';
        }
      };
    },

    // 优化的清理记录详情加载
    async loadCleaningRecordDetailOptimized() {
      const result = await callCloudFunction('hygiene-cleaning', {
        action: 'getCleaningRecordDetail',
        data: {
          record_id: this.recordId
        }
      });

      if (result && result.success && result.data) {
        const record = result.data;
        
        // 使用缓存的格式化器
        const dateTimeFormatter = this.processCache.formatters.dateTime;
        
        // 快速计算周期信息（避免复杂的日期计算）
        const cleanDate = new Date(record.cleaning_date);
        const weekPeriod = this.calculateWeekPeriodOptimized(cleanDate);

        // 直接构建基本记录信息
        this.recordInfo = {
          id: record._id,
          areaId: record.area_id,
          areaName: record.area_name || '未知责任区',
          operatorName: record.cleaner_name || record.user_name || '未知',
          operationTime: dateTimeFormatter(record.cleaning_date),
          operationTimestamp: record.cleaning_date,
          weekPeriod: weekPeriod,
          status: 'completed',
          photos: this.processPhotos(record.photos || []),
          notes: record.remark || '',
          history: [{
            type: 'create',
            title: '提交清理记录',
            description: `${record.cleaner_name || record.user_name || '用户'}提交了清理记录，包含${(record.photos || []).length}张照片`,
            time: dateTimeFormatter(record.cleaning_date)
          }]
        };
        
        // 异步检查编辑权限，不阻塞页面显示
        this.checkEditPermissionAsync();
      } else {
        throw new Error('获取清理记录详情失败');
      }
    },

    // 优化的周期计算
    calculateWeekPeriodOptimized(date) {
      const year = date.getFullYear();
      const weekNum = Math.ceil((date - new Date(year, 0, 1)) / (7 * 24 * 60 * 60 * 1000));
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay() + 1);
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);
      
      return `第${weekNum}周 (${weekStart.getMonth() + 1}月${weekStart.getDate()}日-${weekEnd.getMonth() + 1}月${weekEnd.getDate()}日)`;
    },

    // 优化的照片处理
    processPhotos(photos) {
      return photos.map(photo => ({
        url: photo.url || photo,
        name: photo.name || '清理照片',
        loadError: false
      }));
    },

    // 异步检查编辑权限
    async checkEditPermissionAsync() {
      try {
        await this.checkEditPermission();
      } catch (error) {
        // 权限检查失败不影响主要功能

      }
    },

    // 加载清理记录详情 - 保留原方法作为备用
    async loadCleaningRecordDetail() {
      const result = await callCloudFunction('hygiene-cleaning', {
        action: 'getCleaningRecordDetail',
        data: {
          record_id: this.recordId
        }
      });

      if (result && result.success && result.data) {
        const record = result.data;
        const cleanDate = new Date(record.cleaning_date);
        
        // 计算周期信息
        const weekNum = this.getWeekNumber(cleanDate);
        const weekStart = this.getWeekStart(cleanDate);
        const weekEnd = this.getWeekEnd(cleanDate);
        const weekPeriod = `第${weekNum}周 (${weekStart.getMonth() + 1}月${weekStart.getDate()}日-${weekEnd.getMonth() + 1}月${weekEnd.getDate()}日)`;

        this.recordInfo = {
          id: record._id,
          areaId: record.area_id, // 用于权限检查
          areaName: record.area_name || '未知责任区',
          operatorName: record.cleaner_name || record.user_name || '未知',
          operationTime: this.formatDateTime(record.cleaning_date),
          operationTimestamp: record.cleaning_date, // 保存原始时间戳用于API调用
          weekPeriod: weekPeriod,
          status: 'completed',
          photos: (record.photos || []).map(photo => ({
            url: photo.url || photo,
            name: photo.name || '清理照片',
            loadError: false
          })),
          notes: record.remark || '',
          history: [
            {
              type: 'create',
              title: '提交清理记录',
              description: `${record.cleaner_name || record.user_name || '用户'}提交了清理记录，包含${(record.photos || []).length}张照片`,
              time: this.formatDateTime(record.cleaning_date)
            }
          ]
        };
        
        // 加载完记录详情后检查编辑权限
        await this.checkEditPermission();
      } else {
        throw new Error('获取清理记录详情失败');
      }
    },

    // 优化的检查记录详情加载
    async loadInspectionRecordDetailOptimized() {
      const result = await callCloudFunction('hygiene-inspection', {
        action: 'getInspectionDetail',
        data: {
          id: this.recordId
        }
      });

      if (result && result.success && result.data) {
        const record = result.data;
        
        // 使用缓存的数据处理
        const dateTimeFormatter = this.processCache.formatters.dateTime;
        const categoryMap = this.processCache.categoryMap;
        
        // 快速处理问题信息
        const { issueType, issueDescription } = this.processIssueInfo(record, categoryMap);
        
        // 处理评分和整改任务
        const { finalRating, remediationTask } = this.processRectificationInfo(record, dateTimeFormatter);
        
        // 处理复查信息
        const reviewInfo = this.processReviewInfo(record, remediationTask, dateTimeFormatter);
        
        // 直接构建记录信息
        this.recordInfo = {
          id: record._id,
          areaName: record.area_name || '未知责任区',
          operatorName: record.inspector_name || '未知',
          operationTime: dateTimeFormatter(record.inspection_date),
          operationTimestamp: record.inspection_date,
          status: record.status || 'completed',
          rating: finalRating,
          result: record.has_issues ? 'issues' : 'passed',
          hasIssues: record.has_issues || false,
          issueType: issueType,
          issueDescription: issueDescription,
          summary: record.summary || '',
          photos: this.processInspectionPhotos(record.photos || []),
          remediationTask: remediationTask,
          // 新增复查信息
          reviewInfo: reviewInfo,
          history: this.buildHistoryTimelineOptimized(record, remediationTask, dateTimeFormatter)
        };
      } else {
        throw new Error('获取检查记录详情失败');
      }
    },

    // 优化的问题信息处理
    processIssueInfo(record, categoryMap) {
      let issueType = '';
      let issueDescription = '';
      
      if (record.has_issues && record.issues && record.issues.length > 0) {
        const firstIssue = record.issues[0];
        issueType = categoryMap[firstIssue.category] || firstIssue.category || '其他问题';
        issueDescription = firstIssue.description || '';
      }
      
      return { issueType, issueDescription };
    },

    // 优化的整改信息处理
    processRectificationInfo(record, dateTimeFormatter) {
      // 基本信息中的评分：只显示原始检查评分，不被复查评分覆盖
      let finalRating = record.overall_rating || record.score || 0;
      let remediationTask = null;
      
      if (record.has_issues && record.related_rectifications && record.related_rectifications.length > 0) {
        const relatedRectification = record.related_rectifications.find(rect => 
          rect.inspection_record_id === record._id || 
          (rect.area_id === record.area_id && new Date(rect.created_at) >= new Date(record.inspection_date))
        );
        
        if (relatedRectification) {
          // 注意：不再覆盖 finalRating，保持原始检查评分
          // 复查评分将在 reviewInfo 中单独处理
          
          remediationTask = {
            id: relatedRectification._id,
            status: relatedRectification.status,
            assignee: relatedRectification.assigned_to_name || '未分配',
            deadline: dateTimeFormatter(relatedRectification.deadline),
            completionTime: relatedRectification.submitted_at ? 
              dateTimeFormatter(relatedRectification.submitted_at) : 
              (relatedRectification.completion_date ? dateTimeFormatter(relatedRectification.completion_date) : null),
            completionNotes: relatedRectification.completion_description || '',
            completionPhotos: this.processRectificationPhotos(relatedRectification.completion_photos || [])
          };
        }
      }
      
      return { finalRating, remediationTask };
    },

    // 处理复查信息
    processReviewInfo(record, remediationTask, dateTimeFormatter) {
      let reviewInfo = null;
      
      if (record.has_issues && record.related_rectifications && record.related_rectifications.length > 0) {
        const rectification = record.related_rectifications[0];
        
        if (rectification.review_date && rectification.status === 'verified') {
          reviewInfo = {
            hasReview: true,
            reviewerName: rectification.reviewer_name || '未知',
            reviewTime: dateTimeFormatter(rectification.review_date),
            reviewComments: rectification.review_comments || '',
            reviewRating: rectification.final_rating || rectification.review_rating || 0,
            reviewResult: rectification.review_result || 'approved',
            reviewPhotos: this.processRectificationPhotos(rectification.review_photos || [])
          };
        }
      }
      
      return reviewInfo;
    },

    // 优化的检查照片处理
    processInspectionPhotos(photos) {
      return photos.map(photo => {
        if (typeof photo === 'string') {
          return {
            url: photo,
            name: '检查照片',
            loadError: false
          };
        } else if (typeof photo === 'object' && photo !== null) {
          return {
            url: photo.url || photo.src || '',
            name: photo.name || photo.description || '检查照片',
            loadError: false
          };
        }
        return {
          url: '',
          name: '检查照片',
          loadError: true
        };
      }).filter(photo => photo.url);
    },

    // 处理整改照片
    processRectificationPhotos(photos) {
      if (!Array.isArray(photos)) return [];
      
      return photos.map(photo => {
        if (typeof photo === 'string') {
          return {
            url: photo,
            name: '整改照片',
            loadError: false
          };
        } else if (typeof photo === 'object' && photo !== null) {
          return {
            url: photo.url || photo.src || '',
            name: photo.description || photo.name || '整改照片',
            loadError: false
          };
        }
        return {
          url: '',
          name: '整改照片',
          loadError: true
        };
      }).filter(photo => photo.url);
    },

    // 优化的历史时间线构建
    buildHistoryTimelineOptimized(record, remediationTask, dateTimeFormatter) {
      const history = [{
        type: 'create',
        title: '开始检查',
        description: `${record.inspector_name || '检查员'}开始检查`,
        time: dateTimeFormatter(record.inspection_date)
      }];

      if (record.has_issues) {
        history.push({
          type: 'issue',
          title: '发现问题',
          description: '检查中发现问题，需要进行整改',
          time: dateTimeFormatter(record.inspection_date)
        });
        
        if (remediationTask) {
          // 1. 创建整改任务
          history.push({
            type: 'task',
            title: '创建整改任务',
            description: `已分配给 ${remediationTask.assignee} 进行整改`,
            time: dateTimeFormatter(record.inspection_date)
          });
          
          // 2. 整改过程记录（基于整改任务的详细状态）
          if (record.related_rectifications && record.related_rectifications.length > 0) {
            const rectification = record.related_rectifications[0];
            this.buildRectificationHistory(history, rectification, dateTimeFormatter);
          } else if (remediationTask.completionTime && remediationTask.completionTime !== '--') {
            // 兼容旧数据格式
            this.buildSimpleRectificationHistory(history, remediationTask);
          }
        }
      } else {
        // 检查通过，无问题发现
        const completionDescription = record.overall_rating > 0 
          ? `检查完成，评分${record.overall_rating}分，${this.getRatingDescription(record.overall_rating)}`
          : '检查完成，责任区状况良好，无发现问题';
          
        history.push({
          type: 'complete',
          title: '检查通过',
          description: completionDescription,
          time: dateTimeFormatter(record.inspection_date)
        });
      }
      
      return history.sort((a, b) => {
        const timeA = this.parseTimeForSort(a.time);
        const timeB = this.parseTimeForSort(b.time);
        return timeA - timeB;
      });
    },

    // 构建详细的整改历史记录
    buildRectificationHistory(history, rectification, dateTimeFormatter) {
      // 整改开始
      if (rectification.created_at) {
        history.push({
          type: 'task',
          title: '开始整改',
          description: `${rectification.assigned_to_name || '负责人'}开始处理整改任务`,
          time: dateTimeFormatter(rectification.created_at)
        });
      }

      // 整改提交
      if (rectification.submitted_at) {
        history.push({
          type: 'review',
          title: '提交整改',
          description: `整改已完成，提交检查员复查`,
          time: dateTimeFormatter(rectification.submitted_at)
        });
      }

      // 复查结果
      if (rectification.review_date) {
        const isApproved = rectification.status === 'verified';
        history.push({
          type: isApproved ? 'complete' : 'issue',
          title: isApproved ? '复查通过' : '需重新整改',
          description: isApproved 
            ? `检查员确认整改合格，问题已解决` 
            : `检查员要求重新整改：${rectification.review_comments || '整改不达标'}`,
          time: dateTimeFormatter(rectification.review_date)
        });
      }

      // 如果有多轮整改，递归处理
      if (rectification.status === 'rejected' && rectification.resubmitted_at) {
        history.push({
          type: 'task',
          title: '重新整改',
          description: `根据检查员要求重新进行整改`,
          time: dateTimeFormatter(rectification.resubmitted_at)
        });
      }
    },

    // 构建简化的整改历史（兼容旧数据）
    buildSimpleRectificationHistory(history, remediationTask) {
      const eventType = remediationTask.status === 'verified' ? 'complete' : 
                       remediationTask.status === 'rejected' ? 'issue' : 'review';
      const eventTitle = remediationTask.status === 'verified' ? '整改完成' :
                        remediationTask.status === 'rejected' ? '整改被拒绝' : '整改提交';
      
      history.push({
        type: eventType,
        title: eventTitle,
        description: remediationTask.status === 'verified' 
          ? '整改任务已完成并通过检查员确认' 
          : remediationTask.status === 'rejected' 
          ? '整改未通过复查，需要重新整改' 
          : '整改任务已完成，等待检查员复查',
        time: remediationTask.completionTime
      });
    },

    // 解析时间用于排序
    parseTimeForSort(timeStr) {
      try {
        const match = timeStr.match(/(\d+)月(\d+)日\s(\d+):(\d+)/);
        if (match) {
          const year = new Date().getFullYear();
          const month = parseInt(match[1]);
          const day = parseInt(match[2]);
          const hour = parseInt(match[3]);
          const minute = parseInt(match[4]);
          return new Date(year, month - 1, day, hour, minute);
        }
        return new Date();
      } catch (error) {
        return new Date();
      }
    },

    // 加载检查记录详情 - 保留原方法作为备用
    async loadInspectionRecordDetail() {
      const result = await callCloudFunction('hygiene-inspection', {
        action: 'getInspectionDetail',
        data: {
          id: this.recordId  // 修复：使用正确的参数名
        }
      });

      if (result && result.success && result.data) {
        const record = result.data;
        
        // 处理问题信息
        let issueType = '';
        let issueDescription = '';
        
        if (record.has_issues && record.issues && record.issues.length > 0) {
          const firstIssue = record.issues[0];
          // 中文问题类型映射
          const categoryMap = {
            'equipment': '设备问题',
            'cleanliness': '清洁问题', 
            'organization': '整理问题',
            'safety': '安全问题',
            'environment': '环境问题',
            'standardization': '标识问题',
            'other': '其他问题'
          };
          issueType = categoryMap[firstIssue.category] || firstIssue.category || '其他问题';
          issueDescription = firstIssue.description || '';
        }

        // 处理评分：保持原始检查评分，不被复查评分覆盖
        let finalRating = record.overall_rating || record.score || 0;
        
        // 处理整改任务信息
        let remediationTask = null;
        if (record.has_issues && record.related_rectifications && record.related_rectifications.length > 0) {
          // 查找与当前检查记录相关的整改记录
          const relatedRectification = record.related_rectifications.find(rect => 
            rect.inspection_record_id === record._id || 
            (rect.area_id === record.area_id && new Date(rect.created_at) >= new Date(record.inspection_date))
          );
          
          if (relatedRectification) {
            // 注意：不再覆盖 finalRating，保持原始检查评分
            // 复查评分将在 reviewInfo 中单独处理
            
            // 构建整改任务信息
            remediationTask = {
              id: relatedRectification._id,
              status: relatedRectification.status,
              assignee: relatedRectification.assigned_to_name || '未分配',
              deadline: this.formatDateTime(relatedRectification.deadline),
              // 优先使用 submitted_at，再使用 completion_date
              completionTime: relatedRectification.submitted_at ? 
                this.formatDateTime(relatedRectification.submitted_at) : 
                (relatedRectification.completion_date ? this.formatDateTime(relatedRectification.completion_date) : null),
              completionNotes: relatedRectification.completion_description || ''
            };
          }
        }

        this.recordInfo = {
          id: record._id,
          areaName: record.area_name || '未知责任区',
          operatorName: record.inspector_name || '未知',
          operationTime: this.formatDateTime(record.inspection_date),
          operationTimestamp: record.inspection_date, // 保存原始时间戳用于API调用
          status: record.status || 'completed',
          rating: finalRating, // 使用处理后的最终评分
          result: record.has_issues ? 'issues' : 'passed',
          hasIssues: record.has_issues || false,
          issueType: issueType,
          issueDescription: issueDescription,
          summary: record.summary || '',
          photos: (record.photos || []).map(photo => {
            // 处理不同的照片数据格式
            if (typeof photo === 'string') {
              return {
                url: photo,
                name: '检查照片',
                loadError: false
              };
            } else if (typeof photo === 'object' && photo !== null) {
              return {
                url: photo.url || photo.src || '',
                name: photo.name || photo.description || '检查照片',
                loadError: false
              };
            }
            return {
              url: '',
              name: '检查照片',
              loadError: true
            };
          }).filter(photo => photo.url), // 过滤掉空URL的照片
          remediationTask: remediationTask,
          history: [
            {
              type: 'create',
              title: '开始检查',
              description: `${record.inspector_name || '检查员'}开始检查`,
              time: this.formatDateTime(record.inspection_date)
            }
          ]
        };

        // 如果有问题，添加问题发现历史
        if (record.has_issues) {
          this.recordInfo.history.push({
            type: 'issue',
            title: '发现问题',
            description: issueDescription || '检查中发现问题',
            time: this.formatDateTime(record.inspection_date)
          });
          
          // 添加整改相关的历史记录
          if (remediationTask) {
            // 添加整改任务创建记录
            this.recordInfo.history.push({
              type: 'task',
              title: '创建整改任务',
              description: `已分配给 ${remediationTask.assignee}整改`,
              time: this.formatDateTime(record.inspection_date)
            });
            
            // 根据整改状态添加相应的历史记录
            if (remediationTask.completionTime && remediationTask.completionTime !== '--' && remediationTask.completionTime !== 'null') {
              if (remediationTask.status === 'pending_review') {
                this.recordInfo.history.push({
                  type: 'complete',
                  title: '整改提交',
                  description: `整改任务已完成并提交复查`,
                  time: remediationTask.completionTime
                });
              } else if (remediationTask.status === 'verified') {
                this.recordInfo.history.push({
                  type: 'complete',
                  title: '整改完成',
                  description: `整改任务已完成并通过确认`,
                  time: remediationTask.completionTime
                });
              } else if (remediationTask.status === 'rejected') {
                this.recordInfo.history.push({
                  type: 'issue',
                  title: '整改被拒绝',
                  description: `整改未通过复查，需要重新整改`,
                  time: remediationTask.completionTime
                });
              }
            }
          }
        }
        
        // 按时间排序操作历史 - 使用iOS兼容的日期格式
        this.recordInfo.history.sort((a, b) => {
          // 解析中文时间格式 "8月12日 16:23" 为iOS兼容的日期
          const parseChineseTime = (timeStr) => {
            const match = timeStr.match(/(\d+)月(\d+)日\s(\d+):(\d+)/);
            if (match) {
              const year = new Date().getFullYear();
              const month = parseInt(match[1]);
              const day = parseInt(match[2]);
              const hour = parseInt(match[3]);
              const minute = parseInt(match[4]);
              // 使用 Date 构造函数的数字参数，避免字符串解析问题
              return new Date(year, month - 1, day, hour, minute);
            }
            // 如果无法解析，返回当前时间
            return new Date();
          };
          
          const timeA = parseChineseTime(a.time);
          const timeB = parseChineseTime(b.time);
          return timeA - timeB;
        });
        
      } else {
        throw new Error('获取检查记录详情失败');
      }
    },

    // 获取周数
    getWeekNumber(date) {
      const onejan = new Date(date.getFullYear(), 0, 1);
      const millisecsInDay = 86400000;
      return Math.ceil((((date.getTime() - onejan.getTime()) / millisecsInDay) + onejan.getDay() + 1) / 7);
    },

    // 获取一周的开始日期（周一）
    getWeekStart(date) {
      const d = new Date(date);
      const day = d.getDay();
      const diff = d.getDate() - day + (day === 0 ? -6 : 1);
      const result = new Date(d.setDate(diff));
      result.setHours(0, 0, 0, 0);
      return result;
    },

    // 获取一周的结束日期（周日）
    getWeekEnd(date) {
      const d = new Date(date);
      const day = d.getDay();
      const diff = d.getDate() - day + (day === 0 ? 0 : 7);
      const result = new Date(d.setDate(diff));
      result.setHours(23, 59, 59, 999);
      return result;
    },

    // 格式化日期时间
    formatDateTime(dateString) {
      if (!dateString || dateString === 'null' || dateString === 'undefined') return '--';
      
      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
          return '--';
        }
        
        return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      } catch (error) {
        return '--';
      }
    },

    // 重试加载
    retryLoad() {
      this.dataLoaded = false; // 重置数据加载标记
      this.loadError = false;
      this.loadRecordDetail();
    },

    // 预览照片
    previewPhoto(index) {
      const urls = this.recordInfo.photos.map(photo => photo.url);
      uni.previewImage({
        urls: urls,
        current: index
      });
    },

    // 照片加载重试
    retryPhotoLoad(index) {
      this.$set(this.recordInfo.photos[index], 'loadError', false);
    },

    // 照片加载失败处理
    onPhotoError(index) {
      this.$set(this.recordInfo.photos[index], 'loadError', true);
    },

    // 检查编辑权限
    async checkEditPermission() {
      // 只有清理记录才允许编辑
      if (this.recordType !== 'cleaning') {
        this.editPermission = { canEdit: false, message: '', checking: false };
        return;
      }

      // 1. 检查是否为历史记录
      if (this.isHistoricalRecord) {
        this.editPermission = { 
          canEdit: false, 
          message: '历史记录不允许修改',
          checking: false
        };
        return;
      }

      // 2. 检查记录时效性（基于清理日期）
      const timeCheckResult = this.checkRecordTimeValidity();
      if (!timeCheckResult.canEdit) {
        this.editPermission = { ...timeCheckResult, checking: false };
        return;
      }

      try {
        // 3. 检查该清理记录是否已经被检查过
        const areaId = this.recordInfo.areaId;
        if (!areaId) {
          this.editPermission = { canEdit: true, message: '', checking: false };
          return;
        }

        // 3. 关键检查：该清理记录是否已经被检查员检查过
        const cleaningRecordTime = new Date(this.recordInfo.operationTimestamp);
        
        // 查询该责任区在清理记录之后的检查记录
        let hasInspectionAfterCleaning = false;
        
        try {
          const inspectionResult = await callCloudFunction('hygiene-inspection', {
            action: 'getInspectionRecords',
            data: { 
              area_id: areaId,
              pageSize: 50
            }
          });

          if (inspectionResult && inspectionResult.success && inspectionResult.data) {
            const records = inspectionResult.data.list;
            
            if (Array.isArray(records)) {
              // 筛选在清理记录之后的检查记录
              const relatedInspections = records.filter(inspection => {
                const inspectionTime = new Date(inspection.inspection_date);
                return inspectionTime > cleaningRecordTime;
              });
              
              hasInspectionAfterCleaning = relatedInspections.length > 0;
            }
          }
        } catch (inspectionError) {
          console.warn('检查记录查询失败:', inspectionError);
          // 如果查询失败，为了安全起见，不允许编辑
          hasInspectionAfterCleaning = true;
        }

        // 如果清理记录已被检查，则不允许编辑
        if (hasInspectionAfterCleaning) {
          this.editPermission = { 
            canEdit: false, 
            message: '该清理记录已被检查员检查，不允许修改',
            checking: false
          };

          return;
        }

        // 4. 检查该责任区是否有相关的整改任务（作为备用检查）
        const rectificationResult = await callCloudFunction('hygiene-rectification', {
          action: 'getRectifications',
          data: { 
            area_id: areaId,
            pageSize: 10
          }
        });

        // 检查是否有进行中的整改任务
        let hasActiveTasks = false;
        if (rectificationResult && rectificationResult.success && 
            rectificationResult.data && rectificationResult.data.list) {
          
          // 使用已定义的清理记录时间
          const recordTime = cleaningRecordTime;
          
          // 简化逻辑：主要依赖检查记录判断，整改任务作为辅助判断
          const activeTasks = rectificationResult.data.list.filter(task => {
            const taskCreatedTime = new Date(task.created_at || task.createdAt);
            const isTaskAfterRecord = taskCreatedTime > recordTime;
            
            // 只检查在清理记录之后的未完成整改任务
            const blockingStatuses = [
              'pending_rectification',  // 待整改
              'pending_assignment',     // 待分配  
              'in_progress',           // 整改中
              'pending_review',        // 待复查
              'rectification_completed' // 整改已完成（等待验证）
            ];
            
            const isBlocking = isTaskAfterRecord && blockingStatuses.includes(task.status);
            return isBlocking;
          });
          hasActiveTasks = activeTasks.length > 0;
          

        }

        if (hasActiveTasks) {
          this.editPermission = { 
            canEdit: false, 
            message: '该区域有进行中的整改任务，暂时无法修改清理记录',
            checking: false
          };
        } else {
          this.editPermission = { canEdit: true, message: '', checking: false };
        }
      } catch (error) {
        // 出错时不允许编辑，避免潜在问题
        this.editPermission = { 
          canEdit: false, 
          message: '权限检查失败，暂时无法修改',
          checking: false
        };
      }
    },

    // 检查记录时效性
    checkRecordTimeValidity() {
      if (!this.recordInfo.operationTimestamp) {
        return { canEdit: false, message: '无法确定记录时间，不允许修改' };
      }

      const now = new Date();
      const recordTime = new Date(this.recordInfo.operationTimestamp);
      
      // 检查是否为本周记录
      const currentWeekStart = this.getWeekStart(now);
      const currentWeekEnd = this.getWeekEnd(now);
      
      if (recordTime < currentWeekStart || recordTime > currentWeekEnd) {
        return { 
          canEdit: false, 
          message: '只能修改本周的清理记录' 
        };
      }

      // 检查是否在24小时内
      const timeDiff = now.getTime() - recordTime.getTime();
      const hours24 = 24 * 60 * 60 * 1000;
      
      if (timeDiff > hours24) {
        return { 
          canEdit: false, 
          message: '记录提交超过24小时，不允许修改' 
        };
      }

      return { canEdit: true, message: '' };
    },

    // 获取状态文本 - 使用缓存优化
    getStatusText(status) {
      if (!this.processCache?.statusMaps) this.initProcessCache();
      return this.processCache.statusMaps.record[status] || '已完成';
    },

    // 获取结果文本 - 使用缓存优化
    getResultText(result) {
      if (!this.processCache?.statusMaps) this.initProcessCache();
      return this.processCache.statusMaps.result[result] || result;
    },

    // 获取任务状态文本 - 使用缓存优化
    getTaskStatusText(status) {
      if (!status) return '未知状态';
      if (!this.processCache?.statusMaps) this.initProcessCache();
      return this.processCache.statusMaps.task[status] || status;
    },

    // 获取任务状态图标 - 使用缓存优化
    getTaskStatusIcon(status) {
      if (!status) return 'info';
      if (!this.processCache?.statusMaps) this.initProcessCache();
      return this.processCache.statusMaps.taskIcon[status] || 'info';
    },

    // 编辑记录
    editRecord() {
      // 跳转到清理提交页面，传入记录ID用于编辑
      const areaId = this.recordInfo.areaId || this.areaId;
      const areaType = this.recordInfo.areaType || 'fixed';
      
      uni.navigateTo({
        url: `/pages/6s_pkg/cleaning-upload?mode=edit&recordId=${this.recordId}&areaId=${areaId}&type=${areaType}`
      });
    },

    // 获取评分描述 - 使用缓存优化
    getRatingDescription(rating) {
      if (!this.processCache?.ratingDescCache) {
        if (!this.processCache) this.initProcessCache();
        this.processCache.ratingDescCache = new Map();
      }
      
      // 检查缓存
      if (this.processCache.ratingDescCache.has(rating)) {
        return this.processCache.ratingDescCache.get(rating);
      }
      
      // 计算描述
      let description = '';
      if (rating === 0) description = '请评分';
      else if (rating <= 1) description = '较差';
      else if (rating <= 2) description = '一般';
      else if (rating <= 3) description = '良好';
      else if (rating < 5) description = '优秀';  // 4-4.5分都是优秀
      else if (rating === 5) description = '完美';
      
      // 缓存结果
      this.processCache.ratingDescCache.set(rating, description);
      return description;
    },

    // 处理清理记录更新事件
    handleRecordUpdated(data) {
      // 如果更新的是当前记录，重新加载数据
      if (data.recordId === this.recordId) {
        this.loadRecordDetail();
      }
    },

    // 预览整改照片
    previewRectificationPhoto(index) {
      const urls = this.recordInfo.remediationTask.completionPhotos.map(photo => photo.url);
      uni.previewImage({
        urls: urls,
        current: index
      });
    },

    // 预览复查照片
    previewReviewPhoto(index) {
      const urls = this.recordInfo.reviewInfo.reviewPhotos.map(photo => photo.url);
      uni.previewImage({
        urls: urls,
        current: index
      });
    },

    // 通用照片错误处理方法
    handlePhotoError(photoArray, index) {
      this.$set(photoArray[index], 'loadError', true);
    },

    // 通用照片重试方法
    retryPhotoLoad(photoArray, index) {
      this.$set(photoArray[index], 'loadError', false);
    },

    // 整改照片加载失败处理
    onRectificationPhotoError(index) {
      this.handlePhotoError(this.recordInfo.remediationTask.completionPhotos, index);
    },

    // 复查照片加载失败处理
    onReviewPhotoError(index) {
      this.handlePhotoError(this.recordInfo.reviewInfo.reviewPhotos, index);
    },

    // 整改照片加载重试
    retryRectificationPhotoLoad(index) {
      this.retryPhotoLoad(this.recordInfo.remediationTask.completionPhotos, index);
    },

    // 复查照片加载重试
    retryReviewPhotoLoad(index) {
      this.retryPhotoLoad(this.recordInfo.reviewInfo.reviewPhotos, index);
    },
  }
}
</script>

<style lang="scss" scoped>
.page-container {
	min-height: 100vh;
	background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
	width: 100%;
	overflow-x: hidden; /* 防止水平滚动 */
	box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  padding: 32rpx 32rpx 40rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  position: relative;
  z-index: 1000;
}

.header-content {
  flex: 1;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}

.status-badge-enhanced {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 400;
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(255, 255, 255, 0.2);

  &.status-not-cleaned {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.95);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
  }
  
  &.status-pending {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.95);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
  }
  
  &.status-pending-rectification {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.95);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
  }
  
  &.status-completed {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.95);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
  }
  
  &.status-missed {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.95);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
  }
  
  &.status-cancelled {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.95);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
  }
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 16rpx;
  margin: 24rpx 32rpx 0 32rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 32rpx 32rpx 16rpx 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 4rpx;
}

.card-subtitle {
  font-size: 26rpx;
  color: #8E8E93;
}

.card-subtitle-enhanced {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #8E8E93;
}

.card-body {
  padding: 32rpx;
}

/* 信息列表 */
.info-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  color: #1C1C1E;
}

.info-value {
  font-size: 28rpx;
  color: #8E8E93;
  text-align: right;
  
  &.rating-value {
    display: flex;
    align-items: center;
    gap: 4rpx;
    color: #FFD700;
  }
}

/* 增强信息网格 */
.info-grid-enhanced {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));
  gap: 24rpx;
  margin-top: 24rpx;
}

.info-item-enhanced {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 24rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.info-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #007AFF; /* 默认图标背景色 */
  color: white;
  font-size: 32rpx;

  &.icon-cleaning {
    background-color: #34C759; /* 清理图标背景色 */
  }

  &.icon-inspection {
    background-color: #FF9500; /* 检查图标背景色 */
  }

  &.icon-time {
    background-color: #FF3B30; /* 时间图标背景色 */
  }

  &.icon-cycle {
    background-color: #5AC8FA; /* 周期图标背景色 */
  }

  &.icon-rating {
    background-color: #FFD700; /* 评分图标背景色 */
  }

  &.icon-review {
    background-color: #34C759; /* 复查图标背景色 */
  }
}

.info-content {
  flex: 1;
}

.info-label-enhanced {
  font-size: 28rpx;
  color: #1C1C1E;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.info-value-enhanced {
  font-size: 28rpx;
  color: #8E8E93;
  font-weight: 600;
}

.rating-item-enhanced {
  grid-column: span 2; /* 评分项跨两列 */
}

.period-item-enhanced {
  grid-column: span 2; /* 所属周期项跨两列 */
}

.rating-display-enhanced {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-top: 8rpx;
}

.rating-number {
  font-size: 36rpx;
  font-weight: 700;
  color: #FFD700;
}

.rating-stars {
  display: flex;
  gap: 4rpx;
}

.rating-desc {
  font-size: 26rpx;
  color: #8E8E93;
  margin-left: 12rpx;
}

/* 照片网格 */
.no-photos {
  padding: 40rpx 0;
}

.no-photos-enhanced {
  padding: 40rpx 0;
}

.photos-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.photos-grid-enhanced {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 默认3列布局 */
  gap: 16rpx;
}

.photo-item {
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  background: #F2F2F7;
}

.photo-item-enhanced {
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  background: #F2F2F7;
  display: flex; /* 使用flex布局 */
  flex-direction: column; /* 垂直排列内容 */
  justify-content: flex-end; /* 底部对齐 */
  align-items: center; /* 居中 */
}

.photo-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  overflow: hidden;
}

.photo-image-enhanced {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}

.photo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.photo-overlay-enhanced {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12rpx; /* 与图片圆角一致 */
}

.photo-item:active .photo-overlay {
  opacity: 1;
}

.photo-item-enhanced:active .photo-overlay-enhanced {
  opacity: 1;
}

.overlay-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}

.overlay-text {
  font-size: 24rpx;
  color: white;
}



/* 检查结果 */
.result-section {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-label {
  font-size: 28rpx;
  color: #1C1C1E;
  font-weight: 500;
}

.result-value {
  font-size: 28rpx;
  font-weight: 600;
  
  &.result-passed {
    color: #34C759;
  }
  
  &.result-issues {
    color: #FF3B30;
  }
}

/* 问题详情 */
.issue-details {
  background: #FFF5F5;
  border: 1rpx solid #FFE6E6;
  border-radius: 12rpx;
  padding: 24rpx;
}

/* 复查区域样式 */
.review-section {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #E8F5E8;
  background: #F8FFF8;
  border-radius: 12rpx;
  padding: 24rpx;
}

.review-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #34C759;
  margin-bottom: 16rpx;
}

/* 复查详情区域 */
.review-details-section {
  margin-bottom: 16rpx;
}

.review-info-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 12rpx;
}

.review-info-item {
  flex: 1;
  min-width: 200rpx;
  display: flex;
  align-items: center;
  
  &.rating-item {
    flex: 100%;
  }
}

.review-label {
  font-size: 26rpx;
  color: #8E8E93;
  font-weight: 500;
  white-space: nowrap;
}

.review-value {
  font-size: 26rpx;
  color: #1C1C1E;
  font-weight: 600;
  margin-left: 8rpx;
}

.review-rating-display {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-left: 8rpx;
}

.rating-score {
  font-size: 28rpx;
  color: #34C759;
  font-weight: 700;
}

.rating-level {
  font-size: 24rpx;
  color: #8E8E93;
}

.review-comments {
  margin-top: 16rpx;
}

.review-comment-content {
  font-size: 28rpx;
  color: #1C1C1E;
  line-height: 1.5;
  margin-top: 8rpx;
  background: white;
  padding: 16rpx;
  border-radius: 8rpx;
  border: 1rpx solid #E8F5E8;
}

.review-conclusion {
  text-align: center;
  padding-top: 16rpx;
  border-top: 1rpx solid #E8F5E8;
}

.conclusion-text {
  font-size: 28rpx;
  color: #34C759;
  font-weight: 600;
}

.review-suffix {
  font-size: 24rpx;
  color: #8E8E93;
  margin-left: 8rpx;
}

/* 整改任务中的复查信息 */
.review-details {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(52, 199, 89, 0.2);
}

.review-rating {
  font-size: 26rpx;
  color: #34C759;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.review-comments-task {
  font-size: 26rpx;
  color: #1C1C1E;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

/* 整改照片和复查照片样式 */
.rectification-photos,
.review-photos {
  margin-top: 24rpx;
  padding: 24rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
  border: 1rpx solid #E5E7EB;
}

.photos-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.photos-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
}

.rectification-photos .photos-title {
  color: #34C759;
}

.review-photos .photos-title {
  color: #5AC8FA;
}

/* 独立复查结果区域样式 */
.review-section-standalone {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.review-comments-section {
  margin-top: 24rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.review-conclusion-standalone {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 20rpx;
  background: #F0F9F0;
  border: 1rpx solid #34C759;
  border-radius: 12rpx;
  margin-top: 24rpx;
}

.conclusion-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.review-photos-section {
  margin-top: 24rpx;
}

.issue-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #FF3B30;
  margin-bottom: 16rpx;
}

.issue-time-info {
  font-size: 26rpx;
  color: #8E8E93;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.issue-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.issue-type {
  font-size: 26rpx;
  color: #FF3B30;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.issue-description {
  font-size: 26rpx;
  color: #1C1C1E;
  line-height: 1.5;
  font-weight: 400;
  padding-top: 12rpx;
  border-top: 1rpx solid #FFE6E6;
  margin-top: 8rpx;
  margin-bottom: 4rpx;
}

.remediation-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16rpx;
  border-top: 1rpx solid #FFE6E6;
}

.remediation-label {
  font-size: 26rpx;
  color: #8E8E93;
}

.remediation-value {
  font-size: 26rpx;
  font-weight: 500;
  

}

/* 说明内容 */
.notes-content {
  font-size: 28rpx;
  color: #1C1C1E;
  line-height: 1.6;
  background: #F8F9FA;
  padding: 24rpx;
  border-radius: 12rpx;
}

/* 整改任务 */
.remediation-task {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.task-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-label {
  font-size: 28rpx;
  color: #1C1C1E;
}

.task-value {
  font-size: 28rpx;
  color: #8E8E93;
  
  &.task-status-not-cleaned {
    color: #8E8E93;
  }
  
  &.task-status-pending {
    color: #FF9500;
  }
  
  &.task-status-pending-rectification {
    color: #FF3B30;
  }
  
  &.task-status-completed {
    color: #34C759;
  }
  
  &.task-status-missed {
    color: #8B5CF6;
  }
}

.task-status-badge {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  color: white;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);

  &.task-badge-not-cleaned {
    background: #8E8E93;
    color: white;
    border: none;
    box-shadow: 0 2rpx 8rpx rgba(142, 142, 147, 0.3);
  }
  
  &.task-badge-pending {
    background: #FF9500;
    color: white;
    border: none;
    box-shadow: 0 2rpx 8rpx rgba(255, 149, 0, 0.3);
  }
  
  &.task-badge-pending-rectification {
    background: #FF3B30;
    color: white;
    border: none;
    box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.3);
  }
  
  &.task-badge-completed {
    background: #34C759;
    color: white;
    border: none;
    box-shadow: 0 2rpx 8rpx rgba(52, 199, 89, 0.3);
  }
  
  &.task-badge-missed {
    background: #8B5CF6;
    color: white;
    border: none;
    box-shadow: 0 2rpx 8rpx rgba(139, 92, 246, 0.3);
  }
}

.completion-info {
  background: #E8F5E8;
  border: 1rpx solid #34C759;
  border-radius: 12rpx;
  padding: 20rpx;
}

.completion-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 30rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

/* 根据状态动态设置文字颜色 */
.completion-header text {
  color: inherit; /* 继承父元素颜色 */
}

.completion-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.completion-time,
.completion-notes {
  font-size: 26rpx;
  color: #1C1C1E;
  line-height: 1.5;
  margin-bottom: 4rpx;
}

.completion-time {
  font-weight: 500;
  color: #8E8E93;
}

.completion-notes {
  color: #1C1C1E;
  font-weight: 400;
  padding-top: 12rpx;
  border-top: 1rpx solid rgba(142, 142, 147, 0.3);
  margin-top: 8rpx;
}

/* 时间线 */
.timeline {
  position: relative;
  padding-left: 48rpx;
}

.timeline-item {
  position: relative;
  margin-bottom: 32rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &:not(:last-child)::after {
    content: '';
    position: absolute;
    left: -40rpx;
    top: 32rpx;
    width: 2rpx;
    height: calc(100% + 16rpx);
    background: #E5E5EA;
  }
}

.timeline-dot {
  position: absolute;
  left: -48rpx;
  top: 8rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  
  &.dot-create {
    background: #007AFF;
  }
  
  &.dot-review {
    background: #34C759;
  }
  
  &.dot-issue {
    background: #FF3B30;
  }
  
  &.dot-task {
    background: #FF9500;
  }
  
  &.dot-complete {
    background: #34C759;
  }
  
  &.dot-review {
    background: #5AC8FA;
  }
}

.timeline-content {
  background: #F8F9FA;
  padding: 20rpx;
  border-radius: 12rpx;
}

.timeline-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 8rpx;
}

.timeline-desc {
  font-size: 26rpx;
  color: #8E8E93;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.timeline-time {
  font-size: 24rpx;
  color: #C7C7CC;
}

/* 操作按钮 */
.action-section {
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.action-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  
  &.warning {
    background: #FF9500;
    color: white;
  }
  
  &.primary {
    background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
    color: white;
  }
  
  &.danger {
    background: #FF3B30;
    color: white;
  }
  
  &:active {
    transform: scale(0.98);
  }

  &.btn-loading {
    opacity: 0.7;
    pointer-events: none;
  }
}

/* 编辑禁用提示 */
.edit-disabled-tip {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 24rpx;
  background: #FFF3E0;
  border: 1rpx solid #FFB74D;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #F57C00;
  line-height: 1.4;
}

/* 检查结果状态徽章 */
.result-status-badge {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  color: white;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);

  &.badge-passed {
    background: #34C759;
    color: white;
    border: none;
    box-shadow: 0 2rpx 8rpx rgba(52, 199, 89, 0.3);
  }
  
  &.badge-issues {
    background: #FF3B30;
    color: white;
    border: none;
    box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.3);
  }
}

/* 无问题显示 */
.no-issue-display {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
  gap: 16rpx;
}

.no-issue-icon {
  flex-shrink: 0;
}

.no-issue-content {
  flex: 1;
}

.no-issue-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 4rpx;
}

.no-issue-desc {
  font-size: 28rpx;
  color: #8E8E93;
  line-height: 1.4;
  font-weight: 400;
}





/* 错误状态 */
.error-container {
  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
}

.retry-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: #007AFF;
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  
  &:active {
    transform: scale(0.95);
  }
}

/* 照片错误状态 */
.photo-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.error-text {
  color: white;
  font-size: 24rpx;
  margin-top: 12rpx;
}

/* 文本展开/收起 */
.text-collapsed {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3; /* 默认显示3行 */
  line-clamp: 3; /* 现代浏览器标准属性 */
  overflow: hidden;
  text-overflow: ellipsis;
}

.expand-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #007AFF;
  margin-top: 12rpx;
  cursor: pointer;
}

/* 底部安全间距 */
.bottom-safe-area {
  height: 40rpx;
}

/* 内容区域加载状态 */
.content-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  pointer-events: auto;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #E5E7EB;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
  text-align: center;
  margin-top: 8rpx;
}

/* 内容区域错误状态 */
.content-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

/* H5浏览器端优化 - 条件编译 */
/* #ifdef H5 */
@media screen and (min-width: 600px) {
  .photos-grid-enhanced {
    max-width: 480px;
  }
  
  .photo-item-enhanced {
    max-width: 150px;
    max-height: 150px;
  }
}
/* #endif */

/* 响应式调整 */
@media (max-width: 600rpx) {
  .page-header {
    padding: 24rpx 16rpx;
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }
  
  .card {
    margin: 24rpx 16rpx 0 16rpx;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx;
  }
  
  .action-section {
    padding: 24rpx 16rpx;
  }
  
  .info-grid-enhanced {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }
  
  .rating-item-enhanced,
  .period-item-enhanced {
    grid-column: span 1;
  }
  
  .photos-grid-enhanced {
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
  }
}

/* 小屏设备优化 */
@media (max-width: 400rpx) {
  .photos-grid-enhanced {
    grid-template-columns: 1fr;
  }
  
  .info-grid-enhanced {
    grid-template-columns: 1fr;
  }
  
  .rating-display-enhanced {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }
  
  .status-badge-enhanced,
  .result-status-badge,
  .task-status-badge {
    font-size: 22rpx;
    padding: 6rpx 12rpx;
  }
}


</style> 