require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/cleaning-upload"],{"07fe":function(e,t,r){"use strict";r.r(t);var n=r("784d"),a=r("761c");for(var o in a)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(o);r("39cd");var u=r("828b"),s=Object(u["a"])(a["default"],n["b"],n["c"],!1,null,"372fbbc5",null,!1,n["a"],void 0);t["default"]=s.exports},"236c":function(e,t,r){"use strict";(function(e,t){var n=r("47a9");r("357b"),r("861b");n(r("3240"));var a=n(r("07fe"));e.__webpack_require_UNI_MP_PLUGIN__=r,t(a.default)}).call(this,r("3223")["default"],r("df3c")["createPage"])},"39cd":function(e,t,r){"use strict";var n=r("bf14"),a=r.n(n);a.a},"761c":function(e,t,r){"use strict";r.r(t);var n=r("7b42"),a=r.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(o);t["default"]=a.a},"784d":function(e,t,r){"use strict";r.d(t,"b",(function(){return a})),r.d(t,"c",(function(){return o})),r.d(t,"a",(function(){return n}));var n={uniIcons:function(){return Promise.all([r.e("common/vendor"),r.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(r.bind(null,"6ddf"))}},a=function(){var e=this,t=e.$createElement,r=(e._self._c,"edit"!==e.mode?e.getAreaTypeText(e.areaInfo.type):null),n=e.__map(e.photos,(function(t,r){var n=e.__get_orig(t),a=e.getPhotoDisplayUrl(t);return{$orig:n,m1:a}})),a=e.photos.length,o=0===e.photos.length||e.loading||e.uploading;e.$mp.data=Object.assign({},{$root:{m0:r,l0:n,g0:a,g1:o}})},o=[]},"7b42":function(e,t,r){"use strict";(function(e,n){var a=r("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(r("7eb4")),u=a(r("af34")),s=a(r("7ca3")),i=a(r("ee10")),c=a(r("4ea0")),l=a(r("8ae5"));function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach((function(t){(0,s.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var h={name:"CleaningUpload",data:function(){return{mode:"create",recordId:"",areaInfo:{},photos:[],remarks:"",loading:!1,uploading:!1,uploadProgress:0,autoUpload:!0,uploadQueue:[],processCache:{areaTypeMap:null,photoUrlCache:new Map}}},computed:{remarksLength:function(){return this.remarks?this.remarks.length:0}},onLoad:function(e){this.mode=e.mode||"create",this.recordId=e.recordId||"";var t=e.areaId||e.area_id||e.id,r=e.type||"fixed";this.initProcessCache(),r&&(this.areaInfo.type=r),this.loadAreaInfoOptimized(t),"edit"===this.mode&&this.recordId&&this.loadRecordDataOptimized()},methods:{initProcessCache:function(){this.processCache.areaTypeMap||(this.processCache.areaTypeMap={fixed:"固定责任区清理",public:"公共责任区清理"})},handleRemarksInput:function(t){var r=t.detail.value||"";r.length>200&&(r=r.substring(0,200),e.showToast({title:"备注不能超过200个字符",icon:"none",duration:1500})),this.remarks=r,this.$forceUpdate()},loadAreaInfoOptimized:function(t){var r=this;return(0,i.default)(o.default.mark((function n(){var a;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t){n.next=4;break}return e.showToast({title:"责任区ID不能为空",icon:"none"}),setTimeout((function(){e.navigateBack()}),1500),n.abrupt("return");case 4:return n.prev=4,n.next=7,l.default.getAreaDetail(t);case 7:a=n.sent,r.areaInfo=p(p({},a),{},{type:a.type||r.areaInfo.type||"fixed"}),n.next=14;break;case 11:n.prev=11,n.t0=n["catch"](4),r.handleLoadError(n.t0,(function(){return r.loadAreaInfoOptimized(t)}));case 14:case"end":return n.stop()}}),n,null,[[4,11]])})))()},handleLoadError:function(t,r){var n="获取数据失败";n=t.message.includes("未登录")?"请先登录":t.message.includes("权限")?"您没有权限查看该责任区":t.message.includes("不存在")?"责任区不存在或已删除":t.message||"网络连接失败，请稍后重试",e.showModal({title:"获取数据失败",content:n,showCancel:!0,cancelText:"返回",confirmText:"重试",success:function(t){t.confirm?r():e.navigateBack()}})},loadAreaInfo:function(e){var t=this;return(0,i.default)(o.default.mark((function r(){return o.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.abrupt("return",t.loadAreaInfoOptimized(e));case 1:case"end":return r.stop()}}),r)})))()},loadRecordDataOptimized:function(){var t=this;return(0,i.default)(o.default.mark((function r(){var a,u,s,i;return o.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(t.recordId){r.next=2;break}return r.abrupt("return");case 2:return r.prev=2,e.showLoading({title:"加载记录数据..."}),r.next=6,n.callFunction({name:"hygiene-cleaning",data:{action:"getCleaningRecordDetail",data:{record_id:t.recordId}}});case 6:if(u=r.sent,null===(a=u.result)||void 0===a||!a.success||!u.result.data){r.next=12;break}s=u.result.data,t.processRecordData(s),r.next=13;break;case 12:throw new Error((null===(i=u.result)||void 0===i?void 0:i.message)||"获取记录数据失败");case 13:r.next=18;break;case 15:r.prev=15,r.t0=r["catch"](2),t.handleLoadError(r.t0,(function(){return t.loadRecordDataOptimized()}));case 18:return r.prev=18,e.hideLoading(),r.finish(18);case 21:case"end":return r.stop()}}),r,null,[[2,15,18,21]])})))()},processRecordData:function(e){var t,r=this;this.remarks=e.remark||"",(null===(t=e.photos)||void 0===t?void 0:t.length)>0&&(this.photos=e.photos.map((function(e){var t={url:e.url||e,uploaded:!0,cloudUrl:e.url||e,cloudPath:e.url||e,uploading:!1,belongsToRecord:!0},n=t.url;return r.processCache.photoUrlCache.has(n)||r.processCache.photoUrlCache.set(n,r.processPhotoUrl(t.url)),t})))},processPhotoUrl:function(e){return"string"===typeof e&&e.startsWith("http://tmp/"),e},loadRecordData:function(){var e=this;return(0,i.default)(o.default.mark((function t(){return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.abrupt("return",e.loadRecordDataOptimized());case 1:case"end":return t.stop()}}),t)})))()},addPhoto:function(){var t=this;this.photos.length>=12?e.showToast({title:"最多只能上传12张照片",icon:"none"}):e.chooseImage({count:12-this.photos.length,sizeType:["compressed"],sourceType:["camera","album"],success:function(e){var r,n=t.processNewPhotos(e.tempFilePaths);(r=t.photos).push.apply(r,(0,u.default)(n)),t.autoUpload&&t.autoUploadNewPhotosOptimized(n)},fail:function(){e.showToast({title:"选择照片失败",icon:"none"})}})},processNewPhotos:function(e){var t=this;return e.map((function(e){var r={url:e,uploaded:!1,cloudUrl:"",cloudPath:"",uploading:!1,belongsToRecord:!1},n=e;return t.processCache.photoUrlCache.has(n)||t.processCache.photoUrlCache.set(n,t.processPhotoUrl(e)),r}))},deletePhoto:function(t){var r=this;return(0,i.default)(o.default.mark((function a(){var u;return o.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!(t<0||t>=r.photos.length)){a.next=2;break}return a.abrupt("return");case 2:if(u=r.photos[t],!u.uploaded||!u.cloudPath){a.next=21;break}if(a.prev=4,e.showLoading({title:"删除照片中..."}),"edit"!==r.mode||!r.recordId||!1===u.belongsToRecord){a.next=11;break}return a.next=9,l.default.deleteCleaningPhotos(r.recordId,[u.cloudPath]);case 9:a.next=13;break;case 11:return a.next=13,n.callFunction({name:"delete-file",data:{fileList:[r.extractFileId(u.cloudPath)]}});case 13:a.next=18;break;case 15:a.prev=15,a.t0=a["catch"](4),e.showToast({title:"删除云端照片失败",icon:"none"});case 18:return a.prev=18,e.hideLoading(),a.finish(18);case 21:r.photos.splice(t,1);case 22:case"end":return a.stop()}}),a,null,[[4,15,18,21]])})))()},extractFileId:function(e){if(e.startsWith("cloud://")){var t=e.split("/");return t[t.length-1]}if(e.includes("tcb-api")){var r=new URL(e);return r.pathname.split("/").pop()}return e},previewPhoto:function(t){var r=this,n=this.photos.map((function(e){return r.getPhotoDisplayUrl(e)}));e.previewImage({urls:n,current:t})},autoUploadNewPhotosOptimized:function(t){var r=this;return(0,i.default)(o.default.mark((function n(){var a,u,s;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return a=t.map(function(){var e=(0,i.default)(o.default.mark((function e(t,n){var a,u;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a=r.photos.findIndex((function(e){return e.url===t.url})),-1!==a){e.next=3;break}return e.abrupt("return",{success:!1,index:n});case 3:return e.prev=3,r.$set(r.photos[a],"uploading",!0),e.next=7,r.uploadSinglePhotoOptimized(t);case 7:if(u=e.sent,!u.success){e.next=13;break}return Object.assign(r.photos[a],{uploaded:!0,cloudUrl:u.url,cloudPath:u.cloudPath,uploading:!1}),e.abrupt("return",{success:!0,index:n});case 13:throw new Error(u.error||"上传失败");case 14:e.next=20;break;case 16:return e.prev=16,e.t0=e["catch"](3),r.$set(r.photos[a],"uploading",!1),e.abrupt("return",{success:!1,index:n,error:e.t0.message});case 20:case"end":return e.stop()}}),e,null,[[3,16]])})));return function(t,r){return e.apply(this,arguments)}}()),n.next=3,Promise.allSettled(a);case 3:u=n.sent,s=u.filter((function(e){return"fulfilled"===e.status&&!e.value.success})).map((function(e){return e.value})),s.length>0&&e.showToast({title:"".concat(s.length,"张照片上传失败"),icon:"none",duration:2e3});case 6:case"end":return n.stop()}}),n)})))()},autoUploadNewPhotos:function(e){var t=this;return(0,i.default)(o.default.mark((function r(){return o.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.abrupt("return",t.autoUploadNewPhotosOptimized(e));case 1:case"end":return r.stop()}}),r)})))()},uploadSinglePhotoOptimized:function(e){var t=this;return(0,i.default)(o.default.mark((function r(){var n,a,u;return o.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.prev=0,n=t.generateCloudPath(),r.next=4,c.default.uploadToCloud(e.url,n);case 4:if(a=r.sent,null===a||void 0===a||!a.fileID){r.next=12;break}return r.next=8,c.default.getFileInfo(a.fileID);case 8:return u=r.sent,r.abrupt("return",{success:!0,cloudPath:a.fileID,url:u.tempFileURL||a.fileID,size:a.actualSize});case 12:throw new Error("上传返回结果异常");case 13:r.next=18;break;case 15:return r.prev=15,r.t0=r["catch"](0),r.abrupt("return",{success:!1,error:r.t0.message});case 18:case"end":return r.stop()}}),r,null,[[0,15]])})))()},generateCloudPath:function(){var e=Date.now(),t=Math.random().toString(36).substring(2,8);return"6s/cleaning/".concat(this.areaInfo._id,"/").concat(e,"_").concat(t,".jpg")},uploadSinglePhoto:function(e){var t=this;return(0,i.default)(o.default.mark((function r(){return o.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.abrupt("return",t.uploadSinglePhotoOptimized(e));case 1:case"end":return r.stop()}}),r)})))()},toggleAutoUpload:function(){this.autoUpload=!this.autoUpload,e.showToast({title:this.autoUpload?"已开启自动上传":"已关闭自动上传",icon:"none"})},uploadPhotosOptimized:function(){var e=this;return(0,i.default)(o.default.mark((function t(){var r,n,a;return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(0!==e.photos.length){t.next=2;break}return t.abrupt("return",[]);case 2:if(e.uploading=!0,e.uploadProgress=0,t.prev=4,r=e.photos.filter((function(e){return!e.uploaded})),0!==r.length){t.next=8;break}return t.abrupt("return",e.photos.filter((function(e){return e.uploaded})).map((function(e){return{url:e.cloudPath,type:"cleaning",description:""}})));case 8:return n=r.map((function(e){return e.url})),t.next=11,c.default.batchUpload(n,{onProgress:function(t){e.uploadProgress=t.progress},pathGenerator:function(){return e.generateCloudPath()},maxConcurrent:3});case 11:if(a=t.sent,!a.success){t.next=17;break}return a.results.forEach((function(t,n){var a=e.photos.findIndex((function(e){return e.url===r[n].url}));t.success&&-1!==a&&Object.assign(e.photos[a],{uploaded:!0,cloudUrl:t.url,cloudPath:t.cloudPath})})),t.abrupt("return",e.photos.filter((function(e){return e.uploaded})).map((function(e){return{url:e.cloudPath,type:"cleaning",description:""}})));case 17:throw new Error("照片上传失败");case 18:t.next=23;break;case 20:throw t.prev=20,t.t0=t["catch"](4),t.t0;case 23:return t.prev=23,e.uploading=!1,e.uploadProgress=0,t.finish(23);case 27:case"end":return t.stop()}}),t,null,[[4,20,23,27]])})))()},uploadPhotos:function(){var e=this;return(0,i.default)(o.default.mark((function t(){return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.abrupt("return",e.uploadPhotosOptimized());case 1:case"end":return t.stop()}}),t)})))()},submitCleaning:function(){var t=this;return(0,i.default)(o.default.mark((function r(){var n,a;return o.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(0!==t.photos.length){r.next=3;break}return e.showToast({title:"请至少上传一张照片",icon:"none"}),r.abrupt("return");case 3:if(!t.loading){r.next=5;break}return r.abrupt("return");case 5:return t.loading=!0,r.prev=6,r.next=9,t.preparePhotosForSubmit();case 9:if(n=r.sent,0!==n.length){r.next=12;break}throw new Error("没有可用的照片，请重新上传");case 12:if(a=t.prepareSubmissionData(n),e.showLoading({title:"提交清理记录..."}),"edit"!==t.mode){r.next=19;break}return r.next=17,l.default.updateCleaningRecord(t.recordId,a);case 17:r.next=21;break;case 19:return r.next=21,l.default.createCleaningRecord(a);case 21:t.handleSubmitSuccess(),r.next=27;break;case 24:r.prev=24,r.t0=r["catch"](6),t.handleSubmitError(r.t0);case 27:return r.prev=27,t.loading=!1,r.finish(27);case 30:case"end":return r.stop()}}),r,null,[[6,24,27,30]])})))()},preparePhotosForSubmit:function(){var t=this;return(0,i.default)(o.default.mark((function r(){var n;return o.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(n=t.photos.filter((function(e){return!e.uploaded&&!e.uploading})),!(n.length>0)){r.next=8;break}return e.showLoading({title:"正在上传剩余照片..."}),r.next=5,t.uploadPhotosOptimized();case 5:return r.abrupt("return",r.sent);case 8:return r.abrupt("return",t.photos.filter((function(e){return e.uploaded})).map((function(e){return{url:e.cloudPath,type:"cleaning",description:""}})));case 9:case"end":return r.stop()}}),r)})))()},prepareSubmissionData:function(e){var t=this.remarks.trim();t.length>200&&(t=t.substring(0,200),console.warn("备注被截断到200字符"));var r={photos:e,remark:t};return"create"===this.mode?p(p({},r),{},{area_id:this.areaInfo._id,cleaning_date:(new Date).toISOString(),completion_status:"completed"}):r},handleSubmitSuccess:function(){e.hideLoading(),e.showToast({title:"edit"===this.mode?"修改成功":"提交成功",icon:"success"}),e.$emit("cleaningRecordUpdated",{mode:this.mode,recordId:this.recordId,areaId:this.areaInfo._id}),setTimeout((function(){e.navigateBack()}),1500)},handleSubmitError:function(t){e.hideLoading();var r="提交失败，请重试";t.message&&(r=t.message.includes("未登录")?"请先登录":t.message.includes("权限")?"您没有权限操作该责任区":t.message),e.showModal({title:"提交失败",content:r,showCancel:!1,confirmText:"知道了"})},getAreaTypeText:function(e){return this.processCache.areaTypeMap||this.initProcessCache(),this.processCache.areaTypeMap&&this.processCache.areaTypeMap[e]||"责任区清理"},getPhotoDisplayUrl:function(e){var t=e.url||e,r=t;if(this.processCache.photoUrlCache||this.initProcessCache(),this.processCache.photoUrlCache.has(r))return this.processCache.photoUrlCache.get(r);var n=this.processPhotoUrl(t);return this.processCache.photoUrlCache.set(r,n),n}}};t.default=h}).call(this,r("df3c")["default"],r("861b")["uniCloud"])},bf14:function(e,t,r){}},[["236c","common/runtime","common/vendor","pages/6s_pkg/common/vendor"]]]);