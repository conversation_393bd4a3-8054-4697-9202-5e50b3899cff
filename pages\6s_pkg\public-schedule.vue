<template>
  <view class="page-container">
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-title">公共区清扫日设置</view>
      <view class="header-subtitle">设置公共责任区固定清扫日程安排</view>
    </view>

    <!-- 本周清扫概览 -->
    <view class="week-overview">
      <view class="overview-title">本周清扫安排</view>
      <scroll-view scroll-x class="week-scroll" show-scrollbar="false">
        <view class="week-grid">
          <view 
            class="day-item"
            v-for="(day, index) in weekDays" 
            :key="index"
            :class="{ 'today': day.isToday, 'has-schedule': day.areas.length > 0 }"
          >
            <view class="day-header">
              <text class="day-name">{{ day.name }}</text>
              <text class="day-date">{{ day.date }}</text>
            </view>
            <view class="day-content">
              <view class="area-count" v-if="day.areas.length > 0">
                {{ day.areas.length }}个区域
              </view>
              <view class="no-schedule" v-else>
                无安排
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 筛选栏 -->
    <view class="filter-bar">
      <picker 
        :range="filterOptions" 
        range-key="label" 
        :value="filterIndex"
        @change="onFilterChange"
        class="filter-picker"
      >
        <view class="filter-value">
          <text>{{ filterOptions[filterIndex].label }}</text>
          <uni-icons type="down" size="14" color="#8E8E93" />
        </view>
      </picker>
    </view>

    <!-- 公共区列表 -->
    <!-- 加载状态 -->
    <view v-if="loading" class="list-loading">
      <view class="loading-content">
        <uni-icons type="spinner-cycle" size="40" color="#007AFF"></uni-icons>
        <text class="loading-text">加载公共区数据中...</text>
      </view>
    </view>
    
    <!-- 正常列表 -->
    <view v-else-if="filteredAreas.length > 0" class="area-list">
      <view 
        class="area-item"
        v-for="(area, index) in filteredAreas" 
        :key="index"
      >
        <view class="area-main">
          <view class="area-header">
            <view class="area-name">{{ area.name }}</view>
            <view class="schedule-status" :class="[area.scheduled_day !== null ? 'scheduled' : 'unscheduled']">
          {{ area.scheduled_day !== null ? '已排班' : '未排班' }}
            </view>
          </view>
          <view class="area-info">
            <view class="info-item">
              <uni-icons type="location" size="14" color="#8E8E93" />
              <text>{{ (area.location && area.location.area) || area.location || '未设置位置' }}</text>
            </view>
            <view class="info-item" v-if="hasValidSchedule(area)">
              <uni-icons type="calendar" size="14" color="#8E8E93" />
              <text>{{ getWeekDayText(area.scheduled_day) }}清扫</text>
            </view>
            <view class="info-item" v-else>
              <uni-icons type="calendar" size="14" color="#8E8E93" />
              <text>未设置清扫日程</text>
            </view>
            <view class="info-item" v-if="hasValidSchedule(area)">
              <uni-icons type="time" size="14" color="#8E8E93" />
              <text :class="{ 'today': isToday(area.next_clean_date) }">{{ formatDate(area.next_clean_date) }}</text>
            </view>
          </view>
        </view>
        <view class="area-actions">
          <button class="action-btn set" @click="setSchedule(area)">
            <uni-icons type="calendar" size="14" color="#FFFFFF" />
            <text>{{ area.scheduled_day !== null ? '调整' : '设置' }}</text>
        </button>
        <button class="action-btn clear" @click="clearSchedule(area)" v-if="area.scheduled_day !== null">
            <uni-icons type="close" size="14" color="#FFFFFF" />
            <text>清除</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 筛选后无结果 -->
    <view v-else-if="!loading && areaList.length > 0 && filteredAreas.length === 0" class="filter-empty">
      <p-empty-state
        type="search"
        text="当前筛选条件下暂无数据"
        description="可以尝试切换其他筛选条件"
      ></p-empty-state>
    </view>

    <!-- 真正的空状态 -->
    <p-empty-state
      v-else-if="!loading && areaList.length === 0"
      type="schedule"
      :text="getEmptyText()"
      :description="getEmptyDesc()"
    ></p-empty-state>

    <!-- 排班设置弹窗 -->
    <uni-popup ref="schedulePopup" type="center" :mask-click="false">
      <view class="schedule-popup">
        <view class="popup-header">
          <view class="popup-title">设置清扫日程</view>
          <button class="close-btn" @click="closeSchedulePopup">
            <uni-icons type="close" size="20" color="#8E8E93" />
          </button>
        </view>
        
        <view class="popup-content" v-if="currentArea">
          <!-- 区域信息 -->
          <view class="area-info-card">
            <view class="area-info-header">
              <view class="area-info-name">{{ currentArea.name }}</view>
              <view class="area-status-badge">公共区域</view>
            </view>
            <view class="area-info-location">
              <uni-icons type="location" size="14" color="#8E8E93" />
              <text>{{ (currentArea.location && currentArea.location.area) || currentArea.location || '未设置位置' }}</text>
            </view>
          </view>
          
          <!-- 设置表单 -->
          <view class="setting-form">
            <view class="form-group">
              <view class="form-label">清扫日期</view>
              <picker 
                :range="weekDayOptions" 
                range-key="label" 
                :value="scheduleWeekIndex"
                @change="onScheduleWeekChange"
                class="form-picker"
              >
                <view class="form-input">
                  <text>{{ weekDayOptions[scheduleWeekIndex] ? weekDayOptions[scheduleWeekIndex].label : '请选择' }}</text>
                  <view class="dropdown-icon">
                    <uni-icons type="bottom" size="12" color="#8E8E93" />
                  </view>
                </view>
              </picker>
            </view>
            
            <view class="form-group">
              <view class="form-label">开始日期</view>
              <picker 
                mode="date" 
                :value="scheduleData.start_date" 
                @change="onStartDateChange"
                class="form-picker"
              >
                <view class="form-input">
                  <text>{{ scheduleData.start_date || '请选择开始日期' }}</text>
                  <view class="input-icon">
                    <uni-icons type="calendar" size="14" color="#007AFF" />
                  </view>
                </view>
              </picker>
            </view>
          </view>
          
          <!-- 设置预览 -->
          <view class="setting-preview">
            <view class="preview-header">
              <uni-icons type="eye" size="16" color="#007AFF" />
              <text>设置预览</text>
            </view>
            
            <!-- 完整设置预览 -->
            <view v-if="getPreviewState() === 'complete'" class="preview-summary complete">
              从 <text class="highlight-date">{{ formatFullDate(scheduleData.start_date) }}</text> 开始，每 <text class="highlight-day">{{ getWeekDayText(scheduleData.scheduled_day) }}</text> 清扫
            </view>
            
            <!-- 清除排班预览 -->
            <view v-else-if="getPreviewState() === 'clear'" class="preview-summary clear">
              <text class="clear-info">该区域将不设置固定清扫日程</text>
            </view>
            
            <!-- 部分设置预览 -->
            <view v-else class="preview-summary incomplete">
              <view v-if="scheduleData.start_date" class="preview-item">
                已选择开始日期：<text class="highlight-date">{{ formatFullDate(scheduleData.start_date) }}</text>
                <text class="missing-info">，请选择清扫日期</text>
              </view>
              <view v-else class="preview-item">
                <text class="missing-info">请选择清扫日期和开始日期</text>
              </view>
            </view>
          </view>
        </view>
        
        <view class="popup-footer">
          <button class="popup-btn cancel" @click="closeSchedulePopup">取消</button>
          <button class="popup-btn submit" @click="submitSchedule" :loading="saving">
            确定设置
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { callCloudFunction } from '@/utils/auth.js';

export default {
  name: 'PublicSchedule',
  // 公共区清扫日设置页面
  data() {
    return {
      areaList: [],
      loading: false,
      saving: false,
      currentArea: null,
      scheduleData: {
        scheduled_day: null,
        start_date: ''
      },

      filterOptions: [
        { value: 'all', label: '全部区域' },
        { value: 'scheduled', label: '已排班' },
        { value: 'unscheduled', label: '未排班' },
        { value: 'today', label: '今日清扫' }
      ],
      weekDayOptions: [
        { value: null, label: '请选择清扫日期' },
        { value: 1, label: '每周一' },
        { value: 2, label: '每周二' },
        { value: 3, label: '每周三' },
        { value: 4, label: '每周四' },
        { value: 5, label: '每周五' },
        { value: 6, label: '每周六' },
        { value: 0, label: '每周日' }
      ],

      filterIndex: 0,
      scheduleWeekIndex: 0
    }
  },
  
  computed: {
    filteredAreas() {
      const filter = this.filterOptions[this.filterIndex].value;
      switch (filter) {
        case 'scheduled':
                  return this.areaList.filter(area => area.scheduled_day !== null);
      case 'unscheduled':
        return this.areaList.filter(area => area.scheduled_day === null);
      case 'today':
        const today = new Date().getDay();
        return this.areaList.filter(area => area.scheduled_day === today);
        default:
          return this.areaList;
      }
    },

    weekDays() {
      const today = new Date();
      const days = [];
      
      // 获取本周的周一
      const startOfWeek = new Date(today);
      const dayOfWeek = today.getDay();
      const diffToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
      startOfWeek.setDate(today.getDate() + diffToMonday);      
      const weekNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];      
      for (let i = 0; i < 7; i++) {
        const currentDay = new Date(startOfWeek);
        currentDay.setDate(startOfWeek.getDate() + i);
        
        const dayOfWeek = currentDay.getDay();
        const adjustedDay = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // 调整为周一=0，周日=6
        
        days.push({
          name: weekNames[dayOfWeek],
          date: currentDay.getDate(),
          isToday: currentDay.toDateString() === today.toDateString(),
          areas: this.areaList.filter(area => area.scheduled_day === dayOfWeek)
        });
      }
      
      return days;
    }
  },
  
  onLoad() {
    this.loadData();
  },
  
  methods: {
    // 获取预览状态
    getPreviewState() {
      const hasValidDay = this.scheduleData.scheduled_day !== null && 
                         this.scheduleData.scheduled_day !== undefined && 
                         typeof this.scheduleData.scheduled_day === 'number';
      const hasStartDate = this.scheduleData.start_date && this.scheduleData.start_date.trim();
      
      if (hasValidDay && hasStartDate) {
        return 'complete';
      } else {
        return 'incomplete';
      }
    },
    
    // 判断区域是否有有效的排班设置
    hasValidSchedule(area) {
      return area.scheduled_day !== null && 
             area.scheduled_day !== undefined && 
             typeof area.scheduled_day === 'number' &&
             area.scheduled_day >= 0 && 
             area.scheduled_day <= 6;
    },

    // 加载数据
    async loadData() {
      try {
        this.loading = true;
        
        // 使用云函数获取公共责任区数据
        const result = await callCloudFunction('hygiene-area-management', {
          action: 'getAreaList',
          data: {
            type: 'public' // 只获取公共责任区
          }
        });

        this.areaList = (result.data.list || [])
          .filter(area => area.status === 'active')
          .map(area => ({
            ...area,
            next_clean_date: this.calculateNextCleanDate(area)
          }));
        
      } catch (error) {
        console.error('加载数据失败：', error);
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    
    // 计算下次清扫日期
    calculateNextCleanDate(area) {
      if (area.scheduled_day === null || area.scheduled_day === undefined) {
        return null;
      }
      
      const today = new Date();
      const targetWeekDay = area.scheduled_day;
      
      // 如果有开始日期，则从开始日期计算，否则从今天开始
      let startDate = area.start_date ? new Date(area.start_date) : new Date(today);
      
      // 如果开始日期是未来的日期，则从开始日期开始找
      if (area.start_date && startDate > today) {
        // 从开始日期找第一个目标星期几
        while (startDate.getDay() !== targetWeekDay) {
          startDate.setDate(startDate.getDate() + 1);
        }
        return startDate.toISOString().split('T')[0];
      }
      
      // 从今天开始找下一个目标星期几
      let nextDate = new Date(today);
      while (nextDate.getDay() !== targetWeekDay) {
        nextDate.setDate(nextDate.getDate() + 1);
      }
      
      // 如果今天就是目标星期几，但已经是下午了，则找下周的同一天
      if (nextDate.getDay() === targetWeekDay && nextDate.toDateString() === today.toDateString()) {
        if (today.getHours() >= 12) { // 假设12点后不进行清扫
          nextDate.setDate(nextDate.getDate() + 7);
        }
      }      
      return nextDate.toISOString().split('T')[0];
    },
    
    // 获取下周一的日期
    getNextMonday() {
      const today = new Date();
      const nextMonday = new Date(today);
      const daysUntilMonday = (8 - today.getDay()) % 7;
      nextMonday.setDate(today.getDate() + (daysUntilMonday === 0 ? 7 : daysUntilMonday));
      return nextMonday.toISOString().split('T')[0];
    },
    
    // 筛选变更
    onFilterChange(e) {
      this.filterIndex = e.detail.value;
    },
    
    // 设置排班
    setSchedule(area) {
      this.currentArea = area;
      
      this.scheduleData = {
        scheduled_day: area.scheduled_day,
        start_date: area.start_date || this.getNextMonday()
      };
      
      // 设置星期选择器的索引
      const foundIndex = this.weekDayOptions.findIndex(option => option.value === area.scheduled_day);
      this.scheduleWeekIndex = foundIndex >= 0 ? foundIndex : 0; // 如果找不到匹配项，默认选择"请选择清扫日期"
      this.$refs.schedulePopup.open();
    },
    
    // 清除排班
    clearSchedule(area) {
      uni.showModal({
        title: '确认清除',
        content: `确定要清除"${area.name}"的排班设置吗？`,
        confirmText: '确定',
        confirmColor: '#FF3B30',
        success: (res) => {
          if (res.confirm) {
            this.performClearSchedule(area);
          }
        }
      });
    },
    
    // 执行清除排班
    async performClearSchedule(area) {
      try {
        // 使用云函数清除排班
        await callCloudFunction('hygiene-area-management', {
          action: 'updateArea',
          data: {
            id: area._id || area.id,
            scheduled_day: null,
            start_date: null
          }
        });

        // 更新本地数据
        const index = this.areaList.findIndex(item => (item._id || item.id) === (area._id || area.id));
        if (index > -1) {
          const updatedArea = {
            ...this.areaList[index],
            scheduled_day: null,
            start_date: null,
            next_clean_date: null,
            updated_at: new Date().toISOString()
          };
          
          // 使用 Vue.set 来确保响应式更新
          this.$set(this.areaList, index, updatedArea);
        }
        
        uni.showToast({
          title: '排班已清除',
          icon: 'success'
        });
        
      } catch (error) {
        console.error('清除排班失败：', error);
        uni.showToast({
          title: error.message || '操作失败',
          icon: 'none'
        });
      }
    },
    
    // 排班星期变更
    onScheduleWeekChange(e) {
      this.scheduleWeekIndex = parseInt(e.detail.value);
      const selectedOption = this.weekDayOptions[this.scheduleWeekIndex];
      if (selectedOption) {
        this.scheduleData.scheduled_day = selectedOption.value;
      } else {
        // 如果选择的索引无效，默认选择第一个选项（请选择清扫日期）
        this.scheduleWeekIndex = 0;
        this.scheduleData.scheduled_day = null;
      }
    },
    
    // 开始日期变更
    onStartDateChange(e) {
      this.scheduleData.start_date = e.detail.value;
    },
    
    // 提交排班
    async submitSchedule() {
      // 验证清扫日期
      if (this.scheduleData.scheduled_day === null || this.scheduleData.scheduled_day === undefined) {
        uni.showToast({
          title: '请选择清扫日期',
          icon: 'none'
        });
        return;
      }
      
      // 验证开始日期
      if (!this.scheduleData.start_date) {
        uni.showToast({
          title: '请选择开始日期',
          icon: 'none'
        });
        return;
      }
      
      try {
        this.saving = true;
        
        // 使用云函数设置排班
        await callCloudFunction('hygiene-area-management', {
          action: 'updateArea',
          data: {
            id: this.currentArea._id || this.currentArea.id,
            scheduled_day: this.scheduleData.scheduled_day,
            start_date: this.scheduleData.start_date
          }
        });

        // 更新本地数据
        const index = this.areaList.findIndex(item => (item._id || item.id) === (this.currentArea._id || this.currentArea.id));
        if (index > -1) {
          const updatedArea = {
            ...this.areaList[index],
            scheduled_day: this.scheduleData.scheduled_day,
            start_date: this.scheduleData.start_date,
            next_clean_date: this.calculateNextCleanDate({
              ...this.areaList[index],
              scheduled_day: this.scheduleData.scheduled_day,
              start_date: this.scheduleData.start_date
            }),
            updated_at: new Date().toISOString()
          };
          
          // 使用 Vue.set 或者替换整个数组来确保响应式更新
          this.$set(this.areaList, index, updatedArea);
        }
        
        // 显示成功消息
        const successMessage = '排班设置成功';
        
        uni.showToast({
          title: successMessage,
          icon: 'success'
        });
        
        this.closeSchedulePopup();
        
      } catch (error) {
        console.error('排班设置失败：', error);
        uni.showToast({
          title: error.message || '设置失败',
          icon: 'none'
        });
      } finally {
        this.saving = false;
      }
    },
    
    // 关闭排班弹窗
    closeSchedulePopup() {
      this.$refs.schedulePopup.close();
      
      // 使用更长的延迟来确保弹窗关闭动画完全结束后再重置状态
      setTimeout(() => {
        this.resetPopupState();
      }, 350); // 增加延迟时间以避免动画闪烁
    },

    // 重置弹窗状态
    resetPopupState() {
      this.currentArea = null;
      this.scheduleData = {
        scheduled_day: null,
        start_date: ''
      };
      this.scheduleWeekIndex = 0; // 默认选择第一个选项（请选择清扫日期）
    },
    
    // 获取星期文本
    getWeekDayText(weekDay) {
      if (weekDay === null || weekDay === undefined || typeof weekDay !== 'number') {
        return '未知';
      }
      
      const weekDayMap = {
        0: '周日',
        1: '周一',
        2: '周二',
        3: '周三',
        4: '周四',
        5: '周五',
        6: '周六'
      };
      return weekDayMap[weekDay] || '未知';
    },
    
    // 判断是否是今天
    isToday(dateString) {
      if (!dateString) return false;
      const today = new Date().toISOString().split('T')[0];
      const date = typeof dateString === 'string' ? dateString.split('T')[0] : dateString.toISOString().split('T')[0];
      return today === date;
    },
    
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';
      
      try {
        const date = new Date(dateString);
        
        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          return '';
        }
        
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(today.getDate() + 1);      
        const dateStr = date.toISOString().split('T')[0];
        const todayStr = today.toISOString().split('T')[0];
        const tomorrowStr = tomorrow.toISOString().split('T')[0];
        
        if (dateStr === todayStr) {
          return '今天';
        } else if (dateStr === tomorrowStr) {
          return '明天';
        } else {
          return `${date.getMonth() + 1}-${String(date.getDate()).padStart(2, '0')}`;
        }
      } catch (error) {
        console.error('日期格式化错误：', error);
        return '';
      }
    },
    
    // 格式化完整日期（用于设置说明）
    formatFullDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
    },
    
    // 获取空状态文本
    getEmptyText() {
      const filter = this.filterOptions[this.filterIndex].value;
      switch (filter) {
        case 'scheduled':
          return '暂无已排班区域';
        case 'unscheduled':
          return '暂无未排班区域';
        case 'today':
          return '今日无清扫安排';
        default:
          return '暂无公共责任区';
      }
    },
    
    // 获取空状态描述
    getEmptyDesc() {
      const filter = this.filterOptions[this.filterIndex].value;
      switch (filter) {
        case 'scheduled':
          return '可以切换到"未排班"查看需要设置的区域';
        case 'unscheduled':
          return '所有公共责任区都已完成排班设置';
        case 'today':
          return '可以查看其他日期的清扫安排';
        default:
          return '请先在公共责任区管理中创建区域';
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: #F8F9FA;
  padding-bottom: 40rpx;
}

.header {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  padding: 60rpx 32rpx 40rpx;
  color: white;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 24rpx;
  opacity: 0.8;
}

.week-overview {
  background: white;
  margin: 24rpx 32rpx;
  padding: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.overview-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 24rpx;
}

.week-scroll {
  width: 100%;
}

/* 隐藏水平滚动条 */
.week-scroll::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  background: transparent;
}

.week-scroll {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.week-grid {
  display: flex;
  gap: 12rpx;
  padding: 0 4rpx;
}

.day-item {
  flex-shrink: 0;
  width: 120rpx;
  text-align: center;
  padding: 16rpx 12rpx;
  border-radius: 12rpx;
  background: #F8F9FA;
  transition: all 0.3s ease;
}

.day-item.today {
  background: rgba(0, 122, 255, 0.1);
  border: 2rpx solid #007AFF;
}

.day-item.has-schedule {
  background: rgba(52, 199, 89, 0.1);
}

.day-item.today.has-schedule {
  background: rgba(0, 122, 255, 0.15);
}

.day-header {
  margin-bottom: 8rpx;
}

.day-name {
  font-size: 22rpx;
  color: #8E8E93;
  display: block;
}

.day-date {
  font-size: 26rpx;
  font-weight: 600;
  color: #1C1C1E;
  display: block;
  margin-top: 4rpx;
}

.day-content {
  min-height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.area-count {
  font-size: 20rpx;
  color: #34C759;
  font-weight: 500;
}

.no-schedule {
  font-size: 20rpx;
  color: #C7C7CC;
}

.filter-bar {
  padding: 0 32rpx 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16rpx;
}

.filter-picker {
  flex: 1;
  min-width: 0;
}

.filter-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 56rpx;
  background: white;
  border-radius: 12rpx;
  padding: 0 16rpx;
  font-size: 24rpx;
  color: #1C1C1E;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.area-list {
  padding: 0 32rpx;
}

.area-item {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.area-main {
  flex: 1;
}

.area-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.area-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #1C1C1E;
}

.schedule-status {
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.schedule-status.scheduled {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.schedule-status.unscheduled {
  background: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}

.area-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 12rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  color: #8E8E93;
  flex-shrink: 0;
  min-width: fit-content;
}

.info-item .today {
  color: #007AFF;
  font-weight: 600;
}

.area-actions {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.action-btn {
  height: 56rpx;
  padding: 0 16rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  font-size: 22rpx;
  border: none;
  transition: all 0.3s ease;
  min-width: 100rpx;
  white-space: nowrap;
}

.action-btn.set {
  background: #007AFF;
  color: white;
}

.action-btn.clear {
  background: #8E8E93;
  color: white;
}

.action-btn:active {
  transform: scale(0.95);
}

.schedule-popup, .quick-set-popup {
  width: 92vw;
  max-width: 650rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-left: 5rpx;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F2F2F7;
  border: none;
  margin-right: 5rpx;
}

.popup-content {
  flex: 1;
  overflow-y: auto;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* #ifdef MP-WEIXIN */
/* 微信小程序隐藏滚动条 */
.popup-content::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  background: transparent;
}

.popup-content {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
/* #endif */

/* #ifndef MP-WEIXIN */
/* 非微信小程序环境下也隐藏滚动条 */
.popup-content::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  background: transparent;
}

.popup-content {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
/* #endif */

/* 区域信息卡片 */
.area-info-card {
  background: linear-gradient(180deg, #FFFFFF 0%, #FAFBFC 100%);
  border: 1rpx solid #E5E5EA;
  border-radius: 16rpx;
  padding: 26rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.area-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.area-info-name {
  font-size: 34rpx;
  font-weight: 600;
  color: #1C1C1E;
  letter-spacing: 0.5rpx;
}

.area-status-badge {
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.area-info-location {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #8E8E93;
}

/* 设置表单 */
.setting-form {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-size: 28rpx;
  color: #1C1C1E;
  margin-bottom: 12rpx;
  font-weight: 600;
  position: relative;
  padding-left: 16rpx;
}

.form-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4rpx;
  height: 18rpx;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  border-radius: 2rpx;
}

.form-picker {
  width: 100%;
}

.form-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 88rpx;
  background: #FFFFFF;
  border: 2rpx solid #E5E5EA;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  box-sizing: border-box;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  position: relative;
}

.form-input:active {
  border-color: #007AFF;
  background: #F8F9FA;
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.15);
  transform: translateY(-2rpx);
}

.dropdown-icon, .input-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  border-radius: 6rpx;
  background: rgba(0, 122, 255, 0.08);
  transition: all 0.3s ease;
}



/* 设置预览 */
.setting-preview {
  background: rgba(0, 122, 255, 0.04);
  border: 2rpx solid rgba(0, 122, 255, 0.12);
  border-radius: 16rpx;
  padding: 24rpx;
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.preview-header text {
  font-size: 26rpx;
  font-weight: 600;
  color: #007AFF;
}

.preview-summary {
  font-size: 26rpx;
  color: #1C1C1E;
  line-height: 1.6;
}

.preview-summary .highlight-date {
  color: #007AFF;
  font-weight: 600;
  background: rgba(0, 122, 255, 0.08);
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
}

.preview-summary .highlight-day {
  color: #34C759;
  font-weight: 600;
  background: rgba(52, 199, 89, 0.08);
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
}

.preview-summary.complete {
  color: #1C1C1E;
}

.preview-summary.incomplete {
  color: #8E8E93;
}

.preview-item {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4rpx;
  line-height: 1.6;
}

.missing-info {
  color: #FF9500;
  font-weight: 500;
}

.preview-summary.clear {
  color: #FF3B30;
  font-weight: 600;
  background: rgba(255, 59, 48, 0.08);
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
}

.clear-info {
  color: #FF3B30;
  font-weight: 600;
}


.popup-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #F2F2F7;
}

.popup-btn {
  flex: 1;
  height: 76rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  border: none;
  transition: all 0.3s ease;
}

.popup-btn.cancel {
  background: #F2F2F7;
  color: #8E8E93;
}

.popup-btn.submit {
  background: #007AFF;
  color: white;
}

.popup-btn:active {
  transform: scale(0.95);
}

.list-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  background: #F8F9FA;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
}
</style> 