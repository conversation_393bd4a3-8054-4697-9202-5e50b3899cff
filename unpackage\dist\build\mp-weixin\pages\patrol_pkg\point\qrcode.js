require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/patrol_pkg/point/qrcode"],{1134:function(e,t,n){"use strict";n.r(t);var r=n("a62b"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=a.a},"9fd7":function(e,t,n){},a62b:function(e,t,n){"use strict";(function(e){var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("7eb4")),o=r(n("7ca3")),i=r(n("ee10")),c=r(n("95a7")),s=r(n("b96b"));function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){(0,o.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var l={data:function(){return{pointInfo:{},pointId:"",errorMsg:"",loading:!0,loadingText:{loading:"加载中...",more:"加载更多...",noMore:"没有更多数据了"},qrcodeGenerated:!1,qrcodeEnabled:!1,currentTime:"",generating:!1,regenerating:!1,qrGeneratedTime:null,saving:!1,hasEditPermission:!0,qrcodeContent:"",isInitialLoad:!0,needsNewQRCode:!1}},onLoad:function(e){if(this.pointId=e.id,!this.pointId)return this.errorMsg="缺少点位ID",void(this.loading=!1);this.loadPointDetail()},methods:{loadPointDetail:function(){var e=this;return(0,i.default)(a.default.mark((function t(){var n;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.pointId){t.next=4;break}return e.errorMsg="缺少点位ID",e.loading=!1,t.abrupt("return");case 4:return e.loading=!0,e.errorMsg="",t.prev=6,t.next=9,s.default.getPointDetail(e.pointId);case 9:if(n=t.sent,0!==n.code||!n.data){t.next=25;break}if(e.pointInfo=n.data,e.qrcodeEnabled=!!e.pointInfo.qrcode_enabled,!e.qrcodeEnabled||!e.pointInfo.qrcode_content){t.next=19;break}e.qrcodeContent=e.pointInfo.qrcode_content,e.qrcodeGenerated=!0,e.qrGeneratedTime=e.pointInfo.qrcode_generated_time,t.next=23;break;case 19:if(!e.qrcodeEnabled){t.next=23;break}return e.needsNewQRCode=!0,t.next=23,e.generateQrCode();case 23:t.next=26;break;case 25:e.errorMsg=n.message||"找不到点位信息";case 26:t.next=31;break;case 28:t.prev=28,t.t0=t["catch"](6),e.errorMsg="加载点位详情失败: "+(t.t0.message||"未知错误");case 31:return t.prev=31,e.loading=!1,t.finish(31);case 34:case"end":return t.stop()}}),t,null,[[6,28,31,34]])})))()},generateQrCode:function(){var t=this;return(0,i.default)(a.default.mark((function n(){var r,o;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(n.prev=0,t.pointInfo&&t.pointInfo._id){n.next=4;break}return t.showError("无法生成二维码：点位信息不完整"),n.abrupt("return");case 4:return t.generating=!0,t.errorMsg="",r=c.default.getQRCodeData(u(u({},t.pointInfo),{},{qrcode_content:null}),{includeTimestamp:!1}),n.prev=7,n.next=10,s.default.callPointFunction("updatePoint",{data:{id:t.pointInfo._id,qrcode_content:r,qrcode_generated_time:(new Date).toISOString()}});case 10:if(o=n.sent,0!==o.code){n.next=18;break}t.qrcodeContent=r,t.qrcodeGenerated=!0,t.qrGeneratedTime=new Date,e.showToast({title:"二维码已生成",icon:"success"}),n.next=19;break;case 18:throw new Error(o.message||"保存失败");case 19:n.next=24;break;case 21:n.prev=21,n.t0=n["catch"](7),t.showError("保存二维码失败："+(n.t0.message||"未知错误"));case 24:n.next=29;break;case 26:n.prev=26,n.t1=n["catch"](0),t.showError("生成二维码时发生错误："+(n.t1.message||"未知错误"));case 29:return n.prev=29,t.generating=!1,n.finish(29);case 32:case"end":return n.stop()}}),n,null,[[0,26,29,32],[7,21]])})))()},onQRCodeComplete:function(t){var n=this;return(0,i.default)(a.default.mark((function r(){var o;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!t.success){r.next=21;break}if(!n.generating||n.isInitialLoad&&!n.needsNewQRCode){r.next=19;break}return r.prev=2,r.next=5,s.default.callPointFunction("updatePoint",{data:{id:n.pointInfo._id,qrcode_content:n.qrcodeContent,qrcode_generated_time:(new Date).toISOString(),qrcode_enabled:!0}});case 5:if(o=r.sent,0!==o.code){r.next=13;break}n.qrcodeGenerated=!0,n.qrGeneratedTime=new Date,n.needsNewQRCode=!1,e.hideLoading(),r.next=14;break;case 13:throw new Error(o.message||"保存二维码失败");case 14:r.next=19;break;case 16:r.prev=16,r.t0=r["catch"](2),n.showError("保存二维码失败："+(r.t0.message||"未知错误"));case 19:r.next=22;break;case 21:n.showError("生成二维码失败："+(t.message||"未知错误"));case 22:n.generating=!1;case 23:case"end":return r.stop()}}),r,null,[[2,16]])})))()},regenerateQrCode:function(){var t=this;return(0,i.default)(a.default.mark((function n(){var r;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(n.prev=0,t.pointInfo&&t.pointInfo._id){n.next=4;break}return e.showToast({title:"点位信息不完整",icon:"none"}),n.abrupt("return");case 4:if(!t.regenerating){n.next=6;break}return n.abrupt("return");case 6:return t.regenerating=!0,t.isInitialLoad=!1,n.prev=8,e.showLoading({title:"更新中..."}),n.next=12,c.default.incrementQRCodeVersion(t.pointInfo._id);case 12:if(r=n.sent,!r||!r.updated){n.next=20;break}return t.pointInfo=r.result,n.next=17,t.generateQrCode();case 17:e.showToast({title:"二维码已更新",icon:"success"}),n.next=21;break;case 20:throw new Error("更新失败，请重试");case 21:n.next=26;break;case 23:n.prev=23,n.t0=n["catch"](8),e.showToast({title:n.t0.message||"更新失败，请重试",icon:"none",duration:2e3});case 26:return n.prev=26,e.hideLoading(),t.regenerating=!1,n.finish(26);case 30:n.next=36;break;case 32:n.prev=32,n.t1=n["catch"](0),e.showToast({title:"重新生成失败",icon:"none"}),t.regenerating=!1;case 36:case"end":return n.stop()}}),n,null,[[0,32],[8,23,26,30]])})))()},saveQrCode:function(){var t=this;return(0,i.default)(a.default.mark((function n(){return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t.qrcodeGenerated){n.next=3;break}return e.showToast({title:"请先生成二维码",icon:"none"}),n.abrupt("return");case 3:return t.saving=!0,e.showLoading({title:"保存中..."}),n.prev=5,n.next=8,t.$refs.uqrcode.save({success:function(){e.showToast({title:"已保存到相册",icon:"success"})},fail:function(t){var n="保存失败";t.errMsg&&t.errMsg.includes("auth deny")&&(n="没有保存到相册的权限"),e.showToast({title:n,icon:"none"})}});case 8:return n.prev=8,e.hideLoading(),t.saving=!1,n.finish(8);case 12:case"end":return n.stop()}}),n,null,[[5,,8,12]])})))()},goBack:function(){e.navigateBack()},goToEdit:function(){e.navigateTo({url:"/pages/patrol_pkg/point/edit?id=".concat(this.pointId)})},formatDate:function(e){if(!e)return"";try{"string"===typeof e&&(e=new Date(e));var t=e.getFullYear(),n=String(e.getMonth()+1).padStart(2,"0"),r=String(e.getDate()).padStart(2,"0"),a=String(e.getHours()).padStart(2,"0"),o=String(e.getMinutes()).padStart(2,"0"),i=String(e.getSeconds()).padStart(2,"0");return"".concat(t,"-").concat(n,"-").concat(r," ").concat(a,":").concat(o,":").concat(i)}catch(c){return"日期格式错误"}},showError:function(e){this.errorMsg=e,this.loading=!1}}};t.default=l}).call(this,n("df3c")["default"])},bdcb:function(e,t,n){"use strict";n.r(t);var r=n("fa5f"),a=n("1134");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n("de6a");var i=n("828b"),c=Object(i["a"])(a["default"],r["b"],r["c"],!1,null,null,null,!1,r["a"],void 0);t["default"]=c.exports},de6a:function(e,t,n){"use strict";var r=n("9fd7"),a=n.n(r);a.a},e44c:function(e,t,n){"use strict";(function(e,t){var r=n("47a9");n("357b"),n("861b");r(n("3240"));var a=r(n("bdcb"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},fa5f:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return r}));var r={uniLoadMore:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-load-more/components/uni-load-more/uni-load-more")]).then(n.bind(null,"3282"))},uqrcode:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode")]).then(n.bind(null,"03ba"))}},a=function(){var e=this.$createElement,t=(this._self._c,!this.loading&&!this.errorMsg&&this.qrcodeEnabled&&this.qrcodeGenerated?this.formatDate(this.qrGeneratedTime):null);this.$mp.data=Object.assign({},{$root:{m0:t}})},o=[]}},[["e44c","common/runtime","common/vendor","pages/patrol_pkg/common/vendor"]]]);