{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-review.vue?c539", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-review.vue?98ec", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-review.vue?31a0", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-review.vue?a386", "uni-app:///pages/6s_pkg/rectification-review.vue", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-review.vue?34c7", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-review.vue?fc80"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "taskId", "loading", "dataLoaded", "loadError", "isSubmitting", "taskInfo", "id", "areaName", "areaType", "status", "issueDescription", "issueFoundDate", "inspector", "issuePhotos", "rectificationDescription", "rectificationPhotos", "rectificationSubmitTime", "employeeName", "reviewForm", "result", "comment", "photos", "rating", "autoUpload", "sliderTouching", "computed", "<PERSON><PERSON><PERSON><PERSON>", "canSubmit", "commentPlaceholder", "onLoad", "uni", "title", "content", "showCancel", "cancelText", "confirmText", "success", "methods", "handleCommentInput", "value", "icon", "duration", "loadTaskInfo", "action", "task", "assignee_id", "console", "retryLoad", "processPhotos", "selectReviewResult", "setRating", "getRatingDesc", "getRatingDescription", "onStarRateChange", "setRatingByMark", "onSliderTouchStart", "onSliderTouchMove", "onSliderTouchEnd", "onSliderMouseDown", "onSliderMouseMove", "onSliderMouseUp", "updateRatingFromTouch", "updateRatingFromMouse", "getStatusText", "formatDateTime", "date", "previewPhoto", "urls", "current", "previewIssuePhoto", "previewReviewPhoto", "getPhotoDisplayUrl", "addReviewPhoto", "count", "sizeType", "sourceType", "fail", "res", "newPhotos", "url", "uploaded", "cloudUrl", "cloudPath", "uploading", "belongsToRecord", "deleteReviewPhoto", "index", "photo", "uniCloud", "fileList", "extractFileId", "autoUploadNewPhotos", "i", "photoIndex", "uploadResult", "uploadReviewPhotos", "uploadedPhotos", "description", "timestamp", "uploadSinglePhoto", "random", "uploadUtils", "fileInfo", "size", "error", "submitReview", "resultText", "doSubmit", "reviewData", "review_result", "review_comments", "review_photos", "review_date", "final_rating", "areaId", "mode", "setTimeout", "delta", "errorMessage", "onPhotoError", "retryPhotoLoad", "toggleAutoUpload"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,4BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6I;AAC7I;AACwE;AACL;AACsC;;;AAGzG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,0FAAM;AACR,EAAE,2GAAM;AACR,EAAE,oHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjGA;AAAA;AAAA;AAAA;AAA8mB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACyVloB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QAAA;QACAC;QACAC;QACAC;MACA;;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;;MAEA;MACA;QAAA,OACA;MAAA,EACA;MACA;MAEA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UACAN;QACA;MACA;IACA;EACA;EACAO;IACA;IACAC;MACA;;MAEA;MACA;QACAC;QACA;QACAT;UACAC;UACAS;UACAC;QACA;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;gBACAZ;kBACAC;kBACAC;kBACAC;kBACAE;kBACAC;oBACAN;kBACA;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGA;kBACAa;kBACA5C;oBAAAO;kBAAA;gBACA;cAAA;gBAHAa;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACAyB;gBAEA;kBACAtC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACA4B;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAGA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBACA;gBAEAhB;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;oBACA;sBACA;oBACA;sBACAN;oBACA;kBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiB;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;UACA;QACA;UACA;QACA;QACA;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;UACA;QACA;QACA;QACA;UACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEAC;MACA;QACA;MACA;IACA;IAEAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA/B;QACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAgC;MAAA;MACA;MACAhC;QACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAiC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;QACA;UACA;YACA;YACAC;UACA;YACA;YACAA;UACA;QACA;UACAA;QACA;QAEA;UACA;QACA;QAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QAAA,OACA;MAAA,EACA;MACApC;QACAqC;QACAC;MACA;IACA;IAEA;IACAC;MACA;QAAA,OACA;MAAA,EACA;MACAvC;QACAqC;QACAC;MACA;IACA;IAEA;IACAE;MAAA;MACA;QAAA;MAAA;MACAxC;QACAqC;QACAC;MACA;IACA;IAEA;IACAG;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACA1C;oBACA2C;oBACAC;oBACAC;oBACAvC;oBACAwC;kBACA;gBACA;cAAA;gBARAC;gBAUA;gBACAC;kBAAA;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;kBACA;gBAAA,IAIA;;gBACA;;gBAEA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAtC;gBACAhB;kBACAC;kBACAS;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA6C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIAC,yCAEA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAEAzD;kBAAAC;gBAAA;;gBAEA;gBAAA;gBAAA,OACAyD;kBACA1F;kBACAC;oBACA0F;kBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIA3D;kBACAC;kBACAS;gBACA;cAAA;gBAAA;gBAEAV;gBAAA;cAAA;gBAIA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA4D;MACA;QACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,8DAEAC;kBAAA;kBAAA;oBAAA;sBAAA;wBAAA;0BACAL;0BACAM;4BAAA;0BAAA;0BAAA,MAEAA;4BAAA;4BAAA;0BAAA;0BAAA;wBAAA;0BAAA;0BAGA;0BACA;;0BAEA;0BAAA;0BAAA,OACA;wBAAA;0BAAAC;0BAAA,KAEAA;4BAAA;4BAAA;0BAAA;0BACA;0BACA;0BACA;0BACA;0BACA;0BAAA;0BAAA;wBAAA;0BAAA,MAIA;wBAAA;0BAAA;0BAAA;wBAAA;0BAAA;0BAAA;0BAGA;;0BAEA;0BACAhE;4BACAC;4BACAS;4BACAC;0BACA;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA;gBAhCAmD;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAmCA;IAEA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAEAJ;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACAL;gBAAA,KAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAS;kBACAjB;kBACAkB;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAKA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAJ;gBAAA,KAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAEAE;kBACAjB;kBACAkB;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAIA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;gBAEApE;kBACAC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBA5CA2D;gBAAA;gBAAA;cAAA;gBAAA,kCAkDAI;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAD;gBACAE;gBACAlB,qHAEA;gBAAA;gBAAA,OACAmB;cAAA;gBAAAP;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEAO;cAAA;gBAAAC;gBAAA,kCAEA;kBACAlE;kBACA8C;kBACAH;kBACAwB;gBACA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,kCAGA;kBAAAnE;kBAAAoE;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEAC;gBAEA5E;kBACAC;kBACAC;kBACAI;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAyC;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA,OACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAEA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA8B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAGA;gBACAX;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAA;cAAA;gBAGAY;kBACAtG;kBAAA;kBACAuG;kBAAA;kBACAC;kBACAC;kBAAA;kBACAC;kBACAC;gBACA,GAIA;gBAAA;gBAAA,OACA;kBACAtE;kBACA5C;gBACA;cAAA;gBAHAoB;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACAuF;gBAEA5E;kBACAC;kBACAS;gBACA;;gBAEA;gBACAV;kBACA9B;kBACAmB;kBACA+F;kBACA3G;gBACA;;gBAEA;gBACAuB;kBACA9B;kBACAkH;kBACAC;kBACAhG;gBACA;;gBAEA;gBACAiG;kBACAtF;oBACAuF;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAKAC;gBACA;kBACA;oBACAA;kBACA;oBACAA;kBACA;oBACAA;kBACA;gBACA;gBAEAxF;kBACAC;kBACAC;kBACAC;kBACAE;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAoF;MAAA;MACA;MACA;QACAlG;MACA;QACAA;MACA;QACAA;MACA;MAEA;MAEA;QACA;MACA;QACA,sFACA;UAAA0D;UAAA5E;QAAA,IACA;UAAA4E;UAAAC;UAAAC;UAAAC;UAAAC;UAAAC;UAAAjF;QAAA;QACA;MACA;IACA;IAEA;IACAqH;MAAA;MACA;MACA;QACAnG;MACA;QACAA;MACA;QACAA;MACA;MAEA;MAEA;QACA;MACA;IACA;IAIA;IACAoG;MACA;MACA3F;QACAC;QACAS;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjlCA;AAAA;AAAA;AAAA;AAAirC,CAAgB,upCAAG,EAAC,C;;;;;;;;;;;ACArsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/rectification-review.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/rectification-review.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./rectification-review.vue?vue&type=template&id=665ecd07&scoped=true&\"\nvar renderjs\nimport script from \"./rectification-review.vue?vue&type=script&lang=js&\"\nexport * from \"./rectification-review.vue?vue&type=script&lang=js&\"\nimport style0 from \"./rectification-review.vue?vue&type=style&index=0&id=665ecd07&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"665ecd07\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/rectification-review.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rectification-review.vue?vue&type=template&id=665ecd07&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n    uniRate: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-rate/components/uni-rate/uni-rate\" */ \"@/uni_modules/uni-rate/components/uni-rate/uni-rate.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = !_vm.loading ? _vm.getStatusText(_vm.taskInfo.status) : null\n  var m1 =\n    !_vm.loading && !_vm.loadError && _vm.dataLoaded\n      ? _vm.formatDateTime(_vm.taskInfo.issueFoundDate)\n      : null\n  var g0 =\n    !_vm.loading && !_vm.loadError && _vm.dataLoaded\n      ? _vm.taskInfo.issuePhotos && _vm.taskInfo.issuePhotos.length > 0\n      : null\n  var m2 =\n    !_vm.loading && !_vm.loadError && _vm.dataLoaded\n      ? _vm.formatDateTime(_vm.taskInfo.rectificationSubmitTime)\n      : null\n  var g1 =\n    !_vm.loading && !_vm.loadError && _vm.dataLoaded && !!_vm.dataLoaded\n      ? _vm.taskInfo.rectificationPhotos.length\n      : null\n  var m3 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    _vm.dataLoaded &&\n    _vm.reviewForm.result === \"approved\"\n      ? _vm.getRatingDescription(_vm.reviewForm.rating)\n      : null\n  var l0 =\n    !_vm.loading && !_vm.loadError && _vm.dataLoaded\n      ? _vm.__map(_vm.reviewForm.photos, function (photo, index) {\n          var $orig = _vm.__get_orig(photo)\n          var m4 = _vm.getPhotoDisplayUrl(photo)\n          return {\n            $orig: $orig,\n            m4: m4,\n          }\n        })\n      : null\n  var g2 =\n    !_vm.loading && !_vm.loadError && _vm.dataLoaded\n      ? _vm.reviewForm.photos.length\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        m2: m2,\n        g1: g1,\n        m3: m3,\n        l0: l0,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rectification-review.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rectification-review.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page-container\">\n    <!-- 页面头部 -->\n    <view class=\"page-header\">\n      <view class=\"header-content\">\n        <view class=\"header-title\">整改复查</view>\n        <view class=\"header-subtitle\">{{ loading ? '加载中...' : taskInfo.areaName }}</view>\n      </view>\n      <view v-if=\"!loading\" class=\"status-badge-enhanced\" :class=\"['status-' + taskInfo.status]\">\n        <uni-icons \n          :type=\"taskInfo.status === 'pending_review' ? 'info' : taskInfo.status === 'approved' ? 'checkmarkempty' : taskInfo.status === 'rejected' ? 'close' : taskInfo.status === 'completed' ? 'checkmarkempty' : 'info'\"\n          size=\"16\" \n          color=\"rgba(255, 255, 255, 0.95)\"\n        ></uni-icons>\n        <text>{{ getStatusText(taskInfo.status) }}</text>\n      </view>\n    </view>\n\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"page-loading\">\n      <view class=\"loading-content\">\n        <view class=\"loading-spinner\"></view>\n        <text class=\"loading-text\">加载整改详情...</text>\n      </view>\n    </view>\n\n    <!-- 错误状态 -->\n    <view v-else-if=\"loadError\" class=\"page-error\">\n      <p-empty-state \n        type=\"error\"\n        title=\"加载失败\"\n        description=\"网络异常，请检查网络连接\"\n        :show-button=\"true\"\n        button-text=\"重新加载\"\n        @button-click=\"retryLoad\"\n      ></p-empty-state>\n    </view>\n\n    <!-- 正常内容 -->\n    <view v-else-if=\"dataLoaded\">\n      <!-- 原始问题信息 -->\n      <view class=\"card\">\n        <view class=\"card-header\">\n          <view class=\"card-title\">原始问题</view>\n          <view class=\"card-subtitle\">{{ formatDateTime(taskInfo.issueFoundDate) }}</view>\n        </view>\n        <view class=\"card-body\">\n          <!-- 问题照片 -->\n          <view v-if=\"taskInfo.issuePhotos && taskInfo.issuePhotos.length > 0\" class=\"photo-section\">\n            <view class=\"section-title\">问题照片</view>\n            <view class=\"photo-grid\">\n              <view \n                v-for=\"(photo, index) in taskInfo.issuePhotos\" \n                :key=\"index\" \n                class=\"photo-item\"\n                @click=\"previewIssuePhoto(index)\"\n              >\n                <image :src=\"photo.url || photo\" mode=\"aspectFill\" @error=\"onPhotoError(index, 'issuePhotos')\"></image>\n                <view v-if=\"photo.loadError\" class=\"photo-error\" @click.stop=\"retryPhotoLoad(index, 'issuePhotos')\">\n                  <uni-icons type=\"reload\" size=\"24\" color=\"#8E8E93\"></uni-icons>\n                  <text class=\"error-text\">点击重试</text>\n                </view>\n              </view>\n            </view>\n          </view>\n\n          <!-- 问题说明 -->\n          <view class=\"description-section\">\n            <view class=\"section-title\">问题说明</view>\n            <view class=\"description-content\">\n              {{ taskInfo.issueDescription }}\n            </view>\n            <view class=\"issue-meta\">\n              <text class=\"meta-label\">检查员：</text>\n              <text class=\"meta-value\">{{ taskInfo.inspector }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 员工整改记录 -->\n      <view class=\"card\">\n        <view class=\"card-header\">\n          <view class=\"card-title\">员工整改记录</view>\n          <view class=\"card-subtitle\">{{ formatDateTime(taskInfo.rectificationSubmitTime) }}</view>\n        </view>\n        <view class=\"card-body\">\n          <!-- 整改照片 -->\n          <view class=\"photo-section\">\n            <view class=\"section-title\">整改照片</view>\n            <!-- 页面还在加载时显示整体加载 -->\n            <view v-if=\"!dataLoaded\" class=\"photos-loading-placeholder\">\n              <view class=\"loading-center\">\n                <view class=\"loading-spinner-small\"></view>\n                <view class=\"photos-loading-text\">加载照片中...</view>\n              </view>\n            </view>\n            <!-- 页面已加载，但照片列表为空 -->\n            <view v-else-if=\"taskInfo.rectificationPhotos.length === 0\" class=\"no-photos\">\n              <p-empty-state \n                type=\"no-image\"\n                title=\"暂无照片\"\n                description=\"员工未上传整改照片\"\n              ></p-empty-state>\n            </view>\n            <!-- 显示照片 -->\n            <view v-else class=\"photo-grid\">\n              <view \n                v-for=\"(photo, index) in taskInfo.rectificationPhotos\" \n                :key=\"index\" \n                class=\"photo-item\"\n                @click=\"previewPhoto(index)\"\n              >\n                <image :src=\"photo.url || photo\" mode=\"aspectFill\" @error=\"onPhotoError(index)\"></image>\n                <view v-if=\"photo.loadError\" class=\"photo-error\" @click.stop=\"retryPhotoLoad(index)\">\n                  <uni-icons type=\"reload\" size=\"24\" color=\"#8E8E93\"></uni-icons>\n                  <text class=\"error-text\">点击重试</text>\n                </view>\n              </view>\n            </view>\n          </view>\n\n          <!-- 整改说明 -->\n          <view class=\"description-section\">\n            <view class=\"section-title\">整改说明</view>\n            <view class=\"description-content\">\n              {{ taskInfo.rectificationDescription }}\n            </view>\n            <view class=\"issue-meta\">\n              <text class=\"meta-label\">负责人：</text>\n              <text class=\"meta-value\">{{ taskInfo.employeeName }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 审核结果 -->\n      <view class=\"card\">\n        <view class=\"card-header\">\n          <view class=\"card-title\">复查结果</view>\n          <view class=\"card-subtitle\">请对整改效果进行评价</view>\n        </view>\n        <view class=\"card-body\">\n          <!-- 审核选择 -->\n          <view class=\"review-options\">\n            <view \n              class=\"review-option\"\n              :class=\"{ active: reviewForm.result === 'approved' }\"\n              @click=\"selectReviewResult('approved')\"\n            >\n              <view class=\"option-icon approved\">\n                <uni-icons type=\"checkmarkempty\" size=\"20\" color=\"white\"></uni-icons>\n              </view>\n              <view class=\"option-content\">\n                <view class=\"option-title\">复查通过</view>\n                <view class=\"option-desc\">整改效果良好，问题已解决</view>\n              </view>\n            </view>\n\n            <view \n              class=\"review-option\"\n              :class=\"{ active: reviewForm.result === 'rejected' }\"\n              @click=\"selectReviewResult('rejected')\"\n            >\n              <view class=\"option-icon rejected\">\n                <uni-icons type=\"close\" size=\"20\" color=\"white\"></uni-icons>\n              </view>\n              <view class=\"option-content\">\n                <view class=\"option-title\">需要重新整改</view>\n                <view class=\"option-desc\">整改不到位，需要继续处理</view>\n              </view>\n            </view>\n          </view>\n\n          <!-- 审核通过时的评分 -->\n          <view v-if=\"reviewForm.result === 'approved'\" class=\"rating-section-enhanced\">\n            <view class=\"section-title\">整改后评分</view>\n            <view class=\"rating-container-enhanced\">\n              <!-- 大数字显示 -->\n              <view class=\"rating-display-large\">\n                <view class=\"rating-number-container\">\n                  <text class=\"rating-number\">{{ reviewForm.rating || 0 }}</text>\n                  <text class=\"rating-unit\">/5</text>\n                </view>\n                <view class=\"rating-desc\">{{ getRatingDescription(reviewForm.rating) }}</view>\n              </view>\n              \n              <!-- 星星评分（使用官方uni-rate组件） -->\n              <view class=\"star-rating\">\n                <uni-rate \n                  :value=\"reviewForm.rating\" \n                  @change=\"onStarRateChange\"\n                  allow-half\n                  :size=\"24\"\n                  active-color=\"#FFD700\"\n                  inactive-color=\"#E5E5EA\"\n                  :touchable=\"true\"\n                  :margin=\"8\"\n                />\n              </view>\n              \n              <!-- 自定义滑动条评分 -->\n              <view class=\"custom-slider-rating\">\n                <view class=\"custom-slider-container\" \n                      @touchstart=\"onSliderTouchStart\" \n                      @touchmove=\"onSliderTouchMove\" \n                      @touchend=\"onSliderTouchEnd\"\n                      @mousedown=\"onSliderMouseDown\"\n                      @mousemove=\"onSliderMouseMove\"\n                      @mouseup=\"onSliderMouseUp\"\n                      @mouseleave=\"onSliderMouseUp\">\n                  <!-- 滑动轨道 -->\n                  <view class=\"slider-track\">\n                    <view class=\"slider-track-active\" :style=\"{ width: (reviewForm.rating / 5 * 100) + '%' }\"></view>\n                  </view>\n                  <!-- 滑块 -->\n                  <view class=\"slider-thumb\" :style=\"{ left: (reviewForm.rating / 5 * 100) + '%' }\"></view>\n                  <!-- 刻度点 -->\n                  <view class=\"slider-marks\">\n                    <view \n                      v-for=\"(mark, index) in 6\" \n                      :key=\"index\" \n                      class=\"slider-mark\"\n                      :class=\"{ 'slider-mark-active': index <= reviewForm.rating }\"\n                      :style=\"{ left: (index / 5 * 100) + '%' }\"\n                      @click=\"setRatingByMark(index)\"\n                    ></view>\n                  </view>\n                </view>\n                <!-- 标签 - 放在滑动条容器外面 -->\n                <view class=\"slider-labels-external\">\n                  <text \n                    v-for=\"(label, labelIndex) in ['0','1','2','3','4','5']\" \n                    :key=\"labelIndex\"\n                    class=\"slider-label-external\"\n                    :class=\"{ 'slider-label-active': labelIndex <= reviewForm.rating }\"\n                    :style=\"{ left: (labelIndex / 5 * 100) + '%' }\"\n                  >{{ label }}</text>\n                </view>\n              </view>\n              \n              <view class=\"rating-tips\">\n                <text>请根据整改后的实际效果进行客观评分（支持半星评分）</text>\n              </view>\n            </view>\n          </view>\n\n          <!-- 审核意见 -->\n          <view class=\"review-comment-section\">\n            <view class=\"section-title\">\n              复查意见\n              <text class=\"required-mark\" v-if=\"reviewForm.result === 'rejected'\">*</text>\n            </view>\n            <view class=\"textarea-container-enhanced\">\n              <textarea \n                v-model=\"reviewForm.comment\"\n                :placeholder=\"commentPlaceholder\"\n                maxlength=\"200\"\n                class=\"comment-textarea-enhanced\"\n                @input=\"handleCommentInput\"\n              ></textarea>\n              <!-- 字数统计 - 悬浮在右下角 -->\n              <view class=\"char-count-overlay\">\n                {{ commentLength }}/200\n              </view>\n            </view>\n          </view>\n\n          <!-- 审核照片（可选） -->\n          <view class=\"review-photo-section\">\n            <view class=\"section-header\">\n              <view class=\"section-title\">\n                复查照片\n                <text v-if=\"reviewForm.result === 'rejected'\">（必填）</text>\n                <text v-else>（可选）</text>\n                <text class=\"required-mark\" v-if=\"reviewForm.result === 'rejected'\">*</text>\n              </view>\n              <view class=\"auto-upload-toggle\" @click=\"toggleAutoUpload\">\n                <view class=\"toggle-label\">自动上传</view>\n                <view class=\"toggle-switch\" :class=\"{ active: autoUpload }\">\n                  <view class=\"toggle-circle\"></view>\n                </view>\n              </view>\n            </view>\n            <view class=\"photo-grid\">\n              <view \n                v-for=\"(photo, index) in reviewForm.photos\" \n                :key=\"index\" \n                class=\"photo-item\"\n              >\n                <image :src=\"getPhotoDisplayUrl(photo)\" mode=\"aspectFill\" @click=\"previewReviewPhoto(index)\" @error=\"onPhotoError(index)\"></image>\n                \n                <!-- 上传状态指示器 -->\n                <view v-if=\"photo.uploading\" class=\"photo-uploading\">\n                  <view class=\"upload-spinner\"></view>\n                </view>\n                <view v-else-if=\"photo.uploaded\" class=\"photo-uploaded\">\n                  <uni-icons type=\"checkmarkempty\" size=\"16\" color=\"white\"></uni-icons>\n                </view>\n                \n                <!-- 删除按钮 - 垂直居中 -->\n                <view class=\"photo-delete\" @click.stop=\"deleteReviewPhoto(index)\">\n                  <uni-icons type=\"close\" size=\"18\" color=\"white\"></uni-icons>\n                </view>\n              </view>\n              <view \n                v-if=\"reviewForm.photos.length < 6\" \n                class=\"photo-add\"\n                @click=\"addReviewPhoto\"\n              >\n                <uni-icons type=\"camera\" size=\"28\" color=\"#8E8E93\"></uni-icons>\n                <text>添加照片</text>\n              </view>\n            </view>\n            <view class=\"photo-tip\">\n              <text v-if=\"reviewForm.result === 'rejected'\">需要重新整改时必须上传照片作为证据，最多6张</text>\n              <text v-else>最多可上传6张复查照片</text>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 提交按钮 -->\n      <view class=\"button-container\">\n        <button \n          class=\"primary-button\" \n          @click=\"submitReview\" \n          :disabled=\"!canSubmit\"\n          :class=\"{ loading: isSubmitting }\"\n        >\n          <view v-if=\"isSubmitting\" class=\"button-loading\">\n            <view class=\"loading-spinner\"></view>\n            <text>提交中...</text>\n          </view>\n          <text v-else>提交复查结果</text>\n        </button>\n      </view>\n    </view>\n\n    <!-- 底部安全间距 -->\n    <view class=\"bottom-safe-area\"></view>\n  </view>\n</template>\n\n<script>\nimport { callCloudFunction } from '@/utils/auth.js';\nimport uploadUtils from '@/utils/upload-utils.js';\n\nexport default {\n  name: 'RectificationReview',\n  data() {\n    return {\n      taskId: '',\n      loading: false,\n      dataLoaded: false,\n      loadError: false,\n      isSubmitting: false,\n      taskInfo: {\n        id: '',\n        areaName: '',\n        areaType: '',\n        status: 'pending_review',\n        issueDescription: '',\n        issueFoundDate: '',\n        inspector: '',\n        issuePhotos: [],\n        rectificationDescription: '',\n        rectificationPhotos: [],\n        rectificationSubmitTime: '',\n        employeeName: ''\n      },\n      reviewForm: {\n        result: '', // 'approved' | 'rejected'\n        comment: '',\n        photos: [],\n        rating: 0 // 整改后评分 (1-5)\n      },\n      autoUpload: true, // 自动上传开关（默认开启）\n      sliderTouching: false // 滑动条触摸状态\n    }\n  },\n  computed: {\n    // 计算评论长度，确保响应式更新\n    commentLength() {\n      return this.reviewForm.comment ? this.reviewForm.comment.length : 0;\n    },\n    \n    canSubmit() {\n      if (!this.reviewForm.result) return false;\n      if (this.reviewForm.result === 'rejected' && !this.reviewForm.comment.trim()) return false;\n      if (this.reviewForm.result === 'rejected' && this.reviewForm.photos.length === 0) return false;\n      if (this.reviewForm.result === 'approved' && !this.reviewForm.rating) return false;\n      \n      // 检查是否有照片正在上传\n      const hasUploadingPhotos = this.reviewForm.photos.some(photo => \n        typeof photo === 'object' && photo.uploading\n      );\n      if (hasUploadingPhotos) return false;\n      \n      return !this.isSubmitting;\n    },\n    commentPlaceholder() {\n      if (this.reviewForm.result === 'approved') {\n        return '请对整改效果进行评价（可选）';\n      } else if (this.reviewForm.result === 'rejected') {\n        return '请说明需要重新整改的原因';\n      }\n      return '请输入复查意见';\n    }\n  },\n  onLoad(options) {\n    if (options.taskId) {\n      this.taskId = options.taskId;\n      this.loadTaskInfo();\n    } else {\n      this.loadError = true;\n      uni.showModal({\n        title: '参数错误',\n        content: '缺少整改任务ID参数',\n        showCancel: true,\n        cancelText: '返回',\n        confirmText: '确定',\n        success: (res) => {\n          uni.navigateBack();\n        }\n      });\n    }\n  },\n  methods: {\n    // 处理评论输入，确保字符限制和响应式更新\n    handleCommentInput(e) {\n      let value = e.detail.value || '';\n      \n      // 强制限制字符数量\n      if (value.length > 200) {\n        value = value.substring(0, 200);\n        // 如果超出限制，显示提示\n        uni.showToast({\n          title: '复查意见不能超过200个字符',\n          icon: 'none',\n          duration: 1500\n        });\n      }\n      \n      // 更新数据\n      this.reviewForm.comment = value;\n      \n      // 强制触发视图更新\n      this.$forceUpdate();\n    },\n    \n    // 加载整改任务信息\n    async loadTaskInfo() {\n      if (!this.taskId) {\n        this.loadError = true;\n        uni.showModal({\n          title: '参数错误',\n          content: '整改任务ID不能为空',\n          showCancel: false,\n          confirmText: '返回',\n          success: () => {\n            uni.navigateBack();\n          }\n        });\n        return;\n      }\n\n      this.loading = true;\n      this.dataLoaded = false;\n      this.loadError = false;\n      \n      try {\n        const result = await callCloudFunction('hygiene-rectification', {\n          action: 'getRectificationDetail',\n          data: { id: this.taskId }\n        });\n\n        if (result && result.success && result.data) {\n          const task = result.data;\n          \n          this.taskInfo = {\n            id: task._id || task.id,\n            areaName: task.area_name || '未知责任区',\n            areaType: task.area_type || 'fixed',\n            status: task.status || 'pending_review',\n            issueDescription: task.issue_description || task.description || '无问题描述',\n            issueFoundDate: task.created_at || task.issue_found_date,\n            inspector: task.inspector_name || task.assigned_by_name || task.created_by_name || (task.issue && task.issue.inspector_name) || '未知检查员',\n            issuePhotos: this.processPhotos(task.photos || []),\n            rectificationDescription: task.rectification_description || task.completion_description || '',\n            rectificationPhotos: this.processPhotos(task.completion_photos || []),\n            rectificationSubmitTime: task.submitted_at || task.updated_at,\n            employeeName: task.assigned_to_name || task.assignee_name || '未分配',\n            assignee_id: task.assignee_id\n          };\n        } else {\n          throw new Error(result?.message || '获取整改任务详情失败');\n        }\n        \n        this.dataLoaded = true;\n        \n      } catch (error) {\n        console.error('加载整改任务信息失败:', error);\n        this.loadError = true;\n        \n        uni.showModal({\n          title: '加载失败',\n          content: error.message || '网络异常，请稍后重试',\n          showCancel: true,\n          cancelText: '返回',\n          confirmText: '重试',\n          success: (res) => {\n            if (res.confirm) {\n              this.loadTaskInfo();\n            } else {\n              uni.navigateBack();\n            }\n          }\n        });\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 重新加载数据\n    retryLoad() {\n      this.loadTaskInfo();\n    },\n\n    // 处理照片数据\n    processPhotos(photos) {\n      if (!photos || !Array.isArray(photos)) return [];\n      \n      return photos.map(photo => {\n        if (typeof photo === 'string') {\n          return photo;\n        } else if (photo && photo.url) {\n          return photo.url;\n        }\n        return '';\n      }).filter(url => url);\n    },\n\n    // 选择审核结果\n    selectReviewResult(result) {\n      this.reviewForm.result = result;\n      if (result === 'approved') {\n        // 通过时清空之前可能的拒绝原因\n        if (this.reviewForm.comment.includes('需要') || this.reviewForm.comment.includes('不够')) {\n          this.reviewForm.comment = '';\n        }\n        // 设置默认评分\n        if (!this.reviewForm.rating) {\n          this.reviewForm.rating = 3; // 默认3分\n        }\n      } else {\n        // 拒绝时清空评分\n        this.reviewForm.rating = 0;\n      }\n    },\n\n    // 设置评分\n    setRating(rating) {\n      this.reviewForm.rating = rating;\n    },\n\n    // 获取评分描述\n    getRatingDesc(rating) {\n      const descriptions = {\n        1: '需要改进',\n        2: '基本合格', \n        3: '良好',\n        4: '优秀',\n        5: '卓越'\n      };\n      return descriptions[rating] || '请评分';\n    },\n\n    // 获取评分描述（更详细版本，与inspection-detail.vue保持一致）\n    getRatingDescription(rating) {\n      if (rating === 0) return '请评分';\n      if (rating <= 1) return '较差';\n      if (rating <= 2) return '一般';\n      if (rating <= 3) return '良好';\n      if (rating < 5) return '优秀';  // 4-4.5分都是优秀\n      if (rating === 5) return '完美';\n      return '';\n    },\n\n    // 星星评分变化\n    onStarRateChange(e) {\n      this.reviewForm.rating = e.value;\n    },\n\n    // 点击刻度设置评分\n    setRatingByMark(rating) {\n      this.reviewForm.rating = rating;\n    },\n\n    // 自定义滑动条触摸开始\n    onSliderTouchStart(e) {\n      this.sliderTouching = true;\n      this.updateRatingFromTouch(e);\n    },\n\n    // 自定义滑动条触摸移动\n    onSliderTouchMove(e) {\n      if (this.sliderTouching) {\n        this.updateRatingFromTouch(e);\n      }\n    },\n\n    // 自定义滑动条触摸结束\n    onSliderTouchEnd(e) {\n      this.sliderTouching = false;\n    },\n\n    // 鼠标事件处理（H5端支持）\n    onSliderMouseDown(e) {\n      this.sliderTouching = true;\n      this.updateRatingFromMouse(e);\n    },\n\n    onSliderMouseMove(e) {\n      if (this.sliderTouching) {\n        this.updateRatingFromMouse(e);\n      }\n    },\n\n    onSliderMouseUp(e) {\n      this.sliderTouching = false;\n    },\n\n    // 根据触摸位置更新评分\n    updateRatingFromTouch(e) {\n      const touch = e.touches[0] || e.changedTouches[0];\n      // 获取滑动条容器的位置信息\n      uni.createSelectorQuery().in(this).select('.custom-slider-container').boundingClientRect((rect) => {\n        if (rect) {\n          const x = touch.clientX - rect.left;\n          const percentage = Math.max(0, Math.min(1, x / rect.width));\n          const rating = Math.round(percentage * 5 * 2) / 2; // 精确到0.5\n          this.reviewForm.rating = Math.max(0, Math.min(5, rating));\n        }\n      }).exec();\n    },\n\n    // 根据鼠标位置更新评分（H5端）\n    updateRatingFromMouse(e) {\n      // 获取滑动条容器的位置信息\n      uni.createSelectorQuery().in(this).select('.custom-slider-container').boundingClientRect((rect) => {\n        if (rect) {\n          const x = e.clientX - rect.left;\n          const percentage = Math.max(0, Math.min(1, x / rect.width));\n          const rating = Math.round(percentage * 5 * 2) / 2; // 精确到0.5\n          this.reviewForm.rating = Math.max(0, Math.min(5, rating));\n        }\n      }).exec();\n    },\n\n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        'pending_review': '待复查',\n        'approved': '复查通过',\n        'rejected': '整改不达标',\n        'completed': '已完成'\n      };\n      return statusMap[status] || status;\n    },\n\n    // 格式化日期时间\n    formatDateTime(dateString) {\n      if (!dateString) return '--';\n      \n      try {\n        let date;\n        if (typeof dateString === 'string') {\n          if (dateString.includes('T') || dateString.includes('Z')) {\n            // ISO 8601 格式\n            date = new Date(dateString);\n          } else {\n            // 传统格式\n            date = new Date(dateString.replace(/-/g, '/'));\n          }\n        } else {\n          date = new Date(dateString);\n        }\n        \n        if (isNaN(date.getTime())) {\n          return '--';\n        }\n        \n        return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;\n      } catch (error) {\n        return '--';\n      }\n    },\n\n    // 预览整改照片\n    previewPhoto(index) {\n      const urls = this.taskInfo.rectificationPhotos.map(photo => \n        typeof photo === 'object' ? photo.url : photo\n      );\n      uni.previewImage({\n        urls: urls,\n        current: index\n      });\n    },\n\n    // 预览问题照片\n    previewIssuePhoto(index) {\n      const urls = this.taskInfo.issuePhotos.map(photo => \n        typeof photo === 'object' ? photo.url : photo\n      );\n      uni.previewImage({\n        urls: urls,\n        current: index\n      });\n    },\n\n    // 预览审核照片\n    previewReviewPhoto(index) {\n      const urls = this.reviewForm.photos.map(photo => this.getPhotoDisplayUrl(photo));\n      uni.previewImage({\n        urls: urls,\n        current: index\n      });\n    },\n\n    // 获取照片显示URL\n    getPhotoDisplayUrl(photo) {\n      return photo.url || photo;\n    },\n\n    // 添加审核照片\n    async addReviewPhoto() {\n      try {\n        const res = await new Promise((resolve, reject) => {\n          uni.chooseImage({\n            count: 6 - this.reviewForm.photos.length,\n            sizeType: ['compressed'],\n            sourceType: ['camera', 'album'],\n            success: resolve,\n            fail: reject\n          });\n        });\n        \n        // 添加临时路径到照片列表\n        const newPhotos = res.tempFilePaths.map(path => ({\n          url: path,\n          uploaded: false,\n          cloudUrl: '',\n          cloudPath: '',\n          uploading: false,\n          belongsToRecord: false // 标记为新上传的照片\n        }));\n        \n\n        \n        // 添加到照片列表\n        this.reviewForm.photos = this.reviewForm.photos.concat(newPhotos);\n        \n        // 如果开启自动上传，立即上传新选择的照片\n        if (this.autoUpload) {\n          this.autoUploadNewPhotos(newPhotos);\n        }\n      } catch (error) {\n        console.error('选择照片失败:', error);\n        uni.showToast({\n          title: '选择照片失败',\n          icon: 'none'\n        });\n      }\n    },\n\n    // 删除审核照片\n    async deleteReviewPhoto(index) {\n      if (index < 0 || index >= this.reviewForm.photos.length) {\n        return;\n      }\n\n      const photo = this.reviewForm.photos[index];\n      \n      // 如果照片已经上传到云端，需要删除云端文件\n      if (photo.uploaded && photo.cloudPath) {\n        try {\n          uni.showLoading({ title: '删除照片中...' });\n          \n          // 审核照片都是新上传的，直接调用删除云文件的云函数\n          await uniCloud.callFunction({\n            name: 'delete-file',\n            data: {\n              fileList: [this.extractFileId(photo.cloudPath)]\n            }\n          });\n          \n\n        } catch (error) {\n          uni.showToast({\n            title: '删除云端照片失败',\n            icon: 'none'\n          });\n        } finally {\n          uni.hideLoading();\n        }\n      }\n      \n      // 从本地数组中移除\n      this.reviewForm.photos.splice(index, 1);\n    },\n\n    // 从URL中提取文件ID（与cleaning-upload.vue保持一致）\n    extractFileId(url) {\n      if (url.startsWith('cloud://')) {\n        const parts = url.split('/');\n        return parts[parts.length - 1];\n      } else if (url.includes('tcb-api')) {\n        const urlObj = new URL(url);\n        return urlObj.pathname.split('/').pop();\n      }\n      return url;\n    },\n\n    // 自动上传新选择的照片（与cleaning-upload.vue保持一致）\n    async autoUploadNewPhotos(newPhotos) {\n      \n      for (let i = 0; i < newPhotos.length; i++) {\n        const photo = newPhotos[i];\n        const photoIndex = this.reviewForm.photos.findIndex(p => p.url === photo.url);\n        \n        if (photoIndex === -1) continue;\n        \n        try {\n          // 标记为正在上传\n          this.$set(this.reviewForm.photos[photoIndex], 'uploading', true);\n          \n          // 单张照片上传\n          const uploadResult = await this.uploadSinglePhoto(photo);\n          \n          if (uploadResult.success) {\n            // 更新照片信息（与cleaning-upload.vue保持一致，不更新photo.url）\n            this.$set(this.reviewForm.photos[photoIndex], 'uploaded', true);\n            this.$set(this.reviewForm.photos[photoIndex], 'cloudUrl', uploadResult.url);\n            this.$set(this.reviewForm.photos[photoIndex], 'cloudPath', uploadResult.cloudPath);\n            this.$set(this.reviewForm.photos[photoIndex], 'uploading', false);\n            \n\n          } else {\n            throw new Error(uploadResult.error || '上传失败');\n          }\n        } catch (error) {\n          this.$set(this.reviewForm.photos[photoIndex], 'uploading', false);\n          \n          // 显示上传失败提示\n          uni.showToast({\n            title: `审核照片${i + 1}上传失败`,\n            icon: 'none',\n            duration: 2000\n          });\n        }\n      }\n    },\n\n    // 上传审核照片\n    async uploadReviewPhotos() {\n      const uploadedPhotos = [];\n      \n      for (let i = 0; i < this.reviewForm.photos.length; i++) {\n        const photo = this.reviewForm.photos[i];\n        \n        if (photo.uploaded) {\n          // 已上传的照片，直接使用云端URL\n          uploadedPhotos.push({\n            url: photo.cloudPath,\n            description: '审核照片',\n            timestamp: new Date()\n          });\n          continue;\n        }\n\n        try {\n          // 设置上传状态\n          this.$set(photo, 'uploading', true);\n          \n          // 使用统一的上传工具上传单张照片\n          const uploadResult = await this.uploadSinglePhoto(photo);\n          \n          if (uploadResult.success) {\n            // 更新照片状态（与cleaning-upload.vue保持一致，不更新photo.url）\n            this.$set(photo, 'uploaded', true);\n            this.$set(photo, 'cloudUrl', uploadResult.url);\n            this.$set(photo, 'cloudPath', uploadResult.cloudPath);\n            this.$set(photo, 'uploading', false);\n\n            uploadedPhotos.push({\n              url: uploadResult.cloudPath,\n              description: '审核照片',\n              timestamp: new Date()\n            });\n            \n\n          } else {\n            throw new Error(uploadResult.error || '上传失败');\n          }\n        } catch (error) {\n          this.$set(photo, 'uploading', false);\n          \n          uni.showModal({\n            title: '上传失败',\n            content: `审核照片 ${i + 1} 上传失败，请重试`,\n            showCancel: false\n          });\n          \n          throw error; // 中断整个提交流程\n        }\n      }\n\n      return uploadedPhotos;\n    },\n\n    // 单张照片上传（统一方法）\n    async uploadSinglePhoto(photo) {\n      try {\n        const timestamp = Date.now();\n        const random = Math.random().toString(36).substring(2, 8);\n        const cloudPath = `6s/review/${this.taskInfo.id || 'unknown'}/${timestamp}_${random}.jpg`;\n        \n        // 使用统一的 uploadToCloud 方法\n        const uploadResult = await uploadUtils.uploadToCloud(photo.url, cloudPath);\n        \n        if (uploadResult && uploadResult.fileID) {\n          // 获取访问URL\n          const fileInfo = await uploadUtils.getFileInfo(uploadResult.fileID);\n          \n          return {\n            success: true,\n            cloudPath: uploadResult.fileID,\n            url: fileInfo.tempFileURL || uploadResult.fileID,\n            size: uploadResult.actualSize\n          };\n        } else {\n          throw new Error('上传返回结果异常');\n        }\n      } catch (error) {\n        return { success: false, error: error.message };\n      }\n    },\n\n    // 提交审核结果\n    async submitReview() {\n      if (!this.canSubmit) return;\n\n      const resultText = this.reviewForm.result === 'approved' ? '复查通过' : '需重新整改';\n      \n      uni.showModal({\n        title: '确认提交',\n        content: `确定审核结果为\"${resultText}\"吗？`,\n        success: async (res) => {\n          if (res.confirm) {\n            await this.doSubmit();\n          }\n        }\n      });\n    },\n\n    // 执行提交\n    async doSubmit() {\n      this.isSubmitting = true;\n\n      try {\n        // 上传审核照片\n        let uploadedPhotos = [];\n        if (this.reviewForm.photos.length > 0) {\n          uploadedPhotos = await this.uploadReviewPhotos();\n        }\n\n        const reviewData = {\n          id: this.taskId, // 修正参数名\n          review_result: this.reviewForm.result, // 'approved' 或 'rejected'\n          review_comments: this.reviewForm.comment.trim() || '',\n          review_photos: uploadedPhotos, // 添加审核照片\n          review_date: new Date().toISOString(),\n          final_rating: this.reviewForm.result === 'approved' ? this.reviewForm.rating : 0 // 最终评分\n        };\n\n\n\n        // 调用云函数提交审核结果\n        const result = await callCloudFunction('hygiene-rectification', {\n          action: 'reviewRectification',\n          data: reviewData\n        });\n\n        if (result && result.success) {\n          const resultText = this.reviewForm.result === 'approved' ? '复查通过' : '需重新整改';\n          \n          uni.showToast({\n            title: `审核${resultText}`,\n            icon: 'success'\n          });\n\n          // 通知其他页面数据已更新\n          uni.$emit('rectificationReviewUpdated', {\n            taskId: this.taskId,\n            result: this.reviewForm.result,\n            areaId: this.taskInfo.id,\n            areaName: this.taskInfo.areaName\n          });\n\n          // 同时触发整改记录更新事件，供其他页面监听\n          uni.$emit('rectificationRecordUpdated', {\n            taskId: this.taskId,\n            areaId: this.taskInfo.id,\n            mode: 'review',\n            result: this.reviewForm.result\n          });\n\n          // 返回上一页并刷新\n          setTimeout(() => {\n            uni.navigateBack({\n              delta: 1\n            });\n          }, 1500);\n        } else {\n          throw new Error(result?.message || '提交审核结果失败');\n        }\n\n      } catch (error) {\n        \n        let errorMessage = '提交失败，请重试';\n        if (error.message) {\n          if (error.message.includes('未登录')) {\n            errorMessage = '请先登录';\n          } else if (error.message.includes('权限')) {\n            errorMessage = '您没有权限审核该任务';\n          } else {\n            errorMessage = error.message;\n          }\n        }\n        \n        uni.showModal({\n          title: '提交失败',\n          content: errorMessage,\n          showCancel: false,\n          confirmText: '知道了'\n        });\n      } finally {\n        this.isSubmitting = false;\n      }\n    },\n\n    // 处理照片加载错误（通用方法）\n    onPhotoError(index, photoArray = 'rectificationPhotos') {\n      let photos;\n      if (photoArray === 'rectificationPhotos') {\n        photos = this.taskInfo.rectificationPhotos;\n      } else if (photoArray === 'issuePhotos') {\n        photos = this.taskInfo.issuePhotos;\n      } else {\n        photos = this.reviewForm.photos;\n      }\n      \n      const photo = photos[index];\n      \n      if (typeof photo === 'object') {\n        this.$set(photo, 'loadError', true);\n      } else {\n        const photoObj = (photoArray === 'rectificationPhotos' || photoArray === 'issuePhotos')\n          ? { url: photo, loadError: true }\n          : { url: photo, uploaded: false, cloudUrl: '', cloudPath: '', uploading: false, belongsToRecord: false, loadError: true };\n        this.$set(photos, index, photoObj);\n      }\n    },\n\n    // 重试加载照片（通用方法）\n    retryPhotoLoad(index, photoArray = 'rectificationPhotos') {\n      let photos;\n      if (photoArray === 'rectificationPhotos') {\n        photos = this.taskInfo.rectificationPhotos;\n      } else if (photoArray === 'issuePhotos') {\n        photos = this.taskInfo.issuePhotos;\n      } else {\n        photos = this.reviewForm.photos;\n      }\n      \n      const photo = photos[index];\n      \n      if (typeof photo === 'object') {\n        this.$set(photo, 'loadError', false);\n      }\n    },\n\n\n\n    // 切换自动上传状态\n    toggleAutoUpload() {\n      this.autoUpload = !this.autoUpload;\n      uni.showToast({\n        title: this.autoUpload ? '已开启自动上传' : '已关闭自动上传',\n        icon: 'none',\n        duration: 1500\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\n\twidth: 100%;\n\toverflow-x: hidden; /* 防止水平滚动 */\n\tbox-sizing: border-box;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;\n}\n\n.page-header {\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n  padding: 32rpx 32rpx 40rpx 32rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  color: white;\n}\n\n.header-content {\n  flex: 1;\n}\n\n.header-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  margin-bottom: 8rpx;\n}\n\n.header-subtitle {\n  font-size: 28rpx;\n  opacity: 0.9;\n}\n\n.status-badge-enhanced {\n  display: flex;\n  align-items: center;\n  gap: 6rpx;\n  padding: 6rpx 12rpx;\n  border-radius: 12rpx;\n  font-size: 22rpx;\n  font-weight: 400;\n  background: rgba(255, 255, 255, 0.15);\n  color: rgba(255, 255, 255, 0.95);\n  border: 1rpx solid rgba(255, 255, 255, 0.2);\n  white-space: nowrap;\n\n  &.status-pending_review {\n    background: rgba(255, 255, 255, 0.15);\n    color: rgba(255, 255, 255, 0.95);\n    border: 1rpx solid rgba(255, 255, 255, 0.2);\n  }\n\n  &.status-approved {\n    background: rgba(255, 255, 255, 0.15);\n    color: rgba(255, 255, 255, 0.95);\n    border: 1rpx solid rgba(255, 255, 255, 0.2);\n  }\n\n  &.status-rejected {\n    background: rgba(255, 255, 255, 0.15);\n    color: rgba(255, 255, 255, 0.95);\n    border: 1rpx solid rgba(255, 255, 255, 0.2);\n  }\n\n  &.status-completed {\n    background: rgba(255, 255, 255, 0.15);\n    color: rgba(255, 255, 255, 0.95);\n    border: 1rpx solid rgba(255, 255, 255, 0.2);\n  }\n}\n\n.page-loading {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 400rpx;\n  padding: 60rpx 32rpx;\n}\n\n.loading-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 16rpx;\n}\n\n.loading-spinner {\n  width: 40rpx;\n  height: 40rpx;\n  border: 4rpx solid #E5E7EB;\n  border-top: 4rpx solid #007AFF;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.loading-spinner-small {\n  width: 32rpx;\n  height: 32rpx;\n  border: 3rpx solid #E5E7EB;\n  border-top: 3rpx solid #007AFF;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n  margin-top: 8rpx;\n}\n\n.page-error {\n  padding: 32rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 300rpx; /* Adjust as needed */\n}\n\n.card {\n  background: #ffffff;\n  border-radius: 16rpx;\n  margin: 24rpx 32rpx 0 32rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.card-header {\n  padding: 32rpx 32rpx 16rpx 32rpx;\n  border-bottom: 1rpx solid #F2F2F7;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1D1D1F;\n}\n\n.card-subtitle {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.card-body {\n  padding: 32rpx;\n}\n\n.issue-info {\n  .issue-description {\n    font-size: 28rpx;\n    color: #1D1D1F;\n    line-height: 1.6;\n    margin-bottom: 16rpx;\n  }\n\n  .issue-meta {\n    .meta-label {\n      font-size: 24rpx;\n      color: #8E8E93;\n    }\n\n    .meta-value {\n      font-size: 24rpx;\n      color: #007AFF;\n      margin-left: 8rpx;\n    }\n  }\n}\n\n/* 问题说明中的检查员信息样式 */\n.description-section .issue-meta {\n  margin-top: 16rpx;\n  padding-top: 12rpx;\n  border-top: 1rpx solid #F2F2F7;\n\n  .meta-label {\n    font-size: 24rpx;\n    color: #8E8E93;\n  }\n\n  .meta-value {\n    font-size: 24rpx;\n    color: #007AFF;\n    margin-left: 8rpx;\n  }\n}\n\n.section-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #1D1D1F;\n  margin-bottom: 16rpx;\n\n  .required-mark {\n    color: #FF3B30;\n    margin-left: 4rpx;\n  }\n}\n\n.photo-section {\n  margin-bottom: 32rpx;\n}\n\n.photo-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n}\n\n.photo-item {\n  width: 160rpx;\n  height: 160rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n  position: relative;\n\n  image {\n    width: 100%;\n    height: 100%;\n  }\n}\n\n.photo-add {\n  width: 160rpx;\n  height: 160rpx;\n  border: 2rpx dashed #C7C7CC;\n  border-radius: 12rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: #F9F9F9;\n\n  text {\n    font-size: 24rpx;\n    color: #8E8E93;\n    margin-top: 8rpx;\n  }\n}\n\n.photo-delete {\n  position: absolute;\n  top: 6rpx;\n  right: 6rpx;\n  width: 36rpx;\n  height: 36rpx;\n  background: rgba(0, 0, 0, 0.7);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\n}\n\n.photo-uploading {\n  position: absolute;\n  top: 8rpx;\n  left: 8rpx;\n  width: 32rpx;\n  height: 32rpx;\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-spinner {\n  width: 20rpx;\n  height: 20rpx;\n  border: 2rpx solid rgba(255, 255, 255, 0.3);\n  border-top: 2rpx solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.photo-uploaded {\n  position: absolute;\n  top: 8rpx;\n  left: 8rpx;\n  width: 32rpx;\n  height: 32rpx;\n  background: #34C759;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.photo-tip {\n  font-size: 24rpx;\n  color: #8E8E93;\n  margin-top: 16rpx;\n}\n\n.description-content {\n  font-size: 28rpx;\n  color: #1D1D1F;\n  line-height: 1.6;\n  background: #F9F9F9;\n  padding: 24rpx;\n  border-radius: 12rpx;\n}\n\n.review-options {\n  margin-bottom: 32rpx;\n}\n\n.review-option {\n  display: flex;\n  align-items: center;\n  padding: 24rpx;\n  border: 2rpx solid #E5E5EA;\n  border-radius: 16rpx;\n  margin-bottom: 16rpx;\n  transition: all 0.3s ease;\n\n  &.active {\n    border-color: #007AFF;\n    background: rgba(0, 122, 255, 0.05);\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.option-icon {\n  width: 64rpx;\n  height: 64rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 24rpx;\n\n  &.approved {\n    background: #34C759;\n  }\n\n  &.rejected {\n    background: #FF3B30;\n  }\n}\n\n.option-content {\n  flex: 1;\n}\n\n.option-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #1D1D1F;\n  margin-bottom: 4rpx;\n}\n\n.option-desc {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n/* 增强的评分区域样式 */\n.rating-section-enhanced {\n  margin-top: 32rpx;\n  padding: 32rpx;\n  background: #F8F9FA;\n  border-radius: 16rpx;\n  border: 2rpx solid #E8F5E8;\n}\n\n.rating-container-enhanced {\n  text-align: center;\n}\n\n.rating-display-large {\n  margin-bottom: 32rpx;\n}\n\n.rating-number-container {\n  display: flex;\n  align-items: baseline;\n  justify-content: center;\n  gap: 6rpx;\n  margin-bottom: 8rpx;\n}\n\n.rating-number {\n  font-size: 80rpx;\n  font-weight: 700;\n  color: #007AFF;\n  line-height: 1;\n}\n\n.rating-unit {\n  font-size: 40rpx;\n  color: #8E8E93;\n  font-weight: 500;\n}\n\n.rating-desc {\n  font-size: 24rpx;\n  color: #8E8E93;\n  margin-top: 8rpx;\n  font-weight: 500;\n}\n\n/* 星星评分样式 */\n.star-rating {\n  display: flex;\n  justify-content: center;\n  gap: 16rpx;\n  margin-bottom: 24rpx;\n}\n\n/* 自定义滑动条评分样式 */\n.custom-slider-rating {\n  margin: 32rpx 0 24rpx 0;\n}\n\n.custom-slider-container {\n  position: relative;\n  height: 40rpx;\n  margin: 0 24rpx;\n  display: flex;\n  align-items: center;\n  user-select: none;\n}\n\n.slider-track {\n  position: absolute;\n  width: 100%;\n  height: 4rpx;\n  background: #E5E5EA;\n  border-radius: 2rpx;\n  left: 0;\n}\n\n.slider-track-active {\n  height: 100%;\n  background: #FFD700;\n  border-radius: 2rpx;\n  transition: width 0.2s ease;\n}\n\n.slider-thumb {\n  position: absolute;\n  width: 24rpx;\n  height: 24rpx;\n  background: #FFD700;\n  border-radius: 50%;\n  transform: translateX(-50%);\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);\n  transition: left 0.2s ease;\n  z-index: 2;\n}\n\n.slider-marks {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: -12rpx;\n}\n\n.slider-mark {\n  position: absolute;\n  width: 12rpx;\n  height: 12rpx;\n  background: #E5E5EA;\n  border-radius: 50%;\n  transform: translateX(-50%);\n  z-index: 1;\n}\n\n.slider-mark-active {\n  background: #FFD700;\n  width: 16rpx;\n  height: 16rpx;\n}\n\n.slider-labels-external {\n  position: relative;\n  margin-top: 20rpx;\n  margin-left: 24rpx;\n  margin-right: 24rpx;\n  height: 40rpx;\n}\n\n.slider-label-external {\n  position: absolute;\n  font-size: 24rpx;\n  color: #8E8E93;\n  font-weight: 500;\n  transform: translateX(-50%);\n  text-align: center;\n}\n\n.slider-label-active {\n  color: #FFD700;\n  font-weight: 600;\n}\n\n.rating-tips {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.review-comment-section,\n.review-photo-section {\n  margin-top: 32rpx;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.auto-upload-toggle {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n  cursor: pointer;\n}\n\n.toggle-label {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.toggle-switch {\n  width: 76rpx;\n  height: 44rpx;\n  background: #E5E5EA;\n  border-radius: 22rpx;\n  position: relative;\n  transition: background-color 0.3s ease;\n}\n\n.toggle-switch.active {\n  background: #34C759;\n}\n\n.toggle-circle {\n  width: 36rpx;\n  height: 36rpx;\n  background: white;\n  border-radius: 50%;\n  position: absolute;\n  top: 4rpx;\n  left: 4rpx;\n  transition: transform 0.3s ease;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);\n}\n\n.toggle-switch.active .toggle-circle {\n  transform: translateX(32rpx);\n}\n\n.textarea-container {\n  position: relative;\n}\n\n.textarea-container-enhanced {\n  position: relative;\n  border-radius: 16rpx;\n  border: 2rpx solid #F2F2F7;\n  background: #FAFAFA;\n  overflow: hidden;\n  transition: all 0.3s ease;\n\n  &:focus-within {\n    border-color: #007AFF;\n    background: white;\n    box-shadow: 0 2rpx 12rpx rgba(0, 122, 255, 0.15);\n  }\n}\n\n.comment-textarea-enhanced {\n  width: 100%;\n  min-height: 160rpx;\n  background: transparent;\n  padding: 24rpx 20rpx 16rpx 20rpx;\n  font-size: 28rpx;\n  color: #1C1C1E;\n  line-height: 1.5;\n  border: none;\n  outline: none;\n  resize: none;\n  box-sizing: border-box;\n\n  &::placeholder {\n    color: #C7C7CC;\n    font-size: 28rpx;\n  }\n}\n\n.char-count-overlay {\n  position: absolute;\n  bottom: 16rpx;\n  right: 20rpx;\n  font-size: 24rpx;\n  color: #C7C7CC !important;\n  font-weight: 500;\n  z-index: 2;\n}\n\n.button-container {\n  padding: 32rpx;\n  display: flex;\n  flex-direction: column;\n  gap: 16rpx;\n}\n\n.primary-button {\n  background: #007AFF;\n  color: white;\n  border: none;\n  border-radius: 12rpx;\n  padding: 24rpx;\n  font-size: 32rpx;\n  font-weight: 600;\n  width: 100%;\n}\n\n.primary-button[disabled] {\n  background: #C7C7CC;\n  color: #8E8E93;\n}\n\n.primary-button.loading {\n  background: #0056D6;\n  opacity: 0.9;\n}\n\n.button-loading {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12rpx;\n  color: white;\n}\n\n.button-loading .loading-spinner {\n  width: 32rpx;\n  height: 32rpx;\n  border: 3rpx solid rgba(255, 255, 255, 0.3);\n  border-top: 3rpx solid rgba(255, 255, 255, 0.9);\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.bottom-safe-area {\n  height: 40rpx;\n}\n\n.photos-loading-placeholder {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 160rpx; /* Adjust as needed */\n  background: #f0f0f0;\n  border-radius: 12rpx;\n  margin-bottom: 16rpx;\n}\n\n.loading-center {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.photos-loading-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n  margin-top: 12rpx;\n  text-align: center;\n}\n\n.no-photos {\n  padding: 32rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 160rpx; /* Adjust as needed */\n}\n\n.photo-error {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 12rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  color: #8E8E93;\n  font-size: 24rpx;\n  z-index: 10;\n}\n\n.error-text {\n  margin-top: 8rpx;\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rectification-review.vue?vue&type=style&index=0&id=665ecd07&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rectification-review.vue?vue&type=style&index=0&id=665ecd07&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775844952\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}