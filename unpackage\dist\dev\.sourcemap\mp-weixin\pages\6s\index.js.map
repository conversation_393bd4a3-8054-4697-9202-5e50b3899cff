{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s/index.vue?d6f0", "webpack:///D:/Xwzc/pages/6s/index.vue?ea80", "webpack:///D:/Xwzc/pages/6s/index.vue?9685", "webpack:///D:/Xwzc/pages/6s/index.vue?17b2", "uni-app:///pages/6s/index.vue", "webpack:///D:/Xwzc/pages/6s/index.vue?73e1", "webpack:///D:/Xwzc/pages/6s/index.vue?b432"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "currentTab", "tabs", "name", "key", "selected<PERSON><PERSON><PERSON><PERSON>er", "loading", "dataLoaded", "loadError", "activitiesLoading", "remindersLoading", "processCache", "timeCache", "stats", "totalAreas", "completedAreas", "foundIssues", "averageRating", "completionRate", "urgentIssues", "recentActivities", "smartReminders", "computed", "userInfo", "storedUserInfo", "currentUserInfo", "console", "hasInspectionPermission", "currentTimeRangeText", "currentYear", "currentMonth", "currentWeekRange", "onLoad", "uni", "onUnload", "onShow", "methods", "init<PERSON><PERSON>s", "initProcessCache", "activityTypeMap", "type", "iconType", "iconColor", "reminderTypeMap", "iconSymbol", "title", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initTimeCache", "now", "lastUpdateTime", "createTimeFormatter", "getCurrentWeekRange", "monday", "sunday", "start", "end", "loadPageDataOptimized", "loadSecondaryDataAsync", "Promise", "loadStatsDataOptimized", "action", "timeFilter", "includeDetails", "result", "loadRecentActivitiesOptimized", "limit", "loadSmartRemindersOptimized", "userRole", "processActivitiesData", "typeConfig", "time", "processRemindersData", "description", "silentRefreshData", "handleLoadError", "icon", "duration", "switchTimeFilter", "getTimeRangeTitle", "handleDataUpdated", "goToMyArea", "url", "goToPendingTasks", "uploadPhoto", "manageBaseData"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACuKnnB;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;EAAA,CACA;EACAC;IACA;MACAC;MACAC,OACA;QAAAC;QAAAC;MAAA,EACA;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;QACA;QACA;QACA;QACA,uCACAC,iBACAC;MAEA;QACAC;QACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QAAAC;QAAAC;QAAAC;MAEA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;EACA;EAEAC;IACA;IACA;;IAEA;IACAC;IACAA;IACAA;EACA;EAEAC;IACA;IACAD;IACAA;IACAA;EACA;EAEAE;IACA;IACA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UACA;UACAC;YACA;cACAC;cACAC;cACAC;YACA;YACA;cACAF;cACAC;cACAC;YACA;YACA;cACAF;cACAC;cACAC;YACA;YACA;cACAF;cACAC;cACAC;YACA;YACA;cACAF;cACAC;cACAC;YACA;UACA;UAEA;UACAC;YACA;cACAH;cACAI;cACAC;YACA;YACA;cACAL;cACAI;cACAC;YACA;YACA;cACAL;cACAI;cACAC;YACA;YACA;cACAL;cACAI;cACAC;YACA;YACA;cACAL;cACAI;cACAC;YACA;YACA;cACAL;cACAI;cACAC;YACA;YACA;cACAL;cACAI;cACAC;YACA;YACA;cACAL;cACAI;cACAC;YACA;YACA;cACAL;cACAI;cACAC;YACA;YACA;cACAL;cACAI;cACAC;YACA;UACA;UAEA;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;UACAC;UACAnB;UACAC;UACAC;UACAkB;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;UACA;UACA;UAEA;UACA;UAEA;YACA;YACA;UACA;YACA;UACA;YACA;YACA;YACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;MAEA;MACAC;MAEA;MACAC;MAEA;QACAC;QACAC;QACAH;QACAC;MACA;IACA;IAEA;IACAG;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAEA;gBACA;gBACA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC,aACA,wCACA,qCACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAIA;gBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAC;kBACA5D;oBACA6D;oBACAC;kBACA;gBACA;cAAA;gBANAC;gBAAA,MAQAA;kBAAA;kBAAA;gBAAA;gBACA;kBACAjD;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;gBACA;kBACAL;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACA6C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAJ;kBACA5D;oBACAiE;oBACAJ;kBACA;gBACA;cAAA;gBANAE;gBAQA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBAAA;gBAAA,OAEA;kBACAP;kBACA5D;oBACAmE;oBACAF;kBACA;gBACA;cAAA;gBANAF;gBAQA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAK;MAAA;MACA;MAEA;QACA;UACA5B;UACAC;UACAC;QACA;QAEA,uCACA2B;UACAxB;UACAyB;QAAA;MAEA;IACA;IAEA;IACAC;MAAA;MACA;MAEA;QACA;UACA/B;UACAI;UACAC;QACA;QAEA,uCACAwB;UACAG;QAAA;MAEA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGA;gBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IAEA;IACAC;MACAzC;QACAY;QACA8B;QACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEA;;MAEA;MACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;IACA;IAEAC;MACA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA/C;QACAgD;MACA;IACA;IAEAC;MACAjD;QACAgD;MACA;IACA;IAEAE;MACAlD;QACAgD;MACA;IACA;IAEAG;MACAnD;QACAgD;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9pBA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=96da5e8e&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=96da5e8e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"96da5e8e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s/index.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=96da5e8e&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.activitiesLoading ? _vm.recentActivities.length : null\n  var g1 = !_vm.remindersLoading ? _vm.smartReminders.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"sixs-container\">\n\t\t<view class=\"content-container\">\n\t\t\t<view class=\"tab-content\">\n\t\t\t\t<view class=\"section\">\n\t\t\t\t\t<view class=\"overview-header\">\n\t\t\t\t\t\t<view class=\"header-content\">\n\t\t\t\t\t\t\t<text class=\"overview-title\">6S管理概览</text>\n\t\t\t\t\t\t\t<text class=\"overview-subtitle\">{{ currentTimeRangeText }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 加载中状态 -->\n\t\t\t\t\t<view v-if=\"loading && !dataLoaded\" class=\"loading-container\">\n\t\t\t\t\t\t<view class=\"loading-content\">\n\t\t\t\t\t\t\t<view class=\"loading-spinner\"></view>\n\t\t\t\t\t\t\t<text class=\"loading-text\">统计数据加载中...</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 错误状态 -->\n\t\t\t\t\t<view v-else-if=\"loadError\" class=\"error-container\">\n\t\t\t\t\t\t<view class=\"error-content\">\n\t\t\t\t\t\t\t<uni-icons type=\"info\" size=\"24\" color=\"#FF3B30\"></uni-icons>\n\t\t\t\t\t\t\t<text class=\"error-text\">数据加载失败，请稍后重试</text>\n\t\t\t\t\t\t\t<button class=\"retry-btn\" @click=\"loadPageDataOptimized\">重新加载</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 统计数据 -->\n\t\t\t\t\t<view v-else class=\"overview-stats\">\n\t\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t\t<text class=\"stat-number blue\">{{ stats.totalAreas }}</text>\n\t\t\t\t\t\t\t<text class=\"stat-label\">总责任区</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t\t<text class=\"stat-number green\">{{ stats.completionRate }}%</text>\n\t\t\t\t\t\t\t<text class=\"stat-label\">本周完成率</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t\t<text class=\"stat-number orange\">{{ stats.foundIssues }}</text>\n\t\t\t\t\t\t\t<text class=\"stat-label\">待整改问题</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t\t<text class=\"stat-number red\">{{ stats.urgentIssues }}</text>\n\t\t\t\t\t\t\t<text class=\"stat-label\">逾期未清理</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 快捷操作 -->\n\t\t\t\t<view class=\"section\">\n\t\t\t\t\t<view class=\"quick-actions-header\">\n\t\t\t\t\t\t<view class=\"header-content\">\n\t\t\t\t\t\t\t<text class=\"quick-actions-title\">快捷操作</text>\n\t\t\t\t\t\t\t<text class=\"quick-actions-subtitle\">常用功能快速入口</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"quick-actions-grid\">\n\t\t\t\t\t\t<view class=\"quick-action-btn primary\" @click=\"goToMyArea\">\n\t\t\t\t\t\t\t<view class=\"btn-content\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"list\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t\t\t<text class=\"btn-text\">我的责任区</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"quick-action-btn success\" @click=\"goToPendingTasks\">\n\t\t\t\t\t\t\t<view class=\"btn-content\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"eye\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t\t\t<text class=\"btn-text\">责任区检查</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"quick-action-btn warning\" @click=\"uploadPhoto\">\n\t\t\t\t\t\t\t<view class=\"btn-content\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"camera\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t\t\t<text class=\"btn-text\">月度检查</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"quick-action-btn purple\" @click=\"manageBaseData\">\n\t\t\t\t\t\t\t<view class=\"btn-content\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"gear\" size=\"20\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t\t\t\t<text class=\"btn-text\">基础数据</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 最新动态 -->\n\t\t\t\t<view class=\"section\">\n\t\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t\t<view class=\"header-content\">\n\t\t\t\t\t\t\t<text class=\"section-title\">最新动态</text>\n\t\t\t\t\t\t\t<text class=\"section-subtitle\">最近的检查和整改情况</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"activity-list\">\n\t\t\t\t\t\t<!-- 加载中状态 -->\n\t\t\t\t\t\t<view v-if=\"activitiesLoading\" class=\"loading-container\">\n\t\t\t\t\t\t\t<view class=\"loading-content\">\n\t\t\t\t\t\t\t\t<view class=\"loading-spinner\"></view>\n\t\t\t\t\t\t\t\t<text class=\"loading-text\">动态数据加载中...</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 空状态 -->\n\t\t\t\t\t\t<view v-else-if=\"recentActivities.length === 0\" class=\"empty-state\">\n\t\t\t\t\t\t\t<uni-icons type=\"info\" size=\"20\" color=\"#8E8E93\"></uni-icons>\n\t\t\t\t\t\t\t<text class=\"empty-text\">暂无最新动态</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 活动列表 -->\n\t\t\t\t\t\t<view v-else class=\"activity-item\" v-for=\"(activity, index) in recentActivities\" :key=\"index\">\n\t\t\t\t\t\t\t<view class=\"activity-icon\" :class=\"activity.type\">\n\t\t\t\t\t\t\t\t<!-- 使用uni-icons图标 -->\n\t\t\t\t\t\t\t\t<uni-icons :type=\"activity.iconType\" :color=\"activity.iconColor\" size=\"18\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"activity-content\">\n\t\t\t\t\t\t\t\t<text class=\"activity-title\">{{ activity.title }}</text>\n\t\t\t\t\t\t\t\t<text class=\"activity-time\">{{ activity.time }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 智能提醒 -->\n\t\t\t\t<view class=\"section\">\n\t\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t\t<view class=\"header-content\">\n\t\t\t\t\t\t\t<text class=\"section-title\">智能提醒</text>\n\t\t\t\t\t\t\t<text class=\"section-subtitle\">根据当前时间和业务规则智能生成</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"reminder-list\">\n\t\t\t\t\t\t<!-- 加载中状态 -->\n\t\t\t\t\t\t<view v-if=\"remindersLoading\" class=\"loading-container\">\n\t\t\t\t\t\t\t<view class=\"loading-content\">\n\t\t\t\t\t\t\t\t<view class=\"loading-spinner\"></view>\n\t\t\t\t\t\t\t\t<text class=\"loading-text\">智能提醒生成中...</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 空状态 -->\n\t\t\t\t\t\t<view v-else-if=\"smartReminders.length === 0\" class=\"empty-state\">\n\t\t\t\t\t\t\t<uni-icons type=\"checkmarkempty\" size=\"20\" color=\"#34C759\"></uni-icons>\n\t\t\t\t\t\t\t<text class=\"empty-text\">暂无智能提醒</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 提醒列表 -->\n\t\t\t\t\t\t<view v-else class=\"reminder-item\" :class=\"reminder.type\" v-for=\"(reminder, index) in smartReminders\" :key=\"index\">\n\t\t\t\t\t\t\t<view class=\"reminder-icon\" :class=\"reminder.type\">\n\t\t\t\t\t\t\t\t<!-- 使用CSS文字符号，简洁美观 -->\n\t\t\t\t\t\t\t\t<text class=\"icon-text\">{{ reminder.iconSymbol }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"reminder-content\">\n\t\t\t\t\t\t\t\t<text class=\"reminder-title\">{{ reminder.title }}</text>\n\t\t\t\t\t\t\t\t<text class=\"reminder-desc\">{{ reminder.description }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { callCloudFunction } from '@/utils/auth.js';\n\nexport default {\n\tcomponents: {\n\t\t// uni-icons 组件会自动注册，无需手动导入\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tcurrentTab: 0,\n\t\t\ttabs: [\n\t\t\t\t{ name: '6S首页', key: 'home' }\n\t\t\t],\n\t\t\tselectedTimeFilter: 'week',\n\t\t\t\n\t\t\t// 页面状态\n\t\t\tloading: false,\n\t\t\tdataLoaded: false,\n\t\t\tloadError: false,\n\t\t\tactivitiesLoading: true,  // 初始状态为加载中\n\t\t\tremindersLoading: true,   // 初始状态为加载中\n\t\t\t\n\t\t\t// 性能优化缓存\n\t\t\tprocessCache: null,\n\t\t\ttimeCache: null,\n\t\t\t\n\t\t\t// 数据结构\n\t\t\tstats: {\n\t\t\t\ttotalAreas: 0,\n\t\t\t\tcompletedAreas: 0,\n\t\t\t\tfoundIssues: 0,\n\t\t\t\taverageRating: 0,\n\t\t\t\tcompletionRate: 0,\n\t\t\t\turgentIssues: 0\n\t\t\t},\n\t\t\trecentActivities: [],\n\t\t\tsmartReminders: []\n\t\t}\n\t},\n\n\tcomputed: {\n\t\tuserInfo() {\n\t\t\ttry {\n\t\t\t\tconst currentUserInfo = uniCloud.getCurrentUserInfo();\n\t\t\t\tconst storedUserInfo = uni.getStorageSync('uni-id-pages-userInfo') || {};\n\t\t\t\t// 合并云端用户信息和本地存储的详细信息\n\t\t\t\treturn {\n\t\t\t\t\t...storedUserInfo,\n\t\t\t\t\t...currentUserInfo\n\t\t\t\t};\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取用户信息失败:', error);\n\t\t\t\treturn uni.getStorageSync('uni-id-pages-userInfo') || {};\n\t\t\t}\n\t\t},\n\n\t\thasInspectionPermission() {\n\t\t\tconst role = this.userInfo.role;\n\t\t\treturn ['supervisor', 'PM', 'GM', 'admin'].includes(role);\n\t\t},\n\t\t\n\t\t// 获取当前时间范围的显示文本\n\t\tcurrentTimeRangeText() {\n\t\t\tif (!this.timeCache) return '本周';\n\t\t\tconst { currentYear, currentMonth, currentWeekRange } = this.timeCache;\n\t\t\t\n\t\t\tswitch (this.selectedTimeFilter) {\n\t\t\t\tcase 'today':\n\t\t\t\t\treturn `${currentYear}年${currentMonth}月${new Date().getDate()}日`;\n\t\t\t\tcase 'week':\n\t\t\t\t\treturn `${currentWeekRange.start} - ${currentWeekRange.end}`;\n\t\t\t\tcase 'month':\n\t\t\t\t\treturn `${currentYear}年${currentMonth}月`;\n\t\t\t\tdefault:\n\t\t\t\t\treturn `${currentWeekRange.start} - ${currentWeekRange.end}`;\n\t\t\t}\n\t\t}\n\t},\n\n\tonLoad() {\n\t\tthis.initCaches();\n\t\tthis.loadPageDataOptimized();\n\t\t\n\t\t// 监听数据更新事件\n\t\tuni.$on('cleaningRecordUpdated', this.handleDataUpdated);\n\t\tuni.$on('rectificationRecordUpdated', this.handleDataUpdated);\n\t\tuni.$on('inspectionRecordUpdated', this.handleDataUpdated);\n\t},\n\n\tonUnload() {\n\t\t// 移除事件监听\n\t\tuni.$off('cleaningRecordUpdated', this.handleDataUpdated);\n\t\tuni.$off('rectificationRecordUpdated', this.handleDataUpdated);\n\t\tuni.$off('inspectionRecordUpdated', this.handleDataUpdated);\n\t},\n\n\tonShow() {\n\t\t// 页面重新显示时，如果数据已加载则进行静默刷新\n\t\tif (this.dataLoaded && !this.loading) {\n\t\t\tthis.silentRefreshData();\n\t\t}\n\t},\n\n\tmethods: {\n\t\t// 初始化缓存\n\t\tinitCaches() {\n\t\t\tthis.initProcessCache();\n\t\t\tthis.initTimeCache();\n\t\t},\n\t\t\n\t\t// 初始化处理缓存\n\t\tinitProcessCache() {\n\t\t\tif (!this.processCache) {\n\t\t\t\tthis.processCache = {\n\t\t\t\t\t// 活动类型映射\n\t\t\t\t\tactivityTypeMap: {\n\t\t\t\t\t\t'cleaning_completed': {\n\t\t\t\t\t\t\ttype: 'success',\n\t\t\t\t\t\t\ticonType: 'checkmarkempty',\n\t\t\t\t\t\t\ticonColor: '#34C759'\n\t\t\t\t\t\t},\n\t\t\t\t\t\t'issue_found': {\n\t\t\t\t\t\t\ttype: 'warning',\n\t\t\t\t\t\t\ticonType: 'info',\n\t\t\t\t\t\t\ticonColor: '#FF9500'\n\t\t\t\t\t\t},\n\t\t\t\t\t\t'inspection_completed': {\n\t\t\t\t\t\t\ttype: 'info',\n\t\t\t\t\t\t\ticonType: 'search',\n\t\t\t\t\t\t\ticonColor: '#007AFF'\n\t\t\t\t\t\t},\n\t\t\t\t\t\t'rectification_completed': {\n\t\t\t\t\t\t\ttype: 'success',\n\t\t\t\t\t\t\ticonType: 'checkmarkempty',\n\t\t\t\t\t\t\ticonColor: '#34C759'\n\t\t\t\t\t\t},\n\t\t\t\t\t\t'area_overdue': {\n\t\t\t\t\t\t\ttype: 'danger',\n\t\t\t\t\t\t\ticonType: 'closeempty',\n\t\t\t\t\t\t\ticonColor: '#FF3B30'\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\t\n\t\t\t\t\t// 提醒类型映射\n\t\t\t\t\treminderTypeMap: {\n\t\t\t\t\t\t'pending_cleaning': {\n\t\t\t\t\t\t\ttype: 'info',\n\t\t\t\t\t\t\ticonSymbol: 'i',\n\t\t\t\t\t\t\ttitle: '清理提醒'\n\t\t\t\t\t\t},\n\t\t\t\t\t\t'overdue_cleaning': {\n\t\t\t\t\t\t\ttype: 'warning',\n\t\t\t\t\t\t\ticonSymbol: '⚠',\n\t\t\t\t\t\t\ttitle: '清理逾期'\n\t\t\t\t\t\t},\n\t\t\t\t\t\t'pending_rectification': {\n\t\t\t\t\t\t\ttype: 'warning',\n\t\t\t\t\t\t\ticonSymbol: '!',\n\t\t\t\t\t\t\ttitle: '整改提醒'\n\t\t\t\t\t\t},\n\t\t\t\t\t\t'overdue_rectification': {\n\t\t\t\t\t\t\ttype: 'danger',\n\t\t\t\t\t\t\ticonSymbol: '⚠',\n\t\t\t\t\t\t\ttitle: '整改逾期'\n\t\t\t\t\t\t},\n\t\t\t\t\t\t'inspection_due': {\n\t\t\t\t\t\t\ttype: 'info',\n\t\t\t\t\t\t\ticonSymbol: '○',\n\t\t\t\t\t\t\ttitle: '检查提醒'\n\t\t\t\t\t\t},\n\t\t\t\t\t\t'missed_inspection': {\n\t\t\t\t\t\t\ttype: 'danger',\n\t\t\t\t\t\t\ticonSymbol: '⚠',\n\t\t\t\t\t\t\ttitle: '漏检提醒'\n\t\t\t\t\t\t},\n\t\t\t\t\t\t'overdue_review': {\n\t\t\t\t\t\t\ttype: 'danger',\n\t\t\t\t\t\t\ticonSymbol: '!',\n\t\t\t\t\t\t\ttitle: '复查逾期'\n\t\t\t\t\t\t},\n\t\t\t\t\t\t'not_cleaned': {\n\t\t\t\t\t\t\ttype: 'warning',\n\t\t\t\t\t\t\ticonSymbol: '●',\n\t\t\t\t\t\t\ttitle: '未打扫提醒'\n\t\t\t\t\t\t},\n\t\t\t\t\t\t'all_good': {\n\t\t\t\t\t\t\ttype: 'success',\n\t\t\t\t\t\t\ticonSymbol: '✓',\n\t\t\t\t\t\t\ttitle: '状态良好'\n\t\t\t\t\t\t},\n\t\t\t\t\t\t'no_areas': {\n\t\t\t\t\t\t\ttype: 'info',\n\t\t\t\t\t\t\ticonSymbol: '?',\n\t\t\t\t\t\t\ttitle: '系统提示'\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\t\n\t\t\t\t\t// 时间格式化器\n\t\t\t\t\ttimeFormatter: this.createTimeFormatter()\n\t\t\t\t};\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 初始化时间缓存\n\t\tinitTimeCache() {\n\t\t\tif (!this.timeCache) {\n\t\t\t\tconst now = new Date();\n\t\t\t\tthis.timeCache = {\n\t\t\t\t\tnow,\n\t\t\t\t\tcurrentYear: now.getFullYear(),\n\t\t\t\t\tcurrentMonth: now.getMonth() + 1,\n\t\t\t\t\tcurrentWeekRange: this.getCurrentWeekRange(now),\n\t\t\t\t\tlastUpdateTime: now.getTime()\n\t\t\t\t};\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 创建时间格式化器\n\t\tcreateTimeFormatter() {\n\t\t\treturn (dateString) => {\n\t\t\t\tif (!dateString) return '--';\n\t\t\t\ttry {\n\t\t\t\t\tconst date = new Date(dateString);\n\t\t\t\t\tif (isNaN(date.getTime())) return '--';\n\t\t\t\t\t\n\t\t\t\t\tconst now = new Date();\n\t\t\t\t\tconst diffInHours = Math.floor((now - date) / (1000 * 60 * 60));\n\t\t\t\t\t\n\t\t\t\t\tif (diffInHours < 1) {\n\t\t\t\t\t\tconst diffInMinutes = Math.floor((now - date) / (1000 * 60));\n\t\t\t\t\t\treturn `${diffInMinutes}分钟前`;\n\t\t\t\t\t} else if (diffInHours < 24) {\n\t\t\t\t\t\treturn `${diffInHours}小时前`;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconst diffInDays = Math.floor(diffInHours / 24);\n\t\t\t\t\t\tif (diffInDays === 1) return '昨天';\n\t\t\t\t\t\tif (diffInDays < 7) return `${diffInDays}天前`;\n\t\t\t\t\t\treturn `${date.getMonth() + 1}月${date.getDate()}日`;\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\treturn '--';\n\t\t\t\t}\n\t\t\t};\n\t\t},\n\t\t\n\t\t// 获取当前周的起始和结束日期\n\t\tgetCurrentWeekRange(inputDate = new Date()) {\n\t\t\tconst date = new Date(inputDate);\n\t\t\tconst day = date.getDay();\n\t\t\tconst diff = date.getDate() - day + (day === 0 ? -6 : 1);\n\t\t\t\n\t\t\tconst monday = new Date(date);\n\t\t\tmonday.setDate(diff);\n\t\t\t\n\t\t\tconst sunday = new Date(date);\n\t\t\tsunday.setDate(diff + 6);\n\t\t\t\n\t\t\treturn {\n\t\t\t\tstart: `${monday.getMonth() + 1}月${monday.getDate()}日`,\n\t\t\t\tend: `${sunday.getMonth() + 1}月${sunday.getDate()}日`,\n\t\t\t\tmonday: monday,\n\t\t\t\tsunday: sunday\n\t\t\t};\n\t\t},\n\n\t\t// 优化的页面数据加载\n\t\tasync loadPageDataOptimized() {\n\t\t\tthis.loading = true;\n\t\t\tthis.loadError = false;\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// Phase 1: 快速加载统计数据\n\t\t\t\tawait this.loadStatsDataOptimized();\n\t\t\t\t\n\t\t\t\t// 立即显示基本内容\n\t\t\t\tthis.loading = false;\n\t\t\t\tthis.dataLoaded = true;\n\t\t\t\t\n\t\t\t\t// Phase 2: 异步加载次要数据\n\t\t\t\tthis.loadSecondaryDataAsync();\n\t\t\t} catch (error) {\n\t\t\t\tthis.loading = false;\n\t\t\t\tthis.loadError = true;\n\t\t\t\tthis.handleLoadError(error);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 异步加载次要数据\n\t\tasync loadSecondaryDataAsync() {\n\t\t\ttry {\n\t\t\t\tawait Promise.all([\n\t\t\t\t\tthis.loadRecentActivitiesOptimized(),\n\t\t\t\t\tthis.loadSmartRemindersOptimized()\n\t\t\t\t]);\n\t\t\t} catch (error) {\n\t\t\t\t// 静默处理次要数据加载失败\n\t\t\t} finally {\n\t\t\t\t// 确保加载状态被清除\n\t\t\t\tthis.activitiesLoading = false;\n\t\t\t\tthis.remindersLoading = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 优化的统计数据加载\n\t\tasync loadStatsDataOptimized() {\n\t\t\ttry {\n\t\t\t\tconst result = await callCloudFunction('hygiene-area-management', {\n\t\t\t\t\taction: 'getOverviewStats',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\ttimeFilter: this.selectedTimeFilter,\n\t\t\t\t\t\tincludeDetails: false // 只要统计数据，不要详细列表\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (result && result.success && result.data) {\n\t\t\t\t\tthis.stats = {\n\t\t\t\t\t\ttotalAreas: result.data.totalAreas || 0,\n\t\t\t\t\t\tcompletedAreas: result.data.completedAreas || 0,\n\t\t\t\t\t\tfoundIssues: result.data.pendingRectifications || 0,\n\t\t\t\t\t\taverageRating: result.data.averageRating || 0,\n\t\t\t\t\t\tcompletionRate: result.data.completionRate || 0,\n\t\t\t\t\t\turgentIssues: result.data.overdueAreas || 0\n\t\t\t\t\t};\n\t\t\t\t} else {\n\t\t\t\t\tthrow new Error('获取统计数据失败');\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\t// 加载失败时使用默认值\n\t\t\t\tthis.stats = {\n\t\t\t\t\ttotalAreas: 0,\n\t\t\t\t\tcompletedAreas: 0,\n\t\t\t\t\tfoundIssues: 0,\n\t\t\t\t\taverageRating: 0,\n\t\t\t\t\tcompletionRate: 0,\n\t\t\t\t\turgentIssues: 0\n\t\t\t\t};\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 优化的最新动态加载\n\t\tasync loadRecentActivitiesOptimized() {\n\t\t\ttry {\n\t\t\t\tconst result = await callCloudFunction('hygiene-area-management', {\n\t\t\t\t\taction: 'getRecentActivities',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\tlimit: 5,\n\t\t\t\t\t\ttimeFilter: 'week' // 最新动态始终显示本周\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (result && result.success && result.data) {\n\t\t\t\t\tthis.recentActivities = this.processActivitiesData(result.data);\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tthis.recentActivities = [];\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 优化的智能提醒加载\n\t\tasync loadSmartRemindersOptimized() {\n\t\t\ttry {\n\t\t\t\tconst userRole = this.userInfo.role || 'employee';\n\t\t\t\t\n\t\t\t\tconst result = await callCloudFunction('hygiene-area-management', {\n\t\t\t\t\taction: 'getSmartReminders',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\tuserRole: userRole,\n\t\t\t\t\t\tlimit: 3\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (result && result.success && result.data) {\n\t\t\t\t\tthis.smartReminders = this.processRemindersData(result.data);\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tthis.smartReminders = [];\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 处理活动数据\n\t\tprocessActivitiesData(activities) {\n\t\t\tif (!Array.isArray(activities)) return [];\n\t\t\t\n\t\t\treturn activities.map(activity => {\n\t\t\t\tconst typeConfig = this.processCache.activityTypeMap[activity.type] || {\n\t\t\t\t\ttype: 'info',\n\t\t\t\t\ticonType: 'info',\n\t\t\t\t\ticonColor: '#8E8E93'\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\treturn {\n\t\t\t\t\t...typeConfig,\n\t\t\t\t\ttitle: activity.title || '未知活动',\n\t\t\t\t\ttime: this.processCache.timeFormatter(activity.created_at) || '--'\n\t\t\t\t};\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 处理提醒数据\n\t\tprocessRemindersData(reminders) {\n\t\t\tif (!Array.isArray(reminders)) return [];\n\t\t\t\n\t\t\treturn reminders.map(reminder => {\n\t\t\t\tconst typeConfig = this.processCache.reminderTypeMap[reminder.type] || {\n\t\t\t\t\ttype: 'info',\n\t\t\t\t\ticonSymbol: 'i',\n\t\t\t\t\ttitle: '系统提醒'\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\treturn {\n\t\t\t\t\t...typeConfig,\n\t\t\t\t\tdescription: reminder.description || '暂无详细信息'\n\t\t\t\t};\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 静默刷新数据\n\t\tasync silentRefreshData() {\n\t\t\tif (this.loading) return;\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 更新时间缓存\n\t\t\t\tthis.timeCache = null;\n\t\t\t\tthis.initTimeCache();\n\t\t\t\t\n\t\t\t\t// 静默刷新统计数据\n\t\t\t\tawait this.loadStatsDataOptimized();\n\t\t\t\t\n\t\t\t\t// 静默刷新次要数据\n\t\t\t\tthis.loadSecondaryDataAsync();\n\t\t\t} catch (error) {\n\t\t\t\t// 静默处理错误\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 错误处理\n\t\thandleLoadError(error) {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '数据加载失败',\n\t\t\t\ticon: 'none',\n\t\t\t\tduration: 2000\n\t\t\t});\n\t\t},\n\n\t\t// 时间筛选切换\n\t\tswitchTimeFilter(filter) {\n\t\t\tif (this.selectedTimeFilter === filter) return;\n\t\t\t\n\t\t\tthis.selectedTimeFilter = filter;\n\t\t\t\n\t\t\t// 设置加载状态\n\t\t\tthis.activitiesLoading = true;\n\t\t\tthis.remindersLoading = true;\n\t\t\t\n\t\t\t// 重新加载统计数据\n\t\t\tthis.loadStatsDataOptimized().catch(error => {\n\t\t\t\tthis.handleLoadError(error);\n\t\t\t});\n\t\t\t\n\t\t\t// 重新加载次要数据\n\t\t\tthis.loadSecondaryDataAsync();\n\t\t},\n\n\t\tgetTimeRangeTitle() {\n\t\t\tswitch (this.selectedTimeFilter) {\n\t\t\t\tcase 'today': return '今日';\n\t\t\t\tcase 'week': return '本周';\n\t\t\t\tcase 'month': return '本月';\n\t\t\t\tdefault: return '本周';\n\t\t\t}\n\t\t},\n\n\t\t// 处理数据更新事件\n\t\thandleDataUpdated(data) {\n\t\t\t// 通过事件已经收到更新通知，进行静默刷新\n\t\t\tthis.silentRefreshData();\n\t\t},\n\n\t\t// 快捷操作方法\n\t\tgoToMyArea() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/6s_pkg/my-areas'\n\t\t\t});\n\t\t},\n\n\t\tgoToPendingTasks() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/6s_pkg/area-inspection'\n\t\t\t});\n\t\t},\n\n\t\tuploadPhoto() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/6s_pkg/monthly-check'\n\t\t\t});\n\t\t},\n\n\t\tmanageBaseData() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/6s_pkg/data-manage'\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\" scoped>\n/* 防止水平滚动的全局样式 */\n* {\n\tbox-sizing: border-box;\n}\n\n/* uni-icons样式优化 */\n.sixs-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\n\twidth: 100%;\n\toverflow-x: hidden; /* 防止水平滚动 */\n\tbox-sizing: border-box;\n}\n\n.top-tabs {\n\tdisplay: flex;\n\tbackground: #FFFFFF;\n\tborder-bottom: 1px solid #E5E5E5;\n\n\t.tab-item {\n\t\tflex: 1;\n\t\ttext-align: center;\n\t\tpadding: 15px 0;\n\t\tfont-size: 15px;\n\t\tcolor: #666;\n\t\tposition: relative;\n\n\t\t&.active {\n\t\t\tcolor: #007AFF;\n\t\t\tfont-weight: 500;\n\n\t\t\t&::after {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: absolute;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 50%;\n\t\t\t\ttransform: translateX(-50%);\n\t\t\t\twidth: 30px;\n\t\t\t\theight: 2px;\n\t\t\t\tbackground: #007AFF;\n\t\t\t}\n\t\t}\n\t}\n}\n\n.content-container {\n\twidth: 100%;\n\tmax-width: 100%; /* 移除95%限制，使用全宽 */\n\tmargin: 0;\n\tpadding: 0; /* 移除内边距，由子元素控制 */\n\tposition: relative;\n\tz-index: 5;\n\tbox-sizing: border-box;\n\toverflow-x: hidden; /* 防止水平滚动 */\n}\n\n.card-body {\n\tpadding: 20px;\n\tposition: relative;\n\n\t&::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\theight: 1px;\n\t\tbackground: linear-gradient(90deg, transparent, rgba(0, 122, 255, 0.3), transparent);\n\t}\n}\n\n.tab-content {\n\tpadding-bottom: 20px;\n}\n\n.section {\n\tbackground: #FFFFFF;\n\tborder-radius: 12px;\n\tmargin: 16px 12px; /* 减少左右边距，防止超出屏幕 */\n\toverflow: hidden;\n\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\n\tborder: 1px solid rgba(0, 0, 0, 0.06);\n\tbox-sizing: border-box;\n}\n\n.section-header {\n\tpadding: 24px 24px 16px;\n\tborder-bottom: 1px solid rgba(0, 0, 0, 0.06);\n\n\t.header-content {\n\t\tdisplay: flex;\n\t\tflex-direction: row; /* 水平排列 */\n\t\talign-items: baseline; /* 基线对齐，保持文字对齐 */\n\t\tjustify-content: space-between; /* 左右对齐 */\n\t}\n\n\t.section-title {\n\t\tfont-size: 17px;\n\t\tfont-weight: 600;\n\t\tcolor: #1D1D1F;\n\t\tmargin-bottom: 0; /* 移除margin，使用gap控制 */\n\t}\n\n\t.section-subtitle {\n\t\tfont-size: 13px;\n\t\tcolor: #8E8E93;\n\t}\n}\n\n/* 快捷操作样式 - 完全按照demo实现 */\n.quick-actions-header {\n\tpadding: 24px 24px 16px;\n\tborder-bottom: 1px solid rgba(0, 0, 0, 0.06);\n\n\t.header-content {\n\t\tdisplay: flex;\n\t\tflex-direction: row; /* 水平排列 */\n\t\talign-items: baseline; /* 基线对齐 */\n\t\tjustify-content: space-between; /* 左右对齐 */\n\t}\n\n\t.quick-actions-title {\n\t\tfont-size: 17px;\n\t\tfont-weight: 600;\n\t\tcolor: #1D1D1F;\n\t\tdisplay: block;\n\t\tmargin-bottom: 0; /* 移除margin */\n\t}\n\n\t.quick-actions-subtitle {\n\t\tfont-size: 13px;\n\t\tcolor: #8E8E93;\n\t\tdisplay: block;\n\t}\n}\n\n.quick-actions-grid {\n\tdisplay: grid;\n\tgrid-template-columns: 1fr 1fr;\n\tgap: 12px;\n\tpadding: 16px; /* 减少内边距，防止超出 */\n\tbox-sizing: border-box;\n}\n\n.quick-action-btn {\n\tdisplay: flex;\n\tflex-direction: row; /* 水平布局 */\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 8px 16px; /* 进一步减少内边距 */\n\tborder-radius: 8px;\n\ttransition: all 0.2s ease;\n\theight: 36px; /* 进一步降低高度，更扁 */\n\tmin-width: 0; /* 允许文字换行 */\n\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.04); /* 更轻柔的阴影，与其他卡片一致 */\n\tborder: none; /* 移除边框 */\n\n\t/* 按钮内容容器 */\n\t.btn-content {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tgap: 12px; /* 使用gap控制图标和文字间距 */\n\t}\n\n\t/* uni-icons 图标样式 */\n\t.uni-icons {\n\t\tflex-shrink: 0;\n\t}\n\n\t.btn-text {\n\t\tfont-size: 14px; /* 适配更低的按钮高度 */\n\t\tfont-weight: 600;\n\t\tcolor: white;\n\t\ttext-align: center;\n\t\tline-height: 1.2;\n\t}\n\n\t&.primary {\n\t\tbackground: #007AFF; /* 主题蓝色，突出常用功能 */\n\t}\n\n\t&.warning {\n\t\tbackground: #F59E0B; /* 橙黄色，更接近截图中的\"责任区检查\"按钮 */\n\t}\n\n\t&.success {\n\t\tbackground: #10B981; /* 绿色，更接近截图中的\"上传照片\"按钮 */\n\t}\n\n\t&.purple {\n\t\tbackground: #8B5CF6; /* 紫色，更接近截图中的\"清理历史\"按钮 */\n\t}\n\n\t&:active {\n\t\ttransform: scale(0.98);\n\t\topacity: 0.8;\n\t}\n}\n\n/* 新的概览样式 - 按照设计图 */\n.overview-header {\n\tpadding: 24px 24px 16px;\n\tborder-bottom: 1px solid rgba(0, 0, 0, 0.06);\n\n\t.header-content {\n\t\tdisplay: flex;\n\t\tflex-direction: row; /* 水平排列 */\n\t\talign-items: baseline; /* 基线对齐 */\n\t\tjustify-content: space-between; /* 左右对齐 */\n\t}\n\n\t.overview-title {\n\t\tfont-size: 17px;\n\t\tfont-weight: 600;\n\t\tcolor: #1D1D1F;\n\t\tmargin-bottom: 0; /* 移除margin */\n\t\tdisplay: block;\n\t}\n\n\t.overview-subtitle {\n\t\tfont-size: 13px;\n\t\tcolor: #8E8E93;\n\t\tdisplay: block;\n\t}\n}\n\n.overview-stats {\n\tdisplay: grid;\n\tgrid-template-columns: 1fr 1fr;\n\tgap: 0;\n\tpadding: 12px 16px; /* 减少上下内边距，保持左右间距 */\n\n\t.stat-item {\n\t\ttext-align: center;\n\t\tpadding: 8px 6px; /* 减少内边距，降低高度间距 */\n\n\t\t.stat-number {\n\t\t\tdisplay: block;\n\t\t\tfont-size: 22px; /* 进一步减少到22px，更接近demo */\n\t\t\tfont-weight: 700;\n\t\t\tmargin-bottom: 2px; /* 减少数字和标签间距 */\n\t\t\tline-height: 1;\n\n\t\t\t&.blue {\n\t\t\t\tcolor: #007AFF;\n\t\t\t}\n\n\t\t\t&.green {\n\t\t\t\tcolor: #34C759;\n\t\t\t}\n\n\t\t\t&.orange {\n\t\t\t\tcolor: #FF9500;\n\t\t\t}\n\n\t\t\t&.red {\n\t\t\t\tcolor: #FF3B30;\n\t\t\t}\n\t\t}\n\n\t\t.stat-label {\n\t\t\tfont-size: 11px; /* 进一步减少到11px，更精致 */\n\t\t\tcolor: #8E8E93;\n\t\t\tfont-weight: 500;\n\t\t}\n\t}\n}\n\n.header-decoration {\n\tdisplay: flex;\n\tgap: 4px;\n\talign-items: center;\n\n\t.decoration-dot {\n\t\twidth: 6px;\n\t\theight: 6px;\n\t\tborder-radius: 50%;\n\t\tbackground: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n\t\tanimation: pulse 2s ease-in-out infinite;\n\n\t\t&:nth-child(2) {\n\t\t\tanimation-delay: 0.3s;\n\t\t}\n\n\t\t&:nth-child(3) {\n\t\t\tanimation-delay: 0.6s;\n\t\t}\n\t}\n}\n\n@keyframes pulse {\n\t0%, 100% {\n\t\topacity: 0.4;\n\t\ttransform: scale(1);\n\t}\n\t50% {\n\t\topacity: 1;\n\t\ttransform: scale(1.2);\n\t}\n}\n\n/* 图标容器样式优化 */\n.activity-icon, .reminder-icon {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n/* 最新动态样式 */\n.activity-list {\n\tpadding: 0 20px 20px;\n}\n\n.activity-item {\n\tdisplay: flex;\n\talign-items: flex-start;\n\tpadding: 16px 0;\n\tborder-bottom: 1px solid rgba(0, 0, 0, 0.06);\n\n\t&:last-child {\n\t\tborder-bottom: none;\n\t}\n\n\t.activity-icon {\n\t\twidth: 40px;\n\t\theight: 40px;\n\t\tborder-radius: 8px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-right: 16px;\n\t\tflex-shrink: 0;\n\n\t\t&.success {\n\t\t\tbackground: rgba(52, 199, 89, 0.15);\n\t\t}\n\n\t\t&.warning {\n\t\t\tbackground: rgba(255, 149, 0, 0.15);\n\t\t}\n\n\t\t&.info {\n\t\t\tbackground: rgba(0, 122, 255, 0.15);\n\t\t}\n\t}\n\n\t.activity-content {\n\t\tflex: 1;\n\n\t\t.activity-title {\n\t\t\tfont-size: 15px;\n\t\t\tcolor: #1D1D1F;\n\t\t\tline-height: 1.4;\n\t\t\tmargin-bottom: 4px;\n\t\t\tdisplay: block;\n\t\t\tfont-weight: 600; /* 使用粗体，符合demo */\n\t\t}\n\n\t\t.activity-time {\n\t\t\tfont-size: 13px;\n\t\t\tcolor: #8E8E93;\n\t\t}\n\t}\n}\n\n/* 加载状态样式 */\n.loading-container {\n\tpadding: 40rpx 32rpx;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n}\n\n.loading-content {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 16rpx;\n}\n\n.loading-spinner {\n\twidth: 40rpx;\n\theight: 40rpx;\n\tborder: 4rpx solid #E5E7EB;\n\tborder-top: 4rpx solid #007AFF;\n\tborder-radius: 50%;\n\tanimation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n\t0% { transform: rotate(0deg); }\n\t100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n\tfont-size: 28rpx;\n\tcolor: #8E8E93;\n}\n\n/* 错误状态样式 */\n.error-container {\n\tpadding: 40rpx 32rpx;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n}\n\n.error-content {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 16rpx;\n}\n\n.error-text {\n\tfont-size: 24rpx;\n\tcolor: #FF3B30;\n\ttext-align: center;\n}\n\n.retry-btn {\n\tpadding: 12rpx 24rpx;\n\tbackground: #007AFF;\n\tcolor: white;\n\tborder: none;\n\tborder-radius: 16rpx;\n\tfont-size: 24rpx;\n\t\n\t&:active {\n\t\topacity: 0.8;\n\t}\n}\n\n/* 空状态样式 */\n.empty-state {\n\tpadding: 40rpx 32rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 12rpx;\n}\n\n.empty-text {\n\tfont-size: 24rpx;\n\tcolor: #8E8E93;\n\ttext-align: center;\n}\n\n/* 智能提醒样式 - 完全按照demo的设计实现 */\n.reminder-list {\n\tpadding: 16px;\n}\n\n.reminder-item {\n\tdisplay: flex;\n\talign-items: flex-start;\n\tpadding: 12px;\n\tmargin-bottom: 12px;\n\tborder-radius: 8px;\n\n\t&:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.reminder-icon {\n\t\twidth: 24px;\n\t\theight: 24px;\n\t\tborder-radius: 12px; /* 完全圆形 - demo中是w-6 h-6 */\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-right: 12px;\n\t\tflex-shrink: 0;\n\t\t/* 移除margin-top，确保完全垂直居中 */\n\n\t\t.icon-text {\n\t\t\tfont-size: 12px;\n\t\t\tfont-weight: 600;\n\t\t\tline-height: 1; /* 确保文字垂直居中 */\n\t\t\ttext-align: center;\n\t\t}\n\n\t\t&.info {\n\t\t\tbackground: rgba(0, 122, 255, 0.2); /* demo中的bg-primary/20 */\n\n\t\t\t.icon-text {\n\t\t\t\tcolor: #007AFF; /* demo中的text-primary */\n\t\t\t}\n\t\t}\n\n\t\t&.warning {\n\t\t\tbackground: rgba(255, 149, 0, 0.2); /* 橙色警告 */\n\n\t\t\t.icon-text {\n\t\t\t\tcolor: #FF9500;\n\t\t\t}\n\t\t}\n\t\t\n\t\t&.danger {\n\t\t\tbackground: rgba(255, 59, 48, 0.2); /* 红色危险 */\n\n\t\t\t.icon-text {\n\t\t\t\tcolor: #FF3B30;\n\t\t\t}\n\t\t}\n\t\t\n\t\t&.success {\n\t\t\tbackground: rgba(34, 197, 94, 0.2); /* 绿色背景 */\n\n\t\t\t.icon-text {\n\t\t\t\tcolor: #22C55E; /* 绿色文字 */\n\t\t\t}\n\t\t}\n\t}\n\n\t.reminder-content {\n\t\tflex: 1;\n\n\t\t.reminder-title {\n\t\t\tfont-size: 14px;\n\t\t\tcolor: #1C1C1E; /* demo中的text-dark */\n\t\t\tfont-weight: 600; /* 使用粗体，符合demo */\n\t\t\tmargin-bottom: 4px;\n\t\t\tdisplay: block;\n\t\t}\n\n\t\t.reminder-desc {\n\t\t\tfont-size: 13px;\n\t\t\tcolor: #636366; /* demo中的text-gray-600 */\n\t\t\tline-height: 1.4;\n\t\t}\n\t}\n\n\t/* demo风格的背景色 - 完全按照demo实现 */\n\t&.info {\n\t\tbackground: rgba(59, 130, 246, 0.05); /* demo中的bg-blue-50 */\n\t}\n\n\t&.warning {\n\t\tbackground: rgba(255, 149, 0, 0.05); /* 橙色背景 */\n\t}\n\t\n\t&.danger {\n\t\tbackground: rgba(255, 59, 48, 0.05); /* 红色背景 */\n\t}\n\t\n\t&.success {\n\t\tbackground: rgba(34, 197, 94, 0.05); /* 绿色背景 */\n\t}\n}\n</style>", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=96da5e8e&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=96da5e8e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775853882\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}