<view class="page-container data-v-6e3331a0"><block wx:if="{{loading}}"><view class="loading-container data-v-6e3331a0"><view class="loading-content data-v-6e3331a0"><view class="loading-spinner data-v-6e3331a0"></view><text class="loading-text data-v-6e3331a0">加载整改任务信息中...</text></view></view></block><block wx:else><block wx:if="{{loadError}}"><view class="error-container data-v-6e3331a0"><view class="error-content data-v-6e3331a0"><uni-icons vue-id="53e6ffd6-1" type="info" size="48" color="#FF3B30" class="data-v-6e3331a0" bind:__l="__l"></uni-icons><text class="error-text data-v-6e3331a0">{{loadError}}</text><button data-event-opts="{{[['tap',[['retryLoad',['$event']]]]]}}" class="retry-button data-v-6e3331a0" bindtap="__e">重新加载</button></view></view></block><block wx:else><view class="card data-v-6e3331a0"><view class="card-header data-v-6e3331a0"><view class="header-content data-v-6e3331a0"><view class="card-title data-v-6e3331a0">整改任务</view><view class="card-subtitle data-v-6e3331a0">{{taskInfo.area+" - "+(taskInfo.isPublic?'公共责任区':'固定责任区')}}</view></view></view><view class="card-body data-v-6e3331a0"><view class="task-info data-v-6e3331a0"><view class="info-item data-v-6e3331a0"><text class="info-label data-v-6e3331a0">问题描述：</text><text class="info-value data-v-6e3331a0">{{taskInfo.problemDescription}}</text></view><view class="info-item data-v-6e3331a0"><text class="info-label data-v-6e3331a0">发现时间：</text><text class="info-value data-v-6e3331a0">{{$root.m0}}</text></view><view class="info-item data-v-6e3331a0"><text class="info-label data-v-6e3331a0">检查员：</text><text class="info-value data-v-6e3331a0">{{taskInfo.inspector}}</text></view></view></view></view><block wx:if="{{taskInfo.reviewComments}}"><view class="card review-feedback-card data-v-6e3331a0"><view class="card-header data-v-6e3331a0"><view class="card-title data-v-6e3331a0">审核反馈</view></view><view class="card-body data-v-6e3331a0"><view class="feedback-content data-v-6e3331a0"><view class="feedback-item data-v-6e3331a0"><text class="feedback-label data-v-6e3331a0">审核结果：</text><view class="status-badge status-rejected data-v-6e3331a0">需重新整改</view></view><view class="feedback-item data-v-6e3331a0"><text class="feedback-label data-v-6e3331a0">审核意见：</text><text class="feedback-value data-v-6e3331a0">{{taskInfo.reviewComments}}</text></view><view class="feedback-item data-v-6e3331a0"><text class="feedback-label data-v-6e3331a0">审核时间：</text><text class="feedback-value data-v-6e3331a0">{{$root.m1}}</text></view><view class="feedback-item data-v-6e3331a0"><text class="feedback-label data-v-6e3331a0">审核人员：</text><text class="feedback-value data-v-6e3331a0">{{taskInfo.reviewer}}</text></view></view></view></view></block><block wx:if="{{$root.g0}}"><view class="card inspection-photos-card data-v-6e3331a0"><view class="card-header data-v-6e3331a0"><view class="card-title data-v-6e3331a0">问题照片</view><view class="card-subtitle data-v-6e3331a0">检查员发现问题时拍摄的现场照片</view></view><view class="card-body data-v-6e3331a0"><view class="inspection-photos-grid data-v-6e3331a0"><block wx:for="{{$root.l0}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view class="inspection-photo-item data-v-6e3331a0"><image src="{{photo.m2}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewInspectionPhoto',[index]]]]]}}" bindtap="__e" class="data-v-6e3331a0"></image></view></block></view></view></view></block><block wx:if="{{$root.g1}}"><view class="card previous-photos-card data-v-6e3331a0"><view class="card-header data-v-6e3331a0"><view class="card-title data-v-6e3331a0">之前的整改照片</view><view class="card-subtitle data-v-6e3331a0">供参考，检查员认为需要改进</view></view><view class="card-body data-v-6e3331a0"><view class="previous-photos-grid data-v-6e3331a0"><block wx:for="{{$root.l1}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view class="previous-photo-item data-v-6e3331a0"><image src="{{photo.$orig.url}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewPreviousPhoto',[index]]]]]}}" bindtap="__e" class="data-v-6e3331a0"></image><view class="photo-overlay data-v-6e3331a0"><text class="photo-time data-v-6e3331a0">{{photo.m3}}</text></view></view></block></view></view></view></block><view class="card data-v-6e3331a0"><view class="card-header data-v-6e3331a0"><view class="card-title data-v-6e3331a0">{{taskInfo.reviewComments?'重新整改':'整改记录'}}</view></view><view class="card-body data-v-6e3331a0"><view class="upload-section data-v-6e3331a0"><view class="section-header data-v-6e3331a0"><view class="section-title data-v-6e3331a0">上传整改照片</view><view data-event-opts="{{[['tap',[['toggleAutoUpload',['$event']]]]]}}" class="auto-upload-toggle data-v-6e3331a0" bindtap="__e"><view class="toggle-label data-v-6e3331a0">自动上传</view><view class="{{['toggle-switch','data-v-6e3331a0',(autoUpload)?'active':'']}}"><view class="toggle-circle data-v-6e3331a0"></view></view></view></view><view class="photo-grid data-v-6e3331a0"><block wx:for="{{$root.l2}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view class="photo-item data-v-6e3331a0"><image src="{{photo.m4}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewPhoto',[index]]]]]}}" bindtap="__e" class="data-v-6e3331a0"></image><block wx:if="{{photo.$orig.uploading}}"><view class="photo-uploading data-v-6e3331a0"><view class="upload-spinner data-v-6e3331a0"></view></view></block><block wx:else><block wx:if="{{photo.$orig.uploaded}}"><view class="photo-uploaded data-v-6e3331a0"><uni-icons vue-id="{{'53e6ffd6-2-'+index}}" type="checkmarkempty" size="16" color="white" class="data-v-6e3331a0" bind:__l="__l"></uni-icons></view></block></block><view data-event-opts="{{[['tap',[['deletePhoto',[index]]]]]}}" class="photo-delete data-v-6e3331a0" bindtap="__e"><uni-icons vue-id="{{'53e6ffd6-3-'+index}}" type="close" size="18" color="white" class="data-v-6e3331a0" bind:__l="__l"></uni-icons></view></view></block><block wx:if="{{$root.g2<12}}"><view data-event-opts="{{[['tap',[['addPhoto',['$event']]]]]}}" class="photo-add data-v-6e3331a0" bindtap="__e"><uni-icons vue-id="53e6ffd6-4" type="camera" size="32" color="#8E8E93" class="data-v-6e3331a0" bind:__l="__l"></uni-icons><text class="data-v-6e3331a0">添加照片</text></view></block></view><view class="photo-tip data-v-6e3331a0">最多可上传12张照片，建议拍摄整改前后对比照片</view></view><view class="remarks-section data-v-6e3331a0"><view class="section-title data-v-6e3331a0">整改说明</view><view class="remarks-input-container data-v-6e3331a0"><textarea class="remarks-input data-v-6e3331a0" placeholder="请详细描述整改过程和结果..." maxlength="200" data-event-opts="{{[['input',[['__set_model',['','remarks','$event',[]]],['handleRemarksInput',['$event']]]]]}}" value="{{remarks}}" bindinput="__e"></textarea><view class="char-count-overlay data-v-6e3331a0">{{remarksLength+"/200"}}</view></view></view></view></view></block></block><view class="button-container data-v-6e3331a0"><button class="{{['primary-button','data-v-6e3331a0',(submitting)?'loading':'']}}" disabled="{{$root.g3}}" data-event-opts="{{[['tap',[['submitRectification',['$event']]]]]}}" bindtap="__e"><block wx:if="{{submitting}}"><view class="button-loading data-v-6e3331a0"><view class="loading-spinner data-v-6e3331a0"></view><text class="data-v-6e3331a0">提交中...</text></view></block><block wx:else><text class="data-v-6e3331a0">提交整改记录</text></block></button></view></view>