require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/person-progress"],{"54a8":function(e,t,n){"use strict";n.r(t);var r=n("e5d3"),a=n("dd4e");for(var s in a)["default"].indexOf(s)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(s);n("f7d2");var i=n("828b"),o=Object(i["a"])(a["default"],r["b"],r["c"],!1,null,"4ed25f23",null,!1,r["a"],void 0);t["default"]=o.exports},"90da":function(e,t,n){},a56e:function(e,t,n){"use strict";(function(e){var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("7eb4")),s=r(n("7ca3")),i=r(n("34cf")),o=r(n("ee10"));function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){(0,s.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var l={name:"PersonProgress",data:function(){return{loading:!1,loadingText:"加载中...",personId:null,personName:"",currentFilter:"all",hasManagePermission:!1,displayLimit:6,displayStep:4,personData:{id:"",name:"",role:"",total:0,completed:0,inProgress:0,overdue:0,issues:[]},statusFilters:[{label:"全部",value:"all"},{label:"已分配",value:"assigned"},{label:"待整改",value:"pending"},{label:"整改中",value:"in_progress"},{label:"待检查",value:"pending_review"},{label:"检查通过",value:"approved"}],dataCache:new Map,cacheExpireTime:18e4,lastLoadTime:0}},computed:{completionRate:function(){return 0===this.personData.total?0:Math.round(this.personData.completed/this.personData.total*100)},filteredIssues:function(){var e=this;return"all"===this.currentFilter?this.personData.issues:this.personData.issues.filter((function(t){return t.status===e.currentFilter}))},displayedIssues:function(){return this.filteredIssues.slice(0,this.displayLimit)},hasMoreIssues:function(){return this.filteredIssues.length>this.displayLimit},remainingIssuesCount:function(){return this.filteredIssues.length-this.displayLimit}},onLoad:function(t){this.personId=t.id,this.personName=decodeURIComponent(t.name||"负责人"),this.loadPersonData(),this.checkUserPermissions(),e.$on("monthlyIssueUpdated",this.handleDataUpdated),e.$on("issueDraftUpdated",this.handleDataUpdated)},onUnload:function(){e.$off("monthlyIssueUpdated",this.handleDataUpdated),e.$off("issueDraftUpdated",this.handleDataUpdated)},onShow:function(){this.smartRefresh()},methods:{loadPersonData:function(){var t=this;return(0,o.default)(a.default.mark((function r(){var s,o,u,c,l,d,p,f,h,m,g;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.showLoading("加载个人进度数据..."),r.prev=1,s=n("882c"),s.callCloudFunction,r.next=5,Promise.all([t.loadPersonIssues(),t.loadPersonInfo()]);case 5:o=r.sent,u=(0,i.default)(o,2),c=u[0],l=u[1],d=c||[],p=l||{name:t.personName,role:"负责人"},f=d.length,h=d.filter((function(e){return"approved"===e.status})).length,m=d.filter((function(e){return"in_progress"===e.status})).length,g=d.filter((function(e){return"overdue"===e.status})).length,t.personData={id:t.personId,name:p.name||t.personName,role:p.role||"负责人",total:f,completed:h,inProgress:m,overdue:g,issues:d},t.updateFilterCounts(),r.next=25;break;case 19:r.prev=19,r.t0=r["catch"](1),console.error("加载个人进度数据失败:",r.t0),t.personData={id:t.personId,name:t.personName,role:"负责人",total:0,completed:0,inProgress:0,overdue:0,issues:[]},t.updateFilterCounts(),e.showToast({title:"加载数据失败",icon:"none"});case 25:return r.prev=25,t.hideLoading(),t.lastLoadTime=Date.now(),r.finish(25);case 29:case"end":return r.stop()}}),r,null,[[1,19,25,29]])})))()},checkUserPermissions:function(){var t=this;return(0,o.default)(a.default.mark((function n(){var r,s,i;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:try{r=e.getStorageSync("uni-id-pages-userInfo")||{},s=r.role||[],i=["admin","GM","Integrated","reviser"],t.hasManagePermission=s.some((function(e){return i.includes(e)}))}catch(a){console.error("获取用户权限失败:",a),t.hasManagePermission=!1}case 1:case"end":return n.stop()}}),n)})))()},smartRefresh:function(){var e=this;return(0,o.default)(a.default.mark((function t(){var n,r;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n=Date.now(),r=n-e.lastLoadTime,!(r>e.cacheExpireTime||0===e.personData.issues.length)){t.next=5;break}return t.next=5,e.loadPersonDataSilently();case 5:case"end":return t.stop()}}),t)})))()},loadPersonDataSilently:function(){var e=this;return(0,o.default)(a.default.mark((function t(){var r,s,o,u,l,d,p,f,h,m;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,r=n("882c"),r.callCloudFunction,t.next=4,Promise.all([e.loadPersonIssues(),e.loadPersonInfo()]);case 4:s=t.sent,o=(0,i.default)(s,2),u=o[0],l=o[1],u&&u.length>0&&(d=u,l||{name:e.personName,role:"负责人"},p=d.length,f=d.filter((function(e){return"approved"===e.status})).length,h=d.filter((function(e){return"in_progress"===e.status})).length,m=d.filter((function(e){return"overdue"===e.status})).length,e.personData=c(c({},e.personData),{},{total:p,completed:f,inProgress:h,overdue:m,issues:d}),e.updateFilterCounts(),e.lastLoadTime=Date.now()),t.next=13;break;case 11:t.prev=11,t.t0=t["catch"](0);case 13:case"end":return t.stop()}}),t,null,[[0,11]])})))()},loadPersonIssues:function(){var e=this;return(0,o.default)(a.default.mark((function t(){var r,s,i;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=n("882c"),s=r.callCloudFunction,t.prev=1,t.next=4,s("hygiene-monthly-inspection",{action:"getPersonIssues",data:{responsiblePersonId:e.personId,page:1,pageSize:50}});case 4:if(i=t.sent,!(i&&i.success&&i.data&&i.data.list)){t.next=7;break}return t.abrupt("return",i.data.list.map((function(t,n){return{id:t._id||t.id,number:n+1,title:t.title||t.description,location:e.formatLocation(t.location_info)||t.location||"未知位置",deadline:t.expected_completion_date?new Date(t.expected_completion_date).toISOString().split("T")[0]:e.getDefaultDeadline(),status:e.mapIssueStatus(t.status),priority:t.priority||"normal",description:t.description}})));case 7:t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](1),console.error("获取个人问题失败:",t.t0);case 12:return t.abrupt("return",[]);case 13:case"end":return t.stop()}}),t,null,[[1,9]])})))()},loadPersonInfo:function(){var e=this;return(0,o.default)(a.default.mark((function t(){var r,s,i;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=n("882c"),s=r.callCloudFunction,t.prev=1,t.next=4,s("hygiene-monthly-inspection",{action:"getUserInfo",data:{userId:e.personId}});case 4:if(i=t.sent,!(i&&i.success&&i.data)){t.next=7;break}return t.abrupt("return",{name:i.data.nickname||i.data.username||e.personName,role:e.getRoleDisplayName(i.data.role||[])});case 7:t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](1),console.error("获取个人信息失败:",t.t0);case 12:return t.abrupt("return",null);case 13:case"end":return t.stop()}}),t,null,[[1,9]])})))()},mapIssueStatus:function(e){return{submitted:"pending",assigned:"assigned",pending:"pending",in_progress:"in_progress",pending_review:"pending_review",approved:"approved",closed:"closed",resolved:"approved",overdue:"overdue",rejected:"rejected",reopened:"pending",draft:"pending"}[e]||"pending"},getDefaultDeadline:function(){var e=new Date;return e.setDate(e.getDate()+7),e.toISOString().split("T")[0]},formatLocation:function(e){if(!e)return null;var t=e.location_name||e.name||null;return e.location_category&&(t="".concat(e.location_category," - ").concat(t)),t},getRoleDisplayName:function(e){if(!e||!Array.isArray(e)||0===e.length)return"普通员工";return{admin:"管理员",responsible:"负责人",reviser:"发布人",supervisor:"主管",PM:"副厂长",GM:"厂长",logistics:"后勤员",dispatch:"调度员",Integrated:"综合员",operator:"设备员",technician:"工艺员",mechanic:"技术员",user:"普通员工"}[e[0]]||"普通员工"},updateFilterCounts:function(){},changeFilter:function(e){this.currentFilter=e,this.displayLimit=6,this.$nextTick((function(){}))},getFilterTitle:function(){var e=this,t=this.statusFilters.find((function(t){return t.value===e.currentFilter}));return t?t.label+"问题":"问题"},getStatusText:function(e){return{assigned:"已分配",pending:"待整改",in_progress:"整改中",pending_review:"待检查",approved:"检查通过",rejected:"已驳回",overdue:"已逾期",closed:"已关闭",reopened:"重新打开"}[e]||"未知"},generateIssueHistory:function(e){var t=[];return e.created_at&&t.push({action:"创建问题",description:"6S检查员发现问题并记录",time:this.formatHistoryTime(e.created_at),operator:e.reporter_name||"检查员"}),e.assigned_to_name&&t.push({action:"分配负责人",description:"将问题分配给".concat(e.assigned_to_name,"处理"),time:this.formatHistoryTime(e.updated_at||e.created_at),operator:"管理员"}),"in_progress"===e.status?t.push({action:"开始整改",description:"负责人已开始处理此问题",time:this.formatHistoryTime(e.updated_at),operator:e.assigned_to_name||this.personData.name}):"approved"===e.status&&(t.push({action:"开始整改",description:"负责人已开始处理此问题",time:this.formatHistoryTime(e.updated_at),operator:e.assigned_to_name||this.personData.name},{action:"整改完成",description:"整改措施已实施完成",time:this.formatHistoryTime(e.actual_completion_date||e.updated_at),operator:e.assigned_to_name||this.personData.name}),e.actual_completion_date&&t.push({action:"开始检查",description:"检查员开始验证整改效果",time:this.formatHistoryTime(e.actual_completion_date),operator:"检查员"},{action:"检查通过",description:"检查员确认整改合格",time:this.formatHistoryTime(e.actual_completion_date),operator:"检查员"})),t},formatHistoryTime:function(e){if(!e)return"--";try{var t=new Date(e);return"".concat(t.getFullYear(),"-").concat(String(t.getMonth()+1).padStart(2,"0"),"-").concat(String(t.getDate()).padStart(2,"0")," ").concat(String(t.getHours()).padStart(2,"0"),":").concat(String(t.getMinutes()).padStart(2,"0"))}catch(n){return"--"}},viewIssueDetail:function(t){var n=c(c({},t),{},{responsible:this.personData.name,role:this.personData.role,createdAt:"2024-01-15",updatedAt:"2024-01-15",images:["/static/empty/default-image.png"],history:this.generateIssueHistory(t)});e.setStorageSync("issue_detail_".concat(t.id),n),e.navigateTo({url:"/pages/6s_pkg/issue-detail?id=".concat(t.id)})},showMoreIssues:function(){this.displayLimit+=this.displayStep},showLoading:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"加载中...";this.loading=!0,this.loadingText=e},hideLoading:function(){this.loading=!1},delay:function(e){return new Promise((function(t){return setTimeout(t,e)}))},handleDataUpdated:function(e){var t=this;this.dataCache&&this.dataCache.clear(),setTimeout((function(){t.refreshDataSilently()}),200)},refreshDataSilently:function(){var e=this;return(0,o.default)(a.default.mark((function t(){var n,r,s,o,u,l,d,p,f;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.loading){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,t.next=5,Promise.all([e.loadPersonIssues(),e.loadPersonInfo()]);case 5:n=t.sent,r=(0,i.default)(n,2),s=r[0],o=r[1],s&&s.length>0&&(u=s,o||{name:e.personName,role:"负责人"},l=u.length,d=u.filter((function(e){return"approved"===e.status})).length,p=u.filter((function(e){return"in_progress"===e.status})).length,f=u.filter((function(e){return"overdue"===e.status})).length,e.personData=c(c({},e.personData),{},{total:l,completed:d,inProgress:p,overdue:f,issues:u}),e.updateFilterCounts(),e.lastLoadTime=Date.now()),t.next=14;break;case 12:t.prev=12,t.t0=t["catch"](2);case 14:case"end":return t.stop()}}),t,null,[[2,12]])})))()},refreshData:function(){var e=this;return(0,o.default)(a.default.mark((function t(){return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.dataCache.clear(),t.next=3,e.loadPersonData();case 3:case"end":return t.stop()}}),t)})))()}}};t.default=l}).call(this,n("df3c")["default"])},c0b4:function(e,t,n){"use strict";(function(e,t){var r=n("47a9");n("357b"),n("861b");r(n("3240"));var a=r(n("54a8"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},dd4e:function(e,t,n){"use strict";n.r(t);var r=n("a56e"),a=n.n(r);for(var s in r)["default"].indexOf(s)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(s);t["default"]=a.a},e5d3:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return s})),n.d(t,"a",(function(){return r}));var r={uniIcons:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(n.bind(null,"6ddf"))},pEmptyState:function(){return n.e("components/p-empty-state/p-empty-state").then(n.bind(null,"9b76"))}},a=function(){var e=this,t=e.$createElement,n=(e._self._c,e.getFilterTitle()),r=e.filteredIssues.length,a=e.filteredIssues.length,s=a>0?e.__map(e.displayedIssues,(function(t,n){var r=e.__get_orig(t),a=String(t.number).padStart(2,"0"),s=e.getStatusText(t.status);return{$orig:r,g2:a,m1:s}})):null,i=a>0?null:e.getFilterTitle()||"数据";e.$mp.data=Object.assign({},{$root:{m0:n,g0:r,g1:a,l0:s,m2:i}})},s=[]},f7d2:function(e,t,n){"use strict";var r=n("90da"),a=n.n(r);a.a}},[["c0b4","common/runtime","common/vendor"]]]);