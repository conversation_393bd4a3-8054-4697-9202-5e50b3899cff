(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/6s_pkg/common/vendor"],{

/***/ 538:
/*!************************************!*\
  !*** D:/Xwzc/utils/api/hygiene.js ***!
  \************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _classCallCheck2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ 23));
var _createClass2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/createClass */ 24));
var _auth = __webpack_require__(/*! @/utils/auth.js */ 91);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var HygieneAPI = /*#__PURE__*/function () {
  function HygieneAPI() {
    (0, _classCallCheck2.default)(this, HygieneAPI);
  }
  (0, _createClass2.default)(HygieneAPI, null, [{
    key: "getAreaDetail",
    value:
    /**
     * 获取责任区详情
     * @param {String} areaId 责任区ID
     * @returns {Promise<Object>} 责任区信息
     */
    function () {
      var _getAreaDetail = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee(areaId) {
        var result;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                _context.next = 3;
                return (0, _auth.callCloudFunction)('hygiene-area-management', {
                  action: 'getAreaDetail',
                  data: {
                    id: areaId
                  }
                });
              case 3:
                result = _context.sent;
                if (!result.success) {
                  _context.next = 8;
                  break;
                }
                return _context.abrupt("return", result.data);
              case 8:
                throw new Error(result.message || '获取责任区信息失败');
              case 9:
                _context.next = 15;
                break;
              case 11:
                _context.prev = 11;
                _context.t0 = _context["catch"](0);
                console.error('获取责任区详情失败:', _context.t0);
                throw _context.t0;
              case 15:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[0, 11]]);
      }));
      function getAreaDetail(_x) {
        return _getAreaDetail.apply(this, arguments);
      }
      return getAreaDetail;
    }()
    /**
     * 获取责任区列表
     * @param {Object} params 查询参数
     * @returns {Promise<Object>} 责任区列表
     */
  }, {
    key: "getAreaList",
    value: function () {
      var _getAreaList = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var params,
          result,
          _args2 = arguments;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                params = _args2.length > 0 && _args2[0] !== undefined ? _args2[0] : {};
                _context2.prev = 1;
                _context2.next = 4;
                return (0, _auth.callCloudFunction)('hygiene-area-management', {
                  action: 'getAreaList',
                  data: params
                });
              case 4:
                result = _context2.sent;
                if (!result.success) {
                  _context2.next = 9;
                  break;
                }
                return _context2.abrupt("return", result.data);
              case 9:
                throw new Error(result.message || '获取责任区列表失败');
              case 10:
                _context2.next = 16;
                break;
              case 12:
                _context2.prev = 12;
                _context2.t0 = _context2["catch"](1);
                console.error('获取责任区列表失败:', _context2.t0);
                throw _context2.t0;
              case 16:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[1, 12]]);
      }));
      function getAreaList() {
        return _getAreaList.apply(this, arguments);
      }
      return getAreaList;
    }()
    /**
     * 创建清理记录
     * @param {Object} cleaningData 清理数据
     * @returns {Promise<Object>} 创建结果
     */
  }, {
    key: "createCleaningRecord",
    value: function () {
      var _createCleaningRecord = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3(cleaningData) {
        var result;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                _context3.next = 3;
                return (0, _auth.callCloudFunction)('hygiene-cleaning', {
                  action: 'createCleaningRecord',
                  data: cleaningData
                });
              case 3:
                result = _context3.sent;
                if (!result.success) {
                  _context3.next = 8;
                  break;
                }
                return _context3.abrupt("return", result.data);
              case 8:
                throw new Error(result.message || '创建清理记录失败');
              case 9:
                _context3.next = 15;
                break;
              case 11:
                _context3.prev = 11;
                _context3.t0 = _context3["catch"](0);
                console.error('创建清理记录失败:', _context3.t0);
                throw _context3.t0;
              case 15:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[0, 11]]);
      }));
      function createCleaningRecord(_x2) {
        return _createCleaningRecord.apply(this, arguments);
      }
      return createCleaningRecord;
    }()
    /**
     * 获取清理记录列表
     * @param {Object} params 查询参数
     * @returns {Promise<Object>} 清理记录列表
     */
  }, {
    key: "getCleaningRecords",
    value: function () {
      var _getCleaningRecords = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var params,
          result,
          _args4 = arguments;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                params = _args4.length > 0 && _args4[0] !== undefined ? _args4[0] : {};
                _context4.prev = 1;
                _context4.next = 4;
                return (0, _auth.callCloudFunction)('hygiene-cleaning', {
                  action: 'getCleaningRecords',
                  data: params
                });
              case 4:
                result = _context4.sent;
                if (!result.success) {
                  _context4.next = 9;
                  break;
                }
                return _context4.abrupt("return", result.data);
              case 9:
                throw new Error(result.message || '获取清理记录失败');
              case 10:
                _context4.next = 16;
                break;
              case 12:
                _context4.prev = 12;
                _context4.t0 = _context4["catch"](1);
                console.error('获取清理记录失败:', _context4.t0);
                throw _context4.t0;
              case 16:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[1, 12]]);
      }));
      function getCleaningRecords() {
        return _getCleaningRecords.apply(this, arguments);
      }
      return getCleaningRecords;
    }()
    /**
     * 获取我的清理记录
     * @param {Object} params 查询参数
     * @returns {Promise<Array>} 我的清理记录
     */
  }, {
    key: "getMyCleaningRecords",
    value: function () {
      var _getMyCleaningRecords = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var params,
          result,
          _args5 = arguments;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                params = _args5.length > 0 && _args5[0] !== undefined ? _args5[0] : {};
                _context5.prev = 1;
                _context5.next = 4;
                return (0, _auth.callCloudFunction)('hygiene-cleaning', {
                  action: 'getMyCleaningRecords',
                  data: params
                });
              case 4:
                result = _context5.sent;
                if (!result.success) {
                  _context5.next = 9;
                  break;
                }
                return _context5.abrupt("return", result.data);
              case 9:
                throw new Error(result.message || '获取清理记录失败');
              case 10:
                _context5.next = 16;
                break;
              case 12:
                _context5.prev = 12;
                _context5.t0 = _context5["catch"](1);
                console.error('获取我的清理记录失败:', _context5.t0);
                throw _context5.t0;
              case 16:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[1, 12]]);
      }));
      function getMyCleaningRecords() {
        return _getMyCleaningRecords.apply(this, arguments);
      }
      return getMyCleaningRecords;
    }()
    /**
     * 更新清理记录
     * @param {String} id 记录ID
     * @param {Object} updateData 更新数据
     * @returns {Promise<Object>} 更新结果
     */
  }, {
    key: "updateCleaningRecord",
    value: function () {
      var _updateCleaningRecord = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6(id, updateData) {
        var result;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                _context6.prev = 0;
                _context6.next = 3;
                return (0, _auth.callCloudFunction)('hygiene-cleaning', {
                  action: 'updateCleaningRecord',
                  data: _objectSpread({
                    id: id
                  }, updateData)
                });
              case 3:
                result = _context6.sent;
                if (!result.success) {
                  _context6.next = 8;
                  break;
                }
                return _context6.abrupt("return", result.data);
              case 8:
                throw new Error(result.message || '更新清理记录失败');
              case 9:
                _context6.next = 15;
                break;
              case 11:
                _context6.prev = 11;
                _context6.t0 = _context6["catch"](0);
                console.error('更新清理记录失败:', _context6.t0);
                throw _context6.t0;
              case 15:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[0, 11]]);
      }));
      function updateCleaningRecord(_x3, _x4) {
        return _updateCleaningRecord.apply(this, arguments);
      }
      return updateCleaningRecord;
    }()
    /**
     * 删除清理记录
     * @param {String} id 记录ID
     * @returns {Promise<Object>} 删除结果
     */
  }, {
    key: "deleteCleaningRecord",
    value: function () {
      var _deleteCleaningRecord = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7(id) {
        var result;
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                _context7.prev = 0;
                _context7.next = 3;
                return (0, _auth.callCloudFunction)('hygiene-cleaning', {
                  action: 'deleteCleaningRecord',
                  data: {
                    id: id
                  }
                });
              case 3:
                result = _context7.sent;
                if (!result.success) {
                  _context7.next = 8;
                  break;
                }
                return _context7.abrupt("return", result.data);
              case 8:
                throw new Error(result.message || '删除清理记录失败');
              case 9:
                _context7.next = 15;
                break;
              case 11:
                _context7.prev = 11;
                _context7.t0 = _context7["catch"](0);
                console.error('删除清理记录失败:', _context7.t0);
                throw _context7.t0;
              case 15:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7, null, [[0, 11]]);
      }));
      function deleteCleaningRecord(_x5) {
        return _deleteCleaningRecord.apply(this, arguments);
      }
      return deleteCleaningRecord;
    }()
    /**
     * 获取清理历史
     * @param {String} areaId 责任区ID
     * @param {Number} months 查询月数，默认6个月
     * @returns {Promise<Object>} 清理历史
     */
  }, {
    key: "getCleaningHistory",
    value: function () {
      var _getCleaningHistory = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8(areaId) {
        var months,
          result,
          _args8 = arguments;
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                months = _args8.length > 1 && _args8[1] !== undefined ? _args8[1] : 6;
                _context8.prev = 1;
                _context8.next = 4;
                return (0, _auth.callCloudFunction)('hygiene-cleaning', {
                  action: 'getCleaningHistory',
                  data: {
                    area_id: areaId,
                    months: months
                  }
                });
              case 4:
                result = _context8.sent;
                if (!result.success) {
                  _context8.next = 9;
                  break;
                }
                return _context8.abrupt("return", result.data);
              case 9:
                throw new Error(result.message || '获取清理历史失败');
              case 10:
                _context8.next = 16;
                break;
              case 12:
                _context8.prev = 12;
                _context8.t0 = _context8["catch"](1);
                console.error('获取清理历史失败:', _context8.t0);
                throw _context8.t0;
              case 16:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8, null, [[1, 12]]);
      }));
      function getCleaningHistory(_x6) {
        return _getCleaningHistory.apply(this, arguments);
      }
      return getCleaningHistory;
    }()
    /**
     * 获取周清理计划
     * @param {String} weekStart 周开始时间
     * @param {String} userId 用户ID（可选）
     * @returns {Promise<Object>} 周清理计划
     */
  }, {
    key: "getWeeklySchedule",
    value: function () {
      var _getWeeklySchedule = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9(weekStart, userId) {
        var result;
        return _regenerator.default.wrap(function _callee9$(_context9) {
          while (1) {
            switch (_context9.prev = _context9.next) {
              case 0:
                _context9.prev = 0;
                _context9.next = 3;
                return (0, _auth.callCloudFunction)('hygiene-cleaning', {
                  action: 'getWeeklySchedule',
                  data: {
                    week_start: weekStart,
                    user_id: userId
                  }
                });
              case 3:
                result = _context9.sent;
                if (!result.success) {
                  _context9.next = 8;
                  break;
                }
                return _context9.abrupt("return", result.data);
              case 8:
                throw new Error(result.message || '获取周计划失败');
              case 9:
                _context9.next = 15;
                break;
              case 11:
                _context9.prev = 11;
                _context9.t0 = _context9["catch"](0);
                console.error('获取周清理计划失败:', _context9.t0);
                throw _context9.t0;
              case 15:
              case "end":
                return _context9.stop();
            }
          }
        }, _callee9, null, [[0, 11]]);
      }));
      function getWeeklySchedule(_x7, _x8) {
        return _getWeeklySchedule.apply(this, arguments);
      }
      return getWeeklySchedule;
    }()
    /**
     * 创建检查记录
     * @param {Object} inspectionData 检查数据
     * @returns {Promise<Object>} 创建结果
     */
  }, {
    key: "createInspectionRecord",
    value: function () {
      var _createInspectionRecord = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10(inspectionData) {
        var result;
        return _regenerator.default.wrap(function _callee10$(_context10) {
          while (1) {
            switch (_context10.prev = _context10.next) {
              case 0:
                _context10.prev = 0;
                _context10.next = 3;
                return (0, _auth.callCloudFunction)('hygiene-inspection', {
                  action: 'createInspectionRecord',
                  data: inspectionData
                });
              case 3:
                result = _context10.sent;
                if (!result.success) {
                  _context10.next = 8;
                  break;
                }
                return _context10.abrupt("return", result.data);
              case 8:
                throw new Error(result.message || '创建检查记录失败');
              case 9:
                _context10.next = 15;
                break;
              case 11:
                _context10.prev = 11;
                _context10.t0 = _context10["catch"](0);
                console.error('创建检查记录失败:', _context10.t0);
                throw _context10.t0;
              case 15:
              case "end":
                return _context10.stop();
            }
          }
        }, _callee10, null, [[0, 11]]);
      }));
      function createInspectionRecord(_x9) {
        return _createInspectionRecord.apply(this, arguments);
      }
      return createInspectionRecord;
    }()
    /**
     * 获取检查记录列表
     * @param {Object} params 查询参数
     * @returns {Promise<Object>} 检查记录列表
     */
  }, {
    key: "getInspectionRecords",
    value: function () {
      var _getInspectionRecords = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee11() {
        var params,
          result,
          _args11 = arguments;
        return _regenerator.default.wrap(function _callee11$(_context11) {
          while (1) {
            switch (_context11.prev = _context11.next) {
              case 0:
                params = _args11.length > 0 && _args11[0] !== undefined ? _args11[0] : {};
                _context11.prev = 1;
                _context11.next = 4;
                return (0, _auth.callCloudFunction)('hygiene-inspection', {
                  action: 'getInspectionRecords',
                  data: params
                });
              case 4:
                result = _context11.sent;
                if (!result.success) {
                  _context11.next = 9;
                  break;
                }
                return _context11.abrupt("return", result.data);
              case 9:
                throw new Error(result.message || '获取检查记录失败');
              case 10:
                _context11.next = 16;
                break;
              case 12:
                _context11.prev = 12;
                _context11.t0 = _context11["catch"](1);
                console.error('获取检查记录失败:', _context11.t0);
                throw _context11.t0;
              case 16:
              case "end":
                return _context11.stop();
            }
          }
        }, _callee11, null, [[1, 12]]);
      }));
      function getInspectionRecords() {
        return _getInspectionRecords.apply(this, arguments);
      }
      return getInspectionRecords;
    }()
    /**
     * 创建问题记录
     * @param {Object} issueData 问题数据
     * @returns {Promise<Object>} 创建结果
     */
  }, {
    key: "createIssueRecord",
    value: function () {
      var _createIssueRecord = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee12(issueData) {
        var result;
        return _regenerator.default.wrap(function _callee12$(_context12) {
          while (1) {
            switch (_context12.prev = _context12.next) {
              case 0:
                _context12.prev = 0;
                _context12.next = 3;
                return (0, _auth.callCloudFunction)('hygiene-issues', {
                  action: 'createIssueRecord',
                  data: issueData
                });
              case 3:
                result = _context12.sent;
                if (!result.success) {
                  _context12.next = 8;
                  break;
                }
                return _context12.abrupt("return", result.data);
              case 8:
                throw new Error(result.message || '创建问题记录失败');
              case 9:
                _context12.next = 15;
                break;
              case 11:
                _context12.prev = 11;
                _context12.t0 = _context12["catch"](0);
                console.error('创建问题记录失败:', _context12.t0);
                throw _context12.t0;
              case 15:
              case "end":
                return _context12.stop();
            }
          }
        }, _callee12, null, [[0, 11]]);
      }));
      function createIssueRecord(_x10) {
        return _createIssueRecord.apply(this, arguments);
      }
      return createIssueRecord;
    }()
    /**
     * 获取问题记录列表
     * @param {Object} params 查询参数
     * @returns {Promise<Object>} 问题记录列表
     */
  }, {
    key: "getIssueRecords",
    value: function () {
      var _getIssueRecords = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee13() {
        var params,
          result,
          _args13 = arguments;
        return _regenerator.default.wrap(function _callee13$(_context13) {
          while (1) {
            switch (_context13.prev = _context13.next) {
              case 0:
                params = _args13.length > 0 && _args13[0] !== undefined ? _args13[0] : {};
                _context13.prev = 1;
                _context13.next = 4;
                return (0, _auth.callCloudFunction)('hygiene-issues', {
                  action: 'getIssueRecords',
                  data: params
                });
              case 4:
                result = _context13.sent;
                if (!result.success) {
                  _context13.next = 9;
                  break;
                }
                return _context13.abrupt("return", result.data);
              case 9:
                throw new Error(result.message || '获取问题记录失败');
              case 10:
                _context13.next = 16;
                break;
              case 12:
                _context13.prev = 12;
                _context13.t0 = _context13["catch"](1);
                console.error('获取问题记录失败:', _context13.t0);
                throw _context13.t0;
              case 16:
              case "end":
                return _context13.stop();
            }
          }
        }, _callee13, null, [[1, 12]]);
      }));
      function getIssueRecords() {
        return _getIssueRecords.apply(this, arguments);
      }
      return getIssueRecords;
    }()
    /**
     * 删除清理记录中的照片
     * @param {String} cleaningRecordId 清理记录ID
     * @param {Array} photoUrls 要删除的照片URL列表
     * @returns {Promise<Object>} 删除结果
     */
  }, {
    key: "deleteCleaningPhotos",
    value: function () {
      var _deleteCleaningPhotos = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee14(cleaningRecordId, photoUrls) {
        var result;
        return _regenerator.default.wrap(function _callee14$(_context14) {
          while (1) {
            switch (_context14.prev = _context14.next) {
              case 0:
                _context14.prev = 0;
                _context14.next = 3;
                return (0, _auth.callCloudFunction)('hygiene-cleaning', {
                  action: 'deleteCleaningPhotos',
                  data: {
                    cleaning_record_id: cleaningRecordId,
                    photo_urls: photoUrls
                  }
                });
              case 3:
                result = _context14.sent;
                if (!result.success) {
                  _context14.next = 8;
                  break;
                }
                return _context14.abrupt("return", result.data);
              case 8:
                throw new Error(result.message || '删除照片失败');
              case 9:
                _context14.next = 15;
                break;
              case 11:
                _context14.prev = 11;
                _context14.t0 = _context14["catch"](0);
                console.error('删除清理照片失败:', _context14.t0);
                throw _context14.t0;
              case 15:
              case "end":
                return _context14.stop();
            }
          }
        }, _callee14, null, [[0, 11]]);
      }));
      function deleteCleaningPhotos(_x11, _x12) {
        return _deleteCleaningPhotos.apply(this, arguments);
      }
      return deleteCleaningPhotos;
    }()
    /**
     * 创建整改记录
     * @param {Object} rectificationData 整改数据
     * @returns {Promise<Object>} 创建结果
     */
  }, {
    key: "createRectificationRecord",
    value: function () {
      var _createRectificationRecord = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee15(rectificationData) {
        var result;
        return _regenerator.default.wrap(function _callee15$(_context15) {
          while (1) {
            switch (_context15.prev = _context15.next) {
              case 0:
                _context15.prev = 0;
                _context15.next = 3;
                return (0, _auth.callCloudFunction)('hygiene-rectification', {
                  action: 'createRectificationRecord',
                  data: rectificationData
                });
              case 3:
                result = _context15.sent;
                if (!result.success) {
                  _context15.next = 8;
                  break;
                }
                return _context15.abrupt("return", result.data);
              case 8:
                throw new Error(result.message || '创建整改记录失败');
              case 9:
                _context15.next = 15;
                break;
              case 11:
                _context15.prev = 11;
                _context15.t0 = _context15["catch"](0);
                console.error('创建整改记录失败:', _context15.t0);
                throw _context15.t0;
              case 15:
              case "end":
                return _context15.stop();
            }
          }
        }, _callee15, null, [[0, 11]]);
      }));
      function createRectificationRecord(_x13) {
        return _createRectificationRecord.apply(this, arguments);
      }
      return createRectificationRecord;
    }()
    /**
     * 获取整改记录列表
     * @param {Object} params 查询参数
     * @returns {Promise<Object>} 整改记录列表
     */
  }, {
    key: "getRectificationRecords",
    value: function () {
      var _getRectificationRecords = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee16() {
        var params,
          result,
          _args16 = arguments;
        return _regenerator.default.wrap(function _callee16$(_context16) {
          while (1) {
            switch (_context16.prev = _context16.next) {
              case 0:
                params = _args16.length > 0 && _args16[0] !== undefined ? _args16[0] : {};
                _context16.prev = 1;
                _context16.next = 4;
                return (0, _auth.callCloudFunction)('hygiene-rectification', {
                  action: 'getRectificationRecords',
                  data: params
                });
              case 4:
                result = _context16.sent;
                if (!result.success) {
                  _context16.next = 9;
                  break;
                }
                return _context16.abrupt("return", result.data);
              case 9:
                throw new Error(result.message || '获取整改记录失败');
              case 10:
                _context16.next = 16;
                break;
              case 12:
                _context16.prev = 12;
                _context16.t0 = _context16["catch"](1);
                console.error('获取整改记录失败:', _context16.t0);
                throw _context16.t0;
              case 16:
              case "end":
                return _context16.stop();
            }
          }
        }, _callee16, null, [[1, 12]]);
      }));
      function getRectificationRecords() {
        return _getRectificationRecords.apply(this, arguments);
      }
      return getRectificationRecords;
    }()
  }]);
  return HygieneAPI;
}();
var _default = HygieneAPI;
exports.default = _default;

/***/ })

}]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/6s_pkg/common/vendor.js.map