{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s_pkg/inspector-rectification-detail.vue?8c1b", "webpack:///D:/Xwzc/pages/6s_pkg/inspector-rectification-detail.vue?f5c2", "webpack:///D:/Xwzc/pages/6s_pkg/inspector-rectification-detail.vue?60a9", "webpack:///D:/Xwzc/pages/6s_pkg/inspector-rectification-detail.vue?e628", "uni-app:///pages/6s_pkg/inspector-rectification-detail.vue", "webpack:///D:/Xwzc/pages/6s_pkg/inspector-rectification-detail.vue?a24f", "webpack:///D:/Xwzc/pages/6s_pkg/inspector-rectification-detail.vue?62cb"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "taskId", "loading", "loadError", "taskInfo", "id", "areaName", "areaType", "status", "problemDescription", "issueFoundDate", "assignee", "inspector", "rectificationDescription", "rectificationRequirement", "issuePhotos", "rectificationPhotos", "reviewHistory", "photoErrors", "issue", "rectification", "review", "computed", "hasRectificationResult", "hasRectificationPhotos", "hasIssuePhotos", "hasReviewPhotos", "onLoad", "methods", "loadTaskDetail", "action", "result", "task", "reviewPhotos", "reviewComments", "reviewDate", "reviewResult", "reviewerName", "uni", "title", "content", "showCancel", "cancelText", "confirmText", "success", "retryLoad", "getIssueDescription", "getAs<PERSON>eeName", "getInspectorName", "getIssuePhotos", "getRectificationPhotos", "processPhotos", "processReviewHistory", "comments", "reviewer", "getStatusText", "formatDateTime", "date", "previewPhoto", "urls", "current", "goToReview", "url", "goBack", "getPhotoUrl", "onPhotoError", "onPhotoLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qCAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuJ;AACvJ;AACkF;AACL;AACsC;;;AAGnH;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,oGAAM;AACR,EAAE,qHAAM;AACR,EAAE,8HAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yHAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5GA;AAAA;AAAA;AAAA;AAAwnB,CAAgB,kpBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC+P5oB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;EACAC;IACA;IAEA;MACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGA;kBACAC;kBACA9B;oBAAAK;kBAAA;gBACA;cAAA;gBAHA0B;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACAC;gBAEA;kBACA3B;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACA;kBACAC;kBACAC;kBACAiB;kBACAhB;kBACA;kBACAiB;kBACAC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;gBAEAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;oBACA;sBACA;oBACA;sBACAN;oBACA;kBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAO;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;UACA;QACA;UACA;QACA;QACA;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MAEA;QAAA;UACArB;UACAsB;UACAlB;UACAmB;QACA;MAAA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;QACA;UACA;YACAC;UACA;YACAA;UACA;QACA;UACAA;QACA;QAEA;UACA;QACA;QAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACApB;QACAqB;QACAC;MACA;IACA;IAEA;IACAC;MACAvB;QACAwB;MACA;IACA;IAEA;IACAC;MACAzB;IACA;IAEA;IACA0B;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrjBA;AAAA;AAAA;AAAA;AAA2rC,CAAgB,iqCAAG,EAAC,C;;;;;;;;;;;ACA/sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/inspector-rectification-detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/inspector-rectification-detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./inspector-rectification-detail.vue?vue&type=template&id=ccbb42d4&scoped=true&\"\nvar renderjs\nimport script from \"./inspector-rectification-detail.vue?vue&type=script&lang=js&\"\nexport * from \"./inspector-rectification-detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./inspector-rectification-detail.vue?vue&type=style&index=0&id=ccbb42d4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ccbb42d4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/inspector-rectification-detail.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./inspector-rectification-detail.vue?vue&type=template&id=ccbb42d4&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    !_vm.loading && !_vm.loadError\n      ? _vm.getStatusText(_vm.taskInfo.status)\n      : null\n  var m1 =\n    !_vm.loading && !_vm.loadError\n      ? _vm.formatDateTime(_vm.taskInfo.issueFoundDate)\n      : null\n  var g0 =\n    !_vm.loading && !_vm.loadError && _vm.hasIssuePhotos\n      ? _vm.taskInfo.issuePhotos.length\n      : null\n  var l0 =\n    !_vm.loading && !_vm.loadError && _vm.hasIssuePhotos\n      ? _vm.__map(_vm.taskInfo.issuePhotos, function (photo, index) {\n          var $orig = _vm.__get_orig(photo)\n          var m2 = _vm.getPhotoUrl(photo)\n          return {\n            $orig: $orig,\n            m2: m2,\n          }\n        })\n      : null\n  var l1 =\n    !_vm.loading && !_vm.loadError && _vm.hasRectificationPhotos\n      ? _vm.__map(_vm.taskInfo.rectificationPhotos, function (photo, index) {\n          var $orig = _vm.__get_orig(photo)\n          var m3 = _vm.getPhotoUrl(photo)\n          return {\n            $orig: $orig,\n            m3: m3,\n          }\n        })\n      : null\n  var g1 =\n    !_vm.loading && !_vm.loadError\n      ? _vm.taskInfo.reviewHistory && _vm.taskInfo.reviewHistory.length > 0\n      : null\n  var l2 =\n    !_vm.loading && !_vm.loadError && g1\n      ? _vm.__map(_vm.taskInfo.reviewHistory, function (review, index) {\n          var $orig = _vm.__get_orig(review)\n          var m4 = _vm.formatDateTime(review.reviewDate)\n          return {\n            $orig: $orig,\n            m4: m4,\n          }\n        })\n      : null\n  var l3 =\n    !_vm.loading && !_vm.loadError && _vm.hasReviewPhotos\n      ? _vm.__map(_vm.taskInfo.reviewPhotos, function (photo, index) {\n          var $orig = _vm.__get_orig(photo)\n          var m5 = _vm.getPhotoUrl(photo)\n          return {\n            $orig: $orig,\n            m5: m5,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        l0: l0,\n        l1: l1,\n        g1: g1,\n        l2: l2,\n        l3: l3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./inspector-rectification-detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./inspector-rectification-detail.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"page-container\">\r\n    <!-- 加载状态 -->\r\n    <view v-if=\"loading\" class=\"loading-container\">\r\n      <view class=\"loading-content\">\r\n        <view class=\"loading-spinner\"></view>\r\n        <text class=\"loading-text\">加载整改任务详情中...</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 错误状态 -->\r\n    <view v-else-if=\"loadError\" class=\"error-container\">\r\n      <view class=\"error-content\">\r\n        <uni-icons type=\"info\" size=\"48\" color=\"#FF3B30\"></uni-icons>\r\n        <text class=\"error-text\">{{ loadError }}</text>\r\n        <button class=\"retry-button\" @click=\"retryLoad\">重新加载</button>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 正常内容 -->\r\n    <template v-else>\r\n      <!-- 任务基本信息 -->\r\n      <view class=\"card\">\r\n        <view class=\"card-header\">\r\n          <view class=\"header-content\">\r\n            <view class=\"card-title\">整改任务详情</view>\r\n            <view class=\"card-subtitle\">{{ taskInfo.areaName }} - {{ taskInfo.areaType === 'public' ? '公共责任区' : '固定责任区' }}</view>\r\n          </view>\r\n          <view class=\"status-badge\" :class=\"['status-' + taskInfo.status]\">\r\n            {{ getStatusText(taskInfo.status) }}\r\n          </view>\r\n        </view>\r\n        <view class=\"card-body\">\r\n          <view class=\"info-grid\">\r\n            <view class=\"info-item\">\r\n              <view class=\"info-icon\">\r\n                <uni-icons type=\"calendar\" size=\"18\" color=\"#007AFF\"></uni-icons>\r\n              </view>\r\n              <view class=\"info-content\">\r\n                <view class=\"info-label\">发现时间</view>\r\n                <view class=\"info-value\">{{ formatDateTime(taskInfo.issueFoundDate) }}</view>\r\n              </view>\r\n            </view>\r\n            <view class=\"info-item\">\r\n              <view class=\"info-icon\">\r\n                <uni-icons type=\"person\" size=\"18\" color=\"#FF9500\"></uni-icons>\r\n              </view>\r\n              <view class=\"info-content\">\r\n                <view class=\"info-label\">负责人</view>\r\n                <view class=\"info-value\">{{ taskInfo.assignee }}</view>\r\n              </view>\r\n            </view>\r\n            <view class=\"info-item\">\r\n              <view class=\"info-icon\">\r\n                <uni-icons type=\"contact\" size=\"18\" color=\"#5856D6\"></uni-icons>\r\n              </view>\r\n              <view class=\"info-content\">\r\n                <view class=\"info-label\">检查员</view>\r\n                <view class=\"info-value\">{{ taskInfo.inspector }}</view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 问题描述 -->\r\n      <view class=\"card\">\r\n        <view class=\"card-header\">\r\n          <view class=\"card-title\">发现问题</view>\r\n          <view class=\"card-subtitle\">检查时发现的问题描述</view>\r\n        </view>\r\n        <view class=\"card-body\">\r\n          <view class=\"problem-content\">{{ taskInfo.problemDescription }}</view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 问题照片 -->\r\n      <view v-if=\"hasIssuePhotos\" class=\"card\">\r\n        <view class=\"card-header\">\r\n          <view class=\"card-title\">问题照片</view>\r\n          <view class=\"card-subtitle\">检查时拍摄的问题照片 ({{ taskInfo.issuePhotos.length }}张)</view>\r\n        </view>\r\n        <view class=\"card-body\">\r\n\r\n          <view class=\"photo-grid\">\r\n            <view \r\n              v-for=\"(photo, index) in taskInfo.issuePhotos\" \r\n              :key=\"index\"\r\n              class=\"photo-item\"\r\n              @click=\"previewPhoto(taskInfo.issuePhotos, index)\"\r\n            >\r\n              <image \r\n                :src=\"getPhotoUrl(photo)\" \r\n                mode=\"aspectFill\"\r\n                @error=\"onPhotoError(index, 'issue')\"\r\n                @load=\"onPhotoLoad(index, 'issue')\"\r\n              ></image>\r\n              <view v-if=\"photoErrors.issue && photoErrors.issue[index]\" class=\"photo-error\">\r\n                <uni-icons type=\"image\" size=\"32\" color=\"#8E8E93\"></uni-icons>\r\n                <text>加载失败</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 整改要求 -->\r\n      <view v-if=\"taskInfo.rectificationRequirement\" class=\"card\">\r\n        <view class=\"card-header\">\r\n          <view class=\"card-title\">整改要求</view>\r\n          <view class=\"card-subtitle\">需要完成的整改内容</view>\r\n        </view>\r\n        <view class=\"card-body\">\r\n          <view class=\"requirement-content\">{{ taskInfo.rectificationRequirement }}</view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 整改结果 -->\r\n      <view v-if=\"hasRectificationResult\" class=\"card\">\r\n        <view class=\"card-header\">\r\n          <view class=\"card-title\">整改结果</view>\r\n          <view class=\"card-subtitle\">员工提交的整改说明</view>\r\n        </view>\r\n        <view class=\"card-body\">\r\n          <view v-if=\"taskInfo.rectificationDescription\" class=\"rectification-content\">\r\n            {{ taskInfo.rectificationDescription }}\r\n          </view>\r\n          <view v-else class=\"no-description\">\r\n            <text>暂无整改说明</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 整改照片 -->\r\n      <view v-if=\"hasRectificationPhotos\" class=\"card\">\r\n        <view class=\"card-header\">\r\n          <view class=\"card-title\">整改照片</view>\r\n          <view class=\"card-subtitle\">员工提交的整改后照片</view>\r\n        </view>\r\n        <view class=\"card-body\">\r\n          <view class=\"photo-grid\">\r\n            <view \r\n              v-for=\"(photo, index) in taskInfo.rectificationPhotos\" \r\n              :key=\"index\"\r\n              class=\"photo-item\"\r\n              @click=\"previewPhoto(taskInfo.rectificationPhotos, index)\"\r\n            >\r\n              <image \r\n                :src=\"getPhotoUrl(photo)\" \r\n                mode=\"aspectFill\"\r\n                @error=\"onPhotoError(index, 'rectification')\"\r\n                @load=\"onPhotoLoad(index, 'rectification')\"\r\n              ></image>\r\n              <view v-if=\"photoErrors.rectification && photoErrors.rectification[index]\" class=\"photo-error\">\r\n                <uni-icons type=\"image\" size=\"32\" color=\"#8E8E93\"></uni-icons>\r\n                <text>加载失败</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 审核记录 -->\r\n      <view v-if=\"taskInfo.reviewHistory && taskInfo.reviewHistory.length > 0\" class=\"card\">\r\n        <view class=\"card-header\">\r\n          <view class=\"card-title\">审核记录</view>\r\n          <view class=\"card-subtitle\">历史审核记录</view>\r\n        </view>\r\n        <view class=\"card-body\">\r\n          <view class=\"review-timeline\">\r\n            <view \r\n              v-for=\"(review, index) in taskInfo.reviewHistory\" \r\n              :key=\"index\"\r\n              class=\"review-item\"\r\n            >\r\n              <view class=\"review-dot\" :class=\"['review-' + review.result]\"></view>\r\n              <view class=\"review-content\">\r\n                <view class=\"review-header\">\r\n                  <text class=\"review-result\">{{ review.result === 'approved' ? '复查通过' : '需重新整改' }}</text>\r\n                  <text class=\"review-date\">{{ formatDateTime(review.reviewDate) }}</text>\r\n                </view>\r\n                <view v-if=\"review.comments\" class=\"review-comments\">{{ review.comments }}</view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 审核情况 -->\r\n      <view v-if=\"taskInfo.reviewComments\" class=\"card\">\r\n        <view class=\"card-header\">\r\n          <view class=\"card-title\">审核情况</view>\r\n          <view class=\"card-subtitle\">检查员复查的现场情况</view>\r\n        </view>\r\n        <view class=\"card-body\">\r\n          <view class=\"problem-content\">\r\n            {{ taskInfo.reviewComments }}\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 审核照片 -->\r\n      <view v-if=\"hasReviewPhotos\" class=\"card\">\r\n        <view class=\"card-header\">\r\n          <view class=\"card-title\">审核照片</view>\r\n          <view class=\"card-subtitle\">审核员拍摄的现场照片</view>\r\n        </view>\r\n        <view class=\"card-body\">\r\n          <view class=\"photo-grid\">\r\n            <view \r\n              v-for=\"(photo, index) in taskInfo.reviewPhotos\" \r\n              :key=\"index\"\r\n              class=\"photo-item\"\r\n              @click=\"previewPhoto(taskInfo.reviewPhotos, index)\"\r\n            >\r\n              <image \r\n                :src=\"getPhotoUrl(photo)\" \r\n                mode=\"aspectFill\"\r\n                @error=\"onPhotoError(index, 'review')\"\r\n                @load=\"onPhotoLoad(index, 'review')\"\r\n              ></image>\r\n              <view v-if=\"photoErrors.review && photoErrors.review[index]\" class=\"photo-error\">\r\n                <uni-icons type=\"image\" size=\"32\" color=\"#8E8E93\"></uni-icons>\r\n                <text>加载失败</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </template>\r\n    \r\n    <!-- 操作按钮 -->\r\n    <view v-if=\"!loading && !loadError\" class=\"button-container\">\r\n      <!-- 待复查状态显示复查按钮 -->\r\n      <button \r\n        v-if=\"taskInfo.status === 'pending_review'\"\r\n        class=\"primary-button\" \r\n        @click=\"goToReview\"\r\n      >\r\n        进行复查\r\n      </button>\r\n      \r\n      <!-- 其他状态显示返回按钮 -->\r\n      <button \r\n        v-else\r\n        class=\"primary-button\" \r\n        @click=\"goBack\"\r\n      >\r\n        返回\r\n      </button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { callCloudFunction } from '@/utils/auth.js';\r\n\r\nexport default {\r\n  name: 'InspectorRectificationDetail',\r\n  data() {\r\n    return {\r\n      taskId: '',\r\n      loading: false,\r\n      loadError: '',\r\n      taskInfo: {\r\n        id: '',\r\n        areaName: '',\r\n        areaType: 'fixed',\r\n        status: 'pending',\r\n        problemDescription: '',\r\n        issueFoundDate: '',\r\n        assignee: '',\r\n        inspector: '',\r\n        rectificationDescription: '',\r\n        rectificationRequirement: '',\r\n        issuePhotos: [],\r\n        rectificationPhotos: [],\r\n        reviewHistory: []\r\n      },\r\n      photoErrors: {\r\n        issue: {},\r\n        rectification: {},\r\n        review: {}\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    // 是否有整改结果\r\n    hasRectificationResult() {\r\n      // 只有在状态不是待处理、待分配、处理中时，且有整改描述或照片时才显示\r\n      const hasCompletedStatus = !['pending_rectification', 'pending_assignment', 'pending', 'in_progress'].includes(this.taskInfo.status);\r\n      const hasDescription = this.taskInfo.rectificationDescription && this.taskInfo.rectificationDescription.trim();\r\n      const hasPhotos = this.taskInfo.rectificationPhotos && this.taskInfo.rectificationPhotos.length > 0;\r\n      \r\n      return hasCompletedStatus && (hasDescription || hasPhotos);\r\n    },\r\n    \r\n    // 是否有整改照片\r\n    hasRectificationPhotos() {\r\n      // 只有在有整改结果且有照片时才显示\r\n      return this.hasRectificationResult && this.taskInfo.rectificationPhotos && this.taskInfo.rectificationPhotos.length > 0;\r\n    },\r\n\r\n    // 是否有问题照片\r\n    hasIssuePhotos() {\r\n      return this.taskInfo.issuePhotos && this.taskInfo.issuePhotos.length > 0;\r\n    },\r\n\r\n    // 是否有审核照片\r\n    hasReviewPhotos() {\r\n      return this.taskInfo.reviewPhotos && this.taskInfo.reviewPhotos.length > 0;\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    this.taskId = options.taskId || options.id;\r\n    \r\n    if (this.taskId) {\r\n      this.loadTaskDetail();\r\n    } else {\r\n      this.loadError = '任务ID不能为空';\r\n    }\r\n  },\r\n  methods: {\r\n    // 加载任务详情\r\n    async loadTaskDetail() {\r\n      this.loading = true;\r\n      this.loadError = '';\r\n      \r\n      try {\r\n        const result = await callCloudFunction('hygiene-rectification', {\r\n          action: 'getRectificationDetail',\r\n          data: { id: this.taskId }\r\n        });\r\n\r\n        if (result && result.success && result.data) {\r\n          const task = result.data;\r\n          \r\n          this.taskInfo = {\r\n            id: task._id || task.id,\r\n            areaName: task.area_name || '未知责任区',\r\n            areaType: task.area_type || 'fixed',\r\n            status: task.status || 'pending',\r\n            problemDescription: this.getIssueDescription(task),\r\n            issueFoundDate: task.created_at,\r\n            assignee: this.getAssigneeName(task),\r\n            inspector: this.getInspectorName(task),\r\n            rectificationDescription: task.completion_description || '',\r\n            rectificationRequirement: task.rectification_requirement || '',\r\n            // 统一照片字段映射，与rectification-upload.vue保持一致\r\n            issuePhotos: this.processPhotos(task.photos || []),\r\n            rectificationPhotos: this.processPhotos(task.completion_photos || []),\r\n            reviewPhotos: this.processPhotos(task.review_photos || []),\r\n            reviewHistory: this.processReviewHistory(task.review_history || []),\r\n            // 审核信息\r\n            reviewComments: task.review_comments || '',\r\n            reviewDate: task.review_date || '',\r\n            reviewResult: task.review_result || '',\r\n            reviewerName: task.reviewer_name || ''\r\n          };\r\n        } else {\r\n          throw new Error(result?.message || '获取整改任务详情失败');\r\n        }\r\n      } catch (error) {\r\n        this.loadError = error.message || '加载失败，请稍后重试';\r\n        \r\n        uni.showModal({\r\n          title: '加载失败',\r\n          content: this.loadError,\r\n          showCancel: true,\r\n          cancelText: '返回',\r\n          confirmText: '重试',\r\n          success: (res) => {\r\n            if (res.confirm) {\r\n              this.loadTaskDetail();\r\n            } else {\r\n              uni.navigateBack();\r\n            }\r\n          }\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 重新加载\r\n    retryLoad() {\r\n      this.loadTaskDetail();\r\n    },\r\n\r\n    // 获取问题描述\r\n    getIssueDescription(task) {\r\n      // 直接从 description 字段获取（这是主要存储位置）\r\n      if (task.description) {\r\n        return task.description;\r\n      }\r\n      // 备用：从 issue 子对象中获取\r\n      if (task.issue && task.issue.description) {\r\n        return task.issue.description;\r\n      }\r\n      // 备用：从其他字段获取\r\n      return task.issue_description || task.title || '无问题描述';\r\n    },\r\n\r\n    // 获取负责人姓名\r\n    getAssigneeName(task) {\r\n      // 优先从 area_assignee_name 获取（云函数查询的责任区负责人）\r\n      if (task.area_assignee_name) {\r\n        return task.area_assignee_name;\r\n      }\r\n      // 其次从 assignee_name 获取\r\n      if (task.assignee_name) {\r\n        return task.assignee_name;\r\n      }\r\n      // 最后从 assigned_to_name 获取\r\n      return task.assigned_to_name || '未分配';\r\n    },\r\n\r\n    // 获取检查员姓名\r\n    getInspectorName(task) {\r\n      // 优先从 assigned_by_name 获取（任务分配者，通常是检查员）\r\n      if (task.assigned_by_name) {\r\n        return task.assigned_by_name;\r\n      }\r\n      // 其次从 inspector_name 获取\r\n      if (task.inspector_name) {\r\n        return task.inspector_name;\r\n      }\r\n      // 最后从 created_by_name 获取\r\n      return task.created_by_name || '未知检查员';\r\n    },\r\n\r\n    // 获取问题照片\r\n    getIssuePhotos(task) {\r\n      // 直接从 photos 字段获取问题照片（这是主要存储位置）\r\n      if (task.photos && Array.isArray(task.photos)) {\r\n        return task.photos;\r\n      }\r\n      // 备用：从 issue 子对象中获取照片\r\n      if (task.issue && task.issue.photos && Array.isArray(task.issue.photos)) {\r\n        return task.issue.photos;\r\n      }\r\n      // 备用：从其他可能的字段获取\r\n      if (task.issue_photos && Array.isArray(task.issue_photos)) {\r\n        return task.issue_photos;\r\n      }\r\n      return task.inspection_photos || [];\r\n    },\r\n\r\n    // 获取整改照片\r\n    getRectificationPhotos(task) {\r\n      // 优先从 completion_photos 获取（新的字段名）\r\n      if (task.completion_photos && Array.isArray(task.completion_photos)) {\r\n        return task.completion_photos;\r\n      }\r\n      // 其次从 rectification_photos 获取\r\n      if (task.rectification_photos && Array.isArray(task.rectification_photos)) {\r\n        return task.rectification_photos;\r\n      }\r\n      // 最后从 photos 获取\r\n      return task.photos || [];\r\n    },\r\n\r\n    // 处理照片数据\r\n    processPhotos(photos) {\r\n      if (!photos || !Array.isArray(photos)) return [];\r\n      \r\n      return photos.map(photo => {\r\n        if (typeof photo === 'string') {\r\n          return photo;\r\n        } else if (photo && typeof photo === 'object' && photo.url) {\r\n          return photo.url;\r\n        }\r\n        return '';\r\n      }).filter(url => url);\r\n    },\r\n\r\n    // 处理审核历史\r\n    processReviewHistory(history) {\r\n      if (!history || !Array.isArray(history)) return [];\r\n      \r\n      return history.map(review => ({\r\n        result: review.review_result || review.result,\r\n        comments: review.review_comments || review.comments || '',\r\n        reviewDate: review.review_date || review.created_at,\r\n        reviewer: review.reviewer_name || '未知审核员'\r\n      }));\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        'pending_rectification': '待整改',\r\n        'pending_assignment': '待分配',\r\n        'pending': '待处理',\r\n        'in_progress': '整改中',\r\n        'pending_review': '待复查',\r\n        'approved': '复查通过',\r\n        'verified': '整改合格',\r\n        'rejected': '整改不达标',\r\n        'completed': '已完成'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(dateString) {\r\n      if (!dateString) return '--';\r\n      \r\n      try {\r\n        let date;\r\n        if (typeof dateString === 'string') {\r\n          if (dateString.includes('T') || dateString.includes('Z')) {\r\n            date = new Date(dateString);\r\n          } else {\r\n            date = new Date(dateString.replace(/-/g, '/'));\r\n          }\r\n        } else {\r\n          date = new Date(dateString);\r\n        }\r\n        \r\n        if (isNaN(date.getTime())) {\r\n          return '--';\r\n        }\r\n        \r\n        return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;\r\n      } catch (error) {\r\n        return '--';\r\n      }\r\n    },\r\n\r\n    // 预览照片\r\n    previewPhoto(photos, index) {\r\n      uni.previewImage({\r\n        urls: photos,\r\n        current: index\r\n      });\r\n    },\r\n\r\n    // 前往审核页面\r\n    goToReview() {\r\n      uni.navigateTo({\r\n        url: `/pages/6s_pkg/rectification-review?taskId=${this.taskId}`\r\n      });\r\n    },\r\n\r\n    // 返回\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n\r\n    // 获取照片URL\r\n    getPhotoUrl(photo) {\r\n      return typeof photo === 'string' ? photo : (photo && photo.url ? photo.url : photo);\r\n    },\r\n\r\n    // 处理照片加载错误\r\n    onPhotoError(index, type) {\r\n      this.$set(this.photoErrors[type], index, true);\r\n    },\r\n\r\n    // 处理照片加载成功\r\n    onPhotoLoad(index, type) {\r\n      this.$set(this.photoErrors[type], index, false);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\r\n  width: 100%;\r\n  overflow-x: hidden;\r\n  box-sizing: border-box;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;\r\n  padding: 24rpx;\r\n}\r\n\r\n.card {\r\n  background: white;\r\n  border-radius: 16rpx;\r\n  margin-bottom: 24rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.card-header {\r\n  padding: 32rpx 32rpx 16rpx 32rpx;\r\n  border-bottom: 1rpx solid #F2F2F7;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n}\r\n\r\n.header-content {\r\n  flex: 1;\r\n}\r\n\r\n.card-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #1C1C1E;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.card-subtitle {\r\n  font-size: 26rpx;\r\n  color: #8E8E93;\r\n}\r\n\r\n.status-badge {\r\n  padding: 8rpx 16rpx;\r\n  border-radius: 8rpx;\r\n  font-size: 24rpx;\r\n  font-weight: 600;\r\n  \r\n  &.status-pending,\r\n  &.status-pending_rectification {\r\n    background: #FFF4E6;\r\n    color: #FF9500;\r\n  }\r\n  \r\n  &.status-pending_review {\r\n    background: #E6F3FF;\r\n    color: #007AFF;\r\n  }\r\n  \r\n  &.status-completed {\r\n    background: #E8F5E8;\r\n    color: #34C759;\r\n  }\r\n  \r\n  &.status-overdue,\r\n  &.status-rejected {\r\n    background: #FFE6E6;\r\n    color: #FF3B30;\r\n  }\r\n  \r\n  &.status-in_progress {\r\n    background: #F0F9FF;\r\n    color: #007AFF;\r\n  }\r\n}\r\n\r\n.card-body {\r\n  padding: 32rpx;\r\n}\r\n\r\n/* 信息网格 */\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 24rpx;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16rpx;\r\n  padding: 20rpx;\r\n  background: #F8F9FA;\r\n  border-radius: 12rpx;\r\n  border: 1rpx solid #E5E5EA;\r\n}\r\n\r\n.info-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  background: white;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\r\n  flex-shrink: 0;\r\n}\r\n\r\n.info-content {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.info-label {\r\n  font-size: 24rpx;\r\n  color: #8E8E93;\r\n  margin-bottom: 4rpx;\r\n}\r\n\r\n.info-value {\r\n  font-size: 28rpx;\r\n  color: #1C1C1E;\r\n  font-weight: 500;\r\n  word-break: break-all;\r\n}\r\n\r\n/* 内容区域 */\r\n.problem-content, .requirement-content, .rectification-content {\r\n  font-size: 28rpx;\r\n  color: #1C1C1E;\r\n  line-height: 1.6;\r\n  padding: 20rpx;\r\n  background: #F8F9FA;\r\n  border-radius: 12rpx;\r\n  border: 1rpx solid #E5E5EA;\r\n}\r\n\r\n.no-description {\r\n  padding: 40rpx 20rpx;\r\n  text-align: center;\r\n  color: #8E8E93;\r\n  font-size: 26rpx;\r\n  background: #F8F9FA;\r\n  border-radius: 12rpx;\r\n  border: 1rpx solid #E5E5EA;\r\n}\r\n\r\n/* 照片网格 */\r\n.photo-grid {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 12rpx;\r\n}\r\n\r\n.photo-item {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  border-radius: 12rpx;\r\n  overflow: hidden;\r\n  border: 1rpx solid #E5E5EA;\r\n}\r\n\r\n.photo-item {\r\n  position: relative;\r\n}\r\n\r\n.photo-item image {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.photo-error {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(248, 249, 250, 0.9);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8rpx;\r\n  border-radius: 12rpx;\r\n  \r\n  text {\r\n    font-size: 24rpx;\r\n    color: #8E8E93;\r\n  }\r\n}\r\n\r\n/* 审核记录时间线 */\r\n.review-timeline {\r\n  position: relative;\r\n}\r\n\r\n.review-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 16rpx;\r\n  margin-bottom: 32rpx;\r\n  position: relative;\r\n}\r\n\r\n.review-item:not(:last-child)::after {\r\n  content: '';\r\n  position: absolute;\r\n  left: 12rpx;\r\n  top: 32rpx;\r\n  width: 2rpx;\r\n  height: calc(100% + 16rpx);\r\n  background: #E5E5EA;\r\n}\r\n\r\n.review-dot {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n  border-radius: 50%;\r\n  flex-shrink: 0;\r\n  margin-top: 8rpx;\r\n  \r\n  &.review-approved {\r\n    background: #34C759;\r\n  }\r\n  \r\n  &.review-rejected {\r\n    background: #FF3B30;\r\n  }\r\n}\r\n\r\n.review-content {\r\n  flex: 1;\r\n  padding: 16rpx 20rpx;\r\n  background: #F8F9FA;\r\n  border-radius: 12rpx;\r\n  border: 1rpx solid #E5E5EA;\r\n}\r\n\r\n.review-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.review-result {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #1C1C1E;\r\n}\r\n\r\n.review-date {\r\n  font-size: 24rpx;\r\n  color: #8E8E93;\r\n}\r\n\r\n.review-comments {\r\n  font-size: 26rpx;\r\n  color: #1C1C1E;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 按钮 */\r\n.button-container {\r\n  padding: 32rpx 0;\r\n}\r\n\r\n.primary-button {\r\n  background: #007AFF;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 12rpx;\r\n  padding: 24rpx;\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  width: 100%;\r\n}\r\n\r\n.primary-button[disabled] {\r\n  background: #C7C7CC;\r\n  color: #8E8E93;\r\n}\r\n\r\n.secondary-button {\r\n  background: white;\r\n  color: #007AFF;\r\n  border: 2rpx solid #007AFF;\r\n  border-radius: 12rpx;\r\n  padding: 24rpx;\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  width: 100%;\r\n}\r\n\r\n/* 加载和错误状态 */\r\n.loading-container, .error-container {\r\n  padding: 120rpx 32rpx;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.loading-content, .error-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 24rpx;\r\n  text-align: center;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  border: 4rpx solid #E5E7EB;\r\n  border-top: 4rpx solid #007AFF;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.loading-text, .error-text {\r\n  font-size: 28rpx;\r\n  color: #8E8E93;\r\n  line-height: 1.4;\r\n}\r\n\r\n.retry-button {\r\n  background: #007AFF;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 8rpx;\r\n  padding: 16rpx 32rpx;\r\n  font-size: 26rpx;\r\n}\r\n\r\n/* 响应式 */\r\n@media (max-width: 414px) {\r\n  .info-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 16rpx;\r\n  }\r\n  \r\n  .photo-item {\r\n    width: 160rpx;\r\n    height: 160rpx;\r\n  }\r\n}\r\n</style>", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./inspector-rectification-detail.vue?vue&type=style&index=0&id=ccbb42d4&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./inspector-rectification-detail.vue?vue&type=style&index=0&id=ccbb42d4&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775842283\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}