{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s_pkg/issue-add.vue?3799", "webpack:///D:/Xwzc/pages/6s_pkg/issue-add.vue?ae28", "webpack:///D:/Xwzc/pages/6s_pkg/issue-add.vue?05fb", "webpack:///D:/Xwzc/pages/6s_pkg/issue-add.vue?d9d9", "uni-app:///pages/6s_pkg/issue-add.vue", "webpack:///D:/Xwzc/pages/6s_pkg/issue-add.vue?72fa", "webpack:///D:/Xwzc/pages/6s_pkg/issue-add.vue?8599"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "submitting", "draftSaving", "isEditingDraft", "currentDraftId", "isEditingPublished", "currentEditId", "pendingEditData", "showLocationSuggestions", "uploading", "uploadProgress", "autoUpload", "formData", "title", "description", "location", "responsible", "deadline", "priority", "images", "selectedResponsibleIndex", "selectedPriorityIndex", "<PERSON><PERSON><PERSON><PERSON>", "priorityOptions", "value", "text", "commonLocations", "allLocationSuggestions", "locationCategories", "locationConfigLoading", "onLoad", "uni", "onShow", "computed", "todayDate", "canSubmit", "canSaveDraft", "responsibleDisplayName", "filteredCategorizedSuggestions", "map", "filteredItems", "category", "items", "filter", "methods", "goBack", "loadLocationConfig", "fetchLocationConfigFromServer", "require", "callCloudFunction", "action", "result", "serverConfig", "version", "lastUpdated", "locations", "loadLocationConfigFromCache", "showLocationConfigError", "content", "showCancel", "confirmText", "success", "loadCommonLocations", "selectLocation", "clearLocation", "onLocationInput", "onLocationFocus", "isFavorite", "toggleFavorite", "favoriteLocations", "icon", "duration", "remove<PERSON>rom<PERSON><PERSON>mon", "loadResponsiblePersons", "filteredUsers", "final<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onResponsibleChange", "onDeadlineChange", "selectPriority", "onPriorityChange", "getPriorityText", "chooseImage", "count", "sizeType", "sourceType", "fail", "processNewPhotos", "url", "uploaded", "cloudUrl", "cloudPath", "autoUploadNewPhotos", "uploadPromises", "photoIndex", "index", "uploadResult", "Object", "cloudReady", "error", "Promise", "results", "failures", "uploadSinglePhoto", "uploadUtils", "fileInfo", "size", "generateCloudPath", "deleteImage", "photo", "uniCloud", "fileList", "extractFileId", "previewPhoto", "urls", "current", "getPhotoDisplayUrl", "preloadCloudImage", "src", "validateForm", "submitForm", "submitData", "location_info", "location_type", "location_name", "location_description", "inspection_info", "inspection_date", "inspection_type", "severity", "assigned_to", "assigned_to_name", "photos", "tags", "expected_completion_date", "apiData", "issue_id", "eventData", "issueId", "issueNumber", "issueData", "fromDraft", "setTimeout", "draftId", "preparePhotosForSubmit", "timestamp", "getPriorityToSeverity", "getResponsibleId", "getResponsibleName", "clearDraftIfExists", "saveDraft", "draftData", "displayUrl", "createdAt", "updatedAt", "status", "id", "saveDraftToLocal", "existingDrafts", "loadDraft", "responsible_id", "loadEditData", "setEditSelectors", "processEditImages", "toggleAutoUpload", "hideSuggestions"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvFA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,6nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACwQvnB;AAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC,kBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;MACA;MACA;MACAC;QACAlB;MACA;IACA;;IAEA;IACA;MACA;MACA;MACAkB;QACAlB;MACA;IACA;;IAEA;IACA;IACA;IACA;EACA;EAEAmB;IACA;IACA;;IAEA;IACA;IACA;IAEA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA,2BACA,8BACA,oCACA,iCACA,6BACA;IACA;IAEAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;MACA;MAEA;MAEA,+BACAC;QACA;;QAEA;QACA;UACAC;YAAA,OACAzB;UAAA,EACA;QACA;;QAEA;QACAyB;UAAA,OACA;QAAA,EACA;QAEA,uCACAC;UACAC;QAAA;MAEA,GACAC;QAAA;MAAA;IACA;EAGA;;EACAC;IACAC;MACAd;IACA;IAEA;IACAe;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACA;kBACA;gBACA;;gBAEA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;;gBAEA;gBACA;kBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,WAEAC;gBAAA;gBAAA,OAEAC;kBACAC;gBACA;cAAA;gBAFAC;gBAAA,MAIAA;kBAAA;kBAAA;gBAAA;gBACAC;kBACAC;kBACAC;kBACAC;gBACA,GAEA;gBACA;;gBAEA;gBACA;kBACA;gBACA;;gBAEA;gBACAxB;gBACAA;gBAAA;gBAAA;cAAA;gBAAA,MAIA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAMA;IAEAyB;MACA;QACA;QACA;;QAEA;QACA;QACA;QAEA;UACA;UACA;UAEA;YACA;UACA;QAGA;MAEA;QACA;MAAA;IAEA;IAEAC;MAAA;MACA;MACA1B;QACAlB;QACA6C;QACAC;QACAC;QACAC;UACA;YACA;YACA;UACA;QACA;MACA;;MAEA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;MACA;QACA;MAAA;IAEA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QAEA;UACA;UACAC;UACAtC;YACAlB;YACAyD;YACAC;UACA;QACA;UACA;UACAF;UACAtC;YACAlB;YACAyD;YACAC;UACA;QACA;QAEAxC;;QAEA;QACA;MACA;QACAA;UACAlB;UACAyD;QACA;MACA;IACA;IAEA;IACAE;MAAA;MACAzC;QACAlB;QACA6C;QACAG;UACA;YACA;cACA;cACA;cACA;cAEA;gBACAQ;gBACAtC;cACA;;cAEA;cACA;cAEAA;gBACAlB;gBACAyD;gBACAC;cACA;YACA;cACAxC;gBACAlB;gBACAyD;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,YAEAzB,iGAEA;gBAAA;gBAAA,OACAC;kBACAC;gBACA;cAAA;gBAFAC;gBAIA;kBACA;kBACAuB,4BACA/B;oBACA;oBACA;oBACA;;oBAEA;oBACA;;oBAEA;oBACA;oBACA;;oBAEA;oBACA;oBAEA;kBACA,GACAJ;oBAAA;sBACAf;sBACAC;oBACA;kBAAA,GACAkB;oBAAA;kBAAA;kBAEA;kBACAgC,2BACA;oBAAAnD;oBAAAC;kBAAA,2CACAiD,gBACA/B;oBACA;oBACA;kBACA,IAEA;kBACA;;kBAGA;kBACA;oBACA;oBACA;kBACA;oBACA;oBACA;kBACA;gBACA;kBACAZ;oBACAlB;oBACAyD;kBACA;kBACA;kBACA,6BACA;oBAAA9C;oBAAAC;kBAAA,EACA;kBACA;kBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAM;kBACAlB;kBACAyD;gBACA;gBACA;gBACA,6BACA;kBAAA9C;kBAAAC;gBAAA,EACA;gBACA;gBACA;kBACA;gBACA;;gBAEA;gBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAmD;MACA;MACA;;MAEA;MACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QAAA;MAAA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACAlD;UACAlB;UACAyD;QACA;QACA;MACA;MAEAvC;QACAmD;QACAC;QAAA;QACAC;QACAvB;UAAA;UACA;;UAEA;UACA;;UAEA;UACA;YACA;UACA;QACA;QACAwB;UACAtD;YACAlB;YACAyD;UACA;QACA;MACA;IACA;IAEA;IACAgB;MACA;QAAA;UACAC;UACAC;UACAC;UACAC;UACAjF;QACA;MAAA;IACA;IAEA;IACAkF;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAC;kBAAA;oBAAA;oBAAA;sBAAA;wBAAA;0BAAA;4BACAC;8BAAA;4BAAA;4BAAA,MAEAA;8BAAA;8BAAA;4BAAA;4BAAA;8BAAAhC;8BAAAiC;4BAAA;0BAAA;4BAAA;4BAGA;4BACA;;4BAEA;4BAAA;4BAAA,OACA;0BAAA;4BAAAC;4BAAA,KAEAA;8BAAA;8BAAA;4BAAA;4BACA;4BACAC;8BACAR;8BACAC;8BACAC;8BACAjF;8BACAwF;4BACA;;4BAEA;4BACA;4BAAA,kCACA;8BAAApC;8BAAAiC;4BAAA;0BAAA;4BAAA,MAEA;0BAAA;4BAAA;4BAAA;0BAAA;4BAAA;4BAAA;4BAGA;4BAAA,kCACA;8BAAAjC;8BAAAiC;8BAAAI;4BAAA;0BAAA;0BAAA;4BAAA;wBAAA;sBAAA;oBAAA;kBAAA,CAEA;kBAAA;oBAAA;kBAAA;gBAAA,MAEA;gBAAA;gBAAA,OACAC;cAAA;gBAAAC;gBAEA;gBACAC,mBACA1D;kBAAA;gBAAA,GACAJ;kBAAA;gBAAA;gBAEA;kBACAR;oBACAlB;oBACAyD;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA+B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAZ,yCAEA;gBAAA;gBAAA,OACAa;cAAA;gBAAAR;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEAQ;cAAA;gBAAAC;gBAAA,kCAEA;kBACA3C;kBACA6B;kBACAH;kBACAkB;gBACA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,kCAGA;kBAAA5C;kBAAAqC;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAQ;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACAb;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIAc,wCAEA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAEA7E;kBAAAlB;gBAAA;;gBAEA;gBAAA;gBAAA,OACAgG;kBACA9G;kBACAC;oBACA8G;kBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA/E;kBACAlB;kBACAyD;gBACA;cAAA;gBAAA;gBAEAvC;gBAAA;cAAA;gBAIA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAgF;MACA;QACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QAAA;MAAA;MACAjF;QACAkF;QACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MACArF;QACAsF;QACAxD;UACA;YACA;UACA;QACA;QACAwB;UACA;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAiC;MACA;QACAvF;UACAlB;UACAyD;QACA;QACA;MACA;MAEA;QACAvC;UACAlB;UACAyD;QACA;QACA;MACA;MAEA;QACAvC;UACAlB;UACAyD;QACA;QACA;MACA;MAEA;QACAvC;UACAlB;UACAyD;QACA;QACA;MACA;MAEA;QACAvC;UACAlB;UACAyD;QACA;QACA;MACA;MAEA;QACAvC;UACAlB;UACAyD;QACA;QACA;MACA;MAEA;QACAvC;UACAlB;UACAyD;QACA;QACA;MACA;MAEA;IACA;IAEAiD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAKA;gBAAA,YAEAvE,iGAEA;gBACAwE;kBACA3G;kBACAC;kBACA2G;oBACAC;oBACAC;oBACAC;kBACA;kBACAC;oBACAC;oBACAC;kBACA;kBACAtF;kBAAA;kBACAuF;kBACA9G;kBACA+G;kBACAC;kBACAC;kBACAC;kBACAC;gBACA,GAEA;gBACAnF;gBACAoF;kBACAC;gBAAA,GACAf,cACAA;gBAAA;gBAAA,OAEAvE;kBACAC;kBACAlD;gBACA;cAAA;gBAHAmD;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACApB;kBACAlB;kBACAyD;gBACA;;gBAEA;gBACAkE;kBACAtF;kBACAuF;kBACAC;kBACAC;kBACAC;gBACA;;gBACA7G;;gBAEA;gBACA;;gBAEA;gBACA;kBACA8G;oBACA;sBACA3F;sBACA4F;oBACA;oBACA/G;kBACA;gBACA;;gBAEA;gBACA8G;kBACA9G;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAA;kBACAlB;kBACAyD;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAyE;MACA,4BACApG;QAAA;MAAA;MAAA,CACAJ;QAAA;UACAgD;UACAzE;UACAkI;QACA;MAAA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAIA;IACAC;MAAA;MACA;QACA;UACA;UACA;YAAA;UAAA;UACArH;QACA;UACA;QAAA;MAEA;IACA;IAEAsH;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;kBACA;;kBAEA;;kBAEA;kBACAC,4CACA;oBACA;oBACAnI;sBACA;wBACA;sBACA;sBACA;sBACA;wBACA,uCACAyF;0BACArB;0BAAA;0BACAgE;wBAAA;sBAEA;;sBACA;oBACA;;oBACAC;oBACAC;oBACAC;oBAAA;oBACAC;kBAAA,IAGA;;kBACA;kBAEA5H;oBACAlB;oBACAyD;kBACA;;kBAEA;kBACAvC;oBACAmB;oBACA4F;oBACAQ;kBACA;;kBAEA;kBACAT;oBACA9G;kBACA;gBAEA;kBACAA;oBACAlB;oBACAyD;kBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAsF;MAAA;MACA;QACA;QAEA;UACA;UACA;YAAA;UAAA;UACA;YACAC;cAAAF;YAAA;UACA;QACA;UACA;UACAE;QACA;;QAEA;QACA9H;MAGA;QACA;MAAA;IAEA;IAEA;IACA+H;MACA;QACA;QACA;UAAA;QAAA;QAEA;UACA;UACA;UACA;;UAEA;UACA;YACAC;YACA7I;UACA;QAEA;MACA;QACA;MAAA;IAEA;IAEA;IACA8I;MACA;QACA;QACA;QACA;QACA;;QAEA;QACA;UACAnJ;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;;QAEA;QACA;MAEA;QACAY;UACAlB;UACAyD;QACA;MACA;IACA;IAEA;IACA2F;MACA;MACA;QACA;UAAA;QAAA;QAEA;UACA;UACA;QACA;MACA;;MAEA;MACA;QACA;UAAA;QAAA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;UACA;YACA3E;YACAE;YACAC;YACAF;YACA/E;UACA;QACA;UACA;YACA8E;YACAE;YACAC;YACAF;YACA/E;UACA;QACA;QACA;MACA;IACA;IAEA;IACA0J;MACA;MACApI;QACAlB;QACAyD;MACA;IACA;IAEA;IACA8F;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACh4CA;AAAA;AAAA;AAAA;AAAsqC,CAAgB,4oCAAG,EAAC,C;;;;;;;;;;;ACA1rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/issue-add.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/issue-add.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./issue-add.vue?vue&type=template&id=2242e83e&scoped=true&\"\nvar renderjs\nimport script from \"./issue-add.vue?vue&type=script&lang=js&\"\nexport * from \"./issue-add.vue?vue&type=script&lang=js&\"\nimport style0 from \"./issue-add.vue?vue&type=style&index=0&id=2242e83e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2242e83e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/issue-add.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./issue-add.vue?vue&type=template&id=2242e83e&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.commonLocations.length\n  var g1 = _vm.showLocationSuggestions ? _vm.commonLocations.length : null\n  var g2 = _vm.showLocationSuggestions\n    ? _vm.filteredCategorizedSuggestions.length\n    : null\n  var l1 =\n    _vm.showLocationSuggestions && g2 > 0\n      ? _vm.__map(\n          _vm.filteredCategorizedSuggestions,\n          function (category, categoryIndex) {\n            var $orig = _vm.__get_orig(category)\n            var l0 = _vm.__map(category.items, function (location, itemIndex) {\n              var $orig = _vm.__get_orig(location)\n              var m0 = _vm.isFavorite(location)\n              var m1 = _vm.isFavorite(location)\n              return {\n                $orig: $orig,\n                m0: m0,\n                m1: m1,\n              }\n            })\n            return {\n              $orig: $orig,\n              l0: l0,\n            }\n          }\n        )\n      : null\n  var g3 = _vm.showLocationSuggestions\n    ? !_vm.commonLocations.length && !_vm.filteredCategorizedSuggestions.length\n    : null\n  var l2 = _vm.__map(_vm.formData.images, function (photo, index) {\n    var $orig = _vm.__get_orig(photo)\n    var m2 = _vm.getPhotoDisplayUrl(photo)\n    return {\n      $orig: $orig,\n      m2: m2,\n    }\n  })\n  var g4 = _vm.formData.images.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        l1: l1,\n        g3: g3,\n        l2: l2,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./issue-add.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./issue-add.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"page-container\" @click=\"hideSuggestions\">\r\n    <view class=\"form-container\">\r\n      <!-- 问题标题 -->\r\n      <view class=\"form-item\">\r\n        <view class=\"form-label\">问题标题 <text class=\"required\">*</text></view>\r\n        <input \r\n          class=\"form-input\" \r\n          v-model=\"formData.title\" \r\n          placeholder=\"请输入问题标题\"\r\n          maxlength=\"50\"\r\n        />\r\n      </view>\r\n\r\n      <!-- 问题描述 -->\r\n      <view class=\"form-item\">\r\n        <view class=\"form-label\">问题描述 <text class=\"required\">*</text></view>\r\n        <textarea \r\n          class=\"form-textarea\"\r\n          v-model=\"formData.description\" \r\n          placeholder=\"详细描述发现的问题\"\r\n          maxlength=\"200\"\r\n        />\r\n      </view>\r\n\r\n      <!-- 问题位置 -->\r\n      <view class=\"form-item location-form-item\">\r\n        <view class=\"form-label\">问题位置 <text class=\"required\">*</text></view>\r\n        <view class=\"location-selector\">\r\n          <!-- 常用位置快选 -->\r\n          <view class=\"quick-locations\" v-if=\"commonLocations.length > 0\">\r\n            <view class=\"quick-location-label\">常用位置</view>\r\n            <view class=\"location-tags\">\r\n              <view \r\n                v-for=\"location in commonLocations\" \r\n                :key=\"location\"\r\n                class=\"location-tag\"\r\n                :class=\"{ 'active': formData.location === location }\"\r\n                @click=\"selectLocation(location)\"\r\n              >\r\n                {{ location }}\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 位置输入框 -->\r\n          <view class=\"location-input-wrapper\">\r\n            <input \r\n              class=\"form-input location-input\"\r\n              v-model=\"formData.location\" \r\n              placeholder=\"选择常用位置或手动输入\"\r\n              maxlength=\"30\"\r\n              @input=\"onLocationInput\"\r\n              @focus.stop=\"onLocationFocus\"\r\n              @click.stop\r\n            />\r\n            <view \r\n              v-if=\"formData.location\" \r\n              class=\"clear-location\" \r\n              @click.stop=\"clearLocation\"\r\n              @touchstart.stop=\"clearLocation\"\r\n            >\r\n              <uni-icons type=\"clear\" size=\"16\" color=\"#C7C7CC\" />\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 位置建议列表 -->\r\n          <view \r\n            v-if=\"showLocationSuggestions\" \r\n            class=\"location-suggestions\"\r\n            @click.stop\r\n          >\r\n            <!-- 常用位置（带删除功能） -->\r\n            <view v-if=\"commonLocations.length > 0\" class=\"suggestion-section\">\r\n              <view \r\n                v-for=\"(location, index) in commonLocations\" \r\n                :key=\"index\"\r\n                class=\"suggestion-item common-location\"\r\n              >\r\n                <view class=\"location-content\" @click=\"selectLocation(location)\">\r\n                  <uni-icons type=\"star-filled\" size=\"14\" color=\"#FFB800\" />\r\n                  <text class=\"suggestion-text\">{{ location }}</text>\r\n                </view>\r\n                <view class=\"delete-common\" @click.stop=\"removeFromCommon(location, index)\">\r\n                  <uni-icons type=\"clear\" size=\"16\" color=\"#C7C7CC\" />\r\n                </view>\r\n              </view>\r\n            </view>\r\n            \r\n            <!-- 分类位置建议 -->\r\n            <view v-if=\"filteredCategorizedSuggestions.length > 0\" class=\"suggestion-section location-section\">\r\n              <view \r\n                v-for=\"(category, categoryIndex) in filteredCategorizedSuggestions\" \r\n                :key=\"categoryIndex\"\r\n                class=\"category-group\"\r\n              >\r\n\r\n                <view \r\n                  v-for=\"(location, itemIndex) in category.items\" \r\n                  :key=\"itemIndex\"\r\n                  class=\"suggestion-item regular-location\"\r\n                >\r\n                  <view class=\"location-content\" @click=\"selectLocation(location)\">\r\n                    <uni-icons type=\"location\" size=\"14\" color=\"#007AFF\" />\r\n                    <text class=\"suggestion-text\">{{ location }}</text>\r\n                  </view>\r\n                  <view class=\"star-btn\" @click.stop=\"toggleFavorite(location)\">\r\n                    <uni-icons \r\n                      :type=\"isFavorite(location) ? 'star-filled' : 'star'\" \r\n                      :size=\"16\" \r\n                      :color=\"isFavorite(location) ? '#FFD700' : '#CCCCCC'\"\r\n                    />\r\n                  </view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n            \r\n            <!-- 无匹配结果 -->\r\n            <view v-if=\"!commonLocations.length && !filteredCategorizedSuggestions.length\" class=\"no-suggestions\">\r\n              <text class=\"no-suggestions-text\">暂无匹配的位置</text>\r\n              <text class=\"no-suggestions-hint\">您可以直接输入位置名称</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 负责人 -->\r\n      <view class=\"form-item\">\r\n        <view class=\"form-label\">负责人 <text class=\"required\">*</text></view>\r\n        <picker \r\n          :value=\"selectedResponsibleIndex\" \r\n          :range=\"responsiblePersons\" \r\n          range-key=\"text\"\r\n          @change=\"onResponsibleChange\"\r\n        >\r\n          <view class=\"form-picker\">\r\n            {{ responsibleDisplayName }}\r\n            <text class=\"picker-arrow\">></text>\r\n          </view>\r\n        </picker>\r\n      </view>\r\n\r\n      <!-- 整改期限 -->\r\n      <view class=\"form-item\">\r\n        <view class=\"form-label\">整改期限 <text class=\"required\">*</text></view>\r\n        <picker \r\n          mode=\"date\" \r\n          :value=\"formData.deadline\" \r\n          :start=\"todayDate\"\r\n          @change=\"onDeadlineChange\"\r\n        >\r\n          <view class=\"form-picker\">\r\n            {{ formData.deadline || '请选择整改期限' }}\r\n            <text class=\"picker-arrow\">></text>\r\n          </view>\r\n        </picker>\r\n      </view>\r\n\r\n      <!-- 优先级 -->\r\n      <view class=\"form-item\">\r\n        <view class=\"form-label\">优先级</view>\r\n        <view class=\"priority-buttons\">\r\n          <view \r\n            v-for=\"(option, index) in priorityOptions\" \r\n            :key=\"index\"\r\n            class=\"priority-btn\"\r\n            :class=\"{ \r\n              'priority-btn-active': formData.priority === option.value,\r\n              'priority-btn-urgent': option.value === 'urgent'\r\n            }\"\r\n            @click=\"selectPriority(option.value, index)\"\r\n          >\r\n            <uni-icons \r\n              :type=\"option.value === 'urgent' ? 'fire' : 'info'\" \r\n              :size=\"16\" \r\n              :color=\"formData.priority === option.value ? '#ffffff' : '#666666'\"\r\n            />\r\n            <text class=\"priority-text\">{{ option.text }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 照片上传 -->\r\n      <view class=\"form-item photo-upload-item\">\r\n        <view class=\"upload-section\">\r\n          <view class=\"section-header\">\r\n            <view class=\"form-label\">问题照片</view>\r\n            <view class=\"auto-upload-toggle\" @click=\"toggleAutoUpload\">\r\n              <view class=\"toggle-label\">自动上传</view>\r\n              <view class=\"toggle-switch\" :class=\"{ active: autoUpload }\">\r\n                <view class=\"toggle-circle\"></view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view class=\"image-upload photo-upload-content\">\r\n          <view class=\"photo-grid\">\r\n            <view \r\n              v-for=\"(photo, index) in formData.images\" \r\n              :key=\"index\"\r\n              class=\"photo-item\"\r\n            >\r\n              <image :src=\"getPhotoDisplayUrl(photo)\" mode=\"aspectFill\" @click=\"previewPhoto(index)\" />\r\n              <!-- 上传状态指示器 -->\r\n              <view v-if=\"photo.uploading\" class=\"photo-uploading\">\r\n                <view class=\"upload-spinner\"></view>\r\n              </view>\r\n              <view v-else-if=\"photo.uploaded\" class=\"photo-uploaded\">\r\n                <uni-icons type=\"checkmarkempty\" size=\"16\" color=\"white\"></uni-icons>\r\n              </view>\r\n              <view class=\"photo-delete\" @click=\"deleteImage(index)\">\r\n                <uni-icons type=\"close\" size=\"18\" color=\"white\"></uni-icons>\r\n              </view>\r\n            </view>\r\n            <view \r\n              v-if=\"formData.images.length < 6\" \r\n              class=\"photo-add\"\r\n              @click=\"chooseImage\"\r\n              :class=\"{ disabled: uploading }\"\r\n            >\r\n              <uni-icons type=\"camera\" size=\"32\" color=\"#8E8E93\" />\r\n              <text class=\"add-text\">添加图片</text>\r\n            </view>\r\n          </view>\r\n          <!-- 上传进度 -->\r\n          <view v-if=\"uploading\" class=\"upload-progress\">\r\n            <view class=\"progress-bar\">\r\n              <view class=\"progress-fill\" :style=\"{ width: uploadProgress + '%' }\"></view>\r\n            </view>\r\n            <text class=\"progress-text\">正在上传照片... {{ uploadProgress }}%</text>\r\n          </view>\r\n          <view class=\"photo-tip\">最多可上传6张照片，建议拍摄问题的清晰照片</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 提交按钮 -->\r\n    <view class=\"submit-section\">\r\n      <view class=\"button-group\">\r\n        <button \r\n          class=\"draft-btn\" \r\n          @click=\"saveDraft\" \r\n          :disabled=\"!canSaveDraft\"\r\n          :class=\"{ 'disabled': !canSaveDraft }\"\r\n        >\r\n          {{ draftSaving ? '保存中...' : '保存草稿' }}\r\n        </button>\r\n        <button \r\n          class=\"submit-btn\" \r\n          @click=\"submitForm\" \r\n          :disabled=\"!canSubmit && !submitting\"\r\n          :class=\"{ 'loading': submitting }\"\r\n        >\r\n          <view v-if=\"submitting\" class=\"button-loading\">\r\n            <text>{{ isEditingPublished ? '更新中...' : '发布中...' }}</text>\r\n          </view>\r\n          <text v-else>{{ isEditingPublished ? '保存修改' : '立即发布' }}</text>\r\n        </button>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport uploadUtils from '@/utils/upload-utils.js'\r\n\r\nexport default {\r\n  name: 'IssueAdd',\r\n  data() {\r\n    return {\r\n      submitting: false,\r\n      draftSaving: false,\r\n      isEditingDraft: false,\r\n      currentDraftId: null,\r\n      isEditingPublished: false,\r\n      currentEditId: null,\r\n      pendingEditData: null,\r\n      showLocationSuggestions: false,\r\n      uploading: false,\r\n      uploadProgress: 0,\r\n      autoUpload: true, // 自动上传开关\r\n      formData: {\r\n        title: '',\r\n        description: '',\r\n        location: '',\r\n        responsible: '',\r\n        deadline: '',\r\n        priority: 'normal',\r\n        images: [] // 现在存储包含上传状态的对象数组\r\n      },\r\n      selectedResponsibleIndex: 0, // 默认选择第一项\"请选择负责人\"\r\n      selectedPriorityIndex: 0, // 默认选中一般问题\r\n      responsiblePersons: [],\r\n      priorityOptions: [\r\n        { value: 'normal', text: '一般问题' },\r\n        { value: 'urgent', text: '紧急问题' }\r\n      ],\r\n      // 常用位置列表（基于历史使用频率）\r\n      commonLocations: [],\r\n      // 所有位置建议（动态加载）\r\n      allLocationSuggestions: [],\r\n      // 位置分类数据（用于分类显示）\r\n      locationCategories: [],\r\n      // 位置配置加载状态\r\n      locationConfigLoading: false\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    // 重置编辑状态，确保每次进入页面时状态干净\r\n    this.isEditingPublished = false;\r\n    this.isEditingDraft = false;\r\n    this.currentEditId = null;\r\n    this.currentDraftId = null;\r\n    this.pendingEditData = null;\r\n    \r\n    // 检查是否是编辑草稿\r\n    if (options.draftId) {\r\n      this.loadDraft(options.draftId);\r\n      // 设置草稿编辑模式的导航标题\r\n      uni.setNavigationBarTitle({\r\n        title: '编辑草稿'\r\n      });\r\n    }\r\n    \r\n    // 检查是否是编辑已发布的问题\r\n    if (options.editId && options.editData) {\r\n      this.loadEditData(options.editId, options.editData);\r\n      // 设置编辑模式的导航标题\r\n      uni.setNavigationBarTitle({\r\n        title: '编辑问题'\r\n      });\r\n    }\r\n    \r\n    // 加载位置配置、常用位置和负责人列表\r\n    this.loadLocationConfig();\r\n    this.loadCommonLocations();\r\n    this.loadResponsiblePersons();\r\n  },\r\n\r\n  onShow() {\r\n    // 页面显示时刷新常用位置（可能在其他页面有变化）\r\n    this.loadCommonLocations();\r\n    \r\n    // 检查负责人列表是否需要刷新（如果为空或只有\"请选择\"选项）\r\n    // 但是如果正在编辑（有编辑ID或草稿ID），则不重新加载，避免重置选择器\r\n    const isEditing = this.currentEditId || this.currentDraftId || this.isEditingPublished || this.isEditingDraft;\r\n    \r\n    if (this.responsiblePersons.length <= 1 && !isEditing) {\r\n      this.loadResponsiblePersons();\r\n    }\r\n  },\r\n  computed: {\r\n    todayDate() {\r\n      const today = new Date();\r\n      const year = today.getFullYear();\r\n      const month = String(today.getMonth() + 1).padStart(2, '0');\r\n      const day = String(today.getDate()).padStart(2, '0');\r\n      return `${year}-${month}-${day}`;\r\n    },\r\n    \r\n    canSubmit() {\r\n      return !this.submitting && \r\n             this.formData.title.trim() && \r\n             this.formData.description.trim() && \r\n             this.formData.location.trim() && \r\n             this.formData.responsible && \r\n             this.formData.deadline;\r\n    },\r\n    \r\n    canSaveDraft() {\r\n      // 草稿只需要标题即可保存\r\n      return !this.draftSaving && this.formData.title.trim();\r\n    },\r\n    \r\n    // 负责人显示名称 - 改为computed属性\r\n            responsibleDisplayName() {\r\n          if (this.selectedResponsibleIndex > 0 && this.responsiblePersons[this.selectedResponsibleIndex]) {\r\n            return this.responsiblePersons[this.selectedResponsibleIndex].text;\r\n          }\r\n          return '请选择负责人';\r\n        },\r\n    \r\n    // 过滤后的分类位置建议\r\n    filteredCategorizedSuggestions() {\r\n      if (!this.locationCategories || this.locationCategories.length === 0) {\r\n        return [];\r\n      }\r\n      \r\n      const input = this.formData.location.toLowerCase().trim();\r\n      \r\n      return this.locationCategories\r\n        .map(category => {\r\n          let filteredItems = category.items || [];\r\n          \r\n          // 如果有输入内容，进行筛选\r\n          if (input) {\r\n            filteredItems = filteredItems.filter(location => \r\n              location.toLowerCase().includes(input)\r\n            );\r\n          }\r\n          \r\n          // 排除已在常用位置中的项目\r\n          filteredItems = filteredItems.filter(location => \r\n            !this.commonLocations.includes(location)\r\n          );\r\n          \r\n          return {\r\n            ...category,\r\n            items: filteredItems.slice(0, 5) // 每个分类最多显示5个\r\n          };\r\n        })\r\n        .filter(category => category.items.length > 0); // 只显示有内容的分类\r\n    },\r\n    \r\n\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack()\r\n    },\r\n    \r\n    // 位置相关方法\r\n    async loadLocationConfig() {\r\n      try {\r\n        this.locationConfigLoading = true;\r\n        \r\n        // 1. 尝试从服务器获取最新配置\r\n        await this.fetchLocationConfigFromServer();\r\n        \r\n        // 2. 如果服务器获取失败，使用本地缓存\r\n        if (this.allLocationSuggestions.length === 0) {\r\n          this.loadLocationConfigFromCache();\r\n        }\r\n        \r\n        // 3. 如果本地也没有，显示错误提示\r\n        if (this.allLocationSuggestions.length === 0) {\r\n          this.showLocationConfigError();\r\n        }\r\n        \r\n      } catch (error) {\r\n        this.loadLocationConfigFromCache();\r\n        \r\n        // 如果缓存也失败了，显示错误提示\r\n        if (this.allLocationSuggestions.length === 0) {\r\n          this.showLocationConfigError();\r\n        }\r\n      } finally {\r\n        this.locationConfigLoading = false;\r\n      }\r\n    },\r\n    \r\n    async fetchLocationConfigFromServer() {\r\n      try {\r\n        const { callCloudFunction } = require('@/utils/auth.js');\r\n        \r\n        const result = await callCloudFunction('hygiene-location-management', {\r\n          action: 'getLocationConfig'\r\n        });\r\n        \r\n        if (result && result.success && result.data && result.data.locations) {\r\n          const serverConfig = {\r\n            version: '1.0.0',\r\n            lastUpdated: new Date().toISOString(),\r\n            locations: result.data.locations\r\n          };\r\n          \r\n          // 设置分类数据\r\n          this.locationCategories = serverConfig.locations;\r\n          \r\n          // 展平所有位置\r\n          this.allLocationSuggestions = serverConfig.locations.reduce((acc, category) => {\r\n            return acc.concat(category.items || []);\r\n          }, []);\r\n          \r\n          // 缓存到本地\r\n          uni.setStorageSync('location_config', serverConfig);\r\n          uni.setStorageSync('location_config_timestamp', Date.now());\r\n          \r\n\r\n        } else {\r\n          throw new Error('获取位置配置失败');\r\n        }\r\n        \r\n      } catch (error) {\r\n        throw error;\r\n      }\r\n    },\r\n    \r\n    loadLocationConfigFromCache() {\r\n      try {\r\n        const cachedConfig = uni.getStorageSync('location_config');\r\n        const cacheTimestamp = uni.getStorageSync('location_config_timestamp');\r\n        \r\n        // 检查缓存是否过期（7天）\r\n        const cacheAge = Date.now() - (cacheTimestamp || 0);\r\n        const cacheExpired = cacheAge > 7 * 24 * 60 * 60 * 1000;\r\n        \r\n        if (cachedConfig && !cacheExpired) {\r\n          // 设置分类数据\r\n          this.locationCategories = cachedConfig.locations;\r\n          \r\n          this.allLocationSuggestions = cachedConfig.locations.reduce((acc, category) => {\r\n            return acc.concat(category.items);\r\n          }, []);\r\n          \r\n\r\n        }\r\n        \r\n      } catch (error) {\r\n        // 缓存加载失败，使用空数据\r\n      }\r\n    },\r\n    \r\n    showLocationConfigError() {\r\n      // 显示位置配置加载失败的错误提示\r\n      uni.showModal({\r\n        title: '位置配置加载失败',\r\n        content: '无法获取位置配置数据，请检查网络连接或联系管理员。',\r\n        showCancel: false,\r\n        confirmText: '重试',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 用户点击重试，重新加载位置配置\r\n            this.loadLocationConfig();\r\n          }\r\n        }\r\n      });\r\n      \r\n      // 设置空的位置配置，确保页面不崩溃\r\n      this.locationCategories = [];\r\n      this.allLocationSuggestions = [];\r\n    },\r\n\r\n    loadCommonLocations() {\r\n      try {\r\n        // 从收藏列表加载常用位置\r\n        const favoriteLocations = uni.getStorageSync('favorite_locations') || [];\r\n        this.commonLocations = favoriteLocations;\r\n      } catch (error) {\r\n        // 加载常用位置失败，使用空数据\r\n      }\r\n    },\r\n    \r\n    selectLocation(location) {\r\n      this.formData.location = location;\r\n      this.showLocationSuggestions = false;\r\n      // 移除自动添加到常用位置的逻辑，改为手动星标收藏\r\n    },\r\n    \r\n    clearLocation() {\r\n      this.formData.location = '';\r\n      this.showLocationSuggestions = false; // 清空时隐藏建议列表\r\n    },\r\n    \r\n    onLocationInput() {\r\n      this.showLocationSuggestions = true;\r\n    },\r\n\r\n    onLocationFocus() {\r\n      this.showLocationSuggestions = true;\r\n    },\r\n    \r\n    // 检查位置是否被收藏\r\n    isFavorite(location) {\r\n      return this.commonLocations.includes(location);\r\n    },\r\n    \r\n    // 切换收藏状态\r\n    toggleFavorite(location) {\r\n      try {\r\n        const favoriteLocations = uni.getStorageSync('favorite_locations') || [];\r\n        const index = favoriteLocations.indexOf(location);\r\n        \r\n        if (index > -1) {\r\n          // 取消收藏\r\n          favoriteLocations.splice(index, 1);\r\n          uni.showToast({\r\n            title: '已取消收藏',\r\n            icon: 'none',\r\n            duration: 1500\r\n          });\r\n        } else {\r\n          // 添加收藏\r\n          favoriteLocations.unshift(location); // 添加到开头\r\n          uni.showToast({\r\n            title: '已添加到常用位置',\r\n            icon: 'success',\r\n            duration: 1500\r\n          });\r\n        }\r\n        \r\n        uni.setStorageSync('favorite_locations', favoriteLocations);\r\n        \r\n        // 更新常用位置列表\r\n        this.loadCommonLocations();\r\n      } catch (error) {\r\n        uni.showToast({\r\n          title: '操作失败',\r\n          icon: 'none'\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 从常用位置中删除\r\n    removeFromCommon(location, index) {\r\n      uni.showModal({\r\n        title: '删除常用位置',\r\n        content: `确定要从常用位置中删除\"${location}\"吗？`,\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            try {\r\n              // 从收藏列表中删除\r\n              const favoriteLocations = uni.getStorageSync('favorite_locations') || [];\r\n              const favoriteIndex = favoriteLocations.indexOf(location);\r\n              \r\n              if (favoriteIndex > -1) {\r\n                favoriteLocations.splice(favoriteIndex, 1);\r\n                uni.setStorageSync('favorite_locations', favoriteLocations);\r\n              }\r\n              \r\n              // 从当前常用位置列表中删除\r\n              this.commonLocations.splice(index, 1);\r\n              \r\n              uni.showToast({\r\n                title: '已删除',\r\n                icon: 'success',\r\n                duration: 1500\r\n              });\r\n            } catch (error) {\r\n              uni.showToast({\r\n                title: '删除失败',\r\n                icon: 'none'\r\n              });\r\n            }\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 加载负责人列表\r\n    async loadResponsiblePersons() {\r\n      try {\r\n        const { callCloudFunction } = require('@/utils/auth.js');\r\n        \r\n        // 使用月度检查专用云函数获取负责人列表\r\n        const result = await callCloudFunction('hygiene-monthly-inspection', {\r\n          action: 'getResponsibleUsers'\r\n        });\r\n        \r\n        if (result && result.success && result.data) {\r\n          // 过滤掉匿名用户和admin用户，只显示responsible角色的用户\r\n          const filteredUsers = result.data\r\n            .filter(user => {\r\n              // 排除admin用户\r\n              const roles = Array.isArray(user.role) ? user.role : [user.role];\r\n              if (roles.includes('admin')) return false;\r\n              \r\n              // 只显示角色为\"responsible\"的用户\r\n              if (!roles.includes('responsible')) return false;\r\n              \r\n              // 排除匿名用户（没有姓名的用户）\r\n              const displayName = user.nickname || user.username;\r\n              if (!displayName || displayName.trim() === '') return false;\r\n              \r\n              // 排除以\"匿名\"开头的用户\r\n              if (displayName.startsWith('匿名')) return false;\r\n              \r\n              return true;\r\n            })\r\n            .map(user => ({\r\n              value: user._id,\r\n              text: user.nickname || user.username // 只显示姓名，不显示角色\r\n            }))\r\n            .filter(item => item.value && item.text); // 再次过滤确保没有空的value或text\r\n          \r\n          // 一次性完成数组的构建和清理，避免多次赋值触发picker重置\r\n          const finalResponsiblePersons = [\r\n            { value: '', text: '请选择负责人' },\r\n            ...filteredUsers\r\n          ].filter((item, index) => {\r\n            if (index === 0) return true; // 保留第一项\"请选择负责人\"\r\n            return item && item.text && item.text.trim() !== '';\r\n          });\r\n          \r\n          // 只设置一次，避免触发picker组件的重置\r\n          this.responsiblePersons = finalResponsiblePersons;\r\n\r\n          \r\n          // 如果有待处理的编辑数据，现在设置选择器状态\r\n          if (this.pendingEditData) {\r\n            this.setEditSelectors(this.pendingEditData);\r\n            this.pendingEditData = null;\r\n          } else {\r\n            // 只有在没有编辑数据时才重置选择器索引\r\n            this.selectedResponsibleIndex = 0;\r\n          }\r\n        } else {\r\n          uni.showToast({\r\n            title: '加载负责人列表失败',\r\n            icon: 'none'\r\n          });\r\n          // 初始化为空列表，只包含\"请选择\"选项\r\n          this.responsiblePersons = [\r\n            { value: '', text: '请选择负责人' }\r\n          ];\r\n          // 只有在没有编辑数据时才重置选择器索引\r\n          if (!this.pendingEditData) {\r\n            this.selectedResponsibleIndex = 0;\r\n          }\r\n        }\r\n              } catch (error) {\r\n        uni.showToast({\r\n          title: '网络错误，请稍后重试',\r\n          icon: 'none'\r\n        });\r\n        // 初始化为空列表，只包含\"请选择\"选项\r\n        this.responsiblePersons = [\r\n          { value: '', text: '请选择负责人' }\r\n        ];\r\n        // 只有在没有编辑数据时才重置选择器索引\r\n        if (!this.pendingEditData) {\r\n          this.selectedResponsibleIndex = 0;\r\n        }\r\n        \r\n        // 即使加载失败，也要处理待处理的编辑数据\r\n        if (this.pendingEditData) {\r\n          this.setEditSelectors(this.pendingEditData);\r\n          this.pendingEditData = null;\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 负责人选择\r\n    onResponsibleChange(e) {\r\n      const index = e.detail.value;\r\n      this.selectedResponsibleIndex = index;\r\n      \r\n      // 安全检查：确保索引有效且对应的选项存在\r\n      if (index >= 0 && index < this.responsiblePersons.length && this.responsiblePersons[index]) {\r\n        if (index === 0) {\r\n          // 选择了\"请选择负责人\"，清空值\r\n          this.formData.responsible = '';\r\n        } else {\r\n          // 选择了具体的负责人\r\n          this.formData.responsible = this.responsiblePersons[index].value;\r\n        }\r\n      } else {\r\n        // 如果索引无效，清空选择\r\n        this.formData.responsible = '';\r\n        this.selectedResponsibleIndex = 0; // 重置为\"请选择负责人\"\r\n      }\r\n    },\r\n    \r\n    // 整改期限选择\r\n    onDeadlineChange(e) {\r\n      this.formData.deadline = e.detail.value;\r\n    },\r\n    \r\n    // 优先级选择 - 新的按钮方式\r\n    selectPriority(value, index) {\r\n      this.formData.priority = value;\r\n      this.selectedPriorityIndex = index;\r\n    },\r\n    \r\n    // 优先级选择 - 保留兼容性\r\n    onPriorityChange(e) {\r\n      const index = e.detail.value;\r\n      this.selectedPriorityIndex = index;\r\n      this.formData.priority = this.priorityOptions[index].value;\r\n    },\r\n    \r\n    // 获取优先级文本\r\n    getPriorityText() {\r\n      const option = this.priorityOptions.find(item => item.value === this.formData.priority);\r\n      return option ? option.text : '一般问题';\r\n    },\r\n    \r\n    // 选择图片\r\n    chooseImage() {\r\n      if (this.formData.images.length >= 6) {\r\n        uni.showToast({\r\n          title: '最多只能选择6张图片',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n\r\n      uni.chooseImage({\r\n        count: 6 - this.formData.images.length,\r\n        sizeType: ['compressed'], // 只使用压缩版本以节省内存和上传时间\r\n        sourceType: ['camera', 'album'],\r\n        success: (res) => {\r\n          const newPhotos = this.processNewPhotos(res.tempFilePaths);\r\n          \r\n          // 批量添加到照片列表\r\n          this.formData.images.push(...newPhotos);\r\n          \r\n          // 如果开启自动上传，立即上传新选择的照片\r\n          if (this.autoUpload) {\r\n            this.autoUploadNewPhotos(newPhotos);\r\n          }\r\n        },\r\n        fail: () => {\r\n          uni.showToast({\r\n            title: '选择照片失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    // 处理新选择的照片\r\n    processNewPhotos(tempFilePaths) {\r\n      return tempFilePaths.map(path => ({\r\n        url: path,\r\n        uploaded: false,\r\n        cloudUrl: '',\r\n        cloudPath: '',\r\n        uploading: false\r\n      }));\r\n    },\r\n\r\n    // 自动上传新选择的照片\r\n    async autoUploadNewPhotos(newPhotos) {\r\n      // 使用 Promise.allSettled 并行上传，避免阻塞\r\n      const uploadPromises = newPhotos.map(async (photo, i) => {\r\n        const photoIndex = this.formData.images.findIndex(p => p.url === photo.url);\r\n        \r\n        if (photoIndex === -1) return { success: false, index: i };\r\n        \r\n        try {\r\n          // 标记为正在上传\r\n          this.$set(this.formData.images[photoIndex], 'uploading', true);\r\n          \r\n          // 单张照片上传\r\n          const uploadResult = await this.uploadSinglePhoto(photo);\r\n          \r\n          if (uploadResult.success) {\r\n            // 批量更新照片信息\r\n            Object.assign(this.formData.images[photoIndex], {\r\n              uploaded: true,\r\n              cloudUrl: uploadResult.url,\r\n              cloudPath: uploadResult.cloudPath,\r\n              uploading: false,\r\n              cloudReady: false // 云端图片未准备好\r\n            });\r\n            \r\n            // 预加载云端图片\r\n            this.preloadCloudImage(photoIndex, uploadResult.url);\r\n            return { success: true, index: i };\r\n          } else {\r\n            throw new Error(uploadResult.error || '上传失败');\r\n          }\r\n        } catch (error) {\r\n          this.$set(this.formData.images[photoIndex], 'uploading', false);\r\n          return { success: false, index: i, error: error.message };\r\n        }\r\n      });\r\n\r\n      // 等待所有上传完成\r\n      const results = await Promise.allSettled(uploadPromises);\r\n      \r\n      // 统计失败的上传\r\n      const failures = results\r\n        .filter(result => result.status === 'fulfilled' && !result.value.success)\r\n        .map(result => result.value);\r\n      \r\n      if (failures.length > 0) {\r\n        uni.showToast({\r\n          title: `${failures.length}张照片上传失败`,\r\n          icon: 'none',\r\n          duration: 2000\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 上传单张图片（使用 uploadUtils 支持目录上传）\r\n    async uploadSinglePhoto(photo) {\r\n      try {\r\n        const cloudPath = this.generateCloudPath();\r\n        \r\n        // 使用 uploadToCloud 方法上传单张照片\r\n        const uploadResult = await uploadUtils.uploadToCloud(photo.url, cloudPath);\r\n        \r\n        if (uploadResult?.fileID) {\r\n          // 获取访问URL\r\n          const fileInfo = await uploadUtils.getFileInfo(uploadResult.fileID);\r\n          \r\n          return {\r\n            success: true,\r\n            cloudPath: uploadResult.fileID,\r\n            url: fileInfo.tempFileURL || uploadResult.fileID,\r\n            size: uploadResult.actualSize\r\n          };\r\n        } else {\r\n          throw new Error('上传返回结果异常');\r\n        }\r\n      } catch (error) {\r\n        return { success: false, error: error.message };\r\n      }\r\n    },\r\n\r\n    // 生成云存储路径\r\n    generateCloudPath() {\r\n      const timestamp = Date.now();\r\n      const random = Math.random().toString(36).substring(2, 8);\r\n      return `6s/issues/${timestamp}_${random}.jpg`;\r\n    },\r\n    \r\n    // 删除图片\r\n    async deleteImage(index) {\r\n      if (index < 0 || index >= this.formData.images.length) {\r\n        return;\r\n      }\r\n\r\n      const photo = this.formData.images[index];\r\n      \r\n      // 如果照片已经上传到云端，需要删除云端文件\r\n      if (photo.uploaded && photo.cloudPath) {\r\n        try {\r\n          uni.showLoading({ title: '删除照片中...' });\r\n          \r\n          // 调用云函数删除云端文件\r\n          await uniCloud.callFunction({\r\n            name: 'delete-file',\r\n            data: {\r\n              fileList: [this.extractFileId(photo.cloudPath)]\r\n            }\r\n          });\r\n        } catch (error) {\r\n          uni.showToast({\r\n            title: '删除云端照片失败',\r\n            icon: 'none'\r\n          });\r\n        } finally {\r\n          uni.hideLoading();\r\n        }\r\n      }\r\n      \r\n      // 从本地数组中移除\r\n      this.formData.images.splice(index, 1);\r\n    },\r\n\r\n    // 从URL中提取文件ID\r\n    extractFileId(url) {\r\n      if (url.startsWith('cloud://')) {\r\n        const parts = url.split('/');\r\n        return parts[parts.length - 1];\r\n      } else if (url.includes('tcb-api')) {\r\n        const urlObj = new URL(url);\r\n        return urlObj.pathname.split('/').pop();\r\n      }\r\n      return url;\r\n    },\r\n\r\n    // 预览照片\r\n    previewPhoto(index) {\r\n      const urls = this.formData.images.map(photo => this.getPhotoDisplayUrl(photo));\r\n      uni.previewImage({\r\n        urls: urls,\r\n        current: index\r\n      });\r\n    },\r\n\r\n    // 获取照片显示URL\r\n    getPhotoDisplayUrl(photo) {\r\n      if (typeof photo === 'string') {\r\n        return photo; // 兼容旧的字符串格式\r\n      }\r\n      \r\n      // 只有云端图片预加载完成才切换，避免闪烁\r\n      if (photo.uploaded && photo.cloudUrl && photo.cloudReady) {\r\n        return photo.cloudUrl;\r\n      }\r\n      \r\n      // 默认使用本地图片，直到云端图片准备就绪\r\n      return photo.url || photo.cloudUrl || photo;\r\n    },\r\n\r\n    // 预加载云端图片（小程序兼容）\r\n    preloadCloudImage(photoIndex, cloudUrl) {\r\n      uni.getImageInfo({\r\n        src: cloudUrl,\r\n        success: () => {\r\n          if (this.formData.images[photoIndex]) {\r\n            this.$set(this.formData.images[photoIndex], 'cloudReady', true);\r\n          }\r\n        },\r\n        fail: () => {\r\n          // 预加载失败，继续使用本地图片\r\n          if (this.formData.images[photoIndex]) {\r\n            this.$set(this.formData.images[photoIndex], 'cloudReady', false);\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 表单验证\r\n    validateForm() {\r\n      if (!this.formData.title.trim()) {\r\n        uni.showToast({\r\n          title: '请输入问题标题',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      if (this.formData.title.length < 2 || this.formData.title.length > 50) {\r\n        uni.showToast({\r\n          title: '标题长度在2-50个字符之间',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      if (!this.formData.description.trim()) {\r\n        uni.showToast({\r\n          title: '请输入问题描述',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      if (this.formData.description.length < 5 || this.formData.description.length > 200) {\r\n        uni.showToast({\r\n          title: '描述长度在5-200个字符之间',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      if (!this.formData.location.trim()) {\r\n        uni.showToast({\r\n          title: '请输入问题位置',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      if (!this.formData.responsible) {\r\n        uni.showToast({\r\n          title: '请选择负责人',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      if (!this.formData.deadline) {\r\n        uni.showToast({\r\n          title: '请选择整改期限',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      return true;\r\n    },\r\n    \r\n    async submitForm() {\r\n      if (!this.validateForm()) {\r\n        return;\r\n      }\r\n      \r\n      try {\r\n        this.submitting = true;\r\n        \r\n        const { callCloudFunction } = require('@/utils/auth.js');\r\n        \r\n        // 构建提交数据 - 提交到月度检查系统\r\n        const submitData = {\r\n          title: this.formData.title.trim(),\r\n          description: this.formData.description.trim(),\r\n          location_info: {\r\n            location_type: 'custom',\r\n            location_name: this.formData.location.trim(),\r\n            location_description: ''\r\n          },\r\n          inspection_info: {\r\n            inspection_date: new Date().toISOString(),\r\n            inspection_type: 'monthly_routine'\r\n          },\r\n          category: 'safety', // 默认安全问题\r\n          severity: this.getPriorityToSeverity(this.formData.priority),\r\n          priority: this.formData.priority,\r\n          assigned_to: this.getResponsibleId(),\r\n          assigned_to_name: this.getResponsibleName(),\r\n          photos: this.preparePhotosForSubmit(),\r\n          tags: [],\r\n          expected_completion_date: this.formData.deadline\r\n        };\r\n        \r\n        // 根据模式选择API操作\r\n        const action = this.isEditingPublished ? 'updateMonthlyIssue' : 'createMonthlyIssue';\r\n        const apiData = this.isEditingPublished ? {\r\n          issue_id: this.currentEditId,\r\n          ...submitData\r\n        } : submitData;\r\n        \r\n        const result = await callCloudFunction('hygiene-monthly-inspection', {\r\n          action: action,\r\n          data: apiData\r\n        });\r\n        \r\n        if (result && result.success) {\r\n          uni.showToast({\r\n            title: this.isEditingPublished ? '问题更新成功' : '问题发布成功',\r\n            icon: 'success'\r\n          });\r\n          \r\n          // 通知其他页面数据已更新\r\n          const eventData = {\r\n            action: this.isEditingPublished ? 'update' : 'create',\r\n            issueId: this.isEditingPublished ? this.currentEditId : (result.data?.id || result.data?._id),\r\n            issueNumber: result.data?.issue_number,\r\n            issueData: submitData,\r\n            fromDraft: this.isEditingDraft // 标识是否来自草稿发布\r\n          };\r\n          uni.$emit('monthlyIssueUpdated', eventData);\r\n          \r\n          // 清除草稿（如果有）\r\n          this.clearDraftIfExists();\r\n          \r\n          // 如果是从草稿发布，延迟通知草稿列表更新（确保本地存储操作完成）\r\n          if (this.isEditingDraft && this.currentDraftId) {\r\n            setTimeout(() => {\r\n              const draftEventData = {\r\n                action: 'delete',\r\n                draftId: this.currentDraftId\r\n              };\r\n              uni.$emit('issueDraftUpdated', draftEventData);\r\n            }, 100); // 确保本地存储操作完成\r\n          }\r\n          \r\n          // 返回上一页\r\n          setTimeout(() => {\r\n            uni.navigateBack();\r\n          }, 1500);\r\n        } else {\r\n          throw new Error(result.message || '发布失败');\r\n        }\r\n        \r\n      } catch (error) {\r\n        uni.showToast({\r\n          title: error.message || '发布失败，请重试',\r\n          icon: 'error'\r\n        });\r\n      } finally {\r\n        this.submitting = false;\r\n      }\r\n    },\r\n\r\n    // 准备照片数据用于提交\r\n    preparePhotosForSubmit() {\r\n      return this.formData.images\r\n        .filter(photo => photo.uploaded && photo.cloudPath) // 只提交已上传的照片\r\n        .map(photo => ({\r\n          url: photo.cloudPath,\r\n          description: '月度检查发现的问题',\r\n          timestamp: new Date()\r\n        }));\r\n    },\r\n    \r\n    // 优先级转换为严重程度\r\n    getPriorityToSeverity(priority) {\r\n      const priorityMap = {\r\n        'normal': 'medium',\r\n        'urgent': 'high'\r\n      };\r\n      return priorityMap[priority] || 'medium';\r\n    },\r\n    \r\n    // 获取负责人ID\r\n    getResponsibleId() {\r\n      if (this.selectedResponsibleIndex > 0 && this.responsiblePersons[this.selectedResponsibleIndex]) {\r\n        const value = this.responsiblePersons[this.selectedResponsibleIndex].value;\r\n        return value || null; // 如果value为空字符串，返回null\r\n      }\r\n      return null;\r\n    },\r\n    \r\n    // 获取负责人姓名\r\n    getResponsibleName() {\r\n      if (this.selectedResponsibleIndex > 0 && this.responsiblePersons[this.selectedResponsibleIndex]) {\r\n        const text = this.responsiblePersons[this.selectedResponsibleIndex].text;\r\n        // 由于现在只显示姓名，不需要分割\r\n        return text !== '请选择负责人' ? text : null;\r\n      }\r\n      return null;\r\n    },\r\n\r\n\r\n    \r\n    // 清除草稿\r\n    clearDraftIfExists() {\r\n      if (this.isEditingDraft && this.currentDraftId) {\r\n        try {\r\n          const drafts = uni.getStorageSync('issue_drafts') || [];\r\n          const filteredDrafts = drafts.filter(draft => draft.id !== this.currentDraftId);\r\n          uni.setStorageSync('issue_drafts', filteredDrafts);\r\n        } catch (error) {\r\n          // 清除草稿失败，静默处理\r\n        }\r\n      }\r\n    },\r\n    \r\n    async saveDraft() {\r\n      if (!this.canSaveDraft) {\r\n        return;\r\n      }\r\n      \r\n      try {\r\n        this.draftSaving = true;\r\n        \r\n        // 草稿保存（本地存储）\r\n        \r\n        // 构建草稿数据\r\n        const draftData = {\r\n          ...this.formData,\r\n          // 处理照片数据，优先保存云端URL\r\n          images: this.formData.images.map(photo => {\r\n            if (typeof photo === 'string') {\r\n              return photo; // 兼容旧格式\r\n            }\r\n            // 如果已上传到云端，使用云端数据\r\n            if (photo.uploaded && photo.cloudUrl) {\r\n              return {\r\n                ...photo,\r\n                url: photo.cloudUrl, // 将url字段更新为云端URL\r\n                displayUrl: photo.cloudUrl // 添加显示URL字段\r\n              };\r\n            }\r\n            return photo; // 未上传的保持原样\r\n          }),\r\n          createdAt: new Date().toISOString(),\r\n          updatedAt: new Date().toISOString(),\r\n          status: 'draft', // 草稿状态\r\n          id: Date.now() // 生成本地草稿唯一标识符\r\n        };\r\n        \r\n        // 存储到本地缓存\r\n        this.saveDraftToLocal(draftData);\r\n        \r\n        uni.showToast({\r\n          title: '草稿保存成功',\r\n          icon: 'success'\r\n        });\r\n        \r\n        // 通知其他页面草稿数据已更新\r\n        uni.$emit('issueDraftUpdated', {\r\n          action: this.isEditingDraft ? 'update' : 'create',\r\n          draftId: this.isEditingDraft ? this.currentDraftId : draftData.id,\r\n          draftData: draftData\r\n        });\r\n        \r\n        // 返回上一页\r\n        setTimeout(() => {\r\n          uni.navigateBack();\r\n        }, 1500);\r\n        \r\n      } catch (error) {\r\n        uni.showToast({\r\n          title: '保存失败，请重试',\r\n          icon: 'error'\r\n        });\r\n      } finally {\r\n        this.draftSaving = false;\r\n      }\r\n    },\r\n    \r\n    // 保存草稿到本地缓存\r\n    saveDraftToLocal(draftData) {\r\n      try {\r\n        const existingDrafts = uni.getStorageSync('issue_drafts') || [];\r\n        \r\n        if (this.isEditingDraft) {\r\n          // 更新现有草稿\r\n          const index = existingDrafts.findIndex(draft => draft.id === this.currentDraftId);\r\n          if (index !== -1) {\r\n            existingDrafts[index] = { ...draftData, id: this.currentDraftId };\r\n          }\r\n        } else {\r\n          // 添加新草稿\r\n          existingDrafts.push(draftData);\r\n        }\r\n        \r\n        // 保存到本地\r\n        uni.setStorageSync('issue_drafts', existingDrafts);\r\n        \r\n\r\n      } catch (error) {\r\n        // 保存草稿到本地失败，静默处理\r\n      }\r\n    },\r\n    \r\n    // 加载草稿数据\r\n    loadDraft(draftId) {\r\n      try {\r\n        const drafts = uni.getStorageSync('issue_drafts') || [];\r\n        const draft = drafts.find(d => d.id == draftId);\r\n        \r\n        if (draft) {\r\n          this.isEditingDraft = true;\r\n          this.currentDraftId = draft.id;\r\n          this.formData = { ...draft };\r\n          \r\n          // 保存草稿数据，待负责人列表加载完成后设置\r\n          this.pendingEditData = {\r\n            responsible_id: draft.responsible,\r\n            priority: draft.priority\r\n          };\r\n\r\n        }\r\n      } catch (error) {\r\n        // 加载草稿失败，静默处理\r\n      }\r\n    },\r\n\r\n    // 加载编辑数据\r\n    loadEditData(editId, editDataStr) {\r\n      try {\r\n        const editData = JSON.parse(decodeURIComponent(editDataStr));\r\n        // 设置为编辑模式\r\n        this.isEditingPublished = true;\r\n        this.currentEditId = editId;\r\n        \r\n        // 填充表单数据\r\n        this.formData = {\r\n          title: editData.title || '',\r\n          description: editData.description || '',\r\n          location: editData.location || '',\r\n          responsible: editData.responsible_id || '',\r\n          deadline: editData.deadline || '',\r\n          priority: editData.priority || 'normal',\r\n          images: this.processEditImages(editData.images || [])\r\n        };\r\n        \r\n        // 保存编辑数据，待负责人列表加载完成后设置\r\n        this.pendingEditData = editData;\r\n        \r\n      } catch (error) {\r\n        uni.showToast({\r\n          title: '加载编辑数据失败',\r\n          icon: 'error'\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 设置编辑时的选择器状态\r\n    setEditSelectors(editData) {\r\n      // 设置负责人选择器\r\n      if (editData.responsible_id && this.responsiblePersons.length > 0) {\r\n        const responsibleIndex = this.responsiblePersons.findIndex(p => p.value === editData.responsible_id);\r\n        \r\n        if (responsibleIndex > -1) {\r\n          this.selectedResponsibleIndex = responsibleIndex;\r\n          this.formData.responsible = editData.responsible_id;\r\n        }\r\n      }\r\n      \r\n      // 设置优先级选择器\r\n      if (editData.priority) {\r\n        const priorityIndex = this.priorityOptions.findIndex(p => p.value === editData.priority);\r\n        if (priorityIndex > -1) {\r\n          this.selectedPriorityIndex = priorityIndex;\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 处理编辑时的图片数据\r\n    processEditImages(images) {\r\n      if (!Array.isArray(images)) return [];\r\n      \r\n      return images.map(img => {\r\n        if (typeof img === 'string') {\r\n          return {\r\n            url: img,\r\n            cloudUrl: img,\r\n            cloudPath: img,\r\n            uploaded: true,\r\n            uploading: false\r\n          };\r\n        } else if (img && img.url) {\r\n          return {\r\n            url: img.url,\r\n            cloudUrl: img.url,\r\n            cloudPath: img.url,\r\n            uploaded: true,\r\n            uploading: false\r\n          };\r\n        }\r\n        return img;\r\n      }).filter(Boolean);\r\n    },\r\n\r\n    // 切换自动上传状态\r\n    toggleAutoUpload() {\r\n      this.autoUpload = !this.autoUpload;\r\n      uni.showToast({\r\n        title: this.autoUpload ? '已开启自动上传' : '已关闭自动上传',\r\n        icon: 'none'\r\n      });\r\n    },\r\n\r\n    // 点击页面其他地方隐藏建议列表\r\n    hideSuggestions() {\r\n      this.showLocationSuggestions = false;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-container {\r\n\tmin-height: 100vh;\r\n\tbackground: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\r\n\twidth: 100%;\r\n\toverflow-x: hidden; /* 防止水平滚动 */\r\n\tbox-sizing: border-box;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;\r\n  /* 滚动性能优化 */\r\n  -webkit-overflow-scrolling: touch;\r\n  transform: translateZ(0);\r\n}\r\n\r\n/* 表单容器 */\r\n.form-container {\r\n  padding: 32rpx;\r\n}\r\n\r\n/* 表单项 - 统一卡片样式 */\r\n.form-item {\r\n  background: #ffffff;\r\n  border-radius: 24rpx;\r\n  padding: 32rpx;\r\n  margin-bottom: 24rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08), \r\n              0 2rpx 6rpx rgba(0, 0, 0, 0.04);\r\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\r\n  /* 性能优化 */\r\n  transform: translateZ(0);\r\n  will-change: transform;\r\n}\r\n\r\n.form-label {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #1C1C1E;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.required {\r\n  color: #FF3B30;\r\n  margin-left: 4rpx;\r\n}\r\n\r\n.form-input {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  border: 2rpx solid rgba(240, 240, 247, 0.8);\r\n  border-radius: 16rpx;\r\n  padding: 0 24rpx;\r\n  font-size: 28rpx;\r\n  color: #1C1C1E;\r\n  background: linear-gradient(135deg, rgba(248, 249, 250, 0.9), rgba(255, 255, 255, 0.8));\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  box-sizing: border-box;\r\n  backdrop-filter: blur(5rpx);\r\n}\r\n\r\n.form-input:focus {\r\n  border-color: #007AFF;\r\n  background: #ffffff;\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.15),\r\n              0 2rpx 6rpx rgba(0, 122, 255, 0.1);\r\n  transform: translateY(-1rpx);\r\n}\r\n\r\n.form-textarea {\r\n  width: 100%;\r\n  min-height: 160rpx;\r\n  border: 2rpx solid rgba(240, 240, 247, 0.8);\r\n  border-radius: 16rpx;\r\n  padding: 24rpx;\r\n  font-size: 28rpx;\r\n  color: #1C1C1E;\r\n  background: linear-gradient(135deg, rgba(248, 249, 250, 0.9), rgba(255, 255, 255, 0.8));\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  resize: none;\r\n  line-height: 1.5;\r\n  box-sizing: border-box;\r\n  backdrop-filter: blur(5rpx);\r\n}\r\n\r\n.form-textarea:focus {\r\n  border-color: #007AFF;\r\n  background: #ffffff;\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.15),\r\n              0 2rpx 6rpx rgba(0, 122, 255, 0.1);\r\n  transform: translateY(-1rpx);\r\n}\r\n\r\n.form-picker {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  height: 80rpx;\r\n  border: 2rpx solid rgba(240, 240, 247, 0.8);\r\n  border-radius: 16rpx;\r\n  padding: 0 24rpx;\r\n  font-size: 28rpx;\r\n  color: #1C1C1E;\r\n  background: linear-gradient(135deg, rgba(248, 249, 250, 0.9), rgba(255, 255, 255, 0.8));\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  box-sizing: border-box;\r\n  backdrop-filter: blur(5rpx);\r\n  cursor: pointer;\r\n}\r\n\r\n.picker-arrow {\r\n  font-size: 24rpx;\r\n  color: #8E8E93;\r\n  transform: rotate(90deg);\r\n}\r\n\r\n/* 图片上传 - 仅保留布局，移除视觉样式 */\r\n.upload-section {\r\n  margin: 32rpx 32rpx 16rpx 32rpx; /* 保持与form-item原有内边距一致，减少底部间距 */\r\n  padding: 0; /* 移除内边距 */\r\n  background: transparent; /* 移除背景色 */\r\n  border: none; /* 移除边框 */\r\n  border-radius: 0; /* 移除圆角 */\r\n}\r\n\r\n/* 照片上传专用form-item样式 */\r\n.photo-upload-item {\r\n  padding: 0 !important; /* 移除form-item的默认内边距 */\r\n  margin-bottom: 0 !important; /* 移除问题照片容器的下边距 */\r\n}\r\n\r\n.photo-upload-item .form-label {\r\n  margin-bottom: 0 !important; /* 移除标题下方间距 */\r\n}\r\n\r\n.photo-upload-content {\r\n  margin: 0 32rpx 32rpx 32rpx; /* 与upload-section保持一致的左右边距 */\r\n}\r\n\r\n/* 照片上传区域的section-header特殊样式 */\r\n.photo-upload-item .section-header {\r\n  background: transparent !important; /* 移除灰色背景 */\r\n  border-bottom: none !important; /* 移除下边线 */\r\n  padding: 0 !important; /* 移除内边距 */\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.auto-upload-toggle {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12rpx;\r\n  cursor: pointer;\r\n}\r\n\r\n.toggle-label {\r\n  font-size: 24rpx;\r\n  color: #8E8E93;\r\n}\r\n\r\n.toggle-switch {\r\n  width: 76rpx;\r\n  height: 44rpx;\r\n  background: linear-gradient(135deg, #E5E5EA, #D1D1D6);\r\n  border-radius: 22rpx;\r\n  position: relative;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.toggle-switch.active {\r\n  background: linear-gradient(135deg, #34C759, #30D158);\r\n  box-shadow: 0 4rpx 12rpx rgba(52, 199, 89, 0.3),\r\n              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.toggle-circle {\r\n  width: 36rpx;\r\n  height: 36rpx;\r\n  background: linear-gradient(135deg, #ffffff, #f8f9fa);\r\n  border-radius: 50%;\r\n  position: absolute;\r\n  top: 4rpx;\r\n  left: 4rpx;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15),\r\n              0 2rpx 4rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.toggle-switch.active .toggle-circle {\r\n  transform: translateX(32rpx);\r\n  background: linear-gradient(135deg, #ffffff, #f0fff0);\r\n  box-shadow: 0 4rpx 16rpx rgba(52, 199, 89, 0.2),\r\n              0 2rpx 6rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.image-upload {\r\n  margin-top: 16rpx;\r\n}\r\n\r\n.photo-grid {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 12rpx;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.photo-item {\r\n  position: relative;\r\n  width: 170rpx;\r\n  height: 170rpx;\r\n  border-radius: 20rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1),\r\n              0 2rpx 4rpx rgba(0, 0, 0, 0.06);\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n.photo-item:hover {\r\n  transform: translateY(-2rpx);\r\n  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.15),\r\n              0 3rpx 8rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.photo-item image {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.photo-delete {\r\n  position: absolute;\r\n  top: 6rpx;\r\n  right: 6rpx;\r\n  width: 36rpx;\r\n  height: 36rpx;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.photo-add {\r\n  width: 170rpx;\r\n  height: 170rpx;\r\n  border: 2rpx dashed rgba(0, 122, 255, 0.3);\r\n  border-radius: 20rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8rpx;\r\n  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(255, 255, 255, 0.9));\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  cursor: pointer;\r\n}\r\n\r\n.photo-add:hover {\r\n  border-color: #007AFF;\r\n  background: linear-gradient(135deg, rgba(230, 243, 255, 0.9), rgba(240, 248, 255, 0.95));\r\n  transform: translateY(-2rpx);\r\n  box-shadow: 0 6rpx 20rpx rgba(0, 122, 255, 0.15);\r\n}\r\n\r\n.photo-add.disabled {\r\n  opacity: 0.5;\r\n  pointer-events: none;\r\n}\r\n\r\n.photo-add text {\r\n  font-size: 24rpx;\r\n  color: #8E8E93;\r\n}\r\n\r\n.photo-tip {\r\n  font-size: 24rpx;\r\n  color: #8E8E93;\r\n  line-height: 1.4;\r\n}\r\n\r\n.photo-uploading {\r\n  position: absolute;\r\n  top: 8rpx;\r\n  left: 8rpx;\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-spinner {\r\n  width: 20rpx;\r\n  height: 20rpx;\r\n  border: 2rpx solid rgba(255, 255, 255, 0.3);\r\n  border-top: 2rpx solid white;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n.photo-uploaded {\r\n  position: absolute;\r\n  top: 8rpx;\r\n  left: 8rpx;\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  background: #34C759;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-progress {\r\n  margin: 16rpx 0;\r\n  padding: 16rpx;\r\n  background: #F8F9FA;\r\n  border-radius: 12rpx;\r\n}\r\n\r\n.progress-bar {\r\n  width: 100%;\r\n  height: 8rpx;\r\n  background: #E5E5EA;\r\n  border-radius: 4rpx;\r\n  overflow: hidden;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #007AFF 0%, #5AC8FA 100%);\r\n  border-radius: 4rpx;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n  font-size: 24rpx;\r\n  color: #8E8E93;\r\n  text-align: center;\r\n  display: block;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 优先级按钮样式 */\r\n.priority-buttons {\r\n  display: flex;\r\n  gap: 16rpx;\r\n  margin-top: 16rpx;\r\n}\r\n\r\n.priority-btn {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8rpx;\r\n  padding: 20rpx 16rpx;\r\n  border: 2rpx solid rgba(229, 229, 229, 0.6);\r\n  border-radius: 16rpx;\r\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  cursor: pointer;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08),\r\n              0 2rpx 6rpx rgba(0, 0, 0, 0.04),\r\n              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.8);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.priority-btn:active {\r\n  transform: translateY(2rpx);\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1),\r\n              0 1rpx 3rpx rgba(0, 0, 0, 0.06),\r\n              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.6);\r\n}\r\n\r\n/* 一般问题激活样式 */\r\n.priority-btn-active {\r\n  border-color: #007AFF;\r\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\r\n  box-shadow: 0 6rpx 20rpx rgba(0, 122, 255, 0.3),\r\n              0 4rpx 12rpx rgba(0, 122, 255, 0.2),\r\n              0 2rpx 6rpx rgba(0, 122, 255, 0.1),\r\n              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n/* 紧急问题默认样式 */\r\n.priority-btn-urgent {\r\n  border-color: rgba(229, 229, 229, 0.6);\r\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);\r\n}\r\n\r\n/* 紧急问题激活样式 */\r\n.priority-btn-urgent.priority-btn-active {\r\n  border-color: #FF6B35;\r\n  background: linear-gradient(135deg, #FF6B35 0%, #FF8A50 100%);\r\n  box-shadow: 0 6rpx 20rpx rgba(255, 107, 53, 0.3),\r\n              0 4rpx 12rpx rgba(255, 107, 53, 0.2),\r\n              0 2rpx 6rpx rgba(255, 107, 53, 0.1),\r\n              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n/* 文字样式 */\r\n.priority-text {\r\n  font-size: 28rpx;\r\n  color: #666666;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 紧急问题未选中时的图标和文字颜色 */\r\n.priority-btn-urgent .priority-text {\r\n  color: #666666;\r\n}\r\n\r\n/* 激活状态的文字 - 需要更高权重覆盖上面的样式 */\r\n.priority-btn-active .priority-text {\r\n  color: #ffffff !important;\r\n}\r\n\r\n/* 紧急问题激活状态的文字 */\r\n.priority-btn-urgent.priority-btn-active .priority-text {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.image-upload {\r\n  margin-top: 16rpx;\r\n}\r\n\r\n/* 位置选择器样式 */\r\n.location-selector {\r\n  position: relative;\r\n  z-index: 10000; /* 确保选择器及其子元素在最上层 */\r\n}\r\n\r\n.quick-locations {\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.quick-location-label {\r\n  font-size: 24rpx;\r\n  color: #8E8E93;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.location-tags {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 12rpx;\r\n}\r\n\r\n.location-tag {\r\n  padding: 12rpx 20rpx;\r\n  background: #F2F2F7;\r\n  border: 2rpx solid #F2F2F7;\r\n  border-radius: 20rpx;\r\n  font-size: 24rpx;\r\n  color: #1C1C1E;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.location-tag.active {\r\n  background: #E3F2FD;\r\n  border-color: #007AFF;\r\n  color: #007AFF;\r\n}\r\n\r\n.location-tag:active {\r\n  background: #E0E0E0;\r\n  transform: scale(0.95);\r\n}\r\n\r\n.location-input-wrapper {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.location-input {\r\n  flex: 1;\r\n  margin: 0;\r\n  padding-right: 60rpx; /* 为清空按钮预留空间 */\r\n}\r\n\r\n.clear-location {\r\n  position: absolute;\r\n  right: 16rpx;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  border-radius: 50%;\r\n  background: rgba(199, 199, 204, 0.15);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n  z-index: 10;\r\n}\r\n\r\n.clear-location:hover {\r\n  background: rgba(199, 199, 204, 0.2);\r\n}\r\n\r\n.clear-location:active {\r\n  background: rgba(199, 199, 204, 0.3);\r\n  transform: translateY(-50%) scale(0.9);\r\n}\r\n\r\n/* 位置选择表单项需要允许overflow */\r\n.location-form-item {\r\n  overflow: visible !important;\r\n  z-index: 9999; /* 提高到很高的层级 */\r\n  /* 移除创建层叠上下文的属性 */\r\n  transform: none !important;\r\n  will-change: auto !important;\r\n}\r\n\r\n.location-suggestions {\r\n  position: absolute;\r\n  top: 100%;\r\n  left: 0;\r\n  right: 0;\r\n  background: rgba(255, 255, 255, 0.98);\r\n  backdrop-filter: blur(20rpx);\r\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\r\n  border-radius: 24rpx; /* 统一使用24rpx圆角 */\r\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12), \r\n              0 4rpx 16rpx rgba(0, 0, 0, 0.08);\r\n  z-index: 999999;\r\n  max-height: 480rpx; /* 增加高度 */\r\n  overflow-y: auto;\r\n  margin-top: 8rpx;\r\n  /* 性能优化 */\r\n  transform: translateZ(0);\r\n  will-change: transform;\r\n  /* 平滑滚动 */\r\n  -webkit-overflow-scrolling: touch;\r\n}\r\n\r\n.suggestion-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 16rpx 24rpx; /* 增加内边距 */\r\n\r\n  transition: all 0.2s ease; /* 减少过渡时间，降低闪烁 */\r\n  min-height: 80rpx; /* 增加高度 */\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 使用伪元素作为分割线，避免:last-child规则 */\r\n.suggestion-item::after {\r\n  content: '';\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  height: 1rpx;\r\n  background: rgba(0, 0, 0, 0.05);\r\n  pointer-events: none;\r\n}\r\n\r\n/* 隐藏最后一个分割线 */\r\n.location-suggestions > .suggestion-section:last-child .suggestion-item:last-child::after {\r\n  display: none;\r\n}\r\n\r\n.suggestion-item:active {\r\n  transform: scale(0.98);\r\n  background: rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.suggestion-text {\r\n  font-size: 30rpx; /* 稍微增大字体 */\r\n  color: #1C1C1E;\r\n  font-weight: 500; /* 稍微加粗 */\r\n  letter-spacing: 0.5rpx; /* 增加字间距 */\r\n}\r\n\r\n/* 位置内容区域 */\r\n.location-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16rpx; /* 增加图标和文字间距 */\r\n  flex: 1;\r\n  cursor: pointer;\r\n  padding: 4rpx 0; /* 增加垂直点击区域 */\r\n}\r\n\r\n/* 星标按钮样式 */\r\n.star-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 52rpx;\r\n  height: 52rpx;\r\n  border-radius: 26rpx;\r\n  background: rgba(255, 255, 255, 0.8);\r\n  backdrop-filter: blur(10rpx);\r\n  border: 1rpx solid rgba(255, 255, 255, 0.5);\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  cursor: pointer;\r\n  margin-left: 12rpx;\r\n}\r\n\r\n.star-btn:active {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  transform: scale(0.95);\r\n  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* 分区样式 */\r\n.suggestion-section {\r\n  margin-bottom: 1rpx; /* 移除section之间的间距 */\r\n}\r\n\r\n.suggestion-section:first-child {\r\n  padding-top: 4rpx;\r\n}\r\n\r\n.suggestion-section:last-child {\r\n  margin-bottom: 4rpx;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 8rpx 20rpx 6rpx;\r\n  border-bottom: 1rpx solid #E5E5E5;\r\n  margin-bottom: 2rpx;\r\n  background: #F8F8F8;\r\n}\r\n\r\n.section-title {\r\n  font-size: 26rpx;\r\n  color: #1C1C1E;\r\n  font-weight: 600;\r\n}\r\n\r\n.section-count {\r\n  font-size: 22rpx;\r\n  color: #8E8E93;\r\n  background: #F2F2F7;\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 12rpx;\r\n}\r\n\r\n/* 常用位置样式 */\r\n.common-location {\r\n  position: relative;\r\n  border-left: 4rpx solid #FF9800;\r\n  justify-content: flex-start;\r\n}\r\n\r\n.common-location:active {\r\n  background: rgba(0, 0, 0, 0.05);\r\n  transform: scale(0.98);\r\n}\r\n\r\n/* 常用位置的内容区域样式 */\r\n.common-location .location-content {\r\n  flex: 1;\r\n}\r\n\r\n/* 普通位置样式 - 蓝色彩带效果 */\r\n.regular-location {\r\n  position: relative;\r\n  border-left: 4rpx solid #007AFF;\r\n  justify-content: flex-start;\r\n}\r\n\r\n.regular-location:active {\r\n  background: rgba(0, 0, 0, 0.05);\r\n  transform: scale(0.98);\r\n}\r\n\r\n/* 普通位置的内容区域样式 */\r\n.regular-location .location-content {\r\n  flex: 1;\r\n}\r\n\r\n.delete-common {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  margin-left: 16rpx;\r\n  border-radius: 24rpx;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  backdrop-filter: blur(10rpx);\r\n  border: 1rpx solid rgba(255, 255, 255, 0.6);\r\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  flex-shrink: 0;\r\n}\r\n\r\n.delete-common:hover {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n.delete-common:active {\r\n  background: rgba(240, 240, 240, 0.9);\r\n  transform: scale(0.92);\r\n  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 分类组样式 */\r\n.category-group {\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.category-group:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 无建议样式 */\r\n.no-suggestions {\r\n  padding: 48rpx 24rpx;\r\n  text-align: center;\r\n  background: #FAFAFA;\r\n}\r\n\r\n.no-suggestions-text {\r\n  display: block;\r\n  font-size: 28rpx;\r\n  color: #8E8E93;\r\n  margin-bottom: 12rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.no-suggestions-hint {\r\n  display: block;\r\n  font-size: 24rpx;\r\n  color: #C7C7CC;\r\n}\r\n\r\n/* 提交区域 - 简化为正常文档流 */\r\n.submit-section {\r\n  padding: 24rpx 32rpx 32rpx 32rpx; /* 上24rpx 左右32rpx 下32rpx */\r\n  margin: 0rpx 32rpx 32rpx 32rpx; /* 上间距减少到12rpx，比form-item间距小 */\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(20rpx);\r\n  border-top: 1rpx solid rgba(229, 229, 229, 0.5);\r\n  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);\r\n  border-radius: 24rpx; /* 添加圆角保持与form-item一致 */\r\n}\r\n\r\n.button-group {\r\n  display: flex;\r\n  gap: 20rpx;\r\n}\r\n\r\n.draft-btn {\r\n  flex: 1;\r\n  height: 88rpx;\r\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);\r\n  color: #1C1C1E;\r\n  border: 2rpx solid rgba(0, 122, 255, 0.25);\r\n  border-radius: 16rpx;\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  backdrop-filter: blur(15rpx);\r\n  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.15),\r\n              0 4rpx 12rpx rgba(0, 122, 255, 0.12),\r\n              0 2rpx 6rpx rgba(0, 122, 255, 0.08),\r\n              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.8);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.draft-btn.disabled {\r\n  background: #C7C7CC !important;\r\n  color: #8E8E93 !important;\r\n  border: 2rpx solid #E5E5E5 !important; /* 确保禁用时没有蓝色边框 */\r\n  box-shadow: none !important;\r\n}\r\n\r\n.draft-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(0, 122, 255, 0.1), transparent);\r\n  transition: left 0.6s ease;\r\n}\r\n\r\n.draft-btn:not(.disabled):active::before {\r\n  left: 100%;\r\n}\r\n\r\n.draft-btn:not(.disabled):active {\r\n  background: linear-gradient(135deg, rgba(230, 243, 255, 0.95) 0%, rgba(240, 248, 255, 0.9) 100%);\r\n  transform: translateY(3rpx);\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.15),\r\n              0 2rpx 6rpx rgba(0, 122, 255, 0.1),\r\n              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.6);\r\n}\r\n\r\n.submit-btn {\r\n  flex: 1;\r\n  height: 88rpx;\r\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\r\n  color: #ffffff;\r\n  border: none;\r\n  border-radius: 16rpx;\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  box-shadow: 0 12rpx 32rpx rgba(0, 122, 255, 0.4),\r\n              0 8rpx 16rpx rgba(0, 122, 255, 0.3),\r\n              0 4rpx 8rpx rgba(0, 122, 255, 0.2),\r\n              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);\r\n  position: relative;\r\n  overflow: hidden;\r\n  border: 1rpx solid rgba(90, 200, 250, 0.3);\r\n}\r\n\r\n.submit-btn[disabled] {\r\n  background: #C7C7CC !important;\r\n  color: #8E8E93 !important;\r\n  box-shadow: none !important;\r\n}\r\n\r\n.submit-btn.loading {\r\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%) !important;\r\n}\r\n\r\n.button-loading {\r\n  color: white;\r\n}\r\n\r\n.submit-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n  transition: left 0.6s;\r\n}\r\n\r\n.submit-btn:not(.disabled):active::before {\r\n  left: 100%;\r\n}\r\n\r\n.submit-btn:not(.disabled):active {\r\n  transform: translateY(4rpx);\r\n  box-shadow: 0 6rpx 16rpx rgba(0, 122, 255, 0.3),\r\n              0 3rpx 8rpx rgba(0, 122, 255, 0.2),\r\n              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);\r\n}\r\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./issue-add.vue?vue&type=style&index=0&id=2242e83e&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./issue-add.vue?vue&type=style&index=0&id=2242e83e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775845177\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}