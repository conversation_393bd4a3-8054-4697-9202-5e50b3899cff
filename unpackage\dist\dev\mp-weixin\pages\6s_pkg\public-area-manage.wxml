<view class="page-container data-v-2ae3b0bd"><view class="header data-v-2ae3b0bd"><view class="header-title data-v-2ae3b0bd">公共责任区管理</view><view class="header-subtitle data-v-2ae3b0bd">管理需要轮班清洁的公共区域</view></view><view class="stats-card data-v-2ae3b0bd"><block wx:if="{{loading}}"><view class="stats-loading data-v-2ae3b0bd"><view class="loading-content data-v-2ae3b0bd"><uni-icons vue-id="3192e7e9-1" type="spinner-cycle" size="40" color="#007AFF" class="data-v-2ae3b0bd" bind:__l="__l"></uni-icons><text class="loading-text data-v-2ae3b0bd">加载统计数据中...</text></view></view></block><block wx:else><view class="stat-item data-v-2ae3b0bd"><view class="stat-number data-v-2ae3b0bd">{{$root.g0}}</view><view class="stat-label data-v-2ae3b0bd">总公共区</view></view><view class="stat-item data-v-2ae3b0bd"><view class="stat-number data-v-2ae3b0bd">{{$root.g1}}</view><view class="stat-label data-v-2ae3b0bd">启用中</view></view><view class="stat-item data-v-2ae3b0bd"><view class="stat-number data-v-2ae3b0bd">{{$root.g2}}</view><view class="stat-label data-v-2ae3b0bd">已排班</view></view><view class="stat-item data-v-2ae3b0bd"><view class="stat-number data-v-2ae3b0bd">{{$root.g3}}</view><view class="stat-label data-v-2ae3b0bd">今日清扫</view></view></block></view><view class="action-bar data-v-2ae3b0bd"><button data-event-opts="{{[['tap',[['showAddArea',['$event']]]]]}}" class="action-btn primary data-v-2ae3b0bd" bindtap="__e"><uni-icons vue-id="3192e7e9-2" type="plus" size="16" color="#FFFFFF" class="data-v-2ae3b0bd" bind:__l="__l"></uni-icons><text class="data-v-2ae3b0bd">新增公共区</text></button></view><block wx:if="{{loading}}"><view class="list-loading data-v-2ae3b0bd"><view class="loading-content data-v-2ae3b0bd"><uni-icons vue-id="3192e7e9-3" type="spinner-cycle" size="40" color="#007AFF" class="data-v-2ae3b0bd" bind:__l="__l"></uni-icons><text class="loading-text data-v-2ae3b0bd">加载公共区数据中...</text></view></view></block><block wx:else><block wx:if="{{$root.g4>0}}"><view class="area-list data-v-2ae3b0bd"><block wx:for="{{$root.l0}}" wx:for-item="area" wx:for-index="index" wx:key="index"><view class="area-item data-v-2ae3b0bd"><view class="area-main data-v-2ae3b0bd"><view class="area-header data-v-2ae3b0bd"><view class="area-name data-v-2ae3b0bd">{{area.$orig.name}}</view><view class="{{['area-status','data-v-2ae3b0bd','status-'+area.$orig.status]}}">{{''+area.m0+''}}</view></view><view class="area-info data-v-2ae3b0bd"><view class="info-item data-v-2ae3b0bd"><uni-icons vue-id="{{'3192e7e9-4-'+index}}" type="location" size="14" color="#8E8E93" class="data-v-2ae3b0bd" bind:__l="__l"></uni-icons><text class="data-v-2ae3b0bd">{{area.$orig.location&&area.$orig.location.area||area.$orig.location||'未设置位置'}}</text></view><view class="info-item data-v-2ae3b0bd"><uni-icons vue-id="{{'3192e7e9-5-'+index}}" type="calendar" size="14" color="#8E8E93" class="data-v-2ae3b0bd" bind:__l="__l"></uni-icons><block wx:if="{{area.m1}}"><text class="data-v-2ae3b0bd">{{"每"+area.m2+"清扫"}}</text></block><block wx:else><text class="unscheduled-text data-v-2ae3b0bd">未设置清扫日程</text></block></view></view><block wx:if="{{area.$orig.description}}"><view class="area-description data-v-2ae3b0bd">{{''+area.$orig.description+''}}</view></block><view class="area-schedule data-v-2ae3b0bd"><block wx:if="{{area.m3}}"><view class="schedule-info data-v-2ae3b0bd"><view class="next-clean data-v-2ae3b0bd"><text class="next-label data-v-2ae3b0bd">下次清扫：</text><text class="{{['next-date','data-v-2ae3b0bd',(area.m4)?'today':'']}}">{{''+area.m5+''}}</text></view><block wx:if="{{area.$orig.last_clean_date}}"><view class="last-clean data-v-2ae3b0bd"><text class="last-label data-v-2ae3b0bd">上次完成：</text><text class="last-date data-v-2ae3b0bd">{{area.m6}}</text></view></block></view></block><block wx:else><view class="no-schedule-info data-v-2ae3b0bd"><uni-icons vue-id="{{'3192e7e9-6-'+index}}" type="calendar" size="16" color="#C7C7CC" class="data-v-2ae3b0bd" bind:__l="__l"></uni-icons><text class="data-v-2ae3b0bd">暂未设置清扫日程</text></view></block></view></view><view class="area-actions data-v-2ae3b0bd"><button data-event-opts="{{[['tap',[['setSchedule',['$0'],[[['areaList','',index]]]]]]]}}" class="action-icon-btn schedule data-v-2ae3b0bd" catchtap="__e"><uni-icons vue-id="{{'3192e7e9-7-'+index}}" type="calendar" size="16" color="#34C759" class="data-v-2ae3b0bd" bind:__l="__l"></uni-icons></button><button data-event-opts="{{[['tap',[['editArea',['$0'],[[['areaList','',index]]]]]]]}}" class="action-icon-btn edit data-v-2ae3b0bd" catchtap="__e"><uni-icons vue-id="{{'3192e7e9-8-'+index}}" type="compose" size="16" color="#007AFF" class="data-v-2ae3b0bd" bind:__l="__l"></uni-icons></button><button data-event-opts="{{[['tap',[['deleteArea',['$0'],[[['areaList','',index]]]]]]]}}" class="action-icon-btn delete data-v-2ae3b0bd" catchtap="__e"><uni-icons vue-id="{{'3192e7e9-9-'+index}}" type="trash" size="16" color="#FF3B30" class="data-v-2ae3b0bd" bind:__l="__l"></uni-icons></button></view></view></block></view></block><block wx:else><block wx:if="{{$root.g5}}"><p-empty-state vue-id="3192e7e9-10" type="area" text="暂无公共责任区" description="点击上方按钮创建第一个公共责任区" class="data-v-2ae3b0bd" bind:__l="__l"></p-empty-state></block></block></block><uni-popup vue-id="3192e7e9-11" type="center" mask-click="{{false}}" data-ref="areaFormPopup" class="data-v-2ae3b0bd vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="form-popup data-v-2ae3b0bd"><view class="form-header data-v-2ae3b0bd"><view class="form-title data-v-2ae3b0bd">{{(isEditing?'编辑':'新增')+"公共责任区"}}</view><button data-event-opts="{{[['tap',[['closeForm',['$event']]]]]}}" class="close-btn data-v-2ae3b0bd" bindtap="__e"><uni-icons vue-id="{{('3192e7e9-12')+','+('3192e7e9-11')}}" type="close" size="20" color="#8E8E93" class="data-v-2ae3b0bd" bind:__l="__l"></uni-icons></button></view><view class="form-content data-v-2ae3b0bd"><view class="form-item data-v-2ae3b0bd"><text class="form-label required data-v-2ae3b0bd">责任区名称</text><input class="form-input data-v-2ae3b0bd" type="text" placeholder="请输入公共责任区名称" maxlength="50" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['formData']]]]]}}" value="{{formData.name}}" bindinput="__e"/></view><view class="form-item data-v-2ae3b0bd"><text class="form-label data-v-2ae3b0bd">所在位置</text><input class="form-input data-v-2ae3b0bd" type="text" placeholder="请输入具体位置" maxlength="100" data-event-opts="{{[['input',[['__set_model',['$0','location','$event',[]],['formData']]]]]}}" value="{{formData.location}}" bindinput="__e"/></view><view class="form-item data-v-2ae3b0bd"><text class="form-label data-v-2ae3b0bd">责任区描述</text><textarea class="form-textarea data-v-2ae3b0bd" placeholder="请输入责任区详细描述" maxlength="200" show-count="{{true}}" data-event-opts="{{[['input',[['__set_model',['$0','description','$event',[]],['formData']]]]]}}" value="{{formData.description}}" bindinput="__e"></textarea></view><view class="form-item data-v-2ae3b0bd"><text class="form-label data-v-2ae3b0bd">状态</text><picker class="form-picker data-v-2ae3b0bd" range="{{statusOptions}}" range-key="label" value="{{statusIndex}}" data-event-opts="{{[['change',[['onStatusChange',['$event']]]]]}}" bindchange="__e"><view class="picker-value data-v-2ae3b0bd"><text class="data-v-2ae3b0bd">{{statusOptions[statusIndex].label}}</text><uni-icons vue-id="{{('3192e7e9-13')+','+('3192e7e9-11')}}" type="right" size="16" color="#C7C7CC" class="data-v-2ae3b0bd" bind:__l="__l"></uni-icons></view></picker></view></view><view class="form-footer data-v-2ae3b0bd"><button data-event-opts="{{[['tap',[['closeForm',['$event']]]]]}}" class="form-btn cancel data-v-2ae3b0bd" bindtap="__e">取消</button><button class="form-btn submit data-v-2ae3b0bd" loading="{{saving}}" data-event-opts="{{[['tap',[['submitForm',['$event']]]]]}}" bindtap="__e">{{''+(isEditing?'保存':'创建')+''}}</button></view></view></uni-popup><uni-popup vue-id="3192e7e9-14" type="center" mask-click="{{false}}" data-ref="schedulePopup" class="data-v-2ae3b0bd vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="schedule-popup data-v-2ae3b0bd"><view class="schedule-header data-v-2ae3b0bd"><view class="schedule-title data-v-2ae3b0bd">设置清扫日程</view><button data-event-opts="{{[['tap',[['closeSchedulePopup',['$event']]]]]}}" class="close-btn data-v-2ae3b0bd" bindtap="__e"><uni-icons vue-id="{{('3192e7e9-15')+','+('3192e7e9-14')}}" type="close" size="20" color="#8E8E93" class="data-v-2ae3b0bd" bind:__l="__l"></uni-icons></button></view><block wx:if="{{currentScheduleArea}}"><view class="schedule-content data-v-2ae3b0bd"><view class="area-name-display data-v-2ae3b0bd">{{currentScheduleArea.name}}</view><view class="schedule-item data-v-2ae3b0bd"><text class="schedule-label data-v-2ae3b0bd">清扫日期</text><picker class="schedule-picker data-v-2ae3b0bd" range="{{weekDayOptions}}" range-key="label" value="{{scheduleWeekIndex}}" data-event-opts="{{[['change',[['onScheduleWeekChange',['$event']]]]]}}" bindchange="__e"><view class="schedule-picker-value data-v-2ae3b0bd"><text class="data-v-2ae3b0bd">{{weekDayOptions[scheduleWeekIndex].label}}</text><uni-icons vue-id="{{('3192e7e9-16')+','+('3192e7e9-14')}}" type="right" size="16" color="#C7C7CC" class="data-v-2ae3b0bd" bind:__l="__l"></uni-icons></view></picker></view><view class="schedule-item data-v-2ae3b0bd"><text class="schedule-label data-v-2ae3b0bd">开始日期</text><picker class="schedule-picker data-v-2ae3b0bd" mode="date" value="{{scheduleData.start_date}}" data-event-opts="{{[['change',[['onStartDateChange',['$event']]]]]}}" bindchange="__e"><view class="schedule-picker-value data-v-2ae3b0bd"><text class="data-v-2ae3b0bd">{{scheduleData.start_date||'请选择开始日期'}}</text><uni-icons vue-id="{{('3192e7e9-17')+','+('3192e7e9-14')}}" type="calendar" size="16" color="#C7C7CC" class="data-v-2ae3b0bd" bind:__l="__l"></uni-icons></view></picker></view><block wx:if="{{scheduleData.scheduled_day!==null&&scheduleData.start_date}}"><view class="schedule-preview data-v-2ae3b0bd"><view class="preview-title data-v-2ae3b0bd">预览</view><view class="preview-info data-v-2ae3b0bd"><text class="data-v-2ae3b0bd">{{"从 "+scheduleData.start_date+" 开始，每"+$root.m7+"进行清扫"}}</text></view></view></block></view></block><view class="schedule-footer data-v-2ae3b0bd"><button data-event-opts="{{[['tap',[['closeSchedulePopup',['$event']]]]]}}" class="schedule-btn cancel data-v-2ae3b0bd" bindtap="__e">取消</button><block wx:if="{{currentScheduleArea&&currentScheduleArea.scheduled_day!==null}}"><button data-event-opts="{{[['tap',[['clearSchedule',['$event']]]]]}}" class="schedule-btn clear data-v-2ae3b0bd" bindtap="__e">清除排班</button></block><button class="schedule-btn submit data-v-2ae3b0bd" loading="{{scheduleSaving}}" data-event-opts="{{[['tap',[['submitSchedule',['$event']]]]]}}" bindtap="__e">保存排班</button></view></view></uni-popup></view>