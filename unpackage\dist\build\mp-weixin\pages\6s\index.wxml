<view class="sixs-container data-v-339109bc"><view class="content-container data-v-339109bc"><view class="tab-content data-v-339109bc"><view class="section data-v-339109bc"><view class="overview-header data-v-339109bc"><view class="header-content data-v-339109bc"><text class="overview-title data-v-339109bc">6S管理概览</text><text class="overview-subtitle data-v-339109bc">{{currentTimeRangeText}}</text></view></view><block wx:if="{{loading&&!dataLoaded}}"><view class="loading-container data-v-339109bc"><view class="loading-content data-v-339109bc"><view class="loading-spinner data-v-339109bc"></view><text class="loading-text data-v-339109bc">统计数据加载中...</text></view></view></block><block wx:else><block wx:if="{{loadError}}"><view class="error-container data-v-339109bc"><view class="error-content data-v-339109bc"><uni-icons vue-id="44707126-1" type="info" size="24" color="#FF3B30" class="data-v-339109bc" bind:__l="__l"></uni-icons><text class="error-text data-v-339109bc">数据加载失败，请稍后重试</text><button data-event-opts="{{[['tap',[['loadPageDataOptimized',['$event']]]]]}}" class="retry-btn data-v-339109bc" bindtap="__e">重新加载</button></view></view></block><block wx:else><view class="overview-stats data-v-339109bc"><view class="stat-item data-v-339109bc"><text class="stat-number blue data-v-339109bc">{{stats.totalAreas}}</text><text class="stat-label data-v-339109bc">总责任区</text></view><view class="stat-item data-v-339109bc"><text class="stat-number green data-v-339109bc">{{stats.completionRate+"%"}}</text><text class="stat-label data-v-339109bc">本周完成率</text></view><view class="stat-item data-v-339109bc"><text class="stat-number orange data-v-339109bc">{{stats.foundIssues}}</text><text class="stat-label data-v-339109bc">待整改问题</text></view><view class="stat-item data-v-339109bc"><text class="stat-number red data-v-339109bc">{{stats.urgentIssues}}</text><text class="stat-label data-v-339109bc">逾期未清理</text></view></view></block></block></view><view class="section data-v-339109bc"><view class="quick-actions-header data-v-339109bc"><view class="header-content data-v-339109bc"><text class="quick-actions-title data-v-339109bc">快捷操作</text><text class="quick-actions-subtitle data-v-339109bc">常用功能快速入口</text></view></view><view class="quick-actions-grid data-v-339109bc"><view data-event-opts="{{[['tap',[['goToMyArea',['$event']]]]]}}" class="quick-action-btn primary data-v-339109bc" bindtap="__e"><view class="btn-content data-v-339109bc"><uni-icons vue-id="44707126-2" type="list" size="20" color="#FFFFFF" class="data-v-339109bc" bind:__l="__l"></uni-icons><text class="btn-text data-v-339109bc">我的责任区</text></view></view><view data-event-opts="{{[['tap',[['goToPendingTasks',['$event']]]]]}}" class="quick-action-btn success data-v-339109bc" bindtap="__e"><view class="btn-content data-v-339109bc"><uni-icons vue-id="44707126-3" type="eye" size="20" color="#FFFFFF" class="data-v-339109bc" bind:__l="__l"></uni-icons><text class="btn-text data-v-339109bc">责任区检查</text></view></view><view data-event-opts="{{[['tap',[['uploadPhoto',['$event']]]]]}}" class="quick-action-btn warning data-v-339109bc" bindtap="__e"><view class="btn-content data-v-339109bc"><uni-icons vue-id="44707126-4" type="camera" size="20" color="#FFFFFF" class="data-v-339109bc" bind:__l="__l"></uni-icons><text class="btn-text data-v-339109bc">月度检查</text></view></view><view data-event-opts="{{[['tap',[['manageBaseData',['$event']]]]]}}" class="quick-action-btn purple data-v-339109bc" bindtap="__e"><view class="btn-content data-v-339109bc"><uni-icons vue-id="44707126-5" type="gear" size="20" color="#FFFFFF" class="data-v-339109bc" bind:__l="__l"></uni-icons><text class="btn-text data-v-339109bc">基础数据</text></view></view></view></view><view class="section data-v-339109bc"><view class="section-header data-v-339109bc"><view class="header-content data-v-339109bc"><text class="section-title data-v-339109bc">最新动态</text><text class="section-subtitle data-v-339109bc">最近的检查和整改情况</text></view></view><view class="activity-list data-v-339109bc"><block wx:if="{{activitiesLoading}}"><view class="loading-container data-v-339109bc"><view class="loading-content data-v-339109bc"><view class="loading-spinner data-v-339109bc"></view><text class="loading-text data-v-339109bc">动态数据加载中...</text></view></view></block><block wx:else><block wx:if="{{$root.g0===0}}"><view class="empty-state data-v-339109bc"><uni-icons vue-id="44707126-6" type="info" size="20" color="#8E8E93" class="data-v-339109bc" bind:__l="__l"></uni-icons><text class="empty-text data-v-339109bc">暂无最新动态</text></view></block><block wx:else><block wx:for="{{recentActivities}}" wx:for-item="activity" wx:for-index="index" wx:key="index"><view class="activity-item data-v-339109bc"><view class="{{['activity-icon','data-v-339109bc',activity.type]}}"><uni-icons vue-id="{{'44707126-7-'+index}}" type="{{activity.iconType}}" color="{{activity.iconColor}}" size="18" class="data-v-339109bc" bind:__l="__l"></uni-icons></view><view class="activity-content data-v-339109bc"><text class="activity-title data-v-339109bc">{{activity.title}}</text><text class="activity-time data-v-339109bc">{{activity.time}}</text></view></view></block></block></block></view></view><view class="section data-v-339109bc"><view class="section-header data-v-339109bc"><view class="header-content data-v-339109bc"><text class="section-title data-v-339109bc">智能提醒</text><text class="section-subtitle data-v-339109bc">根据当前时间和业务规则智能生成</text></view></view><view class="reminder-list data-v-339109bc"><block wx:if="{{remindersLoading}}"><view class="loading-container data-v-339109bc"><view class="loading-content data-v-339109bc"><view class="loading-spinner data-v-339109bc"></view><text class="loading-text data-v-339109bc">智能提醒生成中...</text></view></view></block><block wx:else><block wx:if="{{$root.g1===0}}"><view class="empty-state data-v-339109bc"><uni-icons vue-id="44707126-8" type="checkmarkempty" size="20" color="#34C759" class="data-v-339109bc" bind:__l="__l"></uni-icons><text class="empty-text data-v-339109bc">暂无智能提醒</text></view></block><block wx:else><block wx:for="{{smartReminders}}" wx:for-item="reminder" wx:for-index="index" wx:key="index"><view class="{{['reminder-item','data-v-339109bc',reminder.type]}}"><view class="{{['reminder-icon','data-v-339109bc',reminder.type]}}"><text class="icon-text data-v-339109bc">{{reminder.iconSymbol}}</text></view><view class="reminder-content data-v-339109bc"><text class="reminder-title data-v-339109bc">{{reminder.title}}</text><text class="reminder-desc data-v-339109bc">{{reminder.description}}</text></view></view></block></block></block></view></view></view></view></view>