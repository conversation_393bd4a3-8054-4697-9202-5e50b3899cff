<template>
  <view class="page-container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载整改任务详情中...</text>
      </view>
    </view>
    
    <!-- 错误状态 -->
    <view v-else-if="loadError" class="error-container">
      <view class="error-content">
        <uni-icons type="info" size="48" color="#FF3B30"></uni-icons>
        <text class="error-text">{{ loadError }}</text>
        <button class="retry-button" @click="retryLoad">重新加载</button>
      </view>
    </view>
    
    <!-- 正常内容 -->
    <template v-else>
      <!-- 任务基本信息 -->
      <view class="card">
        <view class="card-header">
          <view class="header-content">
            <view class="card-title">整改任务详情</view>
            <view class="card-subtitle">{{ taskInfo.areaName }} - {{ taskInfo.areaType === 'public' ? '公共责任区' : '固定责任区' }}</view>
          </view>
          <view class="status-badge" :class="['status-' + taskInfo.status]">
            {{ getStatusText(taskInfo.status) }}
          </view>
        </view>
        <view class="card-body">
          <view class="info-grid">
            <view class="info-item">
              <view class="info-icon">
                <uni-icons type="calendar" size="18" color="#007AFF"></uni-icons>
              </view>
              <view class="info-content">
                <view class="info-label">发现时间</view>
                <view class="info-value">{{ formatDateTime(taskInfo.issueFoundDate) }}</view>
              </view>
            </view>
            <view class="info-item">
              <view class="info-icon">
                <uni-icons type="person" size="18" color="#FF9500"></uni-icons>
              </view>
              <view class="info-content">
                <view class="info-label">负责人</view>
                <view class="info-value">{{ taskInfo.assignee }}</view>
              </view>
            </view>
            <view class="info-item">
              <view class="info-icon">
                <uni-icons type="contact" size="18" color="#5856D6"></uni-icons>
              </view>
              <view class="info-content">
                <view class="info-label">检查员</view>
                <view class="info-value">{{ taskInfo.inspector }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 问题描述 -->
      <view class="card">
        <view class="card-header">
          <view class="card-title">发现问题</view>
          <view class="card-subtitle">检查时发现的问题描述</view>
        </view>
        <view class="card-body">
          <view class="problem-content">{{ taskInfo.problemDescription }}</view>
        </view>
      </view>
      
      <!-- 问题照片 -->
      <view v-if="hasIssuePhotos" class="card">
        <view class="card-header">
          <view class="card-title">问题照片</view>
          <view class="card-subtitle">检查时拍摄的问题照片 ({{ taskInfo.issuePhotos.length }}张)</view>
        </view>
        <view class="card-body">

          <view class="photo-grid">
            <view 
              v-for="(photo, index) in taskInfo.issuePhotos" 
              :key="index"
              class="photo-item"
              @click="previewPhoto(taskInfo.issuePhotos, index)"
            >
              <image 
                :src="getPhotoUrl(photo)" 
                mode="aspectFill"
                @error="onPhotoError(index, 'issue')"
                @load="onPhotoLoad(index, 'issue')"
              ></image>
              <view v-if="photoErrors.issue && photoErrors.issue[index]" class="photo-error">
                <uni-icons type="image" size="32" color="#8E8E93"></uni-icons>
                <text>加载失败</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 整改要求 -->
      <view v-if="taskInfo.rectificationRequirement" class="card">
        <view class="card-header">
          <view class="card-title">整改要求</view>
          <view class="card-subtitle">需要完成的整改内容</view>
        </view>
        <view class="card-body">
          <view class="requirement-content">{{ taskInfo.rectificationRequirement }}</view>
        </view>
      </view>
      
      <!-- 整改结果 -->
      <view v-if="hasRectificationResult" class="card">
        <view class="card-header">
          <view class="card-title">整改结果</view>
          <view class="card-subtitle">员工提交的整改说明</view>
        </view>
        <view class="card-body">
          <view v-if="taskInfo.rectificationDescription" class="rectification-content">
            {{ taskInfo.rectificationDescription }}
          </view>
          <view v-else class="no-description">
            <text>暂无整改说明</text>
          </view>
        </view>
      </view>
      
      <!-- 整改照片 -->
      <view v-if="hasRectificationPhotos" class="card">
        <view class="card-header">
          <view class="card-title">整改照片</view>
          <view class="card-subtitle">员工提交的整改后照片</view>
        </view>
        <view class="card-body">
          <view class="photo-grid">
            <view 
              v-for="(photo, index) in taskInfo.rectificationPhotos" 
              :key="index"
              class="photo-item"
              @click="previewPhoto(taskInfo.rectificationPhotos, index)"
            >
              <image 
                :src="getPhotoUrl(photo)" 
                mode="aspectFill"
                @error="onPhotoError(index, 'rectification')"
                @load="onPhotoLoad(index, 'rectification')"
              ></image>
              <view v-if="photoErrors.rectification && photoErrors.rectification[index]" class="photo-error">
                <uni-icons type="image" size="32" color="#8E8E93"></uni-icons>
                <text>加载失败</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 审核记录 -->
      <view v-if="taskInfo.reviewHistory && taskInfo.reviewHistory.length > 0" class="card">
        <view class="card-header">
          <view class="card-title">审核记录</view>
          <view class="card-subtitle">历史审核记录</view>
        </view>
        <view class="card-body">
          <view class="review-timeline">
            <view 
              v-for="(review, index) in taskInfo.reviewHistory" 
              :key="index"
              class="review-item"
            >
              <view class="review-dot" :class="['review-' + review.result]"></view>
              <view class="review-content">
                <view class="review-header">
                  <text class="review-result">{{ review.result === 'approved' ? '复查通过' : '需重新整改' }}</text>
                  <text class="review-date">{{ formatDateTime(review.reviewDate) }}</text>
                </view>
                <view v-if="review.comments" class="review-comments">{{ review.comments }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 审核情况 -->
      <view v-if="taskInfo.reviewComments" class="card">
        <view class="card-header">
          <view class="card-title">审核情况</view>
          <view class="card-subtitle">检查员复查的现场情况</view>
        </view>
        <view class="card-body">
          <view class="problem-content">
            {{ taskInfo.reviewComments }}
          </view>
        </view>
      </view>

      <!-- 审核照片 -->
      <view v-if="hasReviewPhotos" class="card">
        <view class="card-header">
          <view class="card-title">审核照片</view>
          <view class="card-subtitle">审核员拍摄的现场照片</view>
        </view>
        <view class="card-body">
          <view class="photo-grid">
            <view 
              v-for="(photo, index) in taskInfo.reviewPhotos" 
              :key="index"
              class="photo-item"
              @click="previewPhoto(taskInfo.reviewPhotos, index)"
            >
              <image 
                :src="getPhotoUrl(photo)" 
                mode="aspectFill"
                @error="onPhotoError(index, 'review')"
                @load="onPhotoLoad(index, 'review')"
              ></image>
              <view v-if="photoErrors.review && photoErrors.review[index]" class="photo-error">
                <uni-icons type="image" size="32" color="#8E8E93"></uni-icons>
                <text>加载失败</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </template>
    
    <!-- 操作按钮 -->
    <view v-if="!loading && !loadError" class="button-container">
      <!-- 待复查状态显示复查按钮 -->
      <button 
        v-if="taskInfo.status === 'pending_review'"
        class="primary-button" 
        @click="goToReview"
      >
        进行复查
      </button>
      
      <!-- 其他状态显示返回按钮 -->
      <button 
        v-else
        class="primary-button" 
        @click="goBack"
      >
        返回
      </button>
    </view>
  </view>
</template>

<script>
import { callCloudFunction } from '@/utils/auth.js';

export default {
  name: 'InspectorRectificationDetail',
  data() {
    return {
      taskId: '',
      loading: false,
      loadError: '',
      taskInfo: {
        id: '',
        areaName: '',
        areaType: 'fixed',
        status: 'pending',
        problemDescription: '',
        issueFoundDate: '',
        assignee: '',
        inspector: '',
        rectificationDescription: '',
        rectificationRequirement: '',
        issuePhotos: [],
        rectificationPhotos: [],
        reviewHistory: []
      },
      photoErrors: {
        issue: {},
        rectification: {},
        review: {}
      }
    }
  },
  computed: {
    // 是否有整改结果
    hasRectificationResult() {
      // 只有在状态不是待处理、待分配、处理中时，且有整改描述或照片时才显示
      const hasCompletedStatus = !['pending_rectification', 'pending_assignment', 'pending', 'in_progress'].includes(this.taskInfo.status);
      const hasDescription = this.taskInfo.rectificationDescription && this.taskInfo.rectificationDescription.trim();
      const hasPhotos = this.taskInfo.rectificationPhotos && this.taskInfo.rectificationPhotos.length > 0;
      
      return hasCompletedStatus && (hasDescription || hasPhotos);
    },
    
    // 是否有整改照片
    hasRectificationPhotos() {
      // 只有在有整改结果且有照片时才显示
      return this.hasRectificationResult && this.taskInfo.rectificationPhotos && this.taskInfo.rectificationPhotos.length > 0;
    },

    // 是否有问题照片
    hasIssuePhotos() {
      return this.taskInfo.issuePhotos && this.taskInfo.issuePhotos.length > 0;
    },

    // 是否有审核照片
    hasReviewPhotos() {
      return this.taskInfo.reviewPhotos && this.taskInfo.reviewPhotos.length > 0;
    }
  },
  onLoad(options) {
    this.taskId = options.taskId || options.id;
    
    if (this.taskId) {
      this.loadTaskDetail();
    } else {
      this.loadError = '任务ID不能为空';
    }
  },
  methods: {
    // 加载任务详情
    async loadTaskDetail() {
      this.loading = true;
      this.loadError = '';
      
      try {
        const result = await callCloudFunction('hygiene-rectification', {
          action: 'getRectificationDetail',
          data: { id: this.taskId }
        });

        if (result && result.success && result.data) {
          const task = result.data;
          
          this.taskInfo = {
            id: task._id || task.id,
            areaName: task.area_name || '未知责任区',
            areaType: task.area_type || 'fixed',
            status: task.status || 'pending',
            problemDescription: this.getIssueDescription(task),
            issueFoundDate: task.created_at,
            assignee: this.getAssigneeName(task),
            inspector: this.getInspectorName(task),
            rectificationDescription: task.completion_description || '',
            rectificationRequirement: task.rectification_requirement || '',
            // 统一照片字段映射，与rectification-upload.vue保持一致
            issuePhotos: this.processPhotos(task.photos || []),
            rectificationPhotos: this.processPhotos(task.completion_photos || []),
            reviewPhotos: this.processPhotos(task.review_photos || []),
            reviewHistory: this.processReviewHistory(task.review_history || []),
            // 审核信息
            reviewComments: task.review_comments || '',
            reviewDate: task.review_date || '',
            reviewResult: task.review_result || '',
            reviewerName: task.reviewer_name || ''
          };
        } else {
          throw new Error(result?.message || '获取整改任务详情失败');
        }
      } catch (error) {
        this.loadError = error.message || '加载失败，请稍后重试';
        
        uni.showModal({
          title: '加载失败',
          content: this.loadError,
          showCancel: true,
          cancelText: '返回',
          confirmText: '重试',
          success: (res) => {
            if (res.confirm) {
              this.loadTaskDetail();
            } else {
              uni.navigateBack();
            }
          }
        });
      } finally {
        this.loading = false;
      }
    },

    // 重新加载
    retryLoad() {
      this.loadTaskDetail();
    },

    // 获取问题描述
    getIssueDescription(task) {
      // 直接从 description 字段获取（这是主要存储位置）
      if (task.description) {
        return task.description;
      }
      // 备用：从 issue 子对象中获取
      if (task.issue && task.issue.description) {
        return task.issue.description;
      }
      // 备用：从其他字段获取
      return task.issue_description || task.title || '无问题描述';
    },

    // 获取负责人姓名
    getAssigneeName(task) {
      // 优先从 area_assignee_name 获取（云函数查询的责任区负责人）
      if (task.area_assignee_name) {
        return task.area_assignee_name;
      }
      // 其次从 assignee_name 获取
      if (task.assignee_name) {
        return task.assignee_name;
      }
      // 最后从 assigned_to_name 获取
      return task.assigned_to_name || '未分配';
    },

    // 获取检查员姓名
    getInspectorName(task) {
      // 优先从 assigned_by_name 获取（任务分配者，通常是检查员）
      if (task.assigned_by_name) {
        return task.assigned_by_name;
      }
      // 其次从 inspector_name 获取
      if (task.inspector_name) {
        return task.inspector_name;
      }
      // 最后从 created_by_name 获取
      return task.created_by_name || '未知检查员';
    },

    // 获取问题照片
    getIssuePhotos(task) {
      // 直接从 photos 字段获取问题照片（这是主要存储位置）
      if (task.photos && Array.isArray(task.photos)) {
        return task.photos;
      }
      // 备用：从 issue 子对象中获取照片
      if (task.issue && task.issue.photos && Array.isArray(task.issue.photos)) {
        return task.issue.photos;
      }
      // 备用：从其他可能的字段获取
      if (task.issue_photos && Array.isArray(task.issue_photos)) {
        return task.issue_photos;
      }
      return task.inspection_photos || [];
    },

    // 获取整改照片
    getRectificationPhotos(task) {
      // 优先从 completion_photos 获取（新的字段名）
      if (task.completion_photos && Array.isArray(task.completion_photos)) {
        return task.completion_photos;
      }
      // 其次从 rectification_photos 获取
      if (task.rectification_photos && Array.isArray(task.rectification_photos)) {
        return task.rectification_photos;
      }
      // 最后从 photos 获取
      return task.photos || [];
    },

    // 处理照片数据
    processPhotos(photos) {
      if (!photos || !Array.isArray(photos)) return [];
      
      return photos.map(photo => {
        if (typeof photo === 'string') {
          return photo;
        } else if (photo && typeof photo === 'object' && photo.url) {
          return photo.url;
        }
        return '';
      }).filter(url => url);
    },

    // 处理审核历史
    processReviewHistory(history) {
      if (!history || !Array.isArray(history)) return [];
      
      return history.map(review => ({
        result: review.review_result || review.result,
        comments: review.review_comments || review.comments || '',
        reviewDate: review.review_date || review.created_at,
        reviewer: review.reviewer_name || '未知审核员'
      }));
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'pending_rectification': '待整改',
        'pending_assignment': '待分配',
        'pending': '待处理',
        'in_progress': '整改中',
        'pending_review': '待复查',
        'approved': '复查通过',
        'verified': '整改合格',
        'rejected': '整改不达标',
        'completed': '已完成'
      };
      return statusMap[status] || '未知状态';
    },

    // 格式化日期时间
    formatDateTime(dateString) {
      if (!dateString) return '--';
      
      try {
        let date;
        if (typeof dateString === 'string') {
          if (dateString.includes('T') || dateString.includes('Z')) {
            date = new Date(dateString);
          } else {
            date = new Date(dateString.replace(/-/g, '/'));
          }
        } else {
          date = new Date(dateString);
        }
        
        if (isNaN(date.getTime())) {
          return '--';
        }
        
        return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      } catch (error) {
        return '--';
      }
    },

    // 预览照片
    previewPhoto(photos, index) {
      uni.previewImage({
        urls: photos,
        current: index
      });
    },

    // 前往审核页面
    goToReview() {
      uni.navigateTo({
        url: `/pages/6s_pkg/rectification-review?taskId=${this.taskId}`
      });
    },

    // 返回
    goBack() {
      uni.navigateBack();
    },

    // 获取照片URL
    getPhotoUrl(photo) {
      return typeof photo === 'string' ? photo : (photo && photo.url ? photo.url : photo);
    },

    // 处理照片加载错误
    onPhotoError(index, type) {
      this.$set(this.photoErrors[type], index, true);
    },

    // 处理照片加载成功
    onPhotoLoad(index, type) {
      this.$set(this.photoErrors[type], index, false);
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
  padding: 24rpx;
}

.card {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 32rpx 32rpx 16rpx 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-content {
  flex: 1;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 8rpx;
}

.card-subtitle {
  font-size: 26rpx;
  color: #8E8E93;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 600;
  
  &.status-pending,
  &.status-pending_rectification {
    background: #FFF4E6;
    color: #FF9500;
  }
  
  &.status-pending_review {
    background: #E6F3FF;
    color: #007AFF;
  }
  
  &.status-completed {
    background: #E8F5E8;
    color: #34C759;
  }
  
  &.status-overdue,
  &.status-rejected {
    background: #FFE6E6;
    color: #FF3B30;
  }
  
  &.status-in_progress {
    background: #F0F9FF;
    color: #007AFF;
  }
}

.card-body {
  padding: 32rpx;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
  border: 1rpx solid #E5E5EA;
}

.info-icon {
  width: 40rpx;
  height: 40rpx;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.info-content {
  flex: 1;
  min-width: 0;
}

.info-label {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 4rpx;
}

.info-value {
  font-size: 28rpx;
  color: #1C1C1E;
  font-weight: 500;
  word-break: break-all;
}

/* 内容区域 */
.problem-content, .requirement-content, .rectification-content {
  font-size: 28rpx;
  color: #1C1C1E;
  line-height: 1.6;
  padding: 20rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
  border: 1rpx solid #E5E5EA;
}

.no-description {
  padding: 40rpx 20rpx;
  text-align: center;
  color: #8E8E93;
  font-size: 26rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
  border: 1rpx solid #E5E5EA;
}

/* 照片网格 */
.photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.photo-item {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  border: 1rpx solid #E5E5EA;
}

.photo-item {
  position: relative;
}

.photo-item image {
  width: 100%;
  height: 100%;
}

.photo-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(248, 249, 250, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  border-radius: 12rpx;
  
  text {
    font-size: 24rpx;
    color: #8E8E93;
  }
}

/* 审核记录时间线 */
.review-timeline {
  position: relative;
}

.review-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 32rpx;
  position: relative;
}

.review-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 12rpx;
  top: 32rpx;
  width: 2rpx;
  height: calc(100% + 16rpx);
  background: #E5E5EA;
}

.review-dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  flex-shrink: 0;
  margin-top: 8rpx;
  
  &.review-approved {
    background: #34C759;
  }
  
  &.review-rejected {
    background: #FF3B30;
  }
}

.review-content {
  flex: 1;
  padding: 16rpx 20rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
  border: 1rpx solid #E5E5EA;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.review-result {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
}

.review-date {
  font-size: 24rpx;
  color: #8E8E93;
}

.review-comments {
  font-size: 26rpx;
  color: #1C1C1E;
  line-height: 1.5;
}

/* 按钮 */
.button-container {
  padding: 32rpx 0;
}

.primary-button {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  width: 100%;
}

.primary-button[disabled] {
  background: #C7C7CC;
  color: #8E8E93;
}

.secondary-button {
  background: white;
  color: #007AFF;
  border: 2rpx solid #007AFF;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  width: 100%;
}

/* 加载和错误状态 */
.loading-container, .error-container {
  padding: 120rpx 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-content, .error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  text-align: center;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #E5E7EB;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text, .error-text {
  font-size: 28rpx;
  color: #8E8E93;
  line-height: 1.4;
}

.retry-button {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
}

/* 响应式 */
@media (max-width: 414px) {
  .info-grid {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }
  
  .photo-item {
    width: 160rpx;
    height: 160rpx;
  }
}
</style>