{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s_pkg/data-manage.vue?3aa1", "webpack:///D:/Xwzc/pages/6s_pkg/data-manage.vue?ff9b", "webpack:///D:/Xwzc/pages/6s_pkg/data-manage.vue?760b", "webpack:///D:/Xwzc/pages/6s_pkg/data-manage.vue?1dbd", "uni-app:///pages/6s_pkg/data-manage.vue", "webpack:///D:/Xwzc/pages/6s_pkg/data-manage.vue?505b", "webpack:///D:/Xwzc/pages/6s_pkg/data-manage.vue?c798"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "loading", "loadError", "dataLoaded", "showPermissionModal", "overview", "fixedAreas", "publicAreas", "locations", "employees", "assignments", "processCache", "overviewData", "cacheTimestamp", "cacheExpiry", "onLoad", "onShow", "methods", "checkUserPermission", "get<PERSON><PERSON><PERSON><PERSON>", "CACHE_KEYS", "userRole", "roles", "console", "uni", "title", "icon", "duration", "setTimeout", "url", "closePermissionModal", "loadOverviewDataOptimized", "Promise", "results", "silentRefreshData", "loadAreasCountOptimized", "action", "type", "pageSize", "result", "extractDataCount", "loadLocationsCountOptimized", "loadEmployeeAssignmentCountOptimized", "processAssignmentData", "map", "filter", "manageFixedAreas", "managePublicAreas", "manageLocations", "manageAssignment", "managePublicSchedule", "isCache<PERSON><PERSON>d", "isCacheExpired", "updateCache", "clearCache", "handleLoadError", "retryLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAqmB,CAAgB,+nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC6LznB;AAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EAEAC;IACA;IACA;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;QACA;UAAAC;UAAAC;;QAEA;QACA;;QAEA;QACA;QACA;QAEA;UACA;YACAC;UACA;YACAA;UACA;QACA;UACAA;QACA;;QAEA;QACA;QACA;UACAC;QACA;UACA;UACAA;QACA;UACA;UACAA;QACA;;QAEA;QACA;QACA;UAAA;QAAA;QAIA;UACA;UACA;UACA;QACA;QAEA;MACA;QACAC;QACAC;UACAC;UACAC;UACAC;QACA;QACA;QACAC;UACAJ;YACAK;UACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACAN;QACAK;MACA;IACA;IAEA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACA;;gBAEA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAKAC,oBACA,wCACA,yCACA,qCACA,6CACA;cAAA;gBALAC;gBAOA;gBACArB;kBACAN;kBACAC;kBACAC;kBACAC;kBACAC;gBACA,GAEA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAwB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGA;gBACA;;gBAEA;gBAAA;gBAAA,OACAF,oBACA,yCACA,0CACA,sCACA,8CACA;cAAA;gBALAC;gBAOArB;kBACAN;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAKA;IAEA;IACAyB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAC;kBACApC;oBAAAqC;oBAAAC;kBAAA;gBACA;cAAA;gBAHAC;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBAAA,kCACA;cAAA;gBAAA,kCAEA;cAAA;gBAAA;gBAAA;gBAAA,kCAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAL;gBACA;cAAA;gBAFAG;gBAAA,MAGAA;kBAAA;kBAAA;gBAAA;gBAAA,kCACAA;kBACA;gBACA;cAAA;gBAAA,kCAEA;cAAA;gBAAA;gBAAA;gBAAA,kCAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAN;kBACApC;oBAAAsC;kBAAA;gBACA;cAAA;gBAHAC;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBAAA,kCACA;cAAA;gBAAA,kCAEA;kBAAA9B;kBAAAC;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,kCAEA;kBAAAD;kBAAAC;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiC;MACA,8BACAC;QAAA;QAAA;MAAA,GACAC;MAEA;QACApC;QACAC;MACA;IACA;IAEA;IACAoC;MACAtB;QACAK;MACA;IACA;IAEA;IACAkB;MACAvB;QACAK;MACA;IACA;IAEA;IACAmB;MACAxB;QACAK;MACA;IACA;IAEA;IACAoB;MACAzB;QACAK;MACA;IACA;IAEA;IACAqB;MACA1B;QACAK;MACA;IACA;IAIA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;IAEA;IACAsB;MACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA/B;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACA6B;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3hBA;AAAA;AAAA;AAAA;AAAwqC,CAAgB,8oCAAG,EAAC,C;;;;;;;;;;;ACA5rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/data-manage.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/data-manage.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./data-manage.vue?vue&type=template&id=98623ac8&scoped=true&\"\nvar renderjs\nimport script from \"./data-manage.vue?vue&type=script&lang=js&\"\nexport * from \"./data-manage.vue?vue&type=script&lang=js&\"\nimport style0 from \"./data-manage.vue?vue&type=style&index=0&id=98623ac8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"98623ac8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/data-manage.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./data-manage.vue?vue&type=template&id=98623ac8&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./data-manage.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./data-manage.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"page-container\">\r\n    <!-- 页面头部 -->\r\n    <view class=\"header\">\r\n      <view class=\"header-title\">基础数据管理</view>\r\n      <view class=\"header-subtitle\">统一管理6S系统所有基础配置数据</view>\r\n    </view>\r\n\r\n    <!-- 数据概览 -->\r\n    <view class=\"overview-card\">\r\n      <view class=\"overview-title\">数据概览</view>\r\n      \r\n      <!-- 加载状态 -->\r\n      <view v-if=\"loading && !dataLoaded\" class=\"stats-loading\">\r\n        <view class=\"loading-content\">\r\n          <view class=\"loading-spinner\"></view>\r\n          <text class=\"loading-text\">加载数据概览中...</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 错误状态 -->\r\n      <view v-else-if=\"loadError\" class=\"error-container\">\r\n        <view class=\"error-content\">\r\n          <uni-icons type=\"info\" size=\"24\" color=\"#FF3B30\"></uni-icons>\r\n          <text class=\"error-text\">数据加载失败，请稍后重试</text>\r\n          <button class=\"retry-btn\" @click=\"retryLoad\">重新加载</button>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 正常统计 -->\r\n      <view v-else class=\"stats-grid\">\r\n        <view class=\"stat-item\">\r\n          <view class=\"stat-number\">{{ overview.fixedAreas || 0 }}</view>\r\n          <view class=\"stat-label\">固定责任区</view>\r\n        </view>\r\n        <view class=\"stat-item\">\r\n          <view class=\"stat-number\">{{ overview.publicAreas || 0 }}</view>\r\n          <view class=\"stat-label\">公共责任区</view>\r\n        </view>\r\n        <view class=\"stat-item\">\r\n          <view class=\"stat-number\">{{ overview.locations || 0 }}</view>\r\n          <view class=\"stat-label\">月度位置数</view>\r\n        </view>\r\n        <view class=\"stat-item\">\r\n          <view class=\"stat-number\">{{ overview.employees || 0 }}</view>\r\n          <view class=\"stat-label\">已分配员工</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 管理模块 -->\r\n    <view class=\"modules-container\">\r\n             <!-- 责任区管理 -->\r\n       <view class=\"module-section\">\r\n         <view class=\"section-title\">\r\n           <uni-icons type=\"home\" size=\"20\" color=\"#007AFF\" />\r\n           <text>责任区管理</text>\r\n         </view>\r\n         <view class=\"module-grid\">\r\n           <view class=\"module-card\" @click=\"manageFixedAreas\">\r\n             <view class=\"module-icon fixed-area\">\r\n               <uni-icons type=\"shop\" size=\"32\" color=\"#007AFF\" />\r\n             </view>\r\n             <view class=\"module-content\">\r\n               <view class=\"module-title\">固定责任区</view>\r\n               <view class=\"module-desc\">管理各部门固定负责的区域</view>\r\n               <view class=\"module-count\">{{ overview.fixedAreas || 0 }} 个</view>\r\n             </view>\r\n             <view class=\"module-arrow\">\r\n               <uni-icons type=\"right\" size=\"16\" color=\"#C7C7CC\" />\r\n             </view>\r\n           </view>\r\n\r\n           <view class=\"module-card\" @click=\"managePublicAreas\">\r\n             <view class=\"module-icon public-area\">\r\n               <uni-icons type=\"list\" size=\"32\" color=\"#34C759\" />\r\n             </view>\r\n             <view class=\"module-content\">\r\n               <view class=\"module-title\">公共责任区</view>\r\n               <view class=\"module-desc\">管理需要轮班清洁的公共区域</view>\r\n               <view class=\"module-count\">{{ overview.publicAreas || 0 }} 个</view>\r\n             </view>\r\n             <view class=\"module-arrow\">\r\n               <uni-icons type=\"right\" size=\"16\" color=\"#C7C7CC\" />\r\n             </view>\r\n           </view>\r\n         </view>\r\n       </view>\r\n\r\n             <!-- 基础配置 -->\r\n       <view class=\"module-section\">\r\n         <view class=\"section-title\">\r\n           <uni-icons type=\"gear\" size=\"20\" color=\"#FF9500\" />\r\n           <text>基础配置</text>\r\n         </view>\r\n         <view class=\"module-grid\">\r\n           <view class=\"module-card\" @click=\"manageLocations\">\r\n             <view class=\"module-icon location\">\r\n               <uni-icons type=\"location\" size=\"32\" color=\"#FF9500\" />\r\n             </view>\r\n             <view class=\"module-content\">\r\n               <view class=\"module-title\">月度位置管理</view>\r\n               <view class=\"module-desc\">配置厂区所有区域位置信息</view>\r\n               <view class=\"module-count\">{{ overview.locations || 0 }} 个</view>\r\n             </view>\r\n             <view class=\"module-arrow\">\r\n               <uni-icons type=\"right\" size=\"16\" color=\"#C7C7CC\" />\r\n             </view>\r\n           </view>\r\n\r\n           <view class=\"module-card\" @click=\"manageAssignment\">\r\n             <view class=\"module-icon assignment\">\r\n               <uni-icons type=\"person\" size=\"32\" color=\"#8B5CF6\" />\r\n             </view>\r\n             <view class=\"module-content\">\r\n               <view class=\"module-title\">员工责任区分配</view>\r\n               <view class=\"module-desc\">为员工分配固定责任区</view>\r\n               <view class=\"module-count\">{{ overview.assignments || 0 }} 个分配</view>\r\n             </view>\r\n             <view class=\"module-arrow\">\r\n               <uni-icons type=\"right\" size=\"16\" color=\"#C7C7CC\" />\r\n             </view>\r\n           </view>\r\n\r\n           <view class=\"module-card\" @click=\"managePublicSchedule\">\r\n             <view class=\"module-icon public-schedule\">\r\n               <uni-icons type=\"compose\" size=\"32\" color=\"#FF3B30\" />\r\n             </view>\r\n             <view class=\"module-content\">\r\n               <view class=\"module-title\">公共区清扫日设置</view>\r\n               <view class=\"module-desc\">设置公共责任区固定清扫日</view>\r\n               <view class=\"module-count\">{{ overview.publicAreas || 0 }} 个区域</view>\r\n             </view>\r\n             <view class=\"module-arrow\">\r\n               <uni-icons type=\"right\" size=\"16\" color=\"#C7C7CC\" />\r\n             </view>\r\n           </view>\r\n         </view>\r\n       </view>\r\n\r\n      <!-- 数据导出 -->\r\n      <!-- TODO: 数据导出功能暂时注释，后期需要时恢复 -->\r\n      <!--\r\n      <view class=\"module-section\">\r\n        <view class=\"section-title\">\r\n          <uni-icons type=\"download\" size=\"20\" color=\"#34C759\" />\r\n          <text>数据导出</text>\r\n        </view>\r\n        <view class=\"module-grid\">\r\n             <view class=\"module-card\" @click=\"navigateToExportPage\">\r\n             <view class=\"module-icon export-data\">\r\n               <uni-icons type=\"upload\" size=\"32\" color=\"#007AFF\" />\r\n             </view>\r\n             <view class=\"module-content\">\r\n               <view class=\"module-title\">数据导出</view>\r\n               <view class=\"module-desc\">导出各类检查数据和统计报告</view>\r\n               <view class=\"module-count\">Excel格式</view>\r\n             </view>\r\n             <view class=\"module-arrow\">\r\n               <uni-icons type=\"right\" size=\"16\" color=\"#C7C7CC\" />\r\n             </view>\r\n           </view>\r\n        </view>\r\n      </view>\r\n      -->\r\n    </view>\r\n\r\n    <!-- 自定义权限不足弹窗 -->\r\n    <view v-if=\"showPermissionModal\" class=\"permission-modal-overlay\">\r\n      <view class=\"permission-modal\">\r\n        <view class=\"modal-icon\">\r\n          <uni-icons type=\"locked\" size=\"48\" color=\"#FF3B30\"></uni-icons>\r\n        </view>\r\n        <view class=\"modal-title\">权限不足</view>\r\n        <view class=\"modal-content\">\r\n          抱歉，您没有访问基础数据管理的权限。\r\n          <br/>该功能仅限管理人员使用。\r\n        </view>\r\n        <view class=\"modal-actions\">\r\n          <button class=\"modal-btn primary\" @click=\"closePermissionModal\">\r\n            返回首页\r\n          </button>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { callCloudFunction } from '@/utils/auth.js';\r\n\r\nexport default {\r\n  name: 'DataManage',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      loadError: false,\r\n      dataLoaded: false,\r\n      // 权限弹窗控制\r\n      showPermissionModal: false,\r\n      overview: {\r\n        fixedAreas: 0,\r\n        publicAreas: 0,\r\n        locations: 0,\r\n        employees: 0,\r\n        assignments: 0\r\n      },\r\n      // 数据缓存\r\n      processCache: {\r\n        overviewData: null,\r\n        cacheTimestamp: null,\r\n        cacheExpiry: 5 * 60 * 1000 // 5分钟缓存\r\n      }\r\n    }\r\n  },\r\n  \r\n  onLoad() {\r\n    // 首先检查用户权限\r\n    this.checkUserPermission();\r\n    this.loadOverviewDataOptimized();\r\n  },\r\n  \r\n  onShow() {\r\n    // 页面显示时检查缓存是否过期，如果过期则静默刷新\r\n    if (this.dataLoaded && this.isCacheExpired()) {\r\n      this.silentRefreshData();\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    // 检查用户权限\r\n    checkUserPermission() {\r\n      try {\r\n        const { getCacheKey, CACHE_KEYS } = require('@/utils/cache.js');\r\n        \r\n        // 获取当前用户信息\r\n        const userInfo = uni.getStorageSync('uni-id-pages-userInfo') || {};\r\n        \r\n        // 获取用户角色信息（从专门的角色缓存中获取）- 与其他页面保持一致\r\n        let userRoleStr = uni.getStorageSync(getCacheKey(CACHE_KEYS.USER_ROLE)) || '{}';\r\n        let userRole = {};\r\n        \r\n        try {\r\n          if (typeof userRoleStr === 'string') {\r\n            userRole = JSON.parse(userRoleStr);\r\n          } else {\r\n            userRole = userRoleStr;\r\n          }\r\n        } catch (e) {\r\n          userRole = {};\r\n        }\r\n        \r\n        // 从userRole.value.userRole获取角色数组\r\n        let roles = [];\r\n        if (userRole && userRole.value && userRole.value.userRole) {\r\n          roles = Array.isArray(userRole.value.userRole) ? userRole.value.userRole : [userRole.value.userRole];\r\n        } else if (userInfo.role) {\r\n          // 备用方案：从用户信息中获取角色\r\n          roles = Array.isArray(userInfo.role) ? userInfo.role : [userInfo.role];\r\n        } else if (userInfo.username === 'admin') {\r\n          // 特殊处理：如果用户名是admin，给予admin角色\r\n          roles = ['admin'];\r\n        }\r\n        \r\n        // 检查是否有基础数据管理权限\r\n        const adminRoles = ['admin', 'GM', 'PM', 'Integrated', 'reviser'];\r\n        const hasPermission = roles.some(role => adminRoles.includes(role));\r\n        \r\n\r\n        \r\n        if (!hasPermission) {\r\n          // 显示自定义权限弹窗\r\n          this.showPermissionModal = true;\r\n          return false;\r\n        }\r\n        \r\n        return true;\r\n      } catch (error) {\r\n        console.error('权限检查失败:', error);\r\n        uni.showToast({\r\n          title: '权限验证失败',\r\n          icon: 'none',\r\n          duration: 2000\r\n        });\r\n        // 权限验证失败时也返回首页\r\n        setTimeout(() => {\r\n          uni.switchTab({\r\n            url: '/pages/6s/index'\r\n          });\r\n        }, 2000);\r\n        return false;\r\n      }\r\n    },\r\n\r\n    // 关闭权限弹窗并返回首页\r\n    closePermissionModal() {\r\n      this.showPermissionModal = false;\r\n      uni.switchTab({\r\n        url: '/pages/6s/index'\r\n      });\r\n    },\r\n\r\n    // 优化后的概览数据加载\r\n    async loadOverviewDataOptimized() {\r\n      try {\r\n        this.loading = true;\r\n        this.loadError = false;\r\n        \r\n        // 检查缓存是否有效\r\n        if (this.isCacheValid()) {\r\n          this.overview = this.processCache.overviewData;\r\n          this.loading = false;\r\n          this.dataLoaded = true;\r\n          return;\r\n        }\r\n        \r\n        // 并行加载所有统计数据\r\n        const results = await Promise.allSettled([\r\n          this.loadAreasCountOptimized('fixed'),\r\n          this.loadAreasCountOptimized('public'),\r\n          this.loadLocationsCountOptimized(),\r\n          this.loadEmployeeAssignmentCountOptimized()\r\n        ]);\r\n        \r\n        // 处理结果\r\n        const overviewData = {\r\n          fixedAreas: results[0].status === 'fulfilled' ? results[0].value : 0,\r\n          publicAreas: results[1].status === 'fulfilled' ? results[1].value : 0,\r\n          locations: results[2].status === 'fulfilled' ? results[2].value : 0,\r\n          employees: results[3].status === 'fulfilled' ? results[3].value.employees : 0,\r\n          assignments: results[3].status === 'fulfilled' ? results[3].value.assignments : 0\r\n        };\r\n        \r\n        // 更新数据和缓存\r\n        this.overview = overviewData;\r\n        this.updateCache(overviewData);\r\n        \r\n      } catch (error) {\r\n        this.handleLoadError(error);\r\n      } finally {\r\n        this.loading = false;\r\n        this.dataLoaded = true;\r\n      }\r\n    },\r\n    \r\n    // 静默刷新数据\r\n    async silentRefreshData() {\r\n      if (this.loading) return;\r\n      \r\n      try {\r\n        // 清除缓存\r\n        this.clearCache();\r\n        \r\n        // 重新加载数据（不显示loading状态）\r\n        const results = await Promise.allSettled([\r\n          this.loadAreasCountOptimized('fixed'),\r\n          this.loadAreasCountOptimized('public'),\r\n          this.loadLocationsCountOptimized(),\r\n          this.loadEmployeeAssignmentCountOptimized()\r\n        ]);\r\n        \r\n        const overviewData = {\r\n          fixedAreas: results[0].status === 'fulfilled' ? results[0].value : this.overview.fixedAreas,\r\n          publicAreas: results[1].status === 'fulfilled' ? results[1].value : this.overview.publicAreas,\r\n          locations: results[2].status === 'fulfilled' ? results[2].value : this.overview.locations,\r\n          employees: results[3].status === 'fulfilled' ? results[3].value.employees : this.overview.employees,\r\n          assignments: results[3].status === 'fulfilled' ? results[3].value.assignments : this.overview.assignments\r\n        };\r\n        \r\n        this.overview = overviewData;\r\n        this.updateCache(overviewData);\r\n        \r\n      } catch (error) {\r\n        // 静默处理错误，不影响用户体验\r\n      }\r\n    },\r\n    \r\n    // 优化后的责任区数量加载（合并固定和公共）\r\n    async loadAreasCountOptimized(type) {\r\n      try {\r\n        const result = await callCloudFunction('hygiene-area-management', {\r\n          action: 'getAreaList',\r\n          data: { type, pageSize: 1000 }\r\n        });\r\n        \r\n        if (result.success && result.data) {\r\n          return this.extractDataCount(result.data);\r\n        }\r\n        return 0;\r\n      } catch (error) {\r\n        return 0;\r\n      }\r\n    },\r\n    \r\n    // 提取数据计数的通用方法\r\n    extractDataCount(data) {\r\n      if (Array.isArray(data)) {\r\n        return data.length;\r\n      } else if (data.data && Array.isArray(data.data)) {\r\n        return data.data.length;\r\n      } else if (typeof data === 'object' && data.total !== undefined) {\r\n        return data.total;\r\n      }\r\n      return 0;\r\n    },\r\n    \r\n    // 优化后的位置数量加载\r\n    async loadLocationsCountOptimized() {\r\n      try {\r\n        const result = await callCloudFunction('hygiene-location-management', {\r\n          action: 'getLocationConfig'\r\n        });\r\n        if (result.success && result.data && result.data.locations) {\r\n          return result.data.locations.reduce((sum, category) => {\r\n            return sum + (category.items ? category.items.length : 0);\r\n          }, 0);\r\n        }\r\n        return 0;\r\n      } catch (error) {\r\n        return 0;\r\n      }\r\n    },\r\n    \r\n    // 优化后的员工和分配数量加载\r\n    async loadEmployeeAssignmentCountOptimized() {\r\n      try {\r\n        const result = await callCloudFunction('hygiene-assignments', {\r\n          action: 'getAssignmentList',\r\n          data: { pageSize: 1000 }\r\n        });\r\n        \r\n        if (result.success && Array.isArray(result.data)) {\r\n          return this.processAssignmentData(result.data);\r\n        }\r\n        return { employees: 0, assignments: 0 };\r\n      } catch (error) {\r\n        return { employees: 0, assignments: 0 };\r\n      }\r\n    },\r\n    \r\n    // 处理分配数据的通用方法\r\n    processAssignmentData(assignments) {\r\n      const employeeIds = assignments\r\n        .map(assignment => assignment.employee_id || assignment.employee?.id || assignment.employee?._id)\r\n        .filter(Boolean);\r\n      \r\n      return {\r\n        employees: new Set(employeeIds).size,\r\n        assignments: assignments.length\r\n      };\r\n    },\r\n    \r\n    // 固定责任区管理\r\n    manageFixedAreas() {\r\n      uni.navigateTo({\r\n        url: '/pages/6s_pkg/fixed-area-manage'\r\n      });\r\n    },\r\n    \r\n    // 公共责任区管理  \r\n    managePublicAreas() {\r\n      uni.navigateTo({\r\n        url: '/pages/6s_pkg/public-area-manage'\r\n      });\r\n    },\r\n    \r\n    // 位置管理\r\n    manageLocations() {\r\n      uni.navigateTo({\r\n        url: '/pages/6s_pkg/location-manage'\r\n      });\r\n    },\r\n    \r\n    // 员工责任区分配\r\n    manageAssignment() {\r\n      uni.navigateTo({\r\n        url: '/pages/6s_pkg/employee-assignment'\r\n      });\r\n    },\r\n    \r\n    // 公共区清扫日设置\r\n    managePublicSchedule() {\r\n      uni.navigateTo({\r\n        url: '/pages/6s_pkg/public-schedule'\r\n      });\r\n    },\r\n     \r\n\r\n\r\n     // 跳转到数据导出页面 - 暂时注释\r\n     /*\r\n     navigateToExportPage() {\r\n       uni.navigateTo({\r\n         url: '/pages/6s_pkg/data-export'\r\n       });\r\n     },\r\n     */\r\n    \r\n    // 缓存管理方法\r\n    isCacheValid() {\r\n      if (!this.processCache.overviewData || !this.processCache.cacheTimestamp) {\r\n        return false;\r\n      }\r\n      return Date.now() - this.processCache.cacheTimestamp < this.processCache.cacheExpiry;\r\n    },\r\n    \r\n    isCacheExpired() {\r\n      if (!this.processCache.cacheTimestamp) {\r\n        return true;\r\n      }\r\n      return Date.now() - this.processCache.cacheTimestamp >= this.processCache.cacheExpiry;\r\n    },\r\n    \r\n    updateCache(data) {\r\n      this.processCache.overviewData = { ...data };\r\n      this.processCache.cacheTimestamp = Date.now();\r\n    },\r\n    \r\n    clearCache() {\r\n      this.processCache.overviewData = null;\r\n      this.processCache.cacheTimestamp = null;\r\n    },\r\n    \r\n    // 错误处理\r\n    handleLoadError(error) {\r\n      this.loadError = true;\r\n      uni.showToast({\r\n        title: '加载数据失败',\r\n        icon: 'none',\r\n        duration: 2000\r\n      });\r\n    },\r\n    \r\n    // 重试加载\r\n    retryLoad() {\r\n      this.clearCache();\r\n      this.loadOverviewDataOptimized();\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-container {\r\n  min-height: 100vh;\r\n  background: #F8F9FA;\r\n  padding-bottom: 40rpx;\r\n}\r\n\r\n.header {\r\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\r\n  padding: 60rpx 32rpx 40rpx;\r\n  color: white;\r\n}\r\n\r\n.header-title {\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.header-subtitle {\r\n  font-size: 24rpx;\r\n  opacity: 0.8;\r\n}\r\n\r\n.overview-card {\r\n  background: white;\r\n  margin: 24rpx 32rpx;\r\n  padding: 32rpx;\r\n  border-radius: 16rpx;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.overview-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #1C1C1E;\r\n  margin-bottom: 24rpx;\r\n}\r\n\r\n.stats-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 24rpx;\r\n}\r\n\r\n.stat-item {\r\n  text-align: center;\r\n  padding: 24rpx;\r\n  background: #F8F9FA;\r\n  border-radius: 12rpx;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 48rpx;\r\n  font-weight: 600;\r\n  color: #007AFF;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 24rpx;\r\n  color: #8E8E93;\r\n}\r\n\r\n.modules-container {\r\n  padding: 0 32rpx;\r\n}\r\n\r\n.module-section {\r\n  margin-bottom: 32rpx;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12rpx;\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #1C1C1E;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.module-grid {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16rpx;\r\n}\r\n\r\n.module-card {\r\n  background: white;\r\n  border-radius: 16rpx;\r\n  padding: 24rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16rpx;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.module-card:active {\r\n  transform: scale(0.98);\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.module-icon {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 16rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.module-icon.fixed-area {\r\n  background: rgba(0, 122, 255, 0.1);\r\n}\r\n\r\n.module-icon.public-area {\r\n  background: rgba(52, 199, 89, 0.1);\r\n}\r\n\r\n.module-icon.location {\r\n  background: rgba(255, 149, 0, 0.1);\r\n}\r\n\r\n.module-icon.assignment {\r\n  background: rgba(139, 92, 246, 0.1);\r\n}\r\n\r\n.module-icon.public-schedule {\r\n  background: rgba(255, 59, 48, 0.1);\r\n}\r\n\r\n.module-icon.export-data {\r\n  background: rgba(0, 122, 255, 0.1);\r\n}\r\n\r\n.module-content {\r\n  flex: 1;\r\n}\r\n\r\n.module-title {\r\n  font-size: 30rpx;\r\n  font-weight: 600;\r\n  color: #1C1C1E;\r\n  margin-bottom: 4rpx;\r\n}\r\n\r\n.module-desc {\r\n  font-size: 24rpx;\r\n  color: #8E8E93;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.module-count {\r\n  font-size: 24rpx;\r\n  color: #007AFF;\r\n  font-weight: 500;\r\n}\r\n\r\n.module-arrow {\r\n  padding: 8rpx;\r\n}\r\n\r\n/* 加载状态样式 */\r\n.stats-loading {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 60rpx 0;\r\n  min-height: 200rpx;\r\n}\r\n\r\n.loading-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16rpx;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  border: 4rpx solid #E5E7EB;\r\n  border-top: 4rpx solid #007AFF;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.loading-text {\r\n  font-size: 28rpx;\r\n  color: #8E8E93;\r\n}\r\n\r\n/* 错误状态样式 */\r\n.error-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 60rpx 0;\r\n  min-height: 200rpx;\r\n}\r\n\r\n.error-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16rpx;\r\n}\r\n\r\n.error-text {\r\n  font-size: 28rpx;\r\n  color: #FF3B30;\r\n  text-align: center;\r\n}\r\n\r\n.retry-btn {\r\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\r\n  color: white;\r\n  border: none;\r\n  border-radius: 20rpx;\r\n  padding: 16rpx 32rpx;\r\n  font-size: 26rpx;\r\n  font-weight: 500;\r\n  margin-top: 16rpx;\r\n}\r\n\r\n.retry-btn:active {\r\n  transform: scale(0.95);\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 权限弹窗样式 */\r\n.permission-modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.4);\r\n  backdrop-filter: blur(20rpx);\r\n  -webkit-backdrop-filter: blur(20rpx);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 9999;\r\n  animation: overlayFadeIn 0.3s ease-out;\r\n}\r\n\r\n@keyframes overlayFadeIn {\r\n  from {\r\n    opacity: 0;\r\n    backdrop-filter: blur(0rpx);\r\n    -webkit-backdrop-filter: blur(0rpx);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    backdrop-filter: blur(20rpx);\r\n    -webkit-backdrop-filter: blur(20rpx);\r\n  }\r\n}\r\n\r\n.permission-modal {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(40rpx);\r\n  -webkit-backdrop-filter: blur(40rpx);\r\n  border-radius: 32rpx;\r\n  padding: 60rpx 48rpx;\r\n  margin: 0 48rpx;\r\n  max-width: 560rpx;\r\n  width: 100%;\r\n  text-align: center;\r\n  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3),\r\n              0 8rpx 20rpx rgba(0, 0, 0, 0.15);\r\n  border: 1rpx solid rgba(255, 255, 255, 0.3);\r\n  animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 添加微妙的光效 */\r\n.permission-modal::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2rpx;\r\n  background: linear-gradient(90deg, \r\n    transparent 0%, \r\n    rgba(255, 255, 255, 0.8) 20%, \r\n    rgba(255, 255, 255, 0.8) 80%, \r\n    transparent 100%);\r\n  opacity: 0.6;\r\n}\r\n\r\n@keyframes modalSlideIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(40rpx) scale(0.95);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0) scale(1);\r\n  }\r\n}\r\n\r\n.modal-icon {\r\n  margin-bottom: 32rpx;\r\n  animation: iconBounce 0.6s ease-out 0.2s both;\r\n}\r\n\r\n@keyframes iconBounce {\r\n  0% {\r\n    opacity: 0;\r\n    transform: scale(0.3);\r\n  }\r\n  50% {\r\n    transform: scale(1.1);\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n.modal-title {\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  color: #1C1C1E;\r\n  margin-bottom: 24rpx;\r\n  letter-spacing: 0.5rpx;\r\n}\r\n\r\n.modal-content {\r\n  font-size: 28rpx;\r\n  color: #6C7B8A;\r\n  line-height: 1.6;\r\n  margin-bottom: 48rpx;\r\n  padding: 0 16rpx;\r\n}\r\n\r\n.modal-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.modal-btn {\r\n  background: linear-gradient(135deg, #FF3B30 0%, #FF453A 100%);\r\n  color: white;\r\n  border: none;\r\n  border-radius: 24rpx;\r\n  padding: 28rpx 48rpx;\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  letter-spacing: 1rpx;\r\n  box-shadow: 0 8rpx 24rpx rgba(255, 59, 48, 0.3),\r\n              0 4rpx 8rpx rgba(255, 59, 48, 0.2);\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.modal-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, \r\n    transparent, \r\n    rgba(255, 255, 255, 0.2), \r\n    transparent);\r\n  transition: left 0.6s;\r\n}\r\n\r\n.modal-btn:active {\r\n  transform: translateY(2rpx);\r\n  box-shadow: 0 4rpx 12rpx rgba(255, 59, 48, 0.3),\r\n              0 2rpx 4rpx rgba(255, 59, 48, 0.2);\r\n}\r\n\r\n.modal-btn:active::before {\r\n  left: 100%;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 480px) {\r\n  .permission-modal {\r\n    margin: 0 32rpx;\r\n    padding: 48rpx 32rpx;\r\n  }\r\n  \r\n  .modal-title {\r\n    font-size: 32rpx;\r\n  }\r\n  \r\n  .modal-content {\r\n    font-size: 26rpx;\r\n  }\r\n  \r\n  .modal-btn {\r\n    padding: 24rpx 40rpx;\r\n    font-size: 30rpx;\r\n  }\r\n}\r\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./data-manage.vue?vue&type=style&index=0&id=98623ac8&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./data-manage.vue?vue&type=style&index=0&id=98623ac8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775842363\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}