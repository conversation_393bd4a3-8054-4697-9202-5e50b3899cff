{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s_pkg/inspection-detail.vue?c75b", "webpack:///D:/Xwzc/pages/6s_pkg/inspection-detail.vue?35a9", "webpack:///D:/Xwzc/pages/6s_pkg/inspection-detail.vue?597f", "webpack:///D:/Xwzc/pages/6s_pkg/inspection-detail.vue?0f8d", "uni-app:///pages/6s_pkg/inspection-detail.vue", "webpack:///D:/Xwzc/pages/6s_pkg/inspection-detail.vue?d061", "webpack:///D:/Xwzc/pages/6s_pkg/inspection-detail.vue?4b4c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "components", "data", "areaId", "isRectificationCheck", "loading", "areaInfo", "id", "assignedEmployee", "location", "cleaningFrequency", "lastCleaningTime", "hasRectificationHistory", "currentTime", "sliderTouching", "inspectionForm", "rating", "photos", "hasIssues", "issueType", "issueDescription", "remarks", "isSubmitting", "autoUpload", "uploading", "uploadProgress", "computed", "issueDescriptionLength", "<PERSON><PERSON><PERSON><PERSON>", "canSubmit", "getLastCleaningClass", "shouldShowLastCleaning", "onLoad", "methods", "updateCurrentTime", "loadAreaInfo", "require", "callCloudFunction", "action", "result", "area", "uni", "title", "icon", "getAssignedEmployee", "getLocationDisplay", "getCleaningFrequency", "getLastCleaningTime", "area_id", "pageSize", "lastRecord", "possibleNames", "formatDateTime", "date", "onStarRateChange", "setRatingByMark", "onSliderTouchStart", "onSliderTouchMove", "onSliderTouchEnd", "onSliderMouseDown", "onSliderMouseMove", "onSliderMouseUp", "updateRatingFromTouch", "updateRatingFromMouse", "getRatingDescription", "chooseImage", "count", "sizeType", "sourceType", "success", "newPhotos", "url", "cloudUrl", "fileID", "uploaded", "uploadImages", "uploadUtils", "pathGenerator", "maxConcurrent", "uploadResult", "successCount", "failCount", "autoUploadNewPhotos", "i", "photo", "photoIndex", "content", "showCancel", "confirmText", "uploadSinglePhoto", "timestamp", "random", "cloudPath", "onProgress", "fileInfo", "size", "error", "toggleAutoUpload", "deletePhoto", "res", "uniCloud", "fileList", "extractFileId", "getPhotoDisplayUrl", "previewPhoto", "urls", "current", "toggleIssues", "handleIssueDescriptionInput", "value", "duration", "handleRemarksInput", "autoDetectIssueType", "type", "words", "submitInspection", "doSubmit", "unuploadedPhotos", "tempFilePaths", "stillUnuploaded", "filter", "map", "description", "inspectionData", "inspection_date", "finalDescription", "console", "severity", "isPassed", "isFromInspection", "setTimeout", "errorMessage"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,yBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0I;AAC1I;AACqE;AACL;AACsC;;;AAGtG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,wGAAM;AACR,EAAE,iHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9DA;AAAA;AAAA;AAAA;AAA2mB,CAAgB,qoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACgT/nB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;EACAC;IACA;EAAA,CACA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MACAC;QACAC;QACAP;QACAQ;QACAC;QACAC;QACAC;QACAC;MACA;;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MAEA;MAEAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEAC;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA,iEACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;EACA;EACAC;IAEA;MACA;MACA;MACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA,WAGAC;gBAAA;gBAAA,OAEAC;kBACAC;kBACApC;oBAAAK;kBAAA;gBACA;cAAA;gBAHAgC;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACAC,oBAEA;gBACA;kBACAjC;kBACAP;kBACAQ;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIA6B;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;QACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,YAEAX;gBAAA;gBAAA,OAEAC;kBACAC;kBACApC;oBACA8C;oBACAC;kBACA;gBACA;cAAA;gBANAV;gBAAA,MAQAA;kBAAA;kBAAA;gBAAA;gBACAW,kCAEA;gBACA;kBACAC,iBACAD,sBACAA,yBACAA,4BACAA,yBACA;oBAAA;kBAAA;kBAEA;oBACA;kBACA;gBACA;gBAAA,kCAEA;cAAA;gBAAA,kCAEA;cAAA;gBAAA;gBAAA;gBAAA,kCAGA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAE;MACA;MAEA;QACA;QACA;UACA;YACAC;UACA;YACAA;UACA;QACA;UACAA;QACA;QAEA;UACA;QACA;QAEA;MACA;QAEA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEAC;MACA;QACA;MACA;IACA;IAEAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACArB;QACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAsB;MAAA;MACA;MACAtB;QACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAuB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACAxB;UACAC;UACAC;QACA;QACA;MACA;MAEAF;QACAyB;QACAC;QACAC;QACAC;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBACA;oBACAC;sBAAA;wBACAC;wBAAA;wBACAC;wBAAA;wBACAC;wBAAA;wBACAzE;wBACA0E;wBACAlD;sBACA;oBAAA;oBAEA;;oBAEA;oBACA;sBACA;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CACA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACAmD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAC;kBACAC;oBACA;oBACA;oBACA;kBACA;kBACAC;gBACA;cAAA;gBAPAC;gBAAA,KASAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAA;kBACA;oBACA;oBACA;sBAAA;oBAAA;oBAEA;sBACA;sBACA;sBACA;sBACA;sBACA;oBACA;kBACA;gBACA;gBAEAC;kBAAA;gBAAA;gBACAC;kBAAA;gBAAA;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAKA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,8DACAC;kBAAA;kBAAA;oBAAA;sBAAA;wBAAA;0BACAC;0BACAC;4BAAA;0BAAA;0BAAA,MAEAA;4BAAA;4BAAA;0BAAA;0BAAA;wBAAA;0BAAA;0BAGA;0BACA;;0BAEA;0BAAA;0BAAA,OACA;wBAAA;0BAAAN;0BAAA,KAEAA;4BAAA;4BAAA;0BAAA;0BACA;0BACA;0BACA;0BACA;0BACA;0BACA;0BAAA;0BAAA;wBAAA;0BAAA,MAEA;wBAAA;0BAAA;0BAAA;wBAAA;0BAAA;0BAAA;0BAGA;;0BAEA;0BACAtC;4BACAC;4BACA4C;4BACAC;4BACAC;0BACA;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA;gBAhCAL;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAmCA;IAEA;IACAM;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBACAC;gBACAC,uGAEA;gBAAA;gBAAA,OACAhB;kBACAiB;oBACA;kBAAA;gBAEA;cAAA;gBAJAd;gBAAA,MAMAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEAH;cAAA;gBAAAkB;gBAAA,kCAEA;kBACAzB;kBACAuB;kBACArB;kBACAwB;gBACA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,kCAGA;kBAAA1B;kBAAA2B;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;MACAxD;QACAC;QACAC;MACA;IACA;IAEA;IACAuD;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAzD;kBACAC;kBACA4C;kBACAjB;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACA8B;gCAAA;gCAAA;8BAAA;8BACAf,6CAEA;8BAAA,MACAA;gCAAA;gCAAA;8BAAA;8BAAA;8BAEA3C;gCAAAC;8BAAA;8BAAA;8BAAA,OAEA0D;gCACApG;gCACAE;kCACAmG;gCACA;8BACA;4BAAA;8BALA9D;8BAOA;gCACA;8BAAA,CACA;gCACA;8BAAA;8BACA;8BAAA;4BAAA;8BAAA;8BAAA;4BAAA;8BAAA;8BAIAE;8BAAA;4BAAA;8BAIA;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAEA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA6D;MACA;QACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;MACA;QACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QAAA;MAAA;MACA/D;QACAgE;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;QACAC;QACA;QACApE;UACAC;UACAC;UACAmE;QACA;MACA;;MAEA;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;QACAF;QACA;QACApE;UACAC;UACAC;UACAmE;QACA;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IAEA;IACAE;MACA;MAEA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA;QAAA;UAAAC;UAAAC;QACA;UAAA;QAAA;UACA;UACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACA1E;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIAF;kBACAC;kBACA4C;kBACAjB;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACA8B;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA,OACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAEA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAiB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA,YAGAhF,iGAEA;gBACA;kBACA;gBACA;;gBAEA;gBACAiF;kBAAA;gBAAA;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA5E;kBACAC;gBACA;;gBAEA;gBACA4E;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACAC;kBAAA;gBAAA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAIA;gBACAtG,uCACAuG;kBAAA;gBAAA,GACAC;kBAAA;oBACAlD;oBACA0C;oBACAS;kBACA;gBAAA;gBAEAC;kBACA3E;kBACA4E;kBACArF;kBAAA;kBACAtB;gBACA,GAEA;gBACA4G;gBACA;kBACAA;kBACA;oBACAA;oBACAC;kBACA;gBACA;;gBAEA;gBACA;kBACA;kBACAH;kBACAA;gBACA;kBACA;kBACAA;kBACAA;kBACAA;oBACAV;oBACAS;oBACAK;kBACA;gBACA;;gBAEA;gBACAJ;gBACAA;gBACAA;gBACAA;gBACAA;;gBAEA;gBAAA;gBAAA,OACAtF;kBACAC;kBACApC;gBACA;cAAA;gBAHAqC;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACA;gBAEAE;kBACAC;kBACAC;gBACA;;gBAEA;gBACAF;kBACAtC;kBACA6H;kBACA5H;gBACA;;gBAEA;gBACA;kBACAqC;oBACAtC;oBACAmC;oBACA2F;kBACA;gBACA;;gBAEA;gBACAC;kBACAzF;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIA;gBAGA0F;gBACA;kBACA;oBACAA;kBACA;oBACAA;kBACA;oBACAA;kBACA;gBACA;gBAEA1F;kBACAC;kBACA4C;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9mCA;AAAA;AAAA;AAAA;AAA8qC,CAAgB,opCAAG,EAAC,C;;;;;;;;;;;ACAlsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/inspection-detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/inspection-detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./inspection-detail.vue?vue&type=template&id=4a16827e&scoped=true&\"\nvar renderjs\nimport script from \"./inspection-detail.vue?vue&type=script&lang=js&\"\nexport * from \"./inspection-detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./inspection-detail.vue?vue&type=style&index=0&id=4a16827e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4a16827e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/inspection-detail.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./inspection-detail.vue?vue&type=template&id=4a16827e&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniRate: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-rate/components/uni-rate/uni-rate\" */ \"@/uni_modules/uni-rate/components/uni-rate/uni-rate.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = !_vm.inspectionForm.hasIssues\n    ? _vm.getRatingDescription(_vm.inspectionForm.rating)\n    : null\n  var l0 = _vm.__map(_vm.inspectionForm.photos, function (photo, index) {\n    var $orig = _vm.__get_orig(photo)\n    var m1 = _vm.getPhotoDisplayUrl(photo)\n    return {\n      $orig: $orig,\n      m1: m1,\n    }\n  })\n  var g0 = _vm.inspectionForm.photos.length\n  var g1 = g0 < 6 ? _vm.inspectionForm.photos.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        l0: l0,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./inspection-detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./inspection-detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page-container\">\n    <!-- 页面头部 -->\n    <view class=\"page-header\">\n      <view class=\"header-content\">\n        <view class=\"header-title\">责任区检查</view>\n        <view class=\"header-subtitle\">\n          <text v-if=\"loading\" style=\"color: white !important; animation: pulse 1.5s ease-in-out infinite;\">加载中...</text>\n          <text v-else>{{ areaInfo.name || '未知责任区' }}</text>\n        </view>\n      </view>\n      <view class=\"check-time\">\n        <text>{{ currentTime }}</text>\n      </view>\n    </view>\n\n    <!-- 责任区信息 -->\n    <view class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"card-title\">责任区信息</view>\n      </view>\n      <view class=\"card-body\">\n        <!-- 加载状态 -->\n        <view v-if=\"loading\" class=\"loading-container\">\n          <view class=\"loading-content\">\n            <view class=\"loading-spinner\"></view>\n            <text class=\"loading-text\">加载责任区信息中...</text>\n          </view>\n        </view>\n        \n        <!-- 正常内容 -->\n        <view v-else class=\"info-grid-enhanced\">\n          <view class=\"info-item-enhanced\">\n            <view class=\"info-icon\">\n              <uni-icons type=\"person\" size=\"18\" color=\"#007AFF\"></uni-icons>\n            </view>\n            <view class=\"info-content\">\n              <view class=\"info-label-enhanced\">负责人</view>\n              <view class=\"info-value-enhanced\">{{ areaInfo.assignedEmployee || '加载中...' }}</view>\n            </view>\n          </view>\n          <view class=\"info-item-enhanced\">\n            <view class=\"info-icon\">\n              <uni-icons type=\"location\" size=\"18\" color=\"#FF9500\"></uni-icons>\n            </view>\n            <view class=\"info-content\">\n              <view class=\"info-label-enhanced\">区域位置</view>\n              <view class=\"info-value-enhanced\">{{ areaInfo.location || '加载中...' }}</view>\n            </view>\n          </view>\n          <view class=\"info-item-enhanced\">\n            <view class=\"info-icon\">\n              <uni-icons type=\"calendar\" size=\"18\" color=\"#5856D6\"></uni-icons>\n            </view>\n            <view class=\"info-content\">\n              <view class=\"info-label-enhanced\">清理频率</view>\n              <view class=\"info-value-enhanced\">{{ areaInfo.cleaningFrequency || '加载中...' }}</view>\n            </view>\n          </view>\n          <view v-if=\"shouldShowLastCleaning\" class=\"info-item-enhanced\">\n            <view class=\"info-icon\">\n              <uni-icons type=\"checkmarkempty\" size=\"18\" color=\"#34C759\"></uni-icons>\n            </view>\n            <view class=\"info-content\">\n              <view class=\"info-label-enhanced\">上次清理</view>\n              <view class=\"info-value-enhanced\" :class=\"getLastCleaningClass\">{{ areaInfo.lastCleaningTime }}</view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 本次检查评分 - 只在检查通过时显示 -->\n    <view v-if=\"!inspectionForm.hasIssues\" class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"card-title\">检查评分</view>\n        <view class=\"card-subtitle\">检查通过后，对责任区状况进行客观评价</view>\n      </view>\n      <view class=\"card-body\">\n        <view class=\"rating-section\">\n          <view class=\"rating-display-large\">\n            <view class=\"rating-number-container\">\n              <text class=\"rating-number\">{{ inspectionForm.rating }}</text>\n              <text class=\"rating-unit\">/5</text>\n            </view>\n            <view class=\"rating-desc\">{{ getRatingDescription(inspectionForm.rating) }}</view>\n          </view>\n          \n          <!-- 星星评分（使用官方uni-rate组件） -->\n          <view class=\"star-rating\">\n            <uni-rate \n              :value=\"inspectionForm.rating\" \n              @change=\"onStarRateChange\"\n              allow-half\n              :size=\"24\"\n              active-color=\"#FFD700\"\n              inactive-color=\"#E5E5EA\"\n              :touchable=\"true\"\n              :margin=\"8\"\n            />\n          </view>\n          \n          <!-- 自定义滑动条评分 -->\n          <view class=\"custom-slider-rating\">\n            <view class=\"custom-slider-container\" \n                  @touchstart=\"onSliderTouchStart\" \n                  @touchmove=\"onSliderTouchMove\" \n                  @touchend=\"onSliderTouchEnd\"\n                  @mousedown=\"onSliderMouseDown\"\n                  @mousemove=\"onSliderMouseMove\"\n                  @mouseup=\"onSliderMouseUp\"\n                  @mouseleave=\"onSliderMouseUp\">\n              <!-- 滑动轨道 -->\n              <view class=\"slider-track\">\n                <view class=\"slider-track-active\" :style=\"{ width: (inspectionForm.rating / 5 * 100) + '%' }\"></view>\n              </view>\n              <!-- 滑块 -->\n              <view class=\"slider-thumb\" :style=\"{ left: (inspectionForm.rating / 5 * 100) + '%' }\"></view>\n              <!-- 刻度点 -->\n              <view class=\"slider-marks\">\n                <view \n                  v-for=\"(mark, index) in 6\" \n                  :key=\"index\" \n                  class=\"slider-mark\"\n                  :class=\"{ 'slider-mark-active': index <= inspectionForm.rating }\"\n                  :style=\"{ left: (index / 5 * 100) + '%' }\"\n                  @click=\"setRatingByMark(index)\"\n                ></view>\n              </view>\n            </view>\n            <!-- 标签 - 放在滑动条容器外面 -->\n            <view class=\"slider-labels-external\">\n              <text \n                v-for=\"(label, labelIndex) in ['0','1','2','3','4','5']\" \n                :key=\"labelIndex\"\n                class=\"slider-label-external\"\n                :class=\"{ 'slider-label-active': labelIndex <= inspectionForm.rating }\"\n                :style=\"{ left: (labelIndex / 5 * 100) + '%' }\"\n              >{{ label }}</text>\n            </view>\n          </view>\n          \n          <view class=\"rating-tips\">\n            <text>请对本次检查结果进行客观评分（支持半星评分）</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 检查照片 -->\n    <view class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"header-content\">\n          <view class=\"card-title\">检查照片</view>\n          <view class=\"card-subtitle\">\n            <text v-if=\"!inspectionForm.hasIssues\">上传现场检查照片（可选）</text>\n            <text v-else>上传现场检查照片（至少上传1张照片）</text>\n          </view>\n        </view>\n        <view class=\"auto-upload-toggle\" @click=\"toggleAutoUpload\">\n          <view class=\"toggle-label\">自动上传</view>\n          <view class=\"toggle-switch\" :class=\"{ active: autoUpload }\">\n            <view class=\"toggle-circle\"></view>\n          </view>\n        </view>\n      </view>\n      <view class=\"card-body\">\n        <view class=\"upload-grid\">\n          <view \n            v-for=\"(photo, index) in inspectionForm.photos\" \n            :key=\"index\" \n            class=\"upload-item has-photo\"\n            @click=\"previewPhoto(index)\"\n          >\n            <image :src=\"getPhotoDisplayUrl(photo)\" mode=\"aspectFill\" class=\"upload-image\"></image>\n            <!-- 上传状态指示器 -->\n            <view v-if=\"photo.uploading\" class=\"photo-uploading\">\n              <view class=\"upload-spinner\"></view>\n            </view>\n            <view v-else-if=\"photo.uploaded\" class=\"photo-uploaded\">\n              <uni-icons type=\"checkmarkempty\" size=\"16\" color=\"white\"></uni-icons>\n            </view>\n            <view class=\"photo-delete\" @click.stop=\"deletePhoto(index)\">\n              <uni-icons type=\"close\" size=\"18\" color=\"white\"></uni-icons>\n            </view>\n          </view>\n          \n          <view \n            v-if=\"inspectionForm.photos.length < 6\" \n            class=\"upload-item add-photo\"\n            @click=\"chooseImage\"\n          >\n            <uni-icons type=\"camera\" size=\"28\" color=\"#8E8E93\"></uni-icons>\n            <text class=\"upload-text\">添加照片</text>\n            <text class=\"upload-count\">{{ inspectionForm.photos.length }}/6</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 问题检查 -->\n    <view class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"card-title\">检查结果</view>\n        <view class=\"card-subtitle\">记录检查发现的问题</view>\n      </view>\n      <view class=\"card-body\">\n        <view class=\"issue-toggle\">\n          <view class=\"toggle-group\">\n            <view \n              class=\"toggle-item enhanced\"\n              :class=\"{ active: !inspectionForm.hasIssues }\"\n              @click=\"toggleIssues(false)\"\n            >\n              <view class=\"toggle-icon\">\n                <uni-icons type=\"checkmarkempty\" size=\"18\" :color=\"!inspectionForm.hasIssues ? '#007AFF' : '#8E8E93'\"></uni-icons>\n              </view>\n              <view class=\"toggle-content\">\n                <text class=\"toggle-title\">检查通过</text>\n                <text class=\"toggle-desc\">可对责任区进行评分</text>\n              </view>\n            </view>\n            <view \n              class=\"toggle-item enhanced\"\n              :class=\"{ active: inspectionForm.hasIssues }\"\n              @click=\"toggleIssues(true)\"\n            >\n              <view class=\"toggle-icon\">\n                <uni-icons type=\"close\" size=\"18\" :color=\"inspectionForm.hasIssues ? '#FF3B30' : '#8E8E93'\"></uni-icons>\n              </view>\n              <view class=\"toggle-content\">\n                <text class=\"toggle-title\">发现问题</text>\n                <text class=\"toggle-desc\">需要记录并整改</text>\n              </view>\n            </view>\n          </view>\n        </view>\n\n        <!-- 检查通过时的备注 -->\n        <view v-if=\"!inspectionForm.hasIssues\" class=\"remarks-form\">\n          <view class=\"form-section\">\n            <view class=\"form-label\">检查备注（可选）</view>\n            <view class=\"textarea-container\">\n              <textarea \n                v-model=\"inspectionForm.remarks\"\n                @input=\"handleRemarksInput\"\n                placeholder=\"可以记录检查过程中的注意事项、建议等...\"\n                maxlength=\"200\"\n                class=\"remarks-textarea-enhanced\"\n              ></textarea>\n              <!-- 字数统计 -->\n              <view class=\"char-count-overlay\">{{ remarksLength }}/200</view>\n            </view>\n          </view>\n        </view>\n\n        <!-- 发现问题时的描述 -->\n        <view v-if=\"inspectionForm.hasIssues\" class=\"issue-form\">\n          <view class=\"form-section\">\n            <view class=\"form-label\">问题描述</view>\n            <view class=\"textarea-container\">\n              <textarea \n                v-model=\"inspectionForm.issueDescription\"\n                @input=\"handleIssueDescriptionInput\"\n                placeholder=\"请详细描述发现的问题...\"\n                maxlength=\"200\"\n                class=\"issue-textarea-enhanced\"\n              ></textarea>\n              <!-- 字数统计 - 始终显示在右下角 -->\n              <view class=\"char-count-overlay\">{{ issueDescriptionLength }}/200</view>\n              \n              <!-- 系统推断提示 - 只在有推断时显示 -->\n              <view v-if=\"inspectionForm.issueDescription && inspectionForm.issueType\" class=\"auto-type-hint-floating\">\n                <uni-icons type=\"checkmarkempty\" size=\"14\" color=\"#52C41A\"></uni-icons>\n                <text>系统推断：{{ inspectionForm.issueType }}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 提交按钮 -->\n    <view class=\"button-container\">\n      <button \n        class=\"primary-button\" \n        @click=\"submitInspection\" \n        :disabled=\"!canSubmit || isSubmitting\"\n        :class=\"{ loading: isSubmitting }\"\n      >\n        <view v-if=\"isSubmitting\" class=\"button-loading\">\n          <view class=\"loading-spinner\"></view>\n          <text>提交中...</text>\n        </view>\n        <text v-else>提交检查记录</text>\n      </button>\n    </view>\n\n    <!-- 底部安全间距 -->\n    <view class=\"bottom-safe-area\"></view>\n  </view>\n</template>\n\n<script>\nimport uploadUtils from '@/utils/upload-utils.js'\n\nexport default {\n  name: 'InspectionDetail',\n  components: {\n    // uni-rate组件会自动注册，无需手动引入\n  },\n  data() {\n    return {\n      areaId: '',\n      isRectificationCheck: false, // 是否是整改复检\n      loading: false,\n      areaInfo: {\n        id: '',\n        name: '',\n        assignedEmployee: '',\n        location: '',\n        cleaningFrequency: '',\n        lastCleaningTime: '',\n        hasRectificationHistory: false // 是否有整改历史\n      },\n      currentTime: '',\n      sliderTouching: false,\n      inspectionForm: {\n        rating: 0,\n        photos: [],\n        hasIssues: false,\n        issueType: '',\n        issueDescription: '',\n        remarks: '',\n\n      },\n\n      isSubmitting: false,\n      autoUpload: true, // 自动上传开关\n      uploading: false, // 是否正在上传\n      uploadProgress: 0 // 上传进度\n    }\n  },\n  computed: {\n    // 计算问题描述长度，确保响应式更新\n    issueDescriptionLength() {\n      return this.inspectionForm.issueDescription ? this.inspectionForm.issueDescription.length : 0;\n    },\n    \n    // 计算备注长度，确保响应式更新\n    remarksLength() {\n      return this.inspectionForm.remarks ? this.inspectionForm.remarks.length : 0;\n    },\n    \n    canSubmit() {\n      if (this.isSubmitting) return false;\n      \n      // 如果检查通过，需要评分\n      if (!this.inspectionForm.hasIssues) {\n        return this.inspectionForm.rating > 0;\n      }\n      \n      // 如果发现问题，需要问题描述和至少一张照片\n      if (this.inspectionForm.hasIssues) {\n        return this.inspectionForm.issueDescription.trim().length > 0 && \n               this.inspectionForm.photos.length > 0;\n      }\n      \n      return false;\n    },\n    \n    // 获取上次清理时间样式\n    getLastCleaningClass() {\n      // 可以根据时间判断是否逾期\n      return 'normal';\n    },\n    \n    // 是否应该显示\"上次清理\"时间\n    shouldShowLastCleaning() {\n      // 只有当该责任区有整改历史时才显示\"上次清理\"时间\n      // 这表示员工已经进行过二次清理（整改后的清理）\n      return this.areaInfo.hasRectificationHistory && this.areaInfo.lastCleaningTime;\n    }\n  },\n  onLoad(options) {\n\n    if (options.id) {\n      this.areaId = options.id;\n      this.isRectificationCheck = options.isRectification === 'true'; // 是否是整改复检\n      this.loadAreaInfo();\n    }\n    this.updateCurrentTime();\n  },\n  methods: {\n    // 更新当前时间\n    updateCurrentTime() {\n      const now = new Date();\n      const year = now.getFullYear();\n      const month = (now.getMonth() + 1).toString().padStart(2, '0');\n      const day = now.getDate().toString().padStart(2, '0');\n      const hour = now.getHours().toString().padStart(2, '0');\n      const minute = now.getMinutes().toString().padStart(2, '0');\n      const second = now.getSeconds().toString().padStart(2, '0');\n      \n      // 获取星期几\n      const weekDays = ['日', '一', '二', '三', '四', '五', '六'];\n      const weekDay = weekDays[now.getDay()];\n      \n      // 格式化为中文日期时间格式\n      this.currentTime = `${year}年${month}月${day}日 星期${weekDay} ${hour}:${minute}:${second}`;\n    },\n\n    // 加载责任区信息\n    async loadAreaInfo() {\n      this.loading = true;\n      \n      try {\n        const { callCloudFunction } = require('@/utils/auth.js');\n        \n        const result = await callCloudFunction('hygiene-area-management', {\n          action: 'getAreaDetail',\n          data: { id: this.areaId }\n        });\n\n        if (result && result.success && result.data) {\n          const area = result.data;\n          \n          // 先设置基本信息\n          this.areaInfo = {\n            id: area._id || area.id,\n            name: area.name || '未知责任区',\n            assignedEmployee: this.getAssignedEmployee(area),\n            location: this.getLocationDisplay(area.location),\n            cleaningFrequency: this.getCleaningFrequency(area),\n            lastCleaningTime: '',\n            hasRectificationHistory: this.isRectificationCheck\n          };\n          \n          // 然后获取清理时间（这个过程中可能会更新负责人）\n          this.areaInfo.lastCleaningTime = await this.getLastCleaningTime(area._id || area.id);\n        } else {\n          throw new Error(result?.message || '获取责任区信息失败');\n        }\n      } catch (error) {\n\n        uni.showToast({\n          title: '加载失败，请重试',\n          icon: 'none'\n        });\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 获取分配的员工\n    getAssignedEmployee(area) {\n      if (area.assigned_user_details && area.assigned_user_details.length > 0) {\n        const user = area.assigned_user_details[0];\n        return user.nickname || user.username || '未知员工';\n      } else if (area.assigned_users && area.assigned_users.length > 0) {\n        return `用户${area.assigned_users[0]}`;\n      }\n      return '未分配';\n    },\n\n    // 获取位置显示\n    getLocationDisplay(location) {\n      if (!location) return '未设置';\n      \n      // 如果是字符串，直接返回\n      if (typeof location === 'string') {\n        return location;\n      }\n      \n      // 如果是对象，尝试获取描述字段\n      if (typeof location === 'object') {\n        return location.description || location.name || location.address || location.text || location.area || '未设置';\n      }\n      \n      return '未设置';\n    },\n\n    // 获取清理频率\n    getCleaningFrequency(area) {\n      if (area.type === 'fixed') {\n        return '每周一次';\n      } else if (area.type === 'public' && area.scheduled_day !== null) {\n        const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\n        return `每${weekDays[area.scheduled_day]}`;\n      }\n      return '未设置';\n    },\n\n    // 获取最后清理时间\n    async getLastCleaningTime(areaId) {\n      try {\n        const { callCloudFunction } = require('@/utils/auth.js');\n        \n        const result = await callCloudFunction('hygiene-cleaning', {\n          action: 'getCleaningRecords',\n          data: {\n            area_id: areaId,\n            pageSize: 1\n          }\n        });\n\n        if (result && result.success && result.data && result.data.list && result.data.list.length > 0) {\n          const lastRecord = result.data.list[0];\n          \n          // 如果责任区没有分配负责人，尝试从清理记录中获取\n          if (this.areaInfo.assignedEmployee === '未分配') {\n            const possibleNames = [\n              lastRecord.user_name,\n              lastRecord.cleaner_name,\n              lastRecord.created_by_name,\n              lastRecord.operator_name\n            ].filter(name => name);\n            \n            if (possibleNames.length > 0) {\n              this.$set(this.areaInfo, 'assignedEmployee', possibleNames[0]);\n            }\n          }\n          \n          return this.formatDateTime(lastRecord.cleaning_date);\n        }\n        return '';\n      } catch (error) {\n\n        return '';\n      }\n    },\n\n    // 格式化日期时间\n    formatDateTime(dateString) {\n      if (!dateString) return '';\n      \n      try {\n        let date;\n        if (typeof dateString === 'string') {\n          if (dateString.includes('T') || dateString.includes('Z')) {\n            date = new Date(dateString);\n          } else {\n            date = new Date(dateString.replace(/-/g, '/'));\n          }\n        } else {\n          date = new Date(dateString);\n        }\n        \n        if (isNaN(date.getTime())) {\n          return '';\n        }\n        \n        return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;\n      } catch (error) {\n\n        return '';\n      }\n    },\n\n    // 星星评分变化\n    onStarRateChange(e) {\n      this.inspectionForm.rating = e.value;\n    },\n\n    // 点击刻度设置评分\n    setRatingByMark(rating) {\n      this.inspectionForm.rating = rating;\n    },\n\n    // 自定义滑动条触摸开始\n    onSliderTouchStart(e) {\n      this.sliderTouching = true;\n      this.updateRatingFromTouch(e);\n    },\n\n    // 自定义滑动条触摸移动\n    onSliderTouchMove(e) {\n      if (this.sliderTouching) {\n        this.updateRatingFromTouch(e);\n      }\n    },\n\n    // 自定义滑动条触摸结束\n    onSliderTouchEnd(e) {\n      this.sliderTouching = false;\n    },\n\n    // 鼠标事件处理（H5端支持）\n    onSliderMouseDown(e) {\n      this.sliderTouching = true;\n      this.updateRatingFromMouse(e);\n    },\n\n    onSliderMouseMove(e) {\n      if (this.sliderTouching) {\n        this.updateRatingFromMouse(e);\n      }\n    },\n\n    onSliderMouseUp(e) {\n      this.sliderTouching = false;\n    },\n\n    // 根据触摸位置更新评分\n    updateRatingFromTouch(e) {\n      const touch = e.touches[0] || e.changedTouches[0];\n      // 获取滑动条容器的位置信息\n      uni.createSelectorQuery().in(this).select('.custom-slider-container').boundingClientRect((rect) => {\n        if (rect) {\n          const x = touch.clientX - rect.left;\n          const percentage = Math.max(0, Math.min(1, x / rect.width));\n          const rating = Math.round(percentage * 5 * 2) / 2; // 精确到0.5\n          this.inspectionForm.rating = Math.max(0, Math.min(5, rating));\n        }\n      }).exec();\n    },\n\n    // 根据鼠标位置更新评分（H5端）\n    updateRatingFromMouse(e) {\n      // 获取滑动条容器的位置信息\n      uni.createSelectorQuery().in(this).select('.custom-slider-container').boundingClientRect((rect) => {\n        if (rect) {\n          const x = e.clientX - rect.left;\n          const percentage = Math.max(0, Math.min(1, x / rect.width));\n          const rating = Math.round(percentage * 5 * 2) / 2; // 精确到0.5\n          this.inspectionForm.rating = Math.max(0, Math.min(5, rating));\n        }\n      }).exec();\n    },\n\n    // 获取评分描述\n    getRatingDescription(rating) {\n      if (rating === 0) return '请评分';\n      if (rating <= 1) return '较差';\n      if (rating <= 2) return '一般';\n      if (rating <= 3) return '良好';\n      if (rating < 5) return '优秀';  // 4-4.5分都是优秀\n      if (rating === 5) return '完美';\n      return '';\n    },\n\n    // 选择图片\n    chooseImage() {\n      if (this.inspectionForm.photos.length >= 6) {\n        uni.showToast({\n          title: '最多只能上传6张照片',\n          icon: 'none'\n        });\n        return;\n      }\n\n      uni.chooseImage({\n        count: 6 - this.inspectionForm.photos.length,\n        sizeType: ['compressed'],\n        sourceType: ['camera', 'album'],\n        success: async (res) => {\n          // 先添加照片到列表（显示本地预览）\n          const newPhotos = res.tempFilePaths.map(path => ({\n            url: path, // 保持本地路径用于显示\n            cloudUrl: '', // 云端访问URL\n            fileID: '', // 云端文件ID\n            name: `检查照片${this.inspectionForm.photos.length + 1}.jpg`,\n            uploaded: false,\n            uploading: false\n          }));\n          \n          this.inspectionForm.photos = this.inspectionForm.photos.concat(newPhotos);\n          \n          // 如果开启自动上传，立即上传新选择的照片\n          if (this.autoUpload) {\n            this.autoUploadNewPhotos(newPhotos);\n          }\n        }\n      });\n    },\n\n    // 上传图片\n    async uploadImages(tempFilePaths) {\n      try {\n        \n        const uploadResult = await uploadUtils.batchUpload(tempFilePaths, {\n          pathGenerator: () => {\n            const timestamp = Date.now();\n            const random = Math.random().toString(36).substring(2, 8);\n            return `6s/inspection/${this.areaId}/${timestamp}_${random}.jpg`;\n          },\n          maxConcurrent: 2\n        });\n\n        if (uploadResult.success) {\n          // 更新现有照片的状态，而不是添加新照片\n          uploadResult.results.forEach((result, index) => {\n            if (result.success) {\n              const tempFilePath = tempFilePaths[index];\n              const photoIndex = this.inspectionForm.photos.findIndex(photo => photo.url === tempFilePath);\n              \n              if (photoIndex !== -1) {\n                this.$set(this.inspectionForm.photos[photoIndex], 'uploaded', true);\n                this.$set(this.inspectionForm.photos[photoIndex], 'cloudUrl', result.url);\n                this.$set(this.inspectionForm.photos[photoIndex], 'fileID', result.cloudPath);\n                this.$set(this.inspectionForm.photos[photoIndex], 'uploading', false);\n                // 保持原始 url 不变，用于显示本地预览\n              }\n            }\n          });\n\n          const successCount = uploadResult.results.filter(r => r.success).length;\n          const failCount = uploadResult.results.filter(r => !r.success).length;\n\n          if (failCount > 0) {\n            throw new Error(`${failCount}张照片上传失败`);\n          }\n        } else {\n          throw new Error('照片上传失败');\n        }\n      } catch (error) {\n        throw error;\n      }\n    },\n\n    // 自动上传新选择的照片\n    async autoUploadNewPhotos(newPhotos) {\n      for (let i = 0; i < newPhotos.length; i++) {\n        const photo = newPhotos[i];\n        const photoIndex = this.inspectionForm.photos.findIndex(p => p.url === photo.url);\n        \n        if (photoIndex === -1) continue;\n        \n        try {\n          // 标记为正在上传\n          this.$set(this.inspectionForm.photos[photoIndex], 'uploading', true);\n          \n          // 单张照片上传\n          const uploadResult = await this.uploadSinglePhoto(photo);\n          \n          if (uploadResult.success) {\n            // 更新照片信息\n            this.$set(this.inspectionForm.photos[photoIndex], 'uploaded', true);\n            this.$set(this.inspectionForm.photos[photoIndex], 'cloudUrl', uploadResult.url);\n            this.$set(this.inspectionForm.photos[photoIndex], 'fileID', uploadResult.cloudPath);\n            this.$set(this.inspectionForm.photos[photoIndex], 'uploading', false);\n            // 保持原始 url 不变，用于显示本地预览\n          } else {\n            throw new Error(uploadResult.error || '上传失败');\n          }\n        } catch (error) {\n          this.$set(this.inspectionForm.photos[photoIndex], 'uploading', false);\n          \n          // 显示上传失败提示\n          uni.showModal({\n            title: '上传失败',\n            content: `照片${i + 1}上传失败: ${error.message || error}`,\n            showCancel: false,\n            confirmText: '知道了'\n          });\n        }\n      }\n    },\n\n    // 单张照片上传\n    async uploadSinglePhoto(photo) {\n      try {\n        const timestamp = Date.now();\n        const random = Math.random().toString(36).substring(2, 8);\n        const cloudPath = `6s/inspection/${this.areaId}/${timestamp}_${random}.jpg`;\n        \n        // 使用 uploadToCloud 方法上传单张照片\n        const uploadResult = await uploadUtils.uploadToCloud(photo.url, cloudPath, {\n          onProgress: (progress) => {\n            // 可以在这里更新单张照片的上传进度\n          }\n        });\n        \n        if (uploadResult && uploadResult.fileID) {\n          // 获取访问URL\n          const fileInfo = await uploadUtils.getFileInfo(uploadResult.fileID);\n          \n          return {\n            success: true,\n            cloudPath: uploadResult.fileID,\n            url: fileInfo.tempFileURL || uploadResult.fileID,\n            size: uploadResult.actualSize\n          };\n        } else {\n          throw new Error('上传返回结果异常');\n        }\n      } catch (error) {\n        return { success: false, error: error.message };\n      }\n    },\n\n    // 切换自动上传状态\n    toggleAutoUpload() {\n      this.autoUpload = !this.autoUpload;\n      uni.showToast({\n        title: this.autoUpload ? '已开启自动上传' : '已关闭自动上传',\n        icon: 'none'\n      });\n    },\n\n    // 删除照片\n    async deletePhoto(index) {\n      uni.showModal({\n        title: '确认删除',\n        content: '确定要删除这张照片吗？',\n        success: async (res) => {\n          if (res.confirm) {\n            const photo = this.inspectionForm.photos[index];\n            \n            // 如果照片已经上传到云存储，尝试删除云存储文件\n            if (photo && photo.fileID) {\n              try {\n                uni.showLoading({ title: '删除照片中...' });\n                \n                const result = await uniCloud.callFunction({\n                  name: 'delete-file',\n                  data: {\n                    fileList: [this.extractFileId(photo.fileID)]\n                  }\n                });\n                \n                if (result.result && result.result.code === 0) {\n                  // 删除成功\n                } else {\n                  // 删除失败但继续删除本地引用\n                }\n              } catch (error) {\n                // 即使删除云存储文件失败，也继续删除本地引用\n              } finally {\n                uni.hideLoading();\n              }\n            }\n            \n            // 删除本地引用\n            this.inspectionForm.photos.splice(index, 1);\n          }\n        }\n      });\n    },\n\n    // 从URL中提取文件ID\n    extractFileId(url) {\n      if (url.startsWith('cloud://')) {\n        const parts = url.split('/');\n        return parts[parts.length - 1];\n      } else if (url.includes('tcb-api')) {\n        const urlObj = new URL(url);\n        return urlObj.pathname.split('/').pop();\n      }\n      return url;\n    },\n\n    // 获取照片显示URL（处理HTTP协议警告）\n    getPhotoDisplayUrl(photo) {\n      const url = photo.url || photo;\n      \n      // 如果是本地临时文件路径，在小程序环境下可能会有HTTP协议警告\n      // 但这是正常的本地文件路径，直接返回即可\n      if (typeof url === 'string' && url.startsWith('http://tmp/')) {\n        // 这是微信小程序的本地临时文件路径，直接使用\n        return url;\n      }\n      \n      // 优先使用本地URL，如果已上传则可以选择使用云端URL（但为了避免闪烁，保持使用本地URL）\n      return url;\n    },\n\n    // 预览照片\n    previewPhoto(index) {\n      const urls = this.inspectionForm.photos.map(photo => this.getPhotoDisplayUrl(photo));\n      uni.previewImage({\n        urls: urls,\n        current: urls[index]\n      });\n    },\n\n    // 切换问题状态\n    toggleIssues(hasIssues) {\n      this.inspectionForm.hasIssues = hasIssues;\n      if (!hasIssues) {\n        // 切换到\"检查通过\"：清空问题相关信息\n        this.inspectionForm.issueDescription = '';\n        this.inspectionForm.issueType = '';\n      } else {\n        // 切换到\"发现问题\"：重置评分和备注\n        this.inspectionForm.rating = 0;\n        this.inspectionForm.remarks = '';\n      }\n    },\n\n    // 处理问题描述输入，确保字符限制和响应式更新\n    handleIssueDescriptionInput(e) {\n      let value = e.detail.value || '';\n      \n      // 强制限制字符数量\n      if (value.length > 200) {\n        value = value.substring(0, 200);\n        // 如果超出限制，显示提示\n        uni.showToast({\n          title: '问题描述不能超过200个字符',\n          icon: 'none',\n          duration: 1500\n        });\n      }\n      \n      // 更新数据\n      this.inspectionForm.issueDescription = value;\n      \n      // 自动推断问题类型\n      this.autoDetectIssueType(value);\n      \n      // 强制触发视图更新\n      this.$forceUpdate();\n    },\n\n    // 处理备注输入，确保字符限制和响应式更新\n    handleRemarksInput(e) {\n      let value = e.detail.value || '';\n      \n      // 强制限制字符数量\n      if (value.length > 200) {\n        value = value.substring(0, 200);\n        // 如果超出限制，显示提示\n        uni.showToast({\n          title: '检查备注不能超过200个字符',\n          icon: 'none',\n          duration: 1500\n        });\n      }\n      \n      // 更新数据\n      this.inspectionForm.remarks = value;\n      \n      // 强制触发视图更新\n      this.$forceUpdate();\n    },\n\n    // 根据问题描述自动推断问题类型\n    autoDetectIssueType(description) {\n      if (!description) return;\n      \n      const keywords = {\n        '设备问题': ['设备', '机器', '故障', '损坏', '噪音', '异响', '漏油', '漏气', '过热', '振动', '磨损'],\n        '清洁问题': ['脏', '污', '积尘', '垃圾', '异味', '地面', '清洁', '卫生', '油污', '水渍', '污渍', '不洁', '打扫'],\n        '整理问题': ['杂乱', '摆放', '工具', '物品', '整理', '混乱', '堆放', '乱放', '凌乱', '无序', '杂物'],\n        '安全问题': ['安全', '隐患', '危险', '滑倒', '积水', '漏电', '阻塞', '堵塞', '阻挡', '绊倒', '碰撞'],\n        '环境问题': ['环境', '温度', '湿度', '通风', '采光', '噪声', '粉尘', '气味', '空气', '污染', '废气'],\n        '标识问题': ['标识', '标志', '标牌', '不清', '缺失', '模糊', '褪色', '破损', '看不清', '无标识'],\n      };\n      \n      for (let [type, words] of Object.entries(keywords)) {\n        if (words.some(word => description.includes(word))) {\n          this.inspectionForm.issueType = type;\n          return;\n        }\n      }\n      \n      // 默认为其他问题\n      this.inspectionForm.issueType = '其他问题';\n    },\n\n    // 提交检查记录\n    async submitInspection() {\n      if (!this.canSubmit) return;\n\n      if (this.inspectionForm.hasIssues && !this.inspectionForm.issueDescription.trim()) {\n        uni.showToast({\n          title: '请填写问题描述',\n          icon: 'none'\n        });\n        return;\n      }\n\n      if (this.inspectionForm.hasIssues && this.inspectionForm.photos.length === 0) {\n        uni.showToast({\n          title: '发现问题时必须上传照片作为证据',\n          icon: 'none'\n        });\n        return;\n      }\n\n      uni.showModal({\n        title: '确认提交',\n        content: '确定要提交本次检查记录吗？',\n        success: async (res) => {\n          if (res.confirm) {\n            await this.doSubmit();\n          }\n        }\n      });\n    },\n\n    // 执行提交\n    async doSubmit() {\n      this.isSubmitting = true;\n\n      try {\n        const { callCloudFunction } = require('@/utils/auth.js');\n        \n        // 如果有问题但没有设置类型，自动推断\n        if (this.inspectionForm.hasIssues && this.inspectionForm.issueDescription && !this.inspectionForm.issueType) {\n          this.autoDetectIssueType(this.inspectionForm.issueDescription);\n        }\n\n        // 检查是否有未上传的照片\n        const unuploadedPhotos = this.inspectionForm.photos.filter(photo => !photo.uploaded && !photo.uploading);\n        \n        if (unuploadedPhotos.length > 0) {\n          uni.showLoading({\n            title: '正在上传剩余照片...'\n          });\n          \n          // 上传剩余未上传的照片\n          const tempFilePaths = unuploadedPhotos.map(photo => photo.url);\n          await this.uploadImages(tempFilePaths);\n          \n          // 重新检查是否还有未上传的照片\n          const stillUnuploaded = this.inspectionForm.photos.filter(photo => !photo.uploaded && !photo.uploading);\n          if (stillUnuploaded.length > 0) {\n            throw new Error('部分照片上传失败，请重试');\n          }\n        }\n\n        // 准备照片数据（只包含已上传的照片）\n        const photos = this.inspectionForm.photos\n          .filter(photo => photo.uploaded && photo.fileID)\n          .map(photo => ({\n            url: photo.fileID,\n            type: 'inspection',\n            description: ''\n          }));\n\n        const inspectionData = {\n          area_id: this.areaId,\n          inspection_date: new Date().toISOString(),\n          result: !this.inspectionForm.hasIssues ? 'passed' : 'failed', // 云函数必需字段\n          photos: photos\n        };\n\n        // 处理问题描述（移到条件判断外面）\n        let finalDescription = '';\n        if (this.inspectionForm.hasIssues) {\n          finalDescription = this.inspectionForm.issueDescription.trim();\n          if (finalDescription.length > 200) {\n            finalDescription = finalDescription.substring(0, 200);\n            console.warn('问题描述被截断到200字符');\n          }\n        }\n\n        // 根据检查结果添加相应数据\n        if (!this.inspectionForm.hasIssues) {\n          // 检查通过：添加评分和备注\n          inspectionData.score = this.inspectionForm.rating;\n          inspectionData.remarks = this.inspectionForm.remarks.trim() || '检查通过';\n        } else {\n          // 发现问题：添加问题信息\n          inspectionData.score = 0;\n          inspectionData.remarks = finalDescription;\n          inspectionData.issues = [{\n            type: this.inspectionForm.issueType || '其他问题',\n            description: finalDescription,\n            severity: 'medium'\n          }];\n        }\n\n        // 保留您原有的字段（如果其他地方需要用到）\n        inspectionData.is_passed = !this.inspectionForm.hasIssues;\n        inspectionData.overall_rating = this.inspectionForm.rating;\n        inspectionData.issue_description = this.inspectionForm.hasIssues ? finalDescription : '';\n        inspectionData.is_rectification_recheck = this.isRectificationCheck;\n        inspectionData.original_record_id = null;\n\n        // 调用云函数提交检查记录\n        const result = await callCloudFunction('hygiene-inspection', {\n          action: 'createInspectionRecord',\n          data: inspectionData\n        });\n\n        if (result && result.success) {\n          this.isSubmitting = false;\n\n          uni.showToast({\n            title: '提交成功',\n            icon: 'success'\n          });\n\n          // 通知其他页面数据已更新\n          uni.$emit('inspectionRecordUpdated', {\n            areaId: this.areaId,\n            isPassed: !this.inspectionForm.hasIssues,\n            isRectificationCheck: this.isRectificationCheck\n          });\n          \n          // 如果发现问题（检查未通过），还需要通知整改任务创建\n          if (this.inspectionForm.hasIssues) {\n            uni.$emit('rectificationRecordUpdated', {\n              areaId: this.areaId,\n              action: 'created',\n              isFromInspection: true\n            });\n          }\n\n          // 2秒后返回上一页\n          setTimeout(() => {\n            uni.navigateBack();\n          }, 1500);\n        } else {\n          throw new Error(result?.message || '提交检查记录失败');\n        }\n\n      } catch (error) {\n        this.isSubmitting = false;\n\n        \n        let errorMessage = '提交失败，请重试';\n        if (error.message) {\n          if (error.message.includes('未登录')) {\n            errorMessage = '请先登录';\n          } else if (error.message.includes('权限')) {\n            errorMessage = '您没有权限操作该责任区';\n          } else {\n            errorMessage = error.message;\n          }\n        }\n        \n        uni.showModal({\n          title: '提交失败',\n          content: errorMessage,\n          showCancel: false,\n          confirmText: '知道了'\n        });\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\n\twidth: 100%;\n\toverflow-x: hidden; /* 防止水平滚动 */\n\tbox-sizing: border-box;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;\n}\n\n/* 页面头部 */\n.page-header {\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n  padding: 32rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.header-content {\n  flex: 1;\n}\n\n.header-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: white;\n  margin-bottom: 8rpx;\n}\n\n.header-subtitle {\n  font-size: 28rpx;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.check-time {\n  background: rgba(255, 255, 255, 0.2);\n  padding: 12rpx 20rpx;\n  border-radius: 20rpx;\n  backdrop-filter: blur(10rpx);\n  font-size: 24rpx;\n  color: white;\n}\n\n/* 卡片样式 */\n.card {\n  background: white;\n  border-radius: 16rpx;\n  margin: 24rpx 32rpx 0 32rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.card-header {\n  padding: 32rpx 32rpx 16rpx 32rpx;\n  border-bottom: 1rpx solid #F2F2F7;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.header-content {\n  flex: 1;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 4rpx;\n}\n\n.card-subtitle {\n  font-size: 26rpx;\n  color: #8E8E93;\n}\n\n/* 自动上传开关样式 */\n.auto-upload-toggle {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n  cursor: pointer;\n}\n\n.toggle-label {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.toggle-switch {\n  width: 76rpx;\n  height: 44rpx;\n  background: #E5E5EA;\n  border-radius: 22rpx;\n  position: relative;\n  transition: background-color 0.3s ease;\n}\n\n.toggle-switch.active {\n  background: #34C759;\n}\n\n.toggle-circle {\n  width: 36rpx;\n  height: 36rpx;\n  background: white;\n  border-radius: 50%;\n  position: absolute;\n  top: 4rpx;\n  left: 4rpx;\n  transition: transform 0.3s ease;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);\n}\n\n.toggle-switch.active .toggle-circle {\n  transform: translateX(32rpx);\n}\n\n.card-body {\n  padding: 32rpx;\n}\n\n/* 增强的信息网格 - 兼容性优化 */\n.info-grid-enhanced {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 20rpx;\n}\n\n/* 为不支持CSS Grid的旧版本提供fallback */\n.info-grid-enhanced {\n  display: flex;\n  flex-wrap: wrap;\n  margin: -10rpx;\n}\n\n.info-grid-enhanced .info-item-enhanced {\n  width: calc(50% - 20rpx);\n  margin: 10rpx;\n}\n\n/* 现代浏览器使用Grid布局 */\n@supports (display: grid) {\n  .info-grid-enhanced {\n    display: grid;\n    grid-template-columns: repeat(2, 1fr);\n    gap: 20rpx;\n    margin: 0;\n  }\n  \n  .info-grid-enhanced .info-item-enhanced {\n    width: auto;\n    margin: 0;\n  }\n}\n\n/* 小屏设备适配 */\n@media (max-width: 600rpx) {\n  .info-grid-enhanced {\n    grid-template-columns: 1fr;\n    gap: 16rpx;\n  }\n  \n  /* Flexbox fallback for small screens */\n  .info-grid-enhanced .info-item-enhanced {\n    width: calc(100% - 20rpx);\n  }\n  \n  @supports (display: grid) {\n    .info-grid-enhanced .info-item-enhanced {\n      width: auto;\n    }\n  }\n}\n\n.info-item-enhanced {\n  display: flex;\n  align-items: flex-start;\n  gap: 12rpx;\n  padding: 20rpx;\n  background: #FAFAFA;\n  border-radius: 16rpx;\n  border: 2rpx solid #F2F2F7;\n  transition: all 0.3s ease;\n}\n\n.info-item-enhanced:hover {\n  background: white;\n  border-color: #E5E5EA;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);\n}\n\n.info-icon {\n  flex-shrink: 0;\n  width: 36rpx;\n  height: 36rpx;\n  background: rgba(255, 255, 255, 0.8);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-top: 2rpx;\n}\n\n.info-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 6rpx;\n}\n\n.info-label-enhanced {\n  font-size: 24rpx;\n  color: #8E8E93;\n  font-weight: 500;\n  line-height: 1.2;\n}\n\n.info-value-enhanced {\n  font-size: 28rpx;\n  color: #1C1C1E;\n  font-weight: 600;\n  line-height: 1.3;\n}\n\n/* 评分区域 */\n.rating-section {\n  text-align: center;\n}\n\n.rating-display-large {\n  margin-bottom: 32rpx;\n}\n\n.rating-number-container {\n  display: flex;\n  align-items: baseline;\n  justify-content: center;\n  gap: 6rpx;\n  margin-bottom: 8rpx;\n}\n\n.rating-number {\n  font-size: 80rpx;\n  font-weight: 700;\n  color: #007AFF;\n  line-height: 1;\n}\n\n.rating-unit {\n  font-size: 40rpx;\n  color: #8E8E93;\n  font-weight: 500;\n}\n\n.star-rating {\n  display: flex;\n  justify-content: center;\n  gap: 16rpx;\n  margin-bottom: 24rpx;\n}\n\n.rating-tips {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n/* 上传区域 */\n.upload-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 16rpx;\n  margin-bottom: 16rpx;\n}\n\n.upload-item {\n  aspect-ratio: 1;\n  border-radius: 12rpx;\n  overflow: hidden;\n  position: relative;\n  \n  &.has-photo {\n    background: #F2F2F7;\n  }\n  \n  &.add-photo {\n    border: 2rpx dashed #C7C7CC;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    gap: 8rpx;\n    background: #FAFAFA;\n  }\n}\n\n.upload-image {\n  width: 100%;\n  height: 100%;\n}\n\n.photo-delete {\n  position: absolute;\n  top: 6rpx;\n  right: 6rpx;\n  width: 36rpx;\n  height: 36rpx;\n  background: rgba(0, 0, 0, 0.7);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\n}\n\n/* 照片状态指示器 */\n.photo-uploading {\n  position: absolute;\n  top: 8rpx;\n  left: 8rpx;\n  width: 32rpx;\n  height: 32rpx;\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-spinner {\n  width: 20rpx;\n  height: 20rpx;\n  border: 2rpx solid rgba(255, 255, 255, 0.3);\n  border-top: 2rpx solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.photo-uploaded {\n  position: absolute;\n  top: 8rpx;\n  left: 8rpx;\n  width: 32rpx;\n  height: 32rpx;\n  background: #34C759;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-text {\n  font-size: 22rpx;\n  color: #8E8E93;\n}\n\n.upload-count {\n  font-size: 20rpx;\n  color: #C7C7CC;\n  margin-top: 4rpx;\n}\n\n/* 问题记录 */\n.issue-toggle {\n  margin-bottom: 32rpx;\n}\n\n.toggle-group {\n  display: flex;\n  gap: 16rpx;\n}\n\n.toggle-item {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8rpx;\n  padding: 16rpx;\n  border-radius: 8rpx;\n  font-size: 28rpx;\n  transition: all 0.3s ease;\n  \n  &.active {\n    background: white;\n    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n  }\n}\n\n.issue-form,\n.remarks-form {\n  display: flex;\n  flex-direction: column;\n  gap: 32rpx;\n}\n\n.form-section {\n  display: flex;\n  flex-direction: column;\n  gap: 16rpx;\n}\n\n.form-label {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 16rpx;\n}\n\n/* 增强的问题描述输入框 */\n.textarea-container {\n  position: relative;\n  border-radius: 16rpx;\n  border: 2rpx solid #F2F2F7;\n  background: #FAFAFA;\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n\n\n.textarea-container:focus-within {\n  border-color: #007AFF;\n  background: white;\n  box-shadow: 0 2rpx 12rpx rgba(0, 122, 255, 0.15);\n}\n\n.issue-textarea-enhanced {\n  width: 100%;\n  min-height: 160rpx;\n  background: transparent;\n  padding: 24rpx 20rpx 16rpx 20rpx;\n  font-size: 28rpx;\n  color: #1C1C1E;\n  line-height: 1.5;\n  border: none;\n  outline: none;\n  resize: none;\n  box-sizing: border-box;\n}\n\n.issue-textarea-enhanced::placeholder {\n  color: #C7C7CC;\n  font-size: 28rpx;\n}\n\n.remarks-textarea-enhanced {\n  width: 100%;\n  min-height: 120rpx;\n  background: transparent;\n  padding: 24rpx 20rpx 16rpx 20rpx;\n  font-size: 28rpx;\n  color: #1C1C1E;\n  line-height: 1.5;\n  border: none;\n  outline: none;\n  resize: none;\n  box-sizing: border-box;\n}\n\n.remarks-textarea-enhanced::placeholder {\n  color: #C7C7CC;\n  font-size: 28rpx;\n}\n\n.char-count-overlay {\n  position: absolute;\n  bottom: 16rpx;\n  right: 20rpx;\n  font-size: 24rpx;\n  color: #C7C7CC;\n  font-weight: 500;\n  background: rgba(255, 255, 255, 0.9);\n  padding: 4rpx 8rpx;\n  border-radius: 8rpx;\n  backdrop-filter: blur(4rpx);\n  z-index: 2;\n}\n\n/* 简洁的按钮样式 */\n.button-container {\n  padding: 32rpx;\n  display: flex;\n  flex-direction: column;\n  gap: 16rpx;\n}\n\n.primary-button {\n  background: #007AFF;\n  color: white;\n  border: none;\n  border-radius: 12rpx;\n  padding: 24rpx;\n  font-size: 32rpx;\n  font-weight: 600;\n  width: 100%;\n}\n\n.primary-button[disabled] {\n  background: #C7C7CC;\n  color: #8E8E93;\n}\n\n.primary-button.loading {\n  background: #0056D6;\n  opacity: 0.9;\n}\n\n.button-loading {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12rpx;\n  color: white;\n}\n\n.loading-spinner {\n  width: 32rpx;\n  height: 32rpx;\n  border: 3rpx solid rgba(255, 255, 255, 0.3);\n  border-top: 3rpx solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.submit-tips-enhanced {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n  padding: 16rpx 20rpx;\n  background: rgba(0, 122, 255, 0.06);\n  border-radius: 16rpx;\n  border-left: 4rpx solid #007AFF;\n}\n\n.tips-icon {\n  flex-shrink: 0;\n  width: 20rpx;\n  height: 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.tips-text {\n  flex: 1;\n  font-size: 26rpx;\n  color: #0056CC;\n  line-height: 1.4;\n  font-weight: 500;\n}\n\n/* 底部安全间距 */\n.bottom-safe-area {\n  height: 40rpx;\n}\n\n/* 响应式调整 - 使用rpx单位更好的跨平台兼容性 */\n@media (max-width: 600rpx) {\n  .page-header {\n    padding: 24rpx 16rpx;\n  }\n  \n  .card {\n    margin: 24rpx 16rpx 0 16rpx;\n  }\n  \n  .button-container {\n    padding: 24rpx 16rpx;\n  }\n}\n\n.auto-type-hint-floating {\n  position: absolute;\n  bottom: 16rpx;\n  left: 20rpx;\n  display: flex;\n  align-items: center;\n  gap: 6rpx;\n  padding: 6rpx 12rpx;\n  background: #F6FFED;\n  border-radius: 12rpx;\n  font-size: 24rpx;\n  color: #52C41A;\n  border: 1rpx solid #B7EB8F;\n  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.15);\n  z-index: 2;\n}\n\n/* 评分描述 */\n.rating-desc {\n  font-size: 24rpx;\n  color: #8E8E93;\n  margin-top: 8rpx;\n  font-weight: 500;\n}\n\n/* 星星评分样式 */\n.star-rating {\n  display: flex;\n  justify-content: center;\n  margin: 24rpx 0;\n}\n\n/* 自定义滑动条评分样式 */\n.custom-slider-rating {\n  margin: 32rpx 0 24rpx 0;\n}\n\n.custom-slider-container {\n  position: relative;\n  height: 40rpx;\n  margin: 0 24rpx;\n  display: flex;\n  align-items: center;\n  cursor: pointer; /* H5端显示手型光标 */\n  user-select: none; /* 防止选中文本 */\n}\n\n.slider-track {\n  position: absolute;\n  width: 100%;\n  height: 4rpx;\n  background: #E5E5EA;\n  border-radius: 2rpx;\n  left: 0;\n}\n\n.slider-track-active {\n  height: 100%;\n  background: #FFD700;\n  border-radius: 2rpx;\n  transition: width 0.2s ease;\n}\n\n.slider-thumb {\n  position: absolute;\n  width: 24rpx;\n  height: 24rpx;\n  background: #FFD700;\n  border-radius: 50%;\n  transform: translateX(-50%);\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);\n  transition: left 0.2s ease;\n  z-index: 2;\n  cursor: grab; /* 拖拽光标 */\n}\n\n.slider-thumb:active {\n  cursor: grabbing; /* 拖拽中光标 */\n}\n\n.slider-marks {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: -12rpx;\n}\n\n.slider-mark {\n  position: absolute;\n  width: 12rpx;\n  height: 12rpx;\n  background: #E5E5EA;\n  border-radius: 50%;\n  transform: translateX(-50%);\n  cursor: pointer;\n  z-index: 1;\n  transition: all 0.2s ease;\n}\n\n.slider-mark-active {\n  background: #FFD700;\n  width: 16rpx;\n  height: 16rpx;\n  box-shadow: 0 2rpx 6rpx rgba(255, 215, 0, 0.3);\n}\n\n.slider-labels-external {\n  position: relative;\n  margin-top: 20rpx;\n  margin-left: 24rpx;\n  margin-right: 24rpx;\n  height: 40rpx;\n}\n\n.slider-label-external {\n  position: absolute;\n  font-size: 24rpx;\n  color: #8E8E93;\n  font-weight: 500;\n  transform: translateX(-50%);\n  text-align: center;\n  transition: all 0.2s ease;\n}\n\n.slider-label-active {\n  color: #FFD700;\n  font-weight: 600;\n  font-size: 26rpx;\n}\n\n/* 增强的切换选项样式 */\n.toggle-item.enhanced {\n  flex: 1;\n  display: flex;\n  align-items: flex-start;\n  padding: 24rpx 20rpx;\n  border-radius: 16rpx;\n  border: 2rpx solid #F2F2F7;\n  background: #FAFAFA;\n  transition: all 0.3s ease;\n  gap: 12rpx;\n}\n\n\n\n.toggle-item.enhanced.active {\n  border-color: currentColor;\n  border-width: 3rpx;\n  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);\n}\n\n.toggle-item.enhanced:first-child.active {\n  border-color: #007AFF;\n  background: rgba(0, 122, 255, 0.08);\n}\n\n.toggle-item.enhanced:last-child.active {\n  border-color: #FF3B30;\n  background: rgba(255, 59, 48, 0.08);\n}\n\n.toggle-icon {\n  flex-shrink: 0;\n  width: 36rpx;\n  height: 36rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-top: 2rpx;\n}\n\n.toggle-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 6rpx;\n}\n\n.toggle-title {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  line-height: 1.2;\n}\n\n.toggle-desc {\n  font-size: 24rpx;\n  color: #8E8E93;\n  line-height: 1.3;\n}\n\n/* 加载状态样式 */\n.loading-container {\n  padding: 60rpx 32rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.loading-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 24rpx;\n}\n\n.loading-spinner {\n  width: 40rpx;\n  height: 40rpx;\n  border: 4rpx solid #F2F2F7;\n  border-top: 4rpx solid #007AFF;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n  text-align: center;\n}\n\n\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n@keyframes pulse {\n  0%, 100% { opacity: 0.7; }\n  50% { opacity: 0.3; }\n}\n\n/* H5浏览器端优化 - 条件编译 */\n/* #ifdef H5 */\n@media screen and (min-width: 600px) {\n  .upload-grid {\n    max-width: 480px;\n  }\n  \n  .upload-item {\n    max-width: 150px;\n    max-height: 150px;\n  }\n}\n/* #endif */\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./inspection-detail.vue?vue&type=style&index=0&id=4a16827e&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./inspection-detail.vue?vue&type=style&index=0&id=4a16827e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775845217\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}