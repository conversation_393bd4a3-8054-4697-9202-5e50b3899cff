require('./common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/6s_pkg/record-detail"],{

/***/ 557:
/*!*****************************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2F6s_pkg%2Frecord-detail"} ***!
  \*****************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _recordDetail = _interopRequireDefault(__webpack_require__(/*! ./pages/6s_pkg/record-detail.vue */ 558));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_recordDetail.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 558:
/*!**********************************************!*\
  !*** D:/Xwzc/pages/6s_pkg/record-detail.vue ***!
  \**********************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _record_detail_vue_vue_type_template_id_9f2cd4fe_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./record-detail.vue?vue&type=template&id=9f2cd4fe&scoped=true& */ 559);
/* harmony import */ var _record_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./record-detail.vue?vue&type=script&lang=js& */ 561);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _record_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _record_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _record_detail_vue_vue_type_style_index_0_id_9f2cd4fe_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./record-detail.vue?vue&type=style&index=0&id=9f2cd4fe&lang=scss&scoped=true& */ 563);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _record_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _record_detail_vue_vue_type_template_id_9f2cd4fe_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _record_detail_vue_vue_type_template_id_9f2cd4fe_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "9f2cd4fe",
  null,
  false,
  _record_detail_vue_vue_type_template_id_9f2cd4fe_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/6s_pkg/record-detail.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 559:
/*!*****************************************************************************************!*\
  !*** D:/Xwzc/pages/6s_pkg/record-detail.vue?vue&type=template&id=9f2cd4fe&scoped=true& ***!
  \*****************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_record_detail_vue_vue_type_template_id_9f2cd4fe_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record-detail.vue?vue&type=template&id=9f2cd4fe&scoped=true& */ 560);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_record_detail_vue_vue_type_template_id_9f2cd4fe_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_record_detail_vue_vue_type_template_id_9f2cd4fe_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_record_detail_vue_vue_type_template_id_9f2cd4fe_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_record_detail_vue_vue_type_template_id_9f2cd4fe_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 560:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/6s_pkg/record-detail.vue?vue&type=template&id=9f2cd4fe&scoped=true& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 118))
    },
    pEmptyState: function () {
      return __webpack_require__.e(/*! import() | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then(__webpack_require__.bind(null, /*! @/components/p-empty-state/p-empty-state.vue */ 677))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = _vm.getPageTitle()
  var m1 = !_vm.loading ? _vm.getStatusText(_vm.recordInfo.status) : null
  var m2 =
    !_vm.loading &&
    !_vm.loadError &&
    _vm.dataLoaded &&
    _vm.recordType === "inspection" &&
    _vm.recordInfo.rating > 0 &&
    !_vm.recordInfo.hasIssues
      ? _vm.getRatingDescription(_vm.recordInfo.rating)
      : null
  var g0 =
    !_vm.loading && !_vm.loadError && _vm.dataLoaded
      ? _vm.recordInfo.photos.length
      : null
  var g1 =
    !_vm.loading && !_vm.loadError && _vm.dataLoaded
      ? _vm.recordInfo.photos.length
      : null
  var m3 =
    !_vm.loading &&
    !_vm.loadError &&
    _vm.dataLoaded &&
    _vm.recordType === "inspection"
      ? _vm.getResultText(_vm.recordInfo.result)
      : null
  var g2 =
    !_vm.loading &&
    !_vm.loadError &&
    _vm.dataLoaded &&
    _vm.recordType === "inspection" &&
    _vm.recordInfo.hasIssues
      ? !_vm.issueDescExpanded && _vm.recordInfo.issueDescription.length > 100
      : null
  var g3 =
    !_vm.loading &&
    !_vm.loadError &&
    _vm.dataLoaded &&
    _vm.recordType === "inspection" &&
    _vm.recordInfo.hasIssues
      ? _vm.recordInfo.issueDescription.length
      : null
  var g4 =
    !_vm.loading &&
    !_vm.loadError &&
    _vm.dataLoaded &&
    _vm.recordType === "inspection" &&
    !_vm.recordInfo.hasIssues &&
    _vm.recordInfo.rating > 0
      ? _vm.getRatingDescription(_vm.recordInfo.rating).toLowerCase()
      : null
  var m4 =
    !_vm.loading &&
    !_vm.loadError &&
    _vm.dataLoaded &&
    _vm.recordType === "inspection" &&
    _vm.recordInfo.remediationTask
      ? _vm.getTaskStatusIcon(_vm.recordInfo.remediationTask.status)
      : null
  var m5 =
    !_vm.loading &&
    !_vm.loadError &&
    _vm.dataLoaded &&
    _vm.recordType === "inspection" &&
    _vm.recordInfo.remediationTask
      ? _vm.getTaskStatusText(_vm.recordInfo.remediationTask.status)
      : null
  var g5 =
    !_vm.loading &&
    !_vm.loadError &&
    _vm.dataLoaded &&
    _vm.recordType === "inspection" &&
    _vm.recordInfo.remediationTask
      ? _vm.recordInfo.remediationTask.completionPhotos &&
        _vm.recordInfo.remediationTask.completionPhotos.length > 0
      : null
  var g6 =
    !_vm.loading &&
    !_vm.loadError &&
    _vm.dataLoaded &&
    _vm.recordType === "inspection" &&
    _vm.recordInfo.remediationTask &&
    g5
      ? _vm.recordInfo.remediationTask.completionPhotos.length
      : null
  var m6 =
    !_vm.loading &&
    !_vm.loadError &&
    _vm.dataLoaded &&
    _vm.recordType === "inspection" &&
    _vm.recordInfo.reviewInfo &&
    _vm.recordInfo.reviewInfo.hasReview &&
    _vm.recordInfo.reviewInfo.reviewRating > 0
      ? _vm.getRatingDescription(_vm.recordInfo.reviewInfo.reviewRating)
      : null
  var g7 =
    !_vm.loading &&
    !_vm.loadError &&
    _vm.dataLoaded &&
    _vm.recordType === "inspection" &&
    _vm.recordInfo.reviewInfo &&
    _vm.recordInfo.reviewInfo.hasReview
      ? _vm.recordInfo.reviewInfo.reviewPhotos &&
        _vm.recordInfo.reviewInfo.reviewPhotos.length > 0
      : null
  var g8 =
    !_vm.loading &&
    !_vm.loadError &&
    _vm.dataLoaded &&
    _vm.recordType === "inspection" &&
    _vm.recordInfo.reviewInfo &&
    _vm.recordInfo.reviewInfo.hasReview &&
    g7
      ? _vm.recordInfo.reviewInfo.reviewPhotos.length
      : null
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      _vm.issueDescExpanded = !_vm.issueDescExpanded
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        m1: m1,
        m2: m2,
        g0: g0,
        g1: g1,
        m3: m3,
        g2: g2,
        g3: g3,
        g4: g4,
        m4: m4,
        m5: m5,
        g5: g5,
        g6: g6,
        m6: m6,
        g7: g7,
        g8: g8,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 561:
/*!***********************************************************************!*\
  !*** D:/Xwzc/pages/6s_pkg/record-detail.vue?vue&type=script&lang=js& ***!
  \***********************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_record_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record-detail.vue?vue&type=script&lang=js& */ 562);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_record_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_record_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_record_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_record_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_record_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 562:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/6s_pkg/record-detail.vue?vue&type=script&lang=js& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _auth = __webpack_require__(/*! @/utils/auth.js */ 91);
var _methods;
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var _default = {
  name: 'RecordDetail',
  data: function data() {
    return {
      recordId: '',
      recordType: 'cleaning',
      // 'cleaning' 或 'inspection'
      loading: false,
      loadError: false,
      dataLoaded: false,
      // 数据是否已加载完成
      editPermission: {
        canEdit: false,
        message: '',
        checking: true
      },
      // 编辑权限
      issueDescExpanded: false,
      // 问题描述是否展开

      // 历史记录相关参数
      isHistoricalRecord: false,
      // 是否为历史记录
      recordWeek: null,
      // 记录所属周次
      cleaningDate: null,
      // 清理日期

      // 性能优化缓存
      processCache: null,
      // 数据处理缓存
      basicDataLoaded: false,
      // 基本数据是否已加载

      recordInfo: {
        id: '',
        areaId: '',
        areaName: '',
        operatorName: '',
        operationTime: '',
        operationTimestamp: '',
        status: '',
        photos: [],
        notes: '',
        // 清理记录特有字段
        weekPeriod: '',
        // 检查记录特有字段
        rating: 0,
        result: '',
        hasIssues: false,
        issueType: '',
        issueDescription: '',
        summary: '',
        remediationTask: null,
        // 操作历史
        history: []
      }
    };
  },
  computed: {
    // 是否显示编辑区域
    showEditSection: function showEditSection() {
      // 只有权限检查完成后才显示编辑区域
      return this.recordType === 'cleaning' && !this.editPermission.checking && (this.editPermission.canEdit || this.editPermission.message);
    },
    // 状态徽章类名
    statusBadgeClass: function statusBadgeClass() {
      return {
        'status-completed': this.recordInfo.status === 'completed' || this.recordInfo.status === 'verified' || this.recordInfo.status === 'reviewed',
        'status-pending': this.recordInfo.status === 'pending',
        'status-pending-rectification': this.recordInfo.status === 'pending_rectification' || this.recordInfo.status === 'rectification_completed',
        'status-cancelled': this.recordInfo.status === 'cancelled'
      };
    },
    // 检查结果徽章类名
    resultBadgeClass: function resultBadgeClass() {
      if (this.recordInfo.result === 'passed') {
        return 'badge-passed';
      } else if (this.recordInfo.result === 'issues') {
        return 'badge-issues';
      }
      return '';
    },
    // 整改任务徽章类名
    taskBadgeClass: function taskBadgeClass() {
      if (!this.recordInfo.remediationTask) return '';
      var status = this.recordInfo.remediationTask.status;
      if (status === 'pending_assignment') {
        return 'task-badge-pending';
      } else if (status === 'pending_rectification') {
        return 'task-badge-pending-rectification';
      } else if (status === 'in_progress') {
        return 'task-badge-pending';
      } else if (status === 'pending_review') {
        return 'task-badge-pending';
      } else if (status === 'verified') {
        return 'task-badge-completed';
      } else if (status === 'rejected') {
        return 'task-badge-missed';
      } else if (status === 'overdue') {
        return 'task-badge-missed';
      }
      return 'task-badge-pending';
    },
    // 任务状态值类名
    taskStatusValueClass: function taskStatusValueClass() {
      if (!this.recordInfo.remediationTask) return '';
      var status = this.recordInfo.remediationTask.status;
      if (status === 'not_cleaned') {
        return 'task-status-not-cleaned';
      } else if (status === 'pending') {
        return 'task-status-pending';
      } else if (status === 'pending_rectification') {
        return 'task-status-pending-rectification';
      } else if (status === 'completed') {
        return 'task-status-completed';
      } else if (status === 'missed') {
        return 'task-status-missed';
      }
      return '';
    }
  },
  onLoad: function onLoad(options) {
    if (options.id && options.type) {
      this.recordId = options.id;
      this.recordType = options.type;

      // 接收历史记录相关参数
      this.isHistoricalRecord = options.isHistorical === 'true' || false;
      this.recordWeek = options.week || null;
      this.cleaningDate = options.cleaningDate || null;
      this.loadRecordDetail();
    }

    // 监听清理记录更新事件
    uni.$on('cleaningRecordUpdated', this.handleRecordUpdated);
  },
  onUnload: function onUnload() {
    // 移除事件监听
    uni.$off('cleaningRecordUpdated', this.handleRecordUpdated);
  },
  onShow: function onShow() {
    // 页面显示时不需要自动刷新，依赖事件驱动的刷新机制
    // 只有当记录真正被更新时，通过 uni.$emit('cleaningRecordUpdated') 事件来刷新
  },
  methods: (_methods = {
    // 获取页面标题
    getPageTitle: function getPageTitle() {
      return this.recordType === 'cleaning' ? '清理记录详情' : '检查记录详情';
    },
    // 加载记录详情 - 性能优化版本
    loadRecordDetail: function loadRecordDetail() {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                _this.loadError = false;
                _this.loading = true;

                // 初始化处理缓存
                _this.initProcessCache();
                if (!(_this.recordType === 'cleaning')) {
                  _context.next = 9;
                  break;
                }
                _context.next = 7;
                return _this.loadCleaningRecordDetailOptimized();
              case 7:
                _context.next = 11;
                break;
              case 9:
                _context.next = 11;
                return _this.loadInspectionRecordDetailOptimized();
              case 11:
                _this.dataLoaded = true;
                _context.next = 17;
                break;
              case 14:
                _context.prev = 14;
                _context.t0 = _context["catch"](0);
                _this.loadError = true;
              case 17:
                _context.prev = 17;
                _this.loading = false;
                return _context.finish(17);
              case 20:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[0, 14, 17, 20]]);
      }))();
    },
    // 初始化数据处理缓存
    initProcessCache: function initProcessCache() {
      if (!this.processCache) {
        this.processCache = {
          // 问题类型映射缓存
          categoryMap: {
            'equipment': '设备问题',
            'cleanliness': '清洁问题',
            'organization': '整理问题',
            'safety': '安全问题',
            'environment': '环境问题',
            'standardization': '标识问题',
            'other': '其他问题'
          },
          // 状态映射缓存
          statusMaps: {
            record: {
              'completed': '已完成',
              'pending_rectification': '待整改',
              'rectification_completed': '整改已提交',
              'verified': '已确认',
              'reviewed': '已审核',
              'pending': '待处理',
              'cancelled': '已取消'
            },
            result: {
              'passed': '检查通过',
              'issues': '发现问题'
            },
            task: {
              'pending_assignment': '待分配',
              'pending_rectification': '待整改',
              'in_progress': '整改中',
              'pending_review': '待复查',
              'completed': '已完成',
              'verified': '已确认',
              'rejected': '已拒绝',
              'overdue': '已逾期'
            },
            taskIcon: {
              'pending_assignment': 'info',
              'pending_rectification': 'flag',
              'in_progress': 'reload',
              'pending_review': 'eye',
              'completed': 'checkmarkempty',
              'verified': 'checkmarkempty',
              'rejected': 'close',
              'overdue': 'close'
            }
          },
          // 常用日期格式化函数
          formatters: {
            dateTime: this.createDateTimeFormatter(),
            date: this.createDateFormatter()
          }
        };
      }
    },
    // 创建优化的日期时间格式化器
    createDateTimeFormatter: function createDateTimeFormatter() {
      return function (dateString) {
        if (!dateString || dateString === 'null' || dateString === 'undefined') return '--';
        try {
          var date = new Date(dateString);
          if (isNaN(date.getTime())) return '--';
          return "".concat(date.getMonth() + 1, "\u6708").concat(date.getDate(), "\u65E5 ").concat(date.getHours().toString().padStart(2, '0'), ":").concat(date.getMinutes().toString().padStart(2, '0'));
        } catch (error) {
          return '--';
        }
      };
    },
    // 创建优化的日期格式化器
    createDateFormatter: function createDateFormatter() {
      return function (dateString) {
        if (!dateString || dateString === 'null' || dateString === 'undefined') return '--';
        try {
          var date = new Date(dateString);
          if (isNaN(date.getTime())) return '--';
          return "".concat(date.getMonth() + 1, "\u6708").concat(date.getDate(), "\u65E5");
        } catch (error) {
          return '--';
        }
      };
    },
    // 优化的清理记录详情加载
    loadCleaningRecordDetailOptimized: function loadCleaningRecordDetailOptimized() {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var result, record, dateTimeFormatter, cleanDate, weekPeriod;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.next = 2;
                return (0, _auth.callCloudFunction)('hygiene-cleaning', {
                  action: 'getCleaningRecordDetail',
                  data: {
                    record_id: _this2.recordId
                  }
                });
              case 2:
                result = _context2.sent;
                if (!(result && result.success && result.data)) {
                  _context2.next = 12;
                  break;
                }
                record = result.data; // 使用缓存的格式化器
                dateTimeFormatter = _this2.processCache.formatters.dateTime; // 快速计算周期信息（避免复杂的日期计算）
                cleanDate = new Date(record.cleaning_date);
                weekPeriod = _this2.calculateWeekPeriodOptimized(cleanDate); // 直接构建基本记录信息
                _this2.recordInfo = {
                  id: record._id,
                  areaId: record.area_id,
                  areaName: record.area_name || '未知责任区',
                  operatorName: record.cleaner_name || record.user_name || '未知',
                  operationTime: dateTimeFormatter(record.cleaning_date),
                  operationTimestamp: record.cleaning_date,
                  weekPeriod: weekPeriod,
                  status: 'completed',
                  photos: _this2.processPhotos(record.photos || []),
                  notes: record.remark || '',
                  history: [{
                    type: 'create',
                    title: '提交清理记录',
                    description: "".concat(record.cleaner_name || record.user_name || '用户', "\u63D0\u4EA4\u4E86\u6E05\u7406\u8BB0\u5F55\uFF0C\u5305\u542B").concat((record.photos || []).length, "\u5F20\u7167\u7247"),
                    time: dateTimeFormatter(record.cleaning_date)
                  }]
                };

                // 异步检查编辑权限，不阻塞页面显示
                _this2.checkEditPermissionAsync();
                _context2.next = 13;
                break;
              case 12:
                throw new Error('获取清理记录详情失败');
              case 13:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2);
      }))();
    },
    // 优化的周期计算
    calculateWeekPeriodOptimized: function calculateWeekPeriodOptimized(date) {
      var year = date.getFullYear();
      var weekNum = Math.ceil((date - new Date(year, 0, 1)) / (7 * 24 * 60 * 60 * 1000));
      var weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay() + 1);
      var weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);
      return "\u7B2C".concat(weekNum, "\u5468 (").concat(weekStart.getMonth() + 1, "\u6708").concat(weekStart.getDate(), "\u65E5-").concat(weekEnd.getMonth() + 1, "\u6708").concat(weekEnd.getDate(), "\u65E5)");
    },
    // 优化的照片处理
    processPhotos: function processPhotos(photos) {
      return photos.map(function (photo) {
        return {
          url: photo.url || photo,
          name: photo.name || '清理照片',
          loadError: false
        };
      });
    },
    // 异步检查编辑权限
    checkEditPermissionAsync: function checkEditPermissionAsync() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                _context3.next = 3;
                return _this3.checkEditPermission();
              case 3:
                _context3.next = 7;
                break;
              case 5:
                _context3.prev = 5;
                _context3.t0 = _context3["catch"](0);
              case 7:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[0, 5]]);
      }))();
    },
    // 加载清理记录详情 - 保留原方法作为备用
    loadCleaningRecordDetail: function loadCleaningRecordDetail() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var result, record, cleanDate, weekNum, weekStart, weekEnd, weekPeriod;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                _context4.next = 2;
                return (0, _auth.callCloudFunction)('hygiene-cleaning', {
                  action: 'getCleaningRecordDetail',
                  data: {
                    record_id: _this4.recordId
                  }
                });
              case 2:
                result = _context4.sent;
                if (!(result && result.success && result.data)) {
                  _context4.next = 15;
                  break;
                }
                record = result.data;
                cleanDate = new Date(record.cleaning_date); // 计算周期信息
                weekNum = _this4.getWeekNumber(cleanDate);
                weekStart = _this4.getWeekStart(cleanDate);
                weekEnd = _this4.getWeekEnd(cleanDate);
                weekPeriod = "\u7B2C".concat(weekNum, "\u5468 (").concat(weekStart.getMonth() + 1, "\u6708").concat(weekStart.getDate(), "\u65E5-").concat(weekEnd.getMonth() + 1, "\u6708").concat(weekEnd.getDate(), "\u65E5)");
                _this4.recordInfo = {
                  id: record._id,
                  areaId: record.area_id,
                  // 用于权限检查
                  areaName: record.area_name || '未知责任区',
                  operatorName: record.cleaner_name || record.user_name || '未知',
                  operationTime: _this4.formatDateTime(record.cleaning_date),
                  operationTimestamp: record.cleaning_date,
                  // 保存原始时间戳用于API调用
                  weekPeriod: weekPeriod,
                  status: 'completed',
                  photos: (record.photos || []).map(function (photo) {
                    return {
                      url: photo.url || photo,
                      name: photo.name || '清理照片',
                      loadError: false
                    };
                  }),
                  notes: record.remark || '',
                  history: [{
                    type: 'create',
                    title: '提交清理记录',
                    description: "".concat(record.cleaner_name || record.user_name || '用户', "\u63D0\u4EA4\u4E86\u6E05\u7406\u8BB0\u5F55\uFF0C\u5305\u542B").concat((record.photos || []).length, "\u5F20\u7167\u7247"),
                    time: _this4.formatDateTime(record.cleaning_date)
                  }]
                };

                // 加载完记录详情后检查编辑权限
                _context4.next = 13;
                return _this4.checkEditPermission();
              case 13:
                _context4.next = 16;
                break;
              case 15:
                throw new Error('获取清理记录详情失败');
              case 16:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4);
      }))();
    },
    // 优化的检查记录详情加载
    loadInspectionRecordDetailOptimized: function loadInspectionRecordDetailOptimized() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var result, record, dateTimeFormatter, categoryMap, _this5$processIssueIn, issueType, issueDescription, _this5$processRectifi, finalRating, remediationTask, reviewInfo;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _context5.next = 2;
                return (0, _auth.callCloudFunction)('hygiene-inspection', {
                  action: 'getInspectionDetail',
                  data: {
                    id: _this5.recordId
                  }
                });
              case 2:
                result = _context5.sent;
                if (!(result && result.success && result.data)) {
                  _context5.next = 13;
                  break;
                }
                record = result.data; // 使用缓存的数据处理
                dateTimeFormatter = _this5.processCache.formatters.dateTime;
                categoryMap = _this5.processCache.categoryMap; // 快速处理问题信息
                _this5$processIssueIn = _this5.processIssueInfo(record, categoryMap), issueType = _this5$processIssueIn.issueType, issueDescription = _this5$processIssueIn.issueDescription; // 处理评分和整改任务
                _this5$processRectifi = _this5.processRectificationInfo(record, dateTimeFormatter), finalRating = _this5$processRectifi.finalRating, remediationTask = _this5$processRectifi.remediationTask; // 处理复查信息
                reviewInfo = _this5.processReviewInfo(record, remediationTask, dateTimeFormatter); // 直接构建记录信息
                _this5.recordInfo = {
                  id: record._id,
                  areaName: record.area_name || '未知责任区',
                  operatorName: record.inspector_name || '未知',
                  operationTime: dateTimeFormatter(record.inspection_date),
                  operationTimestamp: record.inspection_date,
                  status: record.status || 'completed',
                  rating: finalRating,
                  result: record.has_issues ? 'issues' : 'passed',
                  hasIssues: record.has_issues || false,
                  issueType: issueType,
                  issueDescription: issueDescription,
                  summary: record.summary || '',
                  photos: _this5.processInspectionPhotos(record.photos || []),
                  remediationTask: remediationTask,
                  // 新增复查信息
                  reviewInfo: reviewInfo,
                  history: _this5.buildHistoryTimelineOptimized(record, remediationTask, dateTimeFormatter)
                };
                _context5.next = 14;
                break;
              case 13:
                throw new Error('获取检查记录详情失败');
              case 14:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5);
      }))();
    },
    // 优化的问题信息处理
    processIssueInfo: function processIssueInfo(record, categoryMap) {
      var issueType = '';
      var issueDescription = '';
      if (record.has_issues && record.issues && record.issues.length > 0) {
        var firstIssue = record.issues[0];
        issueType = categoryMap[firstIssue.category] || firstIssue.category || '其他问题';
        issueDescription = firstIssue.description || '';
      }
      return {
        issueType: issueType,
        issueDescription: issueDescription
      };
    },
    // 优化的整改信息处理
    processRectificationInfo: function processRectificationInfo(record, dateTimeFormatter) {
      // 基本信息中的评分：只显示原始检查评分，不被复查评分覆盖
      var finalRating = record.overall_rating || record.score || 0;
      var remediationTask = null;
      if (record.has_issues && record.related_rectifications && record.related_rectifications.length > 0) {
        var relatedRectification = record.related_rectifications.find(function (rect) {
          return rect.inspection_record_id === record._id || rect.area_id === record.area_id && new Date(rect.created_at) >= new Date(record.inspection_date);
        });
        if (relatedRectification) {
          // 注意：不再覆盖 finalRating，保持原始检查评分
          // 复查评分将在 reviewInfo 中单独处理

          remediationTask = {
            id: relatedRectification._id,
            status: relatedRectification.status,
            assignee: relatedRectification.assigned_to_name || '未分配',
            deadline: dateTimeFormatter(relatedRectification.deadline),
            completionTime: relatedRectification.submitted_at ? dateTimeFormatter(relatedRectification.submitted_at) : relatedRectification.completion_date ? dateTimeFormatter(relatedRectification.completion_date) : null,
            completionNotes: relatedRectification.completion_description || '',
            completionPhotos: this.processRectificationPhotos(relatedRectification.completion_photos || [])
          };
        }
      }
      return {
        finalRating: finalRating,
        remediationTask: remediationTask
      };
    },
    // 处理复查信息
    processReviewInfo: function processReviewInfo(record, remediationTask, dateTimeFormatter) {
      var reviewInfo = null;
      if (record.has_issues && record.related_rectifications && record.related_rectifications.length > 0) {
        var rectification = record.related_rectifications[0];
        if (rectification.review_date && rectification.status === 'verified') {
          reviewInfo = {
            hasReview: true,
            reviewerName: rectification.reviewer_name || '未知',
            reviewTime: dateTimeFormatter(rectification.review_date),
            reviewComments: rectification.review_comments || '',
            reviewRating: rectification.final_rating || rectification.review_rating || 0,
            reviewResult: rectification.review_result || 'approved',
            reviewPhotos: this.processRectificationPhotos(rectification.review_photos || [])
          };
        }
      }
      return reviewInfo;
    },
    // 优化的检查照片处理
    processInspectionPhotos: function processInspectionPhotos(photos) {
      return photos.map(function (photo) {
        if (typeof photo === 'string') {
          return {
            url: photo,
            name: '检查照片',
            loadError: false
          };
        } else if ((0, _typeof2.default)(photo) === 'object' && photo !== null) {
          return {
            url: photo.url || photo.src || '',
            name: photo.name || photo.description || '检查照片',
            loadError: false
          };
        }
        return {
          url: '',
          name: '检查照片',
          loadError: true
        };
      }).filter(function (photo) {
        return photo.url;
      });
    },
    // 处理整改照片
    processRectificationPhotos: function processRectificationPhotos(photos) {
      if (!Array.isArray(photos)) return [];
      return photos.map(function (photo) {
        if (typeof photo === 'string') {
          return {
            url: photo,
            name: '整改照片',
            loadError: false
          };
        } else if ((0, _typeof2.default)(photo) === 'object' && photo !== null) {
          return {
            url: photo.url || photo.src || '',
            name: photo.description || photo.name || '整改照片',
            loadError: false
          };
        }
        return {
          url: '',
          name: '整改照片',
          loadError: true
        };
      }).filter(function (photo) {
        return photo.url;
      });
    },
    // 优化的历史时间线构建
    buildHistoryTimelineOptimized: function buildHistoryTimelineOptimized(record, remediationTask, dateTimeFormatter) {
      var _this6 = this;
      var history = [{
        type: 'create',
        title: '开始检查',
        description: "".concat(record.inspector_name || '检查员', "\u5F00\u59CB\u68C0\u67E5"),
        time: dateTimeFormatter(record.inspection_date)
      }];
      if (record.has_issues) {
        history.push({
          type: 'issue',
          title: '发现问题',
          description: '检查中发现问题，需要进行整改',
          time: dateTimeFormatter(record.inspection_date)
        });
        if (remediationTask) {
          // 1. 创建整改任务
          history.push({
            type: 'task',
            title: '创建整改任务',
            description: "\u5DF2\u5206\u914D\u7ED9 ".concat(remediationTask.assignee, " \u8FDB\u884C\u6574\u6539"),
            time: dateTimeFormatter(record.inspection_date)
          });

          // 2. 整改过程记录（基于整改任务的详细状态）
          if (record.related_rectifications && record.related_rectifications.length > 0) {
            var rectification = record.related_rectifications[0];
            this.buildRectificationHistory(history, rectification, dateTimeFormatter);
          } else if (remediationTask.completionTime && remediationTask.completionTime !== '--') {
            // 兼容旧数据格式
            this.buildSimpleRectificationHistory(history, remediationTask);
          }
        }
      } else {
        // 检查通过，无问题发现
        var completionDescription = record.overall_rating > 0 ? "\u68C0\u67E5\u5B8C\u6210\uFF0C\u8BC4\u5206".concat(record.overall_rating, "\u5206\uFF0C").concat(this.getRatingDescription(record.overall_rating)) : '检查完成，责任区状况良好，无发现问题';
        history.push({
          type: 'complete',
          title: '检查通过',
          description: completionDescription,
          time: dateTimeFormatter(record.inspection_date)
        });
      }
      return history.sort(function (a, b) {
        var timeA = _this6.parseTimeForSort(a.time);
        var timeB = _this6.parseTimeForSort(b.time);
        return timeA - timeB;
      });
    },
    // 构建详细的整改历史记录
    buildRectificationHistory: function buildRectificationHistory(history, rectification, dateTimeFormatter) {
      // 整改开始
      if (rectification.created_at) {
        history.push({
          type: 'task',
          title: '开始整改',
          description: "".concat(rectification.assigned_to_name || '负责人', "\u5F00\u59CB\u5904\u7406\u6574\u6539\u4EFB\u52A1"),
          time: dateTimeFormatter(rectification.created_at)
        });
      }

      // 整改提交
      if (rectification.submitted_at) {
        history.push({
          type: 'review',
          title: '提交整改',
          description: "\u6574\u6539\u5DF2\u5B8C\u6210\uFF0C\u63D0\u4EA4\u68C0\u67E5\u5458\u590D\u67E5",
          time: dateTimeFormatter(rectification.submitted_at)
        });
      }

      // 复查结果
      if (rectification.review_date) {
        var isApproved = rectification.status === 'verified';
        history.push({
          type: isApproved ? 'complete' : 'issue',
          title: isApproved ? '复查通过' : '需重新整改',
          description: isApproved ? "\u68C0\u67E5\u5458\u786E\u8BA4\u6574\u6539\u5408\u683C\uFF0C\u95EE\u9898\u5DF2\u89E3\u51B3" : "\u68C0\u67E5\u5458\u8981\u6C42\u91CD\u65B0\u6574\u6539\uFF1A".concat(rectification.review_comments || '整改不达标'),
          time: dateTimeFormatter(rectification.review_date)
        });
      }

      // 如果有多轮整改，递归处理
      if (rectification.status === 'rejected' && rectification.resubmitted_at) {
        history.push({
          type: 'task',
          title: '重新整改',
          description: "\u6839\u636E\u68C0\u67E5\u5458\u8981\u6C42\u91CD\u65B0\u8FDB\u884C\u6574\u6539",
          time: dateTimeFormatter(rectification.resubmitted_at)
        });
      }
    },
    // 构建简化的整改历史（兼容旧数据）
    buildSimpleRectificationHistory: function buildSimpleRectificationHistory(history, remediationTask) {
      var eventType = remediationTask.status === 'verified' ? 'complete' : remediationTask.status === 'rejected' ? 'issue' : 'review';
      var eventTitle = remediationTask.status === 'verified' ? '整改完成' : remediationTask.status === 'rejected' ? '整改被拒绝' : '整改提交';
      history.push({
        type: eventType,
        title: eventTitle,
        description: remediationTask.status === 'verified' ? '整改任务已完成并通过检查员确认' : remediationTask.status === 'rejected' ? '整改未通过复查，需要重新整改' : '整改任务已完成，等待检查员复查',
        time: remediationTask.completionTime
      });
    },
    // 解析时间用于排序
    parseTimeForSort: function parseTimeForSort(timeStr) {
      try {
        var match = timeStr.match(/(\d+)月(\d+)日\s(\d+):(\d+)/);
        if (match) {
          var year = new Date().getFullYear();
          var month = parseInt(match[1]);
          var day = parseInt(match[2]);
          var hour = parseInt(match[3]);
          var minute = parseInt(match[4]);
          return new Date(year, month - 1, day, hour, minute);
        }
        return new Date();
      } catch (error) {
        return new Date();
      }
    },
    // 加载检查记录详情 - 保留原方法作为备用
    loadInspectionRecordDetail: function loadInspectionRecordDetail() {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var result, record, issueType, issueDescription, firstIssue, categoryMap, finalRating, remediationTask, relatedRectification;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                _context6.next = 2;
                return (0, _auth.callCloudFunction)('hygiene-inspection', {
                  action: 'getInspectionDetail',
                  data: {
                    id: _this7.recordId // 修复：使用正确的参数名
                  }
                });
              case 2:
                result = _context6.sent;
                if (!(result && result.success && result.data)) {
                  _context6.next = 16;
                  break;
                }
                record = result.data; // 处理问题信息
                issueType = '';
                issueDescription = '';
                if (record.has_issues && record.issues && record.issues.length > 0) {
                  firstIssue = record.issues[0]; // 中文问题类型映射
                  categoryMap = {
                    'equipment': '设备问题',
                    'cleanliness': '清洁问题',
                    'organization': '整理问题',
                    'safety': '安全问题',
                    'environment': '环境问题',
                    'standardization': '标识问题',
                    'other': '其他问题'
                  };
                  issueType = categoryMap[firstIssue.category] || firstIssue.category || '其他问题';
                  issueDescription = firstIssue.description || '';
                }

                // 处理评分：保持原始检查评分，不被复查评分覆盖
                finalRating = record.overall_rating || record.score || 0; // 处理整改任务信息
                remediationTask = null;
                if (record.has_issues && record.related_rectifications && record.related_rectifications.length > 0) {
                  // 查找与当前检查记录相关的整改记录
                  relatedRectification = record.related_rectifications.find(function (rect) {
                    return rect.inspection_record_id === record._id || rect.area_id === record.area_id && new Date(rect.created_at) >= new Date(record.inspection_date);
                  });
                  if (relatedRectification) {
                    // 注意：不再覆盖 finalRating，保持原始检查评分
                    // 复查评分将在 reviewInfo 中单独处理

                    // 构建整改任务信息
                    remediationTask = {
                      id: relatedRectification._id,
                      status: relatedRectification.status,
                      assignee: relatedRectification.assigned_to_name || '未分配',
                      deadline: _this7.formatDateTime(relatedRectification.deadline),
                      // 优先使用 submitted_at，再使用 completion_date
                      completionTime: relatedRectification.submitted_at ? _this7.formatDateTime(relatedRectification.submitted_at) : relatedRectification.completion_date ? _this7.formatDateTime(relatedRectification.completion_date) : null,
                      completionNotes: relatedRectification.completion_description || ''
                    };
                  }
                }
                _this7.recordInfo = {
                  id: record._id,
                  areaName: record.area_name || '未知责任区',
                  operatorName: record.inspector_name || '未知',
                  operationTime: _this7.formatDateTime(record.inspection_date),
                  operationTimestamp: record.inspection_date,
                  // 保存原始时间戳用于API调用
                  status: record.status || 'completed',
                  rating: finalRating,
                  // 使用处理后的最终评分
                  result: record.has_issues ? 'issues' : 'passed',
                  hasIssues: record.has_issues || false,
                  issueType: issueType,
                  issueDescription: issueDescription,
                  summary: record.summary || '',
                  photos: (record.photos || []).map(function (photo) {
                    // 处理不同的照片数据格式
                    if (typeof photo === 'string') {
                      return {
                        url: photo,
                        name: '检查照片',
                        loadError: false
                      };
                    } else if ((0, _typeof2.default)(photo) === 'object' && photo !== null) {
                      return {
                        url: photo.url || photo.src || '',
                        name: photo.name || photo.description || '检查照片',
                        loadError: false
                      };
                    }
                    return {
                      url: '',
                      name: '检查照片',
                      loadError: true
                    };
                  }).filter(function (photo) {
                    return photo.url;
                  }),
                  // 过滤掉空URL的照片
                  remediationTask: remediationTask,
                  history: [{
                    type: 'create',
                    title: '开始检查',
                    description: "".concat(record.inspector_name || '检查员', "\u5F00\u59CB\u68C0\u67E5"),
                    time: _this7.formatDateTime(record.inspection_date)
                  }]
                };

                // 如果有问题，添加问题发现历史
                if (record.has_issues) {
                  _this7.recordInfo.history.push({
                    type: 'issue',
                    title: '发现问题',
                    description: issueDescription || '检查中发现问题',
                    time: _this7.formatDateTime(record.inspection_date)
                  });

                  // 添加整改相关的历史记录
                  if (remediationTask) {
                    // 添加整改任务创建记录
                    _this7.recordInfo.history.push({
                      type: 'task',
                      title: '创建整改任务',
                      description: "\u5DF2\u5206\u914D\u7ED9 ".concat(remediationTask.assignee, "\u6574\u6539"),
                      time: _this7.formatDateTime(record.inspection_date)
                    });

                    // 根据整改状态添加相应的历史记录
                    if (remediationTask.completionTime && remediationTask.completionTime !== '--' && remediationTask.completionTime !== 'null') {
                      if (remediationTask.status === 'pending_review') {
                        _this7.recordInfo.history.push({
                          type: 'complete',
                          title: '整改提交',
                          description: "\u6574\u6539\u4EFB\u52A1\u5DF2\u5B8C\u6210\u5E76\u63D0\u4EA4\u590D\u67E5",
                          time: remediationTask.completionTime
                        });
                      } else if (remediationTask.status === 'verified') {
                        _this7.recordInfo.history.push({
                          type: 'complete',
                          title: '整改完成',
                          description: "\u6574\u6539\u4EFB\u52A1\u5DF2\u5B8C\u6210\u5E76\u901A\u8FC7\u786E\u8BA4",
                          time: remediationTask.completionTime
                        });
                      } else if (remediationTask.status === 'rejected') {
                        _this7.recordInfo.history.push({
                          type: 'issue',
                          title: '整改被拒绝',
                          description: "\u6574\u6539\u672A\u901A\u8FC7\u590D\u67E5\uFF0C\u9700\u8981\u91CD\u65B0\u6574\u6539",
                          time: remediationTask.completionTime
                        });
                      }
                    }
                  }
                }

                // 按时间排序操作历史 - 使用iOS兼容的日期格式
                _this7.recordInfo.history.sort(function (a, b) {
                  // 解析中文时间格式 "8月12日 16:23" 为iOS兼容的日期
                  var parseChineseTime = function parseChineseTime(timeStr) {
                    var match = timeStr.match(/(\d+)月(\d+)日\s(\d+):(\d+)/);
                    if (match) {
                      var year = new Date().getFullYear();
                      var month = parseInt(match[1]);
                      var day = parseInt(match[2]);
                      var hour = parseInt(match[3]);
                      var minute = parseInt(match[4]);
                      // 使用 Date 构造函数的数字参数，避免字符串解析问题
                      return new Date(year, month - 1, day, hour, minute);
                    }
                    // 如果无法解析，返回当前时间
                    return new Date();
                  };
                  var timeA = parseChineseTime(a.time);
                  var timeB = parseChineseTime(b.time);
                  return timeA - timeB;
                });
                _context6.next = 17;
                break;
              case 16:
                throw new Error('获取检查记录详情失败');
              case 17:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6);
      }))();
    },
    // 获取周数
    getWeekNumber: function getWeekNumber(date) {
      var onejan = new Date(date.getFullYear(), 0, 1);
      var millisecsInDay = 86400000;
      return Math.ceil(((date.getTime() - onejan.getTime()) / millisecsInDay + onejan.getDay() + 1) / 7);
    },
    // 获取一周的开始日期（周一）
    getWeekStart: function getWeekStart(date) {
      var d = new Date(date);
      var day = d.getDay();
      var diff = d.getDate() - day + (day === 0 ? -6 : 1);
      var result = new Date(d.setDate(diff));
      result.setHours(0, 0, 0, 0);
      return result;
    },
    // 获取一周的结束日期（周日）
    getWeekEnd: function getWeekEnd(date) {
      var d = new Date(date);
      var day = d.getDay();
      var diff = d.getDate() - day + (day === 0 ? 0 : 7);
      var result = new Date(d.setDate(diff));
      result.setHours(23, 59, 59, 999);
      return result;
    },
    // 格式化日期时间
    formatDateTime: function formatDateTime(dateString) {
      if (!dateString || dateString === 'null' || dateString === 'undefined') return '--';
      try {
        var date = new Date(dateString);
        if (isNaN(date.getTime())) {
          return '--';
        }
        return "".concat(date.getMonth() + 1, "\u6708").concat(date.getDate(), "\u65E5 ").concat(date.getHours().toString().padStart(2, '0'), ":").concat(date.getMinutes().toString().padStart(2, '0'));
      } catch (error) {
        return '--';
      }
    },
    // 重试加载
    retryLoad: function retryLoad() {
      this.dataLoaded = false; // 重置数据加载标记
      this.loadError = false;
      this.loadRecordDetail();
    },
    // 预览照片
    previewPhoto: function previewPhoto(index) {
      var urls = this.recordInfo.photos.map(function (photo) {
        return photo.url;
      });
      uni.previewImage({
        urls: urls,
        current: index
      });
    },
    // 照片加载重试
    retryPhotoLoad: function retryPhotoLoad(index) {
      this.$set(this.recordInfo.photos[index], 'loadError', false);
    },
    // 照片加载失败处理
    onPhotoError: function onPhotoError(index) {
      this.$set(this.recordInfo.photos[index], 'loadError', true);
    },
    // 检查编辑权限
    checkEditPermission: function checkEditPermission() {
      var _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        var timeCheckResult, areaId, cleaningRecordTime, hasInspectionAfterCleaning, inspectionResult, records, relatedInspections, rectificationResult, hasActiveTasks, recordTime, activeTasks;
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                if (!(_this8.recordType !== 'cleaning')) {
                  _context7.next = 3;
                  break;
                }
                _this8.editPermission = {
                  canEdit: false,
                  message: '',
                  checking: false
                };
                return _context7.abrupt("return");
              case 3:
                if (!_this8.isHistoricalRecord) {
                  _context7.next = 6;
                  break;
                }
                _this8.editPermission = {
                  canEdit: false,
                  message: '历史记录不允许修改',
                  checking: false
                };
                return _context7.abrupt("return");
              case 6:
                // 2. 检查记录时效性（基于清理日期）
                timeCheckResult = _this8.checkRecordTimeValidity();
                if (timeCheckResult.canEdit) {
                  _context7.next = 10;
                  break;
                }
                _this8.editPermission = _objectSpread(_objectSpread({}, timeCheckResult), {}, {
                  checking: false
                });
                return _context7.abrupt("return");
              case 10:
                _context7.prev = 10;
                // 3. 检查该清理记录是否已经被检查过
                areaId = _this8.recordInfo.areaId;
                if (areaId) {
                  _context7.next = 15;
                  break;
                }
                _this8.editPermission = {
                  canEdit: true,
                  message: '',
                  checking: false
                };
                return _context7.abrupt("return");
              case 15:
                // 3. 关键检查：该清理记录是否已经被检查员检查过
                cleaningRecordTime = new Date(_this8.recordInfo.operationTimestamp); // 查询该责任区在清理记录之后的检查记录
                hasInspectionAfterCleaning = false;
                _context7.prev = 17;
                _context7.next = 20;
                return (0, _auth.callCloudFunction)('hygiene-inspection', {
                  action: 'getInspectionRecords',
                  data: {
                    area_id: areaId,
                    pageSize: 50
                  }
                });
              case 20:
                inspectionResult = _context7.sent;
                if (inspectionResult && inspectionResult.success && inspectionResult.data) {
                  records = inspectionResult.data.list;
                  if (Array.isArray(records)) {
                    // 筛选在清理记录之后的检查记录
                    relatedInspections = records.filter(function (inspection) {
                      var inspectionTime = new Date(inspection.inspection_date);
                      return inspectionTime > cleaningRecordTime;
                    });
                    hasInspectionAfterCleaning = relatedInspections.length > 0;
                  }
                }
                _context7.next = 28;
                break;
              case 24:
                _context7.prev = 24;
                _context7.t0 = _context7["catch"](17);
                console.warn('检查记录查询失败:', _context7.t0);
                // 如果查询失败，为了安全起见，不允许编辑
                hasInspectionAfterCleaning = true;
              case 28:
                if (!hasInspectionAfterCleaning) {
                  _context7.next = 31;
                  break;
                }
                _this8.editPermission = {
                  canEdit: false,
                  message: '该清理记录已被检查员检查，不允许修改',
                  checking: false
                };
                return _context7.abrupt("return");
              case 31:
                _context7.next = 33;
                return (0, _auth.callCloudFunction)('hygiene-rectification', {
                  action: 'getRectifications',
                  data: {
                    area_id: areaId,
                    pageSize: 10
                  }
                });
              case 33:
                rectificationResult = _context7.sent;
                // 检查是否有进行中的整改任务
                hasActiveTasks = false;
                if (rectificationResult && rectificationResult.success && rectificationResult.data && rectificationResult.data.list) {
                  // 使用已定义的清理记录时间
                  recordTime = cleaningRecordTime; // 简化逻辑：主要依赖检查记录判断，整改任务作为辅助判断
                  activeTasks = rectificationResult.data.list.filter(function (task) {
                    var taskCreatedTime = new Date(task.created_at || task.createdAt);
                    var isTaskAfterRecord = taskCreatedTime > recordTime;

                    // 只检查在清理记录之后的未完成整改任务
                    var blockingStatuses = ['pending_rectification',
                    // 待整改
                    'pending_assignment',
                    // 待分配  
                    'in_progress',
                    // 整改中
                    'pending_review',
                    // 待复查
                    'rectification_completed' // 整改已完成（等待验证）
                    ];

                    var isBlocking = isTaskAfterRecord && blockingStatuses.includes(task.status);
                    return isBlocking;
                  });
                  hasActiveTasks = activeTasks.length > 0;
                }
                if (hasActiveTasks) {
                  _this8.editPermission = {
                    canEdit: false,
                    message: '该区域有进行中的整改任务，暂时无法修改清理记录',
                    checking: false
                  };
                } else {
                  _this8.editPermission = {
                    canEdit: true,
                    message: '',
                    checking: false
                  };
                }
                _context7.next = 42;
                break;
              case 39:
                _context7.prev = 39;
                _context7.t1 = _context7["catch"](10);
                // 出错时不允许编辑，避免潜在问题
                _this8.editPermission = {
                  canEdit: false,
                  message: '权限检查失败，暂时无法修改',
                  checking: false
                };
              case 42:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7, null, [[10, 39], [17, 24]]);
      }))();
    },
    // 检查记录时效性
    checkRecordTimeValidity: function checkRecordTimeValidity() {
      if (!this.recordInfo.operationTimestamp) {
        return {
          canEdit: false,
          message: '无法确定记录时间，不允许修改'
        };
      }
      var now = new Date();
      var recordTime = new Date(this.recordInfo.operationTimestamp);

      // 检查是否为本周记录
      var currentWeekStart = this.getWeekStart(now);
      var currentWeekEnd = this.getWeekEnd(now);
      if (recordTime < currentWeekStart || recordTime > currentWeekEnd) {
        return {
          canEdit: false,
          message: '只能修改本周的清理记录'
        };
      }

      // 检查是否在24小时内
      var timeDiff = now.getTime() - recordTime.getTime();
      var hours24 = 24 * 60 * 60 * 1000;
      if (timeDiff > hours24) {
        return {
          canEdit: false,
          message: '记录提交超过24小时，不允许修改'
        };
      }
      return {
        canEdit: true,
        message: ''
      };
    },
    // 获取状态文本 - 使用缓存优化
    getStatusText: function getStatusText(status) {
      var _this$processCache;
      if (!((_this$processCache = this.processCache) !== null && _this$processCache !== void 0 && _this$processCache.statusMaps)) this.initProcessCache();
      return this.processCache.statusMaps.record[status] || '已完成';
    },
    // 获取结果文本 - 使用缓存优化
    getResultText: function getResultText(result) {
      var _this$processCache2;
      if (!((_this$processCache2 = this.processCache) !== null && _this$processCache2 !== void 0 && _this$processCache2.statusMaps)) this.initProcessCache();
      return this.processCache.statusMaps.result[result] || result;
    },
    // 获取任务状态文本 - 使用缓存优化
    getTaskStatusText: function getTaskStatusText(status) {
      var _this$processCache3;
      if (!status) return '未知状态';
      if (!((_this$processCache3 = this.processCache) !== null && _this$processCache3 !== void 0 && _this$processCache3.statusMaps)) this.initProcessCache();
      return this.processCache.statusMaps.task[status] || status;
    },
    // 获取任务状态图标 - 使用缓存优化
    getTaskStatusIcon: function getTaskStatusIcon(status) {
      var _this$processCache4;
      if (!status) return 'info';
      if (!((_this$processCache4 = this.processCache) !== null && _this$processCache4 !== void 0 && _this$processCache4.statusMaps)) this.initProcessCache();
      return this.processCache.statusMaps.taskIcon[status] || 'info';
    },
    // 编辑记录
    editRecord: function editRecord() {
      // 跳转到清理提交页面，传入记录ID用于编辑
      var areaId = this.recordInfo.areaId || this.areaId;
      var areaType = this.recordInfo.areaType || 'fixed';
      uni.navigateTo({
        url: "/pages/6s_pkg/cleaning-upload?mode=edit&recordId=".concat(this.recordId, "&areaId=").concat(areaId, "&type=").concat(areaType)
      });
    },
    // 获取评分描述 - 使用缓存优化
    getRatingDescription: function getRatingDescription(rating) {
      var _this$processCache5;
      if (!((_this$processCache5 = this.processCache) !== null && _this$processCache5 !== void 0 && _this$processCache5.ratingDescCache)) {
        if (!this.processCache) this.initProcessCache();
        this.processCache.ratingDescCache = new Map();
      }

      // 检查缓存
      if (this.processCache.ratingDescCache.has(rating)) {
        return this.processCache.ratingDescCache.get(rating);
      }

      // 计算描述
      var description = '';
      if (rating === 0) description = '请评分';else if (rating <= 1) description = '较差';else if (rating <= 2) description = '一般';else if (rating <= 3) description = '良好';else if (rating < 5) description = '优秀'; // 4-4.5分都是优秀
      else if (rating === 5) description = '完美';

      // 缓存结果
      this.processCache.ratingDescCache.set(rating, description);
      return description;
    },
    // 处理清理记录更新事件
    handleRecordUpdated: function handleRecordUpdated(data) {
      // 如果更新的是当前记录，重新加载数据
      if (data.recordId === this.recordId) {
        this.loadRecordDetail();
      }
    },
    // 预览整改照片
    previewRectificationPhoto: function previewRectificationPhoto(index) {
      var urls = this.recordInfo.remediationTask.completionPhotos.map(function (photo) {
        return photo.url;
      });
      uni.previewImage({
        urls: urls,
        current: index
      });
    },
    // 预览复查照片
    previewReviewPhoto: function previewReviewPhoto(index) {
      var urls = this.recordInfo.reviewInfo.reviewPhotos.map(function (photo) {
        return photo.url;
      });
      uni.previewImage({
        urls: urls,
        current: index
      });
    },
    // 通用照片错误处理方法
    handlePhotoError: function handlePhotoError(photoArray, index) {
      this.$set(photoArray[index], 'loadError', true);
    }
  }, (0, _defineProperty2.default)(_methods, "retryPhotoLoad", function retryPhotoLoad(photoArray, index) {
    this.$set(photoArray[index], 'loadError', false);
  }), (0, _defineProperty2.default)(_methods, "onRectificationPhotoError", function onRectificationPhotoError(index) {
    this.handlePhotoError(this.recordInfo.remediationTask.completionPhotos, index);
  }), (0, _defineProperty2.default)(_methods, "onReviewPhotoError", function onReviewPhotoError(index) {
    this.handlePhotoError(this.recordInfo.reviewInfo.reviewPhotos, index);
  }), (0, _defineProperty2.default)(_methods, "retryRectificationPhotoLoad", function retryRectificationPhotoLoad(index) {
    this.retryPhotoLoad(this.recordInfo.remediationTask.completionPhotos, index);
  }), (0, _defineProperty2.default)(_methods, "retryReviewPhotoLoad", function retryReviewPhotoLoad(index) {
    this.retryPhotoLoad(this.recordInfo.reviewInfo.reviewPhotos, index);
  }), _methods)
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 563:
/*!********************************************************************************************************!*\
  !*** D:/Xwzc/pages/6s_pkg/record-detail.vue?vue&type=style&index=0&id=9f2cd4fe&lang=scss&scoped=true& ***!
  \********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_record_detail_vue_vue_type_style_index_0_id_9f2cd4fe_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record-detail.vue?vue&type=style&index=0&id=9f2cd4fe&lang=scss&scoped=true& */ 564);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_record_detail_vue_vue_type_style_index_0_id_9f2cd4fe_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_record_detail_vue_vue_type_style_index_0_id_9f2cd4fe_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_record_detail_vue_vue_type_style_index_0_id_9f2cd4fe_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_record_detail_vue_vue_type_style_index_0_id_9f2cd4fe_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_record_detail_vue_vue_type_style_index_0_id_9f2cd4fe_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 564:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/6s_pkg/record-detail.vue?vue&type=style&index=0&id=9f2cd4fe&lang=scss&scoped=true& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[557,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/6s_pkg/record-detail.js.map