{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s_pkg/cleaning-upload.vue?5892", "webpack:///D:/Xwzc/pages/6s_pkg/cleaning-upload.vue?bd14", "webpack:///D:/Xwzc/pages/6s_pkg/cleaning-upload.vue?597d", "webpack:///D:/Xwzc/pages/6s_pkg/cleaning-upload.vue?1183", "uni-app:///pages/6s_pkg/cleaning-upload.vue", "webpack:///D:/Xwzc/pages/6s_pkg/cleaning-upload.vue?c022", "webpack:///D:/Xwzc/pages/6s_pkg/cleaning-upload.vue?dc92"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "mode", "recordId", "areaInfo", "photos", "remarks", "loading", "uploading", "uploadProgress", "autoUpload", "uploadQueue", "processCache", "areaTypeMap", "photoUrlCache", "computed", "<PERSON><PERSON><PERSON><PERSON>", "onLoad", "methods", "initProcessCache", "handleRemarksInput", "value", "uni", "title", "icon", "duration", "loadAreaInfoOptimized", "areaId", "setTimeout", "HygieneAPI", "areaDetail", "type", "handleLoadError", "errorMessage", "content", "showCancel", "cancelText", "confirmText", "success", "retryCallback", "loadAreaInfo", "loadRecordDataOptimized", "uniCloud", "action", "record_id", "result", "record", "processRecordData", "url", "uploaded", "cloudUrl", "cloudPath", "belongsToRecord", "processPhotoUrl", "loadRecordData", "addPhoto", "count", "sizeType", "sourceType", "fail", "processNewPhotos", "deletePhoto", "index", "photo", "fileList", "extractFileId", "previewPhoto", "urls", "current", "autoUploadNewPhotosOptimized", "uploadPromises", "photoIndex", "uploadResult", "Object", "error", "Promise", "results", "failures", "filter", "map", "autoUploadNewPhotos", "uploadSinglePhotoOptimized", "uploadUtils", "fileInfo", "size", "generateCloudPath", "uploadSinglePhoto", "toggleAutoUpload", "uploadPhotosOptimized", "unuploadedPhotos", "description", "photoPaths", "onProgress", "pathGenerator", "maxConcurrent", "uploadPhotos", "submitCleaning", "uploadedPhotos", "submissionData", "preparePhotosForSubmit", "prepareSubmissionData", "finalRemarks", "console", "remark", "baseData", "area_id", "cleaning_date", "completion_status", "handleSubmitSuccess", "handleSubmitError", "getAreaTypeText", "getPhotoDisplayUrl"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAAymB,CAAgB,moBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACmF7nB;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;QACAC;QACAC;MACA;IACA;EACA;;EACAC;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;;IAEA;IACA;MACA;IACA;;IAEA;IACA;;IAEA;IACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;QACAC;QACA;QACAC;UACAC;UACAC;UACAC;QACA;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACAC;kBAAA;kBAAA;gBAAA;gBACAL;kBACAC;kBACAC;gBACA;gBACAI;kBACAN;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKAO;cAAA;gBAAAC;gBACA;gBACA,iDACAA;kBACAC;gBAAA,EACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;kBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;MACA;QACAC;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;MAEAX;QACAC;QACAW;QACAC;QACAC;QACAC;QACAC;UACA;YACAC;UACA;YACAjB;UACA;QACA;MACA;IACA;IAEA;IACAkB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAKAnB;kBAAAC;gBAAA;gBAAA;gBAAA,OAEAmB;kBACA1C;kBACAC;oBACA0C;oBACA1C;sBAAA2C;oBAAA;kBACA;gBACA;cAAA;gBANAC;gBAAA,MAQA;kBAAA;kBAAA;gBAAA;gBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;kBAAA;gBAAA;cAAA;gBAAA;gBAEAxB;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAyB;MAAA;QAAA;MACA;MACA;;MAEA;MACA;QACA;UACA;YACAC;YACAC;YACAC;YACAC;YACA3C;YACA4C;UACA;;UAEA;UACA;UACA;YACA;UACA;UAEA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAIA;IACAC;MAAA;MACA;QACAjC;UACAC;UACAC;QACA;QACA;MACA;MAEAF;QACAkC;QACAC;QAAA;QACAC;QACApB;UAAA;UACA;;UAEA;UACA;;UAEA;UACA;YACA;UACA;QACA;QACAqB;UACArC;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAoC;MAAA;MACA;QACA;UACAZ;UACAC;UACAC;UACAC;UACA3C;UACA4C;QACA;;QAEA;QACA;QACA;UACA;QACA;QAEA;MACA;IACA;IAEAS;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIAC,8BAEA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAEAzC;kBAAAC;gBAAA;;gBAEA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEAM;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGAa;kBACA1C;kBACAC;oBACA+D;kBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA1C;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEAF;gBAAA;cAAA;gBAIA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA2C;MACA;QACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MAAA;MACA;QAAA;MAAA;MACA5C;QACA6C;QACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAC;kBAAA;oBAAA;oBAAA;sBAAA;wBAAA;0BAAA;4BACAC;8BAAA;4BAAA;4BAAA,MAEAA;8BAAA;8BAAA;4BAAA;4BAAA;8BAAAjC;8BAAAwB;4BAAA;0BAAA;4BAAA;4BAGA;4BACA;;4BAEA;4BAAA;4BAAA,OACA;0BAAA;4BAAAU;4BAAA,KAEAA;8BAAA;8BAAA;4BAAA;4BACA;4BACAC;8BACAxB;8BACAC;8BACAC;8BACA3C;4BACA;4BAAA,kCACA;8BAAA8B;8BAAAwB;4BAAA;0BAAA;4BAAA,MAEA;0BAAA;4BAAA;4BAAA;0BAAA;4BAAA;4BAAA;4BAGA;4BAAA,kCACA;8BAAAxB;8BAAAwB;8BAAAY;4BAAA;0BAAA;0BAAA;4BAAA;wBAAA;sBAAA;oBAAA;kBAAA,CAEA;kBAAA;oBAAA;kBAAA;gBAAA,MAEA;gBAAA;gBAAA,OACAC;cAAA;gBAAAC;gBAEA;gBACAC,mBACAC;kBAAA;gBAAA,GACAC;kBAAA;gBAAA;gBAEA;kBACAzD;oBACAC;oBACAC;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAuD;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA9B,yCAEA;gBAAA;gBAAA,OACA+B;cAAA;gBAAAV;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEAU;cAAA;gBAAAC;gBAAA,kCAEA;kBACA7C;kBACAa;kBACAH;kBACAoC;gBACA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,kCAGA;kBAAA9C;kBAAAoC;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAW;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,mCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACAjE;QACAC;QACAC;MACA;IACA;IAEA;IACAgE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA,mCACA;cAAA;gBAGA;gBACA;gBAAA;gBAGA;gBACAC;kBAAA;gBAAA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA,mCAEA,eACAX;kBAAA;gBAAA,GACAC;kBAAA;oBACA/B;oBACAjB;oBACA2D;kBACA;gBAAA;cAAA;gBAGAC;kBAAA;gBAAA;gBAAA;gBAAA,OAEAT;kBACAU;oBACA;kBACA;kBACAC;oBAAA;kBAAA;kBACAC;gBACA;cAAA;gBANAtB;gBAAA,KAQAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAA;kBACA;oBAAA;kBAAA;kBACA;oBACAC;sBACAxB;sBACAC;sBACAC;oBACA;kBACA;gBACA;;gBAEA;gBAAA,mCACA,eACA2B;kBAAA;gBAAA,GACAC;kBAAA;oBACA/B;oBACAjB;oBACA2D;kBACA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAKA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAK;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,mCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA1E;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,KAIA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAAyE;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGA;gBACAC;gBAEA5E;kBACAC;gBACA;;gBAEA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAM;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEAA;cAAA;gBAGA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAsE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAV;kBAAA;gBAAA;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACAnE;kBAAAC;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;cAAA;gBAAA,mCAGA,eACAuD;kBAAA;gBAAA,GACAC;kBAAA;oBACA/B;oBACAjB;oBACA2D;kBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAU;MACA;MACA;MACA;QACAC;QACAC;MACA;MAEA;QACAjG;QACAkG;MACA;MAEA;QACA,uCACAC;UACAC;UACAC;UACAC;QAAA;MAEA;MAEA;IACA;IAEA;IACAC;MACAtF;MACAA;QACAC;QACAC;MACA;;MAEA;MACAF;QACApB;QACAC;QACAwB;MACA;MAEAC;QACAN;MACA;IACA;IAEA;IACAuF;MACAvF;MAEA;MACA;QACA;UACAW;QACA;UACAA;QACA;UACAA;QACA;MACA;MAEAX;QACAC;QACAW;QACAC;QACAE;MACA;IACA;IAEA;IACAyE;MACA;MACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;MACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClvBA;AAAA;AAAA;AAAA;AAA4qC,CAAgB,kpCAAG,EAAC,C;;;;;;;;;;;ACAhsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/cleaning-upload.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/cleaning-upload.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./cleaning-upload.vue?vue&type=template&id=7d0a82ee&scoped=true&\"\nvar renderjs\nimport script from \"./cleaning-upload.vue?vue&type=script&lang=js&\"\nexport * from \"./cleaning-upload.vue?vue&type=script&lang=js&\"\nimport style0 from \"./cleaning-upload.vue?vue&type=style&index=0&id=7d0a82ee&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7d0a82ee\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/cleaning-upload.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cleaning-upload.vue?vue&type=template&id=7d0a82ee&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = !(_vm.mode === \"edit\")\n    ? _vm.getAreaTypeText(_vm.areaInfo.type)\n    : null\n  var l0 = _vm.__map(_vm.photos, function (photo, index) {\n    var $orig = _vm.__get_orig(photo)\n    var m1 = _vm.getPhotoDisplayUrl(photo)\n    return {\n      $orig: $orig,\n      m1: m1,\n    }\n  })\n  var g0 = _vm.photos.length\n  var g1 = _vm.photos.length === 0 || _vm.loading || _vm.uploading\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        l0: l0,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cleaning-upload.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cleaning-upload.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page-container\">\n    <view class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"header-content\">\n          <view class=\"card-title\">{{ areaInfo.name || '责任区清理' }}</view>\n          <view class=\"card-subtitle\">{{ mode === 'edit' ? '修改清理记录' : getAreaTypeText(areaInfo.type) }}</view>\n        </view>\n      </view>\n      <view class=\"card-body\">\n        <view class=\"upload-section\">\n          <view class=\"section-header\">\n            <view class=\"section-title\">上传清理照片</view>\n            <view class=\"auto-upload-toggle\" @click=\"toggleAutoUpload\">\n              <view class=\"toggle-label\">自动上传</view>\n              <view class=\"toggle-switch\" :class=\"{ active: autoUpload }\">\n                <view class=\"toggle-circle\"></view>\n              </view>\n            </view>\n          </view>\n          <view class=\"photo-grid\">\n            <view v-for=\"(photo, index) in photos\" :key=\"index\" class=\"photo-item\">\n              <image :src=\"getPhotoDisplayUrl(photo)\" mode=\"aspectFill\" @click=\"previewPhoto(index)\"></image>\n              <!-- 上传状态指示器 -->\n              <view v-if=\"photo.uploading\" class=\"photo-uploading\">\n                <view class=\"upload-spinner\"></view>\n              </view>\n              <view v-else-if=\"photo.uploaded\" class=\"photo-uploaded\">\n                <uni-icons type=\"checkmarkempty\" size=\"16\" color=\"white\"></uni-icons>\n              </view>\n              <view class=\"photo-delete\" @click=\"deletePhoto(index)\">\n                <uni-icons type=\"close\" size=\"18\" color=\"white\"></uni-icons>\n              </view>\n            </view>\n            <view v-if=\"photos.length < 12\" class=\"photo-add\" @click=\"addPhoto\" :class=\"{ disabled: uploading }\">\n              <uni-icons type=\"camera\" size=\"32\" color=\"#8E8E93\"></uni-icons>\n              <text>添加照片</text>\n            </view>\n          </view>\n          <!-- 上传进度 -->\n          <view v-if=\"uploading\" class=\"upload-progress\">\n            <view class=\"progress-bar\">\n              <view class=\"progress-fill\" :style=\"{ width: uploadProgress + '%' }\"></view>\n            </view>\n            <text class=\"progress-text\">正在上传照片... {{ uploadProgress }}%</text>\n          </view>\n          <view class=\"photo-tip\">最多可上传12张照片，建议拍摄清理前后对比照片</view>\n        </view>\n        \n        <view class=\"remarks-section\">\n          <view class=\"section-title\">清理备注（可选）</view>\n          <view class=\"remarks-input-container\">\n            <textarea \n              v-model=\"remarks\" \n              placeholder=\"请描述清理过程中发现的问题或需要注意的事项...\"\n              maxlength=\"200\"\n              class=\"remarks-input\"\n              @input=\"handleRemarksInput\"\n            ></textarea>\n            <view class=\"char-count-overlay\">{{ remarksLength }}/200</view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <view class=\"button-container\">\n      <button \n        class=\"primary-button\" \n        @click=\"submitCleaning\" \n        :disabled=\"photos.length === 0 || loading || uploading\"\n        :class=\"{ loading: loading || uploading }\"\n      >\n        <view v-if=\"loading || uploading\" class=\"button-loading\">\n          <view class=\"loading-spinner\"></view>\n          <text>{{ uploading ? '上传中...' : '提交中...' }}</text>\n        </view>\n        <text v-else>{{ mode === 'edit' ? '保存修改' : '提交清理记录' }}</text>\n      </button>\n    </view>\n  </view>\n</template>\n\n<script>\nimport uploadUtils from '@/utils/upload-utils.js'\nimport HygieneAPI from '@/utils/api/hygiene.js'\n\nexport default {\n  name: 'CleaningUpload',\n  data() {\n    return {\n      mode: 'create', // 'create' 或 'edit'\n      recordId: '', // 编辑模式下的记录ID\n      areaInfo: {},\n      photos: [],\n      remarks: '',\n      loading: false,\n      uploading: false,\n      uploadProgress: 0,\n      autoUpload: true, // 自动上传开关\n      uploadQueue: [], // 上传队列\n      \n      // 性能优化缓存\n      processCache: {\n        areaTypeMap: null,\n        photoUrlCache: new Map() // 缓存照片URL处理结果\n      }\n    }\n  },\n  computed: {\n    // 计算备注长度，确保响应式更新\n    remarksLength() {\n      return this.remarks ? this.remarks.length : 0;\n    }\n  },\n  onLoad(options) {\n    // 获取参数\n    this.mode = options.mode || 'create';\n    this.recordId = options.recordId || '';\n    const areaId = options.areaId || options.area_id || options.id;\n    const areaType = options.type || 'fixed'; // 获取区域类型参数\n    \n    // 初始化缓存\n    this.initProcessCache();\n    \n    // 如果有区域类型参数，先设置到 areaInfo 中（防止在 API 调用完成前访问时出错）\n    if (areaType) {\n      this.areaInfo.type = areaType;\n    }\n    \n    // 加载数据\n    this.loadAreaInfoOptimized(areaId);\n    \n    // 如果是编辑模式，加载记录数据\n    if (this.mode === 'edit' && this.recordId) {\n      this.loadRecordDataOptimized();\n    }\n  },\n  methods: {\n    // 初始化处理缓存\n    initProcessCache() {\n      if (!this.processCache.areaTypeMap) {\n        this.processCache.areaTypeMap = {\n          'fixed': '固定责任区清理',\n          'public': '公共责任区清理'\n        };\n      }\n    },\n\n    // 处理备注输入，确保字符限制和响应式更新\n    handleRemarksInput(e) {\n      let value = e.detail.value || '';\n      \n      // 强制限制字符数量\n      if (value.length > 200) {\n        value = value.substring(0, 200);\n        // 如果超出限制，显示提示\n        uni.showToast({\n          title: '备注不能超过200个字符',\n          icon: 'none',\n          duration: 1500\n        });\n      }\n      \n      // 更新数据\n      this.remarks = value;\n      \n      // 强制触发视图更新\n      this.$forceUpdate();\n    },\n\n    // 获取责任区信息（优化版）\n    async loadAreaInfoOptimized(areaId) {\n      if (!areaId) {\n        uni.showToast({\n          title: '责任区ID不能为空',\n          icon: 'none'\n        });\n        setTimeout(() => {\n          uni.navigateBack();\n        }, 1500);\n        return;\n      }\n\n      try {\n        const areaDetail = await HygieneAPI.getAreaDetail(areaId);\n        // 保留之前从 URL 参数设置的 type（如果 API 返回的数据中没有 type 字段）\n        this.areaInfo = {\n          ...areaDetail,\n          type: areaDetail.type || this.areaInfo.type || 'fixed'\n        };\n      } catch (error) {\n        this.handleLoadError(error, () => this.loadAreaInfoOptimized(areaId));\n      }\n    },\n\n    // 统一错误处理\n    handleLoadError(error, retryCallback) {\n      let errorMessage = '获取数据失败';\n      if (error.message.includes('未登录')) {\n        errorMessage = '请先登录';\n      } else if (error.message.includes('权限')) {\n        errorMessage = '您没有权限查看该责任区';\n      } else if (error.message.includes('不存在')) {\n        errorMessage = '责任区不存在或已删除';\n      } else {\n        errorMessage = error.message || '网络连接失败，请稍后重试';\n      }\n      \n      uni.showModal({\n        title: '获取数据失败',\n        content: errorMessage,\n        showCancel: true,\n        cancelText: '返回',\n        confirmText: '重试',\n        success: (res) => {\n          if (res.confirm) {\n            retryCallback();\n          } else {\n            uni.navigateBack();\n          }\n        }\n      });\n    },\n\n    // 获取责任区信息（保留原方法以防其他地方调用）\n    async loadAreaInfo(areaId) {\n      return this.loadAreaInfoOptimized(areaId);\n    },\n\n    // 加载记录数据（优化版）\n    async loadRecordDataOptimized() {\n      if (!this.recordId) {\n        return;\n      }\n\n      try {\n        uni.showLoading({ title: '加载记录数据...' });\n        \n        const result = await uniCloud.callFunction({\n          name: 'hygiene-cleaning',\n          data: {\n            action: 'getCleaningRecordDetail',\n            data: { record_id: this.recordId }\n          }\n        });\n\n        if (result.result?.success && result.result.data) {\n          const record = result.result.data;\n          this.processRecordData(record);\n        } else {\n          throw new Error(result.result?.message || '获取记录数据失败');\n        }\n      } catch (error) {\n        this.handleLoadError(error, () => this.loadRecordDataOptimized());\n      } finally {\n        uni.hideLoading();\n      }\n    },\n\n    // 处理记录数据\n    processRecordData(record) {\n      // 填充备注\n      this.remarks = record.remark || '';\n      \n      // 填充照片并预处理URL\n      if (record.photos?.length > 0) {\n        this.photos = record.photos.map(photo => {\n          const photoData = {\n            url: photo.url || photo,\n            uploaded: true,\n            cloudUrl: photo.url || photo,\n            cloudPath: photo.url || photo,\n            uploading: false,\n            belongsToRecord: true\n          };\n          \n          // 预缓存照片URL\n          const cacheKey = photoData.url;\n          if (!this.processCache.photoUrlCache.has(cacheKey)) {\n            this.processCache.photoUrlCache.set(cacheKey, this.processPhotoUrl(photoData.url));\n          }\n          \n          return photoData;\n        });\n      }\n    },\n\n    // 照片URL处理\n    processPhotoUrl(url) {\n      if (typeof url === 'string' && url.startsWith('http://tmp/')) {\n        return url; // 微信小程序本地临时文件路径\n      }\n      return url;\n    },\n\n    // 原方法保留以防其他地方调用\n    async loadRecordData() {\n      return this.loadRecordDataOptimized();\n    },\n\n\n\n    // 优化的添加照片方法\n    addPhoto() {\n      if (this.photos.length >= 12) {\n        uni.showToast({\n          title: '最多只能上传12张照片',\n          icon: 'none'\n        });\n        return;\n      }\n\n      uni.chooseImage({\n        count: 12 - this.photos.length,\n        sizeType: ['compressed'], // 只使用压缩版本以节省内存和上传时间\n        sourceType: ['camera', 'album'],\n        success: (res) => {\n          const newPhotos = this.processNewPhotos(res.tempFilePaths);\n          \n          // 批量添加到照片列表\n          this.photos.push(...newPhotos);\n          \n          // 如果开启自动上传，立即上传新选择的照片\n          if (this.autoUpload) {\n            this.autoUploadNewPhotosOptimized(newPhotos);\n          }\n        },\n        fail: () => {\n          uni.showToast({\n            title: '选择照片失败',\n            icon: 'none'\n          });\n        }\n      });\n    },\n\n    // 处理新选择的照片\n    processNewPhotos(tempFilePaths) {\n      return tempFilePaths.map(path => {\n        const photoData = {\n          url: path,\n          uploaded: false,\n          cloudUrl: '',\n          cloudPath: '',\n          uploading: false,\n          belongsToRecord: false\n        };\n        \n        // 预缓存照片URL\n        const cacheKey = path;\n        if (!this.processCache.photoUrlCache.has(cacheKey)) {\n          this.processCache.photoUrlCache.set(cacheKey, this.processPhotoUrl(path));\n        }\n        \n        return photoData;\n      });\n    },\n    \n    async deletePhoto(index) {\n      if (index < 0 || index >= this.photos.length) {\n        return;\n      }\n\n      const photo = this.photos[index];\n      \n      // 如果照片已经上传到云端，需要删除云端文件\n      if (photo.uploaded && photo.cloudPath) {\n        try {\n          uni.showLoading({ title: '删除照片中...' });\n          \n          // 区分已保存到记录的照片和新上传的照片\n          if (this.mode === 'edit' && this.recordId && photo.belongsToRecord !== false) {\n            // 编辑模式下，属于记录的照片通过API删除\n            await HygieneAPI.deleteCleaningPhotos(this.recordId, [photo.cloudPath]);\n          } else {\n            // 新上传但未保存的照片，直接调用删除云文件的云函数\n            await uniCloud.callFunction({\n              name: 'delete-file',\n              data: {\n                fileList: [this.extractFileId(photo.cloudPath)]\n              }\n            });\n          }\n        } catch (error) {\n          uni.showToast({\n            title: '删除云端照片失败',\n            icon: 'none'\n          });\n        } finally {\n          uni.hideLoading();\n        }\n      }\n      \n      // 从本地数组中移除\n      this.photos.splice(index, 1);\n    },\n\n    // 从URL中提取文件ID\n    extractFileId(url) {\n      if (url.startsWith('cloud://')) {\n        const parts = url.split('/');\n        return parts[parts.length - 1];\n      } else if (url.includes('tcb-api')) {\n        const urlObj = new URL(url);\n        return urlObj.pathname.split('/').pop();\n      }\n      return url;\n    },\n    \n    previewPhoto(index) {\n      const urls = this.photos.map(photo => this.getPhotoDisplayUrl(photo));\n      uni.previewImage({\n        urls: urls,\n        current: index\n      });\n    },\n\n    // 优化的自动上传新选择的照片\n    async autoUploadNewPhotosOptimized(newPhotos) {\n      // 使用 Promise.allSettled 并行上传，避免阻塞\n      const uploadPromises = newPhotos.map(async (photo, i) => {\n        const photoIndex = this.photos.findIndex(p => p.url === photo.url);\n        \n        if (photoIndex === -1) return { success: false, index: i };\n        \n        try {\n          // 标记为正在上传\n          this.$set(this.photos[photoIndex], 'uploading', true);\n          \n          // 单张照片上传\n          const uploadResult = await this.uploadSinglePhotoOptimized(photo);\n          \n          if (uploadResult.success) {\n            // 批量更新照片信息\n            Object.assign(this.photos[photoIndex], {\n              uploaded: true,\n              cloudUrl: uploadResult.url,\n              cloudPath: uploadResult.cloudPath,\n              uploading: false\n            });\n            return { success: true, index: i };\n          } else {\n            throw new Error(uploadResult.error || '上传失败');\n          }\n        } catch (error) {\n          this.$set(this.photos[photoIndex], 'uploading', false);\n          return { success: false, index: i, error: error.message };\n        }\n      });\n\n      // 等待所有上传完成\n      const results = await Promise.allSettled(uploadPromises);\n      \n      // 统计失败的上传\n      const failures = results\n        .filter(result => result.status === 'fulfilled' && !result.value.success)\n        .map(result => result.value);\n      \n      if (failures.length > 0) {\n        uni.showToast({\n          title: `${failures.length}张照片上传失败`,\n          icon: 'none',\n          duration: 2000\n        });\n      }\n    },\n\n    // 保留原方法以防其他地方调用\n    async autoUploadNewPhotos(newPhotos) {\n      return this.autoUploadNewPhotosOptimized(newPhotos);\n    },\n\n    // 优化的单张照片上传\n    async uploadSinglePhotoOptimized(photo) {\n      try {\n        const cloudPath = this.generateCloudPath();\n        \n        // 使用 uploadToCloud 方法上传单张照片\n        const uploadResult = await uploadUtils.uploadToCloud(photo.url, cloudPath);\n        \n        if (uploadResult?.fileID) {\n          // 获取访问URL\n          const fileInfo = await uploadUtils.getFileInfo(uploadResult.fileID);\n          \n          return {\n            success: true,\n            cloudPath: uploadResult.fileID,\n            url: fileInfo.tempFileURL || uploadResult.fileID,\n            size: uploadResult.actualSize\n          };\n        } else {\n          throw new Error('上传返回结果异常');\n        }\n      } catch (error) {\n        return { success: false, error: error.message };\n      }\n    },\n\n    // 生成云存储路径\n    generateCloudPath() {\n      const timestamp = Date.now();\n      const random = Math.random().toString(36).substring(2, 8);\n      return `6s/cleaning/${this.areaInfo._id}/${timestamp}_${random}.jpg`;\n    },\n\n    // 保留原方法以防其他地方调用\n    async uploadSinglePhoto(photo) {\n      return this.uploadSinglePhotoOptimized(photo);\n    },\n\n    // 切换自动上传状态\n    toggleAutoUpload() {\n      this.autoUpload = !this.autoUpload;\n      uni.showToast({\n        title: this.autoUpload ? '已开启自动上传' : '已关闭自动上传',\n        icon: 'none'\n      });\n    },\n    \n    // 优化的上传照片到云存储\n    async uploadPhotosOptimized() {\n      if (this.photos.length === 0) {\n        return [];\n      }\n\n      this.uploading = true;\n      this.uploadProgress = 0;\n\n      try {\n        // 只上传未上传的照片\n        const unuploadedPhotos = this.photos.filter(photo => !photo.uploaded);\n        if (unuploadedPhotos.length === 0) {\n          // 所有照片都已上传，直接返回已上传的照片信息\n          return this.photos\n            .filter(photo => photo.uploaded)\n            .map(photo => ({\n              url: photo.cloudPath,\n              type: 'cleaning',\n              description: ''\n            }));\n        }\n\n        const photoPaths = unuploadedPhotos.map(photo => photo.url);\n        \n        const uploadResult = await uploadUtils.batchUpload(photoPaths, {\n          onProgress: (progress) => {\n            this.uploadProgress = progress.progress;\n          },\n          pathGenerator: () => this.generateCloudPath(),\n          maxConcurrent: 3 // 增加并发数以提高上传速度\n        });\n\n        if (uploadResult.success) {\n          // 更新照片信息\n          uploadResult.results.forEach((result, index) => {\n            const originalIndex = this.photos.findIndex(p => p.url === unuploadedPhotos[index].url);\n            if (result.success && originalIndex !== -1) {\n              Object.assign(this.photos[originalIndex], {\n                uploaded: true,\n                cloudUrl: result.url,\n                cloudPath: result.cloudPath\n              });\n            }\n          });\n\n          // 返回所有已上传的照片信息\n          return this.photos\n            .filter(photo => photo.uploaded)\n            .map(photo => ({\n              url: photo.cloudPath,\n              type: 'cleaning',\n              description: ''\n            }));\n        } else {\n          throw new Error('照片上传失败');\n        }\n      } catch (error) {\n        throw error;\n      } finally {\n        this.uploading = false;\n        this.uploadProgress = 0;\n      }\n    },\n\n    // 保留原方法以防其他地方调用\n    async uploadPhotos() {\n      return this.uploadPhotosOptimized();\n    },\n    \n    // 优化的提交清理记录\n    async submitCleaning() {\n      if (this.photos.length === 0) {\n        uni.showToast({\n          title: '请至少上传一张照片',\n          icon: 'none'\n        });\n        return;\n      }\n\n      if (this.loading) return;\n\n      this.loading = true;\n      \n      try {\n        // 准备上传的照片数据\n        const uploadedPhotos = await this.preparePhotosForSubmit();\n        \n        if (uploadedPhotos.length === 0) {\n          throw new Error('没有可用的照片，请重新上传');\n        }\n\n        // 提交数据\n        const submissionData = this.prepareSubmissionData(uploadedPhotos);\n        \n        uni.showLoading({\n          title: '提交清理记录...'\n        });\n\n        // 根据模式调用不同的API\n        if (this.mode === 'edit') {\n          await HygieneAPI.updateCleaningRecord(this.recordId, submissionData);\n        } else {\n          await HygieneAPI.createCleaningRecord(submissionData);\n        }\n\n        this.handleSubmitSuccess();\n      } catch (error) {\n        this.handleSubmitError(error);\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 准备照片数据用于提交\n    async preparePhotosForSubmit() {\n      const unuploadedPhotos = this.photos.filter(photo => !photo.uploaded && !photo.uploading);\n      \n      if (unuploadedPhotos.length > 0) {\n        uni.showLoading({ title: '正在上传剩余照片...' });\n        return await this.uploadPhotosOptimized();\n      } else {\n        // 所有照片都已上传，直接使用已上传的照片\n        return this.photos\n          .filter(photo => photo.uploaded)\n          .map(photo => ({\n            url: photo.cloudPath,\n            type: 'cleaning',\n            description: ''\n          }));\n      }\n    },\n\n    // 准备提交数据\n    prepareSubmissionData(uploadedPhotos) {\n      // 确保备注不超过限制\n      let finalRemarks = this.remarks.trim();\n      if (finalRemarks.length > 200) {\n        finalRemarks = finalRemarks.substring(0, 200);\n        console.warn('备注被截断到200字符');\n      }\n      \n      const baseData = {\n        photos: uploadedPhotos,\n        remark: finalRemarks\n      };\n\n      if (this.mode === 'create') {\n        return {\n          ...baseData,\n          area_id: this.areaInfo._id,\n          cleaning_date: new Date().toISOString(),\n          completion_status: 'completed'\n        };\n      }\n\n      return baseData;\n    },\n\n    // 处理提交成功\n    handleSubmitSuccess() {\n      uni.hideLoading();\n      uni.showToast({\n        title: this.mode === 'edit' ? '修改成功' : '提交成功',\n        icon: 'success'\n      });\n      \n      // 通知其他页面数据已更新\n      uni.$emit('cleaningRecordUpdated', {\n        mode: this.mode,\n        recordId: this.recordId,\n        areaId: this.areaInfo._id\n      });\n      \n      setTimeout(() => {\n        uni.navigateBack();\n      }, 1500);\n    },\n\n    // 处理提交错误\n    handleSubmitError(error) {\n      uni.hideLoading();\n      \n      let errorMessage = '提交失败，请重试';\n      if (error.message) {\n        if (error.message.includes('未登录')) {\n          errorMessage = '请先登录';\n        } else if (error.message.includes('权限')) {\n          errorMessage = '您没有权限操作该责任区';\n        } else {\n          errorMessage = error.message;\n        }\n      }\n      \n      uni.showModal({\n        title: '提交失败',\n        content: errorMessage,\n        showCancel: false,\n        confirmText: '知道了'\n      });\n    },\n\n    // 获取责任区类型文本（使用缓存）\n    getAreaTypeText(type) {\n      // 确保缓存已初始化\n      if (!this.processCache.areaTypeMap) {\n        this.initProcessCache();\n      }\n      \n      // 安全访问缓存\n      return (this.processCache.areaTypeMap && this.processCache.areaTypeMap[type]) || '责任区清理';\n    },\n\n    // 获取照片显示URL（优化版，使用缓存）\n    getPhotoDisplayUrl(photo) {\n      const url = photo.url || photo;\n      const cacheKey = url;\n      \n      // 确保缓存已初始化\n      if (!this.processCache.photoUrlCache) {\n        this.initProcessCache();\n      }\n      \n      // 先检查缓存\n      if (this.processCache.photoUrlCache.has(cacheKey)) {\n        return this.processCache.photoUrlCache.get(cacheKey);\n      }\n      \n      // 处理URL并缓存\n      const processedUrl = this.processPhotoUrl(url);\n      this.processCache.photoUrlCache.set(cacheKey, processedUrl);\n      \n      return processedUrl;\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\n\twidth: 100%;\n\toverflow-x: hidden; /* 防止水平滚动 */\n\tbox-sizing: border-box;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;\n  min-height: 100vh;\n  padding: 24rpx;\n}\n\n.card {\n  background: #ffffff;\n  border-radius: 16rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.card-header {\n  padding: 32rpx 32rpx 16rpx 32rpx;\n  border-bottom: 1rpx solid #F2F2F7;\n}\n\n.card-body {\n  padding: 32rpx;\n}\n\n.header-content {\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n}\n\n.card-subtitle {\n  font-size: 26rpx;\n  color: #8E8E93;\n}\n\n.upload-section, .remarks-section {\n  margin-bottom: 32rpx;\n}\n\n.remarks-section .section-title {\n  margin-bottom: 16rpx;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.section-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n}\n\n.auto-upload-toggle {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n  cursor: pointer;\n}\n\n.toggle-label {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.toggle-switch {\n  width: 76rpx;\n  height: 44rpx;\n  background: #E5E5EA;\n  border-radius: 22rpx;\n  position: relative;\n  transition: background-color 0.3s ease;\n}\n\n.toggle-switch.active {\n  background: #34C759;\n}\n\n.toggle-circle {\n  width: 36rpx;\n  height: 36rpx;\n  background: white;\n  border-radius: 50%;\n  position: absolute;\n  top: 4rpx;\n  left: 4rpx;\n  transition: transform 0.3s ease;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);\n}\n\n.toggle-switch.active .toggle-circle {\n  transform: translateX(32rpx);\n}\n\n.photo-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12rpx;\n  margin-bottom: 16rpx;\n}\n\n.photo-item {\n  position: relative;\n  width: 170rpx;\n  height: 170rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n}\n\n.photo-item image {\n  width: 100%;\n  height: 100%;\n}\n\n.photo-delete {\n  position: absolute;\n  top: 6rpx;\n  right: 6rpx;\n  width: 36rpx;\n  height: 36rpx;\n  background: rgba(0, 0, 0, 0.7);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\n}\n\n.photo-add {\n  width: 170rpx;\n  height: 170rpx;\n  border: 2rpx dashed #E5E5EA;\n  border-radius: 12rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 8rpx;\n  background: #F8F9FA;\n}\n\n.photo-add text {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.photo-tip {\n  font-size: 24rpx;\n  color: #8E8E93;\n  line-height: 1.4;\n}\n\n.remarks-input-container {\n  position: relative;\n  width: 100%;\n}\n\n.remarks-input {\n  width: 100%;\n  min-height: 120rpx;\n  padding: 16rpx 16rpx 32rpx 16rpx; /* 恢复正常的右侧padding，让文字可以占用全宽 */\n  border: 1rpx solid #E5E5EA;\n  border-radius: 12rpx;\n  font-size: 28rpx;\n  color: #1C1C1E;\n  background: #F8F9FA;\n  box-sizing: border-box;\n  resize: none; /* 防止拖拽改变大小 */\n  line-height: 1.5;\n}\n\n.char-count-overlay {\n  position: absolute;\n  bottom: 8rpx;\n  right: 8rpx;\n  font-size: 22rpx;\n  color: #8E8E93;\n  background: rgba(248, 249, 250, 0.9); /* 半透明背景 */\n  padding: 4rpx 8rpx;\n  border-radius: 8rpx;\n  pointer-events: none; /* 防止遮挡输入 */\n  z-index: 2;\n  backdrop-filter: blur(4rpx); /* 添加模糊效果 */\n}\n\n.button-container {\n  padding: 32rpx 0;\n  display: flex;\n  flex-direction: column;\n  gap: 16rpx;\n}\n\n.primary-button {\n  background: #007AFF;\n  color: white;\n  border: none;\n  border-radius: 12rpx;\n  padding: 24rpx;\n  font-size: 32rpx;\n  font-weight: 600;\n  width: 100%;\n}\n\n.primary-button[disabled] {\n  background: #C7C7CC;\n  color: #8E8E93;\n}\n\n.primary-button.loading {\n  background: #0056D6;\n  opacity: 0.9;\n}\n\n.button-loading {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12rpx;\n  color: white;\n}\n\n.loading-spinner {\n  width: 32rpx;\n  height: 32rpx;\n  border: 3rpx solid rgba(255, 255, 255, 0.3);\n  border-top: 3rpx solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.photo-uploading {\n  position: absolute;\n  top: 8rpx;\n  left: 8rpx;\n  width: 32rpx;\n  height: 32rpx;\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-spinner {\n  width: 20rpx;\n  height: 20rpx;\n  border: 2rpx solid rgba(255, 255, 255, 0.3);\n  border-top: 2rpx solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.photo-uploaded {\n  position: absolute;\n  top: 8rpx;\n  left: 8rpx;\n  width: 32rpx;\n  height: 32rpx;\n  background: #34C759;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.photo-add.disabled {\n  opacity: 0.5;\n  pointer-events: none;\n}\n\n.upload-progress {\n  margin: 16rpx 0;\n  padding: 16rpx;\n  background: #F8F9FA;\n  border-radius: 12rpx;\n}\n\n.progress-bar {\n  width: 100%;\n  height: 8rpx;\n  background: #E5E5EA;\n  border-radius: 4rpx;\n  overflow: hidden;\n  margin-bottom: 8rpx;\n}\n\n.progress-fill {\n  height: 100%;\n  background: linear-gradient(90deg, #007AFF 0%, #5AC8FA 100%);\n  border-radius: 4rpx;\n  transition: width 0.3s ease;\n}\n\n.progress-text {\n  font-size: 24rpx;\n  color: #8E8E93;\n  text-align: center;\n  display: block;\n}\n\n/* H5端优化 - 支持更多照片展示 */\n/* #ifdef H5 */\n@media screen and (min-width: 600px) {\n  .photo-grid {\n    max-width: 600px;\n  }\n  \n  .photo-item, .photo-add {\n    width: 130px;\n    height: 130px;\n    max-width: 130px;\n    max-height: 130px;\n  }\n}\n\n@media screen and (min-width: 900px) {\n  .photo-grid {\n    max-width: 800px;\n  }\n  \n  .photo-item, .photo-add {\n    width: 140px;\n    height: 140px;\n    max-width: 140px;\n    max-height: 140px;\n  }\n}\n/* #endif */\n\n</style>", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cleaning-upload.vue?vue&type=style&index=0&id=7d0a82ee&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cleaning-upload.vue?vue&type=style&index=0&id=7d0a82ee&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775841957\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}