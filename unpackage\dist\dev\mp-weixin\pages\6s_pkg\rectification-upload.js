require('./common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/6s_pkg/rectification-upload"],{

/***/ 605:
/*!************************************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2F6s_pkg%2Frectification-upload"} ***!
  \************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _rectificationUpload = _interopRequireDefault(__webpack_require__(/*! ./pages/6s_pkg/rectification-upload.vue */ 606));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_rectificationUpload.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 606:
/*!*****************************************************!*\
  !*** D:/Xwzc/pages/6s_pkg/rectification-upload.vue ***!
  \*****************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _rectification_upload_vue_vue_type_template_id_6e3331a0_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./rectification-upload.vue?vue&type=template&id=6e3331a0&scoped=true& */ 607);
/* harmony import */ var _rectification_upload_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rectification-upload.vue?vue&type=script&lang=js& */ 609);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _rectification_upload_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _rectification_upload_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _rectification_upload_vue_vue_type_style_index_0_id_6e3331a0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./rectification-upload.vue?vue&type=style&index=0&id=6e3331a0&lang=scss&scoped=true& */ 611);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _rectification_upload_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _rectification_upload_vue_vue_type_template_id_6e3331a0_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _rectification_upload_vue_vue_type_template_id_6e3331a0_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "6e3331a0",
  null,
  false,
  _rectification_upload_vue_vue_type_template_id_6e3331a0_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/6s_pkg/rectification-upload.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 607:
/*!************************************************************************************************!*\
  !*** D:/Xwzc/pages/6s_pkg/rectification-upload.vue?vue&type=template&id=6e3331a0&scoped=true& ***!
  \************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_upload_vue_vue_type_template_id_6e3331a0_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./rectification-upload.vue?vue&type=template&id=6e3331a0&scoped=true& */ 608);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_upload_vue_vue_type_template_id_6e3331a0_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_upload_vue_vue_type_template_id_6e3331a0_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_upload_vue_vue_type_template_id_6e3331a0_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_upload_vue_vue_type_template_id_6e3331a0_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 608:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/6s_pkg/rectification-upload.vue?vue&type=template&id=6e3331a0&scoped=true& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 118))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 =
    !_vm.loading && !_vm.loadError
      ? _vm.formatDateTime(_vm.taskInfo.issueFoundDate)
      : null
  var m1 =
    !_vm.loading && !_vm.loadError && _vm.taskInfo.reviewComments
      ? _vm.formatDateTime(_vm.taskInfo.reviewDate)
      : null
  var g0 =
    !_vm.loading && !_vm.loadError
      ? _vm.taskInfo.inspectionPhotos &&
        _vm.taskInfo.inspectionPhotos.length > 0
      : null
  var l0 =
    !_vm.loading && !_vm.loadError && g0
      ? _vm.__map(_vm.taskInfo.inspectionPhotos, function (photo, index) {
          var $orig = _vm.__get_orig(photo)
          var m2 = _vm.getCloudPhotoUrl(photo)
          return {
            $orig: $orig,
            m2: m2,
          }
        })
      : null
  var g1 =
    !_vm.loading && !_vm.loadError
      ? _vm.taskInfo.previousPhotos && _vm.taskInfo.previousPhotos.length > 0
      : null
  var l1 =
    !_vm.loading && !_vm.loadError && g1
      ? _vm.__map(_vm.taskInfo.previousPhotos, function (photo, index) {
          var $orig = _vm.__get_orig(photo)
          var m3 = _vm.formatDateTime(photo.timestamp)
          return {
            $orig: $orig,
            m3: m3,
          }
        })
      : null
  var l2 =
    !_vm.loading && !_vm.loadError
      ? _vm.__map(_vm.photos, function (photo, index) {
          var $orig = _vm.__get_orig(photo)
          var m4 = _vm.getPhotoDisplayUrl(photo)
          return {
            $orig: $orig,
            m4: m4,
          }
        })
      : null
  var g2 = !_vm.loading && !_vm.loadError ? _vm.photos.length : null
  var g3 = _vm.photos.length === 0 || !_vm.remarks.trim() || _vm.submitting
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        m1: m1,
        g0: g0,
        l0: l0,
        g1: g1,
        l1: l1,
        l2: l2,
        g2: g2,
        g3: g3,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 609:
/*!******************************************************************************!*\
  !*** D:/Xwzc/pages/6s_pkg/rectification-upload.vue?vue&type=script&lang=js& ***!
  \******************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_upload_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./rectification-upload.vue?vue&type=script&lang=js& */ 610);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_upload_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_upload_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_upload_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_upload_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_upload_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 610:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/6s_pkg/rectification-upload.vue?vue&type=script&lang=js& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni, uniCloud) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _auth = __webpack_require__(/*! @/utils/auth.js */ 91);
var _uploadUtils = _interopRequireDefault(__webpack_require__(/*! @/utils/upload-utils.js */ 275));
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  name: 'RectificationUpload',
  data: function data() {
    return {
      taskInfo: {},
      photos: [],
      remarks: '',
      loading: false,
      loadError: '',
      submitting: false,
      autoUpload: true,
      // 自动上传开关
      taskId: '',
      // 性能优化：缓存系统
      processCache: {
        dateFormatter: null,
        // 缓存日期格式化器
        photoUrlCache: new Map(),
        // 缓存照片URL处理结果
        inspectionUrlCache: new Map() // 缓存检查照片URL
      }
    };
  },

  computed: {
    // 计算备注长度，确保响应式更新
    remarksLength: function remarksLength() {
      return this.remarks ? this.remarks.length : 0;
    }
  },
  onLoad: function onLoad(options) {
    this.taskId = options.taskId;
    this.initProcessCache();
    this.loadTaskInfoOptimized(options.taskId);
  },
  methods: {
    // 初始化性能缓存
    initProcessCache: function initProcessCache() {
      // 创建缓存的日期格式化器
      this.processCache.dateFormatter = function (dateString) {
        if (!dateString) return '--';
        try {
          var date;
          if (typeof dateString === 'string') {
            if (dateString.includes('T') || dateString.includes('Z')) {
              date = new Date(dateString);
            } else {
              date = new Date(dateString.replace(/-/g, '/'));
            }
          } else {
            date = new Date(dateString);
          }
          if (isNaN(date.getTime())) {
            return '--';
          }
          return "".concat(date.getMonth() + 1, "\u6708").concat(date.getDate(), "\u65E5 ").concat(date.getHours().toString().padStart(2, '0'), ":").concat(date.getMinutes().toString().padStart(2, '0'));
        } catch (error) {
          return '--';
        }
      };
    },
    // 处理备注输入，确保字符限制和响应式更新
    handleRemarksInput: function handleRemarksInput(e) {
      var value = e.detail.value || '';

      // 强制限制字符数量
      if (value.length > 200) {
        value = value.substring(0, 200);
        // 如果超出限制，显示提示
        uni.showToast({
          title: '整改说明不能超过200个字符',
          icon: 'none',
          duration: 1500
        });
      }

      // 更新数据
      this.remarks = value;

      // 强制触发视图更新
      this.$forceUpdate();
    },
    // 优化的任务信息加载
    loadTaskInfoOptimized: function loadTaskInfoOptimized(taskId) {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var result, task;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                if (taskId) {
                  _context.next = 3;
                  break;
                }
                _this.loadError = '整改任务ID不能为空';
                return _context.abrupt("return");
              case 3:
                _this.loading = true;
                _this.loadError = '';
                _context.prev = 5;
                _context.next = 8;
                return (0, _auth.callCloudFunction)('hygiene-rectification', {
                  action: 'getRectificationDetail',
                  data: {
                    id: taskId
                  }
                });
              case 8:
                result = _context.sent;
                if (!(result && result.success && result.data)) {
                  _context.next = 15;
                  break;
                }
                task = result.data; // 使用优化的数据处理
                _this.taskInfo = _this.processTaskDataOptimized(task);

                // 预处理照片URL
                _this.preprocessPhotos();
                _context.next = 16;
                break;
              case 15:
                throw new Error((result === null || result === void 0 ? void 0 : result.message) || '获取整改任务信息失败');
              case 16:
                _context.next = 22;
                break;
              case 18:
                _context.prev = 18;
                _context.t0 = _context["catch"](5);
                console.error('加载整改任务信息失败:', _context.t0);
                _this.loadError = _context.t0.message || '加载失败，请稍后重试';
              case 22:
                _context.prev = 22;
                _this.loading = false;
                return _context.finish(22);
              case 25:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[5, 18, 22, 25]]);
      }))();
    },
    // 优化的数据处理
    processTaskDataOptimized: function processTaskDataOptimized(task) {
      return {
        id: task._id || task.id,
        area: task.area_name || '未知责任区',
        areaId: task.area_id,
        isPublic: task.area_type === 'public',
        status: task.status || 'pending',
        problemDescription: task.issue_description || task.description || '无问题描述',
        issueFoundDate: task.created_at || task.issue_found_date,
        inspector: task.inspector_name || task.created_by_name || task.assigned_by_name || '未知检查员',
        // 审核反馈信息
        reviewComments: task.review_comments || '',
        reviewDate: task.review_date || '',
        reviewer: task.reviewer_name || '未知审核员',
        reviewResult: task.review_result || '',
        // 检查员发现问题时上传的照片
        inspectionPhotos: task.inspection_photos || [],
        // 之前的整改照片
        previousPhotos: task.completion_photos || []
      };
    },
    // 预处理照片URL，提前缓存
    preprocessPhotos: function preprocessPhotos() {
      var _this2 = this;
      // 预处理检查照片URL
      if (this.taskInfo.inspectionPhotos) {
        this.taskInfo.inspectionPhotos.forEach(function (photo, index) {
          var url = _this2.getCloudPhotoUrl(photo);
          _this2.processCache.inspectionUrlCache.set(index, url);
        });
      }
    },
    // 加载整改任务信息
    loadTaskInfo: function loadTaskInfo(taskId) {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var result, task;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                if (taskId) {
                  _context2.next = 3;
                  break;
                }
                _this3.loadError = '整改任务ID不能为空';
                return _context2.abrupt("return");
              case 3:
                _this3.loading = true;
                _this3.loadError = '';
                _context2.prev = 5;
                _context2.next = 8;
                return (0, _auth.callCloudFunction)('hygiene-rectification', {
                  action: 'getRectificationDetail',
                  data: {
                    id: taskId
                  }
                });
              case 8:
                result = _context2.sent;
                if (!(result && result.success && result.data)) {
                  _context2.next = 14;
                  break;
                }
                task = result.data;
                _this3.taskInfo = {
                  id: task._id || task.id,
                  area: task.area_name || '未知责任区',
                  areaId: task.area_id,
                  isPublic: task.area_type === 'public',
                  status: task.status || 'pending',
                  problemDescription: task.issue_description || task.description || '无问题描述',
                  issueFoundDate: task.created_at || task.issue_found_date,
                  inspector: task.inspector_name || task.created_by_name || task.assigned_by_name || '未知检查员',
                  // 审核反馈信息
                  reviewComments: task.review_comments || '',
                  reviewDate: task.review_date || '',
                  reviewer: task.reviewer_name || '未知审核员',
                  reviewResult: task.review_result || '',
                  // 检查员发现问题时上传的照片
                  inspectionPhotos: task.inspection_photos || [],
                  // 之前的整改照片
                  previousPhotos: task.completion_photos || []
                };
                _context2.next = 15;
                break;
              case 14:
                throw new Error((result === null || result === void 0 ? void 0 : result.message) || '获取整改任务信息失败');
              case 15:
                _context2.next = 21;
                break;
              case 17:
                _context2.prev = 17;
                _context2.t0 = _context2["catch"](5);
                console.error('加载整改任务信息失败:', _context2.t0);
                _this3.loadError = _context2.t0.message || '加载失败，请稍后重试';
              case 21:
                _context2.prev = 21;
                _this3.loading = false;
                return _context2.finish(21);
              case 24:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[5, 17, 21, 24]]);
      }))();
    },
    // 重新加载
    retryLoad: function retryLoad() {
      this.loadTaskInfo(this.taskId);
    },
    // 优化的日期时间格式化（使用缓存）
    formatDateTime: function formatDateTime(dateString) {
      // 确保缓存已初始化
      if (!this.processCache.dateFormatter) {
        this.initProcessCache();
      }

      // 安全调用格式化函数
      if (this.processCache.dateFormatter && typeof this.processCache.dateFormatter === 'function') {
        return this.processCache.dateFormatter(dateString);
      }

      // 回退到默认格式化
      if (!dateString) return '--';
      try {
        var date;
        if (typeof dateString === 'string') {
          if (dateString.includes('T') || dateString.includes('Z')) {
            date = new Date(dateString);
          } else {
            date = new Date(dateString.replace(/-/g, '/'));
          }
        } else {
          date = new Date(dateString);
        }
        if (isNaN(date.getTime())) {
          return '--';
        }
        return "".concat(date.getMonth() + 1, "\u6708").concat(date.getDate(), "\u65E5 ").concat(date.getHours().toString().padStart(2, '0'), ":").concat(date.getMinutes().toString().padStart(2, '0'));
      } catch (error) {
        return '--';
      }
    },
    // 优化的添加照片
    addPhoto: function addPhoto() {
      var _this4 = this;
      if (this.photos.length >= 12) {
        uni.showToast({
          title: '最多只能上传12张照片',
          icon: 'none'
        });
        return;
      }
      uni.chooseImage({
        count: 12 - this.photos.length,
        sizeType: ['compressed'],
        sourceType: ['camera', 'album'],
        success: function () {
          var _success = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3(res) {
            var newPhotos;
            return _regenerator.default.wrap(function _callee3$(_context3) {
              while (1) {
                switch (_context3.prev = _context3.next) {
                  case 0:
                    newPhotos = _this4.processNewPhotos(res.tempFilePaths); // 添加到照片列表
                    _this4.photos = _this4.photos.concat(newPhotos);

                    // 自动上传新选择的照片（优化版）
                    if (_this4.autoUpload) {
                      _this4.autoUploadNewPhotosOptimized(newPhotos);
                    }
                  case 3:
                  case "end":
                    return _context3.stop();
                }
              }
            }, _callee3);
          }));
          function success(_x) {
            return _success.apply(this, arguments);
          }
          return success;
        }(),
        fail: function fail(error) {
          console.error('选择照片失败:', error);
          uni.showToast({
            title: '选择照片失败',
            icon: 'none'
          });
        }
      });
    },
    // 处理新照片数据
    processNewPhotos: function processNewPhotos(tempFilePaths) {
      var _this5 = this;
      return tempFilePaths.map(function (path) {
        var photo = {
          url: path,
          uploaded: false,
          cloudUrl: '',
          cloudPath: '',
          uploading: false
        };

        // 预缓存显示URL
        _this5.processCache.photoUrlCache.set(path, _this5.getPhotoDisplayUrl(photo));
        return photo;
      });
    },
    // 优化的并行自动上传
    autoUploadNewPhotosOptimized: function autoUploadNewPhotosOptimized(newPhotos) {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var maxConcurrent, uploadPromises, i, batch, batchPromises, results, successful, failed;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                if (!(!_this6.autoUpload || newPhotos.length === 0)) {
                  _context4.next = 2;
                  break;
                }
                return _context4.abrupt("return");
              case 2:
                // 并行上传，最大并发数为3
                maxConcurrent = 3;
                uploadPromises = [];
                for (i = 0; i < newPhotos.length; i += maxConcurrent) {
                  batch = newPhotos.slice(i, i + maxConcurrent);
                  batchPromises = batch.map(function (photo) {
                    return _this6.uploadPhotoWithIndex(photo);
                  });
                  uploadPromises.push.apply(uploadPromises, (0, _toConsumableArray2.default)(batchPromises));
                }

                // 使用 Promise.allSettled 确保即使部分失败也能继续
                _context4.next = 7;
                return Promise.allSettled(uploadPromises);
              case 7:
                results = _context4.sent;
                // 统计成功和失败的数量
                successful = results.filter(function (r) {
                  return r.status === 'fulfilled';
                }).length;
                failed = results.filter(function (r) {
                  return r.status === 'rejected';
                }).length;
                if (failed > 0) {
                  uni.showToast({
                    title: "".concat(successful, "\u5F20\u4E0A\u4F20\u6210\u529F\uFF0C").concat(failed, "\u5F20\u5931\u8D25"),
                    icon: 'none',
                    duration: 2000
                  });
                }
              case 11:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4);
      }))();
    },
    // 上传单张照片并更新索引
    uploadPhotoWithIndex: function uploadPhotoWithIndex(photo) {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var photoIndex, uploadResult;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                photoIndex = _this7.photos.findIndex(function (p) {
                  return p.url === photo.url;
                });
                if (!(photoIndex === -1)) {
                  _context5.next = 3;
                  break;
                }
                throw new Error('照片不存在');
              case 3:
                _context5.prev = 3;
                // 标记为正在上传
                _this7.$set(_this7.photos[photoIndex], 'uploading', true);

                // 上传照片
                _context5.next = 7;
                return _this7.uploadSinglePhotoOptimized(photo);
              case 7:
                uploadResult = _context5.sent;
                if (!uploadResult.success) {
                  _context5.next = 16;
                  break;
                }
                // 更新照片信息
                _this7.$set(_this7.photos[photoIndex], 'uploaded', true);
                _this7.$set(_this7.photos[photoIndex], 'cloudUrl', uploadResult.url);
                _this7.$set(_this7.photos[photoIndex], 'cloudPath', uploadResult.cloudPath);
                _this7.$set(_this7.photos[photoIndex], 'uploading', false);
                return _context5.abrupt("return", uploadResult);
              case 16:
                throw new Error(uploadResult.error || '上传失败');
              case 17:
                _context5.next = 23;
                break;
              case 19:
                _context5.prev = 19;
                _context5.t0 = _context5["catch"](3);
                _this7.$set(_this7.photos[photoIndex], 'uploading', false);
                throw _context5.t0;
              case 23:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[3, 19]]);
      }))();
    },
    // 兼容旧方法名
    autoUploadNewPhotos: function autoUploadNewPhotos(newPhotos) {
      var _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                return _context6.abrupt("return", _this8.autoUploadNewPhotosOptimized(newPhotos));
              case 1:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6);
      }))();
    },
    // 优化的单张照片上传
    uploadSinglePhotoOptimized: function uploadSinglePhotoOptimized(photo) {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        var cloudPath, uploadResult, fileInfo;
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                _context7.prev = 0;
                cloudPath = _this9.generateCloudPath(); // 使用 uploadToCloud 方法上传单张照片
                _context7.next = 4;
                return _uploadUtils.default.uploadToCloud(photo.url, cloudPath);
              case 4:
                uploadResult = _context7.sent;
                if (!(uploadResult && uploadResult.fileID)) {
                  _context7.next = 12;
                  break;
                }
                _context7.next = 8;
                return _uploadUtils.default.getFileInfo(uploadResult.fileID);
              case 8:
                fileInfo = _context7.sent;
                return _context7.abrupt("return", {
                  success: true,
                  cloudPath: uploadResult.fileID,
                  url: fileInfo.tempFileURL || uploadResult.fileID,
                  size: uploadResult.actualSize
                });
              case 12:
                throw new Error('上传返回结果异常');
              case 13:
                _context7.next = 19;
                break;
              case 15:
                _context7.prev = 15;
                _context7.t0 = _context7["catch"](0);
                console.error('单张照片上传失败:', _context7.t0);
                return _context7.abrupt("return", {
                  success: false,
                  error: _context7.t0.message
                });
              case 19:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7, null, [[0, 15]]);
      }))();
    },
    // 生成云端路径
    generateCloudPath: function generateCloudPath() {
      var timestamp = Date.now();
      var random = Math.random().toString(36).substring(2, 8);
      return "6s/rectification/".concat(this.taskInfo.areaId, "/").concat(timestamp, "_").concat(random, ".jpg");
    },
    // 兼容旧方法名
    uploadSinglePhoto: function uploadSinglePhoto(photo) {
      var _this10 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                return _context8.abrupt("return", _this10.uploadSinglePhotoOptimized(photo));
              case 1:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8);
      }))();
    },
    // 删除照片
    deletePhoto: function deletePhoto(index) {
      var _this11 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
        var photo;
        return _regenerator.default.wrap(function _callee9$(_context9) {
          while (1) {
            switch (_context9.prev = _context9.next) {
              case 0:
                if (!(index < 0 || index >= _this11.photos.length)) {
                  _context9.next = 2;
                  break;
                }
                return _context9.abrupt("return");
              case 2:
                photo = _this11.photos[index]; // 如果照片已经上传到云端，需要删除云端文件
                if (!(photo.uploaded && photo.cloudPath)) {
                  _context9.next = 17;
                  break;
                }
                _context9.prev = 4;
                uni.showLoading({
                  title: '删除照片中...'
                });
                _context9.next = 8;
                return uniCloud.callFunction({
                  name: 'delete-file',
                  data: {
                    fileList: [_this11.extractFileId(photo.cloudPath)]
                  }
                });
              case 8:
                _context9.next = 14;
                break;
              case 10:
                _context9.prev = 10;
                _context9.t0 = _context9["catch"](4);
                console.error('删除云端照片失败:', _context9.t0);
                uni.showToast({
                  title: '删除云端照片失败',
                  icon: 'none'
                });
              case 14:
                _context9.prev = 14;
                uni.hideLoading();
                return _context9.finish(14);
              case 17:
                // 从本地数组中移除
                _this11.photos.splice(index, 1);
              case 18:
              case "end":
                return _context9.stop();
            }
          }
        }, _callee9, null, [[4, 10, 14, 17]]);
      }))();
    },
    // 从URL中提取文件ID
    extractFileId: function extractFileId(url) {
      if (url.startsWith('cloud://')) {
        var parts = url.split('/');
        return parts[parts.length - 1];
      } else if (url.includes('tcb-api')) {
        var urlObj = new URL(url);
        return urlObj.pathname.split('/').pop();
      }
      return url;
    },
    // 优化的照片显示URL获取（使用缓存）
    getPhotoDisplayUrl: function getPhotoDisplayUrl(photo) {
      var url = photo.url || photo;

      // 确保缓存已初始化
      if (!this.processCache.photoUrlCache) {
        this.initProcessCache();
      }

      // 检查缓存
      if (this.processCache.photoUrlCache && this.processCache.photoUrlCache.has(url)) {
        return this.processCache.photoUrlCache.get(url);
      }

      // 处理URL
      var processedUrl = url;
      if (typeof url === 'string' && url.startsWith('http://tmp/')) {
        // 这是微信小程序的本地临时文件路径，直接使用
        processedUrl = url;
      }

      // 缓存结果
      if (this.processCache.photoUrlCache) {
        this.processCache.photoUrlCache.set(url, processedUrl);
      }
      return processedUrl;
    },
    // 切换自动上传状态
    toggleAutoUpload: function toggleAutoUpload() {
      this.autoUpload = !this.autoUpload;
      uni.showToast({
        title: this.autoUpload ? '已开启自动上传' : '已关闭自动上传',
        icon: 'none'
      });
    },
    // 优化的照片预览（缓存URL列表）
    previewPhoto: function previewPhoto(index) {
      var _this12 = this;
      // 使用缓存的URL列表，避免重复映射
      if (!this._cachedPhotoUrls || this._cachedPhotoUrls.length !== this.photos.length) {
        this._cachedPhotoUrls = this.photos.map(function (photo) {
          return _this12.getPhotoDisplayUrl(photo);
        });
      }
      uni.previewImage({
        urls: this._cachedPhotoUrls,
        current: index
      });
    },
    // 获取云端照片URL
    getCloudPhotoUrl: function getCloudPhotoUrl(photo) {
      if (typeof photo === 'string') {
        return photo; // 直接返回URL字符串
      }

      return photo.url || photo; // 如果是对象，取url字段
    },
    // 优化的检查员问题照片预览（使用缓存）
    previewInspectionPhoto: function previewInspectionPhoto(index) {
      var _this13 = this;
      // 使用预缓存的URL
      if (!this._cachedInspectionUrls) {
        this._cachedInspectionUrls = this.taskInfo.inspectionPhotos.map(function (photo, i) {
          return _this13.processCache.inspectionUrlCache.get(i) || _this13.getCloudPhotoUrl(photo);
        });
      }
      uni.previewImage({
        urls: this._cachedInspectionUrls,
        current: index
      });
    },
    // 预览之前的整改照片
    previewPreviousPhoto: function previewPreviousPhoto(index) {
      var urls = this.taskInfo.previousPhotos.map(function (photo) {
        return photo.url;
      });
      uni.previewImage({
        urls: urls,
        current: index
      });
    },
    // 优化的提交整改记录
    submitRectification: function submitRectification() {
      var _this14 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10() {
        var uploadedPhotos, finalRemarks, submissionData, result;
        return _regenerator.default.wrap(function _callee10$(_context10) {
          while (1) {
            switch (_context10.prev = _context10.next) {
              case 0:
                if (!(_this14.photos.length === 0)) {
                  _context10.next = 3;
                  break;
                }
                uni.showToast({
                  title: '请至少上传一张照片',
                  icon: 'none'
                });
                return _context10.abrupt("return");
              case 3:
                if (_this14.remarks.trim()) {
                  _context10.next = 6;
                  break;
                }
                uni.showToast({
                  title: '请填写整改说明',
                  icon: 'none'
                });
                return _context10.abrupt("return");
              case 6:
                if (!_this14.submitting) {
                  _context10.next = 8;
                  break;
                }
                return _context10.abrupt("return");
              case 8:
                _this14.submitting = true;
                _context10.prev = 9;
                _context10.next = 12;
                return _this14.preparePhotosForSubmit();
              case 12:
                uploadedPhotos = _context10.sent;
                // 准备提交数据
                // 确保整改说明不超过限制
                finalRemarks = _this14.remarks.trim();
                if (finalRemarks.length > 200) {
                  finalRemarks = finalRemarks.substring(0, 200);
                  console.warn('整改说明被截断到200字符');
                }
                submissionData = {
                  id: _this14.taskId,
                  // 云函数期望的参数名
                  completion_description: finalRemarks,
                  // 云函数期望的字段名
                  completion_photos: uploadedPhotos,
                  completion_status: 'completed'
                };
                uni.showLoading({
                  title: '提交整改记录...'
                });

                // 提交整改记录
                _context10.next = 19;
                return (0, _auth.callCloudFunction)('hygiene-rectification', {
                  action: 'completeRectification',
                  data: submissionData
                });
              case 19:
                result = _context10.sent;
                if (!(result && result.success)) {
                  _context10.next = 25;
                  break;
                }
                _context10.next = 23;
                return _this14.handleSubmitSuccess();
              case 23:
                _context10.next = 26;
                break;
              case 25:
                throw new Error((result === null || result === void 0 ? void 0 : result.message) || '提交整改记录失败');
              case 26:
                _context10.next = 33;
                break;
              case 28:
                _context10.prev = 28;
                _context10.t0 = _context10["catch"](9);
                console.error('提交整改记录失败:', _context10.t0);
                _context10.next = 33;
                return _this14.handleSubmitError(_context10.t0);
              case 33:
                _context10.prev = 33;
                _this14.submitting = false;
                return _context10.finish(33);
              case 36:
              case "end":
                return _context10.stop();
            }
          }
        }, _callee10, null, [[9, 28, 33, 36]]);
      }))();
    },
    // 准备照片数据进行提交
    preparePhotosForSubmit: function preparePhotosForSubmit() {
      var _this15 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee11() {
        var unuploadedPhotos, uploadPromises, uploadResults, successfulUploads, existingPhotos, newPhotos;
        return _regenerator.default.wrap(function _callee11$(_context11) {
          while (1) {
            switch (_context11.prev = _context11.next) {
              case 0:
                // 检查是否有未上传的照片
                unuploadedPhotos = _this15.photos.filter(function (photo) {
                  return !photo.uploaded && !photo.uploading;
                });
                if (!(unuploadedPhotos.length > 0)) {
                  _context11.next = 16;
                  break;
                }
                uni.showLoading({
                  title: '正在上传剩余照片...'
                });

                // 并行上传剩余照片
                uploadPromises = unuploadedPhotos.map(function (photo) {
                  return _this15.uploadSinglePhotoOptimized(photo);
                });
                _context11.next = 6;
                return Promise.allSettled(uploadPromises);
              case 6:
                uploadResults = _context11.sent;
                // 检查上传结果
                successfulUploads = uploadResults.filter(function (result) {
                  return result.status === 'fulfilled' && result.value.success;
                }).map(function (result) {
                  return result.value;
                });
                if (!(successfulUploads.length === 0)) {
                  _context11.next = 11;
                  break;
                }
                uni.hideLoading();
                throw new Error('照片上传失败，请重试');
              case 11:
                // 合并所有已上传的照片
                existingPhotos = _this15.photos.filter(function (photo) {
                  return photo.uploaded;
                }).map(function (photo) {
                  return {
                    url: photo.cloudPath,
                    type: 'rectification',
                    description: ''
                  };
                });
                newPhotos = successfulUploads.map(function (upload) {
                  return {
                    url: upload.cloudPath,
                    type: 'rectification',
                    description: ''
                  };
                });
                return _context11.abrupt("return", [].concat((0, _toConsumableArray2.default)(existingPhotos), (0, _toConsumableArray2.default)(newPhotos)));
              case 16:
                return _context11.abrupt("return", _this15.photos.filter(function (photo) {
                  return photo.uploaded;
                }).map(function (photo) {
                  return {
                    url: photo.cloudPath,
                    type: 'rectification',
                    description: ''
                  };
                }));
              case 17:
              case "end":
                return _context11.stop();
            }
          }
        }, _callee11);
      }))();
    },
    // 准备提交数据
    prepareSubmissionData: function prepareSubmissionData(uploadedPhotos) {
      return {
        id: this.taskId,
        completion_description: this.remarks.trim(),
        completion_photos: uploadedPhotos
      };
    },
    // 处理提交成功
    handleSubmitSuccess: function handleSubmitSuccess() {
      var _this16 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee12() {
        return _regenerator.default.wrap(function _callee12$(_context12) {
          while (1) {
            switch (_context12.prev = _context12.next) {
              case 0:
                uni.hideLoading();

                // 发送事件通知其他页面更新
                uni.$emit('rectificationRecordUpdated', {
                  taskId: _this16.taskId,
                  areaId: _this16.taskInfo.areaId,
                  type: 'completed'
                });
                uni.showToast({
                  title: '提交成功',
                  icon: 'success'
                });
                setTimeout(function () {
                  uni.navigateBack();
                }, 1500);
              case 4:
              case "end":
                return _context12.stop();
            }
          }
        }, _callee12);
      }))();
    },
    // 处理提交错误
    handleSubmitError: function handleSubmitError(error) {
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee13() {
        var errorMessage;
        return _regenerator.default.wrap(function _callee13$(_context13) {
          while (1) {
            switch (_context13.prev = _context13.next) {
              case 0:
                uni.hideLoading();
                errorMessage = '提交失败，请重试';
                if (error.message) {
                  if (error.message.includes('未登录')) {
                    errorMessage = '请先登录';
                  } else if (error.message.includes('权限')) {
                    errorMessage = '您没有权限操作该整改任务';
                  } else {
                    errorMessage = error.message;
                  }
                }
                _context13.next = 5;
                return uni.showModal({
                  title: '提交失败',
                  content: errorMessage,
                  showCancel: false,
                  confirmText: '知道了'
                });
              case 5:
              case "end":
                return _context13.stop();
            }
          }
        }, _callee13);
      }))();
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27)["uniCloud"]))

/***/ }),

/***/ 611:
/*!***************************************************************************************************************!*\
  !*** D:/Xwzc/pages/6s_pkg/rectification-upload.vue?vue&type=style&index=0&id=6e3331a0&lang=scss&scoped=true& ***!
  \***************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_upload_vue_vue_type_style_index_0_id_6e3331a0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./rectification-upload.vue?vue&type=style&index=0&id=6e3331a0&lang=scss&scoped=true& */ 612);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_upload_vue_vue_type_style_index_0_id_6e3331a0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_upload_vue_vue_type_style_index_0_id_6e3331a0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_upload_vue_vue_type_style_index_0_id_6e3331a0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_upload_vue_vue_type_style_index_0_id_6e3331a0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_upload_vue_vue_type_style_index_0_id_6e3331a0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 612:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/6s_pkg/rectification-upload.vue?vue&type=style&index=0&id=6e3331a0&lang=scss&scoped=true& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[605,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/6s_pkg/rectification-upload.js.map