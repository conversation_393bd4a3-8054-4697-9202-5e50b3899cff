<template>
  <view class="page-container">
    <!-- 本周清理状态 -->
    <view class="card">
      <view class="card-header">
        <view class="header-content">
          <view class="card-title">{{ getStatusTitle() }}</view>
          <view class="time-selector" @click="showTimeSelector">
            <text class="time-text">{{ getCurrentTimeRange() }}</text>
            <uni-icons type="down" size="12" color="#007AFF"></uni-icons>
          </view>
        </view>
      </view>
      <view class="card-body">
        <!-- 统计数据加载中状态 -->
        <view v-if="loading" class="stats-loading">
          <view class="loading-content">
            <view class="loading-spinner"></view>
            <text class="loading-text">统计数据加载中...</text>
          </view>
        </view>
        
        <!-- 统计数据 -->
        <view v-else class="stats-grid">
          <view class="stats-item">
            <view class="stats-number success">{{ completedCount }}</view>
            <view class="stats-label">{{ selectedQuickFilter === 'week' ? '已完成' : '已完成整改' }}</view>
          </view>
          <view class="stats-item">
            <view class="stats-number warning">{{ pendingCount }}</view>
            <view class="stats-label">{{ selectedQuickFilter === 'week' ? '待处理' : '待整改' }}</view>
          </view>
          <view class="stats-item">
            <view class="stats-number danger">{{ overdueCount }}</view>
            <view class="stats-label">已逾期</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 我的固定责任区 (每人一个) -->
    <view class="card">
      <view class="card-header">
        <view class="header-content">
          <view class="card-title">固定责任区</view>
          <view class="card-subtitle">每人分配固定责任区进行清理</view>
        </view>
      </view>
      
      <!-- 加载中状态 -->
      <view v-if="loading" class="list-loading">
        <view class="loading-content">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载责任区数据中...</text>
        </view>
      </view>
      
      <template v-else>
        <p-empty-state
          v-if="myFixedAreas.length === 0"
          useIcon
          iconName="info"
          iconColor="#8E8E93"
          text="暂未分配固定责任区"
          description="请联系管理员在责任区检查中进行分配"
        ></p-empty-state>
        
        <view v-for="(area, index) in myFixedAreas" :key="index" class="list-item" @click="openAreaDetail(area)">
        <view class="list-item-icon" :class="['icon-bg-' + area.status]">
          <uni-icons :type="area.icon" size="18" :color="getIconColor(area.status)"></uni-icons>
        </view>
        <view class="list-item-content">
          <view class="list-item-title">
            {{ area.name }}
            <text class="fixed-tag">固定</text>
          </view>
          <view class="list-item-subtitle">{{ area.subtitle }}</view>
        </view>
        <view class="list-item-right">
          <view class="status-badge" :class="['status-' + area.status]">{{ getStatusText(area.status) }}</view>
          <uni-icons type="right" size="14" color="#C7C7CC"></uni-icons>
        </view>
      </view>
      </template>
    </view>

    <!-- 公共责任区 (所有员工共同维护) -->
    <view class="card" v-if="myPublicAreas.length > 0 || (!loading && dataLoaded)">
      <view class="card-header">
        <view class="header-content">
          <view class="card-title">公共责任区</view>
          <view class="card-subtitle">每周轮班公共责任区进行清理</view>
        </view>
      </view>
      
      <!-- 加载中状态与固定责任区共享 -->
      <view v-if="loading" class="list-loading">
        <view class="loading-content">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载责任区数据中...</text>
        </view>
      </view>
      
      <template v-else>
        <p-empty-state
          v-if="myPublicAreas.length === 0"
          useIcon
          iconName="home"
          iconColor="#8E8E93"
          text="暂无公共责任区"
          description="管理员可在责任区检查中添加公共责任区"
        ></p-empty-state>
      
      <view v-for="(area, index) in myPublicAreas" :key="index" class="list-item" @click="openPublicAreaDetail(area)">
        <view class="list-item-icon" :class="['icon-bg-' + area.status]">
          <uni-icons :type="area.icon" size="18" :color="getIconColor(area.status)"></uni-icons>
        </view>
        <view class="list-item-content">
          <view class="list-item-title">
            {{ area.name }}
            <text class="public-tag">公共</text>
          </view>
          <view class="list-item-subtitle">{{ area.subtitle }}</view>
        </view>
        <view class="list-item-right">
          <view class="status-badge" :class="['status-' + area.status]">{{ getStatusText(area.status) }}</view>
          <uni-icons type="right" size="14" color="#C7C7CC"></uni-icons>
        </view>
      </view>
      </template>
    </view>

    <!-- 我的整改记录 -->
    <view class="card">
      <view class="card-header">
        <view class="header-content">
          <view class="card-title">我的整改记录</view>
          <view class="card-subtitle">我的责任区整改记录</view>
        </view>
      </view>
      
      <!-- 加载中状态 -->
      <view v-if="loading" class="list-loading">
        <view class="loading-content">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载整改记录中...</text>
        </view>
      </view>
      
      <template v-else>
        <p-empty-state
          v-if="groupedRectificationTasks.length === 0"
          useIcon
          iconName="checkmarkempty"
          iconColor="#34C759"
          text="暂无整改任务"
          description="当责任区检查发现问题时，会在此显示需要整改的任务"
        ></p-empty-state>
        
        <!-- 时间分组的整改记录列表 -->
        <view v-for="group in groupedRectificationTasks" :key="group.weekKey" class="time-group">
        <!-- 时间分组标题 -->
        <view class="time-group-header" @click="toggleRectificationWeek(group.weekKey)">
          <view class="time-group-title">
            <uni-icons :type="group.expanded ? 'down' : 'right'" size="16" color="#007AFF"></uni-icons>
            <text class="time-title">{{ group.title }}</text>
            <view class="time-count">{{ group.tasks.length }}条</view>
          </view>
        </view>
        
        <!-- 分组任务列表 -->
        <view v-if="group.expanded" class="time-group-content">
          <view v-for="(task, index) in group.tasks" :key="index" class="list-item" @click="openRectificationDetail(task)">
            <view class="list-item-icon" :class="['icon-bg-' + task.status]">
              <uni-icons :type="task.icon" size="18" :color="getIconColor(task.status)"></uni-icons>
            </view>
            <view class="list-item-content">
              <view class="list-item-title">
                {{ task.areaName }}
                <text v-if="task.areaType === 'public'" class="public-tag">公共</text>
                <text v-else class="fixed-tag">固定</text>
              </view>
              <view class="list-item-subtitle">{{ task.subtitle }}</view>
              <view class="task-time-info">
                <text class="time-label">发现时间：</text>
                <text class="time-value">{{ task.issueFoundDate }}</text>
                <text v-if="task.completedDate" class="time-label"> · 完成时间：</text>
                <text v-if="task.completedDate" class="time-value">{{ task.completedDate }}</text>
              </view>
            </view>
            <view class="list-item-right">
              <view class="status-badge" :class="['status-' + task.status]">{{ getStatusText(task.status) }}</view>
              <uni-icons type="right" size="14" color="#C7C7CC"></uni-icons>
            </view>
          </view>
        </view>
      </view>
      </template>
    </view>

    <!-- 我的清理记录 -->
    <view class="card">
      <view class="card-header">
        <view class="header-content">
          <view class="card-title">我的清理记录</view>
          <view class="card-subtitle">最近的清理工作记录</view>
        </view>
      </view>
      
      <!-- 加载中状态 -->
      <view v-if="loading" class="loading-container">
        <view class="loading-content">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载清理记录中...</text>
        </view>
      </view>
      
      <template v-else>
        <p-empty-state
          v-if="cleaningRecords.list.length === 0"
          useIcon
          iconName="checkmarkempty"
          iconColor="#34C759"
          text="暂无清理记录"
          description="完成清理任务后，记录会在此显示"
        ></p-empty-state>
        
        <view v-for="(record, index) in cleaningRecords.list" :key="index" class="list-item" @click="openCleaningRecordDetail(record)">
          <view class="list-item-icon" :class="['icon-bg-' + record.status]">
            <uni-icons :type="record.icon" size="18" :color="getIconColor(record.status)"></uni-icons>
          </view>
          <view class="list-item-content">
            <view class="list-item-title">
              {{ record.areaName }}
              <text v-if="record.areaType === 'public'" class="public-tag">公共</text>
              <text v-else class="fixed-tag">固定</text>
            </view>
            <view class="list-item-subtitle">{{ record.formattedDate }}</view>
            <view class="cleaning-record-meta">
              <text class="meta-label">照片：</text>
              <text class="meta-value">{{ record.photos }}张</text>
              <text v-if="record.remark" class="meta-label"> · 备注：</text>
              <text v-if="record.remark" class="meta-value meta-remark">{{ record.remark.substring(0, 15) }}{{ record.remark.length > 15 ? '...' : '' }}</text>
            </view>
          </view>
          <view class="list-item-right">
            <view class="status-badge" :class="['status-' + record.status]">{{ getCleaningRecordStatusText(record.status) }}</view>
            <uni-icons type="right" size="14" color="#C7C7CC"></uni-icons>
          </view>
        </view>
        
        <!-- 分页加载更多按钮 -->
        <view v-if="cleaningRecords.hasMore" class="load-more-section">
          <button 
            class="load-more-btn" 
            :disabled="cleaningRecords.loading"
            @click="loadMoreCleaningRecords"
          >
            <view 
              v-if="cleaningRecords.loading" 
              class="btn-loading-spinner"
            ></view>
            <text>{{ cleaningRecords.loading ? '加载中...' : '加载更多' }}</text>
          </button>
        </view>
      </template>
    </view>
    
    <!-- 底部安全间距 -->
    <view class="bottom-safe-area"></view>

    <!-- 日历式时间选择弹窗 -->
    <uni-popup ref="timePopup" type="bottom" border-radius="16rpx 16rpx 0 0">
      <view class="date-picker-popup">
        <view class="popup-header">
          <text class="popup-title">筛选时间</text>
          <view class="popup-close" @click="closeDatePicker">
            <uni-icons type="close" size="18" color="#8E8E93"></uni-icons>
          </view>
        </view>

        <!-- 快捷选择模式 -->
        <view v-if="dateFilterMode === 'quick'" class="quick-date-section">
          <view class="section-title">快捷选择</view>
          <view class="quick-options">
            <view v-for="option in quickDateOptions" :key="option.value" class="quick-option"
              :class="{ active: selectedQuickFilter === option.value }"
              @click="selectQuickDateOption(option)">
              <text class="quick-text">{{ option.label }}</text>
              </view>
            </view>
            </view>

        <!-- 日期范围选择模式 -->
        <view v-else class="range-date-section">
          <!-- 简洁的日历选择器 -->
          <view class="calendar-section">
            <view class="calendar-header">
              <text class="calendar-tip">在日历上点击选择开始和结束日期</text>
          </view>

            <!-- uni-calendar 日历组件 -->
            <uni-calendar ref="calendar" :range="true" :date="calendarDate" :start-date="calendarStartDate"
              :end-date="calendarEndDate" @change="onCalendarChange"
              @monthSwitch="onMonthSwitch"></uni-calendar>

            <!-- 已选择的日期范围显示 -->
            <view v-if="customDateRange.startDate && customDateRange.endDate" class="selected-range">
              <view class="range-item">
                <text class="range-label">开始日期</text>
                <text class="range-value">{{ formatSelectedDate(customDateRange.startDate) }}</text>
              </view>
              <view class="range-separator">→</view>
              <view class="range-item">
                <text class="range-label">结束日期</text>
                <text class="range-value">{{ formatSelectedDate(customDateRange.endDate) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { callCloudFunction } from '@/utils/auth.js';

export default {
  name: 'MyAreas',
  data() {
    return {
      // 日期筛选方式：'quick' 快捷选择, 'range' 日期范围选择
      dateFilterMode: 'quick',
      selectedTimeFilter: 'week',

      // 自定义日期范围
      customDateRange: {
        startDate: '',
        endDate: ''
      },

      // 日历组件相关数据
      calendarDate: '',
      calendarStartDate: '',
      calendarEndDate: '',

      // 防重复查询的定时器
      refreshTimer: null,
      calendarChangeTimer: null,

      // 快捷日期选项
      quickDateOptions: [
        { label: '本周', value: 'week' },
        { label: '上周', value: 'last_week' },
        { label: '本月', value: 'month' },
        { label: '自定义', value: 'custom' }
      ],
      selectedQuickFilter: 'week',
      
      // 我的责任区（从API加载）
      myAreas: [],
      
      // 整改任务记录（从API加载）
      rectificationTasks: [],
      
      // 我的清理记录（分页结构）
      cleaningRecords: {
        list: [],           // 当前显示的记录列表
        pageSize: 10,       // 每页显示数量
        hasMore: true,      // 是否还有更多数据
        loading: false      // 加载状态
      },
      allCleaningRecords: [], // 所有清理记录（用于分页）
      
      // 加载状态
      loading: false,
      dataLoaded: false, // 标记数据是否已加载
      needsRefresh: false, // 标记是否需要刷新数据
      
      // 展开的整改记录周次（动态设置，默认展开最新的）
      expandedRectificationWeeks: [],
      
      // 时间计算缓存（避免重复计算）
      currentWeekCache: null,
      
      // 区域分类缓存（避免重复过滤）
      areasCache: null
    }
  },
  computed: {
    // 固定责任区（永远显示本周状态，不受时间筛选影响）
    myFixedAreas() {
      // 使用缓存避免重复计算
      if (!this.areasCache || this.areasCache.sourceLength !== this.myAreas.length) {
        this.areasCache = {
          sourceLength: this.myAreas.length,
          fixed: this.myAreas.filter(area => area.type === 'fixed'),
          public: this.myAreas.filter(area => area.type === 'public')
        };
      }
      return this.areasCache.fixed;
    },
    // 公共责任区（永远显示本周状态，不受时间筛选影响）
    myPublicAreas() {
      // 使用缓存避免重复计算
      if (!this.areasCache || this.areasCache.sourceLength !== this.myAreas.length) {
        this.areasCache = {
          sourceLength: this.myAreas.length,
          fixed: this.myAreas.filter(area => area.type === 'fixed'),
          public: this.myAreas.filter(area => area.type === 'public')
        };
      }
      return this.areasCache.public;
    },
    
              completedCount() {
        try {
          // 如果选择的是本周，统计本周已完成的责任区 + 本周已完成的整改记录
          if (this.selectedQuickFilter === 'week') {
            const completedAreas = this.myAreas.filter(area => area.status === 'completed').length || 0;
            const completedRectifications = this.getThisWeekRectificationTasks().filter(task => 
              task.status === 'completed' || task.status === 'verified'
            ).length || 0;
            return completedAreas + completedRectifications;
          } else {
            // 历史时间范围：只统计该时间范围内的整改记录（责任区状态是当前状态，不应该按历史统计）
            const count = this.filteredRectificationTasks.filter(task => 
              task.status === 'completed' || task.status === 'verified'
            ).length || 0;
            return count;
          }
        } catch (error) {
          console.error('计算已完成数量出错:', error);
          return 0;
        }
      },
    pendingCount() {
      try {
        // 如果选择的是本周，统计本周真正需要员工处理的任务
        if (this.selectedQuickFilter === 'week') {
          // 责任区：只统计真正可以清理的（排除"未到时间"的公共责任区）
          const pendingAreas = this.myAreas.filter(area => 
            area.status === 'pending' // 只统计真正待清理的，不包括scheduled(未到时间)
          ).length || 0;
          // 整改记录：只统计真正需要员工操作的（排除"待复查"）
          const pendingRectifications = this.getThisWeekRectificationTasks().filter(task => 
            task.status === 'pending_rectification' // 只统计待整改，不包括pending_review(待复查)
          ).length || 0;
          return pendingAreas + pendingRectifications;
        } else {
          // 历史时间范围：只统计该时间范围内需要员工操作的整改记录
          const count = this.filteredRectificationTasks.filter(task => 
            task.status === 'pending_rectification' // 只统计待整改
          ).length || 0;
          return count;
        }
      } catch (error) {
        console.error('计算待处理数量出错:', error);
        return 0;
      }
    },
    overdueCount() {
      try {
        // 如果选择的是本周，统计本周已逾期的责任区 + 逾期整改记录
        if (this.selectedQuickFilter === 'week') {
          const overdueAreas = this.myAreas.filter(area => area.status === 'overdue').length || 0;
          // 整改记录逾期：使用更精确的逾期判断逻辑
          const overdueRectifications = this.getThisWeekRectificationTasks().filter(task => 
            task.status === 'overdue' || this.isRectificationOverdue(task)
          ).length || 0;
          return overdueAreas + overdueRectifications;
        } else {
          // 历史时间范围：只统计该时间范围内的逾期整改记录
          const count = this.filteredRectificationTasks.filter(task => 
            task.status === 'overdue' || this.isRectificationOverdue(task)
          ).length || 0;
          return count;
        }
      } catch (error) {
        console.error('计算逾期数量出错:', error);
        return 0;
      }
    },
    // 根据时间筛选的整改任务记录
    filteredRectificationTasks() {
      let tasks = this.rectificationTasks;
      
      // 基于日期范围的直接筛选
      const dateRange = this.getCurrentDateRange();
      if (dateRange) {
        tasks = tasks.filter(task => {
          if (!task.created_at) return false;
          const taskDate = new Date(task.created_at);
          return taskDate >= dateRange.start && taskDate <= dateRange.end;
        });
      }
      
      return tasks;
    },
    
    // 时间分组的整改任务记录
    groupedRectificationTasks() {
      const tasks = this.filteredRectificationTasks;
      const grouped = {};
      
      tasks.forEach(task => {
        // 重新计算周键以确保使用最新的时区逻辑
        // 使用原始的created_at而不是格式化后的issueFoundDate
        const weekKey = this.getWeekKey(task.created_at);
        if (!grouped[weekKey]) {
          grouped[weekKey] = {
            weekKey,
            title: this.getWeekTitle(weekKey),
            tasks: [],
            expanded: this.expandedRectificationWeeks.includes(weekKey)
          };
        }
        grouped[weekKey].tasks.push(task);
      });
      
      // 按周次降序排序
      const sortedGroups = Object.values(grouped).sort((a, b) => {
        // 更精确的排序：先按年份，再按周数
        const [yearA, weekA] = a.weekKey.split('-W').map(x => parseInt(x));
        const [yearB, weekB] = b.weekKey.split('-W').map(x => parseInt(x));
        
        if (yearA !== yearB) {
          return yearB - yearA; // 年份降序
        }
        return weekB - weekA; // 同年内周数降序
      });
      
      // 同步展开状态：完全基于用户手动操作的状态
      sortedGroups.forEach(group => {
        group.expanded = this.expandedRectificationWeeks.includes(group.weekKey);
      });
      

      
      return sortedGroups;
    }
  },
  created() {
  },
  onLoad() {
    this.loadMyAreas();
    
    // 监听清理记录更新事件
    uni.$on('cleaningRecordUpdated', this.handleRecordUpdated);
    // 监听整改记录更新事件
    uni.$on('rectificationRecordUpdated', this.handleRecordUpdated);
  },
  onUnload() {
    // 移除事件监听
    uni.$off('cleaningRecordUpdated', this.handleRecordUpdated);
    uni.$off('rectificationRecordUpdated', this.handleRecordUpdated);
    
    // 清理定时器
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }
    if (this.calendarChangeTimer) {
      clearTimeout(this.calendarChangeTimer);
    }
  },
  onShow() {
    // 页面重新显示时，只在确实需要刷新数据的情况下才刷新
    // 使用静默刷新，避免显示加载动画
    if (this.dataLoaded && !this.loading && this.needsRefresh) {
      this.silentRefreshData();
      this.needsRefresh = false; // 重置刷新标记
    }
  },
  methods: {

    
    // 获取本周的整改任务
    getThisWeekRectificationTasks() {
      try {
        const now = new Date();
        const beijingNow = this.toBeijingTime(now);
        
        if (!beijingNow) {
          console.error('无法获取北京时间');
          return [];
        }
        
        const weekStart = this.getWeekStart(beijingNow);
        const weekEnd = this.getWeekEnd(beijingNow);
        
        if (!weekStart || !weekEnd) {
          console.error('无法计算本周时间范围');
          return [];
        }
        
        if (!Array.isArray(this.rectificationTasks)) {
          return [];
        }
        
        return this.rectificationTasks.filter(task => {
          if (!task || !task.created_at) return false;
          try {
            const taskDate = new Date(task.created_at);
            if (isNaN(taskDate.getTime())) return false;
            return taskDate >= weekStart && taskDate <= weekEnd;
          } catch (error) {
            console.error('处理任务日期出错:', error, task);
            return false;
          }
        });
      } catch (error) {
        console.error('获取本周整改任务出错:', error);
        return [];
      }
    },

    // 判断整改任务是否逾期（严格按周计算）
    isRectificationOverdue(task) {
      if (!task || task.status === 'completed' || task.status === 'verified') {
        return false; // 已完成的任务不算逾期
      }
      
      if (!task.created_at) {
        return false;
      }
      
      // 只有待整改和待分配的任务才能算逾期
      if (task.status !== 'pending_rectification' && task.status !== 'pending_assignment') {
        return false;
      }
      
      const now = new Date();
      const beijingNow = this.toBeijingTime(now);
      
      // 获取任务创建时间所在周的结束时间（周日23:59:59）
      const taskCreatedTime = new Date(task.created_at);
      const beijingCreatedTime = this.toBeijingTime(taskCreatedTime);
      const taskWeekEnd = this.getWeekEnd(beijingCreatedTime);
      
      // 如果当前时间超过了任务创建周的周日23:59:59，则为逾期
      return beijingNow.getTime() > taskWeekEnd.getTime();
    },

    // 加载用户责任区数据
    async loadMyAreas() {
      this.loading = true;
      try {
        // 并行加载所有数据，等待全部完成后统一显示
        await Promise.all([
          this.loadUserAssignments(),
          this.loadUserRectificationTasks(),
          this.loadMyCleaningRecords()
        ]);
        
      } catch (error) {
        uni.showToast({
          title: '加载数据失败',
          icon: 'none',
          duration: 2000
        });
        // 设置为空数组以显示空状态
        this.myAreas = [];
        this.rectificationTasks = [];
        this.cleaningRecords.list = [];
      } finally {
        this.loading = false;
        this.dataLoaded = true; // 标记数据已加载
      }
    },

    // 加载用户的责任区分配
    async loadUserAssignments() {
      try {
        // 调用云函数获取当前用户的责任区分配
        const result = await callCloudFunction('hygiene-assignments', {
          action: 'getUserAssignments',
          data: {}
        });

        if (result && result.success) {
          const assignments = result.data || [];
          
          // 将分配记录转换为页面需要的格式
          this.myAreas = [];
          
          // 收集所有责任区ID，用于批量查询清理记录
          const areaIds = [];
          const areaDataMap = new Map();
          
          assignments.forEach((assignment) => {
            if (assignment.areas_info && Array.isArray(assignment.areas_info)) {
              assignment.areas_info.forEach((area) => {
                const areaId = area._id || area.id;
                areaIds.push(areaId);
                
                // 先创建基础数据结构
                const areaData = {
                  id: areaId,
                  name: area.name,
                  icon: area.type === 'fixed' ? 'gear' : 'videocam',
                  type: area.type || 'fixed',
                  weeklyRequired: area.type === 'fixed',
                  location: area.location,
                  description: area.description,
                  last_clean_date: null // 将通过清理记录查询获得真实值
                };
                
                // 如果是公共责任区，添加排班信息
                if (area.type === 'public' && area.scheduled_day !== null) {
                  areaData.scheduledDay = area.scheduled_day;
                  areaData.scheduled_day = area.scheduled_day;
                }
                
                areaDataMap.set(areaId, areaData);
              });
            }
          });
          
          // 查询这些责任区的最新清理记录
          if (areaIds.length > 0) {
            await this.loadLatestCleaningRecords(areaIds, areaDataMap);
          }
          
          // 转换为最终格式
          this.myAreas = Array.from(areaDataMap.values()).map(area => {
            const status = this.calculateAreaStatus(area);
            return {
              ...area,
              subtitle: this.generateAreaSubtitle(area),
              status: status,
              icon: this.getAreaIcon(status),
              lastCleaningDate: area.last_clean_date
            };
          });
          
          // 清理缓存，确保计算属性重新计算
          this.areasCache = null;
        } else {
          this.myAreas = [];
        }
      } catch (error) {
        this.myAreas = [];
      }
    },

    // 加载责任区的最新清理记录
    async loadLatestCleaningRecords(areaIds, areaDataMap) {
      try {
        // 获取本周时间范围（缓存计算结果）
        if (!this.currentWeekCache) {
          const now = new Date();
          this.currentWeekCache = {
            now,
            weekStart: this.getWeekStart(now),
            weekEnd: this.getWeekEnd(now)
          };
        }
        const { weekStart, weekEnd } = this.currentWeekCache;
        
        // 批量查询所有责任区的清理记录（一次API调用替代多次）
        try {
          const result = await callCloudFunction('hygiene-cleaning', {
            action: 'getBatchCleaningRecords',
            data: {
              area_ids: areaIds,
              start_date: weekStart.toISOString(),
              end_date: weekEnd.toISOString(),
              latest_only: true // 只返回每个区域的最新记录
            }
          });

          if (result && result.success && result.data) {
            // 处理批量返回的记录
            result.data.forEach(record => {
              if (areaDataMap.has(record.area_id)) {
                areaDataMap.get(record.area_id).last_clean_date = record.cleaning_date;
              }
            });
          }
        } catch (error) {
          // 如果批量查询失败，回退到单独查询

          const promises = areaIds.map(async (areaId) => {
            try {
              const result = await callCloudFunction('hygiene-cleaning', {
                action: 'getCleaningRecords',
                data: {
                  area_id: areaId,
                  start_date: weekStart.toISOString(),
                  end_date: weekEnd.toISOString(),
                  pageSize: 1 // 只需要最新的1条记录
                }
              });

              if (result && result.success && result.data && result.data.list && result.data.list.length > 0) {
                const latestRecord = result.data.list[0];
                if (areaDataMap.has(areaId)) {
                  areaDataMap.get(areaId).last_clean_date = latestRecord.cleaning_date;
                }
              }
            } catch (error) {
              // 静默处理单个责任区查询失败
            }
          });
          await Promise.all(promises);
        }
        
      } catch (error) {
        // 即使加载失败也不影响主流程，只是状态可能不准确
      }
    },

    // 加载用户的清理记录
    async loadMyCleaningRecords() {
      try {
        // 设置加载状态
        this.cleaningRecords.loading = true;
        
        // 构建请求参数，包含日期范围筛选
        const requestData = {
          pageSize: 20 // 显示最近20条记录
        };
        
        // 如果有日期范围筛选，添加到请求参数中
        const dateRange = this.getCurrentDateRange();
        if (dateRange) {
          requestData.start_date = dateRange.start.toISOString();
          requestData.end_date = dateRange.end.toISOString();
        }
        
        // 调用云函数获取当前用户的清理记录
        const result = await callCloudFunction('hygiene-cleaning', {
          action: 'getMyCleaningRecords',
          data: requestData
        });

        if (result && result.success) {
          const records = result.data || [];
          
          // 将清理记录转换为页面需要的格式
          const formattedRecords = records.map(record => {
            return {
              id: record._id || record.id,
              areaId: record.area_id,
              areaName: record.area_name || '未知责任区',
              areaType: record.area_type || 'fixed',
              cleaningDate: record.cleaning_date,
              formattedDate: this.formatDateTime(record.cleaning_date),
              photos: record.photos ? record.photos.length : 0,
              remark: record.remark || '',
              status: 'completed', // 所有记录都是已完成
              icon: 'checkmarkempty',
              week: this.getWeekKey(record.cleaning_date)
            };
          });
          
          // 保存所有记录用于分页
          this.allCleaningRecords = formattedRecords;
          
          // 设置首页数据
          this.cleaningRecords.list = formattedRecords.slice(0, this.cleaningRecords.pageSize);
          this.cleaningRecords.hasMore = records.length > this.cleaningRecords.pageSize;
          
          // 按清理时间降序排序
          this.cleaningRecords.list.sort((a, b) => 
            new Date(b.cleaningDate) - new Date(a.cleaningDate)
          );
        } else {
          this.cleaningRecords.list = [];
          this.cleaningRecords.hasMore = false;
        }
      } catch (error) {
        this.cleaningRecords.list = [];
        this.cleaningRecords.hasMore = false;
      } finally {
        // 重置加载状态
        this.cleaningRecords.loading = false;
      }
    },

    // 加载更多清理记录（从本地缓存的数据中分页显示）
    async loadMoreCleaningRecords() {
      if (this.cleaningRecords.loading || !this.cleaningRecords.hasMore) {
        return;
      }
      
      this.cleaningRecords.loading = true;
      
      const currentDisplayed = this.cleaningRecords.list.length;
      const nextPageSize = this.cleaningRecords.pageSize;
      const nextPageEnd = currentDisplayed + nextPageSize;
      
      // 从所有记录中获取下一页数据
      if (this.allCleaningRecords && this.allCleaningRecords.length > currentDisplayed) {
        const nextPageData = this.allCleaningRecords.slice(currentDisplayed, nextPageEnd);
        
        // 追加新数据
        this.cleaningRecords.list = [...this.cleaningRecords.list, ...nextPageData];
        
        // 检查是否还有更多数据
        this.cleaningRecords.hasMore = this.cleaningRecords.list.length < this.allCleaningRecords.length;
      } else {
        this.cleaningRecords.hasMore = false;
      }
      
      this.cleaningRecords.loading = false;
    },

    // 加载用户的整改任务记录
    async loadUserRectificationTasks() {
      try {
        // 构建请求参数，包含日期范围筛选
        const requestData = {};
        
        // 如果有日期范围筛选，添加到请求参数中
        const dateRange = this.getCurrentDateRange();
        if (dateRange) {
          requestData.start_date = dateRange.start.toISOString();
          requestData.end_date = dateRange.end.toISOString();
        }
        
        // 调用云函数获取当前用户的整改记录
        const result = await callCloudFunction('hygiene-rectification', {
          action: 'getMyRectifications',
          data: requestData
        });

        if (result && result.success) {
          const tasks = result.data || [];
          
          // 将整改记录转换为页面需要的格式
          this.rectificationTasks = tasks.map(task => {
            const weekKey = this.getWeekKey(task.created_at);
            
            // 计算实际状态（考虑时间逾期）
            const actualStatus = this.calculateRectificationStatus(task);
            
            // 确保有有效的ID（支持多种可能的ID字段）
            const taskId = task._id || task.id || task.issue_id || task.task_id;
            if (!taskId) {
              return null; // 跳过无效的任务数据
            }
            
            return {
              id: taskId,
              areaId: task.area_id,
              areaName: task.area_name,
              subtitle: this.formatDescription(task.issue_description || task.description),
              status: actualStatus,
              originalStatus: task.status, // 保留数据库原始状态
              icon: this.getTaskIcon(actualStatus),
              areaType: task.area_type || 'fixed',
              week: weekKey,
              issueFoundDate: this.formatDate(task.created_at),
              completedDate: task.completed_at ? this.formatDate(task.completed_at) : null,
              assignee_id: task.assignee_id,
              assignee_name: task.assignee_name,
              created_at: task.created_at, // 保留原始创建时间用于筛选
              deadline: task.deadline // 保留截止时间用于逾期判断
            };
          }).filter(task => task && task.id); // 过滤掉null和没有ID的任务
          
          // 设置默认展开最新的周
          this.setDefaultExpandedWeek();
        } else {
          this.rectificationTasks = [];
        }
      } catch (error) {
        this.rectificationTasks = [];
      }
    },

    // 生成责任区副标题
    generateAreaSubtitle(area) {
      if (area.type === 'fixed') {
        // 固定责任区：显示本周状态
        if (area.last_clean_date) {
          const now = new Date();
          const weekStart = this.getWeekStart(now);
          const weekEnd = this.getWeekEnd(now);
          const cleanDate = new Date(area.last_clean_date);
          
          // 检查清理日期是否在本周内
          if (cleanDate >= weekStart && cleanDate <= weekEnd) {
            const formattedDate = this.formatDate(area.last_clean_date);
            return `本周已清理 · ${formattedDate}`;
          } else {
            return '本周待清理 · 每周完成一次';
          }
        } else {
          return '本周待清理 · 每周完成一次';
        }
      } else if (area.type === 'public') {
        // 公共责任区：根据排班和状态显示
        if (area.scheduled_day !== null) {
          if (area.last_clean_date) {
            const cleanDate = this.formatDate(area.last_clean_date);
            return `本周已清理 · ${cleanDate}`;
          } else {
            const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
            const scheduledDay = area.scheduled_day === 0 ? 0 : area.scheduled_day;
            
            // 根据状态显示不同的副标题
            if (area.status === 'scheduled') {
              return `计划${weekDays[scheduledDay]}清理 · 未到时间`;
            } else if (area.status === 'pending') {
              return `计划${weekDays[scheduledDay]}清理 · 可以清理`;
            } else if (area.status === 'overdue') {
              return `计划${weekDays[scheduledDay]}清理 · 已逾期`;
            } else {
              return `计划${weekDays[scheduledDay]}清理`;
            }
          }
        } else {
          return '未设置清理日程';
        }
      }
      return '待处理';
    },

    // 计算责任区状态
    calculateAreaStatus(area) {
      // 使用缓存的时间计算结果
      if (!this.currentWeekCache) {
        const now = new Date();
        this.currentWeekCache = {
          now,
          weekStart: this.getWeekStart(now),
          weekEnd: this.getWeekEnd(now)
        };
      }
      const { now, weekStart, weekEnd } = this.currentWeekCache;
      
      if (area.type === 'fixed') {
        // 固定责任区：每周循环，从周一到周日
        if (area.last_clean_date) {
          const cleanDate = new Date(area.last_clean_date);
          if (cleanDate >= weekStart && cleanDate <= weekEnd) {
            return 'completed';
          }
        }
        
        // 固定责任区逾期逻辑：只有过了本周且本周没有清理才算逾期
        // 本周内（周一到周日）都应该显示"待清理"，不算逾期
        // 逾期判断：已经是下周了，但上周没有完成清理
        const nextWeekStart = new Date(weekEnd);
        nextWeekStart.setDate(nextWeekStart.getDate() + 1); // 下周一
        
        if (now >= nextWeekStart) { // 已经到了下周
          // 检查上周（当前计算的这一周）是否有清理记录
          if (!area.last_clean_date) {
            return 'overdue'; // 没有任何清理记录
          }
          
          const cleanDate = new Date(area.last_clean_date);
          if (!(cleanDate >= weekStart && cleanDate <= weekEnd)) {
            return 'overdue'; // 上周没有清理记录
          }
        }
        
        // 默认为待清理状态
        return 'pending';
      } else if (area.type === 'public') {
        // 公共责任区：根据排班计算状态
        if (area.scheduled_day !== null) {
          if (area.last_clean_date) {
            const cleanDate = new Date(area.last_clean_date);
            if (cleanDate >= weekStart && cleanDate <= weekEnd) {
              return 'completed';
            }
          }
          
          // 公共责任区状态逻辑：严格按排班日的0点到23:59分
          const beijingNow = this.toBeijingTime(now);
          const scheduledDay = area.scheduled_day === 0 ? 0 : area.scheduled_day; // 0=周日, 1=周一, ..., 6=周六
          
          // 计算本周排班日的开始和结束时间
          const beijingWeekStart = this.getWeekStart(beijingNow);
          const scheduledDate = new Date(beijingWeekStart);
          
          if (scheduledDay === 0) {
            // 周日：本周的最后一天
            scheduledDate.setDate(beijingWeekStart.getDate() + 6);
          } else {
            // 周一到周六：相应调整天数
            scheduledDate.setDate(beijingWeekStart.getDate() + scheduledDay - 1);
          }
          
          const scheduledStart = new Date(scheduledDate);
          scheduledStart.setHours(0, 0, 0, 0); // 排班日的0点
          
          const scheduledEnd = new Date(scheduledDate);
          scheduledEnd.setHours(23, 59, 59, 999); // 排班日的23:59:59
          
          const currentTime = beijingNow.getTime();
          
          // 如果当前时间在排班日内（0点到23:59分），可以清理
          if (currentTime >= scheduledStart.getTime() && currentTime <= scheduledEnd.getTime()) {
            return 'pending';
          }
          // 如果当前时间超过了排班日的23:59分，则逾期
          else if (currentTime > scheduledEnd.getTime()) {
            return 'overdue';
          }
          // 如果当前时间还未到排班日的0点，显示为"未到时间"
          else {
            return 'scheduled';
          }
        }
        return 'pending';
      }
      return 'pending';
    },

    // 获取任务图标
    getTaskIcon(status) {
      const iconMap = {
        'completed': 'checkmarkempty',
        'pending_rectification': 'refreshempty',
        'pending_assignment': 'refreshempty',  // 待分配，使用刷新图标
        'pending_review': 'eye',
        'overdue': 'closeempty',
        'rejected': 'closeempty',  // 整改不达标，使用关闭图标
        'verified': 'checkmarkempty',  // 整改合格，使用对勾图标
        'approved': 'checkmarkempty',  // 复查通过，使用对勾图标
        'in_progress': 'gear'  // 整改中，使用齿轮图标
      };
      return iconMap[status] || 'help';
    },

    // 获取责任区图标
    getAreaIcon(status) {
      const iconMap = {
        'completed': 'checkmarkempty',    // 已完成 - 对勾
        'pending': 'minus',              // 待清理 - 减号
        'overdue': 'closeempty',         // 已逾期 - 关闭
        'scheduled': 'calendar'          // 未到时间 - 日历
      };
      return iconMap[status] || 'gear';  // 默认齿轮图标
    },

    // 转换为北京时间的工具函数
    toBeijingTime(dateInput) {
      if (!dateInput) {
        return null;
      }
      
      try {
        const inputDate = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
        if (isNaN(inputDate.getTime())) {
          return null;
        }
        
        // 小程序环境中 toLocaleString 可能不稳定，使用更兼容的方式
        // 手动计算北京时间（UTC+8）
        const utcTime = inputDate.getTime();
        const beijingTime = new Date(utcTime + 8 * 60 * 60 * 1000);
        
        // 检查计算结果是否有效
        if (isNaN(beijingTime.getTime())) {
          return null;
        }
        
        // 返回标准的Date对象，直接使用正确的北京时间
        return {
          getFullYear: () => beijingTime.getUTCFullYear(),
          getMonth: () => beijingTime.getUTCMonth(),
          getDate: () => beijingTime.getUTCDate(),
          getDay: () => beijingTime.getUTCDay(),
          getHours: () => beijingTime.getUTCHours(),
          getMinutes: () => beijingTime.getUTCMinutes(),
          getSeconds: () => beijingTime.getUTCSeconds(),
          getTime: () => beijingTime.getTime(),
          toISOString: () => beijingTime.toISOString(),
          // 兼容Date对象的比较
          valueOf: () => beijingTime.getTime()
        };
      } catch (error) {
        console.error('北京时间转换失败:', error, dateInput);
        return null;
      }
    },

    // 获取周次键值（统一使用北京时间）
    getWeekKey(dateString) {
      try {
        if (!dateString) return '';
        
        const beijingDate = this.toBeijingTime(dateString);
        if (!beijingDate) {
          console.error('无法转换为北京时间:', dateString);
          return '';
        }
        
        const year = beijingDate.getFullYear();
        const weekNum = this.getWeekNumber(beijingDate);
        
        if (isNaN(year) || isNaN(weekNum) || year < 1900 || year > 2100) {
          console.error('日期参数异常:', { year, weekNum, dateString });
          return '';
        }
        
        const weekKey = `${year}-W${weekNum.toString().padStart(2, '0')}`;
        
        return weekKey;
      } catch (error) {
        console.error('生成周次键值出错:', error, dateString);
        return '';
      }
    },

    // 获取日期对应的周数（ISO 8601标准，周一为一周开始）
    getWeekNumber(dateObj) {
      try {
        if (!dateObj) {
          return 1; // 返回默认值
        }
        
        // 兼容自定义日期对象和原生Date对象
        let year, month, day, dayOfWeek;
        
        if (typeof dateObj.getFullYear === 'function') {
          // 自定义日期对象或原生Date对象
          year = dateObj.getFullYear();
          month = dateObj.getMonth(); // 0-11
          day = dateObj.getDate(); // 1-31
          dayOfWeek = dateObj.getDay(); // 0=周日, 1=周一
        } else {
          // 备用：原生Date对象
          const d = new Date(dateObj);
          if (isNaN(d.getTime())) {
            return 1; // 返回默认值
          }
          year = d.getFullYear();
          month = d.getMonth();
          day = d.getDate();
          dayOfWeek = d.getDay();
        }
        
        // 检查获取的值是否有效
        if (isNaN(year) || isNaN(month) || isNaN(day) || isNaN(dayOfWeek)) {
          return 1;
        }
        
        // 创建该日期的Date对象用于计算
        const d = new Date(year, month, day, 12, 0, 0, 0);
        if (isNaN(d.getTime())) {
          return 1; // 返回默认值
        }
        
        // 转换为ISO标准（1=周一, 2=周二, ..., 7=周日）
        const isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;
        
        // 找到这一周的周四（ISO 8601标准：包含周四的周属于该年）
        const thursday = new Date(d);
        thursday.setDate(d.getDate() + 4 - isoDayOfWeek);
        
        if (isNaN(thursday.getTime())) {
          return 1; // 返回默认值
        }
        
        // 获取周四所在的年份
        const thursdayYear = thursday.getFullYear();
        
        // 计算该年第一个周四
        const firstThursday = new Date(thursdayYear, 0, 4);
        const firstThursdayDayOfWeek = firstThursday.getDay();
        const firstThursdayIsoDay = firstThursdayDayOfWeek === 0 ? 7 : firstThursdayDayOfWeek;
        
        // 调整到第一个周四
        firstThursday.setDate(4 + (4 - firstThursdayIsoDay));
        
        if (isNaN(firstThursday.getTime())) {
          return 1; // 返回默认值
        }
        
        // 计算周数
        const weekNum = Math.floor((thursday - firstThursday) / (7 * 24 * 60 * 60 * 1000)) + 1;
        
        return isNaN(weekNum) ? 1 : weekNum;
      } catch (error) {
        console.error('计算周数出错:', error, dateObj);
        return 1; // 返回默认值
      }
    },

    // 获取一周的开始日期（周一 00:00:00）- 与getWeekNumber保持一致
    getWeekStart(dateObj) {
      if (!dateObj) {
        return null;
      }
      
      try {
        // 兼容自定义日期对象和原生Date对象
        let year, month, day, dayOfWeek;
        
        if (typeof dateObj.getFullYear === 'function') {
          // 自定义日期对象或原生Date对象
          year = dateObj.getFullYear();
          month = dateObj.getMonth(); // 0-11
          day = dateObj.getDate(); // 1-31
          dayOfWeek = dateObj.getDay(); // 0=周日, 1=周一
        } else {
          // 备用：原生Date对象
          const d = new Date(dateObj);
          if (isNaN(d.getTime())) {
            return null;
          }
          year = d.getFullYear();
          month = d.getMonth();
          day = d.getDate();
          dayOfWeek = d.getDay();
        }
        
        // 检查获取的值是否有效
        if (isNaN(year) || isNaN(month) || isNaN(day) || isNaN(dayOfWeek)) {
          return null;
        }
        
        const isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek; // 转换为ISO标准
        
        // 计算周一
        const monday = new Date(year, month, day - isoDayOfWeek + 1, 0, 0, 0, 0);
        
        if (isNaN(monday.getTime())) {
          return null;
        }
        
        return monday;
      } catch (error) {
        console.error('计算周开始时间出错:', error, dateObj);
        return null;
      }
    },

    // 获取一周的结束日期（周日 23:59:59）- 与getWeekNumber保持一致  
    getWeekEnd(dateObj) {
      if (!dateObj) {
        return null;
      }
      
      try {
        // 兼容自定义日期对象和原生Date对象
        let year, month, day, dayOfWeek;
        
        if (typeof dateObj.getFullYear === 'function') {
          // 自定义日期对象或原生Date对象
          year = dateObj.getFullYear();
          month = dateObj.getMonth(); // 0-11
          day = dateObj.getDate(); // 1-31
          dayOfWeek = dateObj.getDay(); // 0=周日, 1=周一
        } else {
          // 备用：原生Date对象
          const d = new Date(dateObj);
          if (isNaN(d.getTime())) {
            return null;
          }
          year = d.getFullYear();
          month = d.getMonth();
          day = d.getDate();
          dayOfWeek = d.getDay();
        }
        
        // 检查获取的值是否有效
        if (isNaN(year) || isNaN(month) || isNaN(day) || isNaN(dayOfWeek)) {
          return null;
        }
        
        const isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek; // 转换为ISO标准
        
        // 计算周日
        const sunday = new Date(year, month, day - isoDayOfWeek + 7, 23, 59, 59, 999);
        
        if (isNaN(sunday.getTime())) {
          return null;
        }
        
        return sunday;
      } catch (error) {
        console.error('计算周结束时间出错:', error, dateObj);
        return null;
      }
    },
    
    // 格式化日期显示（只显示日期）
    formatDate(dateString) {
      if (!dateString) return '--';
      
      try {
        // 兼容多种日期格式
        let date;
        if (typeof dateString === 'string') {
          // ISO格式直接解析
          if (dateString.includes('T') || dateString.includes('Z')) {
            date = new Date(dateString);
          } else {
            // 其他格式尝试替换处理
            date = new Date(dateString.replace(/-/g, '/'));
          }
        } else {
          date = new Date(dateString);
        }
        
        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          return '--';
        }
        
        return `${date.getMonth() + 1}月${date.getDate()}日`;
      } catch (error) {
        return '--';
      }
    },

    // 格式化日期时间显示
    formatDateTime(dateString) {
      if (!dateString) return '--';
      
      try {
        // 兼容多种日期格式
        let date;
        if (typeof dateString === 'string') {
          // ISO格式直接解析
          if (dateString.includes('T') || dateString.includes('Z')) {
            date = new Date(dateString);
          } else {
            // 其他格式尝试替换处理
            date = new Date(dateString.replace(/-/g, '/'));
          }
        } else {
          date = new Date(dateString);
        }
        
        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          return '--';
        }
        
        return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      } catch (error) {
        return '--';
      }
    },

    getStatusTitle() {
      if (this.selectedQuickFilter === 'week') {
        return '本周清理状态';
      } else if (this.dateFilterMode === 'quick' && this.selectedQuickFilter !== 'custom') {
        const option = this.quickDateOptions.find(opt => opt.value === this.selectedQuickFilter);
        return option ? `${option.label}整改记录统计` : '整改记录统计';
      } else if (this.customDateRange.startDate && this.customDateRange.endDate) {
        return '自定义时间段整改记录统计';
      }
      return '清理状态';
    },

    getCurrentTimeRange() {
      if (this.selectedTimeFilter === 'custom' && this.dateFilterMode === 'range') {
        return this.getDateRangeText();
      } else {
        const option = this.quickDateOptions.find(opt => opt.value === this.selectedQuickFilter);
        return option ? option.label : '本周';
      }
    },

    // 获取周标题
    getWeekTitle(weekKey) {
      // 解析weekKey，例如 '2025-W03' -> 2025年第3周
      const match = weekKey.match(/(\d{4})-W(\d{2})/);
      if (!match) return weekKey;
      
      const year = parseInt(match[1]);
      const weekNum = parseInt(match[2]);
      const beijingNow = this.toBeijingTime(new Date());
      const currentYear = beijingNow.getFullYear();
      const currentWeek = this.getCurrentWeekNumber();
      
      // 计算该周的日期范围
      const weekRange = this.getWeekRangeByWeekKey(weekKey);
      
      // 判断是否为当前周
      if (year === currentYear && weekNum === currentWeek) {
        return `本周 (${weekRange.start}-${weekRange.end})`;
      } else if (year === currentYear && weekNum === currentWeek - 1) {
        return `上周 (${weekRange.start}-${weekRange.end})`;
      } else if (year === currentYear && weekNum === currentWeek - 2) {
        return `前周 (${weekRange.start}-${weekRange.end})`;
      } else if (year === currentYear) {
        // 计算周数差
        const weekDiff = currentWeek - weekNum;
        if (weekDiff > 0 && weekDiff <= 4) {
          return `${weekDiff}周前 (${weekRange.start}-${weekRange.end})`;
        } else if (weekDiff > 4) {
          return `${year}年${weekRange.start}-${weekRange.end}`;
        } else {
          // 未来的周
          return `第${weekNum}周 (${weekRange.start}-${weekRange.end})`;
        }
      } else {
        return `${year}年${weekRange.start}-${weekRange.end}`;
      }
    },

    // 根据weekKey获取该周的日期范围
    getWeekRangeByWeekKey(weekKey) {
      try {
        if (!weekKey) {
          return { start: '', end: '' };
        }
        
        const match = weekKey.match(/(\d{4})-W(\d{2})/);
        if (!match) return { start: '', end: '' };
        
        const year = parseInt(match[1]);
        const weekNum = parseInt(match[2]);
        
        if (isNaN(year) || isNaN(weekNum) || year < 1900 || year > 2100 || weekNum < 1 || weekNum > 53) {
          return { start: '', end: '' };
        }
        
        // 计算该年第一个周四
        const firstThursday = new Date(year, 0, 4);
        if (isNaN(firstThursday.getTime())) {
          return { start: '', end: '' };
        }
        
        const firstThursdayDayOfWeek = firstThursday.getDay();
        const firstThursdayIsoDay = firstThursdayDayOfWeek === 0 ? 7 : firstThursdayDayOfWeek;
        
        // 调整到第一个周四
        firstThursday.setDate(4 + (4 - firstThursdayIsoDay));
        
        // 计算目标周的周四
        const targetThursday = new Date(firstThursday);
        targetThursday.setDate(firstThursday.getDate() + (weekNum - 1) * 7);
        
        if (isNaN(targetThursday.getTime())) {
          return { start: '', end: '' };
        }
        
        // 计算该周的周一（开始）
        const monday = new Date(targetThursday);
        monday.setDate(targetThursday.getDate() - 3); // 周四往前3天是周一
        
        // 计算该周的周日（结束）
        const sunday = new Date(targetThursday);
        sunday.setDate(targetThursday.getDate() + 3); // 周四往后3天是周日
        
        if (isNaN(monday.getTime()) || isNaN(sunday.getTime())) {
          return { start: '', end: '' };
        }
        
        return {
          start: `${monday.getMonth() + 1}月${monday.getDate()}日`,
          end: `${sunday.getMonth() + 1}月${sunday.getDate()}日`,
          monday: monday,
          sunday: sunday
        };
      } catch (error) {
        console.error('计算周次日期范围出错:', error, weekKey);
        return { start: '', end: '' };
      }
    },

    // 获取当前是一年中的第几周（北京时间）
    getCurrentWeekNumber() {
      try {
        const now = new Date();
        const beijingNow = this.toBeijingTime(now);
        
        if (!beijingNow) {
          console.error('无法获取北京时间');
          return 1; // 返回默认值
        }
        
        return this.getWeekNumber(beijingNow);
      } catch (error) {
        console.error('获取当前周数出错:', error);
        return 1; // 返回默认值
      }
    },

    // 计算整改任务的实际状态（考虑时间逾期）
    calculateRectificationStatus(task) {
      const now = new Date();
      

      
      // 如果任务已经完成，状态不变
      if (task.status === 'completed') {
        return 'completed';
      }
      
      // 检查是否有deadline字段进行逾期判断
      if (task.deadline) {
        const deadline = new Date(task.deadline);
        if (!isNaN(deadline.getTime()) && now > deadline) {
          // 超过截止时间且未完成，判定为逾期
          if (task.status === 'pending_rectification' || task.status === 'pending_assignment') {
            return 'overdue';
          }
        }
      }
      
      // 业务逻辑：整改任务应该在创建当周内完成，超过当周就算逾期
      if (task.created_at) {
        const beijingCreatedDate = this.toBeijingTime(task.created_at);
        const beijingNow = this.toBeijingTime(now);
        
        if (beijingCreatedDate && beijingNow) {
          // 计算创建日期所在的周（基于北京时间）
          const createdWeekStart = this.getWeekStart(beijingCreatedDate);
          const createdWeekEnd = this.getWeekEnd(beijingCreatedDate);
          
          // 如果当前北京时间已经超过创建周的结束时间，且任务未完成，判定为逾期
          if (beijingNow.getTime() > createdWeekEnd.getTime() && (task.status === 'pending_rectification' || task.status === 'pending_assignment')) {
            return 'overdue';
          }
        }
      }
      
      // 返回原始状态
      return task.status;
    },

    // 智能设置默认展开的周（优先本周，没有则展开最新周）
    setDefaultExpandedWeek() {
      // 获取当前筛选数据中的所有唯一周次
      const allWeeks = [...new Set(this.filteredRectificationTasks.map(task => task.week))];
      
      if (allWeeks.length > 0) {
        // 获取当前周的周次键
        const now = new Date();
        const beijingNow = this.toBeijingTime(now);
        const currentWeekKey = this.getWeekKey(beijingNow);
        
        let targetWeek;
        
        // 智能选择展开的周次：优先本周，没有则选最新周
        if (allWeeks.includes(currentWeekKey)) {
          // 如果筛选结果中包含本周，优先展开本周
          targetWeek = currentWeekKey;
        } else {
          // 如果没有本周数据，按周次排序找到最新的周
          const sortedWeeks = allWeeks.sort((a, b) => {
            const [yearA, weekA] = a.split('-W').map(x => parseInt(x));
            const [yearB, weekB] = b.split('-W').map(x => parseInt(x));
            
            if (yearA !== yearB) {
              return yearB - yearA; // 年份降序
            }
            return weekB - weekA; // 同年内周数降序
          });
          
          targetWeek = sortedWeeks[0];
        }
        
        // 确保目标周在展开列表中
        if (targetWeek && !this.expandedRectificationWeeks.includes(targetWeek)) {
          this.expandedRectificationWeeks.push(targetWeek);
        }
      }
    },

    // 切换整改记录周展开状态
    toggleRectificationWeek(weekKey) {
      const index = this.expandedRectificationWeeks.indexOf(weekKey);
      if (index > -1) {
        this.expandedRectificationWeeks.splice(index, 1);
      } else {
        this.expandedRectificationWeeks.push(weekKey);
      }
    },

    // 重置展开状态
    resetExpandedState() {
      this.expandedRectificationWeeks = [];
    },
    
    // 智能重置展开状态（清空后等数据重新加载）
    smartResetExpandedState() {
      this.expandedRectificationWeeks = [];
      // 等待下一个tick和少量延时后设置默认展开，确保数据已更新
      this.$nextTick(() => {
        setTimeout(() => {
          this.setDefaultExpandedWeek();
        }, 50);
      });
    },
    
    getStatusText(status) {
      const statusMap = {
        'pending': '待清理',
        'pending_review': '待复查', 
        'completed': '已完成',
        'pending_rectification': '待整改',
        'pending_assignment': '待分配',  // 待分配状态
        'overdue': '已逾期',
        'scheduled': '未到时间',  // 公共责任区未到排班日
        'rejected': '整改不达标',
        'verified': '整改合格',
        'approved': '复查通过',
        'in_progress': '整改中',
        'rectification_completed': '整改已完成'
      }
      return statusMap[status] || '未知'
    },

    // 获取清理记录状态文本
    getCleaningRecordStatusText(status) {
      return '已完成'; // 所有清理记录都是已完成状态
    },

    // 格式化描述文本，智能处理换行和长度
    formatDescription(description) {
      if (!description) return '';
      
      // 智能清理文本
      let formatted = description
        .replace(/\n/g, ' ')        // 将换行符替换为空格
        .replace(/\r/g, ' ')        // 将回车符替换为空格
        .replace(/\s+/g, ' ')       // 将多个连续空格合并为一个
        .trim();                    // 去除首尾空格
      
      // 限制长度，避免显示过长导致换行
      if (formatted.length > 16) {
        formatted = formatted.substring(0, 16) + '...';
      }
      
      return formatted;
    },

    // 处理清理记录更新事件
    handleRecordUpdated(data) {
      // 通过事件已经收到更新通知，进行静默刷新
      this.silentRefreshData();
    },

    // 静默刷新数据（不显示加载状态）
    async silentRefreshData() {
      if (this.loading) return; // 如果正在加载，跳过
      
      try {
        // 清理所有缓存
        this.currentWeekCache = null;
        this.areasCache = null;
        
        // 静默重新加载数据，不显示loading状态
        await Promise.all([
          this.loadUserAssignments(),
          this.loadUserRectificationTasks(),
          this.loadMyCleaningRecords()
        ]);
        
        // 数据加载完成后，确保第一个分组展开
        this.$nextTick(() => {
          this.setDefaultExpandedWeek();
        });
        
        // 清除刷新标记，避免用户返回页面时再次显示loading
        this.needsRefresh = false;
      } catch (error) {
        // 静默处理错误，不显示错误提示
      }
    },

    getIconColor(status) {
      const colorMap = {
        'completed': '#34C759',
        'pending': '#FF9500',
        'overdue': '#FF3B30',
        'pending_review': '#007AFF',
        'pending_rectification': '#FF6B35',
        'pending_assignment': '#FF9500',  // 待分配，使用橙色
        'scheduled': '#8E8E93',  // 未到时间，使用灰色
        'rejected': '#FF3B30',  // 整改不达标，使用红色
        'verified': '#34C759',  // 整改合格，使用绿色
        'approved': '#34C759',  // 复查通过，使用绿色
        'in_progress': '#007AFF',  // 整改中，使用蓝色
        'rectification_completed': '#FF9500'  // 整改已完成，使用橙色
      }
      return colorMap[status] || '#8E8E93'
    },

    // 处理逾期状态的通用方法
    handleOverdueStatus(area) {
      const isPublic = area.type === 'public';
      const content = isPublic 
        ? '排班清理时间已过，无法再进行清理操作。请等待下周重新分配。'
        : '本周清理时间已过，无法再进行清理操作。请等待下周重新分配。';
      
      uni.showModal({
        title: '清理任务已逾期',
        content,
        showCancel: false,
        confirmText: '知道了'
      });
      return true; // 返回true表示已处理逾期状态
    },
    
    openAreaDetail(area) {
      if (area.status === 'overdue') {
        this.handleOverdueStatus(area);
        return;
      }
      
      if (area.status === 'pending') {
        // 跳转到清理页面，由事件监听机制处理数据更新，无需手动刷新
        uni.navigateTo({
          url: `/pages/6s_pkg/cleaning-upload?areaId=${area._id || area.id}&type=fixed`
        })
      } else {
        // 跳转到详情页面不需要刷新数据
        uni.navigateTo({
          url: `/pages/6s_pkg/area-detail?areaId=${area._id || area.id}&type=fixed`
        })
      }
    },
    
    openPublicAreaDetail(area) {
      if (area.status === 'overdue') {
        this.handleOverdueStatus(area);
        return;
      }
      
      if (area.status === 'scheduled') {
        // 未到排班日，不允许清理
        const weekDays = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
        const scheduledDay = area.scheduled_day === 0 ? 7 : area.scheduled_day;
        uni.showModal({
          title: '未到清理时间',
          content: `该公共责任区安排在${weekDays[scheduledDay]}进行清理，当前时间无需清理。`,
          showCancel: false,
          confirmText: '知道了'
        });
        return;
      }
      
      if (area.status === 'pending') {
        // 到了排班日，可以进行清理，由事件监听机制处理数据更新，无需手动刷新
        uni.navigateTo({
          url: `/pages/6s_pkg/cleaning-upload?areaId=${area._id || area.id}&type=public`
        })
      } else {
        // 跳转到详情页面不需要刷新数据
        uni.navigateTo({
          url: `/pages/6s_pkg/area-detail?areaId=${area._id || area.id}&type=public`
        })
      }
    },
    
    openRectificationDetail(task) {
      // 检查任务ID是否有效
      const taskId = task.id || task._id || task.issue_id || task.task_id;
      
      if (!taskId) {
        uni.showToast({
          title: '任务数据异常',
          icon: 'error'
        });
        return;
      }
      
      if (task.status === 'overdue') {
        uni.showModal({
          title: '整改任务已逾期',
          content: '该整改任务已逾期，记录已锁定',
          showCancel: false,
          confirmText: '知道了'
        });
        return;
      }
      
      if (task.status === 'pending_rectification') {
        // 待整改任务，直接跳转到整改上传页面，由事件监听机制处理数据更新，无需手动刷新
        uni.navigateTo({
          url: `/pages/6s_pkg/rectification-upload?taskId=${taskId}&areaType=${task.areaType}`
        });
      } else if (task.status === 'pending_review') {
        // 待复查任务，员工只能查看详情，不需要刷新数据
        uni.navigateTo({
          url: `/pages/6s_pkg/rectification-detail?taskId=${taskId}`
        });
      } else {
        // 已完成任务，跳转到整改详情页面，不需要刷新数据
        uni.navigateTo({
          url: `/pages/6s_pkg/rectification-detail?taskId=${taskId}`
        });
      }
    },

    // 打开清理记录详情
    openCleaningRecordDetail(record) {
      // 将整个 record 对象转换为 JSON 字符串进行传递
      const recordStr = encodeURIComponent(JSON.stringify(record));
      
      uni.navigateTo({
        url: `/pages/6s_pkg/area-detail?record=${recordStr}`
      });
    },

    // ======== 日期选择器方法 ========
    showTimeSelector() {
      // 初始化日期范围
      this.initializeDateRange();
      this.$refs.timePopup.open();
    },

    closeDatePicker() {
      this.$refs.timePopup.close();
    },

    // 初始化日期范围
    initializeDateRange() {
      if (!this.customDateRange.startDate) {
        const now = new Date();
        // 根据当前快捷选择初始化日期范围
        const range = this.getQuickDateRange(this.selectedQuickFilter);
        this.customDateRange.startDate = this.formatDateForPicker(range.start);
        this.customDateRange.endDate = this.formatDateForPicker(range.end);
      }

      // 初始化日历组件的日期
      if (!this.calendarDate) {
        this.calendarDate = this.formatDateForPicker(new Date());
      }
    },

    // 切换到范围选择模式
    switchToRangeMode() {
      this.dateFilterMode = 'range';
    },

    // 切换到快捷选择模式  
    switchToQuickMode() {
      this.dateFilterMode = 'quick';
    },

    // 快捷日期选择
    selectQuickDateOption(option) {
      this.selectedQuickFilter = option.value;
      if (option.value === 'custom') {
        this.switchToRangeMode();
      } else {
        // 快捷选择时，智能重置展开状态
        this.smartResetExpandedState();

        // 快捷选择立即生效，关闭弹窗
        this.closeDatePicker();

        // 重新加载数据以匹配新的筛选条件
        this.loading = true;
        clearTimeout(this.refreshTimer);
        this.refreshTimer = setTimeout(async () => {
          try {
            await this.loadMyAreas();
            // 数据加载完成后，确保第一个分组展开
            this.$nextTick(() => {
              this.setDefaultExpandedWeek();
            });
          } finally {
            this.loading = false;
          }
        }, 100);
      }
    },

    // 获取当前日期范围
    getCurrentDateRange() {
      if (this.dateFilterMode === 'quick' && this.selectedQuickFilter !== 'custom') {
        return this.getQuickDateRange(this.selectedQuickFilter);
      } else if (this.customDateRange.startDate && this.customDateRange.endDate) {
        return {
          start: new Date(this.customDateRange.startDate),
          end: new Date(this.customDateRange.endDate)
        };
      }
      return null;
    },

    // 获取快捷日期的范围
    getQuickDateRange(quickValue) {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      switch (quickValue) {
        case 'week':
          // 本周：周一到周日
          const startOfWeek = new Date(today);
          const dayOfWeek = startOfWeek.getDay();
          const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
          startOfWeek.setDate(startOfWeek.getDate() - daysToMonday);

          const endOfWeek = new Date(startOfWeek);
          endOfWeek.setDate(endOfWeek.getDate() + 6);
          endOfWeek.setHours(23, 59, 59, 999);

          return { start: startOfWeek, end: endOfWeek };

        case 'last_week':
          // 上周：上周一到上周日
          const lastWeekStart = new Date(today);
          const currentDayOfWeek = lastWeekStart.getDay();
          const daysToLastMonday = currentDayOfWeek === 0 ? 13 : currentDayOfWeek + 6;
          lastWeekStart.setDate(lastWeekStart.getDate() - daysToLastMonday);

          const lastWeekEnd = new Date(lastWeekStart);
          lastWeekEnd.setDate(lastWeekEnd.getDate() + 6);
          lastWeekEnd.setHours(23, 59, 59, 999);

          return { start: lastWeekStart, end: lastWeekEnd };

        case 'month':
          // 本月：1号到月底
          const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
          const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
          endOfMonth.setHours(23, 59, 59, 999);

          return { start: startOfMonth, end: endOfMonth };

        default:
          // 默认返回本周
          return this.getQuickDateRange('week');
      }
    },

    // 格式化日期用于picker
    formatDateForPicker(date) {
      if (!date) return '';
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 获取日期范围显示文本
    getDateRangeText() {
      if (this.dateFilterMode === 'quick') {
        const option = this.quickDateOptions.find(opt => opt.value === this.selectedQuickFilter);
        return option ? option.label : '本周';
      } else {
        const startText = this.getStartDateText();
        const endText = this.getEndDateText();
        if (startText === '请选择' || endText === '请选择') {
          return '选择日期范围';
        }
        return `${startText} - ${endText}`;
      }
    },

    // 获取开始日期显示文本  
    getStartDateText() {
      if (!this.customDateRange.startDate) return '请选择开始日期';
      return this.formatDisplayDate(new Date(this.customDateRange.startDate));
    },

    // 获取结束日期显示文本
    getEndDateText() {
      if (!this.customDateRange.endDate) return '请选择结束日期';
      return this.formatDisplayDate(new Date(this.customDateRange.endDate));
    },

    // 格式化显示日期
    formatDisplayDate(date) {
      const d = new Date(date);
      const month = d.getMonth() + 1;
      const day = d.getDate();
      return `${month}月${day}日`;
    },

    // 日历组件事件处理
    onCalendarChange(event) {
      // 防止重复处理
      clearTimeout(this.calendarChangeTimer);
      this.calendarChangeTimer = setTimeout(() => {
        // 处理日期范围选择
        if (event.range) {
          if (event.range.before && event.range.after) {
            // 选择了完整的日期范围
            this.customDateRange.startDate = event.range.before;
            this.customDateRange.endDate = event.range.after;

            // 立即生效：关闭弹窗并重新加载数据
            this.selectedTimeFilter = 'custom';
            this.smartResetExpandedState();
            this.closeDatePicker();

            // 重新加载数据
            this.loading = true;
            clearTimeout(this.refreshTimer);
            this.refreshTimer = setTimeout(async () => {
              try {
                await this.loadMyAreas();
                // 数据加载完成后，确保第一个分组展开
                this.$nextTick(() => {
                  this.setDefaultExpandedWeek();
                });
              } finally {
                this.loading = false;
              }
            }, 100);

            uni.showToast({
              title: '日期范围选择完成',
              icon: 'success',
              duration: 1500
            });
          }
        } else if (event.fulldate) {
          // 单个日期选择
          if (!this.customDateRange.startDate || this.customDateRange.endDate) {
            // 选择开始日期或重新选择
            this.customDateRange.startDate = event.fulldate;
            this.customDateRange.endDate = '';
          } else {
            // 选择结束日期
            this.customDateRange.endDate = event.fulldate;

            // 确保开始日期不晚于结束日期
            if (new Date(this.customDateRange.startDate) > new Date(this.customDateRange.endDate)) {
              const temp = this.customDateRange.startDate;
              this.customDateRange.startDate = this.customDateRange.endDate;
              this.customDateRange.endDate = temp;
            }

            // 选择完成后立即生效：关闭弹窗并重新加载数据
            this.selectedTimeFilter = 'custom';
            this.smartResetExpandedState();
            this.closeDatePicker();

            // 重新加载数据
            this.loading = true;
            clearTimeout(this.refreshTimer);
            this.refreshTimer = setTimeout(async () => {
              try {
                await this.loadMyAreas();
                // 数据加载完成后，确保第一个分组展开
                this.$nextTick(() => {
                  this.setDefaultExpandedWeek();
                });
              } finally {
                this.loading = false;
              }
            }, 100);

            uni.showToast({
              title: '日期范围选择完成',
              icon: 'success',
              duration: 1500
            });
          }
        }
      }, 100);
    },

    // 月份切换事件
    onMonthSwitch(event) {
      // 安全地处理月份切换事件
      if (event && event.current && event.current.fulldate) {
        this.calendarDate = event.current.fulldate;
      } else if (event && event.year && event.month) {
        // 如果没有fulldate，手动构造日期
        const year = event.year;
        const month = String(event.month).padStart(2, '0');
        this.calendarDate = `${year}-${month}-01`;
      }
    },

    // 格式化选中的日期显示
    formatSelectedDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      const dayName = dayNames[date.getDay()];
      return `${month}月${day}日 ${dayName}`;
    },
  }
}
</script>

<style lang="scss" scoped>
.page-container {
	min-height: 100vh;
	background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
	width: 100%;
	overflow-x: hidden; /* 防止水平滚动 */
	box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
}

/* 时间选择器样式 */
.time-selector {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 20rpx;
  border: 2rpx solid rgba(0, 122, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:active {
    background: rgba(0, 122, 255, 0.15);
    transform: scale(0.98);
  }
}

.time-text {
  font-size: 24rpx;
  color: #007AFF;
  font-weight: 500;
}

/* ======== 新的日历式日期选择器样式 ======== */
.date-picker-popup {
  background: white;
  padding: 0;
  max-height: 80vh;
  overflow-y: auto;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
}

.popup-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F2F2F7;
  border-radius: 50%;
}

.quick-date-section,
.range-date-section {
  padding: 24rpx 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quick-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.quick-option {
  flex: 1;
  min-width: 120rpx;
  padding: 24rpx 16rpx;
  border: 2rpx solid #E5E5EA;
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.2s;
}

.quick-option.active {
  border-color: #007AFF;
  background: rgba(0, 122, 255, 0.1);
}

.quick-text {
  font-size: 28rpx;
  color: #1C1C1E;
}

.quick-option.active .quick-text {
      color: #007AFF;
      font-weight: 600;
    }

/* 日历组件样式 */
.calendar-section {
  margin: 24rpx 0;
}

.calendar-header {
  margin-bottom: 24rpx;
}

.calendar-tip {
  font-size: 24rpx;
  color: #8E8E93;
  display: block;
}

.selected-range {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 24rpx;
  padding: 20rpx;
  background: #F8F9FA;
  border-radius: 16rpx;
  gap: 16rpx;
}

.range-item {
  text-align: center;
  flex: 1;
}

.range-label {
  font-size: 22rpx;
  color: #8E8E93;
  display: block;
  margin-bottom: 4rpx;
}

.range-value {
  font-size: 26rpx;
  color: #1C1C1E;
  font-weight: 600;
  display: block;
}

.range-separator {
  font-size: 24rpx;
  color: #007AFF;
  font-weight: 600;
}

/* 卡片样式 */
.card {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 24rpx 32rpx 0 32rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card:first-child {
  margin-top: 24rpx;
}

.card-header {
  padding: 32rpx 32rpx 16rpx 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
}

.card-body {
  padding: 32rpx;
}

.header-content {
  display: flex;
  flex-direction: row;
  align-items: baseline;
  justify-content: space-between;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 0;
}

.card-subtitle {
  font-size: 26rpx;
  color: #8E8E93;
}

/* 时间选择器 */
.time-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.time-selector-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.time-selector-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
}

.time-selector-picker {
  background: rgba(0, 122, 255, 0.1);
  border: 1rpx solid rgba(0, 122, 255, 0.2);
  border-radius: 20rpx;
  padding: 12rpx 20rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #007AFF;
  font-weight: 500;
  transition: all 0.2s ease;
  
  &:active {
    background: rgba(0, 122, 255, 0.15);
    transform: scale(0.98);
  }
}



/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
  text-align: center;
}

.stats-grid-two {
  grid-template-columns: repeat(2, 1fr);
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-number {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  
  &.success {
    color: #34C759;
  }
  
  &.warning {
    color: #FF9500;
  }
  
  &.info {
    color: #007AFF;
  }
  
  &.danger {
    color: #FF3B30;
  }
}

.stats-label {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 列表项样式 */
.list-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #F2F2F7;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
  
  &.icon-bg-completed {
    background: rgba(52, 199, 89, 0.1);
  }
  
  &.icon-bg-pending {
    background: rgba(255, 149, 0, 0.1);
  }
  
  &.icon-bg-overdue {
    background: rgba(255, 59, 48, 0.1);
  }
  
  &.icon-bg-pending_review {
    background: rgba(0, 122, 255, 0.1);
  }
  
  &.icon-bg-pending_rectification {
    background: rgba(255, 107, 53, 0.1);
  }
  
  &.icon-bg-pending_assignment {
    background: rgba(255, 149, 0, 0.1);
  }
  
  &.icon-bg-scheduled {
    background: rgba(142, 142, 147, 0.1);
  }
  
  &.icon-bg-rejected {
    background: rgba(255, 59, 48, 0.1);
  }
  
  &.icon-bg-verified {
    background: rgba(52, 199, 89, 0.1);
  }
  
  &.icon-bg-approved {
    background: rgba(52, 199, 89, 0.1);
  }
  
  &.icon-bg-in_progress {
    background: rgba(0, 122, 255, 0.1);
  }
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 4rpx;
}

.list-item-subtitle {
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.4;
}

.list-item-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

/* 状态标签 */
.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 600;
  
  &.status-completed {
    background: #E8F5E8;
    color: #34C759;
  }
  
  &.status-pending {
    background: #FFF4E6;
    color: #FF9500;
  }
  
  &.status-overdue {
    background: #FFE6E6;
    color: #FF3B30;
  }
  
  &.status-pending_review {
    background: #E6F3FF;
    color: #007AFF;
  }
  
  &.status-pending_rectification {
    background: #FFF0E6;
    color: #FF6B35;
  }
  
  &.status-pending_assignment {
    background: #FFF4E6;
    color: #FF9500;
  }
  
  &.status-scheduled {
    background: #F2F2F7;
    color: #8E8E93;
  }
  
  &.status-rejected {
    background: #FFE6E6;
    color: #FF3B30;
  }
  
  &.status-verified {
    background: #E8F5E8;
    color: #34C759;
  }
  
  &.status-approved {
    background: #E8F5E8;
    color: #34C759;
  }
  
  &.status-in_progress {
    background: #E6F3FF;
    color: #007AFF;
  }
  
  &.status-rectification_completed {
    background: #FFF4E6;
    color: #FF9500;
  }
}

/* 公共区域标签 */
.public-tag {
  display: inline-block;
  background: #E6F3FF;
  color: #007AFF;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  margin-left: 12rpx;
  font-weight: 400;
}

/* 固定区域标签 */
.fixed-tag {
  display: inline-block;
  background: #E8F5E8;
  color: #34C759;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  margin-left: 12rpx;
  font-weight: 400;
}

/* 整改任务时间信息 */
.task-time-info {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
  font-size: 22rpx;
  color: #8E8E93;
}

.time-label {
  margin-right: 8rpx;
}

.time-value {
  font-weight: 500;
  color: #1C1C1E;
}

/* 清理记录元信息 */
.cleaning-record-meta {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
  font-size: 22rpx;
  color: #8E8E93;
}

.meta-label {
  margin-right: 4rpx;
}

.meta-value {
  font-weight: 500;
  color: #1C1C1E;
  margin-right: 12rpx;
}

.meta-remark {
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 时间分组样式 */
.time-group {
  margin-bottom: 16rpx;
}

.time-group-header {
  padding: 24rpx 32rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
  margin-bottom: 8rpx;
}

.time-group-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.time-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #1D1D1F;
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.time-count {
  font-size: 24rpx;
  color: #8E8E93;
  background: #E5E7EB;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  min-width: 48rpx;
  text-align: center;
}

/* 加载状态样式 */
.loading-container {
  padding: 80rpx 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #FFFFFF;
  border-radius: 24rpx;
  margin: 20rpx;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
}

/* 统计区域加载状态 */
.stats-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 24rpx;
  min-height: 200rpx;
}

/* 列表加载状态 */
.list-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 20rpx;
  background: #FFFFFF;
  border-radius: 24rpx;
  margin: 20rpx;
  border: 1rpx solid #F2F2F7;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #F0F4F8;
  border-top: 6rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

/* 按钮内的加载动画 */
.btn-loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #FFFFFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 12rpx;
}

@keyframes spin {
  0% { 
    transform: rotate(0deg);
  }
  100% { 
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #6B7280;
  text-align: center;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}



.list-loading .loading-text {
  color: #374151;
}

/* 加载更多按钮 */
.load-more-section {
  padding: 32rpx;
  display: flex;
  justify-content: center;
}

.load-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx 48rpx;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  color: white;
  border: none;
  border-radius: 25rpx;
  font-size: 28rpx;
  font-weight: 500;
  min-width: 200rpx;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  &:active:not(:disabled) {
    transform: scale(0.95);
  }
}

/* 底部安全间距 */
.bottom-safe-area {
  height: 40rpx;
}

/* 响应式调整 */
@media (max-width: 414px) {
  .page-header {
    padding: 24rpx 16rpx;
  }
  
  .card {
    margin: 24rpx 16rpx 0 16rpx;
  }
  
  .card:first-child {
    margin-top: 24rpx;
  }
  
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 24rpx;
  }
}
</style>