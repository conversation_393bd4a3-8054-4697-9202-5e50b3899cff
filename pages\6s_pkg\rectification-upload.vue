<template>
  <view class="page-container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载整改任务信息中...</text>
      </view>
    </view>
    
    <!-- 错误状态 -->
    <view v-else-if="loadError" class="error-container">
      <view class="error-content">
        <uni-icons type="info" size="48" color="#FF3B30"></uni-icons>
        <text class="error-text">{{ loadError }}</text>
        <button class="retry-button" @click="retryLoad">重新加载</button>
      </view>
    </view>
    
    <!-- 正常内容 -->
    <template v-else>
      <view class="card">
        <view class="card-header">
          <view class="header-content">
            <view class="card-title">整改任务</view>
            <view class="card-subtitle">{{ taskInfo.area }} - {{ taskInfo.isPublic ? '公共责任区' : '固定责任区' }}</view>
          </view>
        </view>
        <view class="card-body">
          <view class="task-info">
            <view class="info-item">
              <text class="info-label">问题描述：</text>
              <text class="info-value">{{ taskInfo.problemDescription }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">发现时间：</text>
              <text class="info-value">{{ formatDateTime(taskInfo.issueFoundDate) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">检查员：</text>
              <text class="info-value">{{ taskInfo.inspector }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 审核反馈（如果存在） -->
      <view v-if="taskInfo.reviewComments" class="card review-feedback-card">
        <view class="card-header">
          <view class="card-title">审核反馈</view>
        </view>
        <view class="card-body">
          <view class="feedback-content">
            <view class="feedback-item">
              <text class="feedback-label">审核结果：</text>
              <view class="status-badge status-rejected">需重新整改</view>
            </view>
            <view class="feedback-item">
              <text class="feedback-label">审核意见：</text>
              <text class="feedback-value">{{ taskInfo.reviewComments }}</text>
            </view>
            <view class="feedback-item">
              <text class="feedback-label">审核时间：</text>
              <text class="feedback-value">{{ formatDateTime(taskInfo.reviewDate) }}</text>
            </view>
            <view class="feedback-item">
              <text class="feedback-label">审核人员：</text>
              <text class="feedback-value">{{ taskInfo.reviewer }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 检查员发现问题时的照片（如果存在） -->
      <view v-if="taskInfo.inspectionPhotos && taskInfo.inspectionPhotos.length > 0" class="card inspection-photos-card">
        <view class="card-header">
          <view class="card-title">问题照片</view>
          <view class="card-subtitle">检查员发现问题时拍摄的现场照片</view>
        </view>
        <view class="card-body">
          <view class="inspection-photos-grid">
            <view v-for="(photo, index) in taskInfo.inspectionPhotos" :key="index" class="inspection-photo-item">
              <image :src="getCloudPhotoUrl(photo)" mode="aspectFill" @click="previewInspectionPhoto(index)"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 之前的整改照片（如果存在） -->
      <view v-if="taskInfo.previousPhotos && taskInfo.previousPhotos.length > 0" class="card previous-photos-card">
        <view class="card-header">
          <view class="card-title">之前的整改照片</view>
          <view class="card-subtitle">供参考，检查员认为需要改进</view>
        </view>
        <view class="card-body">
          <view class="previous-photos-grid">
            <view v-for="(photo, index) in taskInfo.previousPhotos" :key="index" class="previous-photo-item">
              <image :src="photo.url" mode="aspectFill" @click="previewPreviousPhoto(index)"></image>
              <view class="photo-overlay">
                <text class="photo-time">{{ formatDateTime(photo.timestamp) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="card">
        <view class="card-header">
          <view class="card-title">{{ taskInfo.reviewComments ? '重新整改' : '整改记录' }}</view>
        </view>
        <view class="card-body">
          <view class="upload-section">
            <view class="section-header">
              <view class="section-title">上传整改照片</view>
              <view class="auto-upload-toggle" @click="toggleAutoUpload">
                <view class="toggle-label">自动上传</view>
                <view class="toggle-switch" :class="{ active: autoUpload }">
                  <view class="toggle-circle"></view>
                </view>
              </view>
            </view>
            <view class="photo-grid">
              <view v-for="(photo, index) in photos" :key="index" class="photo-item">
                <image :src="getPhotoDisplayUrl(photo)" mode="aspectFill" @click="previewPhoto(index)"></image>
                <!-- 上传状态指示器 -->
                <view v-if="photo.uploading" class="photo-uploading">
                  <view class="upload-spinner"></view>
                </view>
                <view v-else-if="photo.uploaded" class="photo-uploaded">
                  <uni-icons type="checkmarkempty" size="16" color="white"></uni-icons>
                </view>
                <view class="photo-delete" @click="deletePhoto(index)">
                  <uni-icons type="close" size="18" color="white"></uni-icons>
                </view>
              </view>
              <view v-if="photos.length < 12" class="photo-add" @click="addPhoto">
                <uni-icons type="camera" size="32" color="#8E8E93"></uni-icons>
                <text>添加照片</text>
              </view>
            </view>
            <view class="photo-tip">最多可上传12张照片，建议拍摄整改前后对比照片</view>
          </view>
          
          <view class="remarks-section">
            <view class="section-title">整改说明</view>
            <view class="remarks-input-container">
              <textarea 
                v-model="remarks" 
                placeholder="请详细描述整改过程和结果..."
                maxlength="200"
                class="remarks-input"
                @input="handleRemarksInput"
              ></textarea>
              <view class="char-count-overlay">{{ remarksLength }}/200</view>
            </view>
          </view>
        </view>
      </view>
    </template>
    
    <view class="button-container">
      <button 
        class="primary-button" 
        @click="submitRectification" 
        :disabled="photos.length === 0 || !remarks.trim() || submitting"
        :class="{ loading: submitting }"
      >
        <view v-if="submitting" class="button-loading">
          <view class="loading-spinner"></view>
          <text>提交中...</text>
        </view>
        <text v-else>提交整改记录</text>
      </button>
    </view>
  </view>
</template>

<script>
import { callCloudFunction } from '@/utils/auth.js';
import uploadUtils from '@/utils/upload-utils.js';

export default {
  name: 'RectificationUpload',
  data() {
    return {
      taskInfo: {},
      photos: [],
      remarks: '',
      loading: false,
      loadError: '',
      submitting: false,
      autoUpload: true, // 自动上传开关
      taskId: '',
      // 性能优化：缓存系统
      processCache: {
        dateFormatter: null, // 缓存日期格式化器
        photoUrlCache: new Map(), // 缓存照片URL处理结果
        inspectionUrlCache: new Map() // 缓存检查照片URL
      }
    }
  },
  computed: {
    // 计算备注长度，确保响应式更新
    remarksLength() {
      return this.remarks ? this.remarks.length : 0;
    }
  },
  onLoad(options) {
    this.taskId = options.taskId;
    this.initProcessCache();
    this.loadTaskInfoOptimized(options.taskId);
  },
  
  methods: {
    // 初始化性能缓存
    initProcessCache() {
      // 创建缓存的日期格式化器
      this.processCache.dateFormatter = (dateString) => {
        if (!dateString) return '--';
        
        try {
          let date;
          if (typeof dateString === 'string') {
            if (dateString.includes('T') || dateString.includes('Z')) {
              date = new Date(dateString);
            } else {
              date = new Date(dateString.replace(/-/g, '/'));
            }
          } else {
            date = new Date(dateString);
          }
          
          if (isNaN(date.getTime())) {
            return '--';
          }
          
          return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
        } catch (error) {
          return '--';
        }
      };
    },

    // 处理备注输入，确保字符限制和响应式更新
    handleRemarksInput(e) {
      let value = e.detail.value || '';
      
      // 强制限制字符数量
      if (value.length > 200) {
        value = value.substring(0, 200);
        // 如果超出限制，显示提示
        uni.showToast({
          title: '整改说明不能超过200个字符',
          icon: 'none',
          duration: 1500
        });
      }
      
      // 更新数据
      this.remarks = value;
      
      // 强制触发视图更新
      this.$forceUpdate();
    },

    // 优化的任务信息加载
    async loadTaskInfoOptimized(taskId) {
      if (!taskId) {
        this.loadError = '整改任务ID不能为空';
        return;
      }

      this.loading = true;
      this.loadError = '';

      try {
        const result = await callCloudFunction('hygiene-rectification', {
          action: 'getRectificationDetail',
          data: { id: taskId }
        });

        if (result && result.success && result.data) {
          const task = result.data;
          
          // 使用优化的数据处理
          this.taskInfo = this.processTaskDataOptimized(task);
          
          // 预处理照片URL
          this.preprocessPhotos();
        } else {
          throw new Error(result?.message || '获取整改任务信息失败');
        }
      } catch (error) {
        console.error('加载整改任务信息失败:', error);
        this.loadError = error.message || '加载失败，请稍后重试';
      } finally {
        this.loading = false;
      }
    },

    // 优化的数据处理
    processTaskDataOptimized(task) {
      return {
        id: task._id || task.id,
        area: task.area_name || '未知责任区',
        areaId: task.area_id,
        isPublic: task.area_type === 'public',
        status: task.status || 'pending',
        problemDescription: task.issue_description || task.description || '无问题描述',
        issueFoundDate: task.created_at || task.issue_found_date,
        inspector: task.inspector_name || task.created_by_name || task.assigned_by_name || '未知检查员',
        // 审核反馈信息
        reviewComments: task.review_comments || '',
        reviewDate: task.review_date || '',
        reviewer: task.reviewer_name || '未知审核员',
        reviewResult: task.review_result || '',
        // 检查员发现问题时上传的照片
        inspectionPhotos: task.inspection_photos || [],
        // 之前的整改照片
        previousPhotos: task.completion_photos || []
      };
    },

    // 预处理照片URL，提前缓存
    preprocessPhotos() {
      // 预处理检查照片URL
      if (this.taskInfo.inspectionPhotos) {
        this.taskInfo.inspectionPhotos.forEach((photo, index) => {
          const url = this.getCloudPhotoUrl(photo);
          this.processCache.inspectionUrlCache.set(index, url);
        });
      }
    },

    // 加载整改任务信息
    async loadTaskInfo(taskId) {
      if (!taskId) {
        this.loadError = '整改任务ID不能为空';
        return;
      }

      this.loading = true;
      this.loadError = '';

      try {
        const result = await callCloudFunction('hygiene-rectification', {
          action: 'getRectificationDetail',
          data: { id: taskId }
        });

        if (result && result.success && result.data) {
          const task = result.data;
          
          this.taskInfo = {
            id: task._id || task.id,
            area: task.area_name || '未知责任区',
            areaId: task.area_id,
            isPublic: task.area_type === 'public',
            status: task.status || 'pending',
            problemDescription: task.issue_description || task.description || '无问题描述',
            issueFoundDate: task.created_at || task.issue_found_date,
            inspector: task.inspector_name || task.created_by_name || task.assigned_by_name || '未知检查员',
            // 审核反馈信息
            reviewComments: task.review_comments || '',
            reviewDate: task.review_date || '',
            reviewer: task.reviewer_name || '未知审核员',
            reviewResult: task.review_result || '',
            // 检查员发现问题时上传的照片
            inspectionPhotos: task.inspection_photos || [],
            // 之前的整改照片
            previousPhotos: task.completion_photos || []
          };
        } else {
          throw new Error(result?.message || '获取整改任务信息失败');
        }
      } catch (error) {
        console.error('加载整改任务信息失败:', error);
        this.loadError = error.message || '加载失败，请稍后重试';
      } finally {
        this.loading = false;
      }
    },

    // 重新加载
    retryLoad() {
      this.loadTaskInfo(this.taskId);
    },

    // 优化的日期时间格式化（使用缓存）
    formatDateTime(dateString) {
      // 确保缓存已初始化
      if (!this.processCache.dateFormatter) {
        this.initProcessCache();
      }
      
      // 安全调用格式化函数
      if (this.processCache.dateFormatter && typeof this.processCache.dateFormatter === 'function') {
        return this.processCache.dateFormatter(dateString);
      }
      
      // 回退到默认格式化
      if (!dateString) return '--';
      try {
        let date;
        if (typeof dateString === 'string') {
          if (dateString.includes('T') || dateString.includes('Z')) {
            date = new Date(dateString);
          } else {
            date = new Date(dateString.replace(/-/g, '/'));
          }
        } else {
          date = new Date(dateString);
        }
        if (isNaN(date.getTime())) {
          return '--';
        }
        return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      } catch (error) {
        return '--';
      }
    },
    
    // 优化的添加照片
    addPhoto() {
      if (this.photos.length >= 12) {
        uni.showToast({
          title: '最多只能上传12张照片',
          icon: 'none'
        });
        return;
      }

      uni.chooseImage({
        count: 12 - this.photos.length,
        sizeType: ['compressed'],
        sourceType: ['camera', 'album'],
        success: async (res) => {
          const newPhotos = this.processNewPhotos(res.tempFilePaths);
          
          // 添加到照片列表
          this.photos = this.photos.concat(newPhotos);
          
          // 自动上传新选择的照片（优化版）
          if (this.autoUpload) {
            this.autoUploadNewPhotosOptimized(newPhotos);
          }
        },
        fail: (error) => {
          console.error('选择照片失败:', error);
          uni.showToast({
            title: '选择照片失败',
            icon: 'none'
          });
        }
      });
    },

    // 处理新照片数据
    processNewPhotos(tempFilePaths) {
      return tempFilePaths.map(path => {
        const photo = {
          url: path,
          uploaded: false,
          cloudUrl: '',
          cloudPath: '',
          uploading: false
        };
        
        // 预缓存显示URL
        this.processCache.photoUrlCache.set(path, this.getPhotoDisplayUrl(photo));
        
        return photo;
      });
    },
    
    // 优化的并行自动上传
    async autoUploadNewPhotosOptimized(newPhotos) {
      if (!this.autoUpload || newPhotos.length === 0) return;
      
      // 并行上传，最大并发数为3
      const maxConcurrent = 3;
      const uploadPromises = [];
      
      for (let i = 0; i < newPhotos.length; i += maxConcurrent) {
        const batch = newPhotos.slice(i, i + maxConcurrent);
        const batchPromises = batch.map(photo => this.uploadPhotoWithIndex(photo));
        uploadPromises.push(...batchPromises);
      }
      
      // 使用 Promise.allSettled 确保即使部分失败也能继续
      const results = await Promise.allSettled(uploadPromises);
      
      // 统计成功和失败的数量
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;
      
      if (failed > 0) {
        uni.showToast({
          title: `${successful}张上传成功，${failed}张失败`,
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 上传单张照片并更新索引
    async uploadPhotoWithIndex(photo) {
      const photoIndex = this.photos.findIndex(p => p.url === photo.url);
      if (photoIndex === -1) throw new Error('照片不存在');
      
      try {
        // 标记为正在上传
        this.$set(this.photos[photoIndex], 'uploading', true);
        
        // 上传照片
        const uploadResult = await this.uploadSinglePhotoOptimized(photo);
        
        if (uploadResult.success) {
          // 更新照片信息
          this.$set(this.photos[photoIndex], 'uploaded', true);
          this.$set(this.photos[photoIndex], 'cloudUrl', uploadResult.url);
          this.$set(this.photos[photoIndex], 'cloudPath', uploadResult.cloudPath);
          this.$set(this.photos[photoIndex], 'uploading', false);
          
          return uploadResult;
        } else {
          throw new Error(uploadResult.error || '上传失败');
        }
      } catch (error) {
        this.$set(this.photos[photoIndex], 'uploading', false);
        throw error;
      }
    },

    // 兼容旧方法名
    async autoUploadNewPhotos(newPhotos) {
      return this.autoUploadNewPhotosOptimized(newPhotos);
    },

    // 优化的单张照片上传
    async uploadSinglePhotoOptimized(photo) {
      try {
        const cloudPath = this.generateCloudPath();
        
        // 使用 uploadToCloud 方法上传单张照片
        const uploadResult = await uploadUtils.uploadToCloud(photo.url, cloudPath);
        
        if (uploadResult && uploadResult.fileID) {
          // 获取访问URL
          const fileInfo = await uploadUtils.getFileInfo(uploadResult.fileID);
          
          return {
            success: true,
            cloudPath: uploadResult.fileID,
            url: fileInfo.tempFileURL || uploadResult.fileID,
            size: uploadResult.actualSize
          };
        } else {
          throw new Error('上传返回结果异常');
        }
      } catch (error) {
        console.error('单张照片上传失败:', error);
        return { success: false, error: error.message };
      }
    },

    // 生成云端路径
    generateCloudPath() {
      const timestamp = Date.now();
      const random = Math.random().toString(36).substring(2, 8);
      return `6s/rectification/${this.taskInfo.areaId}/${timestamp}_${random}.jpg`;
    },

    // 兼容旧方法名
    async uploadSinglePhoto(photo) {
      return this.uploadSinglePhotoOptimized(photo);
    },
    
    // 删除照片
    async deletePhoto(index) {
      if (index < 0 || index >= this.photos.length) {
        return;
      }

      const photo = this.photos[index];
      
      // 如果照片已经上传到云端，需要删除云端文件
      if (photo.uploaded && photo.cloudPath) {
        try {
          uni.showLoading({ title: '删除照片中...' });
          
          await uniCloud.callFunction({
            name: 'delete-file',
            data: {
              fileList: [this.extractFileId(photo.cloudPath)]
            }
          });
        } catch (error) {
          console.error('删除云端照片失败:', error);
          uni.showToast({
            title: '删除云端照片失败',
            icon: 'none'
          });
        } finally {
          uni.hideLoading();
        }
      }
      
      // 从本地数组中移除
      this.photos.splice(index, 1);
    },

    // 从URL中提取文件ID
    extractFileId(url) {
      if (url.startsWith('cloud://')) {
        const parts = url.split('/');
        return parts[parts.length - 1];
      } else if (url.includes('tcb-api')) {
        const urlObj = new URL(url);
        return urlObj.pathname.split('/').pop();
      }
      return url;
    },

    // 优化的照片显示URL获取（使用缓存）
    getPhotoDisplayUrl(photo) {
      const url = photo.url || photo;
      
      // 确保缓存已初始化
      if (!this.processCache.photoUrlCache) {
        this.initProcessCache();
      }
      
      // 检查缓存
      if (this.processCache.photoUrlCache && this.processCache.photoUrlCache.has(url)) {
        return this.processCache.photoUrlCache.get(url);
      }
      
      // 处理URL
      let processedUrl = url;
      if (typeof url === 'string' && url.startsWith('http://tmp/')) {
        // 这是微信小程序的本地临时文件路径，直接使用
        processedUrl = url;
      }
      
      // 缓存结果
      if (this.processCache.photoUrlCache) {
        this.processCache.photoUrlCache.set(url, processedUrl);
      }
      
      return processedUrl;
    },

    // 切换自动上传状态
    toggleAutoUpload() {
      this.autoUpload = !this.autoUpload;
      uni.showToast({
        title: this.autoUpload ? '已开启自动上传' : '已关闭自动上传',
        icon: 'none'
      });
    },

    // 优化的照片预览（缓存URL列表）
    previewPhoto(index) {
      // 使用缓存的URL列表，避免重复映射
      if (!this._cachedPhotoUrls || this._cachedPhotoUrls.length !== this.photos.length) {
        this._cachedPhotoUrls = this.photos.map(photo => this.getPhotoDisplayUrl(photo));
      }
      
      uni.previewImage({
        urls: this._cachedPhotoUrls,
        current: index
      });
    },

    // 获取云端照片URL
    getCloudPhotoUrl(photo) {
      if (typeof photo === 'string') {
        return photo; // 直接返回URL字符串
      }
      return photo.url || photo; // 如果是对象，取url字段
    },

    // 优化的检查员问题照片预览（使用缓存）
    previewInspectionPhoto(index) {
      // 使用预缓存的URL
      if (!this._cachedInspectionUrls) {
        this._cachedInspectionUrls = this.taskInfo.inspectionPhotos.map((photo, i) => {
          return this.processCache.inspectionUrlCache.get(i) || this.getCloudPhotoUrl(photo);
        });
      }
      
      uni.previewImage({
        urls: this._cachedInspectionUrls,
        current: index
      });
    },

    // 预览之前的整改照片
    previewPreviousPhoto(index) {
      const urls = this.taskInfo.previousPhotos.map(photo => photo.url);
      uni.previewImage({
        urls: urls,
        current: index
      });
    },
    
    // 优化的提交整改记录
    async submitRectification() {
      if (this.photos.length === 0) {
        uni.showToast({
          title: '请至少上传一张照片',
          icon: 'none'
        });
        return;
      }

      if (!this.remarks.trim()) {
        uni.showToast({
          title: '请填写整改说明',
          icon: 'none'
        });
        return;
      }

      if (this.submitting) return;

      this.submitting = true;
      
      try {
        // 准备照片数据
        const uploadedPhotos = await this.preparePhotosForSubmit();
        
        // 准备提交数据
        // 确保整改说明不超过限制
        let finalRemarks = this.remarks.trim();
        if (finalRemarks.length > 200) {
          finalRemarks = finalRemarks.substring(0, 200);
          console.warn('整改说明被截断到200字符');
        }
        
        const submissionData = {
          id: this.taskId,  // 云函数期望的参数名
          completion_description: finalRemarks,  // 云函数期望的字段名
          completion_photos: uploadedPhotos,
          completion_status: 'completed'
        };
        
        uni.showLoading({
          title: '提交整改记录...'
        });
        
        // 提交整改记录
        const result = await callCloudFunction('hygiene-rectification', {
          action: 'completeRectification',
          data: submissionData
        });
        
        if (result && result.success) {
          await this.handleSubmitSuccess();
        } else {
          throw new Error(result?.message || '提交整改记录失败');
        }
      } catch (error) {
        console.error('提交整改记录失败:', error);
        await this.handleSubmitError(error);
      } finally {
        this.submitting = false;
      }
    },

    // 准备照片数据进行提交
    async preparePhotosForSubmit() {
      // 检查是否有未上传的照片
      const unuploadedPhotos = this.photos.filter(photo => !photo.uploaded && !photo.uploading);
      
      if (unuploadedPhotos.length > 0) {
        uni.showLoading({
          title: '正在上传剩余照片...'
        });
        
        // 并行上传剩余照片
        const uploadPromises = unuploadedPhotos.map(photo => this.uploadSinglePhotoOptimized(photo));
        const uploadResults = await Promise.allSettled(uploadPromises);
        
        // 检查上传结果
        const successfulUploads = uploadResults
          .filter(result => result.status === 'fulfilled' && result.value.success)
          .map(result => result.value);
        
        if (successfulUploads.length === 0) {
          uni.hideLoading();
          throw new Error('照片上传失败，请重试');
        }
        
        // 合并所有已上传的照片
        const existingPhotos = this.photos
          .filter(photo => photo.uploaded)
          .map(photo => ({ url: photo.cloudPath, type: 'rectification', description: '' }));
        
        const newPhotos = successfulUploads.map(upload => ({
          url: upload.cloudPath,
          type: 'rectification',
          description: ''
        }));
        
        return [...existingPhotos, ...newPhotos];
      } else {
        // 所有照片都已上传
        return this.photos
          .filter(photo => photo.uploaded)
          .map(photo => ({
            url: photo.cloudPath,
            type: 'rectification',
            description: ''
          }));
      }
    },

    // 准备提交数据
    prepareSubmissionData(uploadedPhotos) {
      return {
        id: this.taskId,
        completion_description: this.remarks.trim(),
        completion_photos: uploadedPhotos
      };
    },

    // 处理提交成功
    async handleSubmitSuccess() {
      uni.hideLoading();
      
      // 发送事件通知其他页面更新
      uni.$emit('rectificationRecordUpdated', {
        taskId: this.taskId,
        areaId: this.taskInfo.areaId,
        type: 'completed'
      });
      
      uni.showToast({
        title: '提交成功',
        icon: 'success'
      });
      
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    },

    // 处理提交错误
    async handleSubmitError(error) {
      uni.hideLoading();
      
      let errorMessage = '提交失败，请重试';
      if (error.message) {
        if (error.message.includes('未登录')) {
          errorMessage = '请先登录';
        } else if (error.message.includes('权限')) {
          errorMessage = '您没有权限操作该整改任务';
        } else {
          errorMessage = error.message;
        }
      }
      
      await uni.showModal({
        title: '提交失败',
        content: errorMessage,
        showCancel: false,
        confirmText: '知道了'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
  padding: 24rpx;
}

.card {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 32rpx 32rpx 16rpx 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
}

.card-body {
  padding: 32rpx;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
}

.card-subtitle {
  font-size: 26rpx;
  color: #8E8E93;
}

.task-info {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.info-label {
  font-size: 28rpx;
  color: #8E8E93;
  width: 140rpx; /* Fixed width for alignment */
  flex-shrink: 0; /* Prevent shrinking */
  text-align: left; /* Align text to left */
}

.info-value {
  font-size: 28rpx;
  color: #1C1C1E;
  flex: 1;
  line-height: 1.4;
}

.upload-section, .remarks-section {
  margin-bottom: 32rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
}

.auto-upload-toggle {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.toggle-label {
  font-size: 24rpx;
  color: #8E8E93;
}

.toggle-switch {
  width: 76rpx;
  height: 44rpx;
  background: #E5E5EA;
  border-radius: 22rpx;
  position: relative;
  transition: background-color 0.3s ease;
}

.toggle-switch.active {
  background: #34C759;
}

.toggle-circle {
  width: 36rpx;
  height: 36rpx;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  transition: transform 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.toggle-switch.active .toggle-circle {
  transform: translateX(32rpx);
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.photo-item {
  position: relative;
  width: 170rpx;
  height: 170rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.photo-item image {
  width: 100%;
  height: 100%;
}

.photo-delete {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  width: 36rpx;
  height: 36rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.photo-add {
  width: 170rpx;
  height: 170rpx;
  border: 2rpx dashed #E5E5EA;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  background: #F8F9FA;
}

.photo-add text {
  font-size: 24rpx;
  color: #8E8E93;
}



.photo-tip {
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.4;
}

.remarks-section .section-title {
  margin-bottom: 16rpx;
}

.remarks-input-container {
  position: relative;
  width: 100%;
}

.remarks-input {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx 16rpx 32rpx 16rpx;
  border: 1rpx solid #E5E5EA;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  background: #F8F9FA;
  box-sizing: border-box;
  resize: none;
  line-height: 1.5;
}

.char-count-overlay {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  font-size: 22rpx;
  color: #8E8E93;
  background: rgba(248, 249, 250, 0.9);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  pointer-events: none;
  z-index: 2;
  backdrop-filter: blur(4rpx);
}

.button-container {
  padding: 32rpx 0;
}

.primary-button {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  width: 100%;
}

.primary-button[disabled] {
  background: #C7C7CC;
  color: #8E8E93;
}

.primary-button.loading {
  background: #0056D6;
  opacity: 0.9;
}

.button-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  color: white;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.photo-uploading {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-spinner {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-top: 2rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.photo-uploaded {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #34C759;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 加载状态 */
.loading-container {
  padding: 120rpx 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
  text-align: center;
}

/* 错误状态 */
.error-container {
  padding: 120rpx 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  text-align: center;
}

.error-text {
  font-size: 28rpx;
  color: #8E8E93;
  line-height: 1.4;
}

.retry-button {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
}

/* 审核反馈卡片 */
.feedback-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.feedback-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.feedback-label {
  font-size: 28rpx;
  color: #8E8E93;
  width: 140rpx;
  flex-shrink: 0;
  text-align: left;
}

.feedback-value {
  font-size: 28rpx;
  color: #1C1C1E;
  flex: 1;
  line-height: 1.4;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 600;
}

.status-rejected {
  background: #FFE6E6;
  color: #FF3B30;
}

/* 检查员问题照片 */
.inspection-photos-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.inspection-photo-item {
  position: relative;
  width: 170rpx;
  height: 170rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.inspection-photo-item image {
  width: 100%;
  height: 100%;
}

.previous-photos-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.previous-photo-item {
  position: relative;
  width: 170rpx;
  height: 170rpx;
  border-radius: 12rpx;
  overflow: hidden;
  opacity: 0.8;
}

.previous-photo-item image {
  width: 100%;
  height: 100%;
}

.photo-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 16rpx 12rpx 8rpx;
}

.photo-time {
  font-size: 20rpx;
  color: white;
  text-align: center;
  display: block;
}
</style>