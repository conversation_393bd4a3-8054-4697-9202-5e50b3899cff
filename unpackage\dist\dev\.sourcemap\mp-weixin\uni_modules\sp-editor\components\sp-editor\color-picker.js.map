{"version": 3, "sources": ["webpack:///D:/Xwzc/uni_modules/sp-editor/components/sp-editor/color-picker.vue?be97", "webpack:///D:/Xwzc/uni_modules/sp-editor/components/sp-editor/color-picker.vue?142f", "webpack:///D:/Xwzc/uni_modules/sp-editor/components/sp-editor/color-picker.vue?07f8", "webpack:///D:/Xwzc/uni_modules/sp-editor/components/sp-editor/color-picker.vue?c629", "uni-app:///uni_modules/sp-editor/components/sp-editor/color-picker.vue", "webpack:///D:/Xwzc/uni_modules/sp-editor/components/sp-editor/color-picker.vue?27c2", "webpack:///D:/Xwzc/uni_modules/sp-editor/components/sp-editor/color-picker.vue?8c05"], "names": ["props", "color", "type", "default", "r", "g", "b", "a", "spareColor", "data", "show", "active", "rgba", "hsb", "h", "s", "site", "top", "left", "index", "bgcolor", "hex", "mode", "colorList", "created", "methods", "ready", "init", "moveHandle", "open", "setTimeout", "close", "confirm", "select", "selectColor", "touchstart", "pageX", "pageY", "clientX", "clientY", "touchmove", "touchend", "setPosition", "width", "height", "setColor", "setValue", "setControl", "rgbToHex", "setColorBySelect", "rgb", "changeViewByHsb", "c", "HSBToRGB", "rgbToHsb", "getSelectorQuery", "views", "selectAll", "boundingClientRect", "exec", "hex2Rgb", "watch"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAsmB,CAAgB,goBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC8F1nB;EACAA;IACAC;MACAC;MACAC;QACA;UACAC;UACAC;UACAC;UACAC;QACA;MACA;IACA;IACAC;MACAN;MACAC;QACA;MACA;IACA;EACA;EACAM;IACA;MACAC;MACAC;MACA;MACAC;QACAR;QACAC;QACAC;QACAC;MACA;MACA;MACAM;QACAC;QACAC;QACAT;MACA;MACAU,OACA;QACAC;QACAC;MACA,GACA;QACAA;MACA,GACA;QACAA;MACA,EACA;MACAC;MACAC;QACAhB;QACAC;QACAC;QACAC;MACA;MACAc;MACAC;MACAC,YACA;QACAnB;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA;IAEA;EACA;EACAiB;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;IACAC;MAAA;MACA;MACA;QACA;QACAC;UACA;UACAA;YACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACAD;UACA;QACA;MACA;IACA;IACAE;MACA;MACA;QACApB;QACAS;MACA;IACA;IACA;IACAY;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QAAAC;QAAAC;QAAAC;QAAAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QAAAJ;QAAAC;QAAAC;QAAAC;MACA;MACA;MACA;IACA;IACAE;IACA;AACA;AACA;IACAC;MACA;MACA;QAAAzB;QAAAC;QAAAyB;QAAAC;MACA;;MAEA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;IACA;IACAC;MACA;QAAA9B;QAAAC;QAAAyB;QAAAC;MAEA;QACA;QACA;UACA9B;UACAC;UACAT;QACA;QACA;MACA;QACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACA0C;MACA;MACA3B;QACA;UACAA;QACA;MACA;MACA;IACA;IACA4B;MACA;QAAA5C;QAAAC;QAAAC;MACA;MACA2C;QACA9C;QACAC;QACAC;QACAC;MACA;MACA;MACA;MACA;IACA;IACA4C;MACA;QAAA5C;QAAAD;QAAA8C;MACA;MACA;MACA;MACA;MACA;QACAtC;QACAC;QACAT;MACA;MAEA;MACA;IACA;IACA;AACA;AACA;AACA;IACA+C;MACA;MACA;MACA;MACA;MACA;QACAH;MACA;QACA;QACA;QACA;QACA;QACA;UACAA;UACAA;UACAA;QACA;UACAA;UACAA;UACAA;QACA;UACAA;UACAA;UACAA;QACA;UACAA;UACAA;UACAA;QACA;UACAA;UACAA;UACAA;QACA;UACAA;UACAA;UACAA;QACA;UACAA;UACAA;UACAA;QACA;MACA;MACA;QACA9C;QACAC;QACAC;MACA;IACA;IACAgD;MACA;QACAxC;QACAC;QACAT;MACA;MACA;MACA;MACA;MACAO;MACAA;MACA;QACA,uDACA,2DACAA;MACA;MACAA;MACA;MACAA;MACAA;MACA;IACA;IACA0C;MAAA;MACA;MACAC,MACAC,mBACAC;QACA;UACA5B;YAAA;UAAA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA6B;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;QACAxD;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAsD;IACArD;MACA;IACA;IACAP;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACviBA;AAAA;AAAA;AAAA;AAAw3B,CAAgB,y3BAAG,EAAC,C;;;;;;;;;;;ACA54B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/sp-editor/components/sp-editor/color-picker.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./color-picker.vue?vue&type=template&id=1202e01c&\"\nvar renderjs\nimport script from \"./color-picker.vue?vue&type=script&lang=js&\"\nexport * from \"./color-picker.vue?vue&type=script&lang=js&\"\nimport style0 from \"./color-picker.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/sp-editor/components/sp-editor/color-picker.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./color-picker.vue?vue&type=template&id=1202e01c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./color-picker.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./color-picker.vue?vue&type=script&lang=js&\"", "<template>\n  <view v-if=\"show\" class=\"t-wrapper\" @touchmove.stop.prevent=\"moveHandle\">\n    <view class=\"t-mask\" :class=\"{ active: active }\" @click.stop=\"close\"></view>\n    <view class=\"t-box\" :class=\"{ active: active }\">\n      <view class=\"t-header\">\n        <view class=\"t-header-button\" @click=\"close\">取消</view>\n        <view class=\"t-header-button\" @click=\"confirm\">确认</view>\n      </view>\n      <view class=\"t-color__box\" :style=\"{ background: 'rgb(' + bgcolor.r + ',' + bgcolor.g + ',' + bgcolor.b + ')' }\">\n        <view\n          class=\"t-background boxs\"\n          @touchstart=\"touchstart($event, 0)\"\n          @touchmove=\"touchmove($event, 0)\"\n          @touchend=\"touchend($event, 0)\"\n        >\n          <view class=\"t-color-mask\"></view>\n          <view class=\"t-pointer\" :style=\"{ top: site[0].top - 8 + 'px', left: site[0].left - 8 + 'px' }\"></view>\n        </view>\n      </view>\n      <view class=\"t-control__box\">\n        <view class=\"t-control__color\">\n          <view\n            class=\"t-control__color-content\"\n            :style=\"{ background: 'rgba(' + rgba.r + ',' + rgba.g + ',' + rgba.b + ',' + rgba.a + ')' }\"\n          ></view>\n        </view>\n        <view class=\"t-control-box__item\">\n          <view\n            class=\"t-controller boxs\"\n            @touchstart=\"touchstart($event, 1)\"\n            @touchmove=\"touchmove($event, 1)\"\n            @touchend=\"touchend($event, 1)\"\n          >\n            <view class=\"t-hue\">\n              <view class=\"t-circle\" :style=\"{ left: site[1].left - 12 + 'px' }\"></view>\n            </view>\n          </view>\n          <view\n            class=\"t-controller boxs\"\n            @touchstart=\"touchstart($event, 2)\"\n            @touchmove=\"touchmove($event, 2)\"\n            @touchend=\"touchend($event, 2)\"\n          >\n            <view class=\"t-transparency\">\n              <view class=\"t-circle\" :style=\"{ left: site[2].left - 12 + 'px' }\"></view>\n            </view>\n          </view>\n        </view>\n      </view>\n      <view class=\"t-result__box\">\n        <view v-if=\"mode\" class=\"t-result__item\">\n          <view class=\"t-result__box-input\">{{ hex }}</view>\n          <view class=\"t-result__box-text\">HEX</view>\n        </view>\n        <template v-else>\n          <view class=\"t-result__item\">\n            <view class=\"t-result__box-input\">{{ rgba.r }}</view>\n            <view class=\"t-result__box-text\">R</view>\n          </view>\n          <view class=\"t-result__item\">\n            <view class=\"t-result__box-input\">{{ rgba.g }}</view>\n            <view class=\"t-result__box-text\">G</view>\n          </view>\n          <view class=\"t-result__item\">\n            <view class=\"t-result__box-input\">{{ rgba.b }}</view>\n            <view class=\"t-result__box-text\">B</view>\n          </view>\n          <view class=\"t-result__item\">\n            <view class=\"t-result__box-input\">{{ rgba.a }}</view>\n            <view class=\"t-result__box-text\">A</view>\n          </view>\n        </template>\n\n        <view class=\"t-result__item t-select\" @click=\"select\">\n          <view class=\"t-result__box-input\">\n            <view>切换</view>\n            <view>模式</view>\n          </view>\n        </view>\n      </view>\n      <view class=\"t-alternative\">\n        <view class=\"t-alternative__item\" v-for=\"(item, index) in colorList\" :key=\"index\">\n          <view\n            class=\"t-alternative__item-content\"\n            :style=\"{ background: 'rgba(' + item.r + ',' + item.g + ',' + item.b + ',' + item.a + ')' }\"\n            @click=\"selectColor(item)\"\n          ></view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  props: {\n    color: {\n      type: Object,\n      default: () => {\n        return {\n          r: 0,\n          g: 0,\n          b: 0,\n          a: 0\n        }\n      }\n    },\n    spareColor: {\n      type: Array,\n      default() {\n        return []\n      }\n    }\n  },\n  data() {\n    return {\n      show: false,\n      active: false,\n      // rgba 颜色\n      rgba: {\n        r: 0,\n        g: 0,\n        b: 0,\n        a: 1\n      },\n      // hsb 颜色\n      hsb: {\n        h: 0,\n        s: 0,\n        b: 0\n      },\n      site: [\n        {\n          top: 0,\n          left: 0\n        },\n        {\n          left: 0\n        },\n        {\n          left: 0\n        }\n      ],\n      index: 0,\n      bgcolor: {\n        r: 255,\n        g: 0,\n        b: 0,\n        a: 1\n      },\n      hex: '#000000',\n      mode: true,\n      colorList: [\n        {\n          r: 244,\n          g: 67,\n          b: 54,\n          a: 1\n        },\n        {\n          r: 233,\n          g: 30,\n          b: 99,\n          a: 1\n        },\n        {\n          r: 156,\n          g: 39,\n          b: 176,\n          a: 1\n        },\n        {\n          r: 103,\n          g: 58,\n          b: 183,\n          a: 1\n        },\n        {\n          r: 63,\n          g: 81,\n          b: 181,\n          a: 1\n        },\n        {\n          r: 33,\n          g: 150,\n          b: 243,\n          a: 1\n        },\n        {\n          r: 3,\n          g: 169,\n          b: 244,\n          a: 1\n        },\n        {\n          r: 0,\n          g: 188,\n          b: 212,\n          a: 1\n        },\n        {\n          r: 0,\n          g: 150,\n          b: 136,\n          a: 1\n        },\n        {\n          r: 76,\n          g: 175,\n          b: 80,\n          a: 1\n        },\n        {\n          r: 139,\n          g: 195,\n          b: 74,\n          a: 1\n        },\n        {\n          r: 205,\n          g: 220,\n          b: 57,\n          a: 1\n        },\n        {\n          r: 255,\n          g: 235,\n          b: 59,\n          a: 1\n        },\n        {\n          r: 255,\n          g: 193,\n          b: 7,\n          a: 1\n        },\n        {\n          r: 255,\n          g: 152,\n          b: 0,\n          a: 1\n        },\n        {\n          r: 255,\n          g: 87,\n          b: 34,\n          a: 1\n        },\n        {\n          r: 121,\n          g: 85,\n          b: 72,\n          a: 1\n        },\n        {\n          r: 158,\n          g: 158,\n          b: 158,\n          a: 1\n        },\n        {\n          r: 0,\n          g: 0,\n          b: 0,\n          a: 0.5\n        },\n        {\n          r: 0,\n          g: 0,\n          b: 0,\n          a: 0\n        }\n      ]\n    }\n  },\n  created() {\n    this.ready()\n  },\n  methods: {\n    ready() {\n      this.rgba = this.color\n      if (this.spareColor.length !== 0) {\n        this.colorList = this.spareColor\n      }\n    },\n    /**\n     * 初始化\n     */\n    init() {\n      // hsb 颜色\n      this.hsb = this.rgbToHex(this.rgba)\n      // this.setColor();\n      this.setValue(this.rgba)\n    },\n    moveHandle() {},\n    open() {\n      this.show = true\n      this.$nextTick(() => {\n        this.init()\n        setTimeout(() => {\n          this.active = true\n          setTimeout(() => {\n            this.getSelectorQuery()\n          }, 350)\n        }, 50)\n      })\n    },\n    close() {\n      this.active = false\n      this.$nextTick(() => {\n        setTimeout(() => {\n          this.show = false\n        }, 500)\n      })\n    },\n    confirm() {\n      this.close()\n      this.$emit('confirm', {\n        rgba: this.rgba,\n        hex: this.hex\n      })\n    },\n    // 选择模式\n    select() {\n      this.mode = !this.mode\n    },\n    // 常用颜色选择\n    selectColor(item) {\n      this.setColorBySelect(item)\n    },\n    touchstart(e, index) {\r\n      const { pageX, pageY, clientX, clientY } = e.touches[0]\n      // 部分机型可能没有pageX或clientX，因此此处需要做兼容\n      this.moveX = clientX || pageX\n      this.moveY = clientY || pageY\n      this.setPosition(this.moveX, this.moveY, index)\n    },\n    touchmove(e, index) {\r\n      const { pageX, pageY, clientX, clientY } = e.touches[0]\n      this.moveX = clientX || pageX\n      this.moveY = clientY || pageY\n      this.setPosition(this.moveX, this.moveY, index)\n    },\n    touchend(e, index) {},\n    /**\n     * 设置位置\n     */\n    setPosition(x, y, index) {\n      this.index = index\n      const { top, left, width, height } = this.position[index]\n      // 设置最大最小值\n\n      this.site[index].left = Math.max(0, Math.min(parseInt(x - left), width))\n      if (index === 0) {\n        this.site[index].top = Math.max(0, Math.min(parseInt(y - top), height))\n        // 设置颜色\n        this.hsb.s = parseInt((100 * this.site[index].left) / width)\n        this.hsb.b = parseInt(100 - (100 * this.site[index].top) / height)\n        this.setColor()\n        this.setValue(this.rgba)\n      } else {\n        this.setControl(index, this.site[index].left)\n      }\n    },\n    /**\n     * 设置 rgb 颜色\n     */\n    setColor() {\n      const rgb = this.HSBToRGB(this.hsb)\n      this.rgba.r = rgb.r\n      this.rgba.g = rgb.g\n      this.rgba.b = rgb.b\n    },\n    /**\n     * 设置二进制颜色\n     * @param {Object} rgb\n     */\n    setValue(rgb) {\n      this.hex = '#' + this.rgbToHex(rgb)\n    },\n    setControl(index, x) {\n      const { top, left, width, height } = this.position[index]\n\n      if (index === 1) {\n        this.hsb.h = parseInt((360 * x) / width)\n        this.bgcolor = this.HSBToRGB({\n          h: this.hsb.h,\n          s: 100,\n          b: 100\n        })\n        this.setColor()\n      } else {\n        this.rgba.a = (x / width).toFixed(1)\n      }\n      this.setValue(this.rgba)\n    },\n    /**\n     * rgb 转 二进制 hex\n     * @param {Object} rgb\n     */\n    rgbToHex(rgb) {\n      let hex = [rgb.r.toString(16), rgb.g.toString(16), rgb.b.toString(16)]\n      hex.map(function (str, i) {\n        if (str.length == 1) {\n          hex[i] = '0' + str\n        }\n      })\n      return hex.join('')\n    },\n    setColorBySelect(getrgb) {\n      const { r, g, b, a } = getrgb\n      let rgb = {}\n      rgb = {\n        r: r ? parseInt(r) : 0,\n        g: g ? parseInt(g) : 0,\n        b: b ? parseInt(b) : 0,\n        a: a ? a : 0\n      }\n      this.rgba = rgb\n      this.hsb = this.rgbToHsb(rgb)\n      this.changeViewByHsb()\n    },\n    changeViewByHsb() {\n      const [a, b, c] = this.position\n      this.site[0].left = parseInt((this.hsb.s * a.width) / 100)\n      this.site[0].top = parseInt(((100 - this.hsb.b) * a.height) / 100)\n      this.setColor(this.hsb.h)\n      this.setValue(this.rgba)\n      this.bgcolor = this.HSBToRGB({\n        h: this.hsb.h,\n        s: 100,\n        b: 100\n      })\n\n      this.site[1].left = (this.hsb.h / 360) * b.width\n      this.site[2].left = this.rgba.a * c.width\n    },\n    /**\n     * hsb 转 rgb\n     * @param {Object} 颜色模式  H(hues)表示色相，S(saturation)表示饱和度，B（brightness）表示亮度\n     */\n    HSBToRGB(hsb) {\n      let rgb = {}\n      let h = Math.round(hsb.h)\n      let s = Math.round((hsb.s * 255) / 100)\n      let v = Math.round((hsb.b * 255) / 100)\n      if (s == 0) {\n        rgb.r = rgb.g = rgb.b = v\n      } else {\n        let t1 = v\n        let t2 = ((255 - s) * v) / 255\n        let t3 = ((t1 - t2) * (h % 60)) / 60\n        if (h == 360) h = 0\n        if (h < 60) {\n          rgb.r = t1\n          rgb.b = t2\n          rgb.g = t2 + t3\n        } else if (h < 120) {\n          rgb.g = t1\n          rgb.b = t2\n          rgb.r = t1 - t3\n        } else if (h < 180) {\n          rgb.g = t1\n          rgb.r = t2\n          rgb.b = t2 + t3\n        } else if (h < 240) {\n          rgb.b = t1\n          rgb.r = t2\n          rgb.g = t1 - t3\n        } else if (h < 300) {\n          rgb.b = t1\n          rgb.g = t2\n          rgb.r = t2 + t3\n        } else if (h < 360) {\n          rgb.r = t1\n          rgb.g = t2\n          rgb.b = t1 - t3\n        } else {\n          rgb.r = 0\n          rgb.g = 0\n          rgb.b = 0\n        }\n      }\n      return {\n        r: Math.round(rgb.r),\n        g: Math.round(rgb.g),\n        b: Math.round(rgb.b)\n      }\n    },\n    rgbToHsb(rgb) {\n      let hsb = {\n        h: 0,\n        s: 0,\n        b: 0\n      }\n      let min = Math.min(rgb.r, rgb.g, rgb.b)\n      let max = Math.max(rgb.r, rgb.g, rgb.b)\n      let delta = max - min\n      hsb.b = max\n      hsb.s = max != 0 ? (255 * delta) / max : 0\n      if (hsb.s != 0) {\n        if (rgb.r == max) hsb.h = (rgb.g - rgb.b) / delta\n        else if (rgb.g == max) hsb.h = 2 + (rgb.b - rgb.r) / delta\n        else hsb.h = 4 + (rgb.r - rgb.g) / delta\n      } else hsb.h = -1\n      hsb.h *= 60\n      if (hsb.h < 0) hsb.h = 0\n      hsb.s *= 100 / 255\n      hsb.b *= 100 / 255\n      return hsb\n    },\n    getSelectorQuery() {\n      const views = uni.createSelectorQuery().in(this)\n      views\n        .selectAll('.boxs')\n        .boundingClientRect((data) => {\n          if (!data || data.length === 0) {\n            setTimeout(() => this.getSelectorQuery(), 20)\n            return\n          }\n          this.position = data\n          // this.site[0].top = data[0].height;\n          // this.site[0].left = 0;\n          // this.site[1].left = data[1].width;\n          // this.site[2].left = data[2].width;\n          this.setColorBySelect(this.rgba)\n        })\n        .exec()\n    },\n    hex2Rgb(hexColor, alpha = 1) {\n      const color = hexColor.slice(1)\n      const r = parseInt(color.slice(0, 2), 16)\n      const g = parseInt(color.slice(2, 4), 16)\n      const b = parseInt(color.slice(4, 6), 16)\n      return {\n        r: r,\n        g: g,\n        b: b,\n        a: alpha\n      }\n    }\n  },\n  watch: {\n    spareColor(newVal) {\n      this.colorList = newVal\n    },\n    color(newVal) {\n      this.ready()\n    }\n  }\n}\n</script>\n\n<style>\n.t-wrapper {\n  position: fixed;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  box-sizing: border-box;\n  z-index: 9999;\n}\n\n.t-box {\n  width: 100%;\n  position: absolute;\n  bottom: 0;\n  padding: 30upx 0;\n  padding-top: 0;\n  background: #fff;\n  transition: all 0.3s;\n  transform: translateY(100%);\n}\n\n.t-box.active {\n  transform: translateY(0%);\n}\n\n.t-header {\n  display: flex;\n  justify-content: space-between;\n  width: 100%;\n  height: 100upx;\n  border-bottom: 1px #eee solid;\n  box-shadow: 1px 0 2px rgba(0, 0, 0, 0.1);\n  background: #fff;\n}\n\n.t-header-button {\n  display: flex;\n  align-items: center;\n  width: 150upx;\n  height: 100upx;\n  font-size: 30upx;\n  color: #666;\n  padding-left: 20upx;\n}\n\n.t-header-button:last-child {\n  justify-content: flex-end;\n  padding-right: 20upx;\n}\n\n.t-mask {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.6);\n  z-index: -1;\n  transition: all 0.3s;\n  opacity: 0;\n}\n\n.t-mask.active {\n  opacity: 1;\n}\n\n.t-color__box {\n  position: relative;\n  height: 400upx;\n  background: rgb(255, 0, 0);\n  overflow: hidden;\n  box-sizing: border-box;\n  margin: 0 20upx;\n  margin-top: 20upx;\n  box-sizing: border-box;\n}\n\n.t-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(to right, #fff, rgba(255, 255, 255, 0));\n}\n\n.t-color-mask {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  width: 100%;\n  height: 400upx;\n  background: linear-gradient(to top, #000, rgba(0, 0, 0, 0));\n}\n\n.t-pointer {\n  position: absolute;\n  bottom: -8px;\n  left: -8px;\n  z-index: 2;\n  width: 15px;\n  height: 15px;\n  border: 1px #fff solid;\n  border-radius: 50%;\n}\n\n.t-show-color {\n  width: 100upx;\n  height: 50upx;\n}\n\n.t-control__box {\n  margin-top: 50upx;\n  width: 100%;\n  display: flex;\n  padding-left: 20upx;\n  box-sizing: border-box;\n}\n\n.t-control__color {\n  flex-shrink: 0;\n  width: 100upx;\n  height: 100upx;\n  border-radius: 50%;\n  background-color: #fff;\n  background-image: linear-gradient(45deg, #eee 25%, transparent 25%, transparent 75%, #eee 75%, #eee),\n    linear-gradient(45deg, #eee 25%, transparent 25%, transparent 75%, #eee 75%, #eee);\n  background-size: 36upx 36upx;\n  background-position: 0 0, 18upx 18upx;\n  border: 1px #eee solid;\n  overflow: hidden;\n}\n\n.t-control__color-content {\n  width: 100%;\n  height: 100%;\n}\n\n.t-control-box__item {\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  width: 100%;\n  padding: 0 30upx;\n}\n\n.t-controller {\n  position: relative;\n  width: 100%;\n  height: 16px;\n  background-color: #fff;\n  background-image: linear-gradient(45deg, #eee 25%, transparent 25%, transparent 75%, #eee 75%, #eee),\n    linear-gradient(45deg, #eee 25%, transparent 25%, transparent 75%, #eee 75%, #eee);\n  background-size: 32upx 32upx;\n  background-position: 0 0, 16upx 16upx;\n}\n\n.t-hue {\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);\n}\n\n.t-transparency {\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(to right, rgba(0, 0, 0, 0) 0%, rgb(0, 0, 0));\n}\n\n.t-circle {\n  position: absolute;\n  /* right: -10px; */\n  top: -2px;\n  width: 20px;\n  height: 20px;\n  box-sizing: border-box;\n  border-radius: 50%;\n  background: #fff;\n  box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.1);\n}\n\n.t-result__box {\n  margin-top: 20upx;\n  padding: 10upx;\n  width: 100%;\n  display: flex;\n  box-sizing: border-box;\n}\n\n.t-result__item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 10upx;\n  width: 100%;\n  box-sizing: border-box;\n}\n\n.t-result__box-input {\n  padding: 10upx 0;\n  width: 100%;\n  font-size: 28upx;\n  box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);\n  color: #999;\n  text-align: center;\n  background: #fff;\n}\n\n.t-result__box-text {\n  margin-top: 10upx;\n  font-size: 28upx;\n  line-height: 2;\n}\n\n.t-select {\n  flex-shrink: 0;\n  width: 150upx;\n  padding: 0 30upx;\n}\n\n.t-select .t-result__box-input {\n  border-radius: 10upx;\n  border: none;\n  color: #999;\n  box-shadow: 1px 1px 2px 1px rgba(0, 0, 0, 0.1);\n  background: #fff;\n}\n\n.t-select .t-result__box-input:active {\n  box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.1);\n}\n\n.t-alternative {\n  display: flex;\n  flex-wrap: wrap;\n  /* justify-content: space-between; */\n  width: 100%;\n  padding-right: 10upx;\n  box-sizing: border-box;\n}\n\n.t-alternative__item {\n  margin-left: 12upx;\n  margin-top: 10upx;\n  width: 50upx;\n  height: 50upx;\n  border-radius: 10upx;\n  background-color: #fff;\n  background-image: linear-gradient(45deg, #eee 25%, transparent 25%, transparent 75%, #eee 75%, #eee),\n    linear-gradient(45deg, #eee 25%, transparent 25%, transparent 75%, #eee 75%, #eee);\n  background-size: 36upx 36upx;\n  background-position: 0 0, 18upx 18upx;\n  border: 1px #eee solid;\n  overflow: hidden;\n}\n\n.t-alternative__item-content {\n  width: 50upx;\n  height: 50upx;\n  background: rgba(255, 0, 0, 0.5);\n}\n\n.t-alternative__item:active {\n  transition: all 0.3s;\n  transform: scale(1.1);\n}\n</style>\n", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./color-picker.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./color-picker.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775854770\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}