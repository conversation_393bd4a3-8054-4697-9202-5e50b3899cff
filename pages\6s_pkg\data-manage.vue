<template>
  <view class="page-container">
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-title">基础数据管理</view>
      <view class="header-subtitle">统一管理6S系统所有基础配置数据</view>
    </view>

    <!-- 数据概览 -->
    <view class="overview-card">
      <view class="overview-title">数据概览</view>
      
      <!-- 加载状态 -->
      <view v-if="loading && !dataLoaded" class="stats-loading">
        <view class="loading-content">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载数据概览中...</text>
        </view>
      </view>
      
      <!-- 错误状态 -->
      <view v-else-if="loadError" class="error-container">
        <view class="error-content">
          <uni-icons type="info" size="24" color="#FF3B30"></uni-icons>
          <text class="error-text">数据加载失败，请稍后重试</text>
          <button class="retry-btn" @click="retryLoad">重新加载</button>
        </view>
      </view>
      
      <!-- 正常统计 -->
      <view v-else class="stats-grid">
        <view class="stat-item">
          <view class="stat-number">{{ overview.fixedAreas || 0 }}</view>
          <view class="stat-label">固定责任区</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{ overview.publicAreas || 0 }}</view>
          <view class="stat-label">公共责任区</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{ overview.locations || 0 }}</view>
          <view class="stat-label">月度位置数</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{ overview.employees || 0 }}</view>
          <view class="stat-label">已分配员工</view>
        </view>
      </view>
    </view>

    <!-- 管理模块 -->
    <view class="modules-container">
             <!-- 责任区管理 -->
       <view class="module-section">
         <view class="section-title">
           <uni-icons type="home" size="20" color="#007AFF" />
           <text>责任区管理</text>
         </view>
         <view class="module-grid">
           <view class="module-card" @click="manageFixedAreas">
             <view class="module-icon fixed-area">
               <uni-icons type="shop" size="32" color="#007AFF" />
             </view>
             <view class="module-content">
               <view class="module-title">固定责任区</view>
               <view class="module-desc">管理各部门固定负责的区域</view>
               <view class="module-count">{{ overview.fixedAreas || 0 }} 个</view>
             </view>
             <view class="module-arrow">
               <uni-icons type="right" size="16" color="#C7C7CC" />
             </view>
           </view>

           <view class="module-card" @click="managePublicAreas">
             <view class="module-icon public-area">
               <uni-icons type="list" size="32" color="#34C759" />
             </view>
             <view class="module-content">
               <view class="module-title">公共责任区</view>
               <view class="module-desc">管理需要轮班清洁的公共区域</view>
               <view class="module-count">{{ overview.publicAreas || 0 }} 个</view>
             </view>
             <view class="module-arrow">
               <uni-icons type="right" size="16" color="#C7C7CC" />
             </view>
           </view>
         </view>
       </view>

             <!-- 基础配置 -->
       <view class="module-section">
         <view class="section-title">
           <uni-icons type="gear" size="20" color="#FF9500" />
           <text>基础配置</text>
         </view>
         <view class="module-grid">
           <view class="module-card" @click="manageLocations">
             <view class="module-icon location">
               <uni-icons type="location" size="32" color="#FF9500" />
             </view>
             <view class="module-content">
               <view class="module-title">月度位置管理</view>
               <view class="module-desc">配置厂区所有区域位置信息</view>
               <view class="module-count">{{ overview.locations || 0 }} 个</view>
             </view>
             <view class="module-arrow">
               <uni-icons type="right" size="16" color="#C7C7CC" />
             </view>
           </view>

           <view class="module-card" @click="manageAssignment">
             <view class="module-icon assignment">
               <uni-icons type="person" size="32" color="#8B5CF6" />
             </view>
             <view class="module-content">
               <view class="module-title">员工责任区分配</view>
               <view class="module-desc">为员工分配固定责任区</view>
               <view class="module-count">{{ overview.assignments || 0 }} 个分配</view>
             </view>
             <view class="module-arrow">
               <uni-icons type="right" size="16" color="#C7C7CC" />
             </view>
           </view>

           <view class="module-card" @click="managePublicSchedule">
             <view class="module-icon public-schedule">
               <uni-icons type="compose" size="32" color="#FF3B30" />
             </view>
             <view class="module-content">
               <view class="module-title">公共区清扫日设置</view>
               <view class="module-desc">设置公共责任区固定清扫日</view>
               <view class="module-count">{{ overview.publicAreas || 0 }} 个区域</view>
             </view>
             <view class="module-arrow">
               <uni-icons type="right" size="16" color="#C7C7CC" />
             </view>
           </view>
         </view>
       </view>

      <!-- 数据导出 -->
      <!-- TODO: 数据导出功能暂时注释，后期需要时恢复 -->
      <!--
      <view class="module-section">
        <view class="section-title">
          <uni-icons type="download" size="20" color="#34C759" />
          <text>数据导出</text>
        </view>
        <view class="module-grid">
             <view class="module-card" @click="navigateToExportPage">
             <view class="module-icon export-data">
               <uni-icons type="upload" size="32" color="#007AFF" />
             </view>
             <view class="module-content">
               <view class="module-title">数据导出</view>
               <view class="module-desc">导出各类检查数据和统计报告</view>
               <view class="module-count">Excel格式</view>
             </view>
             <view class="module-arrow">
               <uni-icons type="right" size="16" color="#C7C7CC" />
             </view>
           </view>
        </view>
      </view>
      -->
    </view>

    <!-- 自定义权限不足弹窗 -->
    <view v-if="showPermissionModal" class="permission-modal-overlay">
      <view class="permission-modal">
        <view class="modal-icon">
          <uni-icons type="locked" size="48" color="#FF3B30"></uni-icons>
        </view>
        <view class="modal-title">权限不足</view>
        <view class="modal-content">
          抱歉，您没有访问基础数据管理的权限。
          <br/>该功能仅限管理人员使用。
        </view>
        <view class="modal-actions">
          <button class="modal-btn primary" @click="closePermissionModal">
            返回首页
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { callCloudFunction } from '@/utils/auth.js';

export default {
  name: 'DataManage',
  data() {
    return {
      loading: false,
      loadError: false,
      dataLoaded: false,
      // 权限弹窗控制
      showPermissionModal: false,
      overview: {
        fixedAreas: 0,
        publicAreas: 0,
        locations: 0,
        employees: 0,
        assignments: 0
      },
      // 数据缓存
      processCache: {
        overviewData: null,
        cacheTimestamp: null,
        cacheExpiry: 5 * 60 * 1000 // 5分钟缓存
      }
    }
  },
  
  onLoad() {
    // 首先检查用户权限
    this.checkUserPermission();
    this.loadOverviewDataOptimized();
  },
  
  onShow() {
    // 页面显示时检查缓存是否过期，如果过期则静默刷新
    if (this.dataLoaded && this.isCacheExpired()) {
      this.silentRefreshData();
    }
  },
  
  methods: {
    // 检查用户权限
    checkUserPermission() {
      try {
        const { getCacheKey, CACHE_KEYS } = require('@/utils/cache.js');
        
        // 获取当前用户信息
        const userInfo = uni.getStorageSync('uni-id-pages-userInfo') || {};
        
        // 获取用户角色信息（从专门的角色缓存中获取）- 与其他页面保持一致
        let userRoleStr = uni.getStorageSync(getCacheKey(CACHE_KEYS.USER_ROLE)) || '{}';
        let userRole = {};
        
        try {
          if (typeof userRoleStr === 'string') {
            userRole = JSON.parse(userRoleStr);
          } else {
            userRole = userRoleStr;
          }
        } catch (e) {
          userRole = {};
        }
        
        // 从userRole.value.userRole获取角色数组
        let roles = [];
        if (userRole && userRole.value && userRole.value.userRole) {
          roles = Array.isArray(userRole.value.userRole) ? userRole.value.userRole : [userRole.value.userRole];
        } else if (userInfo.role) {
          // 备用方案：从用户信息中获取角色
          roles = Array.isArray(userInfo.role) ? userInfo.role : [userInfo.role];
        } else if (userInfo.username === 'admin') {
          // 特殊处理：如果用户名是admin，给予admin角色
          roles = ['admin'];
        }
        
        // 检查是否有基础数据管理权限
        const adminRoles = ['admin', 'GM', 'PM', 'Integrated', 'reviser'];
        const hasPermission = roles.some(role => adminRoles.includes(role));
        

        
        if (!hasPermission) {
          // 显示自定义权限弹窗
          this.showPermissionModal = true;
          return false;
        }
        
        return true;
      } catch (error) {
        console.error('权限检查失败:', error);
        uni.showToast({
          title: '权限验证失败',
          icon: 'none',
          duration: 2000
        });
        // 权限验证失败时也返回首页
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/6s/index'
          });
        }, 2000);
        return false;
      }
    },

    // 关闭权限弹窗并返回首页
    closePermissionModal() {
      this.showPermissionModal = false;
      uni.switchTab({
        url: '/pages/6s/index'
      });
    },

    // 优化后的概览数据加载
    async loadOverviewDataOptimized() {
      try {
        this.loading = true;
        this.loadError = false;
        
        // 检查缓存是否有效
        if (this.isCacheValid()) {
          this.overview = this.processCache.overviewData;
          this.loading = false;
          this.dataLoaded = true;
          return;
        }
        
        // 并行加载所有统计数据
        const results = await Promise.allSettled([
          this.loadAreasCountOptimized('fixed'),
          this.loadAreasCountOptimized('public'),
          this.loadLocationsCountOptimized(),
          this.loadEmployeeAssignmentCountOptimized()
        ]);
        
        // 处理结果
        const overviewData = {
          fixedAreas: results[0].status === 'fulfilled' ? results[0].value : 0,
          publicAreas: results[1].status === 'fulfilled' ? results[1].value : 0,
          locations: results[2].status === 'fulfilled' ? results[2].value : 0,
          employees: results[3].status === 'fulfilled' ? results[3].value.employees : 0,
          assignments: results[3].status === 'fulfilled' ? results[3].value.assignments : 0
        };
        
        // 更新数据和缓存
        this.overview = overviewData;
        this.updateCache(overviewData);
        
      } catch (error) {
        this.handleLoadError(error);
      } finally {
        this.loading = false;
        this.dataLoaded = true;
      }
    },
    
    // 静默刷新数据
    async silentRefreshData() {
      if (this.loading) return;
      
      try {
        // 清除缓存
        this.clearCache();
        
        // 重新加载数据（不显示loading状态）
        const results = await Promise.allSettled([
          this.loadAreasCountOptimized('fixed'),
          this.loadAreasCountOptimized('public'),
          this.loadLocationsCountOptimized(),
          this.loadEmployeeAssignmentCountOptimized()
        ]);
        
        const overviewData = {
          fixedAreas: results[0].status === 'fulfilled' ? results[0].value : this.overview.fixedAreas,
          publicAreas: results[1].status === 'fulfilled' ? results[1].value : this.overview.publicAreas,
          locations: results[2].status === 'fulfilled' ? results[2].value : this.overview.locations,
          employees: results[3].status === 'fulfilled' ? results[3].value.employees : this.overview.employees,
          assignments: results[3].status === 'fulfilled' ? results[3].value.assignments : this.overview.assignments
        };
        
        this.overview = overviewData;
        this.updateCache(overviewData);
        
      } catch (error) {
        // 静默处理错误，不影响用户体验
      }
    },
    
    // 优化后的责任区数量加载（合并固定和公共）
    async loadAreasCountOptimized(type) {
      try {
        const result = await callCloudFunction('hygiene-area-management', {
          action: 'getAreaList',
          data: { type, pageSize: 1000 }
        });
        
        if (result.success && result.data) {
          return this.extractDataCount(result.data);
        }
        return 0;
      } catch (error) {
        return 0;
      }
    },
    
    // 提取数据计数的通用方法
    extractDataCount(data) {
      if (Array.isArray(data)) {
        return data.length;
      } else if (data.data && Array.isArray(data.data)) {
        return data.data.length;
      } else if (typeof data === 'object' && data.total !== undefined) {
        return data.total;
      }
      return 0;
    },
    
    // 优化后的位置数量加载
    async loadLocationsCountOptimized() {
      try {
        const result = await callCloudFunction('hygiene-location-management', {
          action: 'getLocationConfig'
        });
        if (result.success && result.data && result.data.locations) {
          return result.data.locations.reduce((sum, category) => {
            return sum + (category.items ? category.items.length : 0);
          }, 0);
        }
        return 0;
      } catch (error) {
        return 0;
      }
    },
    
    // 优化后的员工和分配数量加载
    async loadEmployeeAssignmentCountOptimized() {
      try {
        const result = await callCloudFunction('hygiene-assignments', {
          action: 'getAssignmentList',
          data: { pageSize: 1000 }
        });
        
        if (result.success && Array.isArray(result.data)) {
          return this.processAssignmentData(result.data);
        }
        return { employees: 0, assignments: 0 };
      } catch (error) {
        return { employees: 0, assignments: 0 };
      }
    },
    
    // 处理分配数据的通用方法
    processAssignmentData(assignments) {
      const employeeIds = assignments
        .map(assignment => assignment.employee_id || assignment.employee?.id || assignment.employee?._id)
        .filter(Boolean);
      
      return {
        employees: new Set(employeeIds).size,
        assignments: assignments.length
      };
    },
    
    // 固定责任区管理
    manageFixedAreas() {
      uni.navigateTo({
        url: '/pages/6s_pkg/fixed-area-manage'
      });
    },
    
    // 公共责任区管理  
    managePublicAreas() {
      uni.navigateTo({
        url: '/pages/6s_pkg/public-area-manage'
      });
    },
    
    // 位置管理
    manageLocations() {
      uni.navigateTo({
        url: '/pages/6s_pkg/location-manage'
      });
    },
    
    // 员工责任区分配
    manageAssignment() {
      uni.navigateTo({
        url: '/pages/6s_pkg/employee-assignment'
      });
    },
    
    // 公共区清扫日设置
    managePublicSchedule() {
      uni.navigateTo({
        url: '/pages/6s_pkg/public-schedule'
      });
    },
     


     // 跳转到数据导出页面 - 暂时注释
     /*
     navigateToExportPage() {
       uni.navigateTo({
         url: '/pages/6s_pkg/data-export'
       });
     },
     */
    
    // 缓存管理方法
    isCacheValid() {
      if (!this.processCache.overviewData || !this.processCache.cacheTimestamp) {
        return false;
      }
      return Date.now() - this.processCache.cacheTimestamp < this.processCache.cacheExpiry;
    },
    
    isCacheExpired() {
      if (!this.processCache.cacheTimestamp) {
        return true;
      }
      return Date.now() - this.processCache.cacheTimestamp >= this.processCache.cacheExpiry;
    },
    
    updateCache(data) {
      this.processCache.overviewData = { ...data };
      this.processCache.cacheTimestamp = Date.now();
    },
    
    clearCache() {
      this.processCache.overviewData = null;
      this.processCache.cacheTimestamp = null;
    },
    
    // 错误处理
    handleLoadError(error) {
      this.loadError = true;
      uni.showToast({
        title: '加载数据失败',
        icon: 'none',
        duration: 2000
      });
    },
    
    // 重试加载
    retryLoad() {
      this.clearCache();
      this.loadOverviewDataOptimized();
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: #F8F9FA;
  padding-bottom: 40rpx;
}

.header {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  padding: 60rpx 32rpx 40rpx;
  color: white;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 24rpx;
  opacity: 0.8;
}

.overview-card {
  background: white;
  margin: 24rpx 32rpx;
  padding: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.overview-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 24rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.stat-item {
  text-align: center;
  padding: 24rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 600;
  color: #007AFF;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #8E8E93;
}

.modules-container {
  padding: 0 32rpx;
}

.module-section {
  margin-bottom: 32rpx;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 16rpx;
}

.module-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.module-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.module-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.module-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.module-icon.fixed-area {
  background: rgba(0, 122, 255, 0.1);
}

.module-icon.public-area {
  background: rgba(52, 199, 89, 0.1);
}

.module-icon.location {
  background: rgba(255, 149, 0, 0.1);
}

.module-icon.assignment {
  background: rgba(139, 92, 246, 0.1);
}

.module-icon.public-schedule {
  background: rgba(255, 59, 48, 0.1);
}

.module-icon.export-data {
  background: rgba(0, 122, 255, 0.1);
}

.module-content {
  flex: 1;
}

.module-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 4rpx;
}

.module-desc {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
}

.module-count {
  font-size: 24rpx;
  color: #007AFF;
  font-weight: 500;
}

.module-arrow {
  padding: 8rpx;
}

/* 加载状态样式 */
.stats-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
  min-height: 200rpx;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #E5E7EB;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
  min-height: 200rpx;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.error-text {
  font-size: 28rpx;
  color: #FF3B30;
  text-align: center;
}

.retry-btn {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  font-weight: 500;
  margin-top: 16rpx;
}

.retry-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 权限弹窗样式 */
.permission-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: overlayFadeIn 0.3s ease-out;
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0rpx);
    -webkit-backdrop-filter: blur(0rpx);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(20rpx);
    -webkit-backdrop-filter: blur(20rpx);
  }
}

.permission-modal {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(40rpx);
  -webkit-backdrop-filter: blur(40rpx);
  border-radius: 32rpx;
  padding: 60rpx 48rpx;
  margin: 0 48rpx;
  max-width: 560rpx;
  width: 100%;
  text-align: center;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3),
              0 8rpx 20rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

/* 添加微妙的光效 */
.permission-modal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.8) 20%, 
    rgba(255, 255, 255, 0.8) 80%, 
    transparent 100%);
  opacity: 0.6;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(40rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-icon {
  margin-bottom: 32rpx;
  animation: iconBounce 0.6s ease-out 0.2s both;
}

@keyframes iconBounce {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 24rpx;
  letter-spacing: 0.5rpx;
}

.modal-content {
  font-size: 28rpx;
  color: #6C7B8A;
  line-height: 1.6;
  margin-bottom: 48rpx;
  padding: 0 16rpx;
}

.modal-actions {
  display: flex;
  justify-content: center;
}

.modal-btn {
  background: linear-gradient(135deg, #FF3B30 0%, #FF453A 100%);
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 28rpx 48rpx;
  font-size: 32rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 59, 48, 0.3),
              0 4rpx 8rpx rgba(255, 59, 48, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modal-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.2), 
    transparent);
  transition: left 0.6s;
}

.modal-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 59, 48, 0.3),
              0 2rpx 4rpx rgba(255, 59, 48, 0.2);
}

.modal-btn:active::before {
  left: 100%;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .permission-modal {
    margin: 0 32rpx;
    padding: 48rpx 32rpx;
  }
  
  .modal-title {
    font-size: 32rpx;
  }
  
  .modal-content {
    font-size: 26rpx;
  }
  
  .modal-btn {
    padding: 24rpx 40rpx;
    font-size: 30rpx;
  }
}
</style> 