{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/Xwzc/pages/6s_pkg/area-detail.vue?5d3f", "webpack:///D:/Xwzc/pages/6s_pkg/area-detail.vue?b2a8", "webpack:///D:/Xwzc/pages/6s_pkg/area-detail.vue?6055", "uni-app:///pages/6s_pkg/area-detail.vue", "webpack:///D:/Xwzc/pages/6s_pkg/area-detail.vue?92cb", "webpack:///D:/Xwzc/pages/6s_pkg/area-detail.vue?05f6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "areaId", "areaType", "mode", "recordId", "cleaningDate", "week", "isHistoricalView", "loading", "loadError", "areaInfo", "id", "type", "location", "description", "assignedEmployee", "cleaningFrequency", "nextCleaningTime", "status", "canClean", "currentWeekText", "currentWeekRecord", "selected<PERSON><PERSON><PERSON><PERSON>er", "cleaningHistory", "inspectionHistory", "expandedMonths", "timeCache", "historyDataLoaded", "computed", "groupedHistory", "groups", "month", "monthText", "records", "sort", "map", "group", "onLoad", "console", "uni", "onUnload", "methods", "initCurrentWeekText", "initExpandedMonths", "getWeekNumber", "toBeijingTime", "getFullYear", "getMonth", "getDate", "getDay", "getHours", "getMinutes", "getSeconds", "getTime", "toISOString", "valueOf", "getWeekStart", "date<PERSON><PERSON>j", "currentDate", "getWeekEnd", "loadPageData", "Promise", "title", "icon", "loadHistoryDataAsync", "initTimeCache", "now", "currentYear", "currentMonth", "cutoffDates", "cutoffDate", "loadAreaInfo", "action", "result", "area", "user", "weekDays", "locationText", "scheduledDay", "cleaning_requirements", "calculateAreaStatus", "scheduledDate", "loadCleaningHistory", "area_id", "start_date", "end_date", "pageSize", "latestRecord", "weekText", "subtitle", "photoCount", "submitDate", "remark", "loadInspectionHistoryOptimized", "inspectionResult", "rectificationResult", "rectificationRecords", "rectificationMap", "relatedRectifications", "rect", "finalRating", "record", "related_rectifications", "formatInspectionRecord", "currentRectStatus", "isOverdue", "<PERSON><PERSON><PERSON>", "time", "rating", "inspection_date", "statusIndicator", "loadInspectionHistory", "inspection_record_id", "rectificationRecord", "recordsWithRectificationRating", "checkCurrentWeekRecord", "cleanDate", "beijingCleanDate", "weekStart", "weekEnd", "beijingNow", "submitTime", "photos", "getAreaTypeText", "getStatusText", "getWeekDeadlineText", "getIconColor", "goToCleaning", "url", "viewWeekPhotos", "urls", "current", "viewRecordDetail", "viewInspectionDetail", "duration", "showTimeFilter", "itemList", "success", "to<PERSON><PERSON><PERSON><PERSON>", "getRatingDescription", "getStatusIndicator", "text", "color", "desc", "checkCurrentWeekInspectionStatus", "clearInterval", "resolve", "setTimeout", "currentCleaningRecord", "hasInspectionRecord", "inspectionStatus", "checkMissedInspections", "fourWeeksAgo", "cleaningRecords", "inspectionRecords", "missedInspections", "isHistoricalRecordOverdue", "isCurrentWeekOverdue", "handleRecordUpdated"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/GA;AAAA;AAAA;AAAA;AAAqmB,CAAgB,+nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;AC0OznB;AAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;QACAC;QACAZ;QACAa;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;;MAEA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;MAEA;QACA;QACA;QAEA;UACAC;YACAC;YACAC;YACAC;UACA;QACA;QAEAH;MACA;;MAEA;MACA,6BACAI;QAAA;MAAA,GACAC;QAAA,uCACAC;UACAH;YAAA;UAAA;QAAA;MAAA,CACA;IACA;EAGA;EACAI;IACA;IACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACAC;QACA;QACA;MACA;IACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACA;IACA;;IAEA;IACAC;IACA;IACAA;IACA;IACAA;EACA;EACAC;IACA;IACAD;IACAA;IACAA;EACA;EACAE;IACA;IACAC;MACA;QACA;UACA;UACA;UACA;UAEA;YACA;YACA;UACA;UAEA;UACA;UAEA;YACA;YACA;UACA;UAEA;UACA;UACA;UAEA;QACA;UACA;UACA;UACA;UAEA;YACA;YACA;UACA;UAEA;UACA;UAEA;YACA;YACA;UACA;UAEA;UACA;UACA;UAEA;QACA;MACA;QACAJ;QACA;MACA;IACA;IAEA;IACAK;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MAEA;QACA;QACA;UACA;QACA;;QAEA;QACA;QACA;QACA;;QAEA;QACA;UACA;QACA;QAEA;UACAC;YAAA;UAAA;UACAC;YAAA;UAAA;UACAC;YAAA;UAAA;UACAC;YAAA;UAAA;UACAC;YAAA;UAAA;UACAC;YAAA;UAAA;UACAC;YAAA;UAAA;UACAC;YAAA;UAAA;UACAC;YAAA;UAAA;UACA;UACAC;YAAA;UAAA;QACA;MACA;QACAjB;QACA;MACA;IACA;IAEA;IACAkB;MACA;QACA;MACA;MAEA;QACA;QACA;UACA;UACAC;QACA;UACA;UACAA;UACA;QACA;QAEA;QACA;QACA;QACA;;QAEA;QACA;UACA;QACA;QAEA;QACA;QACAC;QACAA;QAEA;UACA;QACA;QAEA;MACA;QACApB;QACA;MACA;IACA;IAEA;IACAqB;MACA;QACA;MACA;MAEA;QACA;QACA;UACA;UACAF;QACA;UACA;UACAA;UACA;QACA;QAEA;QACA;QACA;QACA;;QAEA;QACA;UACA;QACA;QAEA;QACA;QACAC;QACAA;QAEA;UACA;QACA;QAEA;MACA;QACApB;QACA;MACA;IACA;IAEA;IACAsB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAIAC,aACA,sBACA,+BACA;cAAA;gBAEA;gBACA;kBACA;kBACA;gBACA;;gBAEA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACAvB;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIA;gBACAC;kBACAuB;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACA;;gBAEA;gBAAA;gBAAA,OACAH,aACA,8BACA,wCACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAKA;IAEA;IACAI;MAAA;MACA;QACA;QACA;UACAC;UACAC;UACAC;UACA;UACAC;QACA;;QAEA;QACA;UACA;UACA;YACA;cACAC;cACA;YACA;cACAA;cACA;YACA;cACAA;cACA;YACA;cACAA;cACA;UAAA;UAEA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAC;kBACAxE;oBACAW;kBACA;gBACA;cAAA;gBALA8D;gBAAA,MAOAA;kBAAA;kBAAA;gBAAA;gBACAC,oBAEA;gBACA3D;gBACA;kBACA;kBACA4D;kBACA5D;gBACA;kBACA;kBACAA;gBACA;;gBAEA;gBACA;kBACAA;gBACA;;gBAEA;gBACAC;gBACAC;gBAEA;kBACAD;kBACAC;gBACA;kBACA2D;kBACA5D;kBACAC;gBACA;;gBAEA;gBACA4D;gBACA;kBAAA,iBACAH;kBACAG;gBACA;gBAEA;kBACAlE;kBACAZ;kBACAa;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBAAA;kBACAC;kBAAA;kBACA2D;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAKA;IAEA;IACAC;MACA;MACA;QACA;MACA;MAEA;MACA;MAEA;QACA;QACA;QAEA;UACA;QACA;UACA;QACA;MACA;QACA;QACA;;QAEA;QACA;QACA;QAEA;UACA;UACAC;QACA;UACA;UACAA;QACA;QAEAA;;QAEA;UACA;QACA;UACA;QACA;MACA;;MAEA;MACA;IACA;IAIA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAZ,yLACA,gHACA;gBAEAJ;gBAAA;gBAAA,OAEA;kBACAM;kBACAxE;oBACAmF;oBACAC;oBACAC;oBACAC;kBACA;gBACA;cAAA;gBARAb;gBAUA;kBACAxC,kCAEA;kBACA;oBACAsD;sBAAA;oBAAA;oBACA;sBACA;oBACA;sBACA;oBACA;kBACA;kBAEA;oBACA;oBACA;oBACA;oBAEA;sBACA5E;sBACA6E;sBACAC;sBACAvE;sBAAA;sBACA6C;sBACA2B;sBACAC;sBAAA;sBACAC;oBACA;kBACA;oBAAA;kBAAA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAhC,aACA;kBACAW;kBACAxE;oBACAmF;oBACAG;kBACA;gBACA;gBACA;gBACA;kBACAd;kBACAxE;oBACAmF;oBACAG;kBACA;gBACA,GACA;cAAA;gBAAA;gBAAA;gBAhBAQ;gBAAAC;gBAkBA;kBACA9D;kBACA+D,gPAEA;kBACAC;kBACAD;oBACA;oBACA;sBACAC;oBACA;oBACAA;kBACA;;kBAEA;kBACA;oBACA;oBACA;;oBAEA;oBACA;sBACA;sBACAC;wBAAA,OACAC,mCACA,+CACA;sBAAA;sBAAA,CACA;oBACA;;oBAEA;oBACA;oBACA;sBACA;wBAAA;sBAAA;sBACA;wBACAC;sBACA;oBACA;;oBAEA;oBACA,+DACAC;sBACAC;oBAAA,EACA;oBAEA;kBACA;oBAAA;kBAAA;gBACA;kBACA;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACAC;MACA;MACA;MAEA;MACA;MACA;MACA;;MAEA;MACA;QACA;QACA;UACA9B;UACAV;UACA0B;QACA;UACAhB;UACAV;UACA0B;QACA;UACAhB;UACAV;UACA0B;QACA;UACAhB;UACAV;UACA0B;QACA;;QAEA;QACA;UACA;UACAe;UAEA;YACA/B;YACAV;YACA0B;UACA;YACA;YACA;YACA;;YAEA;YACA;YAEA;cACA;cACA;cACA;cACA;cAEA;gBACAR;cACA;gBACAA;cACA;cACAA;cAEAwB;YACA;cACA;cACA;cACA;cACAA;YACA;YAEA;cACA;cACAhC;cACAV;cACA0B;YACA;cACA;cACAhB;cACAV;cACA0B;YACA;UACA;YACA;YACAhB;YACAV;YACA0B;UACA;QACA;MACA;QACA;QACAhB;QACAV;QACA0B;MACA;MAEA;QACA9E;QACA+F;QACAC;QACAlB;QACAhB;QACAV;QACA6C;QACAC;QACA3F;QACA;QACA4F;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAvC;kBACAxE;oBACAmF;oBACAG;kBACA;gBACA;cAAA;gBANAb;gBAAA,MAQAA;kBAAA;kBAAA;gBAAA;gBACAxC,kCAEA;gBAAA;gBAAA,OACA4B,YACA5B;kBAAA;oBAAA;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA,MACAoE;8BAAA;8BAAA;4BAAA;4BAAA;4BAAA;4BAAA,OAGA;8BACA7B;8BACAxE;gCACAgH;gCACA9F;gCACAoE;8BACA;4BACA;0BAAA;4BAPAS;4BASA,0DACAA,6DACAA;8BACAkB;8BACAZ;4BACA;4BAAA;4BAAA;0BAAA;4BAAA;4BAAA;0BAAA;4BAAA,kCAKAA;0BAAA;0BAAA;4BAAA;wBAAA;sBAAA;oBAAA;kBAAA,CACA;kBAAA;oBAAA;kBAAA;gBAAA,KACA;cAAA;gBA1BAa;gBA4BA;kBACA;kBACA;kBAEA;kBACA;kBACA;;kBAEA;kBACA;oBACA;oBACA;sBACAzC;sBACAV;sBACA0B;oBACA;sBACAhB;sBACAV;sBACA0B;oBACA;sBACAhB;sBACAV;sBACA0B;oBACA;sBACAhB;sBACAV;sBACA0B;oBACA;kBACA;oBACA;oBACAhB;oBACAV;oBACA0B;kBACA;kBAEA;oBACA9E;oBACA+F;oBACAC;oBACAlB;oBACAhB;oBACAV;oBACA6C;oBAAA;oBACAC;oBACA3F;kBACA;gBACA;kBAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAIA;kBACA;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;kBACA;kBACArD;kBACAsD;kBACAF;kBACAC;gBACA;gBAAA;gBAAA,OAEA;kBACA/C;kBACAxE;oBACAmF;oBACAC;oBACAC;oBACAC;kBACA;gBACA;cAAA;gBARAb;gBAUA;kBACA4B;kBACAV;kBACA8B;kBAEA;oBACA9G;oBACA8G;oBACAC;kBACA;gBACA;kBACA;gBACA;;gBAEA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACAxF;QACAyF;MACA;IACA;IAEA;IACAC;MACA;QACA;UAAA;QAAA;QACA1F;UACA2F;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACA7F;QACAyF;MACA;IACA;IAEA;IACAK;MACA;MACA;QACA9F;UACAuB;UACAC;UACAuE;QACA;QACA;MACA;;MAEA;MACA;QACA/F;UACAuB;UACAC;UACAuE;QACA;QACA;MACA;MAEA/F;QACAyF;MACA;IACA;IAEA;IACAO;MAAA;MACA;MACAhG;QACAiG;QACAC;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA,oCACA,kCACA,kCACA,kCACA;MAAA,KACA,mCACA;IACA;IAEA;IACAC;MACA;QACA;UAAAC;UAAAC;UAAAC;QAAA;QACA;UAAAF;UAAAC;UAAAC;QAAA;QACA;UAAAF;UAAAC;UAAAC;QAAA;QACA;UAAAF;UAAAC;UAAAC;QAAA;QACA;UAAAF;UAAAC;UAAAC;QAAA;MACA;MAEA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;kBACA;oBACA;sBACAC;sBACAC;oBACA;kBACA;;kBAEA;kBACAC;oBACAF;oBACAC;kBACA;gBACA;cAAA;gBAEA;gBACAE;kBAAA;gBAAA;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA/I,2DAEA;gBACAgJ;kBAAA,OACA,oFACA,oDACAhD;gBAAA;gBAAA,CACA,EAEA;gBAAA,KACAgD;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA5C,wDAEA;gBACA6C;kBACA3I;kBACA+F;kBACAC;kBACAlB;kBACAhB;kBACAV;kBACA6C;kBACAC;kBACA3F;kBACA4F,6BACA;oBAAA+B;oBAAAC;oBAAAC;kBAAA,IACA;oBAAAF;oBAAAC;oBAAAC;kBAAA;gBACA,GAEA;gBACA;gBACA;gBACA;kBAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAzG;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiH;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;kBACA;oBACA;sBACAN;sBACAC;oBACA;kBACA;;kBAEA;kBACAC;oBACAF;oBACAC;kBACA;gBACA;cAAA;gBAEA;gBACAhF;gBACAsF,mEAEA;gBACAC;kBACA;kBACA;gBACA,IAEA;gBACAC;kBAAA;gBAAA,IAEA;gBACAC;gBAEAF;kBACA;;kBAEA;kBACA;oBACA;oBACA;oBACA;oBACA;kBACA;;kBAEA;kBACA;oBACAE;sBACAhJ;sBACA+F;sBACAC;sBACAlB;sBACAhB;sBACAV;sBACA6C;sBACAC;sBACA3F;sBACA4F;wBAAA+B;wBAAAC;wBAAAC;sBAAA;oBACA;kBACA;gBACA;;gBAEA;gBACA;kBACA,sHACA7G;oBAAA;kBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;gBACAI;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAsH;MACA;MAEA;QACA;QACA;QACA;QACA;QAEA;UACA3E;QACA;UACAA;QACA;QACAA;QAEA;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACA4E;MACA;MAEA;QACA;QACA;QACA;QACA;QAEA;UACA5E;QACA;UACAA;QACA;QACAA;QAEA;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACA6E;MAAA;MACA;MACA;QACA;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACl/CA;AAAA;AAAA;AAAA;AAAwqC,CAAgB,8oCAAG,EAAC,C;;;;;;;;;;;ACA5rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/area-detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/area-detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./area-detail.vue?vue&type=template&id=4c4c6a76&scoped=true&\"\nvar renderjs\nimport script from \"./area-detail.vue?vue&type=script&lang=js&\"\nexport * from \"./area-detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./area-detail.vue?vue&type=style&index=0&id=4c4c6a76&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4c4c6a76\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/area-detail.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./area-detail.vue?vue&type=template&id=4c4c6a76&scoped=true&\"", "var components\ntry {\n  components = {\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    !_vm.loading && !_vm.loadError\n      ? _vm.getAreaTypeText(_vm.areaInfo.type)\n      : null\n  var m1 =\n    !_vm.loading && !_vm.loadError\n      ? _vm.getStatusText(_vm.areaInfo.status)\n      : null\n  var g0 =\n    !_vm.loading && !_vm.loadError && _vm.currentWeekRecord\n      ? _vm.currentWeekRecord.photos.length\n      : null\n  var m2 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    !_vm.currentWeekRecord &&\n    !_vm.isHistoricalView\n      ? _vm.getWeekDeadlineText()\n      : null\n  var g1 = !_vm.loading && !_vm.loadError ? _vm.cleaningHistory.length : null\n  var l1 =\n    !_vm.loading && !_vm.loadError && !(g1 === 0)\n      ? _vm.__map(_vm.groupedHistory, function (monthGroup, __i0__) {\n          var $orig = _vm.__get_orig(monthGroup)\n          var g2 = monthGroup.records.length\n          var g3 = _vm.expandedMonths.includes(monthGroup.month)\n          var g4 = _vm.expandedMonths.includes(monthGroup.month)\n          var l0 = g4\n            ? _vm.__map(monthGroup.records, function (record, recordIndex) {\n                var $orig = _vm.__get_orig(record)\n                var m3 = _vm.getIconColor(record.status)\n                return {\n                  $orig: $orig,\n                  m3: m3,\n                }\n              })\n            : null\n          return {\n            $orig: $orig,\n            g2: g2,\n            g3: g3,\n            g4: g4,\n            l0: l0,\n          }\n        })\n      : null\n  var g5 = !_vm.loading && !_vm.loadError ? _vm.inspectionHistory.length : null\n  var l2 =\n    !_vm.loading && !_vm.loadError && !(g5 === 0)\n      ? _vm.__map(_vm.inspectionHistory, function (record, recordIndex) {\n          var $orig = _vm.__get_orig(record)\n          var m4 = _vm.getIconColor(record.result)\n          return {\n            $orig: $orig,\n            m4: m4,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        m2: m2,\n        g1: g1,\n        l1: l1,\n        g5: g5,\n        l2: l2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./area-detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./area-detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page-container\">\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-container\">\n      <view class=\"loading-content\">\n        <view class=\"loading-spinner\"></view>\n        <text class=\"loading-text\">加载区域详情中...</text>\n      </view>\n    </view>\n\n    <!-- 错误状态 -->\n    <view v-else-if=\"loadError\" class=\"error-container\">\n      <p-empty-state \n        type=\"error\"\n        title=\"加载失败\"\n        description=\"网络异常，请检查网络连接\"\n        :show-button=\"true\"\n        button-text=\"重新加载\"\n        @button-click=\"loadPageData\"\n      ></p-empty-state>\n    </view>\n\n    <!-- 正常内容 -->\n    <template v-else>\n    <!-- 责任区概览 -->\n    <view class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"header-content\">\n          <view class=\"card-title\">{{ areaInfo.name }}</view>\n          <view class=\"card-subtitle\">{{ getAreaTypeText(areaInfo.type) }}</view>\n        </view>\n        <view class=\"status-badge\" :class=\"['status-' + areaInfo.status]\">\n          {{ getStatusText(areaInfo.status) }}\n        </view>\n      </view>\n    </view>\n\n    <!-- 基本信息 -->\n    <view class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"card-title\">基本信息</view>\n      </view>\n      <view class=\"card-body\">\n        <view class=\"info-list\">\n          <view class=\"info-item\">\n            <view class=\"info-label\">\n              <uni-icons type=\"location\" size=\"16\" color=\"#8E8E93\"></uni-icons>\n              <text>责任区位置</text>\n            </view>\n            <view class=\"info-value\">{{ areaInfo.location }}</view>\n          </view>\n          <view class=\"info-item\">\n            <view class=\"info-label\">\n              <uni-icons type=\"person\" size=\"16\" color=\"#8E8E93\"></uni-icons>\n              <text>负责人</text>\n            </view>\n            <view class=\"info-value\">{{ areaInfo.assignedEmployee }}</view>\n          </view>\n          <view class=\"info-item\">\n            <view class=\"info-label\">\n              <uni-icons type=\"reload\" size=\"16\" color=\"#8E8E93\"></uni-icons>\n              <text>清理频率</text>\n            </view>\n            <view class=\"info-value\">{{ areaInfo.cleaningFrequency }}</view>\n          </view>\n          <view class=\"info-item\">\n            <view class=\"info-label\">\n              <uni-icons type=\"calendar\" size=\"16\" color=\"#8E8E93\"></uni-icons>\n              <text>下次清理</text>\n            </view>\n            <view class=\"info-value\">{{ areaInfo.nextCleaningTime }}</view>\n          </view>\n        </view>\n        \n        <view v-if=\"areaInfo.description\" class=\"description-section\">\n          <view class=\"description-title\">区域说明</view>\n          <view class=\"description-text\">{{ areaInfo.description }}</view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 周状态（动态显示本周或历史周） -->\n    <view class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"card-title\">{{ isHistoricalView ? '历史状态' : '本周状态' }}</view>\n        <view class=\"card-subtitle\">{{ currentWeekText }}</view>\n      </view>\n      <view class=\"card-body\">\n        <view v-if=\"currentWeekRecord\" class=\"week-status completed\">\n          <view class=\"status-icon\">\n            <uni-icons type=\"checkmarkempty\" size=\"24\" color=\"#34C759\"></uni-icons>\n          </view>\n          <view class=\"status-content\">\n            <view class=\"status-title\">{{ isHistoricalView ? '已完成清理' : '本周已完成清理' }}</view>\n            <view class=\"status-desc\">{{ currentWeekRecord.submitTime }}</view>\n            <view class=\"status-photos\" @click=\"viewWeekPhotos\">\n              <text>查看清理照片 ({{ currentWeekRecord.photos.length }}张)</text>\n              <uni-icons type=\"right\" size=\"14\" color=\"#007AFF\"></uni-icons>\n            </view>\n          </view>\n        </view>\n        \n        <view v-else class=\"week-status pending\">\n          <view class=\"status-icon\">\n            <uni-icons type=\"clock\" size=\"24\" color=\"#FF9500\"></uni-icons>\n          </view>\n          <view class=\"status-content\">\n            <view class=\"status-title\">{{ isHistoricalView ? '当时未清理' : '本周待清理' }}</view>\n            <view class=\"status-desc\">{{ isHistoricalView ? '该时间段内未进行清理' : getWeekDeadlineText() }}</view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n\n    <!-- 操作按钮（历史模式下不显示） -->\n    <view v-if=\"!isHistoricalView && !currentWeekRecord && areaInfo.canClean\" class=\"action-section\">\n      <view class=\"action-btn primary\" @click=\"goToCleaning\">\n        <uni-icons type=\"camera\" size=\"20\" color=\"white\"></uni-icons>\n        <text>立即清理</text>\n      </view>\n    </view>\n\n    <!-- 清理历史 -->\n    <view class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"header-content\">\n          <view class=\"card-title\">清理历史</view>\n          <view class=\"card-subtitle\">最近的清理记录</view>\n        </view>\n        <view class=\"time-filter\" @click=\"showTimeFilter\">\n          <text>{{ selectedTimeFilter }}</text>\n          <uni-icons type=\"down\" size=\"12\" color=\"#007AFF\"></uni-icons>\n        </view>\n      </view>\n      \n      <p-empty-state\n        v-if=\"cleaningHistory.length === 0\"\n        type=\"data\"\n        text=\"暂无清理记录\"\n        description=\"还没有清理记录，开始第一次清理吧\"\n      ></p-empty-state>\n      \n      <view v-else class=\"history-list\">\n        <view v-for=\"monthGroup in groupedHistory\" :key=\"monthGroup.month\" class=\"month-group\">\n          <!-- 月份标题 -->\n          <view class=\"month-header\" @click=\"toggleMonth(monthGroup.month)\">\n            <view class=\"month-info\">\n              <text class=\"month-title\">📅 {{ monthGroup.monthText }}</text>\n              <text class=\"month-count\">({{ monthGroup.records.length }}条记录)</text>\n            </view>\n            <uni-icons \n              :type=\"expandedMonths.includes(monthGroup.month) ? 'down' : 'right'\" \n              size=\"16\" \n              color=\"#8E8E93\"\n            ></uni-icons>\n          </view>\n          \n          <!-- 月份记录列表 -->\n          <view v-if=\"expandedMonths.includes(monthGroup.month)\" class=\"month-records\">\n            <view \n              v-for=\"(record, recordIndex) in monthGroup.records\" \n              :key=\"recordIndex\" \n              class=\"history-item\"\n              @click=\"viewRecordDetail(record)\"\n            >\n              <view class=\"history-icon\" :class=\"['icon-bg-' + record.status]\">\n                <uni-icons :type=\"record.icon\" size=\"16\" :color=\"getIconColor(record.status)\"></uni-icons>\n              </view>\n              <view class=\"history-content\">\n                <view class=\"history-title\">{{ record.weekText }}</view>\n                <view class=\"history-subtitle\">{{ record.subtitle }}</view>\n              </view>\n              <view class=\"history-right\">\n                <view class=\"photo-count\">{{ record.photoCount }}张照片</view>\n                <uni-icons type=\"right\" size=\"14\" color=\"#C7C7CC\"></uni-icons>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 检查记录 -->\n    <view class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"card-title\">检查记录</view>\n        <view class=\"card-subtitle\">管理层抽查记录</view>\n      </view>\n      \n      <p-empty-state\n        v-if=\"inspectionHistory.length === 0\"\n        type=\"data\"\n        text=\"暂无检查记录\"\n        description=\"还没有管理层检查记录\"\n      ></p-empty-state>\n      \n      <view v-else class=\"history-list\">\n        <view \n          v-for=\"(record, recordIndex) in inspectionHistory\" \n          :key=\"recordIndex\" \n          class=\"history-item\"\n          @click=\"viewInspectionDetail(record)\"\n        >\n          <view class=\"history-icon\" :class=\"['icon-bg-' + record.result]\">\n            <uni-icons :type=\"record.icon\" size=\"18\" :color=\"getIconColor(record.result)\"></uni-icons>\n          </view>\n          <view class=\"history-content\">\n            <view class=\"history-title\">{{ record.inspectorName }} · {{ record.time }}</view>\n            <view class=\"history-subtitle\">{{ record.subtitle }}</view>\n          </view>\n                      <view class=\"history-right\">\n              <!-- 优先显示评分，无评分时显示状态指示器 -->\n              <view class=\"rating-stars\" v-if=\"record.rating\">\n                <text>{{ record.rating }}/5</text>\n                <uni-icons type=\"star-filled\" size=\"12\" color=\"#FFD700\"></uni-icons>\n              </view>\n              <view class=\"status-indicator\" v-else-if=\"record.statusIndicator\" :style=\"{ color: record.statusIndicator.color }\">\n                <text class=\"indicator-text\">{{ record.statusIndicator.text }}</text>\n                <text class=\"indicator-desc\">{{ record.statusIndicator.desc }}</text>\n              </view>\n              <uni-icons type=\"right\" size=\"14\" color=\"#C7C7CC\"></uni-icons>\n            </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 底部安全间距 -->\n    <view class=\"bottom-safe-area\"></view>\n    </template>\n  </view>\n</template>\n\n<script>\nimport { callCloudFunction } from '@/utils/auth.js';\n\nexport default {\n  name: 'AreaDetail',\n  data() {\n    return {\n      areaId: '',\n      areaType: '',\n      mode: 'view', // view(查看模式) 或 edit(编辑模式)\n      recordId: '', // 特定记录ID(从我的清理记录跳转时使用)\n      cleaningDate: '', // 清理日期(从历史记录跳转时使用)\n      week: '', // 周次信息(从历史记录跳转时使用)\n      isHistoricalView: false, // 是否是历史查看模式\n      loading: true,\n      loadError: false,\n      areaInfo: {\n        id: '',\n        name: '',\n        type: '',\n        location: '',\n        description: '',\n        assignedEmployee: '',\n        cleaningFrequency: '',\n        nextCleaningTime: '',\n        status: '',\n        canClean: true\n      },\n      currentWeekText: '',\n      currentWeekRecord: null,\n      selectedTimeFilter: '近3个月',\n      cleaningHistory: [],\n      inspectionHistory: [],\n      expandedMonths: [], // 默认展开月份\n      \n      // 性能优化缓存\n      timeCache: null, // 时间计算缓存\n      historyDataLoaded: false // 历史数据是否已加载\n    }\n  },\n  computed: {\n    // 按月份分组的历史记录\n    groupedHistory() {\n      const groups = {};\n      \n      this.cleaningHistory.forEach(record => {\n        const date = new Date(record.submitDate.replace(/-/g, '/'));\n        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;\n        \n        if (!groups[monthKey]) {\n          groups[monthKey] = {\n            month: monthKey,\n            monthText: `${date.getFullYear()}年${date.getMonth() + 1}月`,\n            records: []\n          };\n        }\n        \n        groups[monthKey].records.push(record);\n      });\n      \n      // 按月份倒序排列，每月内的记录也按日期倒序\n      return Object.values(groups)\n        .sort((a, b) => b.month.localeCompare(a.month))\n        .map(group => ({\n          ...group,\n          records: group.records.sort((a, b) => b.submitDate.localeCompare(a.submitDate))\n        }));\n    },\n\n\n  },\n  onLoad(options) {\n    // 兼容新的 record 参数\n    if (options.record) {\n      try {\n        const record = JSON.parse(decodeURIComponent(options.record));\n        this.areaId = record.areaId;\n        this.areaType = record.areaType;\n        this.mode = 'view';\n        this.recordId = record.id; // 假设 record 中有 id\n        this.cleaningDate = record.cleaningDate;\n        this.week = record.week;\n        this.isHistoricalView = true; // 从记录列表跳转都视为历史查看\n      } catch (e) {\n        console.error(\"Parsing record from URL failed\", e);\n        this.loadError = true;\n        return;\n      }\n    } else if (options.areaId) {\n      // 兼容旧的参数模式\n      this.areaId = options.areaId;\n      this.areaType = options.type || 'fixed';\n      this.mode = options.mode || 'view';\n      this.recordId = options.recordId || '';\n      this.cleaningDate = options.cleaningDate || '';\n      this.week = options.week || '';\n      this.isHistoricalView = !!(this.cleaningDate || this.week || this.recordId);\n    }\n\n    this.initCurrentWeekText();\n    this.initExpandedMonths();\n    this.loadPageData();\n    \n    // 监听清理记录更新事件\n    uni.$on('cleaningRecordUpdated', this.handleRecordUpdated);\n    // 监听整改记录更新事件\n    uni.$on('rectificationRecordUpdated', this.handleRecordUpdated);\n    // 监听检查记录更新事件\n    uni.$on('inspectionRecordUpdated', this.handleRecordUpdated);\n  },\n  onUnload() {\n    // 移除事件监听\n    uni.$off('cleaningRecordUpdated', this.handleRecordUpdated);\n    uni.$off('rectificationRecordUpdated', this.handleRecordUpdated);\n    uni.$off('inspectionRecordUpdated', this.handleRecordUpdated);\n  },\n  methods: {\n    // 初始化当前周文本\n    initCurrentWeekText() {\n      try {\n        if (this.isHistoricalView && this.cleaningDate) {\n          // 历史模式：根据传入的清理日期计算对应周的信息\n          const cleanDate = new Date(this.cleaningDate);\n          const beijingCleanDate = this.toBeijingTime(cleanDate);\n          \n          if (!beijingCleanDate) {\n            this.currentWeekText = '日期信息异常';\n            return;\n          }\n          \n          const weekStart = this.getWeekStart(beijingCleanDate);\n          const weekEnd = this.getWeekEnd(beijingCleanDate);\n          \n          if (!weekStart || !weekEnd) {\n            this.currentWeekText = '无法计算周次信息';\n            return;\n          }\n          \n          const startText = `${weekStart.getMonth() + 1}月${weekStart.getDate()}日`;\n          const endText = `${weekEnd.getMonth() + 1}月${weekEnd.getDate()}日`;\n          const weekNum = this.getWeekNumber(weekStart);\n          \n          this.currentWeekText = `第${weekNum}周 (${startText} - ${endText})`;\n        } else {\n          // 本周模式：显示当前周的信息\n          const now = new Date();\n          const beijingNow = this.toBeijingTime(now);\n          \n          if (!beijingNow) {\n            this.currentWeekText = '当前时间异常';\n            return;\n          }\n          \n          const weekStart = this.getWeekStart(beijingNow);\n          const weekEnd = this.getWeekEnd(beijingNow);\n          \n          if (!weekStart || !weekEnd) {\n            this.currentWeekText = '无法计算本周信息';\n            return;\n          }\n          \n          const startText = `${weekStart.getMonth() + 1}月${weekStart.getDate()}日`;\n          const endText = `${weekEnd.getMonth() + 1}月${weekEnd.getDate()}日`;\n          const weekNum = this.getWeekNumber(weekStart);\n          \n          this.currentWeekText = `第${weekNum}周 (${startText} - ${endText})`;\n        }\n      } catch (error) {\n        console.error('初始化当前周文本出错:', error);\n        this.currentWeekText = '时间计算异常';\n      }\n    },\n\n    // 初始化展开月份（展开当前月和上个月）\n    initExpandedMonths() {\n      const now = new Date();\n      const currentMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;\n      const lastMonth = `${now.getFullYear()}-${String(now.getMonth()).padStart(2, '0')}`;\n      this.expandedMonths = [currentMonth, lastMonth];\n    },\n\n    // 获取周数\n    getWeekNumber(date) {\n      const onejan = new Date(date.getFullYear(), 0, 1);\n      const millisecsInDay = 86400000;\n      return Math.ceil((((date.getTime() - onejan.getTime()) / millisecsInDay) + onejan.getDay() + 1) / 7);\n    },\n\n    // 北京时间转换函数\n    toBeijingTime(dateInput) {\n      if (!dateInput) {\n        return null;\n      }\n      \n      try {\n        const inputDate = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;\n        if (isNaN(inputDate.getTime())) {\n          return null;\n        }\n        \n        // 小程序环境中使用更兼容的方式\n        // 手动计算北京时间（UTC+8）\n        const utcTime = inputDate.getTime();\n        const beijingTime = new Date(utcTime + 8 * 60 * 60 * 1000);\n        \n        // 检查计算结果是否有效\n        if (isNaN(beijingTime.getTime())) {\n          return null;\n        }\n        \n        return {\n          getFullYear: () => beijingTime.getUTCFullYear(),\n          getMonth: () => beijingTime.getUTCMonth(),\n          getDate: () => beijingTime.getUTCDate(),\n          getDay: () => beijingTime.getUTCDay(),\n          getHours: () => beijingTime.getUTCHours(),\n          getMinutes: () => beijingTime.getUTCMinutes(),\n          getSeconds: () => beijingTime.getUTCSeconds(),\n          getTime: () => beijingTime.getTime(),\n          toISOString: () => beijingTime.toISOString(),\n          // 兼容Date对象的比较\n          valueOf: () => beijingTime.getTime()\n        };\n      } catch (error) {\n        console.error('北京时间转换失败:', error, dateInput);\n        return null;\n      }\n    },\n\n    // 获取一周的开始日期（周一）- 使用北京时间\n    getWeekStart(date) {\n      if (!date) {\n        return null;\n      }\n      \n      try {\n        let dateObj;\n        if (date && typeof date.getFullYear === 'function') {\n          // 如果是自定义的北京时间对象或原生Date对象\n          dateObj = date;\n        } else {\n          // 如果是时间戳或字符串，先转换为北京时间\n          dateObj = this.toBeijingTime(date);\n          if (!dateObj) return null;\n        }\n        \n        const year = dateObj.getFullYear();\n        const month = dateObj.getMonth();\n        const day = dateObj.getDate();\n        const dayOfWeek = dateObj.getDay(); // 0=周日, 1=周一, ..., 6=周六\n        \n        // 检查获取的值是否有效\n        if (isNaN(year) || isNaN(month) || isNaN(day) || isNaN(dayOfWeek)) {\n          return null;\n        }\n        \n        const currentDate = new Date(year, month, day);\n        const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;\n        currentDate.setDate(day + mondayOffset);\n        currentDate.setHours(0, 0, 0, 0);\n        \n        if (isNaN(currentDate.getTime())) {\n          return null;\n        }\n        \n        return currentDate;\n      } catch (error) {\n        console.error('计算周开始时间出错:', error, date);\n        return null;\n      }\n    },\n\n    // 获取一周的结束日期（周日）- 使用北京时间\n    getWeekEnd(date) {\n      if (!date) {\n        return null;\n      }\n      \n      try {\n        let dateObj;\n        if (date && typeof date.getFullYear === 'function') {\n          // 如果是自定义的北京时间对象或原生Date对象\n          dateObj = date;\n        } else {\n          // 如果是时间戳或字符串，先转换为北京时间\n          dateObj = this.toBeijingTime(date);\n          if (!dateObj) return null;\n        }\n        \n        const year = dateObj.getFullYear();\n        const month = dateObj.getMonth();\n        const day = dateObj.getDate();\n        const dayOfWeek = dateObj.getDay(); // 0=周日, 1=周一, ..., 6=周六\n        \n        // 检查获取的值是否有效\n        if (isNaN(year) || isNaN(month) || isNaN(day) || isNaN(dayOfWeek)) {\n          return null;\n        }\n        \n        const currentDate = new Date(year, month, day);\n        const sundayOffset = dayOfWeek === 0 ? 0 : 7 - dayOfWeek;\n        currentDate.setDate(day + sundayOffset);\n        currentDate.setHours(23, 59, 59, 999);\n        \n        if (isNaN(currentDate.getTime())) {\n          return null;\n        }\n        \n        return currentDate;\n      } catch (error) {\n        console.error('计算周结束时间出错:', error, date);\n        return null;\n      }\n    },\n\n    // 加载页面数据 - 性能优化版本\n    async loadPageData() {\n      this.loading = true;\n      this.loadError = false;\n      \n      try {\n        // 第一阶段：快速加载关键信息，让用户看到基本内容\n        await Promise.all([\n          this.loadAreaInfo(),\n          this.checkCurrentWeekRecord()\n        ]);\n        \n        // 更新状态（基于本周记录）\n        if (this.areaInfo.id) {\n          this.areaInfo.status = this.calculateAreaStatus(this.areaInfo);\n          this.areaInfo.canClean = !this.currentWeekRecord;\n        }\n        \n        // 第二阶段：等待所有历史数据加载完成\n        try {\n          await this.loadHistoryDataAsync();\n          \n          // 第三阶段：检查检查记录状态（先历史后本周，避免冲突）\n          await this.checkMissedInspections();\n          await this.checkCurrentWeekInspectionStatus();\n        } catch (historyError) {\n          // 历史数据加载失败不影响基本功能\n          console.warn('历史数据加载失败:', historyError);\n        }\n        \n      } catch (error) {\n        this.loadError = true;\n        uni.showToast({\n          title: '加载失败',\n          icon: 'none'\n        });\n      } finally {\n        this.loading = false; // 所有数据加载完成，显示完整页面\n      }\n    },\n\n    // 加载历史数据\n    async loadHistoryDataAsync() {\n      try {\n        // 缓存时间计算结果\n        this.initTimeCache();\n        \n        // 并行加载历史数据\n        await Promise.all([\n          this.loadCleaningHistory(),\n          this.loadInspectionHistoryOptimized()\n        ]);\n      } catch (error) {\n        // 历史数据加载失败不影响主要功能\n\n      }\n    },\n\n    // 初始化时间缓存\n    initTimeCache() {\n      if (!this.timeCache) {\n        const now = new Date();\n        this.timeCache = {\n          now,\n          currentYear: now.getFullYear(),\n          currentMonth: now.getMonth() + 1,\n          // 预计算常用的时间范围\n          cutoffDates: {}\n        };\n        \n        // 预计算各种时间筛选的截止日期\n        ['近1个月', '近3个月', '近6个月', '近1年'].forEach(filter => {\n          let cutoffDate;\n          switch(filter) {\n            case '近1个月':\n              cutoffDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());\n              break;\n            case '近3个月':\n              cutoffDate = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());\n              break;\n            case '近6个月':\n              cutoffDate = new Date(now.getFullYear(), now.getMonth() - 6, now.getDate());\n              break;\n            case '近1年':\n              cutoffDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());\n              break;\n          }\n          this.timeCache.cutoffDates[filter] = cutoffDate;\n        });\n      }\n    },\n\n    // 加载责任区信息\n    async loadAreaInfo() {\n      try {\n        const result = await callCloudFunction('hygiene-area-management', {\n          action: 'getAreaDetail',\n          data: {\n            id: this.areaId  // 使用 id 而不是 area_id\n          }\n        });\n\n        if (result && result.success && result.data) {\n          const area = result.data;\n          \n          // 获取负责人信息\n          let assignedEmployee = '未分配';\n          if (area.assigned_user_details && area.assigned_user_details.length > 0) {\n            // 使用用户的昵称或用户名\n            const user = area.assigned_user_details[0];\n            assignedEmployee = user.nickname || user.username || user._id || '未知用户';\n          } else if (area.assigned_users && area.assigned_users.length > 0) {\n            // 如果有分配用户但没有用户详情，尝试显示用户ID\n            assignedEmployee = `用户${area.assigned_users[0]}`;\n          }\n          \n          // 如果在assigned_users中没有找到负责人，从最近的清理记录中获取\n          if (assignedEmployee === '未分配') {\n            assignedEmployee = '待确定';\n          }\n\n          // 计算清理频率文本\n          let cleaningFrequency = '未设置';\n          let nextCleaningTime = '待定';\n          \n          if (area.type === 'fixed') {\n            cleaningFrequency = area.cleaning_frequency === 'weekly' ? '每周一次' : '按需清理';\n            nextCleaningTime = '本周内完成';\n          } else if (area.type === 'public' && area.scheduled_day) {\n            const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\n            cleaningFrequency = `每${weekDays[area.scheduled_day]}清理`;\n            nextCleaningTime = `下个${weekDays[area.scheduled_day]}`;\n          }\n\n          // 格式化位置信息\n          let locationText = '';\n          if (area.location) {\n            const { building, floor, area: areaName } = area.location;\n            locationText = [building, floor, areaName].filter(Boolean).join(' ');\n          }\n\n          this.areaInfo = {\n            id: area._id,\n            name: area.name,\n            type: area.type,\n            location: locationText || '未设置',\n            description: area.description || '',\n            assignedEmployee: assignedEmployee,\n            cleaningFrequency: cleaningFrequency,\n            nextCleaningTime: nextCleaningTime,\n            status: 'pending', // 初始状态，将在加载完本周记录后重新计算\n            canClean: true, // 初始值，将在加载完本周记录后重新计算\n            scheduledDay: area.scheduled_day,\n            cleaning_requirements: area.cleaning_requirements || []\n          };\n        } else {\n          throw new Error('获取责任区信息失败');\n        }\n      } catch (error) {\n        throw error;\n      }\n    },\n\n    // 计算责任区状态\n    calculateAreaStatus(area) {\n      // 如果本周有清理记录，则为已完成\n      if (this.currentWeekRecord) {\n        return 'completed';\n      }\n      \n      const now = new Date();\n      const beijingNow = this.toBeijingTime(now);\n      \n      if (area.type === 'fixed') {\n        // 固定责任区：本周内完成即可，周日23:59:59为截止时间\n        const weekEnd = this.getWeekEnd(beijingNow);\n        \n        if (now > weekEnd) {\n          return 'overdue'; // 已逾期\n        } else {\n          return 'pending'; // 待清理\n        }\n      } else if (area.type === 'public' && area.scheduled_day !== undefined) {\n        // 公共责任区：需要在指定的周几完成\n        const scheduledDay = area.scheduled_day; // 0=周日, 1=周一, ..., 6=周六\n        \n        // 计算本周指定日期\n        const weekStart = this.getWeekStart(beijingNow);\n        const scheduledDate = new Date(weekStart);\n        \n        if (scheduledDay === 0) {\n          // 周日：本周的最后一天\n          scheduledDate.setDate(weekStart.getDate() + 6);\n        } else {\n          // 周一到周六：相应调整天数\n          scheduledDate.setDate(weekStart.getDate() + scheduledDay - 1);\n        }\n        \n        scheduledDate.setHours(23, 59, 59, 999); // 设置为当天结束时间\n        \n        if (now > scheduledDate) {\n          return 'overdue'; // 已逾期\n        } else {\n          return 'pending'; // 待清理\n        }\n      }\n      \n      // 默认返回待清理状态\n      return 'pending';\n    },\n\n\n\n    // 加载清理历史 - 优化版本\n    async loadCleaningHistory() {\n      try {\n        // 使用缓存的时间计算结果\n        const cutoffDate = this.timeCache?.cutoffDates[this.selectedTimeFilter] || \n                          this.timeCache?.cutoffDates['近3个月'] ||\n                          new Date(Date.now() - 90 * 24 * 60 * 60 * 1000); // 默认3个月\n        \n        const now = this.timeCache?.now || new Date();\n\n        const result = await callCloudFunction('hygiene-cleaning', {\n          action: 'getCleaningRecords',\n          data: {\n            area_id: this.areaId,\n            start_date: cutoffDate.toISOString(),\n            end_date: now.toISOString(),\n            pageSize: 100\n          }\n        });\n\n        if (result && result.success && result.data) {\n          const records = result.data.list || [];\n          \n          // 如果负责人未确定，从最近的清理记录中获取\n          if (this.areaInfo.assignedEmployee === '待确定' && records.length > 0) {\n            const latestRecord = records.sort((a, b) => new Date(b.cleaning_date) - new Date(a.cleaning_date))[0];\n            if (latestRecord.user_name) {\n              this.areaInfo.assignedEmployee = latestRecord.user_name;\n            } else if (latestRecord.cleaner_name) {\n              this.areaInfo.assignedEmployee = latestRecord.cleaner_name;\n            }\n          }\n          \n          this.cleaningHistory = records.map(record => {\n            const cleanDate = new Date(record.cleaning_date);\n            const weekText = `${cleanDate.getMonth() + 1}月${cleanDate.getDate()}日`;\n            const timeText = `${cleanDate.getHours().toString().padStart(2, '0')}:${cleanDate.getMinutes().toString().padStart(2, '0')}`;\n            \n            return {\n              id: record._id,\n              weekText: weekText,\n              subtitle: `清理任务 · ${timeText}完成`,\n              status: 'completed', // 根据业务逻辑，所有记录都是已完成\n              icon: 'checkmarkempty',\n              photoCount: record.photos ? record.photos.length : 0,\n              submitDate: record.cleaning_date.split('T')[0], // 格式化为YYYY-MM-DD\n              remark: record.remark || ''\n            };\n          }).sort((a, b) => b.submitDate.localeCompare(a.submitDate));\n        } else {\n          this.cleaningHistory = [];\n        }\n      } catch (error) {\n        this.cleaningHistory = [];\n      }\n    },\n\n    // 优化的检查记录加载 - 减少API调用次数\n    async loadInspectionHistoryOptimized() {\n      try {\n        // 一次性获取检查记录和相关的整改记录\n        const [inspectionResult, rectificationResult] = await Promise.all([\n          callCloudFunction('hygiene-inspection', {\n            action: 'getInspectionRecords',\n            data: {\n              area_id: this.areaId,\n              pageSize: 20\n            }\n          }),\n          // 批量获取该区域的所有整改记录，避免逐个查询\n          callCloudFunction('hygiene-rectification', {\n            action: 'getRectifications',\n            data: {\n              area_id: this.areaId,\n              pageSize: 50 // 获取更多整改记录用于匹配\n            }\n          })\n        ]);\n\n        if (inspectionResult && inspectionResult.success && inspectionResult.data) {\n          const records = inspectionResult.data.list || [];\n          const rectificationRecords = rectificationResult?.data?.list || [];\n          \n          // 建立整改记录的快速查找映射\n          const rectificationMap = new Map();\n          rectificationRecords.forEach(rect => {\n            const key = rect.inspection_record_id || `${rect.area_id}_${rect.created_at}`;\n            if (!rectificationMap.has(key)) {\n              rectificationMap.set(key, []);\n            }\n            rectificationMap.get(key).push(rect);\n          });\n          \n          // 处理检查记录，匹配整改评分\n          this.inspectionHistory = records.map(record => {\n            // 优先通过inspection_record_id匹配，再通过区域和时间匹配\n            let relatedRectifications = rectificationMap.get(record._id) || [];\n            \n            // 如果没有直接匹配，通过时间范围匹配（同一区域，检查时间之后的整改）\n            if (relatedRectifications.length === 0) {\n              const inspectionTime = new Date(record.inspection_date);\n              relatedRectifications = rectificationRecords.filter(rect => \n                rect.area_id === record.area_id && \n                new Date(rect.created_at) >= inspectionTime &&\n                new Date(rect.created_at) <= new Date(inspectionTime.getTime() + 7 * 24 * 60 * 60 * 1000) // 一周内\n              );\n            }\n            \n            // 获取最终评分（优先整改评分）\n            let finalRating = record.overall_rating || record.score || 0;\n            if (record.has_issues && relatedRectifications.length > 0) {\n              const verifiedRectification = relatedRectifications.find(rect => rect.status === 'verified');\n              if (verifiedRectification) {\n                finalRating = verifiedRectification.final_rating || verifiedRectification.review_rating || finalRating;\n              }\n            }\n            \n            // 将相关整改记录附加到检查记录上\n            const recordWithRectifications = {\n              ...record,\n              related_rectifications: relatedRectifications\n            };\n            \n            return this.formatInspectionRecord(recordWithRectifications, finalRating);\n          }).sort((a, b) => new Date(b.inspection_date) - new Date(a.inspection_date));\n        } else {\n          this.inspectionHistory = [];\n        }\n        \n        this.historyDataLoaded = true;\n      } catch (error) {\n        this.inspectionHistory = [];\n\n      }\n    },\n\n    // 格式化检查记录（提取公共逻辑）\n    formatInspectionRecord(record, finalRating) {\n      const inspectDate = new Date(record.inspection_date);\n      const timeText = `${inspectDate.getMonth() + 1}月${inspectDate.getDate()}日 ${inspectDate.getHours().toString().padStart(2, '0')}:${inspectDate.getMinutes().toString().padStart(2, '0')}`;\n      \n      let subtitle = '';\n      let result = 'passed';\n      let icon = 'checkmarkempty';\n      let currentRectStatus = null;  // 用于跟踪当前整改状态\n      \n      // 根据检查状态和是否有问题来确定显示内容\n      if (record.has_issues) {\n        // 有问题的情况 - 需要详细区分整改流程状态\n        if (record.status === 'verified') {\n          result = 'completed';\n          icon = 'checkmarkempty';\n          subtitle = `发现问题已整改完成 · 整改质量${this.getRatingDescription(finalRating).toLowerCase()}`;\n        } else if (record.status === 'rectification_completed') {\n          result = 'pending';\n          icon = 'reload';\n          subtitle = `整改已提交 · 等待检查员复查确认`;\n        } else if (record.status === 'pending_rectification') {\n          result = 'issues';\n          icon = 'info';\n          subtitle = `发现问题 · ${record.issues && record.issues.length > 0 ? record.issues[0].description : '需要整改'}`;\n        } else {\n          result = 'issues';\n          icon = 'info';\n          subtitle = `发现问题 · 需要整改`;\n        }\n        \n        // 检查相关整改记录的详细状态\n        if (record.related_rectifications && record.related_rectifications.length > 0) {\n          const latestRectification = record.related_rectifications[0];\n          currentRectStatus = latestRectification.status;\n          \n          if (currentRectStatus === 'verified') {\n            result = 'completed';\n            icon = 'checkmarkempty';\n            subtitle = `发现问题已整改完成 · 整改质量${this.getRatingDescription(finalRating).toLowerCase()}`;\n          } else if (currentRectStatus === 'pending_review') {\n            // 关键判断：员工已提交整改，检查员应该复查但未及时复查\n            const submitTime = new Date(latestRectification.submitted_at || latestRectification.updated_at);\n            const now = new Date();\n            \n            // 根据责任区类型判断是否漏检查\n            let isOverdue = false;\n            \n            if (this.areaInfo.type === 'public' && this.areaInfo.scheduledDay !== undefined) {\n              // 公共责任区：严格按指定日期，当天23:59:59为截止\n              const submitDate = this.toBeijingTime(submitTime);\n              const weekStart = this.getWeekStart(submitDate);\n              const scheduledDate = new Date(weekStart);\n              \n              if (this.areaInfo.scheduledDay === 0) {\n                scheduledDate.setDate(weekStart.getDate() + 6); // 周日\n              } else {\n                scheduledDate.setDate(weekStart.getDate() + this.areaInfo.scheduledDay - 1);\n              }\n              scheduledDate.setHours(23, 59, 59, 999);\n              \n              isOverdue = now > scheduledDate;\n            } else {\n              // 固定责任区：本周截止，周日23:59:59为截止\n              const submitDate = this.toBeijingTime(submitTime);\n              const weekEnd = this.getWeekEnd(submitDate);\n              isOverdue = now > weekEnd;\n            }\n            \n            if (isOverdue) {\n              // 超过截止时间，这是检查员漏检查\n              result = 'overdue';\n              icon = 'info';\n              subtitle = `检查员漏检查 · 整改已提交但未及时复查`;\n            } else {\n              // 截止时间内，正常显示待复查\n              result = 'issues';\n              icon = 'eye';\n              subtitle = `发现问题 · 整改已提交，待检查员复查`;\n            }\n          } else {\n            // 其他状态统一显示为\"发现问题，整改处理中\"\n            result = 'issues';\n            icon = 'info';\n            subtitle = `发现问题 · ${record.issues && record.issues.length > 0 ? record.issues[0].description : '整改处理中'}`;\n          }\n        }\n      } else {\n        // 没有问题，检查通过\n        result = 'passed';\n        icon = 'checkmarkempty';\n        subtitle = `检查通过 · 清理效果${this.getRatingDescription(finalRating).toLowerCase()} · 无问题发现`;\n      }\n\n      return {\n        id: record._id,\n        inspectorName: `检查员：${record.inspector_name || '未知'}`,\n        time: timeText,\n        subtitle: subtitle,\n        result: result,\n        icon: icon,\n        rating: finalRating,\n        inspection_date: record.inspection_date,\n        status: record.status || 'unknown',\n        // 添加状态指示器信息\n        statusIndicator: this.getStatusIndicator(result, currentRectStatus || record.status)\n      };\n    },\n\n    // 加载检查记录 - 保留原方法作为备用\n    async loadInspectionHistory() {\n      try {\n        const result = await callCloudFunction('hygiene-inspection', {\n          action: 'getInspectionRecords',\n          data: {\n            area_id: this.areaId,\n            pageSize: 20\n          }\n        });\n\n        if (result && result.success && result.data) {\n          const records = result.data.list || [];\n          \n          // 为每个有问题的检查记录获取相关的整改评分\n          const recordsWithRectificationRating = await Promise.all(\n            records.map(async (record) => {\n              if (record.has_issues && record.status === 'verified') {\n                // 获取相关的整改记录评分\n                try {\n                  const rectificationResult = await callCloudFunction('hygiene-rectification', {\n                    action: 'getRectifications',\n                    data: {\n                      inspection_record_id: record._id,\n                      status: 'verified',\n                      pageSize: 1\n                    }\n                  });\n                  \n                  if (rectificationResult && rectificationResult.success && \n                      rectificationResult.data && rectificationResult.data.list && \n                      rectificationResult.data.list.length > 0) {\n                    const rectificationRecord = rectificationResult.data.list[0];\n                    record.rectification_rating = rectificationRecord.final_rating || rectificationRecord.review_rating || 0;\n                  }\n                } catch (error) {\n                  // 静默处理整改评分获取失败\n                }\n              }\n              return record;\n            })\n          );\n          \n          this.inspectionHistory = recordsWithRectificationRating.map(record => {\n            const inspectDate = new Date(record.inspection_date);\n            const timeText = `${inspectDate.getMonth() + 1}月${inspectDate.getDate()}日 ${inspectDate.getHours().toString().padStart(2, '0')}:${inspectDate.getMinutes().toString().padStart(2, '0')}`;\n            \n            let subtitle = '';\n            let result = 'passed';\n            let icon = 'checkmarkempty';\n            \n            // 根据检查状态和是否有问题来确定显示内容\n            if (record.has_issues) {\n              // 有问题的情况\n              if (record.status === 'verified') {\n                result = 'completed';\n                icon = 'checkmarkempty';\n                subtitle = `发现问题已整改完成 · 整改质量${this.getRatingDescription(record.rectification_rating || record.overall_rating || record.score || 0).toLowerCase()}`;\n              } else if (record.status === 'rectification_completed') {\n                result = 'pending';\n                icon = 'reload';\n                subtitle = `整改已提交 · 等待确认`;\n              } else if (record.status === 'pending_rectification') {\n                result = 'issues';\n                icon = 'info';\n                subtitle = `发现问题 · ${record.issues && record.issues.length > 0 ? record.issues[0].description : '需要整改'}`;\n              } else {\n                result = 'issues';\n                icon = 'info';\n                subtitle = `发现问题 · 需要整改`;\n              }\n            } else {\n              // 没有问题，检查通过\n              result = 'passed';\n              icon = 'checkmarkempty';\n              subtitle = `检查通过 · 清理效果${this.getRatingDescription(record.rectification_rating || record.overall_rating || record.score || 0).toLowerCase()} · 无问题发现`;\n            }\n\n            return {\n              id: record._id,\n              inspectorName: `检查员：${record.inspector_name || '未知'}`,\n              time: timeText,\n              subtitle: subtitle,\n              result: result,\n              icon: icon,\n              rating: record.rectification_rating || record.overall_rating || record.score || 0, // 优先显示整改评分\n              inspection_date: record.inspection_date,\n              status: record.status || 'unknown'\n            };\n          }).sort((a, b) => new Date(b.inspection_date) - new Date(a.inspection_date));\n        } else {\n          this.inspectionHistory = [];\n        }\n      } catch (error) {\n        this.inspectionHistory = [];\n      }\n    },\n\n    // 检查当前周或历史周记录\n    async checkCurrentWeekRecord() {\n      try {\n        let weekStart, weekEnd;\n        \n        if (this.isHistoricalView && this.cleaningDate) {\n          // 历史模式：查询指定清理日期所在周的记录\n          const cleanDate = new Date(this.cleaningDate);\n          const beijingCleanDate = this.toBeijingTime(cleanDate);\n          weekStart = this.getWeekStart(beijingCleanDate);\n          weekEnd = this.getWeekEnd(beijingCleanDate);\n        } else {\n          // 本周模式：查询当前周的记录\n          const now = new Date();\n          const beijingNow = this.toBeijingTime(now);\n          weekStart = this.getWeekStart(beijingNow);\n          weekEnd = this.getWeekEnd(beijingNow);\n        }\n\n        const result = await callCloudFunction('hygiene-cleaning', {\n          action: 'getCleaningRecords',\n          data: {\n            area_id: this.areaId,\n            start_date: weekStart.toISOString(),\n            end_date: weekEnd.toISOString(),\n            pageSize: 1\n          }\n        });\n\n        if (result && result.success && result.data && result.data.list && result.data.list.length > 0) {\n          const record = result.data.list[0];\n          const submitDate = new Date(record.cleaning_date);\n          const submitTime = `${submitDate.getMonth() + 1}月${submitDate.getDate()}日 ${submitDate.getHours().toString().padStart(2, '0')}:${submitDate.getMinutes().toString().padStart(2, '0')} 提交`;\n          \n          this.currentWeekRecord = {\n            id: record._id,\n            submitTime: submitTime,\n            photos: record.photos || []\n          };\n        } else {\n          this.currentWeekRecord = null;\n        }\n        \n        // 更新清理权限\n        if (this.areaInfo) {\n          this.areaInfo.canClean = !this.currentWeekRecord;\n        }\n      } catch (error) {\n        this.currentWeekRecord = null;\n      }\n    },\n\n    // 获取区域类型文本\n    getAreaTypeText(type) {\n      const typeMap = {\n        'fixed': '固定责任区',\n        'public': '公共责任区'\n      };\n      return typeMap[type] || '责任区';\n    },\n\n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        'completed': '已完成',\n        'pending': '待清理',\n        'overdue': '已逾期'\n      };\n      return statusMap[status] || '未知';\n    },\n\n    // 获取截止时间文本（动态计算）\n    getWeekDeadlineText() {\n      const now = new Date();\n      const beijingNow = this.toBeijingTime(now);\n      const weekEnd = this.getWeekEnd(beijingNow);\n      const timeDiff = weekEnd.getTime() - now.getTime();\n      const daysLeft = Math.ceil(timeDiff / (24 * 60 * 60 * 1000));\n      \n      if (daysLeft <= 0) {\n        return '已超过截止时间';\n      } else if (daysLeft === 1) {\n        return '距离本周截止还有1天';\n      } else {\n        return `距离本周截止还有${daysLeft}天`;\n      }\n    },\n\n    // 获取图标颜色\n    getIconColor(status) {\n      const colorMap = {\n        'completed': '#34C759',\n        'pending': '#FF9500',\n        'overdue': '#FF3B30',\n        'passed': '#34C759',\n        'issues': '#FF3B30'\n      };\n      return colorMap[status] || '#8E8E93';\n    },\n\n    // 跳转到清理页面\n    goToCleaning() {\n      uni.navigateTo({\n        url: `/pages/6s_pkg/cleaning-upload?areaId=${this.areaId}&type=${this.areaType}`\n      });\n    },\n\n    // 查看本周照片\n    viewWeekPhotos() {\n      if (this.currentWeekRecord && this.currentWeekRecord.photos.length > 0) {\n        const urls = this.currentWeekRecord.photos.map(photo => photo.url);\n        uni.previewImage({\n          urls: urls,\n          current: urls[0]\n        });\n      }\n    },\n\n    // 查看记录详情\n    viewRecordDetail(record) {\n      uni.navigateTo({\n        url: `/pages/6s_pkg/record-detail?id=${record.id}&type=cleaning`\n      });\n    },\n\n    // 查看检查详情\n    viewInspectionDetail(record) {\n      // 如果是漏检查记录，显示提示而不是跳转\n      if (record.result === 'overdue' && record.id.startsWith('missed_')) {\n        uni.showToast({\n          title: '检查员未检查此记录',\n          icon: 'none',\n          duration: 2000\n        });\n        return;\n      }\n      \n      // 如果是待检查记录，显示提示而不是跳转\n      if (record.result === 'pending' && record.id === 'current_week_status') {\n        uni.showToast({\n          title: '检查员还未检查此记录',\n          icon: 'none',\n          duration: 2000\n        });\n        return;\n      }\n      \n      uni.navigateTo({\n        url: `/pages/6s_pkg/record-detail?id=${record.id}&type=inspection`\n      });\n    },\n\n    // 显示时间筛选\n    showTimeFilter() {\n      const timeOptions = ['近1个月', '近3个月', '近6个月', '近1年'];\n      uni.showActionSheet({\n        itemList: timeOptions,\n        success: (res) => {\n          this.selectedTimeFilter = timeOptions[res.tapIndex];\n          this.loadCleaningHistory(); // 重新加载数据\n        }\n      });\n    },\n\n    // 切换月份展开/折叠\n    toggleMonth(monthKey) {\n      const index = this.expandedMonths.indexOf(monthKey);\n      if (index > -1) {\n        // 已展开，则折叠\n        this.expandedMonths.splice(index, 1);\n      } else {\n        // 未展开，则展开\n        this.expandedMonths.push(monthKey);\n      }\n    },\n\n    // 获取评分描述\n    getRatingDescription(rating) {\n      if (rating === 0) return '请评分';\n      else if (rating <= 1) return '较差';\n      else if (rating <= 2) return '一般';\n      else if (rating <= 3) return '良好';\n      else if (rating < 5) return '优秀';  // 4-4.5分都是优秀\n      else if (rating === 5) return '完美';\n      else return '良好'; // 默认值\n    },\n\n    // 获取状态指示器信息（用于评分区域右侧显示）\n    getStatusIndicator(result, status) {\n      const indicators = {\n        'passed': { text: '✓', color: '#34C759', desc: '检查通过' },\n        'completed': { text: '✓', color: '#34C759', desc: '整改完成' },\n        'pending': { text: '○', color: '#FF9500', desc: '待检查' },\n        'issues': { text: '!', color: '#FF3B30', desc: '发现问题' },\n        'overdue': { text: '⚠', color: '#FF3B30', desc: '漏检查' }\n      };\n      \n      return indicators[result] || indicators['issues']; // 默认返回问题状态\n    },\n\n    // 检查本周清理记录的检查状态（待检查/漏检查）\n    async checkCurrentWeekInspectionStatus() {\n      try {\n        // 只处理本周有清理记录但无对应检查记录的情况\n        if (!this.currentWeekRecord) return;\n        \n        // 等待历史数据加载完成\n        await new Promise(resolve => {\n          const checkInterval = setInterval(() => {\n            if (this.historyDataLoaded) {\n              clearInterval(checkInterval);\n              resolve();\n            }\n          }, 100);\n          \n          // 超时保护\n          setTimeout(() => {\n            clearInterval(checkInterval);\n            resolve();\n          }, 5000);\n        });\n        \n        // 获取本周清理记录的实际时间\n        const currentCleaningRecord = this.cleaningHistory.find(record => record.id === this.currentWeekRecord.id);\n        if (!currentCleaningRecord) return;\n        \n        const cleaningDate = new Date(currentCleaningRecord.submitDate);\n        \n        // 检查是否已有对应的检查记录或者已有本周状态记录\n        const hasInspectionRecord = this.inspectionHistory.some(record => \n          (!record.id.startsWith('missed_') && !record.id.startsWith('current_week_status') && \n           new Date(record.inspection_date) >= cleaningDate) ||\n          record.id === 'current_week_status' // 避免重复添加本周状态\n        );\n        \n        // 如果已有检查记录，不需要显示待检查/漏检查\n        if (hasInspectionRecord) return;\n        \n        // 判断是否超过截止时间\n        const isOverdue = this.isCurrentWeekOverdue(cleaningDate);\n        \n        // 创建待检查或漏检查记录\n        const inspectionStatus = {\n          id: 'current_week_status',\n          inspectorName: isOverdue ? '检查员漏检查' : '待检查',\n          time: `${cleaningDate.getMonth() + 1}月${cleaningDate.getDate()}日清理`,\n          subtitle: isOverdue ? '清理记录已提交，但检查员未及时检查' : '清理记录已提交，等待检查员检查',\n          result: isOverdue ? 'overdue' : 'pending',\n          icon: 'info',\n          rating: 0,\n          inspection_date: currentCleaningRecord.submitDate,\n          status: isOverdue ? 'missed' : 'pending',\n          statusIndicator: isOverdue ? \n            { text: '⚠', color: '#FF3B30', desc: '漏检查' } : \n            { text: '○', color: '#FF9500', desc: '待检查' }\n        };\n        \n        // 添加到检查历史的最前面，但要确保不与已有的漏检查记录冲突\n        this.inspectionHistory.unshift(inspectionStatus);\n        // 重新排序\n        this.inspectionHistory.sort((a, b) => new Date(b.inspection_date) - new Date(a.inspection_date));\n        \n      } catch (error) {\n        console.warn('检查本周清理状态失败:', error);\n      }\n    },\n\n    // 检查是否有遗漏的检查记录\n    async checkMissedInspections() {\n      try {\n        // 等待历史数据加载完成\n        await new Promise(resolve => {\n          const checkInterval = setInterval(() => {\n            if (this.historyDataLoaded) {\n              clearInterval(checkInterval);\n              resolve();\n            }\n          }, 100);\n          \n          // 超时保护\n          setTimeout(() => {\n            clearInterval(checkInterval);\n            resolve();\n          }, 5000);\n        });\n        \n        // 检查最近4周的清理记录是否有对应的检查记录\n        const now = new Date();\n        const fourWeeksAgo = new Date(now.getTime() - 28 * 24 * 60 * 60 * 1000);\n        \n        // 获取清理记录\n        const cleaningRecords = this.cleaningHistory.filter(record => {\n          const recordDate = new Date(record.submitDate);\n          return recordDate > fourWeeksAgo;\n        });\n        \n        // 获取检查记录（排除已有的漏检查记录）\n        const inspectionRecords = this.inspectionHistory.filter(record => !record.id.startsWith('missed_'));\n        \n        // 找出没有对应检查记录的清理记录\n        const missedInspections = [];\n        \n        cleaningRecords.forEach(cleanRecord => {\n          const cleanDate = new Date(cleanRecord.submitDate);\n          \n          // 查找该清理记录后7天内的检查记录\n          const hasInspection = inspectionRecords.some(inspectRecord => {\n            const inspectDate = new Date(inspectRecord.inspection_date);\n            const timeDiff = inspectDate.getTime() - cleanDate.getTime();\n            // 检查是否在清理后7天内有检查\n            return timeDiff >= 0 && timeDiff <= 7 * 24 * 60 * 60 * 1000;\n          });\n          \n          // 只有完全没有检查记录且超过截止时间的才算漏检查\n          if (!hasInspection && this.isHistoricalRecordOverdue(cleanDate)) {\n            missedInspections.push({\n              id: 'missed_' + cleanRecord.id,\n              inspectorName: '检查员漏检查',\n              time: `${cleanDate.getMonth() + 1}月${cleanDate.getDate()}日清理`,\n              subtitle: '清理记录已提交，但检查员未及时检查',\n              result: 'overdue',\n              icon: 'info',\n              rating: 0,\n              inspection_date: cleanRecord.submitDate,\n              status: 'missed',\n              statusIndicator: { text: '⚠', color: '#FF3B30', desc: '漏检查' }\n            });\n          }\n        });\n        \n        // 将遗漏的检查记录插入检查历史\n        if (missedInspections.length > 0) {\n          this.inspectionHistory = [...missedInspections, ...this.inspectionHistory]\n            .sort((a, b) => new Date(b.inspection_date) - new Date(a.inspection_date));\n        }\n        \n      } catch (error) {\n        // 遗漏检查检测失败不影响主要功能\n        console.warn('检查遗漏检查记录失败:', error);\n      }\n    },\n\n    // 判断历史记录是否超过截止时间（用于历史漏检查检测，严格按截止时间）\n    isHistoricalRecordOverdue(cleanDate) {\n      const now = new Date();\n      \n      if (this.areaInfo.type === 'public' && this.areaInfo.scheduledDay !== undefined) {\n        // 公共责任区：严格按指定日期，当天23:59:59为截止\n        const cleanDateBeijing = this.toBeijingTime(cleanDate);\n        const weekStart = this.getWeekStart(cleanDateBeijing);\n        const scheduledDate = new Date(weekStart);\n        \n        if (this.areaInfo.scheduledDay === 0) {\n          scheduledDate.setDate(weekStart.getDate() + 6); // 周日\n        } else {\n          scheduledDate.setDate(weekStart.getDate() + this.areaInfo.scheduledDay - 1);\n        }\n        scheduledDate.setHours(23, 59, 59, 999);\n        \n        return now > scheduledDate;\n      } else {\n        // 固定责任区：本周截止，周日23:59:59为截止\n        const cleanDateBeijing = this.toBeijingTime(cleanDate);\n        const weekEnd = this.getWeekEnd(cleanDateBeijing);\n        return now > weekEnd;\n      }\n    },\n\n    // 判断本周清理记录是否超时（根据责任区类型严格判断）\n    isCurrentWeekOverdue(cleanDate) {\n      const now = new Date();\n      \n      if (this.areaInfo.type === 'public' && this.areaInfo.scheduledDay !== undefined) {\n        // 公共责任区：严格按指定日期，当天23:59:59为截止\n        const cleanDateBeijing = this.toBeijingTime(cleanDate);\n        const weekStart = this.getWeekStart(cleanDateBeijing);\n        const scheduledDate = new Date(weekStart);\n        \n        if (this.areaInfo.scheduledDay === 0) {\n          scheduledDate.setDate(weekStart.getDate() + 6); // 周日\n        } else {\n          scheduledDate.setDate(weekStart.getDate() + this.areaInfo.scheduledDay - 1);\n        }\n        scheduledDate.setHours(23, 59, 59, 999);\n        \n        return now > scheduledDate;\n      } else {\n        // 固定责任区：本周截止，周日23:59:59为截止\n        const cleanDateBeijing = this.toBeijingTime(cleanDate);\n        const weekEnd = this.getWeekEnd(cleanDateBeijing);\n        return now > weekEnd;\n      }\n    },\n\n    // 处理记录更新事件\n    handleRecordUpdated(data) {\n      // 如果更新的是当前责任区的记录，重新加载相关数据\n      if (data.areaId === this.areaId) {\n        // 重新检查本周记录状态\n        this.checkCurrentWeekRecord();\n        \n        // 重新加载历史记录\n        if (data.mode === 'cleaning' || data.recordId) {\n          this.loadCleaningHistory();\n        }\n        \n        // 如果是检查记录更新，也需要重新加载检查历史\n        if (data.isPassed !== undefined) {\n          this.loadInspectionHistory();\n        }\n        \n        // 重新检查检查记录状态（先历史后本周，避免冲突）\n        this.checkMissedInspections().then(() => {\n          this.checkCurrentWeekInspectionStatus();\n        });\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\n\twidth: 100%;\n\toverflow-x: hidden; /* 防止水平滚动 */\n\tbox-sizing: border-box;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;\n}\n\n/* 页面头部 */\n.page-header {\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n  padding: 32rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-end;\n}\n\n.header-content {\n  flex: 1;\n}\n\n.header-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: white;\n  margin-bottom: 8rpx;\n}\n\n.header-subtitle {\n  font-size: 28rpx;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.status-badge {\n  padding: 12rpx 20rpx;\n  border-radius: 20rpx;\n  font-size: 24rpx;\n  font-weight: 500;\n  \n  &.status-completed {\n    background: rgba(52, 199, 89, 0.2);\n    color: #34C759;\n    border: 1rpx solid rgba(52, 199, 89, 0.3);\n  }\n  \n  &.status-pending {\n    background: rgba(255, 149, 0, 0.2);\n    color: #FF9500;\n    border: 1rpx solid rgba(255, 149, 0, 0.3);\n  }\n  \n  &.status-overdue {\n    background: rgba(255, 59, 48, 0.2);\n    color: #FF3B30;\n    border: 1rpx solid rgba(255, 59, 48, 0.3);\n  }\n}\n\n/* 卡片样式 */\n.card {\n  background: white;\n  border-radius: 16rpx;\n  margin: 24rpx 32rpx 0 32rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.card-header {\n  padding: 32rpx 32rpx 16rpx 32rpx;\n  border-bottom: 1rpx solid #F2F2F7;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.header-content {\n  flex: 1;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 4rpx;\n}\n\n.card-subtitle {\n  font-size: 26rpx;\n  color: #8E8E93;\n}\n\n.card-body {\n  padding: 32rpx;\n}\n\n/* 信息列表 */\n.info-list {\n  display: flex;\n  flex-direction: column;\n  gap: 24rpx;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.info-label {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  font-size: 28rpx;\n  color: #1C1C1E;\n  flex: 1;\n}\n\n.info-value {\n  font-size: 28rpx;\n  color: #8E8E93;\n  text-align: right;\n}\n\n/* 描述部分 */\n.description-section {\n  margin-top: 32rpx;\n  padding-top: 32rpx;\n  border-top: 1rpx solid #F2F2F7;\n}\n\n.description-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 12rpx;\n}\n\n.description-text {\n  font-size: 26rpx;\n  color: #8E8E93;\n  line-height: 1.5;\n}\n\n/* 本周状态 */\n.week-status {\n  display: flex;\n  align-items: center;\n  padding: 24rpx;\n  border-radius: 12rpx;\n  \n  &.completed {\n    background: #E8F5E8;\n    border: 1rpx solid #34C759;\n  }\n  \n  &.pending {\n    background: #FFF4E6;\n    border: 1rpx solid #FF9500;\n  }\n}\n\n.status-icon {\n  width: 64rpx;\n  height: 64rpx;\n  border-radius: 50%;\n  background: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 24rpx;\n}\n\n.status-content {\n  flex: 1;\n}\n\n.status-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 4rpx;\n}\n\n.status-desc {\n  font-size: 24rpx;\n  color: #8E8E93;\n  margin-bottom: 8rpx;\n}\n\n.status-photos {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  font-size: 26rpx;\n  color: #007AFF;\n}\n\n/* 统计网格 */\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 32rpx;\n  padding: 32rpx;\n  text-align: center;\n}\n\n.stats-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.stats-number {\n  font-size: 32rpx;\n  font-weight: bold;\n  margin-bottom: 8rpx;\n  color: #1C1C1E;\n  \n  &.success {\n    color: #34C759;\n  }\n  \n  &.warning {\n    color: #FF9500;\n  }\n  \n  &.info {\n    color: #007AFF;\n  }\n  \n  &.danger {\n    color: #FF3B30;\n  }\n  \n  &.primary {\n    color: #007AFF;\n  }\n}\n\n.stats-label {\n  font-size: 22rpx;\n  color: #8E8E93;\n}\n\n/* 操作按钮 */\n.action-section {\n  padding: 32rpx;\n}\n\n.action-btn {\n  width: 100%;\n  height: 96rpx;\n  border-radius: 16rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12rpx;\n  font-size: 32rpx;\n  font-weight: 600;\n  \n  &.primary {\n    background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n    color: white;\n  }\n  \n  &:active {\n    transform: scale(0.98);\n  }\n}\n\n/* 时间筛选 */\n.time-filter {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  padding: 8rpx 16rpx;\n  background: rgba(0, 122, 255, 0.1);\n  border-radius: 16rpx;\n  font-size: 24rpx;\n  color: #007AFF;\n}\n\n/* 历史记录列表 */\n.history-list {\n  padding-bottom: 16rpx;\n}\n\n/* 月份分组 */\n.month-group {\n  margin-bottom: 16rpx;\n}\n\n.month-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 24rpx 32rpx;\n  background: #F8F9FA;\n  border-bottom: 1rpx solid #E5E5EA;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n  \n  &:active {\n    background: #E5E5EA;\n  }\n}\n\n.month-info {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n}\n\n.month-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n}\n\n.month-count {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.month-records {\n  background: white;\n}\n\n.history-item {\n  display: flex;\n  align-items: center;\n  padding: 24rpx 32rpx;\n  border-bottom: 1rpx solid #F2F2F7;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n}\n\n.history-icon {\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 24rpx;\n  \n  &.icon-bg-completed,\n  &.icon-bg-passed {\n    background: rgba(52, 199, 89, 0.1);\n  }\n  \n  &.icon-bg-pending {\n    background: rgba(255, 149, 0, 0.1);\n  }\n  \n  &.icon-bg-issues {\n    background: rgba(255, 59, 48, 0.1);\n  }\n  \n  &.icon-bg-overdue {\n    background: rgba(255, 59, 48, 0.1);\n  }\n}\n\n.history-content {\n  flex: 1;\n}\n\n.history-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 4rpx;\n}\n\n.history-subtitle {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.history-right {\n  display: flex;\n  align-items: center;\n  gap: 16rpx;\n}\n\n.photo-count {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.status-indicator {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 2rpx;\n  margin-right: 12rpx;\n}\n\n.indicator-text {\n  font-size: 20rpx;\n  font-weight: 600;\n}\n\n.indicator-desc {\n  font-size: 20rpx;\n  opacity: 0.8;\n}\n\n.rating-stars {\n  display: flex;\n  align-items: center;\n  gap: 4rpx;\n  font-size: 24rpx;\n  color: #FFD700;\n}\n\n/* 底部安全间距 */\n.bottom-safe-area {\n  height: 40rpx;\n}\n\n/* 加载状态 */\n.loading-container {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 32rpx;\n}\n\n.loading-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 16rpx;\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n  text-align: center;\n}\n\n.loading-spinner {\n  width: 40rpx;\n  height: 40rpx;\n  border: 4rpx solid #E5E7EB;\n  border-top: 4rpx solid #007AFF;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* 错误状态 */\n.error-container {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 32rpx;\n}\n\n/* 响应式调整 */\n@media (max-width: 414px) {\n  .page-header {\n    padding: 24rpx 16rpx;\n  }\n  \n  .card {\n    margin: 24rpx 16rpx 0 16rpx;\n  }\n  \n  .action-section {\n    padding: 24rpx 16rpx;\n  }\n  \n  .stats-grid {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 24rpx;\n  }\n}\n\n\n</style>", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./area-detail.vue?vue&type=style&index=0&id=4c4c6a76&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./area-detail.vue?vue&type=style&index=0&id=4c4c6a76&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755777837985\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}