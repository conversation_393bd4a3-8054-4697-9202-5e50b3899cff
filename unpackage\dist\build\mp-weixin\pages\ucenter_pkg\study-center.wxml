<view class="study-center-container data-v-126ab1e9"><view class="search-bar data-v-126ab1e9"><uni-search-bar vue-id="1633f87c-1" placeholder="搜索文档标题、描述或文件名" focus="{{false}}" show-action="{{false}}" bg-color="#f5f5f5" value="{{searchKeyword}}" data-event-opts="{{[['^confirm',[['onSearch']]],['^input',[['__set_model',['','searchKeyword','$event',[]]],['onSearchInput']]]]}}" bind:confirm="__e" bind:input="__e" class="data-v-126ab1e9" bind:__l="__l"></uni-search-bar></view><view class="category-filter data-v-126ab1e9"><scroll-view class="category-scroll data-v-126ab1e9" scroll-x="true"><view class="category-list data-v-126ab1e9"><view data-event-opts="{{[['tap',[['selectCategory',['']]]]]}}" class="{{['category-item','data-v-126ab1e9',(selectedCategoryId==='')?'active':'']}}" bindtap="__e">全部</view><block wx:for="{{categories}}" wx:for-item="category" wx:for-index="__i0__" wx:key="_id"><view data-event-opts="{{[['tap',[['selectCategory',['$0'],[[['categories','_id',category._id,'_id']]]]]]]}}" class="{{['category-item','data-v-126ab1e9',(selectedCategoryId===category._id)?'active':'']}}" bindtap="__e">{{''+category.name+''}}<text class="category-count data-v-126ab1e9">{{"("+category.document_count+")"}}</text></view></block><block wx:if="{{canManageCategory}}"><view data-event-opts="{{[['tap',[['showCategoryManager',['$event']]]]]}}" class="category-manage-btn data-v-126ab1e9" bindtap="__e"><uni-icons vue-id="1633f87c-2" type="gear" size="18" color="#666" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view></block></view></scroll-view></view><view class="document-list data-v-126ab1e9"><block wx:if="{{$root.g0}}"><view class="loading-container data-v-126ab1e9"><uni-load-more vue-id="1633f87c-3" status="loading" class="data-v-126ab1e9" bind:__l="__l"></uni-load-more></view></block><block wx:else><block wx:if="{{$root.g1===0}}"><view class="empty-container data-v-126ab1e9"><p-empty-state vue-id="1633f87c-4" image="/static/empty/empty_data.png" title="暂无文档" description="还没有上传任何学习资料" class="data-v-126ab1e9" bind:__l="__l"></p-empty-state></view></block><block wx:else><view class="data-v-126ab1e9"><block wx:for="{{$root.l0}}" wx:for-item="document" wx:for-index="__i1__" wx:key="_id"><view data-event-opts="{{[['tap',[['viewDocument',['$0'],[[['documents','_id',document.$orig._id]]]]]]]}}" class="document-item data-v-126ab1e9" bindtap="__e"><view class="document-icon data-v-126ab1e9"><uni-icons vue-id="{{'1633f87c-5-'+__i1__}}" type="{{document.$orig.type==='collection'?'paperclip':document.m0}}" size="24" color="{{document.$orig.type==='collection'?'#ff9800':document.m1}}" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view><view class="document-content data-v-126ab1e9"><view class="document-title data-v-126ab1e9">{{''+document.$orig.title+''}}</view><view class="document-info data-v-126ab1e9"><text class="category-tag data-v-126ab1e9">{{document.$orig.category_name}}</text><block wx:if="{{document.$orig.type==='collection'}}"><text class="file-count data-v-126ab1e9">{{(document.$orig.files?document.g2:0)+"个文件"}}</text></block><block wx:else><text class="file-size data-v-126ab1e9">{{document.m2}}</text></block></view><view class="document-meta data-v-126ab1e9"><view class="meta-item data-v-126ab1e9"><uni-icons vue-id="{{'1633f87c-6-'+__i1__}}" type="person" size="14" color="#666" class="data-v-126ab1e9" bind:__l="__l"></uni-icons><text class="uploader data-v-126ab1e9">{{document.$orig.uploader_name}}</text></view><view class="meta-item data-v-126ab1e9"><uni-icons vue-id="{{'1633f87c-7-'+__i1__}}" type="calendar" size="14" color="#666" class="data-v-126ab1e9" bind:__l="__l"></uni-icons><text class="upload-time data-v-126ab1e9">{{document.m3}}</text></view><view class="meta-item data-v-126ab1e9"><uni-icons vue-id="{{'1633f87c-8-'+__i1__}}" type="download" size="14" color="#666" class="data-v-126ab1e9" bind:__l="__l"></uni-icons><text class="download-count data-v-126ab1e9">{{"下载 "+document.$orig.download_count}}</text></view></view><block wx:if="{{document.$orig.description}}"><view class="document-desc data-v-126ab1e9">{{''+document.$orig.description+''}}</view></block></view><view class="document-actions data-v-126ab1e9"><view data-event-opts="{{[['tap',[['downloadDocument',['$0'],[[['documents','_id',document.$orig._id]]]]]]]}}" class="action-btn download-btn data-v-126ab1e9" catchtap="__e"><uni-icons vue-id="{{'1633f87c-9-'+__i1__}}" type="download" size="18" color="#4caf50" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view><block wx:if="{{document.m4}}"><view data-event-opts="{{[['tap',[['editDocument',['$0'],[[['documents','_id',document.$orig._id]]]]]]]}}" class="action-btn edit-btn data-v-126ab1e9" catchtap="__e"><uni-icons vue-id="{{'1633f87c-10-'+__i1__}}" type="compose" size="18" color="#ff9800" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view></block><block wx:if="{{document.m5}}"><view data-event-opts="{{[['tap',[['deleteDocument',['$0'],[[['documents','_id',document.$orig._id]]]]]]]}}" class="action-btn delete-btn data-v-126ab1e9" catchtap="__e"><uni-icons vue-id="{{'1633f87c-11-'+__i1__}}" type="trash" size="18" color="#f44336" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view></block></view></view></block></view></block></block><block wx:if="{{$root.g3>0}}"><view class="load-more data-v-126ab1e9"><block wx:if="{{hasMore}}"><view class="load-more-content data-v-126ab1e9"><uni-load-more vue-id="1633f87c-12" status="{{loadMoreStatus}}" data-event-opts="{{[['^clickLoadMore',[['loadMore']]]]}}" bind:clickLoadMore="__e" class="data-v-126ab1e9" bind:__l="__l"></uni-load-more></view></block><block wx:else><view class="no-more-content data-v-126ab1e9"><text class="no-more-text data-v-126ab1e9">{{"已显示全部 "+$root.g4+" 个文档"}}</text></view></block></view></block></view><block wx:if="{{canUpload}}"><view class="fab-container data-v-126ab1e9"><view data-event-opts="{{[['tap',[['showUploadModal',['$event']]]]]}}" class="fab-btn data-v-126ab1e9" bindtap="__e"><uni-icons vue-id="1633f87c-13" type="plus" size="24" color="#fff" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view></view></block><uni-popup class="popup-high-level data-v-126ab1e9 vue-ref" vue-id="1633f87c-14" type="center" mask-click="{{false}}" animation="{{false}}" data-ref="uploadPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="upload-modal data-v-126ab1e9"><view class="modal-header data-v-126ab1e9"><text class="modal-title data-v-126ab1e9">{{isEditMode?'编辑文档':'上传文档'}}</text><view data-event-opts="{{[['tap',[['closeUploadModal',['$event']]]]]}}" class="modal-close data-v-126ab1e9" bindtap="__e">×</view></view><view class="modal-content data-v-126ab1e9"><view class="form-item data-v-126ab1e9"><text class="form-label data-v-126ab1e9">文档标题</text><input class="form-input data-v-126ab1e9" placeholder="请输入文档标题" maxlength="100" data-event-opts="{{[['input',[['__set_model',['','documentTitle','$event',[]]]]]]}}" value="{{documentTitle}}" bindinput="__e"/></view><view class="form-item data-v-126ab1e9"><text class="form-label data-v-126ab1e9">选择分类</text><view data-event-opts="{{[['tap',[['openCategorySelector',['$event']]]]]}}" class="category-selector data-v-126ab1e9" bindtap="__e"><view class="picker-input data-v-126ab1e9">{{''+$root.m6+''}}<text class="picker-arrow data-v-126ab1e9">></text></view></view></view><view class="form-item data-v-126ab1e9"><text class="form-label data-v-126ab1e9">文档描述</text><textarea class="form-textarea data-v-126ab1e9" placeholder="请输入文档描述（可选）" maxlength="500" auto-height="{{true}}" data-event-opts="{{[['input',[['__set_model',['','documentDescription','$event',[]]]]]]}}" value="{{documentDescription}}" bindinput="__e"></textarea></view><block wx:if="{{!isEditMode}}"><view class="form-item data-v-126ab1e9"><text class="form-label data-v-126ab1e9">文档类型</text><view class="type-selector data-v-126ab1e9"><view data-event-opts="{{[['tap',[['selectDocumentType',['single']]]]]}}" class="{{['type-option','data-v-126ab1e9',(uploadForm.type==='single')?'active':'']}}" bindtap="__e"><uni-icons vue-id="{{('1633f87c-15')+','+('1633f87c-14')}}" type="paperclip" size="20" color="{{uploadForm.type==='single'?'#007aff':'#999'}}" class="data-v-126ab1e9" bind:__l="__l"></uni-icons><text class="{{['type-text','data-v-126ab1e9',(uploadForm.type==='single')?'active':'']}}">单个文档</text></view><view data-event-opts="{{[['tap',[['selectDocumentType',['collection']]]]]}}" class="{{['type-option','data-v-126ab1e9',(uploadForm.type==='collection')?'active':'']}}" bindtap="__e"><uni-icons vue-id="{{('1633f87c-16')+','+('1633f87c-14')}}" type="folder-add" size="20" color="{{uploadForm.type==='collection'?'#007aff':'#999'}}" class="data-v-126ab1e9" bind:__l="__l"></uni-icons><text class="{{['type-text','data-v-126ab1e9',(uploadForm.type==='collection')?'active':'']}}">文档集合</text></view></view></view></block><block wx:if="{{isEditMode}}"><view class="form-item data-v-126ab1e9"><text class="form-label data-v-126ab1e9">文档文件</text><block wx:if="{{$root.m7}}"><view class="current-document data-v-126ab1e9"><view class="doc-icon data-v-126ab1e9"><uni-icons vue-id="{{('1633f87c-17')+','+('1633f87c-14')}}" type="paperclip" size="24" color="#007aff" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view><view class="doc-info data-v-126ab1e9"><text class="doc-name data-v-126ab1e9">{{$root.m8}}</text></view><view class="doc-actions data-v-126ab1e9"><view class="action-btn remove-btn data-v-126ab1e9" title="删除文档" data-event-opts="{{[['tap',[['removeFileInEdit',['$event']]]]]}}" bindtap="__e"><uni-icons vue-id="{{('1633f87c-18')+','+('1633f87c-14')}}" type="trash" size="14" color="#f44336" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view></view></view></block><block wx:if="{{$root.m9}}"><view class="collection-edit data-v-126ab1e9"><view class="collection-header data-v-126ab1e9"><view class="collection-icon data-v-126ab1e9"><uni-icons vue-id="{{('1633f87c-19')+','+('1633f87c-14')}}" type="folder-add" size="24" color="#007aff" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view><view class="collection-info data-v-126ab1e9"><text class="collection-title data-v-126ab1e9">{{$root.m10.title}}</text></view></view><block wx:if="{{$root.m11}}"><view class="collection-files data-v-126ab1e9"><block wx:for="{{$root.l1}}" wx:for-item="file" wx:for-index="index" wx:key="index"><view class="collection-file-item data-v-126ab1e9"><view class="file-icon data-v-126ab1e9"><uni-icons vue-id="{{('1633f87c-20-'+index)+','+('1633f87c-14')}}" type="{{file.m12}}" size="20" color="{{file.m13}}" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view><view class="file-details data-v-126ab1e9"><text class="file-name data-v-126ab1e9">{{file.$orig.file_name}}</text><text class="file-size data-v-126ab1e9">{{file.m14}}</text></view><view class="file-actions data-v-126ab1e9"><view class="action-btn remove-btn data-v-126ab1e9" title="删除文件" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({index})}}" bindtap="__e"><uni-icons vue-id="{{('1633f87c-21-'+index)+','+('1633f87c-14')}}" type="trash" size="14" color="#f44336" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view></view></view></block></view></block><view class="collection-actions data-v-126ab1e9"><view data-event-opts="{{[['tap',[['addFilesToCollection',['$event']]]]]}}" class="collection-add-btn data-v-126ab1e9" bindtap="__e"><uni-icons vue-id="{{('1633f87c-22')+','+('1633f87c-14')}}" type="plus" size="18" color="#333" class="data-v-126ab1e9" bind:__l="__l"></uni-icons><text class="data-v-126ab1e9">添加文件</text></view></view></view></block></view></block><block wx:if="{{!isEditMode}}"><view class="form-item data-v-126ab1e9"><text class="form-label data-v-126ab1e9">{{uploadForm.type==='collection'?'选择文件（可多选）':'选择文件'}}</text><view class="file-upload-container data-v-126ab1e9"><block wx:if="{{uploadForm.type==='single'}}"><block wx:if="{{!uploadForm.file_name}}"><view data-event-opts="{{[['tap',[['chooseFile',['$event']]]]]}}" class="{{['upload-area','data-v-126ab1e9',(uploading)?'uploading':'']}}" bindtap="__e"><view class="upload-icon data-v-126ab1e9"><uni-icons vue-id="{{('1633f87c-23')+','+('1633f87c-14')}}" type="cloud-upload" size="48" color="#007aff" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view><text class="upload-text data-v-126ab1e9">{{uploading?'上传中...':'点击选择文件'}}</text><text class="upload-hint data-v-126ab1e9">支持 PDF、Word、Excel、PPT 等格式</text></view></block><block wx:if="{{uploadForm.file_name}}"><view class="file-selected data-v-126ab1e9"><view class="file-icon data-v-126ab1e9"><uni-icons vue-id="{{('1633f87c-24')+','+('1633f87c-14')}}" type="{{$root.m15}}" size="32" color="{{$root.m16}}" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view><view class="file-details data-v-126ab1e9"><text class="file-name data-v-126ab1e9">{{uploadForm.file_name}}</text><text class="file-size data-v-126ab1e9">{{$root.m17}}</text></view><view class="file-actions data-v-126ab1e9"><view class="action-btn remove-btn data-v-126ab1e9" title="删除文件" data-event-opts="{{[['tap',[['removeFile',['$event']]]]]}}" bindtap="__e"><uni-icons vue-id="{{('1633f87c-25')+','+('1633f87c-14')}}" type="trash" size="16" color="#f44336" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view></view></view></block></block><block wx:if="{{uploadForm.type==='collection'}}"><view class="multi-file-upload data-v-126ab1e9"><view data-event-opts="{{[['tap',[['chooseMultipleFiles',['$event']]]]]}}" class="{{['upload-area','multi-upload','data-v-126ab1e9',(uploading)?'uploading':'']}}" bindtap="__e"><view class="upload-icon data-v-126ab1e9"><uni-icons vue-id="{{('1633f87c-26')+','+('1633f87c-14')}}" type="cloud-upload" size="48" color="#007aff" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view><text class="upload-text data-v-126ab1e9">{{uploading?'上传中...':'点击选择多个文件'}}</text><text class="upload-hint data-v-126ab1e9">支持同时选择多个文件组成集合</text></view><block wx:if="{{$root.g5}}"><view class="files-list data-v-126ab1e9"><block wx:for="{{$root.l2}}" wx:for-item="file" wx:for-index="index" wx:key="index"><view class="file-item data-v-126ab1e9"><view class="file-icon data-v-126ab1e9"><uni-icons vue-id="{{('1633f87c-27-'+index)+','+('1633f87c-14')}}" type="{{file.m18}}" size="24" color="{{file.m19}}" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view><view class="file-details data-v-126ab1e9"><text class="file-name data-v-126ab1e9">{{file.$orig.file_name}}</text><text class="file-size data-v-126ab1e9">{{file.m20}}</text></view><view class="file-actions data-v-126ab1e9"><view class="action-btn remove-btn data-v-126ab1e9" title="删除文件" data-event-opts="{{[['tap',[['removeFileFromCollection',[index]]]]]}}" bindtap="__e"><uni-icons vue-id="{{('1633f87c-28-'+index)+','+('1633f87c-14')}}" type="trash" size="16" color="#f44336" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view></view></view></block></view></block></view></block></view></view></block></view><view class="modal-footer data-v-126ab1e9"><button class="btn btn-cancel data-v-126ab1e9" disabled="{{isSubmitting||uploading}}" data-event-opts="{{[['tap',[['closeUploadModal',['$event']]]]]}}" bindtap="__e">{{''+(isSubmitting||uploading?'处理中...':'取消')+''}}</button><button class="btn btn-primary data-v-126ab1e9" disabled="{{isEditMode?!canSubmitEdit||isSubmitting:!canSubmit||uploading||isSubmitting}}" data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" bindtap="__e">{{''+(isEditMode?isSubmitting?'保存中...':'确定修改':uploading?'上传中...':isSubmitting?'提交中...':'确定上传')+''}}</button></view></view></uni-popup><uni-popup class="popup-high-level data-v-126ab1e9 vue-ref" vue-id="1633f87c-29" type="bottom" mask-click="{{false}}" animation="{{false}}" data-ref="categorySelectorPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="selector-modal-content data-v-126ab1e9"><view class="selector-modal-header data-v-126ab1e9"><text class="selector-modal-title data-v-126ab1e9">选择分类</text><view data-event-opts="{{[['tap',[['closeCategorySelector',['$event']]]]]}}" class="selector-modal-close data-v-126ab1e9" bindtap="__e"><uni-icons vue-id="{{('1633f87c-30')+','+('1633f87c-29')}}" type="close" size="22" color="#999" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view></view><scroll-view class="selector-list-container data-v-126ab1e9" scroll-y="{{true}}"><block wx:if="{{$root.g6===0}}"><view class="empty-category-hint data-v-126ab1e9"><text class="empty-text data-v-126ab1e9">暂无分类</text><block wx:if="{{canManageCategory}}"><text class="empty-desc data-v-126ab1e9">请先到分类管理中添加</text></block></view></block><block wx:for="{{categoryOptions}}" wx:for-item="category" wx:for-index="__i2__" wx:key="_id"><view data-event-opts="{{[['tap',[['handleCategorySelect',['$0'],[[['categoryOptions','_id',category._id]]]]]]]}}" class="{{['selector-item','data-v-126ab1e9',((isEditMode?editForm.category_id:uploadForm.category_id)===category._id)?'selected':'']}}" bindtap="__e"><view class="selector-item-content data-v-126ab1e9"><text class="selector-name data-v-126ab1e9">{{category.name}}</text></view><view class="selector-item-left data-v-126ab1e9"><block wx:if="{{(isEditMode?editForm.category_id:uploadForm.category_id)===category._id}}"><uni-icons vue-id="{{('1633f87c-31-'+__i2__)+','+('1633f87c-29')}}" type="checkmarkempty" size="24" color="#007aff" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></block></view></view></block></scroll-view></view></uni-popup><uni-popup class="popup-high-level data-v-126ab1e9 vue-ref" vue-id="1633f87c-32" type="center" mask-click="{{false}}" animation="{{false}}" data-ref="categoryEditPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="category-edit-modal data-v-126ab1e9"><view class="modal-header data-v-126ab1e9"><text class="modal-title data-v-126ab1e9">编辑分类</text><view data-event-opts="{{[['tap',[['closeCategoryEditModal',['$event']]]]]}}" class="modal-close data-v-126ab1e9" bindtap="__e">×</view></view><view class="modal-content data-v-126ab1e9"><view class="form-item data-v-126ab1e9"><text class="form-label data-v-126ab1e9">分类名称</text><input class="form-input data-v-126ab1e9" placeholder="请输入分类名称" maxlength="50" data-event-opts="{{[['input',[['__set_model',['','editingCategoryName','$event',[]]]]]]}}" value="{{editingCategoryName}}" bindinput="__e"/></view><view class="form-item data-v-126ab1e9"><text class="form-label data-v-126ab1e9">分类描述</text><textarea class="form-textarea data-v-126ab1e9" placeholder="请输入分类描述（可选）" maxlength="200" auto-height="{{true}}" data-event-opts="{{[['input',[['__set_model',['','editingCategoryDescription','$event',[]]]]]]}}" value="{{editingCategoryDescription}}" bindinput="__e"></textarea></view></view><view class="modal-footer data-v-126ab1e9"><button class="btn btn-cancel data-v-126ab1e9" disabled="{{isSubmitting}}" data-event-opts="{{[['tap',[['closeCategoryEditModal',['$event']]]]]}}" bindtap="__e">{{''+(isSubmitting?'处理中...':'取消')+''}}</button><button data-event-opts="{{[['tap',[['submitCategoryEdit',['$event']]]]]}}" class="btn btn-primary data-v-126ab1e9" bindtap="__e">确定修改</button></view></view></uni-popup><block wx:if="{{isCategoryManagerVisible}}"><view class="category-manager-overlay data-v-126ab1e9"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="category-manager-content data-v-126ab1e9" catchtap="__e"><view class="manager-header data-v-126ab1e9"><text class="manager-title data-v-126ab1e9">分类管理</text><view data-event-opts="{{[['tap',[['closeCategoryManager',['$event']]]]]}}" class="manager-close data-v-126ab1e9" bindtap="__e"><uni-icons vue-id="1633f87c-33" type="close" size="18" color="#8a94a6" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view></view><view class="manager-add-section data-v-126ab1e9"><view class="add-form data-v-126ab1e9"><view class="form-row data-v-126ab1e9"><input class="form-input data-v-126ab1e9" placeholder="分类名称" maxlength="50" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['newCategory']]]]]}}" value="{{newCategory.name}}" bindinput="__e"/><input class="form-input data-v-126ab1e9" placeholder="分类描述（可选）" maxlength="200" data-event-opts="{{[['input',[['__set_model',['$0','description','$event',[]],['newCategory']]]]]}}" value="{{newCategory.description}}" bindinput="__e"/></view><button class="add-btn data-v-126ab1e9" disabled="{{$root.g7}}" data-event-opts="{{[['tap',[['createCategory',['$event']]]]]}}" bindtap="__e"><uni-icons vue-id="1633f87c-34" type="plus" size="16" color="#FFFFFF" class="data-v-126ab1e9" bind:__l="__l"></uni-icons><text class="data-v-126ab1e9">添加</text></button></view></view><scroll-view class="manager-list data-v-126ab1e9" scroll-y="{{true}}"><block wx:if="{{$root.g8===0}}"><view class="empty-state data-v-126ab1e9"><uni-icons vue-id="1633f87c-35" type="folder" size="40" color="#c4c4c4" class="data-v-126ab1e9" bind:__l="__l"></uni-icons><text class="empty-text data-v-126ab1e9">暂无分类</text></view></block><block wx:for="{{categories}}" wx:for-item="category" wx:for-index="__i3__" wx:key="_id"><view class="manager-item data-v-126ab1e9"><view class="item-info data-v-126ab1e9"><text class="item-name data-v-126ab1e9">{{category.name}}</text><block wx:if="{{category.description}}"><text class="item-desc data-v-126ab1e9">{{category.description}}</text></block><text class="item-count data-v-126ab1e9">{{(category.document_count||0)+"个文档"}}</text></view><view class="item-actions data-v-126ab1e9"><view data-event-opts="{{[['tap',[['editCategory',['$0'],[[['categories','_id',category._id]]]]]]]}}" class="manager-action-btn edit-btn data-v-126ab1e9" bindtap="__e"><uni-icons vue-id="{{'1633f87c-36-'+__i3__}}" type="compose" size="16" color="#3a86ff" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view><view data-event-opts="{{[['tap',[['confirmDeleteCategory',['$0'],[[['categories','_id',category._id]]]]]]]}}" class="manager-action-btn delete-btn data-v-126ab1e9" bindtap="__e"><uni-icons vue-id="{{'1633f87c-37-'+__i3__}}" type="trash" size="16" color="#ef4444" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view></view></view></block></scroll-view></view></view></block><uni-popup class="popup-high-level data-v-126ab1e9 vue-ref" vue-id="1633f87c-38" type="center" mask-click="{{false}}" animation="{{false}}" data-ref="collectionDownloadPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="collection-download-modal data-v-126ab1e9"><view class="modal-header data-v-126ab1e9"><view class="header-content data-v-126ab1e9"><view class="collection-info data-v-126ab1e9"><uni-icons vue-id="{{('1633f87c-39')+','+('1633f87c-38')}}" type="paperclip" size="24" color="#007aff" class="data-v-126ab1e9" bind:__l="__l"></uni-icons><text class="collection-title data-v-126ab1e9">{{currentCollection?currentCollection.title:'文档集合'}}</text></view><text class="collection-desc data-v-126ab1e9">{{currentCollection?currentCollection.description:''}}</text></view><view data-event-opts="{{[['tap',[['closeCollectionDownloadModal',['$event']]]]]}}" class="modal-close data-v-126ab1e9" bindtap="__e"><uni-icons vue-id="{{('1633f87c-40')+','+('1633f87c-38')}}" type="close" size="20" color="#999" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view></view><view class="modal-content data-v-126ab1e9"><view class="files-section data-v-126ab1e9"><view class="section-header data-v-126ab1e9"><text class="section-title data-v-126ab1e9">选择要下载的文件</text><text class="file-count data-v-126ab1e9">{{currentCollectionFileCount+" 个文件"}}</text></view><scroll-view class="files-list data-v-126ab1e9" scroll-y="{{true}}"><block wx:for="{{$root.l3}}" wx:for-item="file" wx:for-index="index" wx:key="index"><block wx:if="{{currentCollectionFileCount>0}}"><view class="{{['file-item','data-v-126ab1e9',(file.g9)?'downloading':'']}}"><view class="file-info data-v-126ab1e9"><view class="file-icon data-v-126ab1e9"><uni-icons vue-id="{{('1633f87c-41-'+index)+','+('1633f87c-38')}}" type="{{file.m21}}" size="20" color="{{file.m22}}" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view><view class="file-details data-v-126ab1e9"><text class="file-name data-v-126ab1e9">{{file.$orig.file_name}}</text><text class="file-size data-v-126ab1e9">{{file.m23}}</text></view></view><view class="file-actions data-v-126ab1e9"><block wx:if="{{file.g10}}"><view class="download-success data-v-126ab1e9"><uni-icons vue-id="{{('1633f87c-42-'+index)+','+('1633f87c-38')}}" type="checkmarkempty" size="16" color="#4caf50" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view></block><block wx:else><block wx:if="{{file.g11}}"><view class="download-progress data-v-126ab1e9"><uni-icons vue-id="{{('1633f87c-43-'+index)+','+('1633f87c-38')}}" type="spinner-cycle" size="16" color="#007aff" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view></block><block wx:else><view data-event-opts="{{[['tap',[['downloadCollectionFile',['$0',index],[[['currentCollectionFiles','',index]]]]]]]}}" class="download-btn data-v-126ab1e9" bindtap="__e"><uni-icons vue-id="{{('1633f87c-44-'+index)+','+('1633f87c-38')}}" type="download" size="16" color="#007aff" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view></block></block></view></view></block></block><block wx:if="{{currentCollectionFileCount===0}}"><view class="empty-files data-v-126ab1e9"><uni-icons vue-id="{{('1633f87c-45')+','+('1633f87c-38')}}" type="paperclip" size="48" color="#ccc" class="data-v-126ab1e9" bind:__l="__l"></uni-icons><text class="empty-text data-v-126ab1e9">暂无文件</text></view></block></scroll-view></view></view><view class="modal-footer data-v-126ab1e9"><button data-event-opts="{{[['tap',[['closeCollectionDownloadModal',['$event']]]]]}}" class="btn btn-cancel data-v-126ab1e9" bindtap="__e">关闭</button><block wx:if="{{currentCollectionFileCount>0}}"><button class="btn btn-primary data-v-126ab1e9" disabled="{{$root.g12>0}}" data-event-opts="{{[['tap',[['downloadAllFiles',['$event']]]]]}}" bindtap="__e">下载全部</button></block></view></view></uni-popup><uni-popup vue-id="1633f87c-46" type="center" mask-click="{{false}}" animation="{{false}}" data-ref="documentPreviewPopup" class="data-v-126ab1e9 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="document-preview-modal data-v-126ab1e9"><view class="modal-header data-v-126ab1e9"><view class="header-content data-v-126ab1e9"><view class="document-info data-v-126ab1e9"><uni-icons vue-id="{{('1633f87c-47')+','+('1633f87c-46')}}" type="{{$root.m24}}" size="24" color="{{$root.m25}}" class="data-v-126ab1e9" bind:__l="__l"></uni-icons><text class="document-title data-v-126ab1e9">{{currentPreviewDocument?currentPreviewDocument.title:'文档预览'}}</text></view><text class="document-desc data-v-126ab1e9">{{currentPreviewDocument?currentPreviewDocument.description:''}}</text></view><view data-event-opts="{{[['tap',[['closeDocumentPreviewModal',['$event']]]]]}}" class="modal-close data-v-126ab1e9" bindtap="__e"><uni-icons vue-id="{{('1633f87c-48')+','+('1633f87c-46')}}" type="close" size="20" color="#999" class="data-v-126ab1e9" bind:__l="__l"></uni-icons></view></view><view class="modal-content data-v-126ab1e9"><view class="preview-section data-v-126ab1e9"><view class="preview-item data-v-126ab1e9"><text class="preview-label data-v-126ab1e9">文件名</text><text class="preview-value data-v-126ab1e9">{{currentPreviewDocument?currentPreviewDocument.file_name:''}}</text></view><view class="preview-item data-v-126ab1e9"><text class="preview-label data-v-126ab1e9">文件大小</text><text class="preview-value data-v-126ab1e9">{{currentPreviewDocument?$root.m26:''}}</text></view><view class="preview-item data-v-126ab1e9"><text class="preview-label data-v-126ab1e9">上传者</text><text class="preview-value data-v-126ab1e9">{{currentPreviewDocument?currentPreviewDocument.uploader_name:''}}</text></view><view class="preview-item data-v-126ab1e9"><text class="preview-label data-v-126ab1e9">上传时间</text><text class="preview-value data-v-126ab1e9">{{currentPreviewDocument?$root.m27:''}}</text></view><view class="preview-item data-v-126ab1e9"><text class="preview-label data-v-126ab1e9">下载次数</text><text class="preview-value data-v-126ab1e9">{{(currentPreviewDocument?currentPreviewDocument.download_count:0)+" 次"}}</text></view></view></view><view class="modal-footer data-v-126ab1e9"><button data-event-opts="{{[['tap',[['closeDocumentPreviewModal',['$event']]]]]}}" class="btn btn-cancel data-v-126ab1e9" bindtap="__e">关闭</button><button data-event-opts="{{[['tap',[['downloadDocument',['$0'],['currentPreviewDocument']]]]]}}" class="btn btn-primary data-v-126ab1e9" bindtap="__e"><uni-icons vue-id="{{('1633f87c-49')+','+('1633f87c-46')}}" type="download" size="16" color="#fff" class="data-v-126ab1e9" bind:__l="__l"></uni-icons><text class="data-v-126ab1e9">下载文档</text></button></view></view></uni-popup></view>