(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/honor_pkg/avatar-picker/avatar-picker"],{"037d":function(e,t,a){"use strict";var r=a("d524"),n=a.n(r);n.a},5190:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return s})),a.d(t,"a",(function(){return r}));var r={uniIcons:function(){return Promise.all([a.e("common/vendor"),a.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(a.bind(null,"6ddf"))},uniPopup:function(){return a.e("uni_modules/uni-popup/components/uni-popup/uni-popup").then(a.bind(null,"a2b7"))},uniPopupDialog:function(){return Promise.all([a.e("common/vendor"),a.e("uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog")]).then(a.bind(null,"1f14"))},pEmptyState:function(){return a.e("components/p-empty-state/p-empty-state").then(a.bind(null,"9b76"))},uniEasyinput:function(){return a.e("uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput").then(a.bind(null,"6cf4"))}},n=function(){var e=this,t=e.$createElement,a=(e._self._c,e.historyAvatars.length);e._isMounted||(e.e0=function(t){return e.$refs.deleteConfirm.close()}),e.$mp.data=Object.assign({},{$root:{g0:a}})},s=[]},"854b":function(e,t,a){"use strict";a.r(t);var r=a("fcde"),n=a.n(r);for(var s in r)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(s);t["default"]=n.a},baea:function(e,t,a){"use strict";a.r(t);var r=a("5190"),n=a("854b");for(var s in n)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(s);a("037d");var o=a("828b"),i=Object(o["a"])(n["default"],r["b"],r["c"],!1,null,"2c953513",null,!1,r["a"],void 0);t["default"]=i.exports},d524:function(e,t,a){},fcde:function(e,t,a){"use strict";(function(e,r){var n=a("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s,o=n(a("7eb4")),i=n(a("7ca3")),u=n(a("ee10")),c=n(a("4ea0")),l={maxSize:10485760,timeout:3e4,validTypes:["jpg","jpeg","png","webp","JPG","JPEG","PNG","WEBP"],compressQuality:.8},d={name:"AvatarPicker",components:{PEmptyState:function(){a.e("components/p-empty-state/p-empty-state").then(function(){return resolve(a("9b76"))}.bind(null,a)).catch(a.oe)}},props:{value:{type:String,default:""},label:{type:String,default:"头像"},showActions:{type:Boolean,default:!0},autoUpload:{type:Boolean,default:!0},customPath:{type:String,default:""},sizeLimit:{type:Number,default:l.maxSize},timeout:{type:Number,default:l.timeout}},data:function(){return{uploading:!1,uploadProgress:0,uploadError:!1,avatarUrl:"",uploadTimer:null,currentFile:null,uploadRes:null,historyAvatars:[],batchList:[],recentUserName:"",defaultAvatar:"/static/user/default-avatar.png"}},computed:{displayAvatar:function(){return this.avatarUrl||this.defaultAvatar}},watch:{value:{immediate:!0,handler:function(e){this.avatarUrl=e}}},created:function(){this.loadHistoryAvatars()},methods:(s={loadHistoryAvatars:function(){var t=this;return(0,u.default)(o.default.mark((function a(){var r,n;return o.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,e.callFunction({name:"history-avatar-get",data:{}});case 3:r=a.sent,n=r.result,0===n.code?t.historyAvatars=n.data:console.error("获取历史头像失败:",n.message),a.next=11;break;case 8:a.prev=8,a.t0=a["catch"](0),console.error("获取历史头像失败:",a.t0);case 11:case"end":return a.stop()}}),a,null,[[0,8]])})))()},onUploadSuccess:function(e){var t=this;return(0,u.default)(o.default.mark((function e(){return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.loadHistoryAvatars();case 2:case"end":return e.stop()}}),e)})))()},handleError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"upload",a={upload:"上传失败",compress:"压缩失败",select:"选择失败",size:"文件过大",format:"格式不支持"};this.uploadError="upload"===t,r.showToast({title:e.message||a[t],icon:"none"}),this.$emit("error",{type:t,error:e})},handleSelectImage:function(){var e=this;return(0,u.default)(o.default.mark((function t(){var a,n,s,i,u;return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.uploading){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,n={count:1,sizeType:["compressed"],sourceType:["album"]},t.next=6,r.chooseImage(n);case 6:if(s=t.sent,null===(a=s.tempFilePaths)||void 0===a||!a[0]){t.next=17;break}if(i=s.tempFiles[0],u=s.tempFilePaths[0],!(i.size>e.sizeLimit)){t.next=13;break}return e.handleError({message:"文件大小".concat(e.formatFileSize(i.size),"超过限制").concat(e.formatFileSize(e.sizeLimit))},"size"),t.abrupt("return");case 13:if(e.currentFile={path:u,size:i.size},!e.autoUpload){t.next=17;break}return t.next=17,e.uploadAvatar();case 17:t.next=23;break;case 19:t.prev=19,t.t0=t["catch"](2),console.error("选择图片失败:",t.t0),e.handleError(t.t0,"select");case 23:case"end":return t.stop()}}),t,null,[[2,19]])})))()},retryUpload:function(){var e=this;return(0,u.default)(o.default.mark((function t(){return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.uploadError=!1,t.next=3,e.uploadAvatar();case 3:case"end":return t.stop()}}),t)})))()},previewAvatar:function(){this.avatarUrl&&r.previewImage({urls:[this.avatarUrl]})},confirmDelete:function(){this.$refs.deleteConfirm.open()},handleDelete:function(){var t=this;return(0,u.default)(o.default.mark((function a(){var n,s,i;return o.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,!t.avatarUrl){a.next=17;break}return n=t.avatarUrl,n.startsWith("http")&&t.uploadRes&&t.uploadRes.cloudPath&&(n=t.uploadRes.cloudPath),a.prev=4,a.next=7,e.callFunction({name:"delete-file",data:{fileList:[n]}});case 7:if(s=a.sent,i=s.result,0===i.code){a.next=11;break}throw new Error(i.message||"云端文件删除失败");case 11:a.next=17;break;case 13:throw a.prev=13,a.t0=a["catch"](4),console.error("云端文件删除失败:",a.t0),new Error(a.t0.message||"云端文件删除失败");case 17:t.avatarUrl="",t.currentFile=null,t.uploadRes=null,t.$emit("input",""),t.$emit("delete"),t.$refs.deleteConfirm.close(),r.showToast({title:"删除成功",icon:"success"}),a.next=31;break;case 26:a.prev=26,a.t1=a["catch"](0),console.error("删除失败:",a.t1),t.handleError(a.t1,"delete"),t.$refs.deleteConfirm.close();case 31:case"end":return a.stop()}}),a,null,[[0,26],[4,13]])})))()},validateImageFormat:function(e){var t;if(!e)return!1;var a=null===(t=e.split(".").pop())||void 0===t?void 0:t.toLowerCase();return!!a&&l.validTypes.map((function(e){return e.toLowerCase()})).includes(a)},formatFileSize:function(e){if(0===e)return"0 Bytes";var t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB","TB"][t]},formatDate:function(e){var t=new Date(e),a=t.getFullYear(),r=String(t.getMonth()+1).padStart(2,"0"),n=String(t.getDate()).padStart(2,"0"),s=String(t.getHours()).padStart(2,"0"),o=String(t.getMinutes()).padStart(2,"0");return"".concat(a,"-").concat(r,"-").concat(n," ").concat(s,":").concat(o)},openHistorySelect:function(){this.$refs.historyPopup.open()},closeHistorySelect:function(){this.$refs.historyPopup.close()},selectHistoryAvatar:function(e){this.avatarUrl=e.url,this.$emit("input",e.url),this.$emit("change",{url:e.url,cloudPath:e.fileID}),this.closeHistorySelect(),r.showToast({title:"已选择头像",icon:"success"})},uploadAvatar:function(){var t=this;return(0,u.default)(o.default.mark((function a(){var n,s,i,u,c;return o.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t.currentFile){a.next=2;break}return a.abrupt("return");case 2:return a.prev=2,t.uploading=!0,t.uploadError=!1,a.next=7,e.uploadFile({filePath:t.currentFile.path,cloudPath:"avatars/".concat(Date.now(),"-").concat(Math.random().toString(36).slice(2),".").concat(t.currentFile.path.split(".").pop())});case 7:if(s=a.sent,s.fileID){a.next=10;break}throw new Error("上传失败");case 10:return a.next=12,e.getTempFileURL({fileList:[s.fileID]});case 12:if(i=a.sent,u=i.fileList,null!==u&&void 0!==u&&null!==(n=u[0])&&void 0!==n&&n.tempFileURL){a.next=16;break}throw new Error("获取临时链接失败");case 16:return c=u[0].tempFileURL,a.next=19,t.saveHistoryAvatar(c,s.fileID);case 19:t.avatarUrl=c,t.$emit("input",c),t.$emit("change",c),r.showToast({title:"上传成功",icon:"success"}),a.next=30;break;case 25:a.prev=25,a.t0=a["catch"](2),console.error("上传头像失败:",a.t0),t.uploadError=!0,r.showToast({title:a.t0.message||"上传失败",icon:"none"});case 30:return a.prev=30,t.uploading=!1,t.currentFile=null,a.finish(30);case 34:case"end":return a.stop()}}),a,null,[[2,25,30,34]])})))()},openBatchUpload:function(){this.batchList=[{userName:"",url:"",saving:!1}],this.$refs.batchUploadPopup.open()},closeBatchUpload:function(){this.$refs.batchUploadPopup.close()},addBatchItem:function(){this.batchList.push({userName:"",url:"",saving:!1})},removeBatchItem:function(e){this.batchList.splice(e,1),0===this.batchList.length&&this.addBatchItem()},selectImage:function(e){var t=this;return(0,u.default)(o.default.mark((function a(){var n,s,i;return o.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,r.chooseImage({count:1,sizeType:["compressed"],sourceType:["album"]});case 3:s=a.sent,null!==(n=s.tempFilePaths)&&void 0!==n&&n[0]&&(i=t.batchList[e],i.url=s.tempFilePaths[0],i.file=s.tempFiles[0]),a.next=10;break;case 7:a.prev=7,a.t0=a["catch"](0),console.error("选择图片失败:",a.t0);case 10:case"end":return a.stop()}}),a,null,[[0,7]])})))()},saveAvatar:function(t){var a=this;return(0,u.default)(o.default.mark((function n(){var s,i,u,l;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(s=a.batchList[t],s.userName){n.next=4;break}return r.showToast({title:"请输入姓名",icon:"none",duration:2e3}),n.abrupt("return");case 4:if(s.url){n.next=7;break}return r.showToast({title:"请选择头像",icon:"none",duration:2e3}),n.abrupt("return");case 7:if(!s.saving){n.next=9;break}return n.abrupt("return");case 9:return s.saving=!0,n.prev=10,n.next=13,c.default.uploadAvatar(s.url);case 13:return i=n.sent,n.next=16,e.callFunction({name:"history-avatar-save",data:{url:i.cloudPath,userName:s.userName,createTime:Date.now()}});case 16:if(u=n.sent,l=u.result,0!==l.code){n.next=26;break}return r.showToast({title:l.message||"保存成功",icon:"success"}),s.userName="",s.url="",n.next=24,a.loadHistoryAvatars();case 24:n.next=27;break;case 26:throw new Error(l.message);case 27:n.next=32;break;case 29:n.prev=29,n.t0=n["catch"](10),r.showToast({title:n.t0.message||"保存失败",icon:"none"});case 32:return n.prev=32,s.saving=!1,n.finish(32);case 35:case"end":return n.stop()}}),n,null,[[10,29,32,35]])})))()},clearAvatar:function(){this.avatarUrl=this.defaultAvatar,this.$emit("update:modelValue",""),this.$emit("change","")}},(0,i.default)(s,"confirmDelete",(function(){var e=this;return(0,u.default)(o.default.mark((function t(){return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:r.showModal({title:"删除确认",content:"确定要删除当前头像吗？删除后将恢复为默认头像。",success:function(){var t=(0,u.default)(o.default.mark((function t(a){return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a.confirm){t.next=3;break}return t.next=3,e.deleteAvatar();case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()});case 1:case"end":return t.stop()}}),t)})))()})),(0,i.default)(s,"deleteAvatar",(function(){var t=this;return(0,u.default)(o.default.mark((function a(){var n,s,i,u;return o.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,r.showLoading({title:"删除中...",mask:!0}),n=e.database(),s=n.collection("history-avatar"),a.next=6,s.where({url:t.avatarUrl}).get();case 6:if(i=a.sent,!(i.data&&i.data.length>0)){a.next=14;break}if(u=i.data[0],!u.fileID){a.next=12;break}return a.next=12,e.deleteFile({fileList:[u.fileID]});case 12:return a.next=14,s.doc(u._id).remove();case 14:return t.avatarUrl=t.value||"/static/user/default-avatar.png",t.$emit("input",""),a.next=18,t.loadHistoryAvatars();case 18:r.showToast({title:"删除成功",icon:"success"}),a.next=25;break;case 21:a.prev=21,a.t0=a["catch"](0),console.error("删除头像失败:",a.t0),r.showToast({title:"删除失败",icon:"error"});case 25:return a.prev=25,r.hideLoading(),a.finish(25);case 28:case"end":return a.stop()}}),a,null,[[0,21,25,28]])})))()})),(0,i.default)(s,"confirmDeleteHistory",(function(e){var t=this;r.showModal({title:"删除确认",content:"确定要删除这个头像吗？此操作不可恢复。",success:function(){var a=(0,u.default)(o.default.mark((function a(r){return o.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!r.confirm){a.next=3;break}return a.next=3,t.deleteHistoryAvatar(e);case 3:case"end":return a.stop()}}),a)})));return function(e){return a.apply(this,arguments)}}()})})),(0,i.default)(s,"deleteHistoryAvatar",(function(t){var a=this;return(0,u.default)(o.default.mark((function n(){var s,i;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t&&t._id){n.next=3;break}return r.showToast({title:"无效的记录",icon:"none"}),n.abrupt("return");case 3:return n.prev=3,r.showLoading({title:"删除中...",mask:!0}),n.next=7,e.callFunction({name:"history-avatar-delete",data:{_id:t._id}});case 7:if(s=n.sent,i=s.result,0!==i.code){n.next=16;break}return t.url===a.avatarUrl&&(a.avatarUrl=a.value||"/static/user/default-avatar.png",a.$emit("input","")),n.next=13,a.loadHistoryAvatars();case 13:r.showToast({title:"删除成功",icon:"success"}),n.next=17;break;case 16:throw new Error(i.msg||"删除失败");case 17:n.next=23;break;case 19:n.prev=19,n.t0=n["catch"](3),console.error("删除历史头像失败:",n.t0),r.showToast({title:n.t0.message||"删除失败",icon:"none"});case 23:return n.prev=23,r.hideLoading(),n.finish(23);case 26:case"end":return n.stop()}}),n,null,[[3,19,23,26]])})))()})),(0,i.default)(s,"saveHistoryAvatar",(function(t,a){var n=this;return(0,u.default)(o.default.mark((function s(){var i,u;return o.default.wrap((function(s){while(1)switch(s.prev=s.next){case 0:return s.prev=0,s.next=3,e.callFunction({name:"history-avatar-save",data:{url:t,fileID:a,userName:n.userName||(r.getStorageSync("uni-id-pages-userInfo")||{}).username||"未命名"}});case 3:if(i=s.sent,u=i.result,0!==u.code){s.next=10;break}return s.next=8,n.loadHistoryAvatars();case 8:s.next=11;break;case 10:r.showToast({title:u.msg,icon:"none"});case 11:s.next=17;break;case 13:s.prev=13,s.t0=s["catch"](0),console.error("保存历史头像失败:",s.t0),r.showToast({title:"保存历史头像失败",icon:"none"});case 17:case"end":return s.stop()}}),s,null,[[0,13]])})))()})),s)};t.default=d}).call(this,a("861b")["uniCloud"],a("df3c")["default"])}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pages/honor_pkg/avatar-picker/avatar-picker-create-component',
    {
        'pages/honor_pkg/avatar-picker/avatar-picker-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("baea"))
        })
    },
    [['pages/honor_pkg/avatar-picker/avatar-picker-create-component']]
]);
