<view class="page-container data-v-bb11638e"><block wx:if="{{loading}}"><view class="loading-container data-v-bb11638e"><view class="loading-content data-v-bb11638e"><view class="loading-spinner data-v-bb11638e"></view><text class="loading-text data-v-bb11638e">加载整改任务信息中...</text></view></view></block><block wx:else><block wx:if="{{loadError}}"><view class="error-container data-v-bb11638e"><view class="error-content data-v-bb11638e"><uni-icons vue-id="53e6ffd6-1" type="info" size="48" color="#FF3B30" class="data-v-bb11638e" bind:__l="__l"></uni-icons><text class="error-text data-v-bb11638e">{{loadError}}</text><button data-event-opts="{{[['tap',[['retryLoad',['$event']]]]]}}" class="retry-button data-v-bb11638e" bindtap="__e">重新加载</button></view></view></block><block wx:else><view class="card data-v-bb11638e"><view class="card-header data-v-bb11638e"><view class="header-content data-v-bb11638e"><view class="card-title data-v-bb11638e">整改任务</view><view class="card-subtitle data-v-bb11638e">{{taskInfo.area+" - "+(taskInfo.isPublic?'公共责任区':'固定责任区')}}</view></view></view><view class="card-body data-v-bb11638e"><view class="task-info data-v-bb11638e"><view class="info-item data-v-bb11638e"><text class="info-label data-v-bb11638e">问题描述：</text><text class="info-value data-v-bb11638e">{{taskInfo.problemDescription}}</text></view><view class="info-item data-v-bb11638e"><text class="info-label data-v-bb11638e">发现时间：</text><text class="info-value data-v-bb11638e">{{$root.m0}}</text></view><view class="info-item data-v-bb11638e"><text class="info-label data-v-bb11638e">检查员：</text><text class="info-value data-v-bb11638e">{{taskInfo.inspector}}</text></view></view></view></view><block wx:if="{{taskInfo.reviewComments}}"><view class="card review-feedback-card data-v-bb11638e"><view class="card-header data-v-bb11638e"><view class="card-title data-v-bb11638e">审核反馈</view></view><view class="card-body data-v-bb11638e"><view class="feedback-content data-v-bb11638e"><view class="feedback-item data-v-bb11638e"><text class="feedback-label data-v-bb11638e">审核结果：</text><view class="status-badge status-rejected data-v-bb11638e">需重新整改</view></view><view class="feedback-item data-v-bb11638e"><text class="feedback-label data-v-bb11638e">审核意见：</text><text class="feedback-value data-v-bb11638e">{{taskInfo.reviewComments}}</text></view><view class="feedback-item data-v-bb11638e"><text class="feedback-label data-v-bb11638e">审核时间：</text><text class="feedback-value data-v-bb11638e">{{$root.m1}}</text></view><view class="feedback-item data-v-bb11638e"><text class="feedback-label data-v-bb11638e">审核人员：</text><text class="feedback-value data-v-bb11638e">{{taskInfo.reviewer}}</text></view></view></view></view></block><block wx:if="{{$root.g0}}"><view class="card inspection-photos-card data-v-bb11638e"><view class="card-header data-v-bb11638e"><view class="card-title data-v-bb11638e">问题照片</view><view class="card-subtitle data-v-bb11638e">检查员发现问题时拍摄的现场照片</view></view><view class="card-body data-v-bb11638e"><view class="inspection-photos-grid data-v-bb11638e"><block wx:for="{{$root.l0}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view class="inspection-photo-item data-v-bb11638e"><image src="{{photo.m2}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewInspectionPhoto',[index]]]]]}}" bindtap="__e" class="data-v-bb11638e"></image></view></block></view></view></view></block><block wx:if="{{$root.g1}}"><view class="card previous-photos-card data-v-bb11638e"><view class="card-header data-v-bb11638e"><view class="card-title data-v-bb11638e">之前的整改照片</view><view class="card-subtitle data-v-bb11638e">供参考，检查员认为需要改进</view></view><view class="card-body data-v-bb11638e"><view class="previous-photos-grid data-v-bb11638e"><block wx:for="{{$root.l1}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view class="previous-photo-item data-v-bb11638e"><image src="{{photo.$orig.url}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewPreviousPhoto',[index]]]]]}}" bindtap="__e" class="data-v-bb11638e"></image><view class="photo-overlay data-v-bb11638e"><text class="photo-time data-v-bb11638e">{{photo.m3}}</text></view></view></block></view></view></view></block><view class="card data-v-bb11638e"><view class="card-header data-v-bb11638e"><view class="card-title data-v-bb11638e">{{taskInfo.reviewComments?'重新整改':'整改记录'}}</view></view><view class="card-body data-v-bb11638e"><view class="upload-section data-v-bb11638e"><view class="section-header data-v-bb11638e"><view class="section-title data-v-bb11638e">上传整改照片</view><view data-event-opts="{{[['tap',[['toggleAutoUpload',['$event']]]]]}}" class="auto-upload-toggle data-v-bb11638e" bindtap="__e"><view class="toggle-label data-v-bb11638e">自动上传</view><view class="{{['toggle-switch','data-v-bb11638e',(autoUpload)?'active':'']}}"><view class="toggle-circle data-v-bb11638e"></view></view></view></view><view class="photo-grid data-v-bb11638e"><block wx:for="{{$root.l2}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view class="photo-item data-v-bb11638e"><image src="{{photo.m4}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewPhoto',[index]]]]]}}" bindtap="__e" class="data-v-bb11638e"></image><block wx:if="{{photo.$orig.uploading}}"><view class="photo-uploading data-v-bb11638e"><view class="upload-spinner data-v-bb11638e"></view></view></block><block wx:else><block wx:if="{{photo.$orig.uploaded}}"><view class="photo-uploaded data-v-bb11638e"><uni-icons vue-id="{{'53e6ffd6-2-'+index}}" type="checkmarkempty" size="16" color="white" class="data-v-bb11638e" bind:__l="__l"></uni-icons></view></block></block><view data-event-opts="{{[['tap',[['deletePhoto',[index]]]]]}}" class="photo-delete data-v-bb11638e" bindtap="__e"><uni-icons vue-id="{{'53e6ffd6-3-'+index}}" type="close" size="18" color="white" class="data-v-bb11638e" bind:__l="__l"></uni-icons></view></view></block><block wx:if="{{$root.g2<12}}"><view data-event-opts="{{[['tap',[['addPhoto',['$event']]]]]}}" class="photo-add data-v-bb11638e" bindtap="__e"><uni-icons vue-id="53e6ffd6-4" type="camera" size="32" color="#8E8E93" class="data-v-bb11638e" bind:__l="__l"></uni-icons><text class="data-v-bb11638e">添加照片</text></view></block></view><view class="photo-tip data-v-bb11638e">最多可上传12张照片，建议拍摄整改前后对比照片</view></view><view class="remarks-section data-v-bb11638e"><view class="section-title data-v-bb11638e">整改说明</view><view class="remarks-input-container data-v-bb11638e"><textarea class="remarks-input data-v-bb11638e" placeholder="请详细描述整改过程和结果..." maxlength="200" data-event-opts="{{[['input',[['__set_model',['','remarks','$event',[]]],['handleRemarksInput',['$event']]]]]}}" value="{{remarks}}" bindinput="__e"></textarea><view class="char-count-overlay data-v-bb11638e">{{remarksLength+"/200"}}</view></view></view></view></view></block></block><view class="button-container data-v-bb11638e"><button class="{{['primary-button','data-v-bb11638e',(submitting)?'loading':'']}}" disabled="{{$root.g3}}" data-event-opts="{{[['tap',[['submitRectification',['$event']]]]]}}" bindtap="__e"><block wx:if="{{submitting}}"><view class="button-loading data-v-bb11638e"><view class="loading-spinner data-v-bb11638e"></view><text class="data-v-bb11638e">提交中...</text></view></block><block wx:else><text class="data-v-bb11638e">提交整改记录</text></block></button></view></view>