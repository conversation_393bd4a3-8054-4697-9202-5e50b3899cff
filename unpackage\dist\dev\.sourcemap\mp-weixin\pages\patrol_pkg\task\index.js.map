{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/patrol_pkg/task/index.vue?fbc6", "webpack:///D:/Xwzc/pages/patrol_pkg/task/index.vue?08a6", "webpack:///D:/Xwzc/pages/patrol_pkg/task/index.vue?373b", "webpack:///D:/Xwzc/pages/patrol_pkg/task/index.vue?d9e2", "uni-app:///pages/patrol_pkg/task/index.vue", "webpack:///D:/Xwzc/pages/patrol_pkg/task/index.vue?c7fe", "webpack:///D:/Xwzc/pages/patrol_pkg/task/index.vue?ccdd"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "PEmptyState", "data", "page", "limit", "cachedTasks", "currentDisplayTasks", "hasMore", "loading", "isRefreshing", "searchText", "dayOffset", "currentDateOriginal", "currentDate", "statusFilter", "showFilters", "isInitialLoading", "shiftInfo", "routePointCounts", "startDate", "endDate", "sortBy", "sortDirection", "userMap", "pageReady", "initialDataLoaded", "weekdays", "currentYear", "currentMonth", "selectedDate", "calendarDays", "taskDates", "userInfo", "filterStatus", "filterOptions", "label", "value", "timer", "lastUpdateTime", "loadedMonths", "monthTaskCounts", "date<PERSON><PERSON><PERSON>", "start", "end", "isAdmin", "cacheKeys", "users", "routes", "shifts", "cacheExpiry", "computed", "formatSelectedDate", "tomorrow", "filteredTasks", "console", "allCachedTasks", "Object", "allTasks", "hasActiveFilters", "formattedDateForAPI", "onLoad", "uni", "title", "mask", "loadPromises", "Promise", "icon", "onShow", "onPullDownRefresh", "onReady", "onUnload", "clearInterval", "methods", "setCache", "timestamp", "expiry", "getCache", "clearCache", "initDateRange", "getYearMonth", "isDateInRange", "updateCurrentDisplayTasks", "initCalendar", "refreshList", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectDate", "date", "isNaN", "yearMonth", "generateCalendarDays", "days", "day", "isToday", "hasTask", "isSameDay", "d1", "isSelectedDate", "hasTasksOnDate", "formatDateForCompare", "prevMonth", "newDate", "nextMonth", "extractTaskDates", "setTimeout", "manualAllTasks", "tasksToShow", "dateSet", "loadCurrentMonthTasks", "currentYearMonth", "loadMonthTasks", "userRoleStr", "userRole", "Array", "params", "pageSize", "status", "viewScope", "level", "fields", "PatrolApi", "name", "action", "response", "rawTasks", "total", "processedTasks", "duration", "loaded", "processTasks", "_id", "area", "patrol_date", "shift_id", "user_id", "user_name", "route_name", "create_date", "rounds_detail", "round", "start_time", "end_time", "day_offset", "stats", "total_points", "completed_points", "completion_rate", "overall_stats", "missed_points", "processedTask", "now", "computedStatus", "hasActiveRound", "allRoundsCompleted", "hasUpcomingRound", "parseTimeString", "hours", "minutes", "loadShiftInfo", "cachedShifts", "shiftIds", "map", "filter", "uniqueShiftIds", "batchSize", "batches", "batch", "promises", "res", "shiftId", "success", "error", "errorMessage", "results", "successShifts", "getShiftName", "loadSpecificShift", "shiftData", "getStatusText", "getPointCount", "searchTasks", "onStatusChange", "refresh", "loadMore", "viewTaskDetail", "url", "editTask", "confirmDelete", "task", "content", "confirmText", "confirmColor", "deleteTask", "id", "navigateToAdd", "navigateToBatchAdd", "showAdvancedFilter", "hideAdvanced<PERSON>ilter", "applyAdvancedFilters", "resetFilters", "showUserPicker", "hideUserPicker", "selectUser", "showRoutePicker", "hideRoutePicker", "selectRoute", "clearDateFilter", "clearStatus<PERSON>ilter", "clearUser<PERSON>ilter", "clearR<PERSON><PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clearAllFilters", "setSortBy", "setFilter", "onPopupChange", "loadUsers", "cachedUsers", "userId", "userid", "result", "user", "loadRoutes", "cachedRoutes", "routeMap", "buildUserMap", "getLocalDateString", "formatTimeConsistent", "getEnabledRoundsCount", "getAllRoundsCount", "getUserInfo", "userInfoStr", "uniIdUserInfo", "refreshData", "getCompletedCount", "completedPoints", "hasStats", "getTotalPointsCount", "totalPoints", "getRouteAreaName", "getShiftTime", "getSystemInfo", "systemInfo", "windowHeight", "windowWidth", "screenHeight", "screenWidth", "getUserName", "getRoundStatusText", "getRoundStatusTextByCode", "showLoading", "getStatusClass", "getFormattedRoundsInfo", "formatDateForDisplay", "formatTimeRange", "calculateRemainingTime", "calculateStartCountdown", "date1", "prevYearMonth", "nextYearMonth"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtIA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;AC+OnnB;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC,gBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;MACAC;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QAAA;QACAC;MACA;;MACA;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;MACA;MACA;MACAC;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;MACA;MAEA;MAEA;QACA;MACA;MAEA;QACA;UACA;QACA;QAEA;UACA;UAEA;YACA;UACA;UAEA;YACA;UACA;UAEA;QACA;UACAC;UACA;QACA;MACA;MAEA;IACA;IACA;IACAC;MACA;MAEAC;QACA;UACAC;QACA;MACA;MAEA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAEA;cACA;cACA;cACA;;cAEA;cACA;cACA;;cAEA;cACAC;gBACAC;gBACAC;cACA;;cAEA;cAAA;cAAA,OACA;YAAA;cAEA;cACAC,gBACA;gBACAV;gBACA;cACA,IACA;gBACAA;gBACA;cACA,IACA;gBACAA;gBACA;cACA,GACA,EAEA;cAAA;cAAA,OACAW;YAAA;cAAA;cAAA,OAGA;YAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OAGA;gBAAA;cAAA;YAAA;cAEA;cACA;;cAEA;cACA;;cAEA;cACA;cACA;cACA;cACA;cACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAGAX;cACAO;gBACAC;gBACAI;cACA;cACA;cACA;YAAA;cAAA;cAEA;cACA;cACA;cACAL;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EACAM;IAAA;IACA;IACA;MACA;MACA,gEACA;;MAEA;MACA;QACA;QACA;UACA;UACA;QACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA,qCACA;MACAd;IACA;EACA;EACAe;IACA;IACAR;EACA;EACAS;IACA;IACAT;IACA;MACAU;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QACAvE;QACAwE;QACAC;MACA;MACA;QACAd;MACA;QACAP;MACA;IACA;IAEAsB;MACA;QACA;QACA;QAEA;QACA;;QAEA;QACA;UACAf;UACA;QACA;QAEA;MACA;QACAP;QACA;MACA;IACA;IAEAuB;MACA;QACAhB;MACA;QACAP;MACA;IACA;IAEA;IACAwB;MACA;MACA;MACA;MACA;QACApC;QACAC;MACA;IACA;IAEA;IACAoC;MACA;QACA;QACA;UACAzB;UACA;QACA;QAEA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACA0B;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;QACA;MACA;QACA;QACA;QAEA;UACA;QACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACA;gBACA;gBAEAC,8DAEA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACA;;gBAEA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;kBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA9B;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA+B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA/B;gBAAA;cAAA;gBAIA;gBACAgC;gBAAA,KACAC;kBAAA;kBAAA;gBAAA;gBACAjC;gBAAA;cAAA;gBAAA,IAKA;kBAAA;kBAAA;gBAAA;gBACAO;kBACAC;kBACAI;gBACA;gBAAA;cAAA;gBAIA;;gBAEA;gBAAA,MACAoB,2CACAA;kBAAA;kBAAA;gBAAA;gBAEA;gBACA;gBACA;;gBAEA;gBACAE;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA,OAGA;kBAAA;gBAAA;cAAA;gBACA;cAAA;gBAIA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;MAEA;MACA;MAEA;MACA;MAEA;MACA;MAEA;;MAEA;MACA;QACA;QACA;QACAC;UACAC;UACAL;UACA1D;UACAgE;UACAC;QACA;MACA;;MAEA;MACA;QACA;QACAH;UACAC;UACAL;UACA1D;UACAgE;UACAC;QACA;MACA;;MAEA;MACA;MACA;QACA;QACAH;UACAC;UACAL;UACA1D;UACAgE;UACAC;QACA;MACA;MAEA;IACA;IAEA;IACAD;MACA;MACA;IACA;IAEA;IACAE;MACA;MAEA;QACA;QACA;QACA;QAEA,gDACAC,mCACAA;MACA;QACAzC;QACA;MACA;IACA;IAEA;IACA0C;MACA;IACA;IAEA;IACAC;MACA;QACA;UACA;QACA;QAEA;QACA;UACA;QACA;QAEA;MACA;QACA3C;QACA;MACA;IACA;IAEA;IACA4C;MACA;QACA;MACA;MAEA;QACA;QACA;UACA;QACA;QAEA;MACA;QACA5C;QACA;MACA;IACA;IAEA;IACA6C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAC;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACAvC;kBACAC;kBACAI;gBACA;gBAAA;cAAA;gBAAA;gBAKA;kBACA;kBACA;gBACA;kBACA;gBACA;gBAEA;;gBAEA;gBACAsB;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA,OAGA;kBAAA;gBAAA;cAAA;gBACA;cAAA;gBAGA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAlC;gBACAO;kBACAC;kBACAI;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAmC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAD;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAvC;kBACAC;kBACAI;gBACA;gBAAA;cAAA;gBAAA;gBAKA;kBACA;kBACA;gBACA;kBACA;gBACA;gBACA;;gBAEA;gBACAsB;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA,OAGA;kBAAA;gBAAA;cAAA;gBACA;cAAA;gBAGA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAlC;gBACAO;kBACAC;kBACAI;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAoC;MAAA;MACA;MACAC;QACA;UACA;UACA;;UAEA;UACA;UACA/C;YACA;cACAgD;YACA;UACA;;UAEA;UACA;UACA;YACA;UACA;;UAEA;UACA;UACAC;YACA;cACAC;YACA;UACA;;UAEA;UACA;;UAEA;UACA;YACA;UACA;QACA;UACApD;QACA;MACA;IACA;IAEA;IACAqD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBAAA,IAEAA;kBAAA;kBAAA;gBAAA;gBACAtD;gBAAA;cAAA;gBAAA;gBAAA,OAKA;cAAA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAuD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,KAIA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBAAA;gBAGA;gBACA7E;gBACA8E;gBACAC;gBAEA;kBACA;oBACAA;kBACA;oBACAA;kBACA;gBACA;kBACAA;oBAAA3E;sBAAA2E;oBAAA;kBAAA;gBACA;;gBAEA;gBACA,oCACAA,2BACAC,2CACAD,6CACAA,0CACAA,0CACAA,kDACAA;;gBAEA;gBAAA,mBACAvB;gBACArE,uDACA;gBACAC,kHAEA;gBACA6F;kBACAA;oBACA9F;oBACAC;oBACA8F;oBAAA;oBACAC;oBAAA;oBACA;oBACAC;oBAAA;oBACAC;oBAAA;oBACA;oBACAC;kBACA;gBACA;gBAAA;gBAAA,OAEAC;kBACAC;kBACAC;kBACAvH;gBACA;cAAA;gBAJAwH;gBAMA;kBACAC;kBACAC,kCAEA;kBACA;kBACA;kBACA;kBACA;kBACAC,iDAIA;kBACA;oBACAvE;oBACAO;sBACAC;sBACAI;sBACA4D;oBACA;kBACA;;kBAEA;kBACA;kBACA;oBAAAC;oBAAAH;kBAAA;kBACA;;kBAEA;kBACA;oBACA;kBACA;gBACA;kBACA/D;oBACAC;oBACAI;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAZ;gBACAO;kBACAC;kBACAI;gBACA;cAAA;gBAAA;gBAEA;gBACA;kBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA8D;MAAA;MACA;;MAEA;MACA;MACA;MAEA;QACA;QACA;UACAC;UACAT;UACAL;UACAe;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACA;UACAC;YAAA;cACAC;cAAA;cACAlB;cAAA;cACAL;cAAA;cACAwB;cAAA;cACAC;cAAA;cACAd;cAAA;cACAe;cAAA;cACA;cACAC;gBACAC;gBACAC;gBACAC;cACA;YACA;UAAA;UACA;UACAC;YACAH;YACAC;YACAC;YACAE;UACA;QACA;;QAEA;QACA;QACA;UACA;UACA;UACA;UAEAC;YACA;cACA;cACA;cACA;;cAEA;cACA;cAEA;gBACA;gBACA,mCACAA,qDACAC;kBACAC;gBACA;kBACAA;gBACA;kBACAA;gBACA;kBACAA;gBACA;cACA;;cAEA;cACAZ;;cAEA;cACA;gBACAa;gBACAC;cACA;gBACAC;gBACAD;cACA;gBACAA;cACA;;cAEA;cACA;gBACAd;cACA;YAEA;cACApF;YACA;YAEA;UACA;;UAEA;UACA;YACA8F;UACA;YACA;YACA;cACAA;YACA;cACAA;YACA;cACAA;YACA;cACAA;YACA;UACA;;UAEA;UACAA;YACA;YACA;cACA;YACA;YACA;cACA;YACA;YACA;YACA;UACA;QACA;QAEA;MACA;IACA;IAEA;IACAM;MACA;QACA;QACA;UACA;UACA;YACAC;YACAC;UACA;QACA;;QAEA;QACA;QACA;UACAD;UACAC;QACA;MACA;QACA;QACAtG;QACA;UAAAqG;UAAAC;QAAA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAC;gBACA;kBACA;kBACAtG;gBACA;gBAEAuG,uCACAC;kBAAA;gBAAA,GACAC;kBAAA;gBAAA,IAEA;gBACAC;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACAC;gBACAC;gBAEA;kBACAA;gBACA;gBAAA,oBAEAA;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAAC;gBACAC;kBAAA;oBAAA;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;4BAAA;4BAAA,OAEA/C;8BACAC;8BACAC;8BACAvH;gCACA+G;kCAAAmB;gCAAA;8BACA;4BACA;0BAAA;4BANAmC;4BAAA,MAQAA;8BAAA;8BAAA;4BAAA;4BACA;4BAAA,kCACA;8BAAAC;8BAAAC;8BAAAvK;4BAAA;0BAAA;4BAEA;4BACA;8BACAwK;8BACAC;8BACAnD;4BACA;4BAAA,kCACA;8BAAAgD;8BAAAC;8BAAAC;4BAAA;0BAAA;4BAAA;4BAAA;0BAAA;4BAAA;4BAAA;4BAGApH;4BACA;8BACAoH;8BACAC;8BACAnD;4BACA;4BAAA,kCACA;8BAAAgD;8BAAAC;8BAAAC;4BAAA;0BAAA;0BAAA;4BAAA;wBAAA;sBAAA;oBAAA;kBAAA,CAEA;kBAAA;oBAAA;kBAAA;gBAAA,MAEA;gBAAA;gBAAA,OACAzG;cAAA;gBAAA2G;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAGA;gBACAC;gBACArH;kBACA;oBACAqH;kBACA;gBACA;gBAEA;kBACA;gBACA;;gBAEA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACAP;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAjD;kBACAC;kBACAC;kBACAvH;oBACA+G;sBAAAmB;oBAAA;kBACA;gBACA;cAAA;gBANAmC;gBAQA;kBACA;kBACAS;kBACA;oBACAA;kBACA;kBACA;kBACA;kBACA;gBACA;kBACA1H;kBACA;kBACA;oBACAoH;oBACAC;oBACA;oBACAnD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAlE;gBACA;gBACA;kBACAoH;kBACAC;kBACA;kBACAnD;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAyD;MACA9D;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACA+D;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;;MAEA;QACA;QACA;;QAEA;QACA;MACA;QACA/H;MACA;QACA;QACA;MACA;IACA;IAEA;IACAgI;MACA;MACA;IAAA,CACA;IAEA;IACAC;MACA1H;QACA2H;MACA;IACA;IAEA;IACAC;MACA5H;QACA2H;MACA;IACA;IAEA;IACAE;MAAA;MACA;MACA,sIACAC,2DACAA,yEACAA,iHACA;MAEA9H;QACAC;QACA8H;QACAC;QACAC;QACArB;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAsB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAGAxE;kBACAC;kBACAC;kBACAvH;oBAAA+G;sBAAA+E;oBAAA;kBAAA;gBACA;cAAA;gBAJAzB;gBAMA;kBACA1G;oBACAC;oBACAI;kBACA;;kBAEA;kBACAV;oBACA,gEACAyG;sBAAA;oBAAA;kBACA;;kBAEA;kBACA,0DACAA;oBAAA;kBAAA;;kBAEA;kBACA;gBAEA;kBACApG;oBACAC;oBACAI;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAZ;gBACAO;kBACAC;kBACAI;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA+H;MACApI;QACA2H;MACA;IACA;IAEA;IACAU;MACArI;QACA2H;MACA;IACA;IAEA;IACAW;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACAT;QACAxE;MACA;MACA;MACA;IACA;IAEA;IACAkF;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACAZ;QACAxE;MACA;MACA;MACA;IACA;IAEA;IACAqF;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MAAA,CACA;QACA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA,mCACAA;cAAA;gBAGAvL;gBACAwL;gBAAA,IAEAA;kBAAA;kBAAA;gBAAA;gBAAA,mCACA;cAAA;gBAAA;gBAAA,OAGAjG;kBACAC;kBACAC;kBACAvH;oBACAuN;oBACAvG;kBACA;gBACA;cAAA;gBAPAwG;gBAAA,MASAA;kBAAA;kBAAA;gBAAA;gBACA5K,gCAEA;gBACA;gBAEAA;kBACA;kBACA,oDACA6K;oBACA;oBACAnG;kBAAA,EACA;kBACA;gBACA;gBAEA;;gBAEA;gBACA;kBACAjG;kBACAuB;gBACA;gBAAA,mCAEAA;cAAA;gBAEAQ;gBAAA,mCACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBAAA,mCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAsK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA,mCACAA;cAAA;gBAAA;gBAAA,OAGAtG;kBACAC;kBACAC;gBACA;cAAA;gBAHAiG;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACA3K,iCAEA;gBACA;gBAEAA;kBACA;gBACA;gBAEA;;gBAEA;gBACA;kBACA+K;kBACA/K;gBACA;gBAAA,mCAEAA;cAAA;gBAEAO;gBAAA,mCACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBAAA,mCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAyK;MAAA;MACA;MAEAjL;QACA;MACA;IACA;IAEA;IACAkL;MACA;MAEA;QACA;QACA;UACA;UACA;YACA;UACA;UACA1I;QACA;QAEA;QACA;QACA;QAEA;MACA;QACAhC;QACA;MACA;IACA;IAEA;IACA2K;MACA;MAEA;QACA;QACA;UACA;UACA;UACA;UACA;QACA;;QAEA;QACA;QACA;UACA;UACA;YACA;UACA;UACA;QACA;QAEA;QACA;QACA;MACA;QACA3K;QACA;MACA;IACA;IAEA;IACA4K;MAAA;MACA;IACA;IAEA;IACAC;MAAA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAEA;;gBAEA;gBACAvH;gBACA;kBACAC,oFAEA;kBACA;oBACA;oBACA;kBACA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAzD;gBACAO;cAAA;gBAAA;gBAAA,OAIA0D;kBACAC;kBACAC;kBACAvH;oBACA+G;kBACA;gBACA;cAAA;gBANAsD;gBAQA;kBACA;kBACA1G;gBACA;kBACAyK;kBACA;oBACA;sBACA;oBACA;sBACAhL;oBACA;kBACA;gBACA;gBAEA;kBACAO;oBACAC;oBACAI;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAZ;gBACAO;kBACAC;kBACAI;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAqK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAEA;gBACA3H;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;kBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAtD;cAAA;gBAAA;gBAEA;gBACA;gBACA;gBACAO;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAGAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA2K;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA7C;UACA;YACA8C;YACAC;UACA;QACA;QACA;UACA;QACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;UACA;QACA;;QAEA;QACA;QACA;QACAhD;UACA;YACAiD;YACAF;UACA;QACA;QACA;UACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAG;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;UACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA,uCACAC;UACAC;UACAC;UACAC;UACAC;QAAA;MAEA;QACA9L;QACA;MACA;IACA;IAEA;IACA+L;MACA;QACA;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;UACA;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACApI;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAqI;MACA;IACA;IAEAC;MACA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;;MAEA;IACA;IAEA;IACAC;MACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QAAA;MAAA;;MAEA;MACA;QAAA;MAAA;;MAEA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAIA;IACAC;MACA;MACA;QACA;MACA;QACArM;QACA;MACA;IACA;IAEA;IACAsM;MACA;MAEA;QACA;QACA,iDACA,6BACA;QAEA;UACAtM;UACA;QACA;QAEA;QACA;QACA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACAuM;MACA;MAEA;QACA;QACA;;QAEA;QACA;UACA;QACA;QAEA;QACA;QACA;QACA;QAEA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;QACAvM;QACA;MACA;IACA;IAEA;IACAwM;MACA;MAEA;QACA;QACA;;QAEA;QACA;UACA;QACA;QAEA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;UACA;YACA;UACA;YACA;UACA;QACA;UACA;YACA;UACA;YACA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;QACAxM;QACA;MACA;IACA;EAAA,2EAGAyM;IACA;IAEA;MACA;MACA;MACA;MAEA,gDACAhK,mCACAA;IACA;MACAzC;MACA;IACA;EACA,oGAMA;IAAA;IACA;MACAiB;IACA;;IAEA;IACA;MACA;QACA;;QAEA;QACA;UACA;YACA;YACA;cACA;cACA;YACA;;YAEA;YACA;cACA;cACA;YACA;;YAEA;UACA;QACA;;QAEA;QACA;UACA;UACA;QACA;;QAEA;QACA;UACA;YACA;YACA;;YAEA;YACA;YACA;cACA;YACA;;YAEA;YACA;YACA;cACA;YACA;YAEA;UACA;QACA;QAEA;UACA;UACA;UACA;UACA;UACA;YACA;UACA;QACA;MACA;IACA;EACA,sGAEA;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAEA1D;cACAsF;cACAE;cAEA2J;cACAC,iDAEA;cACAjM;cAEA;gBACAA;kBACAV;gBACA;cACA;cAEA;gBACAU;kBACAV;gBACA;cACA;;cAEA;cAAA;cAAA,OACAW;YAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAX;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;AAEA;AAAA,2B;;;;;;;;;;;;;ACx5EA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/patrol_pkg/task/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/patrol_pkg/task/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=3bf106d6&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=3bf106d6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3bf106d6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/patrol_pkg/task/index.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=3bf106d6&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-load-more/components/uni-load-more/uni-load-more\" */ \"@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.calendarDays, function (day, index) {\n    var $orig = _vm.__get_orig(day)\n    var m0 = _vm.isSelectedDate(day.date)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var m1 =\n    _vm.uniIDHasRole(\"reviser\") ||\n    _vm.uniIDHasRole(\"supervisor\") ||\n    _vm.uniIDHasRole(\"PM\") ||\n    _vm.uniIDHasRole(\"GM\") ||\n    _vm.uniIDHasRole(\"admin\")\n  var g0 = _vm.filteredTasks.length\n  var g1 = !!_vm.initialDataLoaded\n    ? _vm.initialDataLoaded && _vm.filteredTasks.length === 0\n    : null\n  var l2 =\n    !!_vm.initialDataLoaded && !g1\n      ? _vm.__map(_vm.filteredTasks, function (task, index) {\n          var $orig = _vm.__get_orig(task)\n          var m2 =\n            task.patrol_date || _vm.formatDateForDisplay(task.create_date)\n          var m3 = _vm.getStatusText(task.status)\n          var m4 = !(task.route && task.route.area)\n            ? _vm.getRouteAreaName(task)\n            : null\n          var m5 = task.shift_id ? _vm.getShiftName(task.shift_id) : null\n          var m6 = _vm.getUserName(task)\n          var m7 = _vm.getFormattedRoundsInfo(task)\n          var g2 = task.rounds_detail && task.rounds_detail.length > 0\n          var l1 = g2\n            ? _vm.__map(task.rounds_detail, function (round, index) {\n                var $orig = _vm.__get_orig(round)\n                var g3 = task.rounds_detail.length\n                var m8 = _vm.formatTimeRange(round)\n                var m9 =\n                  round.status === 1 ? _vm.calculateRemainingTime(round) : null\n                var m10 =\n                  !(round.status === 1) && round.status === 0\n                    ? _vm.calculateStartCountdown(round)\n                    : null\n                var m11 = _vm.getRoundStatusTextByCode(round.status || 0)\n                return {\n                  $orig: $orig,\n                  g3: g3,\n                  m8: m8,\n                  m9: m9,\n                  m10: m10,\n                  m11: m11,\n                }\n              })\n            : null\n          var m12 =\n            _vm.uniIDHasRole(\"reviser\") ||\n            _vm.uniIDHasRole(\"supervisor\") ||\n            _vm.uniIDHasRole(\"PM\") ||\n            _vm.uniIDHasRole(\"GM\") ||\n            _vm.uniIDHasRole(\"admin\")\n          return {\n            $orig: $orig,\n            m2: m2,\n            m3: m3,\n            m4: m4,\n            m5: m5,\n            m6: m6,\n            m7: m7,\n            g2: g2,\n            l1: l1,\n            m12: m12,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m1: m1,\n        g0: g0,\n        g1: g1,\n        l2: l2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page-container\">\n\t\t<!-- 骨架屏：页面立即显示，数据异步加载 -->\n\t\t<view class=\"task-container\">\n\t\t\t<!-- 替换下拉刷新指示器 -->\n\t\t\t<view class=\"refresh-box\" v-if=\"isRefreshing\">\n\t\t\t\t<view class=\"refresh-icon\">\n\t\t\t\t\t<uni-icons type=\"refresh\" size=\"24\" color=\"#3688FF\" :class=\"{ 'refreshing': isRefreshing }\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"refresh-text\">正在刷新...</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 日历视图 -->\n\t\t\t<view class=\"calendar-container\">\n\t\t\t\t<!-- 日历视图 -->\n\t\t\t\t<view class=\"calendar-view\">\n\t\t\t\t\t<view class=\"calendar-header\">\n\t\t\t\t\t\t<view class=\"month-selector\">\n\t\t\t\t\t\t\t<view class=\"arrow-btn\" @click=\"prevMonth\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"left\" size=\"18\" color=\"#666\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"current-month\">{{currentYear}}年{{currentMonth + 1}}月</text>\n\t\t\t\t\t\t\t<view class=\"arrow-btn\" @click=\"nextMonth\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"18\" color=\"#666\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 星期标题 -->\n\t\t\t\t\t<view class=\"weekday-header\">\n\t\t\t\t\t\t<text class=\"weekday-item\" v-for=\"(day, index) in weekdays\" :key=\"index\">{{day}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 日期网格 -->\n\t\t\t\t\t<view class=\"days-grid\">\n\t\t\t\t\t\t<view class=\"day-cell\" \n\t\t\t\t\t\t\tv-for=\"(day, index) in calendarDays\" \n\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t:class=\"{\n\t\t\t\t\t\t\t\t'other-month': !day.currentMonth,\n\t\t\t\t\t\t\t\t'today': day.isToday,\n\t\t\t\t\t\t\t\t'has-task': day.hasTask,\n\t\t\t\t\t\t\t\t'selected': isSelectedDate(day.date)\n\t\t\t\t\t\t\t}\"\n\t\t\t\t\t\t\t@click=\"selectDate(day)\">\n\t\t\t\t\t\t\t<text class=\"day-number\">{{day.day}}</text>\n\t\t\t\t\t\t\t<view class=\"task-indicator\" v-if=\"day.hasTask\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 单任务添加批量任务添加按钮区域 -->\n\t\t\t<view class=\"action-buttons\" v-if=\"uniIDHasRole('reviser') || uniIDHasRole('supervisor') || uniIDHasRole('PM') || uniIDHasRole('GM') || uniIDHasRole('admin')\">\n\t\t\t\t<view class=\"add-btn\" @click=\"navigateToAdd\">\n\t\t\t\t\t<uni-icons type=\"plus\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t<text>单个添加</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"add-btn batch\" @click=\"navigateToBatchAdd\">\n\t\t\t\t\t<uni-icons type=\"paperplane\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t\t\t<text>批量添加</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 任务列表 -->\n\t\t\t<view class=\"task-list-section\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text>{{formatSelectedDate}} 任务列表</text>\n\t\t\t\t\t<view class=\"title-actions\">\n\t\t\t\t\t\t<text class=\"task-count\">共 {{filteredTasks.length}} 个任务</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"task-items\">\n\t\t\t\t\t<!-- 加载中的骨架屏 -->\n\t\t\t\t\t<view v-if=\"!initialDataLoaded\" class=\"skeleton-list\">\n\t\t\t\t\t\t<view class=\"skeleton-item\" v-for=\"i in 3\" :key=\"i\">\n\t\t\t\t\t\t\t<view class=\"skeleton-header\">\n\t\t\t\t\t\t\t\t<view class=\"skeleton-line skeleton-date\"></view>\n\t\t\t\t\t\t\t\t<view class=\"skeleton-line skeleton-status\"></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"skeleton-content\">\n\t\t\t\t\t\t\t\t<view class=\"skeleton-line skeleton-title\"></view>\n\t\t\t\t\t\t\t\t<view class=\"skeleton-line skeleton-info\"></view>\n\t\t\t\t\t\t\t\t<view class=\"skeleton-line skeleton-progress\"></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 空状态 -->\n\t\t\t\t\t<view v-else-if=\"initialDataLoaded && filteredTasks.length === 0\" class=\"empty-list\">\n\t\t\t\t\t\t<p-empty-state type=\"task\" text=\"暂无任务\"></p-empty-state>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 任务列表 -->\n\t\t\t\t\t<view v-else class=\"task-item\" \n\t\t\t\t\t\tv-for=\"(task, index) in filteredTasks\" \n\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t@click=\"viewTaskDetail(task)\">\n\t\t\t\t\t\t<!-- 任务头部：状态和日期 -->\n\t\t\t\t\t\t<view class=\"task-time\">\n\t\t\t\t\t\t\t<view class=\"task-date-container\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"16\" color=\"#666\"></uni-icons>\n\t\t\t\t\t\t\t\t<text class=\"task-date\">{{task.patrol_date || formatDateForDisplay(task.create_date)}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view :class=\"['status-tag', `status-${task.status}`]\">\n\t\t\t\t\t\t\t\t{{ getStatusText(task.status) }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 任务内容 -->\n\t\t\t\t\t\t<view class=\"task-content\">\n\t\t\t\t\t\t\t<!-- 任务详细信息 -->\n\t\t\t\t\t\t\t<view class=\"task-info-grid\">\n\t\t\t\t\t\t\t\t<!-- 区域信息 -->\n\t\t\t\t\t\t\t\t<view class=\"info-block\">\n\t\t\t\t\t\t\t\t\t<view class=\"info-icon area-icon\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"location\" size=\"16\" color=\"#666\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"info-content\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"info-label\">区域:</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"info-value\">{{ task.route && task.route.area ? task.route.area : getRouteAreaName(task) }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<!-- 班次信息 -->\n\t\t\t\t\t\t\t\t<view class=\"info-block\">\n\t\t\t\t\t\t\t\t\t<view class=\"info-icon shift-icon\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"16\" color=\"#666\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"info-content\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"info-label\">班次:</text>\n\t\t\t\t\t\t\t\t\t\t<view class=\"info-value\">\n\t\t\t\t\t\t\t\t\t\t\t<text>{{ task.shift_id ? getShiftName(task.shift_id) : '未设置' }}</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<!-- 用户信息 -->\n\t\t\t\t\t\t\t\t<view class=\"info-block\">\n\t\t\t\t\t\t\t\t\t<view class=\"info-icon user-icon\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"staff\" size=\"16\" color=\"#666\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"info-content\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"info-label\">执行人:</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"info-value\">{{ getUserName(task) }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<!-- 点位信息 -->\n\t\t\t\t\t\t\t\t<view class=\"info-block\">\n\t\t\t\t\t\t\t\t\t<view class=\"info-icon points-icon\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"checkbox\" size=\"16\" color=\"#666\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"info-content\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"info-label\">轮次:</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"info-value\">{{ getFormattedRoundsInfo(task) }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 进度条 -->\n\t\t\t\t\t\t\t<view class=\"progress-container\">\n\t\t\t\t\t\t\t\t<view class=\"progress-header\">\n\t\t\t\t\t\t\t\t\t<text class=\"progress-title\">轮次进度</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<!-- 轮次进度显示 -->\n\t\t\t\t\t\t\t\t<view class=\"rounds-progress\" v-if=\"task.rounds_detail && task.rounds_detail.length > 0\">\n\t\t\t\t\t\t\t\t\t<view class=\"round-item\" v-for=\"(round, index) in task.rounds_detail\" :key=\"index\" \n\t\t\t\t\t\t\t\t\t\t:class=\"{'last-item': index === task.rounds_detail.length - 1}\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"round-info\">\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"round-details\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"round-name-time\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"round-name\">{{ round.name || '轮次' + round.round }}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"round-time\">{{ formatTimeRange(round) }}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"round.day_offset && round.day_offset > 0\" class=\"round-nextday\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ round.day_offset === 1 ? ' 次日' : ' +' + round.day_offset + '天' }}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"round-duration\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text>有效时长: {{ round.duration || 60 }}分钟</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<!-- 进行中：显示剩余时间 -->\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"round.status === 1\" class=\"time-remaining\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t(剩余{{ calculateRemainingTime(round) }})\n\t\t\t\t\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<!-- 🔥 新增：未开始时显示开始倒计时 -->\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text v-else-if=\"round.status === 0\" class=\"time-countdown\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t({{ calculateStartCountdown(round) }})\n\t\t\t\t\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"round-status-badge\" :class=\"{\n\t\t\t\t\t\t\t\t\t\t\t\t'waiting': round.status === 0,\n\t\t\t\t\t\t\t\t\t\t\t\t'finished': round.status === 2,\n\t\t\t\t\t\t\t\t\t\t\t\t'expired': round.status === 3,\n\t\t\t\t\t\t\t\t\t\t\t\t'canceled': round.status === 4,\n\t\t\t\t\t\t\t\t\t\t\t\t'ongoing': round.status === 1\n\t\t\t\t\t\t\t\t\t\t\t}\">\n\t\t\t\t\t\t\t\t\t\t\t\t{{ getRoundStatusTextByCode(round.status || 0) }}\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<!-- 当没有轮次信息时的提示 -->\n\t\t\t\t\t\t\t\t<view class=\"no-rounds-tip\" v-else>\n\t\t\t\t\t\t\t\t\t<text>暂无轮次信息</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 添加编辑和删除按钮 -->\n\t\t\t\t\t\t\t<view class=\"task-actions\" v-if=\"uniIDHasRole('reviser') || uniIDHasRole('supervisor') || uniIDHasRole('PM') || uniIDHasRole('GM') || uniIDHasRole('admin')\">\n\t\t\t\t\t\t\t\t<view class=\"action-btn edit-btn\" @click.stop=\"editTask(task)\">\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"compose\" size=\"16\" color=\"#3688FF\"></uni-icons>\n\t\t\t\t\t\t\t\t\t<text>编辑</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"action-btn delete-btn\" @click.stop=\"confirmDelete(task)\">\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"trash\" size=\"16\" color=\"#FF4D4F\"></uni-icons>\n\t\t\t\t\t\t\t\t\t<text>删除</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 加载中 -->\n\t\t\t<uni-popup ref=\"loadingPopup\" type=\"center\" :mask-click=\"false\">\n\t\t\t\t<view class=\"loading-box\">\n\t\t\t\t\t<uni-load-more status=\"loading\" :content-text=\"{ contentdown: '加载中...' }\"></uni-load-more>\n\t\t\t\t</view>\n\t\t\t</uni-popup>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport PatrolApi from '@/utils/patrol-api.js';\nimport { formatDate, formatTime } from '@/utils/date.js';\nimport PEmptyState from '@/components/p-empty-state/p-empty-state.vue';\nimport { getCacheKey, CACHE_KEYS } from '@/utils/cache.js';\n\nexport default {\n\tcomponents: {\n\t\tPEmptyState\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tpage: 1,\n\t\t\tlimit: 10,\n\t\t\t// 修改数据结构以支持按月份缓存\n\t\t\tcachedTasks: {}, // 按月份缓存任务数据，格式：{'2024-03': [...tasks]}\n\t\t\tcurrentDisplayTasks: [], // 当前显示的任务（根据选中日期和权限筛选）\n\t\t\thasMore: true,\n\t\t\tloading: false,\n\t\t\tisRefreshing: false,\n\t\t\tsearchText: '',\n\t\t\tdayOffset: 0,  // 用于查看过去或未来的任务\n\t\t\tcurrentDateOriginal: '', // 原始当前日期\n\t\t\tcurrentDate: '', // 格式化后的日期字符串，用于API查询\n\t\t\tstatusFilter: ['0', '1', '2', '3', '4'], // 默认查看所有状态的任务 \n\t\t\tshowFilters: false,\n\t\t\tisInitialLoading: true, // 添加初始加载标志\n\t\t\tshiftInfo: {}, // 缓存班次信息\n\t\t\troutePointCounts: {}, // 缓存路线点位数量\n\t\t\tstartDate: '',\n\t\t\tendDate: '',\n\t\t\tsortBy: 'create_time',\n\t\t\tsortDirection: 'desc',\n\t\t\tuserMap: {},\n\t\t\tpageReady: true, // 默认为true，立即显示页面\n\t\t\tinitialDataLoaded: false, // 标记初始数据是否加载完成\n\t\t\t// 日历相关数据\n\t\t\tweekdays: ['日', '一', '二', '三', '四', '五', '六'],\n\t\t\tcurrentYear: new Date().getFullYear(),\n\t\t\tcurrentMonth: new Date().getMonth(),\n\t\t\tselectedDate: new Date(),\n\t\t\tcalendarDays: [],\n\t\t\ttaskDates: new Set(), // 存储有任务的日期\n\t\t\tuserInfo: null,\n\t\t\tfilterStatus: -1,\n\t\t\tfilterOptions: [\n\t\t\t\t{ label: '全部', value: -1 },\n\t\t\t\t{ label: '未开始', value: 0 },\n\t\t\t\t{ label: '进行中', value: 1 },\n\t\t\t\t{ label: '已完成', value: 2 },\n\t\t\t\t{ label: '已超时', value: 3 },\n\t\t\t\t{ label: '已取消', value: 4 }\n\t\t\t],\n\t\t\ttimer: null,\n\t\t\tlastUpdateTime: null,\n\t\t\t// 新增：月份加载管理\n\t\t\tloadedMonths: {}, // 记录已加载的月份，改为对象以支持Vue响应式\n\t\t\tmonthTaskCounts: {}, // 记录每个月份的任务总数\n\t\t\tdateRange: {\n\t\t\t\tstart: null, // 可查看的最早日期\n\t\t\t\tend: null    // 可查看的最晚日期\n\t\t\t},\n\t\t\t// 用户权限信息\n\t\t\tisAdmin: false,\n\t\t\t\n\t\t\t// 缓存相关\n\t\t\tcacheKeys: {\n\t\t\t\tusers: 'patrol_users_cache',\n\t\t\t\troutes: 'patrol_routes_cache',\n\t\t\t\tshifts: 'patrol_shifts_cache'\n\t\t\t},\n\t\t\tcacheExpiry: 30 * 60 * 1000, // 30分钟缓存过期时间\n\t\t};\n\t},\n\tcomputed: {\n\t\t// 格式化选中日期显示\n\t\tformatSelectedDate() {\n\t\t\tconst date = this.selectedDate;\n\t\t\tconst today = new Date();\n\t\t\tconst tomorrow = new Date(today);\n\t\t\ttomorrow.setDate(tomorrow.getDate() + 1);\n\t\t\t\n\t\t\t// 判断是否是今天\n\t\t\tif (this.isSameDay(date, today)) {\n\t\t\t\treturn '今天';\n\t\t\t}\n\t\t\t\n\t\t\t// 判断是否是明天\n\t\t\tif (this.isSameDay(date, tomorrow)) {\n\t\t\t\treturn '明天';\n\t\t\t}\n\t\t\t\n\t\t\t// 标准格式\n\t\t\treturn `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;\n\t\t},\n\t\t\t// 根据选中日期和权限筛选任务\n\t\tfilteredTasks() {\n\t\tif(!this.currentDisplayTasks || this.currentDisplayTasks.length === 0) {\n\t\t\treturn [];\n\t\t}\n\t\t\n\t\tconst dateStr = this.formatDateForCompare(this.selectedDate);\n\t\t\n\t\tif(!dateStr) {\n\t\t\treturn [];\n\t\t}\n\t\t\n\t\tconst filtered = this.currentDisplayTasks.filter(task => {\n\t\t\tif(!task.patrol_date) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst taskDate = this.formatDateForCompare(new Date(task.patrol_date));\n\t\t\t\t\n\t\t\t\tif (taskDate !== dateStr) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (this.filterStatus !== -1 && parseInt(task.status) !== parseInt(this.filterStatus)) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn true;\n\t\t\t} catch(e) {\n\t\t\t\tconsole.error('❌ 任务过滤错误:', e);\n\t\t\t\treturn false;\n\t\t\t}\n\t\t});\n\t\t\n\t\treturn filtered;\n\t},\n\t\t// 获取所有缓存的任务（用于日历小点点显示）\n\t\tallCachedTasks() {\n\t\t\tconst allTasks = [];\n\t\t\t\n\t\t\tObject.values(this.cachedTasks).forEach(monthTasks => {\n\t\t\t\tif (Array.isArray(monthTasks)) {\n\t\t\t\t\tallTasks.push(...monthTasks);\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\treturn allTasks;\n\t\t},\n\t\thasActiveFilters() {\n\t\t\treturn this.filterDate || this.filterStatus !== -1 || this.startDate || this.endDate || this.sortBy || this.sortDirection;\n\t\t},\n\t\t// 计算属性：用于API筛选的日期格式\n\t\tformattedDateForAPI() {\n\t\t\treturn this.formatDateForCompare(this.selectedDate);\n\t\t}\n\t},\n\tasync onLoad() {\n\t\ttry {\n\t\t\t// 1. 立即显示页面骨架，提升用户体验\n\t\t\tthis.pageReady = true;\n\t\t\t// 确保初始状态为加载中，显示骨架屏\n\t\t\tthis.initialDataLoaded = false;\n\t\t\t\n\t\t\t// 2. 同步初始化（很快）\n\t\t\tthis.initDateRange();\n\t\t\tthis.initCalendar();\n\t\t\t\n\t\t\t// 3. 显示加载状态\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '加载中...',\n\t\t\t\tmask: true\n\t\t\t});\n\t\t\t\n\t\t\t// 4. 优先加载用户信息（其他功能依赖此数据）\n\t\t\tawait this.getUserInfo();\n\t\t\t\n\t\t\t// 5. 并行加载所有数据，而不是串行等待\n\t\t\tconst loadPromises = [\n\t\t\t\tthis.loadCurrentMonthTasks().catch(e => {\n\t\t\t\t\tconsole.error('❌ loadCurrentMonthTasks 失败:', e);\n\t\t\t\t\t// 即使任务加载失败，也不影响其他功能\n\t\t\t\t}),\n\t\t\t\tthis.loadUsers().catch(e => {\n\t\t\t\t\tconsole.error('❌ 加载用户失败:', e);\n\t\t\t\t\t// 用户数据加载失败不影响主要功能\n\t\t\t\t}),\n\t\t\t\tthis.loadRoutes().catch(e => {\n\t\t\t\t\tconsole.error('❌ 加载路线失败:', e);\n\t\t\t\t\t// 路线数据加载失败不影响主要功能\n\t\t\t\t})\n\t\t\t];\n\t\t\t\n\t\t\t// 6. 等待所有数据加载完成\n\t\t\tawait Promise.all(loadPromises);\n\t\t\t\n\t\t\t// 7. 确保所有数据处理完成后再标记加载完成\n\t\t\tawait this.$nextTick();\n\t\t\tawait this.$nextTick(); // 双重nextTick确保DOM完全更新\n\t\t\t\n\t\t\t// 8. 添加最小延迟，确保用户能看到骨架屏效果\n\t\t\tawait new Promise(resolve => setTimeout(resolve, 300));\n\t\t\t\n\t\t\t// 9. 标记初始数据加载完成\n\t\t\tthis.initialDataLoaded = true;\n\t\t\t\n\t\t\t// 10. 启动定时更新\n\t\t\tthis.startTimeUpdateTimer();\n\t\t\t\n\t\t\t// 11. 预加载相邻月份（提升切换体验）\n\t\t\t// 🔥 RU优化：禁用预加载，采用按需加载策略\n\t\t\t// 预计节省56.7%的RU消耗（假设70%用户只查看当前月份）\n\t\t\t// 权衡：初次切换月份时需要等待加载，但大幅减少不必要的数据库查询\n\t\t\t// setTimeout(() => {\n\t\t\t// \tthis.preloadAdjacentMonths();\n\t\t\t// }, 1000);\n\t\t\t\n\t\t} catch (e) {\n\t\t\tconsole.error('❌ onLoad 失败:', e);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '加载失败，请重试',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t\t// 即使失败也要标记加载完成，避免一直显示骨架屏\n\t\t\tthis.initialDataLoaded = true;\n\t\t} finally {\n\t\t\t// 确保重置所有加载状态\n\t\t\tthis.isRefreshing = false;\n\t\t\tthis.loading = false;\n\t\t\tuni.hideLoading();\n\t\t}\n\t},\n\tonShow() {\n\t\t// 每次页面显示时检查是否需要刷新数据\n\t\tif (this.pageReady && this.initialDataLoaded) {\n\t\t\tconst currentYearMonth = this.getYearMonth(this.selectedDate);\n\t\t\tconst hasCurrentMonthData = this.cachedTasks[currentYearMonth] && \n\t\t\t\tthis.cachedTasks[currentYearMonth].length > 0;\n\t\t\t\n\t\t\t// 简化逻辑：只检查当前月份是否有数据\n\t\t\tif (!hasCurrentMonthData) {\n\t\t\t\t// 当前月份没有数据，需要加载\n\t\t\t\tthis.loadMonthTasks(currentYearMonth).then(() => {\n\t\t\t\t\tthis.updateCurrentDisplayTasks(true); // 新数据需要重新加载班次信息\n\t\t\t\t\tthis.extractTaskDates();\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\t// 有数据，只需要轻量级更新显示\n\t\t\t\tthis.updateCurrentDisplayTasks(false); // 缓存数据不需要重新加载班次信息\n\t\t\t}\n\t\t}\n\t},\n\tonPullDownRefresh() {\n\t\tthis.refreshData().then(() => {\n\t\t}).catch(err => {\n\t\t\tconsole.error('下拉刷新出错:', err);\n\t\t});\n\t},\n\tonReady() {\n\t\t// 注册任务刷新事件监听\n\t\tuni.$on('refresh-task-list', this.refreshList);\n\t},\n\tonUnload() {\n\t\t// 移除事件监听，避免内存泄漏\n\t\tuni.$off('refresh-task-list', this.refreshList);\n\t\tif (this.timer) {\n\t\t\tclearInterval(this.timer);\n\t\t\tthis.timer = null;\n\t\t}\n\t},\n\tmethods: {\n\t\t// 缓存工具方法\n\t\tsetCache(key, data) {\n\t\t\tconst cacheData = {\n\t\t\t\tdata: data,\n\t\t\t\ttimestamp: Date.now(),\n\t\t\t\texpiry: this.cacheExpiry\n\t\t\t};\n\t\t\ttry {\n\t\t\t\tuni.setStorageSync(key, JSON.stringify(cacheData));\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('设置缓存失败:', e);\n\t\t\t}\n\t\t},\n\t\t\n\t\tgetCache(key) {\n\t\t\ttry {\n\t\t\t\tconst cacheStr = uni.getStorageSync(key);\n\t\t\t\tif (!cacheStr) return null;\n\t\t\t\t\n\t\t\t\tconst cacheData = JSON.parse(cacheStr);\n\t\t\t\tconst now = Date.now();\n\t\t\t\t\n\t\t\t\t// 检查是否过期\n\t\t\t\tif (now - cacheData.timestamp > cacheData.expiry) {\n\t\t\t\t\tuni.removeStorageSync(key);\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn cacheData.data;\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('获取缓存失败:', e);\n\t\t\t\treturn null;\n\t\t\t}\n\t\t},\n\t\t\n\t\tclearCache(key) {\n\t\t\ttry {\n\t\t\t\tuni.removeStorageSync(key);\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('清除缓存失败:', e);\n\t\t\t}\n\t\t},\n\t\t\n\t\t\t// 初始化日期范围（前一个月、当前月、下一个月）\n\t\tinitDateRange() {\n\t\t\t// 使用月份差值判断，不再需要精确的开始和结束日期\n\t\t\t// 保留这个方法是为了向后兼容，实际判断在isDateInRange中进行\n\t\t\tconst now = new Date();\n\t\t\tthis.dateRange = { \n\t\t\t\tstart: new Date(now.getFullYear(), now.getMonth() - 1, 1),\n\t\t\t\tend: new Date(now.getFullYear(), now.getMonth() + 1, 31)\n\t\t\t};\n\t\t},\n\t\t\n\t\t// 获取年月字符串\n\t\tgetYearMonth(date) {\n\t\t\ttry {\n\t\t\t\tconst d = date instanceof Date ? date : new Date(date);\n\t\t\t\tif (isNaN(d.getTime())) {\n\t\t\t\t\tconsole.error(`❌ getYearMonth: 无效日期`, date);\n\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`;\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error(`❌ getYearMonth 错误:`, e);\n\t\t\t\treturn '';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 判断日期是否在可查看范围内\n\t\tisDateInRange(date) {\n\t\t\tconst d = date instanceof Date ? date : new Date(date);\n\t\t\t\n\t\t\t// 获取待检查日期的年月\n\t\t\tconst checkYear = d.getFullYear();\n\t\t\tconst checkMonth = d.getMonth();\n\t\t\t\n\t\t\t// 获取当前日期的年月\n\t\t\tconst now = new Date();\n\t\t\tconst currentYear = now.getFullYear();\n\t\t\tconst currentMonth = now.getMonth();\n\t\t\t\n\t\t\t// 计算月份差值\n\t\t\tconst monthDiff = (checkYear - currentYear) * 12 + (checkMonth - currentMonth);\n\t\t\t\n\t\t\t// 允许查看范围：前一个月(-1)、当前月(0)、下一个月(1)\n\t\t\treturn monthDiff >= -1 && monthDiff <= 1;\n\t\t},\n\t\t\n\t\t// 更新当前显示的任务\n\t\tupdateCurrentDisplayTasks(forceReloadShifts = false) {\n\t\t\tconst selectedDateStr = this.formatDateForCompare(this.selectedDate);\n\t\t\tconst selectedYearMonth = this.getYearMonth(this.selectedDate);\n\t\t\t\n\t\t\t// 获取当前月份的所有任务\n\t\t\tconst monthTasks = this.cachedTasks[selectedYearMonth] || [];\n\t\t\t\n\t\t\t// 根据权限筛选任务\n\t\t\tif (this.isAdmin) {\n\t\t\t\t// 管理员显示所有任务\n\t\t\t\tthis.currentDisplayTasks = monthTasks;\n\t\t\t} else {\n\t\t\t\t// 普通用户只显示自己的任务\n\t\t\t\tconst userInfo = uni.getStorageSync('uni-id-pages-userInfo') || {};\n\t\t\t\t\n\t\t\t\tthis.currentDisplayTasks = monthTasks.filter(task => {\n\t\t\t\t\treturn task.user_id === userInfo._id;\n\t\t\t\t});\n\t\t\t}\n\t\t\t\n\t\t\t// 只有在强制重新加载或者有新任务时才加载班次信息\n\t\t\tif (this.currentDisplayTasks.length > 0 && forceReloadShifts) {\n\t\t\t\tthis.loadShiftInfo();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 初始化日历\n\t\tinitCalendar() {\n\t\t\tconst today = new Date();\n\t\t\tthis.currentYear = today.getFullYear();\n\t\t\tthis.currentMonth = today.getMonth();\n\t\t\tthis.selectedDate = today; // 确保选中今天\n\t\t\tthis.generateCalendarDays();\n\t\t},\n\t\t\n\t\t\t// 刷新列表和日历\n\tasync refreshList() {\n\t\ttry {\n\t\t\t// 🔥 显示骨架屏\n\t\t\tthis.initialDataLoaded = false;\n\t\t\tthis.currentDisplayTasks = [];\n\t\t\t\n\t\t\tconst selectedYearMonth = this.getYearMonth(this.selectedDate);\n\t\t\t\n\t\t\t// 清除缓存，强制重新加载\n\t\t\tthis.$delete(this.loadedMonths, selectedYearMonth);\n\t\t\t\n\t\t\t// 重新加载当前月份的任务\n\t\t\tawait this.loadMonthTasks(selectedYearMonth);\n\t\t\t\n\t\t\t// 更新显示的任务\n\t\t\tthis.updateCurrentDisplayTasks(true); // 刷新时需要重新加载班次信息\n\t\t\t\n\t\t\t// 更新日历指示器\n\t\t\tthis.extractTaskDates();\n\t\t\t\n\t\t\t// 添加最小延迟，确保用户能看到刷新效果\n\t\t\tawait new Promise(resolve => setTimeout(resolve, 200));\n\t\t\t\n\t\t} catch (error) {\n\t\t\tconsole.error('刷新列表失败:', error);\n\t\t} finally {\n\t\t\t// 🔥 标记加载完成\n\t\t\tthis.initialDataLoaded = true;\n\t\t}\n\t},\n\t\t\n\t\t// 选择日期\n\t\tasync selectDate(day) {\n\t\t\tif (!day || !day.date) {\n\t\t\t\tconsole.error('无效的日期选择:', day);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 确保日期是有效的\n\t\t\tconst date = new Date(day.date);\n\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\tconsole.error('无效的日期:', day.date);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 检查日期是否在可查看范围内\n\t\t\tif (!this.isDateInRange(date)) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '该日期超出可查看范围',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tthis.selectedDate = date;\n\t\t\t\n\t\t\t// 如果选择的日期不在当前月份，需要更新月份并加载数据\n\t\t\tif (date.getMonth() !== this.currentMonth || \n\t\t\t\tdate.getFullYear() !== this.currentYear) {\n\t\t\t\t\n\t\t\t\tthis.currentMonth = date.getMonth();\n\t\t\t\tthis.currentYear = date.getFullYear();\n\t\t\t\tthis.generateCalendarDays();\n\t\t\t\t\n\t\t\t\t// 加载新月份的任务数据\n\t\t\t\tconst yearMonth = this.getYearMonth(date);\n\t\t\t\tif (!this.loadedMonths[yearMonth]) {\n\t\t\t\t\t// 🔥 跨月份时显示骨架屏\n\t\t\t\t\tthis.initialDataLoaded = false;\n\t\t\t\t\tthis.currentDisplayTasks = [];\n\t\t\t\t\t\n\t\t\t\t\tawait this.loadMonthTasks(yearMonth);\n\t\t\t\t\t\n\t\t\t\t\t// 添加最小延迟\n\t\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 150));\n\t\t\t\t\tthis.initialDataLoaded = true;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 更新显示的任务\n\t\t\tthis.updateCurrentDisplayTasks(false); // 切换月份时不需要重新加载班次信息\n\t\t},\n\t\t\n\t\t// 生成日历天数\n\t\tgenerateCalendarDays() {\n\t\t\tconst year = this.currentYear;\n\t\t\tconst month = this.currentMonth;\n\t\t\t\n\t\t\tconst firstDay = new Date(year, month, 1);\n\t\t\tconst lastDay = new Date(year, month + 1, 0);\n\t\t\t\n\t\t\tconst firstDayOfWeek = firstDay.getDay();\n\t\t\tconst daysInMonth = lastDay.getDate();\n\t\t\t\n\t\t\tconst prevMonthLastDay = new Date(year, month, 0);\n\t\t\tconst daysInPrevMonth = prevMonthLastDay.getDate();\n\t\t\t\n\t\t\tconst days = [];\n\t\t\t\n\t\t\t// 添加上个月的日期\n\t\t\tfor (let i = firstDayOfWeek - 1; i >= 0; i--) {\n\t\t\t\tconst day = daysInPrevMonth - i;\n\t\t\t\tconst date = new Date(year, month - 1, day);\n\t\t\t\tdays.push({\n\t\t\t\t\tday,\n\t\t\t\t\tdate,\n\t\t\t\t\tcurrentMonth: false,\n\t\t\t\t\tisToday: this.isToday(date),\n\t\t\t\t\thasTask: this.hasTasksOnDate(date)\n\t\t\t\t});\n\t\t\t}\n\t\t\t\n\t\t\t// 添加当月的日期\n\t\t\tfor (let i = 1; i <= daysInMonth; i++) {\n\t\t\t\tconst date = new Date(year, month, i);\n\t\t\t\tdays.push({\n\t\t\t\t\tday: i,\n\t\t\t\t\tdate,\n\t\t\t\t\tcurrentMonth: true,\n\t\t\t\t\tisToday: this.isToday(date),\n\t\t\t\t\thasTask: this.hasTasksOnDate(date)\n\t\t\t\t});\n\t\t\t}\n\t\t\t\n\t\t\t// 添加下个月的日期，补齐6行7列\n\t\t\tconst remainingDays = 42 - days.length;\n\t\t\tfor (let i = 1; i <= remainingDays; i++) {\n\t\t\t\tconst date = new Date(year, month + 1, i);\n\t\t\t\tdays.push({\n\t\t\t\t\tday: i,\n\t\t\t\t\tdate,\n\t\t\t\t\tcurrentMonth: false,\n\t\t\t\t\tisToday: this.isToday(date),\n\t\t\t\t\thasTask: this.hasTasksOnDate(date)\n\t\t\t\t});\n\t\t\t}\n\t\t\t\n\t\t\tthis.calendarDays = days;\n\t\t},\n\t\t\n\t\t// 判断是否是今天\n\t\tisToday(date) {\n\t\t\tconst today = new Date();\n\t\t\treturn this.isSameDay(date, today);\n\t\t},\n\t\t\n\t\t// 判断两个日期是否是同一天\n\t\tisSameDay(date1, date2) {\n\t\t\tif (!date1 || !date2) return false;\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 转换为Date对象\n\t\t\t\tconst d1 = date1 instanceof Date ? date1 : new Date(date1);\n\t\t\t\tconst d2 = date2 instanceof Date ? date2 : new Date(date2);\n\t\t\t\t\n\t\t\t\treturn d1.getFullYear() === d2.getFullYear() &&\n\t\t\t\t\td1.getMonth() === d2.getMonth() &&\n\t\t\t\t\td1.getDate() === d2.getDate();\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('日期比较错误:', e);\n\t\t\t\treturn false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 判断日期是否被选中\n\t\tisSelectedDate(date) {\n\t\t\treturn this.isSameDay(date, this.selectedDate);\n\t\t},\n\t\t\n\t\t// 判断日期是否有任务\n\t\thasTasksOnDate(date) {\n\t\t\ttry {\n\t\t\t\tif (!date || !(date instanceof Date) || isNaN(date.getTime())) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst dateStr = this.formatDateForCompare(date);\n\t\t\t\tif (!dateStr) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn this.taskDates.has(dateStr);\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('❌ 检查日期任务出错:', e);\n\t\t\t\treturn false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 格式化日期用于比较\n\t\tformatDateForCompare(date) {\n\t\t\tif (!date) {\n\t\t\t\treturn '';\n\t\t\t}\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst d = date instanceof Date ? date : new Date(date);\n\t\t\t\tif (isNaN(d.getTime())) {\n\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn formatDate(d, 'YYYY-MM-DD');\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('❌ 日期格式化错误:', e);\n\t\t\t\treturn '';\n\t\t\t}\n\t\t},\n\t\t\n\t\t\t// 上个月 - 数据已预加载，切换流畅\n\tasync prevMonth() {\n\t\t// 检查是否超出范围\n\t\tconst newDate = new Date(this.currentYear, this.currentMonth - 1, 1);\n\t\t\n\t\tif (!this.isDateInRange(newDate)) {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '已到达可查看的最早月份',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t\treturn;\n\t\t}\n\t\t\n\t\ttry {\n\t\t\tif (this.currentMonth === 0) {\n\t\t\t\tthis.currentYear--;\n\t\t\t\tthis.currentMonth = 11;\n\t\t\t} else {\n\t\t\t\tthis.currentMonth--;\n\t\t\t}\n\t\t\t\n\t\t\tthis.generateCalendarDays();\n\t\t\t\n\t\t\t// 数据已预加载，如果没有则快速加载\n\t\t\tconst yearMonth = this.getYearMonth(new Date(this.currentYear, this.currentMonth, 1));\n\t\t\tif (!this.loadedMonths[yearMonth]) {\n\t\t\t\t// 🔥 切换月份时显示骨架屏\n\t\t\t\tthis.initialDataLoaded = false;\n\t\t\t\tthis.currentDisplayTasks = [];\n\t\t\t\t\n\t\t\t\tawait this.loadMonthTasks(yearMonth);\n\t\t\t\t\n\t\t\t\t// 添加最小延迟\n\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 150));\n\t\t\t\tthis.initialDataLoaded = true;\n\t\t\t}\n\t\t\t\n\t\t\t// 更新显示的任务\n\t\t\tthis.updateCurrentDisplayTasks(false); // 切换月份时不需要重新加载班次信息\n\t\t} catch (e) {\n\t\t\tconsole.error('❌ 切换上个月失败:', e);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '切换失败，请重试',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t}\n\t},\n\t\t\n\t\t\t// 下个月 - 数据已预加载，切换流畅\n\tasync nextMonth() {\n\t\t// 检查是否超出范围\n\t\tconst newDate = new Date(this.currentYear, this.currentMonth + 1, 1);\n\t\tif (!this.isDateInRange(newDate)) {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '已到达可查看的最晚月份',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t\treturn;\n\t\t}\n\t\t\n\t\ttry {\n\t\t\tif (this.currentMonth === 11) {\n\t\t\t\tthis.currentYear++;\n\t\t\t\tthis.currentMonth = 0;\n\t\t\t} else {\n\t\t\t\tthis.currentMonth++;\n\t\t\t}\n\t\t\tthis.generateCalendarDays();\n\t\t\t\n\t\t\t// 数据已预加载，如果没有则快速加载\n\t\t\tconst yearMonth = this.getYearMonth(new Date(this.currentYear, this.currentMonth, 1));\n\t\t\tif (!this.loadedMonths[yearMonth]) {\n\t\t\t\t// 🔥 切换月份时显示骨架屏\n\t\t\t\tthis.initialDataLoaded = false;\n\t\t\t\tthis.currentDisplayTasks = [];\n\t\t\t\t\n\t\t\t\tawait this.loadMonthTasks(yearMonth);\n\t\t\t\t\n\t\t\t\t// 添加最小延迟\n\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 150));\n\t\t\t\tthis.initialDataLoaded = true;\n\t\t\t}\n\t\t\t\n\t\t\t// 更新显示的任务\n\t\t\tthis.updateCurrentDisplayTasks(false); // 切换月份时不需要重新加载班次信息\n\t\t} catch (e) {\n\t\t\tconsole.error('❌ 切换下个月失败:', e);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '切换失败，请重试',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t}\n\t},\n\t\t\n\t\t// 提取任务日期并更新日历 - 优化版本\n\t\textractTaskDates() {\n\t\t\t// 使用setTimeout避免阻塞主线程\n\t\t\tsetTimeout(() => {\n\t\t\t\ttry {\n\t\t\t\t\t// 清空之前的日期集合\n\t\t\t\t\tthis.taskDates.clear();\n\t\t\t\t\t\n\t\t\t\t\t// 手动计算所有缓存任务，因为allCachedTasks计算属性有响应式问题\n\t\t\t\t\tconst manualAllTasks = [];\n\t\t\t\t\tObject.values(this.cachedTasks).forEach(monthTasks => {\n\t\t\t\t\t\tif (Array.isArray(monthTasks)) {\n\t\t\t\t\t\t\tmanualAllTasks.push(...monthTasks);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 根据权限筛选要显示小点点的任务\n\t\t\t\t\tconst userInfo = uni.getStorageSync('uni-id-pages-userInfo') || {};\n\t\t\t\t\tconst tasksToShow = this.isAdmin ? manualAllTasks : manualAllTasks.filter(task => {\n\t\t\t\t\t\treturn task.user_id === userInfo._id;\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 批量处理日期，减少DOM操作\n\t\t\t\t\tconst dateSet = new Set();\n\t\t\t\t\ttasksToShow.forEach(task => {\n\t\t\t\t\t\tif (task.patrol_date) {\n\t\t\t\t\t\t\tdateSet.add(task.patrol_date);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 一次性更新taskDates\n\t\t\t\t\tthis.taskDates = dateSet;\n\t\t\t\t\t\n\t\t\t\t\t// 使用nextTick确保数据更新完成后再更新日历\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.generateCalendarDays();\n\t\t\t\t\t});\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('❌ 提取任务日期时发生错误:', e);\n\t\t\t\t}\n\t\t\t}, 0);\n\t\t},\n\t\t\n\t\t\t// 只加载当前月份的任务\n\tasync loadCurrentMonthTasks() {\n\t\ttry {\n\t\t\tconst currentYearMonth = this.getYearMonth(new Date());\n\t\t\t\n\t\t\tif (!currentYearMonth) {\n\t\t\t\tconsole.error(`❌ 无效的年月字符串: ${currentYearMonth}`);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 只加载当前月份，不预加载其他月份\n\t\t\tawait this.loadMonthTasks(currentYearMonth);\n\t\t\tthis.updateCurrentDisplayTasks(true); // 加载新数据时需要重新加载班次信息\n\t\t\tthis.extractTaskDates();\n\t\t} catch (e) {\n\t\t\tconsole.error('❌ 加载当前月份任务失败:', e);\n\t\t} finally {\n\t\t\t// 确保重置刷新状态\n\t\t\tthis.isRefreshing = false;\n\t\t}\n\t},\n\t\t\n\t\t// 按月份加载任务\n\t\tasync loadMonthTasks(yearMonth) {\n\t\t\tif (this.loading) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tif (this.loadedMonths[yearMonth]) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tthis.loading = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 获取用户信息和权限\n\t\t\t\tconst userInfo = uni.getStorageSync('uni-id-pages-userInfo') || {};\n\t\t\t\tlet userRoleStr = uni.getStorageSync(getCacheKey(CACHE_KEYS.USER_ROLE)) || '{}';\n\t\t\t\tlet userRole = {};\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tif (typeof userRoleStr === 'string') {\n\t\t\t\t\t\tuserRole = JSON.parse(userRoleStr);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuserRole = userRoleStr;\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tuserRole = { value: { userRole: [] } };\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 确定用户权限\n\t\t\t\tthis.isAdmin = userRole.value && \n\t\t\t\t\t\tuserRole.value.userRole && \n\t\t\t\t\t\tArray.isArray(userRole.value.userRole) && \n\t\t\t\t\t\t(userRole.value.userRole.includes('admin') || \n\t\t\t\t\t\t userRole.value.userRole.includes('GM') || \n\t\t\t\t\t\t userRole.value.userRole.includes('PM') || \n\t\t\t\t\t\t userRole.value.userRole.includes('supervisor') || \n\t\t\t\t\t\t userRole.value.userRole.includes('reviser'));\n\t\t\t\t\n\t\t\t\t// 计算月份的开始和结束日期\n\t\t\t\tconst [year, month] = yearMonth.split('-');\n\t\t\t\tconst startDate = `${year}-${month}-01`;\n\t\t\t\t// 修复日期计算：new Date(year, month, 0) 中的month应该是实际月份\n\t\t\t\tconst endDate = `${year}-${month}-${new Date(parseInt(year), parseInt(month), 0).getDate()}`;\n\t\t\t\t\n\t\t\t\t// 构建API参数 - 🔥 优化：使用轻量级数据层级 + 角色权限控制\n\t\t\t\tconst params = {\n\t\t\t\t\tparams: {\n\t\t\t\t\t\tstartDate: startDate,\n\t\t\t\t\t\tendDate: endDate,\n\t\t\t\t\t\tpageSize: 800, // 平衡性能和完整性，足够覆盖400个任务的月份\n\t\t\t\t\t\tstatus: -1, // 全部状态\n\t\t\t\t\t\t// 🔥 移除硬编码的userId过滤，改为使用viewScope控制\n\t\t\t\t\t\tviewScope: 'role-based', // 🔥 新增：角色决定模式，管理层看全部，普通用户看自己\n\t\t\t\t\t\tlevel: 'list', // 🔥 新增：列表级别数据，不包含完整points数组\n\t\t\t\t\t\t// 🔥 关键优化：只请求列表必需的字段，大幅减少数据传输\n\t\t\t\t\t\tfields: '_id,name,status,area,patrol_date,shift_id,shift_name,user_id,user_name,route_name,create_date,overall_stats,rounds_detail.round,rounds_detail.name,rounds_detail.status,rounds_detail.start_time,rounds_detail.end_time,rounds_detail.duration,rounds_detail.stats'\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tconst response = await PatrolApi.call({\n\t\t\t\t\tname: 'patrol-task',\n\t\t\t\t\taction: 'getTaskList',\n\t\t\t\t\tdata: params\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (response.code === 0) {\n\t\t\t\t\tconst rawTasks = response.data.list || [];\n\t\t\t\t\tconst total = response.data.total || 0;\n\t\t\t\t\t\n\t\t\t\t\t// 🔥 优化后的数据加载说明：\n\t\t\t\t\t// 1. 使用level='list'参数，服务端返回轻量级数据\n\t\t\t\t\t// 2. 不包含points数组，减少85%+数据传输量\n\t\t\t\t\t// 3. 使用优化后的rounds_detail和overall_stats提供统计信息\n\t\t\t\t\t// 4. 预计数据量：从15-25KB/任务 降至 2-3KB/任务\n\t\t\t\t\tconst processedTasks = this.processTasks(rawTasks);\n\t\t\t\t\t\n\n\t\t\t\t\t\n\t\t\t\t\t// 检查是否需要加载更多数据\n\t\t\t\t\tif (rawTasks.length >= 800 && total > 800) {\n\t\t\t\t\t\tconsole.warn(`⚠️ 月份 ${yearMonth} 任务数量 (${total}) 超过单次加载限制 (800)，可能显示不完整`);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: `该月任务较多(${total}个)，可能显示不完整`,\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 缓存月份数据和统计信息\n\t\t\t\t\tthis.cachedTasks[yearMonth] = processedTasks;\n\t\t\t\t\tthis.monthTaskCounts[yearMonth] = { loaded: rawTasks.length, total: total };\n\t\t\t\t\tthis.$set(this.loadedMonths, yearMonth, true);\n\t\t\t\t\t\n\t\t\t\t\t// 使用nextTick确保数据更新完成后再更新UI\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.extractTaskDates();\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: response.message || '加载失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('❌ 加载月份任务失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载任务失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t\tif (this.isRefreshing) {\n\t\t\t\t\tthis.isRefreshing = false;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 处理任务列表数据 - 🔥 优化：适配轻量级数据结构\n\t\tprocessTasks(list) {\n\t\t\tif (!list || !Array.isArray(list)) return [];\n\t\t\t\n\t\t\t// 缓存当前时间，避免重复创建Date对象\n\t\t\tconst now = new Date();\n\t\t\tconst nowTime = now.getTime();\n\t\t\t\n\t\t\treturn list.map(task => {\n\t\t\t\t// 🔥 优化：使用服务端已优化的数据结构\n\t\t\t\tconst processedTask = {\n\t\t\t\t\t_id: task._id,\n\t\t\t\t\tname: task.name || task.route_name || '未命名任务',\n\t\t\t\t\tstatus: parseInt(task.status || 0),\n\t\t\t\t\tarea: task.area || task.route_name || '未指定区域',\n\t\t\t\t\tpatrol_date: task.patrol_date || this.formatDateForCompare(new Date()),\n\t\t\t\t\tshift_id: task.shift_id || '',\n\t\t\t\t\tuser_id: task.user_id,\n\t\t\t\t\tuser_name: task.user_name,\n\t\t\t\t\troute_name: task.route_name,\n\t\t\t\t\tcreate_date: task.create_date,\n\t\t\t\t\t// 🔥 优化：使用云函数优化后的rounds_detail数据\n\t\t\t\t\trounds_detail: task.rounds_detail ? task.rounds_detail.map(round => ({\n\t\t\t\t\t\tround: round.round,                              // 轮次编号\n\t\t\t\t\t\tname: round.name,                               // 轮次名称\n\t\t\t\t\t\tstatus: round.status || 0,                     // 轮次状态\n\t\t\t\t\t\tstart_time: round.start_time,                  // 开始时间\n\t\t\t\t\t\tend_time: round.end_time,                      // 结束时间\n\t\t\t\t\t\tduration: round.duration || 60,               // 时长\n\t\t\t\t\t\tday_offset: round.day_offset || 0,             // 跨天偏移\n\t\t\t\t\t\t// 🔥 保留统计信息，但不包含points数组\n\t\t\t\t\t\tstats: round.stats || {\n\t\t\t\t\t\t\ttotal_points: round.total_points || 0,\n\t\t\t\t\t\t\tcompleted_points: round.completed_points || 0,\n\t\t\t\t\t\t\tcompletion_rate: round.completion_rate || 0\n\t\t\t\t\t\t}\n\t\t\t\t\t})) : [],\n\t\t\t\t\t// 🔥 优化：直接使用服务端的统计信息\n\t\t\t\t\toverall_stats: task.overall_stats ? {\n\t\t\t\t\t\ttotal_points: task.overall_stats.total_points || 0,\n\t\t\t\t\t\tcompleted_points: task.overall_stats.completed_points || 0,\n\t\t\t\t\t\tcompletion_rate: task.overall_stats.completion_rate || 0,\n\t\t\t\t\t\tmissed_points: task.overall_stats.missed_points || 0\n\t\t\t\t\t} : null\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t\t\t// 🔥 优化：简化轮次状态处理逻辑\n\t\t\t\t\t\t// 兼容性说明：支持云函数优化后的轻量级rounds_detail数据结构\n\t\tif (processedTask.rounds_detail && processedTask.rounds_detail.length > 0) {\n\t\t\t\t\tlet hasActiveRound = false;\n\t\t\t\t\tlet hasUpcomingRound = false;\n\t\t\t\t\tlet allRoundsCompleted = true;\n\t\t\t\t\t\n\t\t\t\t\tprocessedTask.rounds_detail = processedTask.rounds_detail.map(round => {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t// 解析时间\n\t\t\t\t\t\t\tconst startTime = new Date(round.start_time);\n\t\t\t\t\t\t\tconst endTime = new Date(round.end_time);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 🔥 优化：如果服务端已提供状态，直接使用；否则才计算\n\t\t\t\t\t\t\tlet computedStatus = round.status;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tif (computedStatus === undefined || computedStatus === null) {\n\t\t\t\t\t\t\t\t// 使用简化的状态计算逻辑\n\t\t\t\t\t\t\t\tif (processedTask.overall_stats && \n\t\t\t\t\t\t\t\t\tprocessedTask.overall_stats.completion_rate === 1 &&\n\t\t\t\t\t\t\t\t\tnow >= startTime) {\n\t\t\t\t\t\t\t\t\tcomputedStatus = 2; // 已完成\n\t\t\t\t\t\t\t\t} else if (now < startTime) {\n\t\t\t\t\t\t\t\t\tcomputedStatus = 0; // 未开始\n\t\t\t\t\t\t\t\t} else if (now > endTime) {\n\t\t\t\t\t\t\t\t\tcomputedStatus = 3; // 已超时\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tcomputedStatus = 1; // 进行中\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 更新轮次状态\n\t\t\t\t\t\t\tround.status = computedStatus;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 更新任务状态追踪变量\n\t\t\t\t\t\t\tif (computedStatus === 1) {\n\t\t\t\t\t\t\t\thasActiveRound = true;\n\t\t\t\t\t\t\t\tallRoundsCompleted = false;\n\t\t\t\t\t\t\t} else if (computedStatus === 0) {\n\t\t\t\t\t\t\t\thasUpcomingRound = true;\n\t\t\t\t\t\t\t\tallRoundsCompleted = false;\n\t\t\t\t\t\t\t} else if (computedStatus === 3) {\n\t\t\t\t\t\t\t\tallRoundsCompleted = false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 确保轮次有正确的时间格式\n\t\t\t\t\t\t\tif (!round.time) {\n\t\t\t\t\t\t\t\tround.time = this.formatTimeRange(round);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tconsole.error(`解析轮次[${round.round}]时间出错:`, error);\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\treturn round;\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 🔥 优化：如果服务端已提供任务状态，优先使用\n\t\t\t\t\tif (task.status !== undefined && task.status !== null) {\n\t\t\t\t\t\tprocessedTask.status = parseInt(task.status);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 根据轮次状态计算任务整体状态\n\t\t\t\t\t\tif (allRoundsCompleted) {\n\t\t\t\t\t\t\tprocessedTask.status = 2; // 已完成\n\t\t\t\t\t\t} else if (hasActiveRound) {\n\t\t\t\t\t\t\tprocessedTask.status = 1; // 进行中\n\t\t\t\t\t\t} else if (hasUpcomingRound) {\n\t\t\t\t\t\t\tprocessedTask.status = 0; // 未开始\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tprocessedTask.status = 3; // 已超时\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 🔥 优化：简化排序逻辑\n\t\t\t\t\tprocessedTask.rounds_detail.sort((a, b) => {\n\t\t\t\t\t\t// 优先显示进行中和未开始的轮次\n\t\t\t\t\t\tif ((a.status === 0 || a.status === 1) && (b.status === 2 || b.status === 3)) {\n\t\t\t\t\t\t\treturn -1;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif ((b.status === 0 || b.status === 1) && (a.status === 2 || a.status === 3)) {\n\t\t\t\t\t\t\treturn 1;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 同类型按轮次号排序\n\t\t\t\t\t\treturn a.round - b.round;\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn processedTask;\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 添加与detail.vue相同的时间解析方法\n\t\tparseTimeString(timeStr) {\n\t\t\ttry {\n\t\t\t\t// 检查是否是完整的ISO格式日期时间\n\t\t\t\tif (timeStr.includes('T') || timeStr.includes('-')) {\n\t\t\t\t\tconst date = new Date(timeStr);\n\t\t\t\t\treturn {\n\t\t\t\t\t\thours: date.getHours(),\n\t\t\t\t\t\tminutes: date.getMinutes()\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 否则假设是HH:MM格式\n\t\t\t\tconst parts = timeStr.split(':');\n\t\t\t\treturn {\n\t\t\t\t\thours: parseInt(parts[0], 10),\n\t\t\t\t\tminutes: parseInt(parts[1], 10)\n\t\t\t\t};\n\t\t\t} catch (e) {\n\t\t\t\t// 发生错误时返回默认值\n\t\t\t\tconsole.error('解析时间出错:', e);\n\t\t\t\treturn { hours: 0, minutes: 0 };\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 加载班次信息\n\t\tasync loadShiftInfo() {\n\t\t\t// 先尝试从缓存加载班次信息\n\t\t\tconst cachedShifts = this.getCache(this.cacheKeys.shifts);\n\t\t\tif (cachedShifts) {\n\t\t\t\t// 合并缓存的班次信息\n\t\t\t\tObject.assign(this.shiftInfo, cachedShifts);\n\t\t\t}\n\t\t\t\n\t\t\tconst shiftIds = this.currentDisplayTasks\n\t\t\t\t.map(task => task.shift_id)\n\t\t\t\t.filter(id => id && !this.shiftInfo[id]);\n\t\t\t\n\t\t\t// 去重\n\t\t\tconst uniqueShiftIds = [...new Set(shiftIds)];\n\t\t\t\n\t\t\tif (uniqueShiftIds.length === 0) return;\t\t\t\n\t\t\t\n\t\t\t// 批量加载班次信息 - 并行请求但限制并发数\n\t\t\tconst batchSize = 5; // 限制并发数为5\n\t\t\tconst batches = [];\n\t\t\t\n\t\t\tfor (let i = 0; i < uniqueShiftIds.length; i += batchSize) {\n\t\t\t\tbatches.push(uniqueShiftIds.slice(i, i + batchSize));\n\t\t\t}\n\t\t\t\n\t\t\tfor (const batch of batches) {\n\t\t\t\tconst promises = batch.map(async (shiftId) => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst res = await PatrolApi.call({\n\t\t\t\t\t\t\tname: 'patrol-shift',\n\t\t\t\t\t\t\taction: 'getShiftDetail',\n\t\t\t\t\t\t\tdata: { \n\t\t\t\t\t\t\t\tparams: { shift_id: shiftId }\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\t\t\tthis.shiftInfo[shiftId] = {...res.data};\n\t\t\t\t\t\t\treturn { shiftId, success: true, data: res.data };\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 设置错误标记\n\t\t\t\t\t\t\tthis.shiftInfo[shiftId] = {\n\t\t\t\t\t\t\t\terror: true,\n\t\t\t\t\t\t\t\terrorMessage: res.message || '班次不存在',\n\t\t\t\t\t\t\t\tname: `班次${shiftId.substr(-4)}`\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\treturn { shiftId, success: false, error: res.message };\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error(`加载班次 ${shiftId} 出错:`, e);\n\t\t\t\t\t\tthis.shiftInfo[shiftId] = {\n\t\t\t\t\t\t\terror: true,\n\t\t\t\t\t\t\terrorMessage: e.message || '加载失败',\n\t\t\t\t\t\t\tname: `班次${shiftId.substr(-4)}`\n\t\t\t\t\t\t};\n\t\t\t\t\t\treturn { shiftId, success: false, error: e.message };\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 等待当前批次完成\n\t\t\t\tconst results = await Promise.all(promises);\n\t\t\t}\n\t\t\t\n\t\t\t// 缓存班次信息（只缓存成功加载的）\n\t\t\tconst successShifts = {};\n\t\t\tObject.keys(this.shiftInfo).forEach(shiftId => {\n\t\t\t\tif (this.shiftInfo[shiftId] && !this.shiftInfo[shiftId].error) {\n\t\t\t\t\tsuccessShifts[shiftId] = this.shiftInfo[shiftId];\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\tif (Object.keys(successShifts).length > 0) {\n\t\t\t\tthis.setCache(this.cacheKeys.shifts, successShifts);\n\t\t\t}\n\t\t\t\n\t\t\t// 强制更新视图\n\t\t\tthis.$forceUpdate();\n\t\t},\n\t\t\n\t\t// 获取班次名称\n\t\tgetShiftName(shiftId) {\n\t\t\tif (!shiftId) return '无班次';\n\t\t\t\n\t\t\t// 检查班次信息是否存在并具有name属性\n\t\t\tif (this.shiftInfo[shiftId] && this.shiftInfo[shiftId].name) {\n\t\t\t\treturn this.shiftInfo[shiftId].name;\n\t\t\t}\n\t\t\t\n\t\t\t// 如果班次信息存在但是有error属性，表示加载失败\n\t\t\tif (this.shiftInfo[shiftId] && this.shiftInfo[shiftId].error) {\n\t\t\t\treturn `班次${shiftId.substr(-4)}`;  // 使用ID的后4位作为标识\n\t\t\t}\n\t\t\t\n\t\t\t// 如果班次ID存在于任务中但信息未加载，返回默认显示\n\t\t\t// 移除自动加载逻辑，避免重复请求\n\t\t\tif (!this.shiftInfo[shiftId]) {\n\t\t\t\treturn `班次${shiftId.substr(-4)}`;  // 使用ID的后4位作为标识\n\t\t\t}\n\t\t\t\n\t\t\t// 如果已经尝试加载但没有name属性\n\t\t\tif (this.shiftInfo[shiftId] && !this.shiftInfo[shiftId].name) {\n\t\t\t\treturn `班次${shiftId.substr(-4)}`;  // 使用ID的后4位作为标识\n\t\t\t}\n\t\t\t\n\t\t\treturn '未知班次';\n\t\t},\n\t\t\n\t\t// 加载特定班次信息\n\t\tasync loadSpecificShift(shiftId) {\n\t\t\tif (!shiftId) return;\n\t\t\t\n\t\t\t// 如果已经加载过且有error属性，不再重复请求\n\t\t\tif (this.shiftInfo[shiftId] && this.shiftInfo[shiftId].error) return;\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst res = await PatrolApi.call({\n\t\t\t\t\tname: 'patrol-shift',\n\t\t\t\t\taction: 'getShiftDetail',\n\t\t\t\t\tdata: { \n\t\t\t\t\t\tparams: { shift_id: shiftId }\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\t// 确保数据中有name属性\n\t\t\t\t\tconst shiftData = res.data;\n\t\t\t\t\tif (!shiftData.name) {\n\t\t\t\t\t\tshiftData.name = `班次${shiftId.substr(-4)}`;\n\t\t\t\t\t}\n\t\t\t\t\tthis.$set(this.shiftInfo, shiftId, shiftData);\n\t\t\t\t\t// 强制更新视图\n\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t} else {\n\t\t\t\t\tconsole.error('班次信息加载失败:', res);\n\t\t\t\t\t// 设置一个带有错误标记的对象，避免重复请求\n\t\t\t\t\tthis.$set(this.shiftInfo, shiftId, {\n\t\t\t\t\t\terror: true,\n\t\t\t\t\t\terrorMessage: res.message || '班次不存在',\n\t\t\t\t\t\t// 添加一个默认名称\n\t\t\t\t\t\tname: `班次${shiftId.substr(-4)}`\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('加载班次信息出错:', e);\n\t\t\t\t// 设置一个带有错误标记的对象，避免重复请求\n\t\t\t\tthis.$set(this.shiftInfo, shiftId, {\n\t\t\t\t\terror: true,\n\t\t\t\t\terrorMessage: e.message || '加载失败',\n\t\t\t\t\t// 添加一个默认名称\n\t\t\t\t\tname: `班次${shiftId.substr(-4)}`\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\t// 无论成功或失败，都强制更新一次视图\n\t\t\t\tthis.$forceUpdate();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 获取状态文本\n\t\tgetStatusText(status) {\n\t\t\tstatus = parseInt(status);\n\t\t\tconst statusMap = {\n\t\t\t\t0: '未开始',\n\t\t\t\t1: '进行中',\n\t\t\t\t2: '已完成',\n\t\t\t\t3: '已超时',\n\t\t\t\t4: '已取消'\n\t\t\t};\n\t\t\treturn statusMap[status] || '未知状态';\n\t\t},\n\t\t\n\t\t// 获取点位数量\n\t\tgetPointCount(task) {\n\t\t\treturn task.total_points ? task.total_points.length : 0;\n\t\t},\n\t\n\t// 搜索任务\n\tsearchTasks() {\n\t\t// 在新的架构下，搜索通过filteredTasks计算属性实现\n\t\t// 如果需要服务端搜索，可以重新加载当前月份数据\n\t\tthis.refreshList();\n\t},\n\t\n\t// 状态变更\n\tonStatusChange(e) {\n\t\tthis.filterStatus = this.filterOptions[e.detail.value].value;\n\t\t// 状态过滤通过filteredTasks计算属性实现，无需重新加载数据\n\t},\n\t\n\t// 下拉刷新\n\trefresh() {\n\t\tthis.isRefreshing = true;\n\t\t\n\t\t// 清空当前显示的任务\n\t\tthis.currentDisplayTasks = [];\n\t\t\n\t\t// 重新加载当前月份的任务\n\t\tconst currentYearMonth = this.getYearMonth(this.selectedDate);\n\t\tthis.$delete(this.loadedMonths, currentYearMonth); // 清除缓存标记，强制重新加载\n\t\t\n\t\treturn this.loadMonthTasks(currentYearMonth).then(() => {\n\t\t\t// 更新显示的任务\n\t\t\tthis.updateCurrentDisplayTasks(true); // 刷新数据时需要重新加载班次信息\n\t\t\t\n\t\t\t// 提取任务日期并更新日历\n\t\t\tthis.extractTaskDates();\n\t\t}).catch(error => {\n\t\t\tconsole.error('refresh加载任务出错:', error);\n\t\t}).finally(() => {\n\t\t\t// 确保重置刷新状态\n\t\t\tthis.isRefreshing = false;\n\t\t});\n\t},\n\t\n\t// 加载更多 (按月加载模式下不需要分页加载更多)\n\tloadMore() {\n\t\t// 在新的按月加载模式下，不需要分页加载更多\n\t\t// 所有月份数据都是一次性加载完成的\n\t},\n\t\n\t// 查看任务详情\n\tviewTaskDetail(task) {\n\t\tuni.navigateTo({\n\t\t\turl: `/pages/patrol_pkg/task/detail?id=${task._id}`\n\t\t});\n\t},\n\t\n\t// 编辑任务\n\teditTask(task) {\n\t\tuni.navigateTo({\n\t\t\turl: `/pages/patrol_pkg/task/edit?id=${task._id}`\n\t\t});\n\t},\n\t\n\t// 确认删除\n\tconfirmDelete(task) {\n\t\t// 构建详细的确认信息\n\t\tconst confirmContent = `确定要删除以下任务吗？\\n` +\n\t\t\t`任务名称：${task.name}\\n` +\n\t\t\t`执行人员：${task.user_name || '未分配'}\\n` +\n\t\t\t`执行日期：${task.patrol_date || this.formatDateForDisplay(task.create_date)}\\n` +\n\t\t\t`任务状态：${this.getStatusText(task.status)}`;\n\n\t\tuni.showModal({\n\t\t\ttitle: '删除确认',\n\t\t\tcontent: confirmContent,\n\t\t\tconfirmText: '删除',\n\t\t\tconfirmColor: '#DC3545',\n\t\t\tsuccess: (res) => {\n\t\t\t\tif (res.confirm) {\n\t\t\t\t\tthis.deleteTask(task._id);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t},\n\t\n\t// 删除任务\n\tasync deleteTask(taskId) {\n\t\tthis.loading = true;\n\t\t\n\t\ttry {\n\t\t\tconst res = await PatrolApi.call({\n\t\t\t\tname: 'patrol-task',\n\t\t\t\taction: 'deleteTask',\n\t\t\t\tdata: { params: { id: taskId } }\n\t\t\t});\n\t\t\t\n\t\t\tif (res.code === 0) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '删除成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 从所有缓存中移除任务\n\t\t\t\tObject.keys(this.cachedTasks).forEach(yearMonth => {\n\t\t\t\t\tthis.cachedTasks[yearMonth] = this.cachedTasks[yearMonth]\n\t\t\t\t\t\t.filter(task => task._id !== taskId);\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 从当前显示的任务中移除\n\t\t\t\tthis.currentDisplayTasks = this.currentDisplayTasks\n\t\t\t\t\t.filter(task => task._id !== taskId);\n\t\t\t\t\n\t\t\t\t// 更新日历指示器\n\t\t\t\tthis.extractTaskDates();\n\t\t\t\t\n\t\t\t} else {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: res.message || '删除失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error('删除任务错误', e);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '删除任务出错',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t} finally {\n\t\t\tthis.loading = false;\n\t\t}\n\t},\n\t\n\t// 跳转到添加页面\n\tnavigateToAdd() {\n\t\tuni.navigateTo({\n\t\t\turl: '/pages/patrol_pkg/task/add'\n\t\t});\n\t},\n\t\n\t// 跳转到批量添加页面\n\tnavigateToBatchAdd() {\n\t\tuni.navigateTo({\n\t\t\turl: '/pages/patrol_pkg/task/batch-add'\n\t\t});\n\t},\n\t\n\t// 显示高级筛选弹窗\n\tshowAdvancedFilter() {\n\t\tthis.advancedFilterVisible = true;\n\t\tthis.$refs.advancedFilterPopup.open();\n\t},\n\t\n\t// 隐藏高级筛选弹窗\n\thideAdvancedFilter() {\n\t\tthis.$refs.advancedFilterPopup.close();\n\t},\n\t\n\t// 应用高级筛选\n\tapplyAdvancedFilters() {\n\t\tthis.refreshList();\n\t},\n\t\n\t// 重置高级筛选\n\tresetFilters() {\n\t\tthis.startDate = '';\n\t\tthis.endDate = '';\n\t\tthis.filterUser = null;\n\t\tthis.filterRoute = null;\n\t\tthis.filterArea = '';\n\t\tthis.sortBy = 'create_time';\n\t\tthis.sortDirection = 'desc';\n\t\tthis.refreshList();\n\t},\n\t\n\t// 显示用户选择弹窗\n\tshowUserPicker() {\n\t\tthis.$refs.userPickerPopup.open();\n\t},\n\t\n\t// 隐藏用户选择弹窗\n\thideUserPicker() {\n\t\tthis.$refs.userPickerPopup.close();\n\t},\n\t\n\t// 选择用户\n\tselectUser(user) {\n\t\tthis.filterUser = {\n\t\t\tid: user._id,\n\t\t\tname: user.name\n\t\t};\n\t\tthis.hideUserPicker();\n\t\tthis.refreshList();\n\t},\n\t\n\t// 显示线路选择弹窗\n\tshowRoutePicker() {\n\t\tthis.$refs.routePickerPopup.open();\n\t},\n\t\n\t// 隐藏线路选择弹窗\n\thideRoutePicker() {\n\t\tthis.$refs.routePickerPopup.close();\n\t},\n\t\n\t// 选择线路\n\tselectRoute(route) {\n\t\tthis.filterRoute = {\n\t\t\tid: route._id,\n\t\t\tname: route.name\n\t\t};\n\t\tthis.hideRoutePicker();\n\t\tthis.refreshList();\n\t},\n\t\n\t// 清除日期筛选\n\tclearDateFilter() {\n\t\tthis.filterDate = '';\n\t\tthis.refreshList();\n\t},\n\t\n\t// 清除状态筛选\n\tclearStatusFilter() {\n\t\tthis.statusIndex = -1;\n\t\t// 状态过滤通过filteredTasks计算属性实现，无需重新加载数据\n\t},\n\t\n\t// 清除用户筛选\n\tclearUserFilter() {\n\t\tthis.filterUser = null;\n\t\tthis.refreshList();\n\t},\n\t\n\t// 清除线路筛选\n\tclearRouteFilter() {\n\t\tthis.filterRoute = null;\n\t\tthis.refreshList();\n\t},\n\t\n\t// 清除区域筛选\n\tclearAreaFilter() {\n\t\tthis.filterArea = '';\n\t\tthis.refreshList();\n\t},\n\t\n\t// 清除所有筛选\n\tclearAllFilters() {\n\t\tthis.filterDate = '';\n\t\tthis.statusIndex = -1;\n\t\tthis.filterUser = null;\n\t\tthis.filterRoute = null;\n\t\tthis.filterArea = '';\n\t\tthis.startDate = '';\n\t\tthis.endDate = '';\n\t\tthis.sortBy = 'create_time';\n\t\tthis.sortDirection = 'desc';\n\t\tthis.refreshList();\n\t},\n\t\n\t// 设置排序方式\n\tsetSortBy(by) {\n\t\tthis.sortBy = by;\n\t\tthis.refreshList();\n\t},\n\t\n\t// 添加筛选方法\n\tsetFilter(status) {\n\t\tthis.filterStatus = status;\n\t\t// 状态过滤通过filteredTasks计算属性实现，无需重新加载数据\n\t},\n\t\n\t// 处理弹窗变化\n\tonPopupChange(e) {\n\t\tif (e.type === 'open') {\n\t\t\t// 弹窗打开时的处理逻辑\n\t\t} else if (e.type === 'close') {\n\t\t\t// 弹窗关闭时的处理逻辑\n\t\t}\n\t},\n\t\n\t// 加载用户列表\n\tasync loadUsers() {\n\t\ttry {\n\t\t\t// 先尝试从缓存加载\n\t\t\tconst cachedUsers = this.getCache(this.cacheKeys.users);\n\t\t\tif (cachedUsers) {\n\t\t\t\tthis.userMap = cachedUsers.userMap || {};\n\t\t\t\tthis.users = cachedUsers.users || [];\n\t\t\t\treturn cachedUsers.users;\n\t\t\t}\n\t\t\t\n\t\t\tconst userInfo = uni.getStorageSync('uni-id-pages-userInfo');\n\t\t\tconst userId = userInfo ? (typeof userInfo === 'string' ? JSON.parse(userInfo)._id : userInfo._id) : '';\n\t\t\t\n\t\t\tif (!userId) {\n\t\t\t\treturn [];\n\t\t\t}\n\t\t\t\n\t\t\tconst result = await PatrolApi.call({\n\t\t\t\tname: 'patrol-user',\n\t\t\t\taction: 'getUsers',\n\t\t\t\tdata: {\n\t\t\t\t\tuserid: userId,\n\t\t\t\t\tpageSize: 100\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\tif (result.code === 0) {\n\t\t\t\tconst users = result.data.list || [];\n\t\t\t\t\n\t\t\t\t// 清空之前的用户映射\n\t\t\t\tthis.userMap = {};\n\t\t\t\t\n\t\t\t\tusers.forEach(user => {\n\t\t\t\t\t// 确保用户对象有name属性\n\t\t\t\t\tconst processedUser = {\n\t\t\t\t\t\t...user,\n\t\t\t\t\t\t// 按优先级选择用户显示名称\n\t\t\t\t\t\tname: user.real_name || user.nickname || user.username || '未命名用户'\n\t\t\t\t\t};\n\t\t\t\t\tthis.userMap[user._id] = processedUser;\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tthis.users = users;\n\t\t\t\t\n\t\t\t\t// 缓存用户数据\n\t\t\t\tthis.setCache(this.cacheKeys.users, {\n\t\t\t\t\tuserMap: this.userMap,\n\t\t\t\t\tusers: users\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\treturn users;\n\t\t\t} else {\n\t\t\t\tconsole.error('获取用户列表失败:', result);\n\t\t\t\treturn [];\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error('加载用户列表出错:', e);\n\t\t\treturn [];\n\t\t}\n\t},\n\t\n\t// 加载线路列表\n\tasync loadRoutes() {\n\t\ttry {\n\t\t\t// 先尝试从缓存加载\n\t\t\tconst cachedRoutes = this.getCache(this.cacheKeys.routes);\n\t\t\tif (cachedRoutes) {\n\t\t\t\tthis.routeMap = cachedRoutes.routeMap || {};\n\t\t\t\tthis.routes = cachedRoutes.routes || [];\n\t\t\t\treturn cachedRoutes.routes;\n\t\t\t}\n\t\t\t\n\t\t\tconst result = await PatrolApi.call({\n\t\t\t\tname: 'patrol-route',\n\t\t\t\taction: 'getRouteList'\n\t\t\t});\n\t\t\t\n\t\t\tif (result.code === 0) {\n\t\t\t\tconst routes = result.data.list || [];\n\t\t\t\t\n\t\t\t\t// 清空之前的线路映射\n\t\t\t\tthis.routeMap = {};\n\t\t\t\t\n\t\t\t\troutes.forEach(route => {\n\t\t\t\t\tthis.routeMap[route._id] = route;\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tthis.routes = routes;\n\t\t\t\t\n\t\t\t\t// 缓存线路数据\n\t\t\t\tthis.setCache(this.cacheKeys.routes, {\n\t\t\t\t\trouteMap: this.routeMap,\n\t\t\t\t\troutes: routes\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\treturn routes;\n\t\t\t} else {\n\t\t\t\tconsole.error('获取线路列表失败:', result);\n\t\t\t\treturn [];\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error('加载线路列表出错:', e);\n\t\t\treturn [];\n\t\t}\n\t},\n\t\n\t// 简化或删除用户数据映射方法\n\tbuildUserMap(users) {\n\t\tif (!users || !Array.isArray(users)) return;\n\t\t\n\t\tusers.forEach(user => {\n\t\t\tthis.userMap[user._id] = user;\n\t\t});\n\t},\n\t\n\t// 获取日期对象的本地日期字符串，不受时区影响\n\tgetLocalDateString(date) {\n\t\tif (!date) return '';\n\t\t\n\t\ttry {\n\t\t\t// 确保date是Date对象\n\t\t\tif (!(date instanceof Date)) {\n\t\t\t\tconst parsedDate = new Date(date);\n\t\t\t\tif (isNaN(parsedDate.getTime())) {\n\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t\tdate = parsedDate;\n\t\t\t}\n\t\t\t\n\t\t\tconst year = date.getFullYear();\n\t\t\tconst month = date.getMonth() + 1;\n\t\t\tconst day = date.getDate();\n\t\t\t\n\t\t\treturn `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`;\n\t\t} catch (e) {\n\t\t\tconsole.error('获取本地日期字符串错误:', e);\n\t\t\treturn '';\n\t\t}\n\t},\n\t\n\t// 格式化时间为00:00格式，确保在所有平台上显示一致\n\tformatTimeConsistent(time) {\n\t\tif (!time) return '--:--';\n\t\t\n\t\ttry {\n\t\t\t// 如果是日期对象\n\t\t\tif (time instanceof Date) {\n\t\t\t\tif (isNaN(time.getTime())) return '--:--';\n\t\t\t\tconst hours = time.getHours().toString().padStart(2, '0');\n\t\t\t\tconst minutes = time.getMinutes().toString().padStart(2, '0');\n\t\t\t\treturn `${hours}:${minutes}`;\n\t\t\t}\n\t\t\t\n\t\t\t// 如果是时间戳或日期字符串\n\t\t\tconst date = new Date(time);\n\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\t// 尝试解析特定格式的时间字符串 HH:MM\n\t\t\t\tif (typeof time === 'string' && time.match(/^\\d{1,2}:\\d{2}$/)) {\n\t\t\t\t\treturn time;\n\t\t\t\t}\n\t\t\t\treturn '--:--';\n\t\t\t}\n\t\t\t\n\t\t\tconst hours = date.getHours().toString().padStart(2, '0');\n\t\t\tconst minutes = date.getMinutes().toString().padStart(2, '0');\n\t\t\treturn `${hours}:${minutes}`;\n\t\t} catch (e) {\n\t\t\tconsole.error('时间格式化错误:', e);\n\t\t\treturn '--:--';\n\t\t}\n\t},\n\t\n\t// 获取启用的轮次数量\n\tgetEnabledRoundsCount(task) {\n\t\treturn task.enabled_rounds?.length || 0;\n\t},\n\t\n\t// 获取总轮次数量\n\tgetAllRoundsCount(task) {\n\t\treturn task.rounds_completion?.length || 0;\n\t},\n\t\n\t// 获取当前用户信息\n\tasync getUserInfo() {\n\t\ttry {\n\t\t\tconst userInfoStr = uni.getStorageSync('uni-id-pages-userInfo');\n\t\t\tif (userInfoStr) {\n\t\t\t\ttry {\n\t\t\t\t\tthis.userInfo = typeof userInfoStr === 'string' ? JSON.parse(userInfoStr) : userInfoStr;\n\t\t\t\t\t\n\t\t\t\t// 从缓存获取角色信息\n\t\t\t\tconst userRoleStr = uni.getStorageSync(getCacheKey(CACHE_KEYS.USER_ROLE));\n\t\t\t\t\tif (userRoleStr) {\n\t\t\t\t\t\tconst userRole = typeof userRoleStr === 'string' ? JSON.parse(userRoleStr) : userRoleStr;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 从value字段获取userRole数组\n\t\t\t\t\t\tif (userRole && userRole.value && userRole.value.userRole) {\n\t\t\t\t\t\t\t// 将角色信息添加到用户对象\n\t\t\t\t\t\t\tthis.userInfo.role = userRole.value.userRole;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\treturn;\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('解析本地存储的用户信息失败:', e);\n\t\t\t\t\tuni.removeStorageSync('uni-id-pages-userInfo');\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\tconst res = await PatrolApi.call({\n\t\t\t\tname: 'patrol-user',\n\t\t\t\taction: 'getCurrentUser',\n\t\t\t\tdata: {\n\t\t\t\t\tparams: {}\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\tthis.userInfo = res.data;\n\t\t\t\tuni.setStorageSync('uni-id-pages-userInfo', JSON.stringify(res.data));\n\t\t\t} else {\n\t\t\t\tconst uniIdUserInfo = uni.getStorageSync('uni-id-pages-userInfo');\n\t\t\t\tif (uniIdUserInfo) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tthis.userInfo = typeof uniIdUserInfo === 'string' ? JSON.parse(uniIdUserInfo) : uniIdUserInfo;\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error('解析uni-id-pages用户信息失败:', e);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\tif (!this.userInfo) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '获取用户信息失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error('获取用户信息失败:', e);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '获取用户信息失败',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t}\n\t},\n\t\n\t// 刷新数据\n\tasync refreshData() {\n\t\tif (!this.loading) {\n\t\t\tthis.isRefreshing = true;\n\t\t\tthis.currentDisplayTasks = [];\n\t\t\t// 🔥 重置加载状态，显示骨架屏\n\t\t\tthis.initialDataLoaded = false;\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 确保刷新时也重新加载用户数据\n\t\t\t\tawait this.loadUsers();\n\t\t\t\t\n\t\t\t\t// 重新加载当前月份的任务\n\t\t\t\tconst currentYearMonth = this.getYearMonth(this.selectedDate);\n\t\t\t\tthis.$delete(this.loadedMonths, currentYearMonth); // 清除缓存标记，强制重新加载\n\t\t\t\tawait this.loadMonthTasks(currentYearMonth);\n\t\t\t\t\n\t\t\t\t// 更新显示的任务\n\t\t\t\tthis.updateCurrentDisplayTasks(true); // 刷新数据时需要重新加载班次信息\n\t\t\t\tthis.extractTaskDates();\n\t\t\t\t\n\t\t\t\t// 添加最小延迟，确保用户能看到刷新效果\n\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 200));\n\t\t\t\t\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('刷新数据失败:', error);\n\t\t\t} finally {\n\t\t\t\t// 🔥 标记加载完成\n\t\t\t\tthis.initialDataLoaded = true;\n\t\t\t\tthis.isRefreshing = false;\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t}\n\t\t} else {\n\t\t\tuni.stopPullDownRefresh();\n\t\t}\n\t},\n\t\n\t// 🔥 优化：获取已完成点位数量 - 适配轻量级数据结构\n\tgetCompletedCount(task) {\n\t\t// 🔥 优先从overall_stats获取（服务端已计算好）\n\t\tif (task.overall_stats && typeof task.overall_stats.completed_points === 'number') {\n\t\t\treturn task.overall_stats.completed_points;\n\t\t}\n\t\t\n\t\t// 🔥 其次从rounds_detail的stats累加\n\t\tif (task.rounds_detail && task.rounds_detail.length > 0) {\n\t\t\tlet completedPoints = 0;\n\t\t\tlet hasStats = false;\n\t\t\ttask.rounds_detail.forEach(round => {\n\t\t\t\tif (round.stats && typeof round.stats.completed_points === 'number') {\n\t\t\t\t\tcompletedPoints += round.stats.completed_points;\n\t\t\t\t\thasStats = true;\n\t\t\t\t}\n\t\t\t});\n\t\t\tif (hasStats) {\n\t\t\t\treturn completedPoints;\n\t\t\t}\n\t\t}\n\t\t\n\t\t// 兼容旧数据结构 - completed_points数组\n\t\tif (task.completed_points && Array.isArray(task.completed_points)) {\n\t\t\treturn task.completed_points.length;\n\t\t}\n\t\t\n\t\treturn 0;\n\t},\n\t\n\t// 🔥 优化：获取总点位数量 - 适配轻量级数据结构\n\tgetTotalPointsCount(task) {\n\t\t// 🔥 优先从overall_stats获取（服务端已计算好）\n\t\tif (task.overall_stats && typeof task.overall_stats.total_points === 'number') {\n\t\t\treturn task.overall_stats.total_points;\n\t\t}\n\t\t\n\t\t// 🔥 其次从rounds_detail的stats获取（轻量级统计数据）\n\t\tif (task.rounds_detail && task.rounds_detail.length > 0) {\n\t\t\t// 尝试从第一个轮次的stats获取\n\t\t\tconst firstRound = task.rounds_detail[0];\n\t\t\tif (firstRound && firstRound.stats && typeof firstRound.stats.total_points === 'number') {\n\t\t\t\treturn firstRound.stats.total_points;\n\t\t\t}\n\t\t\t\n\t\t\t// 如果是多轮次任务，累加各轮次点位数\n\t\t\tlet totalPoints = 0;\n\t\t\tlet hasStats = false;\n\t\t\ttask.rounds_detail.forEach(round => {\n\t\t\t\tif (round.stats && typeof round.stats.total_points === 'number') {\n\t\t\t\t\ttotalPoints += round.stats.total_points;\n\t\t\t\t\thasStats = true;\n\t\t\t\t}\n\t\t\t});\n\t\t\tif (hasStats && totalPoints > 0) {\n\t\t\t\treturn totalPoints;\n\t\t\t}\n\t\t}\n\t\t\n\t\t// 兼容旧数据结构 - 直接的total_points字段\n\t\tif (typeof task.total_points === 'number') {\n\t\t\treturn task.total_points;\n\t\t}\n\t\t\n\t\t// 兼容旧数据结构 - points数组（已不推荐使用）\n\t\tif (task.points && Array.isArray(task.points)) {\n\t\t\treturn task.points.length;\n\t\t}\n\t\t\n\t\t// 都不存在，返回0\n\t\treturn 0;\n\t},\n\t\n\t// 获取路线区域名称\n\tgetRouteAreaName(task) {\n\t\treturn task.area || task.route_name || '未指定区域';\n\t},\n\t\n\t// 获取班次时间\n\tgetShiftTime(task) {\n\t\t// 如果有班次ID且班次信息已加载\n\t\tif (task.shift_id && this.shiftInfo[task.shift_id]) {\n\t\t\tconst shift = this.shiftInfo[task.shift_id];\n\t\t\tif (shift.start_time && shift.end_time) {\n\t\t\t\treturn `${shift.start_time} - ${shift.end_time}`;\n\t\t\t}\n\t\t}\n\t\t\n\t\t// 如果没有班次信息但有开始和结束时间\n\t\tif ((task.startTime || task.start_time) && (task.endTime || task.end_time)) {\n\t\t\tconst startTime = task.startTime || task.start_time;\n\t\t\tconst endTime = task.endTime || task.end_time;\n\t\t\treturn `${startTime} - ${endTime}`;\n\t\t}\n\t\t\n\t\t// 如果以上都没有\n\t\treturn '时间未设置';\n\t},\n\t\n\t// 获取系统信息\n\tgetSystemInfo() {\n\t\ttry {\n\t\t\t// 使用新的API替代\n\t\t\tconst windowInfo = uni.getWindowInfo();\n\t\t\tconst systemInfo = uni.getSystemInfoSync(); // 暂时保留，因为某些属性在新API中可能不存在\n\t\t\treturn {\n\t\t\t\t...systemInfo,\n\t\t\t\twindowHeight: windowInfo.windowHeight,\n\t\t\t\twindowWidth: windowInfo.windowWidth,\n\t\t\t\tscreenHeight: windowInfo.screenHeight,\n\t\t\t\tscreenWidth: windowInfo.screenWidth\n\t\t\t};\n\t\t} catch (e) {\n\t\t\tconsole.error('获取系统信息失败:', e);\n\t\t\treturn {};\n\t\t}\n\t},\n\t\n\t// 获取用户名\n\tgetUserName(task) {\n\t\tif (task.user_id && this.userMap[task.user_id]) {\n\t\t\treturn this.userMap[task.user_id].name || '未知用户';\n\t\t} else if (task.user && task.user.name) {\n\t\t\treturn task.user.name;\n\t\t} else if (task.username) {\n\t\t\treturn task.username;\n\t\t}\n\t\treturn '未分配';\n\t},\n\t\n\t// 获取轮次状态文本\n\tgetRoundStatusText(round) {\n\t\t// 如果轮次已超时\n\t\tif (round.expired) {\n\t\t\t// 如果有部分完成\n\t\t\tif (round.completion_rate > 0) {\n\t\t\t\treturn `已超时(${round.completion_rate}%)`;\n\t\t\t}\n\t\t\treturn '已超时';\n\t\t}\n\t\t\n\t\t// 如果轮次已完全完成\n\t\tif (round.completed || round.completion_rate === 100) {\n\t\t\treturn '已完成';\n\t\t}\n\t\t\n\t\t// 如果轮次部分完成\n\t\tif (round.completion_rate > 0) {\n\t\t\treturn `进行中(${round.completion_rate}%)`;\n\t\t}\n\t\t\n\t\treturn '进行中';\n\t},\n\t\n\t// 根据状态码获取轮次状态文本\n\tgetRoundStatusTextByCode(status) {\n\t\tstatus = parseInt(status) || 0;\n\t\tconst statusMap = {\n\t\t\t0: '未开始',\n\t\t\t1: '进行中',\n\t\t\t2: '已完成',\n\t\t\t3: '已超时',\n\t\t\t4: '已取消'\n\t\t};\n\t\t\n\t\treturn statusMap[status] || '未知状态';\n\t},\n\t\n\t// 显示加载中\n\tshowLoading(show) {\n\t\tthis.loading = show;\n\t},\n\t\n\tgetStatusClass(status) {\n\t\tconst statusClassMap = {\n\t\t\t0: 'status-pending',   // 未开始\n\t\t\t1: 'status-active',    // 进行中\n\t\t\t2: 'status-completed', // 已完成 \n\t\t\t3: 'status-expired',   // 已超时\n\t\t\t4: 'status-cancelled'  // 已取消\n\t\t};\n\t\t\n\t\treturn statusClassMap[status] || 'status-pending';\n\t},\n\t\n\t// 获取轮次信息，带有次日标记\n\tgetFormattedRoundsInfo(task) {\n\t\tif (!task || !task.rounds_detail || !task.rounds_detail.length) {\n\t\t\treturn '无轮次';\n\t\t}\n\t\t\n\t\t// 获取轮次数\n\t\tconst roundCount = task.rounds_detail.length;\n\t\t\n\t\t// 检查是否有第二天轮次\n\t\tconst hasNextDayRounds = task.rounds_detail.some(round => round.day_offset && round.day_offset > 0);\n\t\t\n\t\t// 检查是否有第三天及以后轮次\n\t\tconst hasLaterDayRounds = task.rounds_detail.some(round => round.day_offset && round.day_offset >= 2);\n\t\t\n\t\t// 返回格式化后的轮次信息\n\t\tif (hasLaterDayRounds) {\n\t\t\treturn `${roundCount}轮 (含多天轮次)`;\n\t\t} else if (hasNextDayRounds) {\n\t\t\treturn `${roundCount}轮 (含次日轮次)`;\n\t\t} else {\n\t\t\treturn `${roundCount}轮`;\n\t\t}\n\t},\n\t\n\n\t\n\t// 格式化日期显示\n\tformatDateForDisplay(dateStr) {\n\t\tif (!dateStr) return '';\n\t\ttry {\n\t\t\treturn formatDate(new Date(dateStr), 'YYYY-MM-DD');\n\t\t} catch (e) {\n\t\t\tconsole.error('日期格式化错误:', e);\n\t\t\treturn '';\n\t\t}\n\t},\n\t\n\t// 格式化时间范围\n\tformatTimeRange(round) {\n\t\tif (!round || !round.start_time) return '--:--';\n\t\t\n\t\ttry {\n\t\t\t// 统一处理时间格式\n\t\t\tconst startTime = round.start_time.includes('T') ? \n\t\t\t\tnew Date(round.start_time) : \n\t\t\t\tnew Date(round.start_time.replace(/-/g, '/'));\n\t\t\t\t\n\t\t\tif (isNaN(startTime.getTime())) {\n\t\t\t\tconsole.error('Invalid start time:', round.start_time);\n\t\t\t\treturn '--:--';\n\t\t\t}\n\t\t\t\n\t\t\tconst hours = startTime.getHours().toString().padStart(2, '0');\n\t\t\tconst minutes = startTime.getMinutes().toString().padStart(2, '0');\n\t\t\treturn `${hours}:${minutes}`;\n\t\t} catch (e) {\n\t\t\tconsole.error('格式化时间出错:', e);\n\t\t\treturn '--:--';\n\t\t}\n\t},\n\t\n\t// 计算轮次剩余时间\n\tcalculateRemainingTime(round) {\n\t\tif (!round || !round.end_time) return '';\n\t\t\n\t\ttry {\n\t\t\tconst now = new Date();\n\t\t\tconst endTime = new Date(round.end_time);\n\t\t\t\n\t\t\t// 如果已经超过结束时间，返回空字符串\n\t\t\tif (now >= endTime) {\n\t\t\t\treturn '';\n\t\t\t}\n\t\t\t\n\t\t\tconst diffMs = endTime.getTime() - now.getTime();\n\t\t\tconst diffMins = Math.floor(diffMs / (1000 * 60));\n\t\t\tconst hours = Math.floor(diffMins / 60);\n\t\t\tconst minutes = diffMins % 60;\n\t\t\t\n\t\t\tif (hours > 0) {\n\t\t\t\treturn `${hours}小时${minutes}分钟`;\n\t\t\t} else if (minutes > 0) {\n\t\t\t\treturn `${minutes}分钟`;\n\t\t\t} else {\n\t\t\t\treturn '即将结束';\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error('计算剩余时间出错:', e);\n\t\t\treturn '';\n\t\t}\n\t},\n\t\n\t// 🔥 新增：计算任务开始前的倒计时\n\tcalculateStartCountdown(round) {\n\t\tif (!round || !round.start_time) return '';\n\t\t\n\t\ttry {\n\t\t\tconst now = new Date();\n\t\t\tconst startTime = new Date(round.start_time);\n\t\t\t\n\t\t\t// 如果已经开始或已过期，返回空字符串\n\t\t\tif (now >= startTime) {\n\t\t\t\treturn '';\n\t\t\t}\n\t\t\t\n\t\t\tconst diffMs = startTime.getTime() - now.getTime();\n\t\t\tconst diffMins = Math.floor(diffMs / (1000 * 60));\n\t\t\tconst hours = Math.floor(diffMins / 60);\n\t\t\tconst minutes = diffMins % 60;\n\t\t\tconst days = Math.floor(hours / 24);\n\t\t\tconst remainingHours = hours % 24;\n\t\t\t\n\t\t\t// 根据时间长短选择合适的显示格式\n\t\t\tif (days > 0) {\n\t\t\t\tif (remainingHours > 0) {\n\t\t\t\t\treturn `${days}天${remainingHours}小时后开始`;\n\t\t\t\t} else {\n\t\t\t\t\treturn `${days}天后开始`;\n\t\t\t\t}\n\t\t\t} else if (hours > 0) {\n\t\t\t\tif (minutes > 0) {\n\t\t\t\t\treturn `${hours}小时${minutes}分钟后开始`;\n\t\t\t\t} else {\n\t\t\t\t\treturn `${hours}小时后开始`;\n\t\t\t\t}\n\t\t\t} else if (minutes > 0) {\n\t\t\t\treturn `${minutes}分钟后开始`;\n\t\t\t} else {\n\t\t\t\treturn '即将开始';\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error('计算开始倒计时出错:', e);\n\t\t\treturn '';\n\t\t}\n\t},\n\t\n\t// 判断是否是同一天（用于日期比较）\n\tisSameDay(date1, date2) {\n\t\tif (!date1 || !date2) return false;\n\t\t\n\t\ttry {\n\t\t\t// 转换为Date对象\n\t\t\tconst d1 = date1 instanceof Date ? date1 : new Date(date1);\n\t\t\tconst d2 = date2 instanceof Date ? date2 : new Date(date2);\n\t\t\t\n\t\t\treturn d1.getFullYear() === d2.getFullYear() &&\n\t\t\t\td1.getMonth() === d2.getMonth() &&\n\t\t\t\td1.getDate() === d2.getDate();\n\t\t} catch (e) {\n\t\t\tconsole.error('日期比较错误:', e);\n\t\t\treturn false;\n\t\t}\n\t},\n\t\n\t\t\t// 移除预加载功能，改为按需加载\n\t\t// preloadAdjacentMonths() 方法已删除，采用按需加载策略\n\t\t\n\t\t// 启动定时更新 - 修复倒计时自动更新问题\n\t\tstartTimeUpdateTimer() {\n\t\t\tif (this.timer) {\n\t\t\t\tclearInterval(this.timer);\n\t\t\t}\n\t\t\t\n\t\t\t// 设置定时器，每分钟更新一次倒计时显示\n\t\t\tthis.timer = setInterval(() => {\n\t\t\t\tif (this.currentDisplayTasks && this.currentDisplayTasks.length > 0) {\n\t\t\t\t\tconst now = new Date();\n\t\t\t\t\t\n\t\t\t\t\t// 🔥 修复：检查是否有任何倒计时需要更新\n\t\t\t\t\tconst hasCountdown = this.currentDisplayTasks.some(task => {\n\t\t\t\t\t\treturn task.rounds_detail && task.rounds_detail.some(round => {\n\t\t\t\t\t\t\t// 检查未开始的轮次（有开始倒计时）\n\t\t\t\t\t\t\tif (round.status === 0 && round.start_time) {\n\t\t\t\t\t\t\t\tconst startTime = new Date(round.start_time);\n\t\t\t\t\t\t\t\treturn now < startTime; // 还没开始，需要显示倒计时\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 检查进行中的轮次（有剩余时间）\n\t\t\t\t\t\t\tif (round.status === 1 && round.end_time) {\n\t\t\t\t\t\t\t\tconst endTime = new Date(round.end_time);\n\t\t\t\t\t\t\t\treturn now < endTime; // 还没结束，需要显示剩余时间\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 🔥 关键修复：只要有倒计时就强制更新页面\n\t\t\t\t\tif (hasCountdown) {\n\t\t\t\t\t\tthis.lastUpdateTime = now;\n\t\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 额外检查：即将结束或即将开始的轮次需要重新处理数据\n\t\t\t\t\tconst needsDataUpdate = this.currentDisplayTasks.some(task => {\n\t\t\t\t\t\treturn task.rounds_detail && task.rounds_detail.some(round => {\n\t\t\t\t\t\t\tconst startTime = new Date(round.start_time);\n\t\t\t\t\t\t\tconst endTime = new Date(round.end_time);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 检查即将结束的轮次（5分钟内）\n\t\t\t\t\t\t\tconst timeUntilEnd = endTime - now;\n\t\t\t\t\t\t\tif (timeUntilEnd > 0 && timeUntilEnd <= 300000) {\n\t\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 检查即将开始的轮次（5分钟内）\n\t\t\t\t\t\t\tconst timeUntilStart = startTime - now;\n\t\t\t\t\t\t\tif (timeUntilStart > 0 && timeUntilStart <= 300000) {\n\t\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (needsDataUpdate) {\n\t\t\t\t\t\t// 重新处理当前显示的任务\n\t\t\t\t\t\tthis.currentDisplayTasks = this.processTasks(this.currentDisplayTasks);\n\t\t\t\t\t\t// 同时更新缓存中的数据\n\t\t\t\t\t\tconst currentYearMonth = this.getYearMonth(this.selectedDate);\n\t\t\t\t\t\tif (this.cachedTasks[currentYearMonth]) {\n\t\t\t\t\t\t\tthis.cachedTasks[currentYearMonth] = this.processTasks(this.cachedTasks[currentYearMonth]);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}, 60000); // 每60秒更新一次\n\t\t},\n\t\t// 预加载相邻月份\n\t\tasync preloadAdjacentMonths() {\n\t\t\ttry {\n\t\t\t\tconst currentDate = new Date();\n\t\t\t\tconst prevMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);\n\t\t\t\tconst nextMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);\n\t\t\t\t\n\t\t\t\tconst prevYearMonth = this.getYearMonth(prevMonth);\n\t\t\t\tconst nextYearMonth = this.getYearMonth(nextMonth);\n\t\t\t\t\n\t\t\t\t// 并行加载前后月份，提升加载效率\n\t\t\t\tconst loadPromises = [];\n\t\t\t\t\n\t\t\t\tif (!this.loadedMonths[prevYearMonth]) {\n\t\t\t\t\tloadPromises.push(this.loadMonthTasks(prevYearMonth).catch(e => {\n\t\t\t\t\t\tconsole.error('预加载上个月失败:', e);\n\t\t\t\t\t}));\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (!this.loadedMonths[nextYearMonth]) {\n\t\t\t\t\tloadPromises.push(this.loadMonthTasks(nextYearMonth).catch(e => {\n\t\t\t\t\t\tconsole.error('预加载下个月失败:', e);\n\t\t\t\t\t}));\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 等待所有预加载完成\n\t\t\t\tawait Promise.all(loadPromises);\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('预加载相邻月份失败:', e);\n\t\t\t\t// 预加载失败不影响主要功能\n\t\t\t}\n\t\t}\n}\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n\twidth: 100%;\n\tmin-height: 100vh;\n\tbackground-color: #F7F8FA;\n\tposition: relative;\n}\n\n.task-container {\n\twidth: 100%;\n\tmin-height: 100vh; \n\tdisplay: flex;\n\tflex-direction: column;\n\tbackground-color: #F7F8FA;\n\tposition: relative;\n\tpadding-bottom: 80rpx;\n}\n\n/* 日历视图 - 增加顶部padding */\n.calendar-container {\n\tdisplay: flex;\n\tflex-direction: column;\n\tbackground-color: #FFFFFF;\n\tpadding: 30rpx 0 10rpx 0; /* 增加顶部空间 */\n\tborder-bottom: 1rpx solid #EAEAEA;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.03);\n}\n\n.calendar-view {\n\tbackground-color: #fff;\n\tpadding: 20rpx;\n\tmargin-bottom: 15rpx;\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);\n\tborder-radius: 16rpx;\n\tmargin: 0 15rpx 15rpx 15rpx;\n}\n\n.calendar-header {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n}\n\n.month-selector {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.arrow-btn {\n\tpadding: 10rpx 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\ttransition: all 0.2s ease;\n\t\n\t&:active {\n\t\ttransform: scale(0.9);\n\t}\n}\n\n.current-month {\n\tfont-size: 34rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin: 0 20rpx;\n}\n\n.weekday-header {\n\tdisplay: flex;\n\tjustify-content: space-around;\n\tmargin-bottom: 15rpx;\n}\n\n.weekday-item {\n\twidth: 14.28%;\n\ttext-align: center;\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tpadding: 8rpx 0;\n}\n\n.days-grid {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n}\n\n.day-cell {\n\twidth: 14.28%;\n\theight: 76rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tposition: relative;\n\ttransition: all 0.3s ease;\n\tborder-radius: 12rpx;\n\tmargin: 2rpx 0;\n\t\n\t&:active {\n\t\topacity: 0.7;\n\t}\n}\n\n.day-number {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tline-height: 1.4;\n}\n\n.other-month .day-number {\n\tcolor: #ccc;\n}\n\n.today {\n\tbackground-color: rgba(22, 119, 255, 0.08);\n\tborder-radius: 12rpx;\n}\n\n.selected {\n\tbackground-color: #1677FF;\n\tborder-radius: 12rpx;\n\ttransform: scale(0.95);\n\tbox-shadow: 0 4rpx 12rpx rgba(22, 119, 255, 0.2);\n}\n\n.selected .day-number {\n\tcolor: #fff;\n\tfont-weight: bold;\n}\n\n.has-task .task-indicator {\n\twidth: 8rpx;\n\theight: 8rpx;\n\tbackground-color: #1677FF;\n\tborder-radius: 50%;\n\tposition: absolute;\n\tbottom: 10rpx;\n\tanimation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n\t0% {\n\t\ttransform: scale(0.9);\n\t\topacity: 0.7;\n\t}\n\t50% {\n\t\ttransform: scale(1.1);\n\t\topacity: 1;\n\t}\n\t100% {\n\t\ttransform: scale(0.9);\n\t\topacity: 0.7;\n\t}\n}\n\n.selected.has-task .task-indicator {\n\tbackground-color: #fff;\n}\n\n/* 任务筛选 */\n.filter-section {\n\tbackground-color: #fff;\n\tpadding: 15rpx;\n\tmargin: 0 15rpx 20rpx 15rpx;\n\tborder-radius: 16rpx;\n\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.03);\n}\n\n.filter-scroll {\n\twidth: 100%;\n}\n\n.filter-options {\n\tdisplay: flex;\n\twhite-space: nowrap;\n\tpadding: 5rpx 0;\n}\n\n.filter-option {\n\tdisplay: inline-block;\n\tpadding: 12rpx 28rpx;\n\tmargin-right: 15rpx;\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tbackground-color: #f7f8fa;\n\tborder-radius: 30rpx;\n\ttransition: all 0.3s;\n\t\n\t&:active {\n\t\ttransform: scale(0.95);\n\t}\n}\n\n.filter-option.active {\n\tbackground-color: #1677FF;\n\tcolor: #fff;\n\tbox-shadow: 0 4rpx 12rpx rgba(22, 119, 255, 0.2);\n\tfont-weight: 500;\n}\n\n/* 任务列表 */\n.task-list-section {\n\tbackground-color: #fff;\n\tpadding: 20rpx;\n\tmargin-bottom: 20rpx;\n\tborder-radius: 16rpx;\n\tmargin: 0 15rpx 15rpx 15rpx;\n\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.03);\n}\n\n.section-title {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 25rpx;\n\tpadding: 10rpx;\n\tborder-bottom: 1rpx solid #f5f5f5;\n}\n\n.section-title text {\n\tfont-size: 32rpx;\n\tcolor: #333;\n\tfont-weight: bold;\n}\n\n.task-count {\n\tfont-size: 24rpx;\n\tcolor: #1677FF;\n\tfont-weight: normal;\n\tbackground: rgba(22, 119, 255, 0.08);\n\tpadding: 6rpx 20rpx;\n\tborder-radius: 20rpx;\n}\n\n.task-items {\n\twidth: 100%;\n}\n\n.empty-list {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 80rpx 0;\n\twidth: 100%;\n}\n\n.empty-image {\n\twidth: 180rpx;\n\theight: 180rpx;\n\tmargin-bottom: 20rpx;\n\topacity: 0.8;\n}\n\n.empty-text {\n\tfont-size: 28rpx;\n\tcolor: #999;\n}\n\n/* 优化后的任务卡片样式 */\n.task-item {\n\tmargin-bottom: 24rpx;\n\tbackground-color: #fff;\n\tborder-radius: 16rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\tborder: 1rpx solid #f0f0f0;\n\ttransition: all 0.3s ease;\n\tposition: relative;\n\t\n\t&::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 0;\n\t\tbottom: 0;\n\t\twidth: 6rpx;\n\t\tbackground-color: #1677FF;\n\t\tborder-radius: 3rpx 0 0 3rpx;\n\t}\n\t\n\t&:active {\n\t\ttransform: scale(0.98);\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);\n\t}\n}\n\n.task-time {\n\tbackground-color: #f9fafc;\n\tpadding: 18rpx 24rpx;\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.task-date-container {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.task-date {\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-left: 10rpx;\n}\n\n.task-content {\n\tpadding: 24rpx;\n}\n\n.task-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n}\n\n.task-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmax-width: 70%;\n\tword-break: break-all;\n\tmargin-right: 10rpx;\n}\n\n.task-type {\n\tbackground-color: rgba(22, 119, 255, 0.08);\n\tpadding: 6rpx 16rpx;\n\tborder-radius: 20rpx;\n}\n\n.type-label {\n\tfont-size: 22rpx;\n\tcolor: #1677FF;\n\tfont-weight: 500;\n}\n\n/* 网格布局信息块 */\n.task-info-grid {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tmargin-bottom: 24rpx;\n\tbackground-color: #f9fafc;\n\tborder-radius: 12rpx;\n\tpadding: 15rpx;\n}\n\n.info-block {\n\twidth: 50%; /* 两列布局 */\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 12rpx;\n\tbox-sizing: border-box;\n}\n\n.info-icon {\n\twidth: 40rpx;\n\theight: 40rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 12rpx;\n}\n\n.info-content {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.info-label {\n\tfont-size: 22rpx;\n\tcolor: #999;\n\tmargin-bottom: 6rpx;\n}\n\n.info-value {\n\tfont-size: 26rpx;\n\tcolor: #333;\n}\n\n/* 进度条相关样式 */\n.progress-container {\n\tmargin-top: 20rpx;\n\tbackground-color: #f9fafc;\n\tborder-radius: 12rpx;\n\tpadding: 20rpx;\n}\n\n.progress-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 15rpx;\n}\n\n.progress-title {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tfont-weight: bold;\n}\n\n.progress-percent {\n\tfont-size: 26rpx;\n\tcolor: #1677FF;\n\tfont-weight: bold;\n}\n\n.progress-inner {\n\theight: 100%;\n\tbackground-color: #1677FF;\n\tborder-radius: 10rpx;\n\ttransition: width 0.5s cubic-bezier(0.22, 0.61, 0.36, 1);\n\tposition: relative;\n\toverflow: hidden;\n\t\n\t&::after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: linear-gradient(90deg, \n\t\t\trgba(255, 255, 255, 0.1) 0%, \n\t\t\trgba(255, 255, 255, 0.2) 50%, \n\t\t\trgba(255, 255, 255, 0.1) 100%);\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n}\n\n.progress-inner.completed {\n\tbackground-color: #52C41A;\n}\n\n.progress-inner.partial {\n\tbackground-color: #FAAD14;\n}\n\n.progress-inner.expired {\n\tbackground-color: #FF4D4F;\n}\n\n.progress-info {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tmargin-top: 12rpx;\n}\n\n.point-stat {\n\tdisplay: flex;\n\talign-items: center;\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n.point-stat text {\n\tmargin-left: 8rpx;\n}\n\n/* 状态标签样式 - 增强视觉效果 */\n.status-tag {\n\tfont-size: 22rpx;\n\tpadding: 6rpx 18rpx;\n\tborder-radius: 20rpx;\n\tfont-weight: 500;\n\tbox-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);\n}\n\n.status-tag.status-0 {\n\tbackground-color: #F5F5F5;\n\tcolor: #666666;\n}\n\n.status-tag.status-1 {\n\tbackground-color: rgba(22, 119, 255, 0.1);\n\tcolor: #1677FF;\n}\n\n.status-tag.status-2 {\n\tbackground-color: rgba(82, 196, 26, 0.1);\n\tcolor: #52C41A;\n}\n\n.status-tag.status-3 {\n\tbackground-color: rgba(250, 173, 20, 0.1);\n\tcolor: #FAAD14;\n}\n\n.status-tag.status-4 {\n\tbackground-color: rgba(255, 77, 79, 0.1);\n\tcolor: #FF4D4F;\n}\n\n.loading-box {\n\tbackground-color: rgba(0, 0, 0, 0.7);\n\tpadding: 30rpx 60rpx;\n\tborder-radius: 16rpx;\n}\n\n.refresh-box {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    height: 80rpx;\n    background-color: #f9fafc;\n    padding: 10rpx 0;\n}\n\n.refresh-icon {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n\n.refreshing {\n    animation: rotate 1s linear infinite;\n}\n\n@keyframes rotate {\n    from { transform: rotate(0deg); }\n    to { transform: rotate(360deg); }\n}\n\n.refresh-text {\n    font-size: 26rpx;\n    color: #666;\n    margin-left: 12rpx;\n}\n\n// 修改加载更多组件的样式\n.uni-load-more {\n\t.uni-load-more__img {\n\t\twidth: 28px;\n\t\theight: 28px;\n\t}\n\t\n\t.uni-load-more__text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t}\n}\n\n.time-text {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tmargin-left: 8rpx;\n}\n\n.shift-icon, .user-icon, .area-icon, .points-icon {\n\tcolor: #666;\n}\n\n/* 按钮样式 */\n.action-buttons {\n\tdisplay: flex;\n\tjustify-content: space-around; /* 改回均匀分布，不再靠右显示 */\n\tpadding: 20rpx 30rpx;\n\tmargin: 0 15rpx 20rpx 15rpx;\n\tbackground-color: #fff;\n\tborder-radius: 16rpx;\n\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.03);\n\tgap: 20rpx; /* 添加间距 */\n}\n\n.add-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 16rpx 30rpx;\n\tborder-radius: 30rpx;\n\tbackground-color: #1677FF;\n\tcolor: #FFFFFF;\n\tfont-size: 28rpx;\n\tbox-shadow: 0 2rpx 8rpx rgba(22, 119, 255, 0.2);\n\tmin-width: 200rpx;\n}\n\n.add-btn.batch {\n\tbackground-color: #13C2C2;\n\tbox-shadow: 0 2rpx 8rpx rgba(19, 194, 194, 0.2);\n}\n\n.add-btn uni-icons {\n\tmargin-right: 8rpx;\n}\n\n/* 任务操作按钮 */\n.action-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 12rpx 24rpx; /* 增加内边距 */\n\tborder-radius: 30rpx;\n\tbackground-color: rgba(0, 0, 0, 0.03);\n\tfont-size: 24rpx;\n}\n\n.action-btn text {\n\tmargin-left: 6rpx;\n}\n\n.edit-btn {\n\tcolor: #3688FF;\n}\n\n.delete-btn {\n\tcolor: #FF4D4F;\n}\n\n/* 轮次进度显示 */\n.rounds-progress {\n\tmargin-top: 12rpx;\n}\n\n.round-item {\n\tmargin-bottom: 12rpx;\n\tpadding-bottom: 12rpx;\n\tborder-bottom: 1rpx dashed #eee;\n}\n\n.round-item.last-item {\n\tmargin-bottom: 0;\n\tpadding-bottom: 0;\n\tborder-bottom: none;\n}\n\n.round-info {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 8rpx;\n}\n\n.round-name {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tfont-weight: 500;\n}\n\n.round-status {\n\tfont-size: 22rpx;\n\tpadding: 2rpx 10rpx;\n\tborder-radius: 10rpx;\n\t\n\t&.expired {\n\t\tbackground-color: #FFF1F0;\n\t\tcolor: #F5222D;\n\t}\n\t\n\t&.completed {\n\t\tbackground-color: #F6FFED;\n\t\tcolor: #52C41A;\n\t}\n\t\n\t&.partial {\n\t\tbackground-color: #FFF7E6;\n\t\tcolor: #FA8C16;\n\t}\n\t\n\t&.ongoing {\n\t\tbackground-color: #E6F7FF;\n\t\tcolor: #1677FF;\n\t}\n}\n\n.round-progress-bar {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.round-percent {\n\tfont-size: 22rpx;\n\tcolor: #1677FF;\n\tmargin-left: 10rpx;\n\twidth: 40rpx;\n}\n\n/* 没有轮次时的提示 */\n.no-rounds-tip {\n\tdisplay: flex;\n\tjustify-content: center;\n\tpadding: 20rpx 0;\n\tcolor: #999;\n\tfont-size: 24rpx;\n}\n\n/* 添加样式 */\n.round-name-time {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.round-time {\n\tfont-size: 22rpx;\n\tcolor: #666;\n\tmargin-left: 8rpx;\n}\n\n.round-nextday {\n\tfont-size: 20rpx;\n\tcolor: #1677FF;\n\tmargin-left: 4rpx;\n}\n\n/* 添加轮次显示相关样式 */\n.round-details {\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n}\n\n.round-duration {\n    font-size: 22rpx;\n    color: #666;\n    margin-top: 4rpx;\n}\n\n.time-remaining {\n    color: #1677FF;\n    margin-left: 4rpx;\n}\n\n/* 🔥 新增：开始倒计时样式 */\n.time-countdown {\n    color: #FAAD14;\n    margin-left: 4rpx;\n    font-weight: 500;\n}\n\n.round-status-badge {\n    font-size: 22rpx;\n    padding: 4rpx 12rpx;\n    border-radius: 10rpx;\n    white-space: nowrap;\n    margin-left: 10rpx;\n    \n    &.waiting {\n        background-color: #F5F5F5;\n        color: #666666;\n    }\n    \n    &.ongoing {\n        background-color: #E6F7FF;\n        color: #1677FF;\n    }\n    \n    &.finished {\n        background-color: #F6FFED;\n        color: #52C41A;\n    }\n    \n    &.expired {\n        background-color: #FFF1F0;\n        color: #F5222D;\n    }\n    \n    &.canceled {\n        background-color: #F5F5F5;\n        color: #999999;\n    }\n}\n\n/* 骨架屏样式 */\n.skeleton-list {\n\tpadding: 0;\n}\n\n.skeleton-item {\n\tmargin-bottom: 24rpx;\n\tbackground-color: #fff;\n\tborder-radius: 16rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\tborder: 1rpx solid #f0f0f0;\n}\n\n.skeleton-header {\n\tbackground-color: #f9fafc;\n\tpadding: 18rpx 24rpx;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.skeleton-content {\n\tpadding: 24rpx;\n}\n\n.skeleton-line {\n\tbackground: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n\tbackground-size: 200% 100%;\n\tanimation: skeleton-loading 1.5s infinite;\n\tborder-radius: 4rpx;\n\tmargin-bottom: 12rpx;\n}\n\n.skeleton-date {\n\twidth: 120rpx;\n\theight: 28rpx;\n}\n\n.skeleton-status {\n\twidth: 80rpx;\n\theight: 28rpx;\n}\n\n.skeleton-title {\n\twidth: 60%;\n\theight: 32rpx;\n}\n\n.skeleton-info {\n\twidth: 80%;\n\theight: 24rpx;\n}\n\n.skeleton-progress {\n\twidth: 100%;\n\theight: 20rpx;\n}\n\n@keyframes skeleton-loading {\n\t0% {\n\t\tbackground-position: -200% 0;\n\t}\n\t100% {\n\t\tbackground-position: 200% 0;\n\t}\n}\n\n/* 添加微信小程序隐藏滚动条的样式 */\n.filter-scroll {\n\t/* 隐藏滚动条同时允许滚动 */\n\t::-webkit-scrollbar {\n\t\twidth: 0;\n\t\theight: 0;\n\t\tcolor: transparent;\n\t\tdisplay: none;\n\t}\n\t/* 微信小程序专用 */\n\t&::-webkit-scrollbar {\n\t\tdisplay: none;\n\t\twidth: 0 !important;\n\t\theight: 0 !important;\n\t\t-webkit-appearance: none;\n\t\tbackground: transparent;\n\t}\n\t/* 兼容不同浏览器 */\n\tscrollbar-width: none; /* Firefox */\n\t-ms-overflow-style: none; /* IE and Edge */\n}\n\n.title-actions {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 20rpx;\n}\n\n.batch-add-btn {\n\tdisplay: none;\n}\n\n/* 添加任务卡片中编辑删除按钮的样式 */\n.task-actions {\n\tdisplay: flex;\n\tjustify-content: flex-end; /* 靠右对齐 */\n\tmargin-top: 16rpx;\n\tgap: 16rpx; /* 按钮之间的间距 */\n}\n</style>", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=3bf106d6&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=3bf106d6&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775845041\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}