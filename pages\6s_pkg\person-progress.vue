<template>
  <view class="page-container">
    <view class="content">
      <!-- 负责人信息卡片 -->
      <view class="person-card">
        <view class="person-header">
          <view class="person-avatar">
            <uni-icons type="person" size="32" color="#007AFF"></uni-icons>
          </view>
          <view class="person-info">
            <view class="person-name">{{ personData.name }}</view>
            <view class="person-role">{{ personData.role }}</view>
          </view>
        </view>
        
        <view class="progress-summary">
          <view class="summary-item">
            <view class="summary-number primary">{{ personData.total }}</view>
            <view class="summary-label">总问题</view>
          </view>
          <view class="summary-item">
            <view class="summary-number success">{{ personData.completed }}</view>
            <view class="summary-label">已完成</view>
          </view>
          <view class="summary-item">
            <view class="summary-number warning">{{ personData.inProgress }}</view>
            <view class="summary-label">进行中</view>
          </view>
          <view class="summary-item">
            <view class="summary-number danger">{{ personData.overdue }}</view>
            <view class="summary-label">已逾期</view>
          </view>
        </view>
        
        <view class="completion-rate">
          <view class="rate-info">
            <text class="rate-label">完成率</text>
            <text class="rate-value">{{ completionRate }}%</text>
          </view>
          <view class="rate-bar">
            <view class="rate-fill" :style="{ width: completionRate + '%' }"></view>
          </view>
        </view>
      </view>

      <!-- 问题筛选 -->
      <view class="filter-card">
        <view class="card-header">
          <scroll-view class="filter-tabs" scroll-x="true" show-scrollbar="false">
            <view 
              v-for="filter in statusFilters" 
              :key="filter.value"
              class="filter-tab"
              :class="{ active: currentFilter === filter.value }"
              @click="changeFilter(filter.value)"
            >
              {{ filter.label }}
            </view>
          </scroll-view>
        </view>
      </view>

      <!-- 问题列表 -->
      <view class="issues-card">
        <view class="card-header">
          <view class="card-title">{{ getFilterTitle() }}</view>
          <view class="card-subtitle">共{{ filteredIssues.length }}个问题</view>
        </view>
        
        <view v-if="filteredIssues.length > 0" class="issues-list">
          <view 
            v-for="(issue, index) in displayedIssues" 
            :key="index"
            class="issue-item"
            @click="viewIssueDetail(issue)"
          >
            <view class="issue-icon" :class="['icon-bg-' + issue.status]">
              <text class="issue-number">{{ String(issue.number).padStart(2, '0') }}</text>
            </view>
            <view class="issue-content">
              <view class="issue-title">{{ issue.title }}</view>
              <view class="issue-location">
                <uni-icons type="location" size="14" color="#8E8E93"></uni-icons>
                <text>{{ issue.location }}</text>
              </view>
              <view class="issue-deadline">
                <uni-icons type="calendar" size="14" color="#8E8E93"></uni-icons>
                <text>截止：{{ issue.deadline }}</text>
              </view>
            </view>
            <view class="issue-right">
              <view class="issue-status" :class="['status-' + issue.status]">
                {{ getStatusText(issue.status) }}
              </view>
              <uni-icons type="right" size="14" color="#C7C7CC"></uni-icons>
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <p-empty-state
          v-else
          useIcon
          iconName="info"
          iconColor="#C7C7CC"
          size="large"
          :text="'暂无' + (getFilterTitle() || '数据')"
        ></p-empty-state>
        
        <!-- 查看更多按钮 -->
        <view v-if="hasMoreIssues" class="more-section">
          <view class="more-btn" @click="showMoreIssues">
            <uni-icons type="down" size="16" color="#007AFF"></uni-icons>
            <text>查看更多问题 ({{ remainingIssuesCount }})</text>
          </view>
        </view>
      </view>

      <!-- 底部安全间距 -->
      <view class="bottom-safe-area"></view>
    </view>

    <!-- 加载状态 - 自定义遮罩 -->
    <view v-if="loading" class="custom-loading-mask">
      <view class="loading-container-enhanced">
        <uni-icons type="spinner-cycle" size="48" color="#007AFF"></uni-icons>
        <text class="loading-text-enhanced">{{ loadingText }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PersonProgress',
  data() {
    return {
      loading: false,
      loadingText: '加载中...',
      personId: null,
      personName: '',
      currentFilter: 'all',
      hasManagePermission: false,
      // 分页控制
      displayLimit: 6, // 初始显示数量
      displayStep: 4,   // 每次加载更多的数量
      personData: {
        id: '',
        name: '',
        role: '',
        total: 0,
        completed: 0,
        inProgress: 0,
        overdue: 0,
        issues: []
      },
      statusFilters: [
        { label: '全部', value: 'all' },
        { label: '已分配', value: 'assigned' },
        { label: '待整改', value: 'pending' },
        { label: '整改中', value: 'in_progress' },
        { label: '待检查', value: 'pending_review' },
        { label: '检查通过', value: 'approved' }
      ],
      // 数据缓存（性能优化）
      dataCache: new Map(),
      cacheExpireTime: 3 * 60 * 1000, // 3分钟缓存
      lastLoadTime: 0
    }
  },
  computed: {
    completionRate() {
      if (this.personData.total === 0) return 0
      return Math.round((this.personData.completed / this.personData.total) * 100)
    },
    filteredIssues() {
      if (this.currentFilter === 'all') {
        return this.personData.issues
      }
      return this.personData.issues.filter(issue => issue.status === this.currentFilter)
    },
    displayedIssues() {
      return this.filteredIssues.slice(0, this.displayLimit)
    },
    hasMoreIssues() {
      return this.filteredIssues.length > this.displayLimit
    },
    remainingIssuesCount() {
      return this.filteredIssues.length - this.displayLimit
    }
  },
  onLoad(options) {
    this.personId = options.id;
    this.personName = decodeURIComponent(options.name || '负责人');
    
    this.loadPersonData();
    this.checkUserPermissions();
    
    // 监听数据更新事件
    uni.$on('monthlyIssueUpdated', this.handleDataUpdated);
    uni.$on('issueDraftUpdated', this.handleDataUpdated);
  },

  onUnload() {
    // 移除事件监听
    uni.$off('monthlyIssueUpdated', this.handleDataUpdated);
    uni.$off('issueDraftUpdated', this.handleDataUpdated);
  },

  onShow() {
    // 智能刷新：检查缓存是否过期
    this.smartRefresh();
  },
  methods: {

    async loadPersonData() {
      this.showLoading('加载个人进度数据...')
      try {
        const { callCloudFunction } = require('@/utils/auth.js');
        
        // 并行调用获取个人问题数据和用户信息
        const [issuesResult, userResult] = await Promise.all([
          this.loadPersonIssues(),
          this.loadPersonInfo()
        ]);
        
        const issues = issuesResult || [];
        const userInfo = userResult || { name: this.personName, role: '负责人' };
        
        // 计算统计数据
        const total = issues.length;
        const completed = issues.filter(issue => issue.status === 'approved').length;
        const inProgress = issues.filter(issue => issue.status === 'in_progress').length;
        const overdue = issues.filter(issue => issue.status === 'overdue').length;
        
        this.personData = {
          id: this.personId,
          name: userInfo.name || this.personName,
          role: userInfo.role || '负责人',
          total,
          completed,
          inProgress,
          overdue,
          issues
        };
        
        this.updateFilterCounts();
        
      } catch (error) {
        console.error('加载个人进度数据失败:', error);
        
        // 使用默认空数据
        this.personData = {
          id: this.personId,
          name: this.personName,
          role: '负责人',
          total: 0,
          completed: 0,
          inProgress: 0,
          overdue: 0,
          issues: []
        };
        
        this.updateFilterCounts();
        
        uni.showToast({
          title: '加载数据失败',
          icon: 'none'
        });
      } finally {
        this.hideLoading();
        this.lastLoadTime = Date.now();
      }
    },

    // 检查用户权限 - 与其他页面保持一致
    async checkUserPermissions() {
      try {
        const userInfo = uni.getStorageSync('uni-id-pages-userInfo') || {};
        const role = userInfo.role || [];
        
        // 管理权限：管理员、厂长、6S专员
        const adminRoles = ['admin', 'GM', 'Integrated', 'reviser'];
        this.hasManagePermission = role.some(r => adminRoles.includes(r));
        
      } catch (error) {
        console.error('获取用户权限失败:', error);
        this.hasManagePermission = false;
      }
    },

    // 智能刷新（性能优化）
    async smartRefresh() {
      const now = Date.now();
      const timeSinceLastLoad = now - this.lastLoadTime;
      
      // 如果距离上次加载超过3分钟，或者数据为空，则静默刷新
      if (timeSinceLastLoad > this.cacheExpireTime || this.personData.issues.length === 0) {
        await this.loadPersonDataSilently();
      }
    },

    // 静默加载个人数据
    async loadPersonDataSilently() {
      try {
        const { callCloudFunction } = require('@/utils/auth.js');
        
        const [issuesResult, userResult] = await Promise.all([
          this.loadPersonIssues(),
          this.loadPersonInfo()
        ]);
        
        if (issuesResult && issuesResult.length > 0) {
          const issues = issuesResult;
          const userInfo = userResult || { name: this.personName, role: '负责人' };
          
          // 计算统计数据
          const total = issues.length;
          const completed = issues.filter(issue => issue.status === 'approved').length;
          const inProgress = issues.filter(issue => issue.status === 'in_progress').length;
          const overdue = issues.filter(issue => issue.status === 'overdue').length;
          
          this.personData = {
            ...this.personData,
            total,
            completed,
            inProgress,
            overdue,
            issues
          };
          
          this.updateFilterCounts();
          this.lastLoadTime = Date.now();
        }
      } catch (error) {
        // 静默处理刷新失败
      }
    },

    // 加载个人问题数据
    async loadPersonIssues() {
      const { callCloudFunction } = require('@/utils/auth.js');
      
      try {
        // 调用新的月度检查云函数获取分配给该人员的问题
        const result = await callCloudFunction('hygiene-monthly-inspection', {
          action: 'getPersonIssues',
          data: {
            responsiblePersonId: this.personId,
            page: 1,
            pageSize: 50
          }
        });
        
        if (result && result.success && result.data && result.data.list) {
          return result.data.list.map((issue, index) => ({
            id: issue._id || issue.id,
            number: index + 1,
            title: issue.title || issue.description,
            location: this.formatLocation(issue.location_info) || issue.location || '未知位置',
            deadline: issue.expected_completion_date ? 
              new Date(issue.expected_completion_date).toISOString().split('T')[0] : 
              this.getDefaultDeadline(),
            status: this.mapIssueStatus(issue.status),
            priority: issue.priority || 'normal',
            description: issue.description
          }));
        }
      } catch (error) {
        console.error('获取个人问题失败:', error);
      }
      
      return [];
    },

    // 加载个人信息
    async loadPersonInfo() {
      const { callCloudFunction } = require('@/utils/auth.js');
      
      try {
        // 通过新的月度检查云函数获取用户信息
        const result = await callCloudFunction('hygiene-monthly-inspection', {
          action: 'getUserInfo',
          data: {
            userId: this.personId
          }
        });
        
        if (result && result.success && result.data) {
          return {
            name: result.data.nickname || result.data.username || this.personName,
            role: this.getRoleDisplayName(result.data.role || [])
          };
        }
      } catch (error) {
        console.error('获取个人信息失败:', error);
      }
      
      return null;
    },

    // 映射问题状态 - 与其他页面保持一致
    mapIssueStatus(apiStatus) {
      const statusMap = {
        'submitted': 'pending',
        'assigned': 'assigned',     // 已分配
        'pending': 'pending',       // 待整改
        'in_progress': 'in_progress', // 整改中
        'pending_review': 'pending_review', // 待检查
        'approved': 'approved',     // 检查通过
        'closed': 'closed',         // 已关闭
        'resolved': 'approved',     // API的resolved映射为approved
        'overdue': 'overdue',
        'rejected': 'rejected',     // 已驳回
        'reopened': 'pending',      // 重新打开映射为待整改
        'draft': 'pending'
      };
      return statusMap[apiStatus] || 'pending';
    },

    // 获取默认截止日期（7天后）
    getDefaultDeadline() {
      const date = new Date();
      date.setDate(date.getDate() + 7);
      return date.toISOString().split('T')[0];
    },

    // 格式化位置信息 - 与monthly-check.vue保持一致
    formatLocation(locationInfo) {
      if (!locationInfo) return null;
      
      let location = locationInfo.location_name || locationInfo.name || null;
      
      // 如果有分类信息，添加到前面
      if (locationInfo.location_category) {
        location = `${locationInfo.location_category} - ${location}`;
      }
      
      return location;
    },

    // 获取角色显示名称 - 与其他页面保持统一
    getRoleDisplayName(roles) {
      if (!roles || !Array.isArray(roles) || roles.length === 0) {
        return '普通员工';
      }
      
      // 统一的角色映射表 - 与用户中心、员工分配等页面保持一致
      const roleMap = {
        'admin': '管理员',
        'responsible': '负责人',
        'reviser': '发布人',
        'supervisor': '主管',
        'PM': '副厂长',
        'GM': '厂长',
        'logistics': '后勤员',
        'dispatch': '调度员',
        'Integrated': '综合员',
        'operator': '设备员',
        'technician': '工艺员',
        'mechanic': '技术员',
        'user': '普通员工'
      };
      
      // 如果有多个角色，显示第一个主要角色
      return roleMap[roles[0]] || '普通员工';
    },

    updateFilterCounts() {
      // 统计数字已移除，保留方法以避免调用错误
    },

    changeFilter(value) {
      this.currentFilter = value;
      // 切换筛选条件时重置显示限制
      this.displayLimit = 6;
      
      // 性能优化：延迟更新筛选结果，减少频繁计算
      this.$nextTick(() => {
        // 筛选操作在计算属性中完成，这里只需要触发重新渲染
      });
    },

    getFilterTitle() {
      const filter = this.statusFilters.find(f => f.value === this.currentFilter)
      return filter ? filter.label + '问题' : '问题'
    },

    getStatusText(status) {
      const statusMap = {
        'assigned': '已分配',
        'pending': '待整改',
        'in_progress': '整改中',
        'pending_review': '待检查',
        'approved': '检查通过',
        'rejected': '已驳回',
        'overdue': '已逾期',
        'closed': '已关闭',
        'reopened': '重新打开'
      }
      return statusMap[status] || '未知'
    },

    // 生成问题历史记录（优化版，基于实际数据）
    generateIssueHistory(issue) {
      const history = [];
      
      // 创建问题
      if (issue.created_at) {
        history.push({
          action: '创建问题',
          description: '6S检查员发现问题并记录',
          time: this.formatHistoryTime(issue.created_at),
          operator: issue.reporter_name || '检查员'
        });
      }
      
      // 分配负责人
      if (issue.assigned_to_name) {
        history.push({
          action: '分配负责人',
          description: `将问题分配给${issue.assigned_to_name}处理`,
          time: this.formatHistoryTime(issue.updated_at || issue.created_at),
          operator: '管理员'
        });
      }
      
      // 根据状态添加相应的历史记录
      if (issue.status === 'in_progress') {
        history.push({
          action: '开始整改',
          description: '负责人已开始处理此问题',
          time: this.formatHistoryTime(issue.updated_at),
          operator: issue.assigned_to_name || this.personData.name
        });
      } else if (issue.status === 'approved') {
        history.push(
          {
            action: '开始整改',
            description: '负责人已开始处理此问题',
            time: this.formatHistoryTime(issue.updated_at),
            operator: issue.assigned_to_name || this.personData.name
          },
          {
            action: '整改完成',
            description: '整改措施已实施完成',
            time: this.formatHistoryTime(issue.actual_completion_date || issue.updated_at),
            operator: issue.assigned_to_name || this.personData.name
          }
        );
        
        // 如果有解决日期，表示已经通过检查
        if (issue.actual_completion_date) {
          history.push(
            {
              action: '开始检查',
              description: '检查员开始验证整改效果',
              time: this.formatHistoryTime(issue.actual_completion_date),
              operator: '检查员'
            },
            {
              action: '检查通过',
              description: '检查员确认整改合格',
              time: this.formatHistoryTime(issue.actual_completion_date),
              operator: '检查员'
            }
          );
        }
      }
      
      return history;
    },

    // 格式化历史记录时间
    formatHistoryTime(dateString) {
      if (!dateString) return '--';
      
      try {
        const date = new Date(dateString);
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
      } catch (error) {
        return '--';
      }
    },

    viewIssueDetail(issue) {
      // 补充完整的问题数据并存储
      const completeIssue = {
        ...issue,
        responsible: this.personData.name,
        role: this.personData.role,
        createdAt: '2024-01-15',
        updatedAt: '2024-01-15',
        images: ['/static/empty/default-image.png'],
        history: this.generateIssueHistory(issue)
      };
      
      uni.setStorageSync(`issue_detail_${issue.id}`, completeIssue);
      uni.navigateTo({
        url: `/pages/6s_pkg/issue-detail?id=${issue.id}`
      })
    },

    showMoreIssues() {
      // 增加显示数量
      this.displayLimit += this.displayStep;
    },

    showLoading(text = '加载中...') {
      this.loading = true
      this.loadingText = text
    },

    hideLoading() {
      this.loading = false
    },

    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    },

    // 处理数据更新事件
    handleDataUpdated(data) {
      // 清除缓存，确保获取最新数据
      if (this.dataCache) {
        this.dataCache.clear();
      }
      
      // 延迟一下再刷新，确保提交操作完全完成
      setTimeout(() => {
        this.refreshDataSilently();
      }, 200);
    },

    // 静默刷新数据（不显示加载状态）
    async refreshDataSilently() {
      if (this.loading) return; // 如果正在加载，跳过
      
      try {
        const [issuesResult, userResult] = await Promise.all([
          this.loadPersonIssues(),
          this.loadPersonInfo()
        ]);
        
        if (issuesResult && issuesResult.length > 0) {
          const issues = issuesResult;
          const userInfo = userResult || { name: this.personName, role: '负责人' };
          
          // 计算统计数据
          const total = issues.length;
          const completed = issues.filter(issue => 
            issue.status === 'approved'
          ).length;
          const inProgress = issues.filter(issue => issue.status === 'in_progress').length;
          const overdue = issues.filter(issue => issue.status === 'overdue').length;
          
          this.personData = {
            ...this.personData,
            total,
            completed,
            inProgress,
            overdue,
            issues
          };
          
          this.updateFilterCounts();
          this.lastLoadTime = Date.now();
        }
      } catch (error) {
        // 静默处理刷新失败
      }
    },

    // 数据刷新方法（供外部调用）
    async refreshData() {
      this.dataCache.clear();
      await this.loadPersonData();
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
	min-height: 100vh;
	background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
	width: 100%;
	overflow-x: hidden; /* 防止水平滚动 */
	box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
}

.content {
  padding: 32rpx;
  padding-bottom: 120rpx;
}

/* 卡片通用样式 */
.person-card,
.issues-card,
.chart-card {
  background: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

/* 筛选卡片特殊样式 - 控制溢出行为 */
.filter-card {
  background: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

/* 负责人信息卡片 */
.person-card {
  padding: 32rpx;
}

.person-header {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.person-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: rgba(0, 122, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.person-info {
  flex: 1;
}

.person-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 8rpx;
}

.person-role {
  font-size: 26rpx;
  color: #8E8E93;
}

.progress-summary {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;
  margin-bottom: 32rpx;
  text-align: center;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-number {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;

  &.primary {
    color: #007AFF;
  }

  &.success {
    color: #34C759;
  }

  &.warning {
    color: #FF9500;
  }

  &.danger {
    color: #FF3B30;
  }
}

.summary-label {
  font-size: 24rpx;
  color: #8E8E93;
}

.completion-rate {
  padding-top: 32rpx;
  border-top: 1rpx solid #F0F0F0;
}

.rate-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.rate-label {
  font-size: 28rpx;
  color: #1C1C1E;
  font-weight: 500;
}

.rate-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #007AFF;
}

.rate-bar {
  height: 12rpx;
  background: #F0F0F0;
  border-radius: 6rpx;
  overflow: hidden;
}

.rate-fill {
  height: 100%;
  background: linear-gradient(90deg, #007AFF 0%, #34C759 100%);
  border-radius: 6rpx;
  transition: width 0.5s ease;
}

/* 筛选卡片 */
.filter-card {
  overflow: hidden;
}

.filter-card .card-header {
  padding: 32rpx 32rpx 24rpx 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.filter-tabs {
  white-space: nowrap;
  padding: 12rpx 0 0 0;
  margin-top: 0rpx;
}

.filter-tabs::-webkit-scrollbar {
  display: none;
}

.filter-tab {
  display: inline-block;
  padding: 12rpx 20rpx;
  border: 2rpx solid #E5E5E5;
  background: #ffffff;
  border-radius: 24rpx;
  font-size: 24rpx;
  color: #8E8E93;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  letter-spacing: 0.3rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  white-space: nowrap;
  margin: 4rpx 12rpx 4rpx 0;

  &.active {
    border-color: #007AFF;
    background: linear-gradient(135deg, #007AFF, #5AC8FA);
    color: #ffffff;
    transform: translateY(-2rpx);
    box-shadow: 0 6rpx 16rpx rgba(0, 122, 255, 0.3);
  }
  
  &:hover:not(.active) {
    border-color: #B3D9FF;
    background: #F8FBFF;
    transform: translateY(-1rpx);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  }
}



/* 问题列表卡片 */
.issues-card {
  overflow: hidden;
}

.card-header {
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 4rpx;
}

.card-subtitle {
  font-size: 24rpx;
  color: #8E8E93;
}

.issues-list {
  padding: 0;
}

.issue-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F0F0F0;

  &:last-child {
    border-bottom: none;
  }
}

.issue-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;

  &.icon-bg-approved {
    background: rgba(52, 199, 89, 0.1);
  }

  &.icon-bg-in_progress {
    background: rgba(255, 149, 0, 0.1);
  }

  &.icon-bg-overdue {
    background: rgba(255, 59, 48, 0.1);
  }

  &.icon-bg-pending {
    background: rgba(0, 122, 255, 0.1);
  }

  &.icon-bg-assigned {
    background: rgba(0, 122, 255, 0.1);
  }

  &.icon-bg-pending_review {
    background: rgba(88, 86, 214, 0.1);
  }

  &.icon-bg-approved {
    background: rgba(52, 199, 89, 0.1);
  }

  &.icon-bg-rejected {
    background: rgba(255, 149, 0, 0.1);
  }
}

.issue-number {
  font-size: 28rpx;
  font-weight: bold;
  color: #1C1C1E;
}

.issue-icon.icon-bg-approved .issue-number {
  color: #34C759;
}

.issue-icon.icon-bg-in_progress .issue-number {
  color: #FF9500;
}

.issue-icon.icon-bg-overdue .issue-number {
  color: #FF3B30;
}

.issue-icon.icon-bg-pending .issue-number {
  color: #007AFF;
}

.issue-icon.icon-bg-assigned .issue-number {
  color: #007AFF;
}

.issue-icon.icon-bg-pending_review .issue-number {
  color: #5856D6;
}

.issue-icon.icon-bg-approved .issue-number {
  color: #34C759;
}

.issue-icon.icon-bg-rejected .issue-number {
  color: #FF9500;
}

.issue-content {
  flex: 1;
}

.issue-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #1C1C1E;
  margin-bottom: 8rpx;
}

.issue-location,
.issue-deadline {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 4rpx;
}

.issue-right {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.issue-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 600;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.08);

  &.status-approved {
    background: linear-gradient(135deg, #E8F5E8, #F0FFF0);
    color: #34C759;
    border: 2rpx solid rgba(52, 199, 89, 0.2);
  }

  &.status-in_progress {
    background: linear-gradient(135deg, #FFF4E6, #FFFBF0);
    color: #FF9500;
    border: 2rpx solid rgba(255, 149, 0, 0.2);
  }

  &.status-overdue {
    background: linear-gradient(135deg, #FFE6E6, #FFF0F0);
    color: #FF3B30;
    border: 2rpx solid rgba(255, 59, 48, 0.2);
  }

  &.status-pending {
    background: linear-gradient(135deg, #E6F3FF, #F0F7FF);
    color: #007AFF;
    border: 2rpx solid rgba(0, 122, 255, 0.2);
  }

  &.status-assigned {
    background: linear-gradient(135deg, #ECFEFF, #F0FDFF);
    color: #0891B2;
    border: 2rpx solid rgba(8, 145, 178, 0.2);
  }

  &.status-pending_review {
    background: linear-gradient(135deg, #F0EFFF, #F8F7FF);
    color: #5856D6;
    border: 2rpx solid rgba(88, 86, 214, 0.2);
  }

  &.status-approved {
    background: linear-gradient(135deg, #E8F5E8, #F0FFF0);
    color: #34C759;
    border: 2rpx solid rgba(52, 199, 89, 0.2);
  }

  &.status-rejected {
    background: linear-gradient(135deg, #F2F2F7, #F8F8F8);
    color: #8E8E93;
    border: 2rpx solid rgba(142, 142, 147, 0.2);
  }
}

/* 查看更多按钮样式 */
.more-section {
  padding: 28rpx;
  border-top: 1rpx solid #F0F0F0;
}

.more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 28rpx;
  font-size: 30rpx;
  color: #007AFF;
  border: 2rpx solid rgba(0, 122, 255, 0.2);
  border-radius: 16rpx;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(255, 255, 255, 0.9));
  font-weight: 600;
  letter-spacing: 0.5rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.more-btn:active {
  transform: translateY(2rpx);
  background: linear-gradient(135deg, rgba(230, 243, 255, 0.9), rgba(240, 247, 255, 0.95));
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.15);
  transition: all 0.1s ease;
}

.more-btn:not(:active) {
  transform: translateY(0);
}

/* 底部安全间距 */
.bottom-safe-area {
  height: 40rpx;
}

/* 加载状态 - 自定义遮罩 */
.custom-loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(8rpx);
  z-index: 9999;
  animation: maskFadeIn 0.3s ease-out;
  /* 确保相对于视口定位，而不是父容器 */
  margin: 0;
  padding: 0;
}

@keyframes maskFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.loading-container-enhanced {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 32rpx;
  padding: 80rpx 60rpx;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  min-width: 320rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.25), 
              0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
  animation: containerSlideIn 0.4s ease-out 0.1s both;
}

@keyframes containerSlideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.loading-text-enhanced {
  font-size: 32rpx;
  color: #1C1C1E;
  font-weight: 600;
  margin-top: 8rpx;
  text-align: center;
  line-height: 1.3;
}
</style> 