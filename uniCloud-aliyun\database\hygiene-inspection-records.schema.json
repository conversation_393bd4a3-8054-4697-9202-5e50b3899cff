{"bsonType": "object", "required": ["area_id", "inspector_id", "inspection_date", "result"], "permission": {"read": true, "create": "auth.role.includes('Integrated') || auth.role.includes('reviser') || auth.role.includes('admin')", "update": "doc.inspector_id == auth.uid || auth.role.includes('admin')", "delete": "auth.role.includes('admin')"}, "properties": {"_id": {"description": "ID，系统自动生成"}, "area_id": {"bsonType": "string", "title": "责任区ID", "description": "被检查的责任区ID", "foreignKey": "hygiene-areas._id", "errorMessage": {"required": "责任区ID不能为空"}}, "area_name": {"bsonType": "string", "title": "责任区名称", "description": "冗余存储的责任区名称", "maxLength": 50}, "inspector_id": {"bsonType": "string", "title": "检查员ID", "description": "执行检查的人员ID", "foreignKey": "uni-id-users._id", "errorMessage": {"required": "检查员ID不能为空"}}, "inspector_name": {"bsonType": "string", "title": "检查员姓名", "description": "冗余存储的检查员姓名", "maxLength": 50}, "inspection_date": {"bsonType": "timestamp", "title": "检查日期", "description": "执行检查的日期", "errorMessage": {"required": "检查日期不能为空"}}, "week_number": {"bsonType": "string", "title": "周次", "description": "检查所属的周次，格式如'2025-W03'", "pattern": "^\\d{4}-W\\d{2}$", "maxLength": 8}, "result": {"bsonType": "string", "title": "检查结果", "description": "检查的总体结果", "enum": ["excellent", "good", "fair", "poor", "failed"], "errorMessage": {"required": "检查结果不能为空", "enum": "请选择有效的检查结果"}}, "score": {"bsonType": "number", "title": "检查评分", "description": "检查的具体分数（0-100分）", "minimum": 0, "maximum": 100}, "overall_rating": {"bsonType": "number", "title": "整体评分", "description": "检查的整体质量评分（1-5分）", "minimum": 1, "maximum": 5}, "has_issues": {"bsonType": "bool", "title": "是否有问题", "description": "本次检查是否发现问题", "default": false}, "issues": {"bsonType": "array", "title": "发现的问题", "description": "检查中发现的具体问题列表", "items": {"bsonType": "object", "properties": {"category": {"bsonType": "string", "title": "问题类别", "enum": ["cleanliness", "organization", "safety", "equipment", "other"]}, "description": {"bsonType": "string", "title": "问题描述", "maxLength": 300}, "severity": {"bsonType": "string", "title": "严重程度", "enum": ["low", "medium", "high", "critical"], "default": "medium"}, "photos": {"bsonType": "array", "title": "问题照片", "items": {"bsonType": "string", "pattern": "^cloud://"}, "maxItems": 5}}}, "maxItems": 20}, "photos": {"bsonType": "array", "title": "检查照片", "description": "检查过程的照片记录", "items": {"bsonType": "object", "properties": {"url": {"bsonType": "string", "title": "图片URL", "pattern": "^cloud://"}, "description": {"bsonType": "string", "title": "照片说明", "maxLength": 100}, "timestamp": {"bsonType": "timestamp", "title": "拍摄时间"}}}, "maxItems": 10}, "recommendations": {"bsonType": "array", "title": "整改建议", "description": "针对发现问题的整改建议", "items": {"bsonType": "string", "maxLength": 200}, "maxItems": 10}, "next_inspection_date": {"bsonType": "timestamp", "title": "下次检查日期", "description": "建议的下次检查时间"}, "status": {"bsonType": "string", "title": "检查状态", "description": "检查记录的处理状态", "enum": ["completed", "pending_rectification", "rectification_completed", "verified"], "default": "completed"}, "reviewed_by": {"bsonType": "string", "title": "审核人", "description": "审核检查结果的人员ID", "foreignKey": "uni-id-users._id"}, "reviewed_at": {"bsonType": "timestamp", "title": "审核时间", "description": "审核的时间"}, "remarks": {"bsonType": "string", "title": "备注", "description": "检查过程的其他备注信息", "maxLength": 500}, "created_at": {"bsonType": "timestamp", "title": "创建时间", "description": "记录创建时间", "forceDefaultValue": {"$env": "now"}}, "updated_at": {"bsonType": "timestamp", "title": "更新时间", "description": "记录最后更新时间", "forceDefaultValue": {"$env": "now"}}}}