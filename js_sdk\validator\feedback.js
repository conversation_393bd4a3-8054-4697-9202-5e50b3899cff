// 表单校验规则由 schema2code 生成，不建议直接修改校验规则，而建议通过 schema2code 生成, 详情: https://uniapp.dcloud.net.cn/uniCloud/schema


const validator = {
  "name": {
    "rules": [
      {
        "required": true,
        "errorMessage": "姓名不能为空"
      },
      {
        "format": "string"
      },
      {
        "minLength": 2,
        "maxLength": 20,
        "errorMessage": "姓名长度不能小于 {minLength} 个字符"
      }
    ],
    "title": "姓名",
    "label": "姓名"
  },
  "project": {
    "rules": [
      {
        "required": true,
        "errorMessage": "请选择找茬项目"
      },
      {
        "format": "string"
      },
      {
        "range": [
          {
            "value": "安全找茬",
            "text": "安全找茬"
          },
          {
            "value": "设备找茬",
            "text": "设备找茬"
          },
          {
            "value": "其他找茬",
            "text": "其他找茬"
          }
        ],
        "errorMessage": "请选择有效的找茬项目"
      }
    ],
    "title": "找茬项目",
    "label": "找茬项目"
  },
  "description": {
    "rules": [
      {
        "required": true,
        "errorMessage": "问题描述不能为空"
      },
      {
        "format": "string"
      },
      {
        "maxLength": 500,
        "errorMessage": "问题描述不能超过 {maxLength} 个字符"
      }
    ],
    "title": "问题描述",
    "label": "问题描述"
  },
  "images": {
    "rules": [
      {
        "format": "array"
      }
    ],
    "title": "图片",
    "label": "图片"
  },
  "createTime": {
    "rules": [
      {
        "format": "timestamp"
      }
    ],
    "title": "创建时间",
    "defaultValue": {
      "$env": "now"
    },
    "label": "创建时间"
  }
}

const enumConverter = {
  "project_valuetotext": {
    "安全找茬": "安全找茬",
    "设备找茬": "设备找茬",
    "其他找茬": "其他找茬"
  }
}

function filterToWhere(filter, command) {
  let where = {}
  for (let field in filter) {
    let { type, value } = filter[field]
    switch (type) {
      case "search":
        if (typeof value === 'string' && value.length) {
          where[field] = new RegExp(value)
        }
        break;
      case "select":
        if (value.length) {
          let selectValue = []
          for (let s of value) {
            selectValue.push(command.eq(s))
          }
          where[field] = command.or(selectValue)
        }
        break;
      case "range":
        if (value.length) {
          let gt = value[0]
          let lt = value[1]
          where[field] = command.and([command.gte(gt), command.lte(lt)])
        }
        break;
      case "date":
        if (value.length) {
          let [s, e] = value
          let startDate = new Date(s)
          let endDate = new Date(e)
          where[field] = command.and([command.gte(startDate), command.lte(endDate)])
        }
        break;
      case "timestamp":
        if (value.length) {
          let [startDate, endDate] = value
          where[field] = command.and([command.gte(startDate), command.lte(endDate)])
        }
        break;
    }
  }
  return where
}

export { validator, enumConverter, filterToWhere }
