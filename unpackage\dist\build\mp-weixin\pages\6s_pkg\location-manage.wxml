<view data-event-opts="{{[['tap',[['handlePageClick',['$event']]]]]}}" class="page-container data-v-5da58f36" bindtap="__e"><view class="header data-v-5da58f36"><view class="header-title data-v-5da58f36">位置配置管理</view><view class="header-subtitle data-v-5da58f36">管理工厂所有区域位置信息</view></view><view class="stats-card data-v-5da58f36"><block wx:if="{{loading}}"><view class="stats-loading data-v-5da58f36"><view class="loading-content data-v-5da58f36"><uni-icons vue-id="35b1d116-1" type="spinner-cycle" size="40" color="#007AFF" class="data-v-5da58f36" bind:__l="__l"></uni-icons><text class="loading-text data-v-5da58f36">加载位置配置中...</text></view></view></block><block wx:else><view class="stats-item data-v-5da58f36"><view class="stats-number data-v-5da58f36">{{totalLocations}}</view><view class="stats-label data-v-5da58f36">总位置数</view></view><view class="stats-item data-v-5da58f36"><view class="stats-number data-v-5da58f36">{{categoriesCount}}</view><view class="stats-label data-v-5da58f36">分类数</view></view></block></view><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="action-buttons data-v-5da58f36" catchtap="__e"><button data-event-opts="{{[['tap',[['addCategory',['$event']]]]]}}" class="action-btn primary data-v-5da58f36" bindtap="__e"><uni-icons vue-id="35b1d116-2" type="plus" size="16" color="#FFFFFF" class="data-v-5da58f36" bind:__l="__l"></uni-icons><text class="data-v-5da58f36">新增分类</text></button></view><block wx:if="{{loading}}"><view class="list-loading data-v-5da58f36"><view class="loading-content data-v-5da58f36"><uni-icons vue-id="35b1d116-3" type="spinner-cycle" size="40" color="#007AFF" class="data-v-5da58f36" bind:__l="__l"></uni-icons><text class="loading-text data-v-5da58f36">加载位置数据中...</text></view></view></block><block wx:else><view class="categories-list data-v-5da58f36"><block wx:for="{{$root.l1}}" wx:for-item="category" wx:for-index="categoryIndex" wx:key="categoryIndex"><view class="category-card data-v-5da58f36"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="category-header data-v-5da58f36" catchtap="__e"><view class="category-title-group data-v-5da58f36"><block wx:if="{{category.$orig.editing}}"><input class="category-title-input data-v-5da58f36" placeholder="请输入分类名称" data-event-opts="{{[['blur',[['saveCategory',[categoryIndex]]]],['confirm',[['saveCategory',[categoryIndex]]]],['input',[['__set_model',['$0','category','$event',[]],[[['locationConfig.locations','',categoryIndex]]]]]]]}}" value="{{category.$orig.category}}" bindblur="__e" bindconfirm="__e" bindinput="__e"/></block><block wx:else><view data-event-opts="{{[['tap',[['editCategory',[categoryIndex]]]]]}}" class="category-title data-v-5da58f36" bindtap="__e">{{''+category.$orig.category+''}}</view></block><view class="category-count data-v-5da58f36">{{"("+category.g0+")"}}</view></view><view class="category-actions data-v-5da58f36"><view data-event-opts="{{[['tap',[['editCategory',[categoryIndex]]]]]}}" class="action-icon data-v-5da58f36" bindtap="__e"><uni-icons vue-id="{{'35b1d116-4-'+categoryIndex}}" type="compose" size="18" color="#007AFF" class="data-v-5da58f36" bind:__l="__l"></uni-icons></view><view data-event-opts="{{[['tap',[['deleteCategory',[categoryIndex]]]]]}}" class="action-icon data-v-5da58f36" bindtap="__e"><uni-icons vue-id="{{'35b1d116-5-'+categoryIndex}}" type="trash" size="18" color="#FF3B30" class="data-v-5da58f36" bind:__l="__l"></uni-icons></view></view></view><view class="locations-grid data-v-5da58f36"><block wx:for="{{category.l0}}" wx:for-item="location" wx:for-index="locationIndex" wx:key="locationIndex"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="location-item data-v-5da58f36" catchtap="__e"><block wx:if="{{location.$orig.editing}}"><input class="location-input data-v-5da58f36" placeholder="请输入位置名称" data-event-opts="{{[['blur',[['saveLocation',[categoryIndex,locationIndex]]]],['confirm',[['saveLocation',[categoryIndex,locationIndex]]]],['input',[['__set_model',['$0','name','$event',[]],[[['locationConfig.locations','',categoryIndex],['items','',locationIndex]]]]]]]}}" value="{{location.$orig.name}}" bindblur="__e" bindconfirm="__e" bindinput="__e"/></block><block wx:else><view data-event-opts="{{[['tap',[['editLocation',[categoryIndex,locationIndex]]]]]}}" class="location-content data-v-5da58f36" bindtap="__e"><text class="location-name data-v-5da58f36">{{location.m0}}</text></view></block><block wx:if="{{!location.$orig.editing}}"><view data-event-opts="{{[['tap',[['deleteLocation',[categoryIndex,locationIndex]]]]]}}" class="location-delete data-v-5da58f36" bindtap="__e"><uni-icons vue-id="{{'35b1d116-6-'+categoryIndex+'-'+locationIndex}}" type="clear" size="16" color="#FF3B30" class="data-v-5da58f36" bind:__l="__l"></uni-icons></view></block></view></block><view data-event-opts="{{[['tap',[['addLocation',[categoryIndex]]]]]}}" class="add-location-btn data-v-5da58f36" catchtap="__e"><uni-icons vue-id="{{'35b1d116-7-'+categoryIndex}}" type="plus" size="20" color="#C7C7CC" class="data-v-5da58f36" bind:__l="__l"></uni-icons><text class="add-text data-v-5da58f36">添加位置</text></view></view></view></block></view></block><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="save-section data-v-5da58f36" catchtap="__e"><button class="save-btn data-v-5da58f36" disabled="{{saving}}" data-event-opts="{{[['tap',[['saveConfig',['$event']]]]]}}" bindtap="__e"><block wx:if="{{saving}}"><uni-icons vue-id="35b1d116-8" type="spinner-cycle" size="16" color="#FFFFFF" class="data-v-5da58f36" bind:__l="__l"></uni-icons></block><text class="data-v-5da58f36">{{saving?'保存中...':'保存配置'}}</text></button></view></view>