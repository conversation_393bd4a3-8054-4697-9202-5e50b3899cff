'use strict';

const db = uniCloud.database();
const dbCmd = db.command;

// 6S权限角色常量 - 基于实际业务场景
const HYGIENE_ROLES = {
  // 最高管理权限：系统管理员、厂长（可以创建、修改、删除，但主要看结果）
  ADMIN_ROLES: ['admin', 'GM'],
  // 6S专员权限：综合员、发布人（专门负责6S的核心工作人员）
  SPECIALIST_ROLES: ['Integrated', 'reviser'],
  // 清洁员权限：所有人都有责任区，都需要做清洁工作
  CLEANER_ROLES: ['admin', 'GM', 'supervisor', 'PM', 'responsible', 'logistics', 'dispatch', 'Integrated', 'operator', 'technician', 'mechanic', 'user', 'reviser'],
  // 检查权限：只有6S专员负责检查（包括卫生检查、问题发现、任务指派）
  INSPECTOR_ROLES: ['Integrated', 'reviser'],
  // 数据查看权限：所有角色
  VIEW_ROLES: ['admin', 'GM', 'supervisor', 'PM', 'responsible', 'logistics', 'dispatch', 'Integrated', 'operator', 'technician', 'mechanic', 'user', 'reviser']
};

exports.main = async (event, context) => {
  const { action, data, uniIdToken } = event;
  let uid = '';
  let role = [];
  
  // 获取用户信息
  if (uniIdToken) {
    const { createInstance } = require('uni-id-common');
    const uniID = createInstance({ context });
    const payload = await uniID.checkToken(uniIdToken);
    
    if (payload.code === 0) {
      uid = payload.uid;
      
      // 从数据库获取用户角色信息（参考学习天地的做法）
      try {
        const userRes = await db.collection('uni-id-users')
          .doc(uid)
          .field({ role: true })
          .get();
        
        if (userRes.data && userRes.data[0] && userRes.data[0].role) {
          role = Array.isArray(userRes.data[0].role) ? userRes.data[0].role : [userRes.data[0].role];
        } else {
          // 如果没有角色信息，给予基本权限
          role = ['user'];
        }
      } catch (error) {
        console.error('获取用户角色失败:', error);
        role = ['user']; // 默认角色
      }
    }
  }
  
  // 权限检查
  if (!uid) {
    return {
      success: false,
      message: '用户未登录'
    };
  }
  
  try {
    switch (action) {
      case 'createArea':
        return await createArea(data, uid, role);
      case 'updateArea':
        return await updateArea(data, uid, role);
      case 'deleteArea':
        return await deleteArea(data, uid, role);
      case 'getAreaList':
        return await getAreaList(data, uid, role);
      case 'getAreaDetail':
        return await getAreaDetail(data, uid, role);
      case 'getMyAreas':
        return await getMyAreas(uid);
      case 'assignUser':
        return await assignUser(data, uid, role);
      case 'unassignUser':
        return await unassignUser(data, uid, role);
      case 'getAreaStatus':
        return await getAreaStatus(data, uid, role);
      case 'updateAreaStatus':
        return await updateAreaStatus(data, uid, role);
      case 'getOverviewStats':
        return await getOverviewStats(data, uid, role);
      case 'getRecentActivities':
        return await getRecentActivities(data, uid, role);
      case 'getSmartReminders':
        return await getSmartReminders(data, uid, role);
      default:
        return {
          success: false,
          message: '未知的操作类型'
        };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return {
      success: false,
      message: '服务器错误',
      error: error.message
    };
  }
};

// 创建责任区
async function createArea(data, uid, role) {
  const { name, type, description, location, cleaning_requirements, cleaning_frequency, scheduled_day } = data;
  
  // 权限检查 - 管理员和6S专员可以创建区域
  const hasPermission = HYGIENE_ROLES.ADMIN_ROLES.some(r => role.includes(r)) || 
                        HYGIENE_ROLES.SPECIALIST_ROLES.some(r => role.includes(r));
  if (!hasPermission) {
    return {
      success: false,
      message: '您当前没有创建责任区的权限，请联系6S专员'
    };
  }
  
  // 验证必填字段
  if (!name || !type) {
    return {
      success: false,
      message: '责任区名称和类型不能为空'
    };
  }
  
  // 检查是否重名（同类型内不能重名）
  const existingArea = await db.collection('hygiene-areas')
    .where({
      name: name,
      type: type
    })
    .get();
  
  if (existingArea.data.length > 0) {
    return {
      success: false,
      message: `该名称的${type === 'fixed' ? '固定' : '公共'}责任区已存在`
    };
  }
  
  const areaData = {
    name,
    type,
    description: description || '',
    location: location || {},
    cleaning_requirements: cleaning_requirements || [],
    cleaning_frequency: cleaning_frequency || 'weekly',
    assigned_users: [],
    status: 'active',
    creator_id: uid,
    created_at: new Date(),
    updated_at: new Date()
  };
  
  // 公共责任区需要设置固定清洁日
  if (type === 'public' && scheduled_day) {
    areaData.scheduled_day = scheduled_day;
  }
  
  const result = await db.collection('hygiene-areas').add(areaData);
  
  return {
    success: true,
    message: '责任区创建成功',
    data: {
      _id: result.id,
      ...areaData
    }
  };
}

// 更新责任区
async function updateArea(data, uid, role) {
  const { id, name, type, description, location, cleaning_requirements, cleaning_frequency, scheduled_day, status } = data;
  
  if (!id) {
    return {
      success: false,
      message: '责任区ID不能为空'
    };
  }
  
  // 获取责任区信息
  const areaInfo = await db.collection('hygiene-areas').doc(id).get();
  if (!areaInfo.data.length) {
    return {
      success: false,
      message: '责任区不存在'
    };
  }
  
  const area = areaInfo.data[0];
  
  // 权限检查 - 管理员、6S专员或创建者可以更新
  const hasAdminPermission = HYGIENE_ROLES.ADMIN_ROLES.some(r => role.includes(r));
  const hasSpecialistPermission = HYGIENE_ROLES.SPECIALIST_ROLES.some(r => role.includes(r));
  const isCreator = area.creator_id === uid;
  
  if (!hasAdminPermission && !hasSpecialistPermission && !isCreator) {
    return {
      success: false,
      message: '您当前没有修改此责任区的权限，请联系6S专员'
    };
  }
  
  // 如果修改名称，检查是否重名（同类型内不能重名）
  if (name && name !== area.name) {
    const currentType = type || area.type; // 使用新类型或原类型
    const existingArea = await db.collection('hygiene-areas')
      .where({
        name: name,
        type: currentType,
        _id: dbCmd.neq(id)
      })
      .get();
    
    if (existingArea.data.length > 0) {
      return {
        success: false,
        message: `该名称的${currentType === 'fixed' ? '固定' : '公共'}责任区已存在`
      };
    }
  }
  
  const updateData = {
    updated_at: new Date()
  };
  
  if (name) updateData.name = name;
  if (type) updateData.type = type;
  if (description !== undefined) updateData.description = description;
  if (location) updateData.location = location;
  if (cleaning_requirements) updateData.cleaning_requirements = cleaning_requirements;
  if (cleaning_frequency) updateData.cleaning_frequency = cleaning_frequency;
  if (scheduled_day !== undefined) updateData.scheduled_day = scheduled_day;
  if (data.start_date !== undefined) updateData.start_date = data.start_date;
  if (status) updateData.status = status;
  
  await db.collection('hygiene-areas').doc(id).update(updateData);
  
  return {
    success: true,
    message: '责任区更新成功'
  };
}

// 删除责任区
async function deleteArea(data, uid, role) {
  const { id } = data;
  
  if (!id) {
    return {
      success: false,
      message: '责任区ID不能为空'
    };
  }
  
  // 权限检查 - 管理员和6S专员可以删除责任区
  const hasAdminPermission = HYGIENE_ROLES.ADMIN_ROLES.some(r => role.includes(r));
  const hasSpecialistPermission = HYGIENE_ROLES.SPECIALIST_ROLES.some(r => role.includes(r));
  if (!hasAdminPermission && !hasSpecialistPermission) {
    return {
      success: false,
      message: '删除责任区需要管理员或6S专员权限，请联系相关人员'
    };
  }
  
  // 检查是否存在相关记录
  const [cleaningRecords, inspectionRecords, issues] = await Promise.all([
    db.collection('hygiene-cleaning-records').where({ area_id: id }).count(),
    db.collection('hygiene-inspection-records').where({ area_id: id }).count(),
    db.collection('hygiene-issues').where({ area_id: id }).count()
  ]);
  
  if (cleaningRecords.total > 0 || inspectionRecords.total > 0 || issues.total > 0) {
    return {
      success: false,
      message: '该责任区存在相关记录，无法删除'
    };
  }
  
  await db.collection('hygiene-areas').doc(id).remove();
  
  // 同时删除相关的分配记录
  await db.collection('hygiene-assignments').where({ area_id: id }).remove();
  
  return {
    success: true,
    message: '责任区删除成功'
  };
}

// 获取责任区列表
async function getAreaList(data, uid, role) {
  const { type, status, page = 1, pageSize = 20, keyword } = data;
  
  let whereCondition = {};
  
  // 类型筛选
  if (type) {
    whereCondition.type = type;
  }
  
  // 状态筛选
  if (status) {
    whereCondition.status = status;
  }
  
  // 关键词搜索
  if (keyword) {
    whereCondition.name = new RegExp(keyword, 'i');
  }
  
  const skip = (page - 1) * pageSize;
  
  const [listResult, countResult] = await Promise.all([
    db.collection('hygiene-areas')
      .where(whereCondition)
      .orderBy('created_at', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get(),
    db.collection('hygiene-areas')
      .where(whereCondition)
      .count()
  ]);
  
  // 直接返回数据库数据，前端已统一使用scheduled_day字段
  const mappedList = listResult.data;

  return {
    success: true,
    data: {
      list: mappedList,
      total: countResult.total,
      page,
      pageSize
    }
  };
}

// 获取责任区详情
async function getAreaDetail(data, uid, role) {
  const { id } = data;
  
  if (!id) {
    return {
      success: false,
      message: '责任区ID不能为空'
    };
  }
  
  const areaResult = await db.collection('hygiene-areas').doc(id).get();
  
  if (!areaResult.data.length) {
    return {
      success: false,
      message: '责任区不存在'
    };
  }
  
  const area = areaResult.data[0];
  
  // 从分配表中查询这个责任区的分配信息
  const assignmentResult = await db.collection('hygiene-assignments')
    .where({
      'areas_info.id': area._id,
      status: dbCmd.neq('deleted') // 排除已删除的分配
    })
    .get();
  
  // 如果有分配记录，设置分配用户信息
  if (assignmentResult.data && assignmentResult.data.length > 0) {
    const assignment = assignmentResult.data[0]; // 取第一个有效分配
    
    // 设置分配用户ID列表（为了兼容原有逻辑）
    area.assigned_users = [assignment.employee_id];
    
    // 设置用户详情
    area.assigned_user_details = [{
      _id: assignment.employee_id,
      username: assignment.employee_info?.username || assignment.employee_name,
      nickname: assignment.employee_info?.nickname || assignment.employee_name
    }];
  } else {
    // 没有分配记录，保持原有逻辑
    area.assigned_users = area.assigned_users || [];
    
    // 如果原有字段有数据，查询用户详情
    if (area.assigned_users && area.assigned_users.length > 0) {
      const userResult = await db.collection('uni-id-users')
        .where({
          _id: dbCmd.in(area.assigned_users)
        })
        .field({
          _id: true,
          username: true,
          nickname: true
        })
        .get();
      
      area.assigned_user_details = userResult.data;
    }
  }
  
  return {
    success: true,
    data: area
  };
}

// 获取我的责任区
async function getMyAreas(uid) {
  const result = await db.collection('hygiene-areas')
    .where({
      assigned_users: dbCmd.in([uid]),
      status: 'active'
    })
    .orderBy('type', 'asc')
    .orderBy('name', 'asc')
    .get();
  
  return {
    success: true,
    data: result.data
  };
}

// 分配用户到责任区
async function assignUser(data, uid, role) {
  const { area_id, user_id } = data;
  
  if (!area_id || !user_id) {
    return {
      success: false,
      message: '责任区ID和用户ID不能为空'
    };
  }
  
  // 权限检查 - 管理员和6S专员可以分配用户
  const hasPermission = HYGIENE_ROLES.ADMIN_ROLES.some(r => role.includes(r)) || 
                        HYGIENE_ROLES.SPECIALIST_ROLES.some(r => role.includes(r));
  if (!hasPermission) {
    return {
      success: false,
      message: '您当前没有分配用户的权限，请联系6S专员'
    };
  }
  
  // 检查用户是否已分配
  const areaResult = await db.collection('hygiene-areas').doc(area_id).get();
  if (!areaResult.data.length) {
    return {
      success: false,
      message: '责任区不存在'
    };
  }
  
  const area = areaResult.data[0];
  if (area.assigned_users && area.assigned_users.includes(user_id)) {
    return {
      success: false,
      message: '该用户已分配到此责任区'
    };
  }
  
  // 添加用户到分配列表
  const assigned_users = area.assigned_users || [];
  assigned_users.push(user_id);
  
  await db.collection('hygiene-areas').doc(area_id).update({
    assigned_users,
    updated_at: new Date()
  });
  
  return {
    success: true,
    message: '用户分配成功'
  };
}

// 取消用户分配
async function unassignUser(data, uid, role) {
  const { area_id, user_id } = data;
  
  if (!area_id || !user_id) {
    return {
      success: false,
      message: '责任区ID和用户ID不能为空'
    };
  }
  
  // 权限检查 - 管理员和6S专员可以取消分配
  const hasPermission = HYGIENE_ROLES.ADMIN_ROLES.some(r => role.includes(r)) || 
                        HYGIENE_ROLES.SPECIALIST_ROLES.some(r => role.includes(r));
  if (!hasPermission) {
    return {
      success: false,
      message: '您当前没有取消分配的权限，请联系6S专员'
    };
  }
  
  const areaResult = await db.collection('hygiene-areas').doc(area_id).get();
  if (!areaResult.data.length) {
    return {
      success: false,
      message: '责任区不存在'
    };
  }
  
  const area = areaResult.data[0];
  if (!area.assigned_users || !area.assigned_users.includes(user_id)) {
    return {
      success: false,
      message: '该用户未分配到此责任区'
    };
  }
  
  // 从分配列表中移除用户
  const assigned_users = area.assigned_users.filter(id => id !== user_id);
  
  await db.collection('hygiene-areas').doc(area_id).update({
    assigned_users,
    updated_at: new Date()
  });
  
  return {
    success: true,
    message: '用户分配已取消'
  };
}

// 获取责任区状态
async function getAreaStatus(data, uid, role) {
  const { area_id } = data;
  
  if (!area_id) {
    return {
      success: false,
      message: '责任区ID不能为空'
    };
  }
  
  // 获取最近的清洁记录
  const cleaningResult = await db.collection('hygiene-cleaning-records')
    .where({ area_id })
    .orderBy('cleaning_date', 'desc')
    .limit(1)
    .get();
  
  // 获取最近的检查记录
  const inspectionResult = await db.collection('hygiene-inspection-records')
    .where({ area_id })
    .orderBy('inspection_date', 'desc')
    .limit(1)
    .get();
  
  // 获取未解决的问题数量
  const issuesResult = await db.collection('hygiene-issues')
    .where({
      area_id,
      status: dbCmd.in(['submitted', 'assigned', 'in_progress'])
    })
    .count();
  
  return {
    success: true,
    data: {
      last_cleaning: cleaningResult.data[0] || null,
      last_inspection: inspectionResult.data[0] || null,
      open_issues: issuesResult.total
    }
  };
}

// 更新责任区状态
async function updateAreaStatus(data, uid, role) {
  const { area_id, status } = data;
  
  if (!area_id || !status) {
    return {
      success: false,
      message: '责任区ID和状态不能为空'
    };
  }
  
  // 权限检查 - 管理员和6S专员可以更新状态
  const hasAdminPermission = HYGIENE_ROLES.ADMIN_ROLES.some(r => role.includes(r));
  const hasSpecialistPermission = HYGIENE_ROLES.SPECIALIST_ROLES.some(r => role.includes(r));
  if (!hasAdminPermission && !hasSpecialistPermission) {
    return {
      success: false,
      message: '更新责任区状态需要管理员或6S专员权限'
    };
  }
  
  await db.collection('hygiene-areas').doc(area_id).update({
    status,
    updated_at: new Date()
  });
  
  return {
    success: true,
    message: '责任区状态更新成功'
  };
}

// 获取概览统计数据
async function getOverviewStats(data, uid, role) {
  const { timeFilter = 'week' } = data;
  
  try {
    // 计算时间范围
    const now = new Date();
    let startDate, endDate;
    
    switch (timeFilter) {
      case 'today':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
        break;
      case 'week':
        const weekStart = new Date(now);
        const day = weekStart.getDay();
        const diff = weekStart.getDate() - day + (day === 0 ? -6 : 1);
        weekStart.setDate(diff);
        weekStart.setHours(0, 0, 0, 0);
        
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        weekEnd.setHours(23, 59, 59, 999);
        
        startDate = weekStart;
        endDate = weekEnd;
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        endDate.setHours(23, 59, 59, 999);
        break;
      default:
        // 默认本周
        const defaultWeekStart = new Date(now);
        const defaultDay = defaultWeekStart.getDay();
        const defaultDiff = defaultWeekStart.getDate() - defaultDay + (defaultDay === 0 ? -6 : 1);
        defaultWeekStart.setDate(defaultDiff);
        defaultWeekStart.setHours(0, 0, 0, 0);
        
        const defaultWeekEnd = new Date(defaultWeekStart);
        defaultWeekEnd.setDate(defaultWeekStart.getDate() + 6);
        defaultWeekEnd.setHours(23, 59, 59, 999);
        
        startDate = defaultWeekStart;
        endDate = defaultWeekEnd;
    }
    
    // 并行查询统计数据
    const [
      totalAreasResult,
      completedCleaningResult,
      pendingRectificationsResult,
      overdueAreasResult,
      inspectionRatingsResult
    ] = await Promise.all([
      // 总责任区数量
      db.collection('hygiene-areas')
        .where({ status: 'active' })
        .count(),
      
      // 已完成清理的责任区数量
      db.collection('hygiene-cleaning-records')
        .where({
          cleaning_date: dbCmd.gte(startDate).and(dbCmd.lte(endDate))
        })
        .field({ area_id: true })
        .get()
        .then(result => {
          // 去重计算有多少个不同的责任区完成了清理
          const uniqueAreaIds = [...new Set(result.data.map(record => record.area_id))];
          return { total: uniqueAreaIds.length };
        }),
      
      // 待整改问题数量
      db.collection('hygiene-rectification-records')
        .where({
          status: dbCmd.in(['pending_rectification', 'pending_assignment', 'pending_review']),
          created_at: dbCmd.gte(startDate).and(dbCmd.lte(endDate))
        })
        .count(),
      
      // 逾期未清理的责任区数量
      db.collection('hygiene-areas')
        .where({ status: 'active' })
        .get()
        .then(async areasResult => {
          const areas = areasResult.data;
          let overdueCount = 0;
          
          for (const area of areas) {
            // 检查该责任区在时间范围内是否有清理记录
            const cleaningResult = await db.collection('hygiene-cleaning-records')
              .where({
                area_id: area._id,
                cleaning_date: dbCmd.gte(startDate).and(dbCmd.lte(endDate))
              })
              .count();
            
            // 如果没有清理记录且当前时间已过期，则算作逾期
            if (cleaningResult.total === 0) {
              if (area.type === 'fixed') {
                // 固定责任区：如果本周已过半且没有清理记录，算逾期
                const midWeek = new Date(startDate);
                midWeek.setDate(startDate.getDate() + 3);
                if (now > midWeek) {
                  overdueCount++;
                }
              } else if (area.type === 'public' && area.scheduled_day !== null) {
                // 公共责任区：如果已过排班日且没有清理记录，算逾期
                const scheduledDay = area.scheduled_day === 0 ? 7 : area.scheduled_day;
                const currentDay = now.getDay() === 0 ? 7 : now.getDay();
                if (currentDay > scheduledDay) {
                  overdueCount++;
                }
              }
            }
          }
          
          return { total: overdueCount };
        }),
      
      // 检查记录评分数据（用于计算平均评分）
      db.collection('hygiene-inspection-records')
        .where({
          created_at: dbCmd.gte(startDate).and(dbCmd.lte(endDate)),
          rating: dbCmd.exists(true) // 确保有评分
        })
        .field({ rating: true })
        .get()
    ]);
    
    // 计算完成率
    const totalAreas = totalAreasResult.total;
    const completedAreas = completedCleaningResult.total;
    const completionRate = totalAreas > 0 ? Math.round((completedAreas / totalAreas) * 100) : 0;
    
    // 计算平均评分
    let averageRating = 0;
    if (inspectionRatingsResult.data && inspectionRatingsResult.data.length > 0) {
      const ratings = inspectionRatingsResult.data.map(record => record.rating || 0);
      const totalRating = ratings.reduce((sum, rating) => sum + rating, 0);
      averageRating = parseFloat((totalRating / ratings.length).toFixed(1));
    }
    
    return {
      success: true,
      data: {
        totalAreas,
        completedAreas,
        pendingRectifications: pendingRectificationsResult.total,
        overdueAreas: overdueAreasResult.total,
        completionRate,
        averageRating
      }
    };
    
  } catch (error) {
    console.error('获取概览统计失败:', error);
    return {
      success: false,
      message: '获取统计数据失败',
      error: error.message
    };
  }
}

// 获取最新动态
async function getRecentActivities(data, uid, role) {
  const { limit = 5, timeFilter = 'week' } = data;
  
  try {
    // 计算时间范围（最新动态通常显示最近的数据）
    const now = new Date();
    const startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000); // 最近7天
    
    // 并行查询不同类型的活动
    const [
      recentCleanings,
      recentInspections,
      recentRectifications
    ] = await Promise.all([
      // 最近的清理记录
      db.collection('hygiene-cleaning-records')
        .where({
          created_at: dbCmd.gte(startDate)
        })
        .orderBy('created_at', 'desc')
        .limit(3)
        .get(),
      
      // 最近的检查记录  
      db.collection('hygiene-inspection-records')
        .where({
          created_at: dbCmd.gte(startDate)
        })
        .orderBy('created_at', 'desc')
        .limit(3)
        .get(),
      
      // 最近的整改记录
      db.collection('hygiene-rectification-records')
        .where({
          created_at: dbCmd.gte(startDate)
        })
        .orderBy('created_at', 'desc')
        .limit(3)
        .get()
    ]);
    
    // 整合活动数据
    const activities = [];
    
    // 处理清理记录
    recentCleanings.data.forEach(record => {
      activities.push({
        type: 'cleaning_completed',
        title: `${record.user_name || '员工'}完成了${record.area_name || '某责任区'}清理`,
        created_at: record.created_at,
        area_name: record.area_name,
        user_name: record.user_name
      });
    });
    
    // 处理检查记录
    recentInspections.data.forEach(record => {
      if (record.has_issues) {
        activities.push({
          type: 'issue_found',
          title: `${record.area_name || '某责任区'}发现卫生问题`,
          created_at: record.created_at,
          area_name: record.area_name,
          inspector_name: record.inspector_name
        });
      } else {
        activities.push({
          type: 'inspection_completed',
          title: `${record.inspector_name || '检查员'}完成了${record.area_name || '某责任区'}检查`,
          created_at: record.created_at,
          area_name: record.area_name,
          inspector_name: record.inspector_name
        });
      }
    });
    
    // 处理整改记录
    recentRectifications.data.forEach(record => {
      if (record.status === 'completed' || record.status === 'verified') {
        activities.push({
          type: 'rectification_completed',
          title: `${record.assigned_to_name || '员工'}完成了${record.area_name || '某责任区'}整改`,
          created_at: record.submitted_at || record.created_at,
          area_name: record.area_name,
          user_name: record.assigned_to_name
        });
      }
    });
    
    // 按时间排序并限制数量
    activities.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
    const limitedActivities = activities.slice(0, limit);
    
    return {
      success: true,
      data: limitedActivities
    };
    
  } catch (error) {
    console.error('获取最新动态失败:', error);
    return {
      success: false,
      message: '获取最新动态失败',
      error: error.message
    };
  }
}

// 获取智能提醒
async function getSmartReminders(data, uid, role) {
  const { userRole = 'employee', limit = 3 } = data;
  
  try {
    const now = new Date();
    const reminders = [];
    
    // 计算本周时间范围
    const weekStart = new Date(now);
    const day = weekStart.getDay();
    const diff = weekStart.getDate() - day + (day === 0 ? -6 : 1);
    weekStart.setDate(diff);
    weekStart.setHours(0, 0, 0, 0);
    
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);
    weekEnd.setHours(23, 59, 59, 999);
    
    // 处理角色参数（可能是字符串或数组）
    let userRoles = [];
    if (typeof userRole === 'string') {
      userRoles = [userRole];
    } else if (Array.isArray(userRole)) {
      userRoles = userRole;
    } else {
      userRoles = ['employee']; // 默认角色
    }
    
    // 并行查询基础数据
    const [areasResult, pendingRectificationsResult] = await Promise.all([
      db.collection('hygiene-areas').where({ status: 'active' }).get(),
      db.collection('hygiene-rectification-records')
        .where({
          status: dbCmd.in(['pending_rectification', 'pending_assignment'])
        })
        .count()
    ]);
    
    // 1. 分别统计未清理和逾期的责任区（员工视角）
    let pendingCleaningCount = 0;  // 待清理（还没逾期的）
    let overdueCleaningCount = 0;  // 员工清理任务已逾期
    
    // 批量查询所有区域的清理记录
    const areaIds = areasResult.data.map(area => area._id);
    const cleaningRecordsResult = await db.collection('hygiene-cleaning-records')
      .where({
        area_id: dbCmd.in(areaIds),
        cleaning_date: dbCmd.gte(weekStart).and(dbCmd.lte(weekEnd))
      })
      .field({ area_id: true })
      .get();
    
    const cleanedAreaIds = new Set(cleaningRecordsResult.data.map(record => record.area_id));
    
    for (const area of areasResult.data) {
      const isCleaned = cleanedAreaIds.has(area._id);
      
      if (!isCleaned) {
        // 检查是否逾期
        let isOverdue = false;
        
        if (area.type === 'fixed') {
          // 固定责任区：周四之后算逾期
          const midWeek = new Date(weekStart);
          midWeek.setDate(weekStart.getDate() + 3); // 周四
          isOverdue = now > midWeek;
        } else if (area.type === 'public' && area.scheduled_day !== null) {
          // 公共责任区：过了排班日算逾期
          const scheduledDay = area.scheduled_day === 0 ? 7 : area.scheduled_day;
          const currentDay = now.getDay() === 0 ? 7 : now.getDay();
          isOverdue = currentDay > scheduledDay;
        }
        
        if (isOverdue) {
          overdueCleaningCount++;
        } else {
          pendingCleaningCount++;
        }
      }
    }
    
    // 添加清理提醒（只针对未逾期的待清理任务）
    if (pendingCleaningCount > 0) {
      reminders.push({
        type: 'pending_cleaning',
        description: `本周还有${pendingCleaningCount}个责任区待清理，请及时安排`
      });
    }
    
    // 2. 员工清理逾期提醒（员工视角 - 清理任务逾期）
    if (overdueCleaningCount > 0) {
      reminders.push({
        type: 'overdue_cleaning',
        description: `有${overdueCleaningCount}个清理任务已逾期，将在下周重新安排`
      });
    }
    
    // 3a. 检查待整改的问题（员工视角 - 未逾期的）
    let pendingRectificationCount = 0;
    let overdueRectificationCount = 0;
    
    // 查询所有待整改的任务，区分逾期和未逾期
    try {
      const allRectificationResult = await db.collection('hygiene-rectification-records')
        .where({
          status: dbCmd.in(['pending_rectification', 'pending_assignment'])
        })
        .get();
      
      for (const task of allRectificationResult.data) {
        if (!task.created_at) continue;
        
        const createdDate = new Date(task.created_at);
        
        // 计算创建日期所在周的结束时间
        const createdWeekStart = new Date(createdDate);
        const createdDay = createdWeekStart.getDay();
        const diff = createdWeekStart.getDate() - createdDay + (createdDay === 0 ? -6 : 1);
        createdWeekStart.setDate(diff);
        createdWeekStart.setHours(0, 0, 0, 0);
        
        const createdWeekEnd = new Date(createdWeekStart);
        createdWeekEnd.setDate(createdWeekStart.getDate() + 6);
        createdWeekEnd.setHours(23, 59, 59, 999);
        
        // 判断是否逾期
        if (now > createdWeekEnd) {
          overdueRectificationCount++;
        } else {
          pendingRectificationCount++;
        }
      }
    } catch (error) {
      console.error('整改任务统计失败:', error);
      pendingRectificationCount = pendingRectificationsResult.total; // 回退方案
    }
    
    // 添加待整改提醒（未逾期的）
    if (pendingRectificationCount > 0) {
      reminders.push({
        type: 'pending_rectification',
        description: `有${pendingRectificationCount}个整改任务待处理，请及时跟进`
      });
    }
    
    // 3b. 员工整改逾期提醒（员工视角 - 整改任务逾期）
    if (overdueRectificationCount > 0) {
      reminders.push({
        type: 'overdue_rectification',
        description: `有${overdueRectificationCount}个整改任务已逾期，请联系管理员`
      });
    }
    
    // 4. 检查员/管理员专用提醒
    const isInspectorRole = ['Integrated', 'reviser', 'admin', 'GM', 'supervisor', 'PM'].some(r => userRoles.includes(r));
    
    if (isInspectorRole) {
      // 4a. 待检查的责任区
      const cleanedAreaIdsList = Array.from(cleanedAreaIds);
      
      if (cleanedAreaIdsList.length > 0) {
        const inspectionRecordsResult = await db.collection('hygiene-inspection-records')
          .where({
            area_id: dbCmd.in(cleanedAreaIdsList),
            inspection_date: dbCmd.gte(weekStart).and(dbCmd.lte(weekEnd))
          })
          .field({ area_id: true })
          .get();
        
        const inspectedAreaIds = new Set(inspectionRecordsResult.data.map(record => record.area_id));
        const uninspectedCount = cleanedAreaIdsList.filter(areaId => !inspectedAreaIds.has(areaId)).length;
        
        if (uninspectedCount > 0) {
          reminders.push({
            type: 'inspection_due',
            description: `有${uninspectedCount}个已清理的责任区待检查，请安排检查`
          });
        }
      }
      
      // 4b. 漏检查提醒（基于正确的时效性逻辑）
      try {
        let missedInspectionCount = 0;
        
        // 获取本周所有清理记录
        const allCleaningResult = await db.collection('hygiene-cleaning-records')
          .where({
            cleaning_date: dbCmd.gte(weekStart).and(dbCmd.lte(weekEnd))
          })
          .get();
        
        if (allCleaningResult.data.length > 0) {
          // 并行获取区域信息和检查记录
          const areaIds = allCleaningResult.data.map(record => record.area_id);
          const [areasInfoResult, inspectionRecordsResult] = await Promise.all([
            db.collection('hygiene-areas').where({
              _id: dbCmd.in(areaIds)
            }).get(),
            db.collection('hygiene-inspection-records')
              .where({
                area_id: dbCmd.in(areaIds),
                inspection_date: dbCmd.gte(weekStart).and(dbCmd.lte(weekEnd))
              })
              .get()
          ]);
          
          const areasMap = new Map(areasInfoResult.data.map(area => [area._id, area]));
          const inspectedAreaIds = new Set(inspectionRecordsResult.data.map(record => record.area_id));
          
          // 检查每个清理记录是否需要检查但未检查
          for (const cleaningRecord of allCleaningResult.data) {
            const areaInfo = areasMap.get(cleaningRecord.area_id);
            const cleaningDate = new Date(cleaningRecord.cleaning_date);
            
            if (!areaInfo || inspectedAreaIds.has(cleaningRecord.area_id)) {
              continue; // 跳过已检查的或找不到区域信息的
            }
            
            let isOverdue = false;
            
            if (areaInfo.type === 'fixed') {
              // 固定责任区：如果现在已经过了本周周日23:59，且未检查
              if (now > weekEnd) {
                isOverdue = true;
              }
            } else if (areaInfo.type === 'public' && areaInfo.scheduled_day !== null) {
              // 公共责任区：如果现在已经过了排班日23:59，且未检查
              const scheduledDay = areaInfo.scheduled_day === 0 ? 7 : areaInfo.scheduled_day; // 统一为1-7
              const cleaningDay = cleaningDate.getDay() === 0 ? 7 : cleaningDate.getDay(); // 统一为1-7
              
              // 确认是在排班日清理的
              if (scheduledDay === cleaningDay) {
                // 计算排班日的结束时间（当天23:59:59）
                const scheduledDayEnd = new Date(cleaningDate);
                scheduledDayEnd.setHours(23, 59, 59, 999);
                
                if (now > scheduledDayEnd) {
                  isOverdue = true;
                }
              }
            }
            
            if (isOverdue) {
              missedInspectionCount++;
            }
          }
        }
        
        if (missedInspectionCount > 0) {
          reminders.push({
            type: 'missed_inspection',
            description: `有${missedInspectionCount}个责任区清理后超时未检查，请及时处理`
          });
        }
      } catch (error) {
        console.error('漏检查统计失败:', error);
      }
      
      // 4c. 整改复查逾期提醒（基于正确的时效性逻辑）
      try {
        // 整改任务应该在创建的当周内完成，超过当周末就算逾期
        const overdueRectificationResult = await db.collection('hygiene-rectification-records')
          .where({
            status: 'rectification_completed'
          })
          .get();
        
        let overdueReviewCount = 0;
        
        for (const record of overdueRectificationResult.data) {
          if (!record.submitted_at) continue;
          
          const submitDate = new Date(record.submitted_at);
          
          // 计算提交日期所在周的结束时间
          const submitWeekStart = new Date(submitDate);
          const submitDay = submitWeekStart.getDay();
          const diff = submitWeekStart.getDate() - submitDay + (submitDay === 0 ? -6 : 1);
          submitWeekStart.setDate(diff);
          submitWeekStart.setHours(0, 0, 0, 0);
          
          const submitWeekEnd = new Date(submitWeekStart);
          submitWeekEnd.setDate(submitWeekStart.getDate() + 6);
          submitWeekEnd.setHours(23, 59, 59, 999);
          
          // 如果现在已经超过了提交周的周日23:59，且状态仍为rectification_completed
          if (now > submitWeekEnd && record.status === 'rectification_completed') {
            overdueReviewCount++;
          }
        }
        
        if (overdueReviewCount > 0) {
          reminders.push({
            type: 'overdue_review',
            description: `有${overdueReviewCount}个整改任务提交后超时未复查，请及时处理`
          });
        }
      } catch (error) {
        console.error('逾期复查统计失败:', error);
      }
    }
    
    // 5. 管理员专用提醒（完全未打扫的责任区）
    const isManagerRole = ['admin', 'GM', 'supervisor', 'PM'].some(r => userRoles.includes(r));
    
    if (isManagerRole && areasResult.data.length > cleanedAreaIds.size) {
      const notCleanedCount = areasResult.data.length - cleanedAreaIds.size;
      reminders.push({
        type: 'not_cleaned',
        description: `有${notCleanedCount}个责任区本周完全未打扫，请关注员工执行情况`
      });
    }
    
    // 6. 如果没有任何提醒，添加一个正面的提醒
    if (reminders.length === 0) {
      if (pendingCleaningCount === 0 && overdueCleaningCount === 0 && areasResult.data.length > 0) {
        reminders.push({
          type: 'all_good',
          description: `很棒！本周所有责任区都已完成清理，保持良好状态`
        });
      } else if (areasResult.data.length === 0) {
        reminders.push({
          type: 'no_areas',
          description: `暂无责任区分配，请联系管理员进行责任区设置`
        });
      }
    }
    
    // 限制提醒数量
    const limitedReminders = reminders.slice(0, limit);
    
    return {
      success: true,
      data: limitedReminders
    };
    
  } catch (error) {
    console.error('获取智能提醒失败:', error);
    return {
      success: false,
      message: '获取智能提醒失败',
      error: error.message
    };
  }
} 