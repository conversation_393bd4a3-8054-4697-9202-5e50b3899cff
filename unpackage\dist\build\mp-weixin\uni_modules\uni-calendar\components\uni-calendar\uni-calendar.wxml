<view class="uni-calendar data-v-58d9892f"><block wx:if="{{!insert&&show}}"><view data-event-opts="{{[['tap',[['clean',['$event']]]]]}}" class="{{['uni-calendar__mask','data-v-58d9892f',(aniMaskShow)?'uni-calendar--mask-show':'']}}" bindtap="__e"></view></block><block wx:if="{{insert||show}}"><view class="{{['uni-calendar__content','data-v-58d9892f',(!insert)?'uni-calendar--fixed':'',(aniMaskShow)?'uni-calendar--ani-show':'']}}"><block wx:if="{{!insert}}"><view class="uni-calendar__header uni-calendar--fixed-top data-v-58d9892f"><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="uni-calendar__header-btn-box data-v-58d9892f" bindtap="__e"><text class="uni-calendar__header-text uni-calendar--fixed-width data-v-58d9892f">{{cancelText}}</text></view><view data-event-opts="{{[['tap',[['confirm',['$event']]]]]}}" class="uni-calendar__header-btn-box data-v-58d9892f" bindtap="__e"><text class="uni-calendar__header-text uni-calendar--fixed-width data-v-58d9892f">{{okText}}</text></view></view></block><view class="uni-calendar__header data-v-58d9892f"><view data-event-opts="{{[['tap',[['pre',['$event']]]]]}}" class="uni-calendar__header-btn-box data-v-58d9892f" catchtap="__e"><view class="uni-calendar__header-btn uni-calendar--left data-v-58d9892f"></view></view><picker mode="date" value="{{date}}" fields="month" data-event-opts="{{[['change',[['bindDateChange',['$event']]]]]}}" bindchange="__e" class="data-v-58d9892f"><text class="uni-calendar__header-text data-v-58d9892f">{{(nowDate.year||'')+' / '+(nowDate.month||'')}}</text></picker><view data-event-opts="{{[['tap',[['next',['$event']]]]]}}" class="uni-calendar__header-btn-box data-v-58d9892f" catchtap="__e"><view class="uni-calendar__header-btn uni-calendar--right data-v-58d9892f"></view></view><text data-event-opts="{{[['tap',[['backToday',['$event']]]]]}}" class="uni-calendar__backtoday data-v-58d9892f" bindtap="__e">{{todayText}}</text></view><view class="uni-calendar__box data-v-58d9892f"><block wx:if="{{showMonth}}"><view class="uni-calendar__box-bg data-v-58d9892f"><text class="uni-calendar__box-bg-text data-v-58d9892f">{{nowDate.month}}</text></view></block><view class="uni-calendar__weeks data-v-58d9892f"><view class="uni-calendar__weeks-day data-v-58d9892f"><text class="uni-calendar__weeks-day-text data-v-58d9892f">{{SUNText}}</text></view><view class="uni-calendar__weeks-day data-v-58d9892f"><text class="uni-calendar__weeks-day-text data-v-58d9892f">{{monText}}</text></view><view class="uni-calendar__weeks-day data-v-58d9892f"><text class="uni-calendar__weeks-day-text data-v-58d9892f">{{TUEText}}</text></view><view class="uni-calendar__weeks-day data-v-58d9892f"><text class="uni-calendar__weeks-day-text data-v-58d9892f">{{WEDText}}</text></view><view class="uni-calendar__weeks-day data-v-58d9892f"><text class="uni-calendar__weeks-day-text data-v-58d9892f">{{THUText}}</text></view><view class="uni-calendar__weeks-day data-v-58d9892f"><text class="uni-calendar__weeks-day-text data-v-58d9892f">{{FRIText}}</text></view><view class="uni-calendar__weeks-day data-v-58d9892f"><text class="uni-calendar__weeks-day-text data-v-58d9892f">{{SATText}}</text></view></view><block wx:for="{{weeks}}" wx:for-item="item" wx:for-index="weekIndex" wx:key="weekIndex"><view class="uni-calendar__weeks data-v-58d9892f"><block wx:for="{{item}}" wx:for-item="weeks" wx:for-index="weeksIndex" wx:key="weeksIndex"><view class="uni-calendar__weeks-item data-v-58d9892f"><calendar-item class="uni-calendar-item--hook data-v-58d9892f" vue-id="{{'c9d0ae1e-1-'+weekIndex+'-'+weeksIndex}}" weeks="{{weeks}}" calendar="{{calendar}}" selected="{{selected}}" lunar="{{lunar}}" data-event-opts="{{[['^change',[['choiceDate']]]]}}" bind:change="__e" bind:__l="__l"></calendar-item></view></block></view></block></view></view></block></view>