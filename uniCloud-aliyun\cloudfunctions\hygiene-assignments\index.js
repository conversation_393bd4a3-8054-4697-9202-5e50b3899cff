'use strict';

const db = uniCloud.database();
const dbCmd = db.command;

// 6S权限角色常量
const HYGIENE_ROLES = {
  // 最高管理权限：系统管理员、厂长
  ADMIN_ROLES: ['admin', 'GM'],
  // 6S专员权限：综合员、发布人
  SPECIALIST_ROLES: ['Integrated', 'reviser'],
  // 清洁员权限：所有人都有责任区，都需要做清洁工作
  CLEANER_ROLES: ['admin', 'GM', 'supervisor', 'PM', 'responsible', 'logistics', 'dispatch', 'Integrated', 'operator', 'technician', 'mechanic', 'user', 'reviser'],
  // 检查权限：只有6S专员负责检查
  INSPECTOR_ROLES: ['Integrated', 'reviser'],
  // 数据查看权限：所有角色
  VIEW_ROLES: ['admin', 'GM', 'supervisor', 'PM', 'responsible', 'logistics', 'dispatch', 'Integrated', 'operator', 'technician', 'mechanic', 'user', 'reviser']
};

exports.main = async (event, context) => {
  const { action, data, uniIdToken } = event;
  let uid = '';
  let role = [];
  
  // 获取用户信息
  if (uniIdToken) {
    const { createInstance } = require('uni-id-common');
    const uniID = createInstance({ context });
    const payload = await uniID.checkToken(uniIdToken);
    
    if (payload.code === 0) {
      uid = payload.uid;
      
      // 从数据库获取用户角色信息
      try {
        const userRes = await db.collection('uni-id-users')
          .doc(uid)
          .field({ role: true })
          .get();
        
        if (userRes.data && userRes.data[0] && userRes.data[0].role) {
          role = Array.isArray(userRes.data[0].role) ? userRes.data[0].role : [userRes.data[0].role];
        } else {
          role = ['user'];
        }
      } catch (error) {
        console.error('获取用户角色失败:', error);
        role = ['user'];
      }
    }
  }
  
  // 权限检查
  if (!uid) {
    return {
      success: false,
      message: '用户未登录'
    };
  }
  
  try {
    switch (action) {
      case 'createAssignment':
        return await createAssignment(data, uid, role);
      case 'updateAssignment':
        return await updateAssignment(data, uid, role);
      case 'deleteAssignment':
        return await deleteAssignment(data, uid, role);
      case 'getAssignmentList':
        return await getAssignmentList(data, uid, role);
      case 'getAssignmentDetail':
        return await getAssignmentDetail(data, uid, role);
      case 'getUserAssignments':
        return await getUserAssignments(data, uid, role);
      case 'getEmployeeList':
        return await getEmployeeList(data, uid, role);
      default:
        return {
          success: false,
          message: '未知的操作类型'
        };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return {
      success: false,
      message: '服务器错误',
      error: error.message
    };
  }
};

// 创建分配记录
async function createAssignment(data, uid, role) {
  const { employee_id, employee_name, employee_info, area_ids, area_names, areas_info, note, status } = data;
  
  // 权限检查 - 管理员和6S专员可以创建分配
  const hasPermission = HYGIENE_ROLES.ADMIN_ROLES.some(r => role.includes(r)) || 
                        HYGIENE_ROLES.SPECIALIST_ROLES.some(r => role.includes(r));
  if (!hasPermission) {
    return {
      success: false,
      message: '您当前没有分配员工责任区的权限，请联系6S专员'
    };
  }
  
  // 验证必填字段
  if (!employee_id || !area_ids || area_ids.length === 0) {
    return {
      success: false,
      message: '员工ID和责任区不能为空'
    };
  }
  
  // 检查员工是否已有分配
  const existingAssignment = await db.collection('hygiene-assignments')
    .where({
      employee_id: employee_id
    })
    .get();
  
  if (existingAssignment.data.length > 0) {
    return {
      success: false,
      message: '该员工已有责任区分配，请先删除原有分配或编辑现有分配'
    };
  }
  
  // 检查责任区是否已被分配
  const assignedAreas = await db.collection('hygiene-assignments')
    .where({
      area_ids: dbCmd.in(area_ids)
    })
    .get();
  
  if (assignedAreas.data.length > 0) {
    const conflictAreas = [];
    assignedAreas.data.forEach(assignment => {
      assignment.area_ids.forEach(areaId => {
        if (area_ids.includes(areaId)) {
          const areaName = assignment.area_names[assignment.area_ids.indexOf(areaId)];
          conflictAreas.push(`${areaName}(已分配给${assignment.employee_name})`);
        }
      });
    });
    
    return {
      success: false,
      message: `以下责任区已被分配：${conflictAreas.join('、')}`
    };
  }
  
  const assignmentData = {
    employee_id,
    employee_name,
    employee_info: employee_info || {},
    area_ids,
    area_names,
    areas_info: areas_info || [],
    note: note || '',
    status: status || 'active',
    creator_id: uid,
    created_at: new Date(),
    updated_at: new Date()
  };
  
  const result = await db.collection('hygiene-assignments').add(assignmentData);
  
  return {
    success: true,
    message: '分配成功',
    data: {
      _id: result.id,
      ...assignmentData
    }
  };
}

// 更新分配记录
async function updateAssignment(data, uid, role) {
  const { id, employee_id, employee_name, employee_info, area_ids, area_names, areas_info, note, status } = data;
  
  if (!id) {
    return {
      success: false,
      message: '分配记录ID不能为空'
    };
  }
  
  // 获取分配记录信息
  const assignmentInfo = await db.collection('hygiene-assignments').doc(id).get();
  if (!assignmentInfo.data.length) {
    return {
      success: false,
      message: '分配记录不存在'
    };
  }
  
  const assignment = assignmentInfo.data[0];
  
  // 权限检查 - 管理员、6S专员或创建者可以更新
  const hasAdminPermission = HYGIENE_ROLES.ADMIN_ROLES.some(r => role.includes(r));
  const hasSpecialistPermission = HYGIENE_ROLES.SPECIALIST_ROLES.some(r => role.includes(r));
  const isCreator = assignment.creator_id === uid;
  
  if (!hasAdminPermission && !hasSpecialistPermission && !isCreator) {
    return {
      success: false,
      message: '您当前没有修改此分配记录的权限，请联系6S专员'
    };
  }
  
  // 如果修改了员工，检查新员工是否已有分配
  if (employee_id && employee_id !== assignment.employee_id) {
    const existingAssignment = await db.collection('hygiene-assignments')
      .where({
        employee_id: employee_id,
        _id: dbCmd.neq(id)
      })
      .get();
    
    if (existingAssignment.data.length > 0) {
      return {
        success: false,
        message: '该员工已有责任区分配'
      };
    }
  }
  
  // 如果修改了责任区，检查是否有冲突
  if (area_ids && area_ids.length > 0) {
    const assignedAreas = await db.collection('hygiene-assignments')
      .where({
        area_ids: dbCmd.in(area_ids),
        _id: dbCmd.neq(id)
      })
      .get();
    
    if (assignedAreas.data.length > 0) {
      const conflictAreas = [];
      assignedAreas.data.forEach(otherAssignment => {
        otherAssignment.area_ids.forEach(areaId => {
          if (area_ids.includes(areaId)) {
            const areaName = otherAssignment.area_names[otherAssignment.area_ids.indexOf(areaId)];
            conflictAreas.push(`${areaName}(已分配给${otherAssignment.employee_name})`);
          }
        });
      });
      
      if (conflictAreas.length > 0) {
        return {
          success: false,
          message: `以下责任区已被分配：${conflictAreas.join('、')}`
        };
      }
    }
  }
  
  const updateData = {
    updated_at: new Date()
  };
  
  if (employee_id) updateData.employee_id = employee_id;
  if (employee_name) updateData.employee_name = employee_name;
  if (employee_info) updateData.employee_info = employee_info;
  if (area_ids) updateData.area_ids = area_ids;
  if (area_names) updateData.area_names = area_names;
  if (areas_info) updateData.areas_info = areas_info;
  if (note !== undefined) updateData.note = note;
  if (status) updateData.status = status;
  
  await db.collection('hygiene-assignments').doc(id).update(updateData);
  
  return {
    success: true,
    message: '更新成功',
    data: {
      _id: id,
      ...assignment,
      ...updateData
    }
  };
}

// 删除分配记录
async function deleteAssignment(data, uid, role) {
  const { id } = data;
  
  if (!id) {
    return {
      success: false,
      message: '分配记录ID不能为空'
    };
  }
  
  // 获取分配记录信息
  const assignmentInfo = await db.collection('hygiene-assignments').doc(id).get();
  if (!assignmentInfo.data.length) {
    return {
      success: false,
      message: '分配记录不存在'
    };
  }
  
  const assignment = assignmentInfo.data[0];
  
  // 权限检查 - 管理员、6S专员或创建者可以删除
  const hasAdminPermission = HYGIENE_ROLES.ADMIN_ROLES.some(r => role.includes(r));
  const hasSpecialistPermission = HYGIENE_ROLES.SPECIALIST_ROLES.some(r => role.includes(r));
  const isCreator = assignment.creator_id === uid;
  
  if (!hasAdminPermission && !hasSpecialistPermission && !isCreator) {
    return {
      success: false,
      message: '您当前没有删除此分配记录的权限，请联系6S专员'
    };
  }
  
  // 检查是否存在相关记录
  const [cleaningRecords, inspectionRecords] = await Promise.all([
    db.collection('hygiene-cleaning-records').where({ 
      employee_id: assignment.employee_id 
    }).count(),
    db.collection('hygiene-inspection-records').where({ 
      area_id: dbCmd.in(assignment.area_ids) 
    }).count()
  ]);
  
  if (cleaningRecords.total > 0 || inspectionRecords.total > 0) {
    return {
      success: false,
      message: '该分配记录存在相关清洁或检查记录，无法删除'
    };
  }
  
  await db.collection('hygiene-assignments').doc(id).remove();
  
  return {
    success: true,
    message: '删除成功'
  };
}

// 获取分配记录列表
async function getAssignmentList(data, uid, role) {
  const { page = 1, pageSize = 20, employee_id, area_id, status, keyword } = data;
  
  let whereCondition = {};
  
  // 员工筛选
  if (employee_id) {
    whereCondition.employee_id = employee_id;
  }
  
  // 责任区筛选
  if (area_id) {
    whereCondition.area_ids = dbCmd.in([area_id]);
  }
  
  // 状态筛选
  if (status) {
    whereCondition.status = status;
  }
  
  // 关键词搜索
  if (keyword) {
    whereCondition.employee_name = new RegExp(keyword, 'i');
  }
  
  const skip = (page - 1) * pageSize;
  
  const [listResult, countResult] = await Promise.all([
    db.collection('hygiene-assignments')
      .where(whereCondition)
      .orderBy('created_at', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get(),
    db.collection('hygiene-assignments')
      .where(whereCondition)
      .count()
  ]);
  
  // 处理数据格式，获取最新的区域信息
  const processedList = await Promise.all(listResult.data.map(async assignment => {
    // 从责任区表中获取最新的区域信息
    let latestAreas = assignment.areas_info || [];
    
    if (assignment.area_ids && assignment.area_ids.length > 0) {
      try {
        const areasResult = await db.collection('hygiene-areas')
          .where({
            _id: dbCmd.in(assignment.area_ids)
          })
          .field({
            _id: true,
            name: true,
            location: true,
            type: true,
            status: true
          })
          .get();
        
        if (areasResult.data.length > 0) {
          latestAreas = areasResult.data.map(area => ({
            id: area._id,
            name: area.name,
            location: area.location,
            type: area.type,
            status: area.status
          }));
        }
      } catch (error) {
        console.error('获取最新区域信息失败：', error);
        // 如果获取失败，使用原有的区域信息
      }
    }
    
    return {
      ...assignment,
      employee: assignment.employee_info,
      areas: latestAreas
    };
  }));
  
  return {
    success: true,
    data: processedList,
    total: countResult.total,
    page,
    pageSize
  };
}

// 获取分配记录详情
async function getAssignmentDetail(data, uid, role) {
  const { id } = data;
  
  if (!id) {
    return {
      success: false,
      message: '分配记录ID不能为空'
    };
  }
  
  const assignmentResult = await db.collection('hygiene-assignments').doc(id).get();
  
  if (!assignmentResult.data.length) {
    return {
      success: false,
      message: '分配记录不存在'
    };
  }
  
  const assignment = assignmentResult.data[0];
  
  // 处理数据格式
  const processedAssignment = {
    ...assignment,
    employee: assignment.employee_info,
    areas: assignment.areas_info
  };
  
  return {
    success: true,
    data: processedAssignment
  };
}

// 获取用户的分配记录
async function getUserAssignments(data, uid, role) {
  const { user_id } = data;
  const targetUserId = user_id || uid; // 如果没有指定用户ID，返回当前用户的分配
  
  // 1. 查询用户的固定责任区分配记录（使用 employee_id）
  const assignmentResult = await db.collection('hygiene-assignments')
    .where({
      employee_id: targetUserId,
      status: 'active'
    })
    .get();
  
  // 2. 查询所有启用的公共责任区（所有员工都可以访问）
  const publicAreasResult = await db.collection('hygiene-areas')
    .where({
      type: 'public',
      status: 'active'
    })
    .get();
  
  // 处理固定责任区分配记录
  const processedAssignments = await Promise.all(assignmentResult.data.map(async assignment => {
    // 获取区域详细信息 - 处理 area_ids 数组
    let areasInfo = [];
    
    // 检查是否有 area_ids 数组
    if (assignment.area_ids && Array.isArray(assignment.area_ids) && assignment.area_ids.length > 0) {
      // 批量查询所有区域信息
      try {
        const areaResults = await Promise.all(
          assignment.area_ids.map(async areaId => {
            try {
              const areaResult = await db.collection('hygiene-areas')
                .doc(areaId)
                .get();
              
              if (areaResult.data && areaResult.data.length > 0) {
                return areaResult.data[0];
              } else {
                console.warn('未找到区域信息，区域ID：', areaId);
                return null;
              }
            } catch (error) {
              console.error('获取单个区域信息失败：', areaId, error);
              return null;
            }
          })
        );
        
        // 过滤掉空值
        areasInfo = areaResults.filter(area => area !== null);
        
      } catch (error) {
        console.error('批量获取区域信息失败：', error);
      }
    } else if (assignment.area_id) {
      // 兼容单个 area_id 的情况
      try {
        const areaResult = await db.collection('hygiene-areas')
          .doc(assignment.area_id)
          .get();
        
        if (areaResult.data && areaResult.data.length > 0) {
          areasInfo = [areaResult.data[0]];
        } else {
          console.warn('未找到区域信息，区域ID：', assignment.area_id);
        }
      } catch (error) {
        console.error('获取区域信息失败：', error);
      }
    }
    
    // 构造返回数据，保持与现有代码兼容
    return {
      ...assignment,
      areas_info: areasInfo
    };
  }));
  
  // 如果有公共责任区，创建一个虚拟的分配记录
  if (publicAreasResult.data.length > 0) {
    const publicAssignment = {
      _id: 'public_areas_' + targetUserId,
      employee_id: targetUserId,
      type: 'public',
      status: 'active',
      areas_info: publicAreasResult.data
    };
    
    processedAssignments.push(publicAssignment);
  }
  
  return {
    success: true,
    data: processedAssignments
  };
}

// 获取员工列表（参考巡视系统的实现）
async function getEmployeeList(data, uid, role) {
  const { page = 1, pageSize = 100, roleFilter, status, keyword } = data;
  

  
  // 权限检查 - 管理员和6S专员可以获取员工列表
  const hasPermission = HYGIENE_ROLES.ADMIN_ROLES.some(r => role.includes(r)) || 
                        HYGIENE_ROLES.SPECIALIST_ROLES.some(r => role.includes(r));
  if (!hasPermission) {
    return {
      success: false,
      message: '您当前没有获取员工列表的权限，请联系6S专员'
    };
  }
  
  try {
    // 构建查询条件
    const where = {};
    
    // 处理角色筛选
    if (roleFilter) {
      where.role = roleFilter;
    }
    
    // 处理状态筛选 - 参考巡视系统的逻辑，默认获取所有状态用户
    if (status !== undefined && status !== -1) {
      where.status = status;
    }
    
    // 关键词搜索
    if (keyword) {
      where.$or = [
        { username: new RegExp(keyword, 'i') },
        { nickname: new RegExp(keyword, 'i') },
        { realname: new RegExp(keyword, 'i') }
      ];
    }
    

    
    const [userResult, countResult] = await Promise.all([
      db.collection('uni-id-users')
        .where(where)
        .field({
          _id: true,
          username: true,
          nickname: true,
          realname: true,
          real_name: true, // 巡视系统使用的是这个字段
          mobile: true,
          role: true,
          status: true,
          avatar: true
        })
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .orderBy('realname', 'asc')
        .get(),
      db.collection('uni-id-users')
        .where(where)
        .count()
    ]);

    
    // 处理数据格式，转换为前端期望的格式（参考巡视系统的过滤逻辑）

    
    const employeeList = userResult.data
      .filter(user => {
        // 过滤掉admin角色用户（参考巡视系统）
        if (!user.role) return true; // 没有角色的保留
        if (Array.isArray(user.role)) {
          // 如果是数组，检查是否包含admin
          return !user.role.includes('admin');
        } else {
          // 如果是字符串，检查是否为admin
          return user.role !== 'admin';
        }
      })
      .filter(user => {
        // 过滤掉以"匿名"开头的用户（参考巡视系统，注意字段名是real_name不是realname）
        const name = user.real_name || user.nickname || user.username || '';
        return !name.startsWith('匿名');
      })
      .map(user => ({
        id: user._id,
        name: user.real_name || user.realname || user.nickname || user.username,
        role: getRoleDisplayName(user.role),
        avatar: user.avatar || '/static/user/default-avatar.png',
        phone: user.mobile ? user.mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : '未设置'
      }));
    

    
    return {
      success: true,
      data: {
        list: employeeList,
        total: employeeList.length, // 使用过滤后的实际数量
        page,
        pageSize
      }
    };
    
  } catch (error) {
    console.error('获取员工列表失败:', error);
    return {
      success: false,
      message: '获取员工列表失败'
    };
  }
}

// 获取角色显示名称（与用户中心保持一致）
function getRoleDisplayName(role) {
  const roleNames = {
    'admin': '管理员',
    'responsible': '负责人',
    'reviser': '发布人',
    'supervisor': '主管',
    'PM': '副厂长',
    'GM': '厂长',
    'logistics': '后勤员',
    'dispatch': '调度员',
    'Integrated': '综合员',
    'operator': '设备员',
    'technician': '工艺员',
    'mechanic': '技术员',
    'user': '普通员工'
  };
  
  if (Array.isArray(role)) {
    return role.map(r => roleNames[r] || r).join('、');
  }
  
  return roleNames[role] || role || '普通员工';
} 