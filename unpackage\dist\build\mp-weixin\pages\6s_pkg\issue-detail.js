require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/issue-detail"],{"16c5":function(e,t,i){"use strict";var s=i("d9a9"),n=i.n(s);n.a},"69b6":function(e,t,i){"use strict";i.r(t);var s=i("7953"),n=i("8a6f");for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);i("16c5");var a=i("828b"),o=Object(a["a"])(n["default"],s["b"],s["c"],!1,null,"0ba8b637",null,!1,s["a"],void 0);t["default"]=o.exports},7228:function(e,t,i){"use strict";(function(e){var s=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=s(i("7eb4")),r=s(i("3b2d")),a=s(i("ee10")),o={name:"IssueDetail",data:function(){return{issueId:null,issue:null,loading:!1,loadError:!1,dataLoaded:!1,unifiedTimeline:[],currentUserRole:null,currentUserId:null,issueSubmittedForInspection:!1}},computed:{shouldShowActionButton:function(){if(!this.issue||!this.dataLoaded)return!1;if("viewer"===this.currentUserRole)return!1;var e=this.issue.status;return"responsible"===this.currentUserRole?"assigned"===e||"pending"===e||"rejected"===e||"in_progress"===e&&!this.issueSubmittedForInspection:"admin"===this.currentUserRole||"inspector"===this.currentUserRole&&(this.issueSubmittedForInspection||"pending_review"===e)},isButtonDisabled:function(){return!1},deadlineCountdown:function(){return this.issue&&this.issue.deadline?this.getDeadlineCountdown(this.issue.deadline):""},countdownClass:function(){return this.issue&&this.issue.deadline?this.getCountdownClass(this.issue.deadline):""}},onLoad:function(t){this.issueId=t.id,this.initializeUserInfo(),this.loadIssueDetail(),e.$on("monthlyIssueUpdated",this.handleIssueUpdated)},onUnload:function(){e.$off("monthlyIssueUpdated",this.handleIssueUpdated)},methods:{goBack:function(){e.navigateBack()},initializeUserInfo:function(){var t=this;return(0,a.default)(n.default.mark((function s(){var r,a,o,u,c,d,l;return n.default.wrap((function(s){while(1)switch(s.prev=s.next){case 0:try{r=e.getStorageSync("uni-id-pages-userInfo")||{},a=[];try{o=i("eddf"),u=o.getCacheKey,c=o.CACHE_KEYS,d=e.getStorageSync(u(c.USER_ROLE))||"{}",l={},l="string"===typeof d?JSON.parse(d):d,l&&l.value&&l.value.userRole&&(a=Array.isArray(l.value.userRole)?l.value.userRole:[l.value.userRole])}catch(n){}0===a.length&&r.role?a=Array.isArray(r.role)?r.role:[r.role]:0===a.length&&"admin"===r.username&&(a=["admin"]),r&&(r._id||r.id)?(t.currentUserId=r._id||r.id,a.includes("admin")?t.currentUserRole="admin":a.includes("inspector")||a.includes("Integrated")||a.includes("reviser")?t.currentUserRole="inspector":a.includes("responsible")?t.currentUserRole="responsible":t.currentUserRole="viewer"):(t.currentUserRole="viewer",t.currentUserId=null)}catch(p){console.log("初始化用户信息失败:",p),t.currentUserRole="viewer"}case 1:case"end":return s.stop()}}),s)})))()},loadIssueDetail:function(){var t=this;return(0,a.default)(n.default.mark((function s(){var a,o,u,c,d,l;return n.default.wrap((function(s){while(1)switch(s.prev=s.next){case 0:return s.prev=0,t.loading=!0,t.loadError=!1,a=i("882c"),o=a.callCloudFunction,s.next=6,o("hygiene-monthly-inspection",{action:"getIssueDetail",data:{issue_id:t.issueId}});case 6:if(u=s.sent,!u||!u.success){s.next=18;break}if(u.data){s.next=10;break}throw new Error("未找到对应的问题记录");case 10:u.data.status&&!["pending","in_progress","pending_review","approved","overdue","rejected","open","assigned","resolved","reviewing","submitted","verified","cancelled","suspended","reopened","draft","new","active","inactive","expired"].includes(u.data.status)&&console.warn("未识别的状态值:",u.data.status,"完整数据:",u.data),t.issue=t.formatIssueData(u.data),t.checkIfResponsible(),t.checkSubmissionStatus(),t.generateUnifiedTimeline(),t.dataLoaded=!0,s.next=20;break;case 18:throw c=(null===u||void 0===u?void 0:u.message)||(null===u||void 0===u?void 0:u.error)||"获取问题详情失败",new Error(c);case 20:s.next=41;break;case 22:if(s.prev=22,s.t0=s["catch"](0),s.prev=24,d="issue_detail_".concat(t.issueId),l=e.getStorageSync(d),!l||"object"!==(0,r.default)(l)){s.next=35;break}return t.issue=t.formatIssueData(l),t.checkIfResponsible(),t.checkSubmissionStatus(),t.generateUnifiedTimeline(),t.dataLoaded=!0,e.showToast({title:"已加载缓存数据",icon:"none",duration:2e3}),s.abrupt("return");case 35:s.next=39;break;case 37:s.prev=37,s.t1=s["catch"](24);case 39:t.loadError=!0,e.showToast({title:s.t0.message||"加载失败，请检查网络连接",icon:"error"});case 41:return s.prev=41,t.loading=!1,s.finish(41);case 44:case"end":return s.stop()}}),s,null,[[0,22,41,44],[24,37]])})))()},retryLoad:function(){this.loadError=!1,this.dataLoaded=!1,this.loadIssueDetail()},checkIfResponsible:function(){if(this.issue&&this.currentUserId){var e=this.issue.assigned_to||this.issue.responsible_id;e===this.currentUserId&&(this.currentUserRole="responsible")}},checkSubmissionStatus:function(){if(this.issue){var e=this.issue.history&&this.issue.history.some((function(e){return"提交检查"===e.action||"submit_for_inspection"===e.action||"submit_rectification"===e.action})),t="pending_review"===this.issue.status;this.issueSubmittedForInspection=e||t}},formatIssueData:function(e){var t,i;if(!e||"object"!==(0,r.default)(e))throw new Error("问题数据格式错误");return{id:e._id||e.id||this.issueId,number:e.issue_number||e.number||e.issue_id||this.issueId,title:e.title||e.issue_title||e.name||e.subject||"待确认问题",description:e.description||e.issue_description||"",status:e.status||e.issue_status||"pending",location:(null===(t=e.location_info)||void 0===t?void 0:t.location_name)||(null===(i=e.location_info)||void 0===i?void 0:i.name)||e.location||e.location_name||"待确认",responsible:e.assigned_to_name||e.responsible||e.responsible_name||"未分配",role:e.assigned_to_role||e.role||e.responsible_role||"负责人",deadline:this.formatDate(e.expected_completion_date||e.deadline||e.due_date||e.target_date),priority:e.priority||e.priority_level||"normal",createdAt:this.formatDate(e.created_at||e.createdAt||e.create_time),updatedAt:this.formatDate(e.updated_at||e.updatedAt||e.update_time),images:this.formatImages(e.photos||e.images||e.issue_photos||[]),history:this.formatHistory(e.history||e.progress_logs||e.logs||[]),rectification_description:e.rectification_description||"",rectification_photos:this.formatImages(e.rectification_photos||[]),completed_at:e.completed_at,assigned_to:e.assigned_to,responsible_id:e.responsible_id}},formatDate:function(e){if(!e)return"";try{var t=e;"string"===typeof e&&e.includes("-")&&!e.includes("T")&&(t=e.replace(/-/g,"/"));var i=new Date(t);return isNaN(i.getTime())?e:i.toISOString().split("T")[0]}catch(s){return e}},formatImages:function(e){return Array.isArray(e)?e.map((function(e){return"string"===typeof e?e:e.url||e.cloudPath||e})).filter(Boolean):[]},formatHistory:function(e){var t=this;return Array.isArray(e)?e.map((function(e){return{action:e.action||e.type||"操作",description:e.description||e.content||"",time:t.formatDateTime(e.time||e.timestamp||e.created_at),operator:e.operator||e.user_name||e.created_by||"系统"}})):[]},generateUnifiedTimeline:function(){var e=this,t=[],i=this.issue.status;[{key:"create",title:"创建问题",action:"创建问题"},{key:"assign",title:"分配负责人",action:"分配负责人"},{key:"start",title:"开始整改",action:"开始整改"},{key:"complete",title:"提交整改",action:"整改完成"},{key:"inspect",title:"检查问题",action:"检查问题"},{key:"approve",title:"检查通过",action:"检查通过"}].forEach((function(s){var n={title:s.title,description:"",time:"",operator:"",status:"pending"};if(e.issue.history&&e.issue.history.length>0){var r=e.issue.history.find((function(e){return e.action===s.action}));r&&(n.description=r.description,n.time=r.time,n.operator=r.operator,n.status="completed")}if("completed"!==n.status)if("create"===s.key||"assign"===s.key){n.status="completed",n.description="create"===s.key?"问题已创建并记录":"负责人已分配",n.operator="create"===s.key?"检查员":"管理员";var a=new Date(e.issue.createdAt||"2024-01-15");"assign"===s.key&&a.setHours(a.getHours()+1),n.time=e.formatDateTime(a)}else"start"===s.key?"pending"===i||"assigned"===i?(n.status="current",n.description="等待负责人开始整改"):"reopened"===i?(n.status="current",n.description="问题已重新打开，等待开始整改"):"overdue"===i?(n.status="overdue",n.description="未能在截止时间前开始整改，问题已逾期"):(n.status="completed",n.description="负责人已开始整改",n.operator=e.issue.responsible||"负责人",n.time=e.formatDateTime(new Date(2024,0,16,9,30))):"complete"===s.key?"pending"===i||"assigned"===i?(n.status="pending",n.description="等待开始整改"):"reopened"===i?(n.status="pending",n.description="等待重新开始整改"):"overdue"===i?(n.status="pending",n.description="因问题逾期，此步骤无法执行"):"in_progress"===i||"active"===i?(n.status="current",n.description="负责人正在进行整改"):"rejected"===i?(n.status="current",n.description="检查未通过，需要重新整改"):"pending_review"===i?(n.status="completed",n.description="整改工作已完成，等待检查",n.operator=e.issue.responsible||"负责人",n.time=e.formatDateTime(new Date)):(n.status="completed",n.description="整改工作已完成",n.operator=e.issue.responsible||"负责人",n.time=e.formatDateTime(new Date(2024,0,18,15,20))):"inspect"===s.key?"pending"===i||"assigned"===i||"in_progress"===i||"active"===i?(n.status="pending",n.description="等待整改完成"):"reopened"===i?(n.status="pending",n.description="等待重新整改完成"):"overdue"===i?(n.status="pending",n.description="因问题逾期，此步骤无法执行"):"pending_review"===i?(n.status="current",n.description="等待检查员检查整改效果"):"reviewing"===i||"submitted"===i?(n.status="current",n.description="检查员正在检查"):(n.status="completed",n.description="检查员已验收整改效果",n.operator="检查员",n.time=e.formatDateTime(new Date(2024,0,19,10,15))):"approve"===s.key&&("pending"===i||"assigned"===i||"in_progress"===i||"active"===i?(n.status="pending",n.description="等待检查完成"):"reopened"===i?(n.status="pending",n.description="等待重新检查完成"):"overdue"===i?(n.status="pending",n.description="因问题逾期，此步骤无法执行"):"pending_review"===i?(n.status="current",n.description="等待检查员确认"):"reviewing"===i||"submitted"===i?(n.status="pending",n.description="等待检查完成"):(n.status="completed",n.description="检查员确认整改合格",n.operator="检查员",n.time=e.formatDateTime(new Date(2024,0,19,14,30))));t.push(n)})),this.validateTimelineLogic(t),this.unifiedTimeline=t},validateTimelineLogic:function(e){var t=this,i=this.issue.status,s={assigned:{targetStep:1,currentStep:1},pending:{targetStep:2,currentStep:2},in_progress:{targetStep:3,currentStep:3},pending_review:{targetStep:4,currentStep:4},approved:{targetStep:6,currentStep:-1},overdue:{targetStep:2,currentStep:2},rejected:{targetStep:3,currentStep:3},reopened:{targetStep:2,currentStep:2},reviewing:{targetStep:4,currentStep:4},submitted:{targetStep:4,currentStep:4}}[i]||{targetStep:2,currentStep:2};e.forEach((function(e,n){n<s.targetStep||-1===s.currentStep?(e.status="completed",e.time=e.time||t.generateStepTime(n),e.operator=e.operator||t.getDefaultOperator(n),e.description=e.description||t.getDefaultDescription(n,"completed")):n===s.currentStep?"overdue"===i?(e.status="overdue",e.description="未能在截止时间前完成，问题已逾期"):"rejected"===i?(e.status="current",e.description="检查未通过，需要重新整改"):"reopened"===i?(e.status="current",e.description="问题已重新打开，等待处理"):(e.status="current",e.description=e.description||t.getDefaultDescription(n,"current")):(e.status="pending",e.time="",e.operator="",e.description=t.getDefaultDescription(n,"pending"))}))},generateStepTime:function(e){var t=new Date(this.issue.createdAt||Date.now()),i=new Date(t.getTime()+2*e*60*60*1e3);return this.formatDateTime(i)},getDefaultOperator:function(e){var t=["检查员","管理员",this.issue.responsible||"负责人",this.issue.responsible||"负责人","检查员","检查员"];return t[e]||"系统"},getDefaultDescription:function(e,t){var i,s=["create","assign","start","complete","inspect","approve"][e],n=this.issue.responsible||"负责人",r=this.issue.deadline?"（截止：".concat(this.formatDate(this.issue.deadline),"）"):"",a={create:{completed:"检查员发现问题并记录在案".concat(r?"，已设定整改期限":""),current:"正在创建问题记录",pending:"等待创建问题"},assign:{completed:"已指派".concat(n,"负责整改").concat(r),current:"正在分配负责人",pending:"等待分配负责人"},start:{completed:"".concat(n,"已接收任务并开始整改"),current:"等待".concat(n,"开始整改").concat(r),pending:"等待开始整改",overdue:"整改已逾期".concat(r,"，需要跟进处理"),reopened:"问题已重新打开，需要重新开始整改"},complete:{completed:"".concat(n,"已完成整改并提交，等待检查员验收"),current:"".concat(n,"正在进行整改工作").concat(r),pending:"等待负责人提交整改结果"},inspect:{completed:"检查员已开始现场检查整改效果",current:"等待检查员安排现场检查",pending:"等待检查验收"},approve:{completed:"整改效果符合要求，问题已彻底解决",current:"检查员正在现场验收整改效果",pending:"等待检查验收结果"}};return(null===(i=a[s])||void 0===i?void 0:i[t])||"处理中"},hasHistoryRecord:function(e){var t=["创建问题","分配负责人","开始整改","整改完成","检查通过"][e];return this.issue.history&&this.issue.history.some((function(e){return e.action===t}))},getStatusText:function(e){return{pending:"待整改",in_progress:"整改中",pending_review:"待检查",approved:"检查通过",overdue:"逾期",rejected:"已驳回",open:"待处理",assigned:"已分配",resolved:"已解决",reviewing:"审核中",submitted:"已提交",verified:"已验证",cancelled:"已取消",suspended:"已暂停",reopened:"重新打开",draft:"草稿",new:"新建",active:"进行中",inactive:"非活跃",expired:"已过期"}[e]||(e?"".concat(e,"(待定义)"):"待确认")},getPriorityText:function(e){if(!e||"normal"===e)return"一般问题";return{normal:"一般问题",urgent:"紧急问题"}[e]||"一般问题"},getIssueNumber:function(){var e,t,i=(null===(e=this.issue)||void 0===e?void 0:e.number)||(null===(t=this.issue)||void 0===t?void 0:t.issue_number)||this.issueId;if(i&&String(i).length>0){var s=String(i);if(s.startsWith("YD")&&s.length>10){var n=s.slice(-3);return"YD".concat(n)}if(s.length>8){var r=s.substring(0,2),a=s.slice(-6);return"".concat(r).concat(a)}return s}return String(Date.now()).slice(-8)},getDeadlineCountdown:function(e){if(!e)return"";try{var t=new Date(e),i=new Date;if(isNaN(t.getTime())){var s=new Date(e.replace(/-/g,"/"));if(isNaN(s.getTime()))return"";t.setTime(s.getTime())}var n=new Date(t.getFullYear(),t.getMonth(),t.getDate()),r=new Date(i.getFullYear(),i.getMonth(),i.getDate()),a=n.getTime()-r.getTime(),o=Math.round(a/864e5);return o<0?"已逾期".concat(Math.abs(o),"天"):0===o?"今日到期":1===o?"明日到期":"".concat(o,"天后到期")}catch(u){return""}},getCountdownClass:function(e){if(!e)return"";try{var t=new Date(e),i=new Date;if(isNaN(t.getTime())){var s=new Date(e.replace(/-/g,"/"));if(isNaN(s.getTime()))return"normal";t.setTime(s.getTime())}var n=new Date(t.getFullYear(),t.getMonth(),t.getDate()),r=new Date(i.getFullYear(),i.getMonth(),i.getDate()),a=n.getTime()-r.getTime(),o=Math.round(a/864e5);return o<0?"overdue":o<=1?"urgent":o<=3?"warning":"normal"}catch(u){return"normal"}},getButtonText:function(){if(!this.issue)return"操作";var e=this.issue.status;if("responsible"===this.currentUserRole){if("assigned"===e||"pending"===e)return"开始整改";if("rejected"===e)return"重新整改";if("in_progress"===e&&!this.issueSubmittedForInspection)return"继续整改"}else{if("admin"===this.currentUserRole)return"assigned"===e||"pending"===e||"in_progress"===e?"更改状态":"pending_review"===e?"检查问题":"approved"===e?"重新打开":this.issueSubmittedForInspection?"检查问题":"更改状态";if("inspector"===this.currentUserRole){if(this.issueSubmittedForInspection)return"检查问题";if("pending_review"===e)return"开始检查"}}return"操作"},handleMainAction:function(){if(this.issue){var e=this.issue.status;"responsible"===this.currentUserRole?"assigned"===e||"pending"===e?this.startRectification():("rejected"===e||"in_progress"===e)&&this.continueRectification():"admin"===this.currentUserRole?"assigned"===e||"pending"===e||"in_progress"===e?this.adminManageIssue():"pending_review"===e?this.adminAcceptIssue():this.issueSubmittedForInspection?this.changeIssueStatus():this.adminManageIssue():"inspector"===this.currentUserRole&&this.changeIssueStatus()}},startRectification:function(){var t=this;return(0,a.default)(n.default.mark((function s(){var r,a,o;return n.default.wrap((function(s){while(1)switch(s.prev=s.next){case 0:return s.prev=0,r=i("882c"),a=r.callCloudFunction,s.next=4,a("hygiene-monthly-inspection",{action:"updateMonthlyIssue",data:{issue_id:t.issueId,status:"in_progress",action_type:"start_rectification"}});case 4:if(o=s.sent,!o||!o.success){s.next=9;break}e.navigateTo({url:"/pages/6s_pkg/rectification-submit?issueId=".concat(t.issueId)}),s.next=10;break;case 9:throw new Error((null===o||void 0===o?void 0:o.message)||"开始整改失败");case 10:s.next=15;break;case 12:s.prev=12,s.t0=s["catch"](0),e.showToast({title:s.t0.message||"操作失败",icon:"error"});case 15:case"end":return s.stop()}}),s,null,[[0,12]])})))()},continueRectification:function(){e.navigateTo({url:"/pages/6s_pkg/rectification-submit?issueId=".concat(this.issueId)})},adminManageIssue:function(){this.changeIssueStatus()},adminCheckProgress:function(){var t=this;e.showActionSheet({itemList:["更改状态","联系负责人"],success:function(i){switch(i.tapIndex){case 0:t.changeIssueStatus();break;case 1:e.showToast({title:"功能开发中",icon:"none"});break}}})},adminAcceptIssue:function(){var t=this;e.showActionSheet({itemList:["检查通过","重新整改"],success:function(e){switch(e.tapIndex){case 0:t.approveIssue();break;case 1:t.rejectIssue();break}}})},approveIssue:function(){var t=this;return(0,a.default)(n.default.mark((function s(){return n.default.wrap((function(s){while(1)switch(s.prev=s.next){case 0:e.showModal({title:"检查通过",content:"确定检查通过此问题吗？",success:function(){var s=(0,a.default)(n.default.mark((function s(r){var a,o,u;return n.default.wrap((function(s){while(1)switch(s.prev=s.next){case 0:if(!r.confirm){s.next=12;break}return s.prev=1,a=i("882c"),o=a.callCloudFunction,s.next=5,o("hygiene-monthly-inspection",{action:"updateMonthlyIssue",data:{issue_id:t.issueId,status:"approved",action_type:"approve"}});case 5:u=s.sent,u&&u.success&&(t.issue.status="approved",t.generateUnifiedTimeline(),e.showToast({title:"检查通过",icon:"success"}),e.$emit("monthlyIssueUpdated",{action:"approve",issueId:t.issueId,issueData:t.issue})),s.next=12;break;case 9:s.prev=9,s.t0=s["catch"](1),e.showToast({title:"操作失败",icon:"error"});case 12:case"end":return s.stop()}}),s,null,[[1,9]])})));return function(e){return s.apply(this,arguments)}}()});case 1:case"end":return s.stop()}}),s)})))()},rejectIssue:function(){var t=this;return(0,a.default)(n.default.mark((function s(){return n.default.wrap((function(s){while(1)switch(s.prev=s.next){case 0:e.showModal({title:"重新整改",content:"确定要求重新整改此问题吗？",success:function(){var s=(0,a.default)(n.default.mark((function s(r){var a,o,u;return n.default.wrap((function(s){while(1)switch(s.prev=s.next){case 0:if(!r.confirm){s.next=12;break}return s.prev=1,a=i("882c"),o=a.callCloudFunction,s.next=5,o("hygiene-monthly-inspection",{action:"updateMonthlyIssue",data:{issue_id:t.issueId,status:"rejected",action_type:"reject"}});case 5:u=s.sent,u&&u.success&&(t.issue.status="rejected",t.generateUnifiedTimeline(),e.showToast({title:"已要求重新整改",icon:"success"}),e.$emit("monthlyIssueUpdated",{action:"reject",issueId:t.issueId,issueData:t.issue})),s.next=12;break;case 9:s.prev=9,s.t0=s["catch"](1),e.showToast({title:"操作失败",icon:"error"});case 12:case"end":return s.stop()}}),s,null,[[1,9]])})));return function(e){return s.apply(this,arguments)}}()});case 1:case"end":return s.stop()}}),s)})))()},changeIssueStatus:function(){var t=this,i=[],s=this.issue.status,r=this.issue.responsible||this.issue.assigned_to;i=r?"assigned"===s?[{value:"pending",label:"待整改"},{value:"in_progress",label:"整改中"},{value:"pending_review",label:"待检查"}]:"pending"===s?[{value:"in_progress",label:"整改中"},{value:"pending_review",label:"待检查"}]:"in_progress"===s?[{value:"pending_review",label:"待检查"},{value:"pending",label:"待整改"}]:"pending_review"===s?[{value:"approved",label:"检查通过"},{value:"rejected",label:"重新整改"}]:"approved"===s?[{value:"pending",label:"重新打开"},{value:"rejected",label:"发现新问题，重新整改"}]:[{value:"pending",label:"待整改"},{value:"in_progress",label:"整改中"},{value:"pending_review",label:"待检查"},{value:"approved",label:"检查通过"}]:[{value:"assigned",label:"已分配"}],e.showActionSheet({itemList:i.map((function(e){return e.label})),success:function(){var e=(0,a.default)(n.default.mark((function e(s){var r;return n.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=i[s.tapIndex],!r||r.value===t.issue.status){e.next=4;break}return e.next=4,t.updateIssueStatus(r.value);case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()})},updateIssueStatus:function(t){var s=this;return(0,a.default)(n.default.mark((function r(){var a,o,u,c;return n.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,a=i("882c"),o=a.callCloudFunction,u=t,"rejected"===t&&(u="rejected"),n.next=6,o("hygiene-monthly-inspection",{action:"updateMonthlyIssue",data:{issue_id:s.issueId,status:u,action_type:"admin_update"}});case 6:if(c=n.sent,!c||!c.success){n.next=14;break}s.issue.status=t,s.generateUnifiedTimeline(),e.showToast({title:"状态更新成功",icon:"success"}),e.$emit("monthlyIssueUpdated",{action:"update_status",issueId:s.issueId,newStatus:t,issueData:s.issue}),n.next=15;break;case 14:throw new Error((null===c||void 0===c?void 0:c.message)||(null===c||void 0===c?void 0:c.error)||"状态更新失败");case 15:n.next=21;break;case 17:n.prev=17,n.t0=n["catch"](0),console.error("更新问题状态失败:",n.t0),e.showToast({title:n.t0.message||"更新失败",icon:"error"});case 21:case"end":return n.stop()}}),r,null,[[0,17]])})))()},goToRectificationPage:function(){e.navigateTo({url:"/pages/6s_pkg/rectification-submit?issueId=".concat(this.issueId)})},previewImage:function(t){e.previewImage({urls:this.issue.images,current:t})},previewRectificationImage:function(t){var i=this.issue.rectification_photos.map((function(e){return e.url||e}));e.previewImage({urls:i,current:t})},reopenIssue:function(){var t=this;return(0,a.default)(n.default.mark((function s(){return n.default.wrap((function(s){while(1)switch(s.prev=s.next){case 0:e.showModal({title:"确认重新打开",content:"确定要重新打开这个问题吗？",success:function(){var s=(0,a.default)(n.default.mark((function s(r){var a,o,u;return n.default.wrap((function(s){while(1)switch(s.prev=s.next){case 0:if(!r.confirm){s.next=19;break}return s.prev=1,a=i("882c"),o=a.callCloudFunction,s.next=5,o("hygiene-monthly-inspection",{action:"updateMonthlyIssue",data:{issue_id:t.issueId,status:"in_progress",action_type:"reopen"}});case 5:if(u=s.sent,!u||!u.success){s.next=13;break}t.issue.status="in_progress",t.generateUnifiedTimeline(),e.showToast({title:"已重新打开",icon:"success"}),e.$emit("monthlyIssueUpdated",{action:"reopen",issueId:t.issueId,issueData:t.issue}),s.next=14;break;case 13:throw new Error((null===u||void 0===u?void 0:u.message)||"重新打开失败");case 14:s.next=19;break;case 16:s.prev=16,s.t0=s["catch"](1),e.showToast({title:s.t0.message||"操作失败",icon:"error"});case 19:case"end":return s.stop()}}),s,null,[[1,16]])})));return function(e){return s.apply(this,arguments)}}()});case 1:case"end":return s.stop()}}),s)})))()},editIssue:function(){var t={title:this.issue.title,description:this.issue.description,location:this.issue.location,deadline:this.issue.deadline,responsible:this.issue.responsible,responsible_id:this.issue.assigned_to||this.issue.responsible_id,priority:this.issue.priority,images:this.issue.images||[]};e.navigateTo({url:"/pages/6s_pkg/issue-add?editId=".concat(this.issueId,"&editData=").concat(encodeURIComponent(JSON.stringify(t)))})},formatDateTime:function(e){if(!e)return"";try{var t;if("string"===typeof e){var i=e;e.includes("-")&&e.includes(" ")?(i=e.replace(/-/g,"/"),i.includes(":00",i.length-3)||(i+=":00")):e.includes("-")&&!e.includes("T")&&(i=e.replace(/-/g,"/")),t=new Date(i)}else t=e;if(isNaN(t.getTime()))return(null===e||void 0===e?void 0:e.toString())||"";var s=t.getFullYear(),n=String(t.getMonth()+1).padStart(2,"0"),r=String(t.getDate()).padStart(2,"0"),a=String(t.getHours()).padStart(2,"0"),o=String(t.getMinutes()).padStart(2,"0");return"".concat(s,"-").concat(n,"-").concat(r," ").concat(a,":").concat(o)}catch(u){return(null===e||void 0===e?void 0:e.toString())||""}},handleIssueUpdated:function(t){if(console.log("issue-detail 收到更新事件:",t),t&&t.issueId&&t.issueId===this.issueId)if(console.log("当前问题需要更新，开始刷新数据..."),"submit_rectification"===t.action){if(this.issue){this.issue.status=t.status||"pending_review",this.issue.history||(this.issue.history=[]);var i=this.issue.history.some((function(e){return"submit_rectification"===e.action||"整改完成"===e.action}));i||this.issue.history.push({action:"submit_rectification",description:"负责人已完成整改并提交检查",time:this.formatDateTime(new Date),operator:this.issue.responsible||"负责人"}),this.checkSubmissionStatus(),this.generateUnifiedTimeline(),e.showToast({title:"整改已提交",icon:"success",duration:2e3})}}else this.loadIssueDetail()}}};t.default=o}).call(this,i("df3c")["default"])},7953:function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return s}));var s={uniIcons:function(){return Promise.all([i.e("common/vendor"),i.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(i.bind(null,"6ddf"))},pEmptyState:function(){return i.e("components/p-empty-state/p-empty-state").then(i.bind(null,"9b76"))}},n=function(){var e=this,t=e.$createElement,i=(e._self._c,!e.loading&&!e.loadError&&e.dataLoaded&&e.issue?e.getIssueNumber():null),s=!e.loading&&!e.loadError&&e.dataLoaded&&e.issue?e.getStatusText(e.issue.status):null,n=!e.loading&&!e.loadError&&e.dataLoaded&&e.issue?e.getPriorityText(e.issue&&e.issue.priority?e.issue.priority:"normal"):null,r=!e.loading&&!e.loadError&&e.dataLoaded&&e.issue?e.issue.images&&e.issue.images.length>0:null,a=!e.loading&&!e.loadError&&e.dataLoaded&&e.issue?e.issue.rectification_description||e.issue.rectification_photos&&e.issue.rectification_photos.length>0:null,o=!e.loading&&!e.loadError&&e.dataLoaded&&e.issue&&a?e.issue.rectification_photos&&e.issue.rectification_photos.length>0:null,u=!e.loading&&!e.loadError&&e.dataLoaded&&e.issue&&a&&e.issue.completed_at?e.formatDateTime(e.issue.completed_at):null,c=e.dataLoaded&&e.issue&&e.shouldShowActionButton?e.getButtonText():null;e.$mp.data=Object.assign({},{$root:{m0:i,m1:s,m2:n,g0:r,g1:a,g2:o,m3:u,m4:c}})},r=[]},"8a6f":function(e,t,i){"use strict";i.r(t);var s=i("7228"),n=i.n(s);for(var r in s)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return s[e]}))}(r);t["default"]=n.a},9370:function(e,t,i){"use strict";(function(e,t){var s=i("47a9");i("357b"),i("861b");s(i("3240"));var n=s(i("69b6"));e.__webpack_require_UNI_MP_PLUGIN__=i,t(n.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},d9a9:function(e,t,i){}},[["9370","common/runtime","common/vendor"]]]);