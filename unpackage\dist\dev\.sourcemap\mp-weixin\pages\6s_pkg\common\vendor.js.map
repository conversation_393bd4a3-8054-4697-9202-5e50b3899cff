{"version": 3, "sources": ["uni-app:///utils/api/hygiene.js"], "names": ["HygieneAPI", "areaId", "callCloudFunction", "action", "data", "id", "result", "success", "Error", "message", "console", "error", "params", "cleaningData", "updateData", "months", "area_id", "weekStart", "userId", "week_start", "user_id", "inspectionData", "issueData", "cleaningRecordId", "photoUrls", "cleaning_record_id", "photo_urls", "rectificationData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAKA;AAAmD;AAAA;AAAA,IAE7CA,UAAU;EAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IACd;AACF;AACA;AACA;AACA;IAJE;MAAA,6FAKA,iBAA2BC,MAAM;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAER,IAAAC,uBAAiB,EAAC,yBAAyB,EAAE;kBAChEC,MAAM,EAAE,eAAe;kBACvBC,IAAI,EAAE;oBAAEC,EAAE,EAAEJ;kBAAO;gBACrB,CAAC,CAAC;cAAA;gBAHIK,MAAM;gBAAA,KAKRA,MAAM,CAACC,OAAO;kBAAA;kBAAA;gBAAA;gBAAA,iCACTD,MAAM,CAACF,IAAI;cAAA;gBAAA,MAEZ,IAAII,KAAK,CAACF,MAAM,CAACG,OAAO,IAAI,WAAW,CAAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGhDC,OAAO,CAACC,KAAK,CAAC,YAAY,cAAQ;gBAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAGtC;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;EAJE;IAAA;IAAA;MAAA,2FAKA;QAAA;UAAA;UAAA;QAAA;UAAA;YAAA;cAAA;gBAAyBC,MAAM,8DAAG,CAAC,CAAC;gBAAA;gBAAA;gBAAA,OAEX,IAAAV,uBAAiB,EAAC,yBAAyB,EAAE;kBAChEC,MAAM,EAAE,aAAa;kBACrBC,IAAI,EAAEQ;gBACR,CAAC,CAAC;cAAA;gBAHIN,MAAM;gBAAA,KAKRA,MAAM,CAACC,OAAO;kBAAA;kBAAA;gBAAA;gBAAA,kCACTD,MAAM,CAACF,IAAI;cAAA;gBAAA,MAEZ,IAAII,KAAK,CAACF,MAAM,CAACG,OAAO,IAAI,WAAW,CAAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGhDC,OAAO,CAACC,KAAK,CAAC,YAAY,eAAQ;gBAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAGtC;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;EAJE;IAAA;IAAA;MAAA,oGAKA,kBAAkCE,YAAY;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAErB,IAAAX,uBAAiB,EAAC,kBAAkB,EAAE;kBACzDC,MAAM,EAAE,sBAAsB;kBAC9BC,IAAI,EAAES;gBACR,CAAC,CAAC;cAAA;gBAHIP,MAAM;gBAAA,KAKRA,MAAM,CAACC,OAAO;kBAAA;kBAAA;gBAAA;gBAAA,kCACTD,MAAM,CAACF,IAAI;cAAA;gBAAA,MAEZ,IAAII,KAAK,CAACF,MAAM,CAACG,OAAO,IAAI,UAAU,CAAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAG/CC,OAAO,CAACC,KAAK,CAAC,WAAW,eAAQ;gBAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAGrC;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;EAJE;IAAA;IAAA;MAAA,kGAKA;QAAA;UAAA;UAAA;QAAA;UAAA;YAAA;cAAA;gBAAgCC,MAAM,8DAAG,CAAC,CAAC;gBAAA;gBAAA;gBAAA,OAElB,IAAAV,uBAAiB,EAAC,kBAAkB,EAAE;kBACzDC,MAAM,EAAE,oBAAoB;kBAC5BC,IAAI,EAAEQ;gBACR,CAAC,CAAC;cAAA;gBAHIN,MAAM;gBAAA,KAKRA,MAAM,CAACC,OAAO;kBAAA;kBAAA;gBAAA;gBAAA,kCACTD,MAAM,CAACF,IAAI;cAAA;gBAAA,MAEZ,IAAII,KAAK,CAACF,MAAM,CAACG,OAAO,IAAI,UAAU,CAAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAG/CC,OAAO,CAACC,KAAK,CAAC,WAAW,eAAQ;gBAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAGrC;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;EAJE;IAAA;IAAA;MAAA,oGAKA;QAAA;UAAA;UAAA;QAAA;UAAA;YAAA;cAAA;gBAAkCC,MAAM,8DAAG,CAAC,CAAC;gBAAA;gBAAA;gBAAA,OAEpB,IAAAV,uBAAiB,EAAC,kBAAkB,EAAE;kBACzDC,MAAM,EAAE,sBAAsB;kBAC9BC,IAAI,EAAEQ;gBACR,CAAC,CAAC;cAAA;gBAHIN,MAAM;gBAAA,KAKRA,MAAM,CAACC,OAAO;kBAAA;kBAAA;gBAAA;gBAAA,kCACTD,MAAM,CAACF,IAAI;cAAA;gBAAA,MAEZ,IAAII,KAAK,CAACF,MAAM,CAACG,OAAO,IAAI,UAAU,CAAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAG/CC,OAAO,CAACC,KAAK,CAAC,aAAa,eAAQ;gBAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAGvC;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;EALE;IAAA;IAAA;MAAA,oGAMA,kBAAkCN,EAAE,EAAES,UAAU;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEvB,IAAAZ,uBAAiB,EAAC,kBAAkB,EAAE;kBACzDC,MAAM,EAAE,sBAAsB;kBAC9BC,IAAI;oBAAIC,EAAE,EAAFA;kBAAE,GAAKS,UAAU;gBAC3B,CAAC,CAAC;cAAA;gBAHIR,MAAM;gBAAA,KAKRA,MAAM,CAACC,OAAO;kBAAA;kBAAA;gBAAA;gBAAA,kCACTD,MAAM,CAACF,IAAI;cAAA;gBAAA,MAEZ,IAAII,KAAK,CAACF,MAAM,CAACG,OAAO,IAAI,UAAU,CAAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAG/CC,OAAO,CAACC,KAAK,CAAC,WAAW,eAAQ;gBAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAGrC;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;EAJE;IAAA;IAAA;MAAA,oGAKA,kBAAkCN,EAAE;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEX,IAAAH,uBAAiB,EAAC,kBAAkB,EAAE;kBACzDC,MAAM,EAAE,sBAAsB;kBAC9BC,IAAI,EAAE;oBAAEC,EAAE,EAAFA;kBAAG;gBACb,CAAC,CAAC;cAAA;gBAHIC,MAAM;gBAAA,KAKRA,MAAM,CAACC,OAAO;kBAAA;kBAAA;gBAAA;gBAAA,kCACTD,MAAM,CAACF,IAAI;cAAA;gBAAA,MAEZ,IAAII,KAAK,CAACF,MAAM,CAACG,OAAO,IAAI,UAAU,CAAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAG/CC,OAAO,CAACC,KAAK,CAAC,WAAW,eAAQ;gBAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAGrC;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;EALE;IAAA;IAAA;MAAA,kGAMA,kBAAgCV,MAAM;QAAA;UAAA;UAAA;QAAA;UAAA;YAAA;cAAA;gBAAEc,MAAM,8DAAG,CAAC;gBAAA;gBAAA;gBAAA,OAEzB,IAAAb,uBAAiB,EAAC,kBAAkB,EAAE;kBACzDC,MAAM,EAAE,oBAAoB;kBAC5BC,IAAI,EAAE;oBAAEY,OAAO,EAAEf,MAAM;oBAAEc,MAAM,EAANA;kBAAO;gBAClC,CAAC,CAAC;cAAA;gBAHIT,MAAM;gBAAA,KAKRA,MAAM,CAACC,OAAO;kBAAA;kBAAA;gBAAA;gBAAA,kCACTD,MAAM,CAACF,IAAI;cAAA;gBAAA,MAEZ,IAAII,KAAK,CAACF,MAAM,CAACG,OAAO,IAAI,UAAU,CAAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAG/CC,OAAO,CAACC,KAAK,CAAC,WAAW,eAAQ;gBAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAGrC;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;EALE;IAAA;IAAA;MAAA,iGAMA,kBAA+BM,SAAS,EAAEC,MAAM;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEvB,IAAAhB,uBAAiB,EAAC,kBAAkB,EAAE;kBACzDC,MAAM,EAAE,mBAAmB;kBAC3BC,IAAI,EAAE;oBAAEe,UAAU,EAAEF,SAAS;oBAAEG,OAAO,EAAEF;kBAAO;gBACjD,CAAC,CAAC;cAAA;gBAHIZ,MAAM;gBAAA,KAKRA,MAAM,CAACC,OAAO;kBAAA;kBAAA;gBAAA;gBAAA,kCACTD,MAAM,CAACF,IAAI;cAAA;gBAAA,MAEZ,IAAII,KAAK,CAACF,MAAM,CAACG,OAAO,IAAI,SAAS,CAAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAG9CC,OAAO,CAACC,KAAK,CAAC,YAAY,eAAQ;gBAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAGtC;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;EAJE;IAAA;IAAA;MAAA,sGAKA,mBAAoCU,cAAc;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEzB,IAAAnB,uBAAiB,EAAC,oBAAoB,EAAE;kBAC3DC,MAAM,EAAE,wBAAwB;kBAChCC,IAAI,EAAEiB;gBACR,CAAC,CAAC;cAAA;gBAHIf,MAAM;gBAAA,KAKRA,MAAM,CAACC,OAAO;kBAAA;kBAAA;gBAAA;gBAAA,mCACTD,MAAM,CAACF,IAAI;cAAA;gBAAA,MAEZ,IAAII,KAAK,CAACF,MAAM,CAACG,OAAO,IAAI,UAAU,CAAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAG/CC,OAAO,CAACC,KAAK,CAAC,WAAW,gBAAQ;gBAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAGrC;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;EAJE;IAAA;IAAA;MAAA,oGAKA;QAAA;UAAA;UAAA;QAAA;UAAA;YAAA;cAAA;gBAAkCC,MAAM,iEAAG,CAAC,CAAC;gBAAA;gBAAA;gBAAA,OAEpB,IAAAV,uBAAiB,EAAC,oBAAoB,EAAE;kBAC3DC,MAAM,EAAE,sBAAsB;kBAC9BC,IAAI,EAAEQ;gBACR,CAAC,CAAC;cAAA;gBAHIN,MAAM;gBAAA,KAKRA,MAAM,CAACC,OAAO;kBAAA;kBAAA;gBAAA;gBAAA,mCACTD,MAAM,CAACF,IAAI;cAAA;gBAAA,MAEZ,IAAII,KAAK,CAACF,MAAM,CAACG,OAAO,IAAI,UAAU,CAAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAG/CC,OAAO,CAACC,KAAK,CAAC,WAAW,gBAAQ;gBAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAGrC;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;EAJE;IAAA;IAAA;MAAA,iGAKA,mBAA+BW,SAAS;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEf,IAAApB,uBAAiB,EAAC,gBAAgB,EAAE;kBACvDC,MAAM,EAAE,mBAAmB;kBAC3BC,IAAI,EAAEkB;gBACR,CAAC,CAAC;cAAA;gBAHIhB,MAAM;gBAAA,KAKRA,MAAM,CAACC,OAAO;kBAAA;kBAAA;gBAAA;gBAAA,mCACTD,MAAM,CAACF,IAAI;cAAA;gBAAA,MAEZ,IAAII,KAAK,CAACF,MAAM,CAACG,OAAO,IAAI,UAAU,CAAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAG/CC,OAAO,CAACC,KAAK,CAAC,WAAW,gBAAQ;gBAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAGrC;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;EAJE;IAAA;IAAA;MAAA,+FAKA;QAAA;UAAA;UAAA;QAAA;UAAA;YAAA;cAAA;gBAA6BC,MAAM,iEAAG,CAAC,CAAC;gBAAA;gBAAA;gBAAA,OAEf,IAAAV,uBAAiB,EAAC,gBAAgB,EAAE;kBACvDC,MAAM,EAAE,iBAAiB;kBACzBC,IAAI,EAAEQ;gBACR,CAAC,CAAC;cAAA;gBAHIN,MAAM;gBAAA,KAKRA,MAAM,CAACC,OAAO;kBAAA;kBAAA;gBAAA;gBAAA,mCACTD,MAAM,CAACF,IAAI;cAAA;gBAAA,MAEZ,IAAII,KAAK,CAACF,MAAM,CAACG,OAAO,IAAI,UAAU,CAAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAG/CC,OAAO,CAACC,KAAK,CAAC,WAAW,gBAAQ;gBAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAGrC;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;EALE;IAAA;IAAA;MAAA,oGAMA,mBAAkCY,gBAAgB,EAAEC,SAAS;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEpC,IAAAtB,uBAAiB,EAAC,kBAAkB,EAAE;kBACzDC,MAAM,EAAE,sBAAsB;kBAC9BC,IAAI,EAAE;oBACJqB,kBAAkB,EAAEF,gBAAgB;oBACpCG,UAAU,EAAEF;kBACd;gBACF,CAAC,CAAC;cAAA;gBANIlB,MAAM;gBAAA,KAQRA,MAAM,CAACC,OAAO;kBAAA;kBAAA;gBAAA;gBAAA,mCACTD,MAAM,CAACF,IAAI;cAAA;gBAAA,MAEZ,IAAII,KAAK,CAACF,MAAM,CAACG,OAAO,IAAI,QAAQ,CAAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAG7CC,OAAO,CAACC,KAAK,CAAC,WAAW,gBAAQ;gBAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAGrC;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;EAJE;IAAA;IAAA;MAAA,yGAKA,mBAAuCgB,iBAAiB;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAE/B,IAAAzB,uBAAiB,EAAC,uBAAuB,EAAE;kBAC9DC,MAAM,EAAE,2BAA2B;kBACnCC,IAAI,EAAEuB;gBACR,CAAC,CAAC;cAAA;gBAHIrB,MAAM;gBAAA,KAKRA,MAAM,CAACC,OAAO;kBAAA;kBAAA;gBAAA;gBAAA,mCACTD,MAAM,CAACF,IAAI;cAAA;gBAAA,MAEZ,IAAII,KAAK,CAACF,MAAM,CAACG,OAAO,IAAI,UAAU,CAAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAG/CC,OAAO,CAACC,KAAK,CAAC,WAAW,gBAAQ;gBAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAGrC;MAAA;QAAA;MAAA;MAAA;IAAA;IAED;AACF;AACA;AACA;AACA;EAJE;IAAA;IAAA;MAAA,uGAKA;QAAA;UAAA;UAAA;QAAA;UAAA;YAAA;cAAA;gBAAqCC,MAAM,iEAAG,CAAC,CAAC;gBAAA;gBAAA;gBAAA,OAEvB,IAAAV,uBAAiB,EAAC,uBAAuB,EAAE;kBAC9DC,MAAM,EAAE,yBAAyB;kBACjCC,IAAI,EAAEQ;gBACR,CAAC,CAAC;cAAA;gBAHIN,MAAM;gBAAA,KAKRA,MAAM,CAACC,OAAO;kBAAA;kBAAA;gBAAA;gBAAA,mCACTD,MAAM,CAACF,IAAI;cAAA;gBAAA,MAEZ,IAAII,KAAK,CAACF,MAAM,CAACG,OAAO,IAAI,UAAU,CAAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAG/CC,OAAO,CAACC,KAAK,CAAC,WAAW,gBAAQ;gBAAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAGrC;MAAA;QAAA;MAAA;MAAA;IAAA;EAAA;EAAA;AAAA;AAAA,eAGYX,UAAU;AAAA,2B", "file": "pages/6s_pkg/common/vendor.js", "sourcesContent": ["/**\r\n * 6S卫生管理系统API\r\n * 统一管理6S相关的云函数调用\r\n */\r\n\r\nimport { callCloudFunction } from '@/utils/auth.js'\r\n\r\nclass HygieneAPI {\r\n  /**\r\n   * 获取责任区详情\r\n   * @param {String} areaId 责任区ID\r\n   * @returns {Promise<Object>} 责任区信息\r\n   */\r\n  static async getAreaDetail(areaId) {\r\n    try {\r\n      const result = await callCloudFunction('hygiene-area-management', {\r\n        action: 'getAreaDetail',\r\n        data: { id: areaId }\r\n      });\r\n\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.message || '获取责任区信息失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('获取责任区详情失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取责任区列表\r\n   * @param {Object} params 查询参数\r\n   * @returns {Promise<Object>} 责任区列表\r\n   */\r\n  static async getAreaList(params = {}) {\r\n    try {\r\n      const result = await callCloudFunction('hygiene-area-management', {\r\n        action: 'getAreaList',\r\n        data: params\r\n      });\r\n\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.message || '获取责任区列表失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('获取责任区列表失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 创建清理记录\r\n   * @param {Object} cleaningData 清理数据\r\n   * @returns {Promise<Object>} 创建结果\r\n   */\r\n  static async createCleaningRecord(cleaningData) {\r\n    try {\r\n      const result = await callCloudFunction('hygiene-cleaning', {\r\n        action: 'createCleaningRecord',\r\n        data: cleaningData\r\n      });\r\n\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.message || '创建清理记录失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('创建清理记录失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取清理记录列表\r\n   * @param {Object} params 查询参数\r\n   * @returns {Promise<Object>} 清理记录列表\r\n   */\r\n  static async getCleaningRecords(params = {}) {\r\n    try {\r\n      const result = await callCloudFunction('hygiene-cleaning', {\r\n        action: 'getCleaningRecords',\r\n        data: params\r\n      });\r\n\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.message || '获取清理记录失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('获取清理记录失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取我的清理记录\r\n   * @param {Object} params 查询参数\r\n   * @returns {Promise<Array>} 我的清理记录\r\n   */\r\n  static async getMyCleaningRecords(params = {}) {\r\n    try {\r\n      const result = await callCloudFunction('hygiene-cleaning', {\r\n        action: 'getMyCleaningRecords',\r\n        data: params\r\n      });\r\n\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.message || '获取清理记录失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('获取我的清理记录失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 更新清理记录\r\n   * @param {String} id 记录ID\r\n   * @param {Object} updateData 更新数据\r\n   * @returns {Promise<Object>} 更新结果\r\n   */\r\n  static async updateCleaningRecord(id, updateData) {\r\n    try {\r\n      const result = await callCloudFunction('hygiene-cleaning', {\r\n        action: 'updateCleaningRecord',\r\n        data: { id, ...updateData }\r\n      });\r\n\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.message || '更新清理记录失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('更新清理记录失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 删除清理记录\r\n   * @param {String} id 记录ID\r\n   * @returns {Promise<Object>} 删除结果\r\n   */\r\n  static async deleteCleaningRecord(id) {\r\n    try {\r\n      const result = await callCloudFunction('hygiene-cleaning', {\r\n        action: 'deleteCleaningRecord',\r\n        data: { id }\r\n      });\r\n\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.message || '删除清理记录失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('删除清理记录失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取清理历史\r\n   * @param {String} areaId 责任区ID\r\n   * @param {Number} months 查询月数，默认6个月\r\n   * @returns {Promise<Object>} 清理历史\r\n   */\r\n  static async getCleaningHistory(areaId, months = 6) {\r\n    try {\r\n      const result = await callCloudFunction('hygiene-cleaning', {\r\n        action: 'getCleaningHistory',\r\n        data: { area_id: areaId, months }\r\n      });\r\n\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.message || '获取清理历史失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('获取清理历史失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取周清理计划\r\n   * @param {String} weekStart 周开始时间\r\n   * @param {String} userId 用户ID（可选）\r\n   * @returns {Promise<Object>} 周清理计划\r\n   */\r\n  static async getWeeklySchedule(weekStart, userId) {\r\n    try {\r\n      const result = await callCloudFunction('hygiene-cleaning', {\r\n        action: 'getWeeklySchedule',\r\n        data: { week_start: weekStart, user_id: userId }\r\n      });\r\n\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.message || '获取周计划失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('获取周清理计划失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 创建检查记录\r\n   * @param {Object} inspectionData 检查数据\r\n   * @returns {Promise<Object>} 创建结果\r\n   */\r\n  static async createInspectionRecord(inspectionData) {\r\n    try {\r\n      const result = await callCloudFunction('hygiene-inspection', {\r\n        action: 'createInspectionRecord',\r\n        data: inspectionData\r\n      });\r\n\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.message || '创建检查记录失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('创建检查记录失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取检查记录列表\r\n   * @param {Object} params 查询参数\r\n   * @returns {Promise<Object>} 检查记录列表\r\n   */\r\n  static async getInspectionRecords(params = {}) {\r\n    try {\r\n      const result = await callCloudFunction('hygiene-inspection', {\r\n        action: 'getInspectionRecords',\r\n        data: params\r\n      });\r\n\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.message || '获取检查记录失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('获取检查记录失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 创建问题记录\r\n   * @param {Object} issueData 问题数据\r\n   * @returns {Promise<Object>} 创建结果\r\n   */\r\n  static async createIssueRecord(issueData) {\r\n    try {\r\n      const result = await callCloudFunction('hygiene-issues', {\r\n        action: 'createIssueRecord',\r\n        data: issueData\r\n      });\r\n\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.message || '创建问题记录失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('创建问题记录失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取问题记录列表\r\n   * @param {Object} params 查询参数\r\n   * @returns {Promise<Object>} 问题记录列表\r\n   */\r\n  static async getIssueRecords(params = {}) {\r\n    try {\r\n      const result = await callCloudFunction('hygiene-issues', {\r\n        action: 'getIssueRecords',\r\n        data: params\r\n      });\r\n\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.message || '获取问题记录失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('获取问题记录失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 删除清理记录中的照片\r\n   * @param {String} cleaningRecordId 清理记录ID\r\n   * @param {Array} photoUrls 要删除的照片URL列表\r\n   * @returns {Promise<Object>} 删除结果\r\n   */\r\n  static async deleteCleaningPhotos(cleaningRecordId, photoUrls) {\r\n    try {\r\n      const result = await callCloudFunction('hygiene-cleaning', {\r\n        action: 'deleteCleaningPhotos',\r\n        data: {\r\n          cleaning_record_id: cleaningRecordId,\r\n          photo_urls: photoUrls\r\n        }\r\n      });\r\n\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.message || '删除照片失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('删除清理照片失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 创建整改记录\r\n   * @param {Object} rectificationData 整改数据\r\n   * @returns {Promise<Object>} 创建结果\r\n   */\r\n  static async createRectificationRecord(rectificationData) {\r\n    try {\r\n      const result = await callCloudFunction('hygiene-rectification', {\r\n        action: 'createRectificationRecord',\r\n        data: rectificationData\r\n      });\r\n\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.message || '创建整改记录失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('创建整改记录失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取整改记录列表\r\n   * @param {Object} params 查询参数\r\n   * @returns {Promise<Object>} 整改记录列表\r\n   */\r\n  static async getRectificationRecords(params = {}) {\r\n    try {\r\n      const result = await callCloudFunction('hygiene-rectification', {\r\n        action: 'getRectificationRecords',\r\n        data: params\r\n      });\r\n\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.message || '获取整改记录失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('获取整改记录失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\nexport default HygieneAPI\r\n"], "sourceRoot": ""}