<template>
	<view class="sixs-container">
		<view class="content-container">
			<view class="tab-content">
				<view class="section">
					<view class="overview-header">
						<view class="header-content">
							<text class="overview-title">6S管理概览</text>
							<text class="overview-subtitle">{{ currentTimeRangeText }}</text>
						</view>
					</view>
					
					<!-- 加载中状态 -->
					<view v-if="loading && !dataLoaded" class="loading-container">
						<view class="loading-content">
							<view class="loading-spinner"></view>
							<text class="loading-text">统计数据加载中...</text>
						</view>
					</view>
					
					<!-- 错误状态 -->
					<view v-else-if="loadError" class="error-container">
						<view class="error-content">
							<uni-icons type="info" size="24" color="#FF3B30"></uni-icons>
							<text class="error-text">数据加载失败，请稍后重试</text>
							<button class="retry-btn" @click="loadPageDataOptimized">重新加载</button>
						</view>
					</view>
					
					<!-- 统计数据 -->
					<view v-else class="overview-stats">
						<view class="stat-item">
							<text class="stat-number blue">{{ stats.totalAreas }}</text>
							<text class="stat-label">总责任区</text>
						</view>
						<view class="stat-item">
							<text class="stat-number green">{{ stats.completionRate }}%</text>
							<text class="stat-label">本周完成率</text>
						</view>
						<view class="stat-item">
							<text class="stat-number orange">{{ stats.foundIssues }}</text>
							<text class="stat-label">待整改问题</text>
						</view>
						<view class="stat-item">
							<text class="stat-number red">{{ stats.urgentIssues }}</text>
							<text class="stat-label">逾期未清理</text>
						</view>
					</view>
				</view>

				<!-- 快捷操作 -->
				<view class="section">
					<view class="quick-actions-header">
						<view class="header-content">
							<text class="quick-actions-title">快捷操作</text>
							<text class="quick-actions-subtitle">常用功能快速入口</text>
						</view>
					</view>
					<view class="quick-actions-grid">
						<view class="quick-action-btn primary" @click="goToMyArea">
							<view class="btn-content">
								<uni-icons type="list" size="20" color="#FFFFFF"></uni-icons>
								<text class="btn-text">我的责任区</text>
							</view>
						</view>
						<view class="quick-action-btn success" @click="goToPendingTasks">
							<view class="btn-content">
								<uni-icons type="eye" size="20" color="#FFFFFF"></uni-icons>
								<text class="btn-text">责任区检查</text>
							</view>
						</view>
						<view class="quick-action-btn warning" @click="uploadPhoto">
							<view class="btn-content">
								<uni-icons type="camera" size="20" color="#FFFFFF"></uni-icons>
								<text class="btn-text">月度检查</text>
							</view>
						</view>
						<view class="quick-action-btn purple" @click="manageBaseData">
							<view class="btn-content">
								<uni-icons type="gear" size="20" color="#FFFFFF"></uni-icons>
								<text class="btn-text">基础数据</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 最新动态 -->
				<view class="section">
					<view class="section-header">
						<view class="header-content">
							<text class="section-title">最新动态</text>
							<text class="section-subtitle">最近的检查和整改情况</text>
						</view>
					</view>
					<view class="activity-list">
						<!-- 加载中状态 -->
						<view v-if="activitiesLoading" class="loading-container">
							<view class="loading-content">
								<view class="loading-spinner"></view>
								<text class="loading-text">动态数据加载中...</text>
							</view>
						</view>
						
						<!-- 空状态 -->
						<view v-else-if="recentActivities.length === 0" class="empty-state">
							<uni-icons type="info" size="20" color="#8E8E93"></uni-icons>
							<text class="empty-text">暂无最新动态</text>
						</view>
						
						<!-- 活动列表 -->
						<view v-else class="activity-item" v-for="(activity, index) in recentActivities" :key="index">
							<view class="activity-icon" :class="activity.type">
								<!-- 使用uni-icons图标 -->
								<uni-icons :type="activity.iconType" :color="activity.iconColor" size="18"></uni-icons>
							</view>
							<view class="activity-content">
								<text class="activity-title">{{ activity.title }}</text>
								<text class="activity-time">{{ activity.time }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 智能提醒 -->
				<view class="section">
					<view class="section-header">
						<view class="header-content">
							<text class="section-title">智能提醒</text>
							<text class="section-subtitle">根据当前时间和业务规则智能生成</text>
						</view>
					</view>
					<view class="reminder-list">
						<!-- 加载中状态 -->
						<view v-if="remindersLoading" class="loading-container">
							<view class="loading-content">
								<view class="loading-spinner"></view>
								<text class="loading-text">智能提醒生成中...</text>
							</view>
						</view>
						
						<!-- 空状态 -->
						<view v-else-if="smartReminders.length === 0" class="empty-state">
							<uni-icons type="checkmarkempty" size="20" color="#34C759"></uni-icons>
							<text class="empty-text">暂无智能提醒</text>
						</view>
						
						<!-- 提醒列表 -->
						<view v-else class="reminder-item" :class="reminder.type" v-for="(reminder, index) in smartReminders" :key="index">
							<view class="reminder-icon" :class="reminder.type">
								<!-- 使用CSS文字符号，简洁美观 -->
								<text class="icon-text">{{ reminder.iconSymbol }}</text>
							</view>
							<view class="reminder-content">
								<text class="reminder-title">{{ reminder.title }}</text>
								<text class="reminder-desc">{{ reminder.description }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>


		</view>
	</view>
</template>

<script>
import { callCloudFunction } from '@/utils/auth.js';

export default {
	components: {
		// uni-icons 组件会自动注册，无需手动导入
	},
	data() {
		return {
			currentTab: 0,
			tabs: [
				{ name: '6S首页', key: 'home' }
			],
			selectedTimeFilter: 'week',
			
			// 页面状态
			loading: false,
			dataLoaded: false,
			loadError: false,
			activitiesLoading: true,  // 初始状态为加载中
			remindersLoading: true,   // 初始状态为加载中
			
			// 性能优化缓存
			processCache: null,
			timeCache: null,
			
			// 数据结构
			stats: {
				totalAreas: 0,
				completedAreas: 0,
				foundIssues: 0,
				averageRating: 0,
				completionRate: 0,
				urgentIssues: 0
			},
			recentActivities: [],
			smartReminders: []
		}
	},

	computed: {
		userInfo() {
			try {
				const currentUserInfo = uniCloud.getCurrentUserInfo();
				const storedUserInfo = uni.getStorageSync('uni-id-pages-userInfo') || {};
				// 合并云端用户信息和本地存储的详细信息
				return {
					...storedUserInfo,
					...currentUserInfo
				};
			} catch (error) {
				console.error('获取用户信息失败:', error);
				return uni.getStorageSync('uni-id-pages-userInfo') || {};
			}
		},

		hasInspectionPermission() {
			const role = this.userInfo.role;
			return ['supervisor', 'PM', 'GM', 'admin'].includes(role);
		},
		
		// 获取当前时间范围的显示文本
		currentTimeRangeText() {
			if (!this.timeCache) return '本周';
			const { currentYear, currentMonth, currentWeekRange } = this.timeCache;
			
			switch (this.selectedTimeFilter) {
				case 'today':
					return `${currentYear}年${currentMonth}月${new Date().getDate()}日`;
				case 'week':
					return `${currentWeekRange.start} - ${currentWeekRange.end}`;
				case 'month':
					return `${currentYear}年${currentMonth}月`;
				default:
					return `${currentWeekRange.start} - ${currentWeekRange.end}`;
			}
		}
	},

	onLoad() {
		this.initCaches();
		this.loadPageDataOptimized();
		
		// 监听数据更新事件
		uni.$on('cleaningRecordUpdated', this.handleDataUpdated);
		uni.$on('rectificationRecordUpdated', this.handleDataUpdated);
		uni.$on('inspectionRecordUpdated', this.handleDataUpdated);
	},

	onUnload() {
		// 移除事件监听
		uni.$off('cleaningRecordUpdated', this.handleDataUpdated);
		uni.$off('rectificationRecordUpdated', this.handleDataUpdated);
		uni.$off('inspectionRecordUpdated', this.handleDataUpdated);
	},

	onShow() {
		// 页面重新显示时，如果数据已加载则进行静默刷新
		if (this.dataLoaded && !this.loading) {
			this.silentRefreshData();
		}
	},

	methods: {
		// 初始化缓存
		initCaches() {
			this.initProcessCache();
			this.initTimeCache();
		},
		
		// 初始化处理缓存
		initProcessCache() {
			if (!this.processCache) {
				this.processCache = {
					// 活动类型映射
					activityTypeMap: {
						'cleaning_completed': {
							type: 'success',
							iconType: 'checkmarkempty',
							iconColor: '#34C759'
						},
						'issue_found': {
							type: 'warning',
							iconType: 'info',
							iconColor: '#FF9500'
						},
						'inspection_completed': {
							type: 'info',
							iconType: 'search',
							iconColor: '#007AFF'
						},
						'rectification_completed': {
							type: 'success',
							iconType: 'checkmarkempty',
							iconColor: '#34C759'
						},
						'area_overdue': {
							type: 'danger',
							iconType: 'closeempty',
							iconColor: '#FF3B30'
						}
					},
					
					// 提醒类型映射
					reminderTypeMap: {
						'pending_cleaning': {
							type: 'info',
							iconSymbol: 'i',
							title: '清理提醒'
						},
						'overdue_cleaning': {
							type: 'warning',
							iconSymbol: '⚠',
							title: '清理逾期'
						},
						'pending_rectification': {
							type: 'warning',
							iconSymbol: '!',
							title: '整改提醒'
						},
						'overdue_rectification': {
							type: 'danger',
							iconSymbol: '⚠',
							title: '整改逾期'
						},
						'inspection_due': {
							type: 'info',
							iconSymbol: '○',
							title: '检查提醒'
						},
						'missed_inspection': {
							type: 'danger',
							iconSymbol: '⚠',
							title: '漏检提醒'
						},
						'overdue_review': {
							type: 'danger',
							iconSymbol: '!',
							title: '复查逾期'
						},
						'not_cleaned': {
							type: 'warning',
							iconSymbol: '●',
							title: '未打扫提醒'
						},
						'all_good': {
							type: 'success',
							iconSymbol: '✓',
							title: '状态良好'
						},
						'no_areas': {
							type: 'info',
							iconSymbol: '?',
							title: '系统提示'
						}
					},
					
					// 时间格式化器
					timeFormatter: this.createTimeFormatter()
				};
			}
		},
		
		// 初始化时间缓存
		initTimeCache() {
			if (!this.timeCache) {
				const now = new Date();
				this.timeCache = {
					now,
					currentYear: now.getFullYear(),
					currentMonth: now.getMonth() + 1,
					currentWeekRange: this.getCurrentWeekRange(now),
					lastUpdateTime: now.getTime()
				};
			}
		},
		
		// 创建时间格式化器
		createTimeFormatter() {
			return (dateString) => {
				if (!dateString) return '--';
				try {
					const date = new Date(dateString);
					if (isNaN(date.getTime())) return '--';
					
					const now = new Date();
					const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
					
					if (diffInHours < 1) {
						const diffInMinutes = Math.floor((now - date) / (1000 * 60));
						return `${diffInMinutes}分钟前`;
					} else if (diffInHours < 24) {
						return `${diffInHours}小时前`;
					} else {
						const diffInDays = Math.floor(diffInHours / 24);
						if (diffInDays === 1) return '昨天';
						if (diffInDays < 7) return `${diffInDays}天前`;
						return `${date.getMonth() + 1}月${date.getDate()}日`;
					}
				} catch (error) {
					return '--';
				}
			};
		},
		
		// 获取当前周的起始和结束日期
		getCurrentWeekRange(inputDate = new Date()) {
			const date = new Date(inputDate);
			const day = date.getDay();
			const diff = date.getDate() - day + (day === 0 ? -6 : 1);
			
			const monday = new Date(date);
			monday.setDate(diff);
			
			const sunday = new Date(date);
			sunday.setDate(diff + 6);
			
			return {
				start: `${monday.getMonth() + 1}月${monday.getDate()}日`,
				end: `${sunday.getMonth() + 1}月${sunday.getDate()}日`,
				monday: monday,
				sunday: sunday
			};
		},

		// 优化的页面数据加载
		async loadPageDataOptimized() {
			this.loading = true;
			this.loadError = false;
			
			try {
				// Phase 1: 快速加载统计数据
				await this.loadStatsDataOptimized();
				
				// 立即显示基本内容
				this.loading = false;
				this.dataLoaded = true;
				
				// Phase 2: 异步加载次要数据
				this.loadSecondaryDataAsync();
			} catch (error) {
				this.loading = false;
				this.loadError = true;
				this.handleLoadError(error);
			}
		},
		
		// 异步加载次要数据
		async loadSecondaryDataAsync() {
			try {
				await Promise.all([
					this.loadRecentActivitiesOptimized(),
					this.loadSmartRemindersOptimized()
				]);
			} catch (error) {
				// 静默处理次要数据加载失败
			} finally {
				// 确保加载状态被清除
				this.activitiesLoading = false;
				this.remindersLoading = false;
			}
		},
		
		// 优化的统计数据加载
		async loadStatsDataOptimized() {
			try {
				const result = await callCloudFunction('hygiene-area-management', {
					action: 'getOverviewStats',
					data: {
						timeFilter: this.selectedTimeFilter,
						includeDetails: false // 只要统计数据，不要详细列表
					}
				});
				
				if (result && result.success && result.data) {
					this.stats = {
						totalAreas: result.data.totalAreas || 0,
						completedAreas: result.data.completedAreas || 0,
						foundIssues: result.data.pendingRectifications || 0,
						averageRating: result.data.averageRating || 0,
						completionRate: result.data.completionRate || 0,
						urgentIssues: result.data.overdueAreas || 0
					};
				} else {
					throw new Error('获取统计数据失败');
				}
			} catch (error) {
				// 加载失败时使用默认值
				this.stats = {
					totalAreas: 0,
					completedAreas: 0,
					foundIssues: 0,
					averageRating: 0,
					completionRate: 0,
					urgentIssues: 0
				};
				throw error;
			}
		},
		
		// 优化的最新动态加载
		async loadRecentActivitiesOptimized() {
			try {
				const result = await callCloudFunction('hygiene-area-management', {
					action: 'getRecentActivities',
					data: {
						limit: 5,
						timeFilter: 'week' // 最新动态始终显示本周
					}
				});
				
				if (result && result.success && result.data) {
					this.recentActivities = this.processActivitiesData(result.data);
				}
			} catch (error) {
				this.recentActivities = [];
			}
		},
		
		// 优化的智能提醒加载
		async loadSmartRemindersOptimized() {
			try {
				const userRole = this.userInfo.role || 'employee';
				
				const result = await callCloudFunction('hygiene-area-management', {
					action: 'getSmartReminders',
					data: {
						userRole: userRole,
						limit: 3
					}
				});
				
				if (result && result.success && result.data) {
					this.smartReminders = this.processRemindersData(result.data);
				}
			} catch (error) {
				this.smartReminders = [];
			}
		},
		
		// 处理活动数据
		processActivitiesData(activities) {
			if (!Array.isArray(activities)) return [];
			
			return activities.map(activity => {
				const typeConfig = this.processCache.activityTypeMap[activity.type] || {
					type: 'info',
					iconType: 'info',
					iconColor: '#8E8E93'
				};
				
				return {
					...typeConfig,
					title: activity.title || '未知活动',
					time: this.processCache.timeFormatter(activity.created_at) || '--'
				};
			});
		},
		
		// 处理提醒数据
		processRemindersData(reminders) {
			if (!Array.isArray(reminders)) return [];
			
			return reminders.map(reminder => {
				const typeConfig = this.processCache.reminderTypeMap[reminder.type] || {
					type: 'info',
					iconSymbol: 'i',
					title: '系统提醒'
				};
				
				return {
					...typeConfig,
					description: reminder.description || '暂无详细信息'
				};
			});
		},
		
		// 静默刷新数据
		async silentRefreshData() {
			if (this.loading) return;
			
			try {
				// 更新时间缓存
				this.timeCache = null;
				this.initTimeCache();
				
				// 静默刷新统计数据
				await this.loadStatsDataOptimized();
				
				// 静默刷新次要数据
				this.loadSecondaryDataAsync();
			} catch (error) {
				// 静默处理错误
			}
		},
		
		// 错误处理
		handleLoadError(error) {
			uni.showToast({
				title: '数据加载失败',
				icon: 'none',
				duration: 2000
			});
		},

		// 时间筛选切换
		switchTimeFilter(filter) {
			if (this.selectedTimeFilter === filter) return;
			
			this.selectedTimeFilter = filter;
			
			// 设置加载状态
			this.activitiesLoading = true;
			this.remindersLoading = true;
			
			// 重新加载统计数据
			this.loadStatsDataOptimized().catch(error => {
				this.handleLoadError(error);
			});
			
			// 重新加载次要数据
			this.loadSecondaryDataAsync();
		},

		getTimeRangeTitle() {
			switch (this.selectedTimeFilter) {
				case 'today': return '今日';
				case 'week': return '本周';
				case 'month': return '本月';
				default: return '本周';
			}
		},

		// 处理数据更新事件
		handleDataUpdated(data) {
			// 通过事件已经收到更新通知，进行静默刷新
			this.silentRefreshData();
		},

		// 快捷操作方法
		goToMyArea() {
			uni.navigateTo({
				url: '/pages/6s_pkg/my-areas'
			});
		},

		goToPendingTasks() {
			uni.navigateTo({
				url: '/pages/6s_pkg/area-inspection'
			});
		},

		uploadPhoto() {
			uni.navigateTo({
				url: '/pages/6s_pkg/monthly-check'
			});
		},

		manageBaseData() {
			uni.navigateTo({
				url: '/pages/6s_pkg/data-manage'
			});
		}
	}
}
</script>

<style lang="scss" scoped>
/* 防止水平滚动的全局样式 */
* {
	box-sizing: border-box;
}

/* uni-icons样式优化 */
.sixs-container {
	min-height: 100vh;
	background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
	width: 100%;
	overflow-x: hidden; /* 防止水平滚动 */
	box-sizing: border-box;
}

.top-tabs {
	display: flex;
	background: #FFFFFF;
	border-bottom: 1px solid #E5E5E5;

	.tab-item {
		flex: 1;
		text-align: center;
		padding: 15px 0;
		font-size: 15px;
		color: #666;
		position: relative;

		&.active {
			color: #007AFF;
			font-weight: 500;

			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 30px;
				height: 2px;
				background: #007AFF;
			}
		}
	}
}

.content-container {
	width: 100%;
	max-width: 100%; /* 移除95%限制，使用全宽 */
	margin: 0;
	padding: 0; /* 移除内边距，由子元素控制 */
	position: relative;
	z-index: 5;
	box-sizing: border-box;
	overflow-x: hidden; /* 防止水平滚动 */
}

.card-body {
	padding: 20px;
	position: relative;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 1px;
		background: linear-gradient(90deg, transparent, rgba(0, 122, 255, 0.3), transparent);
	}
}

.tab-content {
	padding-bottom: 20px;
}

.section {
	background: #FFFFFF;
	border-radius: 12px;
	margin: 16px 12px; /* 减少左右边距，防止超出屏幕 */
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
	border: 1px solid rgba(0, 0, 0, 0.06);
	box-sizing: border-box;
}

.section-header {
	padding: 24px 24px 16px;
	border-bottom: 1px solid rgba(0, 0, 0, 0.06);

	.header-content {
		display: flex;
		flex-direction: row; /* 水平排列 */
		align-items: baseline; /* 基线对齐，保持文字对齐 */
		justify-content: space-between; /* 左右对齐 */
	}

	.section-title {
		font-size: 17px;
		font-weight: 600;
		color: #1D1D1F;
		margin-bottom: 0; /* 移除margin，使用gap控制 */
	}

	.section-subtitle {
		font-size: 13px;
		color: #8E8E93;
	}
}

/* 快捷操作样式 - 完全按照demo实现 */
.quick-actions-header {
	padding: 24px 24px 16px;
	border-bottom: 1px solid rgba(0, 0, 0, 0.06);

	.header-content {
		display: flex;
		flex-direction: row; /* 水平排列 */
		align-items: baseline; /* 基线对齐 */
		justify-content: space-between; /* 左右对齐 */
	}

	.quick-actions-title {
		font-size: 17px;
		font-weight: 600;
		color: #1D1D1F;
		display: block;
		margin-bottom: 0; /* 移除margin */
	}

	.quick-actions-subtitle {
		font-size: 13px;
		color: #8E8E93;
		display: block;
	}
}

.quick-actions-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 12px;
	padding: 16px; /* 减少内边距，防止超出 */
	box-sizing: border-box;
}

.quick-action-btn {
	display: flex;
	flex-direction: row; /* 水平布局 */
	align-items: center;
	justify-content: center;
	padding: 8px 16px; /* 进一步减少内边距 */
	border-radius: 8px;
	transition: all 0.2s ease;
	height: 36px; /* 进一步降低高度，更扁 */
	min-width: 0; /* 允许文字换行 */
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04); /* 更轻柔的阴影，与其他卡片一致 */
	border: none; /* 移除边框 */

	/* 按钮内容容器 */
	.btn-content {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 12px; /* 使用gap控制图标和文字间距 */
	}

	/* uni-icons 图标样式 */
	.uni-icons {
		flex-shrink: 0;
	}

	.btn-text {
		font-size: 14px; /* 适配更低的按钮高度 */
		font-weight: 600;
		color: white;
		text-align: center;
		line-height: 1.2;
	}

	&.primary {
		background: #007AFF; /* 主题蓝色，突出常用功能 */
	}

	&.warning {
		background: #F59E0B; /* 橙黄色，更接近截图中的"责任区检查"按钮 */
	}

	&.success {
		background: #10B981; /* 绿色，更接近截图中的"上传照片"按钮 */
	}

	&.purple {
		background: #8B5CF6; /* 紫色，更接近截图中的"清理历史"按钮 */
	}

	&:active {
		transform: scale(0.98);
		opacity: 0.8;
	}
}

/* 新的概览样式 - 按照设计图 */
.overview-header {
	padding: 24px 24px 16px;
	border-bottom: 1px solid rgba(0, 0, 0, 0.06);

	.header-content {
		display: flex;
		flex-direction: row; /* 水平排列 */
		align-items: baseline; /* 基线对齐 */
		justify-content: space-between; /* 左右对齐 */
	}

	.overview-title {
		font-size: 17px;
		font-weight: 600;
		color: #1D1D1F;
		margin-bottom: 0; /* 移除margin */
		display: block;
	}

	.overview-subtitle {
		font-size: 13px;
		color: #8E8E93;
		display: block;
	}
}

.overview-stats {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 0;
	padding: 12px 16px; /* 减少上下内边距，保持左右间距 */

	.stat-item {
		text-align: center;
		padding: 8px 6px; /* 减少内边距，降低高度间距 */

		.stat-number {
			display: block;
			font-size: 22px; /* 进一步减少到22px，更接近demo */
			font-weight: 700;
			margin-bottom: 2px; /* 减少数字和标签间距 */
			line-height: 1;

			&.blue {
				color: #007AFF;
			}

			&.green {
				color: #34C759;
			}

			&.orange {
				color: #FF9500;
			}

			&.red {
				color: #FF3B30;
			}
		}

		.stat-label {
			font-size: 11px; /* 进一步减少到11px，更精致 */
			color: #8E8E93;
			font-weight: 500;
		}
	}
}

.header-decoration {
	display: flex;
	gap: 4px;
	align-items: center;

	.decoration-dot {
		width: 6px;
		height: 6px;
		border-radius: 50%;
		background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
		animation: pulse 2s ease-in-out infinite;

		&:nth-child(2) {
			animation-delay: 0.3s;
		}

		&:nth-child(3) {
			animation-delay: 0.6s;
		}
	}
}

@keyframes pulse {
	0%, 100% {
		opacity: 0.4;
		transform: scale(1);
	}
	50% {
		opacity: 1;
		transform: scale(1.2);
	}
}

/* 图标容器样式优化 */
.activity-icon, .reminder-icon {
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 最新动态样式 */
.activity-list {
	padding: 0 20px 20px;
}

.activity-item {
	display: flex;
	align-items: flex-start;
	padding: 16px 0;
	border-bottom: 1px solid rgba(0, 0, 0, 0.06);

	&:last-child {
		border-bottom: none;
	}

	.activity-icon {
		width: 40px;
		height: 40px;
		border-radius: 8px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 16px;
		flex-shrink: 0;

		&.success {
			background: rgba(52, 199, 89, 0.15);
		}

		&.warning {
			background: rgba(255, 149, 0, 0.15);
		}

		&.info {
			background: rgba(0, 122, 255, 0.15);
		}
	}

	.activity-content {
		flex: 1;

		.activity-title {
			font-size: 15px;
			color: #1D1D1F;
			line-height: 1.4;
			margin-bottom: 4px;
			display: block;
			font-weight: 600; /* 使用粗体，符合demo */
		}

		.activity-time {
			font-size: 13px;
			color: #8E8E93;
		}
	}
}

/* 加载状态样式 */
.loading-container {
	padding: 40rpx 32rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
}

.loading-spinner {
	width: 40rpx;
	height: 40rpx;
	border: 4rpx solid #E5E7EB;
	border-top: 4rpx solid #007AFF;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #8E8E93;
}

/* 错误状态样式 */
.error-container {
	padding: 40rpx 32rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.error-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
}

.error-text {
	font-size: 24rpx;
	color: #FF3B30;
	text-align: center;
}

.retry-btn {
	padding: 12rpx 24rpx;
	background: #007AFF;
	color: white;
	border: none;
	border-radius: 16rpx;
	font-size: 24rpx;
	
	&:active {
		opacity: 0.8;
	}
}

/* 空状态样式 */
.empty-state {
	padding: 40rpx 32rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12rpx;
}

.empty-text {
	font-size: 24rpx;
	color: #8E8E93;
	text-align: center;
}

/* 智能提醒样式 - 完全按照demo的设计实现 */
.reminder-list {
	padding: 16px;
}

.reminder-item {
	display: flex;
	align-items: flex-start;
	padding: 12px;
	margin-bottom: 12px;
	border-radius: 8px;

	&:last-child {
		margin-bottom: 0;
	}

	.reminder-icon {
		width: 24px;
		height: 24px;
		border-radius: 12px; /* 完全圆形 - demo中是w-6 h-6 */
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 12px;
		flex-shrink: 0;
		/* 移除margin-top，确保完全垂直居中 */

		.icon-text {
			font-size: 12px;
			font-weight: 600;
			line-height: 1; /* 确保文字垂直居中 */
			text-align: center;
		}

		&.info {
			background: rgba(0, 122, 255, 0.2); /* demo中的bg-primary/20 */

			.icon-text {
				color: #007AFF; /* demo中的text-primary */
			}
		}

		&.warning {
			background: rgba(255, 149, 0, 0.2); /* 橙色警告 */

			.icon-text {
				color: #FF9500;
			}
		}
		
		&.danger {
			background: rgba(255, 59, 48, 0.2); /* 红色危险 */

			.icon-text {
				color: #FF3B30;
			}
		}
		
		&.success {
			background: rgba(34, 197, 94, 0.2); /* 绿色背景 */

			.icon-text {
				color: #22C55E; /* 绿色文字 */
			}
		}
	}

	.reminder-content {
		flex: 1;

		.reminder-title {
			font-size: 14px;
			color: #1C1C1E; /* demo中的text-dark */
			font-weight: 600; /* 使用粗体，符合demo */
			margin-bottom: 4px;
			display: block;
		}

		.reminder-desc {
			font-size: 13px;
			color: #636366; /* demo中的text-gray-600 */
			line-height: 1.4;
		}
	}

	/* demo风格的背景色 - 完全按照demo实现 */
	&.info {
		background: rgba(59, 130, 246, 0.05); /* demo中的bg-blue-50 */
	}

	&.warning {
		background: rgba(255, 149, 0, 0.05); /* 橙色背景 */
	}
	
	&.danger {
		background: rgba(255, 59, 48, 0.05); /* 红色背景 */
	}
	
	&.success {
		background: rgba(34, 197, 94, 0.05); /* 绿色背景 */
	}
}
</style>