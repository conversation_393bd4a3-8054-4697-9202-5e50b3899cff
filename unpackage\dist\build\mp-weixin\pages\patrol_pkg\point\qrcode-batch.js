require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/patrol_pkg/point/qrcode-batch"],{"18e5":function(e,t,n){},"4ca1":function(e,t,n){"use strict";n.r(t);var a=n("9060"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},"88a5":function(e,t,n){"use strict";n.r(t);var a=n("f49d"),o=n("4ca1");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);n("db7d");var i=n("828b"),c=Object(i["a"])(o["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);t["default"]=c.exports},9060:function(e,t,n){"use strict";(function(e){var a=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(n("7eb4")),r=a(n("af34")),i=a(n("7ca3")),c=a(n("ee10")),s=a(n("b96b")),u=a(n("95a7"));function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){(0,i.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var f={components:{PEmptyState:function(){n.e("components/p-empty-state/p-empty-state").then(function(){return resolve(n("9b76"))}.bind(null,n)).catch(n.oe)}},data:function(){return{points:[],loading:!1,hasGeneratedCodes:!1,pagination:{page:1,pageSize:10,total:0},loadMoreStatus:"more",isLoadingMore:!1}},onLoad:function(){this.loadPoints(!0)},onReachBottom:function(){this.loadMore()},onPullDownRefresh:function(){this.loadPoints(!0).then((function(){e.stopPullDownRefresh()}))},computed:{allPointsEnabled:function(){return this.points.length>0&&this.points.every((function(e){return e.qrcode_enabled&&e.generated}))}},methods:{goBack:function(){e.navigateBack()},loadMore:function(){var e=this;return(0,c.default)(o.default.mark((function t(){return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("loading"!==e.loadMoreStatus&&"noMore"!==e.loadMoreStatus&&!e.isLoadingMore){t.next=2;break}return t.abrupt("return");case 2:return e.isLoadingMore=!0,e.loadMoreStatus="loading",e.pagination.page++,t.next=7,e.loadPoints();case 7:case"end":return t.stop()}}),t)})))()},loadPoints:function(){var t=arguments,n=this;return(0,c.default)(o.default.mark((function a(){var i,c,u,d,f;return o.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return i=t.length>0&&void 0!==t[0]&&t[0],i&&(n.pagination.page=1,n.points=[],n.loadMoreStatus="more"),a.prev=2,i&&(n.loading=!0,e.showLoading({title:"加载中..."})),a.next=6,s.default.callPointFunction("getPointList",{params:{keyword:"",page:n.pagination.page,pageSize:n.pagination.pageSize,status:1}});case 6:c=a.sent,0===c.code&&c.data&&(u=c.data.list||[],d=u.map((function(e){return l(l({},e),{},{generated:!!e.qrcode_content,qrcodeContent:e.qrcode_content||null})})),n.points=i?d:[].concat((0,r.default)(n.points),(0,r.default)(d)),i&&(f="number"===typeof c.data.total&&c.data.total>0?c.data.total:"string"===typeof c.data.total&&parseInt(c.data.total)>0?parseInt(c.data.total):u.length>=n.pagination.pageSize?u.length+n.pagination.pageSize:u.length,n.pagination.total=f),0===u.length||u.length<n.pagination.pageSize?n.loadMoreStatus="noMore":n.loadMoreStatus="more"),a.next=13;break;case 10:a.prev=10,a.t0=a["catch"](2),e.showToast({title:"加载点位失败",icon:"none"});case 13:return a.prev=13,i&&(n.loading=!1,e.hideLoading()),n.isLoadingMore=!1,a.finish(13);case 17:case"end":return a.stop()}}),a,null,[[2,10,13,17]])})))()},enableQRCode:function(t){var n=this;return(0,c.default)(o.default.mark((function a(){var r,i;return o.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,e.showLoading({title:"启用中..."}),a.next=4,s.default.callPointFunction("updatePoint",{data:{id:t._id,qrcode_enabled:!0,qrcode_version:1}});case 4:if(r=a.sent,0!==r.code){a.next=11;break}i=n.points.findIndex((function(e){return e._id===t._id})),-1!==i&&(n.$set(n.points[i],"qrcode_enabled",!0),n.$set(n.points[i],"qrcode_version",1),n.$nextTick((function(){n.generateQRCode(n.points[i])}))),e.showToast({title:"启用成功",icon:"success"}),a.next=12;break;case 11:throw new Error(r.message||"启用失败");case 12:a.next=17;break;case 14:a.prev=14,a.t0=a["catch"](0),e.showToast({title:a.t0.message||"启用失败",icon:"none"});case 17:return a.prev=17,e.hideLoading(),a.finish(17);case 20:case"end":return a.stop()}}),a,null,[[0,14,17,20]])})))()},disableQRCode:function(t){var n=this;return(0,c.default)(o.default.mark((function a(){return o.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:try{e.showModal({title:"确认关闭",content:"关闭二维码后，该点位的所有二维码数据将被清除，是否继续？",success:function(){var a=(0,c.default)(o.default.mark((function a(r){var i,c;return o.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!r.confirm){a.next=12;break}return e.showLoading({title:"关闭中..."}),a.next=4,s.default.callPointFunction("updatePoint",{data:{id:t._id,qrcode_enabled:!1,qrcode_content:null,qrcode_version:null,qrcode_hash_key:null,qrcode_generated_time:null}});case 4:if(i=a.sent,0!==i.code){a.next=11;break}c=n.points.findIndex((function(e){return e._id===t._id})),-1!==c&&(n.$set(n.points[c],"qrcode_enabled",!1),n.$set(n.points[c],"qrcode_content",null),n.$set(n.points[c],"qrcode_version",null),n.$set(n.points[c],"qrcode_hash_key",null),n.$set(n.points[c],"qrcode_generated_time",null),n.$set(n.points[c],"generated",!1),n.$set(n.points[c],"qrcodeContent",null)),e.showToast({title:"已关闭二维码",icon:"success"}),a.next=12;break;case 11:throw new Error(i.message||"关闭失败");case 12:case"end":return a.stop()}}),a)})));return function(e){return a.apply(this,arguments)}}()})}catch(r){e.showToast({title:r.message||"关闭失败",icon:"none"})}finally{e.hideLoading()}case 1:case"end":return a.stop()}}),a)})))()},generateQRCode:function(t){var n=this;return(0,c.default)(o.default.mark((function a(){var r,i,c,d,f,p,h;return o.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,t.qrcode_enabled){a.next=17;break}return a.next=4,s.default.callPointFunction("updatePoint",{data:{id:t._id,qrcode_enabled:!0,qrcode_version:1}});case 4:if(r=a.sent,0===r.code){a.next=7;break}throw new Error(r.message||"启用二维码失败");case 7:return a.next=9,s.default.callPointFunction("getPointDetail",{point_id:t._id});case 9:if(i=a.sent,0!==i.code||!i.data){a.next=14;break}Object.assign(t,i.data),a.next=15;break;case 14:throw new Error("获取点位详情失败");case 15:c=n.points.findIndex((function(e){return e._id===t._id})),-1!==c&&(n.$set(n.points[c],"qrcode_enabled",!0),n.$set(n.points[c],"qrcode_version",1),n.$set(n.points[c],"qrcode_hash_key",t.qrcode_hash_key));case 17:if(t.qrcode_hash_key){a.next=26;break}return a.next=20,s.default.callPointFunction("getPointDetail",{point_id:t._id});case 20:if(d=a.sent,0!==d.code||!d.data){a.next=25;break}t.qrcode_hash_key=d.data.qrcode_hash_key,a.next=26;break;case 25:throw new Error("获取点位hash_key失败");case 26:return f=u.default.getQRCodeData(l(l({},t),{},{qrcode_content:null}),{includeTimestamp:!1}),a.next=29,s.default.callPointFunction("updatePoint",{data:{id:t._id,qrcode_content:f,qrcode_generated_time:(new Date).toISOString(),qrcode_enabled:!0,qrcode_version:t.qrcode_version||1}});case 29:if(p=a.sent,0===p.code){a.next=32;break}throw new Error(p.message||"生成二维码失败");case 32:h=n.points.findIndex((function(e){return e._id===t._id})),-1!==h&&(n.$set(n.points[h],"qrcodeContent",f),n.$set(n.points[h],"qrcode_content",f),n.$set(n.points[h],"generated",!0),n.$set(n.points[h],"qrcode_enabled",!0),n.$set(n.points[h],"qrcode_version",t.qrcode_version||1),n.$set(n.points[h],"qrcode_generated_time",(new Date).toISOString())),e.showToast({title:"生成成功",icon:"success"}),a.next=41;break;case 37:throw a.prev=37,a.t0=a["catch"](0),e.showToast({title:a.t0.message||"生成失败",icon:"none"}),a.t0;case 41:case"end":return a.stop()}}),a,null,[[0,37]])})))()},onQRCodeComplete:function(e,t){if(e.success){var n=this.points.findIndex((function(e){return e._id===t._id}));-1!==n&&(this.$set(this.points[n],"generated",!0),this.hasGeneratedCodes=!0)}},handleBatchOperation:function(){var e=this;return(0,c.default)(o.default.mark((function t(){return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.allPointsEnabled){t.next=5;break}return t.next=3,e.disableAllQRCodes();case 3:t.next=7;break;case 5:return t.next=7,e.generateAllQRCodes();case 7:case"end":return t.stop()}}),t)})))()},saveQRCode:function(t){var n=this;return(0,c.default)(o.default.mark((function a(){var r,i;return o.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,e.showLoading({title:"保存中..."}),r=n.$refs["qrcode-"+t._id],r){a.next=5;break}throw new Error("二维码组件未找到");case 5:return i=Array.isArray(r)?r[0]:r,a.next=8,i.save({success:function(){e.showToast({title:"保存成功",icon:"success"})},fail:function(t){e.showToast({title:"保存失败",icon:"none"})}});case 8:a.next=13;break;case 10:a.prev=10,a.t0=a["catch"](0),e.showToast({title:"保存失败",icon:"none"});case 13:return a.prev=13,e.hideLoading(),a.finish(13);case 16:case"end":return a.stop()}}),a,null,[[0,10,13,16]])})))()},generateAllQRCodes:function(){var t=this;return(0,c.default)(o.default.mark((function n(){var a,r,i,c,s,u;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(a=t.points.filter((function(e){return!e.qrcode_content||!e.generated})),0!==a.length){n.next=4;break}return e.showToast({title:"没有需要生成的二维码",icon:"none"}),n.abrupt("return");case 4:return n.prev=4,n.next=7,new Promise((function(t,n){e.showModal({title:"确认生成",content:"确定要为".concat(a.length,"个点位启用并生成二维码吗？"),confirmText:"确定",cancelText:"取消",success:t,fail:n})}));case 7:if(r=n.sent,r.confirm){n.next=10;break}return n.abrupt("return");case 10:i=0,c=0,e.showLoading({title:"处理中(0/".concat(a.length,")"),mask:!0}),s=0;case 14:if(!(s<a.length)){n.next=29;break}return u=a[s],n.prev=16,n.next=19,t.generateQRCode(u);case 19:i++,e.showLoading({title:"处理中(".concat(i,"/").concat(a.length,")"),mask:!0}),n.next=26;break;case 23:n.prev=23,n.t0=n["catch"](16),c++;case 26:s++,n.next=14;break;case 29:e.hideLoading(),e.showModal({title:"处理完成",content:"生成完成\n成功：".concat(i,"个\n失败：").concat(c,"个"),showCancel:!1}),n.next=37;break;case 33:n.prev=33,n.t1=n["catch"](4),e.hideLoading(),e.showToast({title:"操作失败",icon:"none"});case 37:case"end":return n.stop()}}),n,null,[[4,33],[16,23]])})))()},disableAllQRCodes:function(){var t=this;return(0,c.default)(o.default.mark((function n(){var a,r,i,c,u,d;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(a=t.points.filter((function(e){return e.qrcode_enabled})),0!==a.length){n.next=4;break}return e.showToast({title:"没有已启用的二维码",icon:"none"}),n.abrupt("return");case 4:return n.next=6,new Promise((function(t){e.showModal({title:"确认关闭",content:"将关闭".concat(a.length,"个点位的二维码功能，关闭后将清除所有二维码数据，是否继续？"),success:t})}));case 6:if(r=n.sent,r.confirm){n.next=9;break}return n.abrupt("return");case 9:n.prev=9,e.showLoading({title:"处理中(0/"+a.length+")",mask:!0}),i=0,c=0,u=o.default.mark((function n(r){var u,d,l;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return u=a[r],n.prev=1,n.next=4,s.default.callPointFunction("updatePoint",{data:{id:u._id,qrcode_enabled:!1,qrcode_content:null,qrcode_version:null,qrcode_hash_key:null,qrcode_generated_time:null}});case 4:if(d=n.sent,0!==d.code){n.next=11;break}l=t.points.findIndex((function(e){return e._id===u._id})),-1!==l&&(t.$set(t.points[l],"qrcode_enabled",!1),t.$set(t.points[l],"qrcode_content",null),t.$set(t.points[l],"qrcode_version",null),t.$set(t.points[l],"qrcode_hash_key",null),t.$set(t.points[l],"qrcode_generated_time",null),t.$set(t.points[l],"generated",!1),t.$set(t.points[l],"qrcodeContent",null)),i++,n.next=12;break;case 11:throw new Error(d.message||"关闭失败");case 12:n.next=17;break;case 14:n.prev=14,n.t0=n["catch"](1),c++;case 17:e.showLoading({title:"处理中(".concat(i,"/").concat(a.length,")"),mask:!0});case 18:case"end":return n.stop()}}),n,null,[[1,14]])})),d=0;case 15:if(!(d<a.length)){n.next=20;break}return n.delegateYield(u(d),"t0",17);case 17:d++,n.next=15;break;case 20:e.hideLoading(),e.showModal({title:"处理完成",content:"成功：".concat(i,"个\n失败：").concat(c,"个\n所有二维码数据已清除"),showCancel:!1}),n.next=27;break;case 24:n.prev=24,n.t1=n["catch"](9),e.showToast({title:"批量关闭失败",icon:"none"});case 27:return n.prev=27,e.hideLoading(),n.finish(27);case 30:case"end":return n.stop()}}),n,null,[[9,24,27,30]])})))()},saveAllQRCodes:function(){var t=this;return(0,c.default)(o.default.mark((function n(){var a,r,i,c,s,u,d,l,f;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(a=t.points.filter((function(e){return e.generated})),0!==a.length){n.next=4;break}return e.showToast({title:"没有已生成的二维码可保存",icon:"none"}),n.abrupt("return");case 4:return n.next=6,new Promise((function(t){e.showModal({title:"确认保存",content:"即将保存 ".concat(a.length," 个二维码图片到系统相册，是否继续？"),success:t})}));case 6:if(r=n.sent,r.confirm){n.next=9;break}return n.abrupt("return");case 9:i=0,c=0,s=a.length,e.showLoading({title:"保存中(0/".concat(s,")"),mask:!0}),u=0;case 14:if(!(u<s)){n.next=31;break}return d=a[u],l=u+1,e.showLoading({title:"保存 ".concat(l,"/").concat(s,": ").concat(d.name||"未命名点位","..."),mask:!0}),n.prev=18,n.delegateYield(o.default.mark((function e(){var n,a;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t.$refs["qrcode-"+d._id],n){e.next=4;break}return c++,e.abrupt("return","continue");case 4:return a=Array.isArray(n)?n[0]:n,e.next=7,new Promise((function(e,t){a.save({success:function(){i++,e()},fail:function(t){c++,e()}})}));case 7:case"end":return e.stop()}}),e)}))(),"t0",20);case 20:if(f=n.t0,"continue"!==f){n.next=23;break}return n.abrupt("continue",28);case 23:n.next=28;break;case 25:n.prev=25,n.t1=n["catch"](18),c++;case 28:u++,n.next=14;break;case 31:e.hideLoading(),e.showModal({title:"保存完成",content:"共处理 ".concat(s," 个二维码。\n成功保存 ").concat(i," 个，失败 ").concat(c," 个。"),showCancel:!1});case 33:case"end":return n.stop()}}),n,null,[[18,25]])})))()}}};t.default=f}).call(this,n("df3c")["default"])},"91c2":function(e,t,n){"use strict";(function(e,t){var a=n("47a9");n("357b"),n("861b");a(n("3240"));var o=a(n("88a5"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},db7d:function(e,t,n){"use strict";var a=n("18e5"),o=n.n(a);o.a},f49d:function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a}));var a={uqrcode:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode")]).then(n.bind(null,"03ba"))},uniIcons:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(n.bind(null,"6ddf"))},pEmptyState:function(){return n.e("components/p-empty-state/p-empty-state").then(n.bind(null,"9b76"))}},o=function(){var e=this,t=e.$createElement,n=(e._self._c,e.__map(e.points,(function(t,n){var a=e.__get_orig(t),o=t.location?t.location.longitude.toFixed(6):null,r=t.location?t.location.latitude.toFixed(6):null;return{$orig:a,g0:o,g1:r}}))),a="noMore"===e.loadMoreStatus&&e.points.length>0,o=!e.loading&&0===e.points.length;e._isMounted||(e.e0=function(t,n){var a=[],o=arguments.length-2;while(o-- >0)a[o]=arguments[o+2];var r=a[a.length-1].currentTarget.dataset,i=r.eventParams||r["event-params"];n=i.point;return e.onQRCodeComplete(t,n)}),e.$mp.data=Object.assign({},{$root:{l0:n,g2:a,g3:o}})},r=[]}},[["91c2","common/runtime","common/vendor","pages/patrol_pkg/common/vendor"]]]);