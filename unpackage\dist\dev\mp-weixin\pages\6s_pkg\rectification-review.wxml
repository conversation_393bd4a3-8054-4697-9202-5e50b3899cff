<view class="page-container data-v-665ecd07"><view class="page-header data-v-665ecd07"><view class="header-content data-v-665ecd07"><view class="header-title data-v-665ecd07">整改复查</view><view class="header-subtitle data-v-665ecd07">{{loading?'加载中...':taskInfo.areaName}}</view></view><block wx:if="{{!loading}}"><view class="{{['status-badge-enhanced','data-v-665ecd07','status-'+taskInfo.status]}}"><uni-icons vue-id="657b54df-1" type="{{taskInfo.status==='pending_review'?'info':taskInfo.status==='approved'?'checkmarkempty':taskInfo.status==='rejected'?'close':taskInfo.status==='completed'?'checkmarkempty':'info'}}" size="16" color="rgba(255, 255, 255, 0.95)" class="data-v-665ecd07" bind:__l="__l"></uni-icons><text class="data-v-665ecd07">{{$root.m0}}</text></view></block></view><block wx:if="{{loading}}"><view class="page-loading data-v-665ecd07"><view class="loading-content data-v-665ecd07"><view class="loading-spinner data-v-665ecd07"></view><text class="loading-text data-v-665ecd07">加载整改详情...</text></view></view></block><block wx:else><block wx:if="{{loadError}}"><view class="page-error data-v-665ecd07"><p-empty-state vue-id="657b54df-2" type="error" title="加载失败" description="网络异常，请检查网络连接" show-button="{{true}}" button-text="重新加载" data-event-opts="{{[['^buttonClick',[['retryLoad']]]]}}" bind:buttonClick="__e" class="data-v-665ecd07" bind:__l="__l"></p-empty-state></view></block><block wx:else><block wx:if="{{dataLoaded}}"><view class="data-v-665ecd07"><view class="card data-v-665ecd07"><view class="card-header data-v-665ecd07"><view class="card-title data-v-665ecd07">原始问题</view><view class="card-subtitle data-v-665ecd07">{{$root.m1}}</view></view><view class="card-body data-v-665ecd07"><block wx:if="{{$root.g0}}"><view class="photo-section data-v-665ecd07"><view class="section-title data-v-665ecd07">问题照片</view><view class="photo-grid data-v-665ecd07"><block wx:for="{{taskInfo.issuePhotos}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['previewIssuePhoto',[index]]]]]}}" class="photo-item data-v-665ecd07" bindtap="__e"><image src="{{photo.url||photo}}" mode="aspectFill" data-event-opts="{{[['error',[['onPhotoError',[index,'issuePhotos']]]]]}}" binderror="__e" class="data-v-665ecd07"></image><block wx:if="{{photo.loadError}}"><view data-event-opts="{{[['tap',[['retryPhotoLoad',[index,'issuePhotos']]]]]}}" class="photo-error data-v-665ecd07" catchtap="__e"><uni-icons vue-id="{{'657b54df-3-'+index}}" type="reload" size="24" color="#8E8E93" class="data-v-665ecd07" bind:__l="__l"></uni-icons><text class="error-text data-v-665ecd07">点击重试</text></view></block></view></block></view></view></block><view class="description-section data-v-665ecd07"><view class="section-title data-v-665ecd07">问题说明</view><view class="description-content data-v-665ecd07">{{''+taskInfo.issueDescription+''}}</view><view class="issue-meta data-v-665ecd07"><text class="meta-label data-v-665ecd07">检查员：</text><text class="meta-value data-v-665ecd07">{{taskInfo.inspector}}</text></view></view></view></view><view class="card data-v-665ecd07"><view class="card-header data-v-665ecd07"><view class="card-title data-v-665ecd07">员工整改记录</view><view class="card-subtitle data-v-665ecd07">{{$root.m2}}</view></view><view class="card-body data-v-665ecd07"><view class="photo-section data-v-665ecd07"><view class="section-title data-v-665ecd07">整改照片</view><block wx:if="{{!dataLoaded}}"><view class="photos-loading-placeholder data-v-665ecd07"><view class="loading-center data-v-665ecd07"><view class="loading-spinner-small data-v-665ecd07"></view><view class="photos-loading-text data-v-665ecd07">加载照片中...</view></view></view></block><block wx:else><block wx:if="{{$root.g1===0}}"><view class="no-photos data-v-665ecd07"><p-empty-state vue-id="657b54df-4" type="no-image" title="暂无照片" description="员工未上传整改照片" class="data-v-665ecd07" bind:__l="__l"></p-empty-state></view></block><block wx:else><view class="photo-grid data-v-665ecd07"><block wx:for="{{taskInfo.rectificationPhotos}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['previewPhoto',[index]]]]]}}" class="photo-item data-v-665ecd07" bindtap="__e"><image src="{{photo.url||photo}}" mode="aspectFill" data-event-opts="{{[['error',[['onPhotoError',[index]]]]]}}" binderror="__e" class="data-v-665ecd07"></image><block wx:if="{{photo.loadError}}"><view data-event-opts="{{[['tap',[['retryPhotoLoad',[index]]]]]}}" class="photo-error data-v-665ecd07" catchtap="__e"><uni-icons vue-id="{{'657b54df-5-'+index}}" type="reload" size="24" color="#8E8E93" class="data-v-665ecd07" bind:__l="__l"></uni-icons><text class="error-text data-v-665ecd07">点击重试</text></view></block></view></block></view></block></block></view><view class="description-section data-v-665ecd07"><view class="section-title data-v-665ecd07">整改说明</view><view class="description-content data-v-665ecd07">{{''+taskInfo.rectificationDescription+''}}</view><view class="issue-meta data-v-665ecd07"><text class="meta-label data-v-665ecd07">负责人：</text><text class="meta-value data-v-665ecd07">{{taskInfo.employeeName}}</text></view></view></view></view><view class="card data-v-665ecd07"><view class="card-header data-v-665ecd07"><view class="card-title data-v-665ecd07">复查结果</view><view class="card-subtitle data-v-665ecd07">请对整改效果进行评价</view></view><view class="card-body data-v-665ecd07"><view class="review-options data-v-665ecd07"><view data-event-opts="{{[['tap',[['selectReviewResult',['approved']]]]]}}" class="{{['review-option','data-v-665ecd07',(reviewForm.result==='approved')?'active':'']}}" bindtap="__e"><view class="option-icon approved data-v-665ecd07"><uni-icons vue-id="657b54df-6" type="checkmarkempty" size="20" color="white" class="data-v-665ecd07" bind:__l="__l"></uni-icons></view><view class="option-content data-v-665ecd07"><view class="option-title data-v-665ecd07">复查通过</view><view class="option-desc data-v-665ecd07">整改效果良好，问题已解决</view></view></view><view data-event-opts="{{[['tap',[['selectReviewResult',['rejected']]]]]}}" class="{{['review-option','data-v-665ecd07',(reviewForm.result==='rejected')?'active':'']}}" bindtap="__e"><view class="option-icon rejected data-v-665ecd07"><uni-icons vue-id="657b54df-7" type="close" size="20" color="white" class="data-v-665ecd07" bind:__l="__l"></uni-icons></view><view class="option-content data-v-665ecd07"><view class="option-title data-v-665ecd07">需要重新整改</view><view class="option-desc data-v-665ecd07">整改不到位，需要继续处理</view></view></view></view><block wx:if="{{reviewForm.result==='approved'}}"><view class="rating-section-enhanced data-v-665ecd07"><view class="section-title data-v-665ecd07">整改后评分</view><view class="rating-container-enhanced data-v-665ecd07"><view class="rating-display-large data-v-665ecd07"><view class="rating-number-container data-v-665ecd07"><text class="rating-number data-v-665ecd07">{{reviewForm.rating||0}}</text><text class="rating-unit data-v-665ecd07">/5</text></view><view class="rating-desc data-v-665ecd07">{{$root.m3}}</view></view><view class="star-rating data-v-665ecd07"><uni-rate vue-id="657b54df-8" value="{{reviewForm.rating}}" allow-half="{{true}}" size="{{24}}" active-color="#FFD700" inactive-color="#E5E5EA" touchable="{{true}}" margin="{{8}}" data-event-opts="{{[['^change',[['onStarRateChange']]]]}}" bind:change="__e" class="data-v-665ecd07" bind:__l="__l"></uni-rate></view><view class="custom-slider-rating data-v-665ecd07"><view data-event-opts="{{[['touchstart',[['onSliderTouchStart',['$event']]]],['touchmove',[['onSliderTouchMove',['$event']]]],['touchend',[['onSliderTouchEnd',['$event']]]],['mousedown',[['onSliderMouseDown',['$event']]]],['mousemove',[['onSliderMouseMove',['$event']]]],['mouseup',[['onSliderMouseUp',['$event']]]],['mouseleave',[['onSliderMouseUp',['$event']]]]]}}" class="custom-slider-container data-v-665ecd07" bindtouchstart="__e" bindtouchmove="__e" bindtouchend="__e" bindmousedown="__e" bindmousemove="__e" bindmouseup="__e" bindmouseleave="__e"><view class="slider-track data-v-665ecd07"><view class="slider-track-active data-v-665ecd07" style="{{'width:'+(reviewForm.rating/5*100+'%')+';'}}"></view></view><view class="slider-thumb data-v-665ecd07" style="{{'left:'+(reviewForm.rating/5*100+'%')+';'}}"></view><view class="slider-marks data-v-665ecd07"><block wx:for="{{6}}" wx:for-item="mark" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['setRatingByMark',[index]]]]]}}" class="{{['slider-mark','data-v-665ecd07',(index<=reviewForm.rating)?'slider-mark-active':'']}}" style="{{'left:'+(index/5*100+'%')+';'}}" bindtap="__e"></view></block></view></view><view class="slider-labels-external data-v-665ecd07"><block wx:for="{{['0','1','2','3','4','5']}}" wx:for-item="label" wx:for-index="labelIndex" wx:key="labelIndex"><text class="{{['slider-label-external','data-v-665ecd07',(labelIndex<=reviewForm.rating)?'slider-label-active':'']}}" style="{{'left:'+(labelIndex/5*100+'%')+';'}}">{{label}}</text></block></view></view><view class="rating-tips data-v-665ecd07"><text class="data-v-665ecd07">请根据整改后的实际效果进行客观评分（支持半星评分）</text></view></view></view></block><view class="review-comment-section data-v-665ecd07"><view class="section-title data-v-665ecd07">复查意见<block wx:if="{{reviewForm.result==='rejected'}}"><text class="required-mark data-v-665ecd07">*</text></block></view><view class="textarea-container-enhanced data-v-665ecd07"><textarea class="comment-textarea-enhanced data-v-665ecd07" placeholder="{{commentPlaceholder}}" maxlength="200" data-event-opts="{{[['input',[['__set_model',['$0','comment','$event',[]],['reviewForm']],['handleCommentInput',['$event']]]]]}}" value="{{reviewForm.comment}}" bindinput="__e"></textarea><view class="char-count-overlay data-v-665ecd07">{{''+commentLength+'/200'}}</view></view></view><view class="review-photo-section data-v-665ecd07"><view class="section-header data-v-665ecd07"><view class="section-title data-v-665ecd07">复查照片<block wx:if="{{reviewForm.result==='rejected'}}"><text class="data-v-665ecd07">（必填）</text></block><block wx:else><text class="data-v-665ecd07">（可选）</text></block><block wx:if="{{reviewForm.result==='rejected'}}"><text class="required-mark data-v-665ecd07">*</text></block></view><view data-event-opts="{{[['tap',[['toggleAutoUpload',['$event']]]]]}}" class="auto-upload-toggle data-v-665ecd07" bindtap="__e"><view class="toggle-label data-v-665ecd07">自动上传</view><view class="{{['toggle-switch','data-v-665ecd07',(autoUpload)?'active':'']}}"><view class="toggle-circle data-v-665ecd07"></view></view></view></view><view class="photo-grid data-v-665ecd07"><block wx:for="{{$root.l0}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view class="photo-item data-v-665ecd07"><image src="{{photo.m4}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewReviewPhoto',[index]]]],['error',[['onPhotoError',[index]]]]]}}" bindtap="__e" binderror="__e" class="data-v-665ecd07"></image><block wx:if="{{photo.$orig.uploading}}"><view class="photo-uploading data-v-665ecd07"><view class="upload-spinner data-v-665ecd07"></view></view></block><block wx:else><block wx:if="{{photo.$orig.uploaded}}"><view class="photo-uploaded data-v-665ecd07"><uni-icons vue-id="{{'657b54df-9-'+index}}" type="checkmarkempty" size="16" color="white" class="data-v-665ecd07" bind:__l="__l"></uni-icons></view></block></block><view data-event-opts="{{[['tap',[['deleteReviewPhoto',[index]]]]]}}" class="photo-delete data-v-665ecd07" catchtap="__e"><uni-icons vue-id="{{'657b54df-10-'+index}}" type="close" size="18" color="white" class="data-v-665ecd07" bind:__l="__l"></uni-icons></view></view></block><block wx:if="{{$root.g2<6}}"><view data-event-opts="{{[['tap',[['addReviewPhoto',['$event']]]]]}}" class="photo-add data-v-665ecd07" bindtap="__e"><uni-icons vue-id="657b54df-11" type="camera" size="28" color="#8E8E93" class="data-v-665ecd07" bind:__l="__l"></uni-icons><text class="data-v-665ecd07">添加照片</text></view></block></view><view class="photo-tip data-v-665ecd07"><block wx:if="{{reviewForm.result==='rejected'}}"><text class="data-v-665ecd07">需要重新整改时必须上传照片作为证据，最多6张</text></block><block wx:else><text class="data-v-665ecd07">最多可上传6张复查照片</text></block></view></view></view></view><view class="button-container data-v-665ecd07"><button class="{{['primary-button','data-v-665ecd07',(isSubmitting)?'loading':'']}}" disabled="{{!canSubmit}}" data-event-opts="{{[['tap',[['submitReview',['$event']]]]]}}" bindtap="__e"><block wx:if="{{isSubmitting}}"><view class="button-loading data-v-665ecd07"><view class="loading-spinner data-v-665ecd07"></view><text class="data-v-665ecd07">提交中...</text></view></block><block wx:else><text class="data-v-665ecd07">提交复查结果</text></block></button></view></view></block></block></block><view class="bottom-safe-area data-v-665ecd07"></view></view>