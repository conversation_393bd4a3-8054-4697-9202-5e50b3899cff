{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/patrol_pkg/point/add.vue?b516", "webpack:///D:/Xwzc/pages/patrol_pkg/point/add.vue?05f2", "webpack:///D:/Xwzc/pages/patrol_pkg/point/add.vue?dfc1", "webpack:///D:/Xwzc/pages/patrol_pkg/point/add.vue?2710", "uni-app:///pages/patrol_pkg/point/add.vue", "webpack:///D:/Xwzc/pages/patrol_pkg/point/add.vue?9a8a", "webpack:///D:/Xwzc/pages/patrol_pkg/point/add.vue?00d3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "formData", "name", "address", "latitude", "longitude", "accuracy", "range", "status", "qrcode_enabled", "qrcode_required", "rules", "required", "errorMessage", "mapScale", "markers", "circles", "locationChangeListener", "currentLocation", "computed", "markerData", "id", "title", "iconPath", "width", "height", "anchor", "x", "y", "circleData", "color", "fillColor", "radius", "strokeWidth", "watch", "onLoad", "onUnload", "methods", "startLocationWatch", "uni", "type", "isHighAccuracy", "success", "fail", "console", "stopLocationWatch", "updateMapOverlays", "mapTap", "getAddressByLocation", "url", "method", "location", "key", "get_poi", "resolve", "reject", "res", "getCurrentLocation", "icon", "complete", "validateForm", "handleCancel", "handleSubmit", "submitData", "PatrolApi", "pages", "prevPage", "duration", "setTimeout", "content", "showCancel", "getAccuracyColor"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACc;;;AAGhE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,8RAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtFA;AAAA;AAAA;AAAA;AAA6lB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACqKjnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAT;UACAS,QACA;YACAC;YACAC;UACA;QAEA;QACAN;UACAI,QACA;YACAC;YACAC;UACA;QAEA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAd;QACAC;QACAC;MACA;IACA;EACA;EACAa;IACAC;MACA;QACAC;QACAjB;QACAC;QACAiB;QACAC;QACAC;QACAC;QACAC;UACAC;UACAC;QACA;MACA;IACA;IACAC;MACA;QACAzB;QACAC;QACAyB;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;MAEA;MAEAC;QACAC;QACAC;QACAC;UACAH;YACA;cACAnC;cACAC;cACAC;YACA;YACA;UACA;QACA;QACAqC;UACAC;QACA;MACA;IACA;IAEAC;MAAA;MACA;MAEAN;QACAG;UACAH;UACA;QACA;MACA;IACA;IAEAO;MACA;MACA;IACA;IAEAC;MACA;QAAA3C;QAAAC;MAEA;MACA;MAEA;IACA;IAEA2C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAT;oBACAU;oBACAC;oBACAlD;sBACAmD;sBACAC;sBACAC;oBACA;oBACAX;sBACAY;oBACA;oBACAX;sBACAY;oBACA;kBACA;gBACA;cAAA;gBAhBAC;gBAkBA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAZ;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAa;MAAA;MACAlB;QACAjB;MACA;MAEAiB;QACAC;QACAC;QACAC;UACA;UACA;UACA;UAEA;UAEA;QACA;QACAC;UACAC;UACAL;YACAjB;YACAoC;UACA;QACA;QACAC;UACApB;QACA;MACA;IACA;IAEAqB;MAAA;MACA;QACA;UACA;YACAN;UACA;YACAC;UACA;QACA;UACAA;QACA;MACA;IACA;IAEAM;MACAtB;IACA;IAEAuB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAEAvB;kBACAjB;gBACA;gBAEAyC;kBACA7D;kBACAiD;oBACA/C;oBACAC;oBACAF;oBACAG;kBACA;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OAEAsD;kBAAAhE;gBAAA;cAAA;gBAAAwD;gBAEAjB;gBAEA;kBACA0B;kBACAC;kBACA;oBACAA;kBACA;kBAEA3B;oBACAjB;oBACAoC;oBACAS;oBACAR;sBACAS;wBACA7B;sBACA;oBACA;kBACA;gBACA;kBACAA;oBACAjB;oBACA+C;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA1B;gBACA;kBACAL;oBACAjB;oBACAoC;kBACA;gBACA;kBACAnB;oBACAjB;oBACAoC;kBACA;gBACA;cAAA;gBAAA;gBAEAnB;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAgC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5cA;AAAA;AAAA;AAAA;AAAwoC,CAAgB,8mCAAG,EAAC,C;;;;;;;;;;;ACA5pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/patrol_pkg/point/add.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/patrol_pkg/point/add.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./add.vue?vue&type=template&id=59269865&\"\nvar renderjs\nimport script from \"./add.vue?vue&type=script&lang=js&\"\nexport * from \"./add.vue?vue&type=script&lang=js&\"\nimport style0 from \"./add.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/patrol_pkg/point/add.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add.vue?vue&type=template&id=59269865&\"", "var components\ntry {\n  components = {\n    uniForms: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms/uni-forms\" */ \"@/uni_modules/uni-forms/components/uni-forms/uni-forms.vue\"\n      )\n    },\n    uniFormsItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms-item/uni-forms-item\" */ \"@/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getAccuracyColor()\n  var g0 = _vm.formData.accuracy.toFixed(1)\n  var g1 = _vm.formData.longitude.toFixed(6)\n  var g2 = _vm.formData.latitude.toFixed(6)\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.formData.range = Math.max(5, _vm.formData.range - 1)\n    }\n    _vm.e1 = function ($event) {\n      _vm.formData.range = Math.min(50, _vm.formData.range + 1)\n    }\n    _vm.e2 = function (e) {\n      return (_vm.formData.status = e.detail.value ? 1 : 0)\n    }\n    _vm.e3 = function (e) {\n      return (_vm.formData.qrcode_enabled = e.detail.value)\n    }\n    _vm.e4 = function (e) {\n      return (_vm.formData.qrcode_required = e.detail.value)\n    }\n    _vm.e5 = function ($event) {\n      _vm.mapScale = Math.min(_vm.mapScale + 2, 20)\n    }\n    _vm.e6 = function ($event) {\n      _vm.mapScale = Math.max(_vm.mapScale - 2, 5)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"point-form-container\">\n\t\t<uni-forms ref=\"form\" :modelValue=\"formData\" :rules=\"rules\">\n\t\t\t<!-- 基本信息区域 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"section-title\">基本信息</view>\n\t\t\t\t<view class=\"form-content\">\n\t\t\t\t\t<uni-forms-item name=\"name\" required>\n\t\t\t\t\t\t<template v-slot:label>\n\t\t\t\t\t\t\t<view class=\"form-label\">\n\t\t\t\t\t\t\t\t<text class=\"required-mark\">*</text>\n\t\t\t\t\t\t\t\t<text>点位名称</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<uni-easyinput class=\"form-input\" v-model=\"formData.name\" placeholder=\"请输入点位名称\" />\n\t\t\t\t\t</uni-forms-item>\n\t\t\t\t\t\n\t\t\t\t\t<uni-forms-item name=\"address\">\n\t\t\t\t\t\t<template v-slot:label>\n\t\t\t\t\t\t\t<view class=\"form-label\">\n\t\t\t\t\t\t\t\t<text>点位地址</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<uni-easyinput class=\"form-input\" v-model=\"formData.address\" placeholder=\"请输入点位地址\" />\n\t\t\t\t\t</uni-forms-item>\n\t\t\t\t\t\n\t\t\t\t\t<uni-forms-item name=\"range\" required>\n\t\t\t\t\t\t<template v-slot:label>\n\t\t\t\t\t\t\t<view class=\"form-label\">\n\t\t\t\t\t\t\t\t<text class=\"required-mark\">*</text>\n\t\t\t\t\t\t\t\t<text>点位范围</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<view class=\"range-container\">\n\t\t\t\t\t\t\t<view class=\"minus-btn\" @click=\"formData.range = Math.max(5, formData.range - 1)\">-</view>\n\t\t\t\t\t\t\t<input type=\"number\" class=\"number-input\" v-model=\"formData.range\" />\n\t\t\t\t\t\t\t<view class=\"plus-btn\" @click=\"formData.range = Math.min(50, formData.range + 1)\">+</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"range-unit\">米</text>\n\t\t\t\t\t</uni-forms-item>\n\t\t\t\t\t\n\t\t\t\t\t<uni-forms-item>\n\t\t\t\t\t\t<template v-slot:label>\n\t\t\t\t\t\t\t<view class=\"form-label\">\n\t\t\t\t\t\t\t\t<text>点位状态</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<view class=\"switch-container\">\n\t\t\t\t\t\t\t<switch :checked=\"formData.status === 1\" @change=\"(e) => formData.status = e.detail.value ? 1 : 0\" color=\"#1677FF\" scale=\"0.8\" style=\"transform: scale(0.8);\" />\n\t\t\t\t\t\t\t<text class=\"switch-text\">{{ formData.status === 1 ? '启用' : '停用' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</uni-forms-item>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 二维码设置区域 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"section-title\">二维码设置</view>\n\t\t\t\t<view class=\"form-content\">\n\t\t\t\t\t<uni-forms-item>\n\t\t\t\t\t\t<template v-slot:label>\n\t\t\t\t\t\t\t<view class=\"form-label\">\n\t\t\t\t\t\t\t\t<text>二维码打卡</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<view class=\"switch-container\">\n\t\t\t\t\t\t\t<switch :checked=\"formData.qrcode_enabled\" @change=\"(e) => formData.qrcode_enabled = e.detail.value\" color=\"#1677FF\" scale=\"0.8\" style=\"transform: scale(0.8);\" />\n\t\t\t\t\t\t\t<text class=\"switch-text\">{{ formData.qrcode_enabled ? '已开启' : '未开启' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</uni-forms-item>\n\t\t\t\t\t\n\t\t\t\t\t<uni-forms-item v-if=\"formData.qrcode_enabled\">\n\t\t\t\t\t\t<template v-slot:label>\n\t\t\t\t\t\t\t<view class=\"form-label\">\n\t\t\t\t\t\t\t\t<text>不限距离</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<view class=\"switch-container\">\n\t\t\t\t\t\t\t<switch :checked=\"formData.qrcode_required\" @change=\"(e) => formData.qrcode_required = e.detail.value\" color=\"#1677FF\" scale=\"0.8\" style=\"transform: scale(0.8);\" />\n\t\t\t\t\t\t\t<text class=\"switch-text\">{{ formData.qrcode_required ? '已开启' : '未开启' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</uni-forms-item>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"qrcode-tips\" v-if=\"formData.qrcode_enabled\">\n\t\t\t\t\t\t<view class=\"tips-version\" v-if=\"formData.qrcode_version > 0\">\n\t\t\t\t\t\t\t当前版本: {{ formData.qrcode_version }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"tips-content\">\n\t\t\t\t\t\t\t<view class=\"tip-item\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"info\" size=\"14\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t<text>开启二维码打卡后，将使用扫码方式进行打卡</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"tip-item\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"info\" size=\"14\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t<text>开启不限距离后，可在任意位置进行扫码打卡</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 地图选点区域 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"section-title\">位置选择</view>\n\t\t\t\t<view class=\"map-wrapper\">\n\t\t\t\t\t<view class=\"map-tips\">\n\t\t\t\t\t\t<uni-icons type=\"info\" size=\"14\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t<text class=\"tips-text\">点击地图选择位置，或点击右上角定位按钮</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"map-container\">\n\t\t\t\t\t\t<map \n\t\t\t\t\t\t\tclass=\"location-map\" \n\t\t\t\t\t\t\t:latitude=\"formData.latitude\" \n\t\t\t\t\t\t\t:longitude=\"formData.longitude\"\n\t\t\t\t\t\t\t:markers=\"markers\"\n\t\t\t\t\t\t\t:circles=\"circles\"\n\t\t\t\t\t\t\t:scale=\"mapScale\"\n\t\t\t\t\t\t\tshow-location\n\t\t\t\t\t\t\t@tap=\"mapTap\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<view class=\"map-tools\">\n\t\t\t\t\t\t\t\t<view class=\"tool-item\" @click=\"getCurrentLocation\">\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"location\" size=\"20\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t\t<text class=\"text-label\">定位</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"tool-item\" @click=\"mapScale = Math.min(mapScale + 2, 20)\">\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"plus\" size=\"20\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t\t<text class=\"text-label\">放大</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"tool-item\" @click=\"mapScale = Math.max(mapScale - 2, 5)\">\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"minus\" size=\"20\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t\t<text class=\"text-label\">缩小</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<view class=\"location-accuracy\">\n\t\t\t\t\t\t\t\t<view class=\"status-dot\" :style=\"{ background: getAccuracyColor() }\"></view>\n\t\t\t\t\t\t\t\t<text class=\"accuracy-text\">GPS精度: {{ formData.accuracy.toFixed(1) }}米</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</map>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"location-coordinates\">\n\t\t\t\t\t\t<view class=\"coordinate-item\">\n\t\t\t\t\t\t\t<text class=\"coordinate-label\">经度</text>\n\t\t\t\t\t\t\t<text class=\"coordinate-value\">{{ formData.longitude.toFixed(6) }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"coordinate-divider\"></view>\n\t\t\t\t\t\t<view class=\"coordinate-item\">\n\t\t\t\t\t\t\t<text class=\"coordinate-label\">纬度</text>\n\t\t\t\t\t\t\t<text class=\"coordinate-value\">{{ formData.latitude.toFixed(6) }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-forms>\n\t\t\n\t\t<!-- 底部按钮区域 -->\n\t\t<view class=\"form-actions\">\n\t\t\t<view class=\"cancel-btn\" @click=\"handleCancel\">取消</view>\n\t\t\t<view class=\"submit-btn\" @click=\"handleSubmit\">保存</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport PatrolApi from '@/utils/patrol-api.js';\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tformData: {\n\t\t\t\tname: '',\n\t\t\t\taddress: '',\n\t\t\t\tlatitude: 39.908823,\n\t\t\t\tlongitude: 116.397470,\n\t\t\t\taccuracy: 0,\n\t\t\t\trange: 10,\n\t\t\t\tstatus: 1,\n\t\t\t\tqrcode_enabled: false,\n\t\t\t\tqrcode_required: false\n\t\t\t},\n\t\t\trules: {\n\t\t\t\tname: {\n\t\t\t\t\trules: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\t\terrorMessage: '请输入点位名称'\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t},\n\t\t\t\trange: {\n\t\t\t\t\trules: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\t\terrorMessage: '请设置点位范围'\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t}\n\t\t\t},\n\t\t\tmapScale: 16,\n\t\t\tmarkers: [],\n\t\t\tcircles: [],\n\t\t\tlocationChangeListener: null,\n\t\t\tcurrentLocation: {\n\t\t\t\tlatitude: 0,\n\t\t\t\tlongitude: 0,\n\t\t\t\taccuracy: 0\n\t\t\t}\n\t\t}\n\t},\n\tcomputed: {\n\t\tmarkerData() {\n\t\t\treturn {\n\t\t\t\tid: 1,\n\t\t\t\tlatitude: this.formData.latitude,\n\t\t\t\tlongitude: this.formData.longitude,\n\t\t\t\ttitle: this.formData.name || '新点位',\n\t\t\t\ticonPath: '/static/map/marker.png',\n\t\t\t\twidth: 32,\n\t\t\t\theight: 32,\n\t\t\t\tanchor: {\n\t\t\t\t\tx: 0.5,\n\t\t\t\t\ty: 1\n\t\t\t\t}\n\t\t\t};\n\t\t},\n\t\tcircleData() {\n\t\t\treturn {\n\t\t\t\tlatitude: this.formData.latitude,\n\t\t\t\tlongitude: this.formData.longitude,\n\t\t\t\tcolor: '#1E5A8D11',\n\t\t\t\tfillColor: '#4CBBCE33',\n\t\t\t\tradius: this.formData.range,\n\t\t\t\tstrokeWidth: 2\n\t\t\t};\n\t\t}\n\t},\n\twatch: {\n\t\t'formData.latitude'() {\n\t\t\tthis.updateMapOverlays();\n\t\t},\n\t\t'formData.longitude'() {\n\t\t\tthis.updateMapOverlays();\n\t\t},\n\t\t'formData.range'() {\n\t\t\tthis.updateMapOverlays();\n\t\t}\n\t},\n\tonLoad() {\n\t\tthis.getCurrentLocation();\n\t\tthis.startLocationWatch();\n\t},\n\tonUnload() {\n\t\tthis.stopLocationWatch();\n\t},\n\tmethods: {\n\t\tstartLocationWatch() {\n\t\t\tif (this.locationChangeListener) return;\n\t\t\t\n\t\t\tthis.locationChangeListener = true;\n\t\t\t\n\t\t\tuni.startLocationUpdate({\n\t\t\t\ttype: 'gcj02',\n\t\t\t\tisHighAccuracy: true,\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tuni.onLocationChange(res => {\n\t\t\t\t\t\tthis.currentLocation = {\n\t\t\t\t\t\t\tlatitude: res.latitude,\n\t\t\t\t\t\t\tlongitude: res.longitude,\n\t\t\t\t\t\t\taccuracy: res.accuracy || 0\n\t\t\t\t\t\t};\n\t\t\t\t\t\tthis.formData.accuracy = res.accuracy || 0;\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('开始监听位置变化失败', err);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\tstopLocationWatch() {\n\t\t\tif (!this.locationChangeListener) return;\n\t\t\t\n\t\t\tuni.stopLocationUpdate({\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tuni.offLocationChange();\n\t\t\t\t\tthis.locationChangeListener = null;\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\tupdateMapOverlays() {\n\t\t\tthis.markers = [this.markerData];\n\t\t\tthis.circles = [this.circleData];\n\t\t},\n\t\t\n\t\tmapTap(e) {\n\t\t\tconst { latitude, longitude } = e.detail;\n\t\t\t\n\t\t\tthis.formData.latitude = latitude;\n\t\t\tthis.formData.longitude = longitude;\n\t\t\t\n\t\t\tthis.getAddressByLocation(latitude, longitude);\n\t\t},\n\t\t\n\t\tasync getAddressByLocation(latitude, longitude) {\n\t\t\ttry {\n\t\t\t\tconst res = await new Promise((resolve, reject) => {\n\t\t\t\t\tuni.request({\n\t\t\t\t\t\turl: 'https://apis.map.qq.com/ws/geocoder/v1/',\n\t\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tlocation: `${latitude},${longitude}`,\n\t\t\t\t\t\t\tkey: '5MPBZ-FCW63-3C43B-R7G72-UOSAO-ZWBTJ',\n\t\t\t\t\t\t\tget_poi: 0\n\t\t\t\t\t\t},\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tresolve(res);\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\treject(err);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.statusCode === 200 && res.data && res.data.status === 0) {\n\t\t\t\t\tthis.formData.address = res.data.result.address;\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('获取地址信息错误', e);\n\t\t\t}\n\t\t},\n\t\t\n\t\tgetCurrentLocation() {\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '定位中...'\n\t\t\t});\n\t\t\t\n\t\t\tuni.getLocation({\n\t\t\t\ttype: 'gcj02',\n\t\t\t\tisHighAccuracy: true,\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tthis.formData.latitude = res.latitude;\n\t\t\t\t\tthis.formData.longitude = res.longitude;\n\t\t\t\t\tthis.formData.accuracy = res.accuracy || 0;\n\t\t\t\t\t\n\t\t\t\t\tthis.mapScale = 16;\n\t\t\t\t\t\n\t\t\t\t\tthis.getAddressByLocation(res.latitude, res.longitude);\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('获取位置失败', err);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取位置失败，请检查定位权限',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\tcomplete: () => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\tvalidateForm() {\n\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\tthis.$refs.form.validate().then(res => {\n\t\t\t\t\tif (res) {\n\t\t\t\t\t\tresolve(true);\n\t\t\t\t\t} else {\n\t\t\t\t\t\treject('表单验证不通过');\n\t\t\t\t\t}\n\t\t\t\t}).catch(err => {\n\t\t\t\t\treject(err);\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\t\n\t\thandleCancel() {\n\t\t\tuni.navigateBack();\n\t\t},\n\t\t\n\t\tasync handleSubmit() {\n\t\t\ttry {\n\t\t\t\tawait this.validateForm();\n\t\t\t\t\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '保存中...'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tconst submitData = {\n\t\t\t\t\tname: this.formData.name,\n\t\t\t\t\tlocation: {\n\t\t\t\t\t\tlatitude: this.formData.latitude,\n\t\t\t\t\t\tlongitude: this.formData.longitude,\n\t\t\t\t\t\taddress: this.formData.address,\n\t\t\t\t\t\taccuracy: this.formData.accuracy\n\t\t\t\t\t},\n\t\t\t\t\trange: this.formData.range,\n\t\t\t\t\tstatus: this.formData.status,\n\t\t\t\t\tqrcode_enabled: this.formData.qrcode_enabled,\n\t\t\t\t\tqrcode_required: this.formData.qrcode_required\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tconst res = await PatrolApi.callPointFunction('addPoint', { data: submitData });\n\t\t\t\t\n\t\t\t\tuni.hideLoading();\n\t\t\t\t\n\t\t\t\tif (res.code === 0) {\n\t\t\t\t\tconst pages = getCurrentPages();\n\t\t\t\t\tconst prevPage = pages[pages.length - 2];\n\t\t\t\t\tif (prevPage && prevPage.$vm) {\n\t\t\t\t\t\tprevPage.$vm.needRefresh = true;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '添加成功',\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 2000,\n\t\t\t\t\t\tcomplete: () => {\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t}, 500);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '添加失败',\n\t\t\t\t\t\tcontent: res.message || '保存点位信息失败',\n\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('保存点位出错', e);\n\t\t\t\tif (typeof e === 'string') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: e,\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '保存出错',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\t\t\n\t\tgetAccuracyColor() {\n\t\t\tconst accuracy = this.formData.accuracy;\n\t\t\tif (!accuracy) return '#999999';\n\t\t\tif (accuracy <= 4) return '#34C759';\n\t\t\tif (accuracy <= 8) return '#00C58E';\n\t\t\tif (accuracy <= 12) return '#FFD60A';\n\t\t\tif (accuracy <= 16) return '#FF9500';\n\t\t\tif (accuracy <= 20) return '#FF6B2C';\n\t\t\treturn '#FF3B30';\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\">\n$primary-color: #1677FF;\n$primary-light: #E7F1FF;\n$primary-dark: #0E5FD8;\n\n$success-color: #07C160;\n$warning-color: #FFA300;\n$danger-color: #FF3B30;\n$info-color: #8F959E;\n\n$text-primary: #2C3E50;\n$text-secondary: #666666;\n$text-tertiary: #999999;\n$border-color: #EAEAEA;\n$background: #F5F5F5;\n\n$radius-sm: 6rpx;\n$radius-md: 12rpx;\n$radius-lg: 16rpx;\n$radius-xl: 24rpx;\n$radius-full: 999rpx;\n\n$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n$shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\n$shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);\n\n.point-form-container {\n\tdisplay: flex;\n\tflex-direction: column;\n\tmin-height: 100vh;\n\tbackground-color: $background;\n\tpadding-bottom: 30rpx;\n}\n\n.form-section {\n\tbackground-color: #FFFFFF;\n\tmargin: 20rpx;\n\tborder-radius: $radius-lg;\n\tpadding: 0;\n\tbox-shadow: $shadow-sm;\n\toverflow: hidden;\n}\n\n.section-title {\n\tfont-size: 30rpx;\n\tfont-weight: 600;\n\tcolor: $text-primary;\n\tpadding: 24rpx 30rpx;\n\tborder-bottom: 1px solid $border-color;\n\tposition: relative;\n\tpadding-left: 50rpx;\n\tbackground-color: #FFFFFF;\n\t\n\t&:before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 30rpx;\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\twidth: 6rpx;\n\t\theight: 24rpx;\n\t\tbackground-color: $primary-color;\n\t\tborder-radius: $radius-sm;\n\t}\n}\n\n.form-content {\n\tpadding: 10rpx 30rpx;\n}\n\n.form-label {\n\tdisplay: flex;\n\talign-items: center;\n\tfont-size: 28rpx;\n\tcolor: $text-secondary;\n\tmargin-bottom: 0;\n\twidth: 160rpx;\n\tflex-shrink: 0;\n}\n\n.required-mark {\n\tcolor: $danger-color;\n\tmargin-right: 4rpx;\n}\n\n.form-input {\n\tflex: 1;\n}\n\n.range-container {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: flex-end;\n}\n\n.number-box-wrapper {\n\tdisplay: flex;\n\talign-items: center;\n\theight: 56rpx;\n\tborder-radius: $radius-md;\n\tbackground-color: $background;\n\toverflow: hidden;\n}\n\n.minus-btn, .plus-btn {\n\twidth: 56rpx;\n\theight: 56rpx;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tbackground-color: $primary-light;\n\tcolor: $primary-color;\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\t\n\t&:active {\n\t\topacity: 0.8;\n\t}\n}\n\n.number-input {\n\twidth: 80rpx;\n\theight: 56rpx;\n\tbackground-color: #FFFFFF;\n\ttext-align: center;\n\tfont-size: 28rpx;\n\tcolor: $text-primary;\n\tmargin: 0;\n}\n\n.range-unit {\n\tmargin-left: 20rpx;\n\tfont-size: 28rpx;\n\tcolor: $text-secondary;\n}\n\n.switch-container {\n\tdisplay: flex;\n\talign-items: center;\n\t\n\t.switch-text {\n\t\tmargin-left: 5rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: $text-secondary;\n\t}\n}\n\n.map-wrapper {\n\tpadding: 0 0 16rpx;\n}\n\n.map-tips {\n\tpadding: 16rpx 24rpx;\n\tbackground-color: $primary-light;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\t\n\t.tips-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: $text-secondary;\n\t\tflex: 1;\n\t}\n}\n\n.map-container {\n\tposition: relative;\n\twidth: 100%;\n\theight: 500rpx;\n\toverflow: hidden;\n}\n\n.location-map {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.map-tools {\n\tposition: absolute;\n\tright: 20rpx;\n\ttop: 20rpx;\n\tbackground-color: rgba(255, 255, 255, 0.95);\n\tborder-radius: $radius-lg;\n\tbox-shadow: $shadow-md;\n\toverflow: hidden;\n\tbackdrop-filter: blur(10px);\n}\n\n.tool-item {\n\tpadding: 16rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 4rpx;\n\tborder-bottom: 1px solid rgba(0, 0, 0, 0.05);\n\t\n\t&:last-child {\n\t\tborder-bottom: none;\n\t}\n\t\n\t.text-label {\n\t\tfont-size: 20rpx;\n\t\tcolor: $text-secondary;\n\t}\n\t\n\t&:active {\n\t\tbackground-color: $primary-light;\n\t}\n}\n\n.form-actions {\n\tposition: relative;\n\tbackground-color: #FFFFFF;\n\tbox-shadow: $shadow-md;\n\tpadding: 20rpx 30rpx;\n\tmargin: 20rpx;\n\tborder-radius: $radius-lg;\n\tz-index: 10;\n\tdisplay: flex;\n\tgap: 20rpx;\n}\n\n.cancel-btn, .submit-btn {\n\tflex: 1;\n\theight: 80rpx;\n\tborder-radius: $radius-full;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\ttransition: all 0.3s ease;\n\t\n\t&:active {\n\t\ttransform: scale(0.95);\n\t\topacity: 0.9;\n\t}\n}\n\n.cancel-btn {\n\tbackground-color: $background;\n\tcolor: $text-secondary;\n\tborder: 1rpx solid $border-color;\n}\n\n.submit-btn {\n\tbackground-color: $primary-color;\n\tcolor: #FFFFFF;\n}\n\n::v-deep .uni-forms-item {\n\tmargin-bottom: 20rpx;\n\t\n\t&:last-child {\n\t\tmargin-bottom: 0;\n\t}\n}\n\n::v-deep .uni-forms-item__inner {\n\tpadding: 0;\n}\n\n::v-deep .uni-forms-item__content {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n}\n\n::v-deep .uni-easyinput__content {\n\tbackground-color: $background;\n\tborder-radius: $radius-md;\n\theight: 80rpx;\n\tpadding: 0 16rpx;\n\tborder: none;\n\tmargin-top: 0;\n\twidth: 480rpx;\n}\n\n::v-deep .uni-easyinput__placeholder-class {\n\tfont-size: 28rpx;\n}\n\n::v-deep .uni-forms-item__error {\n\tcolor: $danger-color;\n\tpadding-top: 8rpx;\n}\n\n::v-deep .uni-numbox {\n\theight: 60rpx;\n\twidth: 200rpx;\n\tbackground-color: $background;\n\tborder-radius: $radius-md;\n\tborder: none;\n\tmargin-top: 0;\n\t\n\t.uni-numbox__value {\n\t\tbackground-color: #FFFFFF;\n\t\tcolor: $text-primary;\n\t\tfont-size: 28rpx;\n\t\theight: 60rpx;\n\t\tline-height: 60rpx;\n\t}\n\t\n\t.uni-numbox__minus,\n\t.uni-numbox__plus {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tline-height: 60rpx;\n\t\tbackground-color: $primary-light;\n\t\tcolor: $primary-color;\n\t\t\n\t\t&:active {\n\t\t\topacity: 0.8;\n\t\t}\n\t}\n}\n\n.compact-numbox {\n\t::v-deep .uni-numbox {\n\t\ttransform: scale(0.9);\n\t\ttransform-origin: left center;\n\t}\n}\n\n.location-coordinates {\n\tpadding: 20rpx 30rpx 0 30rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tgap: 40rpx;\n\tborder-top: 1px solid $border-color;\n\tbackground-color: #FFFFFF;\n}\n\n.coordinate-item {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 12rpx;\n}\n\n.coordinate-label {\n\tfont-size: 28rpx;\n\tcolor: $text-secondary;\n}\n\n.coordinate-value {\n\tfont-size: 28rpx;\n\tcolor: $text-primary;\n\tfont-family: Monaco, Consolas, monospace;\n\tfont-weight: 500;\n}\n\n.coordinate-divider {\n\twidth: 1px;\n\theight: 24rpx;\n\tbackground-color: $border-color;\n}\n\n.location-accuracy {\n\tposition: absolute;\n\tleft: 20rpx;\n\tbottom: 20rpx;\n\tbackground: rgba(255, 255, 255, 0.9);\n\tpadding: 8rpx 16rpx;\n\tborder-radius: $radius-md;\n\tbox-shadow: $shadow-sm;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\tbackdrop-filter: blur(10px);\n\t\n\t.status-dot {\n\t\twidth: 8rpx;\n\t\theight: 8rpx;\n\t\tborder-radius: 50%;\n\t}\n\t\n\t.accuracy-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: $text-secondary;\n\t\tfont-weight: 500;\n\t}\n}\n\n.qrcode-tips {\n\tmargin-top: 16rpx;\n\tborder-radius: $radius-md;\n\toverflow: hidden;\n\t\n\t.tips-version {\n\t\tbackground-color: #E7F1FF;\n\t\tpadding: 12rpx 16rpx;\n\t\tfont-size: 24rpx;\n\t\tcolor: #1677FF;\n\t\tfont-weight: 500;\n\t}\n\t\n\t.tips-content {\n\t\tbackground-color: #F5F7FA;\n\t\tpadding: 16rpx;\n\t}\n\t\n\t.tip-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 8rpx 0;\n\t\t\n\t\t.uni-icons {\n\t\t\tflex-shrink: 0;\n\t\t\tmargin-right: 8rpx;\n\t\t}\n\t\t\n\t\ttext {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #666666;\n\t\t\tline-height: 1.4;\n\t\t}\n\t\t\n\t\t&:not(:last-child) {\n\t\t\tmargin-bottom: 8rpx;\n\t\t}\n\t}\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775847608\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}