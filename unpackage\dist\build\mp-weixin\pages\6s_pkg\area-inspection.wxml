<view class="page-container data-v-01698be5"><view class="card data-v-01698be5"><view class="card-header data-v-01698be5"><view class="header-content data-v-01698be5"><view class="card-title data-v-01698be5">抽查统计</view><view class="filters-row data-v-01698be5"><view data-event-opts="{{[['tap',[['showTimeSelector',['$event']]]]]}}" class="time-selector data-v-01698be5" bindtap="__e"><text class="time-text data-v-01698be5">{{$root.m0}}</text><uni-icons vue-id="62a0b4c4-1" type="down" size="12" color="#007AFF" class="data-v-01698be5" bind:__l="__l"></uni-icons></view><block wx:if="{{selectedStatus!=='all'}}"><view data-event-opts="{{[['tap',[['selectStatus',['all']]]]]}}" class="status-filter-indicator data-v-01698be5" bindtap="__e"><text class="filter-text data-v-01698be5">{{$root.m1}}</text><uni-icons vue-id="62a0b4c4-2" type="close" size="12" color="#8E8E93" class="data-v-01698be5" bind:__l="__l"></uni-icons></view></block></view></view></view><block wx:if="{{loading}}"><view class="stats-loading data-v-01698be5"><view class="loading-spinner data-v-01698be5"></view><view class="loading-text data-v-01698be5">加载统计数据中...</view></view></block><block wx:else><view class="stats-grid stats-grid-six data-v-01698be5"><view data-event-opts="{{[['tap',[['selectStatus',['pending_review']]]]]}}" class="{{['stats-item','data-v-01698be5',(selectedStatus==='pending_review')?'active':'']}}" bindtap="__e"><view class="stats-number review data-v-01698be5">{{$root.m2.pending_review}}</view><view class="stats-label data-v-01698be5">待复查</view></view><view data-event-opts="{{[['tap',[['selectStatus',['pending']]]]]}}" class="{{['stats-item','data-v-01698be5',(selectedStatus==='pending')?'active':'']}}" bindtap="__e"><view class="stats-number warning data-v-01698be5">{{$root.m3.pending}}</view><view class="stats-label data-v-01698be5">待检查</view></view><view data-event-opts="{{[['tap',[['selectStatus',['pending_rectification']]]]]}}" class="{{['stats-item','data-v-01698be5',(selectedStatus==='pending_rectification')?'active':'']}}" bindtap="__e"><view class="stats-number danger data-v-01698be5">{{$root.m4.pending_rectification}}</view><view class="stats-label data-v-01698be5">待整改</view></view><view data-event-opts="{{[['tap',[['selectStatus',['missed']]]]]}}" class="{{['stats-item','data-v-01698be5',(selectedStatus==='missed')?'active':'']}}" bindtap="__e"><view class="stats-number missed data-v-01698be5">{{$root.m5.missed}}</view><view class="stats-label data-v-01698be5">漏检查</view></view><view data-event-opts="{{[['tap',[['selectStatus',['not_cleaned']]]]]}}" class="{{['stats-item','data-v-01698be5',(selectedStatus==='not_cleaned')?'active':'']}}" bindtap="__e"><view class="stats-number not-cleaned data-v-01698be5">{{$root.m6.not_cleaned}}</view><view class="stats-label data-v-01698be5">未打扫</view></view><view data-event-opts="{{[['tap',[['selectStatus',['completed']]]]]}}" class="{{['stats-item','data-v-01698be5',(selectedStatus==='completed')?'active':'']}}" bindtap="__e"><view class="stats-number success data-v-01698be5">{{$root.m7.completed}}</view><view class="stats-label data-v-01698be5">已完成</view></view></view></block></view><view class="card data-v-01698be5"><view class="card-header data-v-01698be5"><view class="header-content data-v-01698be5"><view class="card-title data-v-01698be5">责任区检查</view><view class="card-subtitle data-v-01698be5">固定和公共责任区检查任务</view></view></view><scroll-view class="category-scroll data-v-01698be5" scroll-x="true" show-scrollbar="false"><view class="category-tabs data-v-01698be5"><block wx:for="{{$root.l0}}" wx:for-item="category" wx:for-index="__i0__" wx:key="value"><view data-event-opts="{{[['tap',[['selectCategory',['$0'],[[['categoryTabs','value',category.$orig.value,'value']]]]]]]}}" class="{{['category-tab','data-v-01698be5',(selectedCategory===category.$orig.value)?'active':'']}}" bindtap="__e"><text class="tab-text data-v-01698be5">{{category.$orig.label}}</text><text class="tab-count data-v-01698be5">{{category.m8}}</text></view></block></view></scroll-view><block wx:if="{{loading}}"><view class="list-loading data-v-01698be5"><view class="loading-spinner data-v-01698be5"></view><view class="loading-text data-v-01698be5">加载数据中...</view></view></block><block wx:else><block wx:if="{{$root.g0>0}}"><view class="data-v-01698be5"><block wx:for="{{$root.l2}}" wx:for-item="group" wx:for-index="__i1__" wx:key="weekKey"><view class="time-group data-v-01698be5"><view data-event-opts="{{[['tap',[['toggleWeek',['$0'],[[['groupedFilteredRecords','weekKey',group.$orig.weekKey,'weekKey']]]]]]]}}" class="time-group-header data-v-01698be5" bindtap="__e"><view class="time-group-title data-v-01698be5"><uni-icons vue-id="{{'62a0b4c4-3-'+__i1__}}" type="{{group.$orig.expanded?'down':'right'}}" size="16" color="#007AFF" class="data-v-01698be5" bind:__l="__l"></uni-icons><text class="time-title data-v-01698be5">{{group.$orig.title}}</text><view class="time-count data-v-01698be5">{{group.g1+"条"}}</view></view></view><block wx:if="{{group.$orig.expanded}}"><view class="time-group-content data-v-01698be5"><block wx:for="{{group.l1}}" wx:for-item="record" wx:for-index="recordIndex" wx:key="recordIndex"><view data-event-opts="{{[['tap',[['handleRecordClick',['$0'],[[['groupedFilteredRecords','weekKey',group.$orig.weekKey],['records','',recordIndex]]]]]]]}}" class="list-item data-v-01698be5" bindtap="__e"><view class="{{['list-item-icon','data-v-01698be5','icon-bg-'+record.$orig.status]}}"><uni-icons vue-id="{{'62a0b4c4-4-'+__i1__+'-'+recordIndex}}" type="{{record.$orig.icon}}" size="18" color="{{record.m9}}" class="data-v-01698be5" bind:__l="__l"></uni-icons></view><view class="list-item-content data-v-01698be5"><view class="list-item-title data-v-01698be5"><text class="data-v-01698be5">{{record.$orig.areaName}}</text><view class="badges-container data-v-01698be5"><view class="{{['area-type-badge','data-v-01698be5','type-'+record.$orig.type]}}"><text class="badge-text data-v-01698be5">{{record.m10}}</text></view><block wx:if="{{record.$orig.isRectificationRecheck}}"><view class="rectification-badge data-v-01698be5"><text class="badge-text data-v-01698be5">整改任务</text></view></block></view></view><view class="list-item-subtitle data-v-01698be5">{{record.$orig.subtitle}}</view></view><view class="list-item-right data-v-01698be5"><view class="{{['status-badge','data-v-01698be5','status-'+record.$orig.status]}}">{{''+record.m11}}</view><uni-icons vue-id="{{'62a0b4c4-5-'+__i1__+'-'+recordIndex}}" type="right" size="14" color="#C7C7CC" class="data-v-01698be5" bind:__l="__l"></uni-icons></view></view></block></view></block></view></block></view></block><block wx:else><p-empty-state vue-id="62a0b4c4-6" useIcon="{{true}}" iconName="info" iconColor="#C7C7CC" size="large" text="暂无数据" class="data-v-01698be5" bind:__l="__l"></p-empty-state></block></block></view><view class="bottom-safe-area data-v-01698be5"></view><uni-popup vue-id="62a0b4c4-7" type="bottom" border-radius="16rpx 16rpx 0 0" data-ref="timePopup" class="data-v-01698be5 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="date-picker-popup data-v-01698be5"><view class="popup-header data-v-01698be5"><text class="popup-title data-v-01698be5">筛选时间</text><view data-event-opts="{{[['tap',[['closeDatePicker',['$event']]]]]}}" class="popup-close data-v-01698be5" bindtap="__e"><uni-icons vue-id="{{('62a0b4c4-8')+','+('62a0b4c4-7')}}" type="close" size="18" color="#8E8E93" class="data-v-01698be5" bind:__l="__l"></uni-icons></view></view><block wx:if="{{dateFilterMode==='quick'}}"><view class="quick-date-section data-v-01698be5"><view class="section-title data-v-01698be5">快捷选择</view><view class="quick-options data-v-01698be5"><block wx:for="{{quickDateOptions}}" wx:for-item="option" wx:for-index="__i2__" wx:key="value"><view data-event-opts="{{[['tap',[['selectQuickDateOption',['$0'],[[['quickDateOptions','value',option.value]]]]]]]}}" class="{{['quick-option','data-v-01698be5',(selectedQuickFilter===option.value)?'active':'']}}" bindtap="__e"><text class="quick-text data-v-01698be5">{{option.label}}</text></view></block></view></view></block><block wx:else><view class="range-date-section data-v-01698be5"><view class="calendar-section data-v-01698be5"><view class="calendar-header data-v-01698be5"><text class="calendar-tip data-v-01698be5">在日历上点击选择开始和结束日期</text></view><uni-calendar vue-id="{{('62a0b4c4-9')+','+('62a0b4c4-7')}}" range="{{true}}" date="{{calendarDate}}" start-date="{{calendarStartDate}}" end-date="{{calendarEndDate}}" data-ref="calendar" data-event-opts="{{[['^change',[['onCalendarChange']]],['^monthSwitch',[['onMonthSwitch']]]]}}" bind:change="__e" bind:monthSwitch="__e" class="data-v-01698be5 vue-ref" bind:__l="__l"></uni-calendar><block wx:if="{{customDateRange.startDate&&customDateRange.endDate}}"><view class="selected-range data-v-01698be5"><view class="range-item data-v-01698be5"><text class="range-label data-v-01698be5">开始日期</text><text class="range-value data-v-01698be5">{{$root.m12}}</text></view><view class="range-separator data-v-01698be5">→</view><view class="range-item data-v-01698be5"><text class="range-label data-v-01698be5">结束日期</text><text class="range-value data-v-01698be5">{{$root.m13}}</text></view></view></block></view></view></block></view></uni-popup></view>