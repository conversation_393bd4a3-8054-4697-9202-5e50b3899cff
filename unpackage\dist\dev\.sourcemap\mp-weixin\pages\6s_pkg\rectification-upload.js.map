{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-upload.vue?1d36", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-upload.vue?50a5", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-upload.vue?01b0", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-upload.vue?0abc", "uni-app:///pages/6s_pkg/rectification-upload.vue", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-upload.vue?087d", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-upload.vue?d9fa"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "taskInfo", "photos", "remarks", "loading", "loadError", "submitting", "autoUpload", "taskId", "processCache", "dateF<PERSON><PERSON><PERSON>", "photoUrlCache", "inspectionUrlCache", "computed", "<PERSON><PERSON><PERSON><PERSON>", "onLoad", "methods", "initProcessCache", "date", "handleRemarksInput", "value", "uni", "title", "icon", "duration", "loadTaskInfoOptimized", "action", "id", "result", "task", "console", "processTaskDataOptimized", "area", "areaId", "isPublic", "status", "problemDescription", "issueFoundDate", "inspector", "reviewComments", "reviewDate", "reviewer", "reviewResult", "inspectionPhotos", "previousPhotos", "preprocessPhotos", "loadTaskInfo", "retryLoad", "formatDateTime", "addPhoto", "count", "sizeType", "sourceType", "success", "newPhotos", "fail", "processNewPhotos", "url", "uploaded", "cloudUrl", "cloudPath", "uploading", "autoUploadNewPhotosOptimized", "maxConcurrent", "uploadPromises", "batch", "batchPromises", "Promise", "results", "successful", "failed", "uploadPhotoWithIndex", "photoIndex", "uploadResult", "autoUploadNewPhotos", "uploadSinglePhotoOptimized", "uploadUtils", "fileInfo", "size", "error", "generateCloudPath", "uploadSinglePhoto", "deletePhoto", "index", "photo", "uniCloud", "fileList", "extractFileId", "getPhotoDisplayUrl", "processedUrl", "toggleAutoUpload", "previewPhoto", "urls", "current", "getCloudPhotoUrl", "previewInspectionPhoto", "previewPreviousPhoto", "submitRectification", "uploadedPhotos", "finalRemarks", "submissionData", "completion_description", "completion_photos", "completion_status", "preparePhotosForSubmit", "unuploadedPhotos", "uploadResults", "successfulUploads", "filter", "map", "existingPhotos", "type", "description", "prepareSubmissionData", "handleSubmitSuccess", "setTimeout", "handleSubmitError", "errorMessage", "content", "showCancel", "confirmText"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,4BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6I;AAC7I;AACwE;AACL;AACsC;;;AAGzG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,0FAAM;AACR,EAAE,2GAAM;AACR,EAAE,oHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrGA;AAAA;AAAA;AAAA;AAA8mB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACkLloB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACA;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;IACA;EACA;;EACAC;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;QACA;QAEA;UACA;UACA;YACA;cACAC;YACA;cACAA;YACA;UACA;YACAA;UACA;UAEA;YACA;UACA;UAEA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;QACAC;QACA;QACAC;UACAC;UACAC;UACAC;QACA;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACAjB;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBAAA;gBAAA;gBAAA,OAGA;kBACAkB;kBACA1B;oBAAA2B;kBAAA;gBACA;cAAA;gBAHAC;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACAC,oBAEA;gBACA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACAJ;QACAK;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACA;QACAC;QACAC;QACAC;QACAC;QACA;QACAC;QACA;QACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACAtC;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBAAA;gBAAA;gBAAA,OAGA;kBACAkB;kBACA1B;oBAAA2B;kBAAA;gBACA;cAAA;gBAHAC;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACAC;gBAEA;kBACAF;kBACAK;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACA;kBACAC;kBACAC;kBACAC;kBACAC;kBACA;kBACAC;kBACA;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAd;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiB;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;MACA;QACA;QACA;UACA;YACA9B;UACA;YACAA;UACA;QACA;UACAA;QACA;QACA;UACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACA+B;MAAA;MACA;QACA5B;UACAC;UACAC;QACA;QACA;MACA;MAEAF;QACA6B;QACAC;QACAC;QACAC;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBACAC,wDAEA;oBACA;;oBAEA;oBACA;sBACA;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CACA;UAAA;YAAA;UAAA;UAAA;QAAA;QACAC;UACAzB;UACAT;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAiC;MAAA;MACA;QACA;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;;QAEA;QACA;QAEA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACAC;gBACAC;gBAEA;kBACAC;kBACAC;oBAAA;kBAAA;kBACAF;gBACA;;gBAEA;gBAAA;gBAAA,OACAG;cAAA;gBAAAC;gBAEA;gBACAC;kBAAA;gBAAA;gBACAC;kBAAA;gBAAA;gBAEA;kBACAjD;oBACAC;oBACAC;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA+C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBAAA;gBAAA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBAAA,KAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAAA,kCAEAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAf,wCAEA;gBAAA;gBAAA,OACAgB;cAAA;gBAAAH;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEAG;cAAA;gBAAAC;gBAAA,kCAEA;kBACAxB;kBACAO;kBACAH;kBACAqB;gBACA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAhD;gBAAA,kCACA;kBAAAuB;kBAAA0B;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIAC,+BAEA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAEA/D;kBAAAC;gBAAA;gBAAA;gBAAA,OAEA+D;kBACAtF;kBACAC;oBACAsF;kBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAxD;gBACAT;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEAF;gBAAA;cAAA;gBAIA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAkE;MACA;QACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;MACA;QACA;QACAC;MACA;;MAEA;MACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACArE;QACAC;QACAC;MACA;IACA;IAEA;IACAoE;MAAA;MACA;MACA;QACA;UAAA;QAAA;MACA;MAEAtE;QACAuE;QACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACA;UACA;QACA;MACA;MAEA1E;QACAuE;QACAC;MACA;IACA;IAEA;IACAG;MACA;QAAA;MAAA;MACA3E;QACAuE;QACAC;MACA;IACA;IAEA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA5E;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,KAIA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAA2E;gBAEA;gBACA;gBACAC;gBACA;kBACAA;kBACArE;gBACA;gBAEAsE;kBACAzE;kBAAA;kBACA0E;kBAAA;kBACAC;kBACAC;gBACA;gBAEAlF;kBACAC;gBACA;;gBAEA;gBAAA;gBAAA,OACA;kBACAI;kBACA1B;gBACA;cAAA;gBAHA4B;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAE;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA0E;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAC;kBAAA;gBAAA;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACApF;kBACAC;gBACA;;gBAEA;gBACA0C;kBAAA;gBAAA;gBAAA;gBAAA,OACAG;cAAA;gBAAAuC;gBAEA;gBACAC,kCACAC;kBAAA;gBAAA,GACAC;kBAAA;gBAAA;gBAAA,MAEAF;kBAAA;kBAAA;gBAAA;gBACAtF;gBAAA,MACA;cAAA;gBAGA;gBACAyF,gCACAF;kBAAA;gBAAA,GACAC;kBAAA;oBAAApD;oBAAAsD;oBAAAC;kBAAA;gBAAA;gBAEA1D;kBAAA;oBACAG;oBACAsD;oBACAC;kBACA;gBAAA;gBAAA,8EAEAF;cAAA;gBAAA,mCAGA,eACAF;kBAAA;gBAAA,GACAC;kBAAA;oBACApD;oBACAsD;oBACAC;kBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACAtF;QACA0E;QACAC;MACA;IACA;IAEA;IACAY;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA7F;;gBAEA;gBACAA;kBACAb;kBACAyB;kBACA8E;gBACA;gBAEA1F;kBACAC;kBACAC;gBACA;gBAEA4F;kBACA9F;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA+F;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA/F;gBAEAgG;gBACA;kBACA;oBACAA;kBACA;oBACAA;kBACA;oBACAA;kBACA;gBACA;gBAAA;gBAAA,OAEAhG;kBACAC;kBACAgG;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACx2BA;AAAA;AAAA;AAAA;AAAirC,CAAgB,upCAAG,EAAC,C;;;;;;;;;;;ACArsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/rectification-upload.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/rectification-upload.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./rectification-upload.vue?vue&type=template&id=6e3331a0&scoped=true&\"\nvar renderjs\nimport script from \"./rectification-upload.vue?vue&type=script&lang=js&\"\nexport * from \"./rectification-upload.vue?vue&type=script&lang=js&\"\nimport style0 from \"./rectification-upload.vue?vue&type=style&index=0&id=6e3331a0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6e3331a0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/rectification-upload.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rectification-upload.vue?vue&type=template&id=6e3331a0&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    !_vm.loading && !_vm.loadError\n      ? _vm.formatDateTime(_vm.taskInfo.issueFoundDate)\n      : null\n  var m1 =\n    !_vm.loading && !_vm.loadError && _vm.taskInfo.reviewComments\n      ? _vm.formatDateTime(_vm.taskInfo.reviewDate)\n      : null\n  var g0 =\n    !_vm.loading && !_vm.loadError\n      ? _vm.taskInfo.inspectionPhotos &&\n        _vm.taskInfo.inspectionPhotos.length > 0\n      : null\n  var l0 =\n    !_vm.loading && !_vm.loadError && g0\n      ? _vm.__map(_vm.taskInfo.inspectionPhotos, function (photo, index) {\n          var $orig = _vm.__get_orig(photo)\n          var m2 = _vm.getCloudPhotoUrl(photo)\n          return {\n            $orig: $orig,\n            m2: m2,\n          }\n        })\n      : null\n  var g1 =\n    !_vm.loading && !_vm.loadError\n      ? _vm.taskInfo.previousPhotos && _vm.taskInfo.previousPhotos.length > 0\n      : null\n  var l1 =\n    !_vm.loading && !_vm.loadError && g1\n      ? _vm.__map(_vm.taskInfo.previousPhotos, function (photo, index) {\n          var $orig = _vm.__get_orig(photo)\n          var m3 = _vm.formatDateTime(photo.timestamp)\n          return {\n            $orig: $orig,\n            m3: m3,\n          }\n        })\n      : null\n  var l2 =\n    !_vm.loading && !_vm.loadError\n      ? _vm.__map(_vm.photos, function (photo, index) {\n          var $orig = _vm.__get_orig(photo)\n          var m4 = _vm.getPhotoDisplayUrl(photo)\n          return {\n            $orig: $orig,\n            m4: m4,\n          }\n        })\n      : null\n  var g2 = !_vm.loading && !_vm.loadError ? _vm.photos.length : null\n  var g3 = _vm.photos.length === 0 || !_vm.remarks.trim() || _vm.submitting\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        l1: l1,\n        l2: l2,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rectification-upload.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rectification-upload.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page-container\">\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-container\">\n      <view class=\"loading-content\">\n        <view class=\"loading-spinner\"></view>\n        <text class=\"loading-text\">加载整改任务信息中...</text>\n      </view>\n    </view>\n    \n    <!-- 错误状态 -->\n    <view v-else-if=\"loadError\" class=\"error-container\">\n      <view class=\"error-content\">\n        <uni-icons type=\"info\" size=\"48\" color=\"#FF3B30\"></uni-icons>\n        <text class=\"error-text\">{{ loadError }}</text>\n        <button class=\"retry-button\" @click=\"retryLoad\">重新加载</button>\n      </view>\n    </view>\n    \n    <!-- 正常内容 -->\n    <template v-else>\n      <view class=\"card\">\n        <view class=\"card-header\">\n          <view class=\"header-content\">\n            <view class=\"card-title\">整改任务</view>\n            <view class=\"card-subtitle\">{{ taskInfo.area }} - {{ taskInfo.isPublic ? '公共责任区' : '固定责任区' }}</view>\n          </view>\n        </view>\n        <view class=\"card-body\">\n          <view class=\"task-info\">\n            <view class=\"info-item\">\n              <text class=\"info-label\">问题描述：</text>\n              <text class=\"info-value\">{{ taskInfo.problemDescription }}</text>\n            </view>\n            <view class=\"info-item\">\n              <text class=\"info-label\">发现时间：</text>\n              <text class=\"info-value\">{{ formatDateTime(taskInfo.issueFoundDate) }}</text>\n            </view>\n            <view class=\"info-item\">\n              <text class=\"info-label\">检查员：</text>\n              <text class=\"info-value\">{{ taskInfo.inspector }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 审核反馈（如果存在） -->\n      <view v-if=\"taskInfo.reviewComments\" class=\"card review-feedback-card\">\n        <view class=\"card-header\">\n          <view class=\"card-title\">审核反馈</view>\n        </view>\n        <view class=\"card-body\">\n          <view class=\"feedback-content\">\n            <view class=\"feedback-item\">\n              <text class=\"feedback-label\">审核结果：</text>\n              <view class=\"status-badge status-rejected\">需重新整改</view>\n            </view>\n            <view class=\"feedback-item\">\n              <text class=\"feedback-label\">审核意见：</text>\n              <text class=\"feedback-value\">{{ taskInfo.reviewComments }}</text>\n            </view>\n            <view class=\"feedback-item\">\n              <text class=\"feedback-label\">审核时间：</text>\n              <text class=\"feedback-value\">{{ formatDateTime(taskInfo.reviewDate) }}</text>\n            </view>\n            <view class=\"feedback-item\">\n              <text class=\"feedback-label\">审核人员：</text>\n              <text class=\"feedback-value\">{{ taskInfo.reviewer }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 检查员发现问题时的照片（如果存在） -->\n      <view v-if=\"taskInfo.inspectionPhotos && taskInfo.inspectionPhotos.length > 0\" class=\"card inspection-photos-card\">\n        <view class=\"card-header\">\n          <view class=\"card-title\">问题照片</view>\n          <view class=\"card-subtitle\">检查员发现问题时拍摄的现场照片</view>\n        </view>\n        <view class=\"card-body\">\n          <view class=\"inspection-photos-grid\">\n            <view v-for=\"(photo, index) in taskInfo.inspectionPhotos\" :key=\"index\" class=\"inspection-photo-item\">\n              <image :src=\"getCloudPhotoUrl(photo)\" mode=\"aspectFill\" @click=\"previewInspectionPhoto(index)\"></image>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 之前的整改照片（如果存在） -->\n      <view v-if=\"taskInfo.previousPhotos && taskInfo.previousPhotos.length > 0\" class=\"card previous-photos-card\">\n        <view class=\"card-header\">\n          <view class=\"card-title\">之前的整改照片</view>\n          <view class=\"card-subtitle\">供参考，检查员认为需要改进</view>\n        </view>\n        <view class=\"card-body\">\n          <view class=\"previous-photos-grid\">\n            <view v-for=\"(photo, index) in taskInfo.previousPhotos\" :key=\"index\" class=\"previous-photo-item\">\n              <image :src=\"photo.url\" mode=\"aspectFill\" @click=\"previewPreviousPhoto(index)\"></image>\n              <view class=\"photo-overlay\">\n                <text class=\"photo-time\">{{ formatDateTime(photo.timestamp) }}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <view class=\"card\">\n        <view class=\"card-header\">\n          <view class=\"card-title\">{{ taskInfo.reviewComments ? '重新整改' : '整改记录' }}</view>\n        </view>\n        <view class=\"card-body\">\n          <view class=\"upload-section\">\n            <view class=\"section-header\">\n              <view class=\"section-title\">上传整改照片</view>\n              <view class=\"auto-upload-toggle\" @click=\"toggleAutoUpload\">\n                <view class=\"toggle-label\">自动上传</view>\n                <view class=\"toggle-switch\" :class=\"{ active: autoUpload }\">\n                  <view class=\"toggle-circle\"></view>\n                </view>\n              </view>\n            </view>\n            <view class=\"photo-grid\">\n              <view v-for=\"(photo, index) in photos\" :key=\"index\" class=\"photo-item\">\n                <image :src=\"getPhotoDisplayUrl(photo)\" mode=\"aspectFill\" @click=\"previewPhoto(index)\"></image>\n                <!-- 上传状态指示器 -->\n                <view v-if=\"photo.uploading\" class=\"photo-uploading\">\n                  <view class=\"upload-spinner\"></view>\n                </view>\n                <view v-else-if=\"photo.uploaded\" class=\"photo-uploaded\">\n                  <uni-icons type=\"checkmarkempty\" size=\"16\" color=\"white\"></uni-icons>\n                </view>\n                <view class=\"photo-delete\" @click=\"deletePhoto(index)\">\n                  <uni-icons type=\"close\" size=\"18\" color=\"white\"></uni-icons>\n                </view>\n              </view>\n              <view v-if=\"photos.length < 12\" class=\"photo-add\" @click=\"addPhoto\">\n                <uni-icons type=\"camera\" size=\"32\" color=\"#8E8E93\"></uni-icons>\n                <text>添加照片</text>\n              </view>\n            </view>\n            <view class=\"photo-tip\">最多可上传12张照片，建议拍摄整改前后对比照片</view>\n          </view>\n          \n          <view class=\"remarks-section\">\n            <view class=\"section-title\">整改说明</view>\n            <view class=\"remarks-input-container\">\n              <textarea \n                v-model=\"remarks\" \n                placeholder=\"请详细描述整改过程和结果...\"\n                maxlength=\"200\"\n                class=\"remarks-input\"\n                @input=\"handleRemarksInput\"\n              ></textarea>\n              <view class=\"char-count-overlay\">{{ remarksLength }}/200</view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </template>\n    \n    <view class=\"button-container\">\n      <button \n        class=\"primary-button\" \n        @click=\"submitRectification\" \n        :disabled=\"photos.length === 0 || !remarks.trim() || submitting\"\n        :class=\"{ loading: submitting }\"\n      >\n        <view v-if=\"submitting\" class=\"button-loading\">\n          <view class=\"loading-spinner\"></view>\n          <text>提交中...</text>\n        </view>\n        <text v-else>提交整改记录</text>\n      </button>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { callCloudFunction } from '@/utils/auth.js';\nimport uploadUtils from '@/utils/upload-utils.js';\n\nexport default {\n  name: 'RectificationUpload',\n  data() {\n    return {\n      taskInfo: {},\n      photos: [],\n      remarks: '',\n      loading: false,\n      loadError: '',\n      submitting: false,\n      autoUpload: true, // 自动上传开关\n      taskId: '',\n      // 性能优化：缓存系统\n      processCache: {\n        dateFormatter: null, // 缓存日期格式化器\n        photoUrlCache: new Map(), // 缓存照片URL处理结果\n        inspectionUrlCache: new Map() // 缓存检查照片URL\n      }\n    }\n  },\n  computed: {\n    // 计算备注长度，确保响应式更新\n    remarksLength() {\n      return this.remarks ? this.remarks.length : 0;\n    }\n  },\n  onLoad(options) {\n    this.taskId = options.taskId;\n    this.initProcessCache();\n    this.loadTaskInfoOptimized(options.taskId);\n  },\n  \n  methods: {\n    // 初始化性能缓存\n    initProcessCache() {\n      // 创建缓存的日期格式化器\n      this.processCache.dateFormatter = (dateString) => {\n        if (!dateString) return '--';\n        \n        try {\n          let date;\n          if (typeof dateString === 'string') {\n            if (dateString.includes('T') || dateString.includes('Z')) {\n              date = new Date(dateString);\n            } else {\n              date = new Date(dateString.replace(/-/g, '/'));\n            }\n          } else {\n            date = new Date(dateString);\n          }\n          \n          if (isNaN(date.getTime())) {\n            return '--';\n          }\n          \n          return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;\n        } catch (error) {\n          return '--';\n        }\n      };\n    },\n\n    // 处理备注输入，确保字符限制和响应式更新\n    handleRemarksInput(e) {\n      let value = e.detail.value || '';\n      \n      // 强制限制字符数量\n      if (value.length > 200) {\n        value = value.substring(0, 200);\n        // 如果超出限制，显示提示\n        uni.showToast({\n          title: '整改说明不能超过200个字符',\n          icon: 'none',\n          duration: 1500\n        });\n      }\n      \n      // 更新数据\n      this.remarks = value;\n      \n      // 强制触发视图更新\n      this.$forceUpdate();\n    },\n\n    // 优化的任务信息加载\n    async loadTaskInfoOptimized(taskId) {\n      if (!taskId) {\n        this.loadError = '整改任务ID不能为空';\n        return;\n      }\n\n      this.loading = true;\n      this.loadError = '';\n\n      try {\n        const result = await callCloudFunction('hygiene-rectification', {\n          action: 'getRectificationDetail',\n          data: { id: taskId }\n        });\n\n        if (result && result.success && result.data) {\n          const task = result.data;\n          \n          // 使用优化的数据处理\n          this.taskInfo = this.processTaskDataOptimized(task);\n          \n          // 预处理照片URL\n          this.preprocessPhotos();\n        } else {\n          throw new Error(result?.message || '获取整改任务信息失败');\n        }\n      } catch (error) {\n        console.error('加载整改任务信息失败:', error);\n        this.loadError = error.message || '加载失败，请稍后重试';\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 优化的数据处理\n    processTaskDataOptimized(task) {\n      return {\n        id: task._id || task.id,\n        area: task.area_name || '未知责任区',\n        areaId: task.area_id,\n        isPublic: task.area_type === 'public',\n        status: task.status || 'pending',\n        problemDescription: task.issue_description || task.description || '无问题描述',\n        issueFoundDate: task.created_at || task.issue_found_date,\n        inspector: task.inspector_name || task.created_by_name || task.assigned_by_name || '未知检查员',\n        // 审核反馈信息\n        reviewComments: task.review_comments || '',\n        reviewDate: task.review_date || '',\n        reviewer: task.reviewer_name || '未知审核员',\n        reviewResult: task.review_result || '',\n        // 检查员发现问题时上传的照片\n        inspectionPhotos: task.inspection_photos || [],\n        // 之前的整改照片\n        previousPhotos: task.completion_photos || []\n      };\n    },\n\n    // 预处理照片URL，提前缓存\n    preprocessPhotos() {\n      // 预处理检查照片URL\n      if (this.taskInfo.inspectionPhotos) {\n        this.taskInfo.inspectionPhotos.forEach((photo, index) => {\n          const url = this.getCloudPhotoUrl(photo);\n          this.processCache.inspectionUrlCache.set(index, url);\n        });\n      }\n    },\n\n    // 加载整改任务信息\n    async loadTaskInfo(taskId) {\n      if (!taskId) {\n        this.loadError = '整改任务ID不能为空';\n        return;\n      }\n\n      this.loading = true;\n      this.loadError = '';\n\n      try {\n        const result = await callCloudFunction('hygiene-rectification', {\n          action: 'getRectificationDetail',\n          data: { id: taskId }\n        });\n\n        if (result && result.success && result.data) {\n          const task = result.data;\n          \n          this.taskInfo = {\n            id: task._id || task.id,\n            area: task.area_name || '未知责任区',\n            areaId: task.area_id,\n            isPublic: task.area_type === 'public',\n            status: task.status || 'pending',\n            problemDescription: task.issue_description || task.description || '无问题描述',\n            issueFoundDate: task.created_at || task.issue_found_date,\n            inspector: task.inspector_name || task.created_by_name || task.assigned_by_name || '未知检查员',\n            // 审核反馈信息\n            reviewComments: task.review_comments || '',\n            reviewDate: task.review_date || '',\n            reviewer: task.reviewer_name || '未知审核员',\n            reviewResult: task.review_result || '',\n            // 检查员发现问题时上传的照片\n            inspectionPhotos: task.inspection_photos || [],\n            // 之前的整改照片\n            previousPhotos: task.completion_photos || []\n          };\n        } else {\n          throw new Error(result?.message || '获取整改任务信息失败');\n        }\n      } catch (error) {\n        console.error('加载整改任务信息失败:', error);\n        this.loadError = error.message || '加载失败，请稍后重试';\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 重新加载\n    retryLoad() {\n      this.loadTaskInfo(this.taskId);\n    },\n\n    // 优化的日期时间格式化（使用缓存）\n    formatDateTime(dateString) {\n      // 确保缓存已初始化\n      if (!this.processCache.dateFormatter) {\n        this.initProcessCache();\n      }\n      \n      // 安全调用格式化函数\n      if (this.processCache.dateFormatter && typeof this.processCache.dateFormatter === 'function') {\n        return this.processCache.dateFormatter(dateString);\n      }\n      \n      // 回退到默认格式化\n      if (!dateString) return '--';\n      try {\n        let date;\n        if (typeof dateString === 'string') {\n          if (dateString.includes('T') || dateString.includes('Z')) {\n            date = new Date(dateString);\n          } else {\n            date = new Date(dateString.replace(/-/g, '/'));\n          }\n        } else {\n          date = new Date(dateString);\n        }\n        if (isNaN(date.getTime())) {\n          return '--';\n        }\n        return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;\n      } catch (error) {\n        return '--';\n      }\n    },\n    \n    // 优化的添加照片\n    addPhoto() {\n      if (this.photos.length >= 12) {\n        uni.showToast({\n          title: '最多只能上传12张照片',\n          icon: 'none'\n        });\n        return;\n      }\n\n      uni.chooseImage({\n        count: 12 - this.photos.length,\n        sizeType: ['compressed'],\n        sourceType: ['camera', 'album'],\n        success: async (res) => {\n          const newPhotos = this.processNewPhotos(res.tempFilePaths);\n          \n          // 添加到照片列表\n          this.photos = this.photos.concat(newPhotos);\n          \n          // 自动上传新选择的照片（优化版）\n          if (this.autoUpload) {\n            this.autoUploadNewPhotosOptimized(newPhotos);\n          }\n        },\n        fail: (error) => {\n          console.error('选择照片失败:', error);\n          uni.showToast({\n            title: '选择照片失败',\n            icon: 'none'\n          });\n        }\n      });\n    },\n\n    // 处理新照片数据\n    processNewPhotos(tempFilePaths) {\n      return tempFilePaths.map(path => {\n        const photo = {\n          url: path,\n          uploaded: false,\n          cloudUrl: '',\n          cloudPath: '',\n          uploading: false\n        };\n        \n        // 预缓存显示URL\n        this.processCache.photoUrlCache.set(path, this.getPhotoDisplayUrl(photo));\n        \n        return photo;\n      });\n    },\n    \n    // 优化的并行自动上传\n    async autoUploadNewPhotosOptimized(newPhotos) {\n      if (!this.autoUpload || newPhotos.length === 0) return;\n      \n      // 并行上传，最大并发数为3\n      const maxConcurrent = 3;\n      const uploadPromises = [];\n      \n      for (let i = 0; i < newPhotos.length; i += maxConcurrent) {\n        const batch = newPhotos.slice(i, i + maxConcurrent);\n        const batchPromises = batch.map(photo => this.uploadPhotoWithIndex(photo));\n        uploadPromises.push(...batchPromises);\n      }\n      \n      // 使用 Promise.allSettled 确保即使部分失败也能继续\n      const results = await Promise.allSettled(uploadPromises);\n      \n      // 统计成功和失败的数量\n      const successful = results.filter(r => r.status === 'fulfilled').length;\n      const failed = results.filter(r => r.status === 'rejected').length;\n      \n      if (failed > 0) {\n        uni.showToast({\n          title: `${successful}张上传成功，${failed}张失败`,\n          icon: 'none',\n          duration: 2000\n        });\n      }\n    },\n\n    // 上传单张照片并更新索引\n    async uploadPhotoWithIndex(photo) {\n      const photoIndex = this.photos.findIndex(p => p.url === photo.url);\n      if (photoIndex === -1) throw new Error('照片不存在');\n      \n      try {\n        // 标记为正在上传\n        this.$set(this.photos[photoIndex], 'uploading', true);\n        \n        // 上传照片\n        const uploadResult = await this.uploadSinglePhotoOptimized(photo);\n        \n        if (uploadResult.success) {\n          // 更新照片信息\n          this.$set(this.photos[photoIndex], 'uploaded', true);\n          this.$set(this.photos[photoIndex], 'cloudUrl', uploadResult.url);\n          this.$set(this.photos[photoIndex], 'cloudPath', uploadResult.cloudPath);\n          this.$set(this.photos[photoIndex], 'uploading', false);\n          \n          return uploadResult;\n        } else {\n          throw new Error(uploadResult.error || '上传失败');\n        }\n      } catch (error) {\n        this.$set(this.photos[photoIndex], 'uploading', false);\n        throw error;\n      }\n    },\n\n    // 兼容旧方法名\n    async autoUploadNewPhotos(newPhotos) {\n      return this.autoUploadNewPhotosOptimized(newPhotos);\n    },\n\n    // 优化的单张照片上传\n    async uploadSinglePhotoOptimized(photo) {\n      try {\n        const cloudPath = this.generateCloudPath();\n        \n        // 使用 uploadToCloud 方法上传单张照片\n        const uploadResult = await uploadUtils.uploadToCloud(photo.url, cloudPath);\n        \n        if (uploadResult && uploadResult.fileID) {\n          // 获取访问URL\n          const fileInfo = await uploadUtils.getFileInfo(uploadResult.fileID);\n          \n          return {\n            success: true,\n            cloudPath: uploadResult.fileID,\n            url: fileInfo.tempFileURL || uploadResult.fileID,\n            size: uploadResult.actualSize\n          };\n        } else {\n          throw new Error('上传返回结果异常');\n        }\n      } catch (error) {\n        console.error('单张照片上传失败:', error);\n        return { success: false, error: error.message };\n      }\n    },\n\n    // 生成云端路径\n    generateCloudPath() {\n      const timestamp = Date.now();\n      const random = Math.random().toString(36).substring(2, 8);\n      return `6s/rectification/${this.taskInfo.areaId}/${timestamp}_${random}.jpg`;\n    },\n\n    // 兼容旧方法名\n    async uploadSinglePhoto(photo) {\n      return this.uploadSinglePhotoOptimized(photo);\n    },\n    \n    // 删除照片\n    async deletePhoto(index) {\n      if (index < 0 || index >= this.photos.length) {\n        return;\n      }\n\n      const photo = this.photos[index];\n      \n      // 如果照片已经上传到云端，需要删除云端文件\n      if (photo.uploaded && photo.cloudPath) {\n        try {\n          uni.showLoading({ title: '删除照片中...' });\n          \n          await uniCloud.callFunction({\n            name: 'delete-file',\n            data: {\n              fileList: [this.extractFileId(photo.cloudPath)]\n            }\n          });\n        } catch (error) {\n          console.error('删除云端照片失败:', error);\n          uni.showToast({\n            title: '删除云端照片失败',\n            icon: 'none'\n          });\n        } finally {\n          uni.hideLoading();\n        }\n      }\n      \n      // 从本地数组中移除\n      this.photos.splice(index, 1);\n    },\n\n    // 从URL中提取文件ID\n    extractFileId(url) {\n      if (url.startsWith('cloud://')) {\n        const parts = url.split('/');\n        return parts[parts.length - 1];\n      } else if (url.includes('tcb-api')) {\n        const urlObj = new URL(url);\n        return urlObj.pathname.split('/').pop();\n      }\n      return url;\n    },\n\n    // 优化的照片显示URL获取（使用缓存）\n    getPhotoDisplayUrl(photo) {\n      const url = photo.url || photo;\n      \n      // 确保缓存已初始化\n      if (!this.processCache.photoUrlCache) {\n        this.initProcessCache();\n      }\n      \n      // 检查缓存\n      if (this.processCache.photoUrlCache && this.processCache.photoUrlCache.has(url)) {\n        return this.processCache.photoUrlCache.get(url);\n      }\n      \n      // 处理URL\n      let processedUrl = url;\n      if (typeof url === 'string' && url.startsWith('http://tmp/')) {\n        // 这是微信小程序的本地临时文件路径，直接使用\n        processedUrl = url;\n      }\n      \n      // 缓存结果\n      if (this.processCache.photoUrlCache) {\n        this.processCache.photoUrlCache.set(url, processedUrl);\n      }\n      \n      return processedUrl;\n    },\n\n    // 切换自动上传状态\n    toggleAutoUpload() {\n      this.autoUpload = !this.autoUpload;\n      uni.showToast({\n        title: this.autoUpload ? '已开启自动上传' : '已关闭自动上传',\n        icon: 'none'\n      });\n    },\n\n    // 优化的照片预览（缓存URL列表）\n    previewPhoto(index) {\n      // 使用缓存的URL列表，避免重复映射\n      if (!this._cachedPhotoUrls || this._cachedPhotoUrls.length !== this.photos.length) {\n        this._cachedPhotoUrls = this.photos.map(photo => this.getPhotoDisplayUrl(photo));\n      }\n      \n      uni.previewImage({\n        urls: this._cachedPhotoUrls,\n        current: index\n      });\n    },\n\n    // 获取云端照片URL\n    getCloudPhotoUrl(photo) {\n      if (typeof photo === 'string') {\n        return photo; // 直接返回URL字符串\n      }\n      return photo.url || photo; // 如果是对象，取url字段\n    },\n\n    // 优化的检查员问题照片预览（使用缓存）\n    previewInspectionPhoto(index) {\n      // 使用预缓存的URL\n      if (!this._cachedInspectionUrls) {\n        this._cachedInspectionUrls = this.taskInfo.inspectionPhotos.map((photo, i) => {\n          return this.processCache.inspectionUrlCache.get(i) || this.getCloudPhotoUrl(photo);\n        });\n      }\n      \n      uni.previewImage({\n        urls: this._cachedInspectionUrls,\n        current: index\n      });\n    },\n\n    // 预览之前的整改照片\n    previewPreviousPhoto(index) {\n      const urls = this.taskInfo.previousPhotos.map(photo => photo.url);\n      uni.previewImage({\n        urls: urls,\n        current: index\n      });\n    },\n    \n    // 优化的提交整改记录\n    async submitRectification() {\n      if (this.photos.length === 0) {\n        uni.showToast({\n          title: '请至少上传一张照片',\n          icon: 'none'\n        });\n        return;\n      }\n\n      if (!this.remarks.trim()) {\n        uni.showToast({\n          title: '请填写整改说明',\n          icon: 'none'\n        });\n        return;\n      }\n\n      if (this.submitting) return;\n\n      this.submitting = true;\n      \n      try {\n        // 准备照片数据\n        const uploadedPhotos = await this.preparePhotosForSubmit();\n        \n        // 准备提交数据\n        // 确保整改说明不超过限制\n        let finalRemarks = this.remarks.trim();\n        if (finalRemarks.length > 200) {\n          finalRemarks = finalRemarks.substring(0, 200);\n          console.warn('整改说明被截断到200字符');\n        }\n        \n        const submissionData = {\n          id: this.taskId,  // 云函数期望的参数名\n          completion_description: finalRemarks,  // 云函数期望的字段名\n          completion_photos: uploadedPhotos,\n          completion_status: 'completed'\n        };\n        \n        uni.showLoading({\n          title: '提交整改记录...'\n        });\n        \n        // 提交整改记录\n        const result = await callCloudFunction('hygiene-rectification', {\n          action: 'completeRectification',\n          data: submissionData\n        });\n        \n        if (result && result.success) {\n          await this.handleSubmitSuccess();\n        } else {\n          throw new Error(result?.message || '提交整改记录失败');\n        }\n      } catch (error) {\n        console.error('提交整改记录失败:', error);\n        await this.handleSubmitError(error);\n      } finally {\n        this.submitting = false;\n      }\n    },\n\n    // 准备照片数据进行提交\n    async preparePhotosForSubmit() {\n      // 检查是否有未上传的照片\n      const unuploadedPhotos = this.photos.filter(photo => !photo.uploaded && !photo.uploading);\n      \n      if (unuploadedPhotos.length > 0) {\n        uni.showLoading({\n          title: '正在上传剩余照片...'\n        });\n        \n        // 并行上传剩余照片\n        const uploadPromises = unuploadedPhotos.map(photo => this.uploadSinglePhotoOptimized(photo));\n        const uploadResults = await Promise.allSettled(uploadPromises);\n        \n        // 检查上传结果\n        const successfulUploads = uploadResults\n          .filter(result => result.status === 'fulfilled' && result.value.success)\n          .map(result => result.value);\n        \n        if (successfulUploads.length === 0) {\n          uni.hideLoading();\n          throw new Error('照片上传失败，请重试');\n        }\n        \n        // 合并所有已上传的照片\n        const existingPhotos = this.photos\n          .filter(photo => photo.uploaded)\n          .map(photo => ({ url: photo.cloudPath, type: 'rectification', description: '' }));\n        \n        const newPhotos = successfulUploads.map(upload => ({\n          url: upload.cloudPath,\n          type: 'rectification',\n          description: ''\n        }));\n        \n        return [...existingPhotos, ...newPhotos];\n      } else {\n        // 所有照片都已上传\n        return this.photos\n          .filter(photo => photo.uploaded)\n          .map(photo => ({\n            url: photo.cloudPath,\n            type: 'rectification',\n            description: ''\n          }));\n      }\n    },\n\n    // 准备提交数据\n    prepareSubmissionData(uploadedPhotos) {\n      return {\n        id: this.taskId,\n        completion_description: this.remarks.trim(),\n        completion_photos: uploadedPhotos\n      };\n    },\n\n    // 处理提交成功\n    async handleSubmitSuccess() {\n      uni.hideLoading();\n      \n      // 发送事件通知其他页面更新\n      uni.$emit('rectificationRecordUpdated', {\n        taskId: this.taskId,\n        areaId: this.taskInfo.areaId,\n        type: 'completed'\n      });\n      \n      uni.showToast({\n        title: '提交成功',\n        icon: 'success'\n      });\n      \n      setTimeout(() => {\n        uni.navigateBack();\n      }, 1500);\n    },\n\n    // 处理提交错误\n    async handleSubmitError(error) {\n      uni.hideLoading();\n      \n      let errorMessage = '提交失败，请重试';\n      if (error.message) {\n        if (error.message.includes('未登录')) {\n          errorMessage = '请先登录';\n        } else if (error.message.includes('权限')) {\n          errorMessage = '您没有权限操作该整改任务';\n        } else {\n          errorMessage = error.message;\n        }\n      }\n      \n      await uni.showModal({\n        title: '提交失败',\n        content: errorMessage,\n        showCancel: false,\n        confirmText: '知道了'\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n  min-height: 100vh;\n  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\n  width: 100%;\n  overflow-x: hidden;\n  box-sizing: border-box;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;\n  padding: 24rpx;\n}\n\n.card {\n  background: #ffffff;\n  border-radius: 16rpx;\n  margin-bottom: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.card-header {\n  padding: 32rpx 32rpx 16rpx 32rpx;\n  border-bottom: 1rpx solid #F2F2F7;\n}\n\n.card-body {\n  padding: 32rpx;\n}\n\n.header-content {\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n}\n\n.card-subtitle {\n  font-size: 26rpx;\n  color: #8E8E93;\n}\n\n.task-info {\n  display: flex;\n  flex-direction: column;\n  gap: 24rpx;\n}\n\n.info-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 16rpx;\n}\n\n.info-label {\n  font-size: 28rpx;\n  color: #8E8E93;\n  width: 140rpx; /* Fixed width for alignment */\n  flex-shrink: 0; /* Prevent shrinking */\n  text-align: left; /* Align text to left */\n}\n\n.info-value {\n  font-size: 28rpx;\n  color: #1C1C1E;\n  flex: 1;\n  line-height: 1.4;\n}\n\n.upload-section, .remarks-section {\n  margin-bottom: 32rpx;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.section-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n}\n\n.auto-upload-toggle {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n}\n\n.toggle-label {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.toggle-switch {\n  width: 76rpx;\n  height: 44rpx;\n  background: #E5E5EA;\n  border-radius: 22rpx;\n  position: relative;\n  transition: background-color 0.3s ease;\n}\n\n.toggle-switch.active {\n  background: #34C759;\n}\n\n.toggle-circle {\n  width: 36rpx;\n  height: 36rpx;\n  background: white;\n  border-radius: 50%;\n  position: absolute;\n  top: 4rpx;\n  left: 4rpx;\n  transition: transform 0.3s ease;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);\n}\n\n.toggle-switch.active .toggle-circle {\n  transform: translateX(32rpx);\n}\n\n.photo-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12rpx;\n  margin-bottom: 16rpx;\n}\n\n.photo-item {\n  position: relative;\n  width: 170rpx;\n  height: 170rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n}\n\n.photo-item image {\n  width: 100%;\n  height: 100%;\n}\n\n.photo-delete {\n  position: absolute;\n  top: 6rpx;\n  right: 6rpx;\n  width: 36rpx;\n  height: 36rpx;\n  background: rgba(0, 0, 0, 0.7);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\n}\n\n.photo-add {\n  width: 170rpx;\n  height: 170rpx;\n  border: 2rpx dashed #E5E5EA;\n  border-radius: 12rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 8rpx;\n  background: #F8F9FA;\n}\n\n.photo-add text {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n\n\n.photo-tip {\n  font-size: 24rpx;\n  color: #8E8E93;\n  line-height: 1.4;\n}\n\n.remarks-section .section-title {\n  margin-bottom: 16rpx;\n}\n\n.remarks-input-container {\n  position: relative;\n  width: 100%;\n}\n\n.remarks-input {\n  width: 100%;\n  min-height: 120rpx;\n  padding: 16rpx 16rpx 32rpx 16rpx;\n  border: 1rpx solid #E5E5EA;\n  border-radius: 12rpx;\n  font-size: 28rpx;\n  color: #1C1C1E;\n  background: #F8F9FA;\n  box-sizing: border-box;\n  resize: none;\n  line-height: 1.5;\n}\n\n.char-count-overlay {\n  position: absolute;\n  bottom: 8rpx;\n  right: 8rpx;\n  font-size: 22rpx;\n  color: #8E8E93;\n  background: rgba(248, 249, 250, 0.9);\n  padding: 4rpx 8rpx;\n  border-radius: 8rpx;\n  pointer-events: none;\n  z-index: 2;\n  backdrop-filter: blur(4rpx);\n}\n\n.button-container {\n  padding: 32rpx 0;\n}\n\n.primary-button {\n  background: #007AFF;\n  color: white;\n  border: none;\n  border-radius: 12rpx;\n  padding: 24rpx;\n  font-size: 32rpx;\n  font-weight: 600;\n  width: 100%;\n}\n\n.primary-button[disabled] {\n  background: #C7C7CC;\n  color: #8E8E93;\n}\n\n.primary-button.loading {\n  background: #0056D6;\n  opacity: 0.9;\n}\n\n.button-loading {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12rpx;\n  color: white;\n}\n\n.loading-spinner {\n  width: 32rpx;\n  height: 32rpx;\n  border: 3rpx solid rgba(255, 255, 255, 0.3);\n  border-top: 3rpx solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.photo-uploading {\n  position: absolute;\n  top: 8rpx;\n  left: 8rpx;\n  width: 32rpx;\n  height: 32rpx;\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-spinner {\n  width: 20rpx;\n  height: 20rpx;\n  border: 2rpx solid rgba(255, 255, 255, 0.3);\n  border-top: 2rpx solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.photo-uploaded {\n  position: absolute;\n  top: 8rpx;\n  left: 8rpx;\n  width: 32rpx;\n  height: 32rpx;\n  background: #34C759;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 加载状态 */\n.loading-container {\n  padding: 120rpx 32rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.loading-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 24rpx;\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n  text-align: center;\n}\n\n/* 错误状态 */\n.error-container {\n  padding: 120rpx 32rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.error-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 24rpx;\n  text-align: center;\n}\n\n.error-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n  line-height: 1.4;\n}\n\n.retry-button {\n  background: #007AFF;\n  color: white;\n  border: none;\n  border-radius: 8rpx;\n  padding: 16rpx 32rpx;\n  font-size: 26rpx;\n}\n\n/* 审核反馈卡片 */\n.feedback-content {\n  display: flex;\n  flex-direction: column;\n  gap: 24rpx;\n}\n\n.feedback-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 16rpx;\n}\n\n.feedback-label {\n  font-size: 28rpx;\n  color: #8E8E93;\n  width: 140rpx;\n  flex-shrink: 0;\n  text-align: left;\n}\n\n.feedback-value {\n  font-size: 28rpx;\n  color: #1C1C1E;\n  flex: 1;\n  line-height: 1.4;\n}\n\n.status-badge {\n  padding: 8rpx 16rpx;\n  border-radius: 8rpx;\n  font-size: 24rpx;\n  font-weight: 600;\n}\n\n.status-rejected {\n  background: #FFE6E6;\n  color: #FF3B30;\n}\n\n/* 检查员问题照片 */\n.inspection-photos-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12rpx;\n}\n\n.inspection-photo-item {\n  position: relative;\n  width: 170rpx;\n  height: 170rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n}\n\n.inspection-photo-item image {\n  width: 100%;\n  height: 100%;\n}\n\n.previous-photos-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12rpx;\n}\n\n.previous-photo-item {\n  position: relative;\n  width: 170rpx;\n  height: 170rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n  opacity: 0.8;\n}\n\n.previous-photo-item image {\n  width: 100%;\n  height: 100%;\n}\n\n.photo-overlay {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));\n  padding: 16rpx 12rpx 8rpx;\n}\n\n.photo-time {\n  font-size: 20rpx;\n  color: white;\n  text-align: center;\n  display: block;\n}\n</style>", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rectification-upload.vue?vue&type=style&index=0&id=6e3331a0&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rectification-upload.vue?vue&type=style&index=0&id=6e3331a0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775842582\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}