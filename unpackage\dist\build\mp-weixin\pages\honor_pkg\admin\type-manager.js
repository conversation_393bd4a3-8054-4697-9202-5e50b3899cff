(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/honor_pkg/admin/type-manager"],{"050f":function(e,t,n){"use strict";var a=n("9872"),r=n.n(a);r.a},"0ff3":function(e,t,n){"use strict";(function(e,a){var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(n("7eb4")),o=r(n("7ca3")),c=r(n("34cf")),s=r(n("ee10"));function u(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"===typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return l(e,t)}(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var a=0,r=function(){};return{s:r,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw i}}}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var d={name:"TypeManager",data:function(){return{loading:!1,refreshing:!1,saving:!1,stats:{totalTypes:0,activeTypes:0,thisMonthTypes:0},typeList:[],searchKeyword:"",activeFilter:"all",showCreateModal:!1,editingType:null,createForm:{name:"",code:"",description:"",isActive:!0}}},computed:{activeTypeList:function(){return this.typeList.filter((function(e){return e.isActive}))},inactiveTypeList:function(){return this.typeList.filter((function(e){return!e.isActive}))},filteredTypeList:function(){var e=this.typeList;if("active"===this.activeFilter?e=this.activeTypeList:"inactive"===this.activeFilter&&(e=this.inactiveTypeList),this.searchKeyword.trim()){var t=this.searchKeyword.trim().toLowerCase();e=e.filter((function(e){return e.name.toLowerCase().includes(t)||e.code.toLowerCase().includes(t)||e.description&&e.description.toLowerCase().includes(t)}))}return e}},onLoad:function(){this.initializeData()},methods:{initializeData:function(){var t=this;return(0,s.default)(i.default.mark((function n(){return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t.loading=!0,n.prev=1,n.next=4,t.loadTypeList();case 4:t.loadStats(),n.next=10;break;case 7:n.prev=7,n.t0=n["catch"](1),e.showToast({title:"数据加载失败",icon:"none"});case 10:return n.prev=10,t.loading=!1,n.finish(10);case 13:case"end":return n.stop()}}),n,null,[[1,7,10,13]])})))()},loadTypeList:function(){var e=this;return(0,s.default)(i.default.mark((function t(){var n;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,a.callFunction({name:"honor-admin",data:{action:"getHonorTypes"}});case 3:if(n=t.sent,0!==n.result.code){t.next=8;break}e.typeList=n.result.data||[],t.next=9;break;case 8:throw new Error(n.result.message||"获取类型列表失败");case 9:t.next=14;break;case 11:throw t.prev=11,t.t0=t["catch"](0),t.t0;case 14:case"end":return t.stop()}}),t,null,[[0,11]])})))()},loadStats:function(){var e=new Date,t=e.getMonth()+1,n=e.getFullYear(),a=this.typeList.length,r=this.activeTypeList.length,i=this.typeList.filter((function(e){if(!e.createTime)return!1;var a=new Date(e.createTime);return a.getFullYear()===n&&a.getMonth()+1===t})).length;this.stats={totalTypes:a,activeTypes:r,thisMonthTypes:i}},onRefresh:function(){var t=this;return(0,s.default)(i.default.mark((function n(){return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t.refreshing=!0,n.prev=1,n.next=4,t.initializeData();case 4:e.showToast({title:"刷新成功",icon:"success",duration:1500}),n.next=10;break;case 7:n.prev=7,n.t0=n["catch"](1),e.showToast({title:"刷新失败",icon:"none"});case 10:return n.prev=10,t.refreshing=!1,n.finish(10);case 13:case"end":return n.stop()}}),n,null,[[1,7,10,13]])})))()},loadMore:function(){},changeFilter:function(e){this.activeFilter=e},performSearch:function(){},clearSearch:function(){this.searchKeyword=""},openCreateModal:function(){this.showCreateModal=!0,this.editingType=null,this.resetCreateForm()},closeCreateModal:function(){this.showCreateModal=!1,this.editingType=null},resetCreateForm:function(){this.createForm={name:"",code:"",description:"",isActive:!0}},editType:function(e){this.editingType=e,this.createForm={name:e.name,code:e.code,description:e.description||"",isActive:e.isActive},this.showCreateModal=!0},deleteType:function(t){var n=this;e.showModal({title:"确认删除",content:'确定要删除荣誉类型"'.concat(t.name,'"吗？删除后不可恢复。'),success:function(){var r=(0,s.default)(i.default.mark((function r(o){var c;return i.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!o.confirm){r.next=18;break}return r.prev=1,r.next=4,a.callFunction({name:"honor-admin",data:{action:"deleteHonorType",data:{typeId:t._id}}});case 4:if(c=r.sent,0!==c.result.code){r.next=12;break}return e.showToast({title:"删除成功",icon:"success"}),r.next=9,n.loadTypeList();case 9:n.loadStats(),r.next=13;break;case 12:throw new Error(c.result.message||"删除失败");case 13:r.next=18;break;case 15:r.prev=15,r.t0=r["catch"](1),e.showToast({title:r.t0.message||"删除失败",icon:"none"});case 18:case"end":return r.stop()}}),r,null,[[1,15]])})));return function(e){return r.apply(this,arguments)}}()})},onActiveChange:function(e){this.createForm.isActive=e.detail.value},saveType:function(){var t=this;return(0,s.default)(i.default.mark((function n(){var r,o,c;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t.validateCreateForm()){n.next=2;break}return n.abrupt("return");case 2:return t.saving=!0,n.prev=3,r=t.editingType?"updateHonorType":"createHonorType",o={name:t.createForm.name,code:t.createForm.code,description:t.createForm.description,isActive:t.createForm.isActive},t.editingType&&(o.typeId=t.editingType._id),n.next=9,a.callFunction({name:"honor-admin",data:{action:r,data:o}});case 9:if(c=n.sent,0!==c.result.code){n.next=18;break}return e.showToast({title:t.editingType?"更新成功":"创建成功",icon:"success"}),t.closeCreateModal(),n.next=15,t.loadTypeList();case 15:t.loadStats(),n.next=19;break;case 18:throw new Error(c.result.message||"保存失败");case 19:n.next=24;break;case 21:n.prev=21,n.t0=n["catch"](3),e.showToast({title:n.t0.message||"保存失败",icon:"none"});case 24:return n.prev=24,t.saving=!1,n.finish(24);case 27:case"end":return n.stop()}}),n,null,[[3,21,24,27]])})))()},validateCreateForm:function(){return this.createForm.name.trim()?this.createForm.code.trim()?!!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(this.createForm.code)||(e.showToast({title:"类型代码格式不正确",icon:"none"}),!1):(e.showToast({title:"请输入类型代码",icon:"none"}),!1):(e.showToast({title:"请输入类型名称",icon:"none"}),!1)},formatTime:function(e){if(!e)return"";var t=new Date(e);return"".concat(t.getFullYear(),"-").concat((t.getMonth()+1).toString().padStart(2,"0"),"-").concat(t.getDate().toString().padStart(2,"0"))},goBack:function(){e.navigateBack()},generateCode:function(){if(this.createForm.name.trim()){var t={"优秀员工":"excellent_employee","敬业奉献":"dedication_award","技术革新":"technology_innovator","工艺优化":"process_optimization","节能降耗":"energy_saving_award","团队协作":"team_spirit","环保标兵":"environmental_pioneer","安全生产":"safety_production","质量标兵":"quality_champion","创新发明":"innovation_award","服务明星":"service_star","进步奖":"progress_award","突出贡献":"outstanding_contribution","年度标兵":"annual_model","季度之星":"quarterly_star","月度优秀":"monthly_excellent"},n=this.createForm.name.trim();if(t[n])this.createForm.code=t[n];else{for(var a=0,r=Object.entries(t);a<r.length;a++){var i=(0,c.default)(r[a],2),o=i[0],s=i[1];if(n.includes(o)||o.includes(n))return void(this.createForm.code=s)}var u=this.convertToPinyin(n);this.createForm.code=u||"custom_award"}}else e.showToast({title:"请先输入类型名称",icon:"none"})},convertToPinyin:function(e){var t,n,a=(t={"优":"you","秀":"xiu","员":"yuan","工":"gong","敬":"jing","业":"ye","奉":"feng","献":"xian","技":"ji","术":"shu","革":"ge","新":"xin"},(0,o.default)(t,"工","gong"),(0,o.default)(t,"艺","yi"),(0,o.default)(t,"优","you"),(0,o.default)(t,"化","hua"),(0,o.default)(t,"节","jie"),(0,o.default)(t,"能","neng"),(0,o.default)(t,"降","jiang"),(0,o.default)(t,"耗","hao"),(0,o.default)(t,"团","tuan"),(0,o.default)(t,"队","dui"),(0,o.default)(t,"协","xie"),(0,o.default)(t,"作","zuo"),(0,o.default)(t,"环","huan"),(0,o.default)(t,"保","bao"),(0,o.default)(t,"标","biao"),(0,o.default)(t,"兵","bing"),(0,o.default)(t,"安","an"),(0,o.default)(t,"全","quan"),(0,o.default)(t,"生","sheng"),(0,o.default)(t,"产","chan"),(0,o.default)(t,"质","zhi"),(0,o.default)(t,"量","liang"),(0,o.default)(t,"创","chuang"),(0,o.default)(t,"发","fa"),(0,o.default)(t,"明","ming"),(0,o.default)(t,"服","fu"),(0,o.default)(t,"务","wu"),(0,o.default)(t,"明","ming"),(0,o.default)(t,"星","xing"),(0,o.default)(t,"进","jin"),(0,o.default)(t,"步","bu"),(0,o.default)(t,"奖","award"),(0,o.default)(t,"突","tu"),(0,o.default)(t,"出","chu"),(0,o.default)(t,"贡","gong"),(0,o.default)(t,"献","xian"),(0,o.default)(t,"年","nian"),(0,o.default)(t,"度","du"),(0,o.default)(t,"季","ji"),(0,o.default)(t,"月","yue"),t),r="",i=u(e);try{for(i.s();!(n=i.n()).done;){var c=n.value;a[c]&&(r+=a[c]+"_")}}catch(s){i.e(s)}finally{i.f()}return r.slice(0,-1)},onNameChange:function(){this.createForm.name.trim()?this.generateCode():this.createForm.code=""}}};t.default=d}).call(this,n("df3c")["default"],n("861b")["uniCloud"])},"4b95":function(e,t,n){"use strict";(function(e,t){var a=n("47a9");n("357b"),n("861b");a(n("3240"));var r=a(n("5a96"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(r.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"5a96":function(e,t,n){"use strict";n.r(t);var a=n("716c"),r=n("f3bd");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("050f");var o=n("828b"),c=Object(o["a"])(r["default"],a["b"],a["c"],!1,null,"57b3a6d1",null,!1,a["a"],void 0);t["default"]=c.exports},"716c":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return a}));var a={uniIcons:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(n.bind(null,"6ddf"))},uniEasyinput:function(){return n.e("uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput").then(n.bind(null,"6cf4"))}},r=function(){var e=this,t=e.$createElement,n=(e._self._c,e.typeList.length),a=e.activeTypeList.length,r=e.inactiveTypeList.length,i=e.__map(e.filteredTypeList,(function(t,n){var a=e.__get_orig(t),r=e.formatTime(t.createTime);return{$orig:a,m0:r}})),o=e.filteredTypeList.length,c=e.showCreateModal?e.createForm.name.length:null,s=e.showCreateModal?e.createForm.description.length:null;e.$mp.data=Object.assign({},{$root:{g0:n,g1:a,g2:r,l0:i,g3:o,g4:c,g5:s}})},i=[]},9872:function(e,t,n){},f3bd:function(e,t,n){"use strict";n.r(t);var a=n("0ff3"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a}},[["4b95","common/runtime","common/vendor"]]]);