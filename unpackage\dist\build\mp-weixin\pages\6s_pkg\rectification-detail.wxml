<view class="page-container data-v-5453c2e4"><block wx:if="{{loading}}"><view class="loading-container data-v-5453c2e4"><view class="loading-content data-v-5453c2e4"><view class="loading-spinner data-v-5453c2e4"></view><text class="loading-text data-v-5453c2e4">加载整改任务详情中...</text></view></view></block><block wx:else><block wx:if="{{loadError}}"><view class="error-container data-v-5453c2e4"><view class="error-content data-v-5453c2e4"><uni-icons vue-id="cb8a30f4-1" type="info" size="48" color="#FF3B30" class="data-v-5453c2e4" bind:__l="__l"></uni-icons><text class="error-text data-v-5453c2e4">{{loadError}}</text><button data-event-opts="{{[['tap',[['retryLoad',['$event']]]]]}}" class="retry-button data-v-5453c2e4" bindtap="__e">重新加载</button></view></view></block><block wx:else><view class="card data-v-5453c2e4"><view class="card-header data-v-5453c2e4"><view class="header-content data-v-5453c2e4"><view class="card-title data-v-5453c2e4">整改任务详情</view><view class="card-subtitle data-v-5453c2e4">{{taskInfo.area+" - "+(taskInfo.isPublic?'公共责任区':'固定责任区')}}</view></view></view><view class="card-body data-v-5453c2e4"><view class="task-info data-v-5453c2e4"><view class="info-item data-v-5453c2e4"><text class="info-label data-v-5453c2e4">状态：</text><view class="{{['status-badge','data-v-5453c2e4','status-'+taskInfo.status]}}">{{computedData.statusText}}</view></view><view class="info-item data-v-5453c2e4"><text class="info-label data-v-5453c2e4">问题类别：</text><text class="info-value data-v-5453c2e4">{{computedData.categoryText}}</text></view><view class="info-item data-v-5453c2e4"><text class="info-label data-v-5453c2e4">问题描述：</text><text class="info-value data-v-5453c2e4">{{taskInfo.problemDescription}}</text></view><view class="info-item data-v-5453c2e4"><text class="info-label data-v-5453c2e4">发现时间：</text><text class="info-value data-v-5453c2e4">{{$root.m0}}</text></view><view class="info-item data-v-5453c2e4"><text class="info-label data-v-5453c2e4">检查员：</text><text class="info-value data-v-5453c2e4">{{taskInfo.inspector}}</text></view><block wx:if="{{taskInfo.assignee_name}}"><view class="info-item data-v-5453c2e4"><text class="info-label data-v-5453c2e4">负责人：</text><text class="info-value data-v-5453c2e4">{{taskInfo.assignee_name}}</text></view></block><block wx:if="{{computedData.isCompleted&&taskInfo.completedDate}}"><view class="info-item data-v-5453c2e4"><text class="info-label data-v-5453c2e4">完成时间：</text><text class="info-value data-v-5453c2e4">{{$root.m1}}</text></view></block><block wx:else><block wx:if="{{!computedData.isCompleted&&taskInfo.deadline}}"><view class="info-item data-v-5453c2e4"><text class="info-label data-v-5453c2e4">截止时间：</text><text class="info-value data-v-5453c2e4">{{$root.m2}}</text></view></block></block><block wx:if="{{taskInfo.status==='in_progress'&&taskInfo.startDate}}"><view class="info-item data-v-5453c2e4"><text class="info-label data-v-5453c2e4">开始时间：</text><text class="info-value data-v-5453c2e4">{{$root.m3}}</text></view></block></view></view></view><block wx:if="{{$root.g0}}"><view class="card data-v-5453c2e4"><view class="card-header data-v-5453c2e4"><view class="card-title data-v-5453c2e4">问题照片</view><text class="photo-count data-v-5453c2e4">{{$root.g1+"张"}}</text></view><view class="card-body data-v-5453c2e4"><view class="photo-grid data-v-5453c2e4"><block wx:for="{{taskInfo.photos}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['previewPhoto',[index,'problem']]]]]}}" class="photo-item data-v-5453c2e4" bindtap="__e"><image src="{{photo.url||photo}}" mode="aspectFill" class="data-v-5453c2e4"></image><view class="photo-label data-v-5453c2e4">问题</view></view></block></view></view></view></block><block wx:if="{{taskInfo.rectificationDescription}}"><view class="card data-v-5453c2e4"><view class="card-header data-v-5453c2e4"><view class="card-title data-v-5453c2e4">整改说明</view></view><view class="card-body data-v-5453c2e4"><text class="description-text data-v-5453c2e4">{{taskInfo.rectificationDescription}}</text></view></view></block><block wx:if="{{$root.g2}}"><view class="card data-v-5453c2e4"><view class="card-header data-v-5453c2e4"><view class="card-title data-v-5453c2e4">整改后照片</view><text class="photo-count data-v-5453c2e4">{{$root.g3+"张"}}</text></view><view class="card-body data-v-5453c2e4"><view class="photo-grid data-v-5453c2e4"><block wx:for="{{taskInfo.completionPhotos}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['previewPhoto',[index,'completion']]]]]}}" class="photo-item data-v-5453c2e4" bindtap="__e"><image src="{{photo.url||photo}}" mode="aspectFill" class="data-v-5453c2e4"></image><view class="photo-label completion data-v-5453c2e4">整改后</view></view></block></view></view></view></block><block wx:if="{{taskInfo.reviewResult||taskInfo.reviewComments}}"><view class="card data-v-5453c2e4"><view class="card-header data-v-5453c2e4"><view class="card-title data-v-5453c2e4">复查信息</view></view><view class="card-body data-v-5453c2e4"><view class="review-info data-v-5453c2e4"><block wx:if="{{taskInfo.reviewResult}}"><view class="info-item data-v-5453c2e4"><text class="info-label data-v-5453c2e4">复查结果：</text><view class="{{['review-result-badge','data-v-5453c2e4','review-'+taskInfo.reviewResult]}}">{{computedData.reviewResultText}}</view></view></block><block wx:if="{{taskInfo.reviewerName}}"><view class="info-item data-v-5453c2e4"><text class="info-label data-v-5453c2e4">复查人：</text><text class="info-value data-v-5453c2e4">{{taskInfo.reviewerName}}</text></view></block><block wx:if="{{taskInfo.reviewDate}}"><view class="info-item data-v-5453c2e4"><text class="info-label data-v-5453c2e4">复查时间：</text><text class="info-value data-v-5453c2e4">{{$root.m4}}</text></view></block><block wx:if="{{taskInfo.reviewComments}}"><view class="info-item data-v-5453c2e4"><text class="info-label data-v-5453c2e4">复查意见：</text><text class="info-value data-v-5453c2e4">{{taskInfo.reviewComments}}</text></view></block></view><block wx:if="{{$root.g4}}"><view class="review-photos-section data-v-5453c2e4"><view class="review-photos-header data-v-5453c2e4"><text class="review-photos-title data-v-5453c2e4">复查照片</text><text class="photo-count data-v-5453c2e4">{{$root.g5+"张"}}</text></view><view class="photo-grid data-v-5453c2e4"><block wx:for="{{taskInfo.reviewPhotos}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['previewPhoto',[index,'review']]]]]}}" class="photo-item data-v-5453c2e4" bindtap="__e"><image src="{{photo.url||photo}}" mode="aspectFill" class="data-v-5453c2e4"></image><view class="photo-label review data-v-5453c2e4">复查</view></view></block></view></view></block></view></view></block></block></block><view class="button-container data-v-5453c2e4"><button data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="primary-button data-v-5453c2e4" bindtap="__e">返回</button></view></view>