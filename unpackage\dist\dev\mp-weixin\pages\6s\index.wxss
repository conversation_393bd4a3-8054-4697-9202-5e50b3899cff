@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 防止水平滚动的全局样式 */
/* uni-icons样式优化 */
.sixs-container.data-v-96da5e8e {
  min-height: 100vh;
  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
  width: 100%;
  overflow-x: hidden;
  /* 防止水平滚动 */
  box-sizing: border-box;
}
.top-tabs.data-v-96da5e8e {
  display: flex;
  background: #FFFFFF;
  border-bottom: 1px solid #E5E5E5;
}
.top-tabs .tab-item.data-v-96da5e8e {
  flex: 1;
  text-align: center;
  padding: 15px 0;
  font-size: 15px;
  color: #666;
  position: relative;
}
.top-tabs .tab-item.active.data-v-96da5e8e {
  color: #007AFF;
  font-weight: 500;
}
.top-tabs .tab-item.active.data-v-96da5e8e::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 30px;
  height: 2px;
  background: #007AFF;
}
.content-container.data-v-96da5e8e {
  width: 100%;
  max-width: 100%;
  /* 移除95%限制，使用全宽 */
  margin: 0;
  padding: 0;
  /* 移除内边距，由子元素控制 */
  position: relative;
  z-index: 5;
  box-sizing: border-box;
  overflow-x: hidden;
  /* 防止水平滚动 */
}
.card-body.data-v-96da5e8e {
  padding: 20px;
  position: relative;
}
.card-body.data-v-96da5e8e::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 122, 255, 0.3), transparent);
}
.tab-content.data-v-96da5e8e {
  padding-bottom: 20px;
}
.section.data-v-96da5e8e {
  background: #FFFFFF;
  border-radius: 12px;
  margin: 16px 12px;
  /* 减少左右边距，防止超出屏幕 */
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-sizing: border-box;
}
.section-header.data-v-96da5e8e {
  padding: 24px 24px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}
.section-header .header-content.data-v-96da5e8e {
  display: flex;
  flex-direction: row;
  /* 水平排列 */
  align-items: baseline;
  /* 基线对齐，保持文字对齐 */
  justify-content: space-between;
  /* 左右对齐 */
}
.section-header .section-title.data-v-96da5e8e {
  font-size: 17px;
  font-weight: 600;
  color: #1D1D1F;
  margin-bottom: 0;
  /* 移除margin，使用gap控制 */
}
.section-header .section-subtitle.data-v-96da5e8e {
  font-size: 13px;
  color: #8E8E93;
}
/* 快捷操作样式 - 完全按照demo实现 */
.quick-actions-header.data-v-96da5e8e {
  padding: 24px 24px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}
.quick-actions-header .header-content.data-v-96da5e8e {
  display: flex;
  flex-direction: row;
  /* 水平排列 */
  align-items: baseline;
  /* 基线对齐 */
  justify-content: space-between;
  /* 左右对齐 */
}
.quick-actions-header .quick-actions-title.data-v-96da5e8e {
  font-size: 17px;
  font-weight: 600;
  color: #1D1D1F;
  display: block;
  margin-bottom: 0;
  /* 移除margin */
}
.quick-actions-header .quick-actions-subtitle.data-v-96da5e8e {
  font-size: 13px;
  color: #8E8E93;
  display: block;
}
.quick-actions-grid.data-v-96da5e8e {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  padding: 16px;
  /* 减少内边距，防止超出 */
  box-sizing: border-box;
}
.quick-action-btn.data-v-96da5e8e {
  display: flex;
  flex-direction: row;
  /* 水平布局 */
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  /* 进一步减少内边距 */
  border-radius: 8px;
  transition: all 0.2s ease;
  height: 36px;
  /* 进一步降低高度，更扁 */
  min-width: 0;
  /* 允许文字换行 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  /* 更轻柔的阴影，与其他卡片一致 */
  border: none;
  /* 移除边框 */
  /* 按钮内容容器 */
  /* uni-icons 图标样式 */
}
.quick-action-btn .btn-content.data-v-96da5e8e {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  /* 使用gap控制图标和文字间距 */
}
.quick-action-btn .uni-icons.data-v-96da5e8e {
  flex-shrink: 0;
}
.quick-action-btn .btn-text.data-v-96da5e8e {
  font-size: 14px;
  /* 适配更低的按钮高度 */
  font-weight: 600;
  color: white;
  text-align: center;
  line-height: 1.2;
}
.quick-action-btn.primary.data-v-96da5e8e {
  background: #007AFF;
  /* 主题蓝色，突出常用功能 */
}
.quick-action-btn.warning.data-v-96da5e8e {
  background: #F59E0B;
  /* 橙黄色，更接近截图中的"责任区检查"按钮 */
}
.quick-action-btn.success.data-v-96da5e8e {
  background: #10B981;
  /* 绿色，更接近截图中的"上传照片"按钮 */
}
.quick-action-btn.purple.data-v-96da5e8e {
  background: #8B5CF6;
  /* 紫色，更接近截图中的"清理历史"按钮 */
}
.quick-action-btn.data-v-96da5e8e:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  opacity: 0.8;
}
/* 新的概览样式 - 按照设计图 */
.overview-header.data-v-96da5e8e {
  padding: 24px 24px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}
.overview-header .header-content.data-v-96da5e8e {
  display: flex;
  flex-direction: row;
  /* 水平排列 */
  align-items: baseline;
  /* 基线对齐 */
  justify-content: space-between;
  /* 左右对齐 */
}
.overview-header .overview-title.data-v-96da5e8e {
  font-size: 17px;
  font-weight: 600;
  color: #1D1D1F;
  margin-bottom: 0;
  /* 移除margin */
  display: block;
}
.overview-header .overview-subtitle.data-v-96da5e8e {
  font-size: 13px;
  color: #8E8E93;
  display: block;
}
.overview-stats.data-v-96da5e8e {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0;
  padding: 12px 16px;
  /* 减少上下内边距，保持左右间距 */
}
.overview-stats .stat-item.data-v-96da5e8e {
  text-align: center;
  padding: 8px 6px;
  /* 减少内边距，降低高度间距 */
}
.overview-stats .stat-item .stat-number.data-v-96da5e8e {
  display: block;
  font-size: 22px;
  /* 进一步减少到22px，更接近demo */
  font-weight: 700;
  margin-bottom: 2px;
  /* 减少数字和标签间距 */
  line-height: 1;
}
.overview-stats .stat-item .stat-number.blue.data-v-96da5e8e {
  color: #007AFF;
}
.overview-stats .stat-item .stat-number.green.data-v-96da5e8e {
  color: #34C759;
}
.overview-stats .stat-item .stat-number.orange.data-v-96da5e8e {
  color: #FF9500;
}
.overview-stats .stat-item .stat-number.red.data-v-96da5e8e {
  color: #FF3B30;
}
.overview-stats .stat-item .stat-label.data-v-96da5e8e {
  font-size: 11px;
  /* 进一步减少到11px，更精致 */
  color: #8E8E93;
  font-weight: 500;
}
.header-decoration.data-v-96da5e8e {
  display: flex;
  gap: 4px;
  align-items: center;
}
.header-decoration .decoration-dot.data-v-96da5e8e {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  -webkit-animation: pulse-data-v-96da5e8e 2s ease-in-out infinite;
          animation: pulse-data-v-96da5e8e 2s ease-in-out infinite;
}
.header-decoration .decoration-dot.data-v-96da5e8e:nth-child(2) {
  -webkit-animation-delay: 0.3s;
          animation-delay: 0.3s;
}
.header-decoration .decoration-dot.data-v-96da5e8e:nth-child(3) {
  -webkit-animation-delay: 0.6s;
          animation-delay: 0.6s;
}
@-webkit-keyframes pulse-data-v-96da5e8e {
0%, 100% {
    opacity: 0.4;
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    opacity: 1;
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
}
@keyframes pulse-data-v-96da5e8e {
0%, 100% {
    opacity: 0.4;
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    opacity: 1;
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
}
/* 图标容器样式优化 */
.activity-icon.data-v-96da5e8e, .reminder-icon.data-v-96da5e8e {
  display: flex;
  align-items: center;
  justify-content: center;
}
/* 最新动态样式 */
.activity-list.data-v-96da5e8e {
  padding: 0 20px 20px;
}
.activity-item.data-v-96da5e8e {
  display: flex;
  align-items: flex-start;
  padding: 16px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}
.activity-item.data-v-96da5e8e:last-child {
  border-bottom: none;
}
.activity-item .activity-icon.data-v-96da5e8e {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}
.activity-item .activity-icon.success.data-v-96da5e8e {
  background: rgba(52, 199, 89, 0.15);
}
.activity-item .activity-icon.warning.data-v-96da5e8e {
  background: rgba(255, 149, 0, 0.15);
}
.activity-item .activity-icon.info.data-v-96da5e8e {
  background: rgba(0, 122, 255, 0.15);
}
.activity-item .activity-content.data-v-96da5e8e {
  flex: 1;
}
.activity-item .activity-content .activity-title.data-v-96da5e8e {
  font-size: 15px;
  color: #1D1D1F;
  line-height: 1.4;
  margin-bottom: 4px;
  display: block;
  font-weight: 600;
  /* 使用粗体，符合demo */
}
.activity-item .activity-content .activity-time.data-v-96da5e8e {
  font-size: 13px;
  color: #8E8E93;
}
/* 加载状态样式 */
.loading-container.data-v-96da5e8e {
  padding: 40rpx 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.loading-content.data-v-96da5e8e {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}
.loading-spinner.data-v-96da5e8e {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #E5E7EB;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  -webkit-animation: spin-data-v-96da5e8e 1s linear infinite;
          animation: spin-data-v-96da5e8e 1s linear infinite;
}
@-webkit-keyframes spin-data-v-96da5e8e {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin-data-v-96da5e8e {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.loading-text.data-v-96da5e8e {
  font-size: 28rpx;
  color: #8E8E93;
}
/* 错误状态样式 */
.error-container.data-v-96da5e8e {
  padding: 40rpx 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.error-content.data-v-96da5e8e {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}
.error-text.data-v-96da5e8e {
  font-size: 24rpx;
  color: #FF3B30;
  text-align: center;
}
.retry-btn.data-v-96da5e8e {
  padding: 12rpx 24rpx;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 24rpx;
}
.retry-btn.data-v-96da5e8e:active {
  opacity: 0.8;
}
/* 空状态样式 */
.empty-state.data-v-96da5e8e {
  padding: 40rpx 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}
.empty-text.data-v-96da5e8e {
  font-size: 24rpx;
  color: #8E8E93;
  text-align: center;
}
/* 智能提醒样式 - 完全按照demo的设计实现 */
.reminder-list.data-v-96da5e8e {
  padding: 16px;
}
.reminder-item.data-v-96da5e8e {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  margin-bottom: 12px;
  border-radius: 8px;
  /* demo风格的背景色 - 完全按照demo实现 */
}
.reminder-item.data-v-96da5e8e:last-child {
  margin-bottom: 0;
}
.reminder-item .reminder-icon.data-v-96da5e8e {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  /* 完全圆形 - demo中是w-6 h-6 */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
  /* 移除margin-top，确保完全垂直居中 */
}
.reminder-item .reminder-icon .icon-text.data-v-96da5e8e {
  font-size: 12px;
  font-weight: 600;
  line-height: 1;
  /* 确保文字垂直居中 */
  text-align: center;
}
.reminder-item .reminder-icon.info.data-v-96da5e8e {
  background: rgba(0, 122, 255, 0.2);
  /* demo中的bg-primary/20 */
}
.reminder-item .reminder-icon.info .icon-text.data-v-96da5e8e {
  color: #007AFF;
  /* demo中的text-primary */
}
.reminder-item .reminder-icon.warning.data-v-96da5e8e {
  background: rgba(255, 149, 0, 0.2);
  /* 橙色警告 */
}
.reminder-item .reminder-icon.warning .icon-text.data-v-96da5e8e {
  color: #FF9500;
}
.reminder-item .reminder-icon.danger.data-v-96da5e8e {
  background: rgba(255, 59, 48, 0.2);
  /* 红色危险 */
}
.reminder-item .reminder-icon.danger .icon-text.data-v-96da5e8e {
  color: #FF3B30;
}
.reminder-item .reminder-icon.success.data-v-96da5e8e {
  background: rgba(34, 197, 94, 0.2);
  /* 绿色背景 */
}
.reminder-item .reminder-icon.success .icon-text.data-v-96da5e8e {
  color: #22C55E;
  /* 绿色文字 */
}
.reminder-item .reminder-content.data-v-96da5e8e {
  flex: 1;
}
.reminder-item .reminder-content .reminder-title.data-v-96da5e8e {
  font-size: 14px;
  color: #1C1C1E;
  /* demo中的text-dark */
  font-weight: 600;
  /* 使用粗体，符合demo */
  margin-bottom: 4px;
  display: block;
}
.reminder-item .reminder-content .reminder-desc.data-v-96da5e8e {
  font-size: 13px;
  color: #636366;
  /* demo中的text-gray-600 */
  line-height: 1.4;
}
.reminder-item.info.data-v-96da5e8e {
  background: rgba(59, 130, 246, 0.05);
  /* demo中的bg-blue-50 */
}
.reminder-item.warning.data-v-96da5e8e {
  background: rgba(255, 149, 0, 0.05);
  /* 橙色背景 */
}
.reminder-item.danger.data-v-96da5e8e {
  background: rgba(255, 59, 48, 0.05);
  /* 红色背景 */
}
.reminder-item.success.data-v-96da5e8e {
  background: rgba(34, 197, 94, 0.05);
  /* 绿色背景 */
}
