{"version": 3, "sources": ["webpack:///D:/Xwzc/uni_modules/uni-id-pages/components/uni-id-pages-bind-mobile/uni-id-pages-bind-mobile.vue?3139", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/components/uni-id-pages-bind-mobile/uni-id-pages-bind-mobile.vue?71d3", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/components/uni-id-pages-bind-mobile/uni-id-pages-bind-mobile.vue?eca0", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/components/uni-id-pages-bind-mobile/uni-id-pages-bind-mobile.vue?a1fb", "uni-app:///uni_modules/uni-id-pages/components/uni-id-pages-bind-mobile/uni-id-pages-bind-mobile.vue", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/components/uni-id-pages-bind-mobile/uni-id-pages-bind-mobile.vue?7e37", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/components/uni-id-pages-bind-mobile/uni-id-pages-bind-mobile.vue?e2ee"], "names": ["emits", "computed", "data", "methods", "beforeGetphonenumber", "uni", "mask", "wx", "success", "resolve", "fail", "code", "uniCloud", "customUI", "reject", "console", "bindMobileByMpWeixin", "e", "uniIdCo", "open", "closeMe"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiJ;AACjJ;AAC4E;AACL;AACsC;;;AAG7G;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,8FAAM;AACR,EAAE,+GAAM;AACR,EAAE,wHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mHAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAknB,CAAgB,4oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACetoB;AACA;AACA;AAAA,eACA;EACAA;EACAC;EACAC;IACA;EACA;EACAC;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACAC;oBAAAC;kBAAA;kBACAC;oBACAC;sBAEAC;sBACAJ;oBACA;oBACAK;sBAEAH;wBACAC,gCAEA;0BAAA,IADAG;0BAEAC;4BACAC;0BACA;4BAAAF;0BAAA;4BACAF;0BACA;4BAEAK;0BACA;4BACAT;0BACA;wBACA;wBACAK;0BACAK;0BACAD;wBACA;sBACA;oBACA;kBACA;gBACA;cAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAE;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACAC;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChFA;AAAA;AAAA;AAAA;AAAqrC,CAAgB,2pCAAG,EAAC,C;;;;;;;;;;;ACAzsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-id-pages/components/uni-id-pages-bind-mobile/uni-id-pages-bind-mobile.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-id-pages-bind-mobile.vue?vue&type=template&id=612c1e01&scoped=true&\"\nvar renderjs\nimport script from \"./uni-id-pages-bind-mobile.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-id-pages-bind-mobile.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-id-pages-bind-mobile.vue?vue&type=style&index=0&id=612c1e01&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"612c1e01\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-id-pages/components/uni-id-pages-bind-mobile/uni-id-pages-bind-mobile.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-id-pages-bind-mobile.vue?vue&type=template&id=612c1e01&scoped=true&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-id-pages-bind-mobile.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-id-pages-bind-mobile.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<uni-popup ref=\"popup\" type=\"bottom\">\r\n\t\t<view class=\"box\">\r\n\t\t\t<text class=\"headBox\">绑定资料</text>\r\n\t\t\t<text class=\"tip\">将一键获取你的手机号码绑定你的个人资料</text>\r\n\t\t\t<view class=\"btnBox\">\r\n\t\t\t\t<text @click=\"closeMe\" class=\"close\">关闭</text>\r\n\t\t\t\t<button class=\"agree uni-btn\" type=\"primary\" open-type=\"getPhoneNumber\"\r\n\t\t\t\t\t@getphonenumber=\"bindMobileByMpWeixin\">获取</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</uni-popup>\r\n</template>\r\n\r\n<script>\r\n\tconst db = uniCloud.database();\r\n\tconst usersTable = db.collection('uni-id-users')\r\n\tconst uniIdCo = uniCloud.importObject(\"uni-id-co\")\r\n\texport default {\r\n\t\temits: ['success'],\r\n\t\tcomputed: {},\r\n\t\tdata() {\r\n\t\t\treturn {}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync beforeGetphonenumber() {\r\n\t\t\t\treturn await new Promise((resolve,reject)=>{\r\n\t\t\t\t\tuni.showLoading({ mask: true })\r\n\t\t\t\t\twx.checkSession({\r\n\t\t\t\t\t\tsuccess() {\r\n\r\n\t\t\t\t\t\t\tresolve()\r\n\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail() {\r\n\r\n\t\t\t\t\t\t\twx.login({\r\n\t\t\t\t\t\t\t\tsuccess({\r\n\t\t\t\t\t\t\t\t\tcode\r\n\t\t\t\t\t\t\t\t}) {\r\n\t\t\t\t\t\t\t\t\tuniCloud.importObject(\"uni-id-co\",{\r\n\t\t\t\t\t\t\t\t\t\tcustomUI:true\r\n\t\t\t\t\t\t\t\t\t}).loginByWeixin({code}).then(e=>{\r\n\t\t\t\t\t\t\t\t\t\tresolve()\r\n\t\t\t\t\t\t\t\t\t}).catch(e=>{\r\n\r\n\t\t\t\t\t\t\t\t\t\treject()\r\n\t\t\t\t\t\t\t\t\t}).finally(e=>{\r\n\t\t\t\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\tconsole.error(err);\r\n\t\t\t\t\t\t\t\t\treject()\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync bindMobileByMpWeixin(e) {\r\n\t\t\t\tif (e.detail.errMsg == \"getPhoneNumber:ok\") {\r\n\t\t\t\t\t//检查登录信息是否过期，否则通过重新登录刷新session_key\r\n\t\t\t\t\tawait this.beforeGetphonenumber()\r\n\t\t\t\t\tuniIdCo.bindMobileByMpWeixin(e.detail).then(e => {\r\n\t\t\t\t\t\tthis.$emit('success')\r\n\t\t\t\t\t}).finally(e => {\r\n\t\t\t\t\t\tthis.closeMe()\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.closeMe()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync open() {\r\n\t\t\t\tthis.$refs.popup.open()\r\n\t\t\t},\r\n\t\t\tcloseMe(e) {\r\n\t\t\t\tthis.$refs.popup.close()\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"@/uni_modules/uni-id-pages/common/login-page.scss\";\r\n\tview {\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\t.box {\r\n\t\tbackground-color: #FFFFFF;\r\n\t\theight: 200px;\r\n\t\twidth: 750rpx;\r\n\t\tflex-direction: column;\r\n\t\tborder-top-left-radius: 15px;\r\n\t\tborder-top-right-radius: 15px;\r\n\t}\r\n\r\n\t.headBox {\r\n\t\tpadding: 20rpx;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\ttext-align: left;\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #333333;\r\n\t\tmargin-left: 15rpx;\r\n\t}\r\n\r\n\t.tip {\r\n\t\tcolor: #666666;\r\n\t\ttext-align: left;\r\n\t\tjustify-content: center;\r\n\t\tmargin: 10rpx 30rpx;\r\n\t\tfont-size: 18px;\r\n\t}\r\n\r\n\t.btnBox {\r\n\t\tmargin-top: 45rpx;\r\n\t\tjustify-content: center;\r\n\t\tflex-direction: row;\r\n\t}\r\n\r\n\t.close,\r\n\t.agree {\r\n\t\ttext-align: center;\r\n\t\twidth: 200rpx;\r\n\t\theight: 80upx;\r\n\t\tline-height: 80upx;\r\n\t\tborder-radius: 5px;\r\n\t\tmargin: 0 20rpx;\r\n\t\tfont-size: 14px;\r\n\t}\r\n\r\n\t.close {\r\n\t\tcolor: #999999;\r\n\t\tborder-color: #EEEEEE;\r\n\t\tborder-style: solid;\r\n\t\tborder-width: 1px;\r\n\t\tbackground-color: #FFFFFF;\r\n\t}\r\n\r\n\t.close:active {\r\n\t\tcolor: #989898;\r\n\t\tbackground-color: #E2E2E2;\r\n\t}\r\n\r\n\t.agree {\r\n\t\tcolor: #FFFFFF;\r\n\t}\r\n\r\n\t/* #ifdef MP */\r\n\t.agree::after {\r\n\t\tborder: none;\r\n\t}\r\n\t/* #endif */\r\n\t\r\n\t.agree:active {\r\n\t\tbackground-color: #F5F5F6;\r\n\t}\r\n</style>\r\n", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-id-pages-bind-mobile.vue?vue&type=style&index=0&id=612c1e01&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-id-pages-bind-mobile.vue?vue&type=style&index=0&id=612c1e01&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775855260\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}