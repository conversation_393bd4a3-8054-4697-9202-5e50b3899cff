require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/employee-assignment"],{"139b":function(e,t,n){"use strict";(function(e,t){var i=n("47a9");n("357b"),n("861b");i(n("3240"));var s=i(n("a1f9"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(s.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"8d18":function(e,t,n){"use strict";n.d(t,"b",(function(){return s})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return i}));var i={uniIcons:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(n.bind(null,"6ddf"))},pEmptyState:function(){return n.e("components/p-empty-state/p-empty-state").then(n.bind(null,"9b76"))},uniPopup:function(){return n.e("uni_modules/uni-popup/components/uni-popup/uni-popup").then(n.bind(null,"a2b7"))}},s=function(){var e=this,t=e.$createElement,n=(e._self._c,e.loading?null:e.assignmentList.length),i=!e.loading&&n>0?e.__map(e.assignmentList,(function(t,n){var i=e.__get_orig(t),s=String(n+1).padStart(3,"0"),a=t.areas.length,r=e.getStatusText(t.status),o=t.updated_at&&t.updated_at!==t.assigned_at?e.formatDate(t.updated_at):null,c=t.updated_at&&t.updated_at!==t.assigned_at?null:e.formatDate(t.assigned_at);return{$orig:i,g1:s,g2:a,m0:r,m1:o,m2:c}})):null,s=e.__map(e.availableAreas,(function(t,n){var i=e.__get_orig(t),s=e.isAreaSelected(t),a=e.isAreaSelected(t);return{$orig:i,m3:s,m4:a}}));e.$mp.data=Object.assign({},{$root:{g0:n,l0:i,l1:s}})},a=[]},a1f9:function(e,t,n){"use strict";n.r(t);var i=n("8d18"),s=n("caed");for(var a in s)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return s[e]}))}(a);n("b64e");var r=n("828b"),o=Object(r["a"])(s["default"],i["b"],i["c"],!1,null,"ca5ce792",null,!1,i["a"],void 0);t["default"]=o.exports},b64e:function(e,t,n){"use strict";var i=n("c4d1"),s=n.n(i);s.a},bb9f:function(e,t,n){"use strict";(function(e){var i=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=i(n("7eb4")),a=i(n("7ca3")),r=i(n("af34")),o=i(n("ee10")),c=n("882c");function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){(0,a.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var d={name:"EmployeeAssignment",data:function(){return{assignmentList:[],employeeList:[],areaList:[],loading:!1,saving:!1,isEditing:!1,currentAssignment:null,selectedEmployee:null,selectedAreas:[],assignmentNote:"",searchKeyword:"",filteredEmployees:[]}},computed:{totalEmployees:function(){return this.employeeList.length},assignedEmployees:function(){return this.assignmentList.length},totalAreas:function(){return this.areaList.length},unassignedAreas:function(){if(this.loading||0===this.areaList.length)return 0;var e=this.assignmentList.reduce((function(e,t){return t.areas.forEach((function(t){var n=t._id||t.id;e.add(n)})),e}),new Set),t=this.areaList.filter((function(t){var n=t._id||t.id;return!e.has(n)})).length;return t},availableAreas:function(){var e=this;if(this.isEditing&&this.currentAssignment){var t=new Set(this.currentAssignment.areas.map((function(e){return e.id}))),n=this.assignmentList.filter((function(t){return t.id!==e.currentAssignment.id})).reduce((function(e,t){return t.areas.forEach((function(t){return e.add(t.id)})),e}),new Set);return this.areaList.filter((function(e){return t.has(e.id)||!n.has(e.id)}))}var i=this.assignmentList.reduce((function(e,t){return t.areas.forEach((function(t){return e.add(t.id)})),e}),new Set);return this.areaList.filter((function(e){return!i.has(e.id)}))}},onLoad:function(){this.loadData()},methods:{loadData:function(){var t=this;return(0,o.default)(s.default.mark((function n(){return s.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,t.loading=!0,n.next=4,Promise.all([t.loadEmployees(),t.loadAreas(),t.loadAssignments()]);case 4:n.next=10;break;case 6:n.prev=6,n.t0=n["catch"](0),console.error("加载数据失败：",n.t0),e.showToast({title:"加载失败",icon:"none"});case 10:return n.prev=10,t.loading=!1,n.finish(10);case 13:case"end":return n.stop()}}),n,null,[[0,6,10,13]])})))()},loadEmployees:function(){var e=this;return(0,o.default)(s.default.mark((function t(){var n;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,c.callCloudFunction)("hygiene-assignments",{action:"getEmployeeList",data:{pageSize:200}});case 3:n=t.sent,e.employeeList=n.data.list||[],e.filteredEmployees=(0,r.default)(e.employeeList),t.next=13;break;case 8:t.prev=8,t.t0=t["catch"](0),console.error("加载员工数据失败：",t.t0),e.employeeList=[],e.filteredEmployees=[];case 13:case"end":return t.stop()}}),t,null,[[0,8]])})))()},getCurrentUserId:function(){try{var t=e.getStorageSync("uni-id-pages-userInfo");return t&&t._id||""}catch(n){return""}},convertRoleToDisplay:function(e){var t={admin:"管理员",responsible:"负责人",reviser:"发布人",supervisor:"主管",PM:"副厂长",GM:"厂长",logistics:"后勤员",dispatch:"调度员",Integrated:"综合员",operator:"设备员",technician:"工艺员",mechanic:"技术员",user:"普通员工"};return Array.isArray(e)?e.map((function(e){return t[e]||e})).join("、"):t[e]||e||"普通员工"},loadAreas:function(){var e=this;return(0,o.default)(s.default.mark((function t(){var n;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,c.callCloudFunction)("hygiene-area-management",{action:"getAreaList",data:{type:"fixed"}});case 3:n=t.sent,e.areaList=(n.data.list||[]).filter((function(e){return"active"===e.status})),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("加载责任区数据失败：",t.t0),e.areaList=[];case 11:case"end":return t.stop()}}),t,null,[[0,7]])})))()},loadAssignments:function(){var e=this;return(0,o.default)(s.default.mark((function t(){var n;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,c.callCloudFunction)("hygiene-assignments",{action:"getAssignmentList",data:{}});case 3:n=t.sent,e.assignmentList=(n.data||[]).sort((function(e,t){var n=new Date(e.created_at||e.assigned_at||0),i=new Date(t.created_at||t.assigned_at||0);return n-i})),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("加载分配记录失败：",t.t0),e.assignmentList=[];case 11:case"end":return t.stop()}}),t,null,[[0,7]])})))()},showAssignModal:function(){this.isEditing=!1,this.currentAssignment=null,this.resetForm(),this.$refs.assignPopup.open()},editAssignment:function(e){this.isEditing=!0,this.currentAssignment=e,this.selectedEmployee=e.employee,this.selectedAreas=(0,r.default)(e.areas),this.assignmentNote=e.note||"",this.$refs.assignPopup.open()},deleteAssignment:function(t){var n=this;e.showModal({title:"确认删除",content:"确定要删除 ".concat(t.employee.name," 的责任区分配吗？"),confirmText:"删除",confirmColor:"#FF3B30",success:function(e){e.confirm&&n.performDeleteAssignment(t)}})},performDeleteAssignment:function(t){var n=this;return(0,o.default)(s.default.mark((function i(){var a;return s.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.prev=0,i.next=3,(0,c.callCloudFunction)("hygiene-assignments",{action:"deleteAssignment",data:{id:t._id||t.id}});case 3:return a=n.assignmentList.findIndex((function(e){return(e._id||e.id)===(t._id||t.id)})),a>-1&&n.assignmentList.splice(a,1),i.next=7,n.loadAreas();case 7:e.showToast({title:"删除成功",icon:"success"}),i.next=14;break;case 10:i.prev=10,i.t0=i["catch"](0),console.error("删除失败：",i.t0),e.showToast({title:i.t0.message||"删除失败",icon:"none",duration:3e3});case 14:case"end":return i.stop()}}),i,null,[[0,10]])})))()},showEmployeeSelect:function(){this.$refs.employeeSelectPopup.open()},closeEmployeeSelect:function(){this.$refs.employeeSelectPopup.close()},selectEmployee:function(e){this.selectedEmployee=e,this.closeEmployeeSelect()},filterEmployees:function(){var e=this.searchKeyword.toLowerCase();this.filteredEmployees=e?this.employeeList.filter((function(t){return t.name.toLowerCase().includes(e)||t.role&&t.role.toLowerCase().includes(e)})):(0,r.default)(this.employeeList)},toggleAreaSelection:function(e){var t=e._id||e.id,n=this.selectedAreas.findIndex((function(e){return(e._id||e.id)===t}));n>-1?this.selectedAreas.splice(n,1):this.selectedAreas.push(e)},isAreaSelected:function(e){var t=e._id||e.id;return this.selectedAreas.some((function(e){return(e._id||e.id)===t}))},submitAssignment:function(){var t=this;return(0,o.default)(s.default.mark((function n(){var i,a,r,o,u,d,f,p,m;return s.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t.validateAssignment()){n.next=2;break}return n.abrupt("return");case 2:if(n.prev=2,t.saving=!0,i={employee_id:t.selectedEmployee.id,employee_name:t.selectedEmployee.name,employee_info:{name:t.selectedEmployee.name,role:t.selectedEmployee.role,avatar:t.selectedEmployee.avatar,phone:t.selectedEmployee.phone},area_ids:t.selectedAreas.map((function(e){return e._id||e.id})),area_names:t.selectedAreas.map((function(e){return e.name})),areas_info:t.selectedAreas.map((function(e){return{id:e._id||e.id,name:e.name,location:e.location,description:e.description}})),note:t.assignmentNote,status:"active"},!t.isEditing){n.next=11;break}return n.next=8,(0,c.callCloudFunction)("hygiene-assignments",{action:"updateAssignment",data:l({id:t.currentAssignment._id||t.currentAssignment.id},i)});case 8:a=n.sent,n.next=14;break;case 11:return n.next=13,(0,c.callCloudFunction)("hygiene-assignments",{action:"createAssignment",data:i});case 13:a=n.sent;case 14:return t.isEditing?(r=t.assignmentList.findIndex((function(e){return(e._id||e.id)===(t.currentAssignment._id||t.currentAssignment.id)})),r>-1&&(t.assignmentList[r]=l(l({},a.data||{}),{},{employee:(null===(o=a.data)||void 0===o?void 0:o.employee_info)||t.selectedEmployee,areas:(null===(u=a.data)||void 0===u?void 0:u.areas_info)||t.selectedAreas}))):(m=l(l({},a.data||{}),{},{employee:(null===(d=a.data)||void 0===d?void 0:d.employee_info)||t.selectedEmployee,areas:(null===(f=a.data)||void 0===f?void 0:f.areas_info)||t.selectedAreas,created_at:(null===(p=a.data)||void 0===p?void 0:p.created_at)||(new Date).toISOString()}),t.assignmentList.push(m)),n.next=17,t.loadAreas();case 17:e.showToast({title:t.isEditing?"保存成功":"分配成功",icon:"success"}),t.closeAssignModal(),n.next=25;break;case 21:n.prev=21,n.t0=n["catch"](2),console.error("保存失败：",n.t0),e.showToast({title:n.t0.message||"保存失败",icon:"none",duration:3e3});case 25:return n.prev=25,t.saving=!1,n.finish(25);case 28:case"end":return n.stop()}}),n,null,[[2,21,25,28]])})))()},validateAssignment:function(){var t=this;if(!this.selectedEmployee)return e.showToast({title:"请选择员工",icon:"none"}),!1;if(0===this.selectedAreas.length)return e.showToast({title:"请选择至少一个责任区",icon:"none"}),!1;var n=this.assignmentList.find((function(e){return e.employee.id===t.selectedEmployee.id&&(!t.isEditing||e.id!==t.currentAssignment.id)}));return!n||(e.showToast({title:"该员工已有责任区分配",icon:"none"}),!1)},resetForm:function(){this.selectedEmployee=null,this.selectedAreas=[],this.assignmentNote="",this.searchKeyword="",this.filteredEmployees=(0,r.default)(this.employeeList)},closeAssignModal:function(){this.$refs.assignPopup.close(),this.resetForm()},viewAreaDetail:function(t){e.showModal({title:t.name,content:"位置：".concat(t.location&&t.location.area||t.location||"未设置","\n描述：").concat(t.description||"无"),showCancel:!1})},getStatusText:function(e){return{active:"正常",inactive:"暂停",expired:"过期"}[e]||"未知"},formatDate:function(e){if(!e)return"";var t=new Date(e);return"".concat(t.getFullYear(),"-").concat(String(t.getMonth()+1).padStart(2,"0"),"-").concat(String(t.getDate()).padStart(2,"0"))}}};t.default=d}).call(this,n("df3c")["default"])},c4d1:function(e,t,n){},caed:function(e,t,n){"use strict";n.r(t);var i=n("bb9f"),s=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(a);t["default"]=s.a}},[["139b","common/runtime","common/vendor"]]]);