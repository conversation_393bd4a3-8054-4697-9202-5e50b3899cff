<template>
	<view class="qrcode-container">
		<!-- 删除自定义导航栏，使用系统导航栏 -->
		<view class="content">
			<!-- 加载状态 -->
			<view class="loading-box" v-if="loading">
				<uni-load-more status="loading" :content-text="loadingText"></uni-load-more>
			</view>
			
			<!-- 错误提示 -->
			<view class="error-box" v-else-if="errorMsg">
				<view class="error-icon">
					<text class="iconfont icon-warning"></text>
				</view>
				<view class="error-text">{{errorMsg}}</view>
				<view class="error-action">
					<button class="btn-retry" @click="loadPointDetail">重试</button>
				</view>
			</view>
			
			<!-- 二维码信息 -->
			<view class="qrcode-box" v-else>
				<view class="qrcode-info">
					<view class="qrcode-title">{{pointInfo.name || '未命名巡检点'}}</view>
					<view class="qrcode-subtitle">{{(pointInfo.location && pointInfo.location.address) || '无地址信息'}}</view>
					<view class="qrcode-id">ID: {{pointInfo._id}}</view>
				</view>
				
				<view class="qrcode-content">
					<!-- 二维码未启用提示 -->
					<view class="qrcode-disabled" v-if="!qrcodeEnabled">
						<view class="disabled-icon">
							<text class="iconfont icon-qrcode-disabled"></text>
						</view>
						<view class="disabled-text">此巡检点未启用二维码</view>
						<view class="disabled-action">
							<button class="btn-enable" @click="goToEdit" v-if="hasEditPermission">去启用</button>
						</view>
					</view>
					
					<!-- 二维码展示区域 -->
					<view class="qrcode-display" v-else>
						<view class="qrcode-canvas-container" :class="{ 'is-generating': generating }">
							<!-- 加载中显示 -->
							<view class="qrcode-loading" v-if="generating">
								<uni-load-more status="loading" :contentText="{ contentdown: '生成中...', contentrefresh: '生成中...' }"></uni-load-more>
							</view>
							
							<!-- 二维码组件 -->
							<uqrcode
								v-if="qrcodeEnabled && qrcodeContent"
								ref="uqrcode"
								:canvas-id="'qrcode-canvas'"
								class="qrcode-canvas"
								:value="qrcodeContent"
								:options="{
									size: 200,
									margin: 10,
									backgroundColor: '#ffffff',
									foregroundColor: '#000000',
									errorCorrectLevel: 'H',
									type: 'image'
								}"
								@complete="onQRCodeComplete"
							></uqrcode>
						</view>
						
						<view class="qrcode-meta" v-if="qrcodeGenerated">
							<view class="meta-item">
								<text class="meta-label">版本</text>
								<text class="meta-value">{{pointInfo.qrcode_version || 1}}</text>
							</view>
							<view class="meta-item">
								<text class="meta-label">生成时间</text>
								<text class="meta-value">{{formatDate(qrGeneratedTime)}}</text>
							</view>
						</view>
						
						<view class="qrcode-actions">
							<button class="btn-generate" @click="generateQrCode" v-if="!qrcodeGenerated" :disabled="generating">
								<text class="iconfont icon-qrcode"></text>
								<text>{{generating ? '生成中...' : '生成二维码'}}</text>
							</button>
							<button class="btn-regenerate" @click="regenerateQrCode" v-if="qrcodeGenerated" :disabled="generating">
								<text class="iconfont icon-refresh"></text>
								<text>{{generating ? '生成中...' : '重新生成'}}</text>
							</button>
							<button class="btn-save" @click="saveQrCode" v-if="qrcodeGenerated" :disabled="generating || saving">
								<text class="iconfont icon-download"></text>
								<text>{{saving ? '保存中...' : '保存到相册'}}</text>
							</button>
						</view>
						
						<view class="qrcode-help">
							<view class="help-title">
								<text class="iconfont icon-info"></text>
								<text>使用说明</text>
							</view>
							<view class="help-content">
								<text class="help-item">1. 生成二维码后，请保存备用</text>
								<text class="help-item">2. 打印后张贴在巡检点位置</text>
								<text class="help-item">3. 巡检员可通过扫描二维码进行精准打卡</text>
								<text class="help-item">4. 重新生成二维码将使旧二维码失效</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import QRCodeUtil from '@/utils/qrcode-utils.js';
import PatrolApi from '@/utils/patrol-api.js';

export default {
	data() {
		return {
			pointInfo: {},
			pointId: '',
			errorMsg: '',
			loading: true,
			loadingText: {
				loading: '加载中...',
				more: '加载更多...',
				noMore: '没有更多数据了'
			},
			qrcodeGenerated: false,
			qrcodeEnabled: false,
			currentTime: '',
			generating: false,
			regenerating: false,
			qrGeneratedTime: null,
			saving: false,
			hasEditPermission: true,
			qrcodeContent: '',
			isInitialLoad: true,
			needsNewQRCode: false,
		};
	},
	onLoad(options) {
		this.pointId = options.id;
		if (!this.pointId) {
			this.errorMsg = '缺少点位ID';
			this.loading = false;
			return;
		}
		this.loadPointDetail();
	},
	methods: {
		// 加载点位详情
		async loadPointDetail() {
			if (!this.pointId) {
				this.errorMsg = '缺少点位ID';
				this.loading = false;
				return;
			}
			
			this.loading = true;
			this.errorMsg = '';
			
			try {
				const res = await PatrolApi.getPointDetail(this.pointId);
				
				if (res.code === 0 && res.data) {
					this.pointInfo = res.data;
					this.qrcodeEnabled = !!this.pointInfo.qrcode_enabled;
					
					// 如果已经有二维码内容，直接显示
					if (this.qrcodeEnabled && this.pointInfo.qrcode_content) {
						this.qrcodeContent = this.pointInfo.qrcode_content;
						this.qrcodeGenerated = true;
						this.qrGeneratedTime = this.pointInfo.qrcode_generated_time;
					} else if (this.qrcodeEnabled) {
						// 如果启用了二维码但还没有内容，自动生成
						this.needsNewQRCode = true;
						await this.generateQrCode();
					}
				} else {
					this.errorMsg = res.message || '找不到点位信息';
				}
			} catch (e) {

				this.errorMsg = '加载点位详情失败: ' + (e.message || '未知错误');
			} finally {
				this.loading = false;
			}
		},
		
		// 生成二维码
		async generateQrCode() {
			try {
				if (!this.pointInfo || !this.pointInfo._id) {
					this.showError('无法生成二维码：点位信息不完整');
					return;
				}
				
				this.generating = true;
				this.errorMsg = '';
				
				// 始终重新生成二维码内容，使用最新的hash_key
				const qrContent = QRCodeUtil.getQRCodeData({
					...this.pointInfo,
					qrcode_content: null  // 强制重新生成
				}, {
					includeTimestamp: false  // 不包含时间戳
				});
				

				
				try {
					// 保存到数据库
					const updateResult = await PatrolApi.callPointFunction('updatePoint', { 
						data: {
							id: this.pointInfo._id,
							qrcode_content: qrContent,
							qrcode_generated_time: new Date().toISOString()
						}
					});
					
					if (updateResult.code === 0) {
						// 更新本地数据
						this.qrcodeContent = qrContent;
						this.qrcodeGenerated = true;
						this.qrGeneratedTime = new Date();
						
						uni.showToast({
							title: '二维码已生成',
							icon: 'success'
						});
					} else {
						throw new Error(updateResult.message || '保存失败');
					}
				} catch (err) {

					this.showError('保存二维码失败：' + (err.message || '未知错误'));
				}
				
			} catch (err) {

				this.showError('生成二维码时发生错误：' + (err.message || '未知错误'));
			} finally {
				this.generating = false;
			}
		},
		
		// 二维码生成完成回调
		async onQRCodeComplete(res) {

			if (res.success) {
				// 只有在以下情况才更新数据库：
				// 1. 手动点击生成/重新生成按钮 (!isInitialLoad)
				// 2. 自动生成新二维码 (needsNewQRCode)
				if (this.generating && (!this.isInitialLoad || this.needsNewQRCode)) {
					try {
						// 保存二维码内容到数据库
						const updateResult = await PatrolApi.callPointFunction('updatePoint', { 
							data: {
								id: this.pointInfo._id,
								qrcode_content: this.qrcodeContent,
								qrcode_generated_time: new Date().toISOString(),
								qrcode_enabled: true
							}
						});
						
						if (updateResult.code === 0) {
							this.qrcodeGenerated = true;
							this.qrGeneratedTime = new Date();
							this.needsNewQRCode = false;
							uni.hideLoading();
						} else {
							throw new Error(updateResult.message || '保存二维码失败');
						}
					} catch (err) {
	
						this.showError('保存二维码失败：' + (err.message || '未知错误'));
					}
				}
			} else {
				this.showError('生成二维码失败：' + (res.message || '未知错误'));
			}
			this.generating = false;
		},
		
		// 重新生成二维码
		async regenerateQrCode() {
			try {
				if (!this.pointInfo || !this.pointInfo._id) {

					uni.showToast({
						title: '点位信息不完整',
						icon: 'none'
					});
					return;
				}
				
				if (this.regenerating) {

					return;
				}
				
				this.regenerating = true;
				this.isInitialLoad = false;
				
				try {
					uni.showLoading({
						title: '更新中...'
					});
					
					// 先更新版本号
					const result = await QRCodeUtil.incrementQRCodeVersion(this.pointInfo._id);

					
					if (result && result.updated) {
						// 更新本地点位信息
						this.pointInfo = result.result;  // 使用返回的最新点位信息
						
						// 重新生成二维码
						await this.generateQrCode();
						
						uni.showToast({
							title: '二维码已更新',
							icon: 'success'
						});
					} else {
						throw new Error('更新失败，请重试');
					}
				} catch (e) {

					uni.showToast({
						title: e.message || '更新失败，请重试',
						icon: 'none',
						duration: 2000
					});
				} finally {
					uni.hideLoading();
					this.regenerating = false;
				}
			} catch (e) {

				uni.showToast({
					title: '重新生成失败',
					icon: 'none'
				});
				this.regenerating = false;
			}
		},
		
		// 保存到相册
		async saveQrCode() {
			if (!this.qrcodeGenerated) {
				uni.showToast({
					title: '请先生成二维码',
					icon: 'none'
				});
				return;
			}
			
			this.saving = true;
			uni.showLoading({
				title: '保存中...'
			});
			
			try {
				// #ifdef H5
				// H5平台：直接获取canvas并触发下载
				try {
					// 获取二维码的canvas元素
					const canvas = this.$refs.uqrcode.$el.querySelector('canvas');
					if (!canvas) {
						throw new Error('未找到二维码canvas');
					}
					
					// 将canvas转换为blob
					canvas.toBlob((blob) => {
						if (!blob) {
							uni.showToast({ title: '保存失败', icon: 'none' });
							return;
						}
						
						// 创建下载链接
						const url = URL.createObjectURL(blob);
						const link = document.createElement('a');
						link.href = url;
						link.download = `${this.pointInfo.name || this.pointInfo._id}.png`;
						
						// 触发下载
						document.body.appendChild(link);
						link.click();
						document.body.removeChild(link);
						
						// 清理URL对象
						setTimeout(() => {
							URL.revokeObjectURL(url);
						}, 100);
						
						uni.showToast({
							title: '保存成功',
							icon: 'success'
						});
					}, 'image/png', 1.0);
					
				} catch (canvasError) {
					// 回退到原始的save方法
					await this.$refs.uqrcode.save({
						success: () => {
							uni.showToast({
								title: '已保存到相册',
								icon: 'success'
							});
						},
						fail: (err) => {
							let errorMsg = '保存失败';
							
							// 检查是否是权限问题
							if (err.errMsg && err.errMsg.includes('auth deny')) {
								errorMsg = '没有保存到相册的权限';
							}
							
							uni.showToast({
								title: errorMsg,
								icon: 'none'
							});
						}
					});
				}
				// #endif
				
				// #ifndef H5
				// 非H5平台使用原始方法
				await this.$refs.uqrcode.save({
					success: () => {
						uni.showToast({
							title: '已保存到相册',
							icon: 'success'
						});
					},
					fail: (err) => {
						let errorMsg = '保存失败';
						
						// 检查是否是权限问题
						if (err.errMsg && err.errMsg.includes('auth deny')) {
							errorMsg = '没有保存到相册的权限';
						}
						
						uni.showToast({
							title: errorMsg,
							icon: 'none'
						});
					}
				});
				// #endif
			} finally {
				uni.hideLoading();
				this.saving = false;
			}
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 前往编辑页
		goToEdit() {
			uni.navigateTo({
				url: `/pages/patrol_pkg/point/edit?id=${this.pointId}`
			});
		},
		
		// 格式化日期
		formatDate(date) {
			if (!date) return '';
			try {
				if (typeof date === 'string') {
					date = new Date(date);
				}
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');
				
				return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
			} catch (e) {

				return '日期格式错误';
			}
		},
		
		// 显示错误信息
		showError(message) {
			this.errorMsg = message;
			this.loading = false;
		}
	}
}
</script>

<style lang="scss">
.qrcode-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding-bottom: env(safe-area-inset-bottom);
	
	.content {
		padding: 20rpx;
		padding-top: 20rpx;
		
		.loading-box {
			margin-top: 100rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
		}
		
		.error-box {
			margin-top: 100rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			
			.error-icon {
				font-size: 100rpx;
				color: #ff5a5f;
				margin-bottom: 30rpx;
			}
			
			.error-text {
				color: #666;
				margin-bottom: 40rpx;
				text-align: center;
			}
			
			.btn-retry {
				font-size: 28rpx;
				color: #ffffff;
				background-color: #007aff;
				border-radius: 8rpx;
				padding: 12rpx 32rpx;
			}
		}
		
		.qrcode-box {
			.qrcode-info {
				background-color: #fff;
				padding: 40rpx;
				border-radius: 16rpx;
				margin-bottom: 20rpx;
				box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
				
				.qrcode-title {
					font-size: 40rpx;
					font-weight: 600;
					color: #333;
					margin-bottom: 16rpx;
				}
				
				.qrcode-subtitle {
					font-size: 28rpx;
					color: #666;
					margin-bottom: 12rpx;
				}
				
				.qrcode-id {
					font-size: 24rpx;
					color: #999;
					font-family: monospace;
				}
			}
			
			.qrcode-content {
				background-color: #fff;
				padding: 40rpx;
				border-radius: 16rpx;
				box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
				
				.qrcode-disabled {
					display: flex;
					flex-direction: column;
					align-items: center;
					padding: 80rpx 0;
					
					.disabled-icon {
						font-size: 120rpx;
						color: #999;
						margin-bottom: 30rpx;
					}
					
					.disabled-text {
						color: #666;
						margin-bottom: 40rpx;
						font-size: 32rpx;
					}
					
					.btn-enable {
						font-size: 28rpx;
						color: #ffffff;
						background-color: #007aff;
						border-radius: 8rpx;
						padding: 16rpx 48rpx;
					}
				}
				
				.qrcode-display {
					.qrcode-canvas-container {
						width: 280px;
						height: 280px;
						margin: 20px auto 40rpx;
						border-radius: 16rpx;
						background-color: #fff;
						box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.08);
						position: relative;
						box-sizing: border-box;
						display: flex;
						justify-content: center;
						align-items: center;
						padding: 20px;
						
						&.is-generating {
							background-color: #f8f8f8;
						}						
					
						.qrcode-loading {
							position: absolute;
							left: 0;
							top: 0;
							right: 0;
							bottom: 0;
							display: flex;
							justify-content: center;
							align-items: center;
							background-color: rgba(248,248,248,0.9);
							border-radius: 16rpx;
						}
					}
					
					.qrcode-meta {
						display: flex;
						flex-direction: column;
						gap: 16rpx;
						padding: 24rpx;
						background-color: #f8f8f8;
						border-radius: 12rpx;
						margin-bottom: 40rpx;
						
						.meta-item {
							display: flex;
							align-items: center;
							justify-content: space-between;
							
							.meta-label {
								font-size: 26rpx;
								color: #666;
							}
							
							.meta-value {
								font-size: 26rpx;
								color: #333;
								font-family: monospace;
							}
						}
					}
					
					.qrcode-actions {
						display: flex;
						flex-direction: column;
						gap: 20rpx;
						margin-bottom: 40rpx;
						
						button {
							display: flex;
							align-items: center;
							justify-content: center;
							gap: 12rpx;
							margin: 0;
							font-size: 30rpx;
							border-radius: 12rpx;
							height: 88rpx;
							line-height: 1;
							
							.iconfont {
								font-size: 36rpx;
							}
							
							&:active {
								transform: scale(0.98);
								transition: transform 0.2s;
							}
						}
						
						.btn-generate {
							background-color: #007aff;
							color: #ffffff;
							
							&:disabled {
								opacity: 0.6;
							}
						}
						
						.btn-regenerate {
							background-color: #ff9500;
							color: #ffffff;
							
							&:disabled {
								opacity: 0.6;
							}
						}
						
						.btn-save {
							background-color: #34c759;
							color: #ffffff;
							
							&:disabled {
								opacity: 0.6;
							}
						}
					}
					
					.qrcode-help {
						background-color: #f8f8f8;
						padding: 24rpx;
						border-radius: 12rpx;
						
						.help-title {
							display: flex;
							align-items: center;
							gap: 12rpx;
							margin-bottom: 20rpx;
							font-size: 28rpx;
							color: #333;
							font-weight: 500;
							
							.iconfont {
								color: #007aff;
							}
						}
						
						.help-content {
							display: flex;
							flex-direction: column;
							gap: 16rpx;
							
							.help-item {
								font-size: 26rpx;
								color: #666;
								line-height: 1.5;
							}
						}
					}
				}
			}
		}
	}
}
</style> 