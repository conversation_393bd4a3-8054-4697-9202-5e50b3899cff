{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/pages/userinfo/userinfo.vue?607c", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/pages/userinfo/userinfo.vue?f4e9", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/pages/userinfo/userinfo.vue?da8e", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/pages/userinfo/userinfo.vue?7d24", "uni-app:///uni_modules/uni-id-pages/pages/userinfo/userinfo.vue", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/pages/userinfo/userinfo.vue?2ffb", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/pages/userinfo/userinfo.vue?6f73"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "univerifyStyle", "authButton", "otherLogin<PERSON><PERSON>on", "hasPwd", "showLoginManage", "setNicknameIng", "computed", "userInfo", "realNameStatus", "isDefaultNickname", "nicknameDisplayText", "canChangeNickname", "onShow", "onLoad", "uniIdCo", "res", "methods", "login", "uni", "url", "complete", "logout", "mutations", "bindMobileSuccess", "changePassword", "handleNicknameClick", "bindMobile", "univerify", "success", "fail", "bindMobileBySmsCode", "setNickname", "title", "content", "showCancel", "confirmText", "nickname", "icon", "deactivate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>unt", "bind<PERSON>ield", "weixin", "alipay", "apple", "qq", "provider", "onlyAuthorize", "code", "duration", "realNameVerify"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,gWAEN;AACP,KAAK;AACL;AACA,aAAa,mWAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,4nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC2DtnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA;AAAA,eAKA;EACAC;IACA;MACAC;QACAC;UACA;QACA;;QACAC;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;MAEA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACA;gBACA;cACA;cACA;cAAA;cAAA,OACAC;YAAA;cAAAC;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IACAC;MACAC;QACAC;QACAC,gCACA;MACA;IACA;IACAC;MACAC;IACA;IACAC;MACAD;IACA;IACAE;MACAN;QACAC;QACAC,gCACA;MACA;IACA;IACA;IACAK;MACA;IACA;IACAC;MAaA;IAOA;IACAC;MAAA;MACAT;QACA;QACA;QACAU;UAAA;YAAA;cAAA;gBAAA;kBAAA;oBACAd;sBACAQ;oBACA,uBACA;sBACAJ;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CACA;UAAA;YAAA;UAAA;UAAA;QAAA;QACAW;UACA;YACA;UACA;QACA;MACA;IACA;IACAC;MACAZ;QACAC;MACA;IACA;IACAY;MAAA;MACA;QACA;QACA;UACAb;YACAc;YACAC;YACAC;YACAC;UACA;UACA;QACA;;QAEA;QACAjB;UACAc;UACAC;UACAL;YACA;cACAN;gBACAc;cACA;cACAlB;gBACAc;gBACAK;cACA;cAEA;cACA;YACA;UACA;QACA;MACA;QACA;QACA;UACAnB;YACAc;YACAC;YACAC;YACAC;UACA;UACA;QACA;;QAEA;QACA;MACA;IACA;IACAG;MACApB;QACAC;MACA;IACA;IACAoB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAzB;gBACA0B;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA9B;cAAA;gBAAA;gBAAA,OACAQ;cAAA;gBAAA;gBAAA;cAAA;gBAEAJ;kBACA2B;kBACAC;kBACAlB;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA;8BAAA,OACAd;gCACAiC;8BACA;4BAAA;8BAFAhC;8BAGA;gCACAG;kCACAc;kCACAgB;gCACA;8BACA;8BAAA;8BAAA,OACA1B;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CACA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;kBACAO;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BACAX;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CACA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA+B;MACA/B;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/RA;AAAA;AAAA;AAAA;AAAqqC,CAAgB,2oCAAG,EAAC,C;;;;;;;;;;;ACAzrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-id-pages/pages/userinfo/userinfo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './uni_modules/uni-id-pages/pages/userinfo/userinfo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./userinfo.vue?vue&type=template&id=451985ee&scoped=true&\"\nvar renderjs\nimport script from \"./userinfo.vue?vue&type=script&lang=js&\"\nexport * from \"./userinfo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./userinfo.vue?vue&type=style&index=0&id=451985ee&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"451985ee\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-id-pages/pages/userinfo/userinfo.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./userinfo.vue?vue&type=template&id=451985ee&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog\" */ \"@/uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    uniIdPagesBindMobile: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-id-pages/components/uni-id-pages-bind-mobile/uni-id-pages-bind-mobile\" */ \"@/uni_modules/uni-id-pages/components/uni-id-pages-bind-mobile/uni-id-pages-bind-mobile.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./userinfo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./userinfo.vue?vue&type=script&lang=js&\"", "<!-- 用户资料页 -->\n<template>\n\t<view class=\"container\">\n\t\t<view class=\"page-header\">\n\t\t\t<text class=\"page-title\">用户设置</text>\n\t\t</view>\n\t\t\n\t\t<view class=\"user-info-card\">\n\t\t\t<view class=\"setting-item\" @click=\"handleNicknameClick\" :class=\"{ 'disabled': !canChangeNickname }\" hover-class=\"none\">\n\t\t\t\t<view class=\"item-left\" hover-class=\"none\">\n\t\t\t\t\t<view class=\"item-icon\" hover-class=\"none\">\n\t\t\t\t\t\t<uni-icons type=\"person\" size=\"22\" color=\"#3688FF\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"item-title\">修改昵称</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"item-right\" hover-class=\"none\">\n\t\t\t\t\t<text class=\"item-value\">{{ nicknameDisplayText }}</text>\n\t\t\t\t\t<view class=\"item-arrow\" hover-class=\"none\">\n\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#C0C0C0\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"divider\"></view>\n\t\t\t\n\t\t\t<view class=\"setting-item\" v-if=\"hasPwd\" @click=\"changePassword\" hover-class=\"none\">\n\t\t\t\t<view class=\"item-left\" hover-class=\"none\">\n\t\t\t\t\t<view class=\"item-icon\" hover-class=\"none\">\n\t\t\t\t\t\t<uni-icons type=\"locked\" size=\"22\" color=\"#3688FF\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"item-title\">修改密码</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"item-right\" hover-class=\"none\">\n\t\t\t\t\t<view class=\"item-arrow\" hover-class=\"none\">\n\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#C0C0C0\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<uni-popup ref=\"dialog\" type=\"dialog\">\n\t\t\t<uni-popup-dialog mode=\"input\" :value=\"userInfo.nickname\" @confirm=\"setNickname\" :inputType=\"setNicknameIng?'nickname':'text'\"\n\t\t\t\ttitle=\"设置昵称\" placeholder=\"请输入要设置的昵称\">\n\t\t\t</uni-popup-dialog>\n\t\t</uni-popup>\n\t\t\n\t\t<uni-id-pages-bind-mobile ref=\"bind-mobile-by-sms\" @success=\"bindMobileSuccess\"></uni-id-pages-bind-mobile>\n\t\t\n\t\t<template v-if=\"showLoginManage\">\n\t\t\t<view class=\"logout-btn-wrapper\">\n\t\t\t\t<button v-if=\"userInfo._id\" class=\"logout-btn\" @click=\"logout\">退出登录</button>\n\t\t\t\t<button v-else class=\"login-btn\" @click=\"login\">去登录</button>\n\t\t\t</view>\n\t\t</template>\n\t</view>\n</template>\n\n<script>\nconst uniIdCo = uniCloud.importObject(\"uni-id-co\")\n  import {\n    store,\n    mutations\n  } from '@/uni_modules/uni-id-pages/common/store.js'\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tuniverifyStyle: {\n\t\t\t\t\tauthButton: {\n\t\t\t\t\t\t\"title\": \"本机号码一键绑定\", // 授权按钮文案\n\t\t\t\t\t},\n\t\t\t\t\totherLoginButton: {\n\t\t\t\t\t\t\"title\": \"其他号码绑定\",\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t// userInfo: {\n\t\t\t\t// \tmobile:'',\n\t\t\t\t// \tnickname:''\n\t\t\t\t// },\n\t\t\t\thasPwd: false,\n\t\t\t\tshowLoginManage: false ,//通过页面传参隐藏登录&退出登录按钮\n\t\t\t\tsetNicknameIng:false\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tuserInfo() {\n\t\t\t\treturn store.userInfo\n\t\t\t},\n\t\t\trealNameStatus () {\n\t\t\t\tif (!this.userInfo.realNameAuth) {\n\t\t\t\t\treturn 0\n\t\t\t\t}\n\n\t\t\t\treturn this.userInfo.realNameAuth.authStatus\n\t\t\t},\n\t\t\t// 判断是否为默认昵称（可修改）\n\t\t\tisDefaultNickname() {\n\t\t\t\tif (!this.userInfo.nickname) return false\n\t\t\t\treturn /^匿名[a-zA-Z0-9]+$/.test(this.userInfo.nickname)\n\t\t\t},\n\t\t\t// 昵称显示文本\n\t\t\tnicknameDisplayText() {\n\t\t\t\tif (!this.userInfo.nickname) return '未设置'\n\t\t\t\tif (this.isDefaultNickname) {\n\t\t\t\t\treturn this.userInfo.nickname\n\t\t\t\t}\n\t\t\t\treturn this.userInfo.nickname\n\t\t\t},\n\t\t\t// 是否可以修改昵称\n\t\t\tcanChangeNickname() {\n\t\t\t\treturn this.isDefaultNickname\n\t\t\t}\n\t\t},\n\t\tasync onShow() {\n\t\t\tthis.univerifyStyle.authButton.title = \"本机号码一键绑定\"\n\t\t\tthis.univerifyStyle.otherLoginButton.title = \"其他号码绑定\"\n\t\t},\n\t\tasync onLoad(e) {\n\t\t\tif (e.showLoginManage) {\n\t\t\t\tthis.showLoginManage = true //通过页面传参隐藏登录&退出登录按钮\n\t\t\t}\n\t\t\t//判断当前用户是否有密码，否则就不显示密码修改功能\n\t\t\tlet res = await uniIdCo.getAccountInfo()\n\t\t\tthis.hasPwd = res.isPasswordSet\n\t\t},\n\t\tmethods: {\n\t\t\tlogin() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/uni_modules/uni-id-pages/pages/login/login-withoutpwd',\n\t\t\t\t\tcomplete: (e) => {\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tlogout() {\n\t\t\t\tmutations.logout()\n\t\t\t},\n\t\t\tbindMobileSuccess() {\n\t\t\t\tmutations.updateUserInfo()\n\t\t\t},\n\t\t\tchangePassword() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/uni_modules/uni-id-pages/pages/userinfo/change_pwd/change_pwd',\n\t\t\t\t\tcomplete: (e) => {\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 处理昵称点击事件\n\t\t\thandleNicknameClick() {\n\t\t\t\tthis.setNickname('')\n\t\t\t},\n\t\t\tbindMobile() {\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\tuni.preLogin({\n\t\t\t\t\tprovider: 'univerify',\n\t\t\t\t\tsuccess: this.univerify(), //预登录成功\n\t\t\t\t\tfail: (res) => { // 预登录失败\n\t\t\t\t\t\t// 不显示一键登录选项（或置灰）\n\t\t\t\t\t\tthis.bindMobileBySmsCode()\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t// #endif\n\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\tthis.$refs['bind-mobile-by-sms'].open()\n\t\t\t\t// #endif\n\n\t\t\t\t// #ifdef H5\n\t\t\t\t//...去用验证码绑定\n\t\t\t\tthis.bindMobileBySmsCode()\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tuniverify() {\n\t\t\t\tuni.login({\n\t\t\t\t\t\"provider\": 'univerify',\n\t\t\t\t\t\"univerifyStyle\": this.univerifyStyle,\n\t\t\t\t\tsuccess: async e => {\n\t\t\t\t\t\tuniIdCo.bindMobileByUniverify(e.authResult).then(res => {\n\t\t\t\t\t\t\tmutations.updateUserInfo()\n\t\t\t\t\t\t}).catch(e => {\n\t\t\t\t\t\t}).finally(e => {\n\t\t\t\t\t\t\tuni.closeAuthView()\n\t\t\t\t\t\t})\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tif (err.code == '30002' || err.code == '30001') {\n\t\t\t\t\t\t\tthis.bindMobileBySmsCode()\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tbindMobileBySmsCode() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: './bind-mobile/bind-mobile'\n\t\t\t\t})\n\t\t\t},\n\t\t\tsetNickname(nickname) {\n\t\t\t\tif (nickname) {\n\t\t\t\t\t// 检查是否可以修改昵称\n\t\t\t\t\tif (!this.canChangeNickname) {\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '昵称修改限制',\n\t\t\t\t\t\t\tcontent: '昵称只能修改一次。如需更改，请联系管理员处理。',\n\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\tconfirmText: '我知道了'\n\t\t\t\t\t\t})\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 修改前确认\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '修改昵称',\n\t\t\t\t\t\tcontent: `昵称只能修改一次，确认设置为\"${nickname}\"吗？`,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tmutations.updateUserInfo({\n\t\t\t\t\t\t\t\t\tnickname\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '昵称修改成功',\n\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tthis.setNicknameIng = false\n\t\t\t\t\t\t\t\tthis.$refs.dialog.close()\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t} else {\n\t\t\t\t\t// 检查是否可以修改昵称\n\t\t\t\t\tif (!this.canChangeNickname) {\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '昵称修改限制',\n\t\t\t\t\t\t\tcontent: '昵称只能修改一次。如需更改，请联系管理员处理。',\n\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\tconfirmText: '我知道了'\n\t\t\t\t\t\t})\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 直接打开输入框\n\t\t\t\t\tthis.$refs.dialog.open()\n\t\t\t\t}\n\t\t\t},\n\t\t\tdeactivate(){\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl:\"/uni_modules/uni-id-pages/pages/userinfo/deactivate/deactivate\"\n\t\t\t\t})\n\t\t\t},\n\t\t\tasync bindThirdAccount(provider) {\n\t\t\t\tconst uniIdCo = uniCloud.importObject(\"uni-id-co\")\n\t\t\t\tconst bindField = {\n\t\t\t\t\tweixin: 'wx_openid',\n\t\t\t\t\talipay: 'ali_openid',\n\t\t\t\t\tapple: 'apple_openid',\n\t\t\t\t\tqq: 'qq_openid'\n\t\t\t\t}[provider.toLowerCase()]\n\n\t\t\t\tif (this.userInfo[bindField]) {\n\t\t\t\t\tawait uniIdCo['unbind' + provider]()\n\t\t\t\t\tawait mutations.updateUserInfo()\n\t\t\t\t} else {\n\t\t\t\t\tuni.login({\n\t\t\t\t\t\tprovider: provider.toLowerCase(),\n\t\t\t\t\t\tonlyAuthorize: true,\n\t\t\t\t\t\tsuccess: async e => {\n\t\t\t\t\t\t\tconst res = await uniIdCo['bind' + provider]({\n\t\t\t\t\t\t\t\tcode: e.code\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\tif (res.errCode) {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: res.errMsg || '绑定失败',\n\t\t\t\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tawait mutations.updateUserInfo()\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: async (err) => {\n\t\t\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\trealNameVerify () {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: \"/uni_modules/uni-id-pages/pages/userinfo/realname-verify/realname-verify\"\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t/* #ifndef APP-NVUE */\n\tview {\n\t\tbox-sizing: border-box;\n\t}\n\t\n\t.container view {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\tpage {\n\t\tbackground-color: #f0f4f8;\n\t}\n\t/* #endif*/\n\t\n\t.container {\n\t\tflex: 1;\n\t\tflex-direction: column;\n\t\tbackground-color: #f0f4f8;\n\t\tmin-height: 100vh;\n\t\tpadding: 30rpx;\n\t\tbackground: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\n\t}\n\t\n\t.page-header {\n\t\tpadding: 40rpx;\n\t\tbackground: linear-gradient(145deg, #3688FF, #5A9FFF);\n\t\tmargin-bottom: 40rpx;\n\t\tborder-radius: 24rpx;\n\t\tbox-shadow: 0 8rpx 30rpx rgba(54, 136, 255, 0.15);\n\t}\n\t\n\t.page-title {\n\t\tfont-size: 40rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #FFFFFF;\n\t\tletter-spacing: 1rpx;\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n\t}\n\t\n\t.user-info-card {\n\t\tbackground-color: #FFFFFF;\n\t\tborder-radius: 24rpx;\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);\n\t\tmargin-bottom: 40rpx;\n\t\tpadding: 12rpx 0;\n\t}\n\t\n\t.user-info-card .setting-item {\n\t\tdisplay: flex !important;\n\t\tflex-direction: row !important;\n\t\talign-items: center !important;\n\t\tjustify-content: space-between !important;\n\t\tpadding: 32rpx 40rpx;\n\t\tmin-height: 100rpx;\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t\tbackground-color: transparent !important;\n\t\t/* #ifdef H5 */\n\t\ttransition: background-color 0.2s ease;\n\t\tcursor: pointer;\n\t\t/* #endif */\n\t}\n\t\n\t/* #ifdef H5 */\n\t.user-info-card .setting-item:hover:not(.disabled) {\n\t\tbackground-color: rgba(54, 136, 255, 0.05) !important;\n\t}\n\t\n\t.user-info-card .setting-item:active:not(.disabled) {\n\t\tbackground-color: rgba(54, 136, 255, 0.1) !important;\n\t}\n\t/* #endif */\n\t\n\t/* #ifdef MP-WEIXIN */\n\t.user-info-card .setting-item {\n\t\t-webkit-tap-highlight-color: transparent;\n\t}\n\t\n\t.user-info-card .setting-item:active {\n\t\tbackground-color: transparent !important;\n\t}\n\t/* #endif */\n\t\n\t.user-info-card .setting-item.disabled {\n\t\topacity: 0.6;\n\t\tcursor: not-allowed;\n\t}\n\t\n\t.user-info-card .setting-item.disabled .item-title {\n\t\tcolor: #999999;\n\t}\n\t\n\t.user-info-card .setting-item.disabled .item-value {\n\t\tcolor: #AAAAAA;\n\t}\n\t\n\t.user-info-card .item-left {\n\t\tdisplay: flex !important;\n\t\tflex-direction: row !important;\n\t\talign-items: center !important;\n\t\tflex: 0 0 auto;\n\t}\n\t\n\t.user-info-card .item-icon {\n\t\tdisplay: flex !important;\n\t\tflex-direction: row !important;\n\t\talign-items: center !important;\n\t\tjustify-content: center !important;\n\t\twidth: 44rpx;\n\t\theight: 44rpx;\n\t\tmargin-right: 20rpx;\n\t\tflex-shrink: 0;\n\t}\n\t\n\t.user-info-card .item-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #333333;\n\t\tline-height: 44rpx;\n\t\twhite-space: nowrap;\n\t}\n\t\n\t.user-info-card .item-right {\n\t\tdisplay: flex !important;\n\t\tflex-direction: row !important;\n\t\talign-items: center !important;\n\t\tjustify-content: flex-end !important;\n\t\tgap: 16rpx;\n\t\tflex: 1 1 auto;\n\t\tmargin-left: 20rpx;\n\t}\n\t\n\t.user-info-card .item-value {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 400;\n\t\tcolor: #666666;\n\t\tline-height: 44rpx;\n\t\ttext-align: right;\n\t\tmax-width: 300rpx;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t}\n\t\n\t.user-info-card .item-arrow {\n\t\tdisplay: flex !important;\n\t\tflex-direction: row !important;\n\t\talign-items: center !important;\n\t\tjustify-content: center !important;\n\t\topacity: 0.6;\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t\tflex-shrink: 0;\n\t}\n\t\n\t.divider {\n\t\theight: 1rpx;\n\t\tbackground-color: #F0F0F0;\n\t\tmargin: 0 40rpx;\n\t}\n\t\n\t.logout-btn-wrapper {\n\t\tpadding: 40rpx;\n\t}\n\t\n\t.logout-btn, .login-btn {\n\t\twidth: 100%;\n\t\theight: 90rpx;\n\t\tline-height: 90rpx;\n\t\ttext-align: center;\n\t\tborder-radius: 45rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tmargin-top: 40rpx;\n\t}\n\t\n\t.logout-btn {\n\t\tbackground-color: #FF5A5F;\n\t\tcolor: #FFFFFF;\n\t}\n\t\n\t.login-btn {\n\t\tbackground-color: #3688FF;\n\t\tcolor: #FFFFFF;\n\t}\n\t\n</style>\n", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./userinfo.vue?vue&type=style&index=0&id=451985ee&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./userinfo.vue?vue&type=style&index=0&id=451985ee&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775852402\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}