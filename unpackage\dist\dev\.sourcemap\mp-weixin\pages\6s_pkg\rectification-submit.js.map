{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-submit.vue?9a15", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-submit.vue?a469", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-submit.vue?b27c", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-submit.vue?fa9a", "uni-app:///pages/6s_pkg/rectification-submit.vue", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-submit.vue?8e5a", "webpack:///D:/Xwzc/pages/6s_pkg/rectification-submit.vue?42a8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "taskId", "isFromIssue", "loading", "loadingText", "submitting", "maxPhotos", "autoUpload", "taskInfo", "id", "title", "areaName", "areaId", "deadline", "description", "issuePhotos", "<PERSON><PERSON><PERSON>", "createdAt", "status", "rectificationPhotos", "rectificationDescription", "computed", "<PERSON><PERSON><PERSON><PERSON>", "canSubmit", "onLoad", "uni", "icon", "setTimeout", "methods", "handleDescriptionInput", "value", "duration", "loadTaskInfo", "action", "issue_id", "result", "issue", "console", "errorMessage", "formatTaskData", "formatIssueAsTask", "generateTaskTitle", "formatDeadline", "formatIssuePhotos", "chooseImage", "count", "sizeType", "sourceType", "res", "newPhotos", "url", "uploaded", "cloudUrl", "cloudPath", "uploading", "uploadTime", "autoUploadNewPhotos", "photo", "uploadSinglePhoto", "uploadUtils", "uploadResult", "fileInfo", "Object", "size", "generateCloudPath", "toggleAutoUpload", "previewPhoto", "urls", "current", "previewOriginalImage", "deletePhoto", "index", "uniCloud", "fileList", "extractFileId", "submitRectification", "unUploadedPhotos", "uploadingPhotos", "failedPhotos", "content", "success", "submitData", "rectification_photos", "type", "rectification_description", "submitted_at", "action_type", "completed_at", "completion_description", "completion_photos", "issueId", "retryFailedUploads", "delay"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,4BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6I;AAC7I;AACwE;AACL;AACsC;;;AAGzG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,0FAAM;AACR,EAAE,2GAAM;AACR,EAAE,oHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnDA;AAAA;AAAA;AAAA;AAA8mB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC8JloB;AACA;AAAA;AAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;;MAEA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QAAA;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;MAEA;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;IACA;IAEAC;MACA;MACA;MACA;QAAA;MAAA;MAEA;IACA;EACA;EAEAC;IACA;IACA;IACA;;IAEA;MACAC;QACAf;QACAgB;MACA;MACAC;QAAA;MAAA;MACA;IACA;IAEA;;IAEA;IACAF;MACAf;IACA;EACA;EAEAkB;IACA;IACAC;MACA;;MAEA;MACA;QACAC;QACA;QACAL;UACAf;UACAgB;UACAK;QACA;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA,KAKA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAC;kBACAjC;oBAAAkC;kBAAA;gBACA;cAAA;gBAHAC;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAIA;kBACAH;kBACAjC;oBAAAS;kBAAA;gBACA;cAAA;gBAHA0B;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAKAE;;gBAEA;gBACAC;gBACA;kBACAA;gBACA;kBACAA;gBACA;kBACAA;gBACA;kBACAA;gBACA;gBAEAb;kBACAf;kBACAgB;kBACAK;gBACA;gBAEAJ;kBAAA;gBAAA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAY;MACA;QACA9B;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAsB;MAAA;MACA;QACA/B;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAuB;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAnB;kBACAf;kBACAgB;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKAD;kBACAoB;kBACAC;kBAAA;kBACAC;gBACA;cAAA;gBAJAC;gBAMA;gBACAC;kBAAA;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;kBACA;gBAAA,IAEA;gBACA;;gBAEA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAlB;gBACAZ;kBACAf;kBACAgB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA8B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACAC;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACAD;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEAA;gBACA;gBAAA;gBAGAJ,wCAEA;gBAAA;gBAAA,OACAM;cAAA;gBAAAC;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEAD;cAAA;gBAAAE;gBAEA;gBACAC;kBACAX;kBACAG;kBACAF;kBACAC;kBACAU;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA1B;gBACAoB;gBACAA;gBAEAhC;kBACAf;kBACAgB;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAsC;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAxC;QACAf;QACAgB;MACA;IACA;IAEAwC;MACA;QAAA;MAAA;MACAzC;QACA0C;QACAC;MACA;IACA;IAEAC;MACA5C;QACA0C;QACAC;MACA;IACA;IAEAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIAd,2CAEA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAEAhC;kBAAAf;gBAAA;;gBAEA;gBAAA;gBAAA,OACA8D;kBACAzE;kBACAC;oBACAyE;kBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEApC;gBACAZ;kBACAf;kBACAgB;gBACA;gBAAA;cAAA;gBAAA;gBAGAD;gBAAA;cAAA;gBAIA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAiD;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACAlD;kBACAf;kBACAgB;gBACA;gBAAA;cAAA;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBACAD;kBACAf;kBACAgB;gBACA;gBAAA;cAAA;gBAIA;gBACAkD;kBAAA;gBAAA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAnD;kBACAf;kBACAgB;kBACAK;gBACA;gBAAA;cAAA;gBAIA;gBACA8C;kBAAA;gBAAA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACApD;kBACAf;kBACAgB;gBACA;gBAAA;cAAA;gBAIA;gBACAoD;kBAAA;gBAAA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACArD;kBACAf;kBACAqE;kBACAC;oBACA;sBACA;oBACA;kBACA;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBACA;gBAAA;gBAGA;gBACAC;kBACAxE;kBACAyE;oBAAA;sBACAhC;sBACAiC;sBACArE;oBACA;kBAAA;kBACAsE;kBACAC;kBACAnE;gBACA,GAEA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAe;kBACAjC;oBACAkC;oBACAhB;oBACAoE;oBACAF;oBACAF;oBACAK;kBACA;gBACA;cAAA;gBAVApD;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAaA;kBACAF;kBACAjC;oBACAS;oBACA+E;oBACAC;kBACA;gBACA;cAAA;gBAPAtD;cAAA;gBAAA,MAUAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;kBACAV;oBACAQ;oBACAyD;oBACAxE;kBACA;gBACA;kBACAO;oBACAxB;oBACAW;oBACAM;kBACA;gBACA;gBAEAO;kBACAf;kBACAgB;gBACA;gBAEAC;kBACAF;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAY;gBACAZ;kBACAf;kBACAgB;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAb;kBAAA;gBAAA;gBAAA,uCACAA;gBAAA;gBAAA;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAArB;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAmC;MACA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvrBA;AAAA;AAAA;AAAA;AAAirC,CAAgB,upCAAG,EAAC,C;;;;;;;;;;;ACArsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/rectification-submit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/rectification-submit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./rectification-submit.vue?vue&type=template&id=5b99b5c7&scoped=true&\"\nvar renderjs\nimport script from \"./rectification-submit.vue?vue&type=script&lang=js&\"\nexport * from \"./rectification-submit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./rectification-submit.vue?vue&type=style&index=0&id=5b99b5c7&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5b99b5c7\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/rectification-submit.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rectification-submit.vue?vue&type=template&id=5b99b5c7&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.taskInfo.issuePhotos.length\n  var g1 = _vm.rectificationPhotos.length\n  var g2 = g1 < _vm.maxPhotos ? _vm.rectificationPhotos.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rectification-submit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rectification-submit.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"page-container\">\r\n\r\n    <!-- 整改任务信息卡片 -->\r\n    <view class=\"issue-card\">\r\n      <view class=\"issue-header\">\r\n        <view class=\"issue-status\">整改中</view>\r\n        <view class=\"issue-title-header\">{{ taskInfo.title }}</view>\r\n      </view>\r\n      <view class=\"issue-meta\">\r\n        <view class=\"meta-item\">\r\n          <uni-icons type=\"location\" size=\"16\" color=\"#007AFF\"></uni-icons>\r\n          <text>{{ taskInfo.areaName }}</text>\r\n        </view>\r\n        <view class=\"meta-item\">\r\n          <uni-icons type=\"calendar\" size=\"16\" color=\"#FF9500\"></uni-icons>\r\n          <text>截止：{{ taskInfo.deadline }}</text>\r\n        </view>\r\n      </view>\r\n      <view v-if=\"taskInfo.description\" class=\"issue-description\">\r\n        <text class=\"desc-label\">问题描述：</text>\r\n        <text class=\"desc-content\">{{ taskInfo.description }}</text>\r\n      </view>\r\n\r\n    </view>\r\n\r\n    <!-- 原始问题图片 -->\r\n    <view class=\"section-card\">\r\n      <view class=\"section-header\">\r\n        <view class=\"section-title\">问题图片</view>\r\n        <view class=\"section-subtitle\">发现时拍摄</view>\r\n      </view>\r\n      <p-empty-state\r\n        v-if=\"taskInfo.issuePhotos.length === 0\"\r\n        type=\"data\"\r\n        text=\"暂无问题图片\"\r\n        description=\"检查员未上传问题照片\"\r\n      ></p-empty-state>\r\n      \r\n      <view v-else class=\"image-grid original-images\">\r\n        <view \r\n          v-for=\"(image, index) in taskInfo.issuePhotos\" \r\n          :key=\"index\"\r\n          class=\"image-item\"\r\n          @click=\"previewOriginalImage(index)\"\r\n        >\r\n          <image :src=\"image\" mode=\"aspectFill\" class=\"issue-image\"></image>\r\n          <view class=\"image-label\">问题图片</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 整改照片上传 -->\r\n    <view class=\"section-card\">\r\n      <view class=\"section-header\">\r\n        <view class=\"section-header-main\">\r\n          <view class=\"section-title\">整改照片</view>\r\n          <view class=\"section-subtitle\">请上传整改后的照片</view>\r\n        </view>\r\n        <view class=\"auto-upload-toggle\" @click=\"toggleAutoUpload\">\r\n          <view class=\"toggle-label\">自动上传</view>\r\n          <view class=\"toggle-switch\" :class=\"{ active: autoUpload }\">\r\n            <view class=\"toggle-circle\"></view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      <view class=\"upload-area\">\r\n        <view class=\"image-grid\">\r\n          <view \r\n            v-for=\"(photo, index) in rectificationPhotos\" \r\n            :key=\"index\"\r\n            class=\"image-item photo-item\"\r\n          >\r\n            <image :src=\"photo.url\" mode=\"aspectFill\" class=\"uploaded-image\" @click=\"previewPhoto(index)\"></image>\r\n            \r\n            <!-- 上传状态指示器 -->\r\n            <view v-if=\"photo.uploading\" class=\"photo-uploading\">\r\n              <view class=\"upload-spinner\"></view>\r\n            </view>\r\n            <view v-else-if=\"photo.uploaded\" class=\"photo-uploaded\">\r\n              <uni-icons type=\"checkmarkempty\" size=\"16\" color=\"white\"></uni-icons>\r\n            </view>\r\n            <view v-else class=\"photo-failed\" @click=\"uploadSinglePhoto(photo)\">\r\n              <uni-icons type=\"refreshempty\" size=\"16\" color=\"white\"></uni-icons>\r\n            </view>\r\n            \r\n            <!-- 删除按钮 -->\r\n            <view class=\"photo-delete\" @click=\"deletePhoto(index)\">\r\n              <uni-icons type=\"close\" size=\"18\" color=\"white\"></uni-icons>\r\n            </view>\r\n          </view>\r\n          <view \r\n            v-if=\"rectificationPhotos.length < maxPhotos\" \r\n            class=\"image-item add-photo\"\r\n            @click=\"chooseImage\"\r\n          >\r\n            <view class=\"add-icon\">\r\n              <uni-icons type=\"camera-filled\" size=\"32\" color=\"#8E8E93\"></uni-icons>\r\n            </view>\r\n            <text class=\"add-text\">添加照片</text>\r\n            <text class=\"add-count\">{{ rectificationPhotos.length }}/{{ maxPhotos }}</text>\r\n          </view>\r\n        </view>\r\n        <view class=\"upload-tips\">\r\n          <text class=\"tip-text\">• 建议拍摄整改后的实际效果照片</text>\r\n          <text class=\"tip-text\">• 可上传 {{ maxPhotos }} 张照片，支持多角度拍摄</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 整改说明 -->\r\n    <view class=\"section-card\">\r\n      <view class=\"section-header\">\r\n        <view class=\"section-title\">整改说明</view>\r\n        <view class=\"section-subtitle\">详细描述整改措施和效果</view>\r\n      </view>\r\n      <view class=\"textarea-container\">\r\n        <view class=\"description-input-container\">\r\n          <textarea \r\n            v-model=\"rectificationDescription\"\r\n            class=\"description-input\"\r\n            placeholder=\"请详细说明采取的整改措施、解决方案和预期效果\"\r\n            :maxlength=\"200\"\r\n            auto-height\r\n            @input=\"handleDescriptionInput\"\r\n          ></textarea>\r\n          <view class=\"char-count-overlay\">{{ descriptionLength }}/200</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 操作按钮 -->\r\n    <view class=\"button-container\">\r\n      <button \r\n        class=\"primary-button\" \r\n        @click=\"submitRectification\"\r\n        :disabled=\"submitting\"\r\n        :class=\"{ loading: submitting }\"\r\n      >\r\n        <view v-if=\"submitting\" class=\"button-loading\">\r\n          <view class=\"loading-spinner\"></view>\r\n          <text>提交中...</text>\r\n        </view>\r\n        <text v-else>提交整改</text>\r\n      </button>\r\n    </view>\r\n\r\n    <!-- 加载遮罩 -->\r\n    <view v-if=\"loading\" class=\"loading-mask\">\r\n      <view class=\"loading-content\">\r\n        <uni-icons type=\"spinner-cycle\" size=\"32\" color=\"#007AFF\"></uni-icons>\r\n        <text class=\"loading-text\">{{ loadingText }}</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { callCloudFunction } from '@/utils/auth.js';\r\nimport uploadUtils from '@/utils/upload-utils.js'\r\n\r\nexport default {\r\n  name: 'RectificationSubmit',\r\n  data() {\r\n    return {\r\n      taskId: null, // 整改任务ID或月检问题ID\r\n      isFromIssue: false, // 是否来自月检问题\r\n      loading: false,\r\n      loadingText: '加载中...',\r\n      submitting: false,\r\n      maxPhotos: 6,\r\n      autoUpload: true, // 自动上传开关\r\n      \r\n      // 整改任务信息\r\n      taskInfo: {\r\n        id: null,\r\n        title: '',\r\n        areaName: '',\r\n        areaId: '',\r\n        deadline: '',\r\n        description: '',\r\n        issuePhotos: [], // 问题照片\r\n        inspectorName: '',\r\n        createdAt: '',\r\n        status: 'pending_rectification'\r\n      },\r\n      \r\n      // 整改照片\r\n      rectificationPhotos: [],\r\n      \r\n      // 整改说明\r\n      rectificationDescription: ''\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    // 计算描述长度，确保响应式更新\r\n    descriptionLength() {\r\n      return this.rectificationDescription ? this.rectificationDescription.length : 0;\r\n    },\r\n    \r\n    canSubmit() {\r\n      const hasPhotos = this.rectificationPhotos.length > 0;\r\n      const hasDescription = this.rectificationDescription.trim().length >= 2;\r\n      const allPhotosUploaded = this.rectificationPhotos.every(photo => photo.uploaded);\r\n      \r\n      return hasPhotos && hasDescription && allPhotosUploaded;\r\n    }\r\n  },\r\n  \r\n  onLoad(options) {\r\n    // 支持两种参数：taskId（整改任务ID）或 issueId（月检问题ID）\r\n    this.taskId = options.taskId || options.issueId;\r\n    this.isFromIssue = !!options.issueId; // 标记是否来自月检问题\r\n    \r\n    if (!this.taskId) {\r\n      uni.showToast({\r\n        title: '缺少参数',\r\n        icon: 'error'\r\n      });\r\n      setTimeout(() => uni.navigateBack(), 1500);\r\n      return;\r\n    }\r\n    \r\n    this.loadTaskInfo();\r\n    \r\n    // 设置导航栏标题\r\n    uni.setNavigationBarTitle({\r\n      title: '整改提交'\r\n    });\r\n  },\r\n\r\n  methods: {\r\n    // 处理描述输入，确保字符限制和响应式更新\r\n    handleDescriptionInput(e) {\r\n      let value = e.detail.value || '';\r\n      \r\n      // 强制限制字符数量\r\n      if (value.length > 200) {\r\n        value = value.substring(0, 200);\r\n        // 如果超出限制，显示提示\r\n        uni.showToast({\r\n          title: '整改说明不能超过200个字符',\r\n          icon: 'none',\r\n          duration: 1500\r\n        });\r\n      }\r\n      \r\n      // 更新数据\r\n      this.rectificationDescription = value;\r\n      \r\n      // 强制触发视图更新\r\n      this.$forceUpdate();\r\n    },\r\n    \r\n    async loadTaskInfo() {\r\n      this.loading = true;\r\n      this.loadingText = this.isFromIssue ? '加载问题信息...' : '加载整改任务信息...';\r\n      \r\n      try {\r\n        let result;\r\n        \r\n        if (this.isFromIssue) {\r\n          // 来自月检问题，直接获取问题详情并格式化为任务数据\r\n          result = await callCloudFunction('hygiene-monthly-inspection', {\r\n            action: 'getIssueDetail',\r\n            data: { issue_id: this.taskId }\r\n          });\r\n          \r\n          if (result && result.success && result.data) {\r\n            const issue = result.data;\r\n            this.taskInfo = this.formatIssueAsTask(issue);\r\n          } else {\r\n            throw new Error('获取问题信息失败');\r\n          }\r\n        } else {\r\n          // 直接加载整改任务\r\n          result = await callCloudFunction('hygiene-rectification', {\r\n            action: 'getRectificationDetail',\r\n            data: { id: this.taskId }\r\n          });\r\n\r\n          if (result && result.success && result.data) {\r\n            this.taskInfo = this.formatTaskData(result.data);\r\n          } else {\r\n            throw new Error('获取整改任务信息失败');\r\n          }\r\n        }\r\n        \r\n      } catch (error) {\r\n        console.error('加载失败:', error);\r\n        \r\n        // 根据错误类型提供更友好的提示\r\n        let errorMessage = '加载失败';\r\n        if (error.message?.includes('未知的操作类型')) {\r\n          errorMessage = 'API接口暂时不可用，请稍后重试';\r\n        } else if (error.message?.includes('问题信息失败')) {\r\n          errorMessage = '问题不存在或已被删除';\r\n        } else if (error.message?.includes('整改任务信息失败')) {\r\n          errorMessage = '整改任务不存在或已被删除';\r\n        } else if (error.message) {\r\n          errorMessage = error.message;\r\n        }\r\n        \r\n        uni.showToast({\r\n          title: errorMessage,\r\n          icon: 'error',\r\n          duration: 3000\r\n        });\r\n        \r\n        setTimeout(() => uni.navigateBack(), 2000);\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 格式化整改任务数据\r\n    formatTaskData(task) {\r\n      return {\r\n        id: task._id || task.id,\r\n        title: this.generateTaskTitle(task),\r\n        areaName: task.area_name || '未知责任区',\r\n        areaId: task.area_id,\r\n        deadline: this.formatDeadline(task.deadline || task.created_at),\r\n        description: task.issue_description || task.description || '',\r\n        issuePhotos: this.formatIssuePhotos(task.issue_photos || []),\r\n        inspectorName: task.inspector_name || '检查员',\r\n        createdAt: task.created_at,\r\n        status: task.status || 'pending_rectification'\r\n      };\r\n    },\r\n\r\n    // 将月检问题格式化为任务数据\r\n    formatIssueAsTask(issue) {\r\n      return {\r\n        id: issue._id || issue.id,\r\n        title: issue.title || issue.issue_title || '整改任务',\r\n        areaName: issue.location_info?.location_name || issue.location || '未知区域',\r\n        areaId: issue.area_id || issue.location_info?.area_id || '',\r\n        deadline: this.formatDeadline(issue.expected_completion_date || issue.deadline),\r\n        description: issue.description || issue.issue_description || '',\r\n        issuePhotos: this.formatIssuePhotos(issue.photos || issue.images || issue.issue_photos || []),\r\n        inspectorName: issue.inspector_name || '检查员',\r\n        createdAt: issue.created_at,\r\n        status: 'pending_rectification'\r\n      };\r\n    },\r\n\r\n    // 生成任务标题\r\n    generateTaskTitle(task) {\r\n      const issueType = task.issue_type || '清理问题';\r\n      const number = task.number || Math.floor(Math.random() * 999) + 1;\r\n      return `${issueType} #${String(number).padStart(3, '0')}`;\r\n    },\r\n\r\n    // 格式化截止时间\r\n    formatDeadline(dateString) {\r\n      if (!dateString) return '待定';\r\n      const date = new Date(dateString);\r\n      const now = new Date();\r\n      \r\n      // 默认给48小时完成时间\r\n      const deadline = new Date(date.getTime() + 48 * 60 * 60 * 1000);      \r\n      return `${deadline.getMonth() + 1}月${deadline.getDate()}日 ${deadline.getHours().toString().padStart(2, '0')}:${deadline.getMinutes().toString().padStart(2, '0')}`;\r\n    },\r\n\r\n    // 格式化问题照片\r\n    formatIssuePhotos(photos) {\r\n      if (!Array.isArray(photos)) return [];\r\n      return photos.map(photo => {\r\n        if (typeof photo === 'string') return photo;\r\n        return photo.url || photo.path || photo;\r\n      });\r\n    },\r\n    \r\n    async chooseImage() {\r\n      if (this.rectificationPhotos.length >= this.maxPhotos) {\r\n        uni.showToast({\r\n          title: `最多只能上传${this.maxPhotos}张照片`,\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const res = await uni.chooseImage({\r\n          count: this.maxPhotos - this.rectificationPhotos.length,\r\n          sizeType: ['compressed'], // 只使用压缩版本以节省内存和上传时间\r\n          sourceType: ['camera', 'album']\r\n        });\r\n        \r\n        // 处理新选择的照片\r\n        const newPhotos = res.tempFilePaths.map(path => ({\r\n          url: path,\r\n          uploaded: false,\r\n          cloudUrl: '',\r\n          cloudPath: '',\r\n          uploading: false,\r\n          uploadTime: new Date().toISOString()\r\n        }));\r\n        \r\n        // 批量添加到照片列表\r\n        this.rectificationPhotos.push(...newPhotos);\r\n        \r\n        // 如果开启自动上传，立即上传新选择的照片\r\n        if (this.autoUpload) {\r\n          this.autoUploadNewPhotos(newPhotos);\r\n        }\r\n        \r\n      } catch (error) {\r\n        console.error('选择图片失败:', error);\r\n        uni.showToast({\r\n          title: '选择照片失败',\r\n          icon: 'none'\r\n        });\r\n      }\r\n    },\r\n\r\n    // 自动上传新选择的照片\r\n    async autoUploadNewPhotos(newPhotos) {\r\n      for (let i = 0; i < newPhotos.length; i++) {\r\n        const photo = newPhotos[i];\r\n        if (!photo.uploaded && !photo.uploading) {\r\n          this.uploadSinglePhoto(photo);\r\n        }\r\n      }\r\n    },\r\n\r\n    // 上传单张照片\r\n    async uploadSinglePhoto(photo) {\r\n      if (photo.uploading || photo.uploaded) return;\r\n      \r\n      photo.uploading = true;\r\n      this.$forceUpdate(); // 触发视图更新\r\n      \r\n      try {\r\n        const cloudPath = this.generateCloudPath();\r\n        \r\n        // 使用 uploadToCloud 方法上传照片\r\n        const uploadResult = await uploadUtils.uploadToCloud(photo.url, cloudPath);\r\n        \r\n        if (uploadResult?.fileID) {\r\n          // 获取访问URL\r\n          const fileInfo = await uploadUtils.getFileInfo(uploadResult.fileID);\r\n          \r\n          // 更新照片信息\r\n          Object.assign(photo, {\r\n            uploaded: true,\r\n            uploading: false,\r\n            cloudUrl: fileInfo.tempFileURL || uploadResult.fileID,\r\n            cloudPath: uploadResult.fileID,\r\n            size: uploadResult.actualSize\r\n          });\r\n        } else {\r\n          throw new Error('上传返回结果异常');\r\n        }\r\n      } catch (error) {\r\n        console.error('照片上传失败:', error);\r\n        photo.uploading = false;\r\n        photo.uploaded = false;\r\n        \r\n        uni.showToast({\r\n          title: '照片上传失败',\r\n          icon: 'none'\r\n        });\r\n      } finally {\r\n        this.$forceUpdate(); // 触发视图更新\r\n      }\r\n    },\r\n\r\n    // 生成云存储路径\r\n    generateCloudPath() {\r\n      const timestamp = Date.now();\r\n      const random = Math.random().toString(36).substring(2, 8);\r\n      // 优先使用 areaId，其次使用 taskId，最后才是 unknown\r\n      const areaPath = this.taskInfo.areaId || this.taskId || 'temp';\r\n      return `6s/rectification/${areaPath}/${timestamp}_${random}.jpg`;\r\n    },\r\n\r\n    // 切换自动上传状态\r\n    toggleAutoUpload() {\r\n      this.autoUpload = !this.autoUpload;\r\n      uni.showToast({\r\n        title: this.autoUpload ? '已开启自动上传' : '已关闭自动上传',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    previewPhoto(index) {\r\n      const urls = this.rectificationPhotos.map(photo => photo.url);\r\n      uni.previewImage({\r\n        urls: urls,\r\n        current: index\r\n      });\r\n    },\r\n    \r\n    previewOriginalImage(index) {\r\n      uni.previewImage({\r\n        urls: this.taskInfo.issuePhotos,\r\n        current: index\r\n      });\r\n    },\r\n    \r\n    async deletePhoto(index) {\r\n      if (index < 0 || index >= this.rectificationPhotos.length) {\r\n        return;\r\n      }\r\n\r\n      const photo = this.rectificationPhotos[index];\r\n      \r\n      // 如果照片已经上传到云端，需要删除云端文件\r\n      if (photo.uploaded && photo.cloudPath) {\r\n        try {\r\n          uni.showLoading({ title: '删除照片中...' });\r\n          \r\n          // 调用删除云文件的云函数\r\n          await uniCloud.callFunction({\r\n            name: 'delete-file',\r\n            data: {\r\n              fileList: [this.extractFileId(photo.cloudPath)]\r\n            }\r\n          });\r\n        } catch (error) {\r\n          console.error('删除云文件失败:', error);\r\n          uni.showToast({\r\n            title: '删除失败',\r\n            icon: 'none'\r\n          });\r\n          return;\r\n        } finally {\r\n          uni.hideLoading();\r\n        }\r\n      }\r\n      \r\n      // 从数组中移除照片\r\n      this.rectificationPhotos.splice(index, 1);\r\n    },\r\n\r\n    // 提取文件ID（用于删除云文件）\r\n    extractFileId(cloudPath) {\r\n      return cloudPath.replace(/^cloud:\\/\\//, '');\r\n    },\r\n    \r\n    async submitRectification() {\r\n      // 检查照片\r\n      if (this.rectificationPhotos.length === 0) {\r\n        uni.showToast({\r\n          title: '请至少上传一张整改照片',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 检查整改说明\r\n      if (this.rectificationDescription.trim().length < 2) {\r\n        uni.showToast({\r\n          title: '请输入整改说明（至少2个字符）',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 检查照片是否都已上传成功\r\n      const unUploadedPhotos = this.rectificationPhotos.filter(photo => !photo.uploaded);\r\n      if (unUploadedPhotos.length > 0) {\r\n        uni.showToast({\r\n          title: '请等待照片上传完成或重新上传失败的照片',\r\n          icon: 'none',\r\n          duration: 3000\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 检查是否有照片正在上传\r\n      const uploadingPhotos = this.rectificationPhotos.filter(photo => photo.uploading);\r\n      if (uploadingPhotos.length > 0) {\r\n        uni.showToast({\r\n          title: '照片正在上传中，请稍候...',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 检查是否有照片上传失败\r\n      const failedPhotos = this.rectificationPhotos.filter(photo => !photo.uploaded);\r\n      if (failedPhotos.length > 0) {\r\n        uni.showModal({\r\n          title: '提示',\r\n          content: '有照片未上传成功，是否重新上传？',\r\n          success: (res) => {\r\n            if (res.confirm) {\r\n              this.retryFailedUploads();\r\n            }\r\n          }\r\n        });\r\n        return;\r\n      }\r\n      \r\n      this.submitting = true;\r\n      this.loading = true;\r\n      this.loadingText = '提交整改中...';\r\n      \r\n      try {\r\n        // 准备提交数据\r\n        const submitData = {\r\n          id: this.taskId,\r\n          rectification_photos: this.rectificationPhotos.map(photo => ({\r\n            url: photo.cloudUrl || photo.cloudPath,\r\n            type: 'rectification',\r\n            description: ''\r\n          })),\r\n          rectification_description: this.rectificationDescription.trim(),\r\n          submitted_at: new Date().toISOString(),\r\n          status: 'pending_review'\r\n        };\r\n\r\n        // 根据来源选择对应的API\r\n        let result;\r\n        if (this.isFromIssue) {\r\n          // 来自月检问题，更新问题状态为已完成\r\n          result = await callCloudFunction('hygiene-monthly-inspection', {\r\n            action: 'updateMonthlyIssue',\r\n            data: {\r\n              issue_id: this.taskId,\r\n              status: 'pending_review',\r\n              action_type: 'submit_rectification',\r\n              rectification_description: submitData.rectification_description,\r\n              rectification_photos: submitData.rectification_photos,\r\n              completed_at: submitData.submitted_at\r\n            }\r\n          });\r\n        } else {\r\n          // 标准整改任务提交\r\n          result = await callCloudFunction('hygiene-rectification', {\r\n            action: 'completeRectification',\r\n            data: {\r\n              id: this.taskId,\r\n              completion_description: submitData.rectification_description,\r\n              completion_photos: submitData.rectification_photos\r\n            }\r\n          });\r\n        }\r\n\r\n        if (result && result.success) {\r\n          // 发送更新事件\r\n          if (this.isFromIssue) {\r\n            uni.$emit('monthlyIssueUpdated', {\r\n              action: 'submit_rectification',\r\n              issueId: this.taskId,\r\n              status: 'pending_review'\r\n            });\r\n          } else {\r\n            uni.$emit('rectificationRecordUpdated', {\r\n              taskId: this.taskId,\r\n              areaId: this.taskInfo.areaId,\r\n              status: 'pending_review'\r\n            });\r\n          }\r\n          \r\n          uni.showToast({\r\n            title: '整改提交成功',\r\n            icon: 'success'\r\n          });\r\n          \r\n          setTimeout(() => {\r\n            uni.navigateBack();\r\n          }, 1500);\r\n        } else {\r\n          throw new Error(result?.message || '提交失败');\r\n        }\r\n        \r\n      } catch (error) {\r\n        console.error('提交整改失败:', error);\r\n        uni.showToast({\r\n          title: error.message || '提交失败',\r\n          icon: 'error'\r\n        });\r\n      } finally {\r\n        this.submitting = false;\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 重试失败的上传\r\n    async retryFailedUploads() {\r\n      const failedPhotos = this.rectificationPhotos.filter(photo => !photo.uploaded);\r\n      for (let photo of failedPhotos) {\r\n        await this.uploadSinglePhoto(photo);\r\n      }\r\n    },\r\n    \r\n    delay(ms) {\r\n      return new Promise(resolve => setTimeout(resolve, ms));\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-container {\r\n\tmin-height: 100vh;\r\n\tbackground: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\r\n\twidth: 100%;\r\n\toverflow-x: hidden; /* 防止水平滚动 */\r\n\tbox-sizing: border-box;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;\r\n}\r\n\r\n/* 问题信息卡片 */\r\n.issue-card {\r\n  background: #FFFFFF;\r\n  margin: 24rpx 32rpx;\r\n  border-radius: 16rpx;\r\n  padding: 32rpx;\r\n  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.issue-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.issue-status {\r\n  background: #FF9500;\r\n  color: #FFFFFF;\r\n  font-size: 22rpx;\r\n  font-weight: 600;\r\n  padding: 8rpx 16rpx;\r\n  border-radius: 12rpx;\r\n  white-space: nowrap;\r\n}\r\n\r\n.issue-title-header {\r\n  flex: 1;\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #1C1C1E;\r\n  margin-left: 16rpx;\r\n  line-height: 1.4;\r\n  word-wrap: break-word;\r\n}\r\n\r\n.issue-meta {\r\n  display: flex;\r\n  gap: 24rpx;\r\n  margin-bottom: 24rpx;\r\n}\r\n\r\n.meta-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6rpx;\r\n  font-size: 24rpx;\r\n  color: #8E8E93;\r\n}\r\n\r\n.issue-description {\r\n  background: #F8F9FA;\r\n  padding: 20rpx;\r\n  border-radius: 12rpx;\r\n}\r\n\r\n.desc-label {\r\n  font-size: 24rpx;\r\n  color: #8E8E93;\r\n  margin-right: 8rpx;\r\n}\r\n\r\n.desc-content {\r\n  font-size: 26rpx;\r\n  color: #1C1C1E;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 通用卡片样式 */\r\n.section-card {\r\n  background: #FFFFFF;\r\n  margin: 24rpx 32rpx;\r\n  border-radius: 16rpx;\r\n  padding: 32rpx;\r\n  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 24rpx;\r\n}\r\n\r\n.section-header-main {\r\n  flex: 1;\r\n}\r\n\r\n.section-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #1C1C1E;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.section-subtitle {\r\n  font-size: 24rpx;\r\n  color: #8E8E93;\r\n}\r\n\r\n/* 自动上传开关 */\r\n.auto-upload-toggle {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12rpx;\r\n  cursor: pointer;\r\n}\r\n\r\n.toggle-label {\r\n  font-size: 24rpx;\r\n  color: #1C1C1E;\r\n  font-weight: 500;\r\n}\r\n\r\n.toggle-switch {\r\n  width: 80rpx;\r\n  height: 48rpx;\r\n  background: #E5E5EA;\r\n  border-radius: 24rpx;\r\n  position: relative;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.toggle-switch.active {\r\n  background: #34C759;\r\n}\r\n\r\n.toggle-circle {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 50%;\r\n  position: absolute;\r\n  top: 4rpx;\r\n  left: 4rpx;\r\n  transition: transform 0.3s ease;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.toggle-switch.active .toggle-circle {\r\n  transform: translateX(32rpx);\r\n}\r\n\r\n/* 图片网格 */\r\n.image-grid {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 12rpx;\r\n}\r\n\r\n.image-item {\r\n  width: 170rpx;\r\n  height: 170rpx;\r\n  border-radius: 12rpx;\r\n  overflow: hidden;\r\n  position: relative;\r\n  background: #F5F5F5;\r\n}\r\n\r\n.issue-image, .uploaded-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.image-label {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  color: #FFFFFF;\r\n  font-size: 20rpx;\r\n  text-align: center;\r\n  padding: 6rpx;\r\n}\r\n\r\n\r\n\r\n/* 照片状态指示器 */\r\n.photo-uploading,\r\n.photo-uploaded,\r\n.photo-failed {\r\n  position: absolute;\r\n  top: 8rpx;\r\n  left: 8rpx;\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.photo-uploading {\r\n  background: rgba(0, 0, 0, 0.6);\r\n}\r\n\r\n.photo-uploaded {\r\n  background: #34C759;\r\n}\r\n\r\n.photo-failed {\r\n  background: rgba(255, 59, 48, 0.9);\r\n  cursor: pointer;\r\n}\r\n\r\n.upload-spinner {\r\n  width: 20rpx;\r\n  height: 20rpx;\r\n  border: 2rpx solid rgba(255, 255, 255, 0.3);\r\n  border-top: 2rpx solid white;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.photo-delete {\r\n  position: absolute;\r\n  top: 8rpx;\r\n  right: 8rpx;\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  border-radius: 50%;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n\r\n\r\n.add-photo {\r\n  width: 170rpx;\r\n  height: 170rpx;\r\n  border: 2rpx dashed #D1D1D6;\r\n  background: #FAFBFC;\r\n  border-radius: 12rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8rpx;\r\n}\r\n\r\n.add-icon {\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.add-text {\r\n  font-size: 24rpx;\r\n  color: #8E8E93;\r\n  font-weight: 500;\r\n}\r\n\r\n.add-count {\r\n  font-size: 20rpx;\r\n  color: #8E8E93;\r\n}\r\n\r\n.upload-tips {\r\n  margin-top: 16rpx;\r\n  padding: 16rpx;\r\n  background: #F8F9FA;\r\n  border-radius: 8rpx;\r\n}\r\n\r\n.tip-text {\r\n  display: block;\r\n  font-size: 22rpx;\r\n  color: #8E8E93;\r\n  line-height: 1.5;\r\n  margin-bottom: 4rpx;\r\n}\r\n\r\n/* 文本输入 */\r\n.textarea-container {\r\n  position: relative;\r\n}\r\n\r\n.description-input-container {\r\n  position: relative;\r\n  width: 100%;\r\n}\r\n\r\n.description-input {\r\n  width: 100%;\r\n  min-height: 180rpx;\r\n  padding: 20rpx 16rpx 40rpx 16rpx;\r\n  border: 1rpx solid #E5E5EA;\r\n  border-radius: 12rpx;\r\n  font-size: 28rpx;\r\n  color: #1C1C1E;\r\n  background: #F8F9FA;\r\n  box-sizing: border-box;\r\n  resize: none;\r\n  line-height: 1.6;\r\n}\r\n\r\n.char-count-overlay {\r\n  position: absolute;\r\n  bottom: 8rpx;\r\n  right: 8rpx;\r\n  font-size: 22rpx;\r\n  color: #8E8E93;\r\n  background: rgba(248, 249, 250, 0.9);\r\n  padding: 4rpx 8rpx;\r\n  border-radius: 8rpx;\r\n  pointer-events: none;\r\n  z-index: 2;\r\n  backdrop-filter: blur(4rpx);\r\n}\r\n\r\n/* 按钮容器 */\r\n.button-container {\r\n  padding: 32rpx;\r\n  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));\r\n}\r\n\r\n.primary-button {\r\n  background: #007AFF;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 12rpx;\r\n  padding: 24rpx;\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 88rpx;\r\n}\r\n\r\n.primary-button:disabled {\r\n  background: #C7C7CC;\r\n  color: #8E8E93;\r\n  opacity: 0.7;\r\n}\r\n\r\n.primary-button.loading {\r\n  background: #0056D6;\r\n  opacity: 0.9;\r\n}\r\n\r\n.button-loading {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 12rpx;\r\n  color: white;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  border: 4rpx solid rgba(255, 255, 255, 0.3);\r\n  border-top: 4rpx solid white;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n/* 加载遮罩 */\r\n.loading-mask {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.4);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 9999;\r\n}\r\n\r\n.loading-content {\r\n  background: #FFFFFF;\r\n  padding: 48rpx;\r\n  border-radius: 16rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 24rpx;\r\n}\r\n\r\n.loading-text {\r\n  font-size: 28rpx;\r\n  color: #1C1C1E;\r\n}\r\n\r\n/* H5平台图片尺寸优化 */\r\n/* #ifdef H5 */\r\n@media screen and (min-width: 600px) {\r\n  .image-grid {\r\n    max-width: 600px;\r\n  }\r\n  \r\n  .image-item, .add-photo {\r\n    width: 130px;\r\n    height: 130px;\r\n    max-width: 130px;\r\n    max-height: 130px;\r\n  }\r\n}\r\n\r\n@media screen and (min-width: 900px) {\r\n  .image-grid {\r\n    max-width: 800px;\r\n  }\r\n  \r\n  .image-item, .add-photo {\r\n    width: 140px;\r\n    height: 140px;\r\n    max-width: 140px;\r\n    max-height: 140px;\r\n  }\r\n}\r\n/* #endif */\r\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rectification-submit.vue?vue&type=style&index=0&id=5b99b5c7&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rectification-submit.vue?vue&type=style&index=0&id=5b99b5c7&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775842434\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}