{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/patrol_pkg/route/index.vue?11e3", "webpack:///D:/Xwzc/pages/patrol_pkg/route/index.vue?7db0", "webpack:///D:/Xwzc/pages/patrol_pkg/route/index.vue?ee82", "webpack:///D:/Xwzc/pages/patrol_pkg/route/index.vue?401f", "uni-app:///pages/patrol_pkg/route/index.vue", "webpack:///D:/Xwzc/pages/patrol_pkg/route/index.vue?92dd", "webpack:///D:/Xwzc/pages/patrol_pkg/route/index.vue?52a2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "PEmptyState", "data", "routeList", "loading", "keyword", "statusFilter", "loadMoreStatus", "showFab", "pagination", "page", "pageSize", "total", "pageCount", "isRefreshing", "onLoad", "onShow", "getApp", "onPullDownRefresh", "onReachBottom", "methods", "formatDate", "formatDistance", "calculateDistance", "Math", "deg2rad", "calculateRouteDistance", "p1", "p2", "totalDistance", "calculateRouteDistanceFromPoints", "getRouteList", "reset", "params", "PatrolApi", "res", "promises", "route_id", "detailRes", "pointsDetail", "validPoints", "point", "route", "resolve", "Promise", "uni", "title", "icon", "refreshList", "loadMore", "handleSearch", "handleSearchClear", "handleStatusFilter", "handleToggleStatus", "newStatus", "status", "handleEdit", "url", "handleDelete", "content", "success", "result", "handleAdd", "handleDetail"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvEA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACqJnnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;MACA;MACAC;MACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;MACA;MACA;MAEA,+DACAC;MACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;QACA;QACA;QAEA;QAEA,6CACAC,aACAA,cACAC,aACAA,aACA;QAEAC;MACA;MAEA;IACA;IAEA;IACAC;MACA;MAEA;MACA;QACA;QACA;QAEA;QAEA;QACA;QACA;QACA;QAEA;QAEA;QACAD;MACA;MAEA;IACA;IAEA;IACAE;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;kBACA;kBACA;kBACA;gBACA;;gBAEA;gBACA;gBAAA;gBAGAC;kBACAvB;kBACAC;kBACAN;gBACA,GAEA;gBACA;kBACA4B;gBACA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;kBACA;gBACA;kBACA;gBACA;;gBAEA;gBACAC;kBACA;oBACA;sBAAA;wBAAA;wBAAA;0BAAA;4BAAA;8BAAA;gCAAA;gCAAA;gCAAA,OAGAF;kCACAG;gCACA;8BAAA;gCAFAC;gCAIA;kCACAC,4CAEA;kCACAC;oCAAA,OACAC,kBACA,+CACA;kCAAA,EACA;kCAEA;oCACA;oCACAC;kCACA;gCACA;gCAAA;gCAAA;8BAAA;gCAAA;gCAAA;8BAAA;gCAIAC;8BAAA;8BAAA;gCAAA;4BAAA;0BAAA;wBAAA;sBAAA,CACA;sBAAA;wBAAA;sBAAA;oBAAA;kBACA;kBACA;gBACA,IAEA;gBAAA;gBAAA,OACAC;cAAA;gBACA;;gBAEA;gBACA;kBACA;kBACA/B;kBAEA;oBACAH;oBACAC;oBACAC;oBACAC;kBACA;;kBAEA;kBACA;oBACA;kBACA;oBACA;kBACA;gBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEAgC;kBACAC;kBACAC;gBACA;cAAA;gBAGA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAF;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBACAF;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAG;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBACA;gBACAH;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAI;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAR;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIAF;kBACAC;gBACA;gBAAA;gBAGAQ;gBAAA;gBAAA,OAEApB;kBACAG;kBACAkB;gBACA;cAAA;gBAHApB;gBAKA;kBACA;kBACA;kBAEAU;oBACAC;oBACAC;kBACA;gBACA;kBACAF;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAF;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEAF;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAW;MACA;QACAX;UACAC;UACAC;QACA;QACA;MACA;MACAF;QACAY;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACAb;UACAC;UACAC;QACA;QACA;MACA;MACAF;QACAC;QACAa;QACAC;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACAzB;sBAAA;sBAAA;oBAAA;oBACAU;sBACAC;oBACA;oBAAA;oBAAA;oBAAA,OAGAZ;sBACAG;oBACA;kBAAA;oBAFAwB;oBAIA;sBACA;sBACA;sBAEAhB;wBACAC;wBACAC;sBACA;oBACA;sBACAF;wBACAC;wBACAC;sBACA;oBACA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAEAF;sBACAC;sBACAC;oBACA;kBAAA;oBAAA;oBAEAF;oBAAA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAGA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACAiB;MACAjB;QACAY;MACA;IACA;IAEA;IACAM;MACA;QACAlB;UACAC;UACAC;QACA;QACA;MACA;MACAF;QACAY;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjjBA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/patrol_pkg/route/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/patrol_pkg/route/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=0f46968f&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/patrol_pkg/route/index.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=0f46968f&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-load-more/components/uni-load-more/uni-load-more\" */ \"@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.pagination.total || _vm.routeList.length\n  var g1 = _vm.loading && _vm.routeList.length === 0\n  var l0 = !g1\n    ? _vm.__map(_vm.routeList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g2 = (item.points && item.points.length) || 0\n        var m0 = _vm.formatDate(item.createTime)\n        return {\n          $orig: $orig,\n          g2: g2,\n          m0: m0,\n        }\n      })\n    : null\n  var g3 = !g1 ? _vm.routeList.length : null\n  var g4 = !g1 ? _vm.routeList.length === 0 && !_vm.loading : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        g3: g3,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"route-container\">\n\t\t<!-- 头部区域 -->\n\t\t<view class=\"header-section\">\n\t\t\t<view class=\"header-content\">\n\t\t\t\t<view class=\"header-left\">\n\t\t\t\t\t<text class=\"header-title\">线路管理</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"header-right\">\n\t\t\t\t\t<view class=\"route-count\">\n\t\t\t\t\t\t<text>{{ pagination.total || routeList.length }}</text>\n\t\t\t\t\t\t<text>个线路</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 搜索区域 -->\n\t\t<view class=\"search-area\">\n\t\t\t<view class=\"search-box\">\n\t\t\t\t<uni-icons type=\"search\" size=\"18\" color=\"#8F959E\"></uni-icons>\n\t\t\t\t<input\n\t\t\t\t\tv-model=\"keyword\"\n\t\t\t\t\tplaceholder=\"搜索线路名称\"\n\t\t\t\t\tplaceholder-class=\"placeholder\"\n\t\t\t\t\t@confirm=\"handleSearch\"\n\t\t\t\t\t@input=\"handleSearch\"\n\t\t\t\t/>\n\t\t\t\t<view class=\"clear-button\" v-if=\"keyword\" @click=\"handleSearchClear\">\n\t\t\t\t\t<uni-icons type=\"clear\" size=\"14\" color=\"#8F959E\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 筛选区域 -->\n\t\t<view class=\"filter-area-wrapper\">\n\t\t\t<scroll-view scroll-x=\"true\" class=\"filter-scroll scroll-view-hidden-scrollbar\" :show-scrollbar=\"false\">\n\t\t\t\t<view class=\"filter-area\">\n\t\t\t\t\t<view class=\"filter-item\" :class=\"{ active: statusFilter === '' }\" @click=\"handleStatusFilter('')\">\n\t\t\t\t\t\t<text>全部</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"filter-item\" :class=\"{ active: statusFilter === 1 }\" @click=\"handleStatusFilter(1)\">\n\t\t\t\t\t\t<text>启用中</text>\n\t\t\t\t\t\t<view class=\"status-dot active-dot\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"filter-item\" :class=\"{ active: statusFilter === 0 }\" @click=\"handleStatusFilter(0)\">\n\t\t\t\t\t\t<text>已停用</text>\n\t\t\t\t\t\t<view class=\"status-dot inactive-dot\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</view>\n\t\t\n\t\t<!-- 列表区域 -->\n\t\t<scroll-view \n\t\t\tscroll-y=\"true\" \n\t\t\tclass=\"list-area scroll-view-hidden-scrollbar\"\n\t\t\t@scrolltolower=\"loadMore\"\n\t\t\trefresher-enabled\n\t\t\t:refresher-triggered=\"isRefreshing\"\n\t\t\t@refresherrefresh=\"refreshList\"\n\t\t\t:show-scrollbar=\"false\"\n\t\t>\n\t\t\t<!-- 加载中占位 -->\n\t\t\t<view class=\"loading-skeleton\" v-if=\"loading && routeList.length === 0\">\n\t\t\t\t<view class=\"skeleton-item\" v-for=\"i in 3\" :key=\"i\">\n\t\t\t\t\t<view class=\"skeleton-header\">\n\t\t\t\t\t\t<view class=\"skeleton-title\"></view>\n\t\t\t\t\t\t<view class=\"skeleton-status\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"skeleton-details\">\n\t\t\t\t\t\t<view class=\"skeleton-line\"></view>\n\t\t\t\t\t\t<view class=\"skeleton-line\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"skeleton-actions\">\n\t\t\t\t\t\t<view class=\"skeleton-action\"></view>\n\t\t\t\t\t\t<view class=\"skeleton-action\"></view>\n\t\t\t\t\t\t<view class=\"skeleton-action\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 列表区域 -->\n\t\t\t<view class=\"list-content\" v-else>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"route-item\" \n\t\t\t\t\tv-for=\"(item, index) in routeList\" \n\t\t\t\t\t:key=\"item._id\" \n\t\t\t\t\t@click=\"handleDetail(item)\"\n\t\t\t\t>\n\t\t\t\t\t<view class=\"route-info\">\n\t\t\t\t\t\t<view class=\"route-header\">\n\t\t\t\t\t\t\t<text class=\"route-name\">{{ item.name }}</text>\n\t\t\t\t\t\t\t<view class=\"route-status\" :class=\"{ 'status-enabled': item.status === 1, 'status-disabled': item.status === 0 }\">\n\t\t\t\t\t\t\t\t{{ item.status === 1 ? '启用中' : '已停用' }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"route-content\">\n\t\t\t\t\t\t\t<view class=\"route-point-info\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"map\" size=\"16\" color=\"#666666\"></uni-icons>\n\t\t\t\t\t\t\t\t<text class=\"route-value\">{{ (item.points && item.points.length) || 0 }}个点位</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"route-time\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"14\" color=\"#999999\"></uni-icons>\n\t\t\t\t\t\t\t\t<text>{{ formatDate(item.createTime) }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"route-actions\">\n\t\t\t\t\t\t<view class=\"action-item\" @click.stop=\"handleToggleStatus(item, index)\">\n\t\t\t\t\t\t\t<uni-icons :type=\"item.status === 1 ? 'eye-slash' : 'eye'\" size=\"18\" :color=\"item.status === 1 ? '#666666' : '#1677FF'\"></uni-icons>\n\t\t\t\t\t\t\t<text class=\"action-label\">{{ item.status === 1 ? '禁用' : '启用' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"action-item\" @click.stop=\"handleEdit(item)\">\n\t\t\t\t\t\t\t<uni-icons type=\"compose\" size=\"18\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t<text class=\"action-label\">编辑</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"action-item\" @click.stop=\"handleDelete(item, index)\">\n\t\t\t\t\t\t\t<uni-icons type=\"trash\" size=\"18\" color=\"#FF3B30\"></uni-icons>\n\t\t\t\t\t\t\t<text class=\"action-label\">删除</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 加载更多区域 -->\n\t\t\t\t<uni-load-more v-if=\"routeList.length > 0\" :status=\"loadMoreStatus\"></uni-load-more>\n\t\t\t\t\n\t\t\t\t<!-- 空列表提示 -->\n\t\t\t\t<view class=\"empty-container\" v-if=\"routeList.length === 0 && !loading\">\n\t\t\t\t\t<p-empty-state \n\t\t\t\t\t\ttype=\"data\" \n\t\t\t\t\t\ttext=\"暂无线路数据\" \n\t\t\t\t\t/>\n\t\t\t\t\t<view class=\"empty-tip\" @click=\"handleAdd\">\n\t\t\t\t\t\t<text>添加线路</text>\n\t\t\t\t\t\t<uni-icons type=\"plus\" size=\"14\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</scroll-view>\n\t\t\n\t\t<!-- 悬浮按钮 -->\n\t\t<view class=\"fab-button\" v-if=\"showFab\" @click=\"handleAdd\">\n\t\t\t<uni-icons type=\"plusempty\" size=\"24\" color=\"#FFFFFF\"></uni-icons>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport PatrolApi from '@/utils/patrol-api.js';\nimport PEmptyState from '@/components/p-empty-state/p-empty-state.vue';\n\nexport default {\n\tcomponents: {\n\t\tPEmptyState\n\t},\n\tdata() {\n\t\treturn {\n\t\t\trouteList: [], // 线路列表\n\t\t\tloading: false, // 加载状态\n\t\t\tkeyword: '', // 搜索关键词\n\t\t\tstatusFilter: '', // 状态筛选\n\t\t\tloadMoreStatus: 'more', // 加载更多状态 more|loading|noMore\n\t\t\tshowFab: false, // 控制悬浮按钮显示\n\t\t\tpagination: {\n\t\t\t\tpage: 1,\n\t\t\t\tpageSize: 10,\n\t\t\t\ttotal: 0,\n\t\t\t\tpageCount: 0\n\t\t\t},\n\t\t\tisRefreshing: false, // 下拉刷新状态\n\t\t}\n\t},\n\tonLoad() {\n\t\tthis.getRouteList(true);\n\t},\n\tonShow() {\n\t\t// 如果有页面数据需要刷新的标记，则刷新列表\n\t\tif (getApp().globalData && getApp().globalData.routeListNeedRefresh) {\n\t\t\t// 清除标记\n\t\t\tgetApp().globalData.routeListNeedRefresh = false;\n\t\t\t// 刷新列表\n\t\t\tthis.refreshList();\n\t\t}\n\t},\n\tonPullDownRefresh() {\n\t\tthis.refreshList();\n\t},\n\tonReachBottom() {\n\t\tthis.loadMore();\n\t},\n\tmethods: {\n\t\t// 格式化时间\n\t\tformatDate(timestamp) {\n\t\t\tif (!timestamp) return '未知';\n\t\t\tconst date = new Date(timestamp);\n\t\t\treturn `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n\t\t},\n\t\t\n\t\t// 格式化距离\n\t\tformatDistance(meters) {\n\t\t\tif (!meters) return '0米';\n\t\t\tif (meters < 1000) {\n\t\t\t\treturn `${meters.toFixed(0)}米`;\n\t\t\t} else {\n\t\t\t\treturn `${(meters / 1000).toFixed(2)}公里`;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 计算两点之间的距离(米)\n\t\tcalculateDistance(lat1, lng1, lat2, lng2) {\n\t\t\tif (!lat1 || !lng1 || !lat2 || !lng2) return 0;\n\t\t\t\n\t\t\tconst radLat1 = this.deg2rad(lat1);\n\t\t\tconst radLat2 = this.deg2rad(lat2);\n\t\t\tconst a = radLat1 - radLat2;\n\t\t\tconst b = this.deg2rad(lng1) - this.deg2rad(lng2);\n\t\t\t\n\t\t\tconst s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a/2),2) + \n\t\t\t\tMath.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b/2),2)));\n\t\t\tconst earthRadius = 6378137; // 地球半径，单位米\n\t\t\tconst distance = s * earthRadius;\n\t\t\t\n\t\t\treturn Math.round(distance);\n\t\t},\n\t\t\n\t\t// 角度转弧度\n\t\tdeg2rad(deg) {\n\t\t\treturn deg * (Math.PI / 180);\n\t\t},\n\t\t\n\t\t// 计算路线总距离\n\t\tcalculateRouteDistance(points) {\n\t\t\tif (!points || points.length < 2) return 0;\n\t\t\t\n\t\t\tlet totalDistance = 0;\n\t\t\tfor (let i = 0; i < points.length - 1; i++) {\n\t\t\t\tconst p1 = points[i];\n\t\t\t\tconst p2 = points[i+1];\n\t\t\t\t\n\t\t\t\tif (!p1.latitude || !p1.longitude || !p2.latitude || !p2.longitude) continue;\n\t\t\t\t\n\t\t\t\tconst segmentDistance = this.calculateDistance(\n\t\t\t\t\tp1.latitude, \n\t\t\t\t\tp1.longitude, \n\t\t\t\t\tp2.latitude, \n\t\t\t\t\tp2.longitude\n\t\t\t\t);\n\t\t\t\t\n\t\t\t\ttotalDistance += segmentDistance;\n\t\t\t}\n\t\t\t\n\t\t\treturn totalDistance;\n\t\t},\n\t\t\n\t\t// 从点位详情计算路线总距离\n\t\tcalculateRouteDistanceFromPoints(points) {\n\t\t\tif (!points || points.length < 2) return 0;\n\t\t\t\n\t\t\tlet totalDistance = 0;\n\t\t\tfor (let i = 0; i < points.length - 1; i++) {\n\t\t\t\tconst p1 = points[i];\n\t\t\t\tconst p2 = points[i+1];\n\t\t\t\t\n\t\t\t\tif (!p1.location || !p2.location) continue;\n\t\t\t\t\n\t\t\t\tconst lat1 = parseFloat(p1.location.latitude) || 0;\n\t\t\t\tconst lng1 = parseFloat(p1.location.longitude) || 0;\n\t\t\t\tconst lat2 = parseFloat(p2.location.latitude) || 0;\n\t\t\t\tconst lng2 = parseFloat(p2.location.longitude) || 0;\n\t\t\t\t\n\t\t\t\tif (!lat1 || !lng1 || !lat2 || !lng2) continue;\n\t\t\t\t\n\t\t\t\tconst segmentDistance = this.calculateDistance(lat1, lng1, lat2, lng2);\n\t\t\t\ttotalDistance += segmentDistance;\n\t\t\t}\n\t\t\t\n\t\t\treturn totalDistance;\n\t\t},\n\t\t\n\t\t// 获取线路列表\n\t\tasync getRouteList(reset = false) {\n\t\t\tif (this.loading) return;\n\t\t\t\n\t\t\t// 如果是重置，则重置分页\n\t\t\tif (reset) {\n\t\t\t\tthis.pagination.page = 1;\n\t\t\t\tthis.routeList = [];\n\t\t\t\tthis.showFab = false; // 重置时隐藏悬浮按钮\n\t\t\t}\n\t\t\t\n\t\t\tthis.loading = true;\n\t\t\tthis.loadMoreStatus = 'loading';\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst params = {\n\t\t\t\t\tpage: this.pagination.page,\n\t\t\t\t\tpageSize: this.pagination.pageSize,\n\t\t\t\t\tkeyword: this.keyword,\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t// 添加状态筛选\n\t\t\t\tif (this.statusFilter !== '') {\n\t\t\t\t\tparams.status = this.statusFilter;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst res = await PatrolApi.callRouteFunction('getRouteList', params);\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\t// 如果是重置，则直接赋值，否则追加\n\t\t\t\t\tif (reset) {\n\t\t\t\t\t\tthis.routeList = res.data.list;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.routeList = [...this.routeList, ...res.data.list];\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 获取线路详情，计算总距离\n\t\t\t\t\tconst promises = this.routeList.map(route => {\n\t\t\t\t\t\tif (route.points && route.points.length >= 2) {\n\t\t\t\t\t\t\treturn new Promise(async (resolve) => {\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\t// 获取线路详情\n\t\t\t\t\t\t\t\t\tconst detailRes = await PatrolApi.callRouteFunction('getRouteDetail', {\n\t\t\t\t\t\t\t\t\t\troute_id: route._id\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tif (detailRes.code === 0 && detailRes.data && detailRes.data.pointsDetail) {\n\t\t\t\t\t\t\t\t\t\tconst pointsDetail = detailRes.data.pointsDetail;\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t// 检查点位是否包含有效坐标\n\t\t\t\t\t\t\t\t\t\tconst validPoints = pointsDetail.filter(point => \n\t\t\t\t\t\t\t\t\t\t\tpoint.location && \n\t\t\t\t\t\t\t\t\t\t\ttypeof point.location.latitude === 'number' && \n\t\t\t\t\t\t\t\t\t\t\ttypeof point.location.longitude === 'number'\n\t\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\tif (validPoints.length >= 2) {\n\t\t\t\t\t\t\t\t\t\t\t// 计算线路总距离\n\t\t\t\t\t\t\t\t\t\t\troute.totalDistance = this.calculateRouteDistanceFromPoints(validPoints);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\t\t\t// 捕获错误但不影响列表显示\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tresolve();\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn Promise.resolve();\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 等待所有请求完成后更新视图\n\t\t\t\t\tawait Promise.all(promises);\n\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t\n\t\t\t\t\t// 更新分页信息\n\t\t\t\t\tif (res.data.page && res.data.pageSize && res.data.total) {\n\t\t\t\t\t\t// 计算总页数\n\t\t\t\t\t\tconst pageCount = Math.ceil(res.data.total / res.data.pageSize);\n\t\t\t\t\t\t\n\t\t\t\t\t\tthis.pagination = {\n\t\t\t\t\t\t\tpage: res.data.page,\n\t\t\t\t\t\t\tpageSize: res.data.pageSize,\n\t\t\t\t\t\t\ttotal: res.data.total,\n\t\t\t\t\t\t\tpageCount: pageCount\n\t\t\t\t\t\t};\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 更新加载更多状态\n\t\t\t\t\t\tif (this.pagination.page >= pageCount) {\n\t\t\t\t\t\t\tthis.loadMoreStatus = 'noMore';\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.loadMoreStatus = 'more';\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 没有分页信息，默认为没有更多数据\n\t\t\t\t\t\tthis.loadMoreStatus = 'noMore';\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.message || '获取线路列表失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 在数据加载完成后显示悬浮按钮\n\t\t\t\tthis.showFab = true;\n\t\t\t} catch (e) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '获取线路列表出错',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 刷新列表 - 修改刷新功能以支持下拉刷新\n\t\tasync refreshList() {\n\t\t\tthis.isRefreshing = true;\n\t\t\tawait this.getRouteList(true);\n\t\t\tthis.isRefreshing = false;\n\t\t\tuni.stopPullDownRefresh(); // 停止系统下拉刷新\n\t\t},\n\t\t\n\t\t// 加载更多\n\t\tloadMore() {\n\t\t\tif (this.loadMoreStatus !== 'more') return;\n\t\t\t\n\t\t\t// 下一页\n\t\t\tthis.pagination.page++;\n\t\t\tthis.getRouteList();\n\t\t},\n\t\t\n\t\t// 搜索\n\t\thandleSearch() {\n\t\t\tthis.getRouteList(true);\n\t\t},\n\t\t\n\t\t// 处理搜索清除\n\t\thandleSearchClear() {\n\t\t\tthis.keyword = '';\n\t\t\tthis.handleSearch();\n\t\t},\n\t\t\n\t\t// 状态筛选\n\t\thandleStatusFilter(status) {\n\t\t\tthis.statusFilter = status;\n\t\t\tthis.getRouteList(true);\n\t\t},\n\t\t\n\t\t// 切换状态\n\t\tasync handleToggleStatus(item, index) {\n\t\t\tif (!item || !item._id) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '无效的线路ID',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '正在处理...'\n\t\t\t});\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst newStatus = item.status === 1 ? 0 : 1;\n\t\t\t\t\n\t\t\t\tconst res = await PatrolApi.callRouteFunction('toggleRouteStatus', {\n\t\t\t\t\troute_id: item._id,\n\t\t\t\t\tstatus: newStatus\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t// 更新本地数据\n\t\t\t\t\tthis.routeList[index].status = newStatus;\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: newStatus === 1 ? '启用成功' : '禁用成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.message || '操作失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '操作出错',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 编辑线路\n\t\thandleEdit(item) {\n\t\t\tif (!item || !item._id) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '无效的线路ID',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tuni.navigateTo({\n\t\t\t\turl: './edit?id=' + item._id\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 删除线路\n\t\thandleDelete(item, index) {\n\t\t\tif (!item || !item._id) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '无效的线路ID',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '确认删除',\n\t\t\t\tcontent: `确定要删除线路\"${item.name}\"吗？`,\n\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\ttitle: '正在删除...'\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst result = await PatrolApi.callRouteFunction('deleteRoute', {\n\t\t\t\t\t\t\t\troute_id: item._id\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tif (result.code === 0) {\n\t\t\t\t\t\t\t\t// 从列表中移除\n\t\t\t\t\t\t\t\tthis.routeList.splice(index, 1);\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '删除成功',\n\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: result.message || '删除失败',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '删除出错',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} finally {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 添加线路\n\t\thandleAdd() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: './add'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 处理跳转到详情\n\t\thandleDetail(item) {\n\t\t\tif (!item || !item._id) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '无效的线路ID',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tuni.navigateTo({\n\t\t\t\turl: './detail?id=' + item._id\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\">\n/* 主色调变量 */\n$primary-color: #1677FF; // 支付宝蓝\n$primary-light: #E7F1FF;\n$primary-dark: #0E5FD8;\n\n$success-color: #07C160;\n$warning-color: #FFA300;\n$danger-color: #FF3B30;\n$info-color: #8F959E;\n\n$text-primary: #2C3E50;\n$text-secondary: #666666;\n$text-tertiary: #999999;\n$border-color: #EAEAEA;\n$background: #F5F5F5;\n\n$radius-sm: 6rpx;\n$radius-md: 12rpx;\n$radius-lg: 16rpx;\n$radius-xl: 24rpx;\n$radius-full: 999rpx;\n\n$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n$shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\n$shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);\n\n/* 淡入动画 */\n@keyframes fadeIn {\n\tfrom {\n\t\topacity: 0;\n\t\ttransform: translateY(20rpx);\n\t}\n\tto {\n\t\topacity: 1;\n\t\ttransform: translateY(0);\n\t}\n}\n\n.route-container {\n\tbackground-color: $background;\n\tdisplay: flex;\n\tflex-direction: column;\n\theight: 100vh;\n\tposition: relative;\n\toverflow: hidden;\n}\n\n.header-section {\n\tbackground-color: #FFFFFF;\n\tpadding: 20rpx 30rpx;\n\tborder-bottom: 1rpx solid $border-color;\n\tposition: relative;\n\tbox-shadow: $shadow-sm;\n\tz-index: 5;\n\t/* 固定头部 */\n\twidth: 100%;\n\tbox-sizing: border-box;\n\t\n\t.header-content {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t}\n\t\n\t.header-title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: 600;\n\t\tcolor: $text-primary;\n\t\tposition: relative;\n\t\tpadding-left: 24rpx;\n\t\t\n\t\t&:before {\n\t\t\tcontent: '';\n\t\t\tposition: absolute;\n\t\t\tleft: 0;\n\t\t\ttop: 50%;\n\t\t\ttransform: translateY(-50%);\n\t\t\twidth: 8rpx;\n\t\t\theight: 32rpx;\n\t\t\tbackground-color: $primary-color;\n\t\t\tborder-radius: $radius-sm;\n\t\t}\n\t}\n\t\n\t.route-count {\n\t\tbackground-color: $primary-light;\n\t\tborder-radius: $radius-full;\n\t\tpadding: 4rpx 16rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\ttransition: all 0.3s ease;\n\t\t\n\t\ttext {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: $primary-color;\n\t\t\t\n\t\t\t&:first-child {\n\t\t\t\tfont-weight: 600;\n\t\t\t\tmargin-right: 4rpx;\n\t\t\t}\n\t\t}\n\t\t\n\t\t&:active {\n\t\t\ttransform: scale(0.95);\n\t\t}\n\t}\n}\n\n/* 搜索区域样式 */\n.search-area {\n\tbackground-color: #FFFFFF;\n\tpadding: 20rpx 30rpx 0rpx;\n\tposition: relative;\n\tz-index: 4;\n\twidth: 100%;\n\tbox-sizing: border-box;\n}\n\n.search-box {\n\tdisplay: flex;\n\talign-items: center;\n\tbackground-color: $background;\n\tborder-radius: $radius-full;\n\tpadding: 16rpx 20rpx;\n\ttransition: all 0.3s ease;\n\t\n\t&:focus-within {\n\t\tbox-shadow: 0 0 0 4rpx rgba(22, 119, 255, 0.1);\n\t}\n\t\n\tinput {\n\t\tflex: 1;\n\t\theight: 60rpx;\n\t\tmargin-left: 12rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: $text-primary;\n\t}\n\t\n\t.placeholder {\n\t\tcolor: $text-tertiary;\n\t}\n\t\n\t.clear-button {\n\t\tpadding: 10rpx;\n\t\topacity: 0.7;\n\t\t\n\t\t&:active {\n\t\t\topacity: 1;\n\t\t}\n\t}\n}\n\n/* 筛选区域样式 */\n.filter-area-wrapper {\n\tbackground-color: #FFFFFF;\n\tz-index: 3;\n\tposition: relative;\n\twidth: 100%;\n\tbox-sizing: border-box;\n}\n\n.filter-scroll {\n\tbackground-color: #FFFFFF;\n\twhite-space: nowrap;\n\tborder-bottom: 1rpx solid $border-color;\n\tposition: relative;\n\tz-index: 3;\n\t/* 统一微信小程序隐藏滚动条样式 */\n\tscrollbar-width: none; /* Firefox */\n\t-ms-overflow-style: none; /* IE/Edge */\n\t&::-webkit-scrollbar {\n\t\tdisplay: none; /* Chrome/Safari */\n\t\twidth: 0 !important;\n\t\theight: 0 !important;\n\t\tbackground: transparent;\n\t}\n}\n\n.filter-area {\n\tdisplay: inline-flex;\n\tpadding: 16rpx 30rpx;\n}\n\n.filter-item {\n\tdisplay: inline-flex;\n\talign-items: center;\n\tpadding: 12rpx 30rpx;\n\tmargin-right: 20rpx;\n\tfont-size: 28rpx;\n\tcolor: $text-secondary;\n\tbackground-color: $background;\n\tborder-radius: $radius-full;\n\ttransition: all 0.3s ease;\n\t\n\t.status-dot {\n\t\twidth: 10rpx;\n\t\theight: 10rpx;\n\t\tborder-radius: 50%;\n\t\tmargin-left: 8rpx;\n\t}\n\t\n\t.active-dot {\n\t\tbackground-color: $success-color;\n\t}\n\t\n\t.inactive-dot {\n\t\tbackground-color: $danger-color;\n\t}\n\t\n\t&.active {\n\t\tbackground-color: $primary-light;\n\t\tcolor: $primary-color;\n\t\tfont-weight: 500;\n\t\ttransform: scale(1.05);\n\t}\n\t\n\t&:active {\n\t\topacity: 0.8;\n\t\ttransform: scale(0.95);\n\t}\n}\n\n.list-area {\n\tflex: 1;\n\theight: calc(100vh - 300rpx);\n\toverflow-y: auto;\n\t/* 隐藏滚动条 */\n\tscrollbar-width: none; /* Firefox */\n\t-ms-overflow-style: none; /* IE/Edge */\n\t&::-webkit-scrollbar {\n\t\tdisplay: none; /* Chrome/Safari */\n\t\twidth: 0 !important;\n\t\theight: 0 !important;\n\t\tbackground: transparent;\n\t}\n}\n\n.list-content {\n\tpadding: 20rpx;\n}\n\n.route-item {\n\tbackground-color: #FFFFFF;\n\tborder-radius: 12rpx;\n\toverflow: hidden;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: $shadow-sm;\n}\n\n.route-info {\n\tpadding: 20rpx 24rpx;\n}\n\n.route-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 12rpx;\n}\n\n.route-name {\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n\tcolor: $text-primary;\n}\n\n.route-status {\n\tfont-size: 24rpx;\n\tpadding: 4rpx 16rpx;\n\tborder-radius: $radius-full;\n\tfont-weight: 400;\n\t\n\t&.status-enabled {\n\t\tbackground-color: #E6F7F0;\n\t\tcolor: $success-color;\n\t}\n\t\n\t&.status-disabled {\n\t\tbackground-color: #F5F5F5;\n\t\tcolor: $info-color;\n\t}\n}\n\n.route-content {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 4rpx;\n\tjustify-content: space-between;\n}\n\n.route-point-info {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.route-value {\n\tfont-size: 26rpx;\n\tmargin-left: 8rpx;\n\tcolor: $text-secondary;\n}\n\n.route-time {\n\tfont-size: 24rpx;\n\tcolor: $text-tertiary;\n\tdisplay: flex;\n\talign-items: center;\n\t\n\ttext {\n\t\tmargin-left: 6rpx;\n\t}\n}\n\n.route-actions {\n\tdisplay: flex;\n\tjustify-content: flex-end;\n\talign-items: center;\n\tpadding: 10rpx 20rpx;\n\tborder-top: 1px solid #EEEEEE;\n\tbackground-color: #FAFAFA;\n}\n\n.action-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 6rpx 0;\n\tmargin-left: 20rpx;\n\t\n\t.action-label {\n\t\tfont-size: 26rpx;\n\t\tmargin-left: 6rpx;\n\t\tcolor: $text-secondary;\n\t}\n}\n\n/* 空状态容器样式 */\n.empty-container {\n\tpadding: 80rpx 0;\n\tdisplay: flex;\n\tflex-direction: column;\n\tjustify-content: center;\n\talign-items: center;\n\tanimation: fadeIn 0.5s ease-out;\n}\n\n.empty-tip {\n\tfont-size: 26rpx;\n\tcolor: $text-tertiary;\n\tmargin-top: 20rpx;\n\tpadding: 10rpx 30rpx;\n\tbackground-color: $primary-light;\n\tborder-radius: $radius-full;\n\topacity: 0.8;\n\tdisplay: flex;\n\talign-items: center;\n\t\n\tuni-icons {\n\t\tmargin-left: 8rpx;\n\t}\n}\n\n/* 悬浮添加按钮 */\n.fab-button {\n\tposition: fixed;\n\tright: 40rpx;\n\tbottom: 100rpx;\n\twidth: 100rpx;\n\theight: 100rpx;\n\tborder-radius: 50%;\n\tbackground: $primary-color;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: $shadow-lg;\n\ttransition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n\tz-index: 999;\n\tanimation: fadeIn 0.5s;\n\t\n\t&:after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tborder-radius: 50%;\n\t\tbackground-color: $primary-color;\n\t\tz-index: -1;\n\t\topacity: 0.5;\n\t\ttransform: scale(0.9);\n\t\ttransition: all 0.3s;\n\t}\n\t\n\t&:active {\n\t\ttransform: scale(0.92) rotate(90deg);\n\t\tbackground-color: $primary-dark;\n\t\t\n\t\t&:after {\n\t\t\ttransform: scale(1.1);\n\t\t\topacity: 0;\n\t\t}\n\t}\n}\n\n/* 骨架屏样式 */\n.loading-skeleton {\n\tpadding: 20rpx;\n\t\n\t.skeleton-item {\n\t\tbackground-color: #FFFFFF;\n\t\tborder-radius: $radius-lg;\n\t\tpadding: 20rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tbox-shadow: $shadow-sm;\n\t\t\n\t\t.skeleton-header {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\tmargin-bottom: 20rpx;\n\t\t\t\n\t\t\t.skeleton-title {\n\t\t\t\twidth: 200rpx;\n\t\t\t\theight: 36rpx;\n\t\t\t\tbackground-color: #EEEEEE;\n\t\t\t\tborder-radius: 8rpx;\n\t\t\t}\n\t\t\t\n\t\t\t.skeleton-status {\n\t\t\t\twidth: 100rpx;\n\t\t\t\theight: 36rpx;\n\t\t\t\tbackground-color: #EEEEEE;\n\t\t\t\tborder-radius: 20rpx;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.skeleton-details {\n\t\t\tmargin-bottom: 20rpx;\n\t\t\t\n\t\t\t.skeleton-line {\n\t\t\t\theight: 30rpx;\n\t\t\t\tbackground-color: #EEEEEE;\n\t\t\t\tborder-radius: 6rpx;\n\t\t\t\tmargin-bottom: 16rpx;\n\t\t\t\t\n\t\t\t\t&:first-child {\n\t\t\t\t\twidth: 80%;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&:last-child {\n\t\t\t\t\twidth: 60%;\n\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.skeleton-coords {\n\t\t\t\twidth: 90%;\n\t\t\t\theight: 30rpx;\n\t\t\t\tbackground-color: #EEEEEE;\n\t\t\t\tborder-radius: 6rpx;\n\t\t\t\tmargin-top: 16rpx;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.skeleton-actions {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: flex-end;\n\t\t\t\n\t\t\t.skeleton-action {\n\t\t\t\twidth: 100rpx;\n\t\t\t\theight: 48rpx;\n\t\t\t\tbackground-color: #EEEEEE;\n\t\t\t\tborder-radius: 24rpx;\n\t\t\t\tmargin-left: 16rpx;\n\t\t\t}\n\t\t}\n\t}\n}\n\n.scroll-view-hidden-scrollbar {\n\t::-webkit-scrollbar {\n\t\tdisplay: none;\n\t\twidth: 0;\n\t\theight: 0;\n\t\tcolor: transparent;\n\t}\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775847591\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}