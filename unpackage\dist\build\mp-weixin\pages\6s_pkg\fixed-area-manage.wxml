<view class="page-container data-v-530a98df"><view class="header data-v-530a98df"><view class="header-title data-v-530a98df">固定责任区管理</view><view class="header-subtitle data-v-530a98df">管理各部门固定负责的区域</view></view><block wx:if="{{loading}}"><view class="stats-loading data-v-530a98df"><view class="loading-content data-v-530a98df"><uni-icons vue-id="04ba3086-1" type="spinner-cycle" size="32" color="#007AFF" class="data-v-530a98df" bind:__l="__l"></uni-icons><text class="loading-text data-v-530a98df">加载统计数据中...</text></view></view></block><block wx:else><view class="stats-card data-v-530a98df"><view class="stat-item data-v-530a98df"><view class="stat-number data-v-530a98df">{{$root.g0}}</view><view class="stat-label data-v-530a98df">总责任区</view></view><view class="stat-item data-v-530a98df"><view class="stat-number data-v-530a98df">{{$root.g1}}</view><view class="stat-label data-v-530a98df">启用中</view></view><view class="stat-item data-v-530a98df"><view class="stat-number data-v-530a98df">{{$root.g2}}</view><view class="stat-label data-v-530a98df">已分配</view></view></view></block><view class="action-bar data-v-530a98df"><button data-event-opts="{{[['tap',[['showAddArea',['$event']]]]]}}" class="action-btn primary data-v-530a98df" bindtap="__e"><uni-icons vue-id="04ba3086-2" type="plus" size="16" color="#FFFFFF" class="data-v-530a98df" bind:__l="__l"></uni-icons><text class="data-v-530a98df">新增责任区</text></button></view><block wx:if="{{loading}}"><view class="list-loading data-v-530a98df"><view class="loading-content data-v-530a98df"><uni-icons vue-id="04ba3086-3" type="spinner-cycle" size="40" color="#007AFF" class="data-v-530a98df" bind:__l="__l"></uni-icons><text class="loading-text data-v-530a98df">加载责任区数据中...</text></view></view></block><block wx:else><block wx:if="{{$root.g3>0}}"><view class="area-list data-v-530a98df"><block wx:for="{{$root.l0}}" wx:for-item="area" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['viewAreaDetail',['$0'],[[['areaList','',index]]]]]]]}}" class="area-item data-v-530a98df" bindtap="__e"><view class="area-main data-v-530a98df"><view class="area-header data-v-530a98df"><view class="area-name data-v-530a98df">{{area.$orig.name}}</view><view class="{{['area-status','data-v-530a98df','status-'+area.$orig.status]}}">{{''+area.m0+''}}</view></view><view class="area-info data-v-530a98df"><view class="info-item data-v-530a98df"><uni-icons vue-id="{{'04ba3086-4-'+index}}" type="location" size="14" color="#8E8E93" class="data-v-530a98df" bind:__l="__l"></uni-icons><text class="data-v-530a98df">{{area.$orig.location&&area.$orig.location.area||area.$orig.location||'未设置位置'}}</text></view><block wx:if="{{area.g4}}"><view class="info-item data-v-530a98df"><uni-icons vue-id="{{'04ba3086-5-'+index}}" type="person" size="14" color="#8E8E93" class="data-v-530a98df" bind:__l="__l"></uni-icons><text class="data-v-530a98df">{{"已分配"+area.g5+"人"}}</text></view></block><view class="info-item data-v-530a98df"><uni-icons vue-id="{{'04ba3086-6-'+index}}" type="calendar" size="14" color="#8E8E93" class="data-v-530a98df" bind:__l="__l"></uni-icons><text class="data-v-530a98df">{{area.m1}}</text></view></view><block wx:if="{{area.$orig.description}}"><view class="area-description data-v-530a98df">{{''+area.$orig.description+''}}</view></block></view><view class="area-actions data-v-530a98df"><button data-event-opts="{{[['tap',[['editArea',['$0'],[[['areaList','',index]]]]]]]}}" class="action-icon-btn edit data-v-530a98df" catchtap="__e"><uni-icons vue-id="{{'04ba3086-7-'+index}}" type="compose" size="16" color="#007AFF" class="data-v-530a98df" bind:__l="__l"></uni-icons></button><button data-event-opts="{{[['tap',[['deleteArea',['$0'],[[['areaList','',index]]]]]]]}}" class="action-icon-btn delete data-v-530a98df" catchtap="__e"><uni-icons vue-id="{{'04ba3086-8-'+index}}" type="trash" size="16" color="#FF3B30" class="data-v-530a98df" bind:__l="__l"></uni-icons></button></view></view></block></view></block><block wx:else><p-empty-state vue-id="04ba3086-9" type="area" text="暂无固定责任区" description="点击上方按钮创建第一个固定责任区" class="data-v-530a98df" bind:__l="__l"></p-empty-state></block></block><uni-popup vue-id="04ba3086-10" type="center" mask-click="{{false}}" data-ref="areaFormPopup" class="data-v-530a98df vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="form-popup data-v-530a98df"><view class="form-header data-v-530a98df"><view class="form-title data-v-530a98df">{{(isEditing?'编辑':'新增')+"固定责任区"}}</view><button data-event-opts="{{[['tap',[['closeForm',['$event']]]]]}}" class="close-btn data-v-530a98df" bindtap="__e"><uni-icons vue-id="{{('04ba3086-11')+','+('04ba3086-10')}}" type="close" size="20" color="#8E8E93" class="data-v-530a98df" bind:__l="__l"></uni-icons></button></view><view class="form-content data-v-530a98df"><view class="form-item data-v-530a98df"><text class="form-label required data-v-530a98df">责任区名称</text><input class="form-input data-v-530a98df" type="text" placeholder="请输入责任区名称" maxlength="50" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['formData']]]]]}}" value="{{formData.name}}" bindinput="__e"/></view><view class="form-item data-v-530a98df"><text class="form-label data-v-530a98df">所在位置</text><input class="form-input data-v-530a98df" type="text" placeholder="请输入具体位置" maxlength="100" data-event-opts="{{[['input',[['__set_model',['$0','location','$event',[]],['formData']]]]]}}" value="{{formData.location}}" bindinput="__e"/></view><view class="form-item data-v-530a98df"><text class="form-label data-v-530a98df">责任区描述</text><textarea class="form-textarea data-v-530a98df" placeholder="请输入责任区详细描述" maxlength="200" show-count="{{true}}" data-event-opts="{{[['input',[['__set_model',['$0','description','$event',[]],['formData']]]]]}}" value="{{formData.description}}" bindinput="__e"></textarea></view><view class="form-item data-v-530a98df"><text class="form-label data-v-530a98df">状态</text><picker class="form-picker data-v-530a98df" range="{{statusOptions}}" range-key="label" value="{{statusIndex}}" data-event-opts="{{[['change',[['onStatusChange',['$event']]]]]}}" bindchange="__e"><view class="picker-value data-v-530a98df"><text class="data-v-530a98df">{{statusOptions[statusIndex].label}}</text><uni-icons vue-id="{{('04ba3086-12')+','+('04ba3086-10')}}" type="right" size="16" color="#C7C7CC" class="data-v-530a98df" bind:__l="__l"></uni-icons></view></picker></view></view><view class="form-footer data-v-530a98df"><button data-event-opts="{{[['tap',[['closeForm',['$event']]]]]}}" class="form-btn cancel data-v-530a98df" bindtap="__e">取消</button><button class="form-btn submit data-v-530a98df" loading="{{saving}}" data-event-opts="{{[['tap',[['submitForm',['$event']]]]]}}" bindtap="__e">{{''+(isEditing?'保存':'创建')+''}}</button></view></view></uni-popup></view>