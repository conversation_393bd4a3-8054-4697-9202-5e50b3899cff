<template>
  <view class="page-container">
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-title">固定责任区管理</view>
      <view class="header-subtitle">管理各部门固定负责的区域</view>
    </view>

    <!-- 统计卡片 -->
    <!-- 加载状态 -->
    <view v-if="loading" class="stats-loading">
      <view class="loading-content">
        <uni-icons type="spinner-cycle" size="32" color="#007AFF"></uni-icons>
        <text class="loading-text">加载统计数据中...</text>
      </view>
    </view>
    
    <!-- 正常统计数据 -->
    <view v-else class="stats-card">
      <view class="stat-item">
        <view class="stat-number">{{ areaList.length }}</view>
        <view class="stat-label">总责任区</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{ enabledAreas.length }}</view>
        <view class="stat-label">启用中</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{ assignedAreas.length }}</view>
        <view class="stat-label">已分配</view>
      </view>
    </view>

    <!-- 操作按钮组 -->
    <view class="action-bar">
      <button class="action-btn primary" @click="showAddArea">
        <uni-icons type="plus" size="16" color="#FFFFFF" />
        <text>新增责任区</text>
      </button>
    </view>

    <!-- 责任区列表 -->
    <!-- 加载状态 -->
    <view v-if="loading" class="list-loading">
      <view class="loading-content">
        <uni-icons type="spinner-cycle" size="40" color="#007AFF"></uni-icons>
        <text class="loading-text">加载责任区数据中...</text>
      </view>
    </view>
    
    <!-- 正常列表 -->
    <view v-else-if="areaList.length > 0" class="area-list">
      <view 
        class="area-item"
        v-for="(area, index) in areaList" 
        :key="index"
        @click="viewAreaDetail(area)"
      >
        <view class="area-main">
          <view class="area-header">
            <view class="area-name">{{ area.name }}</view>
            <view class="area-status" :class="[`status-${area.status}`]">
              {{ getStatusText(area.status) }}
            </view>
          </view>
          <view class="area-info">
            <view class="info-item">
              <uni-icons type="location" size="14" color="#8E8E93" />
              <text>{{ (area.location && area.location.area) || area.location || '未设置位置' }}</text>
            </view>
            <view class="info-item" v-if="area.assigned_users && area.assigned_users.length > 0">
              <uni-icons type="person" size="14" color="#8E8E93" />
              <text>已分配{{ area.assigned_users.length }}人</text>
            </view>
            <view class="info-item">
              <uni-icons type="calendar" size="14" color="#8E8E93" />
              <text>{{ formatDate(area.created_at) }}</text>
            </view>
          </view>
          <view class="area-description" v-if="area.description">
            {{ area.description }}
          </view>
        </view>
        <view class="area-actions">
          <button class="action-icon-btn edit" @click.stop="editArea(area)">
            <uni-icons type="compose" size="16" color="#007AFF" />
          </button>
          <button class="action-icon-btn delete" @click.stop="deleteArea(area)">
            <uni-icons type="trash" size="16" color="#FF3B30" />
          </button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <p-empty-state
      v-else
      type="area"
      text="暂无固定责任区"
      description="点击上方按钮创建第一个固定责任区"
    ></p-empty-state>

    <!-- 新增/编辑弹窗 -->
    <uni-popup ref="areaFormPopup" type="center" :mask-click="false">
      <view class="form-popup">
        <view class="form-header">
          <view class="form-title">{{ isEditing ? '编辑' : '新增' }}固定责任区</view>
          <button class="close-btn" @click="closeForm">
            <uni-icons type="close" size="20" color="#8E8E93" />
          </button>
        </view>
        
        <view class="form-content">
          <view class="form-item">
            <text class="form-label required">责任区名称</text>
            <input 
              class="form-input" 
              type="text" 
              v-model="formData.name"
              placeholder="请输入责任区名称"
              maxlength="50"
            />
          </view>
          
          <view class="form-item">
            <text class="form-label">所在位置</text>
            <input 
              class="form-input" 
              type="text" 
              v-model="formData.location"
              placeholder="请输入具体位置"
              maxlength="100"
            />
          </view>
          
          <view class="form-item">
            <text class="form-label">责任区描述</text>
            <textarea 
              class="form-textarea" 
              v-model="formData.description"
              placeholder="请输入责任区详细描述"
              maxlength="200"
              :show-count="true"
            ></textarea>
          </view>
          
          <view class="form-item">
            <text class="form-label">状态</text>
            <picker 
              :range="statusOptions" 
              range-key="label" 
              :value="statusIndex"
              @change="onStatusChange"
              class="form-picker"
            >
              <view class="picker-value">
                <text>{{ statusOptions[statusIndex].label }}</text>
                <uni-icons type="right" size="16" color="#C7C7CC" />
              </view>
            </picker>
          </view>
        </view>
        
        <view class="form-footer">
          <button class="form-btn cancel" @click="closeForm">取消</button>
          <button class="form-btn submit" @click="submitForm" :loading="saving">
            {{ isEditing ? '保存' : '创建' }}
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { callCloudFunction } from '@/utils/auth.js';

export default {
  name: 'FixedAreaManage',
  data() {
    return {
      areaList: [],
      loading: false,
      saving: false,
      isEditing: false,
      currentArea: null,
      formData: {
        name: '',
        location: '',
        description: '',
        status: 'active'
      },
      statusOptions: [
        { value: 'active', label: '启用' },
        { value: 'inactive', label: '禁用' }
      ],
      statusIndex: 0
    }
  },
  
  computed: {
    enabledAreas() {
      return this.areaList.filter(area => area.status === 'active');
    },
    assignedAreas() {
      return this.areaList.filter(area => area.assigned_users && area.assigned_users.length > 0);
    }
  },
  
      onLoad() {
      this.loadAreaList();
    },
  
  methods: {
    // 加载责任区列表
    async loadAreaList() {
      try {
        this.loading = true;
        
        // 使用认证工具调用云函数
        const result = await callCloudFunction('hygiene-area-management', {
          action: 'getAreaList',
          data: {
            type: 'fixed',
            status: 'active',
            page: 1,
            pageSize: 100
          }
        });
        this.areaList = (result.data && result.data.list) || [];
        
      } catch (error) {
        console.error('加载责任区列表失败：', error);
        
        // 认证工具已经处理了登录相关错误，这里只处理业务错误
        if (!error.message.includes('未登录') && !error.message.includes('登录')) {
          uni.showToast({
            title: error.message || '加载失败',
            icon: 'none'
          });
        }
        this.areaList = [];
      } finally {
        this.loading = false;
      }
    },
    

    
    // 显示新增表单
    showAddArea() {
      this.isEditing = false;
      this.currentArea = null;
      this.resetForm();
      this.$refs.areaFormPopup.open();
    },
    
    // 编辑责任区
    editArea(area) {
      this.isEditing = true;
      this.currentArea = area;
      this.formData = {
        name: area.name || '',
        location: (area.location && area.location.area) || area.location || '',
        description: area.description || '',
        status: area.status || 'active'
      };
      
      // 设置状态选择器的索引
      this.statusIndex = this.statusOptions.findIndex(option => option.value === area.status) || 0;
      
      this.$refs.areaFormPopup.open();
    },
    
    // 删除责任区
    deleteArea(area) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除责任区"${area.name}"吗？删除后不可恢复。`,
        confirmText: '删除',
        confirmColor: '#FF3B30',
        success: (res) => {
          if (res.confirm) {
            this.performDelete(area);
          }
        }
      });
    },
    
    // 执行删除
    async performDelete(area) {
      try {
        // 获取区域ID
        const areaId = area._id;
        if (!areaId) {
          throw new Error('责任区ID不存在，数据可能异常，请刷新页面重试');
        }
        
        // 使用认证工具调用云函数删除责任区
        await callCloudFunction('hygiene-area-management', {
          action: 'deleteArea',
          data: {
            id: areaId
          }
        });
        
        // 从本地列表中移除
        const index = this.areaList.findIndex(item => (item._id || item.id) === areaId);
        if (index > -1) {
          this.areaList.splice(index, 1);
        }
        
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('删除失败：', error);
        const errorMessage = error.message || '删除失败';
        
        // 对权限错误进行特殊处理
        if (errorMessage.includes('权限不足')) {
          uni.showModal({
            title: '权限不足',
            content: errorMessage,
            showCancel: false,
            confirmText: '我知道了'
          });
        } else {
          uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 3000
          });
        }
      }
    },
    
    // 查看详情
    viewAreaDetail(area) {
      const location = (area.location && area.location.area) || area.location || '未设置';
      const assignedCount = (area.assigned_users && area.assigned_users.length) || 0;
      
      uni.showModal({
        title: area.name,
        content: `位置：${location}\n分配人员：${assignedCount}人\n描述：${area.description || '无描述'}`,
        showCancel: false
      });
    },
    

    
    // 状态变更
    onStatusChange(e) {
      this.statusIndex = e.detail.value;
      this.formData.status = this.statusOptions[this.statusIndex].value;
    },
    
    // 提交表单
    async submitForm() {
      if (!this.validateForm()) {
        return;
      }
      
      try {
        this.saving = true;
        
        if (this.isEditing) {
          // 编辑模式 - 使用认证工具调用云函数更新
          const result = await callCloudFunction('hygiene-area-management', {
            action: 'updateArea',
            data: {
              id: this.currentArea._id,
              name: this.formData.name,
              type: 'fixed',
              description: this.formData.description,
              location: {
                area: this.formData.location
              },
              status: this.formData.status
            }
          });
          
          // 更新本地列表
          const index = this.areaList.findIndex(item => item._id === this.currentArea._id);
          if (index > -1) {
            this.areaList[index] = {
              ...this.areaList[index],
              ...this.formData,
              updated_at: new Date().toISOString()
            };
          }
        } else {
          // 新增模式 - 使用认证工具调用云函数创建
          const result = await callCloudFunction('hygiene-area-management', {
            action: 'createArea',
            data: {
              name: this.formData.name,
              type: 'fixed',
              description: this.formData.description,
              location: {
                area: this.formData.location
              },
              status: this.formData.status
            }
          });
          
          // 添加到本地列表
          this.areaList.unshift(result.data || {});
        }
        
        uni.showToast({
          title: this.isEditing ? '保存成功' : '创建成功',
          icon: 'success'
        });
        
        this.closeForm();
        
      } catch (error) {
        console.error('保存失败：', error);
        // 显示详细的错误信息
        const errorMessage = error.message || '保存失败';
        
        // 对权限错误进行特殊处理
        if (errorMessage.includes('权限不足')) {
          uni.showModal({
            title: '权限不足',
            content: errorMessage,
            showCancel: false,
            confirmText: '我知道了'
          });
        } else {
          uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 3000,
            mask: true
          });
        }
      } finally {
        this.saving = false;
      }
    },
    
    // 表单验证
    validateForm() {
      if (!this.formData.name.trim()) {
        uni.showToast({
          title: '请输入责任区名称',
          icon: 'none'
        });
        return false;
      }
      
      if (this.formData.name.length > 50) {
        uni.showToast({
          title: '责任区名称不能超过50个字符',
          icon: 'none'
        });
        return false;
      }
      
      return true;
    },
    
    // 重置表单
    resetForm() {
      this.formData = {
        name: '',
        location: '',
        description: '',
        status: 'active'
      };
      this.statusIndex = 0;
    },
    
    // 关闭表单
    closeForm() {
      this.$refs.areaFormPopup.close();
      this.resetForm();
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'active': '启用',
        'inactive': '禁用'
      };
      return statusMap[status] || '未知';
    },
    
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';
      // 处理云数据库的时间格式
      let date;
      if (typeof dateString === 'object' && dateString.$date) {
        date = new Date(dateString.$date);
      } else {
        date = new Date(dateString);
      }
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: #F8F9FA;
  padding-bottom: 40rpx;
}

.header {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  padding: 60rpx 32rpx 40rpx;
  color: white;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 24rpx;
  opacity: 0.8;
}

.stats-card {
  background: white;
  margin: 24rpx 32rpx;
  padding: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 600;
  color: #007AFF;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #8E8E93;
}

.action-bar {
  padding: 0 32rpx 24rpx;
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 26rpx;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: #007AFF;
  color: white;
}

.action-btn.secondary {
  background: white;
  color: #007AFF;
  border: 2rpx solid #007AFF;
}

.action-btn:active {
  transform: scale(0.95);
}

.area-list {
  padding: 0 32rpx;
}

.area-item {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.area-item:active {
  transform: scale(0.98);
}

.area-main {
  flex: 1;
}

.area-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.area-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #1C1C1E;
}

.area-status {
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.area-status.status-active {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.area-status.status-inactive {
  background: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}



.area-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 12rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  color: #8E8E93;
}

.area-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.area-actions {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.action-icon-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  transition: all 0.3s ease;
}

.action-icon-btn.edit {
  background: rgba(0, 122, 255, 0.1);
}

.action-icon-btn.delete {
  background: rgba(255, 59, 48, 0.1);
}

.action-icon-btn:active {
  transform: scale(0.9);
}

.form-popup {
  width: 92vw;
  max-width: 550rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
}

.form-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-left: 5rpx;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F2F2F7;
  border: none;
  margin-right: 5rpx;
}

.form-content {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

/* #ifdef MP-WEIXIN */
/* 微信小程序隐藏滚动条 */
.form-content::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  background: transparent;
}

.form-content {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
/* #endif */

/* #ifndef MP-WEIXIN */
/* 非微信小程序环境下显示细滚动条 */
.form-content::-webkit-scrollbar {
  width: 4rpx;
  height: 4rpx;
}

.form-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2rpx;
}

.form-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}

.form-content {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
}
/* #endif */

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #1C1C1E;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.form-label.required::before {
  content: '*';
  color: #FF3B30;
  margin-right: 6rpx;
}

.form-input, .form-textarea {
  width: 100%;
  background: #F2F2F7;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  box-sizing: border-box;
}

.form-textarea {
  min-height: 120rpx;
  padding: 16rpx;
  line-height: 1.5;
  resize: none;
}

.form-input {
  height: 80rpx;
  padding: 0 16rpx;
  line-height: 80rpx;
  text-align: left;
}

/* 强制占位符样式保持一致 */
.form-input::placeholder {
  line-height: 80rpx;
  color: #C7C7CC;
}

/* 兼容WebKit浏览器 */
.form-input::-webkit-input-placeholder {
  line-height: 80rpx;
  color: #C7C7CC;
}

/* 兼容Firefox */
.form-input::-moz-placeholder {
  line-height: 80rpx;
  color: #C7C7CC;
  opacity: 1;
}

/* 兼容IE/Edge */
.form-input:-ms-input-placeholder {
  line-height: 80rpx;
  color: #C7C7CC;
}

.form-picker {
  width: 100%;
}

.picker-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 80rpx;
  background: #F2F2F7;
  border-radius: 12rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  box-sizing: border-box;
}

.form-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #F2F2F7;
}

.form-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  border: none;
  transition: all 0.3s ease;
}

.form-btn.cancel {
  background: #F2F2F7;
  color: #8E8E93;
}

.form-btn.submit {
  background: #007AFF;
  color: white;
}

.form-btn:active {
  transform: scale(0.95);
}

/* 加载状态样式 */
.stats-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
  margin: 24rpx 32rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.list-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120rpx 32rpx;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #8E8E93;
  text-align: center;
}
</style> 