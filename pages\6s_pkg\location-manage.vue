<template>
  <view class="page-container" @click="handlePageClick">
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-title">位置配置管理</view>
      <view class="header-subtitle">管理工厂所有区域位置信息</view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-card">
      <!-- 加载状态 -->
      <view v-if="loading" class="stats-loading">
        <view class="loading-content">
          <uni-icons type="spinner-cycle" size="40" color="#007AFF"></uni-icons>
          <text class="loading-text">加载位置配置中...</text>
        </view>
      </view>
      <!-- 正常统计 -->
      <template v-else>
        <view class="stats-item">
          <view class="stats-number">{{ totalLocations }}</view>
          <view class="stats-label">总位置数</view>
        </view>
        <view class="stats-item">
          <view class="stats-number">{{ categoriesCount }}</view>
          <view class="stats-label">分类数</view>
        </view>
      </template>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons" @click.stop>
      <button class="action-btn primary" @click="addCategory">
        <uni-icons type="plus" size="16" color="#FFFFFF" />
        <text>新增分类</text>
      </button>
    </view>

    <!-- 位置分类列表 -->
    <!-- 加载状态 -->
    <view v-if="loading" class="list-loading">
      <view class="loading-content">
        <uni-icons type="spinner-cycle" size="40" color="#007AFF"></uni-icons>
        <text class="loading-text">加载位置数据中...</text>
      </view>
    </view>
    
    <!-- 正常列表 -->
    <view v-else class="categories-list">
      <view 
        v-for="(category, categoryIndex) in locationConfig.locations" 
        :key="categoryIndex"
        class="category-card"
      >
        <!-- 分类头部 -->
        <view class="category-header" @click.stop>
          <view class="category-title-group">
            <input 
              v-if="category.editing"
              v-model="category.category"
              class="category-title-input"
              placeholder="请输入分类名称"
              @blur="saveCategory(categoryIndex)"
              @confirm="saveCategory(categoryIndex)"
            />
            <view v-else class="category-title" @click="editCategory(categoryIndex)">
              {{ category.category }}
            </view>
            <view class="category-count">({{ category.items.length }})</view>
          </view>
          
          <view class="category-actions">
            <view class="action-icon" @click="editCategory(categoryIndex)">
              <uni-icons type="compose" size="18" color="#007AFF" />
            </view>
            <view class="action-icon" @click="deleteCategory(categoryIndex)">
              <uni-icons type="trash" size="18" color="#FF3B30" />
            </view>
          </view>
        </view>

        <!-- 位置列表 -->
        <view class="locations-grid">
          <view 
            v-for="(location, locationIndex) in category.items"
            :key="locationIndex"
            class="location-item"
            @click.stop
          >
            <input 
              v-if="location.editing"
              v-model="location.name"
              class="location-input"
              placeholder="请输入位置名称"
              @blur="saveLocation(categoryIndex, locationIndex)"
              @confirm="saveLocation(categoryIndex, locationIndex)"
            />
            <view v-else class="location-content" @click="editLocation(categoryIndex, locationIndex)">
              <text class="location-name">{{ getLocationDisplayName(location) }}</text>
            </view>
            
            <view v-if="!location.editing" class="location-delete" @click="deleteLocation(categoryIndex, locationIndex)">
              <uni-icons type="clear" size="16" color="#FF3B30" />
            </view>
          </view>
          
          <!-- 添加位置按钮 -->
          <view class="add-location-btn" @click.stop="addLocation(categoryIndex)">
            <uni-icons type="plus" size="20" color="#C7C7CC" />
            <text class="add-text">添加位置</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 保存按钮 -->
    <view class="save-section" @click.stop>
      <button class="save-btn" @click="saveConfig" :disabled="saving">
        <uni-icons v-if="saving" type="spinner-cycle" size="16" color="#FFFFFF" />
        <text>{{ saving ? '保存中...' : '保存配置' }}</text>
      </button>
    </view>
  </view>
</template>

<script>
import { callCloudFunction } from '@/utils/auth.js';

export default {
  name: 'LocationManage',
  data() {
    return {
      loading: false,
      saving: false,
      locationConfig: {
        lastUpdated: new Date().toISOString(),
        locations: []
      }
    }
  },
  
      onLoad() {
    this.loadConfig();
  },
  
  computed: {
    totalLocations() {
      return this.locationConfig.locations.reduce((sum, category) => {
        return sum + category.items.length;
      }, 0);
    },
    
    categoriesCount() {
      return this.locationConfig.locations.length;
    }
  },
  
  methods: {
    // 加载配置
    async loadConfig() {
      try {
        this.loading = true;
        
        // 使用云函数获取位置配置数据
        const result = await callCloudFunction('hygiene-location-management', {
          action: 'getLocationConfig'
        });

        if (result.data && result.data.locations) {
          this.locationConfig = {
            lastUpdated: result.data.lastUpdated || new Date().toISOString(),
            locations: result.data.locations || []
          };
          
          // 确保每个位置都有编辑状态
          this.locationConfig.locations.forEach(category => {
            category.editing = false;
            category.items = category.items.map(item => {
              if (typeof item === 'string') {
                return { name: item, editing: false };
              }
              return { ...item, editing: false };
            });
          });
        } else {
          // 初始化空配置，让用户自己创建
          this.locationConfig = {
            lastUpdated: new Date().toISOString(),
            locations: []
          };
        }
        
      } catch (error) {
        console.error('加载配置失败：', error);
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
        // 初始化空配置
        this.locationConfig = {
          lastUpdated: new Date().toISOString(),
          locations: []
        };
      } finally {
        this.loading = false;
      }
    },
    

    

    
    // 获取位置显示名称
    getLocationDisplayName(location) {
      if (typeof location === 'string') {
        return location;
      }
      return location.name || '未命名位置';
    },
    

    
    // 分类管理
    addCategory() {
      const newCategoryIndex = this.locationConfig.locations.length;
      this.locationConfig.locations.push({
        category: '新分类',
        editing: true,
        items: []
      });
      

    },
    
    editCategory(categoryIndex) {
      this.locationConfig.locations[categoryIndex].editing = true;
      this.$nextTick(() => {
        // 小程序环境下暂不支持自动聚焦
      });
    },
    
    saveCategory(categoryIndex) {
      const category = this.locationConfig.locations[categoryIndex];
      
      // 验证分类名称是否为空
      if (!category.category || !category.category.trim()) {
        // 如果是新建的分类且名称为空，直接删除
        if (category.items.length === 0) {
          this.locationConfig.locations.splice(categoryIndex, 1);
          uni.showToast({
            title: '分类名称不能为空',
            icon: 'none',
            duration: 2000
          });
          return;
        } else {
          // 如果分类下已有位置，提示用户
          uni.showModal({
            title: '提示',
            content: '分类名称不能为空，请输入分类名称',
            showCancel: false,
            success: () => {
              // 重新进入编辑状态
              category.editing = true;
              this.$nextTick(() => {
                // 小程序环境下暂不支持自动聚焦
              });
            }
          });
          return;
        }
      }
      
      // 去除首尾空格
      category.category = category.category.trim();
      
      // 检查是否有重复的分类名称
      const duplicateIndex = this.locationConfig.locations.findIndex((cat, index) => 
        index !== categoryIndex && cat.category === category.category
      );
      
      if (duplicateIndex !== -1) {
        uni.showModal({
          title: '提示',
          content: '分类名称已存在，请使用其他名称',
          showCancel: false,
          success: () => {
            // 重新进入编辑状态
            category.editing = true;
            this.$nextTick(() => {
              // 小程序环境下暂不支持自动聚焦
            });
          }
        });
        return;
      }
      
      category.editing = false;
    },
    
    deleteCategory(categoryIndex) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除"${this.locationConfig.locations[categoryIndex].category}"分类及其所有位置吗？`,
        success: (res) => {
          if (res.confirm) {
            this.locationConfig.locations.splice(categoryIndex, 1);
          }
        }
      });
    },
    
    // 位置管理
    addLocation(categoryIndex) {
      const newLocationIndex = this.locationConfig.locations[categoryIndex].items.length;
      this.locationConfig.locations[categoryIndex].items.push({
        name: '',
        editing: true
      });
      

    },
    
    editLocation(categoryIndex, locationIndex) {
      const location = this.locationConfig.locations[categoryIndex].items[locationIndex];
      // 如果是字符串格式，转换为对象格式
      if (typeof location === 'string') {
        this.locationConfig.locations[categoryIndex].items[locationIndex] = {
          name: location,
          editing: true
        };
      } else {
        location.editing = true;
      }
    },
    
    saveLocation(categoryIndex, locationIndex) {
      const location = this.locationConfig.locations[categoryIndex].items[locationIndex];
      // 处理不同的数据结构（字符串或对象）
      let locationName = typeof location === 'string' ? location : (location.name || '');
      
      if (!locationName.trim()) {
        // 如果名称为空，删除该项
        this.locationConfig.locations[categoryIndex].items.splice(locationIndex, 1);
        uni.showToast({
          title: '位置名称不能为空',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
      // 去除首尾空格
      locationName = locationName.trim();
      
      // 检查同一分类下是否有重复的位置名称
      const categoryItems = this.locationConfig.locations[categoryIndex].items;
      const duplicateIndex = categoryItems.findIndex((item, index) => {
        if (index === locationIndex) return false;
        const itemName = typeof item === 'string' ? item : (item.name || '');
        return itemName === locationName;
      });
      
      if (duplicateIndex !== -1) {
        uni.showModal({
          title: '提示',
          content: '该分类下已存在相同的位置名称，请使用其他名称',
          showCancel: false,
          success: () => {
            // 重新进入编辑状态
            if (typeof location === 'string') {
              this.locationConfig.locations[categoryIndex].items[locationIndex] = {
                name: location,
                editing: true
              };
            } else {
              location.editing = true;
            }
          }
        });
        return;
      }
      
      // 确保location是对象格式
      if (typeof location === 'string') {
        this.locationConfig.locations[categoryIndex].items[locationIndex] = {
          name: locationName,
          editing: false
        };
      } else {
        location.name = locationName;
        location.editing = false;
      }
    },
    
    deleteLocation(categoryIndex, locationIndex) {
      const location = this.locationConfig.locations[categoryIndex].items[locationIndex];
      const locationName = location.name || location || '未命名位置';
      uni.showModal({
        title: '确认删除',
        content: `确定要删除位置"${locationName}"吗？`,
        success: (res) => {
          if (res.confirm) {
            this.locationConfig.locations[categoryIndex].items.splice(locationIndex, 1);
          }
        }
      });
    },
    
    // 配置管理
    async saveConfig() {
      try {
        this.saving = true;
        
        // 清理数据格式
        const configToSave = {
          lastUpdated: new Date().toISOString(),
          locations: this.locationConfig.locations.map(category => ({
            category: category.category,
            items: category.items.map(item => item.name || item).filter(name => name.trim())
          })).filter(category => category.items.length > 0)
        };
        
        // 使用云函数保存位置配置
        await callCloudFunction('hygiene-location-management', {
          action: 'updateLocationConfig',
          data: configToSave
        });
        
        uni.showToast({
          title: '配置保存成功',
          icon: 'success'
        });
        
      } catch (error) {
        console.error('保存配置失败：', error);
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'none'
        });
      } finally {
        this.saving = false;
      }
    },
    

    
    // 处理页面点击事件
    handlePageClick() {
      // 保存所有正在编辑的分类
      this.locationConfig.locations.forEach((category, categoryIndex) => {
        if (category.editing) {
          this.saveCategory(categoryIndex);
        }
      });
      
      // 保存所有正在编辑的位置
      this.locationConfig.locations.forEach((category, categoryIndex) => {
        for (let i = category.items.length - 1; i >= 0; i--) {
          const location = category.items[i];
          if (location.editing) {
            this.saveLocation(categoryIndex, i);
          }
        }
      });
    },
    

  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: #F8F9FA;
  padding-bottom: 200rpx;
}

.header {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  padding: 60rpx 32rpx 40rpx;
  color: white;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 24rpx;
  opacity: 0.8;
}

.stats-card {
  background: white;
  margin: 24rpx 32rpx;
  padding: 32rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.stats-item {
  text-align: center;
}

.stats-number {
  font-size: 48rpx;
  font-weight: 600;
  color: #007AFF;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #8E8E93;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
  padding: 0 32rpx;
  margin-bottom: 24rpx;
}

.action-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 26rpx;
  border: none;
}

.action-btn.primary {
  background: #007AFF;
  color: white;
}

.action-btn.secondary {
  background: white;
  color: #007AFF;
  border: 2rpx solid #007AFF;
}

.categories-list {
  padding: 0 32rpx;
}

.category-card {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #F2F2F7;
}

.category-title-group {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.category-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
}

.category-title-input {
  font-size: 28rpx;
  font-weight: 500;
  color: #1C1C1E;
  border: 2rpx solid #007AFF;
  border-radius: 8rpx;
  padding: 8rpx 12rpx;
  background: white;
  min-width: 200rpx;
}

/* 分类标题输入框占位符样式 */
.category-title-input::placeholder {
  color: #C7C7CC;
  font-size: 26rpx;
  font-weight: 400;
}

.category-title-input::-webkit-input-placeholder {
  color: #C7C7CC;
  font-size: 26rpx;
  font-weight: 400;
}

.category-title-input::-moz-placeholder {
  color: #C7C7CC;
  font-size: 26rpx;
  font-weight: 400;
  opacity: 1;
}

.category-title-input:-ms-input-placeholder {
  color: #C7C7CC;
  font-size: 26rpx;
  font-weight: 400;
}

.category-count {
  font-size: 24rpx;
  color: #8E8E93;
}

.category-actions {
  display: flex;
  gap: 16rpx;
}

.action-icon {
  padding: 8rpx;
  border-radius: 8rpx;
  background: #F2F2F7;
}

.locations-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.location-item {
  position: relative;
  background: #F8F9FA;
  border-radius: 12rpx;
  border: 2rpx solid #F2F2F7;
  overflow: hidden;
}

.location-content {
  padding: 20rpx 50rpx 20rpx 20rpx;
  display: flex;
  align-items: center;
  min-height: 20rpx;
  box-sizing: border-box;
}

.location-name {
  font-size: 26rpx;
  color: #1C1C1E;
  flex: 1;
  line-height: 40rpx;
  display: flex;
  align-items: center;
}



.location-input {
  width: 100%;
  height: 100%;
  padding: 20rpx;
  font-size: 24rpx;
  color: #1C1C1E;
  border: none;
  background: white;
  box-sizing: border-box;
  line-height: 24rpx;
  text-align: left;
  border-radius: 12rpx;
}

/* 位置输入框占位符样式 */
.location-input::placeholder {
  color: #C7C7CC;
  font-size: 22rpx;
  font-weight: 400;
}

.location-input::-webkit-input-placeholder {
  color: #C7C7CC;
  font-size: 22rpx;
  font-weight: 400;
}

.location-input::-moz-placeholder {
  color: #C7C7CC;
  font-size: 22rpx;
  font-weight: 400;
  opacity: 1;
}

.location-input:-ms-input-placeholder {
  color: #C7C7CC;
  font-size: 22rpx;
  font-weight: 400;
}

.location-delete {
  position: absolute;
  top: 50%;
  right: 10rpx;
  transform: translateY(-50%);
  width: 36rpx;
  height: 36rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
}

.add-location-btn {
  background: #F2F2F7;
  border: 2rpx dashed #C7C7CC;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.add-text {
  font-size: 24rpx;
  color: #8E8E93;
}

.save-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  background: white;
  border-top: 2rpx solid #F2F2F7;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.save-btn[disabled] {
  background: #C7C7CC;
}

/* 加载状态样式 */
.stats-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
  width: 100%;
}

.list-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
  background: #F8F9FA;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
}
</style> 