@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container.data-v-6e3331a0 {
  min-height: 100vh;
  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
  padding: 24rpx;
}
.card.data-v-6e3331a0 {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.card-header.data-v-6e3331a0 {
  padding: 32rpx 32rpx 16rpx 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
}
.card-body.data-v-6e3331a0 {
  padding: 32rpx;
}
.header-content.data-v-6e3331a0 {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.card-title.data-v-6e3331a0 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
}
.card-subtitle.data-v-6e3331a0 {
  font-size: 26rpx;
  color: #8E8E93;
}
.task-info.data-v-6e3331a0 {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.info-item.data-v-6e3331a0 {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}
.info-label.data-v-6e3331a0 {
  font-size: 28rpx;
  color: #8E8E93;
  width: 140rpx;
  /* Fixed width for alignment */
  flex-shrink: 0;
  /* Prevent shrinking */
  text-align: left;
  /* Align text to left */
}
.info-value.data-v-6e3331a0 {
  font-size: 28rpx;
  color: #1C1C1E;
  flex: 1;
  line-height: 1.4;
}
.upload-section.data-v-6e3331a0, .remarks-section.data-v-6e3331a0 {
  margin-bottom: 32rpx;
}
.section-header.data-v-6e3331a0 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.section-title.data-v-6e3331a0 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
}
.auto-upload-toggle.data-v-6e3331a0 {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.toggle-label.data-v-6e3331a0 {
  font-size: 24rpx;
  color: #8E8E93;
}
.toggle-switch.data-v-6e3331a0 {
  width: 76rpx;
  height: 44rpx;
  background: #E5E5EA;
  border-radius: 22rpx;
  position: relative;
  transition: background-color 0.3s ease;
}
.toggle-switch.active.data-v-6e3331a0 {
  background: #34C759;
}
.toggle-circle.data-v-6e3331a0 {
  width: 36rpx;
  height: 36rpx;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}
.toggle-switch.active .toggle-circle.data-v-6e3331a0 {
  -webkit-transform: translateX(32rpx);
          transform: translateX(32rpx);
}
.photo-grid.data-v-6e3331a0 {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}
.photo-item.data-v-6e3331a0 {
  position: relative;
  width: 170rpx;
  height: 170rpx;
  border-radius: 12rpx;
  overflow: hidden;
}
.photo-item image.data-v-6e3331a0 {
  width: 100%;
  height: 100%;
}
.photo-delete.data-v-6e3331a0 {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  width: 36rpx;
  height: 36rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}
.photo-add.data-v-6e3331a0 {
  width: 170rpx;
  height: 170rpx;
  border: 2rpx dashed #E5E5EA;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  background: #F8F9FA;
}
.photo-add text.data-v-6e3331a0 {
  font-size: 24rpx;
  color: #8E8E93;
}
.photo-tip.data-v-6e3331a0 {
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.4;
}
.remarks-section .section-title.data-v-6e3331a0 {
  margin-bottom: 16rpx;
}
.remarks-input-container.data-v-6e3331a0 {
  position: relative;
  width: 100%;
}
.remarks-input.data-v-6e3331a0 {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx 16rpx 32rpx 16rpx;
  border: 1rpx solid #E5E5EA;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  background: #F8F9FA;
  box-sizing: border-box;
  resize: none;
  line-height: 1.5;
}
.char-count-overlay.data-v-6e3331a0 {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  font-size: 22rpx;
  color: #8E8E93;
  background: rgba(248, 249, 250, 0.9);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  pointer-events: none;
  z-index: 2;
  -webkit-backdrop-filter: blur(4rpx);
          backdrop-filter: blur(4rpx);
}
.button-container.data-v-6e3331a0 {
  padding: 32rpx 0;
}
.primary-button.data-v-6e3331a0 {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  width: 100%;
}
.primary-button[disabled].data-v-6e3331a0 {
  background: #C7C7CC;
  color: #8E8E93;
}
.primary-button.loading.data-v-6e3331a0 {
  background: #0056D6;
  opacity: 0.9;
}
.button-loading.data-v-6e3331a0 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  color: white;
}
.loading-spinner.data-v-6e3331a0 {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid white;
  border-radius: 50%;
  -webkit-animation: spin-data-v-6e3331a0 1s linear infinite;
          animation: spin-data-v-6e3331a0 1s linear infinite;
}
@-webkit-keyframes spin-data-v-6e3331a0 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin-data-v-6e3331a0 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.photo-uploading.data-v-6e3331a0 {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.upload-spinner.data-v-6e3331a0 {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-top: 2rpx solid white;
  border-radius: 50%;
  -webkit-animation: spin-data-v-6e3331a0 1s linear infinite;
          animation: spin-data-v-6e3331a0 1s linear infinite;
}
.photo-uploaded.data-v-6e3331a0 {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #34C759;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
/* 加载状态 */
.loading-container.data-v-6e3331a0 {
  padding: 120rpx 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.loading-content.data-v-6e3331a0 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}
.loading-text.data-v-6e3331a0 {
  font-size: 28rpx;
  color: #8E8E93;
  text-align: center;
}
/* 错误状态 */
.error-container.data-v-6e3331a0 {
  padding: 120rpx 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.error-content.data-v-6e3331a0 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  text-align: center;
}
.error-text.data-v-6e3331a0 {
  font-size: 28rpx;
  color: #8E8E93;
  line-height: 1.4;
}
.retry-button.data-v-6e3331a0 {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
}
/* 审核反馈卡片 */
.feedback-content.data-v-6e3331a0 {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.feedback-item.data-v-6e3331a0 {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}
.feedback-label.data-v-6e3331a0 {
  font-size: 28rpx;
  color: #8E8E93;
  width: 140rpx;
  flex-shrink: 0;
  text-align: left;
}
.feedback-value.data-v-6e3331a0 {
  font-size: 28rpx;
  color: #1C1C1E;
  flex: 1;
  line-height: 1.4;
}
.status-badge.data-v-6e3331a0 {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 600;
}
.status-rejected.data-v-6e3331a0 {
  background: #FFE6E6;
  color: #FF3B30;
}
/* 检查员问题照片 */
.inspection-photos-grid.data-v-6e3331a0 {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}
.inspection-photo-item.data-v-6e3331a0 {
  position: relative;
  width: 170rpx;
  height: 170rpx;
  border-radius: 12rpx;
  overflow: hidden;
}
.inspection-photo-item image.data-v-6e3331a0 {
  width: 100%;
  height: 100%;
}
.previous-photos-grid.data-v-6e3331a0 {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}
.previous-photo-item.data-v-6e3331a0 {
  position: relative;
  width: 170rpx;
  height: 170rpx;
  border-radius: 12rpx;
  overflow: hidden;
  opacity: 0.8;
}
.previous-photo-item image.data-v-6e3331a0 {
  width: 100%;
  height: 100%;
}
.photo-overlay.data-v-6e3331a0 {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 16rpx 12rpx 8rpx;
}
.photo-time.data-v-6e3331a0 {
  font-size: 20rpx;
  color: white;
  text-align: center;
  display: block;
}
