require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/location-manage"],{"26c2":function(t,n,o){"use strict";o.d(n,"b",(function(){return e})),o.d(n,"c",(function(){return a})),o.d(n,"a",(function(){return i}));var i={uniIcons:function(){return Promise.all([o.e("common/vendor"),o.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(o.bind(null,"6ddf"))}},e=function(){var t=this,n=t.$createElement,o=(t._self._c,t.loading?null:t.__map(t.locationConfig.locations,(function(n,o){var i=t.__get_orig(n),e=n.items.length,a=t.__map(n.items,(function(n,o){var i=t.__get_orig(n),e=n.editing?null:t.getLocationDisplayName(n);return{$orig:i,m0:e}}));return{$orig:i,g0:e,l0:a}})));t.$mp.data=Object.assign({},{$root:{l1:o}})},a=[]},"2ab5":function(t,n,o){"use strict";var i=o("5f01"),e=o.n(i);e.a},4240:function(t,n,o){"use strict";(function(t){var i=o("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e=i(o("7eb4")),a=i(o("7ca3")),c=i(o("ee10")),r=o("882c");function s(t,n){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);n&&(i=i.filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),o.push.apply(o,i)}return o}function l(t){for(var n=1;n<arguments.length;n++){var o=null!=arguments[n]?arguments[n]:{};n%2?s(Object(o),!0).forEach((function(n){(0,a.default)(t,n,o[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):s(Object(o)).forEach((function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(o,n))}))}return t}var u={name:"LocationManage",data:function(){return{loading:!1,saving:!1,locationConfig:{lastUpdated:(new Date).toISOString(),locations:[]}}},onLoad:function(){this.loadConfig()},computed:{totalLocations:function(){return this.locationConfig.locations.reduce((function(t,n){return t+n.items.length}),0)},categoriesCount:function(){return this.locationConfig.locations.length}},methods:{loadConfig:function(){var n=this;return(0,c.default)(e.default.mark((function o(){var i;return e.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:return o.prev=0,n.loading=!0,o.next=4,(0,r.callCloudFunction)("hygiene-location-management",{action:"getLocationConfig"});case 4:i=o.sent,i.data&&i.data.locations?(n.locationConfig={lastUpdated:i.data.lastUpdated||(new Date).toISOString(),locations:i.data.locations||[]},n.locationConfig.locations.forEach((function(t){t.editing=!1,t.items=t.items.map((function(t){return"string"===typeof t?{name:t,editing:!1}:l(l({},t),{},{editing:!1})}))}))):n.locationConfig={lastUpdated:(new Date).toISOString(),locations:[]},o.next=13;break;case 8:o.prev=8,o.t0=o["catch"](0),console.error("加载配置失败：",o.t0),t.showToast({title:"加载失败",icon:"none"}),n.locationConfig={lastUpdated:(new Date).toISOString(),locations:[]};case 13:return o.prev=13,n.loading=!1,o.finish(13);case 16:case"end":return o.stop()}}),o,null,[[0,8,13,16]])})))()},getLocationDisplayName:function(t){return"string"===typeof t?t:t.name||"未命名位置"},addCategory:function(){this.locationConfig.locations.length;this.locationConfig.locations.push({category:"新分类",editing:!0,items:[]})},editCategory:function(t){this.locationConfig.locations[t].editing=!0,this.$nextTick((function(){}))},saveCategory:function(n){var o=this,i=this.locationConfig.locations[n];if(!i.category||!i.category.trim())return 0===i.items.length?(this.locationConfig.locations.splice(n,1),void t.showToast({title:"分类名称不能为空",icon:"none",duration:2e3})):void t.showModal({title:"提示",content:"分类名称不能为空，请输入分类名称",showCancel:!1,success:function(){i.editing=!0,o.$nextTick((function(){}))}});i.category=i.category.trim();var e=this.locationConfig.locations.findIndex((function(t,o){return o!==n&&t.category===i.category}));-1===e?i.editing=!1:t.showModal({title:"提示",content:"分类名称已存在，请使用其他名称",showCancel:!1,success:function(){i.editing=!0,o.$nextTick((function(){}))}})},deleteCategory:function(n){var o=this;t.showModal({title:"确认删除",content:'确定要删除"'.concat(this.locationConfig.locations[n].category,'"分类及其所有位置吗？'),success:function(t){t.confirm&&o.locationConfig.locations.splice(n,1)}})},addLocation:function(t){this.locationConfig.locations[t].items.length;this.locationConfig.locations[t].items.push({name:"",editing:!0})},editLocation:function(t,n){var o=this.locationConfig.locations[t].items[n];"string"===typeof o?this.locationConfig.locations[t].items[n]={name:o,editing:!0}:o.editing=!0},saveLocation:function(n,o){var i=this,e=this.locationConfig.locations[n].items[o],a="string"===typeof e?e:e.name||"";if(!a.trim())return this.locationConfig.locations[n].items.splice(o,1),void t.showToast({title:"位置名称不能为空",icon:"none",duration:2e3});a=a.trim();var c=this.locationConfig.locations[n].items,r=c.findIndex((function(t,n){if(n===o)return!1;var i="string"===typeof t?t:t.name||"";return i===a}));-1===r?"string"===typeof e?this.locationConfig.locations[n].items[o]={name:a,editing:!1}:(e.name=a,e.editing=!1):t.showModal({title:"提示",content:"该分类下已存在相同的位置名称，请使用其他名称",showCancel:!1,success:function(){"string"===typeof e?i.locationConfig.locations[n].items[o]={name:e,editing:!0}:e.editing=!0}})},deleteLocation:function(n,o){var i=this,e=this.locationConfig.locations[n].items[o],a=e.name||e||"未命名位置";t.showModal({title:"确认删除",content:'确定要删除位置"'.concat(a,'"吗？'),success:function(t){t.confirm&&i.locationConfig.locations[n].items.splice(o,1)}})},saveConfig:function(){var n=this;return(0,c.default)(e.default.mark((function o(){var i;return e.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:return o.prev=0,n.saving=!0,i={lastUpdated:(new Date).toISOString(),locations:n.locationConfig.locations.map((function(t){return{category:t.category,items:t.items.map((function(t){return t.name||t})).filter((function(t){return t.trim()}))}})).filter((function(t){return t.items.length>0}))},o.next=5,(0,r.callCloudFunction)("hygiene-location-management",{action:"updateLocationConfig",data:i});case 5:t.showToast({title:"配置保存成功",icon:"success"}),o.next=12;break;case 8:o.prev=8,o.t0=o["catch"](0),console.error("保存配置失败：",o.t0),t.showToast({title:o.t0.message||"保存失败",icon:"none"});case 12:return o.prev=12,n.saving=!1,o.finish(12);case 15:case"end":return o.stop()}}),o,null,[[0,8,12,15]])})))()},handlePageClick:function(){var t=this;this.locationConfig.locations.forEach((function(n,o){n.editing&&t.saveCategory(o)})),this.locationConfig.locations.forEach((function(n,o){for(var i=n.items.length-1;i>=0;i--){var e=n.items[i];e.editing&&t.saveLocation(o,i)}}))}}};n.default=u}).call(this,o("df3c")["default"])},"5a0a":function(t,n,o){"use strict";o.r(n);var i=o("4240"),e=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(n,t,(function(){return i[t]}))}(a);n["default"]=e.a},"5f01":function(t,n,o){},"972d":function(t,n,o){"use strict";o.r(n);var i=o("26c2"),e=o("5a0a");for(var a in e)["default"].indexOf(a)<0&&function(t){o.d(n,t,(function(){return e[t]}))}(a);o("2ab5");var c=o("828b"),r=Object(c["a"])(e["default"],i["b"],i["c"],!1,null,"5da58f36",null,!1,i["a"],void 0);n["default"]=r.exports},d2c8:function(t,n,o){"use strict";(function(t,n){var i=o("47a9");o("357b"),o("861b");i(o("3240"));var e=i(o("972d"));t.__webpack_require_UNI_MP_PLUGIN__=o,n(e.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])}},[["d2c8","common/runtime","common/vendor"]]]);