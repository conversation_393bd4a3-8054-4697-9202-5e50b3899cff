'use strict';

const db = uniCloud.database();
const dbCmd = db.command;
const uniIdCommon = require('uni-id-common');

// 月度检查权限角色常量 - 与其他云函数保持一致
const MONTHLY_INSPECTION_ROLES = {
  // 最高管理权限：系统管理员、厂长（可以创建、修改、删除月度检查问题）
  ADMIN_ROLES: ['admin', 'GM'],
  // 6S专员权限：综合员、发布人（专门负责6S的核心工作人员，可以创建和管理月度检查）
  SPECIALIST_ROLES: ['Integrated', 'reviser'],
  // 月度检查创建权限：管理员、厂长、6S专员
  CREATE_ROLES: ['admin', 'GM', 'Integrated', 'reviser'],
  // 负责人权限：可以被分配任务的所有角色
  ASSIGNEE_ROLES: ['admin', 'GM', 'supervisor', 'PM', 'responsible', 'logistics', 'dispatch', 'Integrated', 'operator', 'technician', 'mechanic', 'user', 'reviser'],
  // 数据查看权限：所有角色
  VIEW_ROLES: ['admin', 'GM', 'supervisor', 'PM', 'responsible', 'logistics', 'dispatch', 'Integrated', 'operator', 'technician', 'mechanic', 'user', 'reviser']
};

exports.main = async (event, context) => {
  const { action, data } = event;
  
  // 使用 uni-id-common 进行用户认证 - 与其他云函数保持一致
  const uniIdInstance = uniIdCommon.createInstance({
    context
  });
  
  const payload = await uniIdInstance.checkToken(event.uniIdToken);
  if (payload.code !== 0) {
    return {
      success: false,
      message: '用户未登录或token无效'
    };
  }
  
  const { uid, role } = payload;
  console.log('当前用户:', uid, '角色:', role);

  try {
    switch (action) {
      case 'createMonthlyIssue':
        return await createMonthlyIssue(data, uid, role);
      case 'getMonthlyIssues':
        return await getMonthlyIssues(data, uid, role);
      case 'updateMonthlyIssue':
        return await updateMonthlyIssue(data, uid, role);
      case 'assignResponsible':
        return await assignResponsible(data, uid, role);
      case 'getMonthlyStatistics':
        return await getMonthlyStatistics(data, uid, role);
      case 'completeIssue':
        return await completeIssue(data, uid, role);
      case 'reviewIssue':
        return await reviewIssue(data, uid, role);
      case 'getIssueDetail':
        return await getIssueDetail(data, uid, role);
      case 'getResponsibleUsers':
        return await getResponsibleUsers(data, uid, role);
      case 'getPersonIssues':
        return await getPersonIssues(data, uid, role);
      case 'getUserInfo':
        return await getUserInfoAction(data, uid, role);
      default:
        return {
          success: false,
          message: '未知的操作类型'
        };
    }
  } catch (error) {
    console.error('Error:', error);
    return {
      success: false,
      message: error.message,
      data: null
    };
  }
};

// 获取用户信息的辅助函数
async function getUserInfo(uid) {
  try {
    const userRes = await db.collection('uni-id-users')
      .doc(uid)
      .field({ nickname: true, username: true, role: true })
      .get();
    
    if (userRes.data && userRes.data[0]) {
      return {
        name: userRes.data[0].nickname || userRes.data[0].username || '用户',
        role: userRes.data[0].role || []
      };
    }
    return {
      name: '用户',
      role: []
    };
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      name: '用户',
      role: []
    };
  }
}

// 角色映射功能
function mapRoleToDisplayName(roles) {
  if (!roles || !Array.isArray(roles) || roles.length === 0) {
    return '普通员工';
  }
  
  const roleMap = {
    'admin': '管理员',
    'GM': '厂长', 
    'PM': '副厂长',
    'supervisor': '主管',
    'responsible': '负责人',
    'reviser': '发布人',
    'logistics': '后勤员',
    'dispatch': '调度员',
    'Integrated': '综合员',
    'operator': '设备员',
    'technician': '工艺员',
    'mechanic': '技术员',
    'user': '普通员工'
  };
  
  // 返回第一个匹配的角色显示名称
  for (let role of roles) {
    if (roleMap[role]) {
      return roleMap[role];
    }
  }
  
  return '普通员工';
}

// 权限检查辅助函数
function hasPermission(userRole, requiredRoles) {
  return Array.isArray(userRole) 
    ? userRole.some(role => requiredRoles.includes(role))
    : requiredRoles.includes(userRole);
}

// 创建月度检查问题记录
async function createMonthlyIssue(data, uid, role) {
  const {
    title,
    description,
    location_info,
    inspection_info = {},
    category = 'safety',
    severity = 'medium',
    priority = 'normal',
    assigned_to,
    assigned_to_name,
    photos = [],
    expected_completion_date,
    tags = []
  } = data;

  // 验证必要字段
  if (!title || !description || !location_info?.location_name) {
    throw new Error('标题、描述和位置信息不能为空');
  }

  // 验证权限：只有管理员、厂领导、综合员可以创建月度检查问题
  if (!hasPermission(role, MONTHLY_INSPECTION_ROLES.CREATE_ROLES)) {
    throw new Error('没有权限创建月度检查问题');
  }

  // 获取用户信息
  const userInfo = await getUserInfo(uid);

  // 生成问题编号
  const issueNumber = await generateIssueNumber();

  // 构建问题记录
  const issueData = {
    title,
    description,
    issue_number: issueNumber,
    
    // 检查信息
    inspection_info: {
      inspection_date: inspection_info.inspection_date ? new Date(inspection_info.inspection_date) : new Date(),
      inspection_type: inspection_info.inspection_type || 'monthly_routine',
      leader_info: {
        leader_id: inspection_info.leader_id || uid,
        leader_name: inspection_info.leader_name || userInfo.name,
        leader_role: inspection_info.leader_role || mapRoleToDisplayName(userInfo.role),
        department: inspection_info.department || ''
      }
    },
    
    // 位置信息
    location_info: {
      location_type: location_info.location_type || 'custom',
      location_category: location_info.location_category || '',
      location_name: location_info.location_name,
      location_description: location_info.location_description || ''
    },
    
    // 问题分类
    category,
    severity,
    priority,
    tags,
    
    // 记录人员信息
    reporter_id: uid,
    reporter_name: userInfo.name,
    
    // 负责人信息
    assigned_to: assigned_to || null,
    assigned_to_name: assigned_to_name || null,
    assigned_by: assigned_to ? uid : null,
    assigned_by_name: assigned_to ? userInfo.name : null,
    
    // 照片信息（只在有照片时添加字段）
    ...(photos && photos.length > 0 ? {
      photos: photos.map(photo => ({
        url: photo.url || photo,
        description: photo.description || '月度检查发现的问题',
        timestamp: new Date()
      }))
    } : {}),
    
    // 状态和时间  
    status: assigned_to ? 'assigned' : 'pending',
    expected_completion_date: expected_completion_date ? new Date(expected_completion_date) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    created_at: new Date(),
    updated_at: new Date()
  };

  const result = await db.collection('hygiene-monthly-issues').add(issueData);

  return {
    success: true,
    message: '月度检查问题记录创建成功',
    data: {
      id: result.id,
      issue_number: issueNumber,
      ...issueData
    }
  };
}

// 生成问题编号
async function generateIssueNumber() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const prefix = `YD${year}${month}`;
  
  // 查询本月最大编号 - 改用字符串匹配方式
  const result = await db.collection('hygiene-monthly-issues')
    .where({
      issue_number: dbCmd.exists(true),
      created_at: dbCmd.gte(new Date(year, now.getMonth(), 1))
    })
    .orderBy('created_at', 'desc')  // 按创建时间排序
    .get();
  
  let nextNumber = 1;
  if (result.data.length > 0) {
    // 从所有记录中找出本月的最大编号
    let maxNumber = 0;
    for (const issue of result.data) {
      const issueNumber = issue.issue_number;
      // 检查是否是正确格式的编号 (YD202508001 格式)
      const match = issueNumber.match(new RegExp(`^${prefix}(\\d{3})$`));
      if (match) {
        const num = parseInt(match[1], 10);
        if (num > maxNumber) {
          maxNumber = num;
        }
      }
    }
    nextNumber = maxNumber + 1;
  }
  
  // 确保编号不超过999，如果超过则从1开始
  if (nextNumber > 999) {
    nextNumber = 1;
  }
  
  return `${prefix}${String(nextNumber).padStart(3, '0')}`;
}

// 获取月度检查问题列表
async function getMonthlyIssues(data, uid, role) {
  const {
    start_date,
    end_date,
    status,
    severity,
    assigned_to,
    category,
    keyword,
    page = 1,
    pageSize = 20
  } = data;

  let whereCondition = {};

  // 时间范围筛选
  if (start_date && end_date) {
    whereCondition.created_at = dbCmd.gte(new Date(start_date)).and(dbCmd.lte(new Date(end_date)));
  } else if (start_date) {
    whereCondition.created_at = dbCmd.gte(new Date(start_date));
  } else if (end_date) {
    whereCondition.created_at = dbCmd.lte(new Date(end_date));
  }

  // 状态筛选
  if (status) {
    whereCondition.status = status;
  }

  // 严重程度筛选
  if (severity) {
    whereCondition.severity = severity;
  }

  // 分类筛选
  if (category) {
    whereCondition.category = category;
  }

  // 负责人筛选
  if (assigned_to) {
    whereCondition.assigned_to = assigned_to;
  }

  // 关键词搜索
  if (keyword) {
    whereCondition.title = new RegExp(keyword, 'i');
  }

  const skip = (page - 1) * pageSize;

  const result = await db.collection('hygiene-monthly-issues')
    .where(whereCondition)
    .orderBy('created_at', 'desc')
    .skip(skip)
    .limit(pageSize)
    .get();

  // 获取总数
  const countResult = await db.collection('hygiene-monthly-issues')
    .where(whereCondition)
    .count();

  return {
    success: true,
    data: {
      list: result.data,
      total: countResult.total,
      page,
      pageSize
    }
  };
}

// 获取月度检查统计数据
async function getMonthlyStatistics(data, uid, role) {
  const { start_date, end_date } = data;

  let whereCondition = {};

  if (start_date && end_date) {
    whereCondition.created_at = dbCmd.gte(new Date(start_date)).and(dbCmd.lte(new Date(end_date)));
  }

  // 获取各状态统计
  const statusPipeline = [
    { $match: whereCondition },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ];

  const statusStats = await db.collection('hygiene-monthly-issues').aggregate(statusPipeline).end();

  // 获取严重程度统计
  const severityPipeline = [
    { $match: whereCondition },
    {
      $group: {
        _id: '$severity',
        count: { $sum: 1 }
      }
    }
  ];

  const severityStats = await db.collection('hygiene-monthly-issues').aggregate(severityPipeline).end();

  // 获取分类统计
  const categoryPipeline = [
    { $match: whereCondition },
    {
      $group: {
        _id: '$category',
        count: { $sum: 1 }
      }
    }
  ];

  const categoryStats = await db.collection('hygiene-monthly-issues').aggregate(categoryPipeline).end();

  // 获取负责人统计
  const responsiblePipeline = [
    { $match: { ...whereCondition, assigned_to: { $ne: null } } },
    {
      $group: {
        _id: {
          assigned_to: '$assigned_to',
          assigned_to_name: '$assigned_to_name'
        },
        total: { $sum: 1 },
        completed: {
          $sum: {
            $cond: [{ $in: ['$status', ['resolved', 'verified', 'closed']] }, 1, 0]
          }
        }
      }
    }
  ];

  const responsibleStats = await db.collection('hygiene-monthly-issues').aggregate(responsiblePipeline).end();

  return {
    success: true,
    data: {
      status_stats: statusStats.data,
      severity_stats: severityStats.data,
      category_stats: categoryStats.data,
      responsible_stats: responsibleStats.data
    }
  };
}

// 分配负责人
async function assignResponsible(data, uid, role) {
  const { issue_id, assigned_to, assigned_to_name, assignment_reason } = data;

  if (!issue_id || !assigned_to) {
    throw new Error('问题ID和负责人不能为空');
  }

  // 验证权限
  if (!hasPermission(role, MONTHLY_INSPECTION_ROLES.CREATE_ROLES)) {
    throw new Error('没有权限分配负责人');
  }

  // 获取用户信息
  const userInfo = await getUserInfo(uid);

  const updateData = {
    assigned_to,
    assigned_to_name,
    assigned_by: uid,
    assigned_by_name: userInfo.name,
    assignment_reason,
    status: 'assigned',
    updated_at: new Date()
  };

  await db.collection('hygiene-monthly-issues').doc(issue_id).update(updateData);

  return {
    success: true,
    message: '负责人分配成功'
  };
}

// 完成问题处理
async function completeIssue(data, uid, role) {
  const { 
    issue_id, 
    resolution_description, 
    resolution_photos = [],
    actual_cost
  } = data;

  if (!issue_id) {
    throw new Error('问题ID不能为空');
  }

  // 获取问题信息验证权限
  const issueResult = await db.collection('hygiene-monthly-issues').doc(issue_id).get();
  if (!issueResult.data.length) {
    throw new Error('问题不存在');
  }

  const issue = issueResult.data[0];
  
  // 验证权限：只有负责人或管理员可以完成
  if (issue.assigned_to !== uid && !hasPermission(role, MONTHLY_INSPECTION_ROLES.ADMIN_ROLES)) {
    throw new Error('没有权限完成此问题');
  }

  const updateData = {
    status: 'resolved',
    actual_completion_date: new Date(),
    resolution_info: {
      resolution_description,
      ...(resolution_photos && resolution_photos.length > 0 ? {
        resolution_photos: resolution_photos.map(photo => ({
          url: photo.url || photo,
          description: photo.description || '问题解决后照片',
          timestamp: new Date()
        }))
      } : {}),
      ...(actual_cost ? { actual_cost } : {})
    },
    updated_at: new Date()
  };

  await db.collection('hygiene-monthly-issues').doc(issue_id).update(updateData);

  return {
    success: true,
    message: '问题处理完成'
  };
}

// 审核问题
async function reviewIssue(data, uid, role) {
  const { 
    issue_id, 
    review_result, 
    review_comments,
    review_rating
  } = data;

  if (!issue_id || !review_result) {
    throw new Error('问题ID和审核结果不能为空');
  }

  // 验证权限：只有管理员和厂领导可以审核
  if (!hasPermission(role, MONTHLY_INSPECTION_ROLES.ADMIN_ROLES)) {
    throw new Error('没有权限审核问题');
  }

  // 获取用户信息
  const userInfo = await getUserInfo(uid);

  const updateData = {
    status: review_result === 'approved' ? 'verified' : 'rejected',
    review_info: {
      reviewer_id: uid,
      reviewer_name: userInfo.name,
      review_date: new Date(),
      review_result,
      review_comments,
      ...(review_rating ? { review_rating } : {})
    },
    updated_at: new Date()
  };

  await db.collection('hygiene-monthly-issues').doc(issue_id).update(updateData);

  return {
    success: true,
    message: '审核完成'
  };
}

// 获取问题详情
async function getIssueDetail(data, uid, role) {
  const { issue_id } = data;

  if (!issue_id) {
    throw new Error('问题ID不能为空');
  }

  const result = await db.collection('hygiene-monthly-issues').doc(issue_id).get();

  if (!result.data.length) {
    throw new Error('问题不存在');
  }

  return {
    success: true,
    data: result.data[0]
  };
}

// 更新问题信息
async function updateMonthlyIssue(data, uid, role) {
  const { issue_id, ...updateFields } = data;

  if (!issue_id) {
    throw new Error('问题ID不能为空');
  }

  // 获取问题信息验证权限
  const issueResult = await db.collection('hygiene-monthly-issues').doc(issue_id).get();
  if (!issueResult.data.length) {
    throw new Error('问题不存在');
  }

  const issue = issueResult.data[0];
  
  // 验证权限：只有创建人、负责人或管理员可以更新
  if (issue.reporter_id !== uid && issue.assigned_to !== uid && !hasPermission(role, MONTHLY_INSPECTION_ROLES.ADMIN_ROLES)) {
    throw new Error('没有权限更新此问题');
  }

  // 过滤允许更新的字段
  const allowedFields = [
    'title', 'description', 'category', 'severity', 'priority', 
    'location_info', 'photos', 'tags', 'expected_completion_date', 'status',
    'rectification_description', 'rectification_photos', 'completed_at', 'action_type'
  ];
  
  const filteredUpdate = {};
  allowedFields.forEach(field => {
    if (updateFields[field] !== undefined) {
      filteredUpdate[field] = updateFields[field];
    }
  });

  if (Object.keys(filteredUpdate).length === 0) {
    throw new Error('没有可更新的字段');
  }

  filteredUpdate.updated_at = new Date();

  await db.collection('hygiene-monthly-issues').doc(issue_id).update(filteredUpdate);

  return {
    success: true,
    message: '问题更新成功'
  };
}

// 获取负责人用户列表
async function getResponsibleUsers(data, uid, role) {
  try {
    console.log('getResponsibleUsers 开始执行，用户角色:', role);
    
    // 检查权限 - 允许所有登录用户查看负责人列表
    if (!hasPermission(role, MONTHLY_INSPECTION_ROLES.VIEW_ROLES)) {
      console.log('权限检查失败，用户角色:', role, '需要角色:', MONTHLY_INSPECTION_ROLES.VIEW_ROLES);
      return {
        success: false,
        message: '权限不足'
      };
    }
    
    console.log('权限检查通过，开始查询数据库');

    // 查询所有用户，然后在代码中过滤（简化查询条件）
    const userRes = await db.collection('uni-id-users')
      .field({
        _id: true,
        nickname: true,
        username: true,
        role: true
      })
      .orderBy('nickname', 'asc')
      .get();

    const allUsers = userRes.data || [];
    console.log('数据库查询成功，共获取用户:', allUsers.length);
    
    // 在代码中过滤用户
    const filteredUsers = allUsers.filter(user => {
      // 排除admin用户
      const roles = Array.isArray(user.role) ? user.role : [user.role];
      if (roles.includes('admin')) return false;
      
      // 只显示角色为"responsible"的用户
      const hasResponsibleRole = roles.includes('responsible');
      if (!hasResponsibleRole) return false;
      
      // 排除匿名用户（没有姓名的用户）
      const displayName = user.nickname || user.username;
      if (!displayName || displayName.trim() === '') return false;
      
      // 排除以"匿名"开头的用户
      if (displayName.startsWith('匿名')) return false;
      
      return true;
    });
    
    console.log('过滤后的用户数量:', filteredUsers.length);
    
    return {
      success: true,
      message: '获取负责人列表成功',
      data: filteredUsers
    };
    
  } catch (error) {
    console.error('获取负责人列表失败:', error);
    return {
      success: false,
      message: '获取负责人列表失败'
    };
  }
}

// 获取个人问题列表
async function getPersonIssues(data, uid, role) {
  try {
    // 检查权限
    if (!hasPermission(role, MONTHLY_INSPECTION_ROLES.VIEW_ROLES)) {
      return {
        success: false,
        message: '权限不足'
      };
    }

    const { responsiblePersonId, page = 1, pageSize = 20 } = data;
    
    if (!responsiblePersonId) {
      return {
        success: false,
        message: '请提供负责人ID'
      };
    }

    const collection = db.collection('hygiene-monthly-issues');
    
    // 查询条件：根据负责人ID过滤
    const query = collection.where({
      assigned_to: responsiblePersonId
    });

    // 获取总数
    const countResult = await query.count();
    const total = countResult.total;

    // 分页查询
    const result = await query
      .orderBy('created_at', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get();

    return {
      success: true,
      data: {
        list: result.data,
        total,
        page,
        pageSize
      }
    };

  } catch (error) {
    console.error('获取个人问题失败:', error);
    return {
      success: false,
      message: '获取个人问题失败: ' + error.message
    };
  }
}

// 获取用户信息的action包装函数
async function getUserInfoAction(data, uid, role) {
  try {
    // 检查权限
    if (!hasPermission(role, MONTHLY_INSPECTION_ROLES.VIEW_ROLES)) {
      return {
        success: false,
        message: '权限不足'
      };
    }

    const { userId } = data;
    
    if (!userId) {
      return {
        success: false,
        message: '请提供用户ID'
      };
    }

    // 查询用户信息
    const userCollection = db.collection('uni-id-users');
    const userResult = await userCollection.doc(userId).get();
    
    if (!userResult.data.length) {
      return {
        success: false,
        message: '用户不存在'
      };
    }

    const user = userResult.data[0];
    
    return {
      success: true,
      data: {
        nickname: user.nickname,
        username: user.username,
        role: user.role || []
      }
    };

  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      success: false,
      message: '获取用户信息失败: ' + error.message
    };
  }
}
