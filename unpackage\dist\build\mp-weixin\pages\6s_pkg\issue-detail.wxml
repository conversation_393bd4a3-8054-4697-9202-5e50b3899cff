<view class="page-container data-v-0ba8b637"><block wx:if="{{loading}}"><view class="content-loading data-v-0ba8b637"><view class="loading-content data-v-0ba8b637"><uni-icons vue-id="2b7b4e36-1" type="spinner-cycle" size="40" color="#007AFF" class="data-v-0ba8b637" bind:__l="__l"></uni-icons><text class="loading-text data-v-0ba8b637">加载中...</text></view></view></block><block wx:else><block wx:if="{{loadError}}"><view class="content-error data-v-0ba8b637"><p-empty-state vue-id="2b7b4e36-2" type="error" title="加载失败" description="网络异常，请检查网络连接" show-button="{{true}}" button-text="重新加载" data-event-opts="{{[['^buttonClick',[['retryLoad']]]]}}" bind:buttonClick="__e" class="data-v-0ba8b637" bind:__l="__l"></p-empty-state></view></block><block wx:else><block wx:if="{{dataLoaded&&issue}}"><view class="content data-v-0ba8b637"><view class="status-card data-v-0ba8b637"><view class="status-bar data-v-0ba8b637"><view class="issue-id data-v-0ba8b637">{{"#"+$root.m0}}</view><view class="{{['status-badge','data-v-0ba8b637','status-'+issue.status]}}">{{''+$root.m1+''}}</view></view><view class="issue-title data-v-0ba8b637">{{issue.title}}</view><view class="key-info data-v-0ba8b637"><view class="info-item data-v-0ba8b637"><view class="info-label data-v-0ba8b637"><uni-icons vue-id="2b7b4e36-3" type="person" size="14" color="#007AFF" class="data-v-0ba8b637" bind:__l="__l"></uni-icons><text class="data-v-0ba8b637">负责人</text></view><view class="info-value data-v-0ba8b637"><text class="main-value data-v-0ba8b637">{{issue.responsible}}</text></view></view><view class="info-item data-v-0ba8b637"><view class="info-label data-v-0ba8b637"><uni-icons vue-id="2b7b4e36-4" type="calendar" size="14" color="#FF9500" class="data-v-0ba8b637" bind:__l="__l"></uni-icons><text class="data-v-0ba8b637">截止时间</text></view><view class="info-value-with-badge data-v-0ba8b637"><text class="main-value data-v-0ba8b637">{{issue.deadline}}</text><block wx:if="{{issue.deadline}}"><view class="{{['countdown-badge','data-v-0ba8b637',countdownClass]}}">{{''+(deadlineCountdown||'计算中...')+''}}</view></block></view></view></view><view class="location-info data-v-0ba8b637"><view class="location-section data-v-0ba8b637"><uni-icons vue-id="2b7b4e36-5" type="location" size="16" color="#007AFF" class="data-v-0ba8b637" bind:__l="__l"></uni-icons><text class="data-v-0ba8b637">{{issue.location}}</text></view><view class="{{['priority-badge','data-v-0ba8b637','priority-'+(issue&&issue.priority?issue.priority:'normal')]}}"><view class="priority-dot data-v-0ba8b637"></view><text class="data-v-0ba8b637">{{$root.m2}}</text></view></view></view><view class="description-card data-v-0ba8b637"><view class="card-title data-v-0ba8b637">问题描述</view><view class="description-text data-v-0ba8b637">{{issue.description}}</view></view><block wx:if="{{$root.g0}}"><view class="images-card data-v-0ba8b637"><view class="card-title data-v-0ba8b637">问题图片</view><view class="images-grid data-v-0ba8b637"><block wx:for="{{issue.images}}" wx:for-item="image" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['previewImage',[index]]]]]}}" class="image-item data-v-0ba8b637" bindtap="__e"><image class="issue-image data-v-0ba8b637" src="{{image}}" mode="aspectFill"></image></view></block></view></view></block><block wx:if="{{$root.g1}}"><view class="rectification-card data-v-0ba8b637"><view class="card-title data-v-0ba8b637">整改内容</view><block wx:if="{{issue.rectification_description}}"><view class="rectification-description data-v-0ba8b637"><view class="rectification-label data-v-0ba8b637">整改说明</view><view class="rectification-text data-v-0ba8b637">{{issue.rectification_description}}</view></view></block><block wx:if="{{$root.g2}}"><view class="rectification-images data-v-0ba8b637"><view class="rectification-label data-v-0ba8b637">整改照片</view><view class="images-grid data-v-0ba8b637"><block wx:for="{{issue.rectification_photos}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['previewRectificationImage',[index]]]]]}}" class="image-item data-v-0ba8b637" bindtap="__e"><image class="rectification-image data-v-0ba8b637" src="{{photo.url||photo}}" mode="aspectFill"></image></view></block></view></view></block><block wx:if="{{issue.completed_at}}"><view class="rectification-time data-v-0ba8b637"><view class="rectification-label data-v-0ba8b637">完成时间</view><view class="rectification-time-text data-v-0ba8b637">{{$root.m3}}</view></view></block></view></block><view class="timeline-card data-v-0ba8b637"><view class="card-title data-v-0ba8b637">处理进展</view><view class="unified-timeline data-v-0ba8b637"><block wx:for="{{unifiedTimeline}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['timeline-item','data-v-0ba8b637',(item.status==='completed')?'completed':'',(item.status==='current')?'current':'',(item.status==='pending')?'pending':'',(item.status==='overdue')?'overdue':'']}}"><view class="timeline-dot data-v-0ba8b637"><block wx:if="{{item.status==='completed'}}"><uni-icons vue-id="{{'2b7b4e36-6-'+index}}" type="checkmarkempty" size="16" color="#ffffff" class="data-v-0ba8b637" bind:__l="__l"></uni-icons></block><block wx:else><block wx:if="{{item.status==='current'}}"><uni-icons vue-id="{{'2b7b4e36-7-'+index}}" type="gear" size="16" color="#ffffff" class="data-v-0ba8b637" bind:__l="__l"></uni-icons></block><block wx:else><block wx:if="{{item.status==='overdue'}}"><uni-icons vue-id="{{'2b7b4e36-8-'+index}}" type="closeempty" size="16" color="#ffffff" class="data-v-0ba8b637" bind:__l="__l"></uni-icons></block><block wx:else><view class="dot-placeholder data-v-0ba8b637"></view></block></block></block></view><view class="timeline-content data-v-0ba8b637"><view class="timeline-header data-v-0ba8b637"><view class="step-title data-v-0ba8b637">{{item.title}}</view><block wx:if="{{item.time}}"><view class="step-time data-v-0ba8b637">{{item.time}}</view></block></view><block wx:if="{{item.description}}"><view class="step-desc data-v-0ba8b637">{{item.description}}</view></block><block wx:if="{{item.operator}}"><view class="step-operator data-v-0ba8b637">{{"操作人："+item.operator}}</view></block></view></view></block></view></view><view class="bottom-safe-area data-v-0ba8b637"></view></view></block></block></block><block wx:if="{{dataLoaded&&issue&&shouldShowActionButton}}"><view class="action-bar data-v-0ba8b637"><view class="action-buttons data-v-0ba8b637"><button class="action-btn primary full data-v-0ba8b637" disabled="{{isButtonDisabled}}" data-event-opts="{{[['tap',[['handleMainAction',['$event']]]]]}}" bindtap="__e">{{''+$root.m4+''}}</button></view></view></block></view>