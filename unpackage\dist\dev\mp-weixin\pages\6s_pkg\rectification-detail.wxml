<view class="page-container data-v-ccc26d00"><block wx:if="{{loading}}"><view class="loading-container data-v-ccc26d00"><view class="loading-content data-v-ccc26d00"><view class="loading-spinner data-v-ccc26d00"></view><text class="loading-text data-v-ccc26d00">加载整改任务详情中...</text></view></view></block><block wx:else><block wx:if="{{loadError}}"><view class="error-container data-v-ccc26d00"><view class="error-content data-v-ccc26d00"><uni-icons vue-id="cb8a30f4-1" type="info" size="48" color="#FF3B30" class="data-v-ccc26d00" bind:__l="__l"></uni-icons><text class="error-text data-v-ccc26d00">{{loadError}}</text><button data-event-opts="{{[['tap',[['retryLoad',['$event']]]]]}}" class="retry-button data-v-ccc26d00" bindtap="__e">重新加载</button></view></view></block><block wx:else><view class="card data-v-ccc26d00"><view class="card-header data-v-ccc26d00"><view class="header-content data-v-ccc26d00"><view class="card-title data-v-ccc26d00">整改任务详情</view><view class="card-subtitle data-v-ccc26d00">{{taskInfo.area+" - "+(taskInfo.isPublic?'公共责任区':'固定责任区')}}</view></view></view><view class="card-body data-v-ccc26d00"><view class="task-info data-v-ccc26d00"><view class="info-item data-v-ccc26d00"><text class="info-label data-v-ccc26d00">状态：</text><view class="{{['status-badge','data-v-ccc26d00','status-'+taskInfo.status]}}">{{computedData.statusText}}</view></view><view class="info-item data-v-ccc26d00"><text class="info-label data-v-ccc26d00">问题类别：</text><text class="info-value data-v-ccc26d00">{{computedData.categoryText}}</text></view><view class="info-item data-v-ccc26d00"><text class="info-label data-v-ccc26d00">问题描述：</text><text class="info-value data-v-ccc26d00">{{taskInfo.problemDescription}}</text></view><view class="info-item data-v-ccc26d00"><text class="info-label data-v-ccc26d00">发现时间：</text><text class="info-value data-v-ccc26d00">{{$root.m0}}</text></view><view class="info-item data-v-ccc26d00"><text class="info-label data-v-ccc26d00">检查员：</text><text class="info-value data-v-ccc26d00">{{taskInfo.inspector}}</text></view><block wx:if="{{taskInfo.assignee_name}}"><view class="info-item data-v-ccc26d00"><text class="info-label data-v-ccc26d00">负责人：</text><text class="info-value data-v-ccc26d00">{{taskInfo.assignee_name}}</text></view></block><block wx:if="{{computedData.isCompleted&&taskInfo.completedDate}}"><view class="info-item data-v-ccc26d00"><text class="info-label data-v-ccc26d00">完成时间：</text><text class="info-value data-v-ccc26d00">{{$root.m1}}</text></view></block><block wx:else><block wx:if="{{!computedData.isCompleted&&taskInfo.deadline}}"><view class="info-item data-v-ccc26d00"><text class="info-label data-v-ccc26d00">截止时间：</text><text class="info-value data-v-ccc26d00">{{$root.m2}}</text></view></block></block><block wx:if="{{taskInfo.status==='in_progress'&&taskInfo.startDate}}"><view class="info-item data-v-ccc26d00"><text class="info-label data-v-ccc26d00">开始时间：</text><text class="info-value data-v-ccc26d00">{{$root.m3}}</text></view></block></view></view></view><block wx:if="{{$root.g0}}"><view class="card data-v-ccc26d00"><view class="card-header data-v-ccc26d00"><view class="card-title data-v-ccc26d00">问题照片</view><text class="photo-count data-v-ccc26d00">{{$root.g1+"张"}}</text></view><view class="card-body data-v-ccc26d00"><view class="photo-grid data-v-ccc26d00"><block wx:for="{{taskInfo.photos}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['previewPhoto',[index,'problem']]]]]}}" class="photo-item data-v-ccc26d00" bindtap="__e"><image src="{{photo.url||photo}}" mode="aspectFill" class="data-v-ccc26d00"></image><view class="photo-label data-v-ccc26d00">问题</view></view></block></view></view></view></block><block wx:if="{{taskInfo.rectificationDescription}}"><view class="card data-v-ccc26d00"><view class="card-header data-v-ccc26d00"><view class="card-title data-v-ccc26d00">整改说明</view></view><view class="card-body data-v-ccc26d00"><text class="description-text data-v-ccc26d00">{{taskInfo.rectificationDescription}}</text></view></view></block><block wx:if="{{$root.g2}}"><view class="card data-v-ccc26d00"><view class="card-header data-v-ccc26d00"><view class="card-title data-v-ccc26d00">整改后照片</view><text class="photo-count data-v-ccc26d00">{{$root.g3+"张"}}</text></view><view class="card-body data-v-ccc26d00"><view class="photo-grid data-v-ccc26d00"><block wx:for="{{taskInfo.completionPhotos}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['previewPhoto',[index,'completion']]]]]}}" class="photo-item data-v-ccc26d00" bindtap="__e"><image src="{{photo.url||photo}}" mode="aspectFill" class="data-v-ccc26d00"></image><view class="photo-label completion data-v-ccc26d00">整改后</view></view></block></view></view></view></block><block wx:if="{{taskInfo.reviewResult||taskInfo.reviewComments}}"><view class="card data-v-ccc26d00"><view class="card-header data-v-ccc26d00"><view class="card-title data-v-ccc26d00">复查信息</view></view><view class="card-body data-v-ccc26d00"><view class="review-info data-v-ccc26d00"><block wx:if="{{taskInfo.reviewResult}}"><view class="info-item data-v-ccc26d00"><text class="info-label data-v-ccc26d00">复查结果：</text><view class="{{['review-result-badge','data-v-ccc26d00','review-'+taskInfo.reviewResult]}}">{{computedData.reviewResultText}}</view></view></block><block wx:if="{{taskInfo.reviewerName}}"><view class="info-item data-v-ccc26d00"><text class="info-label data-v-ccc26d00">复查人：</text><text class="info-value data-v-ccc26d00">{{taskInfo.reviewerName}}</text></view></block><block wx:if="{{taskInfo.reviewDate}}"><view class="info-item data-v-ccc26d00"><text class="info-label data-v-ccc26d00">复查时间：</text><text class="info-value data-v-ccc26d00">{{$root.m4}}</text></view></block><block wx:if="{{taskInfo.reviewComments}}"><view class="info-item data-v-ccc26d00"><text class="info-label data-v-ccc26d00">复查意见：</text><text class="info-value data-v-ccc26d00">{{taskInfo.reviewComments}}</text></view></block></view><block wx:if="{{$root.g4}}"><view class="review-photos-section data-v-ccc26d00"><view class="review-photos-header data-v-ccc26d00"><text class="review-photos-title data-v-ccc26d00">复查照片</text><text class="photo-count data-v-ccc26d00">{{$root.g5+"张"}}</text></view><view class="photo-grid data-v-ccc26d00"><block wx:for="{{taskInfo.reviewPhotos}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['previewPhoto',[index,'review']]]]]}}" class="photo-item data-v-ccc26d00" bindtap="__e"><image src="{{photo.url||photo}}" mode="aspectFill" class="data-v-ccc26d00"></image><view class="photo-label review data-v-ccc26d00">复查</view></view></block></view></view></block></view></view></block></block></block><view class="button-container data-v-ccc26d00"><button data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="primary-button data-v-ccc26d00" bindtap="__e">返回</button></view></view>