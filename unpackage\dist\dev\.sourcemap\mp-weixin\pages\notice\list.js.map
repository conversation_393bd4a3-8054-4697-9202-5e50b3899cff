{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/notice/list.vue?f486", "webpack:///D:/Xwzc/pages/notice/list.vue?3d40", "webpack:///D:/Xwzc/pages/notice/list.vue?9f28", "webpack:///D:/Xwzc/pages/notice/list.vue?1b72", "uni-app:///pages/notice/list.vue", "webpack:///D:/Xwzc/pages/notice/list.vue?0f8e", "webpack:///D:/Xwzc/pages/notice/list.vue?57e2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "PEmptyState", "data", "categories", "value", "text", "field", "collectionList", "searchText", "activeCategory", "hasManagePermission", "deleteId", "queryCondition", "loadMore", "contentdown", "contentrefresh", "contentnomore", "categoryColors", "showFab", "isLoading", "topNotices", "normalNotices", "searchTimer", "dataCache", "onLoad", "uni", "title", "onPullDownRefresh", "onReachBottom", "methods", "getCategoryColor", "checkUserPermission", "currentUserInfo", "userInfo", "hasAdminRole", "hasReviserRole", "roles", "console", "search", "clearTimeout", "performSearch", "icon", "condition", "$regex", "$options", "clearSearch", "filterByCategory", "forceRefreshList", "setTimeout", "clear", "buildQueryCondition", "refreshList", "handleItemClick", "url", "editNotice", "events", "refreshData", "confirmDelete", "content", "success", "deleteNotice", "mask", "db", "doc", "remove", "showCancel", "goToAdd", "formatDate", "onDataLoaded", "timestamp", "onDataError", "loadData", "clearExpiredCache", "key", "onUnload", "computed", "filteredNotices", "filtered", "item"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvGA;AAAA;AAAA;AAAA;AAA8lB,CAAgB,wnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwIlnB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAGA;EACAC;IACAC;EACA;EACAC;IACA;MACAC,aACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACAC;MACAC;IACA;;IAEA;IACA;;IAEA;IACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACAC,iDAEA;kBACAC,yDAEA;kBACAC;kBACAC,wBAEA;kBACA;oBACAC;oBACAF;oBACAC;kBACA;;kBAEA;kBACA;oBACAC;oBACAF;oBACAC;kBACA;kBAEA;gBACA;kBACAE;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACAC;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACAf;UACAC;UACAe;QACA;QACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;;QAEA;QACAC;UACAC;UACAC;QACA;MAEA;;MAEA;MACA;QACAF;MACA;;MAEA;MACA;;MAEA;MACA;;MAEA;MACAjB;IACA;IAEA;IACAoB;MACA;;MAEA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;QACAH;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IAEA;IACAI;MACA;;MAEA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;;QAEA;QACAJ;UACAC;UACAC;QACA;MACA;;MAEA;MACA;QACAF;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IAEA;IACAK;MAAA;MACA;MACA;QACA;QACAC;UACA;YACAC;UACA;YACAxB;UACA;QACA;MACA;QACAY;QACA;QACAW;UACA;YACA;cACAC;YACA;cACAxB;YACA;UACA;QACA;MACA;IACA;IAEA;IACAyB;MACA;;MAEA;MACA;QACA;;QAEA;QACAR;UACAC;UACAC;QACA;MAEA;;MAEA;MACA;QACAF;MACA;MAEA;MACA;IACA;IAEA;IACAS;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA3B;QACA4B;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA7B;UACAC;UACAe;QACA;QACA;MACA;MAEAhB;QACA4B;QACAE;UACA;UACAC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACAhC;UACAC;UACAe;QACA;QACA;MACA;MAEA;MACAhB;QACAC;QACAgC;QACAC;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGAnC;kBACAC;kBACAmC;gBACA;gBAAA;gBAAA,OAEAC,wBACAC,qBACAC;cAAA;gBAEAvC;kBACAC;kBACAe;gBACA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAJ;gBACAZ;kBACAC;kBACAgC;kBACAO;gBACA;cAAA;gBAAA;gBAEAxC;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAyC;MAAA;MACA;QACAzC;UACAC;UACAe;QACA;QACA;MACA;MAEAhB;QACA4B;QACAE;UACA;UACAC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAW;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;;MAEA;MACApB;QACA;MACA;;MAEA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;;MAEA;MACA;QACA;;QAEA;QACA;UACA;UACA;QACA;QAEA;UACA5B;UACAC;UACAgD;QACA;MACA;IACA;IAEA;IACAC;MACA;MACAjC;IACA;IAEA;IACAkC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MAAA,2CAEA;QAAA;MAAA;QAAA;UAAA;YAAAC;YAAArE;UACA;YACA;UACA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;IACA;EACA;EAEA;EACAsE;IACA;IACA;MACAnC;MACA;IACA;;IAEA;IACA;EACA;EAEAoC;IACA;IACAC;MAAA;MACA;MAEA;;MAEA;MACA;QACA;QACAC;UAAA,OACAC;QAAA,EACA;MACA;;MAEA;MACA;QACAD;UAAA;QAAA;MACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtnBA;AAAA;AAAA;AAAA;AAAg3B,CAAgB,i3BAAG,EAAC,C;;;;;;;;;;;ACAp4B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/notice/list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/notice/list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list.vue?vue&type=template&id=efa7a1e8&\"\nvar renderjs\nimport script from \"./list.vue?vue&type=script&lang=js&\"\nexport * from \"./list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/notice/list.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=template&id=efa7a1e8&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    unicloudDb: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-cli-shared/components/unicloud-db\" */ \"@dcloudio/uni-cli-shared/components/unicloud-db.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-load-more/components/uni-load-more/uni-load-more\" */ \"@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.$hasSSP(\"7d5a337a-3\")\n  var m1 = m0 ? _vm.$getSSP(\"7d5a337a-3\", \"default\") : null\n  var m2 = m0 && m1[\"error\"] ? _vm.$getSSP(\"7d5a337a-3\", \"default\") : null\n  var m3 =\n    m0 && !m1[\"error\"]\n      ? !_vm.$getSSP(\"7d5a337a-3\", \"default\")[\"data\"] ||\n        _vm.$getSSP(\"7d5a337a-3\", \"default\")[\"data\"].length === 0\n      : null\n  var l0 =\n    m0 && !m1[\"error\"] && !m3\n      ? _vm.__map(_vm.topNotices, function (item, __i0__) {\n          var $orig = _vm.__get_orig(item)\n          var m4 = _vm.getCategoryColor(item.category)\n          var m5 = _vm.formatDate(item.createTime)\n          return {\n            $orig: $orig,\n            m4: m4,\n            m5: m5,\n          }\n        })\n      : null\n  var l1 =\n    m0 && !m1[\"error\"] && !m3\n      ? _vm.__map(_vm.normalNotices, function (item, __i1__) {\n          var $orig = _vm.__get_orig(item)\n          var m6 = _vm.getCategoryColor(item.category)\n          var m7 = _vm.formatDate(item.createTime)\n          return {\n            $orig: $orig,\n            m6: m6,\n            m7: m7,\n          }\n        })\n      : null\n  var m8 =\n    m0 && !m1[\"error\"] && !m3 ? _vm.$getSSP(\"7d5a337a-3\", \"default\") : null\n  var m9 =\n    m0 && !m1[\"error\"] && !m3 && !m8[\"loading\"]\n      ? _vm.$getSSP(\"7d5a337a-3\", \"default\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        l0: l0,\n        l1: l1,\n        m8: m8,\n        m9: m9,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <!-- 搜索栏 -->\n    <view class=\"search-bar\">\n      <view class=\"search-input-wrapper\">\n        <uni-icons type=\"search\" size=\"16\" color=\"#999\"></uni-icons>\n        <input class=\"search-input\" v-model=\"searchText\" placeholder=\"搜索公告标题\" @confirm=\"search\" confirm-type=\"search\" />\n        <uni-icons v-if=\"searchText\" type=\"clear\" size=\"16\" color=\"#999\" @click=\"clearSearch\"></uni-icons>\n      </view>\n      <text class=\"search-btn\" @click=\"search\">搜索</text>\n    </view>\n    \n    <!-- 分类筛选 -->\n    <scroll-view scroll-x class=\"filter-bar\" :show-scrollbar=\"false\">\n      <view class=\"filter-content\">\n        <view class=\"filter-item\" :class=\"{ active: activeCategory === 'all' }\" @click=\"filterByCategory('all')\">\n          全部\n        </view>\n        <view v-for=\"(category, index) in categories\" :key=\"index\" \n          class=\"filter-item\" \n          :class=\"{ active: activeCategory === category.value }\" \n          @click=\"filterByCategory(category.value)\">\n          {{ category.text }}\n        </view>\n      </view>\n    </scroll-view>\n    \n    <!-- 公告列表 -->\n    <unicloud-db ref=\"udb\" v-slot:default=\"{data, pagination, loading, hasMore, error}\" \n      :collection=\"collectionList\" \n      :where=\"queryCondition\" \n      orderby=\"isTop desc, createTime desc\" \n      :getone=\"false\" \n      :field=\"field\" \n      :page-size=\"20\"\n      :page-current=\"1\"\n      :loadtime=\"'manual'\"\n      @load=\"onDataLoaded\" \n      @error=\"onDataError\">\n      <p-empty-state v-if=\"error\" \n        :text=\"error.message\" \n        buttonText=\"重试\"\n        @buttonClick=\"refreshList\">\n      </p-empty-state>\n      \n      <p-empty-state v-else-if=\"!data || data.length === 0\" \n        text=\"暂无公告\"\n        image=\"/static/empty/empty_todo.png\">\n      </p-empty-state>\n      \n      <view v-else class=\"notice-list\">\n        <!-- 置顶公告 -->\n        <view v-for=\"item in topNotices\" :key=\"item._id\" class=\"notice-item sticky\" @click=\"handleItemClick(item._id)\">\n          <view class=\"notice-header\">\n            <view class=\"notice-title-wrapper\">\n              <text class=\"sticky-tag\">置顶</text>\n              <text class=\"notice-title\">{{ item.title }}</text>\n            </view>\n            <view class=\"category-tag\" :style=\"{ backgroundColor: getCategoryColor(item.category) }\">{{ item.category }}</view>\n          </view>\n          <view class=\"notice-info\">\n            <view class=\"info-item\">\n              <uni-icons type=\"person\" size=\"14\" color=\"#999\"></uni-icons>\n              <text class=\"info-text\">{{ item.publisherName || '未知' }}</text>\n            </view>\n            <view class=\"info-item\">\n              <uni-icons type=\"calendar\" size=\"14\" color=\"#999\"></uni-icons>\n              <text class=\"info-text\">{{ formatDate(item.createTime) }}</text>\n            </view>\n            <view class=\"info-item\">\n              <uni-icons type=\"eye\" size=\"14\" color=\"#999\"></uni-icons>\n              <text class=\"info-text\">{{ item.readCount || 0 }}</text>\n            </view>\n          </view>\n          \n          <!-- 管理操作按钮 -->\n          <view v-if=\"hasManagePermission\" class=\"notice-actions\">\n            <view class=\"action-btn edit\" @click.stop=\"editNotice(item._id)\">\n              <uni-icons type=\"compose\" size=\"14\" color=\"#3c9cff\"></uni-icons>\n              <text class=\"action-text\">编辑</text>\n            </view>\n            <view class=\"action-btn delete\" @click.stop=\"confirmDelete(item._id)\">\n              <uni-icons type=\"trash\" size=\"14\" color=\"#fa3534\"></uni-icons>\n              <text class=\"action-text\">删除</text>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 普通公告 -->\n        <view v-for=\"item in normalNotices\" :key=\"item._id\" class=\"notice-item\" @click=\"handleItemClick(item._id)\">\n          <view class=\"notice-header\">\n            <view class=\"notice-title-wrapper\">\n              <text class=\"notice-title\">{{ item.title }}</text>\n            </view>\n            <view class=\"category-tag\" :style=\"{ backgroundColor: getCategoryColor(item.category) }\">{{ item.category }}</view>\n          </view>\n          <view class=\"notice-info\">\n            <view class=\"info-item\">\n              <uni-icons type=\"person\" size=\"14\" color=\"#999\"></uni-icons>\n              <text class=\"info-text\">{{ item.publisherName || '未知' }}</text>\n            </view>\n            <view class=\"info-item\">\n              <uni-icons type=\"calendar\" size=\"14\" color=\"#999\"></uni-icons>\n              <text class=\"info-text\">{{ formatDate(item.createTime) }}</text>\n            </view>\n            <view class=\"info-item\">\n              <uni-icons type=\"eye\" size=\"14\" color=\"#999\"></uni-icons>\n              <text class=\"info-text\">{{ item.readCount || 0 }}</text>\n            </view>\n          </view>\n          \n          <!-- 管理操作按钮 -->\n          <view v-if=\"hasManagePermission\" class=\"notice-actions\">\n            <view class=\"action-btn edit\" @click.stop=\"editNotice(item._id)\">\n              <uni-icons type=\"compose\" size=\"14\" color=\"#3c9cff\"></uni-icons>\n              <text class=\"action-text\">编辑</text>\n            </view>\n            <view class=\"action-btn delete\" @click.stop=\"confirmDelete(item._id)\">\n              <uni-icons type=\"trash\" size=\"14\" color=\"#fa3534\"></uni-icons>\n              <text class=\"action-text\">删除</text>\n            </view>\n          </view>\n        </view>\n        \n        <uni-load-more :status=\"loading?'loading':(hasMore ? 'more' : 'noMore')\"></uni-load-more>\n      </view>\n    </unicloud-db>\n    \n    <!-- 发布按钮 -->\n    <view v-if=\"hasManagePermission && showFab\" class=\"publish-btn\" @click=\"goToAdd\">\n      <uni-icons type=\"plusempty\" size=\"20\" color=\"#fff\"></uni-icons>\n    </view>\n  </view>\n</template>\n\n<script>\n  const db = uniCloud.database()\n  import PEmptyState from '@/components/p-empty-state/p-empty-state.vue';\n  \n  export default {\n    components: {\n      PEmptyState\n    },\n    data() {\n      return {\n        categories: [\n          { value: '公告通知', text: '公告通知' },\n          { value: '重要通知', text: '重要通知' },\n          { value: '活动通知', text: '活动通知' },\n          { value: '其他通知', text: '其他通知' }\n        ],\n        field: 'title,excerpt,readCount,isTop,publisherName,createTime,category',\n        collectionList: 'notice',\n        searchText: '',\n        activeCategory: 'all',\n        hasManagePermission: false,\n        deleteId: '',\n        queryCondition: {},\n        loadMore: {\n          contentdown: '',\n          contentrefresh: '',\n          contentnomore: ''\n        },\n        // 分类颜色映射\n        categoryColors: {\n          '公告通知': '#4CAF50', // 绿色\n          '重要通知': '#FF5722', // 红色\n          '活动通知': '#2196F3', // 蓝色\n          '其他通知': '#F9AE3D'  // 黄色\n        },\n        showFab: false,  // 控制悬浮按钮显示\n        isLoading: true,  // 添加加载状态控制\n        topNotices: [],\n        normalNotices: [],\n        searchTimer: null, // 搜索防抖定时器\n        dataCache: new Map() // 数据缓存\n      }\n    },\n    onLoad() {\n      // 设置导航栏标题\n      uni.setNavigationBarTitle({\n        title: '公告通知'\n      })\n      \n      // 检查用户权限\n      this.checkUserPermission()\n      \n      // 手动加载数据\n      this.$nextTick(() => {\n        this.loadData()\n      })\n    },\n    onPullDownRefresh() {\n      this.refreshList()\n    },\n    onReachBottom() {\n      this.$refs.udb.loadMore()\n    },\n    methods: {\n      // 获取分类颜色\n      getCategoryColor(category) {\n        return this.categoryColors[category] || '#999';\n      },\n      // 检查用户权限\n      async checkUserPermission() {\n        try {\n          // 获取当前用户信息\n          const currentUserInfo = uniCloud.getCurrentUserInfo()\n          \n          // 从本地存储获取用户信息\n          const userInfo = uni.getStorageSync('uni_id_user_info') || {}\n          \n          // 检查是否有管理员或编辑权限\n          let hasAdminRole = false\n          let hasReviserRole = false\n          \n          // 检查当前用户角色\n          if (currentUserInfo && currentUserInfo.role) {\n            const roles = Array.isArray(currentUserInfo.role) ? currentUserInfo.role : [currentUserInfo.role]\n            hasAdminRole = roles.includes('admin')\n            hasReviserRole = roles.includes('reviser')\n          }\n          \n          // 如果currentUserInfo中没有找到角色，尝试从本地存储中获取\n          if (!hasAdminRole && !hasReviserRole && userInfo.role) {\n            const roles = Array.isArray(userInfo.role) ? userInfo.role : [userInfo.role]\n            hasAdminRole = roles.includes('admin')\n            hasReviserRole = roles.includes('reviser')\n          }\n          \n          this.hasManagePermission = hasAdminRole || hasReviserRole\n        } catch (e) {\n          console.error('检查用户权限失败:', e)\n          this.hasManagePermission = false\n        }\n      },\n      \n      // 搜索\n      search() {\n        // 清除之前的定时器\n        if (this.searchTimer) {\n          clearTimeout(this.searchTimer);\n        }\n        \n        // 防抖处理，300ms后执行搜索\n        this.searchTimer = setTimeout(() => {\n          this.performSearch();\n        }, 300);\n      },\n      \n      // 执行搜索\n      performSearch() {\n        if (!this.searchText.trim()) {\n          uni.showToast({\n            title: '请输入搜索内容',\n            icon: 'none'\n          });\n          return;\n        }\n        \n        // 隐藏悬浮按钮\n        this.showFab = false;\n        this.isLoading = true;\n        \n        // 构建缓存key\n        const cacheKey = `search_${this.searchText.trim()}_${this.activeCategory}`;\n        \n        // 检查缓存\n        if (this.dataCache.has(cacheKey)) {\n          const cachedData = this.dataCache.get(cacheKey);\n          this.topNotices = cachedData.topNotices;\n          this.normalNotices = cachedData.normalNotices;\n          this.isLoading = false;\n          this.showFab = true;\n          return;\n        }\n        \n        // 直接构建查询条件\n        const condition = {};\n        \n        // 搜索条件\n        if (this.searchText && this.searchText.trim()) {\n          const searchText = this.searchText.trim();\n          \n          // 使用正则表达式进行模糊搜索，不区分大小写\n          condition.title = {\n            $regex: searchText,\n            $options: 'i'  // 不区分大小写\n          };\n          \n        }\n        \n        // 保留分类条件\n        if (this.activeCategory !== 'all') {\n          condition.category = this.activeCategory;\n        }\n        \n        // 直接设置查询条件\n        this.queryCondition = condition;\n        \n        // 强制刷新列表\n        this.forceRefreshList();\n        \n        // 收起键盘\n        uni.hideKeyboard();\n      },\n      \n      // 清除搜索\n      clearSearch() {\n        this.searchText = '';\n        \n        // 隐藏悬浮按钮\n        this.showFab = false;\n        this.isLoading = true;\n        \n        // 直接构建查询条件\n        const condition = {};\n        \n        // 保留分类条件\n        if (this.activeCategory !== 'all') {\n          condition.category = this.activeCategory;\n        }\n        \n        // 直接设置查询条件\n        this.queryCondition = condition;\n        \n        // 强制刷新列表\n        this.forceRefreshList();\n      },\n      \n      // 按分类筛选\n      filterByCategory(category) {\n        this.activeCategory = category;\n        \n        // 隐藏悬浮按钮\n        this.showFab = false;\n        this.isLoading = true;\n        \n        // 直接构建查询条件\n        const condition = {};\n        \n        // 搜索条件\n        if (this.searchText && this.searchText.trim()) {\n          const searchText = this.searchText.trim();\n          \n          // 使用正则表达式进行模糊搜索，不区分大小写\n          condition.title = {\n            $regex: searchText,\n            $options: 'i'  // 不区分大小写\n          };\n        }\n        \n        // 分类条件\n        if (category !== 'all') {\n          condition.category = category;\n        }\n        \n        // 直接设置查询条件\n        this.queryCondition = condition;\n        \n        // 强制刷新列表\n        this.forceRefreshList();\n      },\n      \n      // 强制刷新列表\n      forceRefreshList() {\n        // 确保udb组件已经挂载\n        if (this.$refs.udb) {\n          // 使用setTimeout确保在下一个事件循环中执行，避免数据绑定问题\n          setTimeout(() => {\n            this.$refs.udb.loadData({\n              clear: true\n            }, () => {\n              uni.stopPullDownRefresh();\n            });\n          }, 0);\n        } else {\n          console.error('udb组件未挂载，无法刷新列表');\n          // 延迟尝试刷新\n          setTimeout(() => {\n            if (this.$refs.udb) {\n              this.$refs.udb.loadData({\n                clear: true\n              }, () => {\n                uni.stopPullDownRefresh();\n              });\n            }\n          }, 100);\n        }\n      },\n      \n      // 构建查询条件 - 保留此方法以兼容其他可能的调用\n      buildQueryCondition() {\n        const condition = {};\n        \n        // 搜索条件\n        if (this.searchText && this.searchText.trim()) {\n          const searchText = this.searchText.trim();\n          \n          // 使用正则表达式进行模糊搜索，不区分大小写\n          condition.title = {\n            $regex: searchText,\n            $options: 'i'  // 不区分大小写\n          };\n          \n        }\n        \n        // 分类条件\n        if (this.activeCategory !== 'all') {\n          condition.category = this.activeCategory;\n        }\n        \n        this.queryCondition = condition;\n        return condition;\n      },\n      \n      // 刷新列表\n      refreshList() {\n        this.isLoading = true;\n        this.showFab = false;\n        this.forceRefreshList();\n      },\n      \n      // 点击公告项\n      handleItemClick(id) {\n        uni.navigateTo({\n          url: './detail?id=' + id\n        })\n      },\n      \n      // 编辑公告\n      editNotice(id) {\n        if (!this.hasManagePermission) {\n          uni.showToast({\n            title: '无权限操作',\n            icon: 'none'\n          })\n          return\n        }\n        \n        uni.navigateTo({\n          url: './edit?id=' + id,\n          events: {\n            // 监听修改页面成功修改数据后, 刷新当前页面数据\n            refreshData: () => {\n              this.refreshList()\n            }\n          }\n        })\n      },\n      \n      // 确认删除\n      confirmDelete(id) {\n        if (!this.hasManagePermission) {\n          uni.showToast({\n            title: '无权限操作',\n            icon: 'none'\n          })\n          return\n        }\n        \n        this.deleteId = id\n        uni.showModal({\n          title: '删除确认',\n          content: '确定要删除这条公告吗？此操作不可恢复。',\n          success: (res) => {\n            if (res.confirm) {\n              this.deleteNotice()\n            }\n          }\n        })\n      },\n      \n      // 删除公告\n      async deleteNotice() {\n        if (!this.deleteId) return\n        \n        try {\n          uni.showLoading({\n            title: '删除中...',\n            mask: true\n          })\n          \n          await db.collection('notice')\n            .doc(this.deleteId)\n            .remove()\n          \n          uni.showToast({\n            title: '删除成功',\n            icon: 'success'\n          })\n          \n          // 刷新列表\n          this.refreshList()\n        } catch (e) {\n          console.error('删除公告失败:', e)\n          uni.showModal({\n            title: '删除失败',\n            content: e.message || '请稍后重试',\n            showCancel: false\n          })\n        } finally {\n          uni.hideLoading()\n          this.deleteId = ''\n        }\n      },\n      \n      // 前往添加页面\n      goToAdd() {\n        if (!this.hasManagePermission) {\n          uni.showToast({\n            title: '无权限操作',\n            icon: 'none'\n          })\n          return\n        }\n        \n        uni.navigateTo({\n          url: './add',\n          events: {\n            // 监听添加页面成功添加数据后, 刷新当前页面数据\n            refreshData: () => {\n              this.refreshList()\n            }\n          }\n        })\n      },\n      \n      // 格式化日期\n      formatDate(timestamp) {\n        if (!timestamp) return ''\n        const date = new Date(timestamp)\n        const year = date.getFullYear()\n        const month = String(date.getMonth() + 1).padStart(2, '0')\n        const day = String(date.getDate()).padStart(2, '0')\n        return `${year}-${month}-${day}`\n      },\n      \n      // 数据加载完成处理\n      onDataLoaded(data) {\n        this.isLoading = false;\n        \n        // 延迟显示悬浮按钮，模拟加载完成后的过渡效果\n        setTimeout(() => {\n          this.showFab = true;\n        }, 300);\n        \n        // 分离置顶公告和普通公告\n        this.topNotices = data.filter(item => item.isTop);\n        this.normalNotices = data.filter(item => !item.isTop);\n        \n        // 缓存数据（限制缓存大小）\n        if (this.searchText.trim() || this.activeCategory !== 'all') {\n          const cacheKey = `search_${this.searchText.trim()}_${this.activeCategory}`;\n          \n          // 限制缓存大小，最多保存10个查询结果\n          if (this.dataCache.size >= 10) {\n            const firstKey = this.dataCache.keys().next().value;\n            this.dataCache.delete(firstKey);\n          }\n          \n          this.dataCache.set(cacheKey, {\n            topNotices: this.topNotices,\n            normalNotices: this.normalNotices,\n            timestamp: Date.now()\n          });\n        }\n      },\n      \n      // 数据加载错误处理\n      onDataError(error) {\n        this.isLoading = false;\n        console.error('加载数据出错:', error);\n      },\n      \n      // 手动加载数据\n      loadData() {\n        if (this.$refs.udb) {\n          this.$refs.udb.loadData();\n        }\n      },\n      \n      // 清理过期缓存\n      clearExpiredCache() {\n        const now = Date.now();\n        const expireTime = 5 * 60 * 1000; // 5分钟过期\n        \n        for (const [key, value] of this.dataCache.entries()) {\n          if (now - value.timestamp > expireTime) {\n            this.dataCache.delete(key);\n          }\n        }\n              }\n      },\n      \n      // 页面卸载时清理\n      onUnload() {\n        // 清理搜索定时器\n        if (this.searchTimer) {\n          clearTimeout(this.searchTimer);\n          this.searchTimer = null;\n        }\n        \n        // 清理过期缓存（可选）\n        this.clearExpiredCache();\n      },\n    \n    computed: {\n      // 使用计算属性优化分类筛选（备用方案）\n      filteredNotices() {\n        if (!this.data || !Array.isArray(this.data)) return [];\n        \n        let filtered = this.data;\n        \n        // 搜索筛选\n        if (this.searchText.trim()) {\n          const searchText = this.searchText.trim().toLowerCase();\n          filtered = filtered.filter(item => \n            item.title && item.title.toLowerCase().includes(searchText)\n          );\n        }\n        \n        // 分类筛选\n        if (this.activeCategory !== 'all') {\n          filtered = filtered.filter(item => item.category === this.activeCategory);\n        }\n        \n        return filtered;\n      }\n    }\n  }\n</script>\n\n<style>\n.container {\n  padding: 24rpx;\n  background-color: #f8f9fc;\n  min-height: 100vh;\n}\n\n/* 搜索栏 */\n.search-bar {\n  display: flex;\n  align-items: center;\n  padding: 20rpx;\n  background-color: #fff;\n  border-radius: 12rpx;\n  margin-bottom: 24rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n}\n\n.search-input-wrapper {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  background-color: #f5f7fa;\n  border-radius: 30rpx;\n  padding: 0 20rpx;\n  height: 70rpx;\n  transition: all 0.3s ease;\n}\n\n.search-input-wrapper:focus-within {\n  background-color: #eef1f8;\n  box-shadow: 0 0 0 2px rgba(60, 156, 255, 0.15);\n}\n\n.search-input {\n  flex: 1;\n  height: 70rpx;\n  font-size: 28rpx;\n  margin: 0 10rpx;\n  color: #333;\n}\n\n.search-btn {\n  font-size: 28rpx;\n  color: #3c9cff;\n  margin-left: 20rpx;\n  padding: 10rpx 20rpx;\n  border-radius: 30rpx;\n  background-color: rgba(60, 156, 255, 0.1);\n  transition: all 0.3s ease;\n}\n\n.search-btn:active {\n  background-color: rgba(60, 156, 255, 0.2);\n}\n\n/* 分类筛选 */\n.filter-bar {\n  background-color: #fff;\n  border-radius: 12rpx;\n  margin-bottom: 24rpx;\n  white-space: nowrap;\n  padding: 20rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n  /* 隐藏滚动条 - 增强版 */\n  scrollbar-width: none; /* Firefox */\n  -ms-overflow-style: none; /* IE and Edge */\n  overflow: -moz-scrollbars-none; /* 旧版Firefox */\n}\n\n.filter-bar::-webkit-scrollbar {\n  display: none; /* Chrome, Safari, Opera */\n  width: 0 !important;\n  height: 0 !important;\n  background: transparent;\n  -webkit-appearance: none;\n}\n\n/* 确保在Android设备上也能隐藏滚动条 */\n::-webkit-scrollbar {\n  width: 0 !important;\n  height: 0 !important;\n  display: none;\n  background: transparent;\n}\n\n.filter-content {\n  display: inline-block;\n}\n\n.filter-item {\n  display: inline-block;\n  padding: 12rpx 32rpx;\n  margin-right: 20rpx;\n  font-size: 28rpx;\n  color: #666;\n  background-color: #f5f7fa;\n  border-radius: 30rpx;\n  transition: all 0.3s ease;\n}\n\n.filter-item.active {\n  color: #fff;\n  background-color: #3c9cff;\n  box-shadow: 0 4rpx 8rpx rgba(60, 156, 255, 0.25);\n  transform: translateY(-2rpx);\n}\n\n/* 公告列表 */\n.notice-list {\n  padding-bottom: 120rpx;\n}\n\n.notice-item {\n  background-color: #fff;\n  border-radius: 12rpx;\n  padding: 30rpx;\n  margin-bottom: 24rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n  transition: all 0.3s ease;\n  border-left: 6rpx solid transparent;\n  transform: translateY(0);\n}\n\n.notice-item:active {\n  transform: translateY(2rpx);\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);\n}\n\n.notice-item.sticky {\n  border-left: 6rpx solid #ff9800;\n  background-color: rgba(255, 152, 0, 0.03);\n}\n\n.notice-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 16rpx;\n}\n\n.notice-title-wrapper {\n  flex: 1;\n  padding-right: 20rpx;\n}\n\n.sticky-tag {\n  display: inline-block;\n  font-size: 24rpx;\n  color: #fff;\n  background-color: #ff9800;\n  padding: 4rpx 12rpx;\n  border-radius: 4rpx;\n  margin-right: 12rpx;\n}\n\n.notice-title {\n  font-size: 32rpx;\n  color: #333;\n  font-weight: 500;\n  line-height: 1.4;\n}\n\n.category-tag {\n  font-size: 24rpx;\n  color: #fff;\n  background-color: #3c9cff;\n  padding: 6rpx 18rpx;\n  border-radius: 6rpx;\n  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.15);\n}\n\n.notice-info {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  margin-top: 16rpx;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  margin-right: 30rpx;\n  margin-bottom: 8rpx;\n}\n\n.info-text {\n  font-size: 26rpx;\n  color: #999;\n  margin-left: 8rpx;\n}\n\n.notice-actions {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 20rpx;\n  padding-top: 20rpx;\n  border-top: 1px solid #f5f5f5;\n}\n\n/* 为置顶公告添加特殊的分隔线样式 */\n.notice-item.sticky .notice-actions {\n  border-top: 3rpx dashed rgba(255, 152, 0, 0.6);\n  background-color: rgba(255, 152, 0, 0.05);\n  border-radius: 0 0 12rpx 12rpx;\n  padding-top: 24rpx;\n  padding-bottom: 10rpx;\n  margin-top: 24rpx;\n  margin-left: -30rpx;\n  margin-right: -30rpx;\n  padding-left: 30rpx;\n  padding-right: 30rpx;\n}\n\n.action-btn {\n  display: flex;\n  align-items: center;\n  padding: 10rpx 24rpx;\n  margin-left: 20rpx;\n  border-radius: 30rpx;\n  transition: all 0.2s ease;\n}\n\n.action-text {\n  font-size: 26rpx;\n  margin-left: 8rpx;\n}\n\n.action-btn.edit {\n  color: #3c9cff;\n  background-color: rgba(60, 156, 255, 0.1);\n}\n\n.action-btn.edit:active {\n  background-color: rgba(60, 156, 255, 0.2);\n}\n\n.action-btn.delete {\n  color: #fa3534;\n  background-color: rgba(250, 53, 52, 0.1);\n}\n\n.action-btn.delete:active {\n  background-color: rgba(250, 53, 52, 0.2);\n}\n\n/* 发布按钮 */\n.publish-btn {\n  position: fixed;\n  right: 40rpx;\n  bottom: 100rpx;\n  width: 110rpx;\n  height: 110rpx;\n  background: linear-gradient(135deg, #4290f7 0%, #3c9cff 100%);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 6rpx 16rpx rgba(60, 156, 255, 0.35);\n  z-index: 10;\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n  animation: fadeIn 0.5s ease;\n}\n\n.publish-btn:active {\n  transform: translateY(5rpx);\n  box-shadow: 0 2rpx 8rpx rgba(60, 156, 255, 0.25);\n}\n\n.publish-btn:after {\n  content: '';\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  background-color: rgba(60, 156, 255, 0.8);\n  z-index: -1;\n  opacity: 0.5;\n  transform: scale(0.9);\n  transition: all 0.3s;\n}\n\n.publish-btn:active:after {\n  transform: scale(1.1);\n  opacity: 0;\n}\n\n/* 错误和空状态 */\n.error-wrapper, .empty-wrapper {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 0;\n}\n\n.empty-image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 30rpx;\n}\n\n.empty-text, .error-text {\n  font-size: 28rpx;\n  color: #999;\n  margin: 20rpx 0;\n}\n\n.retry-btn {\n  font-size: 28rpx;\n  color: #fff;\n  background-color: #3c9cff;\n  padding: 14rpx 40rpx;\n  border-radius: 30rpx;\n  margin-top: 20rpx;\n  box-shadow: 0 4rpx 8rpx rgba(60, 156, 255, 0.25);\n}\n\n/* 页面加载动画 - 简化版 */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(10rpx);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.notice-item {\n  animation: fadeInUp 0.2s ease-out;\n  animation-fill-mode: both;\n}\n\n/* 移除复杂的延迟动画 */\n.notice-item:nth-child(n) {\n  animation-delay: 0s;\n}\n\n/* 添加动画定义 - 简化版 */\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n</style>\n", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775852186\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}