(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/honor_pkg/admin/add-record"],{"0399":function(e,t,a){"use strict";(function(e,n){var r=a("47a9"),o=a("3b2d");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var c=r(a("7eb4")),s=r(a("7ca3")),i=r(a("ee10")),u=r(a("af34"));function l(e){if("function"!==typeof WeakMap)return null;var t=new WeakMap,a=new WeakMap;return(l=function(e){return e?a:t})(e)}function h(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==o(e)&&"function"!==typeof e)return{default:e};var a=l(t);if(a&&a.has(e))return a.get(e);var n={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var c in e)if("default"!==c&&Object.prototype.hasOwnProperty.call(e,c)){var s=r?Object.getOwnPropertyDescriptor(e,c):null;s&&(s.get||s.set)?Object.defineProperty(n,c,s):n[c]=e[c]}return n.default=e,a&&a.set(e,n),n}function d(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function f(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?d(Object(a),!0).forEach((function(t){(0,s.default)(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):d(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}var p={components:{AvatarPicker:function(){a.e("pages/honor_pkg/avatar-picker/avatar-picker").then(function(){return resolve(a("baea"))}.bind(null,a)).catch(a.oe)}},name:"AddRecord",data:function(){return{loading:!1,saving:!1,loadingText:"加载中...",isEditMode:!1,recordId:null,formData:{userName:"",department:"",userAvatar:"",honorTypeId:"",batchId:"",reason:"",isFeatured:!1,images:[]},departmentOptions:[],honorTypeOptions:[],batchOptions:[],selectedHonorType:null,selectedBatch:null,showBatchSelector:!1,batchSearchKeyword:"",showOlderBatchCount:10,showDepartmentSelector:!1,newDepartmentName:"",showHonorTypeSelector:!1}},computed:{filteredBatchOptions:function(){if(!this.batchSearchKeyword.trim())return this.batchOptions;var e=this.batchSearchKeyword.toLowerCase();return this.batchOptions.filter((function(t){return t.text.toLowerCase().includes(e)}))},recentBatches:function(){var e=(0,u.default)(this.filteredBatchOptions).sort((function(e,t){var a=new Date(e.data.meetingDate||"1970-01-01"),n=new Date(t.data.meetingDate||"1970-01-01");return n-a}));return e.slice(0,5)},currentMonthBatches:function(){var e=new Date,t=e.getMonth()+1,a=e.getFullYear();return this.filteredBatchOptions.filter((function(e){var n=new Date(e.data.meetingDate);return n.getMonth()+1===t&&n.getFullYear()===a}))},olderBatches:function(){var e=new Date;return e.setMonth(e.getMonth()-3),this.filteredBatchOptions.filter((function(t){var a=new Date(t.data.meetingDate);return a<e})).sort((function(e,t){var a=new Date(e.data.meetingDate||"1970-01-01"),n=new Date(t.data.meetingDate||"1970-01-01");return n-a}))},filteredOlderBatches:function(){return this.olderBatches.slice(0,this.showOlderBatchCount)}},onLoad:function(e){var t=this;return(0,i.default)(c.default.mark((function a(){var n;return c.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if("edit"===e.mode&&e.data){t.isEditMode=!0;try{n=JSON.parse(decodeURIComponent(e.data)),t.recordId=n.id,n.userAvatar&&(t.formData.userAvatar=n.userAvatar),t.formData=f(f(f({},t.formData),n),{},{userAvatar:n.userAvatar||""}),delete t.formData.id}catch(r){console.error("解析编辑数据失败:",r)}}return a.next=3,t.initializeData();case 3:case"end":return a.stop()}}),a)})))()},methods:{initializeData:function(){var t=this;return(0,i.default)(c.default.mark((function a(){return c.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,Promise.all([t.loadDepartmentList(),t.loadHonorTypeList(),t.loadBatchList()]);case 3:t.isEditMode&&(t.setSelectedHonorType(),t.setSelectedBatch()),a.next=9;break;case 6:a.prev=6,a.t0=a["catch"](0),e.showToast({title:"初始化失败，请重试",icon:"none"});case 9:case"end":return a.stop()}}),a,null,[[0,6]])})))()},loadDepartmentList:function(){var e=this;return(0,i.default)(c.default.mark((function t(){var a,r;return c.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,n.callFunction({name:"honor-admin",data:{action:"getDepartments"}});case 3:a=t.sent,0===a.result.code&&(r=a.result.data,Array.isArray(r)?e.departmentOptions=r.map((function(e){return{value:e.name||e,text:e.name||e}})):e.departmentOptions=[]),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("加载部门列表失败:",t.t0),e.departmentOptions=[{value:"管理部门",text:"管理部门"},{value:"业务部门",text:"业务部门"},{value:"技术部门",text:"技术部门"},{value:"支持部门",text:"支持部门"}];case 11:case"end":return t.stop()}}),t,null,[[0,7]])})))()},loadHonorTypeList:function(){var e=this;return(0,i.default)(c.default.mark((function t(){var a,r;return c.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,n.callFunction({name:"honor-admin",data:{action:"getHonorTypes"}});case 3:a=t.sent,0===a.result.code&&(r=a.result.data,Array.isArray(r)?e.honorTypeOptions=r.filter((function(e){return!1!==e.isActive})).map((function(e){return{value:e._id||e.id,text:e.name,color:e.color||"#3a86ff",data:e}})):e.honorTypeOptions=[]),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("加载荣誉类型失败:",t.t0),e.honorTypeOptions=[{value:"1",text:"优秀员工",color:"#3a86ff",data:{id:"1",name:"优秀员工",color:"#3a86ff"}},{value:"2",text:"月度之星",color:"#10b981",data:{id:"2",name:"月度之星",color:"#10b981"}},{value:"3",text:"团队协作奖",color:"#f59e0b",data:{id:"3",name:"团队协作奖",color:"#f59e0b"}}];case 11:case"end":return t.stop()}}),t,null,[[0,7]])})))()},loadBatchList:function(){var e=this;return(0,i.default)(c.default.mark((function t(){var a,r,o,s,i;return c.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,n.callFunction({name:"honor-admin",data:{action:"getBatches"}});case 3:a=t.sent,0===a.result.code&&(r=a.result.data,Array.isArray(r)?e.batchOptions=r.map((function(e){return{value:e._id,text:e.name,meetingDate:e.meetingDate,data:e}})):e.batchOptions=[]),t.next=14;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("加载批次列表失败:",t.t0),o=new Date,s=o.getMonth()+1,i=o.getFullYear(),e.batchOptions=[{value:"default_monthly",text:"".concat(i,"年").concat(s,"月表彰"),meetingDate:"".concat(i,"-").concat(s.toString().padStart(2,"0"),"-01"),data:{_id:"default_monthly",name:"".concat(i,"年").concat(s,"月表彰"),type:"monthly",meetingDate:"".concat(i,"-").concat(s.toString().padStart(2,"0"),"-01")}}];case 14:case"end":return t.stop()}}),t,null,[[0,7]])})))()},setSelectedHonorType:function(){var e=this;if(this.formData.honorTypeId){var t=this.honorTypeOptions.find((function(t){return t.value===e.formData.honorTypeId}));t&&(this.selectedHonorType=t.data)}},setSelectedBatch:function(){var e=this;if(this.formData.batchId){var t=this.batchOptions.find((function(t){return t.value===e.formData.batchId}));t&&(this.selectedBatch=t.data)}},onHonorTypeChange:function(e){var t=this.honorTypeOptions.find((function(t){return t.value===e.detail.value}));this.selectedHonorType=t?t.data:null},onBatchChange:function(e){var t=this.batchOptions.find((function(t){return t.value===e.detail.value}));this.selectedBatch=t?t.data:null},onFeaturedChange:function(e){this.formData.isFeatured=e.detail.value},handleAvatarChange:function(t){console.log("头像变化:",t),t&&(t.cloudPath||t.url)?(this.formData.userAvatar=t.url||t.cloudPath,t.cloudPath&&(this.formData.userAvatarCloudPath=t.cloudPath),e.showToast({title:"头像更新成功",icon:"success",duration:2e3})):t||(this.formData.userAvatar="",this.formData.userAvatarCloudPath="")},onAvatarUploadSuccess:function(t){console.log("头像上传成功:",t),t.url&&(this.formData.userAvatar=t.url,console.log("设置头像URL:",t.url),t.compressed&&console.log("压缩信息:",{size:t.size,originalSize:t.originalSize,compressionRatio:t.compressionRatio})),e.showToast({title:"头像上传成功".concat(t.compressed?"(已压缩)":""),icon:"success"})},onAvatarUploadError:function(t){console.error("头像上传失败:",t),e.showToast({title:"头像上传失败",icon:"none"})},chooseImages:function(){var t=this;return(0,i.default)(c.default.mark((function n(){var r,o,s,i,u,l;return c.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=5-t.formData.images.length,n.prev=1,n.next=4,t.chooseImageAsync({count:r,sizeType:["original"],sourceType:["album","camera"]});case 4:if(o=n.sent,"chooseImage:fail cancel"!==o.errMsg){n.next=7;break}return n.abrupt("return");case 7:if(!(o.tempFilePaths&&o.tempFilePaths.length>0)){n.next=24;break}return e.showLoading({title:"处理中..."}),n.next=11,Promise.resolve().then((function(){return h(a("4ea0"))}));case 11:return s=n.sent.default,n.next=14,s.uploadHonorImages(o.tempFilePaths,{compress:!0,onProgress:function(t){"compress"===t.phase?e.showLoading({title:"压缩中 ".concat(t.progress||0,"%")}):"upload"===t.phase&&e.showLoading({title:"上传中 ".concat(t.progress||0,"%")})}});case 14:if(i=n.sent,e.hideLoading(),!i.success){n.next=23;break}i.results.forEach((function(e,a){e.success&&t.formData.images.push({id:Date.now()+Math.random()+a,url:e.url,cloudPath:e.cloudPath})})),u=i.totalUploaded,l=i.totalFailed,l>0?e.showToast({title:"".concat(u,"张成功，").concat(l,"张失败"),icon:"none"}):e.showToast({title:"".concat(u,"张图片上传成功"),icon:"success"}),n.next=24;break;case 23:throw new Error("批量上传失败");case 24:n.next=31;break;case 26:n.prev=26,n.t0=n["catch"](1),e.hideLoading(),console.error("选择图片失败:",n.t0),e.showToast({title:n.t0.message||"图片上传失败",icon:"none"});case 31:case"end":return n.stop()}}),n,null,[[1,26]])})))()},chooseImageAsync:function(t){return new Promise((function(a,n){e.chooseImage(f(f({},t),{},{success:a,fail:n}))}))},removeImage:function(t){var a=this;return(0,i.default)(c.default.mark((function r(){var o,s,i;return c.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(r.prev=0,o=a.formData.images[t],!o||!o.cloudPath){r.next=17;break}return e.showLoading({title:"删除中..."}),r.next=6,n.callFunction({name:"delete-file",data:{fileList:[o.cloudPath]}});case 6:if(s=r.sent,i=s.result,e.hideLoading(),0!==i.code){r.next=14;break}a.formData.images.splice(t,1),e.showToast({title:"删除成功",icon:"success"}),r.next=15;break;case 14:throw new Error(i.message||"删除失败");case 15:r.next=18;break;case 17:a.formData.images.splice(t,1);case 18:r.next=25;break;case 20:r.prev=20,r.t0=r["catch"](0),e.hideLoading(),console.error("删除图片失败:",r.t0),e.showToast({title:r.t0.message||"删除失败",icon:"none"});case 25:case"end":return r.stop()}}),r,null,[[0,20]])})))()},saveRecord:function(){var t=this;return(0,i.default)(c.default.mark((function a(){var r,o,s;return c.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t.validateForm()){a.next=2;break}return a.abrupt("return");case 2:return t.saving=!0,a.prev=3,r=t.isEditMode?"updateHonor":"createHonor",o={userName:t.formData.userName,department:t.formData.department,userAvatar:t.formData.userAvatar,honorTypeId:t.formData.honorTypeId,batchId:t.formData.batchId,reason:t.formData.reason,isFeatured:t.formData.isFeatured,images:t.formData.images},t.isEditMode&&(o.honorId=t.recordId),a.next=9,n.callFunction({name:"honor-admin",data:{action:r,data:o}});case 9:if(s=a.sent,0!==s.result.code){a.next=15;break}e.showToast({title:t.isEditMode?"表彰记录更新成功":"表彰记录创建成功",icon:"success",duration:2e3}),setTimeout((function(){e.navigateBack()}),2e3),a.next=16;break;case 15:throw new Error(s.result.message||(t.isEditMode?"更新失败":"创建失败"));case 16:a.next=21;break;case 18:a.prev=18,a.t0=a["catch"](3),e.showToast({title:a.t0.message||"保存失败",icon:"none"});case 21:return a.prev=21,t.saving=!1,a.finish(21);case 24:case"end":return a.stop()}}),a,null,[[3,18,21,24]])})))()},validateForm:function(){return this.formData.userName.trim()?this.formData.honorTypeId?this.formData.batchId?!!this.formData.reason.trim()||(e.showToast({title:"请填写表彰原因",icon:"none"}),!1):(e.showToast({title:"请选择表彰批次",icon:"none"}),!1):(e.showToast({title:"请选择荣誉类型",icon:"none"}),!1):(e.showToast({title:"请输入被表彰人姓名",icon:"none"}),!1)},goBack:function(){this.hasChanges()?e.showModal({title:"确认离开",content:"您有未保存的内容，确定要离开吗？",success:function(t){t.confirm&&e.navigateBack()}}):e.navigateBack()},hasChanges:function(){return this.formData.userName.trim()||this.formData.reason.trim()||this.formData.honorTypeId||this.formData.batchId||this.formData.images.length>0},openDepartmentSelector:function(){this.showDepartmentSelector=!0},closeDepartmentSelector:function(){this.showDepartmentSelector=!1,this.newDepartmentName=""},selectDepartment:function(e){this.formData.department=e.value,this.closeDepartmentSelector()},addNewDepartment:function(){var t=this;return(0,i.default)(c.default.mark((function a(){var n,r;return c.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(n=t.newDepartmentName.trim(),n){a.next=4;break}return e.showToast({title:"请输入部门名称",icon:"none"}),a.abrupt("return");case 4:if(r=t.departmentOptions.some((function(e){return e.value===n})),!r){a.next=8;break}return e.showToast({title:"部门已存在",icon:"none"}),a.abrupt("return");case 8:t.departmentOptions.push({value:n,text:n}),t.formData.department=n,t.newDepartmentName="",t.closeDepartmentSelector(),e.showToast({title:"部门已添加（临时）",icon:"success",duration:2e3});case 13:case"end":return a.stop()}}),a)})))()},deleteDepartment:function(t){var a=this;this.departmentOptions.length<=1?e.showToast({title:"至少需要保留一个部门",icon:"none"}):e.showModal({title:"确认删除",content:'确定要删除"'.concat(t.text,'"部门吗？'),success:function(){var r=(0,i.default)(c.default.mark((function r(o){var s,i;return c.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!o.confirm){r.next=17;break}return r.prev=1,r.next=4,n.callFunction({name:"honor-admin",data:{action:"checkDepartmentUsage",data:{name:t.value}}});case 4:if(s=r.sent,0===s.result.code){r.next=8;break}return e.showToast({title:s.result.message||"该部门正在使用中，无法删除",icon:"none",duration:3e3}),r.abrupt("return");case 8:r.next=13;break;case 10:r.prev=10,r.t0=r["catch"](1),console.log("检查部门使用情况失败，继续删除:",r.t0);case 13:i=a.departmentOptions.findIndex((function(e){return e.value===t.value})),i>-1&&a.departmentOptions.splice(i,1),a.formData.department===t.value&&(a.formData.department=""),e.showToast({title:"部门已移除",icon:"success"});case 17:case"end":return r.stop()}}),r,null,[[1,10]])})));return function(e){return r.apply(this,arguments)}}()})},openHonorTypeSelector:function(){this.showHonorTypeSelector=!0},closeHonorTypeSelector:function(){this.showHonorTypeSelector=!1},selectHonorType:function(e){this.formData.honorTypeId=e.value,this.selectedHonorType=e.data,this.closeHonorTypeSelector()},openBatchSelector:function(){this.showBatchSelector=!0},closeBatchSelector:function(){this.showBatchSelector=!1,this.batchSearchKeyword=""},onBatchSearch:function(){this.showOlderBatchCount=10},selectBatch:function(e){this.formData.batchId=e.value,this.selectedBatch=e.data,this.closeBatchSelector()},showMoreOlderBatches:function(){this.showOlderBatchCount+=10},formatBatchDate:function(e){if(!e)return"";var t=new Date(e),a=t.getMonth()+1,n=t.getDate();return"".concat(a,"月").concat(n,"日")},getBatchTypeText:function(e){return{weekly:"周表彰",monthly:"月表彰",quarterly:"季度表彰",yearly:"年度表彰",special:"特别表彰"}[e]||"其他"},quickCreateBatch:function(){this.closeBatchSelector(),e.navigateTo({url:"/pages/honor_pkg/admin/batch-manager?quick=true"})},previewImage:function(t){this.formData.images.length>0&&e.previewImage({urls:this.formData.images.map((function(e){return e.url})),current:this.formData.images[t].url,indicator:"number"})}}};t.default=p}).call(this,a("df3c")["default"],a("861b")["uniCloud"])},"095b":function(e,t,a){"use strict";(function(e,t){var n=a("47a9");a("357b"),a("861b");n(a("3240"));var r=n(a("3b53"));e.__webpack_require_UNI_MP_PLUGIN__=a,t(r.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},"3b53":function(e,t,a){"use strict";a.r(t);var n=a("c1a2"),r=a("db73");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("e689");var c=a("828b"),s=Object(c["a"])(r["default"],n["b"],n["c"],!1,null,"34795447",null,!1,n["a"],void 0);t["default"]=s.exports},4381:function(e,t,a){},c1a2:function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return n}));var n={uniIcons:function(){return Promise.all([a.e("common/vendor"),a.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(a.bind(null,"6ddf"))},uniEasyinput:function(){return a.e("uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput").then(a.bind(null,"6cf4"))}},r=function(){var e=this,t=e.$createElement,a=(e._self._c,e.formData.reason.length),n=e.formData.images.length,r=e.formData.images.length,o=r>0?e.formData.images.slice(0,3):null,c=r>0?e.formData.images.length:null,s=r>0&&c>3?e.formData.images.length:null,i=e.showDepartmentSelector?e.newDepartmentName.trim():null,u=e.showDepartmentSelector?e.departmentOptions.length:null,l=e.showDepartmentSelector?e.__map(e.departmentOptions,(function(t,a){var n=e.__get_orig(t),r=e.departmentOptions.length;return{$orig:n,g7:r}})):null,h=e.showBatchSelector?e.recentBatches.length:null,d=e.showBatchSelector&&h>0?e.__map(e.recentBatches,(function(t,a){var n=e.__get_orig(t),r=e.formatBatchDate(t.data.meetingDate),o=e.getBatchTypeText(t.data.type);return{$orig:n,m0:r,m1:o}})):null,f=e.showBatchSelector?e.currentMonthBatches.length:null,p=e.showBatchSelector&&f>0?e.__map(e.currentMonthBatches,(function(t,a){var n=e.__get_orig(t),r=e.formatBatchDate(t.data.meetingDate),o=e.getBatchTypeText(t.data.type);return{$orig:n,m2:r,m3:o}})):null,m=e.showBatchSelector?e.filteredOlderBatches.length:null,g=e.showBatchSelector&&m>0?e.olderBatches.length:null,v=e.showBatchSelector&&m>0?e.__map(e.filteredOlderBatches,(function(t,a){var n=e.__get_orig(t),r=e.formatBatchDate(t.data.meetingDate),o=e.getBatchTypeText(t.data.type);return{$orig:n,m4:r,m5:o}})):null,w=e.showBatchSelector&&m>0?e.olderBatches.length:null,D=e.showBatchSelector&&m>0&&w>e.showOlderBatchCount?e.olderBatches.length:null,b=e.showBatchSelector?e.filteredBatchOptions.length:null;e._isMounted||(e.e0=function(t,a){var n=arguments[arguments.length-1].currentTarget.dataset,r=n.eventParams||n["event-params"];a=r.index;return e.previewImage(a)}),e.$mp.data=Object.assign({},{$root:{g0:a,g1:n,g2:r,l0:o,g3:c,g4:s,g5:i,g6:u,l1:l,g8:h,l2:d,g9:f,l3:p,g10:m,g11:g,l4:v,g12:w,g13:D,g14:b}})},o=[]},db73:function(e,t,a){"use strict";a.r(t);var n=a("0399"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},e689:function(e,t,a){"use strict";var n=a("4381"),r=a.n(n);r.a}},[["095b","common/runtime","common/vendor"]]]);