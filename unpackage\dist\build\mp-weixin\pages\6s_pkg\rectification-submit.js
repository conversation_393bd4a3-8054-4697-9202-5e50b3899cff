require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/rectification-submit"],{"3cc4":function(t,e,n){"use strict";var i=n("f014"),a=n.n(i);a.a},7664:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return i}));var i={uniIcons:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(n.bind(null,"6ddf"))},pEmptyState:function(){return n.e("components/p-empty-state/p-empty-state").then(n.bind(null,"9b76"))}},a=function(){var t=this.$createElement,e=(this._self._c,this.taskInfo.issuePhotos.length),n=this.rectificationPhotos.length,i=n<this.maxPhotos?this.rectificationPhotos.length:null;this.$mp.data=Object.assign({},{$root:{g0:e,g1:n,g2:i}})},r=[]},"7b8f":function(t,e,n){"use strict";n.r(e);var i=n("7664"),a=n("c6df");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("3cc4");var o=n("828b"),s=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"16fef611",null,!1,i["a"],void 0);e["default"]=s.exports},"8a89":function(t,e,n){"use strict";(function(t,e){var i=n("47a9");n("357b"),n("861b");i(n("3240"));var a=i(n("7b8f"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},ad25:function(t,e,n){"use strict";(function(t,i){var a=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=a(n("7eb4")),o=a(n("af34")),s=a(n("ee10")),c=n("882c"),u=a(n("4ea0"));function l(t,e){var n="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"===typeof t)return d(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return d(t,e)}(t))||e&&t&&"number"===typeof t.length){n&&(t=n);var i=0,a=function(){};return{s:a,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,o=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return o=t.done,t},e:function(t){s=!0,r=t},f:function(){try{o||null==n.return||n.return()}finally{if(s)throw r}}}}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}var f={name:"RectificationSubmit",data:function(){return{taskId:null,isFromIssue:!1,loading:!1,loadingText:"加载中...",submitting:!1,maxPhotos:6,autoUpload:!0,taskInfo:{id:null,title:"",areaName:"",areaId:"",deadline:"",description:"",issuePhotos:[],inspectorName:"",createdAt:"",status:"pending_rectification"},rectificationPhotos:[],rectificationDescription:""}},computed:{descriptionLength:function(){return this.rectificationDescription?this.rectificationDescription.length:0},canSubmit:function(){var t=this.rectificationPhotos.length>0,e=this.rectificationDescription.trim().length>=2,n=this.rectificationPhotos.every((function(t){return t.uploaded}));return t&&e&&n}},onLoad:function(e){if(this.taskId=e.taskId||e.issueId,this.isFromIssue=!!e.issueId,!this.taskId)return t.showToast({title:"缺少参数",icon:"error"}),void setTimeout((function(){return t.navigateBack()}),1500);this.loadTaskInfo(),t.setNavigationBarTitle({title:"整改提交"})},methods:{handleDescriptionInput:function(e){var n=e.detail.value||"";n.length>200&&(n=n.substring(0,200),t.showToast({title:"整改说明不能超过200个字符",icon:"none",duration:1500})),this.rectificationDescription=n,this.$forceUpdate()},loadTaskInfo:function(){var e=this;return(0,s.default)(r.default.mark((function n(){var i,a,o,s,u,l;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(e.loading=!0,e.loadingText=e.isFromIssue?"加载问题信息...":"加载整改任务信息...",n.prev=2,!e.isFromIssue){n.next=15;break}return n.next=6,(0,c.callCloudFunction)("hygiene-monthly-inspection",{action:"getIssueDetail",data:{issue_id:e.taskId}});case 6:if(i=n.sent,!(i&&i.success&&i.data)){n.next=12;break}a=i.data,e.taskInfo=e.formatIssueAsTask(a),n.next=13;break;case 12:throw new Error("获取问题信息失败");case 13:n.next=23;break;case 15:return n.next=17,(0,c.callCloudFunction)("hygiene-rectification",{action:"getRectificationDetail",data:{id:e.taskId}});case 17:if(i=n.sent,!(i&&i.success&&i.data)){n.next=22;break}e.taskInfo=e.formatTaskData(i.data),n.next=23;break;case 22:throw new Error("获取整改任务信息失败");case 23:n.next=32;break;case 25:n.prev=25,n.t0=n["catch"](2),console.error("加载失败:",n.t0),l="加载失败",null!==(o=n.t0.message)&&void 0!==o&&o.includes("未知的操作类型")?l="API接口暂时不可用，请稍后重试":null!==(s=n.t0.message)&&void 0!==s&&s.includes("问题信息失败")?l="问题不存在或已被删除":null!==(u=n.t0.message)&&void 0!==u&&u.includes("整改任务信息失败")?l="整改任务不存在或已被删除":n.t0.message&&(l=n.t0.message),t.showToast({title:l,icon:"error",duration:3e3}),setTimeout((function(){return t.navigateBack()}),2e3);case 32:return n.prev=32,e.loading=!1,n.finish(32);case 35:case"end":return n.stop()}}),n,null,[[2,25,32,35]])})))()},formatTaskData:function(t){return{id:t._id||t.id,title:this.generateTaskTitle(t),areaName:t.area_name||"未知责任区",areaId:t.area_id,deadline:this.formatDeadline(t.deadline||t.created_at),description:t.issue_description||t.description||"",issuePhotos:this.formatIssuePhotos(t.issue_photos||[]),inspectorName:t.inspector_name||"检查员",createdAt:t.created_at,status:t.status||"pending_rectification"}},formatIssueAsTask:function(t){var e,n;return{id:t._id||t.id,title:t.title||t.issue_title||"整改任务",areaName:(null===(e=t.location_info)||void 0===e?void 0:e.location_name)||t.location||"未知区域",areaId:t.area_id||(null===(n=t.location_info)||void 0===n?void 0:n.area_id)||"",deadline:this.formatDeadline(t.expected_completion_date||t.deadline),description:t.description||t.issue_description||"",issuePhotos:this.formatIssuePhotos(t.photos||t.images||t.issue_photos||[]),inspectorName:t.inspector_name||"检查员",createdAt:t.created_at,status:"pending_rectification"}},generateTaskTitle:function(t){var e=t.issue_type||"清理问题",n=t.number||Math.floor(999*Math.random())+1;return"".concat(e," #").concat(String(n).padStart(3,"0"))},formatDeadline:function(t){if(!t)return"待定";var e=new Date(t),n=(new Date,new Date(e.getTime()+1728e5));return"".concat(n.getMonth()+1,"月").concat(n.getDate(),"日 ").concat(n.getHours().toString().padStart(2,"0"),":").concat(n.getMinutes().toString().padStart(2,"0"))},formatIssuePhotos:function(t){return Array.isArray(t)?t.map((function(t){return"string"===typeof t?t:t.url||t.path||t})):[]},chooseImage:function(){var e=this;return(0,s.default)(r.default.mark((function n(){var i,a,s;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!(e.rectificationPhotos.length>=e.maxPhotos)){n.next=3;break}return t.showToast({title:"最多只能上传".concat(e.maxPhotos,"张照片"),icon:"none"}),n.abrupt("return");case 3:return n.prev=3,n.next=6,t.chooseImage({count:e.maxPhotos-e.rectificationPhotos.length,sizeType:["compressed"],sourceType:["camera","album"]});case 6:a=n.sent,s=a.tempFilePaths.map((function(t){return{url:t,uploaded:!1,cloudUrl:"",cloudPath:"",uploading:!1,uploadTime:(new Date).toISOString()}})),(i=e.rectificationPhotos).push.apply(i,(0,o.default)(s)),e.autoUpload&&e.autoUploadNewPhotos(s),n.next=16;break;case 12:n.prev=12,n.t0=n["catch"](3),console.error("选择图片失败:",n.t0),t.showToast({title:"选择照片失败",icon:"none"});case 16:case"end":return n.stop()}}),n,null,[[3,12]])})))()},autoUploadNewPhotos:function(t){var e=this;return(0,s.default)(r.default.mark((function n(){var i,a;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:for(i=0;i<t.length;i++)a=t[i],a.uploaded||a.uploading||e.uploadSinglePhoto(a);case 1:case"end":return n.stop()}}),n)})))()},uploadSinglePhoto:function(e){var n=this;return(0,s.default)(r.default.mark((function i(){var a,o,s;return r.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!e.uploading&&!e.uploaded){i.next=2;break}return i.abrupt("return");case 2:return e.uploading=!0,n.$forceUpdate(),i.prev=4,a=n.generateCloudPath(),i.next=8,u.default.uploadToCloud(e.url,a);case 8:if(o=i.sent,null===o||void 0===o||!o.fileID){i.next=16;break}return i.next=12,u.default.getFileInfo(o.fileID);case 12:s=i.sent,Object.assign(e,{uploaded:!0,uploading:!1,cloudUrl:s.tempFileURL||o.fileID,cloudPath:o.fileID,size:o.actualSize}),i.next=17;break;case 16:throw new Error("上传返回结果异常");case 17:i.next=25;break;case 19:i.prev=19,i.t0=i["catch"](4),console.error("照片上传失败:",i.t0),e.uploading=!1,e.uploaded=!1,t.showToast({title:"照片上传失败",icon:"none"});case 25:return i.prev=25,n.$forceUpdate(),i.finish(25);case 28:case"end":return i.stop()}}),i,null,[[4,19,25,28]])})))()},generateCloudPath:function(){var t=Date.now(),e=Math.random().toString(36).substring(2,8),n=this.taskInfo.areaId||this.taskId||"temp";return"6s/rectification/".concat(n,"/").concat(t,"_").concat(e,".jpg")},toggleAutoUpload:function(){this.autoUpload=!this.autoUpload,t.showToast({title:this.autoUpload?"已开启自动上传":"已关闭自动上传",icon:"none"})},previewPhoto:function(e){var n=this.rectificationPhotos.map((function(t){return t.url}));t.previewImage({urls:n,current:e})},previewOriginalImage:function(e){t.previewImage({urls:this.taskInfo.issuePhotos,current:e})},deletePhoto:function(e){var n=this;return(0,s.default)(r.default.mark((function a(){var o;return r.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!(e<0||e>=n.rectificationPhotos.length)){a.next=2;break}return a.abrupt("return");case 2:if(o=n.rectificationPhotos[e],!o.uploaded||!o.cloudPath){a.next=18;break}return a.prev=4,t.showLoading({title:"删除照片中..."}),a.next=8,i.callFunction({name:"delete-file",data:{fileList:[n.extractFileId(o.cloudPath)]}});case 8:a.next=15;break;case 10:return a.prev=10,a.t0=a["catch"](4),console.error("删除云文件失败:",a.t0),t.showToast({title:"删除失败",icon:"none"}),a.abrupt("return");case 15:return a.prev=15,t.hideLoading(),a.finish(15);case 18:n.rectificationPhotos.splice(e,1);case 19:case"end":return a.stop()}}),a,null,[[4,10,15,18]])})))()},extractFileId:function(t){return t.replace(/^cloud:\/\//,"")},submitRectification:function(){var e=this;return(0,s.default)(r.default.mark((function n(){var i,a,o,s,u,l;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(0!==e.rectificationPhotos.length){n.next=3;break}return t.showToast({title:"请至少上传一张整改照片",icon:"none"}),n.abrupt("return");case 3:if(!(e.rectificationDescription.trim().length<2)){n.next=6;break}return t.showToast({title:"请输入整改说明（至少2个字符）",icon:"none"}),n.abrupt("return");case 6:if(i=e.rectificationPhotos.filter((function(t){return!t.uploaded})),!(i.length>0)){n.next=10;break}return t.showToast({title:"请等待照片上传完成或重新上传失败的照片",icon:"none",duration:3e3}),n.abrupt("return");case 10:if(a=e.rectificationPhotos.filter((function(t){return t.uploading})),!(a.length>0)){n.next=14;break}return t.showToast({title:"照片正在上传中，请稍候...",icon:"none"}),n.abrupt("return");case 14:if(o=e.rectificationPhotos.filter((function(t){return!t.uploaded})),!(o.length>0)){n.next=18;break}return t.showModal({title:"提示",content:"有照片未上传成功，是否重新上传？",success:function(t){t.confirm&&e.retryFailedUploads()}}),n.abrupt("return");case 18:if(e.submitting=!0,e.loading=!0,e.loadingText="提交整改中...",n.prev=21,s={id:e.taskId,rectification_photos:e.rectificationPhotos.map((function(t){return{url:t.cloudUrl||t.cloudPath,type:"rectification",description:""}})),rectification_description:e.rectificationDescription.trim(),submitted_at:(new Date).toISOString(),status:"pending_review"},!e.isFromIssue){n.next=29;break}return n.next=26,(0,c.callCloudFunction)("hygiene-monthly-inspection",{action:"updateMonthlyIssue",data:{issue_id:e.taskId,status:"pending_review",action_type:"submit_rectification",rectification_description:s.rectification_description,rectification_photos:s.rectification_photos,completed_at:s.submitted_at}});case 26:u=n.sent,n.next=32;break;case 29:return n.next=31,(0,c.callCloudFunction)("hygiene-rectification",{action:"completeRectification",data:{id:e.taskId,completion_description:s.rectification_description,completion_photos:s.rectification_photos}});case 31:u=n.sent;case 32:if(!u||!u.success){n.next=38;break}e.isFromIssue?t.$emit("monthlyIssueUpdated",{action:"submit_rectification",issueId:e.taskId,status:"pending_review"}):t.$emit("rectificationRecordUpdated",{taskId:e.taskId,areaId:e.taskInfo.areaId,status:"pending_review"}),t.showToast({title:"整改提交成功",icon:"success"}),setTimeout((function(){t.navigateBack()}),1500),n.next=39;break;case 38:throw new Error((null===(l=u)||void 0===l?void 0:l.message)||"提交失败");case 39:n.next=45;break;case 41:n.prev=41,n.t0=n["catch"](21),console.error("提交整改失败:",n.t0),t.showToast({title:n.t0.message||"提交失败",icon:"error"});case 45:return n.prev=45,e.submitting=!1,e.loading=!1,n.finish(45);case 49:case"end":return n.stop()}}),n,null,[[21,41,45,49]])})))()},retryFailedUploads:function(){var t=this;return(0,s.default)(r.default.mark((function e(){var n,i,a,o;return r.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:n=t.rectificationPhotos.filter((function(t){return!t.uploaded})),i=l(n),e.prev=2,i.s();case 4:if((a=i.n()).done){e.next=10;break}return o=a.value,e.next=8,t.uploadSinglePhoto(o);case 8:e.next=4;break;case 10:e.next=15;break;case 12:e.prev=12,e.t0=e["catch"](2),i.e(e.t0);case 15:return e.prev=15,i.f(),e.finish(15);case 18:case"end":return e.stop()}}),e,null,[[2,12,15,18]])})))()},delay:function(t){return new Promise((function(e){return setTimeout(e,t)}))}}};e.default=f}).call(this,n("df3c")["default"],n("861b")["uniCloud"])},c6df:function(t,e,n){"use strict";n.r(e);var i=n("ad25"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},f014:function(t,e,n){}},[["8a89","common/runtime","common/vendor"]]]);