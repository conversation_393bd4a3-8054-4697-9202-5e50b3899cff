{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s_pkg/area-inspection.vue?4d8b", "webpack:///D:/Xwzc/pages/6s_pkg/area-inspection.vue?83c3", "webpack:///D:/Xwzc/pages/6s_pkg/area-inspection.vue?c088", "webpack:///D:/Xwzc/pages/6s_pkg/area-inspection.vue?4d87", "uni-app:///pages/6s_pkg/area-inspection.vue", "webpack:///D:/Xwzc/pages/6s_pkg/area-inspection.vue?930d", "webpack:///D:/Xwzc/pages/6s_pkg/area-inspection.vue?ace3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "API", "AREA_PAGE_SIZE", "INSPECTION_PAGE_SIZE", "RECTIFICATION_PAGE_SIZE", "DEFAULT_QUERY_DAYS", "TIME", "NIGHT_HOUR", "TOAST_DURATION", "DEBOUNCE_DELAY", "name", "data", "dateFilterMode", "selected<PERSON><PERSON><PERSON><PERSON>er", "selectedCate<PERSON><PERSON>", "selectedStatus", "customDateRange", "startDate", "endDate", "calendarDate", "calendarStartDate", "calendarEndDate", "refreshTimer", "calendarChangeTimer", "quickDateOptions", "label", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "categoryTabs", "inspectionRecords", "loading", "loadError", "expandedWeeks", "dataLoaded", "needsRefresh", "processCache", "timeCalculations", "statusMap", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "areaTypeMap", "formattedDatesCache", "areasCache", "computed", "filteredRecords", "records", "groupedFilteredRecords", "grouped", "<PERSON><PERSON><PERSON>", "title", "expanded", "Object", "group", "sortedGroups", "created", "onLoad", "uni", "onUnload", "clearTimeout", "onShow", "methods", "initProcessCache", "initTimeCalculations", "now", "weekStart", "weekEnd", "currentWeekKey", "getAreaTypeText", "getStatusPriority", "getCurrentWeekRange", "monday", "sunday", "start", "end", "loadPageDataOptimized", "currentWeek", "pendingReviewWeeks", "filter", "map", "handleLoadError", "content", "showCancel", "cancelText", "confirmText", "success", "loadPageData", "loadInspectionRecordsOptimized", "action", "pageSize", "areasResult", "areas", "cleaningRecordsByWeekArea", "inspectionRecordsByWeekArea", "weeksToShow", "inspectionTasks", "area", "cleaningRecord", "inspectionRecord", "extractAreas", "batchLoadRecords", "areaIds", "Promise", "cleaningResults", "inspectionResults", "batchGetCleaningRecords", "date<PERSON><PERSON><PERSON>", "area_ids", "start_date", "end_date", "latest_only", "result", "console", "batchGetInspectionRecords", "promises", "area_id", "results", "record", "processAreaInspection", "status", "subtitle", "id", "areaName", "icon", "type", "week", "inspectionDate", "isRectificationRecheck", "areaId", "<PERSON><PERSON><PERSON>", "inspectionRecordId", "isInspectionRecord", "scheduledDay", "processInspectionStatus", "loadInspectionRecords", "getWeekStartOptimized", "getWeekEndOptimized", "getWeekStart", "getWeekEnd", "calculateMissedStatusOptimized", "hasValidInspection", "nextWeekStart", "calculateMissedStatus", "mapInspectionStatus", "generateRecordSubtitle", "getWeekKey", "getCurrentWeekKeyOptimized", "getCurrentWeekKey", "getWeekNumber", "getMonthWeeks", "weeks", "getQuarterWeeks", "generateWeeksToShow", "weekDate", "generateWeeksForCurrentFilter", "currentDate", "processAreaInspectionForWeek", "getWeekTimesFromKey", "processInspectionStatusForWeek", "parseWeekKey", "year", "getWeekDisplayNumber", "getCurrentTimeRange", "getStatusFilterText", "getStatsData", "not_cleaned", "pending", "pending_rectification", "pending_review", "completed", "missed", "selectCategory", "selectStatus", "getCategoryCount", "getEmptyText", "handleRecordClick", "handleNotCleanedClick", "getNotCleanedMessage", "getAreaScheduledDay", "showTimeSelector", "closeDatePicker", "initializeDateRange", "switchToRangeMode", "switchToQuickMode", "selectQuickDateOption", "getCurrentDateRange", "getQuickDateRange", "startOfWeek", "endOfWeek", "lastWeekStart", "lastWeekEnd", "endOfMonth", "isRecordInDateRange", "date", "scheduledDate", "event", "duration", "dateStr", "category", "targetWeek", "yearA", "weekA", "yearB", "weekB", "option", "url", "require", "callCloudFunction", "hasValidTask", "possibleStatuses", "task", "hasSubmitted", "inspection_record_id", "rectificationTask", "taskId", "dateString"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,iVAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3HA;AAAA;AAAA;AAAA;AAAymB,CAAgB,moBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;ACoM7nB;AAAA;AAEA;AACA;EACAC;IACAC;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;EACA;;EACAC;IACAC;IAAA;IACAC;IAAA;IACAC;EACA;AACA;AAAA,eAEA;EACAC;EACAC;IACA;MACA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;QACAC;QACAC;MACA;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC,mBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;MAEA;MACAC,eACA;QAAAH;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MAEA;MACAG;MACA;MACAC;MACAC;MACA;MACAC;MAAA;MACA;MACAC;MACAC;MAAA;;MAEA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;QACAC;UACA;UACA;QACA;MACA;;MAEA;MACA;QACAA;UAAA;QAAA;MACA;;MAEA;MACA;QACAA;UAAA;QAAA;MACA;MAEA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MAEAD;QACA;QACA;UACAE;YACAC;YACAC;YACAJ;YACAK;UACA;QACA;QACAH;MACA;;MAEA;MACAI;QACAC;UAAA;QAAA;MACA;;MAEA;MACA;QACA;QACA;QACA;MACA;;MAEA;MACAC;QACAD;MACA;MAEA;IACA;EACA;EACAE;IACA;EACA;EACAC;IACA;;IAEA;IACAC;IACA;IACAA;IACA;IACAA;EACA;EACAC;IACA;IACAD;IACAA;IACAA;;IAEA;IACA;MACAE;IACA;IACA;MACAA;IACA;EACA;EACAC;IACA;IACA;IACA;MACA;MACA;IACA;EACA;;EACAC;IACA;IACAC;MACA;QACA;UACA;UACA;QACA;MACA;MAEA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACA;QACA;UACAC;UACAC;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;MAEA;MACAC;MAEA;MACAC;MAEA;MACA;MAEA;QACAC;QACAC;QACAH;QACAC;MACA;IACA;IAEA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAGA;gBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACA;kBACAC;kBACA;;kBAEA;kBACAC,8CACAC;oBAAA;kBAAA,GACAC;oBAAA;kBAAA,GACAD;oBAAA;kBAAA;kBAEAD;oBACA;sBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAG;MAAA;MACAvB;QACAP;QACA+B;QACAC;QACAC;QACAC;QACAC;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;kBACAC;kBACA3E;oBAAA4E;kBAAA;gBACA;cAAA;gBAHAC;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACAC,+CAEA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;gBAAAC;gBAAAC;gBAEA;gBACAC,sDAEA;gBACAC;gBAEAJ;kBACAG;oBACA;oBACA;oBAEA;oBACA;oBAEA,+CACAE,MACA/C,SACAgD,gBACAC,iBACA;oBAEA;sBACAH;oBACA;kBACA;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACAI;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBAAA;gBAAA,IAEA;gBAAA;gBAAA,OACAC,oBACA,yCACA,0CACA;cAAA;gBAAA;gBAAA;gBAHAC;gBAAAC;gBAKA;gBACAZ;gBACAC;gBAEA;gBACA;kBACAU;oBACA;oBACA;oBACA;oBACA;;oBAEA;oBACA,8CACA;sBACAX;oBACA;kBACA;gBACA;;gBAEA;gBACA;kBACAY;oBACA;oBACA;oBACA;oBACA;;oBAEA;oBACA,gDACA;sBACAX;oBACA;kBACA;gBACA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAY;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;gBAGA;kBACA;kBACAvF;kBACAC;;kBAEA;kBACAD;kBACAC;gBACA;kBACA;kBACAA;kBACAD;kBACAA;gBACA;gBAEAA;gBACAC;gBAAA;gBAAA,OAEA;kBACAoE;kBACA3E;oBACA8F;oBACAC;oBACAC;oBACAC;kBACA;gBACA;cAAA;gBARAC;gBAAA,kCAWAA;cAAA;gBAAA;gBAAA;gBAEAC;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;kBAAA,OACA;oBACA1B;oBACA3E;sBACAsG;sBACA1B;oBACA;kBACA;gBAAA,EACA;gBAAA;gBAAA,OAEAa;cAAA;gBAAAc;gBACAtE;gBAEAsE;kBAAA;kBACA;oBACA;oBACAL;sBACAM;sBACAvE;oBACA;kBACA;gBACA;gBAAA,kCAEAA;cAAA;gBAAA;gBAAA;gBAEAkE;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAM;MACA;MACA;QAAArD;QAAAC;MAEA;MACA;MAEA;QACA;QAEA;UACA;UACA;YACAqD;YACAC;UACA;YACA;YAEA;cAAA,4BACA;cAAAD;cAAAC;YACA;cACAD;cACAC;YACA;UACA;YACAD;YACAC;UACA;QACA;MACA;MAEA;QACAC;QACAC;QACAF;QACAD;QACAI;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACA;QACAC;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACAd;QACAC;MACA;QACA;QACA;UACAD;UACAC;QACA;UACAD;UACAC;QACA;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;MAEA;QAAAD;QAAAC;MAAA;IACA;IAEA;IACAc;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACAhE;MACAA;MACA;IACA;IAEA;IACAiE;MAAA;MACA;MACA;MACA;MACA;MACAhE;MACAA;MACA;IACA;IAEA;IACAiE;MAAA;MACA;IACA;IAEAC;MAAA;MACA;IACA;IAEA;IACAC;MACA;QAAA3E;QAAAC;QAAAC;;MAEA;MACA;QACA;;QAEA;QACA;UACA;UACA;UACA;YACA;YACA;YACA0E;UACA;;UAEA;UACA;YACA;cACA;cACA;cACAC;;cAEA;gBACA;cACA;YACA;cACA;cACA;;cAEA;cACA;gBACA;cACA;YACA;UACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;UACA;;UAEA;UACA;UACA;YACA;YACA;YACAA;;YAEA;cACA;YACA;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MAEA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA,uDACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACA;MACA;MAEA;MACA;MACA;MAEA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;;MAEA;MACA;MACA;MAEA;MACA;QACA;QACA;UACAC;QACA;MACA;MAEA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MAEA;MACA;QACA;QACA;UACAD;QACA;MACA;MAEA;IACA;IAEA;IACAE;MAAA;MACA;MACA;;MAEA;MACA;QACA;QACAC;QACA;QACA;UACAH;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAI;MACA;MAEA;QACA;QACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACAC;MAEA;QACA;QACA;UACAL;QACA;QACA;QACAK;MACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MAEA;MACA;;MAEA;MACA;QACA;QAEA;UACA;UAEA;YAAA,6BACA;YAAArC;YAAAC;UACA;YACAD;YACAC;UACA;QACA;UACA;UACA;UACA;YACAD;YACAC;UACA;YACAD;YACAC;UACA;QACA;MACA;QACA;;QAEA;QACA;UACA;UACA;YACAD;YACAC;UACA;YACAD;YACAC;UACA;QACA;UACA;UACA;YACAD;YACAC;UACA;YACAD;YACAC;UACA;QACA;MACA;MAEA;QACAC;QACAC;QACAF;QACAD;QACAI;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAyB;MACA;QACA;QACA;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA5F;QACAA;;QAEA;QACA;QACAC;QACAA;QAEA;UAAAD;UAAAC;QAAA;MACA;QACA;MACA;IACA;IAEA;IACA4F;MACA;MACA;MAEA;QACA;QACA;UACAvC;UACAC;QACA;UACAD;UACAC;QACA;MACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QAEA;UACAD;UACAC;QACA;UACAD;UACAC;QACA;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;MAEA;QAAAD;QAAAC;MAAA;IACA;IAEA;IACAuC;MACA;MACA;MAEA;QACAC;QACAnC;MACA;IACA;IAEA;IACAoC;MACA;MACA;IACA;IAEAC;MAAA;MACA;QACA;MACA;QACA;UAAA;QAAA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MAEA;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA;IACA;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;MACA;QAAA;MAAA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACA;QACA;QACA;UAAA;QAAA;QACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;QACAtH;UACAP;UACA+B;UACAC;QACA;MACA;IACA;IAEA;IACA8F;MACA;QAAA9H;QAAA+B;MACAxB;QACAP;QACA+B;QACAC;MACA;IACA;IAEA;IACA+F;MACA;MACA;MACA;;MAEA;MACA;QACA;QACA;UACA/H;UACA+B;QACA;MACA;MAEA;;MAEA;QACA;QACA;QAEA;UACA;UACA;UACA;UAEA;UACA;UAEA;YACA;YACA;cACA/B;cACA+B;YACA;UACA;YACA;YACA;YACA;cAAA;cACA;gBACA/B;gBACA+B;cACA;YACA;cACA;gBACA/B;gBACA+B;cACA;YACA;UACA;YACA;YACA;cACA/B;cACA+B;YACA;UACA;QACA;UACA;UACA;YACA/B;YACA+B;UACA;QACA;MACA;QACA;QACA;QAEA;UACA;UACA;YACA/B;YACA+B;UACA;QACA;UACA;UACA;UACA;YACA;cACA/B;cACA+B;YACA;UACA;YACA;cACA/B;cACA+B;YACA;UACA;QACA;MACA;IACA;IAEA;IACAiG;MAAA;MACA;MACA;MACA;QAAA;MAAA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACA;MACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;QACA7H;QACA;UAAA;YAAA;cAAA;gBAAA;kBAAA;kBAAA;kBAAA,OAEA;gBAAA;kBACA;kBACA;oBACA;kBACA;gBAAA;kBAAA;kBAEA;kBAAA;gBAAA;gBAAA;kBAAA;cAAA;YAAA;UAAA;QAAA,CAEA;MACA;IACA;IAEA;IACA8H;MACA;QACA;MACA;QACA;UACAhH;UACAC;QACA;MACA;MACA;IACA;IAEA;IACAgH;MACA;MACA;MAEA;QACA;UACA;UACA;UACA;UACA;UACAC;UAEA;UACAC;UACAA;UAEA;YAAAnH;YAAAC;UAAA;QAEA;UACA;UACA;UACA;UACA;UACAmH;UAEA;UACAC;UACAA;UAEA;YAAArH;YAAAC;UAAA;QAEA;UACA;UACA;UACA;UACAqH;UAEA;YAAAtH;YAAAC;UAAA;QAEA;UACA;UACA;MAAA;IAEA;IAEA;IACAsH;MACA;MACA;MAEA;MACA;;MAEA;MACA;MACA;MACA;IACA;EAAA,iFAGAC;IACA;IACA;IACA;IACAlF;IACAA;IACA;EACA,sGAGA4C;IACA;MACA;IACA;;IAEA;IACA;IACA;;IAEA;IACA;IACA;IACAuC;IACAA;IAEA;EACA,gGAGAD;IACA;IACA;IACA;IACA;IACA;IACA;EACA,4FAGA;IACA;IACA;EACA,wFAGA;IACA;IACA;EACA,4FAGAA;IACA;IACA;IACA;IACA;EACA,4FAGA;IAAA;IACA;MACA;QAAA;MAAA;MACA;IACA;MACA;MACA;MACA;QACA;MACA;MACA;IACA;EACA,0FAGAE;IAAA;IAEA;IACAxI;IACA;MACA;MACA;QACA;UACA;UACA;UACA;;UAEA;UACA;UACA;UACA;;UAEA;UACA;UACAA;UACA;YAAA;cAAA;gBAAA;kBAAA;oBAAA;oBAAA;oBAAA,OAEA;kBAAA;oBACA;oBACA;sBACA;oBACA;kBAAA;oBAAA;oBAEA;oBAAA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAEA;UAEAF;YACAP;YACAyE;YACAyE;UACA;QACA;MACA;QACA;QACA;UACA;UACA;UACA;QAEA;UACA;UACA;;UAEA;UACA;YACA;YACA;YACA;UACA;;UAEA;UACA;UACA;UACA;;UAEA;UACA;UACAzI;UACA;YAAA;cAAA;gBAAA;kBAAA;oBAAA;oBAAA;oBAAA,OAEA;kBAAA;oBACA;oBACA;sBACA;oBACA;kBAAA;oBAAA;oBAEA;oBAAA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAEA;UAEAF;YACAP;YACAyE;YACAyE;UACA;QACA;MACA;IACA;EACA,oFAGAD;IACA;IACA;MACA;IACA;MACA;MACA;MACA;MACA;IACA;EACA,8FAGAE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA,oFAKA9E;IACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;EACA,wFAGA+E;IACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;EACA,kFAGArJ;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;;IAEA;IACA;MACA;MACA;IACA;MACA;IACA;MACA;IACA;MACA;IACA;EACA,8EAGAA;IACA;IACA;MACA;IACA;MACA;IACA;EACA,gGAGA;IACA;EACA,wGAGA;IAAA;IACA;IACA;MAAA;IAAA;IAEA;MACA;MACA;MACA;MAEA;;MAEA;MACA;QACA;QACAsJ;MACA;QACA;QACA;UACA;cAAA;YAAA;YAAA;YAAAC;YAAAC;UACA;cAAA;YAAA;YAAA;YAAAC;YAAAC;UAEA;YACA;UACA;;UACA;QACA;;QAEAJ;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA,8CACAzH;QAAA;MAAA,GACAC;QAAA;MAAA,GACAD;QAAA;MAAA;;MAEAD;QACA;UACA;QACA;MACA;IACA;EACA,0GAGA;IACA;EACA,8GACA+H;IAAA;IACA;;IAEA;IACA;IAEA;;IAEA;IACA;IACA;MACA;MACA;MACA;QACA;MACA;IACA;EACA,oFAEArF;IACA;EACA,kFAEAA;IACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;EACA,wFACAF;IACA;IACA;IACA;IACA;IACA5D;MACAoJ;IACA;EACA,0FACAxF;IACA;IACA;MACA5D;QACAoJ;MACA;IACA;MACA;MACApJ;QACAoJ;MACA;IACA;EACA,wGAGAxF;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAEA5D;gBAAAP;cAAA;cAAA,WAEA4J;cAAA,MAKAzF;gBAAA;gBAAA;cAAA;cACA;cACAW;cAAA;cAAA,OAEA+E;gBACAvH;gBACA3E;kBACAsG;kBACAI;kBACA9B;gBACA;cACA;YAAA;cAPAsB;cASA;cACAiG;cACA;gBACA;kBACAA;gBACA;kBACAA;gBACA;kBACAA;gBACA;cACA;cAAA,IAEAA;gBAAA;gBAAA;cAAA;cACAC;cAAA,4BACAA;YAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA1F;cAAA;cAAA,OACAwF;gBACAvH;gBACA3E;kBACAsG;kBACAI;kBACA9B;gBACA;cACA;YAAA;cAPAsB;cAAA,MASAA;gBAAA;gBAAA;cAAA;cACAmG;cACA;gBACAA;cACA;gBACAA;cACA;gBACAA;cACA;cAAA,KAEAA;gBAAA;gBAAA;cAAA;cACA;cACAC;cAAA,KACAA;gBAAA;gBAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA;cAAA;cAAA;YAAA;cAQA;cACAnF;cAAA;cAAA,OAEA+E;gBACAvH;gBACA3E;kBACAsG;kBACAI;kBACA9B;gBACA;cACA;YAAA;cAPAsB;cAAA,MAUA,8CACA,8DACA,wDACA;gBAAA;gBAAA;cAAA;cAAA;cAAA,OAEAgG;gBACAvH;gBACA3E;kBACAuM;kBACA3H;gBACA;cACA;YAAA;cANAsB;YAAA;cAUAtD;cAEA;gBACA4J,0BAEA;gBACA;kBACAA;gBACA;kBACAA;gBACA;kBACAA;gBACA;gBAEA;kBACAC;kBAEA;oBACA;oBACA7J;sBACAoJ;oBACA;kBACA;oBACApJ;sBACAP;sBACA+B;sBACAC;oBACA;kBACA;gBACA;kBACAzB;oBACAP;oBACA+B;oBACAC;kBACA;gBACA;cACA;gBACAzB;kBACAP;kBACA+B;kBACAC;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAzB;cACAA;gBACAP;gBACA+B;gBACAC;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA,oHAGAmC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAEA5D;gBAAAP;cAAA;cAAA,YAEA4J;cACA9E,qCAEA;cAAA;cAAA,OACA+E;gBACAvH;gBACA3E;kBACAsG;kBACAI;kBACA9B;gBACA;cACA;YAAA;cAPAsB;cAAA,MAUA,8CACA;gBAAA;gBAAA;cAAA;cAEAkG;cAAA,8BACAA;YAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA1F;cAAA;cAAA,OACAwF;gBACAvH;gBACA3E;kBACAsG;kBACAI;kBACA9B;gBACA;cACA;YAAA;cAPAsB;cAAA,MASAA;gBAAA;gBAAA;cAAA;cACAmG;cACA;gBACAA;cACA;gBACAA;cACA;gBACAA;cACA;cAAA,KAEAA;gBAAA;gBAAA;cAAA;cACA;cACAC;cAAA,KACAA;gBAAA;gBAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA;cAQA1J;cAEA;gBACA4J,0BAEA;gBACA;kBACAA;gBACA;kBACAA;gBACA;kBACAA;gBACA;gBAEA;kBACAC;kBAEA;oBACA;oBACA;oBACA7J;sBACAoJ;oBACA;kBACA;oBACApJ;sBACAP;sBACA+B;sBACAC;oBACA;kBACA;gBACA;kBACAzB;oBACAP;oBACA+B;oBACAC;kBACA;gBACA;cACA;gBACAzB;kBACAP;kBACA+B;kBACAC;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAzB;cACAA;gBACAP;gBACA+B;gBACAC;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA,wGAGAqI;IACA;IAEA;IACA;MACA;IACA;IAEA;MACA;MACA;QACA;UACAtB;QACA;UACAA;QACA;MACA;QACAA;MACA;MAEA;QACA;QACA;MACA;MAEA;MACA;MACA;IACA;MACA;MACA;IACA;EACA,sFAGAsB;IACA;EACA,gHAGA1M;IACA;IACA;EACA,oHAGAA;IACA;IACA;EACA,0HAGAA;IACA;IACA;EACA,8FAGA;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA,KACA;gBAAA;gBAAA;cAAA;cAAA;YAAA;cAAA;cAGA;cACA;cACA;cACA;;cAEA;cACA;;cAEA;cAAA;cAAA,OACA;YAAA;cACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAIA;AAEA;AAAA,2B;;;;;;;;;;;;;ACjvEA;AAAA;AAAA;AAAA;AAA4qC,CAAgB,kpCAAG,EAAC,C;;;;;;;;;;;ACAhsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/area-inspection.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/area-inspection.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./area-inspection.vue?vue&type=template&id=d9e42f30&scoped=true&\"\nvar renderjs\nimport script from \"./area-inspection.vue?vue&type=script&lang=js&\"\nexport * from \"./area-inspection.vue?vue&type=script&lang=js&\"\nimport style0 from \"./area-inspection.vue?vue&type=style&index=0&id=d9e42f30&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d9e42f30\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/area-inspection.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./area-inspection.vue?vue&type=template&id=d9e42f30&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniCalendar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-calendar/components/uni-calendar/uni-calendar\" */ \"@/uni_modules/uni-calendar/components/uni-calendar/uni-calendar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getCurrentTimeRange()\n  var m1 = _vm.selectedStatus !== \"all\" ? _vm.getStatusFilterText() : null\n  var m2 = !_vm.loading ? _vm.getStatsData() : null\n  var m3 = !_vm.loading ? _vm.getStatsData() : null\n  var m4 = !_vm.loading ? _vm.getStatsData() : null\n  var m5 = !_vm.loading ? _vm.getStatsData() : null\n  var m6 = !_vm.loading ? _vm.getStatsData() : null\n  var m7 = !_vm.loading ? _vm.getStatsData() : null\n  var l0 = _vm.__map(_vm.categoryTabs, function (category, __i0__) {\n    var $orig = _vm.__get_orig(category)\n    var m8 = _vm.getCategoryCount(category.value)\n    return {\n      $orig: $orig,\n      m8: m8,\n    }\n  })\n  var g0 = !_vm.loading ? _vm.groupedFilteredRecords.length : null\n  var l2 =\n    !_vm.loading && g0 > 0\n      ? _vm.__map(_vm.groupedFilteredRecords, function (group, __i1__) {\n          var $orig = _vm.__get_orig(group)\n          var g1 = group.records.length\n          var l1 = group.expanded\n            ? _vm.__map(group.records, function (record, recordIndex) {\n                var $orig = _vm.__get_orig(record)\n                var m9 = _vm.getIconColor(record.status)\n                var m10 = _vm.getAreaTypeText(record.type)\n                var m11 = _vm.getStatusText(record.status)\n                return {\n                  $orig: $orig,\n                  m9: m9,\n                  m10: m10,\n                  m11: m11,\n                }\n              })\n            : null\n          return {\n            $orig: $orig,\n            g1: g1,\n            l1: l1,\n          }\n        })\n      : null\n  var m12 =\n    !(_vm.dateFilterMode === \"quick\") &&\n    _vm.customDateRange.startDate &&\n    _vm.customDateRange.endDate\n      ? _vm.formatSelectedDate(_vm.customDateRange.startDate)\n      : null\n  var m13 =\n    !(_vm.dateFilterMode === \"quick\") &&\n    _vm.customDateRange.startDate &&\n    _vm.customDateRange.endDate\n      ? _vm.formatSelectedDate(_vm.customDateRange.endDate)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        l0: l0,\n        g0: g0,\n        l2: l2,\n        m12: m12,\n        m13: m13,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./area-inspection.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./area-inspection.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page-container\">\n\t\t<!-- 抽查统计 -->\n\t\t<view class=\"card\">\n\t\t\t<view class=\"card-header\">\n\t\t\t\t<view class=\"header-content\">\n\t\t\t\t\t<view class=\"card-title\">抽查统计</view>\n\t\t\t\t\t<view class=\"filters-row\">\n\t\t\t\t\t\t<view class=\"time-selector\" @click=\"showTimeSelector\">\n\t\t\t\t\t\t\t<text class=\"time-text\">{{ getCurrentTimeRange() }}</text>\n\t\t\t\t\t\t\t<uni-icons type=\"down\" size=\"12\" color=\"#007AFF\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-if=\"selectedStatus !== 'all'\" class=\"status-filter-indicator\"\n\t\t\t\t\t\t\t@click=\"selectStatus('all')\">\n\t\t\t\t\t\t\t<text class=\"filter-text\">{{ getStatusFilterText() }}</text>\n\t\t\t\t\t\t\t<uni-icons type=\"close\" size=\"12\" color=\"#8E8E93\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<!-- 统计数据 -->\n\t\t\t<view v-if=\"loading\" class=\"stats-loading\">\n\t\t\t\t<view class=\"loading-spinner\"></view>\n\t\t\t\t<view class=\"loading-text\">加载统计数据中...</view>\n\t\t\t</view>\n\t\t\t<view v-else class=\"stats-grid stats-grid-six\">\n\t\t\t\t<!-- 按优先级重新排序：检查员工作流程优先 -->\n\t\t\t\t<view class=\"stats-item\" :class=\"{ active: selectedStatus === 'pending_review' }\"\n\t\t\t\t\t@click=\"selectStatus('pending_review')\">\n\t\t\t\t\t<view class=\"stats-number review\">{{ getStatsData().pending_review }}</view>\n\t\t\t\t\t<view class=\"stats-label\">待复查</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stats-item\" :class=\"{ active: selectedStatus === 'pending' }\"\n\t\t\t\t\t@click=\"selectStatus('pending')\">\n\t\t\t\t\t<view class=\"stats-number warning\">{{ getStatsData().pending }}</view>\n\t\t\t\t\t<view class=\"stats-label\">待检查</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stats-item\" :class=\"{ active: selectedStatus === 'pending_rectification' }\"\n\t\t\t\t\t@click=\"selectStatus('pending_rectification')\">\n\t\t\t\t\t<view class=\"stats-number danger\">{{ getStatsData().pending_rectification }}</view>\n\t\t\t\t\t<view class=\"stats-label\">待整改</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stats-item\" :class=\"{ active: selectedStatus === 'missed' }\"\n\t\t\t\t\t@click=\"selectStatus('missed')\">\n\t\t\t\t\t<view class=\"stats-number missed\">{{ getStatsData().missed }}</view>\n\t\t\t\t\t<view class=\"stats-label\">漏检查</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stats-item\" :class=\"{ active: selectedStatus === 'not_cleaned' }\"\n\t\t\t\t\t@click=\"selectStatus('not_cleaned')\">\n\t\t\t\t\t<view class=\"stats-number not-cleaned\">{{ getStatsData().not_cleaned }}</view>\n\t\t\t\t\t<view class=\"stats-label\">未打扫</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stats-item\" :class=\"{ active: selectedStatus === 'completed' }\"\n\t\t\t\t\t@click=\"selectStatus('completed')\">\n\t\t\t\t\t<view class=\"stats-number success\">{{ getStatsData().completed }}</view>\n\t\t\t\t\t<view class=\"stats-label\">已完成</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 检查记录列表 -->\n\t\t<view class=\"card\">\n\t\t\t<view class=\"card-header\">\n\t\t\t\t<view class=\"header-content\">\n\t\t\t\t\t<view class=\"card-title\">责任区检查</view>\n\t\t\t\t\t<view class=\"card-subtitle\">固定和公共责任区检查任务</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 内容区域 -->\n\t\t\t<!-- 分类标签 -->\n\t\t\t<scroll-view class=\"category-scroll\" scroll-x=\"true\" show-scrollbar=\"false\">\n\t\t\t\t<view class=\"category-tabs\">\n\t\t\t\t\t<view v-for=\"category in categoryTabs\" :key=\"category.value\" class=\"category-tab\"\n\t\t\t\t\t\t:class=\"{ active: selectedCategory === category.value }\"\n\t\t\t\t\t\t@click=\"selectCategory(category.value)\">\n\t\t\t\t\t\t<text class=\"tab-text\">{{ category.label }}</text>\n\t\t\t\t\t\t<text class=\"tab-count\">{{ getCategoryCount(category.value) }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\n\t\t\t<!-- 加载状态 -->\n\t\t\t<view v-if=\"loading\" class=\"list-loading\">\n\t\t\t\t<view class=\"loading-spinner\"></view>\n\t\t\t\t<view class=\"loading-text\">加载数据中...</view>\n\t\t\t</view>\n\n\t\t\t<!-- 时间分组的检查记录列表 -->\n\t\t\t<view v-else-if=\"groupedFilteredRecords.length > 0\">\n\t\t\t\t<view v-for=\"group in groupedFilteredRecords\" :key=\"group.weekKey\" class=\"time-group\">\n\t\t\t\t\t<!-- 时间分组标题 -->\n\t\t\t\t\t<view class=\"time-group-header\" @click=\"toggleWeek(group.weekKey)\">\n\t\t\t\t\t\t<view class=\"time-group-title\">\n\t\t\t\t\t\t\t<uni-icons :type=\"group.expanded ? 'down' : 'right'\" size=\"16\" color=\"#007AFF\"></uni-icons>\n\t\t\t\t\t\t\t<text class=\"time-title\">{{ group.title }}</text>\n\t\t\t\t\t\t\t<view class=\"time-count\">{{ group.records.length }}条</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 分组记录列表 -->\n\t\t\t\t\t<view v-if=\"group.expanded\" class=\"time-group-content\">\n\t\t\t\t\t\t<view v-for=\"(record, recordIndex) in group.records\" :key=\"recordIndex\" class=\"list-item\"\n\t\t\t\t\t\t\t@click=\"handleRecordClick(record)\">\n\t\t\t\t\t\t\t<view class=\"list-item-icon\" :class=\"['icon-bg-' + record.status]\">\n\t\t\t\t\t\t\t\t<uni-icons :type=\"record.icon\" size=\"18\"\n\t\t\t\t\t\t\t\t\t:color=\"getIconColor(record.status)\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"list-item-content\">\n\t\t\t\t\t\t\t\t<view class=\"list-item-title\">\n\t\t\t\t\t\t\t\t\t<text>{{ record.areaName }}</text>\n\t\t\t\t\t\t\t\t\t<view class=\"badges-container\">\n\t\t\t\t\t\t\t\t\t\t<!-- 区域类型标签 -->\n\t\t\t\t\t\t\t\t\t\t<view class=\"area-type-badge\" :class=\"['type-' + record.type]\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"badge-text\">{{ getAreaTypeText(record.type) }}</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<!-- 整改任务标签 -->\n\t\t\t\t\t\t\t\t\t\t<view v-if=\"record.isRectificationRecheck\" class=\"rectification-badge\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"badge-text\">整改任务</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"list-item-subtitle\">{{ record.subtitle }}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"list-item-right\">\n\t\t\t\t\t\t\t\t<view class=\"status-badge\" :class=\"['status-' + record.status]\">\n\t\t\t\t\t\t\t\t\t{{ getStatusText(record.status) }}</view>\n\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"14\" color=\"#C7C7CC\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 无数据状态 -->\n\t\t\t<p-empty-state v-else useIcon iconName=\"info\" iconColor=\"#C7C7CC\" size=\"large\" text=\"暂无数据\"></p-empty-state>\n\t\t</view>\n\n\t\t<!-- 底部安全间距 -->\n\t\t<view class=\"bottom-safe-area\"></view>\n\n\t\t<!-- 日历式时间选择弹窗 -->\n\t\t<uni-popup ref=\"timePopup\" type=\"bottom\" border-radius=\"16rpx 16rpx 0 0\">\n\t\t\t<view class=\"date-picker-popup\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<text class=\"popup-title\">筛选时间</text>\n\t\t\t\t\t<view class=\"popup-close\" @click=\"closeDatePicker\">\n\t\t\t\t\t\t<uni-icons type=\"close\" size=\"18\" color=\"#8E8E93\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 快捷选择模式 -->\n\t\t\t\t<view v-if=\"dateFilterMode === 'quick'\" class=\"quick-date-section\">\n\t\t\t\t\t<view class=\"section-title\">快捷选择</view>\n\t\t\t\t\t<view class=\"quick-options\">\n\t\t\t\t\t\t<view v-for=\"option in quickDateOptions\" :key=\"option.value\" class=\"quick-option\"\n\t\t\t\t\t\t\t:class=\"{ active: selectedQuickFilter === option.value }\"\n\t\t\t\t\t\t\t@click=\"selectQuickDateOption(option)\">\n\t\t\t\t\t\t\t<text class=\"quick-text\">{{ option.label }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 日期范围选择模式 -->\n\t\t\t\t<view v-else class=\"range-date-section\">\n\t\t\t\t\t<!-- 简洁的日历选择器 -->\n\t\t\t\t\t<view class=\"calendar-section\">\n\t\t\t\t\t\t<view class=\"calendar-header\">\n\t\t\t\t\t\t\t<text class=\"calendar-tip\">在日历上点击选择开始和结束日期</text>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- uni-calendar 日历组件 -->\n\t\t\t\t\t\t<uni-calendar ref=\"calendar\" :range=\"true\" :date=\"calendarDate\" :start-date=\"calendarStartDate\"\n\t\t\t\t\t\t\t:end-date=\"calendarEndDate\" @change=\"onCalendarChange\"\n\t\t\t\t\t\t\t@monthSwitch=\"onMonthSwitch\"></uni-calendar>\n\n\t\t\t\t\t\t<!-- 已选择的日期范围显示 -->\n\t\t\t\t\t\t<view v-if=\"customDateRange.startDate && customDateRange.endDate\" class=\"selected-range\">\n\t\t\t\t\t\t\t<view class=\"range-item\">\n\t\t\t\t\t\t\t\t<text class=\"range-label\">开始日期</text>\n\t\t\t\t\t\t\t\t<text class=\"range-value\">{{ formatSelectedDate(customDateRange.startDate) }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"range-separator\">→</view>\n\t\t\t\t\t\t\t<view class=\"range-item\">\n\t\t\t\t\t\t\t\t<text class=\"range-label\">结束日期</text>\n\t\t\t\t\t\t\t\t<text class=\"range-value\">{{ formatSelectedDate(customDateRange.endDate) }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t</view>\n</template>\n\n<script>\nimport { callCloudFunction } from '@/utils/auth.js';\n\n// 配置常量\nconst CONFIG = {\n\tAPI: {\n\t\tAREA_PAGE_SIZE: 100,           // 责任区分页大小\n\t\tINSPECTION_PAGE_SIZE: 20,      // 检查记录分页大小\n\t\tRECTIFICATION_PAGE_SIZE: 1,    // 整改记录分页大小\n\t\tDEFAULT_QUERY_DAYS: 30         // 默认查询天数\n\t},\n\tTIME: {\n\t\tNIGHT_HOUR: 20,                // 晚上8点后算即将逾期\n\t\tTOAST_DURATION: 1500,          // Toast显示时长\n\t\tDEBOUNCE_DELAY: 100            // 防抖延迟\n\t}\n};\n\nexport default {\n\tname: 'AreaInspection',\n\tdata() {\n\t\treturn {\n\t\t\t// 日期筛选方式：'quick' 快捷选择, 'range' 日期范围选择\n\t\t\tdateFilterMode: 'quick',\n\t\t\tselectedTimeFilter: 'week',\n\t\t\tselectedCategory: 'all', // 当前选择的分类\n\t\t\tselectedStatus: 'all', // 当前选择的状态筛选\n\n\t\t\t// 自定义日期范围\n\t\t\tcustomDateRange: {\n\t\t\t\tstartDate: '',\n\t\t\t\tendDate: ''\n\t\t\t},\n\n\t\t\t// 日历组件相关数据\n\t\t\tcalendarDate: '',\n\t\t\tcalendarStartDate: '',\n\t\t\tcalendarEndDate: '',\n\n\t\t\t// 防重复查询的定时器\n\t\t\trefreshTimer: null,\n\t\t\tcalendarChangeTimer: null,\n\n\t\t\t// 快捷日期选项 - 优化为更实用的选项\n\t\t\tquickDateOptions: [\n\t\t\t\t{ label: '本周', value: 'week' },\n\t\t\t\t{ label: '上周', value: 'last_week' },\n\t\t\t\t{ label: '本月', value: 'month' },\n\t\t\t\t{ label: '自定义', value: 'custom' }\n\t\t\t],\n\t\t\tselectedQuickFilter: 'week',\n\n\t\t\t// 分类标签\n\t\t\tcategoryTabs: [\n\t\t\t\t{ label: '全部', value: 'all' },\n\t\t\t\t{ label: '固定责任区', value: 'fixed' },\n\t\t\t\t{ label: '公共责任区', value: 'public' }\n\t\t\t],\n\n\t\t\t// 检查记录数据\n\t\t\tinspectionRecords: [],\n\t\t\t// 加载状态\n\t\t\tloading: false,\n\t\t\tloadError: '',\n\t\t\t// 展开的周次\n\t\t\texpandedWeeks: [], // 将动态设置当前周\n\t\t\t// 数据加载标记\n\t\t\tdataLoaded: false,\n\t\t\tneedsRefresh: false, // 标记是否需要刷新数据\n\n\t\t\t// 性能优化缓存\n\t\t\tprocessCache: {\n\t\t\t\ttimeCalculations: null,\n\t\t\t\tstatusMap: null,\n\t\t\t\tweekKeyCache: new Map(),\n\t\t\t\tareaTypeMap: null,\n\t\t\t\tformattedDatesCache: new Map()\n\t\t\t},\n\t\t\t// 责任区数据缓存\n\t\t\tareasCache: null\n\t\t}\n\t},\n\tcomputed: {\n\t\t// 根据时间和分类筛选记录\n\t\tfilteredRecords() {\n\t\t\tlet records = this.inspectionRecords;\n\n\t\t\t// 获取当前时间信息用于筛选\n\t\t\tconst now = new Date();\n\t\t\tconst currentYear = now.getFullYear();\n\t\t\tconst currentMonth = now.getMonth() + 1;\n\t\t\tconst currentQuarter = Math.ceil(currentMonth / 3);\n\t\t\tconst currentWeekKey = this.getCurrentWeekKeyOptimized(now);\n\n\t\t\t// 基于日期范围的直接筛选\n\t\t\tconst dateRange = this.getCurrentDateRange();\n\t\t\tif (dateRange) {\n\t\t\t\trecords = records.filter(r => {\n\t\t\t\t\t// 直接基于记录的日期信息进行筛选，而不是周次计算\n\t\t\t\t\treturn this.isRecordInDateRange(r, dateRange);\n\t\t\t\t});\n\t\t\t}\n\n\t\t\t// 按分类筛选\n\t\t\tif (this.selectedCategory !== 'all') {\n\t\t\t\trecords = records.filter(r => r.type === this.selectedCategory);\n\t\t\t}\n\n\t\t\t// 按状态筛选\n\t\t\tif (this.selectedStatus !== 'all') {\n\t\t\t\trecords = records.filter(r => r.status === this.selectedStatus);\n\t\t\t}\n\n\t\t\treturn records;\n\t\t},\n\n\t\t// 时间分组的记录\n\t\tgroupedFilteredRecords() {\n\t\t\tconst records = this.filteredRecords;\n\t\t\tconst grouped = {};\n\n\t\t\trecords.forEach(record => {\n\t\t\t\tconst weekKey = record.week;\n\t\t\t\tif (!grouped[weekKey]) {\n\t\t\t\t\tgrouped[weekKey] = {\n\t\t\t\t\t\tweekKey,\n\t\t\t\t\t\ttitle: this.getWeekTitle(weekKey),\n\t\t\t\t\t\trecords: [],\n\t\t\t\t\t\texpanded: this.expandedWeeks.includes(weekKey)\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tgrouped[weekKey].records.push(record);\n\t\t\t});\n\n\t\t\t// 对每个分组内的记录按状态优先级排序\n\t\t\tObject.values(grouped).forEach(group => {\n\t\t\t\tgroup.records.sort((a, b) => this.getStatusPriority(a.status) - this.getStatusPriority(b.status));\n\t\t\t});\n\n\t\t\t// 按周次降序排序\n\t\t\tconst sortedGroups = Object.values(grouped).sort((a, b) => {\n\t\t\t\tconst weekA = parseInt(a.weekKey.split('-W')[1]);\n\t\t\t\tconst weekB = parseInt(b.weekKey.split('-W')[1]);\n\t\t\t\treturn weekB - weekA;\n\t\t\t});\n\n\t\t\t// 同步展开状态：完全基于用户手动操作的状态\n\t\t\tsortedGroups.forEach(group => {\n\t\t\t\tgroup.expanded = this.expandedWeeks.includes(group.weekKey);\n\t\t\t});\n\n\t\t\treturn sortedGroups;\n\t\t},\n\t},\n\tcreated() {\n\t\tthis.initProcessCache();\n\t},\n\tonLoad() {\n\t\tthis.loadPageDataOptimized();\n\n\t\t// 监听清理记录更新事件\n\t\tuni.$on('cleaningRecordUpdated', this.handleCleaningRecordUpdated);\n\t\t// 监听检查记录更新事件\n\t\tuni.$on('inspectionRecordUpdated', this.handleInspectionRecordUpdated);\n\t\t// 监听整改记录更新事件\n\t\tuni.$on('rectificationRecordUpdated', this.handleRectificationRecordUpdated);\n\t},\n\tonUnload() {\n\t\t// 移除事件监听\n\t\tuni.$off('cleaningRecordUpdated', this.handleCleaningRecordUpdated);\n\t\tuni.$off('inspectionRecordUpdated', this.handleInspectionRecordUpdated);\n\t\tuni.$off('rectificationRecordUpdated', this.handleRectificationRecordUpdated);\n\n\t\t// 清理定时器\n\t\tif (this.refreshTimer) {\n\t\t\tclearTimeout(this.refreshTimer);\n\t\t}\n\t\tif (this.calendarChangeTimer) {\n\t\t\tclearTimeout(this.calendarChangeTimer);\n\t\t}\n\t},\n\tonShow() {\n\t\t// 页面重新显示时，只在确实需要刷新数据的情况下才刷新\n\t\t// 使用静默刷新，避免显示加载动画\n\t\tif (this.dataLoaded && !this.loading && this.needsRefresh) {\n\t\t\tthis.silentRefreshData();\n\t\t\tthis.needsRefresh = false; // 重置刷新标记\n\t\t}\n\t},\n\tmethods: {\n\t\t// 初始化处理缓存\n\t\tinitProcessCache() {\n\t\t\tif (!this.processCache.areaTypeMap) {\n\t\t\t\tthis.processCache.areaTypeMap = {\n\t\t\t\t\t'fixed': '固定',\n\t\t\t\t\t'public': '公共'\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (!this.processCache.statusMap) {\n\t\t\t\tthis.processCache.statusMap = {\n\t\t\t\t\t'not_cleaned': '未打扫',\n\t\t\t\t\t'pending': '待检查',\n\t\t\t\t\t'pending_rectification': '待整改',\n\t\t\t\t\t'pending_review': '待复查',\n\t\t\t\t\t'completed': '已完成',\n\t\t\t\t\t'rejected': '整改不达标',\n\t\t\t\t\t'verified': '整改合格',\n\t\t\t\t\t'missed': '漏检查'\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tthis.initTimeCalculations();\n\t\t},\n\n\t\t// 初始化时间计算缓存\n\t\tinitTimeCalculations() {\n\t\t\tif (!this.processCache.timeCalculations) {\n\t\t\t\tconst now = new Date();\n\t\t\t\tthis.processCache.timeCalculations = {\n\t\t\t\t\tnow,\n\t\t\t\t\tweekStart: this.getWeekStartOptimized(now),\n\t\t\t\t\tweekEnd: this.getWeekEndOptimized(now),\n\t\t\t\t\tcurrentWeekKey: this.getCurrentWeekKeyOptimized(now)\n\t\t\t\t};\n\t\t\t}\n\t\t},\n\n\t\t// 获取区域类型文本（使用缓存）\n\t\tgetAreaTypeText(type) {\n\t\t\treturn this.processCache.areaTypeMap[type] || '固定';\n\t\t},\n\n\t\t// 获取状态优先级（数字越小优先级越高）\n\t\tgetStatusPriority(status) {\n\t\t\tconst priorityMap = {\n\t\t\t\t'pending_review': 1,        // 待复查 - 最高优先级\n\t\t\t\t'pending': 2,               // 待检查 - 高优先级\n\t\t\t\t'pending_rectification': 3, // 待整改 - 中高优先级\n\t\t\t\t'missed': 4,                // 漏检查 - 中优先级\n\t\t\t\t'not_cleaned': 5,           // 未打扫 - 低优先级\n\t\t\t\t'completed': 6              // 已完成 - 最低优先级\n\t\t\t};\n\t\t\treturn priorityMap[status] || 999; // 未知状态放在最后\n\t\t},\n\n\t\t// 获取当前周的起始和结束日期\n\t\tgetCurrentWeekRange(inputDate = new Date()) {\n\t\t\tconst date = new Date(inputDate);\n\t\t\tconst day = date.getDay();\n\t\t\tconst diff = date.getDate() - day + (day === 0 ? -6 : 1);\n\n\t\t\tconst monday = new Date(date);\n\t\t\tmonday.setDate(diff);\n\n\t\t\tconst sunday = new Date(date);\n\t\t\tsunday.setDate(diff + 6);\n\n\t\t\tconst startStr = `${monday.getMonth() + 1}月${monday.getDate()}日`;\n\t\t\tconst endStr = `${sunday.getMonth() + 1}月${sunday.getDate()}日`;\n\n\t\t\treturn {\n\t\t\t\tstart: startStr,\n\t\t\t\tend: endStr,\n\t\t\t\tmonday: monday,\n\t\t\t\tsunday: sunday\n\t\t\t};\n\t\t},\n\n\t\t// 优化的加载页面数据\n\t\tasync loadPageDataOptimized() {\n\t\t\tthis.loading = true;\n\t\t\tthis.loadError = '';\n\n\t\t\ttry {\n\t\t\t\t// 重新初始化时间计算缓存\n\t\t\t\tthis.processCache.timeCalculations = null;\n\t\t\t\tthis.initTimeCalculations();\n\n\t\t\t\t// 加载检查记录\n\t\t\t\tawait this.loadInspectionRecordsOptimized();\n\n\t\t\t\t// 设置默认展开的周次\n\t\t\t\tif (this.expandedWeeks.length === 0) {\n\t\t\t\t\tconst currentWeek = this.processCache.timeCalculations.currentWeekKey;\n\t\t\t\t\tthis.expandedWeeks = [currentWeek];\n\n\t\t\t\t\t// 如果有待复查任务，确保包含待复查任务的周次也展开\n\t\t\t\t\tconst pendingReviewWeeks = this.inspectionRecords\n\t\t\t\t\t\t.filter(record => record.status === 'pending_review')\n\t\t\t\t\t\t.map(record => record.week)\n\t\t\t\t\t\t.filter((week, index, arr) => arr.indexOf(week) === index); // 去重\n\n\t\t\t\t\tpendingReviewWeeks.forEach(week => {\n\t\t\t\t\t\tif (!this.expandedWeeks.includes(week)) {\n\t\t\t\t\t\t\tthis.expandedWeeks.push(week);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t} catch (error) {\n\t\t\t\tthis.loadError = '加载数据失败，请稍后重试';\n\t\t\t\tthis.handleLoadError(error);\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t\tthis.dataLoaded = true;\n\t\t\t}\n\t\t},\n\n\t\t// 统一的错误处理\n\t\thandleLoadError(error) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '加载失败',\n\t\t\t\tcontent: this.loadError,\n\t\t\t\tshowCancel: true,\n\t\t\t\tcancelText: '返回',\n\t\t\t\tconfirmText: '重试',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tthis.loadPageDataOptimized();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 保留原方法以防其他地方调用\n\t\tasync loadPageData() {\n\t\t\treturn this.loadPageDataOptimized();\n\t\t},\n\n\t\t// 优化的加载检查记录\n\t\tasync loadInspectionRecordsOptimized() {\n\t\t\ttry {\n\t\t\t\t// 获取所有责任区\n\t\t\t\tconst areasResult = await callCloudFunction('hygiene-area-management', {\n\t\t\t\t\taction: 'getAreaList',\n\t\t\t\t\tdata: { pageSize: CONFIG.API.AREA_PAGE_SIZE }\n\t\t\t\t});\n\n\t\t\t\tif (areasResult?.success) {\n\t\t\t\t\tconst areas = this.extractAreas(areasResult.data);\n\n\t\t\t\t\t// 缓存责任区数据，用于后续查询排班日等信息\n\t\t\t\t\tthis.areasCache = areas;\n\n\t\t\t\t\t// 批量获取清理记录和检查记录\n\t\t\t\t\tconst [cleaningRecordsByWeekArea, inspectionRecordsByWeekArea] = await this.batchLoadRecords(areas);\n\n\t\t\t\t\t// 根据日期筛选模式动态生成需要的周次\n\t\t\t\t\tconst weeksToShow = this.generateWeeksForCurrentFilter();\n\n\t\t\t\t\t// 为每个责任区的每个周次生成检查状态\n\t\t\t\t\tconst inspectionTasks = [];\n\n\t\t\t\t\tareas.forEach(area => {\n\t\t\t\t\t\tweeksToShow.forEach(weekKey => {\n\t\t\t\t\t\t\tconst areaId = area._id || area.id;\n\t\t\t\t\t\t\tconst mapKey = `${weekKey}-${areaId}`;\n\n\t\t\t\t\t\t\tconst cleaningRecord = cleaningRecordsByWeekArea.get(mapKey);\n\t\t\t\t\t\t\tconst inspectionRecord = inspectionRecordsByWeekArea.get(mapKey);\n\n\t\t\t\t\t\t\tconst task = this.processAreaInspectionForWeek(\n\t\t\t\t\t\t\t\tarea,\n\t\t\t\t\t\t\t\tweekKey,\n\t\t\t\t\t\t\t\tcleaningRecord,\n\t\t\t\t\t\t\t\tinspectionRecord\n\t\t\t\t\t\t\t);\n\n\t\t\t\t\t\t\tif (task) {\n\t\t\t\t\t\t\t\tinspectionTasks.push(task);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\n\t\t\t\t\tthis.inspectionRecords = inspectionTasks;\n\t\t\t\t} else {\n\t\t\t\t\tthrow new Error(areasResult?.message || '获取责任区失败');\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tthis.inspectionRecords = [];\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t},\n\n\t\t// 提取责任区数据\n\t\textractAreas(data) {\n\t\t\tif (!data) return [];\n\t\t\tif (Array.isArray(data)) return data;\n\t\t\tif (data.list && Array.isArray(data.list)) return data.list;\n\t\t\treturn [];\n\t\t},\n\n\t\t// 批量加载清理和检查记录\n\t\tasync batchLoadRecords(areas) {\n\t\t\tconst areaIds = areas.map(area => area._id || area.id);\n\n\t\t\t// 并行获取所有清理记录和检查记录\n\t\t\tconst [cleaningResults, inspectionResults] = await Promise.allSettled([\n\t\t\t\tthis.batchGetCleaningRecords(areaIds),\n\t\t\t\tthis.batchGetInspectionRecords(areaIds)\n\t\t\t]);\n\n\t\t\t// 按周次和责任区组织数据\n\t\t\tconst cleaningRecordsByWeekArea = new Map(); // key: \"weekKey-areaId\"\n\t\t\tconst inspectionRecordsByWeekArea = new Map(); // key: \"weekKey-areaId\"\n\n\t\t\t// 处理清理记录结果\n\t\t\tif (cleaningResults.status === 'fulfilled' && cleaningResults.value) {\n\t\t\t\tcleaningResults.value.forEach(record => {\n\t\t\t\t\tconst areaId = record.area_id;\n\t\t\t\t\tconst recordDate = new Date(record.cleaning_date);\n\t\t\t\t\tconst weekKey = this.getCurrentWeekKeyOptimized(recordDate);\n\t\t\t\t\tconst mapKey = `${weekKey}-${areaId}`;\n\n\t\t\t\t\t// 保留每周每个区域的最新记录\n\t\t\t\t\tif (!cleaningRecordsByWeekArea.has(mapKey) ||\n\t\t\t\t\t\tnew Date(record.cleaning_date) > new Date(cleaningRecordsByWeekArea.get(mapKey).cleaning_date)) {\n\t\t\t\t\t\tcleaningRecordsByWeekArea.set(mapKey, record);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\n\t\t\t// 处理检查记录结果\n\t\t\tif (inspectionResults.status === 'fulfilled' && inspectionResults.value) {\n\t\t\t\tinspectionResults.value.forEach(record => {\n\t\t\t\t\tconst areaId = record.area_id;\n\t\t\t\t\tconst recordDate = new Date(record.inspection_date || record.created_at);\n\t\t\t\t\tconst weekKey = this.getCurrentWeekKeyOptimized(recordDate);\n\t\t\t\t\tconst mapKey = `${weekKey}-${areaId}`;\n\n\t\t\t\t\t// 保留每周每个区域的最新记录\n\t\t\t\t\tif (!inspectionRecordsByWeekArea.has(mapKey) ||\n\t\t\t\t\t\tnew Date(record.inspection_date || record.created_at) > new Date(inspectionRecordsByWeekArea.get(mapKey).inspection_date || inspectionRecordsByWeekArea.get(mapKey).created_at)) {\n\t\t\t\t\t\tinspectionRecordsByWeekArea.set(mapKey, record);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t\treturn [cleaningRecordsByWeekArea, inspectionRecordsByWeekArea];\n\t\t},\n\n\t\t// 批量获取清理记录\n\t\tasync batchGetCleaningRecords(areaIds) {\n\t\t\ttry {\n\t\t\t\t// 智能日期范围查询：基于用户选择的日期范围进行精确查询\n\t\t\t\tconst dateRange = this.getCurrentDateRange();\n\n\t\t\t\tlet startDate, endDate;\n\t\t\t\tif (dateRange) {\n\t\t\t\t\t// 使用用户选择的日期范围\n\t\t\t\t\tstartDate = new Date(dateRange.start);\n\t\t\t\t\tendDate = new Date(dateRange.end);\n\n\t\t\t\t\t// 适当扩展边界（前后各2天）确保不遗漏边界数据  \n\t\t\t\t\tstartDate.setDate(startDate.getDate() - 2);\n\t\t\t\t\tendDate.setDate(endDate.getDate() + 2);\n\t\t\t\t} else {\n\t\t\t\t\t// 无选择时默认查询近期天数\n\t\t\t\t\tendDate = new Date();\n\t\t\t\t\tstartDate = new Date();\n\t\t\t\t\tstartDate.setDate(startDate.getDate() - CONFIG.API.DEFAULT_QUERY_DAYS);\n\t\t\t\t}\n\n\t\t\t\tstartDate.setHours(0, 0, 0, 0);\n\t\t\t\tendDate.setHours(23, 59, 59, 999);\n\n\t\t\t\tconst result = await callCloudFunction('hygiene-cleaning', {\n\t\t\t\t\taction: 'getBatchCleaningRecords',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\tarea_ids: areaIds,\n\t\t\t\t\t\tstart_date: startDate.toISOString(),\n\t\t\t\t\t\tend_date: endDate.toISOString(),\n\t\t\t\t\t\tlatest_only: false\n\t\t\t\t\t}\n\t\t\t\t});\n\n\n\t\t\t\treturn result?.success ? result.data : [];\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取清理记录失败:', error);\n\t\t\t\treturn [];\n\t\t\t}\n\t\t},\n\n\t\t// 批量获取检查记录\n\t\tasync batchGetInspectionRecords(areaIds) {\n\t\t\ttry {\n\t\t\t\tconst promises = areaIds.map(areaId =>\n\t\t\t\t\tcallCloudFunction('hygiene-inspection', {\n\t\t\t\t\t\taction: 'getInspectionRecords',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tarea_id: areaId,\n\t\t\t\t\t\t\tpageSize: CONFIG.API.INSPECTION_PAGE_SIZE\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t);\n\n\t\t\t\tconst results = await Promise.allSettled(promises);\n\t\t\t\tconst records = [];\n\n\t\t\t\tresults.forEach((result, index) => {\n\t\t\t\t\tif (result.status === 'fulfilled' && result.value?.success && result.value.data?.list?.length > 0) {\n\t\t\t\t\t\t// 获取所有检查记录，而不只是最新的一条\n\t\t\t\t\t\tresult.value.data.list.forEach(record => {\n\t\t\t\t\t\t\trecord.area_id = areaIds[index];\n\t\t\t\t\t\t\trecords.push(record);\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\treturn records;\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取检查记录失败:', error);\n\t\t\t\treturn [];\n\t\t\t}\n\t\t},\n\n\t\t// 处理单个责任区的检查状态\n\t\tprocessAreaInspection(area, lastCleaningRecord, lastInspection) {\n\t\t\tconst areaId = area._id || area.id;\n\t\t\tconst { weekStart, weekEnd } = this.processCache.timeCalculations;\n\n\t\t\tlet status = 'not_cleaned';\n\t\t\tlet subtitle = '本周未开始打扫';\n\n\t\t\tif (lastCleaningRecord) {\n\t\t\t\tconst cleaningDate = new Date(lastCleaningRecord.cleaning_date);\n\n\t\t\t\tif (cleaningDate >= weekStart && cleaningDate <= weekEnd) {\n\t\t\t\t\tconst missedStatus = this.calculateMissedStatusOptimized(area, lastCleaningRecord, lastInspection);\n\t\t\t\t\tif (missedStatus === 'missed') {\n\t\t\t\t\t\tstatus = 'missed';\n\t\t\t\t\t\tsubtitle = `检查员漏检查 · ${this.formatDateTimeOptimized(cleaningDate)}`;\n\t\t\t\t\t} else if (lastInspection) {\n\t\t\t\t\t\tconst inspectionDate = new Date(lastInspection.inspection_date || lastInspection.created_at);\n\n\t\t\t\t\t\tif (inspectionDate >= cleaningDate) {\n\t\t\t\t\t\t\t({ status, subtitle } = this.processInspectionStatus(lastInspection, inspectionDate, area, lastCleaningRecord));\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tstatus = 'pending';\n\t\t\t\t\t\t\tsubtitle = `员工已提交，待检查 · ${this.formatDateTimeOptimized(cleaningDate)}`;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tstatus = 'pending';\n\t\t\t\t\t\tsubtitle = `员工已提交，待检查 · ${this.formatDateTimeOptimized(cleaningDate)}`;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\tid: lastInspection ? (lastInspection._id || lastInspection.id) : areaId,\n\t\t\t\tareaName: area.name || '未知责任区',\n\t\t\t\tsubtitle,\n\t\t\t\tstatus,\n\t\t\t\ticon: this.getStatusIcon(status),\n\t\t\t\ttype: area.type || 'fixed',\n\t\t\t\tweek: this.processCache.timeCalculations.currentWeekKey,\n\t\t\t\tinspectionDate: lastInspection ? (lastInspection.inspection_date || lastInspection.created_at) : null,\n\t\t\t\tisRectificationRecheck: false,\n\t\t\t\tareaId,\n\t\t\t\tinspectorName: '检查员',\n\t\t\t\tinspectionRecordId: lastInspection ? (lastInspection._id || lastInspection.id) : null,\n\t\t\t\tisInspectionRecord: !!lastInspection,\n\t\t\t\t// 添加排班日信息，用于判断公共责任区的时效\n\t\t\t\tscheduledDay: area.scheduled_day || null\n\t\t\t};\n\t\t},\n\n\t\t// 处理检查状态\n\t\tprocessInspectionStatus(lastInspection, inspectionDate, area, lastCleaningRecord) {\n\t\t\tlet status, subtitle;\n\n\t\t\tif (lastInspection.status === 'pending_rectification') {\n\t\t\t\tstatus = 'pending_rectification';\n\t\t\t\tsubtitle = `检查未通过，待整改 · ${this.formatDateTimeOptimized(inspectionDate)}`;\n\t\t\t} else if (lastInspection.status === 'rectification_completed') {\n\t\t\t\tconst rectificationMissed = this.calculateMissedStatusOptimized(area, lastCleaningRecord, lastInspection);\n\t\t\t\tif (rectificationMissed === 'missed') {\n\t\t\t\t\tstatus = 'missed';\n\t\t\t\t\tsubtitle = `整改复查已超时 · ${this.formatDateTimeOptimized(inspectionDate)}`;\n\t\t\t\t} else {\n\t\t\t\t\tstatus = 'pending_review';\n\t\t\t\t\tsubtitle = `整改已提交，待复查 · ${this.formatDateTimeOptimized(inspectionDate)}`;\n\t\t\t\t}\n\t\t\t} else if (lastInspection.status === 'verified') {\n\t\t\t\tstatus = 'completed';\n\t\t\t\tsubtitle = `整改已确认，检查完成 · ${this.formatDateTimeOptimized(inspectionDate)}`;\n\t\t\t} else if (lastInspection.status === 'completed') {\n\t\t\t\tstatus = 'completed';\n\t\t\t\tsubtitle = `已完成检查 · ${this.formatDateTimeOptimized(inspectionDate)}`;\n\t\t\t} else {\n\t\t\t\tstatus = 'completed';\n\t\t\t\tsubtitle = `已完成检查 · ${this.formatDateTimeOptimized(inspectionDate)}`;\n\t\t\t}\n\n\t\t\treturn { status, subtitle };\n\t\t},\n\n\t\t// 保留原方法以防其他地方调用\n\t\tasync loadInspectionRecords() {\n\t\t\treturn this.loadInspectionRecordsOptimized();\n\t\t},\n\n\t\t// 优化的获取本周开始时间\n\t\tgetWeekStartOptimized(date = new Date()) {\n\t\t\tconst d = new Date(date);\n\t\t\tconst day = d.getDay();\n\t\t\tconst diff = d.getDate() - day + (day === 0 ? -6 : 1);\n\t\t\tconst monday = new Date(d);\n\t\t\tmonday.setDate(diff);\n\t\t\tmonday.setHours(0, 0, 0, 0);\n\t\t\treturn monday;\n\t\t},\n\n\t\t// 优化的获取本周结束时间\n\t\tgetWeekEndOptimized(date = new Date()) {\n\t\t\tconst d = new Date(date);\n\t\t\tconst day = d.getDay();\n\t\t\tconst diff = d.getDate() - day + (day === 0 ? 0 : 7);\n\t\t\tconst sunday = new Date(d);\n\t\t\tsunday.setDate(diff);\n\t\t\tsunday.setHours(23, 59, 59, 999);\n\t\t\treturn sunday;\n\t\t},\n\n\t\t// 保留原方法以防其他地方调用\n\t\tgetWeekStart(date = new Date()) {\n\t\t\treturn this.getWeekStartOptimized(date);\n\t\t},\n\n\t\tgetWeekEnd(date = new Date()) {\n\t\t\treturn this.getWeekEndOptimized(date);\n\t\t},\n\n\t\t// 优化的计算漏检状态\n\t\tcalculateMissedStatusOptimized(area, lastCleaningRecord, lastInspectionRecord) {\n\t\t\tconst { now, weekStart, weekEnd } = this.processCache.timeCalculations;\n\n\t\t\t// 1. 员工已提交清理，但检查员超时未检查\n\t\t\tif (lastCleaningRecord) {\n\t\t\t\tconst cleaningDate = new Date(lastCleaningRecord.cleaning_date);\n\n\t\t\t\t// 只判断本周期内的清理记录\n\t\t\t\tif (cleaningDate >= weekStart && cleaningDate <= weekEnd) {\n\t\t\t\t\t// 检查是否有对应的检查记录\n\t\t\t\t\tlet hasValidInspection = false;\n\t\t\t\t\tif (lastInspectionRecord) {\n\t\t\t\t\t\tconst inspectionDate = new Date(lastInspectionRecord.inspection_date || lastInspectionRecord.created_at);\n\t\t\t\t\t\t// 检查记录必须在清理记录之后\n\t\t\t\t\t\thasValidInspection = inspectionDate >= cleaningDate;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 如果没有有效检查记录，判断是否漏检\n\t\t\t\t\tif (!hasValidInspection) {\n\t\t\t\t\t\tif (area.type === 'fixed') {\n\t\t\t\t\t\t\t// 固定责任区：过了周日23:59:59（即到了下周一）才算漏检\n\t\t\t\t\t\t\tconst nextWeekStart = new Date(weekEnd);\n\t\t\t\t\t\t\tnextWeekStart.setDate(nextWeekStart.getDate() + 1); // 下周一00:00:00\n\n\t\t\t\t\t\t\tif (now >= nextWeekStart) {\n\t\t\t\t\t\t\t\treturn 'missed';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (area.type === 'public') {\n\t\t\t\t\t\t\t// 公共责任区：过了排班日当天23:59:59才算漏检\n\t\t\t\t\t\t\tconst scheduledEndTime = this.getScheduledDayEndTime(now, area.scheduled_day);\n\n\t\t\t\t\t\t\t// 如果当前时间已经超过排班日的23:59:59，算漏检\n\t\t\t\t\t\t\tif (now > scheduledEndTime) {\n\t\t\t\t\t\t\t\treturn 'missed';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 2. 员工已提交整改，但检查员超时未复查\n\t\t\tif (lastInspectionRecord && lastInspectionRecord.status === 'rectification_completed') {\n\t\t\t\t// 查找整改提交时间\n\t\t\t\tconst submitTime = lastInspectionRecord.rectification_submit_time || lastInspectionRecord.updated_at;\n\t\t\t\tif (submitTime) {\n\t\t\t\t\tconst submitDate = new Date(submitTime);\n\n\t\t\t\t\t// 整改复查也应该遵循周期重置逻辑\n\t\t\t\t\t// 如果整改提交在本周内，检查员应该在本周内复查完成\n\t\t\t\t\tif (submitDate >= weekStart && submitDate <= weekEnd) {\n\t\t\t\t\t\t// 如果已经到了下周，且整改是在上周提交的，算漏检\n\t\t\t\t\t\tconst nextWeekStart = new Date(weekEnd);\n\t\t\t\t\t\tnextWeekStart.setDate(nextWeekStart.getDate() + 1); // 下周一00:00:00\n\n\t\t\t\t\t\tif (now >= nextWeekStart) {\n\t\t\t\t\t\t\treturn 'missed';\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn null; // 不是漏检状态\n\t\t},\n\n\t\t// 保留原方法以防其他地方调用\n\t\tcalculateMissedStatus(area, lastCleaningRecord, lastInspectionRecord) {\n\t\t\treturn this.calculateMissedStatusOptimized(area, lastCleaningRecord, lastInspectionRecord);\n\t\t},\n\n\t\t// 映射检查状态\n\t\tmapInspectionStatus(record) {\n\t\t\t// 根据记录的状态和相关信息确定显示状态\n\t\t\tif (record.status === 'completed') {\n\t\t\t\treturn 'completed';\n\t\t\t} else if (record.status === 'pending') {\n\t\t\t\treturn 'pending';\n\t\t\t} else if (record.cleaning_status === 'not_cleaned') {\n\t\t\t\treturn 'not_cleaned';\n\t\t\t} else if (record.rectification_required) {\n\t\t\t\treturn 'pending_rectification';\n\t\t\t} else if (record.status === 'missed' || record.is_overdue) {\n\t\t\t\treturn 'missed';\n\t\t\t}\n\t\t\treturn 'pending';\n\t\t},\n\n\t\t// 生成记录副标题\n\t\tgenerateRecordSubtitle(record) {\n\t\t\tconst status = this.mapInspectionStatus(record);\n\t\t\tconst inspectorName = record.inspector_name || record.created_by_name || '未知检查员';\n\n\t\t\tconst statusTexts = {\n\t\t\t\t'completed': '已完成检查',\n\t\t\t\t'pending': record.is_rectification_recheck ? '整改后待复检' : '员工已提交，待检查',\n\t\t\t\t'not_cleaned': '本周未开始打扫',\n\t\t\t\t'pending_rectification': '检查未通过，待整改',\n\t\t\t\t'missed': '该责任区漏检查'\n\t\t\t};\n\n\t\t\tconst dateStr = record.inspection_date ?\n\t\t\t\t` · ${this.formatDateTime(record.inspection_date)}` : '';\n\n\t\t\treturn `检查员：${inspectorName} · ${statusTexts[status]}${dateStr}`;\n\t\t},\n\n\t\t// 获取周次键值\n\t\tgetWeekKey(dateString) {\n\t\t\tif (!dateString) return this.getCurrentWeekKey();\n\t\t\tconst date = new Date(dateString);\n\t\t\tconst year = date.getFullYear();\n\t\t\tconst weekNum = this.getWeekNumber(date);\n\t\t\treturn `${year}-W${weekNum.toString().padStart(2, '0')}`;\n\t\t},\n\n\t\t// 优化的获取当前周键值\n\t\tgetCurrentWeekKeyOptimized(date = new Date()) {\n\t\t\tconst cacheKey = date.toDateString();\n\t\t\tif (this.processCache.weekKeyCache.has(cacheKey)) {\n\t\t\t\treturn this.processCache.weekKeyCache.get(cacheKey);\n\t\t\t}\n\n\t\t\tconst year = date.getFullYear();\n\t\t\tconst weekNum = this.getWeekNumber(date);\n\t\t\tconst weekKey = `${year}-W${weekNum.toString().padStart(2, '0')}`;\n\n\t\t\tthis.processCache.weekKeyCache.set(cacheKey, weekKey);\n\t\t\treturn weekKey;\n\t\t},\n\n\t\t// 保留原方法以防其他地方调用\n\t\tgetCurrentWeekKey() {\n\t\t\treturn this.getCurrentWeekKeyOptimized();\n\t\t},\n\n\t\t// 获取日期对应的周数\n\t\tgetWeekNumber(date) {\n\t\t\tconst onejan = new Date(date.getFullYear(), 0, 1);\n\t\t\tconst millisecsInDay = 86400000;\n\t\t\treturn Math.ceil((((date.getTime() - onejan.getTime()) / millisecsInDay) + onejan.getDay() + 1) / 7);\n\t\t},\n\n\t\t// 获取当前月的所有周次\n\t\tgetMonthWeeks(date = new Date()) {\n\t\t\tconst year = date.getFullYear();\n\t\t\tconst month = date.getMonth();\n\n\t\t\t// 获取当月第一天和最后一天\n\t\t\tconst firstDay = new Date(year, month, 1);\n\t\t\tconst lastDay = new Date(year, month + 1, 0);\n\n\t\t\tconst weeks = [];\n\t\t\tfor (let d = new Date(firstDay); d <= lastDay; d.setDate(d.getDate() + 7)) {\n\t\t\t\tconst weekKey = this.getCurrentWeekKeyOptimized(d);\n\t\t\t\tif (!weeks.includes(weekKey)) {\n\t\t\t\t\tweeks.push(weekKey);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn weeks;\n\t\t},\n\n\t\t// 获取当前季度的所有周次\n\t\tgetQuarterWeeks(date = new Date()) {\n\t\t\tconst year = date.getFullYear();\n\t\t\tconst month = date.getMonth();\n\t\t\tconst quarter = Math.ceil((month + 1) / 3);\n\n\t\t\t// 获取季度的第一个月和最后一个月\n\t\t\tconst firstMonthOfQuarter = (quarter - 1) * 3;\n\t\t\tconst lastMonthOfQuarter = quarter * 3 - 1;\n\n\t\t\t// 获取季度第一天和最后一天\n\t\t\tconst firstDay = new Date(year, firstMonthOfQuarter, 1);\n\t\t\tconst lastDay = new Date(year, lastMonthOfQuarter + 1, 0);\n\n\t\t\tconst weeks = [];\n\t\t\tfor (let d = new Date(firstDay); d <= lastDay; d.setDate(d.getDate() + 7)) {\n\t\t\t\tconst weekKey = this.getCurrentWeekKeyOptimized(d);\n\t\t\t\tif (!weeks.includes(weekKey)) {\n\t\t\t\t\tweeks.push(weekKey);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn weeks;\n\t\t},\n\n\t\t// 生成需要显示的周次列表\n\t\tgenerateWeeksToShow(weeksCount = 8) {\n\t\t\tconst weeks = [];\n\t\t\tconst now = new Date();\n\n\t\t\t// 生成从当前周往前推的周次\n\t\t\tfor (let i = 0; i < weeksCount; i++) {\n\t\t\t\tconst weekDate = new Date(now);\n\t\t\t\tweekDate.setDate(weekDate.getDate() - (i * 7));\n\t\t\t\tconst weekKey = this.getCurrentWeekKeyOptimized(weekDate);\n\t\t\t\tif (!weeks.includes(weekKey)) {\n\t\t\t\t\tweeks.push(weekKey);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 按周次倒序排列（最新的在前）\n\t\t\treturn weeks.sort((a, b) => {\n\t\t\t\tconst weekA = parseInt(a.split('-W')[1]);\n\t\t\t\tconst weekB = parseInt(b.split('-W')[1]);\n\t\t\t\treturn weekB - weekA;\n\t\t\t});\n\t\t},\n\n\t\t// 根据当前筛选条件生成需要的周次\n\t\tgenerateWeeksForCurrentFilter() {\n\t\t\tconst dateRange = this.getCurrentDateRange();\n\n\t\t\tif (!dateRange) {\n\t\t\t\t// 没有日期范围时，默认生成当前周\n\t\t\t\treturn [this.getCurrentWeekKeyOptimized()];\n\t\t\t}\n\n\t\t\t// 基于日期范围计算所有涉及的周次\n\t\t\tconst weeks = [];\n\t\t\tconst start = new Date(dateRange.start);\n\t\t\tconst end = new Date(dateRange.end);\n\n\t\t\t// 从开始日期所在的周开始\n\t\t\tlet currentDate = new Date(start);\n\t\t\t// 调整到该周的周一\n\t\t\tconst dayOfWeek = currentDate.getDay();\n\t\t\tconst daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;\n\t\t\tcurrentDate.setDate(currentDate.getDate() - daysToMonday);\n\n\t\t\twhile (currentDate <= end) {\n\t\t\t\tconst weekKey = this.getCurrentWeekKeyOptimized(currentDate);\n\t\t\t\tif (!weeks.includes(weekKey)) {\n\t\t\t\t\tweeks.push(weekKey);\n\t\t\t\t}\n\t\t\t\t// 移动到下一周\n\t\t\t\tcurrentDate.setDate(currentDate.getDate() + 7);\n\t\t\t}\n\t\t\treturn weeks.reverse(); // 返回降序（最新的在前）\n\t\t},\n\n\t\t// 为特定周次处理责任区检查状态\n\t\tprocessAreaInspectionForWeek(area, weekKey, cleaningRecord, inspectionRecord) {\n\t\t\tconst areaId = area._id || area.id;\n\n\t\t\t// 计算该周的起始和结束时间\n\t\t\tconst weekTimes = this.getWeekTimesFromKey(weekKey);\n\t\t\tif (!weekTimes) return null;\n\n\t\t\t// 判断是否为当前周\n\t\t\tconst currentWeekKey = this.getCurrentWeekKeyOptimized();\n\t\t\tconst isCurrentWeek = weekKey === currentWeekKey;\n\n\t\t\tlet status = 'not_cleaned';\n\t\t\tlet subtitle = '未开始打扫';\n\n\t\t\t// 如果有清理记录，分析状态\n\t\t\tif (cleaningRecord) {\n\t\t\t\tconst cleaningDate = new Date(cleaningRecord.cleaning_date);\n\n\t\t\t\tif (inspectionRecord) {\n\t\t\t\t\tconst inspectionDate = new Date(inspectionRecord.inspection_date || inspectionRecord.created_at);\n\n\t\t\t\t\tif (inspectionDate >= cleaningDate) {\n\t\t\t\t\t\t({ status, subtitle } = this.processInspectionStatusForWeek(inspectionRecord, inspectionDate, area, cleaningRecord, weekTimes));\n\t\t\t\t\t} else {\n\t\t\t\t\t\tstatus = 'pending';\n\t\t\t\t\t\tsubtitle = `员工已提交，待检查 · ${this.formatDateTimeOptimized(cleaningDate)}`;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// 有清理记录但没有检查记录\n\t\t\t\t\tconst now = new Date();\n\t\t\t\t\tif (now > weekTimes.weekEnd) {\n\t\t\t\t\t\tstatus = 'missed';\n\t\t\t\t\t\tsubtitle = `检查员漏检查 · ${this.formatDateTimeOptimized(cleaningDate)}`;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tstatus = 'pending';\n\t\t\t\t\t\tsubtitle = `员工已提交，待检查 · ${this.formatDateTimeOptimized(cleaningDate)}`;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// 没有清理记录\n\n\t\t\t\tconst now = new Date();\n\t\t\t\tif (now > weekTimes.weekEnd) {\n\t\t\t\t\t// 过去的周次\n\t\t\t\t\tif (isCurrentWeek) {\n\t\t\t\t\t\tstatus = 'not_cleaned';\n\t\t\t\t\t\tsubtitle = '本周未打扫';\n\t\t\t\t\t} else {\n\t\t\t\t\t\tstatus = 'not_cleaned';\n\t\t\t\t\t\tsubtitle = `第${this.getWeekDisplayNumber(weekKey)}周未打扫`;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// 当前或未来的周次\n\t\t\t\t\tif (isCurrentWeek) {\n\t\t\t\t\t\tstatus = 'not_cleaned';\n\t\t\t\t\t\tsubtitle = '未开始打扫';\n\t\t\t\t\t} else {\n\t\t\t\t\t\tstatus = 'not_cleaned';\n\t\t\t\t\t\tsubtitle = '未开始打扫';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\tid: inspectionRecord ? (inspectionRecord._id || inspectionRecord.id) : `${areaId}-${weekKey}`,\n\t\t\t\tareaName: area.name || '未知责任区',\n\t\t\t\tsubtitle,\n\t\t\t\tstatus,\n\t\t\t\ticon: this.getStatusIcon(status),\n\t\t\t\ttype: area.type || 'fixed',\n\t\t\t\tweek: weekKey,\n\t\t\t\tinspectionDate: inspectionRecord ? (inspectionRecord.inspection_date || inspectionRecord.created_at) : null,\n\t\t\t\tisRectificationRecheck: false,\n\t\t\t\tareaId,\n\t\t\t\tinspectorName: '检查员',\n\t\t\t\tinspectionRecordId: inspectionRecord ? (inspectionRecord._id || inspectionRecord.id) : null,\n\t\t\t\tisInspectionRecord: !!inspectionRecord,\n\t\t\t\tscheduledDay: area.scheduled_day || null\n\t\t\t};\n\t\t},\n\n\t\t// 根据周次键获取周的起始和结束时间\n\t\tgetWeekTimesFromKey(weekKey) {\n\t\t\ttry {\n\t\t\t\tconst match = weekKey.match(/(\\d{4})-W(\\d{2})/);\n\t\t\t\tif (!match) return null;\n\n\t\t\t\tconst year = parseInt(match[1]);\n\t\t\t\tconst weekNum = parseInt(match[2]);\n\n\t\t\t\t// 计算该年第一天\n\t\t\t\tconst jan1 = new Date(year, 0, 1);\n\n\t\t\t\t// 计算该周的周一\n\t\t\t\tconst daysSinceJan1 = (weekNum - 1) * 7;\n\t\t\t\tconst weekStart = new Date(jan1);\n\t\t\t\tweekStart.setDate(jan1.getDate() + daysSinceJan1 - jan1.getDay() + 1);\n\t\t\t\tweekStart.setHours(0, 0, 0, 0);\n\n\t\t\t\t// 计算该周的周日\n\t\t\t\tconst weekEnd = new Date(weekStart);\n\t\t\t\tweekEnd.setDate(weekStart.getDate() + 6);\n\t\t\t\tweekEnd.setHours(23, 59, 59, 999);\n\n\t\t\t\treturn { weekStart, weekEnd };\n\t\t\t} catch (error) {\n\t\t\t\treturn null;\n\t\t\t}\n\t\t},\n\n\t\t// 为特定周次处理检查状态\n\t\tprocessInspectionStatusForWeek(inspectionRecord, inspectionDate, area, cleaningRecord, weekTimes) {\n\t\t\tlet status, subtitle;\n\t\t\tconst now = new Date();\n\n\t\t\tif (inspectionRecord.status === 'pending_rectification') {\n\t\t\t\t// 检查整改任务是否已超时\n\t\t\t\tif (now > weekTimes.weekEnd) {\n\t\t\t\t\tstatus = 'missed';\n\t\t\t\t\tsubtitle = `整改任务已超时 · ${this.formatDateTimeOptimized(inspectionDate)}`;\n\t\t\t\t} else {\n\t\t\t\t\tstatus = 'pending_rectification';\n\t\t\t\t\tsubtitle = `检查未通过，待整改 · ${this.formatDateTimeOptimized(inspectionDate)}`;\n\t\t\t\t}\n\t\t\t} else if (inspectionRecord.status === 'rectification_completed') {\n\t\t\t\t// 检查整改复查是否已跨周\n\t\t\t\tconst submitTime = inspectionRecord.rectification_submit_time || inspectionRecord.updated_at;\n\t\t\t\tconst submitDate = submitTime ? new Date(submitTime) : inspectionDate;\n\n\t\t\t\t// 计算复查截止时间（整改提交后的本周结束）\n\t\t\t\tconst submitWeekKey = this.getCurrentWeekKeyOptimized(submitDate);\n\t\t\t\tconst submitWeekTimes = this.getWeekTimesFromKey(submitWeekKey);\n\n\t\t\t\tif (submitWeekTimes && now > submitWeekTimes.weekEnd) {\n\t\t\t\t\tstatus = 'missed';\n\t\t\t\t\tsubtitle = `整改复查已超时 · 提交时间: ${this.formatDateTimeOptimized(submitDate)}`;\n\t\t\t\t} else {\n\t\t\t\t\tstatus = 'pending_review';\n\t\t\t\t\tsubtitle = `整改已提交，待复查 · ${this.formatDateTimeOptimized(submitDate)}`;\n\t\t\t\t}\n\t\t\t} else if (inspectionRecord.status === 'verified') {\n\t\t\t\tstatus = 'completed';\n\t\t\t\tsubtitle = `整改已确认，检查完成 · ${this.formatDateTimeOptimized(inspectionDate)}`;\n\t\t\t} else if (inspectionRecord.status === 'completed') {\n\t\t\t\tstatus = 'completed';\n\t\t\t\tsubtitle = `已完成检查 · ${this.formatDateTimeOptimized(inspectionDate)}`;\n\t\t\t} else {\n\t\t\t\tstatus = 'completed';\n\t\t\t\tsubtitle = `已完成检查 · ${this.formatDateTimeOptimized(inspectionDate)}`;\n\t\t\t}\n\n\t\t\treturn { status, subtitle };\n\t\t},\n\n\t\t// 解析周次键值为年份和周数\n\t\tparseWeekKey(weekKey) {\n\t\t\tconst match = weekKey.match(/(\\d{4})-W(\\d{2})/);\n\t\t\tif (!match) return null;\n\n\t\t\treturn {\n\t\t\t\tyear: parseInt(match[1]),\n\t\t\t\tweek: parseInt(match[2])\n\t\t\t};\n\t\t},\n\n\t\t// 获取周次的显示编号\n\t\tgetWeekDisplayNumber(weekKey) {\n\t\t\tconst parsed = this.parseWeekKey(weekKey);\n\t\t\treturn parsed ? parsed.week : '';\n\t\t},\n\n\t\tgetCurrentTimeRange() {\n\t\t\tif (this.selectedTimeFilter === 'custom' && this.dateFilterMode === 'range') {\n\t\t\t\treturn this.getDateRangeText();\n\t\t\t} else {\n\t\t\t\tconst option = this.quickDateOptions.find(opt => opt.value === this.selectedQuickFilter);\n\t\t\t\treturn option ? option.label : '本周';\n\t\t\t}\n\t\t},\n\n\t\t// 获取状态筛选文本\n\t\tgetStatusFilterText() {\n\t\t\tconst statusTexts = {\n\t\t\t\t'not_cleaned': '未打扫',\n\t\t\t\t'pending': '待检查',\n\t\t\t\t'pending_rectification': '待整改',\n\t\t\t\t'pending_review': '待复查',\n\t\t\t\t'completed': '已完成',\n\t\t\t\t'missed': '漏检查'\n\t\t\t};\n\t\t\treturn statusTexts[this.selectedStatus] || '';\n\t\t},\n\n\t\tgetStatsData() {\n\t\t\t// 基于实际筛选的记录计算统计 - 5种状态独立统计\n\t\t\tconst records = this.filteredRecords;\n\t\t\tconst not_cleaned = records.filter(r => r.status === 'not_cleaned').length;\n\t\t\tconst pending = records.filter(r => r.status === 'pending').length;\n\t\t\tconst pending_rectification = records.filter(r => r.status === 'pending_rectification').length;\n\t\t\tconst pending_review = records.filter(r => r.status === 'pending_review').length;\n\t\t\tconst completed = records.filter(r => r.status === 'completed').length;\n\t\t\tconst missed = records.filter(r => r.status === 'missed').length;\n\n\t\t\treturn { not_cleaned, pending, pending_rectification, pending_review, completed, missed };\n\t\t},\n\t\t// 选择分类\n\t\tselectCategory(category) {\n\t\t\tthis.selectedCategory = category;\n\t\t},\n\n\t\t// 选择状态筛选\n\t\tselectStatus(status) {\n\t\t\t// 如果点击的是当前选中的状态，则取消筛选（回到全部）\n\t\t\tif (this.selectedStatus === status) {\n\t\t\t\tthis.selectedStatus = 'all';\n\t\t\t} else {\n\t\t\t\tthis.selectedStatus = status;\n\t\t\t}\n\t\t},\n\t\t// 获取分类数量\n\t\tgetCategoryCount(category) {\n\t\t\tif (category === 'all') {\n\t\t\t\treturn this.filteredRecords.length;\n\t\t\t}\n\t\t\treturn this.filteredRecords.filter(r => r.type === category).length;\n\t\t},\n\t\t// 获取空状态文本\n\t\tgetEmptyText() {\n\t\t\ttry {\n\t\t\t\tif (this.selectedCategory === 'all') {\n\t\t\t\t\treturn '暂无检查记录';\n\t\t\t\t}\n\t\t\t\tconst category = this.categoryTabs.find(t => t.value === this.selectedCategory);\n\t\t\t\tconst categoryLabel = category ? category.label : '该分类';\n\t\t\t\treturn `暂无${categoryLabel}记录`;\n\t\t\t} catch (error) {\n\t\t\t\treturn '暂无数据';\n\t\t\t}\n\t\t},\n\t\t// 处理记录点击\n\t\thandleRecordClick(record) {\n\t\t\tif (record.status === 'pending') {\n\t\t\t\t// 待检查 - 可以开始检查\n\t\t\t\tthis.startInspection(record);\n\t\t\t} else if (record.status === 'completed') {\n\t\t\t\t// 已完成 - 查看检查记录详情\n\t\t\t\tthis.viewRecordDetail(record);\n\t\t\t} else if (record.status === 'not_cleaned') {\n\t\t\t\t// 未打扫 - 需要根据责任区类型和时效判断\n\t\t\t\tthis.handleNotCleanedClick(record);\n\t\t\t} else if (record.status === 'pending_rectification') {\n\t\t\t\t// 待整改 - 跳转到检查员查看整改详情页面\n\t\t\t\tthis.viewRectificationDetail(record);\n\t\t\t} else if (record.status === 'pending_review') {\n\t\t\t\t// 待复查 - 直接查找整改任务ID并跳转到复查页面\n\t\t\t\tthis.reviewRectificationFromRecord(record);\n\t\t\t} else if (record.status === 'missed') {\n\t\t\t\t// 漏检查 - 历史状态，提示已锁定\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '记录已锁定',\n\t\t\t\t\tcontent: '该责任区已漏检查，历史记录已锁定',\n\t\t\t\t\tshowCancel: false\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 处理未打扫状态的点击\n\t\thandleNotCleanedClick(record) {\n\t\t\tconst { title, content } = this.getNotCleanedMessage(record);\n\t\t\tuni.showModal({\n\t\t\t\ttitle,\n\t\t\t\tcontent,\n\t\t\t\tshowCancel: false\n\t\t\t});\n\t\t},\n\n\t\t// 获取未打扫状态的提示信息\n\t\tgetNotCleanedMessage(record) {\n\t\t\tconst now = new Date();\n\t\t\tconst currentWeekKey = this.getCurrentWeekKeyOptimized();\n\t\t\tconst isCurrentWeek = record.week === currentWeekKey;\n\n\t\t\t// 如果是历史周次，显示相应的历史提示\n\t\t\tif (!isCurrentWeek) {\n\t\t\t\tconst weekNum = this.getWeekDisplayNumber(record.week);\n\t\t\t\treturn {\n\t\t\t\t\ttitle: '历史记录',\n\t\t\t\t\tcontent: `第${weekNum}周该责任区未进行清理，已进入历史记录`\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tconst currentDay = now.getDay(); // 0=周日, 1=周一, ..., 6=周六\n\n\t\t\tif (record.type === 'public') {\n\t\t\t\t// 公共责任区：根据排班日判断\n\t\t\t\tconst scheduledDay = record.scheduledDay;\n\n\t\t\t\tif (scheduledDay !== null && scheduledDay !== undefined) {\n\t\t\t\t\t// 转换：0=周日转为7，保持1-6不变\n\t\t\t\t\tconst normalizedScheduledDay = scheduledDay === 0 ? 7 : scheduledDay;\n\t\t\t\t\tconst normalizedCurrentDay = currentDay === 0 ? 7 : currentDay;\n\n\t\t\t\t\tconst dayNames = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];\n\t\t\t\t\tconst scheduledDayName = dayNames[normalizedScheduledDay];\n\n\t\t\t\t\tif (normalizedCurrentDay > normalizedScheduledDay) {\n\t\t\t\t\t\t// 已过排班日\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\ttitle: '逾期未打扫',\n\t\t\t\t\t\t\tcontent: `该公共责任区排班日为${scheduledDayName}，现已逾期，已进入下个周期`\n\t\t\t\t\t\t};\n\t\t\t\t\t} else if (normalizedCurrentDay === normalizedScheduledDay) {\n\t\t\t\t\t\t// 当天\n\t\t\t\t\t\tconst currentHour = now.getHours();\n\t\t\t\t\t\tif (currentHour >= CONFIG.TIME.NIGHT_HOUR) { // 晚上8点后算即将逾期\n\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\ttitle: '即将逾期',\n\t\t\t\t\t\t\t\tcontent: `该公共责任区排班日为今天（${scheduledDayName}），员工需尽快完成打扫`\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\ttitle: '员工未打扫',\n\t\t\t\t\t\t\t\tcontent: `该公共责任区排班日为今天（${scheduledDayName}），请等待员工完成`\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 还未到排班日\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\ttitle: '员工未打扫',\n\t\t\t\t\t\t\tcontent: `该公共责任区排班日为${scheduledDayName}，请等待员工完成`\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// 没有排班日信息，使用默认提示\n\t\t\t\t\treturn {\n\t\t\t\t\t\ttitle: '员工未打扫',\n\t\t\t\t\t\tcontent: '该公共责任区员工尚未开始打扫，请等待员工完成'\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// 固定责任区：判断是否过了本周日\n\t\t\t\tconst weekEnd = this.getWeekEndOptimized(now);\n\n\t\t\t\tif (now > weekEnd) {\n\t\t\t\t\t// 已过本周日\n\t\t\t\t\treturn {\n\t\t\t\t\t\ttitle: '逾期未打扫',\n\t\t\t\t\t\tcontent: '该固定责任区本周期已结束，已进入下周期'\n\t\t\t\t\t};\n\t\t\t\t} else {\n\t\t\t\t\t// 本周内\n\t\t\t\t\tconst remainingDays = Math.ceil((weekEnd - now) / (24 * 60 * 60 * 1000));\n\t\t\t\t\tif (remainingDays <= 1) {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\ttitle: '即将逾期',\n\t\t\t\t\t\t\tcontent: '该固定责任区本周期即将结束，员工需尽快完成打扫'\n\t\t\t\t\t\t};\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\ttitle: '员工未打扫',\n\t\t\t\t\t\t\tcontent: '该固定责任区员工尚未开始打扫，请等待员工完成'\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\t// 获取责任区的排班日（这里需要根据实际数据结构调整）\n\t\tgetAreaScheduledDay(areaId) {\n\t\t\t// 这里应该从责任区数据中获取scheduled_day\n\t\t\t// 暂时返回null，需要在loadInspectionRecordsOptimized中补充这个信息\n\t\t\tconst area = this.areasCache?.find(a => (a._id || a.id) === areaId);\n\t\t\treturn area?.scheduled_day || null;\n\t\t},\n\n\t\t// ======== 新的日期选择器方法 ========\n\t\tshowTimeSelector() {\n\t\t\t// 初始化日期范围\n\t\t\tthis.initializeDateRange();\n\t\t\tthis.$refs.timePopup.open();\n\t\t},\n\n\t\tcloseDatePicker() {\n\t\t\tthis.$refs.timePopup.close();\n\t\t},\n\n\t\t// 初始化日期范围\n\t\tinitializeDateRange() {\n\t\t\tif (!this.customDateRange.startDate) {\n\t\t\t\tconst now = new Date();\n\t\t\t\t// 根据当前快捷选择初始化日期范围\n\t\t\t\tconst range = this.getQuickDateRange(this.selectedQuickFilter);\n\t\t\t\tthis.customDateRange.startDate = this.formatDateForPicker(range.start);\n\t\t\t\tthis.customDateRange.endDate = this.formatDateForPicker(range.end);\n\t\t\t}\n\n\t\t\t// 初始化日历组件的日期\n\t\t\tif (!this.calendarDate) {\n\t\t\t\tthis.calendarDate = this.formatDateForPicker(new Date());\n\t\t\t}\n\t\t},\n\n\t\t// 切换到范围选择模式\n\t\tswitchToRangeMode() {\n\t\t\tthis.dateFilterMode = 'range';\n\t\t},\n\n\t\t// 切换到快捷选择模式  \n\t\tswitchToQuickMode() {\n\t\t\tthis.dateFilterMode = 'quick';\n\t\t},\n\n\t\t// 快捷日期选择\n\t\tselectQuickDateOption(option) {\n\t\t\tthis.selectedQuickFilter = option.value;\n\t\t\tif (option.value === 'custom') {\n\t\t\t\tthis.switchToRangeMode();\n\t\t\t} else {\n\t\t\t\t// 快捷选择时，智能重置展开状态\n\t\t\t\tthis.smartResetExpandedState();\n\n\t\t\t\t// 快捷选择立即生效，关闭弹窗\n\t\t\t\tthis.closeDatePicker();\n\n\t\t\t\t// 重新加载数据以匹配新的筛选条件\n\t\t\t\tthis.loading = true;\n\t\t\t\tclearTimeout(this.refreshTimer);\n\t\t\t\tthis.refreshTimer = setTimeout(async () => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tawait this.loadInspectionRecordsOptimized();\n\t\t\t\t\t\t// 数据加载完成后，确保第一个分组展开\n\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t\tthis.setDefaultExpandedWeek();\n\t\t\t\t\t\t});\n\t\t\t\t\t} finally {\n\t\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t}\n\t\t\t\t}, CONFIG.TIME.DEBOUNCE_DELAY);\n\t\t\t}\n\t\t},\n\n\t\t// 获取当前日期范围\n\t\tgetCurrentDateRange() {\n\t\t\tif (this.dateFilterMode === 'quick' && this.selectedQuickFilter !== 'custom') {\n\t\t\t\treturn this.getQuickDateRange(this.selectedQuickFilter);\n\t\t\t} else if (this.customDateRange.startDate && this.customDateRange.endDate) {\n\t\t\t\treturn {\n\t\t\t\t\tstart: new Date(this.customDateRange.startDate),\n\t\t\t\t\tend: new Date(this.customDateRange.endDate)\n\t\t\t\t};\n\t\t\t}\n\t\t\treturn null;\n\t\t},\n\n\t\t// 获取快捷日期的范围\n\t\tgetQuickDateRange(quickValue) {\n\t\t\tconst now = new Date();\n\t\t\tconst today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n\n\t\t\tswitch (quickValue) {\n\t\t\t\tcase 'week':\n\t\t\t\t\t// 本周：周一到周日\n\t\t\t\t\tconst startOfWeek = new Date(today);\n\t\t\t\t\tconst dayOfWeek = startOfWeek.getDay();\n\t\t\t\t\tconst daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;\n\t\t\t\t\tstartOfWeek.setDate(startOfWeek.getDate() - daysToMonday);\n\n\t\t\t\t\tconst endOfWeek = new Date(startOfWeek);\n\t\t\t\t\tendOfWeek.setDate(endOfWeek.getDate() + 6);\n\t\t\t\t\tendOfWeek.setHours(23, 59, 59, 999);\n\n\t\t\t\t\treturn { start: startOfWeek, end: endOfWeek };\n\n\t\t\t\tcase 'last_week':\n\t\t\t\t\t// 上周：上周一到上周日\n\t\t\t\t\tconst lastWeekStart = new Date(today);\n\t\t\t\t\tconst currentDayOfWeek = lastWeekStart.getDay();\n\t\t\t\t\tconst daysToLastMonday = currentDayOfWeek === 0 ? 13 : currentDayOfWeek + 6;\n\t\t\t\t\tlastWeekStart.setDate(lastWeekStart.getDate() - daysToLastMonday);\n\n\t\t\t\t\tconst lastWeekEnd = new Date(lastWeekStart);\n\t\t\t\t\tlastWeekEnd.setDate(lastWeekEnd.getDate() + 6);\n\t\t\t\t\tlastWeekEnd.setHours(23, 59, 59, 999);\n\n\t\t\t\t\treturn { start: lastWeekStart, end: lastWeekEnd };\n\n\t\t\t\tcase 'month':\n\t\t\t\t\t// 本月：1号到月底\n\t\t\t\t\tconst startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);\n\t\t\t\t\tconst endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);\n\t\t\t\t\tendOfMonth.setHours(23, 59, 59, 999);\n\n\t\t\t\t\treturn { start: startOfMonth, end: endOfMonth };\n\n\t\t\t\tdefault:\n\t\t\t\t\t// 默认返回本周\n\t\t\t\t\treturn this.getQuickDateRange('week');\n\t\t\t}\n\t\t},\n\n\t\t// 判断记录是否在日期范围内\n\t\tisRecordInDateRange(record, dateRange) {\n\t\t\t// 获取记录对应周的时间范围\n\t\t\tif (!record.week) return false;\n\n\t\t\tconst weekTimes = this.getWeekTimesFromKey(record.week);\n\t\t\tif (!weekTimes) return false;\n\n\t\t\t// 判断周的时间范围是否与选择的日期范围有重叠\n\t\t\t// 周开始时间 <= 选择结束时间 && 周结束时间 >= 选择开始时间\n\t\t\tconst weekOverlapsRange = weekTimes.weekStart <= dateRange.end && weekTimes.weekEnd >= dateRange.start;\n\t\t\treturn weekOverlapsRange;\n\t\t},\n\n\t\t// 获取周的开始日期（周一）\n\t\tgetWeekStart(date) {\n\t\t\tconst result = new Date(date);\n\t\t\tconst dayOfWeek = result.getDay();\n\t\t\tconst daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;\n\t\t\tresult.setDate(result.getDate() - daysToMonday);\n\t\t\tresult.setHours(0, 0, 0, 0);\n\t\t\treturn result;\n\t\t},\n\n\t\t// 获取公共责任区排班日的截止时间（排班日23:59:59）\n\t\tgetScheduledDayEndTime(currentDate, scheduledDay) {\n\t\t\tif (scheduledDay === null || scheduledDay === undefined) {\n\t\t\t\treturn null;\n\t\t\t}\n\n\t\t\t// 转换周日值：0转为7\n\t\t\tconst normalizedScheduledDay = scheduledDay === 0 ? 7 : scheduledDay;\n\t\t\tconst currentDayOfWeek = currentDate.getDay() === 0 ? 7 : currentDate.getDay();\n\n\t\t\t// 计算本周排班日的日期\n\t\t\tconst daysFromScheduled = normalizedScheduledDay - currentDayOfWeek;\n\t\t\tconst scheduledDate = new Date(currentDate);\n\t\t\tscheduledDate.setDate(currentDate.getDate() + daysFromScheduled);\n\t\t\tscheduledDate.setHours(23, 59, 59, 999);\n\n\t\t\treturn scheduledDate;\n\t\t},\n\n\t\t// 格式化日期用于picker\n\t\tformatDateForPicker(date) {\n\t\t\tif (!date) return '';\n\t\t\tconst d = new Date(date);\n\t\t\tconst year = d.getFullYear();\n\t\t\tconst month = String(d.getMonth() + 1).padStart(2, '0');\n\t\t\tconst day = String(d.getDate()).padStart(2, '0');\n\t\t\treturn `${year}-${month}-${day}`;\n\t\t},\n\n\t\t// 获取开始日期显示文本  \n\t\tgetStartDateText() {\n\t\t\tif (!this.customDateRange.startDate) return '请选择开始日期';\n\t\t\treturn this.formatDisplayDate(new Date(this.customDateRange.startDate));\n\t\t},\n\n\t\t// 获取结束日期显示文本\n\t\tgetEndDateText() {\n\t\t\tif (!this.customDateRange.endDate) return '请选择结束日期';\n\t\t\treturn this.formatDisplayDate(new Date(this.customDateRange.endDate));\n\t\t},\n\n\t\t// 格式化显示日期\n\t\tformatDisplayDate(date) {\n\t\t\tconst d = new Date(date);\n\t\t\tconst month = d.getMonth() + 1;\n\t\t\tconst day = d.getDate();\n\t\t\treturn `${month}月${day}日`;\n\t\t},\n\n\t\t// 获取日期范围显示文本\n\t\tgetDateRangeText() {\n\t\t\tif (this.dateFilterMode === 'quick') {\n\t\t\t\tconst option = this.quickDateOptions.find(opt => opt.value === this.selectedQuickFilter);\n\t\t\t\treturn option ? option.label : '本周';\n\t\t\t} else {\n\t\t\t\tconst startText = this.getStartDateText();\n\t\t\t\tconst endText = this.getEndDateText();\n\t\t\t\tif (startText === '请选择' || endText === '请选择') {\n\t\t\t\t\treturn '选择日期范围';\n\t\t\t\t}\n\t\t\t\treturn `${startText} - ${endText}`;\n\t\t\t}\n\t\t},\n\n\t\t// 日历组件事件处理\n\t\tonCalendarChange(event) {\n\n\t\t\t// 防止重复处理\n\t\t\tclearTimeout(this.calendarChangeTimer);\n\t\t\tthis.calendarChangeTimer = setTimeout(() => {\n\t\t\t\t// 处理日期范围选择\n\t\t\t\tif (event.range) {\n\t\t\t\t\tif (event.range.before && event.range.after) {\n\t\t\t\t\t\t// 选择了完整的日期范围\n\t\t\t\t\t\tthis.customDateRange.startDate = event.range.before;\n\t\t\t\t\t\tthis.customDateRange.endDate = event.range.after;\n\n\t\t\t\t\t\t// 立即生效：关闭弹窗并重新加载数据\n\t\t\t\t\t\tthis.selectedTimeFilter = 'custom';\n\t\t\t\t\t\tthis.smartResetExpandedState();\n\t\t\t\t\t\tthis.closeDatePicker();\n\n\t\t\t\t\t\t// 重新加载数据\n\t\t\t\t\t\tthis.loading = true;\n\t\t\t\t\t\tclearTimeout(this.refreshTimer);\n\t\t\t\t\t\tthis.refreshTimer = setTimeout(async () => {\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\tawait this.loadInspectionRecordsOptimized();\n\t\t\t\t\t\t\t\t// 数据加载完成后，确保第一个分组展开\n\t\t\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t\t\t\tthis.setDefaultExpandedWeek();\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} finally {\n\t\t\t\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}, CONFIG.TIME.DEBOUNCE_DELAY);\n\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '日期范围选择完成',\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\tduration: CONFIG.TIME.TOAST_DURATION\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} else if (event.fulldate) {\n\t\t\t\t\t// 单个日期选择\n\t\t\t\t\tif (!this.customDateRange.startDate || this.customDateRange.endDate) {\n\t\t\t\t\t\t// 选择开始日期或重新选择\n\t\t\t\t\t\tthis.customDateRange.startDate = event.fulldate;\n\t\t\t\t\t\tthis.customDateRange.endDate = '';\n\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 选择结束日期\n\t\t\t\t\t\tthis.customDateRange.endDate = event.fulldate;\n\n\t\t\t\t\t\t// 确保开始日期不晚于结束日期\n\t\t\t\t\t\tif (new Date(this.customDateRange.startDate) > new Date(this.customDateRange.endDate)) {\n\t\t\t\t\t\t\tconst temp = this.customDateRange.startDate;\n\t\t\t\t\t\t\tthis.customDateRange.startDate = this.customDateRange.endDate;\n\t\t\t\t\t\t\tthis.customDateRange.endDate = temp;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 选择完成后立即生效：关闭弹窗并重新加载数据\n\t\t\t\t\t\tthis.selectedTimeFilter = 'custom';\n\t\t\t\t\t\tthis.smartResetExpandedState();\n\t\t\t\t\t\tthis.closeDatePicker();\n\n\t\t\t\t\t\t// 重新加载数据\n\t\t\t\t\t\tthis.loading = true;\n\t\t\t\t\t\tclearTimeout(this.refreshTimer);\n\t\t\t\t\t\tthis.refreshTimer = setTimeout(async () => {\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\tawait this.loadInspectionRecordsOptimized();\n\t\t\t\t\t\t\t\t// 数据加载完成后，确保第一个分组展开\n\t\t\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t\t\t\tthis.setDefaultExpandedWeek();\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} finally {\n\t\t\t\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}, CONFIG.TIME.DEBOUNCE_DELAY);\n\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '日期范围选择完成',\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\tduration: CONFIG.TIME.TOAST_DURATION\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}, CONFIG.TIME.DEBOUNCE_DELAY);\n\t\t},\n\n\t\t// 月份切换事件\n\t\tonMonthSwitch(event) {\n\t\t\t// 安全地处理月份切换事件\n\t\t\tif (event && event.current && event.current.fulldate) {\n\t\t\t\tthis.calendarDate = event.current.fulldate;\n\t\t\t} else if (event && event.year && event.month) {\n\t\t\t\t// 如果没有fulldate，手动构造日期\n\t\t\t\tconst year = event.year;\n\t\t\t\tconst month = String(event.month).padStart(2, '0');\n\t\t\t\tthis.calendarDate = `${year}-${month}-01`;\n\t\t\t}\n\t\t},\n\n\t\t// 格式化选中的日期显示\n\t\tformatSelectedDate(dateStr) {\n\t\t\tif (!dateStr) return '';\n\t\t\tconst date = new Date(dateStr);\n\t\t\tconst month = date.getMonth() + 1;\n\t\t\tconst day = date.getDate();\n\t\t\tconst dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\n\t\t\tconst dayName = dayNames[date.getDay()];\n\t\t\treturn `${month}月${day}日 ${dayName}`;\n\t\t},\n\n\n\n\t\t// 获取状态图标\n\t\tgetStatusIcon(status) {\n\t\t\tconst icons = {\n\t\t\t\t'completed': 'checkmarkempty',\n\t\t\t\t'pending': 'calendar',\n\t\t\t\t'not_cleaned': 'minus',\n\t\t\t\t'pending_rectification': 'info',\n\t\t\t\t'pending_review': 'eye',\n\t\t\t\t'missed': 'close'\n\t\t\t};\n\t\t\treturn icons[status] || 'help';\n\t\t},\n\n\t\t// 获取问题类型中文显示\n\t\tgetCategoryText(category) {\n\t\t\tconst categoryMap = {\n\t\t\t\t'cleanliness': '清洁问题',\n\t\t\t\t'safety': '安全问题',\n\t\t\t\t'equipment': '设备问题',\n\t\t\t\t'environment': '环境问题',\n\t\t\t\t'organization': '整理问题',\n\t\t\t\t'standardization': '标识问题',\n\t\t\t\t'other': '其他问题'\n\t\t\t};\n\t\t\treturn categoryMap[category] || '其他';\n\t\t},\n\n\t\t// 获取周标题\n\t\tgetWeekTitle(weekKey) {\n\t\t\t// 解析weekKey，例如 '2025-W03' -> 2025年第3周\n\t\t\tconst match = weekKey.match(/(\\d{4})-W(\\d{2})/);\n\t\t\tif (!match) return weekKey;\n\n\t\t\tconst year = parseInt(match[1]);\n\t\t\tconst weekNum = parseInt(match[2]);\n\t\t\tconst currentYear = new Date().getFullYear();\n\t\t\tconst currentWeek = this.getWeekNumber(new Date());\n\n\t\t\t// 判断是否为当前周\n\t\t\tif (year === currentYear && weekNum === currentWeek) {\n\t\t\t\tconst weekRange = this.getCurrentWeekRange();\n\t\t\t\treturn `本周 (${weekRange.start}-${weekRange.end})`;\n\t\t\t} else if (year === currentYear && weekNum === currentWeek - 1) {\n\t\t\t\treturn `上周 (第${weekNum}周)`;\n\t\t\t} else if (year === currentYear) {\n\t\t\t\treturn `第${weekNum}周 (${year}年)`;\n\t\t\t} else {\n\t\t\t\treturn `第${weekNum}周 (${year}年)`;\n\t\t\t}\n\t\t},\n\n\t\t// 切换周展开状态\n\t\ttoggleWeek(weekKey) {\n\t\t\tconst index = this.expandedWeeks.indexOf(weekKey);\n\t\t\tif (index > -1) {\n\t\t\t\tthis.expandedWeeks.splice(index, 1);\n\t\t\t} else {\n\t\t\t\tthis.expandedWeeks.push(weekKey);\n\t\t\t}\n\t\t},\n\n\t\t// 重置展开状态\n\t\tresetExpandedState() {\n\t\t\tthis.expandedWeeks = [];\n\t\t},\n\t\t\n\t\t// 智能设置默认展开的周次（优先本周，没有则展开最新周）\n\t\tsetDefaultExpandedWeek() {\n\t\t\t// 获取当前筛选数据中的所有唯一周次\n\t\t\tconst allWeeks = [...new Set(this.filteredRecords.map(record => record.week))];\n\t\t\t\n\t\t\tif (allWeeks.length > 0) {\n\t\t\t\t// 获取当前周的周次键\n\t\t\t\tconst now = new Date();\n\t\t\t\tconst currentWeekKey = this.getCurrentWeekKeyOptimized(now);\n\t\t\t\t\n\t\t\t\tlet targetWeek;\n\t\t\t\t\n\t\t\t\t// 智能选择展开的周次：优先本周，没有则选最新周\n\t\t\t\tif (allWeeks.includes(currentWeekKey)) {\n\t\t\t\t\t// 如果筛选结果中包含本周，优先展开本周\n\t\t\t\t\ttargetWeek = currentWeekKey;\n\t\t\t\t} else {\n\t\t\t\t\t// 如果没有本周数据，按周次排序找到最新的周\n\t\t\t\t\tconst sortedWeeks = allWeeks.sort((a, b) => {\n\t\t\t\t\t\tconst [yearA, weekA] = a.split('-W').map(x => parseInt(x));\n\t\t\t\t\t\tconst [yearB, weekB] = b.split('-W').map(x => parseInt(x));\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (yearA !== yearB) {\n\t\t\t\t\t\t\treturn yearB - yearA; // 年份降序\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn weekB - weekA; // 同年内周数降序\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\ttargetWeek = sortedWeeks[0];\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 确保目标周在展开列表中\n\t\t\t\tif (targetWeek && !this.expandedWeeks.includes(targetWeek)) {\n\t\t\t\t\tthis.expandedWeeks.push(targetWeek);\n\t\t\t\t}\n\n\t\t\t\t// 如果有待复查任务，确保包含待复查任务的周次也展开\n\t\t\t\tconst pendingReviewWeeks = this.filteredRecords\n\t\t\t\t\t.filter(record => record.status === 'pending_review')\n\t\t\t\t\t.map(record => record.week)\n\t\t\t\t\t.filter((week, index, arr) => arr.indexOf(week) === index); // 去重\n\n\t\t\t\tpendingReviewWeeks.forEach(week => {\n\t\t\t\t\tif (!this.expandedWeeks.includes(week)) {\n\t\t\t\t\t\tthis.expandedWeeks.push(week);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 智能重置展开状态\n\t\tsmartResetExpandedState() {\n\t\t\tthis.expandedWeeks = [];\n\t\t},\n\t\tselectAndConfirmTimeFilter(option) {\n\t\t\tthis.selectedTimeFilter = option.value;\n\n\t\t\t// 智能重置展开状态\n\t\t\tthis.smartResetExpandedState();\n\n\t\t\tthis.$refs.timePopup.close();\n\n\t\t\t// 显示加载状态并重新加载数据\n\t\t\tthis.loading = true;\n\t\t\tthis.loadPageData().finally(() => {\n\t\t\t\tthis.loading = false;\n\t\t\t\t// 数据加载完成后，确保第一个分组展开\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tthis.setDefaultExpandedWeek();\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\t// 优化的状态文本获取（使用缓存）\n\t\tgetStatusText(status) {\n\t\t\treturn this.processCache.statusMap[status] || status;\n\t\t},\n\n\t\tgetIconColor(status) {\n\t\t\tconst colorMap = {\n\t\t\t\t'not_cleaned': '#8E8E93',\n\t\t\t\t'pending': '#FF9500',\n\t\t\t\t'pending_rectification': '#FF3B30',\n\t\t\t\t'pending_review': '#007AFF',\n\t\t\t\t'completed': '#34C759',\n\t\t\t\t'missed': '#8B5CF6',\n\t\t\t\t'passed': '#34C759',\n\t\t\t\t'issues': '#FF3B30'\n\t\t\t};\n\t\t\treturn colorMap[status] || '#8E8E93';\n\t\t},\n\t\tstartInspection(record) {\n\t\t\t// 对于待检查状态，使用责任区ID\n\t\t\tconst areaId = record.areaId || record.id;\n\t\t\t// 跳转到检查页面时，标记需要刷新数据\n\t\t\tthis.needsRefresh = true;\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/6s_pkg/inspection-detail?id=${areaId}&isRectification=${record.isRectificationRecheck || false}`\n\t\t\t});\n\t\t},\n\t\tviewRecordDetail(record) {\n\t\t\t// 如果有检查记录ID，说明这是基于实际检查记录的状态\n\t\t\tif (record.isInspectionRecord && record.inspectionRecordId) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/6s_pkg/record-detail?id=${record.inspectionRecordId}&type=inspection`\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\t// 否则这是责任区级别的信息，跳转到责任区详情\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/6s_pkg/area-detail?id=${record.areaId}`\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 查看整改详情\n\t\tasync viewRectificationDetail(record) {\n\t\t\ttry {\n\t\t\t\tuni.showLoading({ title: '加载中...' });\n\n\t\t\t\tconst { callCloudFunction } = require('@/utils/auth.js');\n\n\t\t\t\tlet result;\n\n\t\t\t\t// 根据记录状态使用不同的查询方式\n\t\t\t\tif (record.status === 'pending_review') {\n\t\t\t\t\t// 对于待复查状态，使用责任区ID查找整改任务\n\t\t\t\t\tconst areaId = record.areaId || record.id;\n\n\t\t\t\t\tresult = await callCloudFunction('hygiene-rectification', {\n\t\t\t\t\t\taction: 'getRectifications',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tarea_id: areaId,\n\t\t\t\t\t\t\tstatus: 'pending_review',\n\t\t\t\t\t\t\tpageSize: CONFIG.API.RECTIFICATION_PAGE_SIZE\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\t// 如果没找到 pending_review 状态的，尝试查找其他可能需要审核的状态\n\t\t\t\t\tlet hasValidTask = false;\n\t\t\t\t\tif (result && result.success && result.data) {\n\t\t\t\t\t\tif (Array.isArray(result.data) && result.data.length > 0) {\n\t\t\t\t\t\t\thasValidTask = true;\n\t\t\t\t\t\t} else if (result.data.list && Array.isArray(result.data.list) && result.data.list.length > 0) {\n\t\t\t\t\t\t\thasValidTask = true;\n\t\t\t\t\t\t} else if (result.data.records && Array.isArray(result.data.records) && result.data.records.length > 0) {\n\t\t\t\t\t\t\thasValidTask = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tif (!hasValidTask) {\n\t\t\t\t\t\tconst possibleStatuses = ['submitted', 'completed', 'in_progress'];\n\t\t\t\t\t\tfor (const status of possibleStatuses) {\n\t\t\t\t\t\t\tresult = await callCloudFunction('hygiene-rectification', {\n\t\t\t\t\t\t\t\taction: 'getRectifications',\n\t\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\t\tarea_id: record.areaId || record.id,\n\t\t\t\t\t\t\t\t\tstatus: status,\n\t\t\t\t\t\t\t\t\tpageSize: CONFIG.API.RECTIFICATION_PAGE_SIZE\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\tif (result && result.success && result.data) {\n\t\t\t\t\t\t\t\tlet task = null;\n\t\t\t\t\t\t\t\tif (Array.isArray(result.data) && result.data.length > 0) {\n\t\t\t\t\t\t\t\t\ttask = result.data[0];\n\t\t\t\t\t\t\t\t} else if (result.data.list && Array.isArray(result.data.list) && result.data.list.length > 0) {\n\t\t\t\t\t\t\t\t\ttask = result.data.list[0];\n\t\t\t\t\t\t\t\t} else if (result.data.records && Array.isArray(result.data.records) && result.data.records.length > 0) {\n\t\t\t\t\t\t\t\t\ttask = result.data.records[0];\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tif (task) {\n\t\t\t\t\t\t\t\t\t// 检查是否真的需要审核（有提交时间）\n\t\t\t\t\t\t\t\t\tconst hasSubmitted = task.submitted_at || task.rectification_submit_time || task.completion_time;\n\t\t\t\t\t\t\t\t\tif (hasSubmitted) {\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// 对于待整改状态，优先通过责任区ID查找最新的整改任务\n\t\t\t\t\tconst areaId = record.areaId || record.id;\n\n\t\t\t\t\tresult = await callCloudFunction('hygiene-rectification', {\n\t\t\t\t\t\taction: 'getRectifications',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tarea_id: areaId,\n\t\t\t\t\t\t\tstatus: 'pending_rectification',\n\t\t\t\t\t\t\tpageSize: CONFIG.API.RECTIFICATION_PAGE_SIZE\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\t// 如果通过状态查找失败，再尝试通过检查记录ID查找\n\t\t\t\t\tif (!result || !result.success || !result.data ||\n\t\t\t\t\t\t(!Array.isArray(result.data) || result.data.length === 0) &&\n\t\t\t\t\t\t(!result.data.list || result.data.list.length === 0) &&\n\t\t\t\t\t\t(!result.data.records || result.data.records.length === 0)) {\n\n\t\t\t\t\t\tresult = await callCloudFunction('hygiene-rectification', {\n\t\t\t\t\t\t\taction: 'getRectifications',\n\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\tinspection_record_id: record.id,\n\t\t\t\t\t\t\t\tpageSize: CONFIG.API.RECTIFICATION_PAGE_SIZE\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tuni.hideLoading();\n\n\t\t\t\tif (result && result.success && result.data) {\n\t\t\t\t\tlet rectificationTask = null;\n\n\t\t\t\t\t// 处理不同的数据结构\n\t\t\t\t\tif (Array.isArray(result.data) && result.data.length > 0) {\n\t\t\t\t\t\trectificationTask = result.data[0];\n\t\t\t\t\t} else if (result.data.list && Array.isArray(result.data.list) && result.data.list.length > 0) {\n\t\t\t\t\t\trectificationTask = result.data.list[0];\n\t\t\t\t\t} else if (result.data.records && Array.isArray(result.data.records) && result.data.records.length > 0) {\n\t\t\t\t\t\trectificationTask = result.data.records[0];\n\t\t\t\t\t}\n\n\t\t\t\t\tif (rectificationTask) {\n\t\t\t\t\t\tconst taskId = rectificationTask._id || rectificationTask.id;\n\n\t\t\t\t\t\tif (taskId) {\n\t\t\t\t\t\t\t// 跳转到检查员专用的整改详情页面\n\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\turl: `/pages/6s_pkg/inspector-rectification-detail?taskId=${taskId}`\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: '错误',\n\t\t\t\t\t\t\t\tcontent: '整改任务数据异常，缺少ID字段',\n\t\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\tcontent: '未找到对应的整改任务',\n\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: '未找到对应的整改任务',\n\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '加载失败',\n\t\t\t\t\tcontent: '无法加载整改任务详情，请稍后重试',\n\t\t\t\t\tshowCancel: false\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 从检查记录审核整改任务\n\t\tasync reviewRectificationFromRecord(record) {\n\t\t\ttry {\n\t\t\t\tuni.showLoading({ title: '查找整改任务...' });\n\n\t\t\t\tconst { callCloudFunction } = require('@/utils/auth.js');\n\t\t\t\tconst areaId = record.areaId || record.id;\n\n\t\t\t\t// 查找该责任区的整改任务\n\t\t\t\tlet result = await callCloudFunction('hygiene-rectification', {\n\t\t\t\t\taction: 'getRectifications',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\tarea_id: areaId,\n\t\t\t\t\t\tstatus: 'pending_review',\n\t\t\t\t\t\tpageSize: CONFIG.API.RECTIFICATION_PAGE_SIZE\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\t// 如果没找到 pending_review 状态的，尝试查找其他可能需要审核的状态\n\t\t\t\tif (!result || !result.success || !result.data ||\n\t\t\t\t\t(!Array.isArray(result.data) && (!result.data.list || result.data.list.length === 0) && (!result.data.records || result.data.records.length === 0))) {\n\n\t\t\t\t\tconst possibleStatuses = ['submitted', 'completed', 'in_progress'];\n\t\t\t\t\tfor (const status of possibleStatuses) {\n\t\t\t\t\t\tresult = await callCloudFunction('hygiene-rectification', {\n\t\t\t\t\t\t\taction: 'getRectifications',\n\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\tarea_id: areaId,\n\t\t\t\t\t\t\t\tstatus: status,\n\t\t\t\t\t\t\t\tpageSize: CONFIG.API.RECTIFICATION_PAGE_SIZE\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tif (result && result.success && result.data) {\n\t\t\t\t\t\t\tlet task = null;\n\t\t\t\t\t\t\tif (Array.isArray(result.data) && result.data.length > 0) {\n\t\t\t\t\t\t\t\ttask = result.data[0];\n\t\t\t\t\t\t\t} else if (result.data.list && Array.isArray(result.data.list) && result.data.list.length > 0) {\n\t\t\t\t\t\t\t\ttask = result.data.list[0];\n\t\t\t\t\t\t\t} else if (result.data.records && Array.isArray(result.data.records) && result.data.records.length > 0) {\n\t\t\t\t\t\t\t\ttask = result.data.records[0];\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (task) {\n\t\t\t\t\t\t\t\t// 检查是否真的需要审核（有提交时间）\n\t\t\t\t\t\t\t\tconst hasSubmitted = task.submitted_at || task.rectification_submit_time || task.completion_time;\n\t\t\t\t\t\t\t\tif (hasSubmitted) {\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tuni.hideLoading();\n\n\t\t\t\tif (result && result.success && result.data) {\n\t\t\t\t\tlet rectificationTask = null;\n\n\t\t\t\t\t// 处理不同的数据结构\n\t\t\t\t\tif (Array.isArray(result.data) && result.data.length > 0) {\n\t\t\t\t\t\trectificationTask = result.data[0];\n\t\t\t\t\t} else if (result.data.list && Array.isArray(result.data.list) && result.data.list.length > 0) {\n\t\t\t\t\t\trectificationTask = result.data.list[0];\n\t\t\t\t\t} else if (result.data.records && Array.isArray(result.data.records) && result.data.records.length > 0) {\n\t\t\t\t\t\trectificationTask = result.data.records[0];\n\t\t\t\t\t}\n\n\t\t\t\t\tif (rectificationTask) {\n\t\t\t\t\t\tconst taskId = rectificationTask._id || rectificationTask.id;\n\n\t\t\t\t\t\tif (taskId) {\n\t\t\t\t\t\t\t// 跳转到整改复查页面时，标记需要刷新数据\n\t\t\t\t\t\t\tthis.needsRefresh = true;\n\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\turl: `/pages/6s_pkg/rectification-review?taskId=${taskId}`\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: '错误',\n\t\t\t\t\t\t\t\tcontent: '整改任务数据异常，缺少ID字段',\n\t\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\tcontent: '未找到对应的整改任务',\n\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: '未找到对应的整改任务',\n\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '加载失败',\n\t\t\t\t\tcontent: '无法加载整改任务，请稍后重试',\n\t\t\t\t\tshowCancel: false\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 优化的格式化日期时间（使用缓存）\n\t\tformatDateTimeOptimized(dateString) {\n\t\t\tif (!dateString) return '--';\n\n\t\t\tconst cacheKey = dateString.toString();\n\t\t\tif (this.processCache.formattedDatesCache.has(cacheKey)) {\n\t\t\t\treturn this.processCache.formattedDatesCache.get(cacheKey);\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\tlet date;\n\t\t\t\tif (typeof dateString === 'string') {\n\t\t\t\t\tif (dateString.includes('T') || dateString.includes('Z')) {\n\t\t\t\t\t\tdate = new Date(dateString);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tdate = new Date(dateString.replace(/-/g, '/'));\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tdate = new Date(dateString);\n\t\t\t\t}\n\n\t\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\t\tthis.processCache.formattedDatesCache.set(cacheKey, '--');\n\t\t\t\t\treturn '--';\n\t\t\t\t}\n\n\t\t\t\tconst formatted = `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;\n\t\t\t\tthis.processCache.formattedDatesCache.set(cacheKey, formatted);\n\t\t\t\treturn formatted;\n\t\t\t} catch (error) {\n\t\t\t\tthis.processCache.formattedDatesCache.set(cacheKey, '--');\n\t\t\t\treturn '--';\n\t\t\t}\n\t\t},\n\n\t\t// 保留原方法以防其他地方调用\n\t\tformatDateTime(dateString) {\n\t\t\treturn this.formatDateTimeOptimized(dateString);\n\t\t},\n\n\t\t// 处理清理记录更新事件\n\t\thandleCleaningRecordUpdated(data) {\n\t\t\t// 当有清理记录更新时，静默刷新检查任务状态\n\t\t\tthis.silentRefreshData();\n\t\t},\n\n\t\t// 处理检查记录更新事件\n\t\thandleInspectionRecordUpdated(data) {\n\t\t\t// 当有检查记录更新时，静默刷新检查任务状态\n\t\t\tthis.silentRefreshData();\n\t\t},\n\n\t\t// 处理整改记录更新事件\n\t\thandleRectificationRecordUpdated(data) {\n\t\t\t// 当有整改记录更新时，静默刷新检查任务状态\n\t\t\tthis.silentRefreshData();\n\t\t},\n\n\t\t// 优化的静默刷新数据（不显示加载状态）\n\t\tasync silentRefreshData() {\n\t\t\tif (this.loading) return; // 如果正在加载，跳过\n\n\t\t\ttry {\n\t\t\t\t// 清除缓存以获取最新数据\n\t\t\t\tthis.processCache.timeCalculations = null;\n\t\t\t\tthis.processCache.weekKeyCache.clear();\n\t\t\t\tthis.processCache.formattedDatesCache.clear();\n\n\t\t\t\t// 重新初始化时间计算\n\t\t\t\tthis.initTimeCalculations();\n\n\t\t\t\t// 静默重新加载检查记录，不显示loading状态\n\t\t\t\tawait this.loadInspectionRecordsOptimized();\n\t\t\t\t// 清除刷新标记，避免用户返回页面时再次显示loading\n\t\t\t\tthis.needsRefresh = false;\n\t\t\t} catch (error) {\n\t\t\t\t// 静默处理错误，不显示错误提示\n\t\t\t}\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\n\twidth: 100%;\n\toverflow-x: hidden;\n\t/* 防止水平滚动 */\n\tbox-sizing: border-box;\n\tfont-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;\n}\n\n.card {\n\tbackground: white;\n\tborder-radius: 16rpx;\n\tmargin: 0 32rpx 24rpx 32rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\n}\n\n.card:first-child {\n\tmargin-top: 24rpx;\n}\n\n.card-header {\n\tpadding: 32rpx 32rpx 16rpx 32rpx;\n}\n\n.header-content {\n\tdisplay: flex;\n\tflex-direction: row;\n\talign-items: baseline;\n\tjustify-content: space-between;\n}\n\n.filters-row {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 12rpx;\n}\n\n.status-filter-indicator {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 6rpx;\n\tbackground: rgba(0, 122, 255, 0.1);\n\tpadding: 12rpx 20rpx;\n\tborder-radius: 20rpx;\n\tborder: 2rpx solid rgba(0, 122, 255, 0.2);\n\tcursor: pointer;\n\ttransition: all 0.3s ease;\n\n\t&:active {\n\t\tbackground: rgba(0, 122, 255, 0.15);\n\t\ttransform: scale(0.98);\n\t}\n}\n\n.filter-text {\n\tfont-size: 24rpx;\n\tcolor: #007AFF;\n\tfont-weight: 500;\n}\n\n.card-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #1C1C1E;\n\tmargin-bottom: 0;\n}\n\n.card-subtitle {\n\tfont-size: 26rpx;\n\tcolor: #8E8E93;\n}\n\n/* 时间选择器样式 */\n.time-selector {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\tpadding: 12rpx 20rpx;\n\tbackground: rgba(0, 122, 255, 0.1);\n\tborder-radius: 20rpx;\n\tborder: 2rpx solid rgba(0, 122, 255, 0.2);\n\tcursor: pointer;\n\ttransition: all 0.3s ease;\n\n\t&:active {\n\t\tbackground: rgba(0, 122, 255, 0.15);\n\t\ttransform: scale(0.98);\n\t}\n\n\t&.small {\n\t\tpadding: 8rpx 16rpx;\n\t\tborder-radius: 16rpx;\n\t\tbackground: rgba(142, 142, 147, 0.1);\n\t\tborder-color: rgba(142, 142, 147, 0.2);\n\t}\n}\n\n.time-text {\n\tfont-size: 24rpx;\n\tcolor: #007AFF;\n\tfont-weight: 500;\n\n\t.small & {\n\t\tfont-size: 24rpx;\n\t\tcolor: #8E8E93;\n\t}\n}\n\n/* 弹窗样式 */\n.time-popup {\n\tbackground: white;\n\tborder-radius: 24rpx 24rpx 0 0;\n\tpadding: 0 0 env(safe-area-inset-bottom) 0;\n}\n\n.popup-header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 32rpx 32rpx 24rpx 32rpx;\n\tborder-bottom: 1rpx solid #F2F2F7;\n}\n\n.popup-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #1C1C1E;\n}\n\n.popup-close {\n\twidth: 48rpx;\n\theight: 48rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground: #F2F2F7;\n\tborder-radius: 50%;\n}\n\n.time-options {\n\tpadding: 24rpx 32rpx;\n}\n\n.time-option {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 24rpx 0;\n\tborder-bottom: 1rpx solid #F2F2F7;\n\tposition: relative;\n\n\t&:last-child {\n\t\tborder-bottom: none;\n\t}\n\n\t&.active {\n\t\t.option-text {\n\t\t\tcolor: #007AFF;\n\t\t\tfont-weight: 600;\n\t\t}\n\t}\n}\n\n.option-left {\n\tdisplay: flex;\n\talign-items: center;\n\tflex: 1;\n}\n\n.option-content {\n\tmargin-left: 16rpx;\n\tflex: 1;\n}\n\n.option-title-row {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\tmargin-bottom: 4rpx;\n}\n\n.option-text {\n\tfont-size: 28rpx;\n\tcolor: #1C1C1E;\n\tflex: 1;\n}\n\n.option-desc {\n\tfont-size: 24rpx;\n\tcolor: #8E8E93;\n\tdisplay: block;\n}\n\n.smart-badge {\n\tbackground: linear-gradient(135deg, #FF6B35, #FF8E53);\n\tcolor: white;\n\tfont-size: 20rpx;\n\tpadding: 4rpx 8rpx;\n\tborder-radius: 8rpx;\n\tfont-weight: 600;\n\tanimation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n\t0% {\n\t\topacity: 1;\n\t}\n\n\t50% {\n\t\topacity: 0.7;\n\t}\n\n\t100% {\n\t\topacity: 1;\n\t}\n}\n\n.time-option.recommended {\n\tbackground: linear-gradient(135deg, rgba(255, 107, 53, 0.05), rgba(255, 142, 83, 0.02));\n\tborder-left: 4rpx solid #FF6B35;\n}\n\n/* ======== 新的日历式日期选择器样式 ======== */\n.date-picker-popup {\n\tbackground: white;\n\tpadding: 0;\n\tmax-height: 80vh;\n\toverflow-y: auto;\n}\n\n.quick-date-section,\n.range-date-section {\n\tpadding: 24rpx 32rpx;\n}\n\n.section-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #1C1C1E;\n\tmargin-bottom: 24rpx;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.quick-options {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 16rpx;\n\tmargin-bottom: 32rpx;\n}\n\n.quick-option {\n\tflex: 1;\n\tmin-width: 120rpx;\n\tpadding: 24rpx 16rpx;\n\tborder: 2rpx solid #E5E5EA;\n\tborder-radius: 16rpx;\n\ttext-align: center;\n\ttransition: all 0.2s;\n}\n\n.quick-option.active {\n\tborder-color: #007AFF;\n\tbackground: rgba(0, 122, 255, 0.1);\n}\n\n.quick-text {\n\tfont-size: 28rpx;\n\tcolor: #1C1C1E;\n}\n\n.quick-option.active .quick-text {\n\tcolor: #007AFF;\n\tfont-weight: 600;\n}\n\n.date-inputs {\n\tmargin: 32rpx 0;\n}\n\n.date-input-group {\n\tmargin-bottom: 24rpx;\n}\n\n.input-label {\n\tfont-size: 26rpx;\n\tcolor: #8E8E93;\n\tmargin-bottom: 12rpx;\n\tdisplay: block;\n}\n\n.date-input {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 20rpx 24rpx;\n\tborder: 2rpx solid #E5E5EA;\n\tborder-radius: 16rpx;\n\tbackground: #F8F9FA;\n\ttransition: all 0.2s;\n}\n\n.date-input:active {\n\tborder-color: #007AFF;\n\tbackground: rgba(0, 122, 255, 0.05);\n}\n\n.date-text {\n\tfont-size: 28rpx;\n\tcolor: #1C1C1E;\n\tflex: 1;\n}\n\n.mode-switch {\n\ttext-align: center;\n\tpadding: 16rpx 0;\n}\n\n.switch-text {\n\tfont-size: 26rpx;\n\tcolor: #007AFF;\n\tpadding: 12rpx 0;\n}\n\n.popup-actions {\n\tdisplay: flex;\n\tgap: 16rpx;\n\tpadding: 24rpx 32rpx 32rpx;\n\tborder-top: 1rpx solid #E5E5EA;\n}\n\n.action-btn {\n\tflex: 1;\n\tpadding: 20rpx;\n\tborder-radius: 16rpx;\n\ttext-align: center;\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\ttransition: all 0.2s;\n}\n\n.action-btn.cancel {\n\tbackground: #F8F9FA;\n\tcolor: #8E8E93;\n\tborder: 2rpx solid #E5E5EA;\n}\n\n.action-btn.confirm {\n\tbackground: linear-gradient(135deg, #007AFF, #5AC8FA);\n\tcolor: white;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);\n\n\t&.disabled {\n\t\tbackground: #E5E5EA;\n\t\tcolor: #8E8E93;\n\t\tbox-shadow: none;\n\t\tcursor: not-allowed;\n\t}\n}\n\n.action-btn:active {\n\ttransform: scale(0.95);\n}\n\n/* 日历组件样式 */\n.calendar-section {\n\tmargin: 24rpx 0;\n}\n\n.calendar-header {\n\tmargin-bottom: 24rpx;\n}\n\n.calendar-tip {\n\tfont-size: 24rpx;\n\tcolor: #8E8E93;\n\tdisplay: block;\n}\n\n.selected-range {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-top: 24rpx;\n\tpadding: 20rpx;\n\tbackground: #F8F9FA;\n\tborder-radius: 16rpx;\n\tgap: 16rpx;\n}\n\n.range-item {\n\ttext-align: center;\n\tflex: 1;\n}\n\n.range-label {\n\tfont-size: 22rpx;\n\tcolor: #8E8E93;\n\tdisplay: block;\n\tmargin-bottom: 4rpx;\n}\n\n.range-value {\n\tfont-size: 26rpx;\n\tcolor: #1C1C1E;\n\tfont-weight: 600;\n\tdisplay: block;\n}\n\n.range-separator {\n\tfont-size: 24rpx;\n\tcolor: #007AFF;\n\tfont-weight: 600;\n}\n\n.option-check {\n\twidth: 32rpx;\n\theight: 32rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.popup-footer {\n\tpadding: 24rpx 32rpx;\n\tborder-top: 1rpx solid #F2F2F7;\n}\n\n.popup-btn {\n\twidth: 100%;\n\theight: 88rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder-radius: 16rpx;\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\n\t&.confirm {\n\t\tbackground: #007AFF;\n\t\tcolor: white;\n\t}\n}\n\n.stats-grid {\n\tdisplay: grid;\n\tgrid-template-columns: repeat(3, 1fr);\n\tgap: 32rpx;\n\tpadding: 32rpx;\n\ttext-align: center;\n\n\t&.stats-grid-four {\n\t\tgrid-template-columns: repeat(4, 1fr);\n\t\tgap: 24rpx;\n\t}\n\n\t&.stats-grid-five {\n\t\tgrid-template-columns: repeat(5, 1fr);\n\t\tgap: 16rpx;\n\t\tpadding: 24rpx 16rpx;\n\t}\n\n\t&.stats-grid-six {\n\t\tgrid-template-columns: repeat(3, 1fr);\n\t\tgap: 16rpx;\n\t\tpadding: 24rpx 16rpx;\n\n\t\t@media (min-width: 750rpx) {\n\t\t\tgrid-template-columns: repeat(6, 1fr);\n\t\t}\n\t}\n}\n\n.stats-item {\n\ttext-align: center;\n\tcursor: pointer;\n\ttransition: all 0.3s ease;\n\tborder-radius: 12rpx;\n\tpadding: 12rpx 8rpx;\n\n\t&:hover {\n\t\tbackground: rgba(0, 122, 255, 0.05);\n\t}\n\n\t&.active {\n\t\tbackground: rgba(0, 122, 255, 0.1);\n\t\tborder: 2rpx solid #007AFF;\n\t\ttransform: scale(1.05);\n\t}\n}\n\n/* 统计区域加载状态 */\n.stats-loading {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 60rpx 24rpx;\n\tmin-height: 160rpx;\n}\n\n.loading-spinner {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tborder: 4rpx solid #f3f3f3;\n\tborder-top: 4rpx solid #007AFF;\n\tborder-radius: 50%;\n\tanimation: spin 1s linear infinite;\n\tmargin-bottom: 24rpx;\n}\n\n.loading-text {\n\tfont-size: 28rpx;\n\tcolor: #8E8E93;\n\ttext-align: center;\n}\n\n.list-loading {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 120rpx 20rpx;\n\tbackground: #FFFFFF;\n\tborder-radius: 24rpx;\n\tmargin: 20rpx;\n}\n\n@keyframes spin {\n\t0% {\n\t\ttransform: rotate(0deg);\n\t}\n\n\t100% {\n\t\ttransform: rotate(360deg);\n\t}\n}\n\n.stats-number {\n\tfont-size: 48rpx;\n\tfont-weight: bold;\n\tmargin-bottom: 8rpx;\n\n\t&.not-cleaned {\n\t\tcolor: #8E8E93;\n\t}\n\n\t&.warning {\n\t\tcolor: #FF9500;\n\t}\n\n\t&.danger {\n\t\tcolor: #FF3B30;\n\t}\n\n\t&.review {\n\t\tcolor: #007AFF;\n\t}\n\n\t&.success {\n\t\tcolor: #34C759;\n\t}\n\n\t&.missed {\n\t\tcolor: #8B5CF6;\n\t}\n}\n\n.stats-label {\n\tfont-size: 24rpx;\n\tcolor: #8E8E93;\n}\n\n.list-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 24rpx 32rpx;\n\tborder-bottom: 1rpx solid #F2F2F7;\n\n\t&:last-child {\n\t\tborder-bottom: none;\n\t}\n\n}\n\n.list-item-icon {\n\twidth: 48rpx;\n\theight: 48rpx;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 24rpx;\n\n\t&.icon-bg-not_cleaned {\n\t\tbackground: rgba(142, 142, 147, 0.15);\n\t}\n\n\t&.icon-bg-pending {\n\t\tbackground: rgba(255, 149, 0, 0.1);\n\t}\n\n\t&.icon-bg-pending_rectification {\n\t\tbackground: rgba(255, 59, 48, 0.1);\n\t}\n\n\t&.icon-bg-pending_review {\n\t\tbackground: rgba(0, 122, 255, 0.1);\n\t}\n\n\t&.icon-bg-completed {\n\t\tbackground: rgba(52, 199, 89, 0.1);\n\t}\n\n\t&.icon-bg-missed {\n\t\tbackground: rgba(139, 92, 246, 0.1);\n\t}\n\n\t&.icon-bg-passed {\n\t\tbackground: rgba(52, 199, 89, 0.1);\n\t}\n\n\t&.icon-bg-issues {\n\t\tbackground: rgba(255, 59, 48, 0.1);\n\t}\n\n\t&.icon-bg-warning {\n\t\tbackground: rgba(255, 149, 0, 0.1);\n\t}\n}\n\n.list-item-content {\n\tflex: 1;\n}\n\n.list-item-title {\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tcolor: #1C1C1E;\n\tmargin-bottom: 4rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 12rpx;\n}\n\n.list-item-subtitle {\n\tfont-size: 24rpx;\n\tcolor: #8E8E93;\n}\n\n.list-item-right {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 16rpx;\n}\n\n.status-badge {\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 8rpx;\n\tfont-size: 24rpx;\n\tfont-weight: 600;\n\n\t&.status-not_cleaned {\n\t\tbackground: #F2F2F7;\n\t\tcolor: #8E8E93;\n\t}\n\n\t&.status-pending {\n\t\tbackground: #FFF4E6;\n\t\tcolor: #FF9500;\n\t}\n\n\t&.status-pending_rectification {\n\t\tbackground: #FFE6E6;\n\t\tcolor: #FF3B30;\n\t}\n\n\t&.status-pending_review {\n\t\tbackground: #E6F3FF;\n\t\tcolor: #007AFF;\n\t}\n\n\t&.status-completed {\n\t\tbackground: #E8F5E8;\n\t\tcolor: #34C759;\n\t}\n\n\t&.status-missed {\n\t\tbackground: #F3F0FF;\n\t\tcolor: #8B5CF6;\n\t}\n\n\t&.status-passed {\n\t\tbackground: #E8F5E8;\n\t\tcolor: #34C759;\n\t}\n\n\t&.status-issues {\n\t\tbackground: #FFE6E6;\n\t\tcolor: #FF3B30;\n\t}\n\n\t&.status-warning {\n\t\tbackground: #FFF4E6;\n\t\tcolor: #FF9500;\n\t}\n\n\t&.status-rejected {\n\t\tbackground: #FFE6E6;\n\t\tcolor: #FF3B30;\n\t}\n\n\t&.status-verified {\n\t\tbackground: #E8F5E8;\n\t\tcolor: #34C759;\n\t}\n\n\t&.status-approved {\n\t\tbackground: #E8F5E8;\n\t\tcolor: #34C759;\n\t}\n\n\t&.status-in_progress {\n\t\tbackground: #E6F3FF;\n\t\tcolor: #007AFF;\n\t}\n}\n\n.card-body {\n\tpadding: 0 32rpx 32rpx 32rpx;\n}\n\n.more-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tgap: 12rpx;\n\tpadding: 24rpx;\n\tborder: 2rpx dashed #E5E5EA;\n\tborder-radius: 12rpx;\n\tfont-size: 26rpx;\n\tcolor: #007AFF;\n}\n\n.bottom-safe-area {\n\theight: 40rpx;\n}\n\n// 分类标签样式\n.category-scroll {\n\twidth: 100%;\n}\n\n.category-tabs {\n\tdisplay: flex;\n\tpadding: 8rpx 32rpx 16rpx 32rpx;\n\tgap: 16rpx;\n\twhite-space: nowrap;\n}\n\n.category-tab {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tgap: 6rpx;\n\tpadding: 12rpx 20rpx;\n\tborder-radius: 20rpx;\n\tbackground: #F2F2F7;\n\tborder: 2rpx solid transparent;\n\ttransition: all 0.3s ease;\n\tflex-shrink: 0;\n\n\t&.active {\n\t\tbackground: #E6F3FF;\n\t\tborder-color: #007AFF;\n\t}\n}\n\n.tab-text {\n\tfont-size: 24rpx;\n\tfont-weight: 500;\n\tcolor: #8E8E93;\n\n\t.category-tab.active & {\n\t\tcolor: #007AFF;\n\t}\n}\n\n.tab-count {\n\tfont-size: 20rpx;\n\tcolor: #C7C7CC;\n\tbackground: rgba(199, 199, 204, 0.2);\n\tborder-radius: 10rpx;\n\tpadding: 2rpx 8rpx;\n\tmin-width: 32rpx;\n\ttext-align: center;\n\n\t.category-tab.active & {\n\t\tcolor: #007AFF;\n\t\tbackground: rgba(0, 122, 255, 0.15);\n\t}\n}\n\n// 标签容器\n.badges-container {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\tmargin-left: 12rpx;\n}\n\n// 区域类型标签样式\n.area-type-badge {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 4rpx 12rpx;\n\tborder-radius: 12rpx;\n\tborder: 1rpx solid;\n\n\t&.type-fixed {\n\t\tbackground: rgba(52, 199, 89, 0.1);\n\t\tborder-color: rgba(52, 199, 89, 0.2);\n\n\t\t.badge-text {\n\t\t\tcolor: #34C759;\n\t\t}\n\t}\n\n\t&.type-public {\n\t\tbackground: rgba(0, 122, 255, 0.1);\n\t\tborder-color: rgba(0, 122, 255, 0.2);\n\n\t\t.badge-text {\n\t\t\tcolor: #007AFF;\n\t\t}\n\t}\n}\n\n// 整改标识样式\n.rectification-badge {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground: rgba(255, 149, 0, 0.1);\n\tpadding: 4rpx 12rpx;\n\tborder-radius: 12rpx;\n\tborder: 1rpx solid rgba(255, 149, 0, 0.2);\n}\n\n.badge-text {\n\tfont-size: 20rpx;\n\tcolor: #FF9500;\n\tfont-weight: 500;\n\twhite-space: nowrap;\n}\n\n// 时间分组样式\n.time-group {\n\tmargin-bottom: 16rpx;\n}\n\n.time-group-header {\n\tpadding: 24rpx 32rpx;\n\tbackground: #F8F9FA;\n\tborder-radius: 12rpx;\n\tmargin-bottom: 8rpx;\n}\n\n.time-group-title {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 12rpx;\n}\n\n.time-title {\n\tfont-size: 30rpx;\n\tfont-weight: 500;\n\tcolor: #1D1D1F;\n\tflex: 1;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n}\n\n.time-count {\n\tfont-size: 24rpx;\n\tcolor: #8E8E93;\n\tbackground: #E5E7EB;\n\tpadding: 4rpx 12rpx;\n\tborder-radius: 16rpx;\n\tmin-width: 48rpx;\n\ttext-align: center;\n}\n\n/* 加载状态样式 */\n.loading-container {\n\tpadding: 80rpx 32rpx;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n}\n\n.loading-content {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 24rpx;\n}\n\n.loading-spinner {\n\twidth: 40rpx;\n\theight: 40rpx;\n\tborder: 4rpx solid #E5E7EB;\n\tborder-top: 4rpx solid #007AFF;\n\tborder-radius: 50%;\n\tanimation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n\t0% {\n\t\ttransform: rotate(0deg);\n\t}\n\n\t100% {\n\t\ttransform: rotate(360deg);\n\t}\n}\n\n.loading-text {\n\tfont-size: 28rpx;\n\tcolor: #8E8E93;\n\ttext-align: center;\n}\n\n@media (max-width: 414px) {\n\t.card {\n\t\tmargin: 0 16rpx 24rpx 16rpx;\n\t}\n\n\t.card:first-child {\n\t\tmargin-top: 24rpx;\n\t}\n}\n</style>", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./area-inspection.vue?vue&type=style&index=0&id=d9e42f30&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./area-inspection.vue?vue&type=style&index=0&id=d9e42f30&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775845015\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}