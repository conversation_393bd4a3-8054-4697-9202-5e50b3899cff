<view class="container data-v-0d79523c"><view class="page-header data-v-0d79523c"><text class="page-title data-v-0d79523c">{{"待处理事项 ("+total+")"}}</text></view><view class="category-tabs data-v-0d79523c"><view data-event-opts="{{[['tap',[['switchTab',['all']]]]]}}" class="{{['tab-item','data-v-0d79523c',(currentTab==='all')?'active':'']}}" bindtap="__e"><text class="data-v-0d79523c">{{"全部 ("+total+")"}}</text></view><view data-event-opts="{{[['tap',[['switchTab',['review']]]]]}}" class="{{['tab-item','data-v-0d79523c',(currentTab==='review')?'active':'']}}" bindtap="__e"><text class="data-v-0d79523c">{{"审核待办 ("+reviewCount+")"}}</text></view><block wx:if="{{hasResponsiblePermission}}"><view data-event-opts="{{[['tap',[['switchTab',['task']]]]]}}" class="{{['tab-item','data-v-0d79523c',(currentTab==='task')?'active':'']}}" bindtap="__e"><text class="data-v-0d79523c">{{"我的任务 ("+taskCount+")"}}</text></view></block></view><block wx:if="{{$root.g0>0}}"><view class="data-area data-v-0d79523c"><view class="todo-list data-v-0d79523c"><uni-list class="list-wrapper data-v-0d79523c" vue-id="7630afa2-1" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><uni-list-item vue-id="{{('7630afa2-2-'+index)+','+('7630afa2-1')}}" title="{{item.$orig.project||item.$orig.name||'未命名项目'}}" note="{{item.$orig.description||'无描述内容'}}" clickable="{{true}}" data-event-opts="{{[['^click',[['goToDetail',['$0'],[[['todoList','',index]]]]]]]}}" bind:click="__e" class="data-v-0d79523c" bind:__l="__l" vue-slots="{{['header','body','footer']}}"><view class="item-icon data-v-0d79523c" slot="header"><uni-icons vue-id="{{('7630afa2-3-'+index)+','+('7630afa2-2-'+index)}}" type="{{item.m0}}" size="22" color="{{item.m1}}" class="data-v-0d79523c" bind:__l="__l"></uni-icons></view><view class="item-body data-v-0d79523c" slot="body"><view class="item-title-row data-v-0d79523c"><text class="item-title data-v-0d79523c">{{item.$orig.project||item.$orig.name||'未命名项目'}}</text><text class="{{['item-type-badge','data-v-0d79523c',item.$orig.type==='task'?'task-badge':'review-badge']}}">{{item.m2}}</text></view><view class="item-description-container data-v-0d79523c"><text data-event-opts="{{[['tap',[['toggleExpand',[index]]]]]}}" class="{{['item-description','data-v-0d79523c',(expandedItems[index])?'expanded':'']}}" catchtap="__e">{{item.$orig.description||'无描述内容'}}</text><block wx:if="{{item.m3}}"><text data-event-opts="{{[['tap',[['toggleExpand',[index]]]]]}}" class="expand-btn data-v-0d79523c" catchtap="__e">展开</text></block><block wx:if="{{expandedItems[index]}}"><text data-event-opts="{{[['tap',[['toggleExpand',[index]]]]]}}" class="expand-btn data-v-0d79523c" catchtap="__e">收起</text></block></view></view><view class="item-footer data-v-0d79523c" slot="footer"><view class="item-info data-v-0d79523c"><text class="item-time data-v-0d79523c">{{item.m4}}</text><text class="item-dot data-v-0d79523c">·</text><text class="item-user data-v-0d79523c">{{item.m5}}</text></view><uni-icons vue-id="{{('7630afa2-4-'+index)+','+('7630afa2-2-'+index)}}" type="right" size="16" color="#BBBBBB" class="data-v-0d79523c" bind:__l="__l"></uni-icons></view></uni-list-item></block></uni-list></view><block wx:if="{{showLoadMore}}"><uni-load-more vue-id="7630afa2-5" status="{{loadMoreStatus}}" class="data-v-0d79523c" bind:__l="__l"></uni-load-more></block></view></block><block wx:else><view class="data-area data-v-0d79523c"><block wx:if="{{!hasInitialized}}"><view class="data-loading data-v-0d79523c"><uni-load-more vue-id="7630afa2-6" status="loading" content-text="{{({contentdown:'正在加载待办事项...'})}}" class="data-v-0d79523c" bind:__l="__l"></uni-load-more></view></block><block wx:else><block wx:if="{{isLoading}}"><view class="data-loading data-v-0d79523c"><uni-load-more vue-id="7630afa2-7" status="loading" content-text="{{({contentdown:'加载中...'})}}" class="data-v-0d79523c" bind:__l="__l"></uni-load-more></view></block><block wx:else><p-empty-state vue-id="7630afa2-8" type="data" text="暂无待办事项" size="medium" class="data-v-0d79523c" bind:__l="__l"></p-empty-state></block></block></view></block></view>