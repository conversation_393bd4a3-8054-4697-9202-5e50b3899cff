<template>
  <view class="page-container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载区域详情中...</text>
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="loadError" class="error-container">
      <p-empty-state 
        type="error"
        title="加载失败"
        description="网络异常，请检查网络连接"
        :show-button="true"
        button-text="重新加载"
        @button-click="loadPageData"
      ></p-empty-state>
    </view>

    <!-- 正常内容 -->
    <template v-else>
    <!-- 责任区概览 -->
    <view class="card">
      <view class="card-header">
        <view class="header-content">
          <view class="card-title">{{ areaInfo.name }}</view>
          <view class="card-subtitle">{{ getAreaTypeText(areaInfo.type) }}</view>
        </view>
        <view class="status-badge" :class="['status-' + areaInfo.status]">
          {{ getStatusText(areaInfo.status) }}
        </view>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="card">
      <view class="card-header">
        <view class="card-title">基本信息</view>
      </view>
      <view class="card-body">
        <view class="info-list">
          <view class="info-item">
            <view class="info-label">
              <uni-icons type="location" size="16" color="#8E8E93"></uni-icons>
              <text>责任区位置</text>
            </view>
            <view class="info-value">{{ areaInfo.location }}</view>
          </view>
          <view class="info-item">
            <view class="info-label">
              <uni-icons type="person" size="16" color="#8E8E93"></uni-icons>
              <text>负责人</text>
            </view>
            <view class="info-value">{{ areaInfo.assignedEmployee }}</view>
          </view>
          <view class="info-item">
            <view class="info-label">
              <uni-icons type="reload" size="16" color="#8E8E93"></uni-icons>
              <text>清理频率</text>
            </view>
            <view class="info-value">{{ areaInfo.cleaningFrequency }}</view>
          </view>
          <view class="info-item">
            <view class="info-label">
              <uni-icons type="calendar" size="16" color="#8E8E93"></uni-icons>
              <text>下次清理</text>
            </view>
            <view class="info-value">{{ areaInfo.nextCleaningTime }}</view>
          </view>
        </view>
        
        <view v-if="areaInfo.description" class="description-section">
          <view class="description-title">区域说明</view>
          <view class="description-text">{{ areaInfo.description }}</view>
        </view>
      </view>
    </view>

    <!-- 周状态（动态显示本周或历史周） -->
    <view class="card">
      <view class="card-header">
        <view class="card-title">{{ isHistoricalView ? '历史状态' : '本周状态' }}</view>
        <view class="card-subtitle">{{ currentWeekText }}</view>
      </view>
      <view class="card-body">
        <view v-if="currentWeekRecord" class="week-status completed">
          <view class="status-icon">
            <uni-icons type="checkmarkempty" size="24" color="#34C759"></uni-icons>
          </view>
          <view class="status-content">
            <view class="status-title">{{ isHistoricalView ? '已完成清理' : '本周已完成清理' }}</view>
            <view class="status-desc">{{ currentWeekRecord.submitTime }}</view>
            <view class="status-photos" @click="viewWeekPhotos">
              <text>查看清理照片 ({{ currentWeekRecord.photos.length }}张)</text>
              <uni-icons type="right" size="14" color="#007AFF"></uni-icons>
            </view>
          </view>
        </view>
        
        <view v-else class="week-status pending">
          <view class="status-icon">
            <uni-icons type="clock" size="24" color="#FF9500"></uni-icons>
          </view>
          <view class="status-content">
            <view class="status-title">{{ isHistoricalView ? '当时未清理' : '本周待清理' }}</view>
            <view class="status-desc">{{ isHistoricalView ? '该时间段内未进行清理' : getWeekDeadlineText() }}</view>
          </view>
        </view>
      </view>
    </view>


    <!-- 操作按钮（历史模式下不显示） -->
    <view v-if="!isHistoricalView && !currentWeekRecord && areaInfo.canClean" class="action-section">
      <view class="action-btn primary" @click="goToCleaning">
        <uni-icons type="camera" size="20" color="white"></uni-icons>
        <text>立即清理</text>
      </view>
    </view>

    <!-- 清理历史 -->
    <view class="card">
      <view class="card-header">
        <view class="header-content">
          <view class="card-title">清理历史</view>
          <view class="card-subtitle">最近的清理记录</view>
        </view>
        <view class="time-filter" @click="showTimeFilter">
          <text>{{ selectedTimeFilter }}</text>
          <uni-icons type="down" size="12" color="#007AFF"></uni-icons>
        </view>
      </view>
      
      <p-empty-state
        v-if="cleaningHistory.length === 0"
        type="data"
        text="暂无清理记录"
        description="还没有清理记录，开始第一次清理吧"
      ></p-empty-state>
      
      <view v-else class="history-list">
        <view v-for="monthGroup in groupedHistory" :key="monthGroup.month" class="month-group">
          <!-- 月份标题 -->
          <view class="month-header" @click="toggleMonth(monthGroup.month)">
            <view class="month-info">
              <text class="month-title">📅 {{ monthGroup.monthText }}</text>
              <text class="month-count">({{ monthGroup.records.length }}条记录)</text>
            </view>
            <uni-icons 
              :type="expandedMonths.includes(monthGroup.month) ? 'down' : 'right'" 
              size="16" 
              color="#8E8E93"
            ></uni-icons>
          </view>
          
          <!-- 月份记录列表 -->
          <view v-if="expandedMonths.includes(monthGroup.month)" class="month-records">
            <view 
              v-for="(record, recordIndex) in monthGroup.records" 
              :key="recordIndex" 
              class="history-item"
              @click="viewRecordDetail(record)"
            >
              <view class="history-icon" :class="['icon-bg-' + record.status]">
                <uni-icons :type="record.icon" size="16" :color="getIconColor(record.status)"></uni-icons>
              </view>
              <view class="history-content">
                <view class="history-title">{{ record.weekText }}</view>
                <view class="history-subtitle">{{ record.subtitle }}</view>
              </view>
              <view class="history-right">
                <view class="photo-count">{{ record.photoCount }}张照片</view>
                <uni-icons type="right" size="14" color="#C7C7CC"></uni-icons>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 检查记录 -->
    <view class="card">
      <view class="card-header">
        <view class="card-title">检查记录</view>
        <view class="card-subtitle">管理层抽查记录</view>
      </view>
      
      <p-empty-state
        v-if="inspectionHistory.length === 0"
        type="data"
        text="暂无检查记录"
        description="还没有管理层检查记录"
      ></p-empty-state>
      
      <view v-else class="history-list">
        <view 
          v-for="(record, recordIndex) in inspectionHistory" 
          :key="recordIndex" 
          class="history-item"
          @click="viewInspectionDetail(record)"
        >
          <view class="history-icon" :class="['icon-bg-' + record.result]">
            <uni-icons :type="record.icon" size="18" :color="getIconColor(record.result)"></uni-icons>
          </view>
          <view class="history-content">
            <view class="history-title">{{ record.inspectorName }} · {{ record.time }}</view>
            <view class="history-subtitle">{{ record.subtitle }}</view>
          </view>
                      <view class="history-right">
              <!-- 优先显示评分，无评分时显示状态指示器 -->
              <view class="rating-stars" v-if="record.rating">
                <text>{{ record.rating }}/5</text>
                <uni-icons type="star-filled" size="12" color="#FFD700"></uni-icons>
              </view>
              <view class="status-indicator" v-else-if="record.statusIndicator" :style="{ color: record.statusIndicator.color }">
                <text class="indicator-text">{{ record.statusIndicator.text }}</text>
                <text class="indicator-desc">{{ record.statusIndicator.desc }}</text>
              </view>
              <uni-icons type="right" size="14" color="#C7C7CC"></uni-icons>
            </view>
        </view>
      </view>
    </view>

    <!-- 底部安全间距 -->
    <view class="bottom-safe-area"></view>
    </template>
  </view>
</template>

<script>
import { callCloudFunction } from '@/utils/auth.js';

export default {
  name: 'AreaDetail',
  data() {
    return {
      areaId: '',
      areaType: '',
      mode: 'view', // view(查看模式) 或 edit(编辑模式)
      recordId: '', // 特定记录ID(从我的清理记录跳转时使用)
      cleaningDate: '', // 清理日期(从历史记录跳转时使用)
      week: '', // 周次信息(从历史记录跳转时使用)
      isHistoricalView: false, // 是否是历史查看模式
      loading: true,
      loadError: false,
      areaInfo: {
        id: '',
        name: '',
        type: '',
        location: '',
        description: '',
        assignedEmployee: '',
        cleaningFrequency: '',
        nextCleaningTime: '',
        status: '',
        canClean: true
      },
      currentWeekText: '',
      currentWeekRecord: null,
      selectedTimeFilter: '近3个月',
      cleaningHistory: [],
      inspectionHistory: [],
      expandedMonths: [], // 默认展开月份
      
      // 性能优化缓存
      timeCache: null, // 时间计算缓存
      historyDataLoaded: false // 历史数据是否已加载
    }
  },
  computed: {
    // 按月份分组的历史记录
    groupedHistory() {
      const groups = {};
      
      this.cleaningHistory.forEach(record => {
        const date = new Date(record.submitDate.replace(/-/g, '/'));
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        
        if (!groups[monthKey]) {
          groups[monthKey] = {
            month: monthKey,
            monthText: `${date.getFullYear()}年${date.getMonth() + 1}月`,
            records: []
          };
        }
        
        groups[monthKey].records.push(record);
      });
      
      // 按月份倒序排列，每月内的记录也按日期倒序
      return Object.values(groups)
        .sort((a, b) => b.month.localeCompare(a.month))
        .map(group => ({
          ...group,
          records: group.records.sort((a, b) => b.submitDate.localeCompare(a.submitDate))
        }));
    },


  },
  onLoad(options) {
    // 兼容新的 record 参数
    if (options.record) {
      try {
        const record = JSON.parse(decodeURIComponent(options.record));
        this.areaId = record.areaId;
        this.areaType = record.areaType;
        this.mode = 'view';
        this.recordId = record.id; // 假设 record 中有 id
        this.cleaningDate = record.cleaningDate;
        this.week = record.week;
        this.isHistoricalView = true; // 从记录列表跳转都视为历史查看
      } catch (e) {
        console.error("Parsing record from URL failed", e);
        this.loadError = true;
        return;
      }
    } else if (options.areaId) {
      // 兼容旧的参数模式
      this.areaId = options.areaId;
      this.areaType = options.type || 'fixed';
      this.mode = options.mode || 'view';
      this.recordId = options.recordId || '';
      this.cleaningDate = options.cleaningDate || '';
      this.week = options.week || '';
      this.isHistoricalView = !!(this.cleaningDate || this.week || this.recordId);
    }

    this.initCurrentWeekText();
    this.initExpandedMonths();
    this.loadPageData();
    
    // 监听清理记录更新事件
    uni.$on('cleaningRecordUpdated', this.handleRecordUpdated);
    // 监听整改记录更新事件
    uni.$on('rectificationRecordUpdated', this.handleRecordUpdated);
    // 监听检查记录更新事件
    uni.$on('inspectionRecordUpdated', this.handleRecordUpdated);
  },
  onUnload() {
    // 移除事件监听
    uni.$off('cleaningRecordUpdated', this.handleRecordUpdated);
    uni.$off('rectificationRecordUpdated', this.handleRecordUpdated);
    uni.$off('inspectionRecordUpdated', this.handleRecordUpdated);
  },
  methods: {
    // 初始化当前周文本
    initCurrentWeekText() {
      try {
        if (this.isHistoricalView && this.cleaningDate) {
          // 历史模式：根据传入的清理日期计算对应周的信息
          const cleanDate = new Date(this.cleaningDate);
          const beijingCleanDate = this.toBeijingTime(cleanDate);
          
          if (!beijingCleanDate) {
            this.currentWeekText = '日期信息异常';
            return;
          }
          
          const weekStart = this.getWeekStart(beijingCleanDate);
          const weekEnd = this.getWeekEnd(beijingCleanDate);
          
          if (!weekStart || !weekEnd) {
            this.currentWeekText = '无法计算周次信息';
            return;
          }
          
          const startText = `${weekStart.getMonth() + 1}月${weekStart.getDate()}日`;
          const endText = `${weekEnd.getMonth() + 1}月${weekEnd.getDate()}日`;
          const weekNum = this.getWeekNumber(weekStart);
          
          this.currentWeekText = `第${weekNum}周 (${startText} - ${endText})`;
        } else {
          // 本周模式：显示当前周的信息
          const now = new Date();
          const beijingNow = this.toBeijingTime(now);
          
          if (!beijingNow) {
            this.currentWeekText = '当前时间异常';
            return;
          }
          
          const weekStart = this.getWeekStart(beijingNow);
          const weekEnd = this.getWeekEnd(beijingNow);
          
          if (!weekStart || !weekEnd) {
            this.currentWeekText = '无法计算本周信息';
            return;
          }
          
          const startText = `${weekStart.getMonth() + 1}月${weekStart.getDate()}日`;
          const endText = `${weekEnd.getMonth() + 1}月${weekEnd.getDate()}日`;
          const weekNum = this.getWeekNumber(weekStart);
          
          this.currentWeekText = `第${weekNum}周 (${startText} - ${endText})`;
        }
      } catch (error) {
        console.error('初始化当前周文本出错:', error);
        this.currentWeekText = '时间计算异常';
      }
    },

    // 初始化展开月份（展开当前月和上个月）
    initExpandedMonths() {
      const now = new Date();
      const currentMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
      const lastMonth = `${now.getFullYear()}-${String(now.getMonth()).padStart(2, '0')}`;
      this.expandedMonths = [currentMonth, lastMonth];
    },

    // 获取周数
    getWeekNumber(date) {
      const onejan = new Date(date.getFullYear(), 0, 1);
      const millisecsInDay = 86400000;
      return Math.ceil((((date.getTime() - onejan.getTime()) / millisecsInDay) + onejan.getDay() + 1) / 7);
    },

    // 北京时间转换函数
    toBeijingTime(dateInput) {
      if (!dateInput) {
        return null;
      }
      
      try {
        const inputDate = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
        if (isNaN(inputDate.getTime())) {
          return null;
        }
        
        // 小程序环境中使用更兼容的方式
        // 手动计算北京时间（UTC+8）
        const utcTime = inputDate.getTime();
        const beijingTime = new Date(utcTime + 8 * 60 * 60 * 1000);
        
        // 检查计算结果是否有效
        if (isNaN(beijingTime.getTime())) {
          return null;
        }
        
        return {
          getFullYear: () => beijingTime.getUTCFullYear(),
          getMonth: () => beijingTime.getUTCMonth(),
          getDate: () => beijingTime.getUTCDate(),
          getDay: () => beijingTime.getUTCDay(),
          getHours: () => beijingTime.getUTCHours(),
          getMinutes: () => beijingTime.getUTCMinutes(),
          getSeconds: () => beijingTime.getUTCSeconds(),
          getTime: () => beijingTime.getTime(),
          toISOString: () => beijingTime.toISOString(),
          // 兼容Date对象的比较
          valueOf: () => beijingTime.getTime()
        };
      } catch (error) {
        console.error('北京时间转换失败:', error, dateInput);
        return null;
      }
    },

    // 获取一周的开始日期（周一）- 使用北京时间
    getWeekStart(date) {
      if (!date) {
        return null;
      }
      
      try {
        let dateObj;
        if (date && typeof date.getFullYear === 'function') {
          // 如果是自定义的北京时间对象或原生Date对象
          dateObj = date;
        } else {
          // 如果是时间戳或字符串，先转换为北京时间
          dateObj = this.toBeijingTime(date);
          if (!dateObj) return null;
        }
        
        const year = dateObj.getFullYear();
        const month = dateObj.getMonth();
        const day = dateObj.getDate();
        const dayOfWeek = dateObj.getDay(); // 0=周日, 1=周一, ..., 6=周六
        
        // 检查获取的值是否有效
        if (isNaN(year) || isNaN(month) || isNaN(day) || isNaN(dayOfWeek)) {
          return null;
        }
        
        const currentDate = new Date(year, month, day);
        const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
        currentDate.setDate(day + mondayOffset);
        currentDate.setHours(0, 0, 0, 0);
        
        if (isNaN(currentDate.getTime())) {
          return null;
        }
        
        return currentDate;
      } catch (error) {
        console.error('计算周开始时间出错:', error, date);
        return null;
      }
    },

    // 获取一周的结束日期（周日）- 使用北京时间
    getWeekEnd(date) {
      if (!date) {
        return null;
      }
      
      try {
        let dateObj;
        if (date && typeof date.getFullYear === 'function') {
          // 如果是自定义的北京时间对象或原生Date对象
          dateObj = date;
        } else {
          // 如果是时间戳或字符串，先转换为北京时间
          dateObj = this.toBeijingTime(date);
          if (!dateObj) return null;
        }
        
        const year = dateObj.getFullYear();
        const month = dateObj.getMonth();
        const day = dateObj.getDate();
        const dayOfWeek = dateObj.getDay(); // 0=周日, 1=周一, ..., 6=周六
        
        // 检查获取的值是否有效
        if (isNaN(year) || isNaN(month) || isNaN(day) || isNaN(dayOfWeek)) {
          return null;
        }
        
        const currentDate = new Date(year, month, day);
        const sundayOffset = dayOfWeek === 0 ? 0 : 7 - dayOfWeek;
        currentDate.setDate(day + sundayOffset);
        currentDate.setHours(23, 59, 59, 999);
        
        if (isNaN(currentDate.getTime())) {
          return null;
        }
        
        return currentDate;
      } catch (error) {
        console.error('计算周结束时间出错:', error, date);
        return null;
      }
    },

    // 加载页面数据 - 性能优化版本
    async loadPageData() {
      this.loading = true;
      this.loadError = false;
      
      try {
        // 第一阶段：快速加载关键信息，让用户看到基本内容
        await Promise.all([
          this.loadAreaInfo(),
          this.checkCurrentWeekRecord()
        ]);
        
        // 更新状态（基于本周记录）
        if (this.areaInfo.id) {
          this.areaInfo.status = this.calculateAreaStatus(this.areaInfo);
          this.areaInfo.canClean = !this.currentWeekRecord;
        }
        
        // 第二阶段：等待所有历史数据加载完成
        try {
          await this.loadHistoryDataAsync();
          
          // 第三阶段：检查检查记录状态（先历史后本周，避免冲突）
          await this.checkMissedInspections();
          await this.checkCurrentWeekInspectionStatus();
        } catch (historyError) {
          // 历史数据加载失败不影响基本功能
          console.warn('历史数据加载失败:', historyError);
        }
        
      } catch (error) {
        this.loadError = true;
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.loading = false; // 所有数据加载完成，显示完整页面
      }
    },

    // 加载历史数据
    async loadHistoryDataAsync() {
      try {
        // 缓存时间计算结果
        this.initTimeCache();
        
        // 并行加载历史数据
        await Promise.all([
          this.loadCleaningHistory(),
          this.loadInspectionHistoryOptimized()
        ]);
      } catch (error) {
        // 历史数据加载失败不影响主要功能

      }
    },

    // 初始化时间缓存
    initTimeCache() {
      if (!this.timeCache) {
        const now = new Date();
        this.timeCache = {
          now,
          currentYear: now.getFullYear(),
          currentMonth: now.getMonth() + 1,
          // 预计算常用的时间范围
          cutoffDates: {}
        };
        
        // 预计算各种时间筛选的截止日期
        ['近1个月', '近3个月', '近6个月', '近1年'].forEach(filter => {
          let cutoffDate;
          switch(filter) {
            case '近1个月':
              cutoffDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
              break;
            case '近3个月':
              cutoffDate = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
              break;
            case '近6个月':
              cutoffDate = new Date(now.getFullYear(), now.getMonth() - 6, now.getDate());
              break;
            case '近1年':
              cutoffDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
              break;
          }
          this.timeCache.cutoffDates[filter] = cutoffDate;
        });
      }
    },

    // 加载责任区信息
    async loadAreaInfo() {
      try {
        const result = await callCloudFunction('hygiene-area-management', {
          action: 'getAreaDetail',
          data: {
            id: this.areaId  // 使用 id 而不是 area_id
          }
        });

        if (result && result.success && result.data) {
          const area = result.data;
          
          // 获取负责人信息
          let assignedEmployee = '未分配';
          if (area.assigned_user_details && area.assigned_user_details.length > 0) {
            // 使用用户的昵称或用户名
            const user = area.assigned_user_details[0];
            assignedEmployee = user.nickname || user.username || user._id || '未知用户';
          } else if (area.assigned_users && area.assigned_users.length > 0) {
            // 如果有分配用户但没有用户详情，尝试显示用户ID
            assignedEmployee = `用户${area.assigned_users[0]}`;
          }
          
          // 如果在assigned_users中没有找到负责人，从最近的清理记录中获取
          if (assignedEmployee === '未分配') {
            assignedEmployee = '待确定';
          }

          // 计算清理频率文本
          let cleaningFrequency = '未设置';
          let nextCleaningTime = '待定';
          
          if (area.type === 'fixed') {
            cleaningFrequency = area.cleaning_frequency === 'weekly' ? '每周一次' : '按需清理';
            nextCleaningTime = '本周内完成';
          } else if (area.type === 'public' && area.scheduled_day) {
            const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
            cleaningFrequency = `每${weekDays[area.scheduled_day]}清理`;
            nextCleaningTime = `下个${weekDays[area.scheduled_day]}`;
          }

          // 格式化位置信息
          let locationText = '';
          if (area.location) {
            const { building, floor, area: areaName } = area.location;
            locationText = [building, floor, areaName].filter(Boolean).join(' ');
          }

          this.areaInfo = {
            id: area._id,
            name: area.name,
            type: area.type,
            location: locationText || '未设置',
            description: area.description || '',
            assignedEmployee: assignedEmployee,
            cleaningFrequency: cleaningFrequency,
            nextCleaningTime: nextCleaningTime,
            status: 'pending', // 初始状态，将在加载完本周记录后重新计算
            canClean: true, // 初始值，将在加载完本周记录后重新计算
            scheduledDay: area.scheduled_day,
            cleaning_requirements: area.cleaning_requirements || []
          };
        } else {
          throw new Error('获取责任区信息失败');
        }
      } catch (error) {
        throw error;
      }
    },

    // 计算责任区状态
    calculateAreaStatus(area) {
      // 如果本周有清理记录，则为已完成
      if (this.currentWeekRecord) {
        return 'completed';
      }
      
      const now = new Date();
      const beijingNow = this.toBeijingTime(now);
      
      if (area.type === 'fixed') {
        // 固定责任区：本周内完成即可，周日23:59:59为截止时间
        const weekEnd = this.getWeekEnd(beijingNow);
        
        if (now > weekEnd) {
          return 'overdue'; // 已逾期
        } else {
          return 'pending'; // 待清理
        }
      } else if (area.type === 'public' && area.scheduled_day !== undefined) {
        // 公共责任区：需要在指定的周几完成
        const scheduledDay = area.scheduled_day; // 0=周日, 1=周一, ..., 6=周六
        
        // 计算本周指定日期
        const weekStart = this.getWeekStart(beijingNow);
        const scheduledDate = new Date(weekStart);
        
        if (scheduledDay === 0) {
          // 周日：本周的最后一天
          scheduledDate.setDate(weekStart.getDate() + 6);
        } else {
          // 周一到周六：相应调整天数
          scheduledDate.setDate(weekStart.getDate() + scheduledDay - 1);
        }
        
        scheduledDate.setHours(23, 59, 59, 999); // 设置为当天结束时间
        
        if (now > scheduledDate) {
          return 'overdue'; // 已逾期
        } else {
          return 'pending'; // 待清理
        }
      }
      
      // 默认返回待清理状态
      return 'pending';
    },



    // 加载清理历史 - 优化版本
    async loadCleaningHistory() {
      try {
        // 使用缓存的时间计算结果
        const cutoffDate = this.timeCache?.cutoffDates[this.selectedTimeFilter] || 
                          this.timeCache?.cutoffDates['近3个月'] ||
                          new Date(Date.now() - 90 * 24 * 60 * 60 * 1000); // 默认3个月
        
        const now = this.timeCache?.now || new Date();

        const result = await callCloudFunction('hygiene-cleaning', {
          action: 'getCleaningRecords',
          data: {
            area_id: this.areaId,
            start_date: cutoffDate.toISOString(),
            end_date: now.toISOString(),
            pageSize: 100
          }
        });

        if (result && result.success && result.data) {
          const records = result.data.list || [];
          
          // 如果负责人未确定，从最近的清理记录中获取
          if (this.areaInfo.assignedEmployee === '待确定' && records.length > 0) {
            const latestRecord = records.sort((a, b) => new Date(b.cleaning_date) - new Date(a.cleaning_date))[0];
            if (latestRecord.user_name) {
              this.areaInfo.assignedEmployee = latestRecord.user_name;
            } else if (latestRecord.cleaner_name) {
              this.areaInfo.assignedEmployee = latestRecord.cleaner_name;
            }
          }
          
          this.cleaningHistory = records.map(record => {
            const cleanDate = new Date(record.cleaning_date);
            const weekText = `${cleanDate.getMonth() + 1}月${cleanDate.getDate()}日`;
            const timeText = `${cleanDate.getHours().toString().padStart(2, '0')}:${cleanDate.getMinutes().toString().padStart(2, '0')}`;
            
            return {
              id: record._id,
              weekText: weekText,
              subtitle: `清理任务 · ${timeText}完成`,
              status: 'completed', // 根据业务逻辑，所有记录都是已完成
              icon: 'checkmarkempty',
              photoCount: record.photos ? record.photos.length : 0,
              submitDate: record.cleaning_date.split('T')[0], // 格式化为YYYY-MM-DD
              remark: record.remark || ''
            };
          }).sort((a, b) => b.submitDate.localeCompare(a.submitDate));
        } else {
          this.cleaningHistory = [];
        }
      } catch (error) {
        this.cleaningHistory = [];
      }
    },

    // 优化的检查记录加载 - 减少API调用次数
    async loadInspectionHistoryOptimized() {
      try {
        // 一次性获取检查记录和相关的整改记录
        const [inspectionResult, rectificationResult] = await Promise.all([
          callCloudFunction('hygiene-inspection', {
            action: 'getInspectionRecords',
            data: {
              area_id: this.areaId,
              pageSize: 20
            }
          }),
          // 批量获取该区域的所有整改记录，避免逐个查询
          callCloudFunction('hygiene-rectification', {
            action: 'getRectifications',
            data: {
              area_id: this.areaId,
              pageSize: 50 // 获取更多整改记录用于匹配
            }
          })
        ]);

        if (inspectionResult && inspectionResult.success && inspectionResult.data) {
          const records = inspectionResult.data.list || [];
          const rectificationRecords = rectificationResult?.data?.list || [];
          
          // 建立整改记录的快速查找映射
          const rectificationMap = new Map();
          rectificationRecords.forEach(rect => {
            const key = rect.inspection_record_id || `${rect.area_id}_${rect.created_at}`;
            if (!rectificationMap.has(key)) {
              rectificationMap.set(key, []);
            }
            rectificationMap.get(key).push(rect);
          });
          
          // 处理检查记录，匹配整改评分
          this.inspectionHistory = records.map(record => {
            // 优先通过inspection_record_id匹配，再通过区域和时间匹配
            let relatedRectifications = rectificationMap.get(record._id) || [];
            
            // 如果没有直接匹配，通过时间范围匹配（同一区域，检查时间之后的整改）
            if (relatedRectifications.length === 0) {
              const inspectionTime = new Date(record.inspection_date);
              relatedRectifications = rectificationRecords.filter(rect => 
                rect.area_id === record.area_id && 
                new Date(rect.created_at) >= inspectionTime &&
                new Date(rect.created_at) <= new Date(inspectionTime.getTime() + 7 * 24 * 60 * 60 * 1000) // 一周内
              );
            }
            
            // 获取最终评分（优先整改评分）
            let finalRating = record.overall_rating || record.score || 0;
            if (record.has_issues && relatedRectifications.length > 0) {
              const verifiedRectification = relatedRectifications.find(rect => rect.status === 'verified');
              if (verifiedRectification) {
                finalRating = verifiedRectification.final_rating || verifiedRectification.review_rating || finalRating;
              }
            }
            
            // 将相关整改记录附加到检查记录上
            const recordWithRectifications = {
              ...record,
              related_rectifications: relatedRectifications
            };
            
            return this.formatInspectionRecord(recordWithRectifications, finalRating);
          }).sort((a, b) => new Date(b.inspection_date) - new Date(a.inspection_date));
        } else {
          this.inspectionHistory = [];
        }
        
        this.historyDataLoaded = true;
      } catch (error) {
        this.inspectionHistory = [];

      }
    },

    // 格式化检查记录（提取公共逻辑）
    formatInspectionRecord(record, finalRating) {
      const inspectDate = new Date(record.inspection_date);
      const timeText = `${inspectDate.getMonth() + 1}月${inspectDate.getDate()}日 ${inspectDate.getHours().toString().padStart(2, '0')}:${inspectDate.getMinutes().toString().padStart(2, '0')}`;
      
      let subtitle = '';
      let result = 'passed';
      let icon = 'checkmarkempty';
      let currentRectStatus = null;  // 用于跟踪当前整改状态
      
      // 根据检查状态和是否有问题来确定显示内容
      if (record.has_issues) {
        // 有问题的情况 - 需要详细区分整改流程状态
        if (record.status === 'verified') {
          result = 'completed';
          icon = 'checkmarkempty';
          subtitle = `发现问题已整改完成 · 整改质量${this.getRatingDescription(finalRating).toLowerCase()}`;
        } else if (record.status === 'rectification_completed') {
          result = 'pending';
          icon = 'reload';
          subtitle = `整改已提交 · 等待检查员复查确认`;
        } else if (record.status === 'pending_rectification') {
          result = 'issues';
          icon = 'info';
          subtitle = `发现问题 · ${record.issues && record.issues.length > 0 ? record.issues[0].description : '需要整改'}`;
        } else {
          result = 'issues';
          icon = 'info';
          subtitle = `发现问题 · 需要整改`;
        }
        
        // 检查相关整改记录的详细状态
        if (record.related_rectifications && record.related_rectifications.length > 0) {
          const latestRectification = record.related_rectifications[0];
          currentRectStatus = latestRectification.status;
          
          if (currentRectStatus === 'verified') {
            result = 'completed';
            icon = 'checkmarkempty';
            subtitle = `发现问题已整改完成 · 整改质量${this.getRatingDescription(finalRating).toLowerCase()}`;
          } else if (currentRectStatus === 'pending_review') {
            // 关键判断：员工已提交整改，检查员应该复查但未及时复查
            const submitTime = new Date(latestRectification.submitted_at || latestRectification.updated_at);
            const now = new Date();
            
            // 根据责任区类型判断是否漏检查
            let isOverdue = false;
            
            if (this.areaInfo.type === 'public' && this.areaInfo.scheduledDay !== undefined) {
              // 公共责任区：严格按指定日期，当天23:59:59为截止
              const submitDate = this.toBeijingTime(submitTime);
              const weekStart = this.getWeekStart(submitDate);
              const scheduledDate = new Date(weekStart);
              
              if (this.areaInfo.scheduledDay === 0) {
                scheduledDate.setDate(weekStart.getDate() + 6); // 周日
              } else {
                scheduledDate.setDate(weekStart.getDate() + this.areaInfo.scheduledDay - 1);
              }
              scheduledDate.setHours(23, 59, 59, 999);
              
              isOverdue = now > scheduledDate;
            } else {
              // 固定责任区：本周截止，周日23:59:59为截止
              const submitDate = this.toBeijingTime(submitTime);
              const weekEnd = this.getWeekEnd(submitDate);
              isOverdue = now > weekEnd;
            }
            
            if (isOverdue) {
              // 超过截止时间，这是检查员漏检查
              result = 'overdue';
              icon = 'info';
              subtitle = `检查员漏检查 · 整改已提交但未及时复查`;
            } else {
              // 截止时间内，正常显示待复查
              result = 'issues';
              icon = 'eye';
              subtitle = `发现问题 · 整改已提交，待检查员复查`;
            }
          } else {
            // 其他状态统一显示为"发现问题，整改处理中"
            result = 'issues';
            icon = 'info';
            subtitle = `发现问题 · ${record.issues && record.issues.length > 0 ? record.issues[0].description : '整改处理中'}`;
          }
        }
      } else {
        // 没有问题，检查通过
        result = 'passed';
        icon = 'checkmarkempty';
        subtitle = `检查通过 · 清理效果${this.getRatingDescription(finalRating).toLowerCase()} · 无问题发现`;
      }

      return {
        id: record._id,
        inspectorName: `检查员：${record.inspector_name || '未知'}`,
        time: timeText,
        subtitle: subtitle,
        result: result,
        icon: icon,
        rating: finalRating,
        inspection_date: record.inspection_date,
        status: record.status || 'unknown',
        // 添加状态指示器信息
        statusIndicator: this.getStatusIndicator(result, currentRectStatus || record.status)
      };
    },

    // 加载检查记录 - 保留原方法作为备用
    async loadInspectionHistory() {
      try {
        const result = await callCloudFunction('hygiene-inspection', {
          action: 'getInspectionRecords',
          data: {
            area_id: this.areaId,
            pageSize: 20
          }
        });

        if (result && result.success && result.data) {
          const records = result.data.list || [];
          
          // 为每个有问题的检查记录获取相关的整改评分
          const recordsWithRectificationRating = await Promise.all(
            records.map(async (record) => {
              if (record.has_issues && record.status === 'verified') {
                // 获取相关的整改记录评分
                try {
                  const rectificationResult = await callCloudFunction('hygiene-rectification', {
                    action: 'getRectifications',
                    data: {
                      inspection_record_id: record._id,
                      status: 'verified',
                      pageSize: 1
                    }
                  });
                  
                  if (rectificationResult && rectificationResult.success && 
                      rectificationResult.data && rectificationResult.data.list && 
                      rectificationResult.data.list.length > 0) {
                    const rectificationRecord = rectificationResult.data.list[0];
                    record.rectification_rating = rectificationRecord.final_rating || rectificationRecord.review_rating || 0;
                  }
                } catch (error) {
                  // 静默处理整改评分获取失败
                }
              }
              return record;
            })
          );
          
          this.inspectionHistory = recordsWithRectificationRating.map(record => {
            const inspectDate = new Date(record.inspection_date);
            const timeText = `${inspectDate.getMonth() + 1}月${inspectDate.getDate()}日 ${inspectDate.getHours().toString().padStart(2, '0')}:${inspectDate.getMinutes().toString().padStart(2, '0')}`;
            
            let subtitle = '';
            let result = 'passed';
            let icon = 'checkmarkempty';
            
            // 根据检查状态和是否有问题来确定显示内容
            if (record.has_issues) {
              // 有问题的情况
              if (record.status === 'verified') {
                result = 'completed';
                icon = 'checkmarkempty';
                subtitle = `发现问题已整改完成 · 整改质量${this.getRatingDescription(record.rectification_rating || record.overall_rating || record.score || 0).toLowerCase()}`;
              } else if (record.status === 'rectification_completed') {
                result = 'pending';
                icon = 'reload';
                subtitle = `整改已提交 · 等待确认`;
              } else if (record.status === 'pending_rectification') {
                result = 'issues';
                icon = 'info';
                subtitle = `发现问题 · ${record.issues && record.issues.length > 0 ? record.issues[0].description : '需要整改'}`;
              } else {
                result = 'issues';
                icon = 'info';
                subtitle = `发现问题 · 需要整改`;
              }
            } else {
              // 没有问题，检查通过
              result = 'passed';
              icon = 'checkmarkempty';
              subtitle = `检查通过 · 清理效果${this.getRatingDescription(record.rectification_rating || record.overall_rating || record.score || 0).toLowerCase()} · 无问题发现`;
            }

            return {
              id: record._id,
              inspectorName: `检查员：${record.inspector_name || '未知'}`,
              time: timeText,
              subtitle: subtitle,
              result: result,
              icon: icon,
              rating: record.rectification_rating || record.overall_rating || record.score || 0, // 优先显示整改评分
              inspection_date: record.inspection_date,
              status: record.status || 'unknown'
            };
          }).sort((a, b) => new Date(b.inspection_date) - new Date(a.inspection_date));
        } else {
          this.inspectionHistory = [];
        }
      } catch (error) {
        this.inspectionHistory = [];
      }
    },

    // 检查当前周或历史周记录
    async checkCurrentWeekRecord() {
      try {
        let weekStart, weekEnd;
        
        if (this.isHistoricalView && this.cleaningDate) {
          // 历史模式：查询指定清理日期所在周的记录
          const cleanDate = new Date(this.cleaningDate);
          const beijingCleanDate = this.toBeijingTime(cleanDate);
          weekStart = this.getWeekStart(beijingCleanDate);
          weekEnd = this.getWeekEnd(beijingCleanDate);
        } else {
          // 本周模式：查询当前周的记录
          const now = new Date();
          const beijingNow = this.toBeijingTime(now);
          weekStart = this.getWeekStart(beijingNow);
          weekEnd = this.getWeekEnd(beijingNow);
        }

        const result = await callCloudFunction('hygiene-cleaning', {
          action: 'getCleaningRecords',
          data: {
            area_id: this.areaId,
            start_date: weekStart.toISOString(),
            end_date: weekEnd.toISOString(),
            pageSize: 1
          }
        });

        if (result && result.success && result.data && result.data.list && result.data.list.length > 0) {
          const record = result.data.list[0];
          const submitDate = new Date(record.cleaning_date);
          const submitTime = `${submitDate.getMonth() + 1}月${submitDate.getDate()}日 ${submitDate.getHours().toString().padStart(2, '0')}:${submitDate.getMinutes().toString().padStart(2, '0')} 提交`;
          
          this.currentWeekRecord = {
            id: record._id,
            submitTime: submitTime,
            photos: record.photos || []
          };
        } else {
          this.currentWeekRecord = null;
        }
        
        // 更新清理权限
        if (this.areaInfo) {
          this.areaInfo.canClean = !this.currentWeekRecord;
        }
      } catch (error) {
        this.currentWeekRecord = null;
      }
    },

    // 获取区域类型文本
    getAreaTypeText(type) {
      const typeMap = {
        'fixed': '固定责任区',
        'public': '公共责任区'
      };
      return typeMap[type] || '责任区';
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'completed': '已完成',
        'pending': '待清理',
        'overdue': '已逾期'
      };
      return statusMap[status] || '未知';
    },

    // 获取截止时间文本（动态计算）
    getWeekDeadlineText() {
      const now = new Date();
      const beijingNow = this.toBeijingTime(now);
      const weekEnd = this.getWeekEnd(beijingNow);
      const timeDiff = weekEnd.getTime() - now.getTime();
      const daysLeft = Math.ceil(timeDiff / (24 * 60 * 60 * 1000));
      
      if (daysLeft <= 0) {
        return '已超过截止时间';
      } else if (daysLeft === 1) {
        return '距离本周截止还有1天';
      } else {
        return `距离本周截止还有${daysLeft}天`;
      }
    },

    // 获取图标颜色
    getIconColor(status) {
      const colorMap = {
        'completed': '#34C759',
        'pending': '#FF9500',
        'overdue': '#FF3B30',
        'passed': '#34C759',
        'issues': '#FF3B30'
      };
      return colorMap[status] || '#8E8E93';
    },

    // 跳转到清理页面
    goToCleaning() {
      uni.navigateTo({
        url: `/pages/6s_pkg/cleaning-upload?areaId=${this.areaId}&type=${this.areaType}`
      });
    },

    // 查看本周照片
    viewWeekPhotos() {
      if (this.currentWeekRecord && this.currentWeekRecord.photos.length > 0) {
        const urls = this.currentWeekRecord.photos.map(photo => photo.url);
        uni.previewImage({
          urls: urls,
          current: urls[0]
        });
      }
    },

    // 查看记录详情
    viewRecordDetail(record) {
      uni.navigateTo({
        url: `/pages/6s_pkg/record-detail?id=${record.id}&type=cleaning`
      });
    },

    // 查看检查详情
    viewInspectionDetail(record) {
      // 如果是漏检查记录，显示提示而不是跳转
      if (record.result === 'overdue' && record.id.startsWith('missed_')) {
        uni.showToast({
          title: '检查员未检查此记录',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
      // 如果是待检查记录，显示提示而不是跳转
      if (record.result === 'pending' && record.id === 'current_week_status') {
        uni.showToast({
          title: '检查员还未检查此记录',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
      uni.navigateTo({
        url: `/pages/6s_pkg/record-detail?id=${record.id}&type=inspection`
      });
    },

    // 显示时间筛选
    showTimeFilter() {
      const timeOptions = ['近1个月', '近3个月', '近6个月', '近1年'];
      uni.showActionSheet({
        itemList: timeOptions,
        success: (res) => {
          this.selectedTimeFilter = timeOptions[res.tapIndex];
          this.loadCleaningHistory(); // 重新加载数据
        }
      });
    },

    // 切换月份展开/折叠
    toggleMonth(monthKey) {
      const index = this.expandedMonths.indexOf(monthKey);
      if (index > -1) {
        // 已展开，则折叠
        this.expandedMonths.splice(index, 1);
      } else {
        // 未展开，则展开
        this.expandedMonths.push(monthKey);
      }
    },

    // 获取评分描述
    getRatingDescription(rating) {
      if (rating === 0) return '请评分';
      else if (rating <= 1) return '较差';
      else if (rating <= 2) return '一般';
      else if (rating <= 3) return '良好';
      else if (rating < 5) return '优秀';  // 4-4.5分都是优秀
      else if (rating === 5) return '完美';
      else return '良好'; // 默认值
    },

    // 获取状态指示器信息（用于评分区域右侧显示）
    getStatusIndicator(result, status) {
      const indicators = {
        'passed': { text: '✓', color: '#34C759', desc: '检查通过' },
        'completed': { text: '✓', color: '#34C759', desc: '整改完成' },
        'pending': { text: '○', color: '#FF9500', desc: '待检查' },
        'issues': { text: '!', color: '#FF3B30', desc: '发现问题' },
        'overdue': { text: '⚠', color: '#FF3B30', desc: '漏检查' }
      };
      
      return indicators[result] || indicators['issues']; // 默认返回问题状态
    },

    // 检查本周清理记录的检查状态（待检查/漏检查）
    async checkCurrentWeekInspectionStatus() {
      try {
        // 只处理本周有清理记录但无对应检查记录的情况
        if (!this.currentWeekRecord) return;
        
        // 等待历史数据加载完成
        await new Promise(resolve => {
          const checkInterval = setInterval(() => {
            if (this.historyDataLoaded) {
              clearInterval(checkInterval);
              resolve();
            }
          }, 100);
          
          // 超时保护
          setTimeout(() => {
            clearInterval(checkInterval);
            resolve();
          }, 5000);
        });
        
        // 获取本周清理记录的实际时间
        const currentCleaningRecord = this.cleaningHistory.find(record => record.id === this.currentWeekRecord.id);
        if (!currentCleaningRecord) return;
        
        const cleaningDate = new Date(currentCleaningRecord.submitDate);
        
        // 检查是否已有对应的检查记录或者已有本周状态记录
        const hasInspectionRecord = this.inspectionHistory.some(record => 
          (!record.id.startsWith('missed_') && !record.id.startsWith('current_week_status') && 
           new Date(record.inspection_date) >= cleaningDate) ||
          record.id === 'current_week_status' // 避免重复添加本周状态
        );
        
        // 如果已有检查记录，不需要显示待检查/漏检查
        if (hasInspectionRecord) return;
        
        // 判断是否超过截止时间
        const isOverdue = this.isCurrentWeekOverdue(cleaningDate);
        
        // 创建待检查或漏检查记录
        const inspectionStatus = {
          id: 'current_week_status',
          inspectorName: isOverdue ? '检查员漏检查' : '待检查',
          time: `${cleaningDate.getMonth() + 1}月${cleaningDate.getDate()}日清理`,
          subtitle: isOverdue ? '清理记录已提交，但检查员未及时检查' : '清理记录已提交，等待检查员检查',
          result: isOverdue ? 'overdue' : 'pending',
          icon: 'info',
          rating: 0,
          inspection_date: currentCleaningRecord.submitDate,
          status: isOverdue ? 'missed' : 'pending',
          statusIndicator: isOverdue ? 
            { text: '⚠', color: '#FF3B30', desc: '漏检查' } : 
            { text: '○', color: '#FF9500', desc: '待检查' }
        };
        
        // 添加到检查历史的最前面，但要确保不与已有的漏检查记录冲突
        this.inspectionHistory.unshift(inspectionStatus);
        // 重新排序
        this.inspectionHistory.sort((a, b) => new Date(b.inspection_date) - new Date(a.inspection_date));
        
      } catch (error) {
        console.warn('检查本周清理状态失败:', error);
      }
    },

    // 检查是否有遗漏的检查记录
    async checkMissedInspections() {
      try {
        // 等待历史数据加载完成
        await new Promise(resolve => {
          const checkInterval = setInterval(() => {
            if (this.historyDataLoaded) {
              clearInterval(checkInterval);
              resolve();
            }
          }, 100);
          
          // 超时保护
          setTimeout(() => {
            clearInterval(checkInterval);
            resolve();
          }, 5000);
        });
        
        // 检查最近4周的清理记录是否有对应的检查记录
        const now = new Date();
        const fourWeeksAgo = new Date(now.getTime() - 28 * 24 * 60 * 60 * 1000);
        
        // 获取清理记录
        const cleaningRecords = this.cleaningHistory.filter(record => {
          const recordDate = new Date(record.submitDate);
          return recordDate > fourWeeksAgo;
        });
        
        // 获取检查记录（排除已有的漏检查记录）
        const inspectionRecords = this.inspectionHistory.filter(record => !record.id.startsWith('missed_'));
        
        // 找出没有对应检查记录的清理记录
        const missedInspections = [];
        
        cleaningRecords.forEach(cleanRecord => {
          const cleanDate = new Date(cleanRecord.submitDate);
          
          // 查找该清理记录后7天内的检查记录
          const hasInspection = inspectionRecords.some(inspectRecord => {
            const inspectDate = new Date(inspectRecord.inspection_date);
            const timeDiff = inspectDate.getTime() - cleanDate.getTime();
            // 检查是否在清理后7天内有检查
            return timeDiff >= 0 && timeDiff <= 7 * 24 * 60 * 60 * 1000;
          });
          
          // 只有完全没有检查记录且超过截止时间的才算漏检查
          if (!hasInspection && this.isHistoricalRecordOverdue(cleanDate)) {
            missedInspections.push({
              id: 'missed_' + cleanRecord.id,
              inspectorName: '检查员漏检查',
              time: `${cleanDate.getMonth() + 1}月${cleanDate.getDate()}日清理`,
              subtitle: '清理记录已提交，但检查员未及时检查',
              result: 'overdue',
              icon: 'info',
              rating: 0,
              inspection_date: cleanRecord.submitDate,
              status: 'missed',
              statusIndicator: { text: '⚠', color: '#FF3B30', desc: '漏检查' }
            });
          }
        });
        
        // 将遗漏的检查记录插入检查历史
        if (missedInspections.length > 0) {
          this.inspectionHistory = [...missedInspections, ...this.inspectionHistory]
            .sort((a, b) => new Date(b.inspection_date) - new Date(a.inspection_date));
        }
        
      } catch (error) {
        // 遗漏检查检测失败不影响主要功能
        console.warn('检查遗漏检查记录失败:', error);
      }
    },

    // 判断历史记录是否超过截止时间（用于历史漏检查检测，严格按截止时间）
    isHistoricalRecordOverdue(cleanDate) {
      const now = new Date();
      
      if (this.areaInfo.type === 'public' && this.areaInfo.scheduledDay !== undefined) {
        // 公共责任区：严格按指定日期，当天23:59:59为截止
        const cleanDateBeijing = this.toBeijingTime(cleanDate);
        const weekStart = this.getWeekStart(cleanDateBeijing);
        const scheduledDate = new Date(weekStart);
        
        if (this.areaInfo.scheduledDay === 0) {
          scheduledDate.setDate(weekStart.getDate() + 6); // 周日
        } else {
          scheduledDate.setDate(weekStart.getDate() + this.areaInfo.scheduledDay - 1);
        }
        scheduledDate.setHours(23, 59, 59, 999);
        
        return now > scheduledDate;
      } else {
        // 固定责任区：本周截止，周日23:59:59为截止
        const cleanDateBeijing = this.toBeijingTime(cleanDate);
        const weekEnd = this.getWeekEnd(cleanDateBeijing);
        return now > weekEnd;
      }
    },

    // 判断本周清理记录是否超时（根据责任区类型严格判断）
    isCurrentWeekOverdue(cleanDate) {
      const now = new Date();
      
      if (this.areaInfo.type === 'public' && this.areaInfo.scheduledDay !== undefined) {
        // 公共责任区：严格按指定日期，当天23:59:59为截止
        const cleanDateBeijing = this.toBeijingTime(cleanDate);
        const weekStart = this.getWeekStart(cleanDateBeijing);
        const scheduledDate = new Date(weekStart);
        
        if (this.areaInfo.scheduledDay === 0) {
          scheduledDate.setDate(weekStart.getDate() + 6); // 周日
        } else {
          scheduledDate.setDate(weekStart.getDate() + this.areaInfo.scheduledDay - 1);
        }
        scheduledDate.setHours(23, 59, 59, 999);
        
        return now > scheduledDate;
      } else {
        // 固定责任区：本周截止，周日23:59:59为截止
        const cleanDateBeijing = this.toBeijingTime(cleanDate);
        const weekEnd = this.getWeekEnd(cleanDateBeijing);
        return now > weekEnd;
      }
    },

    // 处理记录更新事件
    handleRecordUpdated(data) {
      // 如果更新的是当前责任区的记录，重新加载相关数据
      if (data.areaId === this.areaId) {
        // 重新检查本周记录状态
        this.checkCurrentWeekRecord();
        
        // 重新加载历史记录
        if (data.mode === 'cleaning' || data.recordId) {
          this.loadCleaningHistory();
        }
        
        // 如果是检查记录更新，也需要重新加载检查历史
        if (data.isPassed !== undefined) {
          this.loadInspectionHistory();
        }
        
        // 重新检查检查记录状态（先历史后本周，避免冲突）
        this.checkMissedInspections().then(() => {
          this.checkCurrentWeekInspectionStatus();
        });
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
	min-height: 100vh;
	background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
	width: 100%;
	overflow-x: hidden; /* 防止水平滚动 */
	box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.header-content {
  flex: 1;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}

.status-badge {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  
  &.status-completed {
    background: rgba(52, 199, 89, 0.2);
    color: #34C759;
    border: 1rpx solid rgba(52, 199, 89, 0.3);
  }
  
  &.status-pending {
    background: rgba(255, 149, 0, 0.2);
    color: #FF9500;
    border: 1rpx solid rgba(255, 149, 0, 0.3);
  }
  
  &.status-overdue {
    background: rgba(255, 59, 48, 0.2);
    color: #FF3B30;
    border: 1rpx solid rgba(255, 59, 48, 0.3);
  }
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 16rpx;
  margin: 24rpx 32rpx 0 32rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 32rpx 32rpx 16rpx 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  flex: 1;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 4rpx;
}

.card-subtitle {
  font-size: 26rpx;
  color: #8E8E93;
}

.card-body {
  padding: 32rpx;
}

/* 信息列表 */
.info-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  flex: 1;
}

.info-value {
  font-size: 28rpx;
  color: #8E8E93;
  text-align: right;
}

/* 描述部分 */
.description-section {
  margin-top: 32rpx;
  padding-top: 32rpx;
  border-top: 1rpx solid #F2F2F7;
}

.description-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 12rpx;
}

.description-text {
  font-size: 26rpx;
  color: #8E8E93;
  line-height: 1.5;
}

/* 本周状态 */
.week-status {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-radius: 12rpx;
  
  &.completed {
    background: #E8F5E8;
    border: 1rpx solid #34C759;
  }
  
  &.pending {
    background: #FFF4E6;
    border: 1rpx solid #FF9500;
  }
}

.status-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.status-content {
  flex: 1;
}

.status-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 4rpx;
}

.status-desc {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
}

.status-photos {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #007AFF;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;
  padding: 32rpx;
  text-align: center;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-number {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: #1C1C1E;
  
  &.success {
    color: #34C759;
  }
  
  &.warning {
    color: #FF9500;
  }
  
  &.info {
    color: #007AFF;
  }
  
  &.danger {
    color: #FF3B30;
  }
  
  &.primary {
    color: #007AFF;
  }
}

.stats-label {
  font-size: 22rpx;
  color: #8E8E93;
}

/* 操作按钮 */
.action-section {
  padding: 32rpx;
}

.action-btn {
  width: 100%;
  height: 96rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  
  &.primary {
    background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
    color: white;
  }
  
  &:active {
    transform: scale(0.98);
  }
}

/* 时间筛选 */
.time-filter {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #007AFF;
}

/* 历史记录列表 */
.history-list {
  padding-bottom: 16rpx;
}

/* 月份分组 */
.month-group {
  margin-bottom: 16rpx;
}

.month-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  background: #F8F9FA;
  border-bottom: 1rpx solid #E5E5EA;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:active {
    background: #E5E5EA;
  }
}

.month-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.month-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
}

.month-count {
  font-size: 24rpx;
  color: #8E8E93;
}

.month-records {
  background: white;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
  
  &:last-child {
    border-bottom: none;
  }
}

.history-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  
  &.icon-bg-completed,
  &.icon-bg-passed {
    background: rgba(52, 199, 89, 0.1);
  }
  
  &.icon-bg-pending {
    background: rgba(255, 149, 0, 0.1);
  }
  
  &.icon-bg-issues {
    background: rgba(255, 59, 48, 0.1);
  }
  
  &.icon-bg-overdue {
    background: rgba(255, 59, 48, 0.1);
  }
}

.history-content {
  flex: 1;
}

.history-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 4rpx;
}

.history-subtitle {
  font-size: 24rpx;
  color: #8E8E93;
}

.history-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.photo-count {
  font-size: 24rpx;
  color: #8E8E93;
}

.status-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rpx;
  margin-right: 12rpx;
}

.indicator-text {
  font-size: 20rpx;
  font-weight: 600;
}

.indicator-desc {
  font-size: 20rpx;
  opacity: 0.8;
}

.rating-stars {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 24rpx;
  color: #FFD700;
}

/* 底部安全间距 */
.bottom-safe-area {
  height: 40rpx;
}

/* 加载状态 */
.loading-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
  text-align: center;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #E5E7EB;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.error-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
}

/* 响应式调整 */
@media (max-width: 414px) {
  .page-header {
    padding: 24rpx 16rpx;
  }
  
  .card {
    margin: 24rpx 16rpx 0 16rpx;
  }
  
  .action-section {
    padding: 24rpx 16rpx;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;
  }
}


</style>