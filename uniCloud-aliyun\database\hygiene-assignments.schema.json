{"bsonType": "object", "required": ["user_id", "area_id", "assignment_type"], "permission": {"read": true, "create": "auth.role.includes('admin') || auth.role.includes('GM') || auth.role.includes('Integrated') || auth.role.includes('reviser')", "update": "auth.role.includes('admin') || auth.role.includes('GM') || auth.role.includes('Integrated') || auth.role.includes('reviser')", "delete": "auth.role.includes('admin')"}, "properties": {"_id": {"description": "ID，系统自动生成"}, "user_id": {"bsonType": "string", "title": "用户ID", "description": "被分配的用户ID", "foreignKey": "uni-id-users._id", "errorMessage": {"required": "用户ID不能为空"}}, "user_name": {"bsonType": "string", "title": "用户姓名", "description": "冗余存储的用户姓名", "maxLength": 50}, "area_id": {"bsonType": "string", "title": "责任区ID", "description": "分配的责任区ID", "foreignKey": "hygiene-areas._id", "errorMessage": {"required": "责任区ID不能为空"}}, "area_name": {"bsonType": "string", "title": "责任区名称", "description": "冗余存储的责任区名称", "maxLength": 50}, "assignment_type": {"bsonType": "string", "title": "分配类型", "description": "分配的类型", "enum": ["primary", "secondary", "backup", "temporary"], "errorMessage": {"required": "分配类型不能为空", "enum": "请选择有效的分配类型"}}, "role": {"bsonType": "string", "title": "角色", "description": "在该责任区的角色", "enum": ["cleaner", "inspector", "supervisor", "coordinator"], "default": "cleaner"}, "start_date": {"bsonType": "timestamp", "title": "开始日期", "description": "分配生效的开始日期"}, "end_date": {"bsonType": "timestamp", "title": "结束日期", "description": "分配结束的日期（空表示长期有效）"}, "is_active": {"bsonType": "bool", "title": "是否有效", "description": "该分配是否当前有效", "default": true}, "assigned_by": {"bsonType": "string", "title": "分配人", "description": "执行分配操作的用户ID", "foreignKey": "uni-id-users._id"}, "assigned_by_name": {"bsonType": "string", "title": "分配人姓名", "description": "冗余存储的分配人姓名", "maxLength": 50}, "assignment_reason": {"bsonType": "string", "title": "分配原因", "description": "分配的原因说明", "maxLength": 300}, "responsibilities": {"bsonType": "array", "title": "具体职责", "description": "在该责任区的具体职责列表", "items": {"bsonType": "string", "maxLength": 200}, "maxItems": 10}, "work_schedule": {"bsonType": "object", "title": "工作安排", "description": "具体的工作时间安排", "properties": {"days_of_week": {"bsonType": "array", "title": "工作日", "description": "一周中的工作日（1-7表示周一到周日）", "items": {"bsonType": "number", "minimum": 1, "maximum": 7}, "maxItems": 7}, "start_time": {"bsonType": "string", "title": "开始时间", "description": "每日工作开始时间，格式HH:MM", "pattern": "^\\d{2}:\\d{2}$"}, "end_time": {"bsonType": "string", "title": "结束时间", "description": "每日工作结束时间，格式HH:MM", "pattern": "^\\d{2}:\\d{2}$"}, "frequency": {"bsonType": "string", "title": "频率", "enum": ["daily", "weekly", "monthly", "as_needed"]}}}, "performance_metrics": {"bsonType": "object", "title": "绩效指标", "description": "该员工在此责任区的绩效统计", "properties": {"total_cleanings": {"bsonType": "number", "title": "总清洁次数", "minimum": 0, "default": 0}, "on_time_rate": {"bsonType": "number", "title": "按时完成率", "minimum": 0, "maximum": 100, "default": 0}, "quality_score": {"bsonType": "number", "title": "质量评分", "minimum": 0, "maximum": 10, "default": 0}, "last_performance_update": {"bsonType": "timestamp", "title": "最后更新时间"}}}, "contact_info": {"bsonType": "object", "title": "联系信息", "description": "该分配相关的联系信息", "properties": {"phone": {"bsonType": "string", "title": "联系电话", "maxLength": 20}, "email": {"bsonType": "string", "title": "邮箱", "maxLength": 100}, "emergency_contact": {"bsonType": "string", "title": "紧急联系人", "maxLength": 50}, "emergency_phone": {"bsonType": "string", "title": "紧急联系电话", "maxLength": 20}}}, "notes": {"bsonType": "string", "title": "备注", "description": "其他备注信息", "maxLength": 500}, "created_at": {"bsonType": "timestamp", "title": "创建时间", "description": "记录创建时间", "forceDefaultValue": {"$env": "now"}}, "updated_at": {"bsonType": "timestamp", "title": "更新时间", "description": "记录最后更新时间", "forceDefaultValue": {"$env": "now"}}}}