<template>
  <view 
    class="p-task-card" 
    :class="{
      'p-task-card--active': isActive, 
      'p-task-card--expired': isExpired,
      'p-task-card--completed': isCompleted,
      'p-task-card--selected': active
    }"
    :active="active ? true : false"
    @click="onCardClick"
  >
    <!-- 状态标识和标题 -->
    <view class="p-task-card__header">
      <view class="p-task-card__title-container">
        <text class="p-task-card__title">{{ task.name || task.route_name || '未命名任务' }}</text>
      </view>
      
      <!-- 操作按钮 - 置于右上角，提高点击区域 -->
      <view class="p-task-card__quick-actions">
        <text class="p-task-card__status" :class="{
          'p-task-card__status--pending': statusClass === 'pending',
          'p-task-card__status--active': statusClass === 'active',
          'p-task-card__status--completed': statusClass === 'completed',
          'p-task-card__status--expired': statusClass === 'expired',
          'p-task-card__status--canceled': statusClass === 'canceled'
        }">
          {{ statusText }}
        </text>
        <view 
          v-if="task.status !== 4" 
          class="p-task-card__view-btn" 
          @click.stop="onViewDetail"
        >
          <uni-icons type="info" size="18" color="#1677FF"></uni-icons>
        </view>
      </view>
    </view>
    
    <!-- 班次和时间信息 - 使用图标提升视觉识别 -->
    <view class="p-task-card__info-group">
      <!-- 第一行：区域/路线信息和班次信息 -->
      <view class="p-task-card__info-row">
        <!-- 区域/路线信息 -->
        <view class="p-task-card__info-item">
          <view class="p-task-card__info-icon">
            <uni-icons type="location-filled" size="20" color="#1677FF"></uni-icons>
          </view>
          <view class="p-task-card__info-content">
          <text class="p-task-card__label">线路区域:</text>
          <text class="p-task-card__value">{{ task.area || task.route_name || '未指定区域' }}</text>
          </view>
        </view>
        
        <!-- 班次信息 -->
        <view class="p-task-card__info-item">
          <view class="p-task-card__info-icon">
            <uni-icons type="flag-filled" size="20" color="#1677FF"></uni-icons>
          </view>
          <view class="p-task-card__info-content">
          <text class="p-task-card__label">执行班次:</text>
          <text class="p-task-card__value">
            {{ task.shift_name || (shift ? shift.name : '未指定班次') }}
            <text v-if="isAcrossDay" class="p-task-card__tag">跨天</text>
          </text>
          </view>
        </view>
      </view>
      
      <!-- 第二行：执行人员和执行时间 -->
      <view class="p-task-card__info-row">
        <!-- 执行人员信息 -->
        <view class="p-task-card__info-item">
          <view class="p-task-card__info-icon">
            <uni-icons type="person-filled" size="20" color="#1677FF"></uni-icons>
          </view>
          <view class="p-task-card__info-content">
          <text class="p-task-card__label">执行人员:</text>
          <text class="p-task-card__value">{{ getUserName() }}</text>
          </view>
        </view>
        
        <!-- 执行时间信息 -->
        <view class="p-task-card__info-item">
          <view class="p-task-card__info-icon">
            <uni-icons type="calendar-filled" size="20" color="#1677FF"></uni-icons>
          </view>
          <view class="p-task-card__info-content">
          <text class="p-task-card__label">执行时间:</text>
            <text class="p-task-card__value">{{ task.patrol_date || formatDate(task.create_date, 'YYYY-MM-DD') }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 信息组与轮次信息之间的分隔线 -->
    <view class="p-task-card__divider"></view>
    
    <!-- 轮次信息 - 突出显示，增加未打卡统计 -->
    <view v-if="hasRounds" class="p-task-card__round-info">
      <view class="p-task-card__round-header">
        <uni-icons type="loop" size="18" color="#1677FF"></uni-icons>
        <text class="p-task-card__round-title">轮次信息</text>
      </view>
      
      <!-- 轮次概要信息 -->
      <view class="p-task-card__round-summary">
        <text class="p-task-card__round-summary-text">
          共{{ task.rounds_detail && task.rounds_detail.length > 0 ? task.rounds_detail.length : 0 }}轮巡检
        </text>
        <text v-if="task.status === 2" class="p-task-card__round-summary-text p-task-card__round-current--completed">
          所有轮次已完成
        </text>
        <text v-else-if="getCurrentActiveRound()" class="p-task-card__round-current">
          当前第{{ getCurrentActiveRound().round || '?' }}轮
        </text>
        <text v-else-if="getNextRound()" class="p-task-card__round-current">
          等待第{{ getNextRound().round || '?' }}轮开始
        </text>
      </view>
      
      <!-- 所有轮次状态列表 -->
      <view class="p-task-card__rounds-list">
        <!-- 如果没有轮次数据，手动创建空白提示 -->
        <template v-if="!task.rounds_detail || task.rounds_detail.length === 0">
          <view class="p-task-card__round-item">
            <text class="p-task-card__empty-rounds">暂无轮次数据</text>
          </view>
        </template>
        
        <!-- 如果有轮次数据，正常展示 -->
        <template v-else>
          <view 
            v-for="(round, index) in sortedRounds" 
            :key="index" 
            class="p-task-card__round-item"
            :class="{
              'p-task-card__round-item--active': round.status === 1,
              'p-task-card__round-item--completed': round.status === 2 || task.status === 2,
              'p-task-card__round-item--expired': round.status === 3,
              'p-task-card__round-item--waiting': round.status === 0,
              'p-task-card__round-item--selected': isRoundSelected(round)
            }"
            @click.stop="onRoundClick(round, index)"
          >
            <view class="p-task-card__round-item-header">
              <text class="p-task-card__round-number">{{ round.name || `第${round.round}轮` }}</text>
              <text v-if="round.stats && round.stats.missed_points > 0" class="p-task-card__round-number-missed">缺{{ round.stats.missed_points }}张打卡</text>
              <text 
                class="p-task-card__round-status-badge"
                :class="{
                  'p-task-card__round-status-badge--active': round.status === 1,
                  'p-task-card__round-status-badge--completed': round.status === 2 || task.status === 2,
                  'p-task-card__round-status-badge--expired': round.status === 3,
                  'p-task-card__round-status-badge--waiting': round.status === 0
                }"
              >{{ getRoundStatusText(round) }}</text>
            </view>
            
            <view class="p-task-card__round-item-time">
              <!-- 倒计时显示 -->
              <view v-if="round.status === 1" class="p-task-card__round-countdown">
                <text>剩余{{ formatSimpleCountdown(getRoundTimeRemaining(round)) }}</text>
              </view>
              
              <!-- 等待开始倒计时 -->
              <view v-if="round.status === 0" class="p-task-card__round-countdown">
                <text>{{ formatSimpleCountdown(getRoundTimeUntilStart(round)) }}后开始</text>
              </view>
            </view>
          </view>
        </template>
      </view>
      
      <!-- 实时状态 -->
      <view class="p-task-card__round-status">
        <!-- 任务已完成 -->
        <view v-if="task.status === 2" class="p-task-card__completed-rounds">
          <view class="p-task-card__status-row">
            <text class="p-task-card__round-badge" :class="{
              'p-task-card__round-badge--completed': true
            }">
              所有轮次已完成
            </text>
            
            <!-- 完成情况摘要 -->
            <text v-if="getTotalMissedPoints() > 0" class="p-task-card__missed-badge">
              共缺{{ getTotalMissedPoints() }}张打卡
            </text>
          </view>
        </view>
        
        <!-- 任务已取消 -->
        <view v-else-if="task.status === 4" class="p-task-card__canceled-rounds">
          <view class="p-task-card__status-row">
            <text class="p-task-card__round-badge" :class="{
              'p-task-card__round-badge--canceled': true
            }">
              任务已取消
            </text>
          </view>
        </view>
        
        <!-- 任务已超时 -->
        <view v-else-if="task.status === 3" class="p-task-card__expired-rounds">
          <view class="p-task-card__status-row">
            <text class="p-task-card__round-badge" :class="{
              'p-task-card__round-badge--expired': true
            }">
              任务已超时
            </text>
            <text v-if="getTotalMissedPoints() > 0" class="p-task-card__missed-badge">
              共缺{{ getTotalMissedPoints() }}张打卡
            </text>
          </view>
        </view>
        
        <!-- 进行中且有活跃轮次 -->
        <view v-else-if="task.status === 1 && hasActiveRound" class="p-task-card__active-task">
          <view class="p-task-card__status-row">
            <text class="p-task-card__round-badge" :class="{
              'p-task-card__round-badge--active': true
            }">
              正在进行第{{ getCurrentRoundNumber() }}轮巡检
            </text>
            
            <text v-if="getCurrentRoundMissedPoints() > 0" class="p-task-card__missed-badge">
              当前轮次缺{{ getCurrentRoundMissedPoints() }}张打卡
            </text>
          </view>
        </view>
        
        <!-- 等待开始状态(包括未开始和进行中但轮次未开始) -->
        <view v-else class="p-task-card__other-status">
          <view class="p-task-card__status-row">
            <text class="p-task-card__round-badge" :class="{'p-task-card__round-badge--waiting': true}">
              等待第{{ getNextRoundNumber() }}轮开始
            </text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 轮次信息与进度条之间的分隔线 -->
    <view v-if="hasRounds" class="p-task-card__divider"></view>
    
    <!-- 进度条 - 更直观的视觉设计 -->
    <view class="p-task-card__progress-container">
      <view class="p-task-card__progress-header">
        <text class="p-task-card__progress-label">巡视进度</text>
        <text class="p-task-card__progress-text">
          {{ formatProgress(progressRate) }}
        </text>
      </view>
      
      <view class="p-task-card__progress-bar">
        <view 
          class="p-task-card__progress-inner" 
          :class="{'p-task-card__progress-inner--completed': isCompleted}"
          :style="{
            width: progressRate * 100 + '%'
          }" 
        ></view>
      </view>
      
      <view class="p-task-card__points-info">
        <text class="p-task-card__points-completed">已巡视 {{ completedPoints }} 个点位</text>
        <text class="p-task-card__points-total">共 {{ totalPoints }} 个点位</text>
      </view>
    </view>
    
    <!-- 进度条与底部按钮之间的分隔线 -->
    <view class="p-task-card__divider p-task-card__divider--bottom"></view>
    
    <!-- 底部操作按钮 - 更醒目的主操作按钮 -->
    <view class="p-task-card__actions">
      <view v-if="canContinue && canStartPatrol" class="p-task-card__action-primary">
        <button 
          class="p-task-card__btn p-task-card__btn--primary"
          @click.stop="onContinueTask"
        >
          <uni-icons type="checkmarkempty" size="18" color="#FFFFFF"></uni-icons>
          {{ getCurrentActiveRound() ? '继续巡检' : '开始巡检' }}
        </button>
      </view>
      
      <view v-else-if="canContinue && !canStartPatrol" class="p-task-card__action-waiting">
        <text class="p-task-card__waiting-text">等待开始时间</text>
        <button 
          class="p-task-card__btn p-task-card__btn--disabled"
          disabled
        >
          {{ getCurrentActiveRound() ? '继续巡检' : '开始巡检' }}
        </button>
      </view>
      
      <view v-else-if="isExpired" class="p-task-card__action-expired">
        <text class="p-task-card__expired-text">班次已结束</text>
        <button 
          class="p-task-card__btn p-task-card__btn--secondary"
          @click.stop="onViewDetail"
        >
          查看详情
        </button>
      </view>
      
      <view v-else-if="isCompleted" class="p-task-card__action-completed">
        <text class="p-task-card__completed-text">任务已完成</text>
        <button 
          class="p-task-card__btn p-task-card__btn--secondary"
          @click.stop="onViewDetail"
        >
          查看详情
        </button>
      </view>
    </view>
  </view>
</template>

<script>
/**
 * 任务卡片组件
 * 显示巡视任务信息和状态
 * 依赖父组件提供:
 * 1. 任务数据(task)
 * 2. 完整的用户映射(userMap)
 * 3. 可选的实时轮次信息(roundInfo) - 若不提供，组件将自行计算时间
 */
// 导入日期工具函数
import { formatDate, calculateRoundTime, isToday, calculateEndTime } from '@/utils/date.js';

export default {
  name: 'p-task-card',
  props: {
    task: {
      type: Object,
      required: true
    },
    userMap: {
      type: Object,
      default: () => ({})
    },
    shift: {
      type: Object,
      default: null
    },
    roundInfo: {
      type: Object,
      default: null
    },
    active: {
      type: Boolean,
      default: false
    },
    // 新增：当前选中的轮次
    selectedRoundNumber: {
      type: Number,
      default: -1
    }
  },
  data() {
    return {
      // 简化定时器
      countdownTimer: null,
      // 当前时间（用于计算倒计时）
      currentTime: new Date(),
      // 防抖：记录已经触发过刷新的轮次
      refreshedRounds: new Set(),
      // 缓存倒计时结果，减少闪烁
      countdownCache: {},
      // 🔥 新增：事件节流机制
      eventThrottle: {},
      lastEmitTime: {}
    }
  },
  computed: {
    // 状态样式类 - 直接使用任务状态，不再重新计算
    statusClass() {
      const status = this.task ? this.task.status : 0;
      if (status === 0) return 'pending';
      if (status === 1) return 'active';
      if (status === 2) return 'completed';
      if (status === 3) return 'expired';
      if (status === 4) return 'canceled';
      return 'pending';
    },
    
    // 状态文本 - 直接使用任务状态，不再重新计算
    statusText() {
      const status = this.task ? this.task.status : 0;
      if (status === 0) return '未开始';
      if (status === 1) return '进行中';
      if (status === 2) return '已完成';
      if (status === 3) return '已超时';
      if (status === 4) return '已取消';
      return '未开始';
    },
    
    // 是否处于激活状态 - 直接使用任务状态
    isActive() {
      return this.task && this.task.status === 1;
    },
    
    // 是否已超时 - 直接使用任务状态
    isExpired() {
      return this.task && this.task.status === 3;
    },
    
    // 是否已完成 - 直接使用任务状态
    isCompleted() {
      return this.task && this.task.status === 2;
    },
    
    // 是否可以继续执行 - 简化逻辑
    canContinue() {
      return this.task && (this.task.status === 0 || this.task.status === 1);
    },
    
    // 是否可以开始巡视 - 简化逻辑，依赖父组件传入的状态
    canStartPatrol() {
      if (!this.task) return false;
      return this.task.status === 1 || this.task.status === 0;
    },
    
    // 已完成点位数量
    completedPoints() {
      // 优先使用overall_stats
      if (this.task && this.task.overall_stats && this.task.overall_stats.completed_points !== undefined) {
        return this.task.overall_stats.completed_points;
      }
      return 0;
    },
    
    // 总点位数量
    totalPoints() {
      // 优先使用overall_stats
      if (this.task && this.task.overall_stats && this.task.overall_stats.total_points !== undefined) {
        return this.task.overall_stats.total_points;
      }
      return 0;
    },
    
    // 进度比例计算属性
    progressRate() {
      // 1. 优先使用overall_stats的completion_rate
      if (this.task && this.task.overall_stats && this.task.overall_stats.completion_rate !== undefined) {
        // 确保completion_rate是有效数值且在0-1范围内
        const rate = parseFloat(this.task.overall_stats.completion_rate);
        if (!isNaN(rate)) {
          return rate > 1 ? rate / 100 : rate; // 处理可能的百分比值
        }
      }
      
      // 2. 其次使用task的completion_rate
      if (this.task && this.task.completion_rate !== undefined) {
        // 确保completion_rate是有效数值且在0-1范围内
        const rate = parseFloat(this.task.completion_rate);
        if (!isNaN(rate)) {
          return rate > 1 ? rate / 100 : rate; // 处理可能的百分比值
        }
      }
      
      // 3. 最后计算比例，确保完成点位数大于0且总点位数大于0
      if (this.completedPoints > 0 && this.totalPoints > 0) {
        return this.completedPoints / this.totalPoints;
      }
      
      return 0; // 默认返回0表示无进度
    },
    
    // 是否为通宵/跨天班次
    isAcrossDay() {
      // 1. 首先检查task.shift_detail中的across_day属性
      if (this.task && this.task.shift_detail && this.task.shift_detail.across_day === true) {
        return true;
      }
      
      // 2. 如果直接从shift中获取时间
      if (this.shift && this.shift.start_time && this.shift.end_time) {
        try {
          // 如果开始时间大于结束时间，说明是跨天班次
          const startTime = this.shift.start_time;
          const endTime = this.shift.end_time;
          
          // 解析纯时间格式，例如 "18:00"
          const parseTimeStr = (timeStr) => {
            if (!timeStr || !timeStr.includes(':')) return 0;
            const [hours, minutes] = timeStr.split(':').map(Number);
            return hours * 60 + minutes; // 转换为分钟数便于比较
          };
          
          const startMinutes = parseTimeStr(startTime);
          const endMinutes = parseTimeStr(endTime);
          
          // 如果结束时间小于开始时间，通常是跨天的情况
          return startMinutes > endMinutes;
        } catch (e) {
          return false;
        }
      }
      
      return false;
    },
    
    // 是否有轮次
    hasRounds() {
      return this.task && this.task.rounds_detail && this.task.rounds_detail.length > 0;
    },
    
    // 排序轮次计算属性 - 直接使用传入的轮次数据，不再重新排序
    sortedRounds() {
      if (!this.task || !this.task.rounds_detail || !Array.isArray(this.task.rounds_detail)) {
        return [];
      }
      return this.task.rounds_detail;
    },
    
    // 是否有活跃轮次 - 直接使用轮次状态
    hasActiveRound() {
      return this.task && 
             this.task.rounds_detail && 
             this.task.rounds_detail.some(round => round.status === 1);
    }
  },
  methods: {
    // 格式化进度
    formatProgress(progress) {
      // 确保progress是有效数字且在0-1之间
      if (typeof progress !== 'number' || isNaN(progress)) {
        progress = 0;
      }
      
      // 如果已经是百分比形式(0-100)
      if (progress > 1) {
        return `${Math.min(100, Math.floor(progress))}%`;
      }
      
      // 如果是小数形式(0-1)
      return `${Math.floor(progress * 100)}%`;
    },
    
    // 新增：处理轮次点击事件
    onRoundClick(round, index) {
      if (!round) return;
      
      // 向父组件发送select-round事件，传递轮次信息
      this.$emit('select-round', {
        round,
        index,
        taskId: this.task ? this.task._id : null
      });
    },
    
    // 新增：检查轮次是否被选中
    isRoundSelected(round) {
      if (!round) return false;
      return this.selectedRoundNumber === round.round;
    },
    
    // 获取轮次状态文本
    getRoundStatusText(round) {
      if (!round) return '未知';
      
      // 从轮次状态status获取
      const statusMap = {
        0: '未开始',
        1: '进行中',
        2: '已完成',
        3: '已超时'
      };
      
      return statusMap[round.status] || '未知';
    },
    
    // 获取当前活跃轮次 - 简化逻辑
    getCurrentActiveRound() {
      if (!this.task || !this.task.rounds_detail) return null;
      return this.task.rounds_detail.find(round => round.status === 1);
    },
    
    // 获取下一个轮次 - 简化逻辑
    getNextRound() {
      if (!this.task || !this.task.rounds_detail) return null;
      return this.task.rounds_detail.find(round => round.status === 0);
    },
    
    // 获取总缺卡点数
    getTotalMissedPoints() {
      // 从overall_stats获取缺卡点数
      if (this.task && this.task.overall_stats && this.task.overall_stats.missed_points !== undefined) {
        return this.task.overall_stats.missed_points;
      }
      
      return 0;
    },
    
    // 点击卡片
    onCardClick() {
      if (!this.task) return;
      
      // 点击卡片时触发select事件，传递任务ID
      this.$emit('click', this.task);
    },
    
    // 继续/开始巡检按钮点击事件
    onContinueTask() {
      if (!this.task) return;
      
      // 直接触发continue事件，让父组件处理轮次选择和状态判断
      this.$emit('continue', {
        task: this.task,
        selectedRound: this.selectedRoundNumber !== -1 ? 
          this.task.rounds_detail.find(r => r.round === this.selectedRoundNumber) : 
          null
      });
    },
    
    // 查看详情按钮点击事件
    onViewDetail() {
      if (!this.task) return;
      
      this.$emit('view-detail', this.task);
    },
    
    // 获取当前轮次编号
    getCurrentRoundNumber() {
      const currentRound = this.getCurrentActiveRound();
      return currentRound ? currentRound.round || 1 : 1;
    },
    
    // 获取任务状态文本
    getTaskStatusText() {
      if (!this.task) return '';
      
      // 任务状态: 0=未开始, 1=进行中, 2=已完成, 3=已超时
      switch (this.task.status) {
        case 0:
          return '等待开始';
        case 1:
          return `进行第${this.getCurrentRoundNumber()}轮巡检`;
        case 2:
          return '已完成';
        case 3:
          return '已超时';
        default:
          return '未知状态';
      }
    },
    
    // 获取用户名
    getUserName() {
      if(!this.task) return '';
      
      // 仅从userMap获取用户名
      if(this.userMap && this.task.user_id && this.userMap[this.task.user_id]) {
        return this.userMap[this.task.user_id].name || this.userMap[this.task.user_id].username || '未知用户';
      }
      
      return '未指定人员';
    },
    
    // 获取轮次剩余时间（毫秒）
    getRoundTimeRemaining(round) {
      if (!round || round.status !== 1) return 0;
      
      const cacheKey = `remaining_${round.round}`;
      const now = Date.now();
      
      // 使用缓存减少闪烁，每500ms更新一次
      if (this.countdownCache[cacheKey] && 
          (now - this.countdownCache[cacheKey].timestamp) < 500) {
        return this.countdownCache[cacheKey].value;
      }
      
      let result = 0;
      
      try {
        // 方式1：直接使用ISO格式的end_time（如果可用）
        if (round.end_time) {
          const endTime = this.parseISOTime(round.end_time);
          if (endTime && this.currentTime < endTime) {
            result = Math.max(0, endTime - this.currentTime);
          }
        }
        // 方式2：使用工具函数从time和day_offset计算
        else if (round.time && this.task) {
          // 获取基准日期（优先使用任务日期，其次使用当天日期）
          const baseDate = this.task.patrol_date || formatDate(new Date(), 'YYYY-MM-DD');
          // 使用工具方法计算开始时间，考虑日期偏移
          const startTime = calculateRoundTime(baseDate, round.time, round.day_offset || 0);
          // 使用工具方法计算结束时间，考虑持续时间
          const endTime = calculateEndTime(startTime, round.duration || 60);
          
          if (this.currentTime < endTime) {
            result = Math.max(0, endTime - this.currentTime);
          }
        }
      } catch (e) {
        console.error('计算轮次剩余时间出错:', e, round);
      }
      
      // 缓存结果
      this.countdownCache[cacheKey] = {
        value: result,
        timestamp: now
      };
      
      return result;
    },
    
    // 获取轮次等待开始时间（毫秒）
    getRoundTimeUntilStart(round) {
      if (!round || round.status !== 0) return 0;
      
      const cacheKey = `waiting_${round.round}`;
      const now = Date.now();
      
      // 使用缓存减少闪烁，每500ms更新一次
      if (this.countdownCache[cacheKey] && 
          (now - this.countdownCache[cacheKey].timestamp) < 500) {
        return this.countdownCache[cacheKey].value;
      }
      
      let result = 0;
      
      try {
        // 方式1：直接使用ISO格式的start_time（如果可用）
        if (round.start_time) {
          const startTime = this.parseISOTime(round.start_time);
          if (startTime && this.currentTime < startTime) {
            result = Math.max(0, startTime - this.currentTime);
          }
        }
        // 方式2：使用工具函数从time和day_offset计算
        else if (round.time && this.task) {
          // 获取基准日期（优先使用任务日期，其次使用当天日期）
          const baseDate = this.task.patrol_date || formatDate(new Date(), 'YYYY-MM-DD');
          // 使用工具方法计算时间，考虑日期偏移
          const startTime = calculateRoundTime(baseDate, round.time, round.day_offset || 0);
          
          if (this.currentTime < startTime) {
            result = Math.max(0, startTime - this.currentTime);
          }
        }
      } catch (e) {
        console.error('计算轮次等待时间出错:', e, round);
      }
      
      // 缓存结果
      this.countdownCache[cacheKey] = {
        value: result,
        timestamp: now
      };
      
      return result;
    },
    
    // 解析ISO时间字符串
    parseISOTime(isoString) {
      if (!isoString) return null;
      
      try {
        // 手动解析ISO格式时间字符串，如 "2025-05-27T12:00:00.000Z"
        const match = isoString.match(/^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.(\d{3}))?Z?$/);
        if (match) {
          const [, year, month, day, hour, minute, second, millisecond] = match;
          // 注意：JavaScript的月份是从0开始的
          return new Date(Date.UTC(
            parseInt(year),
            parseInt(month) - 1, // 月份需要减1
            parseInt(day),
            parseInt(hour),
            parseInt(minute),
            parseInt(second),
            parseInt(millisecond || 0)
          ));
        }
        
        // 如果正则匹配失败，尝试直接解析
        const date = new Date(isoString);
        return isNaN(date.getTime()) ? null : date;
      } catch (e) {
        console.error('解析ISO时间出错:', e, isoString);
        return null;
      }
    },

    // 格式化倒计时显示
    formatSimpleCountdown(milliseconds) {
      if (!milliseconds || milliseconds <= 0) return '0秒';
      
      try {
        const totalSeconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;
        
        // 轮次时间倒计时显示
        if (totalSeconds < 60) {
          // 小于1分钟，显示秒数
          return `${seconds}秒`;
        } else if (totalSeconds < 3600) {
          // 小于1小时，显示分钟和秒数
          return `${minutes}分${seconds}秒`;
        } else {
          // 大于等于1小时，显示小时和分钟
          return `${hours}小时${minutes}分钟`;
        }
      } catch (e) {
        console.error('格式化倒计时出错:', e);
        return '0秒';
      }
    },
    
    // 获取当前轮次缺卡数 - 简化逻辑
    getCurrentRoundMissedPoints() {
      const currentRound = this.getCurrentActiveRound();
      if (!currentRound || !currentRound.stats) return 0;
      return currentRound.stats.missed_points || 0;
    },
    
    // 检查轮次是否有真实的打卡数据
    hasRealCheckInData() {
      if (!this.task) return false;
      
      // 如果任务状态是已完成且有进度指标，那么可能已有数据
      if (this.task.status === 2 && this.progressRate > 0) {
        return true;
      }
      
      // 检查current_round_records是否有真实数据
      if (this.task.current_round_records && Array.isArray(this.task.current_round_records)) {
        // 过滤有效记录 - 必须有check_time
        const validRecords = this.task.current_round_records.filter(record => 
          record && typeof record === 'object' && record.check_time
        );
        return validRecords.length > 0;
      }
      
      // 检查所有轮次是否有打卡记录
      if (this.task.rounds_detail && Array.isArray(this.task.rounds_detail)) {
        for (const round of this.task.rounds_detail) {
          if (round && round.stats) {
            // 只有当completed_points大于0时才认为有真实打卡
            if (round.stats.completed_points > 0) {
              return true;
            }
          }
        }
      }
      
      // 没有找到有效打卡记录
      return false;
    },
    
    // 添加判断点位是否完成的辅助方法
    isPointCompleted(point, round) {
      if (!point || !round) return false;
      
      const pointId = point.point_id || point._id;
      if (!pointId) return false;
      
      // 获取轮次记录
      const roundRecord = this.task.round_records?.find(r => r.round === round.round);
      const completedPoints = roundRecord?.completed_points || [];
      
      // 检查完成状态 - 与index.vue保持一致的判断标准
      const statusCompleted = point.status === 1 || point.status === 'completed';
      const hasValidCheckIn = !!point.checkin_time;
      const inCompletedArray = completedPoints.includes(pointId);
      
      return statusCompleted || hasValidCheckIn || inCompletedArray;
    },
    
    getNextRoundNumber() {
      const nextRound = this.getNextRound();
      return nextRound ? nextRound.round : 1;
    },

    // 启动倒计时定时器
    startCountdownTimer() {
      // 先清除已有的定时器
      this.stopCountdownTimer();
      
      // 每秒更新一次当前时间
      this.countdownTimer = setInterval(() => {
        this.currentTime = new Date();
        
        // 检查是否有倒计时结束，需要刷新数据
        this.checkCountdownExpired();
      }, 1000);
    },

    // 停止倒计时定时器
    stopCountdownTimer() {
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
        this.countdownTimer = null;
      }
      // 清理缓存
      this.countdownCache = {};
    },

    // 🔥 新增：节流发送事件，避免短时间内重复触发
    throttledEmit(eventName, data, throttleTime = 5000) {
      const now = Date.now();
      const key = `${eventName}_${this.task._id}`;
      
      if (this.lastEmitTime[key] && (now - this.lastEmitTime[key]) < throttleTime) {
        console.log(`事件${eventName}被节流，跳过重复触发`);
        return;
      }
      
      this.lastEmitTime[key] = now;
      this.$emit(eventName, data);
      
      // 只在必要时发送全局事件
      if (eventName === 'refresh-task' || eventName === 'countdown-expired') {
        // 确保全局事件不重复
        if (!this.eventThrottle[key]) {
          this.eventThrottle[key] = true;
          uni.$emit('refresh-task-list');
          
          setTimeout(() => {
            this.eventThrottle[key] = false;
          }, throttleTime);
        }
      }
    },

    // 检查倒计时是否结束，需要刷新数据
    checkCountdownExpired() {
      if (!this.task || !this.task.rounds_detail) return;
      
      let needRefresh = false;
      let expiredRounds = [];
      
      this.task.rounds_detail.forEach(round => {
        if (!round) return;
        
        const roundKey = `${this.task._id}_${round.round}`;
        
        // 检查进行中的轮次是否已超时
        if (round.status === 1) {
          const remaining = this.getRoundTimeRemaining(round);
          if (remaining <= 0 && !this.refreshedRounds.has(roundKey + '_expired')) {
            needRefresh = true;
            expiredRounds.push(`轮次${round.round}已超时`);
            this.refreshedRounds.add(roundKey + '_expired');
          }
        }
        
        // 检查未开始的轮次是否应该开始了
        if (round.status === 0) {
          const timeUntilStart = this.getRoundTimeUntilStart(round);
          if (timeUntilStart <= 0 && !this.refreshedRounds.has(roundKey + '_start')) {
            needRefresh = true;
            expiredRounds.push(`轮次${round.round}应该开始`);
            this.refreshedRounds.add(roundKey + '_start');
          }
        }
      });
      
      // 如果需要刷新，通知父组件
      if (needRefresh) {
        console.log('倒计时结束，需要刷新任务:', this.task._id, expiredRounds);
        
        // 发送事件给父组件
        this.throttledEmit('countdown-expired', {
          taskId: this.task._id,
          task: this.task,
          expiredRounds: expiredRounds
        });
        
        // 同时发送刷新事件（更通用的事件名）
        this.throttledEmit('refresh-task', {
          taskId: this.task._id,
          reason: 'countdown-expired',
          details: expiredRounds
        });
        
        // 如果父组件没有监听事件，尝试其他方式通知刷新
        this.$nextTick(() => {
          // 通过全局事件总线通知
          if (this.$root && this.$root.$emit) {
            this.$root.$emit('task-countdown-expired', {
              taskId: this.task._id,
              expiredRounds: expiredRounds
            });
          }
          
          // 通过uni.$emit通知（如果是uni-app环境）
          if (typeof uni !== 'undefined' && uni.$emit) {
            uni.$emit('task-countdown-expired', {
              taskId: this.task._id,
              expiredRounds: expiredRounds
            });
            
            // 发送父组件已经监听的刷新事件
            uni.$emit('refresh-task-list');
          }
        });
        
        // 延迟一段时间后清除防抖记录，允许再次触发
        setTimeout(() => {
          expiredRounds.forEach(msg => {
            const roundNum = msg.match(/轮次(\d+)/)?.[1];
            if (roundNum) {
              const roundKey = `${this.task._id}_${roundNum}`;
              this.refreshedRounds.delete(roundKey + '_expired');
              this.refreshedRounds.delete(roundKey + '_start');
            }
          });
        }, 30000); // 30秒后允许再次触发
      }
    }
  },
  mounted() {
    // 启动倒计时定时器
    this.startCountdownTimer();
  },
  beforeDestroy() {
    // 清理定时器
    this.stopCountdownTimer();
  },
  // 添加页面生命周期管理
  onShow() {
    this.startCountdownTimer();
  },

  onHide() {
    this.stopCountdownTimer();
  }
};
</script>

<style lang="scss">
.p-task-card {
  position: relative;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx 24rpx 20rpx 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 8rpx;
    background-color: #ddd;
  }
  
  &--active {
    border-left: none;
    box-shadow: 0 8rpx 24rpx rgba(22, 119, 255, 0.15);
    
    &::before {
      background-color: #1677FF;
    }
  }
  
  &--selected {
    background-color: inherit !important;
    border: none !important;
    box-shadow: 0 6rpx 20rpx rgba(22, 119, 255, 0.25) !important;
    transform: translateY(-2rpx);
  }
  
  // 已添加的active样式
  &[active="true"] {
    border: none !important;
    background-color: inherit !important;
    box-shadow: 0 6rpx 20rpx rgba(22, 119, 255, 0.25) !important;
    transform: translateY(-2rpx);
  }
  
  &--expired {
    border-left: none;
    
    &::before {
      background-color: #F27D42;
    }
  }
  
  &--completed {
    border-left: none;
    
    &::before {
      background-color: #4CBBCE;
    }
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
    padding: 0 10rpx;
  }
  
  &__title-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex: 1;
    padding-left: 8rpx;
  }
  
  &__title {
    font-size: 34rpx;
    font-weight: bold;
    color: #333;
    padding-right: 10rpx;
  }
  
  &__quick-actions {
    display: flex;
    align-items: center;
    margin-left: 10rpx;
  }
  
  &__status {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 22rpx;
    padding: 2rpx 10rpx;
    height: 28rpx;
    border-radius: 4rpx;
    font-weight: 500;
    white-space: nowrap;
    line-height: 1;
    margin-right: 0;
    margin-left: 8rpx;
    
    &--pending {
      background-color: #f2f2f2;
      color: #666;
    }
    
    &--active {
      background-color: #e6f0ff;
      color: #0055cc;
    }
    
    &--completed {
      background-color: #e6f9f7;
      color: #0b9d89;
    }
    
    &--expired {
      background-color: #fdf1e6;
      color: #d85614;
    }
    
    &--canceled {
      background-color: #f2f2f2;
      color: #999;
    }
  }
  
  &__view-btn {
    padding: 8rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: transparent;
    border: none;
    border-radius: 50%;
    margin-left: 8rpx;
    width: 36rpx;
    height: 36rpx;
    transition: all 0.2s ease;
    
    &:active {
      opacity: 0.7;
    }
  }
  
  &__info-group {
    padding: 24rpx 30rpx;
    background-color: #f8fafe;
    margin-bottom: 20rpx;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(22, 119, 255, 0.1);
  }
  
  &__divider {
    height: 2rpx;
    background: linear-gradient(to right, rgba(22, 119, 255, 0.05), rgba(22, 119, 255, 0.3), rgba(22, 119, 255, 0.05));
    margin: 10rpx 30rpx 30rpx;
    border-radius: 2rpx;
  }
  
  &__divider--bottom {
    margin-bottom: 16rpx;
    background: linear-gradient(to right, rgba(22, 119, 255, 0.02), rgba(22, 119, 255, 0.15), rgba(22, 119, 255, 0.02));
  }
  
  &__info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  &__info-item {
    display: flex;
    align-items: flex-start;
    flex: 1;
    max-width: 48%; // 确保两个item之间有间距
  }
  
  &__info-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 48rpx;
    height: 48rpx;
    background-color: rgba(22, 119, 255, 0.1);
    border-radius: 8rpx;
    margin-right: 12rpx;
  }
  
  &__info-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  &__label {
    font-size: 24rpx;
    color: #666666;
    margin-bottom: 4rpx;
  }
  
  &__value {
    font-size: 28rpx;
    color: #222222;
    font-weight: 600;
    line-height: 1.3;
    word-break: break-all;
  }
  
  &__tag {
    display: inline-block;
    font-size: 20rpx;
    background-color: #ff9500;
    color: #fff;
    padding: 0 8rpx;
    border-radius: 6rpx;
    margin-left: 8rpx;
    font-weight: bold;
    vertical-align: middle;
    transform: translateY(-2rpx);
  }
  
  &__round-info {
    background-color: #f0f7ff;
    border-radius: 12rpx;
    padding: 16rpx;
    margin-bottom: 24rpx;
    border: 1px solid rgba(22, 119, 255, 0.1);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  }
  
  &__round-header {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
  }
  
  &__round-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #1677FF;
    margin-left: 10rpx;
  }
  
  &__round-summary {
    margin-bottom: 12rpx;
  }
  
  &__round-summary-text {
    font-size: 26rpx;
    color: #666;
  }
  
  &__round-current {
    font-size: 26rpx;
    color: #1677FF;
    font-weight: bold;
  }
  
  &__round-current--completed {
    color: #4CBBCE;
  }
  
  &__rounds-list {
    margin-bottom: 12rpx;
  }
  
  &__round-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10rpx 12rpx;
    border-radius: 8rpx;
    background-color: #fff;
    margin-bottom: 6rpx;
    transition: all 0.3s;
    cursor: pointer;
    border-left: 4rpx solid transparent;
    
    /* 轮次项悬停效果 */
    &:hover, &:active {
      background-color: #f0f0f0;
      border-left-color: #AAD6FF;
    }
    
    /* 选中的轮次项样式 */
    &--selected {
      background-color: #e6f0ff !important;
      border-left: 4rpx solid #1677FF !important;
      box-shadow: 0 2rpx 10rpx rgba(22, 119, 255, 0.15);
    }
  }
  
  &__round-item-header {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    max-width: 65%;
  }
  
  &__round-number {
    font-size: 26rpx;
    color: #333;
    font-weight: bold;
  }
  
  &__round-number-missed {
    font-size: 24rpx;
    color: #e53935;
    margin-left: 8rpx;
    font-weight: 500;
    background-color: rgba(255, 255, 255, 0.7);
    padding: 0 6rpx;
    border-radius: 4rpx;
  }
  
  &__round-status-badge {
    display: inline-block;
    font-size: 22rpx;
    padding: 2rpx 12rpx;
    border-radius: 16rpx;
    font-weight: 500;
    background-color: #e6e6e6;
    color: #666;
    margin-left: 8rpx;
    
    &--active {
      background-color: #e6f0ff;
      color: #0055cc;
      font-weight: bold;
    }
    
    &--completed {
      background-color: #e6f9f7;
      color: #0b9d89;
      font-weight: bold;
    }
    
    &--expired {
      background-color: #fdf1e6;
      color: #d85614;
      font-weight: bold;
    }
    
    &--waiting {
      background-color: #f0f7ff;
      color: #0055cc;
      font-weight: bold;
    }
  }
  
  &__round-item-time {
    margin-left: 12rpx;
    display: flex;
    align-items: center;
    
    text {
      font-size: 24rpx;
      color: #444;
      font-weight: 500;
      background-color: rgba(255, 255, 255, 0.7);
      padding: 2rpx 8rpx;
      border-radius: 4rpx;
      margin-left: 4rpx;
    }
  }
  
    &__round-countdown {
    display: flex;
    align-items: center;
    margin-left: 10rpx;
    font-size: 24rpx;
    color: #333; // 使用默认的深灰色，更清晰易读
  }
  
  &__round-status {
    margin-top: 12rpx;
  }
  
  &__active-task,
  &__completed-rounds,
  &__other-status {
    padding: 8rpx;
    border-radius: 8rpx;
    background-color: rgba(246, 250, 255, 0.8); // 浅色背景，提高可读性
  }
  
  &__status-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
  
  &__missed-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 22rpx;
    height: 28rpx;
    background-color: #ffeeed;
    color: #f5222d;
    border-radius: 4rpx;
    padding: 2rpx 12rpx;
    margin-left: 8rpx;
    white-space: nowrap;
  }
  
  &__round-badge {
    display: inline-block;
    font-size: 24rpx;
    padding: 4rpx 16rpx;
    border-radius: 20rpx;
    font-weight: 500;
    background-color: #e6e6e6;
    color: #666;
    
    &--waiting {
      background-color: #e6f0ff; // 略微调亮背景
      color: #0055cc; // 更深的蓝色，增加对比度
      font-weight: bold; // 加粗
    }
    
    &--active {
      background-color: #e6f0ff; // 略微调亮背景
      color: #0055cc; // 更深的蓝色，增加对比度
      font-weight: bold; // 加粗
    }
    
    &--completed {
      background-color: #e6f9f7;
      color: #0b9d89; // 更深的绿色，增加对比度
      font-weight: bold; // 加粗
    }
    
    &--expired {
      background-color: #fdf1e6;
      color: #d85614; // 更深的橙色，增加对比度
      font-weight: bold; // 加粗
    }
  }
  
  &__progress-container {
    margin-bottom: 20rpx;
    background-color: #f8fafe;
    border-radius: 12rpx;
    padding: 16rpx;
    border: 1px solid rgba(22, 119, 255, 0.1);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  }
  
  &__progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8rpx;
  }
  
  &__progress-label {
    font-size: 26rpx;
    color: #333;
    font-weight: bold;
  }
  
  &__progress-text {
    font-size: 26rpx;
    color: #1677FF;
    font-weight: bold;
  }
  
  &__progress-bar {
    height: 16rpx;
    background-color: #f2f2f2;
    border-radius: 8rpx;
    overflow: hidden;
    margin-bottom: 8rpx;
  }
  
  &__progress-inner {
    height: 100%;
    background-color: #1677FF;
    border-radius: 8rpx;
    transition: width 0.3s;
    
    &--completed {
      background-color: #4CBBCE;
    }
  }
  
  &__points-info {
    display: flex;
    justify-content: space-between;
    font-size: 24rpx;
    color: #666;
  }
  
  &__points-completed {
    color: #1677FF;
  }
  
  &__actions {
    display: flex;
    justify-content: center;
  }
  
  &__action-primary,
  &__action-expired,
  &__action-completed {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }
  
  &__action-expired,
  &__action-completed {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10rpx 20rpx;
    background-color: #f9f9f9;
    border-radius: 12rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  }
  
  &__action-waiting {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 10rpx 20rpx;
    background-color: #f9f9f9;
    border-radius: 12rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  }
  
  &__expired-text,
  &__completed-text {
    font-size: 28rpx;
    color: #555;
    font-weight: 500;
    background: linear-gradient(to right, #ff9c5a, #ff7b2e);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    padding: 6rpx 0;
    flex: 1; /* 添加flex: 1使文本占据左侧空间 */
  }
  
  &__waiting-text {
    font-size: 28rpx;
    color: #555;
    font-weight: 500;
    background: linear-gradient(to right, #52a0ff, #1677FF);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    padding: 6rpx 0;
    flex: 1; /* 添加flex: 1使文本占据左侧空间 */
  }
  
  &__btn {
    border: none;
    border-radius: 8rpx;
    font-size: 28rpx;
    font-weight: 500;
    padding: 0 40rpx;
    height: 72rpx;
    line-height: 72rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &--primary {
      background-color: #1677FF;
      color: #fff;
      
      uni-icons {
        margin-right: 6rpx;
      }
    }
    
    &--secondary {
      background-color: #f2f8ff;
      color: #1677FF;
      border: 1rpx solid #d9e8ff;
      box-shadow: 0 4rpx 8rpx rgba(22, 119, 255, 0.1);
      transition: all 0.3s ease;
      margin-left: auto; /* 确保按钮靠最右侧 */
      
      &:active {
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 4rpx rgba(22, 119, 255, 0.1);
      }
    }
    
    &--disabled {
      background-color: #e9f0fa;
      color: #a0b8d8;
      border: 1rpx solid #d9e8ff;
      cursor: not-allowed;
      margin-left: auto; /* 确保按钮靠最右侧 */
    }
  }
}



@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style> 