<template>
  <view class="p-map-container" :style="{ height: height }">
    <map
      id="map"
      class="map"
      :longitude="centerLongitude"
      :latitude="centerLatitude"
      :markers="formattedMarkers"
      :polyline="formattedPolylines"
      :circles="formattedCircles"
      :scale="scale"
      :show-location="showLocation"
      @markertap="onMarkerTap"
      @callouttap="onCalloutTap"
      @regionchange="onRegionChange"
    ></map>
    <slot></slot>
  </view>
</template>

<script>
/**
 * 地图组件
 * 封装微信小程序地图组件，提供更丰富的功能
 */
// 删除调试开关和调试日志函数
// const DEBUG = false;
// function debugLog(...args) {
//   if (DEBUG) {
//   }
// }

export default {
  name: 'p-map',
  data() {
    return {
      // 地图相关
      map: null,
      mapCtx: null,
      // 地图数据 - 重命名避免与props冲突
      localMarkers: [],
      localCircles: [],
      localPolyline: [],
      // 显示控制
      showMap: false,
      showMarkers: true,
      showCircles: true,
      showRoute: true,
      // 缓存当前数据
      currentTask: null,
      currentPatrolPoints: null,
      currentRound: null,
      // 正在加载中
      loading: false,
      // 新增 - 缓存新数据结构的轮次详情
      rounds_detail: null,
    }
  },
  props: {
    // 地图ID
    mapId: {
      type: String,
      default: 'patrolMap'
    },
    // 当前任务数据
    task: {
      type: Object,
      default: null
    },
    // 纬度（可选，指定中心点）
    latitude: {
      type: [String, Number],
      default: 30.0
    },
    // 经度（可选，指定中心点）
    longitude: {
      type: [String, Number],
      default: 120.0
    },
    // 缩放级别
    scale: {
      type: [String, Number],
      default: 16
    },
    // 显示定位按钮
    showLocationButton: {
      type: Boolean,
      default: false
    },
    // 显示指南针
    showCompass: {
      type: Boolean,
      default: true
    },
    // 允许旋转
    enableRotate: {
      type: Boolean,
      default: true
    },
    // 地图高度
    height: {
      type: String,
      default: '300px'
    },
    // 标记点数据 - 保留旧名称向后兼容
    markers: {
      type: Array,
      default: () => []
    },
    // 标记点数据 - 新名称
    markerData: {
      type: Array,
      default: () => []
    },
    // 路线数据 - 保留旧名称向后兼容
    polyline: {
      type: Array,
      default: () => []
    },
    // 路线数据 - 新名称
    polylineData: {
      type: Array,
      default: () => []
    },
    // 圆形区域数据 - 保留旧名称向后兼容
    circles: {
      type: Array,
      default: () => []
    },
    // 圆形区域数据 - 新名称
    circleData: {
      type: Array,
      default: () => []
    },
    // 是否显示地图
    show: {
      type: Boolean,
      default: true
    },
    // 是否实时刷新数据（用于追踪模式）
    realtime: {
      type: Boolean,
      default: false
    },
    // 当前轮次数据
    round: {
      type: Object,
      default: null
    },
    // 是否显示当前位置
    showLocation: {
      type: Boolean,
      default: true
    },
  },
  computed: {
    centerLongitude() {
      return Number(this.longitude);
    },
    centerLatitude() {
      return Number(this.latitude);
    },
    formattedMarkers() {
      
      // 优先使用markerData，其次使用markers prop，最后才使用本地数据
      const sourceData = this.markerData || this.markers || this.localMarkers;
      
      // 如果没有有效的标记点数据，返回空数组
      if (!sourceData || !Array.isArray(sourceData) || sourceData.length === 0) {
        return [];
      }
      
      // 确保所有marker的id都是数字类型，并保留callout属性
      return sourceData.map((marker, index) => {
        // 创建marker的副本，避免修改原始数据
        const processedMarker = {...marker};
        
        // 确保id为数字
        if (processedMarker.id === undefined || processedMarker.id === null || typeof processedMarker.id !== 'number') {
          // 如果id不存在或不是数字，则使用索引作为id
          processedMarker.id = index;
        } else if (typeof processedMarker.id === 'string') {
          // 如果id是字符串，尝试转换为数字
          const numId = Number(processedMarker.id);
          // 如果转换成功并且是有效数字，使用转换后的数字，否则使用索引
          processedMarker.id = !isNaN(numId) ? numId : index;
        }
        
        return processedMarker;
      });
    },
    formattedPolylines() {
      
      // 优先使用polylineData，其次使用polyline prop，最后才使用本地数据
      const sourceData = this.polylineData || this.polyline || this.localPolyline;
      
      // 如果没有有效的路线数据，返回空数组
      if (!sourceData || !Array.isArray(sourceData) || sourceData.length === 0) {
        return [];
      }
      
      // 返回路线数据，不做格式处理
      return sourceData;
    },
    formattedCircles() {
      
      // 优先使用circleData，其次使用circles prop，最后才使用本地数据
      const sourceData = this.circleData || this.circles || this.localCircles;
      
      // 如果没有有效的圆形区域数据，返回空数组
      if (!sourceData || !Array.isArray(sourceData) || sourceData.length === 0) {
        return [];
      }
      
      // 返回圆形区域数据，不做格式处理
      return sourceData;
    }
  },
  methods: {
    // 初始化地图
    initMap() {
      // 创建地图上下文
      this.mapCtx = uni.createMapContext('map', this);
    },
    
    // 安全地执行地图操作的辅助函数
    safeMapOperation(operation) {
      if (!this.mapCtx) {
        console.warn('地图上下文不存在，无法执行操作');
        return false;
      }
      
      try {
        operation();
        return true;
      } catch (error) {
        console.error('执行地图操作时出错:', error);
        return false;
      }
    },
    
    // 处理任务变更
    async handleTaskChange(newTask) {
      if (!newTask) {
        // 如果任务为空，清空地图数据
        this.localMarkers = [];
        this.localCircles = [];
        this.localPolyline = [];
        return;
      }
      
      try {
        this.loading = true;
        
        // 存储当前任务
        this.currentTask = newTask;
        
        
        // 检查是否有点位数据
        if (newTask.points && Array.isArray(newTask.points) && newTask.points.length > 0) {
          // 直接使用传入的点位数据
          const markers = this.buildMarkers(newTask.points);
          // 更新markerData
          this.markerData = markers;
          
          // 构建路线
          if (newTask.points.length > 1) {
            const polyline = this.buildPolyline(newTask.points);
            // 更新polylineData
            this.polylineData = [polyline];
          }
        }
        // 优先使用新的数据结构 rounds_detail
        else if (newTask.rounds_detail && newTask.rounds_detail.length > 0) {
          this.rounds_detail = newTask.rounds_detail;
          
          // 处理点位数据
          await this.processTaskMarkers();
        } 
        // 兼容旧数据结构
        else if (newTask.shift && newTask.shift.rounds) {
          // 获取轮次和点位信息
          const roundsData = await this.getRoundsData(newTask);
          if (roundsData && roundsData.points) {
            this.currentPatrolPoints = roundsData.points;
            await this.processTaskMarkers();
          }
        }
        
        this.loading = false;
      } catch (error) {
        console.error('处理任务数据出错：', error);
        this.loading = false;
      }
    },
    
    // 处理任务点位标记
    async processTaskMarkers() {
      let points = [];
      
      // 使用新的数据结构
      if (this.rounds_detail) {
        // 从rounds_detail中获取所有点位
        this.rounds_detail.forEach(round => {
          if (round.points && round.points.length) {
            points = [...points, ...round.points];
          }
        });
      } 
      // 兼容旧数据结构
      else if (this.currentPatrolPoints) {
        points = this.currentPatrolPoints;
      }
      
      if (!points || points.length === 0) {
        console.warn('无法找到任务点位数据');
        return;
      }
      
      // 创建点位标记
      const markers = [];
      const circles = [];
      
      // 存储有效的坐标点用于路线绘制
      let validCoordinates = [];
      
      // 处理每个点位
      for (let i = 0; i < points.length; i++) {
        const point = points[i];
        
        // 跳过无效点位
        if (!point.longitude || !point.latitude) {
          continue;
        }
        // ... existing code ...
      }
    },
    // 点击标记
    onMarkerTap(e) {
      // 确保事件数据正确
      const markerId = e.markerId !== undefined ? e.markerId : (e.detail && e.detail.markerId);
      if (markerId !== undefined) {
        this.$emit('marker-tap', markerId);
      }
    },
    // 点击气泡
    onCalloutTap(e) {
      // 确保事件数据正确
      const markerId = e.markerId !== undefined ? e.markerId : (e.detail && e.detail.markerId);
      if (markerId !== undefined) {
        this.$emit('callout-tap', markerId);
      }
    },
    // 地图区域变化
    onRegionChange(e) {
      if (e.type === 'end' && e.causedBy === 'drag') {
        this.$emit('region-change', {
          longitude: e.longitude,
          latitude: e.latitude
        });
      }
    },
    // 获取地图上下文
    getMapContext() {
      if (!this.mapCtx) {
        this.mapCtx = uni.createMapContext('map', this);
      }
      return this.mapCtx;
    },
    // 移动到指定位置
    moveToLocation(longitude, latitude) {
      if (!this.safeMapOperation(() => {})) return;
      
      const mapContext = this.getMapContext();
      if (!mapContext) return;
      
      if (longitude && latitude) {
        mapContext.moveToLocation({
          longitude: Number(longitude),
          latitude: Number(latitude)
        });
      } else {
        this.moveToCurrentLocation();
      }
    },
    // 移动到当前位置
    moveToCurrentLocation() {
      if (!this.safeMapOperation(() => {})) return;
      
      const mapContext = this.getMapContext();
      if (mapContext) {
        mapContext.moveToLocation();
      }
    },
    // 包含所有点标记
    includePoints(points, padding) {
      if (!this.safeMapOperation(() => {})) return;
      
      const mapContext = this.getMapContext();
      if (mapContext && points && points.length > 0) {
        mapContext.includePoints({
          points,
          padding: padding || [30, 30, 30, 30]
        });
      }
    },
    // 获取当前位置
    getCurrentLocation() {
      return new Promise((resolve, reject) => {
        uni.getLocation({
          type: 'gcj02',
          isHighAccuracy: true,
          highAccuracyExpireTime: 3000,
          success: res => {
            resolve(res);
          },
          fail: err => {
            reject(err);
          }
        });
      });
    },
    // 计算两点之间的距离（米）
    calculateDistance(point1, point2) {
      try {
        const EARTH_RADIUS = 6378137.0; // 地球半径
        const lat1 = (point1.latitude * Math.PI) / 180.0;
        const lng1 = (point1.longitude * Math.PI) / 180.0;
        const lat2 = (point2.latitude * Math.PI) / 180.0;
        const lng2 = (point2.longitude * Math.PI) / 180.0;
        
        const a = lat1 - lat2;
        const b = lng1 - lng2;
        
        const s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + 
            Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(b / 2), 2)));
        
        return s * EARTH_RADIUS;
      } catch (e) {
        console.error('距离计算出错:', e);
        return 0;
      }
    }
  },
  watch: {
    show(val) {
      this.showMap = val;
      if (val) {
        this.$nextTick(() => {
          this.initMap();
        });
      }
    },
    // 监听任务数据变化
    task: {
      handler(newTask) {
        // 如果组件已销毁或正在销毁中，不执行handleTaskChange
        if (!this._isDestroyed && !this._isBeingDestroyed) {
          this.handleTaskChange(newTask);
        }
      },
      deep: true,
      immediate: true
    },
    // 监听当前轮次变化
    round: {
      handler(newRound) {
        // 如果组件已销毁或正在销毁中，不执行更新
        if (this._isDestroyed || this._isBeingDestroyed) return;
        
        this.currentRound = newRound;
        // 如果有任务数据且地图已初始化，更新地图显示
        if (this.currentTask && this.mapCtx) {
          this.processTaskMarkers();
        }
      },
      deep: true
    },
    // 外部传入的标记数据
    markerData: {
      handler(newMarkers) {
        // 检查组件是否已销毁
        if (this._isDestroyed || this._isBeingDestroyed) return;
        
        if (newMarkers && newMarkers.length > 0) {
          this.localMarkers = newMarkers;
        }
      },
      deep: true
    },
    // 外部传入的圆形区域数据
    circleData: {
      handler(newCircles) {
        // 检查组件是否已销毁
        if (this._isDestroyed || this._isBeingDestroyed) return;
        
        if (newCircles && newCircles.length > 0) {
          this.localCircles = newCircles;
        }
      },
      deep: true
    },
    // 外部传入的路线数据
    polylineData: {
      handler(newPolyline) {
        // 检查组件是否已销毁
        if (this._isDestroyed || this._isBeingDestroyed) return;
        
        if (newPolyline && newPolyline.length > 0) {
          // 修复bug：将polyline赋值给data中定义的polyline，而不是polylines
          this.localPolyline = newPolyline;
        }
      },
      deep: true
    }
  },
  // 组件生命周期钩子
  created() {
    // 初始化
    this.showMap = this.show;
  },
  mounted() {
    if (this.showMap) {
      this.$nextTick(() => {
        this.initMap();
      });
    }
  },
  // 销毁时清理资源
  beforeDestroy() {
    // 清理地图实例和上下文
    this.mapCtx = null;
    this.map = null;
    
    // 清空数据，避免持续的异步操作
    this.localMarkers = [];
    this.localCircles = [];
    this.localPolyline = [];
    this.currentTask = null;
  },
};
</script>

<style lang="scss">
.p-map-container {
  position: relative;
  width: 100%;
  height: 300px;
  background-color: rgba(255, 255, 255, 0.85); /* 白色半透明 */
  
  .map {
    width: 100%;
    height: 100%;
  }
}
</style> 