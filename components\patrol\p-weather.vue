<template>
  <view class="p-weather" :class="{'p-weather--mini': mini}">
    <view class="p-weather__content">
      <view class="p-weather__main-info">
        <text class="p-weather__icon-text">{{ weatherIconText }}</text>
        <view class="p-weather__temp-container">
          <text class="p-weather__temp">{{ weather.temperature }}°</text>
          <text
            class="p-weather__text"
            :class="{'p-weather__text--scroll': weather.weather.length > 3}"
          >
            {{ weather.weather }}
          </text>
        </view>
      </view>
      
      <view class="p-weather__secondary" v-if="!mini">
        <view class="p-weather__location" v-if="address">
          <text class="p-weather__location-icon uni-icons-location"></text>
          <text class="p-weather__location-text">{{ address }}</text>
        </view>
        
        <view class="p-weather__details">
          <view class="p-weather__detail-item">
            <text class="p-weather__detail-value">{{ weather.humidity }}</text>
            <text class="p-weather__detail-label">湿度</text>
          </view>
          <view class="p-weather__detail-item">
            <text class="p-weather__detail-value">{{ weather.windPower }}</text>
            <text class="p-weather__detail-label">风力</text>
          </view>
          <view class="p-weather__detail-item" v-if="air">
            <text class="p-weather__detail-value" :class="{
              'p-weather__air-excellent': airQualityLevel === 'excellent',
              'p-weather__air-good': airQualityLevel === 'good',
              'p-weather__air-moderate': airQualityLevel === 'moderate',
              'p-weather__air-poor': airQualityLevel === 'poor',
              'p-weather__air-bad': airQualityLevel === 'bad',
              'p-weather__air-severe': airQualityLevel === 'severe'
            }">{{ air.aqi_name }}</text>
            <text class="p-weather__detail-label">空气</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
/**
 * 天气组件
 * 基于腾讯地图天气API，显示实时天气信息
 */
export default {
  name: 'p-weather',
  props: {
    // 是否为迷你模式（只显示温度和天气状态）
    mini: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      weather: {
        weather: '获取中...',
        temperature: '--',
        weatherCode: 'default',
        humidity: '--',
        windPower: '--',
        windDirection: '--',
        updateTime: '--'
      },
      air: null,
      address: '',
      loading: false,
      error: null,
      apiKey: '5MPBZ-FCW63-3C43B-R7G72-UOSAO-ZWBTJ' // 腾讯地图API Key
    };
  },
  computed: {
    // 根据天气代码返回对应的表情符号
    weatherIconText() {
      // 如果weatherCode不存在，返回默认图标
      if (!this.weather || !this.weather.weatherCode) return '🌈';
      
      // 腾讯地图天气代码映射表
      const iconMap = {
        '00': '☀️', // 晴
        '01': '🌥️', // 多云
        '02': '☁️', // 阴
        '03': '🌦️', // 阵雨
        '04': '⛈️', // 雷阵雨
        '05': '⛈️', // 雷阵雨伴有冰雹
        '06': '🌨️', // 雨夹雪
        '07': '🌧️', // 小雨
        '08': '🌧️', // 中雨
        '09': '🌧️', // 大雨
        '10': '🌧️', // 暴雨
        '11': '🌧️', // 大暴雨
        '12': '🌧️', // 特大暴雨
        '13': '🌨️', // 阵雪
        '14': '❄️', // 小雪
        '15': '❄️', // 中雪
        '16': '❄️', // 大雪
        '17': '❄️', // 暴雪
        '18': '🌫️', // 雾
        '19': '🌧️', // 冻雨
        '20': '🌪️', // 沙尘暴
        '21': '🌧️', // 小到中雨
        '22': '🌧️', // 中到大雨
        '23': '🌧️', // 大到暴雨
        '24': '🌧️', // 暴雨到大暴雨
        '25': '🌧️', // 大暴雨到特大暴雨
        '26': '❄️', // 小到中雪
        '27': '❄️', // 中到大雪
        '28': '❄️', // 大到暴雪
        '29': '🌫️', // 浮尘
        '30': '🌫️', // 扬沙
        '31': '🌪️', // 强沙尘暴
        '32': '🌫️', // 轻雾
        '33': '🌫️', // 大雾
        '34': '🌫️', // 特强浓雾
        '35': '🌡️', // 热
        '36': '🌡️', // 冷
        '37': '🌪️', // 龙卷风
        '38': '🌧️', // 雨
        '39': '🌨️', // 雪
        '40': '☔️', // 阵雨转晴
        '41': '⛈️', // 雷阵雨转晴
        '42': '🌤️', // 晴转多云
        '43': '🌥️', // 多云转晴
        '44': '☁️', // 阴转晴
        '45': '🌦️', // 晴转雨
        '46': '🌨️', // 晴转雪
        '47': '🌫️', // 霾转晴
        '48': '🌫️', // 晴转霾
        '49': '🌪️', // 扬沙转晴
        '50': '🌪️', // 晴转扬沙
        '51': '🌫️', // 雾转晴
        '52': '🌤️', // 晴转雾
        '53': '🌫️', // 霾
        '54': '💨', // 大风
        '55': '🌪️', // 飑
        '56': '🌡️', // 寒潮
        '57': '🌡️', // 热浪
        '58': '🌫️', // 轻度霾
        '59': '🌫️', // 中度霾
        '60': '🌫️', // 重度霾
        // 默认图标
        'default': '🌈'
      };
      
      return iconMap[this.weather.weatherCode] || iconMap['default'];
    },
    airQualityLevel() {
      if (!this.air) return '';
      
      // 根据AQI数值确定空气质量等级
      const aqi = parseInt(this.air.aqi);
      if (aqi <= 50) return 'excellent';
      if (aqi <= 100) return 'good';
      if (aqi <= 150) return 'moderate';
      if (aqi <= 200) return 'poor';
      if (aqi <= 300) return 'bad';
      return 'severe';
    }
  },
  created() {
    // 组件创建时立即加载天气数据
    this.fetchData();
  },
  methods: {
    async fetchData() {
      if (this.loading) return;
      
      this.loading = true;
      this.error = null;
      
      try {
        // 在加载前先设置显示加载状态
        this.weather = {
          weather: '获取中...',
          temperature: '--',
          weatherCode: 'default',
          humidity: '--',
          windPower: '--',
          windDirection: '--',
          updateTime: '--'
        };
        
        // 尝试从缓存获取天气数据
        const cacheKey = 'weather_data';
        let weatherData = this.getCachedData(cacheKey);
        
        if (!weatherData || this.isCacheExpired(weatherData.timestamp)) {
          // 1. 首先获取位置信息
          const locationData = await this.getLocationFromIP();
          
          // 2. 使用位置信息的adcode获取天气
          weatherData = await this.fetchWeatherByAdcode(locationData.adcode);
          
          // 设置地址
          this.address = locationData.address;
          
          // 缓存天气数据
          this.cacheData(cacheKey, weatherData);
        } else {
          weatherData = weatherData.data;
          // 从缓存恢复地址
          this.address = weatherData.address || '';
        }
        
        // 更新天气数据到界面，只在成功获取数据后才更新
        if (weatherData && weatherData.weather) {
          Object.assign(this.weather, weatherData.weather);
          this.air = weatherData.air;
          
          this.$emit('weather-loaded', {
            weather: weatherData.weather,
            air: weatherData.air,
            address: this.address
          });
        }
      } catch (error) {
        this.error = error.message || '获取天气数据失败';
        this.$emit('weather-error', this.error);
      } finally {
        this.loading = false;
      }
    },
    
    // 从IP获取位置信息（包括adcode和地址）
    async getLocationFromIP() {
      return new Promise((resolve, reject) => {
        uni.request({
          url: 'https://apis.map.qq.com/ws/location/v1/ip',
          data: {
            key: this.apiKey,
            output: 'json'
          },
          success: (res) => {
            if (res.statusCode === 200 && res.data && res.data.status === 0) {
              const result = res.data.result;
              const adInfo = result.ad_info;
              
              // 构建地址文本
              const address = `${adInfo.province}${adInfo.city}${adInfo.district}`;
              
              resolve({
                adcode: adInfo.adcode,
                address: address,
                location: result.location
              });
            } else {
              reject(new Error(res.data?.message || 'IP定位请求失败'));
            }
          },
          fail: (err) => {
            reject(new Error(err.errMsg || 'IP定位请求失败'));
          }
        });
      });
    },
    
    // 使用adcode获取天气数据
    async fetchWeatherByAdcode(adcode) {
      return new Promise((resolve, reject) => {
        uni.request({
          url: 'https://apis.map.qq.com/ws/weather/v1/',
          data: {
            key: this.apiKey,
            adcode: adcode,
            output: 'json'
          },
          success: (res) => {
            if (res.statusCode === 200 && res.data && res.data.status === 0) {
              // 处理并标准化天气数据
              const weatherData = this.parseWeatherData(res.data);
              resolve(weatherData);
            } else {
              reject(new Error(res.data?.message || '获取天气数据失败'));
            }
          },
          fail: (err) => {
            reject(new Error(err.errMsg || '请求天气数据失败'));
          }
        });
      });
    },
    
    // 解析腾讯地图天气API返回的数据
    parseWeatherData(data) {
      const result = {
        weather: {},
        air: null
      };
      
      try {
        // 处理实时天气数据 - 腾讯天气API返回格式为 result.realtime[0].infos
        if (data.result && data.result.realtime && data.result.realtime[0] && data.result.realtime[0].infos) {
          const realtime = data.result.realtime[0];
          const infos = realtime.infos;
          
          // 保存地址信息供缓存使用
          result.address = `${realtime.province}${realtime.city}${realtime.district}`;
          
          // 根据文档，获取天气码 (https://lbs.qq.com/service/webService/webServiceGuide/weatherinfo)
          // 将天气文本转换为天气码
          const weatherText = infos.weather || '';
          const weatherCode = this.getWeatherCodeFromText(weatherText);
          
          result.weather = {
            weather: infos.weather || '未知',            // 天气文本
            temperature: String(infos.temperature || '--'),  // 确保温度是字符串
            weatherCode: weatherCode,                   // 天气代码
            humidity: String(infos.humidity || '--') + '%',  // 确保湿度是字符串 
            windPower: infos.wind_power || '--',        // 风力
            windDirection: infos.wind_direction || '--', // 风向
            updateTime: realtime.update_time || '--'   // 更新时间
          };
        } else {
          // 设置默认值防止界面显示错误
          result.weather = {
            weather: '获取中...', // 使用一致的加载中文本
            temperature: '--',
            weatherCode: 'default', // 使用default确保显示默认天气图标
            humidity: '--',
            windPower: '--',
            windDirection: '--',
            updateTime: '--'
          };
        }
      } catch (error) {
        // 出错时设置默认值
        result.weather = {
          weather: '获取中...', // 使用一致的加载中文本
          temperature: '--',
          weatherCode: 'default', // 使用default确保显示默认天气图标
          humidity: '--',
          windPower: '--',
          windDirection: '--',
          updateTime: '--'
        };
      }
      
      return result;
    },
    
    // 根据天气文本获取相应的天气代码
    getWeatherCodeFromText(text) {
      // 天气文本到代码的映射表
      const weatherMapping = {
        '晴': '00',
        '晴天': '00',
        '多云': '01',
        '阴': '02',
        '阵雨': '03',
        '雷阵雨': '04',
        '雷阵雨伴有冰雹': '05',
        '雨夹雪': '06',
        '小雨': '07',
        '中雨': '08',
        '大雨': '09',
        '暴雨': '10',
        '大暴雨': '11',
        '特大暴雨': '12',
        '阵雪': '13',
        '小雪': '14',
        '中雪': '15',
        '大雪': '16',
        '暴雪': '17',
        '雾': '18',
        '冻雨': '19',
        '沙尘暴': '20',
        '小到中雨': '21',
        '中到大雨': '22',
        '大到暴雨': '23',
        '暴雨到大暴雨': '24',
        '大暴雨到特大暴雨': '25',
        '小到中雪': '26',
        '中到大雪': '27',
        '大到暴雪': '28',
        '浮尘': '29',
        '扬沙': '30',
        '强沙尘暴': '31',
        '轻雾': '32',
        '大雾': '33',
        '特强浓雾': '34',
        '热': '35',
        '冷': '36',
        '龙卷风': '37',
        '雨': '38',
        '雪': '39',
        '阵雨转晴': '40',
        '雷阵雨转晴': '41',
        '晴转多云': '42',
        '多云转晴': '43',
        '阴转晴': '44',
        '晴转雨': '45',
        '晴转雪': '46',
        '霾转晴': '47',
        '晴转霾': '48',
        '扬沙转晴': '49',
        '晴转扬沙': '50',
        '雾转晴': '51',
        '晴转雾': '52',
        '霾': '53',
        '大风': '54',
        '飑': '55',
        '寒潮': '56',
        '热浪': '57',
        '轻度霾': '58',
        '中度霾': '59',
        '重度霾': '60'
      };
      
      return weatherMapping[text] || 'default';
    },
    
    // 获取缓存数据
    getCachedData(key) {
      try {
        const cached = uni.getStorageSync(key);
        if (cached) {
          return JSON.parse(cached);
        }
      } catch (e) {
        console.error('获取缓存数据失败', e);
      }
      return null;
    },
    
    // 缓存数据
    cacheData(key, data) {
      try {
        uni.setStorageSync(key, JSON.stringify({
          data: data,
          timestamp: Date.now()
        }));
      } catch (e) {
        console.error('缓存数据失败', e);
      }
    },
    
    // 判断缓存是否过期（1小时）
    isCacheExpired(timestamp) {
      const ONE_HOUR = 60 * 60 * 1000;
      return Date.now() - timestamp > ONE_HOUR;
    },
    
    // 手动刷新天气
    refresh() {
      return this.fetchData();
    },
    
    // 清除缓存
    clearCache(key) {
      try {
        uni.removeStorageSync(key);
        console.log('已清除缓存:', key);
      } catch (e) {
        console.error('清除缓存失败', e);
      }
    }
  }
};
</script>

<style lang="scss">
.p-weather {
  background: rgba(255, 255, 255, 0.25);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  color: #333;
  overflow: hidden;
  padding: 0;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  
  &--mini {
    background: rgba(255, 255, 255, 0.25);
    border-radius: 12rpx;
    padding: 0;
  }
  
  &__content {
    padding: 12rpx 18rpx;
  }
  
  &--mini &__content {
    padding: 8rpx 14rpx;
  }
  
  &__main-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  &__temp-container {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  &__temp {
    font-size: 42rpx;
    font-weight: bold;
    line-height: 1.1;
    text-align: center;
  }
  
  &--mini &__temp {
    font-size: 32rpx;
  }
  
  &__icon-text {
    font-size: 52rpx;
    line-height: 1;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
    margin-right: 6rpx;
  }
  
  &--mini &__icon-text {
    font-size: 44rpx;
    margin-right: 4rpx;
  }
  
  &__text {
    font-size: 24rpx;
    opacity: 0.85;
    margin-top: 2rpx;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 80rpx;
  }
  
  &--mini &__text {
    font-size: 22rpx;
  }
  
  &__text--scroll {
    overflow-x: auto;
    text-overflow: unset;
    white-space: nowrap;
    max-width: 120rpx;
    display: inline-block;
  }
  
  &__text--scroll::-webkit-scrollbar {
    height: 4rpx;
    background: transparent;
  }
  
  &__secondary {
    margin-top: 10rpx;
  }
  
  &__location {
    display: flex;
    align-items: center;
    margin-bottom: 10rpx;
    opacity: 0.8;
    
    &-icon {
      font-size: 22rpx;
      margin-right: 6rpx;
    }
    
    &-text {
      font-size: 22rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 400rpx;
    }
  }
  
  &__details {
    display: flex;
    margin-top: 12rpx;
    border-top: 1rpx solid rgba(0, 0, 0, 0.1);
    padding-top: 12rpx;
  }
  
  &__detail-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
    
    &:not(:last-child):after {
      content: '';
      position: absolute;
      right: 0;
      top: 10%;
      height: 80%;
      width: 1rpx;
      background-color: rgba(0, 0, 0, 0.1);
    }
  }
  
  &__detail-label {
    font-size: 20rpx;
    opacity: 0.7;
    margin-top: 2rpx;
  }
  
  &__detail-value {
    font-size: 24rpx;
    font-weight: 500;
  }
  
  // 空气质量颜色
  &__air {
    &-excellent { color: #10b981; }
    &-good { color: #22c55e; }
    &-moderate { color: #f59e0b; }
    &-poor { color: #f97316; }
    &-bad { color: #ef4444; }
    &-severe { color: #9333ea; }
  }
}
</style> 