{"bsonType": "object", "required": ["title", "description", "reporter_id", "location_info"], "permission": {"read": true, "create": "auth.role.includes('Integrated') || auth.role.includes('reviser') || auth.role.includes('admin') || auth.role.includes('GM')", "update": "doc.reporter_id == auth.uid || doc.assigned_to == auth.uid || auth.role.includes('admin') || auth.role.includes('GM')", "delete": "auth.role.includes('admin')"}, "properties": {"_id": {"description": "ID，系统自动生成"}, "title": {"bsonType": "string", "title": "问题标题", "description": "月度检查发现问题的标题", "minLength": 5, "maxLength": 100, "errorMessage": {"required": "问题标题不能为空", "minLength": "问题标题长度不能小于 {minLength} 个字符", "maxLength": "问题标题长度不能大于 {maxLength} 个字符"}}, "description": {"bsonType": "string", "title": "问题描述", "description": "问题的详细描述", "minLength": 10, "maxLength": 1000, "errorMessage": {"required": "问题描述不能为空", "minLength": "问题描述长度不能小于 {minLength} 个字符", "maxLength": "问题描述长度不能大于 {maxLength} 个字符"}}, "issue_number": {"bsonType": "string", "title": "问题编号", "description": "自动生成的月度检查问题编号", "maxLength": 50}, "inspection_info": {"bsonType": "object", "title": "检查信息", "description": "月度检查相关信息", "properties": {"inspection_date": {"bsonType": "timestamp", "title": "检查日期", "description": "发现问题的检查日期"}, "inspection_type": {"bsonType": "string", "title": "检查类型", "enum": ["monthly_routine", "quarterly_review", "special_inspection", "safety_inspection"], "default": "monthly_routine"}, "leader_info": {"bsonType": "object", "title": "带队领导信息", "properties": {"leader_id": {"bsonType": "string", "foreignKey": "uni-id-users._id"}, "leader_name": {"bsonType": "string", "maxLength": 50}, "leader_role": {"bsonType": "string", "maxLength": 30}, "department": {"bsonType": "string", "maxLength": 50}}}}}, "location_info": {"bsonType": "object", "title": "位置信息", "description": "问题发现的位置详情", "required": ["location_name"], "properties": {"location_type": {"bsonType": "string", "enum": ["preset", "custom"], "description": "位置来源：预设|自定义", "default": "custom"}, "location_category": {"bsonType": "string", "title": "位置分类", "description": "位置分类（来自location-manage）", "maxLength": 50}, "location_name": {"bsonType": "string", "title": "位置名称", "description": "具体位置名称", "maxLength": 100, "errorMessage": {"required": "位置名称不能为空"}}, "location_description": {"bsonType": "string", "title": "位置补充描述", "description": "位置的详细描述", "maxLength": 200}}}, "category": {"bsonType": "string", "title": "问题类别", "description": "问题的分类", "enum": ["safety", "cleanliness", "organization", "equipment", "environment", "management", "other"], "default": "safety"}, "severity": {"bsonType": "string", "title": "严重程度", "description": "问题的严重程度", "enum": ["low", "medium", "high", "critical"], "default": "medium"}, "priority": {"bsonType": "string", "title": "处理优先级", "description": "问题处理的优先级", "enum": ["low", "normal", "high", "urgent"], "default": "normal"}, "reporter_id": {"bsonType": "string", "title": "记录人ID", "description": "记录问题的检查员ID", "foreignKey": "uni-id-users._id", "errorMessage": {"required": "记录人不能为空"}}, "reporter_name": {"bsonType": "string", "title": "记录人姓名", "description": "记录人的姓名", "maxLength": 50}, "assigned_to": {"bsonType": "string", "title": "负责人ID", "description": "负责解决该问题的用户ID", "foreignKey": "uni-id-users._id"}, "assigned_to_name": {"bsonType": "string", "title": "负责人姓名", "description": "负责人的姓名", "maxLength": 50}, "assigned_by": {"bsonType": "string", "title": "分配人ID", "description": "分配负责人的用户ID", "foreignKey": "uni-id-users._id"}, "assigned_by_name": {"bsonType": "string", "title": "分配人姓名", "maxLength": 50}, "assignment_reason": {"bsonType": "string", "title": "分配理由", "description": "分配给该负责人的理由", "maxLength": 200}, "photos": {"bsonType": "array", "title": "问题照片", "description": "展示问题的照片列表", "items": {"bsonType": "object", "properties": {"url": {"bsonType": "string", "title": "图片URL", "pattern": "^cloud://"}, "description": {"bsonType": "string", "title": "照片说明", "maxLength": 100}, "timestamp": {"bsonType": "timestamp", "title": "拍摄时间"}}}, "maxItems": 10}, "status": {"bsonType": "string", "title": "处理状态", "description": "问题的当前处理状态", "enum": ["submitted", "assigned", "in_progress", "pending_review", "resolved", "verified", "closed", "rejected"], "default": "submitted"}, "expected_completion_date": {"bsonType": "timestamp", "title": "期望完成日期", "description": "期望问题解决的日期"}, "actual_completion_date": {"bsonType": "timestamp", "title": "实际完成日期", "description": "问题实际解决的日期"}, "resolution_info": {"bsonType": "object", "title": "解决方案信息", "properties": {"resolution_description": {"bsonType": "string", "title": "解决方案描述", "maxLength": 1000}, "resolution_photos": {"bsonType": "array", "title": "解决后照片", "items": {"bsonType": "object", "properties": {"url": {"bsonType": "string", "pattern": "^cloud://"}, "description": {"bsonType": "string", "maxLength": 100}, "timestamp": {"bsonType": "timestamp"}}}, "maxItems": 10}, "cost_estimate": {"bsonType": "number", "title": "成本估算", "minimum": 0, "maximum": 999999}, "actual_cost": {"bsonType": "number", "title": "实际成本", "minimum": 0, "maximum": 999999}}}, "review_info": {"bsonType": "object", "title": "审核信息", "properties": {"reviewer_id": {"bsonType": "string", "foreignKey": "uni-id-users._id"}, "reviewer_name": {"bsonType": "string", "maxLength": 50}, "review_date": {"bsonType": "timestamp"}, "review_result": {"bsonType": "string", "enum": ["approved", "rejected", "needs_improvement"]}, "review_comments": {"bsonType": "string", "maxLength": 500}, "review_rating": {"bsonType": "number", "minimum": 1, "maximum": 5}}}, "tags": {"bsonType": "array", "title": "标签", "description": "问题的标签，便于分类和搜索", "items": {"bsonType": "string", "maxLength": 20}, "maxItems": 10}, "follow_up_required": {"bsonType": "bool", "title": "需要跟进", "description": "是否需要后续跟进", "default": false}, "follow_up_date": {"bsonType": "timestamp", "title": "跟进日期", "description": "计划跟进的日期"}, "created_at": {"bsonType": "timestamp", "title": "创建时间", "description": "记录创建时间", "forceDefaultValue": {"$env": "now"}}, "updated_at": {"bsonType": "timestamp", "title": "更新时间", "description": "记录最后更新时间", "forceDefaultValue": {"$env": "now"}}}}