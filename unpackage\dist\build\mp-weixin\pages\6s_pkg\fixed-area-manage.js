require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/fixed-area-manage"],{"20d2":function(t,e,n){"use strict";(function(t){var a=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=a(n("7eb4")),i=a(n("3b2d")),o=a(n("7ca3")),s=a(n("ee10")),c=n("882c");function u(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,a)}return n}function l(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?u(Object(n),!0).forEach((function(e){(0,o.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var d={name:"FixedAreaManage",data:function(){return{areaList:[],loading:!1,saving:!1,isEditing:!1,currentArea:null,formData:{name:"",location:"",description:"",status:"active"},statusOptions:[{value:"active",label:"启用"},{value:"inactive",label:"禁用"}],statusIndex:0}},computed:{enabledAreas:function(){return this.areaList.filter((function(t){return"active"===t.status}))},assignedAreas:function(){return this.areaList.filter((function(t){return t.assigned_users&&t.assigned_users.length>0}))}},onLoad:function(){this.loadAreaList()},methods:{loadAreaList:function(){var e=this;return(0,s.default)(r.default.mark((function n(){var a;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,e.loading=!0,n.next=4,(0,c.callCloudFunction)("hygiene-area-management",{action:"getAreaList",data:{type:"fixed",status:"active",page:1,pageSize:100}});case 4:a=n.sent,e.areaList=a.data&&a.data.list||[],n.next=13;break;case 8:n.prev=8,n.t0=n["catch"](0),console.error("加载责任区列表失败：",n.t0),n.t0.message.includes("未登录")||n.t0.message.includes("登录")||t.showToast({title:n.t0.message||"加载失败",icon:"none"}),e.areaList=[];case 13:return n.prev=13,e.loading=!1,n.finish(13);case 16:case"end":return n.stop()}}),n,null,[[0,8,13,16]])})))()},showAddArea:function(){this.isEditing=!1,this.currentArea=null,this.resetForm(),this.$refs.areaFormPopup.open()},editArea:function(t){this.isEditing=!0,this.currentArea=t,this.formData={name:t.name||"",location:t.location&&t.location.area||t.location||"",description:t.description||"",status:t.status||"active"},this.statusIndex=this.statusOptions.findIndex((function(e){return e.value===t.status}))||0,this.$refs.areaFormPopup.open()},deleteArea:function(e){var n=this;t.showModal({title:"确认删除",content:'确定要删除责任区"'.concat(e.name,'"吗？删除后不可恢复。'),confirmText:"删除",confirmColor:"#FF3B30",success:function(t){t.confirm&&n.performDelete(e)}})},performDelete:function(e){var n=this;return(0,s.default)(r.default.mark((function a(){var i,o,s;return r.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,i=e._id,i){a.next=4;break}throw new Error("责任区ID不存在，数据可能异常，请刷新页面重试");case 4:return a.next=6,(0,c.callCloudFunction)("hygiene-area-management",{action:"deleteArea",data:{id:i}});case 6:o=n.areaList.findIndex((function(t){return(t._id||t.id)===i})),o>-1&&n.areaList.splice(o,1),t.showToast({title:"删除成功",icon:"success"}),a.next=16;break;case 11:a.prev=11,a.t0=a["catch"](0),console.error("删除失败：",a.t0),s=a.t0.message||"删除失败",s.includes("权限不足")?t.showModal({title:"权限不足",content:s,showCancel:!1,confirmText:"我知道了"}):t.showToast({title:s,icon:"none",duration:3e3});case 16:case"end":return a.stop()}}),a,null,[[0,11]])})))()},viewAreaDetail:function(e){var n=e.location&&e.location.area||e.location||"未设置",a=e.assigned_users&&e.assigned_users.length||0;t.showModal({title:e.name,content:"位置：".concat(n,"\n分配人员：").concat(a,"人\n描述：").concat(e.description||"无描述"),showCancel:!1})},onStatusChange:function(t){this.statusIndex=t.detail.value,this.formData.status=this.statusOptions[this.statusIndex].value},submitForm:function(){var e=this;return(0,s.default)(r.default.mark((function n(){var a,i,o;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(e.validateForm()){n.next=2;break}return n.abrupt("return");case 2:if(n.prev=2,e.saving=!0,!e.isEditing){n.next=12;break}return n.next=7,(0,c.callCloudFunction)("hygiene-area-management",{action:"updateArea",data:{id:e.currentArea._id,name:e.formData.name,type:"fixed",description:e.formData.description,location:{area:e.formData.location},status:e.formData.status}});case 7:n.sent,a=e.areaList.findIndex((function(t){return t._id===e.currentArea._id})),a>-1&&(e.areaList[a]=l(l(l({},e.areaList[a]),e.formData),{},{updated_at:(new Date).toISOString()})),n.next=16;break;case 12:return n.next=14,(0,c.callCloudFunction)("hygiene-area-management",{action:"createArea",data:{name:e.formData.name,type:"fixed",description:e.formData.description,location:{area:e.formData.location},status:e.formData.status}});case 14:i=n.sent,e.areaList.unshift(i.data||{});case 16:t.showToast({title:e.isEditing?"保存成功":"创建成功",icon:"success"}),e.closeForm(),n.next=25;break;case 20:n.prev=20,n.t0=n["catch"](2),console.error("保存失败：",n.t0),o=n.t0.message||"保存失败",o.includes("权限不足")?t.showModal({title:"权限不足",content:o,showCancel:!1,confirmText:"我知道了"}):t.showToast({title:o,icon:"none",duration:3e3,mask:!0});case 25:return n.prev=25,e.saving=!1,n.finish(25);case 28:case"end":return n.stop()}}),n,null,[[2,20,25,28]])})))()},validateForm:function(){return this.formData.name.trim()?!(this.formData.name.length>50)||(t.showToast({title:"责任区名称不能超过50个字符",icon:"none"}),!1):(t.showToast({title:"请输入责任区名称",icon:"none"}),!1)},resetForm:function(){this.formData={name:"",location:"",description:"",status:"active"},this.statusIndex=0},closeForm:function(){this.$refs.areaFormPopup.close(),this.resetForm()},getStatusText:function(t){return{active:"启用",inactive:"禁用"}[t]||"未知"},formatDate:function(t){return t?(e="object"===(0,i.default)(t)&&t.$date?new Date(t.$date):new Date(t),"".concat(e.getFullYear(),"-").concat(String(e.getMonth()+1).padStart(2,"0"),"-").concat(String(e.getDate()).padStart(2,"0"))):"";var e}}};e.default=d}).call(this,n("df3c")["default"])},32406:function(t,e,n){},"884d":function(t,e,n){"use strict";n.r(e);var a=n("a8c9"),r=n("c035");for(var i in r)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(i);n("bf3a");var o=n("828b"),s=Object(o["a"])(r["default"],a["b"],a["c"],!1,null,"530a98df",null,!1,a["a"],void 0);e["default"]=s.exports},a8c9:function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return a}));var a={uniIcons:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(n.bind(null,"6ddf"))},pEmptyState:function(){return n.e("components/p-empty-state/p-empty-state").then(n.bind(null,"9b76"))},uniPopup:function(){return n.e("uni_modules/uni-popup/components/uni-popup/uni-popup").then(n.bind(null,"a2b7"))}},r=function(){var t=this,e=t.$createElement,n=(t._self._c,t.loading?null:t.areaList.length),a=t.loading?null:t.enabledAreas.length,r=t.loading?null:t.assignedAreas.length,i=t.loading?null:t.areaList.length,o=!t.loading&&i>0?t.__map(t.areaList,(function(e,n){var a=t.__get_orig(e),r=t.getStatusText(e.status),i=e.assigned_users&&e.assigned_users.length>0,o=i?e.assigned_users.length:null,s=t.formatDate(e.created_at);return{$orig:a,m0:r,g4:i,g5:o,m1:s}})):null;t.$mp.data=Object.assign({},{$root:{g0:n,g1:a,g2:r,g3:i,l0:o}})},i=[]},be6c:function(t,e,n){"use strict";(function(t,e){var a=n("47a9");n("357b"),n("861b");a(n("3240"));var r=a(n("884d"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(r.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},bf3a:function(t,e,n){"use strict";var a=n("32406"),r=n.n(a);r.a},c035:function(t,e,n){"use strict";n.r(e);var a=n("20d2"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=r.a}},[["be6c","common/runtime","common/vendor"]]]);