@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container.data-v-ccc26d00 {
  min-height: 100vh;
  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
  padding: 24rpx;
}
.card.data-v-ccc26d00 {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.card-header.data-v-ccc26d00 {
  padding: 32rpx 32rpx 16rpx 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
}
.card-body.data-v-ccc26d00 {
  padding: 32rpx;
}
.header-content.data-v-ccc26d00 {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.card-title.data-v-ccc26d00 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.card-subtitle.data-v-ccc26d00 {
  font-size: 26rpx;
  color: #8E8E93;
}
.photo-count.data-v-ccc26d00 {
  font-size: 24rpx;
  color: #8E8E93;
}
.task-info.data-v-ccc26d00 {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.info-item.data-v-ccc26d00 {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}
.info-label.data-v-ccc26d00 {
  font-size: 28rpx;
  color: #8E8E93;
  width: 140rpx;
  flex-shrink: 0;
  text-align: left;
}
.info-value.data-v-ccc26d00 {
  font-size: 28rpx;
  color: #1C1C1E;
  flex: 1;
  line-height: 1.4;
}
.status-badge.data-v-ccc26d00 {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 600;
}
.status-badge.status-pending.data-v-ccc26d00, .status-badge.status-pending_rectification.data-v-ccc26d00 {
  background: #FFF4E6;
  color: #FF9500;
}
.status-badge.status-pending_review.data-v-ccc26d00 {
  background: #E6F3FF;
  color: #007AFF;
}
.status-badge.status-completed.data-v-ccc26d00 {
  background: #E8F5E8;
  color: #34C759;
}
.status-badge.status-overdue.data-v-ccc26d00 {
  background: #FFE6E6;
  color: #FF3B30;
}
.status-badge.status-in_progress.data-v-ccc26d00 {
  background: #F0F0F5;
  color: #8E8E93;
}
.status-badge.status-rejected.data-v-ccc26d00 {
  background: #FFE6E6;
  color: #FF3B30;
}
.status-badge.status-verified.data-v-ccc26d00 {
  background: #E8F5E8;
  color: #34C759;
}
/* 照片标签 */
.photo-label.data-v-ccc26d00 {
  position: absolute;
  bottom: 8rpx;
  left: 8rpx;
  background: rgba(255, 59, 48, 0.8);
  color: white;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
  font-weight: 600;
}
.photo-label.completion.data-v-ccc26d00 {
  background: rgba(52, 199, 89, 0.8);
}
.photo-label.review.data-v-ccc26d00 {
  background: rgba(0, 122, 255, 0.8);
}
.photo-item.data-v-ccc26d00 {
  position: relative;
}
/* 审核信息 */
.review-info.data-v-ccc26d00 {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.review-result-badge.data-v-ccc26d00 {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 600;
}
.review-result-badge.review-approved.data-v-ccc26d00 {
  background: #E8F5E8;
  color: #34C759;
}
.review-result-badge.review-rejected.data-v-ccc26d00 {
  background: #FFE6E6;
  color: #FF3B30;
}
.review-result-badge.review-needs_improvement.data-v-ccc26d00 {
  background: #FFF4E6;
  color: #FF9500;
}
/* 复查照片部分 */
.review-photos-section.data-v-ccc26d00 {
  margin-top: 32rpx;
  padding-top: 32rpx;
  border-top: 1rpx solid #F2F2F7;
}
.review-photos-header.data-v-ccc26d00 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.review-photos-title.data-v-ccc26d00 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
}
.description-text.data-v-ccc26d00 {
  font-size: 28rpx;
  color: #1C1C1E;
  line-height: 1.6;
}
.photo-grid.data-v-ccc26d00 {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}
.photo-item.data-v-ccc26d00 {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #F8F9FA;
}
.photo-item image.data-v-ccc26d00 {
  width: 100%;
  height: 100%;
}
.button-container.data-v-ccc26d00 {
  padding: 32rpx 0;
}
.primary-button.data-v-ccc26d00 {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  width: 100%;
}
/* 加载状态 */
.loading-container.data-v-ccc26d00 {
  padding: 120rpx 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.loading-content.data-v-ccc26d00 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}
.loading-spinner.data-v-ccc26d00 {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #E5E7EB;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  -webkit-animation: spin-data-v-ccc26d00 1s linear infinite;
          animation: spin-data-v-ccc26d00 1s linear infinite;
}
@-webkit-keyframes spin-data-v-ccc26d00 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin-data-v-ccc26d00 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.loading-text.data-v-ccc26d00 {
  font-size: 28rpx;
  color: #8E8E93;
  text-align: center;
}
/* 错误状态 */
.error-container.data-v-ccc26d00 {
  padding: 120rpx 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.error-content.data-v-ccc26d00 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  text-align: center;
}
.error-text.data-v-ccc26d00 {
  font-size: 28rpx;
  color: #8E8E93;
  line-height: 1.4;
}
.retry-button.data-v-ccc26d00 {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
}
