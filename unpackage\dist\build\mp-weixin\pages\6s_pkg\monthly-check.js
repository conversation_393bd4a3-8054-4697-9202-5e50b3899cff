require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/monthly-check"],{"0019":function(e,t,n){"use strict";n.r(t);var a=n("5451"),s=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=s.a},3123:function(e,t,n){"use strict";(function(e,t){var a=n("47a9");n("357b"),n("861b");a(n("3240"));var s=a(n("de54"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(s.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"37d6":function(e,t,n){"use strict";var a=n("d3d9"),s=n.n(a);s.a},5451:function(e,t,n){"use strict";(function(e){var a=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=a(n("7eb4")),r=a(n("34cf")),i=a(n("ee10")),o={name:"MonthlyCheck",data:function(){return{loading:!1,loadingText:"加载中...",selectedTimeFilter:"current",currentStatusFilter:"all",displayLimit:4,disableAnimations:!1,timeOptions:[],statusFilters:[{label:"全部",value:"all"},{label:"已分配",value:"assigned"},{label:"待整改",value:"pending"},{label:"整改中",value:"in_progress"},{label:"待检查",value:"pending_review"},{label:"检查通过",value:"approved"}],hasManagePermission:!1,monthlyIssues:[],monthlyIssuesTotal:0,responsiblePersons:[],dataCache:new Map,cacheExpireTime:3e5,needsRefresh:!1,config:{maxPageSize:1e3,defaultDeadlineDays:7,adminUsernames:["admin"]}}},computed:{totalIssues:function(){return this.monthlyIssues.length},completedIssues:function(){return this.monthlyIssues.filter((function(e){return"approved"===e.status})).length},inProgressIssues:function(){return this.monthlyIssues.filter((function(e){return"in_progress"===e.status})).length},overdueIssues:function(){return this.monthlyIssues.filter((function(e){return"overdue"===e.status})).length},pendingIssues:function(){return this.monthlyIssues.filter((function(e){return"assigned"===e.status||"pending"===e.status||"rejected"===e.status})).length},completionRate:function(){if(0===this.totalIssues)return 0;var e=this.monthlyIssues.filter((function(e){return"approved"===e.status})).length;return Math.round(e/this.totalIssues*100)},filteredIssues:function(){var e=this;return"all"===this.currentStatusFilter?this.monthlyIssues:this.monthlyIssues.filter((function(t){return t.status===e.currentStatusFilter}))},displayedIssues:function(){return this.filteredIssues.slice(0,this.displayLimit)},hasMoreIssues:function(){return this.filteredIssues.length>this.displayLimit},remainingIssuesCount:function(){return this.filteredIssues.length-this.displayLimit}},onLoad:function(){this.initPage(),this.checkUserPermissions(),e.$on("cleaningRecordUpdated",this.handleDataUpdated),e.$on("rectificationRecordUpdated",this.handleDataUpdated),e.$on("monthlyIssueUpdated",this.handleDataUpdated),e.$on("issueDraftUpdated",this.handleDataUpdated)},onUnload:function(){e.$off("cleaningRecordUpdated",this.handleDataUpdated),e.$off("rectificationRecordUpdated",this.handleDataUpdated),e.$off("monthlyIssueUpdated",this.handleDataUpdated),e.$off("issueDraftUpdated",this.handleDataUpdated)},onShow:function(){this.checkUserPermissions(),0!==this.monthlyIssues.length||this.loading||this.loadDataSilently()},methods:{initPage:function(){var e=this;return(0,i.default)(s.default.mark((function t(){return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.showLoading("初始化页面..."),t.prev=1,t.next=4,e.loadData();case 4:e.updateTimeOptions(),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](1),e.showError("页面初始化失败");case 10:return t.prev=10,e.hideLoading(),t.finish(10);case 13:case"end":return t.stop()}}),t,null,[[1,7,10,13]])})))()},loadData:function(){var e=this;return(0,i.default)(s.default.mark((function t(){var n,a,i,o,u,l;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,n="monthly_data_".concat(e.selectedTimeFilter),a=e.dataCache.get(n),!(a&&Date.now()-a.timestamp<e.cacheExpireTime)){t.next=8;break}return e.monthlyIssues=a.issues,e.monthlyIssuesTotal=a.issuesTotal||a.issues.length,e.responsiblePersons=a.responsiblePersons,t.abrupt("return");case 8:return t.next=10,Promise.all([e.loadMonthlyIssues(),e.loadMonthlyStats()]);case 10:i=t.sent,o=(0,r.default)(i,2),u=o[0],l=o[1],e.monthlyIssues=u||[],l&&e.updateStatsFromAPI(l),e.removeDuplicateIssues(),e.calculateResponsiblePersons(),e.dataCache.set(n,{issues:e.monthlyIssues,issuesTotal:e.monthlyIssuesTotal,responsiblePersons:e.responsiblePersons,timestamp:Date.now()}),t.next=26;break;case 21:t.prev=21,t.t0=t["catch"](0),e.monthlyIssues=[],e.responsiblePersons=[],e.showError("无法加载月度数据，请检查网络连接");case 26:case"end":return t.stop()}}),t,null,[[0,21]])})))()},loadMonthlyIssues:function(){var e=this;return(0,i.default)(s.default.mark((function t(){var a,r,i,o,u;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=n("882c"),r=a.callCloudFunction,i=e.getTimeRangeForAPI(),t.next=4,r("hygiene-monthly-inspection",{action:"getMonthlyIssues",data:{start_date:i.start,end_date:i.end,page:1,pageSize:e.config.maxPageSize}});case 4:if(o=t.sent,!(o&&o.success&&o.data&&o.data.list)){t.next=9;break}return u=o.data.list.map((function(t,n){return{id:t._id||t.id,number:n+1,title:t.title,status:e.mapIssueStatus(t.status),location:e.formatLocation(t.location_info),responsible:t.assigned_to_name||"未分配",responsibleId:t.assigned_to||null,role:"负责人",deadline:t.expected_completion_date?new Date(t.expected_completion_date).toISOString().split("T")[0]:e.getDefaultDeadline(),priority:t.priority||"normal",description:t.description,createdAt:t.created_at,updatedAt:t.updated_at,images:t.photos?t.photos.map((function(e){return e.url||e})):[],history:e.generateIssueHistory(t)}})),e.monthlyIssuesTotal=o.data.total||u.length,t.abrupt("return",u);case 9:return t.abrupt("return",[]);case 10:case"end":return t.stop()}}),t)})))()},loadMonthlyStats:function(){var e=this;return(0,i.default)(s.default.mark((function t(){var a,r,i,o;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=n("882c"),r=a.callCloudFunction,t.prev=1,i=e.getTimeRangeForAPI(),t.next=5,r("hygiene-monthly-inspection",{action:"getMonthlyStatistics",data:{start_date:i.start,end_date:i.end}});case 5:if(o=t.sent,!(o&&o.success&&o.data)){t.next=8;break}return t.abrupt("return",o.data);case 8:t.next=12;break;case 10:t.prev=10,t.t0=t["catch"](1);case 12:return t.abrupt("return",null);case 13:case"end":return t.stop()}}),t,null,[[1,10]])})))()},getTimeRangeForAPI:function(){var e,t,n=new Date;switch(this.selectedTimeFilter){case"current":e=new Date(n.getFullYear(),n.getMonth(),1,0,0,0,0),t=new Date(n.getFullYear(),n.getMonth()+1,0,23,59,59,999);break;case"last":var a=0===n.getMonth()?n.getFullYear()-1:n.getFullYear(),s=0===n.getMonth()?11:n.getMonth()-1;e=new Date(a,s,1,0,0,0,0),t=new Date(n.getFullYear(),n.getMonth(),0,23,59,59,999);break;case"quarter":var r=Math.floor(n.getMonth()/3);e=new Date(n.getFullYear(),3*r,1,0,0,0,0),t=new Date(n.getFullYear(),3*r+3,0,23,59,59,999);break;case"year":e=new Date(n.getFullYear(),0,1,0,0,0,0),t=new Date(n.getFullYear(),11,31,23,59,59,999);break;default:e=new Date(n.getFullYear(),n.getMonth(),1,0,0,0,0),t=new Date(n.getFullYear(),n.getMonth()+1,0,23,59,59,999)}var i=this.formatLocalDate(e),o=this.formatLocalDate(t);return{start:i,end:o}},mapIssueStatus:function(e){return{submitted:"pending",assigned:"assigned",pending:"pending",in_progress:"in_progress",pending_review:"pending_review",approved:"approved",closed:"closed",resolved:"approved",overdue:"overdue",rejected:"rejected",reopened:"pending",draft:"pending"}[e]||"pending"},getDefaultDeadline:function(){var e=new Date;return e.setDate(e.getDate()+this.config.defaultDeadlineDays),e.toISOString().split("T")[0]},formatLocation:function(e){if(!e)return"未知位置";var t=e.location_name||"未知位置";return e.location_category&&(t="".concat(e.location_category," - ").concat(t)),t},updateStatsFromAPI:function(e){},generateIssueHistory:function(e){var t=[];return e.created_at&&t.push({action:"创建问题",description:"6S检查员发现问题并记录",time:this.formatHistoryTime(e.created_at),operator:e.reporter_name||"检查员"}),e.assigned_to_name&&t.push({action:"分配负责人",description:"将问题分配给".concat(e.assigned_to_name,"处理"),time:this.formatHistoryTime(e.updated_at||e.created_at),operator:"管理员"}),"resolved"===e.status||"approved"===e.status?t.push({action:"开始整改",description:"负责人已开始处理此问题",time:this.formatHistoryTime(e.updated_at),operator:e.assigned_to_name||"负责人"},{action:"整改完成",description:"整改措施已实施完成",time:this.formatHistoryTime(e.actual_completion_date||e.updated_at),operator:e.assigned_to_name||"负责人"}):"in_progress"===e.status&&t.push({action:"开始整改",description:"负责人已开始处理此问题",time:this.formatHistoryTime(e.updated_at),operator:e.assigned_to_name||"负责人"}),t},formatHistoryTime:function(e){if(!e)return"--";try{var t=new Date(e);return"".concat(t.getFullYear(),"-").concat(String(t.getMonth()+1).padStart(2,"0"),"-").concat(String(t.getDate()).padStart(2,"0")," ").concat(String(t.getHours()).padStart(2,"0"),":").concat(String(t.getMinutes()).padStart(2,"0"))}catch(n){return"--"}},removeDuplicateIssues:function(){var e=new Map;this.monthlyIssues.forEach((function(t){e.set(t.id,t)})),this.monthlyIssues=Array.from(e.values())},calculateResponsiblePersons:function(){var e=new Map;this.monthlyIssues.forEach((function(t){if(t.responsibleId&&t.responsible&&"未分配"!==t.responsible){var n=t.responsibleId;e.has(n)||e.set(n,{id:t.responsibleId,name:t.responsible,role:"负责人",total:0,completed:0,issues:[]});var a=e.get(n);a.total++,a.issues.push(t),"approved"===t.status&&a.completed++}})),this.responsiblePersons=Array.from(e.values())},showTimeSelector:function(){this.$refs.timePopup.open()},closeTimeSelector:function(){this.$refs.timePopup.close()},selectTimeFilter:function(e){var t=this;return(0,i.default)(s.default.mark((function n(){return s.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t.selectedTimeFilter===e.value){n.next=7;break}return t.selectedTimeFilter=e.value,t.$refs.timePopup.close(),n.next=5,t.refreshData();case 5:n.next=8;break;case 7:t.$refs.timePopup.close();case 8:case"end":return n.stop()}}),n)})))()},refreshData:function(){var t=this;return(0,i.default)(s.default.mark((function n(){return s.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t.showLoading("更新数据..."),n.prev=1,t.dataCache.clear(),n.next=5,t.loadData();case 5:e.showToast({title:"数据已更新",icon:"success"}),n.next=11;break;case 8:n.prev=8,n.t0=n["catch"](1),t.showError("数据更新失败");case 11:return n.prev=11,t.hideLoading(),n.finish(11);case 14:case"end":return n.stop()}}),n,null,[[1,8,11,14]])})))()},loadDataSilently:function(){var e=this;return(0,i.default)(s.default.mark((function t(){return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.loadData();case 3:t.next=7;break;case 5:t.prev=5,t.t0=t["catch"](0);case 7:case"end":return t.stop()}}),t,null,[[0,5]])})))()},changeStatusFilter:function(e){this.currentStatusFilter!==e&&(this.currentStatusFilter=e,this.displayLimit=4)},getCurrentTimeRange:function(){return{current:"本月",last:"上月",quarter:"本季度",year:"本年度"}[this.selectedTimeFilter]||"本月"},getSummaryTitle:function(){var e=new Date,t=e.getFullYear(),n=e.getMonth()+1,a={current:"".concat(t,"年").concat(n,"月汇总"),last:"".concat(n>1?t:t-1,"年").concat(n>1?n-1:12,"月汇总"),quarter:"".concat(t,"年Q").concat(Math.ceil(n/3),"汇总"),year:"".concat(t,"年度汇总")};return a[this.selectedTimeFilter]||"月度汇总"},getIssueTitle:function(){return{current:"本月问题跟踪",last:"上月问题跟踪",quarter:"本季度问题跟踪",year:"本年度问题跟踪"}[this.selectedTimeFilter]||"问题跟踪"},getIssueStatusText:function(e){return{assigned:"已分配",pending:"待整改",in_progress:"整改中",pending_review:"待检查",approved:"检查通过",rejected:"已驳回",overdue:"已逾期",closed:"已关闭",reopened:"重新打开"}[e]||"未知"},getStatusFilterText:function(e){return{all:"",assigned:"已分配",pending:"待整改",in_progress:"整改中",pending_review:"待检查",approved:"检查通过",rejected:"已驳回",overdue:"已逾期"}[e]||""},getProgressWidth:function(e){var t=e.total>0?e.completed/e.total*100:0;return"".concat(t,"%")},getProgressPercentage:function(e){return e.total>0?Math.round(e.completed/e.total*100):0},addNewIssue:function(){e.navigateTo({url:"/pages/6s_pkg/issue-add"})},manageIssues:function(){e.navigateTo({url:"/pages/6s_pkg/issue-list"})},exportReport:function(){var t=this;return(0,i.default)(s.default.mark((function a(){var r,i,o,u;return s.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,t.showLoading("生成报告..."),r=n("882c"),i=r.callCloudFunction,o=t.getTimeRangeForAPI(),a.next=6,i("hygiene-monthly-inspection",{action:"exportMonthlyReport",data:{start_date:o.start,end_date:o.end,time_filter:t.selectedTimeFilter}});case 6:if(u=a.sent,!u||!u.success){a.next=12;break}e.showToast({title:"报告已生成",icon:"success"}),u.data&&u.data.downloadUrl,a.next=13;break;case 12:throw new Error(u.message||"报告生成失败");case 13:a.next=18;break;case 15:a.prev=15,a.t0=a["catch"](0),e.showToast({title:"报告生成失败",icon:"error"});case 18:return a.prev=18,t.hideLoading(),a.finish(18);case 21:case"end":return a.stop()}}),a,null,[[0,15,18,21]])})))()},viewIssueDetail:function(t){e.setStorageSync("issue_detail_".concat(t.id),t),e.navigateTo({url:"/pages/6s_pkg/issue-detail?id=".concat(t.id)})},viewPersonProgress:function(t){e.navigateTo({url:"/pages/6s_pkg/person-progress?id=".concat(encodeURIComponent(t.id),"&name=").concat(encodeURIComponent(t.name))})},showMoreIssues:function(){var e=this;this.disableAnimations||(this.disableAnimations=!0,this.displayLimit+=4,setTimeout((function(){e.disableAnimations=!1}),100))},handleDataUpdated:function(e){var t=this;this.dataCache&&this.dataCache.clear(),setTimeout((function(){t.refreshDataSilently()}),200)},refreshDataSilently:function(){var e=this;return(0,i.default)(s.default.mark((function t(){return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.loading){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,e.dataCache.clear(),t.next=6,new Promise((function(t){setTimeout((0,i.default)(s.default.mark((function n(){var a,i,o,u,l;return s.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,Promise.all([e.loadMonthlyIssues(),e.loadMonthlyStats()]);case 3:a=n.sent,i=(0,r.default)(a,2),o=i[0],u=i[1],e.monthlyIssues=o||[],o&&0!==o.length||(e.monthlyIssuesTotal=0),u&&e.updateStatsFromAPI(u),e.removeDuplicateIssues(),e.calculateResponsiblePersons(),l="monthly_data_".concat(e.selectedTimeFilter),e.dataCache.set(l,{issues:e.monthlyIssues,issuesTotal:e.monthlyIssuesTotal,responsiblePersons:e.responsiblePersons,timestamp:Date.now()}),t(),n.next=20;break;case 17:n.prev=17,n.t0=n["catch"](0),t();case 20:case"end":return n.stop()}}),n,null,[[0,17]])}))),100)}));case 6:e.needsRefresh=!1,t.next=11;break;case 9:t.prev=9,t.t0=t["catch"](2);case 11:case"end":return t.stop()}}),t,null,[[2,9]])})))()},checkUserPermissions:function(){var t=this;return(0,i.default)(s.default.mark((function a(){var r,i,o,u,l,c,d;return s.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:try{r=n("eddf"),i=r.getCacheKey,o=r.CACHE_KEYS,u=e.getStorageSync("uni-id-pages-userInfo")||{},l=e.getStorageSync(i(o.USER_ROLE))||"{}",c={};try{c="string"===typeof l?JSON.parse(l):l}catch(s){c={}}d=[],c&&c.value&&c.value.userRole?d=Array.isArray(c.value.userRole)?c.value.userRole:[c.value.userRole]:u.role?d=Array.isArray(u.role)?u.role:[u.role]:t.config.adminUsernames.includes(u.username)&&(d=["admin"]),t.hasManagePermission=d.includes("admin")||d.includes("Integrated")||d.includes("reviser")}catch(p){t.hasManagePermission=!1}case 1:case"end":return a.stop()}}),a)})))()},updateTimeOptions:function(){var e=new Date,t=e.getFullYear(),n=e.getMonth()+1,a=Math.ceil(n/3);this.timeOptions=[{label:"本月",value:"current",icon:"calendar",desc:"".concat(t,"年").concat(n,"月")},{label:"上月",value:"last",icon:"calendar",desc:"".concat(n>1?t:t-1,"年").concat(n>1?n-1:12,"月")},{label:"本季度",value:"quarter",icon:"calendar",desc:"".concat(t,"年Q").concat(a)},{label:"本年度",value:"year",icon:"calendar",desc:"".concat(t,"年")}]},showLoading:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"加载中...";this.loading=!0,this.loadingText=e},hideLoading:function(){this.loading=!1},showError:function(t){e.showToast({title:t,icon:"none",duration:3e3})},delay:function(e){return new Promise((function(t){return setTimeout(t,e)}))},formatDeadline:function(e){var t=new Date(e),n=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),s=String(t.getDate()).padStart(2,"0");return"".concat(n,"-").concat(a,"-").concat(s)},formatLocalDate:function(e){var t=e.getFullYear(),n=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0");return"".concat(t,"-").concat(n,"-").concat(a)}}};t.default=o}).call(this,n("df3c")["default"])},"73e4":function(e,t,n){"use strict";n.d(t,"b",(function(){return s})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a}));var a={uniIcons:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(n.bind(null,"6ddf"))},pEmptyState:function(){return n.e("components/p-empty-state/p-empty-state").then(n.bind(null,"9b76"))},uniPopup:function(){return n.e("uni_modules/uni-popup/components/uni-popup/uni-popup").then(n.bind(null,"a2b7"))}},s=function(){var e=this,t=e.$createElement,n=(e._self._c,e.getSummaryTitle()),a=e.getCurrentTimeRange(),s=e.getIssueTitle(),r=e.filteredIssues.length,i=r>0?e.__map(e.displayedIssues,(function(t,n){var a=e.__get_orig(t),s=String(n+1).padStart(2,"0"),r=e.formatDeadline(t.deadline),i=e.getIssueStatusText(t.status);return{$orig:a,g1:s,m3:r,m4:i}})):null,o=r>0?null:e.getStatusFilterText(e.currentStatusFilter)||"",u=e.responsiblePersons.length,l=u>0?e.__map(e.responsiblePersons,(function(t,n){var a=e.__get_orig(t),s=e.getProgressPercentage(t),r=e.getProgressPercentage(t),i=e.getProgressPercentage(t),o=e.getProgressWidth(t),u=t.issues&&t.issues.length>0,l=u?t.issues.slice(0,2):null,c=u?t.issues.length:null,d=u&&c>2?t.issues.length:null;return{$orig:a,m6:s,m7:r,m8:i,m9:o,g3:u,l1:l,g4:c,g5:d}})):null;e.$mp.data=Object.assign({},{$root:{m0:n,m1:a,m2:s,g0:r,l0:i,m5:o,g2:u,l2:l}})},r=[]},d3d9:function(e,t,n){},de54:function(e,t,n){"use strict";n.r(t);var a=n("73e4"),s=n("0019");for(var r in s)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return s[e]}))}(r);n("37d6");var i=n("828b"),o=Object(i["a"])(s["default"],a["b"],a["c"],!1,null,"6b5fa68e",null,!1,a["a"],void 0);t["default"]=o.exports}},[["3123","common/runtime","common/vendor"]]]);