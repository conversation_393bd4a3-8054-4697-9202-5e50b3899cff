{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/patrol_pkg/route/detail.vue?7766", "webpack:///D:/Xwzc/pages/patrol_pkg/route/detail.vue?371f", "webpack:///D:/Xwzc/pages/patrol_pkg/route/detail.vue?cdd2", "webpack:///D:/Xwzc/pages/patrol_pkg/route/detail.vue?fe75", "uni-app:///pages/patrol_pkg/route/detail.vue", "webpack:///D:/Xwzc/pages/patrol_pkg/route/detail.vue?31d6", "webpack:///D:/Xwzc/pages/patrol_pkg/route/detail.vue?1670"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "PEmptyState", "data", "route_id", "routeInfo", "name", "status", "points", "pointsDetail", "remarks", "createTime", "updateTime", "mapCenter", "latitude", "longitude", "mapScale", "markers", "polyline", "routeTotalDistance", "onLoad", "uni", "title", "icon", "setTimeout", "methods", "getRouteDetail", "PatrolApi", "res", "calculateDistance", "Math", "deg2rad", "createDistanceMarkers", "point", "distanceMarkers", "id", "iconPath", "width", "height", "anchor", "x", "y", "label", "content", "color", "bgColor", "fontSize", "borderRadius", "borderWidth", "borderColor", "padding", "textAlign", "anchorX", "anchorY", "initMapData", "sumLng", "validPoints", "sumLat", "callout", "display", "dottedLine", "arrowLine", "calculateTotalDistance", "totalDistance", "handleZoomIn", "handleZoomOut", "handleResetMap", "handleEdit", "url", "handleBack", "formatDate", "formatDistance"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AAAgmB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC+GpnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;MACAC;QACAC;QACAC;MACA;MACAC;QACAH;MACA;IACA;EACA;EACAI;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAL;kBACAC;gBACA;gBAAA;gBAAA;gBAAA,OAGAK;kBACAvB;gBACA;cAAA;gBAFAwB;gBAIA;kBACA;;kBAEA;kBACA;gBACA;kBACAP;oBACAC;oBACAC;kBACA;kBACAC;oBACAH;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;kBACAC;kBACAC;gBACA;gBACAC;kBACAH;gBACA;cAAA;gBAAA;gBAEAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAQ;MACA;MACA;MACA;MACA,QACAC,0CACAA,8DACAA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MAEA;QAAA,OACAC,kBACA,+CACA;MAAA,EACA;MAEA;MAEA;;MAEA;MACA;QACA;QACA;QAEA;QAEA;QACA;QACA;QACA;QAEA;;QAEA;QACA;;QAEA;QACA;QACA;;QAEA;QACA,gDACA,0DACAH;;QAEA;QACAI;UACAC;UACArB;UACAC;UACAqB;UACAC;UACAC;UACAC;YACAC;YACAC;UACA;UACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACA;MACA;;MAEA;MACA;QAAA,OACArB,kBACA,+CACA;MAAA,EACA;MAEA;QACA;QACA;UACAnB;UACAC;QACA;QACA;MACA;;MAEA;MACA;QAAAwC;MACAC;QACAC;QACAF;MACA;MAEA;QACAzC;QACAC;MACA;;MAEA;MACA;QACA;UACAoB;UACArB;UACAC;UACAO;UACAc;UACAC;UACAC;UACAC;YACAC;YACAC;UACA;UACAiB;YACAf;YACAC;YACAE;YACAC;YACAF;YACAK;YACAS;UACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;UACAnD;YAAA;cACAM;cACAC;YACA;UAAA;UACA6B;UACAP;UACAuB;UACAC;UACAZ;UACAD;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAc;MACA;MAEA;QACA;QACA;QAEA;QAEA;QACA;QACA;QACA;QAEA;QACAC;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA9C;QACA+C;MACA;IACA;IAEA;IACAC;MACAhD;IACA;IAEA;IACAiD;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/bA;AAAA;AAAA;AAAA;AAA2oC,CAAgB,inCAAG,EAAC,C;;;;;;;;;;;ACA/pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/patrol_pkg/route/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/patrol_pkg/route/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=71df4ae4&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/patrol_pkg/route/detail.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=template&id=71df4ae4&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.routeInfo.points ? _vm.routeInfo.points.length : null\n  var m0 =\n    _vm.routeTotalDistance > 0\n      ? _vm.formatDistance(_vm.routeTotalDistance)\n      : null\n  var m1 = _vm.formatDate(_vm.routeInfo.createTime)\n  var m2 = _vm.formatDate(_vm.routeInfo.updateTime)\n  var g1 = _vm.routeInfo.pointsDetail && _vm.routeInfo.pointsDetail.length > 0\n  var g2 = _vm.routeInfo.pointsDetail && _vm.routeInfo.pointsDetail.length > 0\n  var g3 =\n    !_vm.routeInfo.pointsDetail || _vm.routeInfo.pointsDetail.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"route-detail-container\">\n\t\t<!-- 基本信息区域 -->\n\t\t<view class=\"detail-card\">\n\t\t\t<view class=\"section-header\">\n\t\t\t\t<view class=\"section-title\">基本信息</view>\n\t\t\t</view>\n\t\t\t<view class=\"detail-info\">\n\t\t\t\t<view class=\"detail-header\">\n\t\t\t\t\t<text class=\"route-name\">{{ routeInfo.name }}</text>\n\t\t\t\t\t<view class=\"route-status\" :class=\"{ 'status-enabled': routeInfo.status === 1, 'status-disabled': routeInfo.status === 0 }\">\n\t\t\t\t\t\t{{ routeInfo.status === 1 ? '启用中' : '已停用' }}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\" v-if=\"routeInfo.remarks\">\n\t\t\t\t\t<uni-icons type=\"info\" size=\"16\" color=\"#999999\"></uni-icons>\n\t\t\t\t\t<text class=\"info-label\">备注</text>\n\t\t\t\t\t<text class=\"info-value\">{{ routeInfo.remarks }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<uni-icons type=\"map\" size=\"16\" color=\"#999999\"></uni-icons>\n\t\t\t\t\t<text class=\"info-label\">点位数量</text>\n\t\t\t\t\t<text class=\"info-value\">{{ routeInfo.points ? routeInfo.points.length : 0 }}个</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\" v-if=\"routeTotalDistance > 0\">\n\t\t\t\t\t<uni-icons type=\"arrow-right\" size=\"16\" color=\"#999999\"></uni-icons>\n\t\t\t\t\t<text class=\"info-label\">总距离</text>\n\t\t\t\t\t<text class=\"info-value\">{{ formatDistance(routeTotalDistance) }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<uni-icons type=\"calendar\" size=\"16\" color=\"#999999\"></uni-icons>\n\t\t\t\t\t<text class=\"info-label\">创建时间</text>\n\t\t\t\t\t<text class=\"info-value\">{{ formatDate(routeInfo.createTime) }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<uni-icons type=\"refresh\" size=\"16\" color=\"#999999\"></uni-icons>\n\t\t\t\t\t<text class=\"info-label\">更新时间</text>\n\t\t\t\t\t<text class=\"info-value\">{{ formatDate(routeInfo.updateTime) }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 地图预览区域 -->\n\t\t<view class=\"detail-card\" v-if=\"routeInfo.pointsDetail && routeInfo.pointsDetail.length > 0\">\n\t\t\t<view class=\"section-header\">\n\t\t\t\t<view class=\"section-title\">路线地图</view>\n\t\t\t</view>\n\t\t\t<view class=\"map-container\">\n\t\t\t\t<map \n\t\t\t\t\tclass=\"route-map\" \n\t\t\t\t\t:latitude=\"mapCenter.latitude\" \n\t\t\t\t\t:longitude=\"mapCenter.longitude\" \n\t\t\t\t\t:markers=\"markers\"\n\t\t\t\t\t:polyline=\"polyline\"\n\t\t\t\t\t:scale=\"mapScale\"\n\t\t\t\t\tshow-location\n\t\t\t\t></map>\n\t\t\t\t\n\t\t\t\t<!-- 地图控制按钮 -->\n\t\t\t\t<view class=\"map-controls\">\n\t\t\t\t\t<view class=\"map-control-btn\" @click=\"handleZoomIn\">\n\t\t\t\t\t\t<uni-icons type=\"plus\" size=\"20\" color=\"#333333\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"map-control-btn\" @click=\"handleZoomOut\">\n\t\t\t\t\t\t<uni-icons type=\"minus\" size=\"20\" color=\"#333333\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"map-control-btn\" @click=\"handleResetMap\">\n\t\t\t\t\t\t<uni-icons type=\"redo\" size=\"20\" color=\"#333333\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 点位列表区域 -->\n\t\t<view class=\"detail-card\" v-if=\"routeInfo.pointsDetail && routeInfo.pointsDetail.length > 0\">\n\t\t\t<view class=\"section-header\">\n\t\t\t\t<view class=\"section-title\">点位列表</view>\n\t\t\t</view>\n\t\t\t<view class=\"points-list\">\n\t\t\t\t<view \n\t\t\t\t\tclass=\"point-item\" \n\t\t\t\t\tv-for=\"(point, index) in routeInfo.pointsDetail\" \n\t\t\t\t\t:key=\"point.point_id\"\n\t\t\t\t\t:style=\"{ animationDelay: index * 0.05 + 's' }\"\n\t\t\t\t>\n\t\t\t\t\t<view class=\"point-order\">{{ index + 1 }}</view>\n\t\t\t\t\t<view class=\"point-info\">\n\t\t\t\t\t\t<text class=\"point-name\">{{ point.name }}</text>\n\t\t\t\t\t\t<text class=\"point-address\" v-if=\"point.address\">{{ point.address }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 空提示 -->\n\t\t<view class=\"empty-points\" v-if=\"!routeInfo.pointsDetail || routeInfo.pointsDetail.length === 0\">\n\t\t\t<p-empty-state type=\"data\" text=\"该线路暂无点位\" />\n\t\t\t<text class=\"empty-tip\">请在编辑页面添加点位</text>\n\t\t</view>\n\t\t\n\t\t<!-- 底部操作区域 - 集成到内容中 -->\n\t\t<view class=\"buttons-container\">\n\t\t\t<view class=\"action-buttons\">\n\t\t\t\t<view class=\"btn-secondary\" @click=\"handleBack\">返回</view>\n\t\t\t\t<view class=\"btn-primary\" @click=\"handleEdit\">编辑线路</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport PatrolApi from '@/utils/patrol-api.js';\nimport PEmptyState from '@/components/p-empty-state/p-empty-state.vue';\n\nexport default {\n\tcomponents: {\n\t\tPEmptyState\n\t},\n\tdata() {\n\t\treturn {\n\t\t\troute_id: '',\n\t\t\trouteInfo: {\n\t\t\t\tname: '',\n\t\t\t\tstatus: 1,\n\t\t\t\tpoints: [],\n\t\t\t\tpointsDetail: [],\n\t\t\t\tremarks: '',\n\t\t\t\tcreateTime: 0,\n\t\t\t\tupdateTime: 0\n\t\t\t},\n\t\t\tmapCenter: {\n\t\t\t\tlatitude: 39.908823,\n\t\t\t\tlongitude: 116.397470\n\t\t\t},\n\t\t\tmapScale: 16,\n\t\t\tmarkers: [],\n\t\t\tpolyline: [],\n\t\t\trouteTotalDistance: 0\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tif (options.id) {\n\t\t\tthis.route_id = options.id;\n\t\t\tthis.getRouteDetail();\n\t\t} else {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '参数错误',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.navigateBack();\n\t\t\t}, 1500);\n\t\t}\n\t},\n\tmethods: {\n\t\t// 获取线路详情\n\t\tasync getRouteDetail() {\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '加载中...'\n\t\t\t});\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst res = await PatrolApi.callRouteFunction('getRouteDetail', {\n\t\t\t\t\troute_id: this.route_id\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\tthis.routeInfo = res.data;\n\t\t\t\t\t\n\t\t\t\t\t// 初始化地图数据\n\t\t\t\t\tthis.initMapData();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.message || '获取线路详情失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t}, 1500);\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '获取线路详情出错',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t}, 1500);\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 计算两点之间的距离（米）\n\t\tcalculateDistance(lat1, lng1, lat2, lng2) {\n\t\t\tconst R = 6371000; // 地球半径，单位米\n\t\t\tconst dLat = this.deg2rad(lat2 - lat1);\n\t\t\tconst dLng = this.deg2rad(lng2 - lng1);\n\t\t\tconst a = \n\t\t\t\tMath.sin(dLat/2) * Math.sin(dLat/2) +\n\t\t\t\tMath.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) * \n\t\t\t\tMath.sin(dLng/2) * Math.sin(dLng/2);\n\t\t\tconst c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n\t\t\tconst distance = R * c; // 距离，单位米\n\t\t\treturn distance;\n\t\t},\n\t\t\n\t\t// 角度转弧度\n\t\tdeg2rad(deg) {\n\t\t\treturn deg * (Math.PI/180);\n\t\t},\n\t\t\n\t\t// 创建距离标记\n\t\tcreateDistanceMarkers() {\n\t\t\tif (!this.routeInfo.pointsDetail || this.routeInfo.pointsDetail.length < 2) return [];\n\t\t\t\n\t\t\tconst validPoints = this.routeInfo.pointsDetail.filter(point => \n\t\t\t\tpoint.location && \n\t\t\t\ttypeof point.location.latitude === 'number' && \n\t\t\t\ttypeof point.location.longitude === 'number'\n\t\t\t);\n\t\t\t\n\t\t\tif (validPoints.length < 2) return [];\n\t\t\t\n\t\t\tconst distanceMarkers = [];\n\t\t\t\n\t\t\t// 为每两个相邻点位创建距离标记\n\t\t\tfor (let i = 0; i < validPoints.length - 1; i++) {\n\t\t\t\tconst point1 = validPoints[i].location;\n\t\t\t\tconst point2 = validPoints[i + 1].location;\n\t\t\t\t\n\t\t\t\tif (!point1 || !point2) continue;\n\t\t\t\t\n\t\t\t\tconst lat1 = parseFloat(point1.latitude) || 0;\n\t\t\t\tconst lng1 = parseFloat(point1.longitude) || 0;\n\t\t\t\tconst lat2 = parseFloat(point2.latitude) || 0;\n\t\t\t\tconst lng2 = parseFloat(point2.longitude) || 0;\n\t\t\t\t\n\t\t\t\tif (!lat1 || !lng1 || !lat2 || !lng2) continue;\n\t\t\t\t\n\t\t\t\t// 计算两点之间的距离\n\t\t\t\tconst distance = this.calculateDistance(lat1, lng1, lat2, lng2);\n\t\t\t\t\n\t\t\t\t// 计算两点之间的中点位置，作为标记位置\n\t\t\t\tconst midLat = (lat1 + lat2) / 2;\n\t\t\t\tconst midLng = (lng1 + lng2) / 2;\n\t\t\t\t\n\t\t\t\t// 根据距离显示不同单位\n\t\t\t\tconst distanceLabel = distance > 1000 \n\t\t\t\t\t? `${(distance/1000).toFixed(2)}公里` \n\t\t\t\t\t: `${Math.round(distance)}米`;\n\t\t\t\t\n\t\t\t\t// 创建标记\n\t\t\t\tdistanceMarkers.push({\n\t\t\t\t\tid: 1000 + i, \n\t\t\t\t\tlatitude: midLat,\n\t\t\t\t\tlongitude: midLng,\n\t\t\t\t\ticonPath: '/static/map/transparent.png',\n\t\t\t\t\twidth: 1,\n\t\t\t\t\theight: 1,\n\t\t\t\t\tanchor: {\n\t\t\t\t\t\tx: 0.5,\n\t\t\t\t\t\ty: 0.5\n\t\t\t\t\t},\n\t\t\t\t\tlabel: {\n\t\t\t\t\t\tcontent: distanceLabel,\n\t\t\t\t\t\tcolor: '#1677FF',\n\t\t\t\t\t\tbgColor: '#FFFFFF',\n\t\t\t\t\t\tfontSize: 12,\n\t\t\t\t\t\tborderRadius: 4,\n\t\t\t\t\t\tborderWidth: 1,\n\t\t\t\t\t\tborderColor: '#EEEEEE',\n\t\t\t\t\t\tpadding: 6,\n\t\t\t\t\t\ttextAlign: 'center',\n\t\t\t\t\t\tanchorX: 0,\n\t\t\t\t\t\tanchorY: -30\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t\t\n\t\t\treturn distanceMarkers;\n\t\t},\n\t\t\n\t\t// 初始化地图数据\n\t\tinitMapData() {\n\t\t\tif (!this.routeInfo.pointsDetail || this.routeInfo.pointsDetail.length === 0) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 过滤出有效的坐标点\n\t\t\tconst validPoints = this.routeInfo.pointsDetail.filter(point => \n\t\t\t\tpoint.location && \n\t\t\t\ttypeof point.location.latitude === 'number' && \n\t\t\t\ttypeof point.location.longitude === 'number'\n\t\t\t);\n\n\t\t\tif (validPoints.length === 0) {\n\t\t\t\t// 如果没有有效点，设置默认位置\n\t\t\t\tthis.mapCenter = {\n\t\t\t\t\tlatitude: 39.908823,\n\t\t\t\t\tlongitude: 116.397470\n\t\t\t\t};\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 计算有效点的平均坐标作为地图中心\n\t\t\tlet sumLat = 0, sumLng = 0;\n\t\t\tvalidPoints.forEach(point => {\n\t\t\t\tsumLat += point.location.latitude;\n\t\t\t\tsumLng += point.location.longitude;\n\t\t\t});\n\t\t\t\n\t\t\tthis.mapCenter = {\n\t\t\t\tlatitude: sumLat / validPoints.length,\n\t\t\t\tlongitude: sumLng / validPoints.length\n\t\t\t};\n\n\t\t\t// 创建标记点\n\t\t\tconst pointMarkers = validPoints.map((point, index) => {\n\t\t\t\treturn {\n\t\t\t\t\tid: index,\n\t\t\t\t\tlatitude: point.location.latitude,\n\t\t\t\t\tlongitude: point.location.longitude,\n\t\t\t\t\ttitle: point.name,\n\t\t\t\t\ticonPath: '/static/map/marker.png',\n\t\t\t\t\twidth: 32,\n\t\t\t\t\theight: 32,\n\t\t\t\t\tanchor: {\n\t\t\t\t\t\tx: 0.5,\n\t\t\t\t\t\ty: 1\n\t\t\t\t\t},\n\t\t\t\t\tcallout: {\n\t\t\t\t\t\tcontent: `${index + 1}. ${point.name}`,\n\t\t\t\t\t\tcolor: '#FFFFFF',\n\t\t\t\t\t\tfontSize: 12,\n\t\t\t\t\t\tborderRadius: 4,\n\t\t\t\t\t\tbgColor: '#1677FF',\n\t\t\t\t\t\tpadding: 5,\n\t\t\t\t\t\tdisplay: 'ALWAYS'\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t});\n\t\t\t\n\t\t\t// 不再添加距离标记\n\t\t\t// const distanceMarkers = this.createDistanceMarkers();\n\t\t\t\n\t\t\t// 只使用点位标记\n\t\t\tthis.markers = pointMarkers;\n\n\t\t\t// 创建路线连接\n\t\t\tif (validPoints.length > 1) {\n\t\t\t\tthis.polyline = [{\n\t\t\t\t\tpoints: validPoints.map(point => ({\n\t\t\t\t\t\tlatitude: point.location.latitude,\n\t\t\t\t\t\tlongitude: point.location.longitude\n\t\t\t\t\t})),\n\t\t\t\t\tcolor: '#1677FF',\n\t\t\t\t\twidth: 4,\n\t\t\t\t\tdottedLine: false,\n\t\t\t\t\tarrowLine: true,\n\t\t\t\t\tborderColor: '#E7F1FF',\n\t\t\t\t\tborderWidth: 1\n\t\t\t\t}];\n\t\t\t}\n\n\t\t\t// 计算总距离\n\t\t\tthis.calculateTotalDistance(validPoints);\n\t\t},\n\t\t\n\t\t// 计算路线总距离\n\t\tcalculateTotalDistance(validPoints) {\n\t\t\tlet totalDistance = 0;\n\t\t\t\n\t\t\tfor (let i = 0; i < validPoints.length - 1; i++) {\n\t\t\t\tconst point1 = validPoints[i].location;\n\t\t\t\tconst point2 = validPoints[i + 1].location;\n\t\t\t\t\n\t\t\t\tif (!point1 || !point2) continue;\n\t\t\t\t\n\t\t\t\tconst lat1 = parseFloat(point1.latitude) || 0;\n\t\t\t\tconst lng1 = parseFloat(point1.longitude) || 0;\n\t\t\t\tconst lat2 = parseFloat(point2.latitude) || 0;\n\t\t\t\tconst lng2 = parseFloat(point2.longitude) || 0;\n\t\t\t\t\n\t\t\t\tconst segmentDistance = this.calculateDistance(lat1, lng1, lat2, lng2);\n\t\t\t\ttotalDistance += segmentDistance;\n\t\t\t}\n\t\t\t\n\t\t\tthis.routeTotalDistance = Math.round(totalDistance);\n\t\t},\n\t\t\n\t\t// 放大地图\n\t\thandleZoomIn() {\n\t\t\tif (this.mapScale < 20) {\n\t\t\t\tthis.mapScale += 1;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 缩小地图\n\t\thandleZoomOut() {\n\t\t\tif (this.mapScale > 5) {\n\t\t\t\tthis.mapScale -= 1;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 重置地图\n\t\thandleResetMap() {\n\t\t\tthis.initMapData();\n\t\t\tthis.mapScale = 16;\n\t\t},\n\t\t\n\t\t// 编辑线路\n\t\thandleEdit() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: './edit?id=' + this.route_id\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 返回\n\t\thandleBack() {\n\t\t\tuni.navigateBack();\n\t\t},\n\t\t\n\t\t// 格式化日期\n\t\tformatDate(timestamp) {\n\t\t\tif (!timestamp) return '';\n\t\t\t\n\t\t\tconst date = new Date(timestamp);\n\t\t\tconst year = date.getFullYear();\n\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\n\t\t\tconst day = String(date.getDate()).padStart(2, '0');\n\t\t\tconst hour = String(date.getHours()).padStart(2, '0');\n\t\t\tconst minute = String(date.getMinutes()).padStart(2, '0');\n\t\t\t\n\t\t\treturn `${year}-${month}-${day} ${hour}:${minute}`;\n\t\t},\n\t\t\n\t\t// 格式化距离\n\t\tformatDistance(meters) {\n\t\t\tif (!meters) return '0米';\n\t\t\tif (meters < 1000) {\n\t\t\t\treturn `${meters.toFixed(0)}米`;\n\t\t\t} else {\n\t\t\t\treturn `${(meters / 1000).toFixed(2)}公里`;\n\t\t\t}\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\">\n/* 主色调变量 */\n$primary-color: #1677FF; // 支付宝蓝\n$primary-light: #E7F1FF;\n$primary-dark: #0E5FD8;\n\n$success-color: #07C160;\n$warning-color: #FFA300;\n$danger-color: #FF3B30;\n$info-color: #8F959E;\n\n$text-primary: #2C3E50;\n$text-secondary: #666666;\n$text-tertiary: #999999;\n$border-color: #EAEAEA;\n$background: #F5F5F5;\n\n$radius-sm: 6rpx;\n$radius-md: 12rpx;\n$radius-lg: 16rpx;\n$radius-xl: 24rpx;\n$radius-full: 999rpx;\n\n$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n$shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\n$shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);\n\n/* 淡入动画 */\n@keyframes fadeIn {\n\tfrom {\n\t\topacity: 0;\n\t\ttransform: translateY(20rpx);\n\t}\n\tto {\n\t\topacity: 1;\n\t\ttransform: translateY(0);\n\t}\n}\n\n.route-detail-container {\n\tpadding-bottom: 50rpx;\n\tbackground-color: #F5F5F5;\n\tmin-height: 100vh;\n}\n\n.detail-card {\n\tbackground-color: #FFFFFF;\n\tmargin: 20rpx;\n\tborder-radius: $radius-lg;\n\toverflow: hidden;\n\tbox-shadow: $shadow-sm;\n\tanimation: fadeIn 0.3s ease-out forwards;\n}\n\n.section-header {\n\tpadding: 24rpx 30rpx;\n\tborder-bottom: 1px solid $border-color;\n}\n\n.section-title {\n\tfont-size: 30rpx;\n\tfont-weight: 600;\n\tcolor: $text-primary;\n\tposition: relative;\n\tpadding-left: 20rpx;\n\t\n\t&::before {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\twidth: 6rpx;\n\t\theight: 24rpx;\n\t\tbackground-color: $primary-color;\n\t\tborder-radius: $radius-sm;\n\t}\n}\n\n.detail-info {\n\tpadding: 24rpx 30rpx;\n}\n\n.detail-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 24rpx;\n\tpadding-bottom: 20rpx;\n\tborder-bottom: 1px solid $border-color;\n}\n\n.route-name {\n\tfont-size: 34rpx;\n\tfont-weight: 600;\n\tcolor: $text-primary;\n\tflex: 1;\n}\n\n.route-status {\n\tfont-size: 24rpx;\n\tpadding: 6rpx 20rpx;\n\tborder-radius: $radius-full;\n\tfont-weight: 500;\n\t\n\t&.status-enabled {\n\t\tbackground-color: #E6F7F0;\n\t\tcolor: $success-color;\n\t}\n\t\n\t&.status-disabled {\n\t\tbackground-color: #FFF1F0;\n\t\tcolor: $danger-color;\n\t}\n}\n\n.info-item {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n\tfont-size: 28rpx;\n\t\n\t&:last-child {\n\t\tmargin-bottom: 0;\n\t}\n}\n\n.info-label {\n\tcolor: $text-secondary;\n\tmargin: 0 16rpx 0 8rpx;\n\tmin-width: 120rpx;\n}\n\n.info-value {\n\tcolor: $text-primary;\n\tflex: 1;\n}\n\n.map-container {\n\tmargin: 20rpx;\n\twidth: auto;\n\theight: 400rpx;\n\tborder-radius: $radius-md;\n\toverflow: hidden;\n\tposition: relative;\n\tbox-shadow: $shadow-sm;\n}\n\n.route-map {\n\twidth: 100%;\n\theight: 100%;\n\tborder-radius: $radius-md;\n}\n\n.map-controls {\n\tposition: absolute;\n\tright: 20rpx;\n\ttop: 20rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n\tz-index: 10;\n}\n\n.map-control-btn {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tbackground-color: #FFFFFF;\n\tborder-radius: $radius-md;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-bottom: 10rpx;\n\tbox-shadow: $shadow-sm;\n\t\n\t&:active {\n\t\topacity: 0.8;\n\t\ttransform: scale(0.95);\n\t}\n}\n\n.points-list {\n\tpadding: 20rpx 30rpx;\n}\n\n.point-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 20rpx 0;\n\tborder-bottom: 1px solid $border-color;\n\tanimation: fadeIn 0.5s ease-out forwards;\n\t\n\t&:last-child {\n\t\tborder-bottom: none;\n\t}\n}\n\n.point-order {\n\twidth: 48rpx;\n\theight: 48rpx;\n\tbackground-color: $primary-color;\n\tcolor: #FFFFFF;\n\tfont-size: 28rpx;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 20rpx;\n}\n\n.point-info {\n\tflex: 1;\n}\n\n.point-name {\n\tfont-size: 28rpx;\n\tcolor: $text-primary;\n\tfont-weight: 600;\n\tmargin-bottom: 8rpx;\n\tdisplay: block;\n}\n\n.point-address {\n\tfont-size: 24rpx;\n\tcolor: $text-tertiary;\n\tdisplay: block;\n}\n\n.empty-points {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 100rpx 0;\n\t\n\t.empty-tip {\n\t\tfont-size: 26rpx;\n\t\tcolor: $text-tertiary;\n\t\tmargin-top: 20rpx;\n\t\tpadding: 8rpx 24rpx;\n\t\tbackground-color: $primary-light;\n\t\tborder-radius: $radius-full;\n\t\topacity: 0.8;\n\t}\n}\n\n/* 新增按钮区样式 */\n.buttons-container {\n\tmargin: 30rpx 20rpx;\n\tpadding: 0;\n}\n\n.action-buttons {\n\tdisplay: flex;\n\tgap: 20rpx;\n\tbackground-color: #FFFFFF;\n\tpadding: 20rpx 30rpx;\n\tborder-radius: 12rpx;\n\tbox-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.05);\n}\n\n.btn-primary, .btn-secondary {\n\tflex: 1;\n\theight: 80rpx;\n\tborder-radius: 40rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\ttransition: all 0.3s ease;\n\t\n\t&:active {\n\t\ttransform: scale(0.95);\n\t\topacity: 0.9;\n\t}\n}\n\n.btn-primary {\n\tbackground-color: #1677FF;\n\tcolor: #FFFFFF;\n}\n\n.btn-secondary {\n\tbackground-color: #F5F5F5;\n\tcolor: #666666;\n\tborder: 1rpx solid #EEEEEE;\n}\n\n/* 删除旧的按钮样式 */\n.action-section {\n\tdisplay: none;\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775842109\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}