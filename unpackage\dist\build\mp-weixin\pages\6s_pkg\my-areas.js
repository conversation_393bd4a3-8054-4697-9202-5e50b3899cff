require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/my-areas"],{"3c80":function(e,t,a){"use strict";(function(e,t){var n=a("47a9");a("357b"),a("861b");n(a("3240"));var r=n(a("ca27"));e.__webpack_require_UNI_MP_PLUGIN__=a,t(r.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},4449:function(e,t,a){"use strict";var n=a("aff4"),r=a.n(n);r.a},aff4:function(e,t,a){},ca27:function(e,t,a){"use strict";a.r(t);var n=a("dbbf"),r=a("ce45");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("4449");var c=a("828b"),s=Object(c["a"])(r["default"],n["b"],n["c"],!1,null,"6e170f97",null,!1,n["a"],void 0);t["default"]=s.exports},ce45:function(e,t,a){"use strict";a.r(t);var n=a("fd9e"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},dbbf:function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return n}));var n={uniIcons:function(){return Promise.all([a.e("common/vendor"),a.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(a.bind(null,"6ddf"))},pEmptyState:function(){return a.e("components/p-empty-state/p-empty-state").then(a.bind(null,"9b76"))},uniPopup:function(){return a.e("uni_modules/uni-popup/components/uni-popup/uni-popup").then(a.bind(null,"a2b7"))},uniCalendar:function(){return Promise.all([a.e("common/vendor"),a.e("uni_modules/uni-calendar/components/uni-calendar/uni-calendar")]).then(a.bind(null,"31a6"))}},r=function(){var e=this,t=e.$createElement,a=(e._self._c,e.getStatusTitle()),n=e.getCurrentTimeRange(),r=e.loading?null:e.myFixedAreas.length,i=e.loading?null:e.__map(e.myFixedAreas,(function(t,a){var n=e.__get_orig(t),r=e.getIconColor(t.status),i=e.getStatusText(t.status);return{$orig:n,m2:r,m3:i}})),c=e.myPublicAreas.length>0||!e.loading&&e.dataLoaded,s=c&&!e.loading?e.myPublicAreas.length:null,o=c&&!e.loading?e.__map(e.myPublicAreas,(function(t,a){var n=e.__get_orig(t),r=e.getIconColor(t.status),i=e.getStatusText(t.status);return{$orig:n,m4:r,m5:i}})):null,u=e.loading?null:e.groupedRectificationTasks.length,l=e.loading?null:e.__map(e.groupedRectificationTasks,(function(t,a){var n=e.__get_orig(t),r=t.tasks.length,i=t.expanded?e.__map(t.tasks,(function(t,a){var n=e.__get_orig(t),r=e.getIconColor(t.status),i=e.getStatusText(t.status);return{$orig:n,m6:r,m7:i}})):null;return{$orig:n,g4:r,l2:i}})),d=e.loading?null:e.cleaningRecords.list.length,g=e.loading?null:e.__map(e.cleaningRecords.list,(function(t,a){var n=e.__get_orig(t),r=e.getIconColor(t.status),i=t.remark?t.remark.substring(0,15):null,c=t.remark?t.remark.length:null,s=e.getCleaningRecordStatusText(t.status);return{$orig:n,m8:r,g6:i,g7:c,m9:s}})),f="quick"!==e.dateFilterMode&&e.customDateRange.startDate&&e.customDateRange.endDate?e.formatSelectedDate(e.customDateRange.startDate):null,h="quick"!==e.dateFilterMode&&e.customDateRange.startDate&&e.customDateRange.endDate?e.formatSelectedDate(e.customDateRange.endDate):null;e.$mp.data=Object.assign({},{$root:{m0:a,m1:n,g0:r,l0:i,g1:c,g2:s,l1:o,g3:u,l3:l,g5:d,l4:g,m10:f,m11:h}})},i=[]},fd9e:function(e,t,a){"use strict";(function(e){var n=a("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("7eb4")),i=n(a("af34")),c=n(a("7ca3")),s=n(a("ee10")),o=n(a("34cf")),u=a("882c");function l(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function d(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?l(Object(a),!0).forEach((function(t){(0,c.default)(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):l(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}var g={name:"MyAreas",data:function(){return{dateFilterMode:"quick",selectedTimeFilter:"week",customDateRange:{startDate:"",endDate:""},calendarDate:"",calendarStartDate:"",calendarEndDate:"",refreshTimer:null,calendarChangeTimer:null,quickDateOptions:[{label:"本周",value:"week"},{label:"上周",value:"last_week"},{label:"本月",value:"month"},{label:"自定义",value:"custom"}],selectedQuickFilter:"week",myAreas:[],rectificationTasks:[],cleaningRecords:{list:[],pageSize:10,hasMore:!0,loading:!1},allCleaningRecords:[],loading:!1,dataLoaded:!1,needsRefresh:!1,expandedRectificationWeeks:[],currentWeekCache:null,areasCache:null}},computed:{myFixedAreas:function(){return this.areasCache&&this.areasCache.sourceLength===this.myAreas.length||(this.areasCache={sourceLength:this.myAreas.length,fixed:this.myAreas.filter((function(e){return"fixed"===e.type})),public:this.myAreas.filter((function(e){return"public"===e.type}))}),this.areasCache.fixed},myPublicAreas:function(){return this.areasCache&&this.areasCache.sourceLength===this.myAreas.length||(this.areasCache={sourceLength:this.myAreas.length,fixed:this.myAreas.filter((function(e){return"fixed"===e.type})),public:this.myAreas.filter((function(e){return"public"===e.type}))}),this.areasCache.public},completedCount:function(){try{if("week"===this.selectedQuickFilter){var e=this.myAreas.filter((function(e){return"completed"===e.status})).length||0,t=this.getThisWeekRectificationTasks().filter((function(e){return"completed"===e.status||"verified"===e.status})).length||0;return e+t}var a=this.filteredRectificationTasks.filter((function(e){return"completed"===e.status||"verified"===e.status})).length||0;return a}catch(n){return console.error("计算已完成数量出错:",n),0}},pendingCount:function(){try{if("week"===this.selectedQuickFilter){var e=this.myAreas.filter((function(e){return"pending"===e.status})).length||0,t=this.getThisWeekRectificationTasks().filter((function(e){return"pending_rectification"===e.status})).length||0;return e+t}var a=this.filteredRectificationTasks.filter((function(e){return"pending_rectification"===e.status})).length||0;return a}catch(n){return console.error("计算待处理数量出错:",n),0}},overdueCount:function(){var e=this;try{if("week"===this.selectedQuickFilter){var t=this.myAreas.filter((function(e){return"overdue"===e.status})).length||0,a=this.getThisWeekRectificationTasks().filter((function(t){return"overdue"===t.status||e.isRectificationOverdue(t)})).length||0;return t+a}var n=this.filteredRectificationTasks.filter((function(t){return"overdue"===t.status||e.isRectificationOverdue(t)})).length||0;return n}catch(r){return console.error("计算逾期数量出错:",r),0}},filteredRectificationTasks:function(){var e=this.rectificationTasks,t=this.getCurrentDateRange();return t&&(e=e.filter((function(e){if(!e.created_at)return!1;var a=new Date(e.created_at);return a>=t.start&&a<=t.end}))),e},groupedRectificationTasks:function(){var e=this,t=this.filteredRectificationTasks,a={};t.forEach((function(t){var n=e.getWeekKey(t.created_at);a[n]||(a[n]={weekKey:n,title:e.getWeekTitle(n),tasks:[],expanded:e.expandedRectificationWeeks.includes(n)}),a[n].tasks.push(t)}));var n=Object.values(a).sort((function(e,t){var a=e.weekKey.split("-W").map((function(e){return parseInt(e)})),n=(0,o.default)(a,2),r=n[0],i=n[1],c=t.weekKey.split("-W").map((function(e){return parseInt(e)})),s=(0,o.default)(c,2),u=s[0],l=s[1];return r!==u?u-r:l-i}));return n.forEach((function(t){t.expanded=e.expandedRectificationWeeks.includes(t.weekKey)})),n}},created:function(){},onLoad:function(){this.loadMyAreas(),e.$on("cleaningRecordUpdated",this.handleRecordUpdated),e.$on("rectificationRecordUpdated",this.handleRecordUpdated)},onUnload:function(){e.$off("cleaningRecordUpdated",this.handleRecordUpdated),e.$off("rectificationRecordUpdated",this.handleRecordUpdated),this.refreshTimer&&clearTimeout(this.refreshTimer),this.calendarChangeTimer&&clearTimeout(this.calendarChangeTimer)},onShow:function(){this.dataLoaded&&!this.loading&&this.needsRefresh&&(this.silentRefreshData(),this.needsRefresh=!1)},methods:{getThisWeekRectificationTasks:function(){try{var e=new Date,t=this.toBeijingTime(e),a=this.getWeekStart(t),n=this.getWeekEnd(t);return Array.isArray(this.rectificationTasks)?this.rectificationTasks.filter((function(e){if(!e||!e.created_at)return!1;try{var t=new Date(e.created_at);return!isNaN(t.getTime())&&(t>=a&&t<=n)}catch(r){return console.error("处理任务日期出错:",r,e),!1}})):[]}catch(r){return console.error("获取本周整改任务出错:",r),[]}},isRectificationOverdue:function(e){if(!e||"completed"===e.status||"verified"===e.status)return!1;if(!e.created_at)return!1;if("pending_rectification"!==e.status&&"pending_assignment"!==e.status)return!1;var t=new Date,a=this.toBeijingTime(t),n=new Date(e.created_at),r=this.toBeijingTime(n),i=this.getWeekEnd(r);return a.getTime()>i.getTime()},loadMyAreas:function(){var t=this;return(0,s.default)(r.default.mark((function a(){return r.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.loading=!0,a.prev=1,a.next=4,Promise.all([t.loadUserAssignments(),t.loadUserRectificationTasks(),t.loadMyCleaningRecords()]);case 4:a.next=12;break;case 6:a.prev=6,a.t0=a["catch"](1),e.showToast({title:"加载数据失败",icon:"none",duration:2e3}),t.myAreas=[],t.rectificationTasks=[],t.cleaningRecords.list=[];case 12:return a.prev=12,t.loading=!1,t.dataLoaded=!0,a.finish(12);case 16:case"end":return a.stop()}}),a,null,[[1,6,12,16]])})))()},loadUserAssignments:function(){var e=this;return(0,s.default)(r.default.mark((function t(){var a,n,i,c;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,u.callCloudFunction)("hygiene-assignments",{action:"getUserAssignments",data:{}});case 3:if(a=t.sent,!a||!a.success){t.next=17;break}if(n=a.data||[],e.myAreas=[],i=[],c=new Map,n.forEach((function(e){e.areas_info&&Array.isArray(e.areas_info)&&e.areas_info.forEach((function(e){var t=e._id||e.id;i.push(t);var a={id:t,name:e.name,icon:"fixed"===e.type?"gear":"videocam",type:e.type||"fixed",weeklyRequired:"fixed"===e.type,location:e.location,description:e.description,last_clean_date:null};"public"===e.type&&null!==e.scheduled_day&&(a.scheduledDay=e.scheduled_day,a.scheduled_day=e.scheduled_day),c.set(t,a)}))})),!(i.length>0)){t.next=13;break}return t.next=13,e.loadLatestCleaningRecords(i,c);case 13:e.myAreas=Array.from(c.values()).map((function(t){var a=e.calculateAreaStatus(t);return d(d({},t),{},{subtitle:e.generateAreaSubtitle(t),status:a,icon:e.getAreaIcon(a),lastCleaningDate:t.last_clean_date})})),e.areasCache=null,t.next=18;break;case 17:e.myAreas=[];case 18:t.next=23;break;case 20:t.prev=20,t.t0=t["catch"](0),e.myAreas=[];case 23:case"end":return t.stop()}}),t,null,[[0,20]])})))()},loadLatestCleaningRecords:function(e,t){var a=this;return(0,s.default)(r.default.mark((function n(){var i,c,o,l,d,g;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,a.currentWeekCache||(i=new Date,a.currentWeekCache={now:i,weekStart:a.getWeekStart(i),weekEnd:a.getWeekEnd(i)}),c=a.currentWeekCache,o=c.weekStart,l=c.weekEnd,n.prev=3,n.next=6,(0,u.callCloudFunction)("hygiene-cleaning",{action:"getBatchCleaningRecords",data:{area_ids:e,start_date:o.toISOString(),end_date:l.toISOString(),latest_only:!0}});case 6:d=n.sent,d&&d.success&&d.data&&d.data.forEach((function(e){t.has(e.area_id)&&(t.get(e.area_id).last_clean_date=e.cleaning_date)})),n.next=15;break;case 10:return n.prev=10,n.t0=n["catch"](3),g=e.map(function(){var e=(0,s.default)(r.default.mark((function e(a){var n,i;return r.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,u.callCloudFunction)("hygiene-cleaning",{action:"getCleaningRecords",data:{area_id:a,start_date:o.toISOString(),end_date:l.toISOString(),pageSize:1}});case 3:n=e.sent,n&&n.success&&n.data&&n.data.list&&n.data.list.length>0&&(i=n.data.list[0],t.has(a)&&(t.get(a).last_clean_date=i.cleaning_date)),e.next=9;break;case 7:e.prev=7,e.t0=e["catch"](0);case 9:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t){return e.apply(this,arguments)}}()),n.next=15,Promise.all(g);case 15:n.next=19;break;case 17:n.prev=17,n.t1=n["catch"](0);case 19:case"end":return n.stop()}}),n,null,[[0,17],[3,10]])})))()},loadMyCleaningRecords:function(){var e=this;return(0,s.default)(r.default.mark((function t(){var a,n,i,c,s;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.cleaningRecords.loading=!0,a={pageSize:20},n=e.getCurrentDateRange(),n&&(a.start_date=n.start.toISOString(),a.end_date=n.end.toISOString()),t.next=7,(0,u.callCloudFunction)("hygiene-cleaning",{action:"getMyCleaningRecords",data:a});case 7:i=t.sent,i&&i.success?(c=i.data||[],s=c.map((function(t){return{id:t._id||t.id,areaId:t.area_id,areaName:t.area_name||"未知责任区",areaType:t.area_type||"fixed",cleaningDate:t.cleaning_date,formattedDate:e.formatDateTime(t.cleaning_date),photos:t.photos?t.photos.length:0,remark:t.remark||"",status:"completed",icon:"checkmarkempty",week:e.getWeekKey(t.cleaning_date)}})),e.allCleaningRecords=s,e.cleaningRecords.list=s.slice(0,e.cleaningRecords.pageSize),e.cleaningRecords.hasMore=c.length>e.cleaningRecords.pageSize,e.cleaningRecords.list.sort((function(e,t){return new Date(t.cleaningDate)-new Date(e.cleaningDate)}))):(e.cleaningRecords.list=[],e.cleaningRecords.hasMore=!1),t.next=15;break;case 11:t.prev=11,t.t0=t["catch"](0),e.cleaningRecords.list=[],e.cleaningRecords.hasMore=!1;case 15:return t.prev=15,e.cleaningRecords.loading=!1,t.finish(15);case 18:case"end":return t.stop()}}),t,null,[[0,11,15,18]])})))()},loadMoreCleaningRecords:function(){var e=this;return(0,s.default)(r.default.mark((function t(){var a,n,c,s;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.cleaningRecords.loading&&e.cleaningRecords.hasMore){t.next=2;break}return t.abrupt("return");case 2:e.cleaningRecords.loading=!0,a=e.cleaningRecords.list.length,n=e.cleaningRecords.pageSize,c=a+n,e.allCleaningRecords&&e.allCleaningRecords.length>a?(s=e.allCleaningRecords.slice(a,c),e.cleaningRecords.list=[].concat((0,i.default)(e.cleaningRecords.list),(0,i.default)(s)),e.cleaningRecords.hasMore=e.cleaningRecords.list.length<e.allCleaningRecords.length):e.cleaningRecords.hasMore=!1,e.cleaningRecords.loading=!1;case 8:case"end":return t.stop()}}),t)})))()},loadUserRectificationTasks:function(){var e=this;return(0,s.default)(r.default.mark((function t(){var a,n,i,c;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,a={},n=e.getCurrentDateRange(),n&&(a.start_date=n.start.toISOString(),a.end_date=n.end.toISOString()),t.next=6,(0,u.callCloudFunction)("hygiene-rectification",{action:"getMyRectifications",data:a});case 6:i=t.sent,i&&i.success?(c=i.data||[],e.rectificationTasks=c.map((function(t){var a=e.getWeekKey(t.created_at),n=e.calculateRectificationStatus(t),r=t._id||t.id||t.issue_id||t.task_id;return r?{id:r,areaId:t.area_id,areaName:t.area_name,subtitle:e.formatDescription(t.issue_description||t.description),status:n,originalStatus:t.status,icon:e.getTaskIcon(n),areaType:t.area_type||"fixed",week:a,issueFoundDate:e.formatDate(t.created_at),completedDate:t.completed_at?e.formatDate(t.completed_at):null,assignee_id:t.assignee_id,assignee_name:t.assignee_name,created_at:t.created_at,deadline:t.deadline}:null})).filter((function(e){return e&&e.id})),e.setDefaultExpandedWeek()):e.rectificationTasks=[],t.next=13;break;case 10:t.prev=10,t.t0=t["catch"](0),e.rectificationTasks=[];case 13:case"end":return t.stop()}}),t,null,[[0,10]])})))()},generateAreaSubtitle:function(e){if("fixed"===e.type){if(e.last_clean_date){var t=new Date,a=this.getWeekStart(t),n=this.getWeekEnd(t),r=new Date(e.last_clean_date);if(r>=a&&r<=n){var i=this.formatDate(e.last_clean_date);return"本周已清理 · ".concat(i)}return"本周待清理 · 每周完成一次"}return"本周待清理 · 每周完成一次"}if("public"===e.type){if(null!==e.scheduled_day){if(e.last_clean_date){var c=this.formatDate(e.last_clean_date);return"本周已清理 · ".concat(c)}var s=["周日","周一","周二","周三","周四","周五","周六"],o=0===e.scheduled_day?0:e.scheduled_day;return"scheduled"===e.status?"计划".concat(s[o],"清理 · 未到时间"):"pending"===e.status?"计划".concat(s[o],"清理 · 可以清理"):"overdue"===e.status?"计划".concat(s[o],"清理 · 已逾期"):"计划".concat(s[o],"清理")}return"未设置清理日程"}return"待处理"},calculateAreaStatus:function(e){if(!this.currentWeekCache){var t=new Date;this.currentWeekCache={now:t,weekStart:this.getWeekStart(t),weekEnd:this.getWeekEnd(t)}}var a=this.currentWeekCache,n=a.now,r=a.weekStart,i=a.weekEnd;if("fixed"===e.type){if(e.last_clean_date){var c=new Date(e.last_clean_date);if(c>=r&&c<=i)return"completed"}var s=new Date(i);if(s.setDate(s.getDate()+1),n>=s){if(!e.last_clean_date)return"overdue";var o=new Date(e.last_clean_date);if(!(o>=r&&o<=i))return"overdue"}return"pending"}if("public"===e.type){if(null!==e.scheduled_day){if(e.last_clean_date){var u=new Date(e.last_clean_date);if(u>=r&&u<=i)return"completed"}var l=this.toBeijingTime(n),d=0===e.scheduled_day?0:e.scheduled_day,g=this.getWeekStart(l),f=new Date(g);0===d?f.setDate(g.getDate()+6):f.setDate(g.getDate()+d-1);var h=new Date(f);h.setHours(0,0,0,0);var p=new Date(f);p.setHours(23,59,59,999);var m=l.getTime(),D=l.getDay();if(D===d)return m<=p.getTime()?"pending":"overdue";var v=(D-d+7)%7;return 0===v?"pending":v>0?"overdue":"scheduled"}return"pending"}return"pending"},getTaskIcon:function(e){return{completed:"checkmarkempty",pending_rectification:"refreshempty",pending_assignment:"refreshempty",pending_review:"eye",overdue:"closeempty",rejected:"closeempty",verified:"checkmarkempty",approved:"checkmarkempty",in_progress:"gear"}[e]||"help"},getAreaIcon:function(e){return{completed:"checkmarkempty",pending:"minus",overdue:"closeempty",scheduled:"calendar"}[e]||"gear"},toBeijingTime:function(e){if(!e)return null;var t="string"===typeof e?new Date(e):e;if(isNaN(t.getTime()))return null;var a=new Date(t.toLocaleString("zh-CN",{timeZone:"Asia/Shanghai"}));return{getFullYear:function(){return a.getFullYear()},getMonth:function(){return a.getMonth()},getDate:function(){return a.getDate()},getDay:function(){return a.getDay()},getHours:function(){return a.getHours()},getMinutes:function(){return a.getMinutes()},getSeconds:function(){return a.getSeconds()},getTime:function(){return a.getTime()},toISOString:function(){return a.toISOString()},valueOf:function(){return a.getTime()}}},getWeekKey:function(e){if(!e)return"";var t=this.toBeijingTime(e);if(!t)return"";var a=t.getFullYear(),n=this.getWeekNumber(t),r="".concat(a,"-W").concat(n.toString().padStart(2,"0"));return r},getWeekNumber:function(e){var t,a,n,r;if("function"===typeof e.getFullYear)t=e.getFullYear(),a=e.getMonth(),n=e.getDate(),r=e.getDay();else{var i=new Date(e);t=i.getFullYear(),a=i.getMonth(),n=i.getDate(),r=i.getDay()}var c=new Date(t,a,n,12,0,0,0),s=0===r?7:r,o=new Date(c);o.setDate(c.getDate()+4-s);var u=o.getFullYear(),l=new Date(u,0,4),d=l.getDay(),g=0===d?7:d;l.setDate(4-g+4);var f=Math.floor((o-l)/6048e5)+1;return f},getWeekStart:function(e){var t,a,n,r;if("function"===typeof e.getFullYear)t=e.getFullYear(),a=e.getMonth(),n=e.getDate(),r=e.getDay();else{var i=new Date(e);t=i.getFullYear(),a=i.getMonth(),n=i.getDate(),r=i.getDay()}var c=0===r?7:r,s=new Date(t,a,n-c+1,0,0,0,0);return s},getWeekEnd:function(e){var t,a,n,r;if("function"===typeof e.getFullYear)t=e.getFullYear(),a=e.getMonth(),n=e.getDate(),r=e.getDay();else{var i=new Date(e);t=i.getFullYear(),a=i.getMonth(),n=i.getDate(),r=i.getDay()}var c=0===r?7:r,s=new Date(t,a,n-c+7,23,59,59,999);return s},formatDate:function(e){if(!e)return"--";try{var t;return t="string"===typeof e?e.includes("T")||e.includes("Z")?new Date(e):new Date(e.replace(/-/g,"/")):new Date(e),isNaN(t.getTime())?"--":"".concat(t.getMonth()+1,"月").concat(t.getDate(),"日")}catch(a){return"--"}},formatDateTime:function(e){if(!e)return"--";try{var t;return t="string"===typeof e?e.includes("T")||e.includes("Z")?new Date(e):new Date(e.replace(/-/g,"/")):new Date(e),isNaN(t.getTime())?"--":"".concat(t.getMonth()+1,"月").concat(t.getDate(),"日 ").concat(t.getHours().toString().padStart(2,"0"),":").concat(t.getMinutes().toString().padStart(2,"0"))}catch(a){return"--"}},getStatusTitle:function(){var e=this;if("week"===this.selectedQuickFilter)return"本周清理状态";if("quick"===this.dateFilterMode&&"custom"!==this.selectedQuickFilter){var t=this.quickDateOptions.find((function(t){return t.value===e.selectedQuickFilter}));return t?"".concat(t.label,"整改记录统计"):"整改记录统计"}return this.customDateRange.startDate&&this.customDateRange.endDate?"自定义时间段整改记录统计":"清理状态"},getCurrentTimeRange:function(){var e=this;if("custom"===this.selectedTimeFilter&&"range"===this.dateFilterMode)return this.getDateRangeText();var t=this.quickDateOptions.find((function(t){return t.value===e.selectedQuickFilter}));return t?t.label:"本周"},getWeekTitle:function(e){var t=e.match(/(\d{4})-W(\d{2})/);if(!t)return e;var a=parseInt(t[1]),n=parseInt(t[2]),r=this.toBeijingTime(new Date),i=r.getFullYear(),c=this.getCurrentWeekNumber(),s=this.getWeekRangeByWeekKey(e);if(a===i&&n===c)return"本周 (".concat(s.start,"-").concat(s.end,")");if(a===i&&n===c-1)return"上周 (".concat(s.start,"-").concat(s.end,")");if(a===i&&n===c-2)return"前周 (".concat(s.start,"-").concat(s.end,")");if(a===i){var o=c-n;return o>0&&o<=4?"".concat(o,"周前 (").concat(s.start,"-").concat(s.end,")"):o>4?"".concat(a,"年").concat(s.start,"-").concat(s.end):"第".concat(n,"周 (").concat(s.start,"-").concat(s.end,")")}return"".concat(a,"年").concat(s.start,"-").concat(s.end)},getWeekRangeByWeekKey:function(e){var t=e.match(/(\d{4})-W(\d{2})/);if(!t)return{start:"",end:""};var a=parseInt(t[1]),n=parseInt(t[2]),r=new Date(a,0,4),i=r.getDay(),c=0===i?7:i;r.setDate(4-c+4);var s=new Date(r);s.setDate(r.getDate()+7*(n-1));var o=new Date(s);o.setDate(s.getDate()-3);var u=new Date(s);return u.setDate(s.getDate()+3),{start:"".concat(o.getMonth()+1,"月").concat(o.getDate(),"日"),end:"".concat(u.getMonth()+1,"月").concat(u.getDate(),"日"),monday:o,sunday:u}},getCurrentWeekNumber:function(){var e=new Date,t=this.toBeijingTime(e);return this.getWeekNumber(t)},calculateRectificationStatus:function(e){var t=new Date;if("completed"===e.status)return"completed";if(e.deadline){var a=new Date(e.deadline);if(!isNaN(a.getTime())&&t>a&&("pending_rectification"===e.status||"pending_assignment"===e.status))return"overdue"}if(e.created_at){var n=this.toBeijingTime(e.created_at),r=this.toBeijingTime(t);if(n&&r){this.getWeekStart(n);var i=this.getWeekEnd(n);if(r.getTime()>i.getTime()&&("pending_rectification"===e.status||"pending_assignment"===e.status))return"overdue"}}return e.status},setDefaultExpandedWeek:function(){var e=(0,i.default)(new Set(this.filteredRectificationTasks.map((function(e){return e.week}))));if(e.length>0){var t,a=new Date,n=this.toBeijingTime(a),r=this.getWeekKey(n);if(e.includes(r))t=r;else{var c=e.sort((function(e,t){var a=e.split("-W").map((function(e){return parseInt(e)})),n=(0,o.default)(a,2),r=n[0],i=n[1],c=t.split("-W").map((function(e){return parseInt(e)})),s=(0,o.default)(c,2),u=s[0],l=s[1];return r!==u?u-r:l-i}));t=c[0]}t&&!this.expandedRectificationWeeks.includes(t)&&this.expandedRectificationWeeks.push(t)}},toggleRectificationWeek:function(e){var t=this.expandedRectificationWeeks.indexOf(e);t>-1?this.expandedRectificationWeeks.splice(t,1):this.expandedRectificationWeeks.push(e)},resetExpandedState:function(){this.expandedRectificationWeeks=[]},smartResetExpandedState:function(){var e=this;this.expandedRectificationWeeks=[],this.$nextTick((function(){setTimeout((function(){e.setDefaultExpandedWeek()}),50)}))},getStatusText:function(e){return{pending:"待清理",pending_review:"待复查",completed:"已完成",pending_rectification:"待整改",pending_assignment:"待分配",overdue:"已逾期",scheduled:"未到时间",rejected:"整改不达标",verified:"整改合格",approved:"复查通过",in_progress:"整改中",rectification_completed:"整改已完成"}[e]||"未知"},getCleaningRecordStatusText:function(e){return"已完成"},formatDescription:function(e){if(!e)return"";var t=e.replace(/\n/g," ").replace(/\r/g," ").replace(/\s+/g," ").trim();return t.length>16&&(t=t.substring(0,16)+"..."),t},handleRecordUpdated:function(e){this.silentRefreshData()},silentRefreshData:function(){var e=this;return(0,s.default)(r.default.mark((function t(){return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.loading){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,e.currentWeekCache=null,e.areasCache=null,t.next=7,Promise.all([e.loadUserAssignments(),e.loadUserRectificationTasks(),e.loadMyCleaningRecords()]);case 7:e.$nextTick((function(){e.setDefaultExpandedWeek()})),e.needsRefresh=!1,t.next=13;break;case 11:t.prev=11,t.t0=t["catch"](2);case 13:case"end":return t.stop()}}),t,null,[[2,11]])})))()},getIconColor:function(e){return{completed:"#34C759",pending:"#FF9500",overdue:"#FF3B30",pending_review:"#007AFF",pending_rectification:"#FF6B35",pending_assignment:"#FF9500",scheduled:"#8E8E93",rejected:"#FF3B30",verified:"#34C759",approved:"#34C759",in_progress:"#007AFF",rectification_completed:"#FF9500"}[e]||"#8E8E93"},handleOverdueStatus:function(t){var a="public"===t.type,n=a?"排班清理时间已过，无法再进行清理操作。请等待下周重新分配。":"本周清理时间已过，无法再进行清理操作。请等待下周重新分配。";return e.showModal({title:"清理任务已逾期",content:n,showCancel:!1,confirmText:"知道了"}),!0},openAreaDetail:function(t){"overdue"!==t.status?"pending"===t.status?e.navigateTo({url:"/pages/6s_pkg/cleaning-upload?areaId=".concat(t._id||t.id,"&type=fixed")}):e.navigateTo({url:"/pages/6s_pkg/area-detail?areaId=".concat(t._id||t.id,"&type=fixed")}):this.handleOverdueStatus(t)},openPublicAreaDetail:function(t){if("overdue"!==t.status)if("scheduled"!==t.status)"pending"===t.status?e.navigateTo({url:"/pages/6s_pkg/cleaning-upload?areaId=".concat(t._id||t.id,"&type=public")}):e.navigateTo({url:"/pages/6s_pkg/area-detail?areaId=".concat(t._id||t.id,"&type=public")});else{var a=0===t.scheduled_day?7:t.scheduled_day;e.showModal({title:"未到清理时间",content:"该公共责任区安排在".concat(["","周一","周二","周三","周四","周五","周六","周日"][a],"进行清理，当前时间无需清理。"),showCancel:!1,confirmText:"知道了"})}else this.handleOverdueStatus(t)},openRectificationDetail:function(t){var a=t.id||t._id||t.issue_id||t.task_id;a?"overdue"!==t.status?"pending_rectification"===t.status?e.navigateTo({url:"/pages/6s_pkg/rectification-upload?taskId=".concat(a,"&areaType=").concat(t.areaType)}):(t.status,e.navigateTo({url:"/pages/6s_pkg/rectification-detail?taskId=".concat(a)})):e.showModal({title:"整改任务已逾期",content:"该整改任务已逾期，记录已锁定",showCancel:!1,confirmText:"知道了"}):e.showToast({title:"任务数据异常",icon:"error"})},openCleaningRecordDetail:function(t){var a=new Date,n=this.getWeekStart(a),r=this.getWeekEnd(a),i=new Date(t.cleaningDate),c=i<n||i>r,s=["areaId=".concat(encodeURIComponent(t.areaId)),"type=".concat(encodeURIComponent(t.areaType)),"mode=view","cleaningDate=".concat(encodeURIComponent(t.cleaningDate)),"week=".concat(encodeURIComponent(t.week)),"isHistorical=".concat(c)].join("&");e.navigateTo({url:"/pages/6s_pkg/area-detail?".concat(s)})},showTimeSelector:function(){this.initializeDateRange(),this.$refs.timePopup.open()},closeDatePicker:function(){this.$refs.timePopup.close()},initializeDateRange:function(){if(!this.customDateRange.startDate){new Date;var e=this.getQuickDateRange(this.selectedQuickFilter);this.customDateRange.startDate=this.formatDateForPicker(e.start),this.customDateRange.endDate=this.formatDateForPicker(e.end)}this.calendarDate||(this.calendarDate=this.formatDateForPicker(new Date))},switchToRangeMode:function(){this.dateFilterMode="range"},switchToQuickMode:function(){this.dateFilterMode="quick"},selectQuickDateOption:function(e){var t=this;this.selectedQuickFilter=e.value,"custom"===e.value?this.switchToRangeMode():(this.smartResetExpandedState(),this.closeDatePicker(),this.loading=!0,clearTimeout(this.refreshTimer),this.refreshTimer=setTimeout((0,s.default)(r.default.mark((function e(){return r.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.loadMyAreas();case 3:t.$nextTick((function(){t.setDefaultExpandedWeek()}));case 4:return e.prev=4,t.loading=!1,e.finish(4);case 7:case"end":return e.stop()}}),e,null,[[0,,4,7]])}))),100))},getCurrentDateRange:function(){return"quick"===this.dateFilterMode&&"custom"!==this.selectedQuickFilter?this.getQuickDateRange(this.selectedQuickFilter):this.customDateRange.startDate&&this.customDateRange.endDate?{start:new Date(this.customDateRange.startDate),end:new Date(this.customDateRange.endDate)}:null},getQuickDateRange:function(e){var t=new Date,a=new Date(t.getFullYear(),t.getMonth(),t.getDate());switch(e){case"week":var n=new Date(a),r=n.getDay(),i=0===r?6:r-1;n.setDate(n.getDate()-i);var c=new Date(n);return c.setDate(c.getDate()+6),c.setHours(23,59,59,999),{start:n,end:c};case"last_week":var s=new Date(a),o=s.getDay(),u=0===o?13:o+6;s.setDate(s.getDate()-u);var l=new Date(s);return l.setDate(l.getDate()+6),l.setHours(23,59,59,999),{start:s,end:l};case"month":var d=new Date(a.getFullYear(),a.getMonth(),1),g=new Date(a.getFullYear(),a.getMonth()+1,0);return g.setHours(23,59,59,999),{start:d,end:g};default:return this.getQuickDateRange("week")}},formatDateForPicker:function(e){if(!e)return"";var t=new Date(e),a=t.getFullYear(),n=String(t.getMonth()+1).padStart(2,"0"),r=String(t.getDate()).padStart(2,"0");return"".concat(a,"-").concat(n,"-").concat(r)},getDateRangeText:function(){var e=this;if("quick"===this.dateFilterMode){var t=this.quickDateOptions.find((function(t){return t.value===e.selectedQuickFilter}));return t?t.label:"本周"}var a=this.getStartDateText(),n=this.getEndDateText();return"请选择"===a||"请选择"===n?"选择日期范围":"".concat(a," - ").concat(n)},getStartDateText:function(){return this.customDateRange.startDate?this.formatDisplayDate(new Date(this.customDateRange.startDate)):"请选择开始日期"},getEndDateText:function(){return this.customDateRange.endDate?this.formatDisplayDate(new Date(this.customDateRange.endDate)):"请选择结束日期"},formatDisplayDate:function(e){var t=new Date(e),a=t.getMonth()+1,n=t.getDate();return"".concat(a,"月").concat(n,"日")},onCalendarChange:function(t){var a=this;clearTimeout(this.calendarChangeTimer),this.calendarChangeTimer=setTimeout((function(){if(t.range)t.range.before&&t.range.after&&(a.customDateRange.startDate=t.range.before,a.customDateRange.endDate=t.range.after,a.selectedTimeFilter="custom",a.smartResetExpandedState(),a.closeDatePicker(),a.loading=!0,clearTimeout(a.refreshTimer),a.refreshTimer=setTimeout((0,s.default)(r.default.mark((function e(){return r.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,a.loadMyAreas();case 3:a.$nextTick((function(){a.setDefaultExpandedWeek()}));case 4:return e.prev=4,a.loading=!1,e.finish(4);case 7:case"end":return e.stop()}}),e,null,[[0,,4,7]])}))),100),e.showToast({title:"日期范围选择完成",icon:"success",duration:1500}));else if(t.fulldate)if(!a.customDateRange.startDate||a.customDateRange.endDate)a.customDateRange.startDate=t.fulldate,a.customDateRange.endDate="";else{if(a.customDateRange.endDate=t.fulldate,new Date(a.customDateRange.startDate)>new Date(a.customDateRange.endDate)){var n=a.customDateRange.startDate;a.customDateRange.startDate=a.customDateRange.endDate,a.customDateRange.endDate=n}a.selectedTimeFilter="custom",a.smartResetExpandedState(),a.closeDatePicker(),a.loading=!0,clearTimeout(a.refreshTimer),a.refreshTimer=setTimeout((0,s.default)(r.default.mark((function e(){return r.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,a.loadMyAreas();case 3:a.$nextTick((function(){a.setDefaultExpandedWeek()}));case 4:return e.prev=4,a.loading=!1,e.finish(4);case 7:case"end":return e.stop()}}),e,null,[[0,,4,7]])}))),100),e.showToast({title:"日期范围选择完成",icon:"success",duration:1500})}}),100)},onMonthSwitch:function(e){if(e&&e.current&&e.current.fulldate)this.calendarDate=e.current.fulldate;else if(e&&e.year&&e.month){var t=e.year,a=String(e.month).padStart(2,"0");this.calendarDate="".concat(t,"-").concat(a,"-01")}},formatSelectedDate:function(e){if(!e)return"";var t=new Date(e),a=t.getMonth()+1,n=t.getDate(),r=["周日","周一","周二","周三","周四","周五","周六"][t.getDay()];return"".concat(a,"月").concat(n,"日 ").concat(r)}}};t.default=g}).call(this,a("df3c")["default"])}},[["3c80","common/runtime","common/vendor"]]]);