(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/ucenter_pkg/study-center"],{2897:function(e,t,n){"use strict";(function(e,t){var o=n("47a9");n("357b"),n("861b");o(n("3240"));var r=o(n("4a60"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(r.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"3fd7":function(e,t,n){"use strict";n.r(t);var o=n("a1f4"),r=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);t["default"]=r.a},4361:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return o}));var o={uniSearchBar:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar")]).then(n.bind(null,"b37b"))},uniIcons:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(n.bind(null,"6ddf"))},uniLoadMore:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-load-more/components/uni-load-more/uni-load-more")]).then(n.bind(null,"3282"))},pEmptyState:function(){return n.e("components/p-empty-state/p-empty-state").then(n.bind(null,"9b76"))},uniPopup:function(){return n.e("uni_modules/uni-popup/components/uni-popup/uni-popup").then(n.bind(null,"a2b7"))}},r=function(){var e=this,t=e.$createElement,n=(e._self._c,e.loading||0===e.documents.length&&!e.hasInitialized),o=n?null:e.documents.length,r=n||0===o?null:e.__map(e.documents,(function(t,n){var o=e.__get_orig(t),r="collection"!==t.type?e.getFileIconClass(t.file_type):null,i="collection"!==t.type?e.getFileIconColor(t.file_type):null,a="collection"===t.type&&t.files?t.files.length:null,l="collection"!==t.type?e.formatFileSize(t.file_size):null,c=e.formatTime(t.create_time),s=e.canEditDocument(t),u=e.canDeleteDocument(t);return{$orig:o,m0:r,m1:i,g2:a,m2:l,m3:c,m4:s,m5:u}})),i=e.documents.length,a=i>0&&!e.hasMore?e.documents.length:null,l=e.getCurrentCategoryName()||"请选择分类",c=e.isEditMode?e.getCurrentEditingDocument()&&"single"===e.getCurrentEditingDocument().type&&e.getCurrentEditingDocument().file_name:null,s=e.isEditMode&&c?e.getCurrentEditingDocumentFileName():null,u=e.isEditMode?e.getCurrentEditingDocument()&&"collection"===e.getCurrentEditingDocument().type:null,d=e.isEditMode&&u?e.getCurrentEditingDocument():null,f=e.isEditMode&&u?e.getCurrentEditingDocument().files&&e.getCurrentEditingDocument().files.length>0:null,p=e.isEditMode&&u&&f?e.__map(e.getCurrentEditingDocument().files,(function(t,n){var o=e.__get_orig(t),r=e.getFileIconClass(t.file_type),i=e.getFileIconColor(t.file_type),a=e.formatFileSize(t.file_size);return{$orig:o,m12:r,m13:i,m14:a}})):null,h=!e.isEditMode&&"single"===e.uploadForm.type&&e.uploadForm.file_name?e.getFileIconClass(e.uploadForm.file_type):null,m=!e.isEditMode&&"single"===e.uploadForm.type&&e.uploadForm.file_name?e.getFileIconColor(e.uploadForm.file_type):null,g=!e.isEditMode&&"single"===e.uploadForm.type&&e.uploadForm.file_name?e.formatFileSize(e.uploadForm.file_size):null,w=e.isEditMode||"collection"!==e.uploadForm.type?null:e.uploadForm.files&&e.uploadForm.files.length>0,v=!e.isEditMode&&"collection"===e.uploadForm.type&&w?e.__map(e.uploadForm.files,(function(t,n){var o=e.__get_orig(t),r=e.getFileIconClass(t.file_type),i=e.getFileIconColor(t.file_type),a=e.formatFileSize(t.file_size);return{$orig:o,m18:r,m19:i,m20:a}})):null,F=e.categoryOptions.length,y=e.isCategoryManagerVisible?!e.newCategory.name.trim()||e.isSubmitting:null,_=e.isCategoryManagerVisible?e.categories.length:null,C=e.__map(e.currentCollectionFiles,(function(t,n){var o=e.__get_orig(t),r=e.currentCollectionFileCount>0?e.downloadingFiles.includes(n):null,i=e.currentCollectionFileCount>0?e.getFileIconClass(t.file_type):null,a=e.currentCollectionFileCount>0?e.getFileIconColor(t.file_type):null,l=e.currentCollectionFileCount>0?e.formatFileSize(t.file_size):null,c=e.currentCollectionFileCount>0?e.downloadedFiles.includes(n):null,s=e.currentCollectionFileCount>0&&!c?e.downloadingFiles.includes(n):null;return{$orig:o,g9:r,m21:i,m22:a,m23:l,g10:c,g11:s}})),b=e.currentCollectionFileCount>0?e.downloadingFiles.length:null,x=e.getFileIconClass(e.currentPreviewDocument?e.currentPreviewDocument.file_type:""),k=e.getFileIconColor(e.currentPreviewDocument?e.currentPreviewDocument.file_type:""),D=e.currentPreviewDocument?e.formatFileSize(e.currentPreviewDocument.file_size):null,T=e.currentPreviewDocument?e.formatTime(e.currentPreviewDocument.create_time):null;e._isMounted||(e.e0=function(t,n){var o=arguments[arguments.length-1].currentTarget.dataset,r=o.eventParams||o["event-params"];n=r.index;return e.removeFileFromCollectionEdit(n)},e.e1=function(t){e.isEditMode?e.submitEdit():e.submitUpload()}),e.$mp.data=Object.assign({},{$root:{g0:n,g1:o,l0:r,g3:i,g4:a,m6:l,m7:c,m8:s,m9:u,m10:d,m11:f,l1:p,m15:h,m16:m,m17:g,g5:w,l2:v,g6:F,g7:y,g8:_,l3:C,g12:b,m24:x,m25:k,m26:D,m27:T}})},i=[]},"4a60":function(e,t,n){"use strict";n.r(t);var o=n("4361"),r=n("3fd7");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("5ecf");var a=n("828b"),l=Object(a["a"])(r["default"],o["b"],o["c"],!1,null,"126ab1e9",null,!1,o["a"],void 0);t["default"]=l.exports},"5ecf":function(e,t,n){"use strict";var o=n("d60f"),r=n.n(o);r.a},a1f4:function(e,t,n){"use strict";(function(e,o){var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(n("7eb4")),a=r(n("af34")),l=r(n("ee10")),c=r(n("7ca3")),s=n("8f59");function u(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"===typeof e)return d(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return d(e,t)}(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var o=0,r=function(){};return{s:r,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,l=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){l=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(l)throw i}}}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){(0,c.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var h={name:"StudyCenter",data:function(){return{searchKeyword:"",searchTimer:null,categories:[],categoryOptions:[],selectedCategoryId:"",isCategoryManagerVisible:!1,editingCategory:null,newCategory:{name:"",description:""},documents:[],loading:!1,hasInitialized:!1,page:1,pageSize:20,hasMore:!0,loadMoreStatus:"more",canUpload:!1,canManageCategory:!1,userRole:[],currentUserId:"",ROLE_CONSTANTS:{ADMIN_ROLES:["admin","supervisor","PM","GM"],UPLOAD_ROLES:["admin","supervisor","PM","GM","technician","mechanic","operator"],MANAGE_ROLES:["admin","supervisor","PM","GM"]},uploading:!1,isSubmitting:!1,uploadForm:{title:"",category_id:"",description:"",type:"single",file_name:"",file_url:"",file_type:"",file_size:0,files:[]},isEditMode:!1,editingDocumentId:"",editForm:{title:"",category_id:"",description:"",file_name:"",file_url:"",file_type:"",file_size:0},currentCollection:null,currentPreviewDocument:null,downloadingFiles:[],downloadedFiles:[],files:[]}},computed:p(p({},(0,s.mapGetters)("user",["userInfo"])),{},{selectedCategoryName:function(){var e=this;if(this.uploadForm.category_id){var t=this.categoryOptions.find((function(t){return t._id===e.uploadForm.category_id}));return t?t.name:""}return""},documentTitle:{get:function(){return this.isEditMode?this.editForm.title:this.uploadForm.title},set:function(e){this.isEditMode?this.editForm.title=e:this.uploadForm.title=e}},documentDescription:{get:function(){return this.isEditMode?this.editForm.description:this.uploadForm.description},set:function(e){this.isEditMode?this.editForm.description=e:this.uploadForm.description=e}},editingCategoryName:{get:function(){return this.editingCategory?this.editingCategory.name:""},set:function(e){this.editingCategory&&(this.editingCategory.name=e)}},editingCategoryDescription:{get:function(){return this.editingCategory?this.editingCategory.description:""},set:function(e){this.editingCategory&&(this.editingCategory.description=e)}},canSubmit:function(){return"single"===this.uploadForm.type?this.uploadForm.title&&this.uploadForm.category_id&&this.uploadForm.file_url:"collection"===this.uploadForm.type&&(this.uploadForm.title&&this.uploadForm.category_id&&this.uploadForm.files&&this.uploadForm.files.length>0)},canSubmitEdit:function(){return this.editForm.title&&this.editForm.category_id},currentCollectionFiles:function(){return this.currentCollection&&this.currentCollection.files?this.currentCollection.files:[]},currentCollectionFileCount:function(){return this.currentCollectionFiles.length}}),onLoad:function(){this.initPage()},onPullDownRefresh:function(){this.refreshData()},onReachBottom:function(){this.hasMore&&!this.loading&&this.loadMore()},methods:{initPage:function(){var t=this;return(0,l.default)(i.default.mark((function n(){return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,t.checkPermission();case 3:return n.next=5,Promise.all([t.loadCategories(),t.loadDocuments()]);case 5:t.hasInitialized=!0,n.next=12;break;case 8:n.prev=8,n.t0=n["catch"](0),console.error("页面初始化失败:",n.t0),e.showToast({title:"页面加载失败，请重试",icon:"none"});case 12:case"end":return n.stop()}}),n,null,[[0,8]])})))()},callStudyCenterAPI:function(t){var n=arguments,r=this;return(0,l.default)(i.default.mark((function a(){var l,c,s;return i.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return l=n.length>1&&void 0!==n[1]?n[1]:{},i.prev=1,c=e.getStorageSync("uni_id_token")||r.$store.getters["user/token"],i.next=5,o.callFunction({name:"study-center",data:{action:t,data:l,uniIdToken:c}});case 5:return s=i.sent,i.abrupt("return",s.result);case 9:throw i.prev=9,i.t0=i["catch"](1),console.error("调用study-center API失败 (".concat(t,"):"),i.t0),i.t0;case 13:case"end":return i.stop()}}),a,null,[[1,9]])})))()},handleAPIError:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"操作失败";console.error("API错误:",t);var o=t.message||n;e.showToast({title:o,icon:"none"})},checkPermission:function(){var e=this;return(0,l.default)(i.default.mark((function t(){var n;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:try{n=o.getCurrentUserInfo(),n&&n.uid&&(e.currentUserId=n.uid,e.userRole=n.role||[],e.canUpload=e.ROLE_CONSTANTS.UPLOAD_ROLES.some((function(t){return e.userRole.includes(t)})),e.canManageCategory=e.ROLE_CONSTANTS.MANAGE_ROLES.some((function(t){return e.userRole.includes(t)})))}catch(r){console.error("检查权限失败:",r)}case 1:case"end":return t.stop()}}),t)})))()},loadCategories:function(){var t=this;return(0,l.default)(i.default.mark((function n(){var o;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,t.callStudyCenterAPI("getCategories");case 3:o=n.sent,0===o.code&&(t.categories=o.data,t.categoryOptions=(0,a.default)(t.categories)),n.next=11;break;case 7:n.prev=7,n.t0=n["catch"](0),console.error("加载分类失败:",n.t0),e.showToast({title:"加载分类失败",icon:"none"});case 11:case"end":return n.stop()}}),n,null,[[0,7]])})))()},loadDocuments:function(){var t=arguments,n=this;return(0,l.default)(i.default.mark((function o(){var r,l,c,s,u,d;return i.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(r=t.length>0&&void 0!==t[0]&&t[0],!n.loading){o.next=3;break}return o.abrupt("return");case 3:return Date.now(),n.loading=!0,r||(n.page=1,n.documents=[]),o.prev=6,l={page:n.page,pageSize:n.pageSize},n.selectedCategoryId&&(l.category_id=n.selectedCategoryId),n.searchKeyword&&(l.keyword=n.searchKeyword),o.next=12,n.callStudyCenterAPI("getDocuments",l);case 12:c=o.sent,0===c.code?(s=c.data,u=s.list,d=s.total,n.documents=r?[].concat((0,a.default)(n.documents),(0,a.default)(u)):u,n.hasMore=u.length===n.pageSize&&n.documents.length<d,n.loadMoreStatus=n.hasMore?"more":"noMore"):n.handleAPIError(new Error(c.message),"加载文档失败"),o.next=20;break;case 16:o.prev=16,o.t0=o["catch"](6),console.error("加载文档失败:",o.t0),e.showToast({title:"加载文档失败",icon:"none"});case 20:return o.prev=20,n.loading=!1,e.stopPullDownRefresh(),o.finish(20);case 24:case"end":return o.stop()}}),o,null,[[6,16,20,24]])})))()},onSearchInput:function(e){var t=this;this.searchTimer&&clearTimeout(this.searchTimer),this.searchTimer=setTimeout((function(){t.onSearch(e)}),500)},onSearch:function(e){this.searchKeyword=e,this.loadDocuments()},selectCategory:function(e){this.selectedCategoryId=e,this.loadDocuments()},loadMore:function(){this.hasMore&&!this.loading&&(this.page++,this.loadDocuments(!0))},refreshData:function(){var e=this;return(0,l.default)(i.default.mark((function t(){return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.loadCategories();case 2:return t.next=4,e.loadDocuments();case 4:case"end":return t.stop()}}),t)})))()},viewDocument:function(e){"collection"!==e.type?this.showDocumentPreviewModal(e):this.showCollectionDownloadModal(e)},showDocumentPreviewModal:function(e){this.currentPreviewDocument=e,this.$refs.documentPreviewPopup.open()},closeDocumentPreviewModal:function(){this.$refs.documentPreviewPopup.close(),this.currentPreviewDocument=null},downloadDocument:function(t){var n=this;return(0,l.default)(i.default.mark((function r(){return i.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if("collection"!==t.type){r.next=3;break}return n.showCollectionDownloadModal(t),r.abrupt("return");case 3:return r.prev=3,e.showLoading({title:"下载中..."}),r.next=7,o.callFunction({name:"study-center",data:{action:"incrementDownload",data:{id:t._id}}});case 7:e.downloadFile({url:t.file_url,success:function(o){if(e.hideLoading(),200===o.statusCode){e.showToast({title:"下载成功",icon:"success"});var r=n.documents.findIndex((function(e){return e._id===t._id}));-1!==r&&n.$set(n.documents,r,p(p({},n.documents[r]),{},{download_count:n.documents[r].download_count+1})),n.currentPreviewDocument&&n.currentPreviewDocument._id===t._id&&(n.currentPreviewDocument.download_count=(n.currentPreviewDocument.download_count||0)+1),e.openDocument({filePath:o.tempFilePath,showMenu:!0,fail:function(t){e.showModal({title:"提示",content:"文件已下载，但无法直接打开。请在文件管理器中查看。",showCancel:!1})}})}else e.showToast({title:"下载失败",icon:"none"})},fail:function(t){e.hideLoading(),console.error("下载失败:",t),e.showToast({title:"下载失败",icon:"none"})}}),r.next=15;break;case 10:r.prev=10,r.t0=r["catch"](3),e.hideLoading(),console.error("下载文档失败:",r.t0),e.showToast({title:"下载失败",icon:"none"});case 15:case"end":return r.stop()}}),r,null,[[3,10]])})))()},showCollectionDownloadModal:function(e){this.currentCollection=e,this.downloadingFiles=[],this.downloadedFiles=[],this.$refs.collectionDownloadPopup.open()},closeCollectionDownloadModal:function(){this.$refs.collectionDownloadPopup.close(),this.currentCollection=null,this.downloadingFiles=[],this.downloadedFiles=[]},downloadCollectionFile:function(t,n){var o=this;return(0,l.default)(i.default.mark((function r(){var a;return i.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!o.downloadingFiles.includes(n)){r.next=2;break}return r.abrupt("return");case 2:o.downloadingFiles.push(n);try{e.showLoading({title:"下载中...",mask:!0}),e.downloadFile({url:t.file_url,success:function(t){e.hideLoading(),200===t.statusCode?(o.downloadedFiles.includes(n)||o.downloadedFiles.push(n),e.showToast({title:"下载成功",icon:"success"}),e.openDocument({filePath:t.tempFilePath,showMenu:!0,fail:function(t){e.showModal({title:"提示",content:"文件已下载，但无法直接打开。请在文件管理器中查看。",showCancel:!1})}}),o.incrementCollectionDownload()):e.showToast({title:"下载失败",icon:"none"})},fail:function(t){e.hideLoading(),console.error("下载失败:",t),e.showToast({title:"下载失败",icon:"none"})},complete:function(){var e=o.downloadingFiles.indexOf(n);e>-1&&o.downloadingFiles.splice(e,1)}})}catch(i){e.hideLoading(),console.error("下载文件失败:",i),e.showToast({title:"下载失败",icon:"none"}),a=o.downloadingFiles.indexOf(n),a>-1&&o.downloadingFiles.splice(a,1)}case 4:case"end":return r.stop()}}),r)})))()},incrementCollectionDownload:function(){var e=this;return(0,l.default)(i.default.mark((function t(){var n;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.currentCollection){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,t.next=5,o.callFunction({name:"study-center",data:{action:"incrementCollectionDownload",data:{id:e.currentCollection._id}}});case 5:n=e.documents.findIndex((function(t){return t._id===e.currentCollection._id})),-1!==n&&e.$set(e.documents,n,p(p({},e.documents[n]),{},{download_count:e.documents[n].download_count+1})),e.currentCollection&&(e.currentCollection.download_count=(e.currentCollection.download_count||0)+1),t.next=13;break;case 10:t.prev=10,t.t0=t["catch"](2),console.error("更新集合下载次数失败:",t.t0);case 13:case"end":return t.stop()}}),t,null,[[2,10]])})))()},downloadAllFiles:function(){var t=this;return(0,l.default)(i.default.mark((function n(){var o,r,a;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t.currentCollection&&0!==t.currentCollectionFileCount){n.next=2;break}return n.abrupt("return");case 2:o=t.currentCollectionFiles,r=o.length,a=0,e.showModal({title:"批量下载",content:"确定要下载全部 ".concat(r," 个文件吗？"),success:function(){var n=(0,l.default)(i.default.mark((function n(l){var c,s;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!l.confirm){n.next=13;break}e.showLoading({title:"准备下载...",mask:!0}),c=i.default.mark((function n(r){var l,c,s;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return l=o[r],c=r,n.prev=2,t.downloadingFiles.push(c),n.next=6,new Promise((function(n,o){e.downloadFile({url:l.file_url,success:function(r){200===r.statusCode?(a++,t.downloadedFiles.includes(c)||t.downloadedFiles.push(c),e.openDocument({filePath:r.tempFilePath,showMenu:!0,fail:function(e){}}),n()):o(new Error("下载失败"))},fail:function(e){console.error("下载失败:",e),o(e)}})}));case 6:return n.next=8,new Promise((function(e){return setTimeout(e,500)}));case 8:n.next=13;break;case 10:n.prev=10,n.t0=n["catch"](2),console.error("下载文件 ".concat(l.file_name," 失败:"),n.t0);case 13:return n.prev=13,s=t.downloadingFiles.indexOf(c),s>-1&&t.downloadingFiles.splice(s,1),n.finish(13);case 17:case"end":return n.stop()}}),n,null,[[2,10,13,17]])})),s=0;case 4:if(!(s<o.length)){n.next=9;break}return n.delegateYield(c(s),"t0",6);case 6:s++,n.next=4;break;case 9:return e.hideLoading(),n.next=12,t.incrementCollectionDownload();case 12:a===r?e.showToast({title:"全部下载成功",icon:"success"}):e.showModal({title:"下载完成",content:"成功下载 ".concat(a,"/").concat(r," 个文件"),showCancel:!1});case 13:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()});case 6:case"end":return n.stop()}}),n)})))()},editDocument:function(e){this.isEditMode=!0,this.editingDocumentId=e._id,this.editForm={title:e.title,category_id:e.category_id,description:e.description||""},this.$refs.uploadPopup.open()},getCurrentCategoryName:function(){var e=this.isEditMode?this.editForm.category_id:this.uploadForm.category_id;if(e){var t=this.categoryOptions.find((function(t){return t._id===e}));return t?t.name:""}return""},getCurrentEditingDocument:function(){var e=this;return this.isEditMode&&this.editingDocumentId?this.documents.find((function(t){return t._id===e.editingDocumentId})):null},getCurrentEditingDocumentFileName:function(){var e=this.getCurrentEditingDocument();if(!e)return"未知文档";if("collection"===e.type){var t=e.files?e.files.length:0;return"".concat(e.title," (").concat(t,"个文件)")}return e.file_name||""},removeFileFromCollectionEdit:function(t){var n=this;return(0,l.default)(i.default.mark((function r(){var a,c,s;return i.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(a=n.getCurrentEditingDocument(),!a||"collection"!==a.type||!a.files){r.next=22;break}if(c=a.files[t],s=1===a.files.length,!s){r.next=8;break}e.showModal({title:"确认删除",content:"删除最后一个文件将同时删除整个集合「".concat(a.title,"」，确定继续吗？"),success:function(){var t=(0,l.default)(i.default.mark((function t(r){var l;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!r.confirm){t.next=27;break}if(t.prev=1,e.showLoading({title:"删除中..."}),!c||!c.file_url){t.next=15;break}return t.prev=4,t.next=7,n.deleteCloudFile([c.file_url]);case 7:t.next=15;break;case 9:return t.prev=9,t.t0=t["catch"](4),console.error("删除云存储文件失败:",t.t0),e.showToast({title:"删除云存储文件失败，操作已取消",icon:"none"}),e.hideLoading(),t.abrupt("return");case 15:return t.next=17,o.callFunction({name:"study-center",data:{action:"deleteCollection",data:{id:a._id}}});case 17:l=t.sent,e.hideLoading(),0===l.result.code?(e.showToast({title:"集合删除成功",icon:"success"}),n.closeUploadModal(),n.refreshData()):e.showToast({title:l.result.message||"删除失败",icon:"none"}),t.next=27;break;case 22:t.prev=22,t.t1=t["catch"](1),e.hideLoading(),console.error("删除集合失败:",t.t1),e.showToast({title:"删除失败",icon:"none"});case 27:case"end":return t.stop()}}),t,null,[[1,22],[4,9]])})));return function(e){return t.apply(this,arguments)}}()}),r.next=22;break;case 8:if(!c||!c.file_url){r.next=20;break}return r.prev=9,r.next=12,n.deleteCloudFile([c.file_url]);case 12:e.showToast({title:"文件已从云端删除",icon:"success"}),r.next=20;break;case 15:return r.prev=15,r.t0=r["catch"](9),console.error("删除云端文件失败:",r.t0),e.showToast({title:"删除云端文件失败",icon:"none"}),r.abrupt("return");case 20:a.files.splice(t,1),n.$forceUpdate();case 22:case"end":return r.stop()}}),r,null,[[9,15]])})))()},addFilesToCollection:function(){var t=this;return(0,l.default)(i.default.mark((function n(){var r,a,l,c,s,d,f;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,e.chooseMessageFile({count:9,type:"file"});case 3:if(r=n.sent,a=t.getCurrentEditingDocument(),!a||"collection"!==a.type){n.next=37;break}l=u(r.tempFiles),n.prev=7,l.s();case 9:if((c=l.n()).done){n.next=27;break}return s=c.value,d={file_name:s.name,file_size:s.size,file_type:s.name.split(".").pop().toLowerCase(),file_url:"",order:a.files?a.files.length:0},n.prev=12,n.next=15,o.uploadFile({filePath:s.path,cloudPath:"study-center/".concat(Date.now(),"_").concat(s.name)});case 15:f=n.sent,d.file_url=f.fileID,a.files||(a.files=[]),a.files.push(d),n.next=25;break;case 21:n.prev=21,n.t0=n["catch"](12),console.error("上传文件失败:",n.t0),e.showToast({title:"文件上传失败",icon:"none"});case 25:n.next=9;break;case 27:n.next=32;break;case 29:n.prev=29,n.t1=n["catch"](7),l.e(n.t1);case 32:return n.prev=32,l.f(),n.finish(32);case 35:t.$forceUpdate(),e.showToast({title:"文件添加成功",icon:"success"});case 37:n.next=43;break;case 39:n.prev=39,n.t2=n["catch"](0),console.error("选择文件失败:",n.t2),e.showToast({title:"请从聊天记录中选择文件",icon:"none",duration:3e3});case 43:case"end":return n.stop()}}),n,null,[[0,39],[7,29,32,35],[12,21]])})))()},submitEdit:function(){var t=this;return(0,l.default)(i.default.mark((function n(){var r,a,l,c;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!t.isSubmitting){n.next=2;break}return n.abrupt("return");case 2:if(t.isSubmitting=!0,n.prev=3,e.showLoading({title:"保存中...",mask:!0}),r=t.getCurrentEditingDocument(),r){n.next=10;break}return e.hideLoading(),e.showToast({title:"文档不存在",icon:"none"}),n.abrupt("return");case 10:return a={id:t.editingDocumentId,title:t.editForm.title,category_id:t.editForm.category_id,description:t.editForm.description},l="updateDocument","collection"===r.type?(l="updateCollection",r.files&&(a.files=r.files)):t.editForm.file_url&&(a.file_name=t.editForm.file_name,a.file_url=t.editForm.file_url,a.file_type=t.editForm.file_type,a.file_size=t.editForm.file_size),n.next=15,o.callFunction({name:"study-center",data:{action:l,data:a}});case 15:c=n.sent,e.hideLoading(),0===c.result.code?(e.showToast({title:"修改成功",icon:"success"}),t.closeUploadModal(),t.refreshData()):e.showToast({title:c.result.message||"修改失败",icon:"none"}),n.next=25;break;case 20:n.prev=20,n.t0=n["catch"](3),e.hideLoading(),console.error("编辑文档失败:",n.t0),e.showToast({title:"修改失败",icon:"none"});case 25:return n.prev=25,t.isSubmitting=!1,n.finish(25);case 28:case"end":return n.stop()}}),n,null,[[3,20,25,28]])})))()},deleteDocument:function(t){var n=this,r="collection"===t.type,a=r?"集合":"文档";e.showModal({title:"确认删除",content:"确定要删除".concat(a,"「").concat(t.title,"」吗？此操作不可恢复。"),success:function(){var a=(0,l.default)(i.default.mark((function a(l){var c;return i.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!l.confirm){i.next=33;break}if(i.prev=1,e.showLoading({title:"删除中..."}),!r){i.next=9;break}return i.next=6,o.callFunction({name:"study-center",data:{action:"deleteCollection",data:{id:t._id}}});case 6:c=i.sent,i.next=24;break;case 9:if(!t.file_url){i.next=21;break}return i.prev=10,i.next=13,n.deleteCloudFile([t.file_url]);case 13:i.next=21;break;case 15:return i.prev=15,i.t0=i["catch"](10),console.error("删除云存储文件失败:",i.t0),e.showToast({title:"删除云存储文件失败，操作已取消",icon:"none"}),e.hideLoading(),i.abrupt("return");case 21:return i.next=23,o.callFunction({name:"study-center",data:{action:"deleteDocument",data:{id:t._id}}});case 23:c=i.sent;case 24:e.hideLoading(),0===c.result.code?(e.showToast({title:"删除成功",icon:"success"}),n.refreshData()):e.showToast({title:c.result.message||"删除失败",icon:"none"}),i.next=33;break;case 28:i.prev=28,i.t1=i["catch"](1),e.hideLoading(),console.error("删除文档失败:",i.t1),e.showToast({title:"删除失败",icon:"none"});case 33:case"end":return i.stop()}}),a,null,[[1,28],[10,15]])})));return function(e){return a.apply(this,arguments)}}()})},extractFileIdFromUrl:function(e){try{if(e&&(e.startsWith("cloud://")||e.includes("tcb-api")))return e;var t=e.match(/\/([a-f0-9]{24,})/i);return t?t[1]:e}catch(n){return console.error("提取文件ID失败:",n),e}},canEditDocument:function(e){var t=this;return this.ROLE_CONSTANTS.ADMIN_ROLES.some((function(e){return t.userRole.includes(e)}))||e.uploader_id===this.currentUserId},canDeleteDocument:function(e){var t=this;return this.ROLE_CONSTANTS.ADMIN_ROLES.some((function(e){return t.userRole.includes(e)}))||e.uploader_id===this.currentUserId},showUploadModal:function(){this.canUpload?(this.resetUploadForm(),this.$refs.uploadPopup.open()):e.showToast({title:"权限不足",icon:"none"})},closeUploadModal:function(){this.$refs.uploadPopup.close(),this.isEditMode?this.resetEditForm():this.resetUploadForm()},resetUploadForm:function(){this.uploadForm={title:"",category_id:"",description:"",type:"single",file_name:"",file_url:"",file_type:"",file_size:0,files:[]}},resetEditForm:function(){this.isEditMode=!1,this.editingDocumentId="",this.editForm={title:"",category_id:"",description:"",file_name:"",file_url:"",file_type:"",file_size:0}},openCategorySelector:function(){this.$refs.categorySelectorPopup.open()},closeCategorySelector:function(){this.$refs.categorySelectorPopup.close()},handleCategorySelect:function(e){this.isEditMode?this.editForm.category_id=e._id:this.uploadForm.category_id=e._id,this.closeCategorySelector()},showCategoryManager:function(){this.canManageCategory?this.isCategoryManagerVisible=!0:e.showToast({title:"权限不足",icon:"none"})},closeCategoryManager:function(){this.isCategoryManagerVisible=!1,this.newCategory={name:"",description:""}},createCategory:function(){var t=this;return(0,l.default)(i.default.mark((function n(){var r,a;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t.newCategory.name.trim()){n.next=3;break}return e.showToast({title:"请输入分类名称",icon:"none"}),n.abrupt("return");case 3:if(!t.isSubmitting){n.next=5;break}return n.abrupt("return");case 5:return t.isSubmitting=!0,n.prev=6,r=e.getStorageSync("uni_id_token")||t.$store.getters["user/token"],n.next=10,o.callFunction({name:"study-center",data:{action:"createCategory",data:t.newCategory,uniIdToken:r}});case 10:if(a=n.sent,0!==a.result.code){n.next=18;break}return e.showToast({title:"创建成功",icon:"success"}),t.newCategory={name:"",description:""},n.next=16,t.loadCategories();case 16:n.next=19;break;case 18:e.showToast({title:a.result.message||"创建失败",icon:"none"});case 19:n.next=25;break;case 21:n.prev=21,n.t0=n["catch"](6),console.error("创建分类失败:",n.t0),e.showToast({title:"创建失败",icon:"none"});case 25:return n.prev=25,t.isSubmitting=!1,n.finish(25);case 28:case"end":return n.stop()}}),n,null,[[6,21,25,28]])})))()},editCategory:function(e){var t=this;this.closeCategoryManager(),this.editingCategory=p({},e),setTimeout((function(){t.showCategoryEditModal()}),200)},showCategoryEditModal:function(){this.$refs.categoryEditPopup.open()},closeCategoryEditModal:function(){this.$refs.categoryEditPopup.close(),this.editingCategory=null},submitCategoryEdit:function(){var t=this;return(0,l.default)(i.default.mark((function n(){var r,a;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t.editingCategory&&t.editingCategoryName.trim()){n.next=3;break}return e.showToast({title:"请输入分类名称",icon:"none"}),n.abrupt("return");case 3:if(!t.isSubmitting){n.next=5;break}return n.abrupt("return");case 5:return t.isSubmitting=!0,n.prev=6,e.showLoading({title:"保存中..."}),r=e.getStorageSync("uni_id_token")||t.$store.getters["user/token"],n.next=11,o.callFunction({name:"study-center",data:{action:"updateCategory",data:{id:t.editingCategory._id,name:t.editingCategoryName.trim(),description:t.editingCategoryDescription||""}},uniIdToken:r});case 11:if(a=n.sent,e.hideLoading(),0!==a.result.code){n.next=20;break}return e.showToast({title:"修改成功",icon:"success"}),t.closeCategoryEditModal(),n.next=18,t.loadCategories();case 18:n.next=21;break;case 20:e.showToast({title:a.result.message||"修改失败",icon:"none"});case 21:n.next=28;break;case 23:n.prev=23,n.t0=n["catch"](6),console.error("修改分类失败:",n.t0),e.hideLoading(),e.showToast({title:"修改失败",icon:"none"});case 28:return n.prev=28,t.isSubmitting=!1,n.finish(28);case 31:case"end":return n.stop()}}),n,null,[[6,23,28,31]])})))()},confirmDeleteCategory:function(t){var n=this;this.closeCategoryManager(),setTimeout((function(){e.showModal({title:"确认删除",content:'确定要删除分类"'.concat(t.name,'"吗？删除后无法恢复。'),success:function(e){e.confirm&&n.deleteCategory(t)}})}),200)},deleteCategory:function(t){var n=this;return(0,l.default)(i.default.mark((function r(){var a,l;return i.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.prev=0,e.showLoading({title:"删除中..."}),a=e.getStorageSync("uni_id_token")||n.$store.getters["user/token"],r.next=5,o.callFunction({name:"study-center",data:{action:"deleteCategory",data:{id:t._id}},uniIdToken:a});case 5:if(l=r.sent,e.hideLoading(),0!==l.result.code){r.next=13;break}return e.showToast({title:"删除成功",icon:"success"}),r.next=11,n.loadCategories();case 11:r.next=14;break;case 13:e.showToast({title:l.result.message||"删除失败",icon:"none"});case 14:r.next=21;break;case 16:r.prev=16,r.t0=r["catch"](0),console.error("删除分类失败:",r.t0),e.hideLoading(),e.showToast({title:"删除失败",icon:"none"});case 21:case"end":return r.stop()}}),r,null,[[0,16]])})))()},chooseFile:function(){var t=this;e.chooseMessageFile({count:1,type:"file",success:function(e){var n=e.tempFiles[0];t.uploadForm.file_name=n.name,t.uploadForm.file_size=n.size,t.uploadForm.file_type=n.name.split(".").pop().toLowerCase(),t.uploadForm.title||(t.uploadForm.title=n.name.replace(/\.[^/.]+$/,"")),t.uploadFileToCloud(n.path)},fail:function(t){console.error("选择文件失败:",t),e.showToast({title:"请从聊天记录中选择文件",icon:"none",duration:3e3})}})},removeFile:function(){var t=this;return(0,l.default)(i.default.mark((function n(){return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!t.uploadForm.file_url){n.next=12;break}return n.prev=1,n.next=4,t.deleteCloudFile([t.uploadForm.file_url]);case 4:e.showToast({title:"文件已从云端删除",icon:"success"}),n.next=12;break;case 7:return n.prev=7,n.t0=n["catch"](1),console.error("删除云端文件失败:",n.t0),e.showToast({title:"删除云端文件失败",icon:"none"}),n.abrupt("return");case 12:t.uploadForm.file_name="",t.uploadForm.file_size=0,t.uploadForm.file_type="",t.uploadForm.file_url="",t.tempFilePath="",t.uploadForm.title="",t.uploadForm.description="";case 19:case"end":return n.stop()}}),n,null,[[1,7]])})))()},selectDocumentType:function(e){this.uploadForm.type=e,this.uploadForm.files=[],this.removeFile()},chooseMultipleFiles:function(){var t=this;e.chooseMessageFile({count:9,type:"file",success:function(n){var o=n.tempFiles;t.uploadForm.files.length+o.length>9?e.showToast({title:"最多只能选择9个文件",icon:"none"}):(!t.uploadForm.title&&o.length>0&&(t.uploadForm.title=o[0].name.replace(/\.[^/.]+$/,"")+"等文档集合"),t.uploadMultipleFilesToCloud(o))},fail:function(t){console.error("选择文件失败:",t),e.showToast({title:"请从聊天记录中选择文件",icon:"none",duration:3e3})}})},removeFileFromCollection:function(t){var n=this;return(0,l.default)(i.default.mark((function o(){var r,a;return i.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(r=n.uploadForm.files[t],a=1===n.uploadForm.files.length,!a){o.next=6;break}e.showModal({title:"确认删除",content:"删除最后一个文件将清空整个集合，确定继续吗？",success:function(){var o=(0,l.default)(i.default.mark((function o(a){return i.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(!a.confirm){o.next=16;break}if(!r||!r.file_url){o.next=13;break}return o.prev=2,o.next=5,n.deleteCloudFile([r.file_url]);case 5:e.showToast({title:"文件已从云端删除",icon:"success"}),o.next=13;break;case 8:return o.prev=8,o.t0=o["catch"](2),console.error("删除云端文件失败:",o.t0),e.showToast({title:"删除云端文件失败",icon:"none"}),o.abrupt("return");case 13:n.uploadForm.files.splice(t,1),n.uploadForm.title="",n.uploadForm.description="";case 16:case"end":return o.stop()}}),o,null,[[2,8]])})));return function(e){return o.apply(this,arguments)}}()}),o.next=20;break;case 6:if(!r||!r.file_url){o.next=18;break}return o.prev=7,o.next=10,n.deleteCloudFile([r.file_url]);case 10:e.showToast({title:"文件已从云端删除",icon:"success"}),o.next=18;break;case 13:return o.prev=13,o.t0=o["catch"](7),console.error("删除云端文件失败:",o.t0),e.showToast({title:"删除云端文件失败",icon:"none"}),o.abrupt("return");case 18:n.uploadForm.files.splice(t,1),0===n.uploadForm.files.length&&(n.uploadForm.title="",n.uploadForm.description="");case 20:case"end":return o.stop()}}),o,null,[[7,13]])})))()},chooseFileForEdit:function(){var t=this;e.chooseMessageFile({count:1,type:"file",success:function(e){var n=e.tempFiles[0];t.editForm.file_name=n.name,t.editForm.file_size=n.size,t.editForm.file_type=n.name.split(".").pop().toLowerCase(),t.uploadFileToCloudForEdit(n.path)},fail:function(t){console.error("选择文件失败:",t),e.showToast({title:"请从聊天记录中选择文件",icon:"none",duration:3e3})}})},removeFileInEdit:function(){var t=this;return(0,l.default)(i.default.mark((function n(){var r;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(r=t.getCurrentEditingDocument(),r){n.next=4;break}return e.showToast({title:"未找到要编辑的文档",icon:"none"}),n.abrupt("return");case 4:e.showModal({title:"确认删除",content:"确定要删除文档「".concat(r.title,"」吗？此操作不可恢复。"),success:function(){var n=(0,l.default)(i.default.mark((function n(a){var l;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!a.confirm){n.next=27;break}if(n.prev=1,e.showLoading({title:"删除中..."}),!r.file_url){n.next=15;break}return n.prev=4,n.next=7,t.deleteCloudFile([r.file_url]);case 7:n.next=15;break;case 9:return n.prev=9,n.t0=n["catch"](4),console.error("删除云存储文件失败:",n.t0),e.showToast({title:"删除云存储文件失败，操作已取消",icon:"none"}),e.hideLoading(),n.abrupt("return");case 15:return n.next=17,o.callFunction({name:"study-center",data:{action:"deleteDocument",data:{id:r._id}}});case 17:l=n.sent,e.hideLoading(),0===l.result.code?(e.showToast({title:"删除成功",icon:"success"}),t.closeUploadModal(),t.refreshData()):e.showToast({title:l.result.message||"删除失败",icon:"none"}),n.next=27;break;case 22:n.prev=22,n.t1=n["catch"](1),e.hideLoading(),console.error("删除文档失败:",n.t1),e.showToast({title:"删除失败",icon:"none"});case 27:case"end":return n.stop()}}),n,null,[[1,22],[4,9]])})));return function(e){return n.apply(this,arguments)}}()});case 5:case"end":return n.stop()}}),n)})))()},uploadFileToCloudForEdit:function(t){var n=this;return(0,l.default)(i.default.mark((function r(){var a;return i.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return n.uploading=!0,r.prev=1,r.next=4,o.uploadFile({filePath:t,cloudPath:"study/".concat(Date.now(),"_").concat(n.editForm.file_name),cloudPathAsRealPath:!0});case 4:a=r.sent,n.editForm.file_url=a.fileID,e.showToast({title:"文件上传成功",icon:"success"}),r.next=13;break;case 9:r.prev=9,r.t0=r["catch"](1),console.error("文件上传失败:",r.t0),e.showToast({title:"文件上传失败",icon:"none"});case 13:return r.prev=13,n.uploading=!1,r.finish(13);case 16:case"end":return r.stop()}}),r,null,[[1,9,13,16]])})))()},uploadMultipleFilesToCloud:function(t){var n=this;return(0,l.default)(i.default.mark((function r(){var c,s,u;return i.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return n.uploading=!0,r.prev=1,c=t.map(function(){var e=(0,l.default)(i.default.mark((function e(t,n){var r;return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,o.uploadFile({filePath:t.path,cloudPath:"study/".concat(Date.now(),"_").concat(n,"_").concat(t.name),cloudPathAsRealPath:!0});case 3:return r=e.sent,e.abrupt("return",{file_name:t.name,file_size:t.size,file_type:t.name.split(".").pop().toLowerCase(),file_url:r.fileID,order:n});case 7:return e.prev=7,e.t0=e["catch"](0),console.error("文件 ".concat(t.name," 上传失败:"),e.t0),e.abrupt("return",null);case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t,n){return e.apply(this,arguments)}}()),r.next=5,Promise.all(c);case 5:s=r.sent,u=s.filter((function(e){return null!==e})),u.length>0?(n.uploadForm.files=[].concat((0,a.default)(n.uploadForm.files),(0,a.default)(u)),e.showToast({title:"成功上传 ".concat(u.length," 个文件"),icon:"success"})):e.showToast({title:"文件上传失败",icon:"none"}),r.next=14;break;case 10:r.prev=10,r.t0=r["catch"](1),console.error("批量上传文件失败:",r.t0),e.showToast({title:"文件上传失败",icon:"none"});case 14:return r.prev=14,n.uploading=!1,r.finish(14);case 17:case"end":return r.stop()}}),r,null,[[1,10,14,17]])})))()},uploadFileToCloud:function(t){var n=this;return(0,l.default)(i.default.mark((function r(){var a;return i.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return n.uploading=!0,r.prev=1,r.next=4,o.uploadFile({filePath:t,cloudPath:"study/".concat(Date.now(),"_").concat(n.uploadForm.file_name),cloudPathAsRealPath:!0});case 4:a=r.sent,n.uploadForm.file_url=a.fileID,e.showToast({title:"文件上传成功",icon:"success"}),r.next=13;break;case 9:r.prev=9,r.t0=r["catch"](1),console.error("文件上传失败:",r.t0),e.showToast({title:"文件上传失败",icon:"none"});case 13:return r.prev=13,n.uploading=!1,r.finish(13);case 16:case"end":return r.stop()}}),r,null,[[1,9,13,16]])})))()},submitUpload:function(){var t=this;return(0,l.default)(i.default.mark((function n(){var r,a,l,c;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!t.isSubmitting){n.next=2;break}return n.abrupt("return");case 2:if(t.canSubmit){n.next=5;break}return e.showToast({title:"请完善信息",icon:"none"}),n.abrupt("return");case 5:return t.isSubmitting=!0,n.prev=6,r=e.getStorageSync("uni_id_token")||t.$store.getters["user/token"],"single"===t.uploadForm.type?(a="uploadDocument",l=t.uploadForm):"collection"===t.uploadForm.type&&(a="uploadCollection",l=t.uploadForm),n.next=11,o.callFunction({name:"study-center",data:{action:a,data:l,uniIdToken:r}});case 11:c=n.sent,0===c.result.code?(e.showToast({title:"上传成功",icon:"success"}),t.closeUploadModal(),t.refreshData()):e.showToast({title:c.result.message||"上传失败",icon:"none"}),n.next=19;break;case 15:n.prev=15,n.t0=n["catch"](6),console.error("上传文档失败:",n.t0),e.showToast({title:"上传失败",icon:"none"});case 19:return n.prev=19,t.isSubmitting=!1,n.finish(19);case 22:case"end":return n.stop()}}),n,null,[[6,15,19,22]])})))()},appConfig:function(){return{fileTypes:{pdf:{icon:"paperclip",color:"#ff4757"},doc:{icon:"paperclip",color:"#2e86de"},docx:{icon:"paperclip",color:"#2e86de"},xls:{icon:"paperclip",color:"#10ac84"},xlsx:{icon:"paperclip",color:"#10ac84"},ppt:{icon:"paperclip",color:"#ff6348"},pptx:{icon:"paperclip",color:"#ff6348"},txt:{icon:"paperclip",color:"#747d8c"},zip:{icon:"paperclip",color:"#ffa502"},rar:{icon:"paperclip",color:"#ffa502"},png:{icon:"image",color:"#3a86ff"},jpg:{icon:"image",color:"#3a86ff"},jpeg:{icon:"image",color:"#3a86ff"},gif:{icon:"image",color:"#3a86ff"}},pagination:{defaultPageSize:10,maxPageSize:50},ui:{toastDuration:2e3,loadingDelay:300}}},getFileIconClass:function(e){if(!e)return"folder";return{pdf:"paperclip",doc:"paperclip",docx:"paperclip",xls:"paperclip",xlsx:"paperclip",ppt:"paperclip",pptx:"paperclip",txt:"paperclip",zip:"paperclip",rar:"paperclip",png:"image",jpg:"image",jpeg:"image",gif:"image"}[e]||"folder"},getFileIconColor:function(e){if(!e)return"#666";return{pdf:"#ff4757",doc:"#2e86de",docx:"#2e86de",xls:"#10ac84",xlsx:"#10ac84",ppt:"#ff6348",pptx:"#ff6348",txt:"#747d8c",zip:"#ffa502",rar:"#ffa502",png:"#3a86ff",jpg:"#3a86ff",jpeg:"#3a86ff",gif:"#3a86ff"}[e]||"#666"},deleteCloudFile:function(e){return(0,l.default)(i.default.mark((function t(){var n,r,a;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,n=e.filter((function(e){return e&&e.trim()})).map((function(e){if(e.startsWith("cloud://"))return e;if(e.includes("cdn.bspapp.com")){var t=e.match(/\/([a-f0-9]{24,})/i);return t&&t[1]?t[1]:e}return e})).filter((function(e){return e})),!(n.length>0)){t.next=13;break}return t.next=5,o.callFunction({name:"delete-file",data:{fileList:n}});case 5:if(r=t.sent,!r.result||0!==r.result.code){t.next=9;break}t.next=13;break;case 9:if(!(r.result&&r.result.successCount>0)){t.next=12;break}t.next=13;break;case 12:throw new Error((null===(a=r.result)||void 0===a?void 0:a.message)||"删除失败");case 13:t.next=19;break;case 15:throw t.prev=15,t.t0=t["catch"](0),console.error("删除云端文件失败:",t.t0),t.t0;case 19:case"end":return t.stop()}}),t,null,[[0,15]])})))()},handleError:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"操作",o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return console.error("".concat(n,"失败:"),t),o&&e.showToast({title:"".concat(n,"失败"),icon:"none",duration:this.appConfig.ui.toastDuration||2e3}),!1},showSuccess:function(t,n){e.showToast({title:t,icon:"success",duration:n||this.appConfig.ui.toastDuration||2e3})},formatFileSize:function(e){if(!e)return"0B";var t=["B","KB","MB","GB"],n=0;while(e>=1024&&n<t.length-1)e/=1024,n++;return Math.round(100*e)/100+t[n]},formatTime:function(e){if(!e)return"";var t=new Date(e);if(isNaN(t.getTime()))return"";var n=t.getFullYear(),o=String(t.getMonth()+1).padStart(2,"0"),r=String(t.getDate()).padStart(2,"0");return"".concat(n,"-").concat(o,"-").concat(r)}}};t.default=h}).call(this,n("df3c")["default"],n("861b")["uniCloud"])},d60f:function(e,t,n){}},[["2897","common/runtime","common/vendor"]]]);