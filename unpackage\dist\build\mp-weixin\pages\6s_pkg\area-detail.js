require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/area-detail"],{"2c13":function(e,t,n){},"325d":function(e,t,n){"use strict";n.r(t);var a=n("ce6f"),i=n("bec1");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);n("b914");var o=n("828b"),c=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"76f23d42",null,!1,a["a"],void 0);t["default"]=c.exports},"82c7":function(e,t,n){"use strict";(function(e,t){var a=n("47a9");n("357b"),n("861b");a(n("3240"));var i=a(n("325d"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(i.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"880a":function(e,t,n){"use strict";(function(e){var a=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("7eb4")),r=a(n("af34")),o=a(n("34cf")),c=a(n("ee10")),s=a(n("7ca3")),u=n("882c");function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){(0,s.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var g={name:"AreaDetail",data:function(){return{areaId:"",areaType:"",mode:"view",recordId:"",cleaningDate:"",week:"",isHistoricalView:!1,loading:!0,loadError:!1,areaInfo:{id:"",name:"",type:"",location:"",description:"",assignedEmployee:"",cleaningFrequency:"",nextCleaningTime:"",status:"",canClean:!0},currentWeekText:"",currentWeekRecord:null,selectedTimeFilter:"近3个月",cleaningHistory:[],inspectionHistory:[],expandedMonths:[],timeCache:null,historyDataLoaded:!1}},computed:{groupedHistory:function(){var e={};return this.cleaningHistory.forEach((function(t){var n=new Date(t.submitDate.replace(/-/g,"/")),a="".concat(n.getFullYear(),"-").concat(String(n.getMonth()+1).padStart(2,"0"));e[a]||(e[a]={month:a,monthText:"".concat(n.getFullYear(),"年").concat(n.getMonth()+1,"月"),records:[]}),e[a].records.push(t)})),Object.values(e).sort((function(e,t){return t.month.localeCompare(e.month)})).map((function(e){return l(l({},e),{},{records:e.records.sort((function(e,t){return t.submitDate.localeCompare(e.submitDate)}))})}))}},onLoad:function(t){t.areaId&&(this.areaId=t.areaId,this.areaType=t.type||"fixed",this.mode=t.mode||"view",this.recordId=t.recordId||"",this.cleaningDate=t.cleaningDate||"",this.week=t.week||"",this.isHistoricalView=!!(this.cleaningDate||this.week||this.recordId),this.initCurrentWeekText(),this.initExpandedMonths(),this.loadPageData()),e.$on("cleaningRecordUpdated",this.handleRecordUpdated),e.$on("rectificationRecordUpdated",this.handleRecordUpdated),e.$on("inspectionRecordUpdated",this.handleRecordUpdated)},onUnload:function(){e.$off("cleaningRecordUpdated",this.handleRecordUpdated),e.$off("rectificationRecordUpdated",this.handleRecordUpdated),e.$off("inspectionRecordUpdated",this.handleRecordUpdated)},methods:{initCurrentWeekText:function(){if(this.isHistoricalView&&this.cleaningDate){var e=new Date(this.cleaningDate),t=this.toBeijingTime(e),n=this.getWeekStart(t),a=this.getWeekEnd(t),i="".concat(n.getMonth()+1,"月").concat(n.getDate(),"日"),r="".concat(a.getMonth()+1,"月").concat(a.getDate(),"日"),o=this.getWeekNumber(n);this.currentWeekText="第".concat(o,"周 (").concat(i," - ").concat(r,")")}else{var c=new Date,s=this.toBeijingTime(c),u=this.getWeekStart(s),d=this.getWeekEnd(s),l="".concat(u.getMonth()+1,"月").concat(u.getDate(),"日"),g="".concat(d.getMonth()+1,"月").concat(d.getDate(),"日"),f=this.getWeekNumber(u);this.currentWeekText="第".concat(f,"周 (").concat(l," - ").concat(g,")")}},initExpandedMonths:function(){var e=new Date,t="".concat(e.getFullYear(),"-").concat(String(e.getMonth()+1).padStart(2,"0")),n="".concat(e.getFullYear(),"-").concat(String(e.getMonth()).padStart(2,"0"));this.expandedMonths=[t,n]},getWeekNumber:function(e){var t=new Date(e.getFullYear(),0,1);return Math.ceil(((e.getTime()-t.getTime())/864e5+t.getDay()+1)/7)},toBeijingTime:function(e){if(!e)return null;var t;if("string"===typeof e)t=new Date(e);else{if(!(e instanceof Date))return null;t=e}if(isNaN(t.getTime()))return null;var n=new Date(t.getTime()+288e5);return{getFullYear:function(){return n.getUTCFullYear()},getMonth:function(){return n.getUTCMonth()},getDate:function(){return n.getUTCDate()},getDay:function(){return n.getUTCDay()},getHours:function(){return n.getUTCHours()},getMinutes:function(){return n.getUTCMinutes()},getSeconds:function(){return n.getUTCSeconds()},getTime:function(){return n.getTime()},toISOString:function(){return n.toISOString()}}},getWeekStart:function(e){var t;if(e&&"function"===typeof e.getFullYear)t=e;else if(t=this.toBeijingTime(e),!t)return null;var n=t.getFullYear(),a=t.getMonth(),i=t.getDate(),r=t.getDay(),o=new Date(n,a,i),c=0===r?-6:1-r;return o.setDate(i+c),o.setHours(0,0,0,0),o},getWeekEnd:function(e){var t;if(e&&"function"===typeof e.getFullYear)t=e;else if(t=this.toBeijingTime(e),!t)return null;var n=t.getFullYear(),a=t.getMonth(),i=t.getDate(),r=t.getDay(),o=new Date(n,a,i),c=0===r?0:7-r;return o.setDate(i+c),o.setHours(23,59,59,999),o},loadPageData:function(){var t=this;return(0,c.default)(i.default.mark((function n(){return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t.loading=!0,t.loadError=!1,n.prev=2,n.next=5,Promise.all([t.loadAreaInfo(),t.checkCurrentWeekRecord()]);case 5:return t.areaInfo.id&&(t.areaInfo.status=t.calculateAreaStatus(t.areaInfo),t.areaInfo.canClean=!t.currentWeekRecord),n.prev=6,n.next=9,t.loadHistoryDataAsync();case 9:return n.next=11,t.checkMissedInspections();case 11:return n.next=13,t.checkCurrentWeekInspectionStatus();case 13:n.next=18;break;case 15:n.prev=15,n.t0=n["catch"](6),console.warn("历史数据加载失败:",n.t0);case 18:n.next=24;break;case 20:n.prev=20,n.t1=n["catch"](2),t.loadError=!0,e.showToast({title:"加载失败",icon:"none"});case 24:return n.prev=24,t.loading=!1,n.finish(24);case 27:case"end":return n.stop()}}),n,null,[[2,20,24,27],[6,15]])})))()},loadHistoryDataAsync:function(){var e=this;return(0,c.default)(i.default.mark((function t(){return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.initTimeCache(),t.next=4,Promise.all([e.loadCleaningHistory(),e.loadInspectionHistoryOptimized()]);case 4:t.next=8;break;case 6:t.prev=6,t.t0=t["catch"](0);case 8:case"end":return t.stop()}}),t,null,[[0,6]])})))()},initTimeCache:function(){var e=this;if(!this.timeCache){var t=new Date;this.timeCache={now:t,currentYear:t.getFullYear(),currentMonth:t.getMonth()+1,cutoffDates:{}},["近1个月","近3个月","近6个月","近1年"].forEach((function(n){var a;switch(n){case"近1个月":a=new Date(t.getFullYear(),t.getMonth()-1,t.getDate());break;case"近3个月":a=new Date(t.getFullYear(),t.getMonth()-3,t.getDate());break;case"近6个月":a=new Date(t.getFullYear(),t.getMonth()-6,t.getDate());break;case"近1年":a=new Date(t.getFullYear()-1,t.getMonth(),t.getDate());break}e.timeCache.cutoffDates[n]=a}))}},loadAreaInfo:function(){var e=this;return(0,c.default)(i.default.mark((function t(){var n,a,r,o,c,s,d,l,g,f,p,h;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,u.callCloudFunction)("hygiene-area-management",{action:"getAreaDetail",data:{id:e.areaId}});case 3:if(n=t.sent,!(n&&n.success&&n.data)){t.next=17;break}a=n.data,r="未分配",a.assigned_user_details&&a.assigned_user_details.length>0?(o=a.assigned_user_details[0],r=o.nickname||o.username||o._id||"未知用户"):a.assigned_users&&a.assigned_users.length>0&&(r="用户".concat(a.assigned_users[0])),"未分配"===r&&(r="待确定"),c="未设置",s="待定","fixed"===a.type?(c="weekly"===a.cleaning_frequency?"每周一次":"按需清理",s="本周内完成"):"public"===a.type&&a.scheduled_day&&(d=["周日","周一","周二","周三","周四","周五","周六"],c="每".concat(d[a.scheduled_day],"清理"),s="下个".concat(d[a.scheduled_day])),l="",a.location&&(g=a.location,f=g.building,p=g.floor,h=g.area,l=[f,p,h].filter(Boolean).join(" ")),e.areaInfo={id:a._id,name:a.name,type:a.type,location:l||"未设置",description:a.description||"",assignedEmployee:r,cleaningFrequency:c,nextCleaningTime:s,status:"pending",canClean:!0,scheduledDay:a.scheduled_day,cleaning_requirements:a.cleaning_requirements||[]},t.next=18;break;case 17:throw new Error("获取责任区信息失败");case 18:t.next=23;break;case 20:throw t.prev=20,t.t0=t["catch"](0),t.t0;case 23:case"end":return t.stop()}}),t,null,[[0,20]])})))()},calculateAreaStatus:function(e){if(this.currentWeekRecord)return"completed";var t=new Date,n=this.toBeijingTime(t);if("fixed"===e.type){var a=this.getWeekEnd(n);return t>a?"overdue":"pending"}if("public"===e.type&&void 0!==e.scheduled_day){var i=e.scheduled_day,r=this.getWeekStart(n),o=new Date(r);return 0===i?o.setDate(r.getDate()+6):o.setDate(r.getDate()+i-1),o.setHours(23,59,59,999),t>o?"overdue":"pending"}return"pending"},loadCleaningHistory:function(){var e=this;return(0,c.default)(i.default.mark((function t(){var n,a,r,o,c,s,d,l;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,o=(null===(n=e.timeCache)||void 0===n?void 0:n.cutoffDates[e.selectedTimeFilter])||(null===(a=e.timeCache)||void 0===a?void 0:a.cutoffDates["近3个月"])||new Date(Date.now()-7776e6),c=(null===(r=e.timeCache)||void 0===r?void 0:r.now)||new Date,t.next=5,(0,u.callCloudFunction)("hygiene-cleaning",{action:"getCleaningRecords",data:{area_id:e.areaId,start_date:o.toISOString(),end_date:c.toISOString(),pageSize:100}});case 5:s=t.sent,s&&s.success&&s.data?(d=s.data.list||[],"待确定"===e.areaInfo.assignedEmployee&&d.length>0&&(l=d.sort((function(e,t){return new Date(t.cleaning_date)-new Date(e.cleaning_date)}))[0],l.user_name?e.areaInfo.assignedEmployee=l.user_name:l.cleaner_name&&(e.areaInfo.assignedEmployee=l.cleaner_name)),e.cleaningHistory=d.map((function(e){var t=new Date(e.cleaning_date),n="".concat(t.getMonth()+1,"月").concat(t.getDate(),"日"),a="".concat(t.getHours().toString().padStart(2,"0"),":").concat(t.getMinutes().toString().padStart(2,"0"));return{id:e._id,weekText:n,subtitle:"清理任务 · ".concat(a,"完成"),status:"completed",icon:"checkmarkempty",photoCount:e.photos?e.photos.length:0,submitDate:e.cleaning_date.split("T")[0],remark:e.remark||""}})).sort((function(e,t){return t.submitDate.localeCompare(e.submitDate)}))):e.cleaningHistory=[],t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](0),e.cleaningHistory=[];case 12:case"end":return t.stop()}}),t,null,[[0,9]])})))()},loadInspectionHistoryOptimized:function(){var e=this;return(0,c.default)(i.default.mark((function t(){var n,a,r,c,s,d,g,f;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Promise.all([(0,u.callCloudFunction)("hygiene-inspection",{action:"getInspectionRecords",data:{area_id:e.areaId,pageSize:20}}),(0,u.callCloudFunction)("hygiene-rectification",{action:"getRectifications",data:{area_id:e.areaId,pageSize:50}})]);case 3:n=t.sent,a=(0,o.default)(n,2),r=a[0],c=a[1],r&&r.success&&r.data?(d=r.data.list||[],g=(null===c||void 0===c||null===(s=c.data)||void 0===s?void 0:s.list)||[],f=new Map,g.forEach((function(e){var t=e.inspection_record_id||"".concat(e.area_id,"_").concat(e.created_at);f.has(t)||f.set(t,[]),f.get(t).push(e)})),e.inspectionHistory=d.map((function(t){var n=f.get(t._id)||[];if(0===n.length){var a=new Date(t.inspection_date);n=g.filter((function(e){return e.area_id===t.area_id&&new Date(e.created_at)>=a&&new Date(e.created_at)<=new Date(a.getTime()+6048e5)}))}var i=t.overall_rating||t.score||0;if(t.has_issues&&n.length>0){var r=n.find((function(e){return"verified"===e.status}));r&&(i=r.final_rating||r.review_rating||i)}var o=l(l({},t),{},{related_rectifications:n});return e.formatInspectionRecord(o,i)})).sort((function(e,t){return new Date(t.inspection_date)-new Date(e.inspection_date)}))):e.inspectionHistory=[],e.historyDataLoaded=!0,t.next=14;break;case 11:t.prev=11,t.t0=t["catch"](0),e.inspectionHistory=[];case 14:case"end":return t.stop()}}),t,null,[[0,11]])})))()},formatInspectionRecord:function(e,t){var n=new Date(e.inspection_date),a="".concat(n.getMonth()+1,"月").concat(n.getDate(),"日 ").concat(n.getHours().toString().padStart(2,"0"),":").concat(n.getMinutes().toString().padStart(2,"0")),i="",r="passed",o="checkmarkempty",c=null;if(e.has_issues){if("verified"===e.status?(r="completed",o="checkmarkempty",i="发现问题已整改完成 · 整改质量".concat(this.getRatingDescription(t).toLowerCase())):"rectification_completed"===e.status?(r="pending",o="reload",i="整改已提交 · 等待检查员复查确认"):"pending_rectification"===e.status?(r="issues",o="info",i="发现问题 · ".concat(e.issues&&e.issues.length>0?e.issues[0].description:"需要整改")):(r="issues",o="info",i="发现问题 · 需要整改"),e.related_rectifications&&e.related_rectifications.length>0){var s=e.related_rectifications[0];if(c=s.status,"verified"===c)r="completed",o="checkmarkempty",i="发现问题已整改完成 · 整改质量".concat(this.getRatingDescription(t).toLowerCase());else if("pending_review"===c){var u=new Date(s.submitted_at||s.updated_at),d=new Date,l=!1;if("public"===this.areaInfo.type&&void 0!==this.areaInfo.scheduledDay){var g=this.toBeijingTime(u),f=this.getWeekStart(g),p=new Date(f);0===this.areaInfo.scheduledDay?p.setDate(f.getDate()+6):p.setDate(f.getDate()+this.areaInfo.scheduledDay-1),p.setHours(23,59,59,999),l=d>p}else{var h=this.toBeijingTime(u),m=this.getWeekEnd(h);l=d>m}l?(r="overdue",o="info",i="检查员漏检查 · 整改已提交但未及时复查"):(r="issues",o="eye",i="发现问题 · 整改已提交，待检查员复查")}else r="issues",o="info",i="发现问题 · ".concat(e.issues&&e.issues.length>0?e.issues[0].description:"整改处理中")}}else r="passed",o="checkmarkempty",i="检查通过 · 清理效果".concat(this.getRatingDescription(t).toLowerCase()," · 无问题发现");return{id:e._id,inspectorName:"检查员：".concat(e.inspector_name||"未知"),time:a,subtitle:i,result:r,icon:o,rating:t,inspection_date:e.inspection_date,status:e.status||"unknown",statusIndicator:this.getStatusIndicator(r,c||e.status)}},loadInspectionHistory:function(){var e=this;return(0,c.default)(i.default.mark((function t(){var n,a,r;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,u.callCloudFunction)("hygiene-inspection",{action:"getInspectionRecords",data:{area_id:e.areaId,pageSize:20}});case 3:if(n=t.sent,!(n&&n.success&&n.data)){t.next=12;break}return a=n.data.list||[],t.next=8,Promise.all(a.map(function(){var e=(0,c.default)(i.default.mark((function e(t){var n,a;return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.has_issues||"verified"!==t.status){e.next=10;break}return e.prev=1,e.next=4,(0,u.callCloudFunction)("hygiene-rectification",{action:"getRectifications",data:{inspection_record_id:t._id,status:"verified",pageSize:1}});case 4:n=e.sent,n&&n.success&&n.data&&n.data.list&&n.data.list.length>0&&(a=n.data.list[0],t.rectification_rating=a.final_rating||a.review_rating||0),e.next=10;break;case 8:e.prev=8,e.t0=e["catch"](1);case 10:return e.abrupt("return",t);case 11:case"end":return e.stop()}}),e,null,[[1,8]])})));return function(t){return e.apply(this,arguments)}}()));case 8:r=t.sent,e.inspectionHistory=r.map((function(t){var n=new Date(t.inspection_date),a="".concat(n.getMonth()+1,"月").concat(n.getDate(),"日 ").concat(n.getHours().toString().padStart(2,"0"),":").concat(n.getMinutes().toString().padStart(2,"0")),i="",r="passed",o="checkmarkempty";return t.has_issues?"verified"===t.status?(r="completed",o="checkmarkempty",i="发现问题已整改完成 · 整改质量".concat(e.getRatingDescription(t.rectification_rating||t.overall_rating||t.score||0).toLowerCase())):"rectification_completed"===t.status?(r="pending",o="reload",i="整改已提交 · 等待确认"):"pending_rectification"===t.status?(r="issues",o="info",i="发现问题 · ".concat(t.issues&&t.issues.length>0?t.issues[0].description:"需要整改")):(r="issues",o="info",i="发现问题 · 需要整改"):(r="passed",o="checkmarkempty",i="检查通过 · 清理效果".concat(e.getRatingDescription(t.rectification_rating||t.overall_rating||t.score||0).toLowerCase()," · 无问题发现")),{id:t._id,inspectorName:"检查员：".concat(t.inspector_name||"未知"),time:a,subtitle:i,result:r,icon:o,rating:t.rectification_rating||t.overall_rating||t.score||0,inspection_date:t.inspection_date,status:t.status||"unknown"}})).sort((function(e,t){return new Date(t.inspection_date)-new Date(e.inspection_date)})),t.next=13;break;case 12:e.inspectionHistory=[];case 13:t.next=18;break;case 15:t.prev=15,t.t0=t["catch"](0),e.inspectionHistory=[];case 18:case"end":return t.stop()}}),t,null,[[0,15]])})))()},checkCurrentWeekRecord:function(){var e=this;return(0,c.default)(i.default.mark((function t(){var n,a,r,o,c,s,d,l,g,f;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.isHistoricalView&&e.cleaningDate?(r=new Date(e.cleaningDate),o=e.toBeijingTime(r),n=e.getWeekStart(o),a=e.getWeekEnd(o)):(c=new Date,s=e.toBeijingTime(c),n=e.getWeekStart(s),a=e.getWeekEnd(s)),t.next=4,(0,u.callCloudFunction)("hygiene-cleaning",{action:"getCleaningRecords",data:{area_id:e.areaId,start_date:n.toISOString(),end_date:a.toISOString(),pageSize:1}});case 4:d=t.sent,d&&d.success&&d.data&&d.data.list&&d.data.list.length>0?(l=d.data.list[0],g=new Date(l.cleaning_date),f="".concat(g.getMonth()+1,"月").concat(g.getDate(),"日 ").concat(g.getHours().toString().padStart(2,"0"),":").concat(g.getMinutes().toString().padStart(2,"0")," 提交"),e.currentWeekRecord={id:l._id,submitTime:f,photos:l.photos||[]}):e.currentWeekRecord=null,e.areaInfo&&(e.areaInfo.canClean=!e.currentWeekRecord),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](0),e.currentWeekRecord=null;case 12:case"end":return t.stop()}}),t,null,[[0,9]])})))()},getAreaTypeText:function(e){return{fixed:"固定责任区",public:"公共责任区"}[e]||"责任区"},getStatusText:function(e){return{completed:"已完成",pending:"待清理",overdue:"已逾期"}[e]||"未知"},getWeekDeadlineText:function(){var e=new Date,t=this.toBeijingTime(e),n=this.getWeekEnd(t),a=n.getTime()-e.getTime(),i=Math.ceil(a/864e5);return i<=0?"已超过截止时间":1===i?"距离本周截止还有1天":"距离本周截止还有".concat(i,"天")},getIconColor:function(e){return{completed:"#34C759",pending:"#FF9500",overdue:"#FF3B30",passed:"#34C759",issues:"#FF3B30"}[e]||"#8E8E93"},goToCleaning:function(){e.navigateTo({url:"/pages/6s_pkg/cleaning-upload?areaId=".concat(this.areaId,"&type=").concat(this.areaType)})},viewWeekPhotos:function(){if(this.currentWeekRecord&&this.currentWeekRecord.photos.length>0){var t=this.currentWeekRecord.photos.map((function(e){return e.url}));e.previewImage({urls:t,current:t[0]})}},viewRecordDetail:function(t){e.navigateTo({url:"/pages/6s_pkg/record-detail?id=".concat(t.id,"&type=cleaning")})},viewInspectionDetail:function(t){"overdue"===t.result&&t.id.startsWith("missed_")?e.showToast({title:"检查员未检查此记录",icon:"none",duration:2e3}):"pending"!==t.result||"current_week_status"!==t.id?e.navigateTo({url:"/pages/6s_pkg/record-detail?id=".concat(t.id,"&type=inspection")}):e.showToast({title:"检查员还未检查此记录",icon:"none",duration:2e3})},showTimeFilter:function(){var t=this,n=["近1个月","近3个月","近6个月","近1年"];e.showActionSheet({itemList:n,success:function(e){t.selectedTimeFilter=n[e.tapIndex],t.loadCleaningHistory()}})},toggleMonth:function(e){var t=this.expandedMonths.indexOf(e);t>-1?this.expandedMonths.splice(t,1):this.expandedMonths.push(e)},getRatingDescription:function(e){return 0===e?"请评分":e<=1?"较差":e<=2?"一般":e<=3?"良好":e<5?"优秀":5===e?"完美":"良好"},getStatusIndicator:function(e,t){var n={passed:{text:"✓",color:"#34C759",desc:"检查通过"},completed:{text:"✓",color:"#34C759",desc:"整改完成"},pending:{text:"○",color:"#FF9500",desc:"待检查"},issues:{text:"!",color:"#FF3B30",desc:"发现问题"},overdue:{text:"⚠",color:"#FF3B30",desc:"漏检查"}};return n[e]||n["issues"]},checkCurrentWeekInspectionStatus:function(){var e=this;return(0,c.default)(i.default.mark((function t(){var n,a,r,o,c;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,e.currentWeekRecord){t.next=3;break}return t.abrupt("return");case 3:return t.next=5,new Promise((function(t){var n=setInterval((function(){e.historyDataLoaded&&(clearInterval(n),t())}),100);setTimeout((function(){clearInterval(n),t()}),5e3)}));case 5:if(n=e.cleaningHistory.find((function(t){return t.id===e.currentWeekRecord.id})),n){t.next=8;break}return t.abrupt("return");case 8:if(a=new Date(n.submitDate),r=e.inspectionHistory.some((function(e){return!e.id.startsWith("missed_")&&!e.id.startsWith("current_week_status")&&new Date(e.inspection_date)>=a||"current_week_status"===e.id})),!r){t.next=12;break}return t.abrupt("return");case 12:o=e.isCurrentWeekOverdue(a),c={id:"current_week_status",inspectorName:o?"检查员漏检查":"待检查",time:"".concat(a.getMonth()+1,"月").concat(a.getDate(),"日清理"),subtitle:o?"清理记录已提交，但检查员未及时检查":"清理记录已提交，等待检查员检查",result:o?"overdue":"pending",icon:"info",rating:0,inspection_date:n.submitDate,status:o?"missed":"pending",statusIndicator:o?{text:"⚠",color:"#FF3B30",desc:"漏检查"}:{text:"○",color:"#FF9500",desc:"待检查"}},e.inspectionHistory.unshift(c),e.inspectionHistory.sort((function(e,t){return new Date(t.inspection_date)-new Date(e.inspection_date)})),t.next=21;break;case 18:t.prev=18,t.t0=t["catch"](0),console.warn("检查本周清理状态失败:",t.t0);case 21:case"end":return t.stop()}}),t,null,[[0,18]])})))()},checkMissedInspections:function(){var e=this;return(0,c.default)(i.default.mark((function t(){var n,a,o,c,s;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,new Promise((function(t){var n=setInterval((function(){e.historyDataLoaded&&(clearInterval(n),t())}),100);setTimeout((function(){clearInterval(n),t()}),5e3)}));case 3:n=new Date,a=new Date(n.getTime()-24192e5),o=e.cleaningHistory.filter((function(e){var t=new Date(e.submitDate);return t>a})),c=e.inspectionHistory.filter((function(e){return!e.id.startsWith("missed_")})),s=[],o.forEach((function(t){var n=new Date(t.submitDate),a=c.some((function(e){var t=new Date(e.inspection_date),a=t.getTime()-n.getTime();return a>=0&&a<=6048e5}));!a&&e.isHistoricalRecordOverdue(n)&&s.push({id:"missed_"+t.id,inspectorName:"检查员漏检查",time:"".concat(n.getMonth()+1,"月").concat(n.getDate(),"日清理"),subtitle:"清理记录已提交，但检查员未及时检查",result:"overdue",icon:"info",rating:0,inspection_date:t.submitDate,status:"missed",statusIndicator:{text:"⚠",color:"#FF3B30",desc:"漏检查"}})})),s.length>0&&(e.inspectionHistory=[].concat(s,(0,r.default)(e.inspectionHistory)).sort((function(e,t){return new Date(t.inspection_date)-new Date(e.inspection_date)}))),t.next=15;break;case 12:t.prev=12,t.t0=t["catch"](0),console.warn("检查遗漏检查记录失败:",t.t0);case 15:case"end":return t.stop()}}),t,null,[[0,12]])})))()},isHistoricalRecordOverdue:function(e){var t=new Date;if("public"===this.areaInfo.type&&void 0!==this.areaInfo.scheduledDay){var n=this.toBeijingTime(e),a=this.getWeekStart(n),i=new Date(a);return 0===this.areaInfo.scheduledDay?i.setDate(a.getDate()+6):i.setDate(a.getDate()+this.areaInfo.scheduledDay-1),i.setHours(23,59,59,999),t>i}var r=this.toBeijingTime(e),o=this.getWeekEnd(r);return t>o},isCurrentWeekOverdue:function(e){var t=new Date;if("public"===this.areaInfo.type&&void 0!==this.areaInfo.scheduledDay){var n=this.toBeijingTime(e),a=this.getWeekStart(n),i=new Date(a);return 0===this.areaInfo.scheduledDay?i.setDate(a.getDate()+6):i.setDate(a.getDate()+this.areaInfo.scheduledDay-1),i.setHours(23,59,59,999),t>i}var r=this.toBeijingTime(e),o=this.getWeekEnd(r);return t>o},handleRecordUpdated:function(e){var t=this;e.areaId===this.areaId&&(this.checkCurrentWeekRecord(),("cleaning"===e.mode||e.recordId)&&this.loadCleaningHistory(),void 0!==e.isPassed&&this.loadInspectionHistory(),this.checkMissedInspections().then((function(){t.checkCurrentWeekInspectionStatus()})))}}};t.default=g}).call(this,n("df3c")["default"])},b914:function(e,t,n){"use strict";var a=n("2c13"),i=n.n(a);i.a},bec1:function(e,t,n){"use strict";n.r(t);var a=n("880a"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},ce6f:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a}));var a={pEmptyState:function(){return n.e("components/p-empty-state/p-empty-state").then(n.bind(null,"9b76"))},uniIcons:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(n.bind(null,"6ddf"))}},i=function(){var e=this,t=e.$createElement,n=(e._self._c,e.loading||e.loadError?null:e.getAreaTypeText(e.areaInfo.type)),a=e.loading||e.loadError?null:e.getStatusText(e.areaInfo.status),i=e.loading||e.loadError||!e.currentWeekRecord?null:e.currentWeekRecord.photos.length,r=e.loading||e.loadError||e.currentWeekRecord||e.isHistoricalView?null:e.getWeekDeadlineText(),o=e.loading||e.loadError?null:e.cleaningHistory.length,c=e.loading||e.loadError||0===o?null:e.__map(e.groupedHistory,(function(t,n){var a=e.__get_orig(t),i=t.records.length,r=e.expandedMonths.includes(t.month),o=e.expandedMonths.includes(t.month),c=o?e.__map(t.records,(function(t,n){var a=e.__get_orig(t),i=e.getIconColor(t.status);return{$orig:a,m3:i}})):null;return{$orig:a,g2:i,g3:r,g4:o,l0:c}})),s=e.loading||e.loadError?null:e.inspectionHistory.length,u=e.loading||e.loadError||0===s?null:e.__map(e.inspectionHistory,(function(t,n){var a=e.__get_orig(t),i=e.getIconColor(t.result);return{$orig:a,m4:i}}));e.$mp.data=Object.assign({},{$root:{m0:n,m1:a,g0:i,m2:r,g1:o,l1:c,g5:s,l2:u}})},r=[]}},[["82c7","common/runtime","common/vendor"]]]);