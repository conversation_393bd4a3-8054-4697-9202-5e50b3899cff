<view class="page-container data-v-6d671070"><block wx:if="{{loading}}"><view class="loading-container data-v-6d671070"><view class="loading-content data-v-6d671070"><view class="loading-spinner data-v-6d671070"></view><text class="loading-text data-v-6d671070">加载整改任务详情中...</text></view></view></block><block wx:else><block wx:if="{{loadError}}"><view class="error-container data-v-6d671070"><view class="error-content data-v-6d671070"><uni-icons vue-id="caac15a0-1" type="info" size="48" color="#FF3B30" class="data-v-6d671070" bind:__l="__l"></uni-icons><text class="error-text data-v-6d671070">{{loadError}}</text><button data-event-opts="{{[['tap',[['retryLoad',['$event']]]]]}}" class="retry-button data-v-6d671070" bindtap="__e">重新加载</button></view></view></block><block wx:else><view class="card data-v-6d671070"><view class="card-header data-v-6d671070"><view class="header-content data-v-6d671070"><view class="card-title data-v-6d671070">整改任务详情</view><view class="card-subtitle data-v-6d671070">{{taskInfo.areaName+" - "+(taskInfo.areaType==='public'?'公共责任区':'固定责任区')}}</view></view><view class="{{['status-badge','data-v-6d671070','status-'+taskInfo.status]}}">{{''+$root.m0+''}}</view></view><view class="card-body data-v-6d671070"><view class="info-grid data-v-6d671070"><view class="info-item data-v-6d671070"><view class="info-icon data-v-6d671070"><uni-icons vue-id="caac15a0-2" type="calendar" size="18" color="#007AFF" class="data-v-6d671070" bind:__l="__l"></uni-icons></view><view class="info-content data-v-6d671070"><view class="info-label data-v-6d671070">发现时间</view><view class="info-value data-v-6d671070">{{$root.m1}}</view></view></view><view class="info-item data-v-6d671070"><view class="info-icon data-v-6d671070"><uni-icons vue-id="caac15a0-3" type="person" size="18" color="#FF9500" class="data-v-6d671070" bind:__l="__l"></uni-icons></view><view class="info-content data-v-6d671070"><view class="info-label data-v-6d671070">负责人</view><view class="info-value data-v-6d671070">{{taskInfo.assignee}}</view></view></view><view class="info-item data-v-6d671070"><view class="info-icon data-v-6d671070"><uni-icons vue-id="caac15a0-4" type="contact" size="18" color="#5856D6" class="data-v-6d671070" bind:__l="__l"></uni-icons></view><view class="info-content data-v-6d671070"><view class="info-label data-v-6d671070">检查员</view><view class="info-value data-v-6d671070">{{taskInfo.inspector}}</view></view></view></view></view></view><view class="card data-v-6d671070"><view class="card-header data-v-6d671070"><view class="card-title data-v-6d671070">发现问题</view><view class="card-subtitle data-v-6d671070">检查时发现的问题描述</view></view><view class="card-body data-v-6d671070"><view class="problem-content data-v-6d671070">{{taskInfo.problemDescription}}</view></view></view><block wx:if="{{hasIssuePhotos}}"><view class="card data-v-6d671070"><view class="card-header data-v-6d671070"><view class="card-title data-v-6d671070">问题照片</view><view class="card-subtitle data-v-6d671070">{{"检查时拍摄的问题照片 ("+$root.g0+"张)"}}</view></view><view class="card-body data-v-6d671070"><view class="photo-grid data-v-6d671070"><block wx:for="{{$root.l0}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['previewPhoto',['$0',index],['taskInfo.issuePhotos']]]]]}}" class="photo-item data-v-6d671070" bindtap="__e"><image src="{{photo.m2}}" mode="aspectFill" data-event-opts="{{[['error',[['onPhotoError',[index,'issue']]]],['load',[['onPhotoLoad',[index,'issue']]]]]}}" binderror="__e" bindload="__e" class="data-v-6d671070"></image><block wx:if="{{photoErrors.issue&&photoErrors.issue[index]}}"><view class="photo-error data-v-6d671070"><uni-icons vue-id="{{'caac15a0-5-'+index}}" type="image" size="32" color="#8E8E93" class="data-v-6d671070" bind:__l="__l"></uni-icons><text class="data-v-6d671070">加载失败</text></view></block></view></block></view></view></view></block><block wx:if="{{taskInfo.rectificationRequirement}}"><view class="card data-v-6d671070"><view class="card-header data-v-6d671070"><view class="card-title data-v-6d671070">整改要求</view><view class="card-subtitle data-v-6d671070">需要完成的整改内容</view></view><view class="card-body data-v-6d671070"><view class="requirement-content data-v-6d671070">{{taskInfo.rectificationRequirement}}</view></view></view></block><block wx:if="{{hasRectificationResult}}"><view class="card data-v-6d671070"><view class="card-header data-v-6d671070"><view class="card-title data-v-6d671070">整改结果</view><view class="card-subtitle data-v-6d671070">员工提交的整改说明</view></view><view class="card-body data-v-6d671070"><block wx:if="{{taskInfo.rectificationDescription}}"><view class="rectification-content data-v-6d671070">{{''+taskInfo.rectificationDescription+''}}</view></block><block wx:else><view class="no-description data-v-6d671070"><text class="data-v-6d671070">暂无整改说明</text></view></block></view></view></block><block wx:if="{{hasRectificationPhotos}}"><view class="card data-v-6d671070"><view class="card-header data-v-6d671070"><view class="card-title data-v-6d671070">整改照片</view><view class="card-subtitle data-v-6d671070">员工提交的整改后照片</view></view><view class="card-body data-v-6d671070"><view class="photo-grid data-v-6d671070"><block wx:for="{{$root.l1}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['previewPhoto',['$0',index],['taskInfo.rectificationPhotos']]]]]}}" class="photo-item data-v-6d671070" bindtap="__e"><image src="{{photo.m3}}" mode="aspectFill" data-event-opts="{{[['error',[['onPhotoError',[index,'rectification']]]],['load',[['onPhotoLoad',[index,'rectification']]]]]}}" binderror="__e" bindload="__e" class="data-v-6d671070"></image><block wx:if="{{photoErrors.rectification&&photoErrors.rectification[index]}}"><view class="photo-error data-v-6d671070"><uni-icons vue-id="{{'caac15a0-6-'+index}}" type="image" size="32" color="#8E8E93" class="data-v-6d671070" bind:__l="__l"></uni-icons><text class="data-v-6d671070">加载失败</text></view></block></view></block></view></view></view></block><block wx:if="{{$root.g1}}"><view class="card data-v-6d671070"><view class="card-header data-v-6d671070"><view class="card-title data-v-6d671070">审核记录</view><view class="card-subtitle data-v-6d671070">历史审核记录</view></view><view class="card-body data-v-6d671070"><view class="review-timeline data-v-6d671070"><block wx:for="{{$root.l2}}" wx:for-item="review" wx:for-index="index" wx:key="index"><view class="review-item data-v-6d671070"><view class="{{['review-dot','data-v-6d671070','review-'+review.$orig.result]}}"></view><view class="review-content data-v-6d671070"><view class="review-header data-v-6d671070"><text class="review-result data-v-6d671070">{{review.$orig.result==='approved'?'复查通过':'需重新整改'}}</text><text class="review-date data-v-6d671070">{{review.m4}}</text></view><block wx:if="{{review.$orig.comments}}"><view class="review-comments data-v-6d671070">{{review.$orig.comments}}</view></block></view></view></block></view></view></view></block><block wx:if="{{taskInfo.reviewComments}}"><view class="card data-v-6d671070"><view class="card-header data-v-6d671070"><view class="card-title data-v-6d671070">审核情况</view><view class="card-subtitle data-v-6d671070">检查员复查的现场情况</view></view><view class="card-body data-v-6d671070"><view class="problem-content data-v-6d671070">{{''+taskInfo.reviewComments+''}}</view></view></view></block><block wx:if="{{hasReviewPhotos}}"><view class="card data-v-6d671070"><view class="card-header data-v-6d671070"><view class="card-title data-v-6d671070">审核照片</view><view class="card-subtitle data-v-6d671070">审核员拍摄的现场照片</view></view><view class="card-body data-v-6d671070"><view class="photo-grid data-v-6d671070"><block wx:for="{{$root.l3}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['previewPhoto',['$0',index],['taskInfo.reviewPhotos']]]]]}}" class="photo-item data-v-6d671070" bindtap="__e"><image src="{{photo.m5}}" mode="aspectFill" data-event-opts="{{[['error',[['onPhotoError',[index,'review']]]],['load',[['onPhotoLoad',[index,'review']]]]]}}" binderror="__e" bindload="__e" class="data-v-6d671070"></image><block wx:if="{{photoErrors.review&&photoErrors.review[index]}}"><view class="photo-error data-v-6d671070"><uni-icons vue-id="{{'caac15a0-7-'+index}}" type="image" size="32" color="#8E8E93" class="data-v-6d671070" bind:__l="__l"></uni-icons><text class="data-v-6d671070">加载失败</text></view></block></view></block></view></view></view></block></block></block><block wx:if="{{!loading&&!loadError}}"><view class="button-container data-v-6d671070"><block wx:if="{{taskInfo.status==='pending_review'}}"><button data-event-opts="{{[['tap',[['goToReview',['$event']]]]]}}" class="primary-button data-v-6d671070" bindtap="__e">进行复查</button></block><block wx:else><button data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="primary-button data-v-6d671070" bindtap="__e">返回</button></block></view></block></view>