@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container.data-v-665ecd07 {
  min-height: 100vh;
  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
  width: 100%;
  overflow-x: hidden;
  /* 防止水平滚动 */
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
}
.page-header.data-v-665ecd07 {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  padding: 32rpx 32rpx 40rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}
.header-content.data-v-665ecd07 {
  flex: 1;
}
.header-title.data-v-665ecd07 {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}
.header-subtitle.data-v-665ecd07 {
  font-size: 28rpx;
  opacity: 0.9;
}
.status-badge-enhanced.data-v-665ecd07 {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 400;
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  white-space: nowrap;
}
.status-badge-enhanced.status-pending_review.data-v-665ecd07 {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
.status-badge-enhanced.status-approved.data-v-665ecd07 {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
.status-badge-enhanced.status-rejected.data-v-665ecd07 {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
.status-badge-enhanced.status-completed.data-v-665ecd07 {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
.page-loading.data-v-665ecd07 {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
  padding: 60rpx 32rpx;
}
.loading-content.data-v-665ecd07 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}
.loading-spinner.data-v-665ecd07 {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #E5E7EB;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  -webkit-animation: spin-data-v-665ecd07 1s linear infinite;
          animation: spin-data-v-665ecd07 1s linear infinite;
}
.loading-spinner-small.data-v-665ecd07 {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid #E5E7EB;
  border-top: 3rpx solid #007AFF;
  border-radius: 50%;
  -webkit-animation: spin-data-v-665ecd07 1s linear infinite;
          animation: spin-data-v-665ecd07 1s linear infinite;
}
.loading-text.data-v-665ecd07 {
  font-size: 28rpx;
  color: #8E8E93;
  margin-top: 8rpx;
}
.page-error.data-v-665ecd07 {
  padding: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300rpx;
  /* Adjust as needed */
}
.card.data-v-665ecd07 {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 24rpx 32rpx 0 32rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.card-header.data-v-665ecd07 {
  padding: 32rpx 32rpx 16rpx 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.card-title.data-v-665ecd07 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
}
.card-subtitle.data-v-665ecd07 {
  font-size: 24rpx;
  color: #8E8E93;
}
.card-body.data-v-665ecd07 {
  padding: 32rpx;
}
.issue-info .issue-description.data-v-665ecd07 {
  font-size: 28rpx;
  color: #1D1D1F;
  line-height: 1.6;
  margin-bottom: 16rpx;
}
.issue-info .issue-meta .meta-label.data-v-665ecd07 {
  font-size: 24rpx;
  color: #8E8E93;
}
.issue-info .issue-meta .meta-value.data-v-665ecd07 {
  font-size: 24rpx;
  color: #007AFF;
  margin-left: 8rpx;
}
/* 问题说明中的检查员信息样式 */
.description-section .issue-meta.data-v-665ecd07 {
  margin-top: 16rpx;
  padding-top: 12rpx;
  border-top: 1rpx solid #F2F2F7;
}
.description-section .issue-meta .meta-label.data-v-665ecd07 {
  font-size: 24rpx;
  color: #8E8E93;
}
.description-section .issue-meta .meta-value.data-v-665ecd07 {
  font-size: 24rpx;
  color: #007AFF;
  margin-left: 8rpx;
}
.section-title.data-v-665ecd07 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1D1D1F;
  margin-bottom: 16rpx;
}
.section-title .required-mark.data-v-665ecd07 {
  color: #FF3B30;
  margin-left: 4rpx;
}
.photo-section.data-v-665ecd07 {
  margin-bottom: 32rpx;
}
.photo-grid.data-v-665ecd07 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.photo-item.data-v-665ecd07 {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
}
.photo-item image.data-v-665ecd07 {
  width: 100%;
  height: 100%;
}
.photo-add.data-v-665ecd07 {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #C7C7CC;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #F9F9F9;
}
.photo-add text.data-v-665ecd07 {
  font-size: 24rpx;
  color: #8E8E93;
  margin-top: 8rpx;
}
.photo-delete.data-v-665ecd07 {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  width: 36rpx;
  height: 36rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}
.photo-uploading.data-v-665ecd07 {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.upload-spinner.data-v-665ecd07 {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-top: 2rpx solid white;
  border-radius: 50%;
  -webkit-animation: spin-data-v-665ecd07 1s linear infinite;
          animation: spin-data-v-665ecd07 1s linear infinite;
}
.photo-uploaded.data-v-665ecd07 {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #34C759;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
@-webkit-keyframes spin-data-v-665ecd07 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin-data-v-665ecd07 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.photo-tip.data-v-665ecd07 {
  font-size: 24rpx;
  color: #8E8E93;
  margin-top: 16rpx;
}
.description-content.data-v-665ecd07 {
  font-size: 28rpx;
  color: #1D1D1F;
  line-height: 1.6;
  background: #F9F9F9;
  padding: 24rpx;
  border-radius: 12rpx;
}
.review-options.data-v-665ecd07 {
  margin-bottom: 32rpx;
}
.review-option.data-v-665ecd07 {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #E5E5EA;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}
.review-option.active.data-v-665ecd07 {
  border-color: #007AFF;
  background: rgba(0, 122, 255, 0.05);
}
.review-option.data-v-665ecd07:last-child {
  margin-bottom: 0;
}
.option-icon.data-v-665ecd07 {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.option-icon.approved.data-v-665ecd07 {
  background: #34C759;
}
.option-icon.rejected.data-v-665ecd07 {
  background: #FF3B30;
}
.option-content.data-v-665ecd07 {
  flex: 1;
}
.option-title.data-v-665ecd07 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1D1D1F;
  margin-bottom: 4rpx;
}
.option-desc.data-v-665ecd07 {
  font-size: 24rpx;
  color: #8E8E93;
}
/* 增强的评分区域样式 */
.rating-section-enhanced.data-v-665ecd07 {
  margin-top: 32rpx;
  padding: 32rpx;
  background: #F8F9FA;
  border-radius: 16rpx;
  border: 2rpx solid #E8F5E8;
}
.rating-container-enhanced.data-v-665ecd07 {
  text-align: center;
}
.rating-display-large.data-v-665ecd07 {
  margin-bottom: 32rpx;
}
.rating-number-container.data-v-665ecd07 {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 6rpx;
  margin-bottom: 8rpx;
}
.rating-number.data-v-665ecd07 {
  font-size: 80rpx;
  font-weight: 700;
  color: #007AFF;
  line-height: 1;
}
.rating-unit.data-v-665ecd07 {
  font-size: 40rpx;
  color: #8E8E93;
  font-weight: 500;
}
.rating-desc.data-v-665ecd07 {
  font-size: 24rpx;
  color: #8E8E93;
  margin-top: 8rpx;
  font-weight: 500;
}
/* 星星评分样式 */
.star-rating.data-v-665ecd07 {
  display: flex;
  justify-content: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}
/* 自定义滑动条评分样式 */
.custom-slider-rating.data-v-665ecd07 {
  margin: 32rpx 0 24rpx 0;
}
.custom-slider-container.data-v-665ecd07 {
  position: relative;
  height: 40rpx;
  margin: 0 24rpx;
  display: flex;
  align-items: center;
  -webkit-user-select: none;
          user-select: none;
}
.slider-track.data-v-665ecd07 {
  position: absolute;
  width: 100%;
  height: 4rpx;
  background: #E5E5EA;
  border-radius: 2rpx;
  left: 0;
}
.slider-track-active.data-v-665ecd07 {
  height: 100%;
  background: #FFD700;
  border-radius: 2rpx;
  transition: width 0.2s ease;
}
.slider-thumb.data-v-665ecd07 {
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  background: #FFD700;
  border-radius: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  transition: left 0.2s ease;
  z-index: 2;
}
.slider-marks.data-v-665ecd07 {
  position: absolute;
  width: 100%;
  height: 100%;
  top: -12rpx;
}
.slider-mark.data-v-665ecd07 {
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background: #E5E5EA;
  border-radius: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  z-index: 1;
}
.slider-mark-active.data-v-665ecd07 {
  background: #FFD700;
  width: 16rpx;
  height: 16rpx;
}
.slider-labels-external.data-v-665ecd07 {
  position: relative;
  margin-top: 20rpx;
  margin-left: 24rpx;
  margin-right: 24rpx;
  height: 40rpx;
}
.slider-label-external.data-v-665ecd07 {
  position: absolute;
  font-size: 24rpx;
  color: #8E8E93;
  font-weight: 500;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  text-align: center;
}
.slider-label-active.data-v-665ecd07 {
  color: #FFD700;
  font-weight: 600;
}
.rating-tips.data-v-665ecd07 {
  font-size: 24rpx;
  color: #8E8E93;
}
.review-comment-section.data-v-665ecd07,
.review-photo-section.data-v-665ecd07 {
  margin-top: 32rpx;
}
.section-header.data-v-665ecd07 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.auto-upload-toggle.data-v-665ecd07 {
  display: flex;
  align-items: center;
  gap: 12rpx;
  cursor: pointer;
}
.toggle-label.data-v-665ecd07 {
  font-size: 24rpx;
  color: #8E8E93;
}
.toggle-switch.data-v-665ecd07 {
  width: 76rpx;
  height: 44rpx;
  background: #E5E5EA;
  border-radius: 22rpx;
  position: relative;
  transition: background-color 0.3s ease;
}
.toggle-switch.active.data-v-665ecd07 {
  background: #34C759;
}
.toggle-circle.data-v-665ecd07 {
  width: 36rpx;
  height: 36rpx;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}
.toggle-switch.active .toggle-circle.data-v-665ecd07 {
  -webkit-transform: translateX(32rpx);
          transform: translateX(32rpx);
}
.textarea-container.data-v-665ecd07 {
  position: relative;
}
.textarea-container-enhanced.data-v-665ecd07 {
  position: relative;
  border-radius: 16rpx;
  border: 2rpx solid #F2F2F7;
  background: #FAFAFA;
  overflow: hidden;
  transition: all 0.3s ease;
}
.textarea-container-enhanced.data-v-665ecd07:focus-within {
  border-color: #007AFF;
  background: white;
  box-shadow: 0 2rpx 12rpx rgba(0, 122, 255, 0.15);
}
.comment-textarea-enhanced.data-v-665ecd07 {
  width: 100%;
  min-height: 160rpx;
  background: transparent;
  padding: 24rpx 20rpx 16rpx 20rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  line-height: 1.5;
  border: none;
  outline: none;
  resize: none;
  box-sizing: border-box;
}
.comment-textarea-enhanced.data-v-665ecd07::-webkit-input-placeholder {
  color: #C7C7CC;
  font-size: 28rpx;
}
.comment-textarea-enhanced.data-v-665ecd07::placeholder {
  color: #C7C7CC;
  font-size: 28rpx;
}
.char-count-overlay.data-v-665ecd07 {
  position: absolute;
  bottom: 16rpx;
  right: 20rpx;
  font-size: 24rpx;
  color: #C7C7CC !important;
  font-weight: 500;
  z-index: 2;
}
.button-container.data-v-665ecd07 {
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.primary-button.data-v-665ecd07 {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  width: 100%;
}
.primary-button[disabled].data-v-665ecd07 {
  background: #C7C7CC;
  color: #8E8E93;
}
.primary-button.loading.data-v-665ecd07 {
  background: #0056D6;
  opacity: 0.9;
}
.button-loading.data-v-665ecd07 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  color: white;
}
.button-loading .loading-spinner.data-v-665ecd07 {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  -webkit-animation: spin-data-v-665ecd07 1s linear infinite;
          animation: spin-data-v-665ecd07 1s linear infinite;
}
.bottom-safe-area.data-v-665ecd07 {
  height: 40rpx;
}
.photos-loading-placeholder.data-v-665ecd07 {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 160rpx;
  /* Adjust as needed */
  background: #f0f0f0;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}
.loading-center.data-v-665ecd07 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.photos-loading-text.data-v-665ecd07 {
  font-size: 28rpx;
  color: #8E8E93;
  margin-top: 12rpx;
  text-align: center;
}
.no-photos.data-v-665ecd07 {
  padding: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 160rpx;
  /* Adjust as needed */
}
.photo-error.data-v-665ecd07 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8E8E93;
  font-size: 24rpx;
  z-index: 10;
}
.error-text.data-v-665ecd07 {
  margin-top: 8rpx;
}
