(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/common/vendor"],{"8ae5":function(e,t,r){"use strict";var n=r("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(r("7eb4")),c=n(r("7ca3")),u=n(r("ee10")),s=n(r("67ad")),o=n(r("0bdb")),i=r("882c");function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach((function(t){(0,c.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var p=function(){function e(){(0,s.default)(this,e)}return(0,o.default)(e,null,[{key:"getAreaDetail",value:function(){var e=(0,u.default)(a.default.mark((function e(t){var r;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,i.callCloudFunction)("hygiene-area-management",{action:"getAreaDetail",data:{id:t}});case 3:if(r=e.sent,!r.success){e.next=8;break}return e.abrupt("return",r.data);case 8:throw new Error(r.message||"获取责任区信息失败");case 9:e.next=15;break;case 11:throw e.prev=11,e.t0=e["catch"](0),console.error("获取责任区详情失败:",e.t0),e.t0;case 15:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(t){return e.apply(this,arguments)}}()},{key:"getAreaList",value:function(){var e=(0,u.default)(a.default.mark((function e(){var t,r,n=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=n.length>0&&void 0!==n[0]?n[0]:{},e.prev=1,e.next=4,(0,i.callCloudFunction)("hygiene-area-management",{action:"getAreaList",data:t});case 4:if(r=e.sent,!r.success){e.next=9;break}return e.abrupt("return",r.data);case 9:throw new Error(r.message||"获取责任区列表失败");case 10:e.next=16;break;case 12:throw e.prev=12,e.t0=e["catch"](1),console.error("获取责任区列表失败:",e.t0),e.t0;case 16:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(){return e.apply(this,arguments)}}()},{key:"createCleaningRecord",value:function(){var e=(0,u.default)(a.default.mark((function e(t){var r;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,i.callCloudFunction)("hygiene-cleaning",{action:"createCleaningRecord",data:t});case 3:if(r=e.sent,!r.success){e.next=8;break}return e.abrupt("return",r.data);case 8:throw new Error(r.message||"创建清理记录失败");case 9:e.next=15;break;case 11:throw e.prev=11,e.t0=e["catch"](0),console.error("创建清理记录失败:",e.t0),e.t0;case 15:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(t){return e.apply(this,arguments)}}()},{key:"getCleaningRecords",value:function(){var e=(0,u.default)(a.default.mark((function e(){var t,r,n=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=n.length>0&&void 0!==n[0]?n[0]:{},e.prev=1,e.next=4,(0,i.callCloudFunction)("hygiene-cleaning",{action:"getCleaningRecords",data:t});case 4:if(r=e.sent,!r.success){e.next=9;break}return e.abrupt("return",r.data);case 9:throw new Error(r.message||"获取清理记录失败");case 10:e.next=16;break;case 12:throw e.prev=12,e.t0=e["catch"](1),console.error("获取清理记录失败:",e.t0),e.t0;case 16:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(){return e.apply(this,arguments)}}()},{key:"getMyCleaningRecords",value:function(){var e=(0,u.default)(a.default.mark((function e(){var t,r,n=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=n.length>0&&void 0!==n[0]?n[0]:{},e.prev=1,e.next=4,(0,i.callCloudFunction)("hygiene-cleaning",{action:"getMyCleaningRecords",data:t});case 4:if(r=e.sent,!r.success){e.next=9;break}return e.abrupt("return",r.data);case 9:throw new Error(r.message||"获取清理记录失败");case 10:e.next=16;break;case 12:throw e.prev=12,e.t0=e["catch"](1),console.error("获取我的清理记录失败:",e.t0),e.t0;case 16:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(){return e.apply(this,arguments)}}()},{key:"updateCleaningRecord",value:function(){var e=(0,u.default)(a.default.mark((function e(t,r){var n;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,i.callCloudFunction)("hygiene-cleaning",{action:"updateCleaningRecord",data:d({id:t},r)});case 3:if(n=e.sent,!n.success){e.next=8;break}return e.abrupt("return",n.data);case 8:throw new Error(n.message||"更新清理记录失败");case 9:e.next=15;break;case 11:throw e.prev=11,e.t0=e["catch"](0),console.error("更新清理记录失败:",e.t0),e.t0;case 15:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(t,r){return e.apply(this,arguments)}}()},{key:"deleteCleaningRecord",value:function(){var e=(0,u.default)(a.default.mark((function e(t){var r;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,i.callCloudFunction)("hygiene-cleaning",{action:"deleteCleaningRecord",data:{id:t}});case 3:if(r=e.sent,!r.success){e.next=8;break}return e.abrupt("return",r.data);case 8:throw new Error(r.message||"删除清理记录失败");case 9:e.next=15;break;case 11:throw e.prev=11,e.t0=e["catch"](0),console.error("删除清理记录失败:",e.t0),e.t0;case 15:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(t){return e.apply(this,arguments)}}()},{key:"getCleaningHistory",value:function(){var e=(0,u.default)(a.default.mark((function e(t){var r,n,c=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=c.length>1&&void 0!==c[1]?c[1]:6,e.prev=1,e.next=4,(0,i.callCloudFunction)("hygiene-cleaning",{action:"getCleaningHistory",data:{area_id:t,months:r}});case 4:if(n=e.sent,!n.success){e.next=9;break}return e.abrupt("return",n.data);case 9:throw new Error(n.message||"获取清理历史失败");case 10:e.next=16;break;case 12:throw e.prev=12,e.t0=e["catch"](1),console.error("获取清理历史失败:",e.t0),e.t0;case 16:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(t){return e.apply(this,arguments)}}()},{key:"getWeeklySchedule",value:function(){var e=(0,u.default)(a.default.mark((function e(t,r){var n;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,i.callCloudFunction)("hygiene-cleaning",{action:"getWeeklySchedule",data:{week_start:t,user_id:r}});case 3:if(n=e.sent,!n.success){e.next=8;break}return e.abrupt("return",n.data);case 8:throw new Error(n.message||"获取周计划失败");case 9:e.next=15;break;case 11:throw e.prev=11,e.t0=e["catch"](0),console.error("获取周清理计划失败:",e.t0),e.t0;case 15:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(t,r){return e.apply(this,arguments)}}()},{key:"createInspectionRecord",value:function(){var e=(0,u.default)(a.default.mark((function e(t){var r;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,i.callCloudFunction)("hygiene-inspection",{action:"createInspectionRecord",data:t});case 3:if(r=e.sent,!r.success){e.next=8;break}return e.abrupt("return",r.data);case 8:throw new Error(r.message||"创建检查记录失败");case 9:e.next=15;break;case 11:throw e.prev=11,e.t0=e["catch"](0),console.error("创建检查记录失败:",e.t0),e.t0;case 15:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(t){return e.apply(this,arguments)}}()},{key:"getInspectionRecords",value:function(){var e=(0,u.default)(a.default.mark((function e(){var t,r,n=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=n.length>0&&void 0!==n[0]?n[0]:{},e.prev=1,e.next=4,(0,i.callCloudFunction)("hygiene-inspection",{action:"getInspectionRecords",data:t});case 4:if(r=e.sent,!r.success){e.next=9;break}return e.abrupt("return",r.data);case 9:throw new Error(r.message||"获取检查记录失败");case 10:e.next=16;break;case 12:throw e.prev=12,e.t0=e["catch"](1),console.error("获取检查记录失败:",e.t0),e.t0;case 16:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(){return e.apply(this,arguments)}}()},{key:"createIssueRecord",value:function(){var e=(0,u.default)(a.default.mark((function e(t){var r;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,i.callCloudFunction)("hygiene-issues",{action:"createIssueRecord",data:t});case 3:if(r=e.sent,!r.success){e.next=8;break}return e.abrupt("return",r.data);case 8:throw new Error(r.message||"创建问题记录失败");case 9:e.next=15;break;case 11:throw e.prev=11,e.t0=e["catch"](0),console.error("创建问题记录失败:",e.t0),e.t0;case 15:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(t){return e.apply(this,arguments)}}()},{key:"getIssueRecords",value:function(){var e=(0,u.default)(a.default.mark((function e(){var t,r,n=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=n.length>0&&void 0!==n[0]?n[0]:{},e.prev=1,e.next=4,(0,i.callCloudFunction)("hygiene-issues",{action:"getIssueRecords",data:t});case 4:if(r=e.sent,!r.success){e.next=9;break}return e.abrupt("return",r.data);case 9:throw new Error(r.message||"获取问题记录失败");case 10:e.next=16;break;case 12:throw e.prev=12,e.t0=e["catch"](1),console.error("获取问题记录失败:",e.t0),e.t0;case 16:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(){return e.apply(this,arguments)}}()},{key:"deleteCleaningPhotos",value:function(){var e=(0,u.default)(a.default.mark((function e(t,r){var n;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,i.callCloudFunction)("hygiene-cleaning",{action:"deleteCleaningPhotos",data:{cleaning_record_id:t,photo_urls:r}});case 3:if(n=e.sent,!n.success){e.next=8;break}return e.abrupt("return",n.data);case 8:throw new Error(n.message||"删除照片失败");case 9:e.next=15;break;case 11:throw e.prev=11,e.t0=e["catch"](0),console.error("删除清理照片失败:",e.t0),e.t0;case 15:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(t,r){return e.apply(this,arguments)}}()},{key:"createRectificationRecord",value:function(){var e=(0,u.default)(a.default.mark((function e(t){var r;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,i.callCloudFunction)("hygiene-rectification",{action:"createRectificationRecord",data:t});case 3:if(r=e.sent,!r.success){e.next=8;break}return e.abrupt("return",r.data);case 8:throw new Error(r.message||"创建整改记录失败");case 9:e.next=15;break;case 11:throw e.prev=11,e.t0=e["catch"](0),console.error("创建整改记录失败:",e.t0),e.t0;case 15:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(t){return e.apply(this,arguments)}}()},{key:"getRectificationRecords",value:function(){var e=(0,u.default)(a.default.mark((function e(){var t,r,n=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=n.length>0&&void 0!==n[0]?n[0]:{},e.prev=1,e.next=4,(0,i.callCloudFunction)("hygiene-rectification",{action:"getRectificationRecords",data:t});case 4:if(r=e.sent,!r.success){e.next=9;break}return e.abrupt("return",r.data);case 9:throw new Error(r.message||"获取整改记录失败");case 10:e.next=16;break;case 12:throw e.prev=12,e.t0=e["catch"](1),console.error("获取整改记录失败:",e.t0),e.t0;case 16:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(){return e.apply(this,arguments)}}()}]),e}(),f=p;t.default=f}}]);