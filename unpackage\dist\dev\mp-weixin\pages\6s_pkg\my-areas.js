require('./common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/6s_pkg/my-areas"],{

/***/ 500:
/*!************************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2F6s_pkg%2Fmy-areas"} ***!
  \************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _myAreas = _interopRequireDefault(__webpack_require__(/*! ./pages/6s_pkg/my-areas.vue */ 501));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_myAreas.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 501:
/*!*****************************************!*\
  !*** D:/Xwzc/pages/6s_pkg/my-areas.vue ***!
  \*****************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _my_areas_vue_vue_type_template_id_4c3d3ebe_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./my-areas.vue?vue&type=template&id=4c3d3ebe&scoped=true& */ 502);
/* harmony import */ var _my_areas_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./my-areas.vue?vue&type=script&lang=js& */ 504);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _my_areas_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _my_areas_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _my_areas_vue_vue_type_style_index_0_id_4c3d3ebe_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./my-areas.vue?vue&type=style&index=0&id=4c3d3ebe&lang=scss&scoped=true& */ 506);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _my_areas_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _my_areas_vue_vue_type_template_id_4c3d3ebe_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _my_areas_vue_vue_type_template_id_4c3d3ebe_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "4c3d3ebe",
  null,
  false,
  _my_areas_vue_vue_type_template_id_4c3d3ebe_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/6s_pkg/my-areas.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 502:
/*!************************************************************************************!*\
  !*** D:/Xwzc/pages/6s_pkg/my-areas.vue?vue&type=template&id=4c3d3ebe&scoped=true& ***!
  \************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_my_areas_vue_vue_type_template_id_4c3d3ebe_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my-areas.vue?vue&type=template&id=4c3d3ebe&scoped=true& */ 503);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_my_areas_vue_vue_type_template_id_4c3d3ebe_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_my_areas_vue_vue_type_template_id_4c3d3ebe_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_my_areas_vue_vue_type_template_id_4c3d3ebe_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_my_areas_vue_vue_type_template_id_4c3d3ebe_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 503:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/6s_pkg/my-areas.vue?vue&type=template&id=4c3d3ebe&scoped=true& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 118))
    },
    pEmptyState: function () {
      return __webpack_require__.e(/*! import() | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then(__webpack_require__.bind(null, /*! @/components/p-empty-state/p-empty-state.vue */ 677))
    },
    uniPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-popup/components/uni-popup/uni-popup */ "uni_modules/uni-popup/components/uni-popup/uni-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-popup/components/uni-popup/uni-popup.vue */ 684))
    },
    uniCalendar: function () {
      return Promise.all(/*! import() | uni_modules/uni-calendar/components/uni-calendar/uni-calendar */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-calendar/components/uni-calendar/uni-calendar")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-calendar/components/uni-calendar/uni-calendar.vue */ 958))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = _vm.getStatusTitle()
  var m1 = _vm.getCurrentTimeRange()
  var g0 = !_vm.loading ? _vm.myFixedAreas.length : null
  var l0 = !_vm.loading
    ? _vm.__map(_vm.myFixedAreas, function (area, index) {
        var $orig = _vm.__get_orig(area)
        var m2 = _vm.getIconColor(area.status)
        var m3 = _vm.getStatusText(area.status)
        return {
          $orig: $orig,
          m2: m2,
          m3: m3,
        }
      })
    : null
  var g1 = _vm.myPublicAreas.length > 0 || (!_vm.loading && _vm.dataLoaded)
  var g2 = g1 && !_vm.loading ? _vm.myPublicAreas.length : null
  var l1 =
    g1 && !_vm.loading
      ? _vm.__map(_vm.myPublicAreas, function (area, index) {
          var $orig = _vm.__get_orig(area)
          var m4 = _vm.getIconColor(area.status)
          var m5 = _vm.getStatusText(area.status)
          return {
            $orig: $orig,
            m4: m4,
            m5: m5,
          }
        })
      : null
  var g3 = !_vm.loading ? _vm.groupedRectificationTasks.length : null
  var l3 = !_vm.loading
    ? _vm.__map(_vm.groupedRectificationTasks, function (group, __i0__) {
        var $orig = _vm.__get_orig(group)
        var g4 = group.tasks.length
        var l2 = group.expanded
          ? _vm.__map(group.tasks, function (task, index) {
              var $orig = _vm.__get_orig(task)
              var m6 = _vm.getIconColor(task.status)
              var m7 = _vm.getStatusText(task.status)
              return {
                $orig: $orig,
                m6: m6,
                m7: m7,
              }
            })
          : null
        return {
          $orig: $orig,
          g4: g4,
          l2: l2,
        }
      })
    : null
  var g5 = !_vm.loading ? _vm.cleaningRecords.list.length : null
  var l4 = !_vm.loading
    ? _vm.__map(_vm.cleaningRecords.list, function (record, index) {
        var $orig = _vm.__get_orig(record)
        var m8 = _vm.getIconColor(record.status)
        var g6 = record.remark ? record.remark.substring(0, 15) : null
        var g7 = record.remark ? record.remark.length : null
        var m9 = _vm.getCleaningRecordStatusText(record.status)
        return {
          $orig: $orig,
          m8: m8,
          g6: g6,
          g7: g7,
          m9: m9,
        }
      })
    : null
  var m10 =
    !(_vm.dateFilterMode === "quick") &&
    _vm.customDateRange.startDate &&
    _vm.customDateRange.endDate
      ? _vm.formatSelectedDate(_vm.customDateRange.startDate)
      : null
  var m11 =
    !(_vm.dateFilterMode === "quick") &&
    _vm.customDateRange.startDate &&
    _vm.customDateRange.endDate
      ? _vm.formatSelectedDate(_vm.customDateRange.endDate)
      : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        m1: m1,
        g0: g0,
        l0: l0,
        g1: g1,
        g2: g2,
        l1: l1,
        g3: g3,
        l3: l3,
        g5: g5,
        l4: l4,
        m10: m10,
        m11: m11,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 504:
/*!******************************************************************!*\
  !*** D:/Xwzc/pages/6s_pkg/my-areas.vue?vue&type=script&lang=js& ***!
  \******************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_my_areas_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my-areas.vue?vue&type=script&lang=js& */ 505);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_my_areas_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_my_areas_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_my_areas_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_my_areas_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_my_areas_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 505:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/6s_pkg/my-areas.vue?vue&type=script&lang=js& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ 5));
var _auth = __webpack_require__(/*! @/utils/auth.js */ 91);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var _default = {
  name: 'MyAreas',
  data: function data() {
    return {
      // 日期筛选方式：'quick' 快捷选择, 'range' 日期范围选择
      dateFilterMode: 'quick',
      selectedTimeFilter: 'week',
      // 自定义日期范围
      customDateRange: {
        startDate: '',
        endDate: ''
      },
      // 日历组件相关数据
      calendarDate: '',
      calendarStartDate: '',
      calendarEndDate: '',
      // 防重复查询的定时器
      refreshTimer: null,
      calendarChangeTimer: null,
      // 快捷日期选项
      quickDateOptions: [{
        label: '本周',
        value: 'week'
      }, {
        label: '上周',
        value: 'last_week'
      }, {
        label: '本月',
        value: 'month'
      }, {
        label: '自定义',
        value: 'custom'
      }],
      selectedQuickFilter: 'week',
      // 我的责任区（从API加载）
      myAreas: [],
      // 整改任务记录（从API加载）
      rectificationTasks: [],
      // 我的清理记录（分页结构）
      cleaningRecords: {
        list: [],
        // 当前显示的记录列表
        pageSize: 10,
        // 每页显示数量
        hasMore: true,
        // 是否还有更多数据
        loading: false // 加载状态
      },

      allCleaningRecords: [],
      // 所有清理记录（用于分页）

      // 加载状态
      loading: false,
      dataLoaded: false,
      // 标记数据是否已加载
      needsRefresh: false,
      // 标记是否需要刷新数据

      // 展开的整改记录周次（动态设置，默认展开最新的）
      expandedRectificationWeeks: [],
      // 时间计算缓存（避免重复计算）
      currentWeekCache: null,
      // 区域分类缓存（避免重复过滤）
      areasCache: null
    };
  },
  computed: {
    // 固定责任区（永远显示本周状态，不受时间筛选影响）
    myFixedAreas: function myFixedAreas() {
      // 使用缓存避免重复计算
      if (!this.areasCache || this.areasCache.sourceLength !== this.myAreas.length) {
        this.areasCache = {
          sourceLength: this.myAreas.length,
          fixed: this.myAreas.filter(function (area) {
            return area.type === 'fixed';
          }),
          public: this.myAreas.filter(function (area) {
            return area.type === 'public';
          })
        };
      }
      return this.areasCache.fixed;
    },
    // 公共责任区（永远显示本周状态，不受时间筛选影响）
    myPublicAreas: function myPublicAreas() {
      // 使用缓存避免重复计算
      if (!this.areasCache || this.areasCache.sourceLength !== this.myAreas.length) {
        this.areasCache = {
          sourceLength: this.myAreas.length,
          fixed: this.myAreas.filter(function (area) {
            return area.type === 'fixed';
          }),
          public: this.myAreas.filter(function (area) {
            return area.type === 'public';
          })
        };
      }
      return this.areasCache.public;
    },
    completedCount: function completedCount() {
      try {
        // 如果选择的是本周，统计本周已完成的责任区 + 本周已完成的整改记录
        if (this.selectedQuickFilter === 'week') {
          var completedAreas = this.myAreas.filter(function (area) {
            return area.status === 'completed';
          }).length || 0;
          var completedRectifications = this.getThisWeekRectificationTasks().filter(function (task) {
            return task.status === 'completed' || task.status === 'verified';
          }).length || 0;
          return completedAreas + completedRectifications;
        } else {
          // 历史时间范围：只统计该时间范围内的整改记录（责任区状态是当前状态，不应该按历史统计）
          var count = this.filteredRectificationTasks.filter(function (task) {
            return task.status === 'completed' || task.status === 'verified';
          }).length || 0;
          return count;
        }
      } catch (error) {
        console.error('计算已完成数量出错:', error);
        return 0;
      }
    },
    pendingCount: function pendingCount() {
      try {
        // 如果选择的是本周，统计本周真正需要员工处理的任务
        if (this.selectedQuickFilter === 'week') {
          // 责任区：只统计真正可以清理的（排除"未到时间"的公共责任区）
          var pendingAreas = this.myAreas.filter(function (area) {
            return area.status === 'pending';
          } // 只统计真正待清理的，不包括scheduled(未到时间)
          ).length || 0;
          // 整改记录：只统计真正需要员工操作的（排除"待复查"）
          var pendingRectifications = this.getThisWeekRectificationTasks().filter(function (task) {
            return task.status === 'pending_rectification';
          } // 只统计待整改，不包括pending_review(待复查)
          ).length || 0;
          return pendingAreas + pendingRectifications;
        } else {
          // 历史时间范围：只统计该时间范围内需要员工操作的整改记录
          var count = this.filteredRectificationTasks.filter(function (task) {
            return task.status === 'pending_rectification';
          } // 只统计待整改
          ).length || 0;
          return count;
        }
      } catch (error) {
        console.error('计算待处理数量出错:', error);
        return 0;
      }
    },
    overdueCount: function overdueCount() {
      var _this = this;
      try {
        // 如果选择的是本周，统计本周已逾期的责任区 + 逾期整改记录
        if (this.selectedQuickFilter === 'week') {
          var overdueAreas = this.myAreas.filter(function (area) {
            return area.status === 'overdue';
          }).length || 0;
          // 整改记录逾期：使用更精确的逾期判断逻辑
          var overdueRectifications = this.getThisWeekRectificationTasks().filter(function (task) {
            return task.status === 'overdue' || _this.isRectificationOverdue(task);
          }).length || 0;
          return overdueAreas + overdueRectifications;
        } else {
          // 历史时间范围：只统计该时间范围内的逾期整改记录
          var count = this.filteredRectificationTasks.filter(function (task) {
            return task.status === 'overdue' || _this.isRectificationOverdue(task);
          }).length || 0;
          return count;
        }
      } catch (error) {
        console.error('计算逾期数量出错:', error);
        return 0;
      }
    },
    // 根据时间筛选的整改任务记录
    filteredRectificationTasks: function filteredRectificationTasks() {
      var tasks = this.rectificationTasks;

      // 基于日期范围的直接筛选
      var dateRange = this.getCurrentDateRange();
      if (dateRange) {
        tasks = tasks.filter(function (task) {
          if (!task.created_at) return false;
          var taskDate = new Date(task.created_at);
          return taskDate >= dateRange.start && taskDate <= dateRange.end;
        });
      }
      return tasks;
    },
    // 时间分组的整改任务记录
    groupedRectificationTasks: function groupedRectificationTasks() {
      var _this2 = this;
      var tasks = this.filteredRectificationTasks;
      var grouped = {};
      tasks.forEach(function (task) {
        // 重新计算周键以确保使用最新的时区逻辑
        // 使用原始的created_at而不是格式化后的issueFoundDate
        var weekKey = _this2.getWeekKey(task.created_at);
        if (!grouped[weekKey]) {
          grouped[weekKey] = {
            weekKey: weekKey,
            title: _this2.getWeekTitle(weekKey),
            tasks: [],
            expanded: _this2.expandedRectificationWeeks.includes(weekKey)
          };
        }
        grouped[weekKey].tasks.push(task);
      });

      // 按周次降序排序
      var sortedGroups = Object.values(grouped).sort(function (a, b) {
        // 更精确的排序：先按年份，再按周数
        var _a$weekKey$split$map = a.weekKey.split('-W').map(function (x) {
            return parseInt(x);
          }),
          _a$weekKey$split$map2 = (0, _slicedToArray2.default)(_a$weekKey$split$map, 2),
          yearA = _a$weekKey$split$map2[0],
          weekA = _a$weekKey$split$map2[1];
        var _b$weekKey$split$map = b.weekKey.split('-W').map(function (x) {
            return parseInt(x);
          }),
          _b$weekKey$split$map2 = (0, _slicedToArray2.default)(_b$weekKey$split$map, 2),
          yearB = _b$weekKey$split$map2[0],
          weekB = _b$weekKey$split$map2[1];
        if (yearA !== yearB) {
          return yearB - yearA; // 年份降序
        }

        return weekB - weekA; // 同年内周数降序
      });

      // 同步展开状态：完全基于用户手动操作的状态
      sortedGroups.forEach(function (group) {
        group.expanded = _this2.expandedRectificationWeeks.includes(group.weekKey);
      });
      return sortedGroups;
    }
  },
  created: function created() {},
  onLoad: function onLoad() {
    this.loadMyAreas();

    // 监听清理记录更新事件
    uni.$on('cleaningRecordUpdated', this.handleRecordUpdated);
    // 监听整改记录更新事件
    uni.$on('rectificationRecordUpdated', this.handleRecordUpdated);
  },
  onUnload: function onUnload() {
    // 移除事件监听
    uni.$off('cleaningRecordUpdated', this.handleRecordUpdated);
    uni.$off('rectificationRecordUpdated', this.handleRecordUpdated);

    // 清理定时器
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }
    if (this.calendarChangeTimer) {
      clearTimeout(this.calendarChangeTimer);
    }
  },
  onShow: function onShow() {
    // 页面重新显示时，只在确实需要刷新数据的情况下才刷新
    // 使用静默刷新，避免显示加载动画
    if (this.dataLoaded && !this.loading && this.needsRefresh) {
      this.silentRefreshData();
      this.needsRefresh = false; // 重置刷新标记
    }
  },

  methods: {
    // 获取本周的整改任务
    getThisWeekRectificationTasks: function getThisWeekRectificationTasks() {
      try {
        var now = new Date();
        var beijingNow = this.toBeijingTime(now);
        if (!beijingNow) {
          console.error('无法获取北京时间');
          return [];
        }
        var weekStart = this.getWeekStart(beijingNow);
        var weekEnd = this.getWeekEnd(beijingNow);
        if (!weekStart || !weekEnd) {
          console.error('无法计算本周时间范围');
          return [];
        }
        if (!Array.isArray(this.rectificationTasks)) {
          return [];
        }
        return this.rectificationTasks.filter(function (task) {
          if (!task || !task.created_at) return false;
          try {
            var taskDate = new Date(task.created_at);
            if (isNaN(taskDate.getTime())) return false;
            return taskDate >= weekStart && taskDate <= weekEnd;
          } catch (error) {
            console.error('处理任务日期出错:', error, task);
            return false;
          }
        });
      } catch (error) {
        console.error('获取本周整改任务出错:', error);
        return [];
      }
    },
    // 判断整改任务是否逾期（严格按周计算）
    isRectificationOverdue: function isRectificationOverdue(task) {
      if (!task || task.status === 'completed' || task.status === 'verified') {
        return false; // 已完成的任务不算逾期
      }

      if (!task.created_at) {
        return false;
      }

      // 只有待整改和待分配的任务才能算逾期
      if (task.status !== 'pending_rectification' && task.status !== 'pending_assignment') {
        return false;
      }
      var now = new Date();
      var beijingNow = this.toBeijingTime(now);

      // 获取任务创建时间所在周的结束时间（周日23:59:59）
      var taskCreatedTime = new Date(task.created_at);
      var beijingCreatedTime = this.toBeijingTime(taskCreatedTime);
      var taskWeekEnd = this.getWeekEnd(beijingCreatedTime);

      // 如果当前时间超过了任务创建周的周日23:59:59，则为逾期
      return beijingNow.getTime() > taskWeekEnd.getTime();
    },
    // 加载用户责任区数据
    loadMyAreas: function loadMyAreas() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                _this3.loading = true;
                _context.prev = 1;
                _context.next = 4;
                return Promise.all([_this3.loadUserAssignments(), _this3.loadUserRectificationTasks(), _this3.loadMyCleaningRecords()]);
              case 4:
                _context.next = 12;
                break;
              case 6:
                _context.prev = 6;
                _context.t0 = _context["catch"](1);
                uni.showToast({
                  title: '加载数据失败',
                  icon: 'none',
                  duration: 2000
                });
                // 设置为空数组以显示空状态
                _this3.myAreas = [];
                _this3.rectificationTasks = [];
                _this3.cleaningRecords.list = [];
              case 12:
                _context.prev = 12;
                _this3.loading = false;
                _this3.dataLoaded = true; // 标记数据已加载
                return _context.finish(12);
              case 16:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[1, 6, 12, 16]]);
      }))();
    },
    // 加载用户的责任区分配
    loadUserAssignments: function loadUserAssignments() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var result, assignments, areaIds, areaDataMap;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                _context2.next = 3;
                return (0, _auth.callCloudFunction)('hygiene-assignments', {
                  action: 'getUserAssignments',
                  data: {}
                });
              case 3:
                result = _context2.sent;
                if (!(result && result.success)) {
                  _context2.next = 17;
                  break;
                }
                assignments = result.data || []; // 将分配记录转换为页面需要的格式
                _this4.myAreas = [];

                // 收集所有责任区ID，用于批量查询清理记录
                areaIds = [];
                areaDataMap = new Map();
                assignments.forEach(function (assignment) {
                  if (assignment.areas_info && Array.isArray(assignment.areas_info)) {
                    assignment.areas_info.forEach(function (area) {
                      var areaId = area._id || area.id;
                      areaIds.push(areaId);

                      // 先创建基础数据结构
                      var areaData = {
                        id: areaId,
                        name: area.name,
                        icon: area.type === 'fixed' ? 'gear' : 'videocam',
                        type: area.type || 'fixed',
                        weeklyRequired: area.type === 'fixed',
                        location: area.location,
                        description: area.description,
                        last_clean_date: null // 将通过清理记录查询获得真实值
                      };

                      // 如果是公共责任区，添加排班信息
                      if (area.type === 'public' && area.scheduled_day !== null) {
                        areaData.scheduledDay = area.scheduled_day;
                        areaData.scheduled_day = area.scheduled_day;
                      }
                      areaDataMap.set(areaId, areaData);
                    });
                  }
                });

                // 查询这些责任区的最新清理记录
                if (!(areaIds.length > 0)) {
                  _context2.next = 13;
                  break;
                }
                _context2.next = 13;
                return _this4.loadLatestCleaningRecords(areaIds, areaDataMap);
              case 13:
                // 转换为最终格式
                _this4.myAreas = Array.from(areaDataMap.values()).map(function (area) {
                  var status = _this4.calculateAreaStatus(area);
                  return _objectSpread(_objectSpread({}, area), {}, {
                    subtitle: _this4.generateAreaSubtitle(area),
                    status: status,
                    icon: _this4.getAreaIcon(status),
                    lastCleaningDate: area.last_clean_date
                  });
                });

                // 清理缓存，确保计算属性重新计算
                _this4.areasCache = null;
                _context2.next = 18;
                break;
              case 17:
                _this4.myAreas = [];
              case 18:
                _context2.next = 23;
                break;
              case 20:
                _context2.prev = 20;
                _context2.t0 = _context2["catch"](0);
                _this4.myAreas = [];
              case 23:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 20]]);
      }))();
    },
    // 加载责任区的最新清理记录
    loadLatestCleaningRecords: function loadLatestCleaningRecords(areaIds, areaDataMap) {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var now, _this5$currentWeekCac, weekStart, weekEnd, result, promises;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                _context4.prev = 0;
                // 获取本周时间范围（缓存计算结果）
                if (!_this5.currentWeekCache) {
                  now = new Date();
                  _this5.currentWeekCache = {
                    now: now,
                    weekStart: _this5.getWeekStart(now),
                    weekEnd: _this5.getWeekEnd(now)
                  };
                }
                _this5$currentWeekCac = _this5.currentWeekCache, weekStart = _this5$currentWeekCac.weekStart, weekEnd = _this5$currentWeekCac.weekEnd; // 批量查询所有责任区的清理记录（一次API调用替代多次）
                _context4.prev = 3;
                _context4.next = 6;
                return (0, _auth.callCloudFunction)('hygiene-cleaning', {
                  action: 'getBatchCleaningRecords',
                  data: {
                    area_ids: areaIds,
                    start_date: weekStart.toISOString(),
                    end_date: weekEnd.toISOString(),
                    latest_only: true // 只返回每个区域的最新记录
                  }
                });
              case 6:
                result = _context4.sent;
                if (result && result.success && result.data) {
                  // 处理批量返回的记录
                  result.data.forEach(function (record) {
                    if (areaDataMap.has(record.area_id)) {
                      areaDataMap.get(record.area_id).last_clean_date = record.cleaning_date;
                    }
                  });
                }
                _context4.next = 15;
                break;
              case 10:
                _context4.prev = 10;
                _context4.t0 = _context4["catch"](3);
                // 如果批量查询失败，回退到单独查询
                promises = areaIds.map( /*#__PURE__*/function () {
                  var _ref = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3(areaId) {
                    var _result, latestRecord;
                    return _regenerator.default.wrap(function _callee3$(_context3) {
                      while (1) {
                        switch (_context3.prev = _context3.next) {
                          case 0:
                            _context3.prev = 0;
                            _context3.next = 3;
                            return (0, _auth.callCloudFunction)('hygiene-cleaning', {
                              action: 'getCleaningRecords',
                              data: {
                                area_id: areaId,
                                start_date: weekStart.toISOString(),
                                end_date: weekEnd.toISOString(),
                                pageSize: 1 // 只需要最新的1条记录
                              }
                            });
                          case 3:
                            _result = _context3.sent;
                            if (_result && _result.success && _result.data && _result.data.list && _result.data.list.length > 0) {
                              latestRecord = _result.data.list[0];
                              if (areaDataMap.has(areaId)) {
                                areaDataMap.get(areaId).last_clean_date = latestRecord.cleaning_date;
                              }
                            }
                            _context3.next = 9;
                            break;
                          case 7:
                            _context3.prev = 7;
                            _context3.t0 = _context3["catch"](0);
                          case 9:
                          case "end":
                            return _context3.stop();
                        }
                      }
                    }, _callee3, null, [[0, 7]]);
                  }));
                  return function (_x) {
                    return _ref.apply(this, arguments);
                  };
                }());
                _context4.next = 15;
                return Promise.all(promises);
              case 15:
                _context4.next = 19;
                break;
              case 17:
                _context4.prev = 17;
                _context4.t1 = _context4["catch"](0);
              case 19:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[0, 17], [3, 10]]);
      }))();
    },
    // 加载用户的清理记录
    loadMyCleaningRecords: function loadMyCleaningRecords() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var requestData, dateRange, result, records, formattedRecords;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _context5.prev = 0;
                // 设置加载状态
                _this6.cleaningRecords.loading = true;

                // 构建请求参数，包含日期范围筛选
                requestData = {
                  pageSize: 20 // 显示最近20条记录
                }; // 如果有日期范围筛选，添加到请求参数中
                dateRange = _this6.getCurrentDateRange();
                if (dateRange) {
                  requestData.start_date = dateRange.start.toISOString();
                  requestData.end_date = dateRange.end.toISOString();
                }

                // 调用云函数获取当前用户的清理记录
                _context5.next = 7;
                return (0, _auth.callCloudFunction)('hygiene-cleaning', {
                  action: 'getMyCleaningRecords',
                  data: requestData
                });
              case 7:
                result = _context5.sent;
                if (result && result.success) {
                  records = result.data || []; // 将清理记录转换为页面需要的格式
                  formattedRecords = records.map(function (record) {
                    return {
                      id: record._id || record.id,
                      areaId: record.area_id,
                      areaName: record.area_name || '未知责任区',
                      areaType: record.area_type || 'fixed',
                      cleaningDate: record.cleaning_date,
                      formattedDate: _this6.formatDateTime(record.cleaning_date),
                      photos: record.photos ? record.photos.length : 0,
                      remark: record.remark || '',
                      status: 'completed',
                      // 所有记录都是已完成
                      icon: 'checkmarkempty',
                      week: _this6.getWeekKey(record.cleaning_date)
                    };
                  }); // 保存所有记录用于分页
                  _this6.allCleaningRecords = formattedRecords;

                  // 设置首页数据
                  _this6.cleaningRecords.list = formattedRecords.slice(0, _this6.cleaningRecords.pageSize);
                  _this6.cleaningRecords.hasMore = records.length > _this6.cleaningRecords.pageSize;

                  // 按清理时间降序排序
                  _this6.cleaningRecords.list.sort(function (a, b) {
                    return new Date(b.cleaningDate) - new Date(a.cleaningDate);
                  });
                } else {
                  _this6.cleaningRecords.list = [];
                  _this6.cleaningRecords.hasMore = false;
                }
                _context5.next = 15;
                break;
              case 11:
                _context5.prev = 11;
                _context5.t0 = _context5["catch"](0);
                _this6.cleaningRecords.list = [];
                _this6.cleaningRecords.hasMore = false;
              case 15:
                _context5.prev = 15;
                // 重置加载状态
                _this6.cleaningRecords.loading = false;
                return _context5.finish(15);
              case 18:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[0, 11, 15, 18]]);
      }))();
    },
    // 加载更多清理记录（从本地缓存的数据中分页显示）
    loadMoreCleaningRecords: function loadMoreCleaningRecords() {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var currentDisplayed, nextPageSize, nextPageEnd, nextPageData;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                if (!(_this7.cleaningRecords.loading || !_this7.cleaningRecords.hasMore)) {
                  _context6.next = 2;
                  break;
                }
                return _context6.abrupt("return");
              case 2:
                _this7.cleaningRecords.loading = true;
                currentDisplayed = _this7.cleaningRecords.list.length;
                nextPageSize = _this7.cleaningRecords.pageSize;
                nextPageEnd = currentDisplayed + nextPageSize; // 从所有记录中获取下一页数据
                if (_this7.allCleaningRecords && _this7.allCleaningRecords.length > currentDisplayed) {
                  nextPageData = _this7.allCleaningRecords.slice(currentDisplayed, nextPageEnd); // 追加新数据
                  _this7.cleaningRecords.list = [].concat((0, _toConsumableArray2.default)(_this7.cleaningRecords.list), (0, _toConsumableArray2.default)(nextPageData));

                  // 检查是否还有更多数据
                  _this7.cleaningRecords.hasMore = _this7.cleaningRecords.list.length < _this7.allCleaningRecords.length;
                } else {
                  _this7.cleaningRecords.hasMore = false;
                }
                _this7.cleaningRecords.loading = false;
              case 8:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6);
      }))();
    },
    // 加载用户的整改任务记录
    loadUserRectificationTasks: function loadUserRectificationTasks() {
      var _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        var requestData, dateRange, result, tasks;
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                _context7.prev = 0;
                // 构建请求参数，包含日期范围筛选
                requestData = {}; // 如果有日期范围筛选，添加到请求参数中
                dateRange = _this8.getCurrentDateRange();
                if (dateRange) {
                  requestData.start_date = dateRange.start.toISOString();
                  requestData.end_date = dateRange.end.toISOString();
                }

                // 调用云函数获取当前用户的整改记录
                _context7.next = 6;
                return (0, _auth.callCloudFunction)('hygiene-rectification', {
                  action: 'getMyRectifications',
                  data: requestData
                });
              case 6:
                result = _context7.sent;
                if (result && result.success) {
                  tasks = result.data || []; // 将整改记录转换为页面需要的格式
                  _this8.rectificationTasks = tasks.map(function (task) {
                    var weekKey = _this8.getWeekKey(task.created_at);

                    // 计算实际状态（考虑时间逾期）
                    var actualStatus = _this8.calculateRectificationStatus(task);

                    // 确保有有效的ID（支持多种可能的ID字段）
                    var taskId = task._id || task.id || task.issue_id || task.task_id;
                    if (!taskId) {
                      return null; // 跳过无效的任务数据
                    }

                    return {
                      id: taskId,
                      areaId: task.area_id,
                      areaName: task.area_name,
                      subtitle: _this8.formatDescription(task.issue_description || task.description),
                      status: actualStatus,
                      originalStatus: task.status,
                      // 保留数据库原始状态
                      icon: _this8.getTaskIcon(actualStatus),
                      areaType: task.area_type || 'fixed',
                      week: weekKey,
                      issueFoundDate: _this8.formatDate(task.created_at),
                      completedDate: task.completed_at ? _this8.formatDate(task.completed_at) : null,
                      assignee_id: task.assignee_id,
                      assignee_name: task.assignee_name,
                      created_at: task.created_at,
                      // 保留原始创建时间用于筛选
                      deadline: task.deadline // 保留截止时间用于逾期判断
                    };
                  }).filter(function (task) {
                    return task && task.id;
                  }); // 过滤掉null和没有ID的任务

                  // 设置默认展开最新的周
                  _this8.setDefaultExpandedWeek();
                } else {
                  _this8.rectificationTasks = [];
                }
                _context7.next = 13;
                break;
              case 10:
                _context7.prev = 10;
                _context7.t0 = _context7["catch"](0);
                _this8.rectificationTasks = [];
              case 13:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7, null, [[0, 10]]);
      }))();
    },
    // 生成责任区副标题
    generateAreaSubtitle: function generateAreaSubtitle(area) {
      if (area.type === 'fixed') {
        // 固定责任区：显示本周状态
        if (area.last_clean_date) {
          var now = new Date();
          var weekStart = this.getWeekStart(now);
          var weekEnd = this.getWeekEnd(now);
          var cleanDate = new Date(area.last_clean_date);

          // 检查清理日期是否在本周内
          if (cleanDate >= weekStart && cleanDate <= weekEnd) {
            var formattedDate = this.formatDate(area.last_clean_date);
            return "\u672C\u5468\u5DF2\u6E05\u7406 \xB7 ".concat(formattedDate);
          } else {
            return '本周待清理 · 每周完成一次';
          }
        } else {
          return '本周待清理 · 每周完成一次';
        }
      } else if (area.type === 'public') {
        // 公共责任区：根据排班和状态显示
        if (area.scheduled_day !== null) {
          if (area.last_clean_date) {
            var _cleanDate = this.formatDate(area.last_clean_date);
            return "\u672C\u5468\u5DF2\u6E05\u7406 \xB7 ".concat(_cleanDate);
          } else {
            var weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
            var scheduledDay = area.scheduled_day === 0 ? 0 : area.scheduled_day;

            // 根据状态显示不同的副标题
            if (area.status === 'scheduled') {
              return "\u8BA1\u5212".concat(weekDays[scheduledDay], "\u6E05\u7406 \xB7 \u672A\u5230\u65F6\u95F4");
            } else if (area.status === 'pending') {
              return "\u8BA1\u5212".concat(weekDays[scheduledDay], "\u6E05\u7406 \xB7 \u53EF\u4EE5\u6E05\u7406");
            } else if (area.status === 'overdue') {
              return "\u8BA1\u5212".concat(weekDays[scheduledDay], "\u6E05\u7406 \xB7 \u5DF2\u903E\u671F");
            } else {
              return "\u8BA1\u5212".concat(weekDays[scheduledDay], "\u6E05\u7406");
            }
          }
        } else {
          return '未设置清理日程';
        }
      }
      return '待处理';
    },
    // 计算责任区状态
    calculateAreaStatus: function calculateAreaStatus(area) {
      // 使用缓存的时间计算结果
      if (!this.currentWeekCache) {
        var _now = new Date();
        this.currentWeekCache = {
          now: _now,
          weekStart: this.getWeekStart(_now),
          weekEnd: this.getWeekEnd(_now)
        };
      }
      var _this$currentWeekCach = this.currentWeekCache,
        now = _this$currentWeekCach.now,
        weekStart = _this$currentWeekCach.weekStart,
        weekEnd = _this$currentWeekCach.weekEnd;
      if (area.type === 'fixed') {
        // 固定责任区：每周循环，从周一到周日
        if (area.last_clean_date) {
          var cleanDate = new Date(area.last_clean_date);
          if (cleanDate >= weekStart && cleanDate <= weekEnd) {
            return 'completed';
          }
        }

        // 固定责任区逾期逻辑：只有过了本周且本周没有清理才算逾期
        // 本周内（周一到周日）都应该显示"待清理"，不算逾期
        // 逾期判断：已经是下周了，但上周没有完成清理
        var nextWeekStart = new Date(weekEnd);
        nextWeekStart.setDate(nextWeekStart.getDate() + 1); // 下周一

        if (now >= nextWeekStart) {
          // 已经到了下周
          // 检查上周（当前计算的这一周）是否有清理记录
          if (!area.last_clean_date) {
            return 'overdue'; // 没有任何清理记录
          }

          var _cleanDate2 = new Date(area.last_clean_date);
          if (!(_cleanDate2 >= weekStart && _cleanDate2 <= weekEnd)) {
            return 'overdue'; // 上周没有清理记录
          }
        }

        // 默认为待清理状态
        return 'pending';
      } else if (area.type === 'public') {
        // 公共责任区：根据排班计算状态
        if (area.scheduled_day !== null) {
          if (area.last_clean_date) {
            var _cleanDate3 = new Date(area.last_clean_date);
            if (_cleanDate3 >= weekStart && _cleanDate3 <= weekEnd) {
              return 'completed';
            }
          }

          // 公共责任区状态逻辑：严格按排班日的0点到23:59分
          var beijingNow = this.toBeijingTime(now);
          var scheduledDay = area.scheduled_day === 0 ? 0 : area.scheduled_day; // 0=周日, 1=周一, ..., 6=周六

          // 计算本周排班日的开始和结束时间
          var beijingWeekStart = this.getWeekStart(beijingNow);
          var scheduledDate = new Date(beijingWeekStart);
          if (scheduledDay === 0) {
            // 周日：本周的最后一天
            scheduledDate.setDate(beijingWeekStart.getDate() + 6);
          } else {
            // 周一到周六：相应调整天数
            scheduledDate.setDate(beijingWeekStart.getDate() + scheduledDay - 1);
          }
          var scheduledStart = new Date(scheduledDate);
          scheduledStart.setHours(0, 0, 0, 0); // 排班日的0点

          var scheduledEnd = new Date(scheduledDate);
          scheduledEnd.setHours(23, 59, 59, 999); // 排班日的23:59:59

          var currentTime = beijingNow.getTime();

          // 如果当前时间在排班日内（0点到23:59分），可以清理
          if (currentTime >= scheduledStart.getTime() && currentTime <= scheduledEnd.getTime()) {
            return 'pending';
          }
          // 如果当前时间超过了排班日的23:59分，则逾期
          else if (currentTime > scheduledEnd.getTime()) {
            return 'overdue';
          }
          // 如果当前时间还未到排班日的0点，显示为"未到时间"
          else {
            return 'scheduled';
          }
        }
        return 'pending';
      }
      return 'pending';
    },
    // 获取任务图标
    getTaskIcon: function getTaskIcon(status) {
      var iconMap = {
        'completed': 'checkmarkempty',
        'pending_rectification': 'refreshempty',
        'pending_assignment': 'refreshempty',
        // 待分配，使用刷新图标
        'pending_review': 'eye',
        'overdue': 'closeempty',
        'rejected': 'closeempty',
        // 整改不达标，使用关闭图标
        'verified': 'checkmarkempty',
        // 整改合格，使用对勾图标
        'approved': 'checkmarkempty',
        // 复查通过，使用对勾图标
        'in_progress': 'gear' // 整改中，使用齿轮图标
      };

      return iconMap[status] || 'help';
    },
    // 获取责任区图标
    getAreaIcon: function getAreaIcon(status) {
      var iconMap = {
        'completed': 'checkmarkempty',
        // 已完成 - 对勾
        'pending': 'minus',
        // 待清理 - 减号
        'overdue': 'closeempty',
        // 已逾期 - 关闭
        'scheduled': 'calendar' // 未到时间 - 日历
      };

      return iconMap[status] || 'gear'; // 默认齿轮图标
    },
    // 转换为北京时间的工具函数
    toBeijingTime: function toBeijingTime(dateInput) {
      if (!dateInput) {
        return null;
      }
      try {
        var inputDate = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
        if (isNaN(inputDate.getTime())) {
          return null;
        }

        // 小程序环境中 toLocaleString 可能不稳定，使用更兼容的方式
        // 手动计算北京时间（UTC+8）
        var utcTime = inputDate.getTime();
        var beijingTime = new Date(utcTime + 8 * 60 * 60 * 1000);

        // 检查计算结果是否有效
        if (isNaN(beijingTime.getTime())) {
          return null;
        }

        // 返回标准的Date对象，直接使用正确的北京时间
        return {
          getFullYear: function getFullYear() {
            return beijingTime.getUTCFullYear();
          },
          getMonth: function getMonth() {
            return beijingTime.getUTCMonth();
          },
          getDate: function getDate() {
            return beijingTime.getUTCDate();
          },
          getDay: function getDay() {
            return beijingTime.getUTCDay();
          },
          getHours: function getHours() {
            return beijingTime.getUTCHours();
          },
          getMinutes: function getMinutes() {
            return beijingTime.getUTCMinutes();
          },
          getSeconds: function getSeconds() {
            return beijingTime.getUTCSeconds();
          },
          getTime: function getTime() {
            return beijingTime.getTime();
          },
          toISOString: function toISOString() {
            return beijingTime.toISOString();
          },
          // 兼容Date对象的比较
          valueOf: function valueOf() {
            return beijingTime.getTime();
          }
        };
      } catch (error) {
        console.error('北京时间转换失败:', error, dateInput);
        return null;
      }
    },
    // 获取周次键值（统一使用北京时间）
    getWeekKey: function getWeekKey(dateString) {
      try {
        if (!dateString) return '';
        var beijingDate = this.toBeijingTime(dateString);
        if (!beijingDate) {
          console.error('无法转换为北京时间:', dateString);
          return '';
        }
        var year = beijingDate.getFullYear();
        var weekNum = this.getWeekNumber(beijingDate);
        if (isNaN(year) || isNaN(weekNum) || year < 1900 || year > 2100) {
          console.error('日期参数异常:', {
            year: year,
            weekNum: weekNum,
            dateString: dateString
          });
          return '';
        }
        var weekKey = "".concat(year, "-W").concat(weekNum.toString().padStart(2, '0'));
        return weekKey;
      } catch (error) {
        console.error('生成周次键值出错:', error, dateString);
        return '';
      }
    },
    // 获取日期对应的周数（ISO 8601标准，周一为一周开始）
    getWeekNumber: function getWeekNumber(dateObj) {
      try {
        if (!dateObj) {
          return 1; // 返回默认值
        }

        // 兼容自定义日期对象和原生Date对象
        var year, month, day, dayOfWeek;
        if (typeof dateObj.getFullYear === 'function') {
          // 自定义日期对象或原生Date对象
          year = dateObj.getFullYear();
          month = dateObj.getMonth(); // 0-11
          day = dateObj.getDate(); // 1-31
          dayOfWeek = dateObj.getDay(); // 0=周日, 1=周一
        } else {
          // 备用：原生Date对象
          var _d = new Date(dateObj);
          if (isNaN(_d.getTime())) {
            return 1; // 返回默认值
          }

          year = _d.getFullYear();
          month = _d.getMonth();
          day = _d.getDate();
          dayOfWeek = _d.getDay();
        }

        // 检查获取的值是否有效
        if (isNaN(year) || isNaN(month) || isNaN(day) || isNaN(dayOfWeek)) {
          return 1;
        }

        // 创建该日期的Date对象用于计算
        var d = new Date(year, month, day, 12, 0, 0, 0);
        if (isNaN(d.getTime())) {
          return 1; // 返回默认值
        }

        // 转换为ISO标准（1=周一, 2=周二, ..., 7=周日）
        var isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;

        // 找到这一周的周四（ISO 8601标准：包含周四的周属于该年）
        var thursday = new Date(d);
        thursday.setDate(d.getDate() + 4 - isoDayOfWeek);
        if (isNaN(thursday.getTime())) {
          return 1; // 返回默认值
        }

        // 获取周四所在的年份
        var thursdayYear = thursday.getFullYear();

        // 计算该年第一个周四
        var firstThursday = new Date(thursdayYear, 0, 4);
        var firstThursdayDayOfWeek = firstThursday.getDay();
        var firstThursdayIsoDay = firstThursdayDayOfWeek === 0 ? 7 : firstThursdayDayOfWeek;

        // 调整到第一个周四
        firstThursday.setDate(4 + (4 - firstThursdayIsoDay));
        if (isNaN(firstThursday.getTime())) {
          return 1; // 返回默认值
        }

        // 计算周数
        var weekNum = Math.floor((thursday - firstThursday) / (7 * 24 * 60 * 60 * 1000)) + 1;
        return isNaN(weekNum) ? 1 : weekNum;
      } catch (error) {
        console.error('计算周数出错:', error, dateObj);
        return 1; // 返回默认值
      }
    },
    // 获取一周的开始日期（周一 00:00:00）- 与getWeekNumber保持一致
    getWeekStart: function getWeekStart(dateObj) {
      if (!dateObj) {
        return null;
      }
      try {
        // 兼容自定义日期对象和原生Date对象
        var year, month, day, dayOfWeek;
        if (typeof dateObj.getFullYear === 'function') {
          // 自定义日期对象或原生Date对象
          year = dateObj.getFullYear();
          month = dateObj.getMonth(); // 0-11
          day = dateObj.getDate(); // 1-31
          dayOfWeek = dateObj.getDay(); // 0=周日, 1=周一
        } else {
          // 备用：原生Date对象
          var d = new Date(dateObj);
          if (isNaN(d.getTime())) {
            return null;
          }
          year = d.getFullYear();
          month = d.getMonth();
          day = d.getDate();
          dayOfWeek = d.getDay();
        }

        // 检查获取的值是否有效
        if (isNaN(year) || isNaN(month) || isNaN(day) || isNaN(dayOfWeek)) {
          return null;
        }
        var isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek; // 转换为ISO标准

        // 计算周一
        var monday = new Date(year, month, day - isoDayOfWeek + 1, 0, 0, 0, 0);
        if (isNaN(monday.getTime())) {
          return null;
        }
        return monday;
      } catch (error) {
        console.error('计算周开始时间出错:', error, dateObj);
        return null;
      }
    },
    // 获取一周的结束日期（周日 23:59:59）- 与getWeekNumber保持一致  
    getWeekEnd: function getWeekEnd(dateObj) {
      if (!dateObj) {
        return null;
      }
      try {
        // 兼容自定义日期对象和原生Date对象
        var year, month, day, dayOfWeek;
        if (typeof dateObj.getFullYear === 'function') {
          // 自定义日期对象或原生Date对象
          year = dateObj.getFullYear();
          month = dateObj.getMonth(); // 0-11
          day = dateObj.getDate(); // 1-31
          dayOfWeek = dateObj.getDay(); // 0=周日, 1=周一
        } else {
          // 备用：原生Date对象
          var d = new Date(dateObj);
          if (isNaN(d.getTime())) {
            return null;
          }
          year = d.getFullYear();
          month = d.getMonth();
          day = d.getDate();
          dayOfWeek = d.getDay();
        }

        // 检查获取的值是否有效
        if (isNaN(year) || isNaN(month) || isNaN(day) || isNaN(dayOfWeek)) {
          return null;
        }
        var isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek; // 转换为ISO标准

        // 计算周日
        var sunday = new Date(year, month, day - isoDayOfWeek + 7, 23, 59, 59, 999);
        if (isNaN(sunday.getTime())) {
          return null;
        }
        return sunday;
      } catch (error) {
        console.error('计算周结束时间出错:', error, dateObj);
        return null;
      }
    },
    // 格式化日期显示（只显示日期）
    formatDate: function formatDate(dateString) {
      if (!dateString) return '--';
      try {
        // 兼容多种日期格式
        var date;
        if (typeof dateString === 'string') {
          // ISO格式直接解析
          if (dateString.includes('T') || dateString.includes('Z')) {
            date = new Date(dateString);
          } else {
            // 其他格式尝试替换处理
            date = new Date(dateString.replace(/-/g, '/'));
          }
        } else {
          date = new Date(dateString);
        }

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          return '--';
        }
        return "".concat(date.getMonth() + 1, "\u6708").concat(date.getDate(), "\u65E5");
      } catch (error) {
        return '--';
      }
    },
    // 格式化日期时间显示
    formatDateTime: function formatDateTime(dateString) {
      if (!dateString) return '--';
      try {
        // 兼容多种日期格式
        var date;
        if (typeof dateString === 'string') {
          // ISO格式直接解析
          if (dateString.includes('T') || dateString.includes('Z')) {
            date = new Date(dateString);
          } else {
            // 其他格式尝试替换处理
            date = new Date(dateString.replace(/-/g, '/'));
          }
        } else {
          date = new Date(dateString);
        }

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          return '--';
        }
        return "".concat(date.getMonth() + 1, "\u6708").concat(date.getDate(), "\u65E5 ").concat(date.getHours().toString().padStart(2, '0'), ":").concat(date.getMinutes().toString().padStart(2, '0'));
      } catch (error) {
        return '--';
      }
    },
    getStatusTitle: function getStatusTitle() {
      var _this9 = this;
      if (this.selectedQuickFilter === 'week') {
        return '本周清理状态';
      } else if (this.dateFilterMode === 'quick' && this.selectedQuickFilter !== 'custom') {
        var option = this.quickDateOptions.find(function (opt) {
          return opt.value === _this9.selectedQuickFilter;
        });
        return option ? "".concat(option.label, "\u6574\u6539\u8BB0\u5F55\u7EDF\u8BA1") : '整改记录统计';
      } else if (this.customDateRange.startDate && this.customDateRange.endDate) {
        return '自定义时间段整改记录统计';
      }
      return '清理状态';
    },
    getCurrentTimeRange: function getCurrentTimeRange() {
      var _this10 = this;
      if (this.selectedTimeFilter === 'custom' && this.dateFilterMode === 'range') {
        return this.getDateRangeText();
      } else {
        var option = this.quickDateOptions.find(function (opt) {
          return opt.value === _this10.selectedQuickFilter;
        });
        return option ? option.label : '本周';
      }
    },
    // 获取周标题
    getWeekTitle: function getWeekTitle(weekKey) {
      // 解析weekKey，例如 '2025-W03' -> 2025年第3周
      var match = weekKey.match(/(\d{4})-W(\d{2})/);
      if (!match) return weekKey;
      var year = parseInt(match[1]);
      var weekNum = parseInt(match[2]);
      var beijingNow = this.toBeijingTime(new Date());
      var currentYear = beijingNow.getFullYear();
      var currentWeek = this.getCurrentWeekNumber();

      // 计算该周的日期范围
      var weekRange = this.getWeekRangeByWeekKey(weekKey);

      // 判断是否为当前周
      if (year === currentYear && weekNum === currentWeek) {
        return "\u672C\u5468 (".concat(weekRange.start, "-").concat(weekRange.end, ")");
      } else if (year === currentYear && weekNum === currentWeek - 1) {
        return "\u4E0A\u5468 (".concat(weekRange.start, "-").concat(weekRange.end, ")");
      } else if (year === currentYear && weekNum === currentWeek - 2) {
        return "\u524D\u5468 (".concat(weekRange.start, "-").concat(weekRange.end, ")");
      } else if (year === currentYear) {
        // 计算周数差
        var weekDiff = currentWeek - weekNum;
        if (weekDiff > 0 && weekDiff <= 4) {
          return "".concat(weekDiff, "\u5468\u524D (").concat(weekRange.start, "-").concat(weekRange.end, ")");
        } else if (weekDiff > 4) {
          return "".concat(year, "\u5E74").concat(weekRange.start, "-").concat(weekRange.end);
        } else {
          // 未来的周
          return "\u7B2C".concat(weekNum, "\u5468 (").concat(weekRange.start, "-").concat(weekRange.end, ")");
        }
      } else {
        return "".concat(year, "\u5E74").concat(weekRange.start, "-").concat(weekRange.end);
      }
    },
    // 根据weekKey获取该周的日期范围
    getWeekRangeByWeekKey: function getWeekRangeByWeekKey(weekKey) {
      try {
        if (!weekKey) {
          return {
            start: '',
            end: ''
          };
        }
        var match = weekKey.match(/(\d{4})-W(\d{2})/);
        if (!match) return {
          start: '',
          end: ''
        };
        var year = parseInt(match[1]);
        var weekNum = parseInt(match[2]);
        if (isNaN(year) || isNaN(weekNum) || year < 1900 || year > 2100 || weekNum < 1 || weekNum > 53) {
          return {
            start: '',
            end: ''
          };
        }

        // 计算该年第一个周四
        var firstThursday = new Date(year, 0, 4);
        if (isNaN(firstThursday.getTime())) {
          return {
            start: '',
            end: ''
          };
        }
        var firstThursdayDayOfWeek = firstThursday.getDay();
        var firstThursdayIsoDay = firstThursdayDayOfWeek === 0 ? 7 : firstThursdayDayOfWeek;

        // 调整到第一个周四
        firstThursday.setDate(4 + (4 - firstThursdayIsoDay));

        // 计算目标周的周四
        var targetThursday = new Date(firstThursday);
        targetThursday.setDate(firstThursday.getDate() + (weekNum - 1) * 7);
        if (isNaN(targetThursday.getTime())) {
          return {
            start: '',
            end: ''
          };
        }

        // 计算该周的周一（开始）
        var monday = new Date(targetThursday);
        monday.setDate(targetThursday.getDate() - 3); // 周四往前3天是周一

        // 计算该周的周日（结束）
        var sunday = new Date(targetThursday);
        sunday.setDate(targetThursday.getDate() + 3); // 周四往后3天是周日

        if (isNaN(monday.getTime()) || isNaN(sunday.getTime())) {
          return {
            start: '',
            end: ''
          };
        }
        return {
          start: "".concat(monday.getMonth() + 1, "\u6708").concat(monday.getDate(), "\u65E5"),
          end: "".concat(sunday.getMonth() + 1, "\u6708").concat(sunday.getDate(), "\u65E5"),
          monday: monday,
          sunday: sunday
        };
      } catch (error) {
        console.error('计算周次日期范围出错:', error, weekKey);
        return {
          start: '',
          end: ''
        };
      }
    },
    // 获取当前是一年中的第几周（北京时间）
    getCurrentWeekNumber: function getCurrentWeekNumber() {
      try {
        var now = new Date();
        var beijingNow = this.toBeijingTime(now);
        if (!beijingNow) {
          console.error('无法获取北京时间');
          return 1; // 返回默认值
        }

        return this.getWeekNumber(beijingNow);
      } catch (error) {
        console.error('获取当前周数出错:', error);
        return 1; // 返回默认值
      }
    },
    // 计算整改任务的实际状态（考虑时间逾期）
    calculateRectificationStatus: function calculateRectificationStatus(task) {
      var now = new Date();

      // 如果任务已经完成，状态不变
      if (task.status === 'completed') {
        return 'completed';
      }

      // 检查是否有deadline字段进行逾期判断
      if (task.deadline) {
        var deadline = new Date(task.deadline);
        if (!isNaN(deadline.getTime()) && now > deadline) {
          // 超过截止时间且未完成，判定为逾期
          if (task.status === 'pending_rectification' || task.status === 'pending_assignment') {
            return 'overdue';
          }
        }
      }

      // 业务逻辑：整改任务应该在创建当周内完成，超过当周就算逾期
      if (task.created_at) {
        var beijingCreatedDate = this.toBeijingTime(task.created_at);
        var beijingNow = this.toBeijingTime(now);
        if (beijingCreatedDate && beijingNow) {
          // 计算创建日期所在的周（基于北京时间）
          var createdWeekStart = this.getWeekStart(beijingCreatedDate);
          var createdWeekEnd = this.getWeekEnd(beijingCreatedDate);

          // 如果当前北京时间已经超过创建周的结束时间，且任务未完成，判定为逾期
          if (beijingNow.getTime() > createdWeekEnd.getTime() && (task.status === 'pending_rectification' || task.status === 'pending_assignment')) {
            return 'overdue';
          }
        }
      }

      // 返回原始状态
      return task.status;
    },
    // 智能设置默认展开的周（优先本周，没有则展开最新周）
    setDefaultExpandedWeek: function setDefaultExpandedWeek() {
      // 获取当前筛选数据中的所有唯一周次
      var allWeeks = (0, _toConsumableArray2.default)(new Set(this.filteredRectificationTasks.map(function (task) {
        return task.week;
      })));
      if (allWeeks.length > 0) {
        // 获取当前周的周次键
        var now = new Date();
        var beijingNow = this.toBeijingTime(now);
        var currentWeekKey = this.getWeekKey(beijingNow);
        var targetWeek;

        // 智能选择展开的周次：优先本周，没有则选最新周
        if (allWeeks.includes(currentWeekKey)) {
          // 如果筛选结果中包含本周，优先展开本周
          targetWeek = currentWeekKey;
        } else {
          // 如果没有本周数据，按周次排序找到最新的周
          var sortedWeeks = allWeeks.sort(function (a, b) {
            var _a$split$map = a.split('-W').map(function (x) {
                return parseInt(x);
              }),
              _a$split$map2 = (0, _slicedToArray2.default)(_a$split$map, 2),
              yearA = _a$split$map2[0],
              weekA = _a$split$map2[1];
            var _b$split$map = b.split('-W').map(function (x) {
                return parseInt(x);
              }),
              _b$split$map2 = (0, _slicedToArray2.default)(_b$split$map, 2),
              yearB = _b$split$map2[0],
              weekB = _b$split$map2[1];
            if (yearA !== yearB) {
              return yearB - yearA; // 年份降序
            }

            return weekB - weekA; // 同年内周数降序
          });

          targetWeek = sortedWeeks[0];
        }

        // 确保目标周在展开列表中
        if (targetWeek && !this.expandedRectificationWeeks.includes(targetWeek)) {
          this.expandedRectificationWeeks.push(targetWeek);
        }
      }
    },
    // 切换整改记录周展开状态
    toggleRectificationWeek: function toggleRectificationWeek(weekKey) {
      var index = this.expandedRectificationWeeks.indexOf(weekKey);
      if (index > -1) {
        this.expandedRectificationWeeks.splice(index, 1);
      } else {
        this.expandedRectificationWeeks.push(weekKey);
      }
    },
    // 重置展开状态
    resetExpandedState: function resetExpandedState() {
      this.expandedRectificationWeeks = [];
    },
    // 智能重置展开状态（清空后等数据重新加载）
    smartResetExpandedState: function smartResetExpandedState() {
      var _this11 = this;
      this.expandedRectificationWeeks = [];
      // 等待下一个tick和少量延时后设置默认展开，确保数据已更新
      this.$nextTick(function () {
        setTimeout(function () {
          _this11.setDefaultExpandedWeek();
        }, 50);
      });
    },
    getStatusText: function getStatusText(status) {
      var statusMap = {
        'pending': '待清理',
        'pending_review': '待复查',
        'completed': '已完成',
        'pending_rectification': '待整改',
        'pending_assignment': '待分配',
        // 待分配状态
        'overdue': '已逾期',
        'scheduled': '未到时间',
        // 公共责任区未到排班日
        'rejected': '整改不达标',
        'verified': '整改合格',
        'approved': '复查通过',
        'in_progress': '整改中',
        'rectification_completed': '整改已完成'
      };
      return statusMap[status] || '未知';
    },
    // 获取清理记录状态文本
    getCleaningRecordStatusText: function getCleaningRecordStatusText(status) {
      return '已完成'; // 所有清理记录都是已完成状态
    },
    // 格式化描述文本，智能处理换行和长度
    formatDescription: function formatDescription(description) {
      if (!description) return '';

      // 智能清理文本
      var formatted = description.replace(/\n/g, ' ') // 将换行符替换为空格
      .replace(/\r/g, ' ') // 将回车符替换为空格
      .replace(/\s+/g, ' ') // 将多个连续空格合并为一个
      .trim(); // 去除首尾空格

      // 限制长度，避免显示过长导致换行
      if (formatted.length > 16) {
        formatted = formatted.substring(0, 16) + '...';
      }
      return formatted;
    },
    // 处理清理记录更新事件
    handleRecordUpdated: function handleRecordUpdated(data) {
      // 通过事件已经收到更新通知，进行静默刷新
      this.silentRefreshData();
    },
    // 静默刷新数据（不显示加载状态）
    silentRefreshData: function silentRefreshData() {
      var _this12 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                if (!_this12.loading) {
                  _context8.next = 2;
                  break;
                }
                return _context8.abrupt("return");
              case 2:
                _context8.prev = 2;
                // 清理所有缓存
                _this12.currentWeekCache = null;
                _this12.areasCache = null;

                // 静默重新加载数据，不显示loading状态
                _context8.next = 7;
                return Promise.all([_this12.loadUserAssignments(), _this12.loadUserRectificationTasks(), _this12.loadMyCleaningRecords()]);
              case 7:
                // 数据加载完成后，确保第一个分组展开
                _this12.$nextTick(function () {
                  _this12.setDefaultExpandedWeek();
                });

                // 清除刷新标记，避免用户返回页面时再次显示loading
                _this12.needsRefresh = false;
                _context8.next = 13;
                break;
              case 11:
                _context8.prev = 11;
                _context8.t0 = _context8["catch"](2);
              case 13:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8, null, [[2, 11]]);
      }))();
    },
    getIconColor: function getIconColor(status) {
      var colorMap = {
        'completed': '#34C759',
        'pending': '#FF9500',
        'overdue': '#FF3B30',
        'pending_review': '#007AFF',
        'pending_rectification': '#FF6B35',
        'pending_assignment': '#FF9500',
        // 待分配，使用橙色
        'scheduled': '#8E8E93',
        // 未到时间，使用灰色
        'rejected': '#FF3B30',
        // 整改不达标，使用红色
        'verified': '#34C759',
        // 整改合格，使用绿色
        'approved': '#34C759',
        // 复查通过，使用绿色
        'in_progress': '#007AFF',
        // 整改中，使用蓝色
        'rectification_completed': '#FF9500' // 整改已完成，使用橙色
      };

      return colorMap[status] || '#8E8E93';
    },
    // 处理逾期状态的通用方法
    handleOverdueStatus: function handleOverdueStatus(area) {
      var isPublic = area.type === 'public';
      var content = isPublic ? '排班清理时间已过，无法再进行清理操作。请等待下周重新分配。' : '本周清理时间已过，无法再进行清理操作。请等待下周重新分配。';
      uni.showModal({
        title: '清理任务已逾期',
        content: content,
        showCancel: false,
        confirmText: '知道了'
      });
      return true; // 返回true表示已处理逾期状态
    },
    openAreaDetail: function openAreaDetail(area) {
      if (area.status === 'overdue') {
        this.handleOverdueStatus(area);
        return;
      }
      if (area.status === 'pending') {
        // 跳转到清理页面，由事件监听机制处理数据更新，无需手动刷新
        uni.navigateTo({
          url: "/pages/6s_pkg/cleaning-upload?areaId=".concat(area._id || area.id, "&type=fixed")
        });
      } else {
        // 跳转到详情页面不需要刷新数据
        uni.navigateTo({
          url: "/pages/6s_pkg/area-detail?areaId=".concat(area._id || area.id, "&type=fixed")
        });
      }
    },
    openPublicAreaDetail: function openPublicAreaDetail(area) {
      if (area.status === 'overdue') {
        this.handleOverdueStatus(area);
        return;
      }
      if (area.status === 'scheduled') {
        // 未到排班日，不允许清理
        var weekDays = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
        var scheduledDay = area.scheduled_day === 0 ? 7 : area.scheduled_day;
        uni.showModal({
          title: '未到清理时间',
          content: "\u8BE5\u516C\u5171\u8D23\u4EFB\u533A\u5B89\u6392\u5728".concat(weekDays[scheduledDay], "\u8FDB\u884C\u6E05\u7406\uFF0C\u5F53\u524D\u65F6\u95F4\u65E0\u9700\u6E05\u7406\u3002"),
          showCancel: false,
          confirmText: '知道了'
        });
        return;
      }
      if (area.status === 'pending') {
        // 到了排班日，可以进行清理，由事件监听机制处理数据更新，无需手动刷新
        uni.navigateTo({
          url: "/pages/6s_pkg/cleaning-upload?areaId=".concat(area._id || area.id, "&type=public")
        });
      } else {
        // 跳转到详情页面不需要刷新数据
        uni.navigateTo({
          url: "/pages/6s_pkg/area-detail?areaId=".concat(area._id || area.id, "&type=public")
        });
      }
    },
    openRectificationDetail: function openRectificationDetail(task) {
      // 检查任务ID是否有效
      var taskId = task.id || task._id || task.issue_id || task.task_id;
      if (!taskId) {
        uni.showToast({
          title: '任务数据异常',
          icon: 'error'
        });
        return;
      }
      if (task.status === 'overdue') {
        uni.showModal({
          title: '整改任务已逾期',
          content: '该整改任务已逾期，记录已锁定',
          showCancel: false,
          confirmText: '知道了'
        });
        return;
      }
      if (task.status === 'pending_rectification') {
        // 待整改任务，直接跳转到整改上传页面，由事件监听机制处理数据更新，无需手动刷新
        uni.navigateTo({
          url: "/pages/6s_pkg/rectification-upload?taskId=".concat(taskId, "&areaType=").concat(task.areaType)
        });
      } else if (task.status === 'pending_review') {
        // 待复查任务，员工只能查看详情，不需要刷新数据
        uni.navigateTo({
          url: "/pages/6s_pkg/rectification-detail?taskId=".concat(taskId)
        });
      } else {
        // 已完成任务，跳转到整改详情页面，不需要刷新数据
        uni.navigateTo({
          url: "/pages/6s_pkg/rectification-detail?taskId=".concat(taskId)
        });
      }
    },
    // 打开清理记录详情
    openCleaningRecordDetail: function openCleaningRecordDetail(record) {
      // 将整个 record 对象转换为 JSON 字符串进行传递
      var recordStr = encodeURIComponent(JSON.stringify(record));
      uni.navigateTo({
        url: "/pages/6s_pkg/area-detail?record=".concat(recordStr)
      });
    },
    // ======== 日期选择器方法 ========
    showTimeSelector: function showTimeSelector() {
      // 初始化日期范围
      this.initializeDateRange();
      this.$refs.timePopup.open();
    },
    closeDatePicker: function closeDatePicker() {
      this.$refs.timePopup.close();
    },
    // 初始化日期范围
    initializeDateRange: function initializeDateRange() {
      if (!this.customDateRange.startDate) {
        var now = new Date();
        // 根据当前快捷选择初始化日期范围
        var range = this.getQuickDateRange(this.selectedQuickFilter);
        this.customDateRange.startDate = this.formatDateForPicker(range.start);
        this.customDateRange.endDate = this.formatDateForPicker(range.end);
      }

      // 初始化日历组件的日期
      if (!this.calendarDate) {
        this.calendarDate = this.formatDateForPicker(new Date());
      }
    },
    // 切换到范围选择模式
    switchToRangeMode: function switchToRangeMode() {
      this.dateFilterMode = 'range';
    },
    // 切换到快捷选择模式  
    switchToQuickMode: function switchToQuickMode() {
      this.dateFilterMode = 'quick';
    },
    // 快捷日期选择
    selectQuickDateOption: function selectQuickDateOption(option) {
      var _this13 = this;
      this.selectedQuickFilter = option.value;
      if (option.value === 'custom') {
        this.switchToRangeMode();
      } else {
        // 快捷选择时，智能重置展开状态
        this.smartResetExpandedState();

        // 快捷选择立即生效，关闭弹窗
        this.closeDatePicker();

        // 重新加载数据以匹配新的筛选条件
        this.loading = true;
        clearTimeout(this.refreshTimer);
        this.refreshTimer = setTimeout( /*#__PURE__*/(0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
          return _regenerator.default.wrap(function _callee9$(_context9) {
            while (1) {
              switch (_context9.prev = _context9.next) {
                case 0:
                  _context9.prev = 0;
                  _context9.next = 3;
                  return _this13.loadMyAreas();
                case 3:
                  // 数据加载完成后，确保第一个分组展开
                  _this13.$nextTick(function () {
                    _this13.setDefaultExpandedWeek();
                  });
                case 4:
                  _context9.prev = 4;
                  _this13.loading = false;
                  return _context9.finish(4);
                case 7:
                case "end":
                  return _context9.stop();
              }
            }
          }, _callee9, null, [[0,, 4, 7]]);
        })), 100);
      }
    },
    // 获取当前日期范围
    getCurrentDateRange: function getCurrentDateRange() {
      if (this.dateFilterMode === 'quick' && this.selectedQuickFilter !== 'custom') {
        return this.getQuickDateRange(this.selectedQuickFilter);
      } else if (this.customDateRange.startDate && this.customDateRange.endDate) {
        return {
          start: new Date(this.customDateRange.startDate),
          end: new Date(this.customDateRange.endDate)
        };
      }
      return null;
    },
    // 获取快捷日期的范围
    getQuickDateRange: function getQuickDateRange(quickValue) {
      var now = new Date();
      var today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      switch (quickValue) {
        case 'week':
          // 本周：周一到周日
          var startOfWeek = new Date(today);
          var dayOfWeek = startOfWeek.getDay();
          var daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
          startOfWeek.setDate(startOfWeek.getDate() - daysToMonday);
          var endOfWeek = new Date(startOfWeek);
          endOfWeek.setDate(endOfWeek.getDate() + 6);
          endOfWeek.setHours(23, 59, 59, 999);
          return {
            start: startOfWeek,
            end: endOfWeek
          };
        case 'last_week':
          // 上周：上周一到上周日
          var lastWeekStart = new Date(today);
          var currentDayOfWeek = lastWeekStart.getDay();
          var daysToLastMonday = currentDayOfWeek === 0 ? 13 : currentDayOfWeek + 6;
          lastWeekStart.setDate(lastWeekStart.getDate() - daysToLastMonday);
          var lastWeekEnd = new Date(lastWeekStart);
          lastWeekEnd.setDate(lastWeekEnd.getDate() + 6);
          lastWeekEnd.setHours(23, 59, 59, 999);
          return {
            start: lastWeekStart,
            end: lastWeekEnd
          };
        case 'month':
          // 本月：1号到月底
          var startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
          var endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
          endOfMonth.setHours(23, 59, 59, 999);
          return {
            start: startOfMonth,
            end: endOfMonth
          };
        default:
          // 默认返回本周
          return this.getQuickDateRange('week');
      }
    },
    // 格式化日期用于picker
    formatDateForPicker: function formatDateForPicker(date) {
      if (!date) return '';
      var d = new Date(date);
      var year = d.getFullYear();
      var month = String(d.getMonth() + 1).padStart(2, '0');
      var day = String(d.getDate()).padStart(2, '0');
      return "".concat(year, "-").concat(month, "-").concat(day);
    },
    // 获取日期范围显示文本
    getDateRangeText: function getDateRangeText() {
      var _this14 = this;
      if (this.dateFilterMode === 'quick') {
        var option = this.quickDateOptions.find(function (opt) {
          return opt.value === _this14.selectedQuickFilter;
        });
        return option ? option.label : '本周';
      } else {
        var startText = this.getStartDateText();
        var endText = this.getEndDateText();
        if (startText === '请选择' || endText === '请选择') {
          return '选择日期范围';
        }
        return "".concat(startText, " - ").concat(endText);
      }
    },
    // 获取开始日期显示文本  
    getStartDateText: function getStartDateText() {
      if (!this.customDateRange.startDate) return '请选择开始日期';
      return this.formatDisplayDate(new Date(this.customDateRange.startDate));
    },
    // 获取结束日期显示文本
    getEndDateText: function getEndDateText() {
      if (!this.customDateRange.endDate) return '请选择结束日期';
      return this.formatDisplayDate(new Date(this.customDateRange.endDate));
    },
    // 格式化显示日期
    formatDisplayDate: function formatDisplayDate(date) {
      var d = new Date(date);
      var month = d.getMonth() + 1;
      var day = d.getDate();
      return "".concat(month, "\u6708").concat(day, "\u65E5");
    },
    // 日历组件事件处理
    onCalendarChange: function onCalendarChange(event) {
      var _this15 = this;
      // 防止重复处理
      clearTimeout(this.calendarChangeTimer);
      this.calendarChangeTimer = setTimeout(function () {
        // 处理日期范围选择
        if (event.range) {
          if (event.range.before && event.range.after) {
            // 选择了完整的日期范围
            _this15.customDateRange.startDate = event.range.before;
            _this15.customDateRange.endDate = event.range.after;

            // 立即生效：关闭弹窗并重新加载数据
            _this15.selectedTimeFilter = 'custom';
            _this15.smartResetExpandedState();
            _this15.closeDatePicker();

            // 重新加载数据
            _this15.loading = true;
            clearTimeout(_this15.refreshTimer);
            _this15.refreshTimer = setTimeout( /*#__PURE__*/(0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10() {
              return _regenerator.default.wrap(function _callee10$(_context10) {
                while (1) {
                  switch (_context10.prev = _context10.next) {
                    case 0:
                      _context10.prev = 0;
                      _context10.next = 3;
                      return _this15.loadMyAreas();
                    case 3:
                      // 数据加载完成后，确保第一个分组展开
                      _this15.$nextTick(function () {
                        _this15.setDefaultExpandedWeek();
                      });
                    case 4:
                      _context10.prev = 4;
                      _this15.loading = false;
                      return _context10.finish(4);
                    case 7:
                    case "end":
                      return _context10.stop();
                  }
                }
              }, _callee10, null, [[0,, 4, 7]]);
            })), 100);
            uni.showToast({
              title: '日期范围选择完成',
              icon: 'success',
              duration: 1500
            });
          }
        } else if (event.fulldate) {
          // 单个日期选择
          if (!_this15.customDateRange.startDate || _this15.customDateRange.endDate) {
            // 选择开始日期或重新选择
            _this15.customDateRange.startDate = event.fulldate;
            _this15.customDateRange.endDate = '';
          } else {
            // 选择结束日期
            _this15.customDateRange.endDate = event.fulldate;

            // 确保开始日期不晚于结束日期
            if (new Date(_this15.customDateRange.startDate) > new Date(_this15.customDateRange.endDate)) {
              var temp = _this15.customDateRange.startDate;
              _this15.customDateRange.startDate = _this15.customDateRange.endDate;
              _this15.customDateRange.endDate = temp;
            }

            // 选择完成后立即生效：关闭弹窗并重新加载数据
            _this15.selectedTimeFilter = 'custom';
            _this15.smartResetExpandedState();
            _this15.closeDatePicker();

            // 重新加载数据
            _this15.loading = true;
            clearTimeout(_this15.refreshTimer);
            _this15.refreshTimer = setTimeout( /*#__PURE__*/(0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee11() {
              return _regenerator.default.wrap(function _callee11$(_context11) {
                while (1) {
                  switch (_context11.prev = _context11.next) {
                    case 0:
                      _context11.prev = 0;
                      _context11.next = 3;
                      return _this15.loadMyAreas();
                    case 3:
                      // 数据加载完成后，确保第一个分组展开
                      _this15.$nextTick(function () {
                        _this15.setDefaultExpandedWeek();
                      });
                    case 4:
                      _context11.prev = 4;
                      _this15.loading = false;
                      return _context11.finish(4);
                    case 7:
                    case "end":
                      return _context11.stop();
                  }
                }
              }, _callee11, null, [[0,, 4, 7]]);
            })), 100);
            uni.showToast({
              title: '日期范围选择完成',
              icon: 'success',
              duration: 1500
            });
          }
        }
      }, 100);
    },
    // 月份切换事件
    onMonthSwitch: function onMonthSwitch(event) {
      // 安全地处理月份切换事件
      if (event && event.current && event.current.fulldate) {
        this.calendarDate = event.current.fulldate;
      } else if (event && event.year && event.month) {
        // 如果没有fulldate，手动构造日期
        var year = event.year;
        var month = String(event.month).padStart(2, '0');
        this.calendarDate = "".concat(year, "-").concat(month, "-01");
      }
    },
    // 格式化选中的日期显示
    formatSelectedDate: function formatSelectedDate(dateStr) {
      if (!dateStr) return '';
      var date = new Date(dateStr);
      var month = date.getMonth() + 1;
      var day = date.getDate();
      var dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      var dayName = dayNames[date.getDay()];
      return "".concat(month, "\u6708").concat(day, "\u65E5 ").concat(dayName);
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 506:
/*!***************************************************************************************************!*\
  !*** D:/Xwzc/pages/6s_pkg/my-areas.vue?vue&type=style&index=0&id=4c3d3ebe&lang=scss&scoped=true& ***!
  \***************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_my_areas_vue_vue_type_style_index_0_id_4c3d3ebe_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my-areas.vue?vue&type=style&index=0&id=4c3d3ebe&lang=scss&scoped=true& */ 507);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_my_areas_vue_vue_type_style_index_0_id_4c3d3ebe_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_my_areas_vue_vue_type_style_index_0_id_4c3d3ebe_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_my_areas_vue_vue_type_style_index_0_id_4c3d3ebe_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_my_areas_vue_vue_type_style_index_0_id_4c3d3ebe_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_my_areas_vue_vue_type_style_index_0_id_4c3d3ebe_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 507:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/6s_pkg/my-areas.vue?vue&type=style&index=0&id=4c3d3ebe&lang=scss&scoped=true& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[500,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/6s_pkg/my-areas.js.map