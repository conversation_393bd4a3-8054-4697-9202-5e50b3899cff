{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/patrol_pkg/point/detail.vue?fb97", "webpack:///D:/Xwzc/pages/patrol_pkg/point/detail.vue?8de2", "webpack:///D:/Xwzc/pages/patrol_pkg/point/detail.vue?b336", "webpack:///D:/Xwzc/pages/patrol_pkg/point/detail.vue?7f55", "uni-app:///pages/patrol_pkg/point/detail.vue", "webpack:///D:/Xwzc/pages/patrol_pkg/point/detail.vue?c771", "webpack:///D:/Xwzc/pages/patrol_pkg/point/detail.vue?1322"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "PEmptyState", "data", "point_id", "pointDetail", "name", "address", "latitude", "longitude", "range", "status", "create_time", "mapScale", "markers", "circles", "relatedRoutes", "computed", "markerData", "id", "title", "iconPath", "width", "height", "anchor", "x", "y", "circleData", "color", "fillColor", "radius", "strokeWidth", "marker", "onLoad", "uni", "icon", "setTimeout", "methods", "getPointDetail", "PatrolApi", "res", "console", "getRelatedRoutes", "relatedRes", "routeList", "point", "updateMapOverlays", "openLocation", "scale", "goToRouteDetail", "url", "handleBack", "handleEdit", "formatDate", "goToQrcode", "itemList", "success"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrEA;AAAA;AAAA;AAAA;AAAgmB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC4GpnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;QACAC;QACAX;QACAC;QACAW;QACAC;QACAC;QACAC;QACAC;UACAC;UACAC;QACA;MACA;IACA;IACA;IACAC;MACA;QACAnB;QACAC;QACAmB;QACAC;QACAC;QACAC;MACA;IACA;IACA;IACAC;MACA;MAEA;QACAb;QACAX;QACAC;QACAW;QACAC;QACAC;QACAC;QACAC;UACAC;UACAC;QACA;MACA;IACA;EACA;EACAO;IACA;IACA;MACA;MACA;MACA;MACA;MACA;IACA;MACAC;QACAd;QACAe;MACA;MACAC;QACAF;MACA;IACA;EACA;EACAG;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAJ;kBACAd;gBACA;gBAAA;gBAAA;gBAAA,OAGAmB;kBACAnC;gBACA;cAAA;gBAFAoC;gBAIA;kBACA;kBACA;;kBAEA;kBACA;gBACA;kBACAN;oBACAd;oBACAe;kBACA;kBACAC;oBACAF;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAO;gBACAP;kBACAd;kBACAe;gBACA;gBACAC;kBACAF;gBACA;cAAA;gBAAA;gBAEAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAQ;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAH;kBACAnC;gBACA;kBACA;gBACA;cAAA;gBAJAuC;gBAAA,MAOAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAKAJ;cAAA;gBAAAC;gBAEA;kBACAI,2BAEA;kBACA;oBACA;oBACA;sBACA;oBACA;;oBAEA;oBACA;sBACA;oBACA;;oBAEA;oBACA;sBACA;sBACA;wBACA;sBACA;;sBAEA;sBACA;wBACA;wBACA,IACAC,8CACAA,4CACAA,sDACA;0BACA;wBACA;sBACA;sBAEA;oBACA;oBAEA,eACA;oBAEA;kBACA;kBAEA,sCACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAJ;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAK;MACA;MACA;IACA;IAEA;IACAC;MACAb;QACA1B;QACAC;QACAH;QACAC;QACAyC;MACA;IACA;IAEA;IACAC;MACAf;QACAgB;MACA;IACA;IAEA;IACAC;MACAjB;IACA;IAEA;IACAkB;MACAlB;QACAgB;MACA;IACA;IAEA;IACAG;MACA;MACA;QACA;QACA;QACA;;QAEA;QACA;MACA;QACAZ;QACA;MACA;IACA;IAEA;IACAa;MAAA;MACA;;MAEA;MACApB;QACAqB;QACAC;UACA;YACA;YACAtB;cACAgB;YACA;UACA;YACA;YACAhB;cACAgB;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChYA;AAAA;AAAA;AAAA;AAA2oC,CAAgB,inCAAG,EAAC,C;;;;;;;;;;;ACA/pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/patrol_pkg/point/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/patrol_pkg/point/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=16a6b586&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/patrol_pkg/point/detail.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=template&id=16a6b586&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = (\n    _vm.pointDetail.location && _vm.pointDetail.location.longitude\n      ? _vm.pointDetail.location.longitude\n      : 0\n  ).toFixed(6)\n  var g1 = (\n    _vm.pointDetail.location && _vm.pointDetail.location.latitude\n      ? _vm.pointDetail.location.latitude\n      : 0\n  ).toFixed(6)\n  var m0 = _vm.formatDate(_vm.pointDetail.create_date)\n  var g2 = _vm.relatedRoutes.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.mapScale = Math.min(_vm.mapScale + 2, 20)\n    }\n    _vm.e1 = function ($event) {\n      _vm.mapScale = Math.max(_vm.mapScale - 2, 5)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        m0: m0,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"point-detail-container\">\n\t\t<!-- 基本信息区域 -->\n\t\t<view class=\"detail-section\">\n\t\t\t<view class=\"section-title\">点位信息</view>\n\t\t\t<view class=\"info-group\">\n\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t<text class=\"info-label\">点位名称</text>\n\t\t\t\t\t<text class=\"info-value\">{{ pointDetail.name }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t<text class=\"info-label\">点位状态</text>\n\t\t\t\t\t<view class=\"status-tag\" :class=\"{ 'status-disabled': pointDetail.status === 0, 'status-active': pointDetail.status === 1 }\">\n\t\t\t\t\t\t{{ pointDetail.status === 1 ? '启用中' : '已停用' }}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t<text class=\"info-label\">点位地址</text>\n\t\t\t\t\t<text class=\"info-value\">{{ pointDetail.location && pointDetail.location.address ? pointDetail.location.address : '暂无地址信息' }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t<text class=\"info-label\">点位范围</text>\n\t\t\t\t\t<text class=\"info-value\">{{ pointDetail.range }}米</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t<text class=\"info-label\">坐标</text>\n\t\t\t\t\t<view class=\"coordinate-info\">\n\t\t\t\t\t\t<text>经度: {{ (pointDetail.location && pointDetail.location.longitude ? pointDetail.location.longitude : 0).toFixed(6) }}</text>\n\t\t\t\t\t\t<text class=\"coord-divider\">|</text>\n\t\t\t\t\t\t<text>纬度: {{ (pointDetail.location && pointDetail.location.latitude ? pointDetail.location.latitude : 0).toFixed(6) }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t<text class=\"info-label\">创建时间</text>\n\t\t\t\t\t<text class=\"info-value\">{{ formatDate(pointDetail.create_date) }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-row\" v-if=\"pointDetail\">\n\t\t\t\t\t<text class=\"info-label\">扫码打卡</text>\n\t\t\t\t\t<view class=\"info-value\">\n\t\t\t\t\t\t<text class=\"qrcode-status\" :class=\"{ 'enabled': pointDetail.qrcode_enabled, 'disabled': !pointDetail.qrcode_enabled }\">\n\t\t\t\t\t\t\t{{ pointDetail.qrcode_enabled ? '已启用' : '未启用' }}\n\t\t\t\t\t\t</text>\n\t\t\t\t\t\t<text class=\"qrcode-required\" v-if=\"pointDetail.qrcode_enabled && pointDetail.qrcode_required\">\n\t\t\t\t\t\t\t(不限距离)\n\t\t\t\t\t\t</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 地图区域 -->\n\t\t<view class=\"detail-section\">\n\t\t\t<view class=\"section-title\">位置信息</view>\n\t\t\t<view class=\"map-container\">\n\t\t\t\t<map \n\t\t\t\t\tclass=\"location-map\" \n\t\t\t\t\t:latitude=\"pointDetail.location && pointDetail.location.latitude ? pointDetail.location.latitude : 39.908823\" \n\t\t\t\t\t:longitude=\"pointDetail.location && pointDetail.location.longitude ? pointDetail.location.longitude : 116.397470\"\n\t\t\t\t\t:markers=\"markers\"\n\t\t\t\t\t:circles=\"circles\"\n\t\t\t\t\t:scale=\"mapScale\"\n\t\t\t\t\tshow-location\n\t\t\t\t></map>\n\t\t\t\t<view class=\"map-tools\">\n\t\t\t\t\t<view class=\"tool-item\" @click=\"mapScale = Math.min(mapScale + 2, 20)\">\n\t\t\t\t\t\t<uni-icons type=\"plus\" size=\"20\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tool-item\" @click=\"mapScale = Math.max(mapScale - 2, 5)\">\n\t\t\t\t\t\t<uni-icons type=\"minus\" size=\"20\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tool-item\" @click=\"openLocation\">\n\t\t\t\t\t\t<uni-icons type=\"paperplane\" size=\"20\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 关联信息区域 -->\n\t\t<view class=\"detail-section\">\n\t\t\t<view class=\"section-title\">已关联线路</view>\n\t\t\t<view class=\"routes-list\" v-if=\"relatedRoutes.length > 0\">\n\t\t\t\t<view class=\"route-item\" v-for=\"route in relatedRoutes\" :key=\"route._id\" @click=\"goToRouteDetail(route._id)\">\n\t\t\t\t\t<view class=\"route-info\">\n\t\t\t\t\t\t<text class=\"route-name\">{{ route.name }}</text>\n\t\t\t\t\t\t<view class=\"status-tag\" :class=\"{ 'status-disabled': route.status === 0, 'status-active': route.status === 1 }\">\n\t\t\t\t\t\t\t{{ route.status === 1 ? '启用中' : '已停用' }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#CCCCCC\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"empty-info\" v-else>\n\t\t\t\t<p-empty-state type=\"data\" text=\"暂无关联线路\" />\n\t\t\t\t<text class=\"empty-tip\">请在编辑线路时添加此点位</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 底部按钮区域 - 集成到内容中 -->\n\t\t<view class=\"action-section\">\n\t\t\t<view class=\"action-buttons\">\n\t\t\t\t<view class=\"btn-cancel\" @click=\"handleBack\">返回</view>\n\t\t\t\t<view class=\"btn-primary\" @click=\"handleEdit\">编辑点位</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport PatrolApi from '@/utils/patrol-api.js';\nimport PEmptyState from '@/components/p-empty-state/p-empty-state.vue';\n\nexport default {\n\tcomponents: {\n\t\tPEmptyState\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tpoint_id: '', // 点位ID\n\t\t\tpointDetail: {\n\t\t\t\tname: '',\n\t\t\t\taddress: '',\n\t\t\t\tlatitude: 39.908823,\n\t\t\t\tlongitude: 116.397470,\n\t\t\t\trange: 10,\n\t\t\t\tstatus: 1,\n\t\t\t\tcreate_time: ''\n\t\t\t},\n\t\t\tmapScale: 16,\n\t\t\tmarkers: [],\n\t\t\tcircles: [],\n\t\t\trelatedRoutes: []\n\t\t}\n\t},\n\tcomputed: {\n\t\t// 计算标记点数据\n\t\tmarkerData() {\n\t\t\treturn {\n\t\t\t\tid: 1,\n\t\t\t\tlatitude: this.pointDetail.location && this.pointDetail.location.latitude ? this.pointDetail.location.latitude : 39.908823,\n\t\t\t\tlongitude: this.pointDetail.location && this.pointDetail.location.longitude ? this.pointDetail.location.longitude : 116.397470,\n\t\t\t\ttitle: this.pointDetail.name || '点位标记',\n\t\t\t\ticonPath: '/static/map/marker.png',\n\t\t\t\twidth: 32,\n\t\t\t\theight: 32,\n\t\t\t\tanchor: {\n\t\t\t\t\tx: 0.5,\n\t\t\t\t\ty: 1\n\t\t\t\t}\n\t\t\t};\n\t\t},\n\t\t// 计算圆形区域数据\n\t\tcircleData() {\n\t\t\treturn {\n\t\t\t\tlatitude: this.pointDetail.location && this.pointDetail.location.latitude ? this.pointDetail.location.latitude : 39.908823,\n\t\t\t\tlongitude: this.pointDetail.location && this.pointDetail.location.longitude ? this.pointDetail.location.longitude : 116.397470,\n\t\t\t\tcolor: '#1E5A8D11',\n\t\t\t\tfillColor: '#4CBBCE33',\n\t\t\t\tradius: this.pointDetail.range || 10,\n\t\t\t\tstrokeWidth: 2\n\t\t\t};\n\t\t},\n\t\t// 计算标记数据\n\t\tmarker() {\n\t\t\tif (!this.pointDetail) return null;\n\t\t\t\n\t\t\treturn {\n\t\t\t\tid: 1,\n\t\t\t\tlatitude: this.pointDetail.latitude,\n\t\t\t\tlongitude: this.pointDetail.longitude,\n\t\t\t\ttitle: this.pointDetail.name || '点位标记',\n\t\t\t\ticonPath: '/static/map/marker.png',\n\t\t\t\twidth: 32,\n\t\t\t\theight: 32,\n\t\t\t\tanchor: {\n\t\t\t\t\tx: 0.5,\n\t\t\t\t\ty: 1\n\t\t\t\t}\n\t\t\t};\n\t\t}\n\t},\n\tonLoad(options) {\n\t\t// 获取点位ID\n\t\tif (options.id) {\n\t\t\tthis.point_id = options.id;\n\t\t\t// 加载点位详情\n\t\t\tthis.getPointDetail();\n\t\t\t// 加载关联线路\n\t\t\tthis.getRelatedRoutes();\n\t\t} else {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '参数错误',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.navigateBack();\n\t\t\t}, 1500);\n\t\t}\n\t},\n\tmethods: {\n\t\t// 获取点位详情\n\t\tasync getPointDetail() {\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '加载中...'\n\t\t\t});\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst res = await PatrolApi.callPointFunction('getPointDetail', {\n\t\t\t\t\tpoint_id: this.point_id\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\t// 更新点位数据\n\t\t\t\t\tthis.pointDetail = res.data;\n\t\t\t\t\t\n\t\t\t\t\t// 更新地图覆盖物\n\t\t\t\t\tthis.updateMapOverlays();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.message || '获取点位详情失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t}, 1500);\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('获取点位详情错误', e);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '获取点位详情出错',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t}, 1500);\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 获取关联线路\n\t\tasync getRelatedRoutes() {\n\t\t\ttry {\n\t\t\t\t// 尝试使用专门的API获取点位关联的线路\n\t\t\t\tconst relatedRes = await PatrolApi.callPointFunction('getPointRelatedRoutes', {\n\t\t\t\t\tpoint_id: this.point_id\n\t\t\t\t}).catch(e => {\n\t\t\t\t\treturn null;\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 如果专用API成功返回数据\n\t\t\t\tif (relatedRes && relatedRes.code === 0 && relatedRes.data) {\n\t\t\t\t\tthis.relatedRoutes = relatedRes.data;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果没有专用API，回退到旧方法\n\t\t\t\tconst res = await PatrolApi.callRouteFunction('getRouteList', {});\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data && res.data.list) {\n\t\t\t\t\tconst routeList = res.data.list;\n\t\t\t\t\t\n\t\t\t\t\t// 筛选出包含当前点位的线路\n\t\t\t\t\tthis.relatedRoutes = routeList.filter(route => {\n\t\t\t\t\t\t// 检查是否有points属性\n\t\t\t\t\t\tif (!route.points) {\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 直接检查字符串数组\n\t\t\t\t\t\tif (Array.isArray(route.points) && route.points.includes(this.point_id)) {\n\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 遍历对象数组\n\t\t\t\t\t\tconst hasPoint = route.points.some(point => {\n\t\t\t\t\t\t\t// 字符串直接比较\n\t\t\t\t\t\t\tif (typeof point === 'string') {\n\t\t\t\t\t\t\t\treturn point === this.point_id;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 对象比较\n\t\t\t\t\t\t\tif (point && typeof point === 'object') {\n\t\t\t\t\t\t\t\t// 检查常见ID属性\n\t\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t\t(point._id && point._id === this.point_id) || \n\t\t\t\t\t\t\t\t\t(point.id && point.id === this.point_id) || \n\t\t\t\t\t\t\t\t\t(point.point_id && point.point_id === this.point_id)\n\t\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (hasPoint) {\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\treturn hasPoint;\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (this.relatedRoutes.length > 0) {\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('获取关联线路错误', e);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 更新地图覆盖物\n\t\tupdateMapOverlays() {\n\t\t\tthis.markers = [this.markerData];\n\t\t\tthis.circles = [this.circleData];\n\t\t},\n\t\t\n\t\t// 打开导航\n\t\topenLocation() {\n\t\t\tuni.openLocation({\n\t\t\t\tlatitude: this.pointDetail.location && this.pointDetail.location.latitude ? this.pointDetail.location.latitude : 39.908823,\n\t\t\t\tlongitude: this.pointDetail.location && this.pointDetail.location.longitude ? this.pointDetail.location.longitude : 116.397470,\n\t\t\t\tname: this.pointDetail.name,\n\t\t\t\taddress: this.pointDetail.location && this.pointDetail.location.address ? this.pointDetail.location.address : '',\n\t\t\t\tscale: 18\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 查看线路详情\n\t\tgoToRouteDetail(routeId) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/patrol_pkg/route/detail?id=${routeId}`\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 返回\n\t\thandleBack() {\n\t\t\tuni.navigateBack();\n\t\t},\n\t\t\n\t\t// 编辑\n\t\thandleEdit() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/patrol_pkg/point/edit?id=${this.point_id}`\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 格式化日期\n\t\tformatDate(dateStr) {\n\t\t\tif (!dateStr) return '未知';\n\t\t\ttry {\n\t\t\t\t// 将日期字符串转换为Date对象\n\t\t\t\tconst date = new Date(dateStr);\n\t\t\t\tif (isNaN(date.getTime())) return '无效日期';\n\t\t\t\t\n\t\t\t\t// 格式化为 YYYY-MM-DD HH:MM\n\t\t\t\treturn `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('日期格式化错误', e);\n\t\t\t\treturn '格式错误';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 跳转到二维码管理页面\n\t\tgoToQrcode() {\n\t\t\tif (!this.pointDetail || !this.pointDetail._id) return;\n\t\t\t\n\t\t\t// 显示操作菜单，提供两个选项\n\t\t\tuni.showActionSheet({\n\t\t\t\titemList: ['二维码管理', '测试二维码生成器'],\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.tapIndex === 0) {\n\t\t\t\t\t\t// 进入二维码管理页面\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: `/pages/patrol_pkg/point/qrcode?id=${this.pointDetail._id}`\n\t\t\t\t\t\t});\n\t\t\t\t\t} else if (res.tapIndex === 1) {\n\t\t\t\t\t\t// 进入二维码测试页面\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: `/pages/patrol_pkg/point/qrcode-test?id=${this.pointDetail._id}`\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\">\n/* 主色调变量 */\n$primary-color: #1677FF; // 支付宝蓝\n$primary-light: #E7F1FF;\n$primary-dark: #0E5FD8;\n\n$success-color: #07C160;\n$warning-color: #FFA300;\n$danger-color: #FF3B30;\n$info-color: #8F959E;\n\n$text-primary: #2C3E50;\n$text-secondary: #666666;\n$text-tertiary: #999999;\n$border-color: #EAEAEA;\n$background: #F5F5F5;\n\n$radius-sm: 6rpx;\n$radius-md: 12rpx;\n$radius-lg: 16rpx;\n$radius-xl: 24rpx;\n$radius-full: 999rpx;\n\n$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n$shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\n$shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);\n\n.point-detail-container {\n\tpadding-bottom: 20rpx;\n\tbackground-color: #F5F5F5;\n\tmin-height: 100vh;\n}\n\n.detail-section {\n\tbackground-color: #FFFFFF;\n\tmargin: 20rpx;\n\tborder-radius: $radius-lg;\n\tpadding: 20rpx 30rpx;\n\tbox-shadow: $shadow-sm;\n}\n\n.section-title {\n\tfont-size: 30rpx;\n\tfont-weight: 600;\n\tcolor: $text-primary;\n\tpadding-bottom: 20rpx;\n\tmargin-bottom: 10rpx;\n\tposition: relative;\n\tpadding-left: 20rpx;\n\t\n\t&:before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 6rpx;\n\t\theight: 28rpx;\n\t\twidth: 6rpx;\n\t\tbackground-color: $primary-color;\n\t\tborder-radius: $radius-sm;\n\t}\n}\n\n.info-group {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.info-row {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n\t\n\t&:last-child {\n\t\tmargin-bottom: 0;\n\t}\n}\n\n.info-label {\n\twidth: 160rpx;\n\tfont-size: 28rpx;\n\tcolor: $text-secondary;\n}\n\n.info-value {\n\tflex: 1;\n\tfont-size: 28rpx;\n\tcolor: $text-primary;\n\tword-break: break-all;\n}\n\n.status-tag {\n\tfont-size: 24rpx;\n\tpadding: 4rpx 16rpx;\n\tborder-radius: $radius-full;\n\tbackground-color: rgba(7, 193, 96, 0.1);\n\tcolor: $success-color;\n\t\n\t&.status-disabled {\n\t\tbackground-color: rgba(143, 149, 158, 0.1);\n\t\tcolor: $info-color;\n\t}\n}\n\n.coordinate-info {\n\tfont-size: 28rpx;\n\tcolor: $text-primary;\n\tdisplay: flex;\n\tflex-wrap: wrap;\n}\n\n.coord-divider {\n\tmargin: 0 16rpx;\n\tcolor: $text-tertiary;\n}\n\n.map-container {\n\tposition: relative;\n\twidth: 100%;\n\theight: 400rpx;\n\tborder-radius: $radius-lg;\n\toverflow: hidden;\n\tmargin-top: 10rpx;\n}\n\n.location-map {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.map-tools {\n\tposition: absolute;\n\tright: 20rpx;\n\ttop: 20rpx;\n\tbackground-color: #FFFFFF;\n\tborder-radius: $radius-lg;\n\tbox-shadow: $shadow-md;\n\toverflow: hidden;\n\tpadding: 10rpx 0;\n}\n\n.tool-item {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\twidth: 70rpx;\n\theight: 70rpx;\n\t\n\t&:active {\n\t\tbackground-color: $primary-light;\n\t}\n}\n\n.routes-list {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.route-item {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 20rpx 0;\n\tborder-bottom: 1px solid $border-color;\n\t\n\t&:last-child {\n\t\tborder-bottom: none;\n\t}\n\t\n\t&:active {\n\t\tbackground-color: $primary-light;\n\t}\n}\n\n.route-info {\n\tdisplay: flex;\n\talign-items: center;\n\tflex: 1;\n}\n\n.route-name {\n\tfont-size: 28rpx;\n\tcolor: $text-primary;\n\tmargin-right: 20rpx;\n}\n\n.empty-info {\n\ttext-align: center;\n\tpadding: 40rpx 0;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\t\n\t.empty-tip {\n\t\tfont-size: 26rpx;\n\t\tcolor: $text-tertiary;\n\t\tmargin-top: 20rpx;\n\t\tpadding: 8rpx 24rpx;\n\t\tbackground-color: $primary-light;\n\t\tborder-radius: $radius-full;\n\t\topacity: 0.8;\n\t}\n}\n\n/* 新增按钮区样式 */\n.action-section {\n\tmargin: 20rpx;\n\tpadding: 0;\n}\n\n.action-buttons {\n\tdisplay: flex;\n\tgap: 20rpx;\n\tbackground-color: #FFFFFF;\n\tpadding: 20rpx 30rpx;\n\tborder-radius: 12rpx;\n\tbox-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.05);\n}\n\n.btn-cancel, .btn-primary {\n\tflex: 1;\n\theight: 80rpx;\n\tborder-radius: 40rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\ttransition: all 0.3s ease;\n\t\n\t&:active {\n\t\ttransform: scale(0.95);\n\t\topacity: 0.9;\n\t}\n}\n\n.btn-cancel {\n\tbackground-color: #F5F5F5;\n\tcolor: #666666;\n\tborder: 1rpx solid #EEEEEE;\n}\n\n.btn-primary {\n\tbackground-color: #1677FF;\n\tcolor: #FFFFFF;\n}\n\n.qrcode-item {\n\tmargin-top: 20rpx;\n}\n\n.qrcode-status {\n\tpadding: 4rpx 16rpx;\n\tborder-radius: 99rpx;\n\tfont-size: 24rpx;\n\t\n\t&.enabled {\n\t\tbackground-color: rgba(7, 193, 96, 0.1);\n\t\tcolor: #07C160;\n\t}\n\t\n\t&.disabled {\n\t\tbackground-color: rgba(153, 153, 153, 0.1);\n\t\tcolor: #999999;\n\t}\n}\n\n.qrcode-required {\n\tmargin-left: 10rpx;\n\tfont-size: 24rpx;\n\tcolor: #FF9500;\n}\n\n.qrcode-button {\n\tbackground-color: #E6F7EF;\n\tborder: 1px solid #07C160;\n\tcolor: #07C160;\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775847573\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}