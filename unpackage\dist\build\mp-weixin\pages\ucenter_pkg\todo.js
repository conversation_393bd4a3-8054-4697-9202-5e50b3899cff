(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/ucenter_pkg/todo"],{"0e02":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return o}));var o={uniList:function(){return n.e("uni_modules/uni-list/components/uni-list/uni-list").then(n.bind(null,"e711"))},uniListItem:function(){return n.e("uni_modules/uni-list/components/uni-list-item/uni-list-item").then(n.bind(null,"4e47"))},uniIcons:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(n.bind(null,"6ddf"))},uniLoadMore:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-load-more/components/uni-load-more/uni-load-more")]).then(n.bind(null,"3282"))},pEmptyState:function(){return n.e("components/p-empty-state/p-empty-state").then(n.bind(null,"9b76"))}},i=function(){var e=this,t=e.$createElement,n=(e._self._c,e.todoList.length),o=n>0?e.__map(e.todoList,(function(t,n){var o=e.__get_orig(t),i=e.getItemIcon(t),r=e.getItemColor(t),a=e.getItemTypeText(t),s=!e.expandedItems[n]&&e.isTextOverflow(t.description),u=e.formatDate(t.createTime||t.assignedTime),c=e.getItemUserText(t);return{$orig:o,m0:i,m1:r,m2:a,m3:s,m4:u,m5:c}})):null;e.$mp.data=Object.assign({},{$root:{g0:n,l0:o}})},r=[]},"3a16":function(e,t,n){"use strict";(function(e,o){var i=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(n("7eb4")),a=i(n("7ca3")),s=i(n("af34")),u=i(n("34cf")),c=i(n("ee10"));n("423e");function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function d(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"===typeof e)return f(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return f(e,t)}(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var o=0,i=function(){};return{s:i,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,r=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw r}}}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var h=e.database(),p={components:{PEmptyState:function(){n.e("components/p-empty-state/p-empty-state").then(function(){return resolve(n("9b76"))}.bind(null,n)).catch(n.oe)}},data:function(){return{todoList:[],userRole:[],currentUserId:null,page:1,pageSize:10,total:0,reviewCount:0,taskCount:0,currentTab:"all",loadMoreStatus:"more",expandedItems:{},isLoading:!1,isRefreshing:!1,lastRequestTime:0,cachedQueries:new Map,hasInitialized:!1,roleConfig:{reviewRoles:["supervisor","PM","GM","admin"],responsibleRoles:["logistics","dispatch","Integrated","operator","technician","mechanic","responsible","admin"]},isPageVisible:!0,lastUpdateCheck:0}},computed:{showLoadMore:function(){return this.todoList.length>0&&!this.isLoading},allowedReviewRoles:function(){return this.roleConfig.reviewRoles},hasResponsiblePermission:function(){var e=this;return this.userRole.some((function(t){return e.roleConfig.responsibleRoles.includes(t)}))}},onLoad:function(){var e=this;return(0,c.default)(r.default.mark((function t(){return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.initRoleConfig(),t.next=3,e.getUserInfo();case 3:o.$on("cross-device-update-detected",e.handleCrossDeviceUpdate),o.$on("feedback-updated",e.handleFeedbackUpdated),o.$on("task-completed",e.handleTaskCompleted);case 6:case"end":return t.stop()}}),t)})))()},methods:{initRoleConfig:function(){console.log("✅ 初始化默认角色配置")},getUserInfo:function(){var e=this;return(0,c.default)(r.default.mark((function t(){var n,o;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,h.collection("uni-id-users").where("'_id' == $cloudEnv_uid").field("role, _id").get();case 3:if(n=t.sent,o=n.result,!(o.data&&o.data.length>0)){t.next=12;break}return e.userRole=o.data[0].role||[],e.currentUserId=o.data[0]._id,t.next=10,e.getTodoList();case 10:t.next=13;break;case 12:e.showError("获取用户信息失败","请重新登录");case 13:t.next=19;break;case 15:t.prev=15,t.t0=t["catch"](0),console.error("❌ 获取用户信息失败:",t.t0),e.showError("获取用户信息失败","网络异常，请稍后重试");case 19:case"end":return t.stop()}}),t,null,[[0,15]])})))()},showError:function(e,t){o.showModal({title:e,content:t,showCancel:!1,confirmText:"确定"})},switchTab:function(e){this.currentTab!==e&&(this.currentTab=e,this.page=1,this.todoList=[],this.clearCache(),this.getTodoList(!0))},checkUserPermission:function(){var e=this,t=this.userRole.some((function(t){return e.allowedReviewRoles.includes(t)})),n=this.hasResponsiblePermission;return!(!t&&!n)||(o.showModal({title:"权限不足",content:"您没有查看待办的权限，请联系管理员",showCancel:!1,confirmText:"返回",success:function(){o.navigateBack({fail:function(){o.switchTab({url:"/pages/index/index"})}})}}),!1)},debounceGetTodoList:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=Date.now();t-this.lastRequestTime<300&&!e||(this.lastRequestTime=t,this.getTodoList(e))},generateCacheKey:function(e,t){var n=this.userRole.sort().join("_");return"".concat(this.currentTab,"_").concat(n,"_").concat(e,"_").concat(t)},clearCache:function(){this.cachedQueries.clear()},clearExpiredCache:function(){var e,t=Date.now(),n=d(this.cachedQueries.entries());try{for(n.s();!(e=n.n()).done;){var o=(0,u.default)(e.value,2),i=o[0],r=o[1];t-r.timestamp>3e5&&this.cachedQueries.delete(i)}}catch(a){n.e(a)}finally{n.f()}},getTodoList:function(){var t=arguments,n=this;return(0,c.default)(r.default.mark((function o(){var i,a,s,u,c,l;return r.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(i=t.length>0&&void 0!==t[0]&&t[0],n.checkUserPermission()){o.next=3;break}return o.abrupt("return");case 3:if(!n.isLoading){o.next=5;break}return o.abrupt("return");case 5:if(n.isLoading=!0,i&&(n.page=1,n.todoList=[],n.isRefreshing=!0),n.loadMoreStatus="loading",o.prev=8,a=n.generateCacheKey(n.page,n.pageSize),i||!n.cachedQueries.has(a)){o.next=14;break}return s=n.cachedQueries.get(a),n.applyDataFromCache(s),o.abrupt("return");case 14:return o.next=16,e.callFunction({name:"feedback-workflow",data:{action:"get_todo_list",currentTab:n.currentTab,page:n.page,pageSize:n.pageSize}});case 16:if(u=o.sent,!u.result||0!==u.result.code){o.next=22;break}c=u.result.data,n.updateDataFromResponse(c,a),o.next=23;break;case 22:throw new Error((null===(l=u.result)||void 0===l?void 0:l.message)||"获取待办列表失败");case 23:o.next=29;break;case 25:o.prev=25,o.t0=o["catch"](8),console.error("❌ 获取待办列表失败:",o.t0),n.handleGetTodoListError(o.t0);case 29:return o.prev=29,n.finalizeTodoListLoading(),o.finish(29);case 32:case"end":return o.stop()}}),o,null,[[8,25,29,32]])})))()},applyDataFromCache:function(e){this.total=e.total,this.reviewCount=e.reviewCount||0,this.taskCount=e.taskCount||0,1===this.page?this.todoList=e.list:this.todoList=[].concat((0,s.default)(this.todoList),(0,s.default)(e.list)),this.updateLoadMoreStatus(),this.finalizeTodoListLoading()},updateDataFromResponse:function(e,t){this.total=e.total||0,this.reviewCount=e.reviewCount||0,this.taskCount=e.taskCount||0;var n=e.list||[];this.cachedQueries.set(t,{total:this.total,reviewCount:this.reviewCount,taskCount:this.taskCount,list:n,timestamp:Date.now()}),1===this.page?this.todoList=n:this.todoList=[].concat((0,s.default)(this.todoList),(0,s.default)(n)),this.updateLoadMoreStatus()},updateLoadMoreStatus:function(){this.loadMoreStatus=this.todoList.length>=this.total?"noMore":"more"},handleGetTodoListError:function(e){var t=e.message||"获取待办列表失败";o.showToast({title:t,icon:"none",duration:2e3}),this.loadMoreStatus="more"},finalizeTodoListLoading:function(){this.isLoading=!1,this.hasInitialized=!0,this.isRefreshing&&(o.stopPullDownRefresh(),this.isRefreshing=!1)},handleCrossDeviceUpdate:function(e){if(e.silent){var t=this.shouldRefreshOnCrossDeviceUpdate(e);t&&(console.log("待办页面收到跨设备更新通知，静默刷新数据"),this.silentRefresh())}},shouldRefreshOnCrossDeviceUpdate:function(e){var t,n;if(!this.isPageVisible)return console.log("待办页面不可见，跳过跨设备更新"),!1;var o=Date.now()-(this.lastRequestTime||0);if(o<1e4)return console.log("待办页面距离上次刷新时间太短，跳过跨设备更新"),!1;if(null!==(t=e.updateTypes)&&void 0!==t&&t.includes("feedback_deleted"))return console.log("待办页面检测到删除操作，需要立即刷新"),!0;if(e.updateTypes){var i=["workflow_status_changed","feedback_submitted","feedback_deleted"],r=e.updateTypes.some((function(e){return i.includes(e)}));if(r)return console.log("待办页面检测到相关更新类型，需要刷新:",e.updateTypes),!0}return(null===(n=e.feedbackIds)||void 0===n?void 0:n.length)>0?(console.log("待办页面检测到反馈更新，需要刷新:",e.feedbackIds),!0):e.updateCount>0&&(console.log("待办页面检测到更新记录，采用保守策略刷新:",e.updateCount),!0)},silentRefresh:function(){var e=this;return(0,c.default)(r.default.mark((function t(){return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.clearCache(),t.next=4,e.getTodoList(!0);case 4:t.next=9;break;case 6:t.prev=6,t.t0=t["catch"](0),console.error("❌ 待办页面静默刷新失败:",t.t0);case 9:case"end":return t.stop()}}),t,null,[[0,6]])})))()},refreshList:function(){this.clearExpiredCache(),this.getTodoList(!0)},handleFeedbackUpdated:function(){var e=this;setTimeout((function(){e.silentRefresh()}),1e3)},handleTaskCompleted:function(){var e=this;setTimeout((function(){e.silentRefresh()}),1e3)},goToDetail:function(e){"task"===e.type?o.navigateTo({url:"/pages/ucenter_pkg/complete-task?id="+e._id,fail:function(e){o.showToast({title:"页面跳转失败",icon:"none"})}}):o.navigateTo({url:"/pages/feedback_pkg/examine?id="+e._id,fail:function(e){o.showToast({title:"页面跳转失败",icon:"none"})}})},getItemIcon:function(e){return"task"===e.type?"gear-filled":"checkbox"},getItemColor:function(e){return"task"===e.type?"#FF6B35":"#3688FF"},getItemTypeText:function(e){if("task"===e.type)return"我的任务";switch(e.workflowStatus){case"pending_supervisor":return"主管审核";case"pending_pm":return"副厂长审核";case"pending_gm":return"厂长审核";case"gm_approved_pending_assign":return"待指派负责人";case"completed_by_responsible":return"待厂长确认";default:return"审核待办"}},getItemUserText:function(e){return"task"===e.type?e.assignedByName||"未知分配人":e.name||"未知用户"},formatDate:function(e){if(!e)return"未知时间";var t=new Date(e),n=t.getFullYear(),o=String(t.getMonth()+1).padStart(2,"0"),i=String(t.getDate()).padStart(2,"0");return"".concat(n,"-").concat(o,"-").concat(i)},toggleExpand:function(e){var t=this;if(this.expandedItems[e])this.$set(this.expandedItems,e,!1);else{var n=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){(0,a.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},this.expandedItems);Object.keys(n).forEach((function(e){t.$set(t.expandedItems,e,!1)})),this.$set(this.expandedItems,e,!0)}},isTextOverflow:function(e){if(!e)return!1;return e.length>50}},onPullDownRefresh:function(){this.isRefreshing=!0,this.refreshList()},onReachBottom:function(){"more"!==this.loadMoreStatus||this.isLoading||(this.page++,this.getTodoList())},onShow:function(){var e=this;this.isPageVisible=!0;var t=Date.now(),n=t-this.lastRequestTime,o=n>18e5&&this.todoList.length>0&&!this.isLoading&&this.hasInitialized;o&&setTimeout((function(){!e.isLoading&&Date.now()-e.lastRequestTime>18e5&&(console.log("Todo页面自动刷新"),e.refreshList())}),2e3)},onHide:function(){this.isPageVisible=!1,this.clearExpiredCache()},beforeDestroy:function(){o.$off("cross-device-update-detected",this.handleCrossDeviceUpdate),o.$off("feedback-updated",this.handleFeedbackUpdated),o.$off("task-completed",this.handleTaskCompleted)}};t.default=p}).call(this,n("861b")["uniCloud"],n("df3c")["default"])},"3af0":function(e,t,n){"use strict";n.r(t);var o=n("0e02"),i=n("6f4c");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);n("8dd8");var a=n("828b"),s=Object(a["a"])(i["default"],o["b"],o["c"],!1,null,"0d79523c",null,!1,o["a"],void 0);t["default"]=s.exports},"4a9a":function(e,t,n){},"6f4c":function(e,t,n){"use strict";n.r(t);var o=n("3a16"),i=n.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);t["default"]=i.a},"8dd8":function(e,t,n){"use strict";var o=n("4a9a"),i=n.n(o);i.a},a80c:function(e,t,n){"use strict";(function(e,t){var o=n("47a9");n("357b"),n("861b");o(n("3240"));var i=o(n("3af0"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(i.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])}},[["a80c","common/runtime","common/vendor"]]]);