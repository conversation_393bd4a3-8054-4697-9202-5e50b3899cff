require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/record-detail"],{"2ac5":function(e,t,r){"use strict";r.r(t);var n=r("6d3a"),i=r.n(n);for(var a in n)["default"].indexOf(a)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(a);t["default"]=i.a},"47e0":function(e,t,r){"use strict";r.r(t);var n=r("a0d8"),i=r("2ac5");for(var a in i)["default"].indexOf(a)<0&&function(e){r.d(t,e,(function(){return i[e]}))}(a);r("5a5a");var o=r("828b"),s=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"6217a4bb",null,!1,n["a"],void 0);t["default"]=s.exports},"5a5a":function(e,t,r){"use strict";var n=r("8158"),i=r.n(n);i.a},"6d3a":function(e,t,r){"use strict";(function(e){var n=r("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i,a=n(r("7eb4")),o=n(r("7ca3")),s=n(r("3b2d")),c=n(r("ee10")),d=r("882c");function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach((function(t){(0,o.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var p={name:"RecordDetail",data:function(){return{recordId:"",recordType:"cleaning",loading:!1,loadError:!1,dataLoaded:!1,editPermission:{canEdit:!1,message:"",checking:!0},issueDescExpanded:!1,isHistoricalRecord:!1,recordWeek:null,cleaningDate:null,processCache:null,basicDataLoaded:!1,recordInfo:{id:"",areaId:"",areaName:"",operatorName:"",operationTime:"",operationTimestamp:"",status:"",photos:[],notes:"",weekPeriod:"",rating:0,result:"",hasIssues:!1,issueType:"",issueDescription:"",summary:"",remediationTask:null,history:[]}}},computed:{showEditSection:function(){return"cleaning"===this.recordType&&!this.editPermission.checking&&(this.editPermission.canEdit||this.editPermission.message)},statusBadgeClass:function(){return{"status-completed":"completed"===this.recordInfo.status||"verified"===this.recordInfo.status||"reviewed"===this.recordInfo.status,"status-pending":"pending"===this.recordInfo.status,"status-pending-rectification":"pending_rectification"===this.recordInfo.status||"rectification_completed"===this.recordInfo.status,"status-cancelled":"cancelled"===this.recordInfo.status}},resultBadgeClass:function(){return"passed"===this.recordInfo.result?"badge-passed":"issues"===this.recordInfo.result?"badge-issues":""},taskBadgeClass:function(){if(!this.recordInfo.remediationTask)return"";var e=this.recordInfo.remediationTask.status;return"pending_assignment"===e?"task-badge-pending":"pending_rectification"===e?"task-badge-pending-rectification":"in_progress"===e||"pending_review"===e?"task-badge-pending":"verified"===e?"task-badge-completed":"rejected"===e||"overdue"===e?"task-badge-missed":"task-badge-pending"},taskStatusValueClass:function(){if(!this.recordInfo.remediationTask)return"";var e=this.recordInfo.remediationTask.status;return"not_cleaned"===e?"task-status-not-cleaned":"pending"===e?"task-status-pending":"pending_rectification"===e?"task-status-pending-rectification":"completed"===e?"task-status-completed":"missed"===e?"task-status-missed":""}},onLoad:function(t){t.id&&t.type&&(this.recordId=t.id,this.recordType=t.type,this.isHistoricalRecord="true"===t.isHistorical||!1,this.recordWeek=t.week||null,this.cleaningDate=t.cleaningDate||null,this.loadRecordDetail()),e.$on("cleaningRecordUpdated",this.handleRecordUpdated)},onUnload:function(){e.$off("cleaningRecordUpdated",this.handleRecordUpdated)},onShow:function(){},methods:(i={getPageTitle:function(){return"cleaning"===this.recordType?"清理记录详情":"检查记录详情"},loadRecordDetail:function(){var e=this;return(0,c.default)(a.default.mark((function t(){return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,e.loadError=!1,e.loading=!0,e.initProcessCache(),"cleaning"!==e.recordType){t.next=9;break}return t.next=7,e.loadCleaningRecordDetailOptimized();case 7:t.next=11;break;case 9:return t.next=11,e.loadInspectionRecordDetailOptimized();case 11:e.dataLoaded=!0,t.next=17;break;case 14:t.prev=14,t.t0=t["catch"](0),e.loadError=!0;case 17:return t.prev=17,e.loading=!1,t.finish(17);case 20:case"end":return t.stop()}}),t,null,[[0,14,17,20]])})))()},initProcessCache:function(){this.processCache||(this.processCache={categoryMap:{equipment:"设备问题",cleanliness:"清洁问题",organization:"整理问题",safety:"安全问题",environment:"环境问题",standardization:"标识问题",other:"其他问题"},statusMaps:{record:{completed:"已完成",pending_rectification:"待整改",rectification_completed:"整改已提交",verified:"已确认",reviewed:"已审核",pending:"待处理",cancelled:"已取消"},result:{passed:"检查通过",issues:"发现问题"},task:{pending_assignment:"待分配",pending_rectification:"待整改",in_progress:"整改中",pending_review:"待复查",completed:"已完成",verified:"已确认",rejected:"已拒绝",overdue:"已逾期"},taskIcon:{pending_assignment:"info",pending_rectification:"flag",in_progress:"reload",pending_review:"eye",completed:"checkmarkempty",verified:"checkmarkempty",rejected:"close",overdue:"close"}},formatters:{dateTime:this.createDateTimeFormatter(),date:this.createDateFormatter()}})},createDateTimeFormatter:function(){return function(e){if(!e||"null"===e||"undefined"===e)return"--";try{var t=new Date(e);return isNaN(t.getTime())?"--":"".concat(t.getMonth()+1,"月").concat(t.getDate(),"日 ").concat(t.getHours().toString().padStart(2,"0"),":").concat(t.getMinutes().toString().padStart(2,"0"))}catch(r){return"--"}}},createDateFormatter:function(){return function(e){if(!e||"null"===e||"undefined"===e)return"--";try{var t=new Date(e);return isNaN(t.getTime())?"--":"".concat(t.getMonth()+1,"月").concat(t.getDate(),"日")}catch(r){return"--"}}},loadCleaningRecordDetailOptimized:function(){var e=this;return(0,c.default)(a.default.mark((function t(){var r,n,i,o,s;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,(0,d.callCloudFunction)("hygiene-cleaning",{action:"getCleaningRecordDetail",data:{record_id:e.recordId}});case 2:if(r=t.sent,!(r&&r.success&&r.data)){t.next=12;break}n=r.data,i=e.processCache.formatters.dateTime,o=new Date(n.cleaning_date),s=e.calculateWeekPeriodOptimized(o),e.recordInfo={id:n._id,areaId:n.area_id,areaName:n.area_name||"未知责任区",operatorName:n.cleaner_name||n.user_name||"未知",operationTime:i(n.cleaning_date),operationTimestamp:n.cleaning_date,weekPeriod:s,status:"completed",photos:e.processPhotos(n.photos||[]),notes:n.remark||"",history:[{type:"create",title:"提交清理记录",description:"".concat(n.cleaner_name||n.user_name||"用户","提交了清理记录，包含").concat((n.photos||[]).length,"张照片"),time:i(n.cleaning_date)}]},e.checkEditPermissionAsync(),t.next=13;break;case 12:throw new Error("获取清理记录详情失败");case 13:case"end":return t.stop()}}),t)})))()},calculateWeekPeriodOptimized:function(e){var t=e.getFullYear(),r=Math.ceil((e-new Date(t,0,1))/6048e5),n=new Date(e);n.setDate(e.getDate()-e.getDay()+1);var i=new Date(n);return i.setDate(n.getDate()+6),"第".concat(r,"周 (").concat(n.getMonth()+1,"月").concat(n.getDate(),"日-").concat(i.getMonth()+1,"月").concat(i.getDate(),"日)")},processPhotos:function(e){return e.map((function(e){return{url:e.url||e,name:e.name||"清理照片",loadError:!1}}))},checkEditPermissionAsync:function(){var e=this;return(0,c.default)(a.default.mark((function t(){return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.checkEditPermission();case 3:t.next=7;break;case 5:t.prev=5,t.t0=t["catch"](0);case 7:case"end":return t.stop()}}),t,null,[[0,5]])})))()},loadCleaningRecordDetail:function(){var e=this;return(0,c.default)(a.default.mark((function t(){var r,n,i,o,s,c,u;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,(0,d.callCloudFunction)("hygiene-cleaning",{action:"getCleaningRecordDetail",data:{record_id:e.recordId}});case 2:if(r=t.sent,!(r&&r.success&&r.data)){t.next=15;break}return n=r.data,i=new Date(n.cleaning_date),o=e.getWeekNumber(i),s=e.getWeekStart(i),c=e.getWeekEnd(i),u="第".concat(o,"周 (").concat(s.getMonth()+1,"月").concat(s.getDate(),"日-").concat(c.getMonth()+1,"月").concat(c.getDate(),"日)"),e.recordInfo={id:n._id,areaId:n.area_id,areaName:n.area_name||"未知责任区",operatorName:n.cleaner_name||n.user_name||"未知",operationTime:e.formatDateTime(n.cleaning_date),operationTimestamp:n.cleaning_date,weekPeriod:u,status:"completed",photos:(n.photos||[]).map((function(e){return{url:e.url||e,name:e.name||"清理照片",loadError:!1}})),notes:n.remark||"",history:[{type:"create",title:"提交清理记录",description:"".concat(n.cleaner_name||n.user_name||"用户","提交了清理记录，包含").concat((n.photos||[]).length,"张照片"),time:e.formatDateTime(n.cleaning_date)}]},t.next=13,e.checkEditPermission();case 13:t.next=16;break;case 15:throw new Error("获取清理记录详情失败");case 16:case"end":return t.stop()}}),t)})))()},loadInspectionRecordDetailOptimized:function(){var e=this;return(0,c.default)(a.default.mark((function t(){var r,n,i,o,s,c,u,l,p,f,m;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,(0,d.callCloudFunction)("hygiene-inspection",{action:"getInspectionDetail",data:{id:e.recordId}});case 2:if(r=t.sent,!(r&&r.success&&r.data)){t.next=13;break}n=r.data,i=e.processCache.formatters.dateTime,o=e.processCache.categoryMap,s=e.processIssueInfo(n,o),c=s.issueType,u=s.issueDescription,l=e.processRectificationInfo(n,i),p=l.finalRating,f=l.remediationTask,m=e.processReviewInfo(n,f,i),e.recordInfo={id:n._id,areaName:n.area_name||"未知责任区",operatorName:n.inspector_name||"未知",operationTime:i(n.inspection_date),operationTimestamp:n.inspection_date,status:n.status||"completed",rating:p,result:n.has_issues?"issues":"passed",hasIssues:n.has_issues||!1,issueType:c,issueDescription:u,summary:n.summary||"",photos:e.processInspectionPhotos(n.photos||[]),remediationTask:f,reviewInfo:m,history:e.buildHistoryTimelineOptimized(n,f,i)},t.next=14;break;case 13:throw new Error("获取检查记录详情失败");case 14:case"end":return t.stop()}}),t)})))()},processIssueInfo:function(e,t){var r="",n="";if(e.has_issues&&e.issues&&e.issues.length>0){var i=e.issues[0];r=t[i.category]||i.category||"其他问题",n=i.description||""}return{issueType:r,issueDescription:n}},processRectificationInfo:function(e,t){var r=e.overall_rating||e.score||0,n=null;if(e.has_issues&&e.related_rectifications&&e.related_rectifications.length>0){var i=e.related_rectifications.find((function(t){return t.inspection_record_id===e._id||t.area_id===e.area_id&&new Date(t.created_at)>=new Date(e.inspection_date)}));i&&(n={id:i._id,status:i.status,assignee:i.assigned_to_name||"未分配",deadline:t(i.deadline),completionTime:i.submitted_at?t(i.submitted_at):i.completion_date?t(i.completion_date):null,completionNotes:i.completion_description||"",completionPhotos:this.processRectificationPhotos(i.completion_photos||[])})}return{finalRating:r,remediationTask:n}},processReviewInfo:function(e,t,r){var n=null;if(e.has_issues&&e.related_rectifications&&e.related_rectifications.length>0){var i=e.related_rectifications[0];i.review_date&&"verified"===i.status&&(n={hasReview:!0,reviewerName:i.reviewer_name||"未知",reviewTime:r(i.review_date),reviewComments:i.review_comments||"",reviewRating:i.final_rating||i.review_rating||0,reviewResult:i.review_result||"approved",reviewPhotos:this.processRectificationPhotos(i.review_photos||[])})}return n},processInspectionPhotos:function(e){return e.map((function(e){return"string"===typeof e?{url:e,name:"检查照片",loadError:!1}:"object"===(0,s.default)(e)&&null!==e?{url:e.url||e.src||"",name:e.name||e.description||"检查照片",loadError:!1}:{url:"",name:"检查照片",loadError:!0}})).filter((function(e){return e.url}))},processRectificationPhotos:function(e){return Array.isArray(e)?e.map((function(e){return"string"===typeof e?{url:e,name:"整改照片",loadError:!1}:"object"===(0,s.default)(e)&&null!==e?{url:e.url||e.src||"",name:e.description||e.name||"整改照片",loadError:!1}:{url:"",name:"整改照片",loadError:!0}})).filter((function(e){return e.url})):[]},buildHistoryTimelineOptimized:function(e,t,r){var n=this,i=[{type:"create",title:"开始检查",description:"".concat(e.inspector_name||"检查员","开始检查"),time:r(e.inspection_date)}];if(e.has_issues){if(i.push({type:"issue",title:"发现问题",description:"检查中发现问题，需要进行整改",time:r(e.inspection_date)}),t)if(i.push({type:"task",title:"创建整改任务",description:"已分配给 ".concat(t.assignee," 进行整改"),time:r(e.inspection_date)}),e.related_rectifications&&e.related_rectifications.length>0){var a=e.related_rectifications[0];this.buildRectificationHistory(i,a,r)}else t.completionTime&&"--"!==t.completionTime&&this.buildSimpleRectificationHistory(i,t)}else{var o=e.overall_rating>0?"检查完成，评分".concat(e.overall_rating,"分，").concat(this.getRatingDescription(e.overall_rating)):"检查完成，责任区状况良好，无发现问题";i.push({type:"complete",title:"检查通过",description:o,time:r(e.inspection_date)})}return i.sort((function(e,t){var r=n.parseTimeForSort(e.time),i=n.parseTimeForSort(t.time);return r-i}))},buildRectificationHistory:function(e,t,r){if(t.created_at&&e.push({type:"task",title:"开始整改",description:"".concat(t.assigned_to_name||"负责人","开始处理整改任务"),time:r(t.created_at)}),t.submitted_at&&e.push({type:"review",title:"提交整改",description:"整改已完成，提交检查员复查",time:r(t.submitted_at)}),t.review_date){var n="verified"===t.status;e.push({type:n?"complete":"issue",title:n?"复查通过":"需重新整改",description:n?"检查员确认整改合格，问题已解决":"检查员要求重新整改：".concat(t.review_comments||"整改不达标"),time:r(t.review_date)})}"rejected"===t.status&&t.resubmitted_at&&e.push({type:"task",title:"重新整改",description:"根据检查员要求重新进行整改",time:r(t.resubmitted_at)})},buildSimpleRectificationHistory:function(e,t){var r="verified"===t.status?"complete":"rejected"===t.status?"issue":"review",n="verified"===t.status?"整改完成":"rejected"===t.status?"整改被拒绝":"整改提交";e.push({type:r,title:n,description:"verified"===t.status?"整改任务已完成并通过检查员确认":"rejected"===t.status?"整改未通过复查，需要重新整改":"整改任务已完成，等待检查员复查",time:t.completionTime})},parseTimeForSort:function(e){try{var t=e.match(/(\d+)月(\d+)日\s(\d+):(\d+)/);if(t){var r=(new Date).getFullYear(),n=parseInt(t[1]),i=parseInt(t[2]),a=parseInt(t[3]),o=parseInt(t[4]);return new Date(r,n-1,i,a,o)}return new Date}catch(s){return new Date}},loadInspectionRecordDetail:function(){var e=this;return(0,c.default)(a.default.mark((function t(){var r,n,i,o,c,u,l,p,f;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,(0,d.callCloudFunction)("hygiene-inspection",{action:"getInspectionDetail",data:{id:e.recordId}});case 2:if(r=t.sent,!(r&&r.success&&r.data)){t.next=16;break}n=r.data,i="",o="",n.has_issues&&n.issues&&n.issues.length>0&&(c=n.issues[0],u={equipment:"设备问题",cleanliness:"清洁问题",organization:"整理问题",safety:"安全问题",environment:"环境问题",standardization:"标识问题",other:"其他问题"},i=u[c.category]||c.category||"其他问题",o=c.description||""),l=n.overall_rating||n.score||0,p=null,n.has_issues&&n.related_rectifications&&n.related_rectifications.length>0&&(f=n.related_rectifications.find((function(e){return e.inspection_record_id===n._id||e.area_id===n.area_id&&new Date(e.created_at)>=new Date(n.inspection_date)})),f&&(p={id:f._id,status:f.status,assignee:f.assigned_to_name||"未分配",deadline:e.formatDateTime(f.deadline),completionTime:f.submitted_at?e.formatDateTime(f.submitted_at):f.completion_date?e.formatDateTime(f.completion_date):null,completionNotes:f.completion_description||""})),e.recordInfo={id:n._id,areaName:n.area_name||"未知责任区",operatorName:n.inspector_name||"未知",operationTime:e.formatDateTime(n.inspection_date),operationTimestamp:n.inspection_date,status:n.status||"completed",rating:l,result:n.has_issues?"issues":"passed",hasIssues:n.has_issues||!1,issueType:i,issueDescription:o,summary:n.summary||"",photos:(n.photos||[]).map((function(e){return"string"===typeof e?{url:e,name:"检查照片",loadError:!1}:"object"===(0,s.default)(e)&&null!==e?{url:e.url||e.src||"",name:e.name||e.description||"检查照片",loadError:!1}:{url:"",name:"检查照片",loadError:!0}})).filter((function(e){return e.url})),remediationTask:p,history:[{type:"create",title:"开始检查",description:"".concat(n.inspector_name||"检查员","开始检查"),time:e.formatDateTime(n.inspection_date)}]},n.has_issues&&(e.recordInfo.history.push({type:"issue",title:"发现问题",description:o||"检查中发现问题",time:e.formatDateTime(n.inspection_date)}),p&&(e.recordInfo.history.push({type:"task",title:"创建整改任务",description:"已分配给 ".concat(p.assignee,"整改"),time:e.formatDateTime(n.inspection_date)}),p.completionTime&&"--"!==p.completionTime&&"null"!==p.completionTime&&("pending_review"===p.status?e.recordInfo.history.push({type:"complete",title:"整改提交",description:"整改任务已完成并提交复查",time:p.completionTime}):"verified"===p.status?e.recordInfo.history.push({type:"complete",title:"整改完成",description:"整改任务已完成并通过确认",time:p.completionTime}):"rejected"===p.status&&e.recordInfo.history.push({type:"issue",title:"整改被拒绝",description:"整改未通过复查，需要重新整改",time:p.completionTime})))),e.recordInfo.history.sort((function(e,t){var r=function(e){var t=e.match(/(\d+)月(\d+)日\s(\d+):(\d+)/);if(t){var r=(new Date).getFullYear(),n=parseInt(t[1]),i=parseInt(t[2]),a=parseInt(t[3]),o=parseInt(t[4]);return new Date(r,n-1,i,a,o)}return new Date},n=r(e.time),i=r(t.time);return n-i})),t.next=17;break;case 16:throw new Error("获取检查记录详情失败");case 17:case"end":return t.stop()}}),t)})))()},getWeekNumber:function(e){var t=new Date(e.getFullYear(),0,1);return Math.ceil(((e.getTime()-t.getTime())/864e5+t.getDay()+1)/7)},getWeekStart:function(e){var t=new Date(e),r=t.getDay(),n=t.getDate()-r+(0===r?-6:1),i=new Date(t.setDate(n));return i.setHours(0,0,0,0),i},getWeekEnd:function(e){var t=new Date(e),r=t.getDay(),n=t.getDate()-r+(0===r?0:7),i=new Date(t.setDate(n));return i.setHours(23,59,59,999),i},formatDateTime:function(e){if(!e||"null"===e||"undefined"===e)return"--";try{var t=new Date(e);return isNaN(t.getTime())?"--":"".concat(t.getMonth()+1,"月").concat(t.getDate(),"日 ").concat(t.getHours().toString().padStart(2,"0"),":").concat(t.getMinutes().toString().padStart(2,"0"))}catch(r){return"--"}},retryLoad:function(){this.dataLoaded=!1,this.loadError=!1,this.loadRecordDetail()},previewPhoto:function(t){var r=this.recordInfo.photos.map((function(e){return e.url}));e.previewImage({urls:r,current:t})},retryPhotoLoad:function(e){this.$set(this.recordInfo.photos[e],"loadError",!1)},onPhotoError:function(e){this.$set(this.recordInfo.photos[e],"loadError",!0)},checkEditPermission:function(){var e=this;return(0,c.default)(a.default.mark((function t(){var r,n,i,o,s,c,u,p,f,m,h;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("cleaning"===e.recordType){t.next=3;break}return e.editPermission={canEdit:!1,message:"",checking:!1},t.abrupt("return");case 3:if(!e.isHistoricalRecord){t.next=6;break}return e.editPermission={canEdit:!1,message:"历史记录不允许修改",checking:!1},t.abrupt("return");case 6:if(r=e.checkRecordTimeValidity(),r.canEdit){t.next=10;break}return e.editPermission=l(l({},r),{},{checking:!1}),t.abrupt("return");case 10:if(t.prev=10,n=e.recordInfo.areaId,n){t.next=15;break}return e.editPermission={canEdit:!0,message:"",checking:!1},t.abrupt("return");case 15:return i=new Date(e.recordInfo.operationTimestamp),o=!1,t.prev=17,t.next=20,(0,d.callCloudFunction)("hygiene-inspection",{action:"getInspectionRecords",data:{area_id:n,pageSize:50}});case 20:s=t.sent,s&&s.success&&s.data&&(c=s.data.list,Array.isArray(c)&&(u=c.filter((function(e){var t=new Date(e.inspection_date);return t>i})),o=u.length>0)),t.next=28;break;case 24:t.prev=24,t.t0=t["catch"](17),console.warn("检查记录查询失败:",t.t0),o=!0;case 28:if(!o){t.next=31;break}return e.editPermission={canEdit:!1,message:"该清理记录已被检查员检查，不允许修改",checking:!1},t.abrupt("return");case 31:return t.next=33,(0,d.callCloudFunction)("hygiene-rectification",{action:"getRectifications",data:{area_id:n,pageSize:10}});case 33:p=t.sent,f=!1,p&&p.success&&p.data&&p.data.list&&(m=i,h=p.data.list.filter((function(e){var t=new Date(e.created_at||e.createdAt),r=t>m,n=r&&["pending_rectification","pending_assignment","in_progress","pending_review","rectification_completed"].includes(e.status);return n})),f=h.length>0),e.editPermission=f?{canEdit:!1,message:"该区域有进行中的整改任务，暂时无法修改清理记录",checking:!1}:{canEdit:!0,message:"",checking:!1},t.next=42;break;case 39:t.prev=39,t.t1=t["catch"](10),e.editPermission={canEdit:!1,message:"权限检查失败，暂时无法修改",checking:!1};case 42:case"end":return t.stop()}}),t,null,[[10,39],[17,24]])})))()},checkRecordTimeValidity:function(){if(!this.recordInfo.operationTimestamp)return{canEdit:!1,message:"无法确定记录时间，不允许修改"};var e=new Date,t=new Date(this.recordInfo.operationTimestamp),r=this.getWeekStart(e),n=this.getWeekEnd(e);if(t<r||t>n)return{canEdit:!1,message:"只能修改本周的清理记录"};var i=e.getTime()-t.getTime();return i>864e5?{canEdit:!1,message:"记录提交超过24小时，不允许修改"}:{canEdit:!0,message:""}},getStatusText:function(e){var t;return null!==(t=this.processCache)&&void 0!==t&&t.statusMaps||this.initProcessCache(),this.processCache.statusMaps.record[e]||"已完成"},getResultText:function(e){var t;return null!==(t=this.processCache)&&void 0!==t&&t.statusMaps||this.initProcessCache(),this.processCache.statusMaps.result[e]||e},getTaskStatusText:function(e){var t;return e?(null!==(t=this.processCache)&&void 0!==t&&t.statusMaps||this.initProcessCache(),this.processCache.statusMaps.task[e]||e):"未知状态"},getTaskStatusIcon:function(e){var t;return e?(null!==(t=this.processCache)&&void 0!==t&&t.statusMaps||this.initProcessCache(),this.processCache.statusMaps.taskIcon[e]||"info"):"info"},editRecord:function(){var t=this.recordInfo.areaId||this.areaId,r=this.recordInfo.areaType||"fixed";e.navigateTo({url:"/pages/6s_pkg/cleaning-upload?mode=edit&recordId=".concat(this.recordId,"&areaId=").concat(t,"&type=").concat(r)})},getRatingDescription:function(e){var t;if(null!==(t=this.processCache)&&void 0!==t&&t.ratingDescCache||(this.processCache||this.initProcessCache(),this.processCache.ratingDescCache=new Map),this.processCache.ratingDescCache.has(e))return this.processCache.ratingDescCache.get(e);var r="";return 0===e?r="请评分":e<=1?r="较差":e<=2?r="一般":e<=3?r="良好":e<5?r="优秀":5===e&&(r="完美"),this.processCache.ratingDescCache.set(e,r),r},handleRecordUpdated:function(e){e.recordId===this.recordId&&this.loadRecordDetail()},previewRectificationPhoto:function(t){var r=this.recordInfo.remediationTask.completionPhotos.map((function(e){return e.url}));e.previewImage({urls:r,current:t})},previewReviewPhoto:function(t){var r=this.recordInfo.reviewInfo.reviewPhotos.map((function(e){return e.url}));e.previewImage({urls:r,current:t})},handlePhotoError:function(e,t){this.$set(e[t],"loadError",!0)}},(0,o.default)(i,"retryPhotoLoad",(function(e,t){this.$set(e[t],"loadError",!1)})),(0,o.default)(i,"onRectificationPhotoError",(function(e){this.handlePhotoError(this.recordInfo.remediationTask.completionPhotos,e)})),(0,o.default)(i,"onReviewPhotoError",(function(e){this.handlePhotoError(this.recordInfo.reviewInfo.reviewPhotos,e)})),(0,o.default)(i,"retryRectificationPhotoLoad",(function(e){this.retryPhotoLoad(this.recordInfo.remediationTask.completionPhotos,e)})),(0,o.default)(i,"retryReviewPhotoLoad",(function(e){this.retryPhotoLoad(this.recordInfo.reviewInfo.reviewPhotos,e)})),i)};t.default=p}).call(this,r("df3c")["default"])},8158:function(e,t,r){},a0d8:function(e,t,r){"use strict";r.d(t,"b",(function(){return i})),r.d(t,"c",(function(){return a})),r.d(t,"a",(function(){return n}));var n={uniIcons:function(){return Promise.all([r.e("common/vendor"),r.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(r.bind(null,"6ddf"))},pEmptyState:function(){return r.e("components/p-empty-state/p-empty-state").then(r.bind(null,"9b76"))}},i=function(){var e=this,t=e.$createElement,r=(e._self._c,e.getPageTitle()),n=e.loading?null:e.getStatusText(e.recordInfo.status),i=!e.loading&&!e.loadError&&e.dataLoaded&&"inspection"===e.recordType&&e.recordInfo.rating>0&&!e.recordInfo.hasIssues?e.getRatingDescription(e.recordInfo.rating):null,a=e.loading||e.loadError||!e.dataLoaded?null:e.recordInfo.photos.length,o=e.loading||e.loadError||!e.dataLoaded?null:e.recordInfo.photos.length,s=e.loading||e.loadError||!e.dataLoaded||"inspection"!==e.recordType?null:e.getResultText(e.recordInfo.result),c=!e.loading&&!e.loadError&&e.dataLoaded&&"inspection"===e.recordType&&e.recordInfo.hasIssues?!e.issueDescExpanded&&e.recordInfo.issueDescription.length>100:null,d=!e.loading&&!e.loadError&&e.dataLoaded&&"inspection"===e.recordType&&e.recordInfo.hasIssues?e.recordInfo.issueDescription.length:null,u=!e.loading&&!e.loadError&&e.dataLoaded&&"inspection"===e.recordType&&!e.recordInfo.hasIssues&&e.recordInfo.rating>0?e.getRatingDescription(e.recordInfo.rating).toLowerCase():null,l=!e.loading&&!e.loadError&&e.dataLoaded&&"inspection"===e.recordType&&e.recordInfo.remediationTask?e.getTaskStatusIcon(e.recordInfo.remediationTask.status):null,p=!e.loading&&!e.loadError&&e.dataLoaded&&"inspection"===e.recordType&&e.recordInfo.remediationTask?e.getTaskStatusText(e.recordInfo.remediationTask.status):null,f=!e.loading&&!e.loadError&&e.dataLoaded&&"inspection"===e.recordType&&e.recordInfo.remediationTask?e.recordInfo.remediationTask.completionPhotos&&e.recordInfo.remediationTask.completionPhotos.length>0:null,m=!e.loading&&!e.loadError&&e.dataLoaded&&"inspection"===e.recordType&&e.recordInfo.remediationTask&&f?e.recordInfo.remediationTask.completionPhotos.length:null,h=!e.loading&&!e.loadError&&e.dataLoaded&&"inspection"===e.recordType&&e.recordInfo.reviewInfo&&e.recordInfo.reviewInfo.hasReview&&e.recordInfo.reviewInfo.reviewRating>0?e.getRatingDescription(e.recordInfo.reviewInfo.reviewRating):null,g=!e.loading&&!e.loadError&&e.dataLoaded&&"inspection"===e.recordType&&e.recordInfo.reviewInfo&&e.recordInfo.reviewInfo.hasReview?e.recordInfo.reviewInfo.reviewPhotos&&e.recordInfo.reviewInfo.reviewPhotos.length>0:null,v=!e.loading&&!e.loadError&&e.dataLoaded&&"inspection"===e.recordType&&e.recordInfo.reviewInfo&&e.recordInfo.reviewInfo.hasReview&&g?e.recordInfo.reviewInfo.reviewPhotos.length:null;e._isMounted||(e.e0=function(t){e.issueDescExpanded=!e.issueDescExpanded}),e.$mp.data=Object.assign({},{$root:{m0:r,m1:n,m2:i,g0:a,g1:o,m3:s,g2:c,g3:d,g4:u,m4:l,m5:p,g5:f,g6:m,m6:h,g7:g,g8:v}})},a=[]},c8ff:function(e,t,r){"use strict";(function(e,t){var n=r("47a9");r("357b"),r("861b");n(r("3240"));var i=n(r("47e0"));e.__webpack_require_UNI_MP_PLUGIN__=r,t(i.default)}).call(this,r("3223")["default"],r("df3c")["createPage"])}},[["c8ff","common/runtime","common/vendor"]]]);