// 表单校验规则由 schema2code 生成，不建议直接修改校验规则，而建议通过 schema2code 生成, 详情: https://uniapp.dcloud.net.cn/uniCloud/schema


const validator = {
  "title": {
    "rules": [
      {
        "required": true,
        "errorMessage": "请填写公告标题"
      },
      {
        "format": "string"
      },
      {
        "maxLength": 50,
        "errorMessage": "标题不能超过50个字符"
      }
    ]
  },
  "content": {
    "rules": [
      {
        "required": true,
        "errorMessage": "请填写公告内容"
      },
      {
        "format": "string"
      }
    ]
  },
  "category": {
    "rules": [
      {
        "required": true,
        "errorMessage": "请选择公告分类"
      },
      {
        "format": "string"
      },
      {
        "range": [
          {
            "value": "公告通知",
            "text": "公告通知"
          },
          {
            "value": "重要通知",
            "text": "重要通知"
          },
          {
            "value": "活动通知",
            "text": "活动通知"
          },
          {
            "value": "其他通知",
            "text": "其他通知"
          }
        ]
      }
    ]
  },
  "isTop": {
    "rules": [
      {
        "format": "bool"
      }
    ],
    "defaultValue": false
  },
  "createTime": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "timestamp"
      }
    ],
    "defaultValue": {
      "$env": "now"
    }
  },
  "publisher": {
    "rules": [
      {
        "format": "string"
      }
    ]
  },
  "publisherName": {
    "rules": [
      {
        "format": "string"
      }
    ]
  },
  "readCount": {
    "rules": [
      {
        "format": "int"
      }
    ],
    "defaultValue": 0
  },
  "avatar": {
    "rules": [
      {
        "format": "string"
      }
    ]
  },
  "images": {
    "rules": [
      {
        "format": "array"
      }
    ]
  }
}

const enumConverter = {
  "category_valuetotext": {
    "公告通知": "公告通知",
    "重要通知": "重要通知",
    "活动通知": "活动通知",
    "其他通知": "其他通知"
  }
}

function filterToWhere(filter, command) {
  let where = {}
  for (let field in filter) {
    let { type, value } = filter[field]
    switch (type) {
      case "search":
        if (typeof value === 'string' && value.length) {
          where[field] = new RegExp(value)
        }
        break;
      case "select":
        if (value.length) {
          let selectValue = []
          for (let s of value) {
            selectValue.push(command.eq(s))
          }
          where[field] = command.or(selectValue)
        }
        break;
      case "range":
        if (value.length) {
          let gt = value[0]
          let lt = value[1]
          where[field] = command.and([command.gte(gt), command.lte(lt)])
        }
        break;
      case "date":
        if (value.length) {
          let [s, e] = value
          let startDate = new Date(s)
          let endDate = new Date(e)
          where[field] = command.and([command.gte(startDate), command.lte(endDate)])
        }
        break;
      case "timestamp":
        if (value.length) {
          let [startDate, endDate] = value
          where[field] = command.and([command.gte(startDate), command.lte(endDate)])
        }
        break;
    }
  }
  return where
}

export { validator, enumConverter, filterToWhere }
