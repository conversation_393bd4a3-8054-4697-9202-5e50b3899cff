require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/issue-list"],{"429e":function(e,t,n){"use strict";var a=n("8ec2"),r=n.n(a);r.a},"4c64":function(e,t,n){"use strict";n.r(t);var a=n("a7ac"),r=n("ccc0");for(var s in r)["default"].indexOf(s)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(s);n("429e");var i=n("828b"),u=Object(i["a"])(r["default"],a["b"],a["c"],!1,null,"1bc1a9f8",null,!1,a["a"],void 0);t["default"]=u.exports},6195:function(e,t,n){"use strict";(function(e,t){var a=n("47a9");n("357b"),n("861b");a(n("3240"));var r=a(n("4c64"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(r.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"8ec2":function(e,t,n){},a7ac:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return s})),n.d(t,"a",(function(){return a}));var a={uniIcons:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(n.bind(null,"6ddf"))},pEmptyState:function(){return n.e("components/p-empty-state/p-empty-state").then(n.bind(null,"9b76"))}},r=function(){var e=this,t=e.$createElement,n=(e._self._c,e.loading?null:e.currentList.length),a=!e.loading&&n>0?e.__map(e.currentList,(function(t,n){var a=e.__get_orig(t),r=t.number?e.getIssueNumber(t.number):null,s=e.getStatusText(t.status),i=e.formatTime(t.createdAt||t.updatedAt);return{$orig:a,m0:r,m1:s,m2:i}})):null;e.$mp.data=Object.assign({},{$root:{g0:n,l0:a}})},s=[]},c08b:function(e,t,n){"use strict";(function(e){var a=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n("7eb4")),s=a(n("ee10"));function i(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"===typeof e)return u(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return u(e,t)}(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var a=0,r=function(){};return{s:r,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,i=!0,o=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){o=!0,s=e},f:function(){try{i||null==n.return||n.return()}finally{if(o)throw s}}}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var o={name:"IssueList",data:function(){return{currentTab:"published",publishedIssues:[],draftIssues:[],loading:!1,dataLoaded:!1,needsRefresh:!1}},computed:{currentList:function(){return"published"===this.currentTab?this.publishedIssues:this.draftIssues},publishedCount:function(){return this.publishedIssues.length},draftCount:function(){return this.draftIssues.length},totalCount:function(){return this.publishedCount+this.draftCount}},onLoad:function(){this.loadData(),e.$on("monthlyIssueUpdated",this.handleIssueUpdated),e.$on("issueDraftUpdated",this.handleDraftUpdated)},onUnload:function(){e.$off("monthlyIssueUpdated",this.handleIssueUpdated),e.$off("issueDraftUpdated",this.handleDraftUpdated)},onShow:function(){this.dataLoaded&&!this.loading&&this.needsRefresh&&(this.silentRefreshData(),this.needsRefresh=!1)},methods:{loadData:function(){this.loadPublishedIssues(),this.loadDraftIssues()},loadPublishedIssues:function(){var t=this;return(0,s.default)(r.default.mark((function a(){var s,i,u,o;return r.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,t.loading=!0,s=n("882c"),i=s.callCloudFunction,a.next=5,i("hygiene-monthly-inspection",{action:"getMonthlyIssues",data:{pageSize:50,page:1}});case 5:if(u=a.sent,u&&u.success&&u.data&&u.data.list){t.publishedIssues=u.data.list.map((function(e){return t.formatIssueItem(e)}));try{e.setStorageSync("cached_published_issues",t.publishedIssues)}catch(r){}}else t.publishedIssues=[];a.next=12;break;case 9:a.prev=9,a.t0=a["catch"](0);try{o=e.getStorageSync("cached_published_issues"),o&&Array.isArray(o)?(t.publishedIssues=o,e.showToast({title:"已加载缓存数据",icon:"none",duration:2e3})):t.publishedIssues=[]}catch(r){t.publishedIssues=[]}case 12:return a.prev=12,t.loading=!1,t.dataLoaded=!0,a.finish(12);case 16:case"end":return a.stop()}}),a,null,[[0,9,12,16]])})))()},formatIssueItem:function(e){var t;return{id:e._id||e.id,number:e.issue_number||e.number,title:e.title||"未知问题",description:e.description||"",location:(null===(t=e.location_info)||void 0===t?void 0:t.location_name)||"未设置",responsible:e.assigned_to_name||"未指定",responsible_id:e.assigned_to,deadline:this.formatDate(e.expected_completion_date),priority:e.priority||"normal",status:e.status||"pending",category:e.category,severity:e.severity,images:e.photos||[],createdAt:e.created_at||(new Date).toISOString(),updatedAt:e.updated_at||(new Date).toISOString()}},formatDate:function(e){if(!e)return"";try{var t=new Date(e);return isNaN(t.getTime())?"":t.toISOString().split("T")[0]}catch(n){return""}},loadDraftIssues:function(){var t=this;return(0,s.default)(r.default.mark((function n(){var a,s,u,o,c;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(n.prev=0,a=e.getStorageSync("issue_drafts")||[],!(a.length>0)){n.next=23;break}s=i(a),n.prev=4,s.s();case 6:if((u=s.n()).done){n.next=15;break}if(o=u.value,!o.responsible||o.responsible_name){n.next=13;break}return n.next=11,t.getUserNameById(o.responsible);case 11:c=n.sent,c&&(o.responsible_name=c,o.responsible_id=o.responsible,o.responsible=c);case 13:n.next=6;break;case 15:n.next=20;break;case 17:n.prev=17,n.t0=n["catch"](4),s.e(n.t0);case 20:return n.prev=20,s.f(),n.finish(20);case 23:t.draftIssues=a,n.next=29;break;case 26:n.prev=26,n.t1=n["catch"](0),t.draftIssues=[];case 29:case"end":return n.stop()}}),n,null,[[0,26],[4,17,20,23]])})))()},getUserNameById:function(e){return(0,s.default)(r.default.mark((function t(){var a,s,i;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,a=n("882c"),s=a.callCloudFunction,t.next=4,s("hygiene-monthly-inspection",{action:"getUserInfo",data:{userId:e}});case 4:if(i=t.sent,!(i&&i.success&&i.data)){t.next=7;break}return t.abrupt("return",i.data.nickname||i.data.username||"未知用户");case 7:t.next=11;break;case 9:t.prev=9,t.t0=t["catch"](0);case 11:return t.abrupt("return",null);case 12:case"end":return t.stop()}}),t,null,[[0,9]])})))()},switchTab:function(e){this.currentTab=e},handleItemClick:function(e){"draft"===e.status?this.editDraft(e):this.viewIssueDetail(e)},viewIssueDetail:function(t){e.navigateTo({url:"/pages/6s_pkg/issue-detail?id=".concat(t.id)})},editPublishedIssue:function(t){e.navigateTo({url:"/pages/6s_pkg/issue-add?editId=".concat(t.id,"&editData=").concat(encodeURIComponent(JSON.stringify(t)))})},changeIssueStatus:function(t){var n=this,a=[],i=t.status,u=t.responsible&&"未指定"!==t.responsible;a=u?"assigned"===i?[{value:"pending",label:"待整改"},{value:"in_progress",label:"整改中"},{value:"pending_review",label:"待检查"}]:"pending"===i?[{value:"in_progress",label:"整改中"},{value:"pending_review",label:"待检查"}]:"in_progress"===i?[{value:"pending_review",label:"待检查"},{value:"pending",label:"待整改"}]:"pending_review"===i?[{value:"approved",label:"检查通过"},{value:"rejected",label:"重新整改"}]:"approved"===i?[{value:"pending",label:"重新打开"},{value:"rejected",label:"发现新问题，重新整改"}]:"rejected"===i?[{value:"pending",label:"重新开始整改"},{value:"in_progress",label:"整改中"}]:[{value:"pending",label:"待整改"},{value:"in_progress",label:"整改中"},{value:"pending_review",label:"待检查"},{value:"approved",label:"检查通过"}]:[{value:"assigned",label:"已分配"}],e.showActionSheet({itemList:a.map((function(e){return e.label})),success:function(){var e=(0,s.default)(r.default.mark((function e(s){var i;return r.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(i=a[s.tapIndex],!i||i.value===t.status){e.next=4;break}return e.next=4,n.updateIssueStatus(t.id,i.value);case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()})},updateIssueStatus:function(t,a){var i=this;return(0,s.default)(r.default.mark((function s(){var u,o,c,l;return r.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(r.prev=0,t){r.next=4;break}return e.showToast({title:"问题ID不能为空",icon:"error"}),r.abrupt("return");case 4:if(a){r.next=7;break}return e.showToast({title:"状态参数无效",icon:"error"}),r.abrupt("return");case 7:return e.showLoading({title:"更新中..."}),u=n("882c"),o=u.callCloudFunction,c=a,"rejected"===a&&(c="rejected"),r.next=13,o("hygiene-monthly-inspection",{action:"updateMonthlyIssue",data:{issue_id:t,status:c,action_type:"admin_update"}});case 13:if(l=r.sent,!l||!l.success){r.next=20;break}e.hideLoading(),e.showToast({title:"状态更新成功",icon:"success"}),i.loadData(),r.next=21;break;case 20:throw new Error((null===l||void 0===l?void 0:l.message)||"更新失败");case 21:r.next=27;break;case 23:r.prev=23,r.t0=r["catch"](0),e.hideLoading(),e.showToast({title:r.t0.message||"更新失败",icon:"error"});case 27:case"end":return r.stop()}}),s,null,[[0,23]])})))()},editDraft:function(t){e.navigateTo({url:"/pages/6s_pkg/issue-add?draftId=".concat(t.id)})},publishDraft:function(t){var a=this;return(0,s.default)(r.default.mark((function i(){return r.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(t.description&&t.location&&t.responsible&&t.deadline){i.next=3;break}return e.showModal({title:"信息不完整",content:"草稿信息不完整，需要先编辑补充必要信息后才能发布",showCancel:!1,confirmText:"去编辑",success:function(){a.editDraft(t)}}),i.abrupt("return");case 3:e.showModal({title:"确认发布",content:"确定要发布这个问题吗？",success:function(){var i=(0,s.default)(r.default.mark((function s(i){var u,o,c,l,d,f,p,h;return r.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!i.confirm){r.next=29;break}return r.prev=1,e.showLoading({title:"发布中..."}),u=n("882c"),o=u.callCloudFunction,c=t.responsible_id,l=t.responsible_name||t.responsible,!c&&t.responsible&&"string"===typeof t.responsible&&24===t.responsible.length&&(c=t.responsible),d={title:t.title.trim(),description:t.description.trim(),location_info:{location_type:"custom",location_name:t.location.trim(),location_description:""},inspection_info:{inspection_date:(new Date).toISOString(),inspection_type:"monthly_routine"},category:"safety",severity:"urgent"===t.priority?"high":"medium",priority:t.priority||"normal",assigned_to:c,assigned_to_name:l,photos:t.images||[],tags:[],expected_completion_date:t.deadline},r.next=10,o("hygiene-monthly-inspection",{action:"createMonthlyIssue",data:d});case 10:if(f=r.sent,!f||!f.success){r.next=22;break}return a.removeDraftFromLocal(t.id),a.loadDraftIssues(),r.next=16,a.loadPublishedIssues();case 16:e.hideLoading(),e.showToast({title:"发布成功",icon:"success"}),e.$emit("monthlyIssueUpdated",{action:"create",issueId:(null===(p=f.data)||void 0===p?void 0:p.id)||(null===(h=f.data)||void 0===h?void 0:h._id),fromDraft:!0}),e.$emit("issueDraftUpdated",{action:"delete",draftId:t.id}),r.next=23;break;case 22:throw new Error((null===f||void 0===f?void 0:f.message)||"发布失败");case 23:r.next=29;break;case 25:r.prev=25,r.t0=r["catch"](1),e.hideLoading(),e.showToast({title:r.t0.message||"发布失败",icon:"error"});case 29:case"end":return r.stop()}}),s,null,[[1,25]])})));return function(e){return i.apply(this,arguments)}}()});case 4:case"end":return i.stop()}}),i)})))()},deleteDraft:function(t){var n=this;e.showModal({title:"确认删除",content:"确定要删除这个草稿吗？",success:function(a){a.confirm&&(n.removeDraftFromLocal(t.id),n.loadDraftIssues(),e.showToast({title:"删除成功",icon:"success"}))}})},removeDraftFromLocal:function(t){try{var n=e.getStorageSync("issue_drafts")||[],a=n.filter((function(e){return e.id!==t}));e.setStorageSync("issue_drafts",a)}catch(r){}},addIssue:function(){e.navigateTo({url:"/pages/6s_pkg/issue-add"})},getIssueNumber:function(e){if(!e)return"";var t=String(e);if(t.startsWith("YD")&&t.length>10){var n=t.slice(-3);return"YD".concat(n)}if(t.length>8){var a=t.substring(0,2),r=t.slice(-6);return"".concat(a).concat(r)}return t},getStatusText:function(e){return{draft:"草稿",assigned:"已分配",pending:"待整改",in_progress:"整改中",pending_review:"待检查",approved:"检查通过",rejected:"已驳回",overdue:"已逾期",reopened:"重新打开",open:"待处理",resolved:"已解决",reviewing:"审核中",verified:"已验证",cancelled:"已取消",suspended:"已暂停",new:"新建",active:"进行中",inactive:"非活跃",expired:"已过期"}[e]||"未知"},formatTime:function(e){if(!e)return"";var t=new Date(e),n=new Date,a=n-t;return a<6e4?"刚刚":a<36e5?Math.floor(a/6e4)+"分钟前":a<864e5?Math.floor(a/36e5)+"小时前":t.getMonth()+1+"月"+t.getDate()+"日"},delay:function(e){return new Promise((function(t){return setTimeout(t,e)}))},handleIssueUpdated:function(e){"create"!==e.action&&"update"!==e.action&&"delete"!==e.action||this.silentRefreshPublishedData()},handleDraftUpdated:function(e){var t=this;setTimeout((function(){t.loadDraftIssues()}),100)},silentRefreshData:function(){var e=this;return(0,s.default)(r.default.mark((function t(){return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.loading){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,t.next=5,Promise.all([e.silentRefreshPublishedData(),e.silentRefreshDraftData()]);case 5:t.next=9;break;case 7:t.prev=7,t.t0=t["catch"](2);case 9:case"end":return t.stop()}}),t,null,[[2,7]])})))()},silentRefreshPublishedData:function(){var t=this;return(0,s.default)(r.default.mark((function a(){var s,i,u;return r.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,s=n("882c"),i=s.callCloudFunction,a.next=4,i("hygiene-monthly-inspection",{action:"getMonthlyIssues",data:{pageSize:50,page:1}});case 4:if(u=a.sent,u&&u.success&&u.data&&u.data.list){t.publishedIssues=u.data.list.map((function(e){return t.formatIssueItem(e)}));try{e.setStorageSync("cached_published_issues",t.publishedIssues)}catch(r){}}a.next=10;break;case 8:a.prev=8,a.t0=a["catch"](0);case 10:case"end":return a.stop()}}),a,null,[[0,8]])})))()},silentRefreshDraftData:function(){var t=this;return(0,s.default)(r.default.mark((function n(){var a,s,u,o,c;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(n.prev=0,a=e.getStorageSync("issue_drafts")||[],!(a.length>0)){n.next=23;break}s=i(a),n.prev=4,s.s();case 6:if((u=s.n()).done){n.next=15;break}if(o=u.value,!o.responsible||o.responsible_name){n.next=13;break}return n.next=11,t.getUserNameById(o.responsible);case 11:c=n.sent,c&&(o.responsible_name=c,o.responsible_id=o.responsible,o.responsible=c);case 13:n.next=6;break;case 15:n.next=20;break;case 17:n.prev=17,n.t0=n["catch"](4),s.e(n.t0);case 20:return n.prev=20,s.f(),n.finish(20);case 23:t.draftIssues=a,n.next=28;break;case 26:n.prev=26,n.t1=n["catch"](0);case 28:case"end":return n.stop()}}),n,null,[[0,26],[4,17,20,23]])})))()}}};t.default=o}).call(this,n("df3c")["default"])},ccc0:function(e,t,n){"use strict";n.r(t);var a=n("c08b"),r=n.n(a);for(var s in a)["default"].indexOf(s)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(s);t["default"]=r.a}},[["6195","common/runtime","common/vendor"]]]);