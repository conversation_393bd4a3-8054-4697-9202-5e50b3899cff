{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s_pkg/record-detail.vue?bf27", "webpack:///D:/Xwzc/pages/6s_pkg/record-detail.vue?3dd1", "webpack:///D:/Xwzc/pages/6s_pkg/record-detail.vue?27f5", "webpack:///D:/Xwzc/pages/6s_pkg/record-detail.vue?d9f7", "uni-app:///pages/6s_pkg/record-detail.vue", "webpack:///D:/Xwzc/pages/6s_pkg/record-detail.vue?817e", "webpack:///D:/Xwzc/pages/6s_pkg/record-detail.vue?5bc9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "recordId", "recordType", "loading", "loadError", "dataLoaded", "editPermission", "canEdit", "message", "checking", "issueDescExpanded", "isHistoricalRecord", "recordWeek", "cleaningDate", "processCache", "basicDataLoaded", "recordInfo", "id", "areaId", "areaName", "operatorName", "operationTime", "operationTimestamp", "status", "photos", "notes", "weekPeriod", "rating", "result", "hasIssues", "issueType", "issueDescription", "summary", "remediationTask", "history", "computed", "showEditSection", "statusBadgeClass", "resultBadgeClass", "taskBadgeClass", "taskStatusValueClass", "onLoad", "uni", "onUnload", "onShow", "methods", "getPageTitle", "loadRecordDetail", "initProcessCache", "categoryMap", "statusMaps", "record", "task", "taskIcon", "formatters", "dateTime", "date", "createDateTimeFormatter", "createDateFormatter", "loadCleaningRecordDetailOptimized", "action", "record_id", "dateTimeF<PERSON>atter", "cleanDate", "type", "title", "description", "time", "calculateWeekPeriodOptimized", "weekStart", "weekEnd", "processPhotos", "url", "checkEditPermissionAsync", "loadCleaningRecordDetail", "weekNum", "loadInspectionRecordDetailOptimized", "reviewInfo", "processIssueInfo", "processRectificationInfo", "rect", "assignee", "deadline", "completionTime", "relatedRectification", "completionNotes", "completionPhotos", "finalRating", "processReviewInfo", "has<PERSON>eview", "reviewerName", "reviewTime", "reviewComments", "reviewRating", "reviewResult", "reviewPhotos", "processInspectionPhotos", "processRectificationPhotos", "buildHistoryTimelineOptimized", "buildRectificationHistory", "rectification", "buildSimpleRectificationHistory", "parseTimeForSort", "loadInspectionRecordDetail", "firstIssue", "getWeekNumber", "getWeekStart", "getWeekEnd", "formatDateTime", "retryLoad", "previewPhoto", "urls", "current", "retryPhotoLoad", "onPhotoError", "checkEditPermission", "timeCheckResult", "cleaningRecordTime", "hasInspectionAfterCleaning", "area_id", "pageSize", "inspectionResult", "records", "relatedInspections", "console", "rectificationResult", "hasActiveTasks", "recordTime", "activeTasks", "checkRecordTimeValidity", "getStatusText", "getResultText", "getTaskStatusText", "getTaskStatusIcon", "editRecord", "getRatingDescription", "handleRecordUpdated", "previewRectificationPhoto", "previewReviewPhoto", "handlePhotoError", "photoArray", "index"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrLA;AAAA;AAAA;AAAA;AAAumB,CAAgB,ioBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC4c3nB;AAAA;AAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;QAAAC;QAAAC;QAAAC;MAAA;MAAA;MACAC;MAAA;;MAEA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MAAA;MACAC;MAAA;;MAEAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACA;QACAC;QACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACA;QACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MAEA;IACA;;IAEA;IACAC;EACA;EACAC;IACA;IACAD;EACA;EACAE;IACA;IACA;EAAA,CACA;EACAC;IACA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACA;;gBAEA;gBACA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAGA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;UACA;UACAC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA;UACA;UACAC;YACAC;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA;YACAvB;cACA;cACA;YACA;YACAwB;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA;YACAC;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA;UACA;UACA;UACAC;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QAEA;UACA;UACA;UAEA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QAEA;UACA;UACA;UAEA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACAC;kBACA5D;oBACA6D;kBACA;gBACA;cAAA;gBALAjC;gBAAA,MAOAA;kBAAA;kBAAA;gBAAA;gBACAuB,sBAEA;gBACAW,6DAEA;gBACAC;gBACArC,6DAEA;gBACA;kBACAT;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAI;kBACAH;kBACAC;kBACAC;kBACAS;oBACA8B;oBACAC;oBACAC;oBACAC;kBACA;gBACA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;MACA;MACA;MACAC;MACA;MACAC;MAEA;IACA;IAEA;IACAC;MACA;QAAA;UACAC;UACAzE;UACAK;QACA;MAAA;IACA;IAEA;IACAqE;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAKA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACAd;kBACA5D;oBACA6D;kBACA;gBACA;cAAA;gBALAjC;gBAAA,MAOAA;kBAAA;kBAAA;gBAAA;gBACAuB;gBACAY,4CAEA;gBACAY;gBACAN;gBACAC;gBACA5C;gBAEA;kBACAT;kBACAC;kBAAA;kBACAC;kBACAC;kBACAC;kBACAC;kBAAA;kBACAI;kBACAH;kBACAC;oBAAA;sBACAgD;sBACAzE;sBACAK;oBACA;kBAAA;kBACAqB;kBACAS,UACA;oBACA8B;oBACAC;oBACAC;oBACAC;kBACA;gBAEA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAS;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACAhB;kBACA5D;oBACAiB;kBACA;gBACA;cAAA;gBALAW;gBAAA,MAOAA;kBAAA;kBAAA;gBAAA;gBACAuB,sBAEA;gBACAW;gBACAb,+CAEA;gBAAA,wBACA,sJAEA;gBAAA,wBACA,sKAEA;gBACA4B,mFAEA;gBACA;kBACA5D;kBACAE;kBACAC;kBACAC;kBACAC;kBACAC;kBACAI;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAR;kBACAS;kBACA;kBACA4C;kBACA3C;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA4C;MACA;MACA;MAEA;QACA;QACAhD;QACAC;MACA;MAEA;QAAAD;QAAAC;MAAA;IACA;IAEA;IACAgD;MACA;MACA;MACA;MAEA;QACA;UAAA,OACAC,4CACAA;QAAA,EACA;QAEA;UACA;UACA;;UAEA/C;YACAhB;YACAM;YACA0D;YACAC;YACAC,oDACArB,uDACAsB;YACAC;YACAC;UACA;QACA;MACA;MAEA;QAAAC;QAAAtD;MAAA;IACA;IAEA;IACAuD;MACA;MAEA;QACA;QAEA;UACAX;YACAY;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACA;UACA;YACAxB;YACAzE;YACAK;UACA;QACA;UACA;YACAoE;YACAzE;YACAK;UACA;QACA;QACA;UACAoE;UACAzE;UACAK;QACA;MACA;QAAA;MAAA;IACA;IAEA;IACA6F;MACA;MAEA;QACA;UACA;YACAzB;YACAzE;YACAK;UACA;QACA;UACA;YACAoE;YACAzE;YACAK;UACA;QACA;QACA;UACAoE;UACAzE;UACAK;QACA;MACA;QAAA;MAAA;IACA;IAEA;IACA8F;MAAA;MACA;QACAlC;QACAC;QACAC;QACAC;MACA;MAEA;QACAjC;UACA8B;UACAC;UACAC;UACAC;QACA;QAEA;UACA;UACAjC;YACA8B;YACAC;YACAC;YACAC;UACA;;UAEA;UACA;YACA;YACA;UACA;YACA;YACA;UACA;QACA;MACA;QACA;QACA,4GACAhB,kGACA;QAEAjB;UACA8B;UACAC;UACAC;UACAC;QACA;MACA;MAEA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAgC;MACA;MACA;QACAjE;UACA8B;UACAC;UACAC;UACAC;QACA;MACA;;MAEA;MACA;QACAjC;UACA8B;UACAC;UACAC;UACAC;QACA;MACA;;MAEA;MACA;QACA;QACAjC;UACA8B;UACAC;UACAC,+LAEAkC;UACAjC;QACA;MACA;;MAEA;MACA;QACAjC;UACA8B;UACAC;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAkC;MACA,qEACApE;MACA,kEACAA;MAEAC;QACA8B;QACAC;QACAC,qDACA,oBACAjC,wCACA,mBACA;QACAkC;MACA;IACA;IAEA;IACAmC;MACA;QACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACA3C;kBACA5D;oBACAiB;kBACA;gBACA;cAAA;gBALAW;gBAAA,MAOAA;kBAAA;kBAAA;gBAAA;gBACAuB,sBAEA;gBACArB;gBACAC;gBAEA;kBACAyE,+BACA;kBACAvD;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;kBACA;kBACAnB;kBACAC;gBACA;;gBAEA;gBACAwD,0DAEA;gBACAtD;gBACA;kBACA;kBACAmD;oBAAA,OACAJ,4CACAA;kBAAA,EACA;kBAEA;oBACA;oBACA;;oBAEA;oBACA/C;sBACAhB;sBACAM;sBACA0D;sBACAC;sBACA;sBACAC,oDACA,2DACAC;sBACAC;oBACA;kBACA;gBACA;gBAEA;kBACApE;kBACAE;kBACAC;kBACAC;kBACAC;kBAAA;kBACAC;kBACAI;kBAAA;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAR;oBACA;oBACA;sBACA;wBACAgD;wBACAzE;wBACAK;sBACA;oBACA;sBACA;wBACAoE;wBACAzE;wBACAK;sBACA;oBACA;oBACA;sBACAoE;sBACAzE;sBACAK;oBACA;kBACA;oBAAA;kBAAA;kBAAA;kBACA6B;kBACAC,UACA;oBACA8B;oBACAC;oBACAC;oBACAC;kBACA;gBAEA;;gBAEA;gBACA;kBACA;oBACAH;oBACAC;oBACAC;oBACAC;kBACA;;kBAEA;kBACA;oBACA;oBACA;sBACAH;sBACAC;sBACAC;sBACAC;oBACA;;oBAEA;oBACA;sBACA;wBACA;0BACAH;0BACAC;0BACAC;0BACAC;wBACA;sBACA;wBACA;0BACAH;0BACAC;0BACAC;0BACAC;wBACA;sBACA;wBACA;0BACAH;0BACAC;0BACAC;0BACAC;wBACA;sBACA;oBACA;kBACA;gBACA;;gBAEA;gBACA;kBACA;kBACA;oBACA;oBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;oBACA;oBACA;oBACA;kBACA;kBAEA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAsC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA9E;MACA;IACA;IAEA;IACA+E;MACA;MACA;MACA;MACA;MACA/E;MACA;IACA;IAEA;IACAgF;MACA;MAEA;QACA;QACA;UACA;QACA;QAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QAAA;MAAA;MACApE;QACAqE;QACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACA;kBAAA5G;kBAAAC;kBAAAC;gBAAA;gBAAA;cAAA;gBAAA,KAKA;kBAAA;kBAAA;gBAAA;gBACA;kBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBACA2G;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA;kBAAA3G;gBAAA;gBAAA;cAAA;gBAAA;gBAKA;gBACAS;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA;kBAAAX;kBAAAC;kBAAAC;gBAAA;gBAAA;cAAA;gBAIA;gBACA4G,qEAEA;gBACAC;gBAAA;gBAAA;gBAAA,OAGA;kBACA1D;kBACA5D;oBACAuH;oBACAC;kBACA;gBACA;cAAA;gBANAC;gBAQA;kBACAC;kBAEA;oBACA;oBACAC;sBACA;sBACA;oBACA;oBAEAL;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAM;gBACA;gBACAN;cAAA;gBAAA,KAIAA;kBAAA;kBAAA;gBAAA;gBACA;kBACA/G;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAMA;kBACAmD;kBACA5D;oBACAuH;oBACAC;kBACA;gBACA;cAAA;gBANAK;gBAQA;gBACAC;gBACA,0DACAD;kBAEA;kBACAE,iCAEA;kBACAC;oBACA;oBACA;;oBAEA;oBACA,wBACA;oBAAA;oBACA;oBAAA;oBACA;oBAAA;oBACA;oBAAA;oBACA;oBAAA,CACA;;oBAEA;oBACA;kBACA;kBACAF;gBAGA;gBAEA;kBACA;oBACAvH;oBACAC;oBACAC;kBACA;gBACA;kBACA;oBAAAF;oBAAAC;oBAAAC;kBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA;kBACAF;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAwH;MACA;QACA;UAAA1H;UAAAC;QAAA;MACA;MAEA;MACA;;MAEA;MACA;MACA;MAEA;QACA;UACAD;UACAC;QACA;MACA;;MAEA;MACA;MACA;MAEA;QACA;UACAD;UACAC;QACA;MACA;MAEA;QAAAD;QAAAC;MAAA;IACA;IAEA;IACA0H;MAAA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MAEA5F;QACA8B;MACA;IACA;IAEA;IACA+D;MAAA;MACA;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;MACA,2CACA,yCACA,yCACA,yCACA;MAAA,KACA;;MAEA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QAAA;MAAA;MACA/F;QACAqE;QACAC;MACA;IACA;IAEA;IACA0B;MACA;QAAA;MAAA;MACAhG;QACAqE;QACAC;MACA;IACA;IAEA;IACA2B;MACA;IACA;EAAA,qFAGAC;IACA;EACA,4GAGAC;IACA;EACA,8FAGAA;IACA;EACA,gHAGAA;IACA;EACA,kGAGAA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACjqDA;AAAA;AAAA;AAAA;AAA0qC,CAAgB,gpCAAG,EAAC,C;;;;;;;;;;;ACA9rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/record-detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/record-detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./record-detail.vue?vue&type=template&id=9f2cd4fe&scoped=true&\"\nvar renderjs\nimport script from \"./record-detail.vue?vue&type=script&lang=js&\"\nexport * from \"./record-detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./record-detail.vue?vue&type=style&index=0&id=9f2cd4fe&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9f2cd4fe\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/record-detail.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./record-detail.vue?vue&type=template&id=9f2cd4fe&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getPageTitle()\n  var m1 = !_vm.loading ? _vm.getStatusText(_vm.recordInfo.status) : null\n  var m2 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    _vm.dataLoaded &&\n    _vm.recordType === \"inspection\" &&\n    _vm.recordInfo.rating > 0 &&\n    !_vm.recordInfo.hasIssues\n      ? _vm.getRatingDescription(_vm.recordInfo.rating)\n      : null\n  var g0 =\n    !_vm.loading && !_vm.loadError && _vm.dataLoaded\n      ? _vm.recordInfo.photos.length\n      : null\n  var g1 =\n    !_vm.loading && !_vm.loadError && _vm.dataLoaded\n      ? _vm.recordInfo.photos.length\n      : null\n  var m3 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    _vm.dataLoaded &&\n    _vm.recordType === \"inspection\"\n      ? _vm.getResultText(_vm.recordInfo.result)\n      : null\n  var g2 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    _vm.dataLoaded &&\n    _vm.recordType === \"inspection\" &&\n    _vm.recordInfo.hasIssues\n      ? !_vm.issueDescExpanded && _vm.recordInfo.issueDescription.length > 100\n      : null\n  var g3 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    _vm.dataLoaded &&\n    _vm.recordType === \"inspection\" &&\n    _vm.recordInfo.hasIssues\n      ? _vm.recordInfo.issueDescription.length\n      : null\n  var g4 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    _vm.dataLoaded &&\n    _vm.recordType === \"inspection\" &&\n    !_vm.recordInfo.hasIssues &&\n    _vm.recordInfo.rating > 0\n      ? _vm.getRatingDescription(_vm.recordInfo.rating).toLowerCase()\n      : null\n  var m4 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    _vm.dataLoaded &&\n    _vm.recordType === \"inspection\" &&\n    _vm.recordInfo.remediationTask\n      ? _vm.getTaskStatusIcon(_vm.recordInfo.remediationTask.status)\n      : null\n  var m5 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    _vm.dataLoaded &&\n    _vm.recordType === \"inspection\" &&\n    _vm.recordInfo.remediationTask\n      ? _vm.getTaskStatusText(_vm.recordInfo.remediationTask.status)\n      : null\n  var g5 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    _vm.dataLoaded &&\n    _vm.recordType === \"inspection\" &&\n    _vm.recordInfo.remediationTask\n      ? _vm.recordInfo.remediationTask.completionPhotos &&\n        _vm.recordInfo.remediationTask.completionPhotos.length > 0\n      : null\n  var g6 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    _vm.dataLoaded &&\n    _vm.recordType === \"inspection\" &&\n    _vm.recordInfo.remediationTask &&\n    g5\n      ? _vm.recordInfo.remediationTask.completionPhotos.length\n      : null\n  var m6 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    _vm.dataLoaded &&\n    _vm.recordType === \"inspection\" &&\n    _vm.recordInfo.reviewInfo &&\n    _vm.recordInfo.reviewInfo.hasReview &&\n    _vm.recordInfo.reviewInfo.reviewRating > 0\n      ? _vm.getRatingDescription(_vm.recordInfo.reviewInfo.reviewRating)\n      : null\n  var g7 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    _vm.dataLoaded &&\n    _vm.recordType === \"inspection\" &&\n    _vm.recordInfo.reviewInfo &&\n    _vm.recordInfo.reviewInfo.hasReview\n      ? _vm.recordInfo.reviewInfo.reviewPhotos &&\n        _vm.recordInfo.reviewInfo.reviewPhotos.length > 0\n      : null\n  var g8 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    _vm.dataLoaded &&\n    _vm.recordType === \"inspection\" &&\n    _vm.recordInfo.reviewInfo &&\n    _vm.recordInfo.reviewInfo.hasReview &&\n    g7\n      ? _vm.recordInfo.reviewInfo.reviewPhotos.length\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.issueDescExpanded = !_vm.issueDescExpanded\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        g0: g0,\n        g1: g1,\n        m3: m3,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        m4: m4,\n        m5: m5,\n        g5: g5,\n        g6: g6,\n        m6: m6,\n        g7: g7,\n        g8: g8,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./record-detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./record-detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page-container\">\n    <!-- 页面头部 - 始终显示 -->\n    <view class=\"page-header\">\n      <view class=\"header-content\">\n        <view class=\"header-title\">{{ getPageTitle() }}</view>\n        <view class=\"header-subtitle\">{{ loading ? '加载中...' : recordInfo.areaName }}</view>\n      </view>\n      <view v-if=\"!loading\" class=\"status-badge-enhanced\" :class=\"statusBadgeClass\">\n        <uni-icons \n          :type=\"recordInfo.status === 'completed' ? 'checkmarkempty' : recordInfo.status === 'pending' ? 'info' : recordInfo.status === 'pending_rectification' ? 'flag' : recordInfo.status === 'missed' ? 'close' : recordInfo.status === 'cancelled' ? 'close' : 'info'\"\n          size=\"16\" \n          color=\"white\"\n        ></uni-icons>\n        <text>{{ getStatusText(recordInfo.status) }}</text>\n      </view>\n    </view>\n\n    <!-- 内容区域加载状态 -->\n    <view v-if=\"loading\" class=\"content-loading\">\n      <view class=\"loading-content\">\n        <view class=\"loading-spinner\"></view>\n        <text class=\"loading-text\">加载记录详情...</text>\n      </view>\n    </view>\n\n    <!-- 错误状态 -->\n    <view v-else-if=\"loadError\" class=\"content-error\">\n      <p-empty-state \n        type=\"error\"\n        title=\"加载失败\"\n        description=\"网络异常，请检查网络连接\"\n        :show-button=\"true\"\n        button-text=\"重新加载\"\n        @button-click=\"retryLoad\"\n      ></p-empty-state>\n    </view>\n\n    <!-- 正常内容 -->\n    <view v-else-if=\"dataLoaded\">\n    <!-- 基本信息 -->\n    <view class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"card-title\">基本信息</view>\n      </view>\n      <view class=\"card-body\">\n        <view class=\"info-grid-enhanced\">\n          <view class=\"info-item-enhanced\">\n            <view class=\"info-icon\" :class=\"recordType === 'cleaning' ? 'icon-cleaning' : 'icon-inspection'\">\n              <uni-icons type=\"person\" size=\"18\" color=\"white\"></uni-icons>\n            </view>\n            <view class=\"info-content\">\n              <view class=\"info-label-enhanced\">{{ recordType === 'cleaning' ? '清理人员' : '检查人员' }}</view>\n              <view class=\"info-value-enhanced\">{{ recordInfo.operatorName }}</view>\n            </view>\n          </view>\n          \n          <view class=\"info-item-enhanced\">\n            <view class=\"info-icon icon-time\">\n              <uni-icons type=\"calendar\" size=\"18\" color=\"white\"></uni-icons>\n            </view>\n            <view class=\"info-content\">\n              <view class=\"info-label-enhanced\">{{ recordType === 'cleaning' ? '清理时间' : '检查时间' }}</view>\n              <view class=\"info-value-enhanced\">{{ recordInfo.operationTime }}</view>\n            </view>\n          </view>\n          \n          <view v-if=\"recordType === 'cleaning'\" class=\"info-item-enhanced period-item-enhanced\">\n            <view class=\"info-icon icon-cycle\">\n              <uni-icons type=\"reload\" size=\"18\" color=\"white\"></uni-icons>\n            </view>\n            <view class=\"info-content\">\n              <view class=\"info-label-enhanced\">所属周期</view>\n              <view class=\"info-value-enhanced\">{{ recordInfo.weekPeriod }}</view>\n            </view>\n          </view>\n          \n          <view v-if=\"recordType === 'inspection' && recordInfo.rating > 0 && !recordInfo.hasIssues\" class=\"info-item-enhanced rating-item-enhanced\">\n            <view class=\"info-icon icon-rating\">\n              <uni-icons type=\"star-filled\" size=\"18\" color=\"white\"></uni-icons>\n            </view>\n            <view class=\"info-content\">\n              <view class=\"info-label-enhanced\">检查评分</view>\n              <view class=\"rating-display-enhanced\">\n                <view class=\"rating-number\">{{ recordInfo.rating }}</view>\n                <view class=\"rating-stars\">\n                  <uni-icons \n                    v-for=\"star in 5\" \n                    :key=\"star\"\n                    type=\"star-filled\" \n                    size=\"14\" \n                    :color=\"star <= recordInfo.rating ? '#FFD700' : '#E5E5EA'\"\n                  ></uni-icons>\n                </view>\n                <view class=\"rating-desc\">{{ getRatingDescription(recordInfo.rating) }}</view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 照片展示 -->\n    <view class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"card-title\">{{ recordType === 'cleaning' ? '清理照片' : '检查照片' }}</view>\n        <view class=\"card-subtitle-enhanced\">\n          <uni-icons type=\"image\" size=\"14\" color=\"#8E8E93\"></uni-icons>\n          <text>{{ recordInfo.photos.length }}张照片</text>\n        </view>\n      </view>\n      <view class=\"card-body\">\n        <view v-if=\"recordInfo.photos.length === 0\" class=\"no-photos-enhanced\">\n          <p-empty-state \n            type=\"no-image\"\n            title=\"暂无照片\"\n            description=\"该记录没有上传照片\"\n          ></p-empty-state>\n        </view>\n        <view v-else class=\"photos-grid-enhanced\">\n          <view \n            v-for=\"(photo, index) in recordInfo.photos\" \n            :key=\"index\" \n            class=\"photo-item-enhanced\"\n            @click=\"previewPhoto(index)\"\n          >\n            <!-- 照片容器 -->\n            <view class=\"photo-container\">\n              <!-- 照片图片 -->\n              <image \n                :src=\"photo.url\" \n                mode=\"aspectFill\" \n                class=\"photo-image-enhanced\"\n                @error=\"onPhotoError(index)\"\n              ></image>\n              \n              <!-- 照片加载失败覆盖层 -->\n              <view v-if=\"photo.loadError\" class=\"photo-error\" @click.stop=\"retryPhotoLoad(index)\">\n                <uni-icons type=\"reload\" size=\"24\" color=\"#8E8E93\"></uni-icons>\n                <text class=\"error-text\">点击重试</text>\n              </view>\n              \n              <!-- 正常状态的覆盖层 -->\n              <view v-else class=\"photo-overlay-enhanced\">\n                <view class=\"overlay-content\">\n                  <uni-icons type=\"eye\" size=\"24\" color=\"white\"></uni-icons>\n                  <text class=\"overlay-text\">预览</text>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 检查结果（仅检查记录显示） -->\n    <view v-if=\"recordType === 'inspection'\" class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"card-title\">检查结果</view>\n        <view class=\"result-status-badge\" :class=\"resultBadgeClass\">\n          <uni-icons \n            :type=\"recordInfo.result === 'passed' ? 'checkmarkempty' : 'info'\"\n            size=\"14\" \n            color=\"white\"\n          ></uni-icons>\n          <text>{{ getResultText(recordInfo.result) }}</text>\n        </view>\n      </view>\n      <view class=\"card-body\">\n        <view class=\"result-section\">\n          <view v-if=\"recordInfo.hasIssues\" class=\"issue-details\">\n            <view class=\"issue-header\">\n              <uni-icons type=\"info\" size=\"16\" color=\"#FF3B30\"></uni-icons>\n              <text>问题类型：{{ recordInfo.issueType }}</text>\n            </view>\n            <view class=\"issue-time-info\">\n              检查时间：{{ recordInfo.operationTime }}\n            </view>\n            <view class=\"issue-content\">\n              <view class=\"issue-description\" :class=\"{ 'text-collapsed': !issueDescExpanded && recordInfo.issueDescription.length > 100 }\">\n                问题描述：{{ recordInfo.issueDescription }}\n              </view>\n              <view v-if=\"recordInfo.issueDescription.length > 100\" class=\"expand-btn\" @click=\"issueDescExpanded = !issueDescExpanded\">\n                <text>{{ issueDescExpanded ? '收起' : '展开' }}</text>\n                <uni-icons :type=\"issueDescExpanded ? 'up' : 'down'\" size=\"14\" color=\"#007AFF\"></uni-icons>\n              </view>\n            </view>\n          </view>\n          \n          <view v-else class=\"no-issue-display\">\n            <view class=\"no-issue-icon\">\n              <uni-icons type=\"checkmarkempty\" size=\"32\" color=\"#34C759\"></uni-icons>\n            </view>\n            <view class=\"no-issue-content\">\n              <view class=\"no-issue-title\">检查通过</view>\n              <view class=\"no-issue-desc\">\n                {{ recordInfo.rating > 0 \n                   ? `该责任区状况${getRatingDescription(recordInfo.rating).toLowerCase()}，无发现问题` \n                   : '该责任区状况良好，无发现问题' }}\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 清理说明 -->\n    <view v-if=\"recordType === 'cleaning' && recordInfo.notes\" class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"card-title\">清理说明</view>\n      </view>\n      <view class=\"card-body\">\n        <view class=\"notes-content\">\n          {{ recordInfo.notes }}\n        </view>\n      </view>\n    </view>\n\n    <!-- 整改任务（如有） -->\n    <view v-if=\"recordType === 'inspection' && recordInfo.remediationTask\" class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"card-title\">整改任务</view>\n        <view class=\"task-status-badge\" :class=\"taskBadgeClass\">\n          <uni-icons \n            :type=\"getTaskStatusIcon(recordInfo.remediationTask.status)\"\n            size=\"14\" \n            color=\"white\"\n          ></uni-icons>\n          <text>{{ getTaskStatusText(recordInfo.remediationTask.status) }}</text>\n        </view>\n      </view>\n      <view class=\"card-body\">\n        <view class=\"remediation-task\">\n          <view class=\"task-info\">\n            <view class=\"task-item\">\n              <view class=\"task-label\">负责人</view>\n              <view class=\"task-value\">{{ recordInfo.remediationTask.assignee }}</view>\n            </view>\n          </view>\n          \n          <view v-if=\"recordInfo.remediationTask.status === 'verified' || recordInfo.remediationTask.status === 'completed' || recordInfo.remediationTask.status === 'pending_review'\" class=\"completion-info\">\n            <view class=\"completion-header\" :style=\"{ color: recordInfo.remediationTask.status === 'verified' ? '#2E7D32' : '#F57C00' }\">\n              <uni-icons \n                :type=\"recordInfo.remediationTask.status === 'verified' ? 'checkmarkempty' : 'eye'\"\n                size=\"16\" \n                :color=\"recordInfo.remediationTask.status === 'verified' ? '#2E7D32' : '#F57C00'\"\n              ></uni-icons>\n              <text>{{ recordInfo.remediationTask.status === 'verified' ? '整改完成' : '整改已提交' }}</text>\n            </view>\n            <view class=\"completion-details\">\n              <view v-if=\"recordInfo.remediationTask.completionTime && recordInfo.remediationTask.completionTime !== '--' && recordInfo.remediationTask.completionTime !== 'null'\" class=\"completion-time\">\n                完成时间：{{ recordInfo.remediationTask.completionTime }}\n              </view>\n              \n              <view v-if=\"recordInfo.remediationTask.completionNotes\" class=\"completion-notes\">\n                整改说明：{{ recordInfo.remediationTask.completionNotes }}\n              </view>\n            </view>\n          </view>\n          \n          <!-- 整改照片展示 -->\n          <view v-if=\"recordInfo.remediationTask.completionPhotos && recordInfo.remediationTask.completionPhotos.length > 0\" class=\"rectification-photos\">\n            <view class=\"photos-header\">\n              <uni-icons type=\"image\" size=\"16\" color=\"#34C759\"></uni-icons>\n              <text class=\"photos-title\">整改照片 ({{ recordInfo.remediationTask.completionPhotos.length }}张)</text>\n        </view>\n            <view class=\"photos-grid-enhanced\">\n              <view \n                v-for=\"(photo, index) in recordInfo.remediationTask.completionPhotos\" \n                :key=\"index\" \n                class=\"photo-item-enhanced\"\n                @click=\"previewRectificationPhoto(index)\"\n              >\n                <view class=\"photo-container\">\n                  <image \n                    :src=\"photo.url\" \n                    mode=\"aspectFill\" \n                    class=\"photo-image-enhanced\"\n                    @error=\"onRectificationPhotoError(index)\"\n                  ></image>\n                  \n                  <view v-if=\"photo.loadError\" class=\"photo-error\" @click.stop=\"retryRectificationPhotoLoad(index)\">\n                    <uni-icons type=\"reload\" size=\"24\" color=\"#8E8E93\"></uni-icons>\n                    <text class=\"error-text\">点击重试</text>\n                  </view>\n                  \n                  <view v-else class=\"photo-overlay-enhanced\">\n                    <view class=\"overlay-content\">\n                      <uni-icons type=\"eye\" size=\"24\" color=\"white\"></uni-icons>\n                      <text class=\"overlay-text\">预览</text>\n                    </view>\n                  </view>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 复查结果（如有） -->\n    <view v-if=\"recordType === 'inspection' && recordInfo.reviewInfo && recordInfo.reviewInfo.hasReview\" class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"card-title\">复查结果</view>\n        <view class=\"result-status-badge badge-passed\">\n          <uni-icons type=\"checkmarkempty\" size=\"14\" color=\"white\"></uni-icons>\n          <text>复查通过</text>\n        </view>\n      </view>\n      <view class=\"card-body\">\n        <view class=\"review-section-standalone\">\n          <!-- 复查基本信息 -->\n          <view class=\"info-grid-enhanced\">\n            <view class=\"info-item-enhanced\">\n              <view class=\"info-icon icon-review\">\n                <uni-icons type=\"person\" size=\"18\" color=\"white\"></uni-icons>\n              </view>\n              <view class=\"info-content\">\n                <view class=\"info-label-enhanced\">复查人员</view>\n                <view class=\"info-value-enhanced\">{{ recordInfo.reviewInfo.reviewerName }}</view>\n              </view>\n            </view>\n            \n            <view class=\"info-item-enhanced\">\n              <view class=\"info-icon icon-review\">\n                <uni-icons type=\"calendar\" size=\"18\" color=\"white\"></uni-icons>\n              </view>\n              <view class=\"info-content\">\n                <view class=\"info-label-enhanced\">复查时间</view>\n                <view class=\"info-value-enhanced\">{{ recordInfo.reviewInfo.reviewTime }}</view>\n              </view>\n            </view>\n            \n            <view v-if=\"recordInfo.reviewInfo.reviewRating > 0\" class=\"info-item-enhanced rating-item-enhanced\">\n              <view class=\"info-icon icon-rating\">\n                <uni-icons type=\"star-filled\" size=\"18\" color=\"white\"></uni-icons>\n              </view>\n              <view class=\"info-content\">\n                <view class=\"info-label-enhanced\">复查评分</view>\n                <view class=\"rating-display-enhanced\">\n                  <view class=\"rating-number\">{{ recordInfo.reviewInfo.reviewRating }}</view>\n                  <view class=\"rating-stars\">\n                    <uni-icons \n                      v-for=\"star in 5\" \n                      :key=\"star\"\n                      type=\"star-filled\" \n                      size=\"14\" \n                      :color=\"star <= recordInfo.reviewInfo.reviewRating ? '#FFD700' : '#E5E5EA'\"\n                    ></uni-icons>\n                  </view>\n                  <view class=\"rating-desc\">{{ getRatingDescription(recordInfo.reviewInfo.reviewRating) }}</view>\n                </view>\n              </view>\n            </view>\n          </view>\n          \n          <!-- 复查意见 -->\n          <view v-if=\"recordInfo.reviewInfo.reviewComments\" class=\"review-comments-section\">\n            <view class=\"section-title\">复查意见</view>\n            <view class=\"review-comment-content\">\n              {{ recordInfo.reviewInfo.reviewComments }}\n            </view>\n          </view>\n          \n          <!-- 最终结论 -->\n          <view class=\"review-conclusion-standalone\">\n            <view class=\"conclusion-icon\">\n              <uni-icons type=\"checkmarkempty\" size=\"24\" color=\"#34C759\"></uni-icons>\n            </view>\n            <text class=\"conclusion-text\">整改合格，问题已解决</text>\n          </view>\n          \n          <!-- 复查照片 -->\n          <view v-if=\"recordInfo.reviewInfo.reviewPhotos && recordInfo.reviewInfo.reviewPhotos.length > 0\" class=\"review-photos-section\">\n            <view class=\"section-title\">\n              <uni-icons type=\"image\" size=\"16\" color=\"#5AC8FA\"></uni-icons>\n              <text>复查照片 ({{ recordInfo.reviewInfo.reviewPhotos.length }}张)</text>\n            </view>\n            <view class=\"photos-grid-enhanced\">\n              <view \n                v-for=\"(photo, index) in recordInfo.reviewInfo.reviewPhotos\" \n                :key=\"index\" \n                class=\"photo-item-enhanced\"\n                @click=\"previewReviewPhoto(index)\"\n              >\n                <view class=\"photo-container\">\n                  <image \n                    :src=\"photo.url\" \n                    mode=\"aspectFill\" \n                    class=\"photo-image-enhanced\"\n                    @error=\"onReviewPhotoError(index)\"\n                  ></image>\n                  \n                  <view v-if=\"photo.loadError\" class=\"photo-error\" @click.stop=\"retryReviewPhotoLoad(index)\">\n                    <uni-icons type=\"reload\" size=\"24\" color=\"#8E8E93\"></uni-icons>\n                    <text class=\"error-text\">点击重试</text>\n                  </view>\n                  \n                  <view v-else class=\"photo-overlay-enhanced\">\n                    <view class=\"overlay-content\">\n                      <uni-icons type=\"eye\" size=\"24\" color=\"white\"></uni-icons>\n                      <text class=\"overlay-text\">预览</text>\n                    </view>\n                  </view>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 操作历史 -->\n    <view class=\"card\">\n      <view class=\"card-header\">\n        <view class=\"card-title\">操作历史</view>\n      </view>\n      <view class=\"card-body\">\n        <view class=\"timeline\">\n          <view \n            v-for=\"(event, index) in recordInfo.history\" \n            :key=\"index\" \n            class=\"timeline-item\"\n          >\n            <view class=\"timeline-dot\" :class=\"['dot-' + event.type]\"></view>\n            <view class=\"timeline-content\">\n              <view class=\"timeline-title\">{{ event.title }}</view>\n              <view class=\"timeline-desc\">{{ event.description }}</view>\n              <view class=\"timeline-time\">{{ event.time }}</view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 操作按钮 -->\n    <view v-if=\"showEditSection\" class=\"action-section\">\n      <!-- 修改清理记录按钮 -->\n      <view v-if=\"recordType === 'cleaning' && editPermission.canEdit\" \n            class=\"action-btn primary\" \n            @click=\"editRecord\">\n        <uni-icons type=\"compose\" size=\"20\" color=\"white\"></uni-icons>\n        <text>修改记录</text>\n      </view>\n      \n      <!-- 不可修改提示 -->\n      <view v-if=\"recordType === 'cleaning' && !editPermission.canEdit && editPermission.message\" \n            class=\"edit-disabled-tip\">\n        <uni-icons type=\"info-filled\" size=\"16\" color=\"#FF9500\"></uni-icons>\n        <text>{{ editPermission.message }}</text>\n      </view>\n    </view>\n\n    <!-- 底部安全间距 -->\n    <view class=\"bottom-safe-area\"></view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { callCloudFunction } from '@/utils/auth.js';\n\nexport default {\n  name: 'RecordDetail',\n  data() {\n    return {\n      recordId: '',\n      recordType: 'cleaning', // 'cleaning' 或 'inspection'\n      loading: false,\n      loadError: false,\n      dataLoaded: false, // 数据是否已加载完成\n      editPermission: { canEdit: false, message: '', checking: true }, // 编辑权限\n      issueDescExpanded: false, // 问题描述是否展开\n      \n      // 历史记录相关参数\n      isHistoricalRecord: false, // 是否为历史记录\n      recordWeek: null, // 记录所属周次\n      cleaningDate: null, // 清理日期\n      \n      // 性能优化缓存\n      processCache: null, // 数据处理缓存\n      basicDataLoaded: false, // 基本数据是否已加载\n      \n      recordInfo: {\n        id: '',\n        areaId: '',\n        areaName: '',\n        operatorName: '',\n        operationTime: '',\n        operationTimestamp: '',\n        status: '',\n        photos: [],\n        notes: '',\n        // 清理记录特有字段\n        weekPeriod: '',\n        // 检查记录特有字段\n        rating: 0,\n        result: '',\n        hasIssues: false,\n        issueType: '',\n        issueDescription: '',\n        summary: '',\n        remediationTask: null,\n        // 操作历史\n        history: []\n      }\n    }\n  },\n  computed: {\n    // 是否显示编辑区域\n    showEditSection() {\n      // 只有权限检查完成后才显示编辑区域\n      return this.recordType === 'cleaning' && !this.editPermission.checking && (this.editPermission.canEdit || this.editPermission.message);\n    },\n    \n    // 状态徽章类名\n    statusBadgeClass() {\n      return {\n        'status-completed': this.recordInfo.status === 'completed' || this.recordInfo.status === 'verified' || this.recordInfo.status === 'reviewed',\n        'status-pending': this.recordInfo.status === 'pending',\n        'status-pending-rectification': this.recordInfo.status === 'pending_rectification' || this.recordInfo.status === 'rectification_completed',\n        'status-cancelled': this.recordInfo.status === 'cancelled'\n      };\n    },\n    \n    // 检查结果徽章类名\n    resultBadgeClass() {\n      if (this.recordInfo.result === 'passed') {\n        return 'badge-passed';\n      } else if (this.recordInfo.result === 'issues') {\n        return 'badge-issues';\n      }\n      return '';\n    },\n    \n    // 整改任务徽章类名\n    taskBadgeClass() {\n      if (!this.recordInfo.remediationTask) return '';\n      const status = this.recordInfo.remediationTask.status;\n      if (status === 'pending_assignment') {\n        return 'task-badge-pending';\n      } else if (status === 'pending_rectification') {\n        return 'task-badge-pending-rectification';\n      } else if (status === 'in_progress') {\n        return 'task-badge-pending';\n      } else if (status === 'pending_review') {\n        return 'task-badge-pending';\n      } else if (status === 'verified') {\n        return 'task-badge-completed';\n      } else if (status === 'rejected') {\n        return 'task-badge-missed';\n      } else if (status === 'overdue') {\n        return 'task-badge-missed';\n      }\n      return 'task-badge-pending';\n    },\n    \n    // 任务状态值类名\n    taskStatusValueClass() {\n      if (!this.recordInfo.remediationTask) return '';\n      const status = this.recordInfo.remediationTask.status;\n      if (status === 'not_cleaned') {\n        return 'task-status-not-cleaned';\n      } else if (status === 'pending') {\n        return 'task-status-pending';\n      } else if (status === 'pending_rectification') {\n        return 'task-status-pending-rectification';\n      } else if (status === 'completed') {\n        return 'task-status-completed';\n      } else if (status === 'missed') {\n        return 'task-status-missed';\n      }\n      return '';\n    }\n  },\n  onLoad(options) {\n    if (options.id && options.type) {\n      this.recordId = options.id;\n      this.recordType = options.type;\n      \n      // 接收历史记录相关参数\n      this.isHistoricalRecord = options.isHistorical === 'true' || false;\n      this.recordWeek = options.week || null;\n      this.cleaningDate = options.cleaningDate || null;\n      \n      this.loadRecordDetail();\n    }\n    \n    // 监听清理记录更新事件\n    uni.$on('cleaningRecordUpdated', this.handleRecordUpdated);\n  },\n  onUnload() {\n    // 移除事件监听\n    uni.$off('cleaningRecordUpdated', this.handleRecordUpdated);\n  },\n  onShow() {\n    // 页面显示时不需要自动刷新，依赖事件驱动的刷新机制\n    // 只有当记录真正被更新时，通过 uni.$emit('cleaningRecordUpdated') 事件来刷新\n  },\n  methods: {\n    // 获取页面标题\n    getPageTitle() {\n      return this.recordType === 'cleaning' ? '清理记录详情' : '检查记录详情';\n    },\n\n    // 加载记录详情 - 性能优化版本\n    async loadRecordDetail() {\n      try {\n        this.loadError = false;\n        this.loading = true;\n        \n        // 初始化处理缓存\n        this.initProcessCache();\n        \n        if (this.recordType === 'cleaning') {\n          await this.loadCleaningRecordDetailOptimized();\n        } else {\n          await this.loadInspectionRecordDetailOptimized();\n        }\n        \n        this.dataLoaded = true;        \n      } catch (error) {\n        this.loadError = true;\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 初始化数据处理缓存\n    initProcessCache() {\n      if (!this.processCache) {\n        this.processCache = {\n          // 问题类型映射缓存\n          categoryMap: {\n            'equipment': '设备问题',\n            'cleanliness': '清洁问题', \n            'organization': '整理问题',\n            'safety': '安全问题',\n            'environment': '环境问题',\n            'standardization': '标识问题',\n            'other': '其他问题'\n          },\n          // 状态映射缓存\n          statusMaps: {\n            record: {\n              'completed': '已完成',\n              'pending_rectification': '待整改',\n              'rectification_completed': '整改已提交',\n              'verified': '已确认',\n              'reviewed': '已审核',\n              'pending': '待处理',\n              'cancelled': '已取消'\n            },\n            result: {\n              'passed': '检查通过',\n              'issues': '发现问题'\n            },\n            task: {\n              'pending_assignment': '待分配',\n              'pending_rectification': '待整改',\n              'in_progress': '整改中',\n              'pending_review': '待复查',\n              'completed': '已完成',\n              'verified': '已确认',\n              'rejected': '已拒绝',\n              'overdue': '已逾期'\n            },\n            taskIcon: {\n              'pending_assignment': 'info',\n              'pending_rectification': 'flag',\n              'in_progress': 'reload',\n              'pending_review': 'eye',\n              'completed': 'checkmarkempty',\n              'verified': 'checkmarkempty',\n              'rejected': 'close',\n              'overdue': 'close'\n            }\n          },\n          // 常用日期格式化函数\n          formatters: {\n            dateTime: this.createDateTimeFormatter(),\n            date: this.createDateFormatter()\n          }\n        };\n      }\n    },\n\n    // 创建优化的日期时间格式化器\n    createDateTimeFormatter() {\n      return (dateString) => {\n        if (!dateString || dateString === 'null' || dateString === 'undefined') return '--';\n        \n        try {\n          const date = new Date(dateString);\n          if (isNaN(date.getTime())) return '--';\n          \n          return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;\n        } catch (error) {\n          return '--';\n        }\n      };\n    },\n\n    // 创建优化的日期格式化器\n    createDateFormatter() {\n      return (dateString) => {\n        if (!dateString || dateString === 'null' || dateString === 'undefined') return '--';\n        \n        try {\n          const date = new Date(dateString);\n          if (isNaN(date.getTime())) return '--';\n          \n          return `${date.getMonth() + 1}月${date.getDate()}日`;\n        } catch (error) {\n          return '--';\n        }\n      };\n    },\n\n    // 优化的清理记录详情加载\n    async loadCleaningRecordDetailOptimized() {\n      const result = await callCloudFunction('hygiene-cleaning', {\n        action: 'getCleaningRecordDetail',\n        data: {\n          record_id: this.recordId\n        }\n      });\n\n      if (result && result.success && result.data) {\n        const record = result.data;\n        \n        // 使用缓存的格式化器\n        const dateTimeFormatter = this.processCache.formatters.dateTime;\n        \n        // 快速计算周期信息（避免复杂的日期计算）\n        const cleanDate = new Date(record.cleaning_date);\n        const weekPeriod = this.calculateWeekPeriodOptimized(cleanDate);\n\n        // 直接构建基本记录信息\n        this.recordInfo = {\n          id: record._id,\n          areaId: record.area_id,\n          areaName: record.area_name || '未知责任区',\n          operatorName: record.cleaner_name || record.user_name || '未知',\n          operationTime: dateTimeFormatter(record.cleaning_date),\n          operationTimestamp: record.cleaning_date,\n          weekPeriod: weekPeriod,\n          status: 'completed',\n          photos: this.processPhotos(record.photos || []),\n          notes: record.remark || '',\n          history: [{\n            type: 'create',\n            title: '提交清理记录',\n            description: `${record.cleaner_name || record.user_name || '用户'}提交了清理记录，包含${(record.photos || []).length}张照片`,\n            time: dateTimeFormatter(record.cleaning_date)\n          }]\n        };\n        \n        // 异步检查编辑权限，不阻塞页面显示\n        this.checkEditPermissionAsync();\n      } else {\n        throw new Error('获取清理记录详情失败');\n      }\n    },\n\n    // 优化的周期计算\n    calculateWeekPeriodOptimized(date) {\n      const year = date.getFullYear();\n      const weekNum = Math.ceil((date - new Date(year, 0, 1)) / (7 * 24 * 60 * 60 * 1000));\n      const weekStart = new Date(date);\n      weekStart.setDate(date.getDate() - date.getDay() + 1);\n      const weekEnd = new Date(weekStart);\n      weekEnd.setDate(weekStart.getDate() + 6);\n      \n      return `第${weekNum}周 (${weekStart.getMonth() + 1}月${weekStart.getDate()}日-${weekEnd.getMonth() + 1}月${weekEnd.getDate()}日)`;\n    },\n\n    // 优化的照片处理\n    processPhotos(photos) {\n      return photos.map(photo => ({\n        url: photo.url || photo,\n        name: photo.name || '清理照片',\n        loadError: false\n      }));\n    },\n\n    // 异步检查编辑权限\n    async checkEditPermissionAsync() {\n      try {\n        await this.checkEditPermission();\n      } catch (error) {\n        // 权限检查失败不影响主要功能\n\n      }\n    },\n\n    // 加载清理记录详情 - 保留原方法作为备用\n    async loadCleaningRecordDetail() {\n      const result = await callCloudFunction('hygiene-cleaning', {\n        action: 'getCleaningRecordDetail',\n        data: {\n          record_id: this.recordId\n        }\n      });\n\n      if (result && result.success && result.data) {\n        const record = result.data;\n        const cleanDate = new Date(record.cleaning_date);\n        \n        // 计算周期信息\n        const weekNum = this.getWeekNumber(cleanDate);\n        const weekStart = this.getWeekStart(cleanDate);\n        const weekEnd = this.getWeekEnd(cleanDate);\n        const weekPeriod = `第${weekNum}周 (${weekStart.getMonth() + 1}月${weekStart.getDate()}日-${weekEnd.getMonth() + 1}月${weekEnd.getDate()}日)`;\n\n        this.recordInfo = {\n          id: record._id,\n          areaId: record.area_id, // 用于权限检查\n          areaName: record.area_name || '未知责任区',\n          operatorName: record.cleaner_name || record.user_name || '未知',\n          operationTime: this.formatDateTime(record.cleaning_date),\n          operationTimestamp: record.cleaning_date, // 保存原始时间戳用于API调用\n          weekPeriod: weekPeriod,\n          status: 'completed',\n          photos: (record.photos || []).map(photo => ({\n            url: photo.url || photo,\n            name: photo.name || '清理照片',\n            loadError: false\n          })),\n          notes: record.remark || '',\n          history: [\n            {\n              type: 'create',\n              title: '提交清理记录',\n              description: `${record.cleaner_name || record.user_name || '用户'}提交了清理记录，包含${(record.photos || []).length}张照片`,\n              time: this.formatDateTime(record.cleaning_date)\n            }\n          ]\n        };\n        \n        // 加载完记录详情后检查编辑权限\n        await this.checkEditPermission();\n      } else {\n        throw new Error('获取清理记录详情失败');\n      }\n    },\n\n    // 优化的检查记录详情加载\n    async loadInspectionRecordDetailOptimized() {\n      const result = await callCloudFunction('hygiene-inspection', {\n        action: 'getInspectionDetail',\n        data: {\n          id: this.recordId\n        }\n      });\n\n      if (result && result.success && result.data) {\n        const record = result.data;\n        \n        // 使用缓存的数据处理\n        const dateTimeFormatter = this.processCache.formatters.dateTime;\n        const categoryMap = this.processCache.categoryMap;\n        \n        // 快速处理问题信息\n        const { issueType, issueDescription } = this.processIssueInfo(record, categoryMap);\n        \n        // 处理评分和整改任务\n        const { finalRating, remediationTask } = this.processRectificationInfo(record, dateTimeFormatter);\n        \n        // 处理复查信息\n        const reviewInfo = this.processReviewInfo(record, remediationTask, dateTimeFormatter);\n        \n        // 直接构建记录信息\n        this.recordInfo = {\n          id: record._id,\n          areaName: record.area_name || '未知责任区',\n          operatorName: record.inspector_name || '未知',\n          operationTime: dateTimeFormatter(record.inspection_date),\n          operationTimestamp: record.inspection_date,\n          status: record.status || 'completed',\n          rating: finalRating,\n          result: record.has_issues ? 'issues' : 'passed',\n          hasIssues: record.has_issues || false,\n          issueType: issueType,\n          issueDescription: issueDescription,\n          summary: record.summary || '',\n          photos: this.processInspectionPhotos(record.photos || []),\n          remediationTask: remediationTask,\n          // 新增复查信息\n          reviewInfo: reviewInfo,\n          history: this.buildHistoryTimelineOptimized(record, remediationTask, dateTimeFormatter)\n        };\n      } else {\n        throw new Error('获取检查记录详情失败');\n      }\n    },\n\n    // 优化的问题信息处理\n    processIssueInfo(record, categoryMap) {\n      let issueType = '';\n      let issueDescription = '';\n      \n      if (record.has_issues && record.issues && record.issues.length > 0) {\n        const firstIssue = record.issues[0];\n        issueType = categoryMap[firstIssue.category] || firstIssue.category || '其他问题';\n        issueDescription = firstIssue.description || '';\n      }\n      \n      return { issueType, issueDescription };\n    },\n\n    // 优化的整改信息处理\n    processRectificationInfo(record, dateTimeFormatter) {\n      // 基本信息中的评分：只显示原始检查评分，不被复查评分覆盖\n      let finalRating = record.overall_rating || record.score || 0;\n      let remediationTask = null;\n      \n      if (record.has_issues && record.related_rectifications && record.related_rectifications.length > 0) {\n        const relatedRectification = record.related_rectifications.find(rect => \n          rect.inspection_record_id === record._id || \n          (rect.area_id === record.area_id && new Date(rect.created_at) >= new Date(record.inspection_date))\n        );\n        \n        if (relatedRectification) {\n          // 注意：不再覆盖 finalRating，保持原始检查评分\n          // 复查评分将在 reviewInfo 中单独处理\n          \n          remediationTask = {\n            id: relatedRectification._id,\n            status: relatedRectification.status,\n            assignee: relatedRectification.assigned_to_name || '未分配',\n            deadline: dateTimeFormatter(relatedRectification.deadline),\n            completionTime: relatedRectification.submitted_at ? \n              dateTimeFormatter(relatedRectification.submitted_at) : \n              (relatedRectification.completion_date ? dateTimeFormatter(relatedRectification.completion_date) : null),\n            completionNotes: relatedRectification.completion_description || '',\n            completionPhotos: this.processRectificationPhotos(relatedRectification.completion_photos || [])\n          };\n        }\n      }\n      \n      return { finalRating, remediationTask };\n    },\n\n    // 处理复查信息\n    processReviewInfo(record, remediationTask, dateTimeFormatter) {\n      let reviewInfo = null;\n      \n      if (record.has_issues && record.related_rectifications && record.related_rectifications.length > 0) {\n        const rectification = record.related_rectifications[0];\n        \n        if (rectification.review_date && rectification.status === 'verified') {\n          reviewInfo = {\n            hasReview: true,\n            reviewerName: rectification.reviewer_name || '未知',\n            reviewTime: dateTimeFormatter(rectification.review_date),\n            reviewComments: rectification.review_comments || '',\n            reviewRating: rectification.final_rating || rectification.review_rating || 0,\n            reviewResult: rectification.review_result || 'approved',\n            reviewPhotos: this.processRectificationPhotos(rectification.review_photos || [])\n          };\n        }\n      }\n      \n      return reviewInfo;\n    },\n\n    // 优化的检查照片处理\n    processInspectionPhotos(photos) {\n      return photos.map(photo => {\n        if (typeof photo === 'string') {\n          return {\n            url: photo,\n            name: '检查照片',\n            loadError: false\n          };\n        } else if (typeof photo === 'object' && photo !== null) {\n          return {\n            url: photo.url || photo.src || '',\n            name: photo.name || photo.description || '检查照片',\n            loadError: false\n          };\n        }\n        return {\n          url: '',\n          name: '检查照片',\n          loadError: true\n        };\n      }).filter(photo => photo.url);\n    },\n\n    // 处理整改照片\n    processRectificationPhotos(photos) {\n      if (!Array.isArray(photos)) return [];\n      \n      return photos.map(photo => {\n        if (typeof photo === 'string') {\n          return {\n            url: photo,\n            name: '整改照片',\n            loadError: false\n          };\n        } else if (typeof photo === 'object' && photo !== null) {\n          return {\n            url: photo.url || photo.src || '',\n            name: photo.description || photo.name || '整改照片',\n            loadError: false\n          };\n        }\n        return {\n          url: '',\n          name: '整改照片',\n          loadError: true\n        };\n      }).filter(photo => photo.url);\n    },\n\n    // 优化的历史时间线构建\n    buildHistoryTimelineOptimized(record, remediationTask, dateTimeFormatter) {\n      const history = [{\n        type: 'create',\n        title: '开始检查',\n        description: `${record.inspector_name || '检查员'}开始检查`,\n        time: dateTimeFormatter(record.inspection_date)\n      }];\n\n      if (record.has_issues) {\n        history.push({\n          type: 'issue',\n          title: '发现问题',\n          description: '检查中发现问题，需要进行整改',\n          time: dateTimeFormatter(record.inspection_date)\n        });\n        \n        if (remediationTask) {\n          // 1. 创建整改任务\n          history.push({\n            type: 'task',\n            title: '创建整改任务',\n            description: `已分配给 ${remediationTask.assignee} 进行整改`,\n            time: dateTimeFormatter(record.inspection_date)\n          });\n          \n          // 2. 整改过程记录（基于整改任务的详细状态）\n          if (record.related_rectifications && record.related_rectifications.length > 0) {\n            const rectification = record.related_rectifications[0];\n            this.buildRectificationHistory(history, rectification, dateTimeFormatter);\n          } else if (remediationTask.completionTime && remediationTask.completionTime !== '--') {\n            // 兼容旧数据格式\n            this.buildSimpleRectificationHistory(history, remediationTask);\n          }\n        }\n      } else {\n        // 检查通过，无问题发现\n        const completionDescription = record.overall_rating > 0 \n          ? `检查完成，评分${record.overall_rating}分，${this.getRatingDescription(record.overall_rating)}`\n          : '检查完成，责任区状况良好，无发现问题';\n          \n        history.push({\n          type: 'complete',\n          title: '检查通过',\n          description: completionDescription,\n          time: dateTimeFormatter(record.inspection_date)\n        });\n      }\n      \n      return history.sort((a, b) => {\n        const timeA = this.parseTimeForSort(a.time);\n        const timeB = this.parseTimeForSort(b.time);\n        return timeA - timeB;\n      });\n    },\n\n    // 构建详细的整改历史记录\n    buildRectificationHistory(history, rectification, dateTimeFormatter) {\n      // 整改开始\n      if (rectification.created_at) {\n        history.push({\n          type: 'task',\n          title: '开始整改',\n          description: `${rectification.assigned_to_name || '负责人'}开始处理整改任务`,\n          time: dateTimeFormatter(rectification.created_at)\n        });\n      }\n\n      // 整改提交\n      if (rectification.submitted_at) {\n        history.push({\n          type: 'review',\n          title: '提交整改',\n          description: `整改已完成，提交检查员复查`,\n          time: dateTimeFormatter(rectification.submitted_at)\n        });\n      }\n\n      // 复查结果\n      if (rectification.review_date) {\n        const isApproved = rectification.status === 'verified';\n        history.push({\n          type: isApproved ? 'complete' : 'issue',\n          title: isApproved ? '复查通过' : '需重新整改',\n          description: isApproved \n            ? `检查员确认整改合格，问题已解决` \n            : `检查员要求重新整改：${rectification.review_comments || '整改不达标'}`,\n          time: dateTimeFormatter(rectification.review_date)\n        });\n      }\n\n      // 如果有多轮整改，递归处理\n      if (rectification.status === 'rejected' && rectification.resubmitted_at) {\n        history.push({\n          type: 'task',\n          title: '重新整改',\n          description: `根据检查员要求重新进行整改`,\n          time: dateTimeFormatter(rectification.resubmitted_at)\n        });\n      }\n    },\n\n    // 构建简化的整改历史（兼容旧数据）\n    buildSimpleRectificationHistory(history, remediationTask) {\n      const eventType = remediationTask.status === 'verified' ? 'complete' : \n                       remediationTask.status === 'rejected' ? 'issue' : 'review';\n      const eventTitle = remediationTask.status === 'verified' ? '整改完成' :\n                        remediationTask.status === 'rejected' ? '整改被拒绝' : '整改提交';\n      \n      history.push({\n        type: eventType,\n        title: eventTitle,\n        description: remediationTask.status === 'verified' \n          ? '整改任务已完成并通过检查员确认' \n          : remediationTask.status === 'rejected' \n          ? '整改未通过复查，需要重新整改' \n          : '整改任务已完成，等待检查员复查',\n        time: remediationTask.completionTime\n      });\n    },\n\n    // 解析时间用于排序\n    parseTimeForSort(timeStr) {\n      try {\n        const match = timeStr.match(/(\\d+)月(\\d+)日\\s(\\d+):(\\d+)/);\n        if (match) {\n          const year = new Date().getFullYear();\n          const month = parseInt(match[1]);\n          const day = parseInt(match[2]);\n          const hour = parseInt(match[3]);\n          const minute = parseInt(match[4]);\n          return new Date(year, month - 1, day, hour, minute);\n        }\n        return new Date();\n      } catch (error) {\n        return new Date();\n      }\n    },\n\n    // 加载检查记录详情 - 保留原方法作为备用\n    async loadInspectionRecordDetail() {\n      const result = await callCloudFunction('hygiene-inspection', {\n        action: 'getInspectionDetail',\n        data: {\n          id: this.recordId  // 修复：使用正确的参数名\n        }\n      });\n\n      if (result && result.success && result.data) {\n        const record = result.data;\n        \n        // 处理问题信息\n        let issueType = '';\n        let issueDescription = '';\n        \n        if (record.has_issues && record.issues && record.issues.length > 0) {\n          const firstIssue = record.issues[0];\n          // 中文问题类型映射\n          const categoryMap = {\n            'equipment': '设备问题',\n            'cleanliness': '清洁问题', \n            'organization': '整理问题',\n            'safety': '安全问题',\n            'environment': '环境问题',\n            'standardization': '标识问题',\n            'other': '其他问题'\n          };\n          issueType = categoryMap[firstIssue.category] || firstIssue.category || '其他问题';\n          issueDescription = firstIssue.description || '';\n        }\n\n        // 处理评分：保持原始检查评分，不被复查评分覆盖\n        let finalRating = record.overall_rating || record.score || 0;\n        \n        // 处理整改任务信息\n        let remediationTask = null;\n        if (record.has_issues && record.related_rectifications && record.related_rectifications.length > 0) {\n          // 查找与当前检查记录相关的整改记录\n          const relatedRectification = record.related_rectifications.find(rect => \n            rect.inspection_record_id === record._id || \n            (rect.area_id === record.area_id && new Date(rect.created_at) >= new Date(record.inspection_date))\n          );\n          \n          if (relatedRectification) {\n            // 注意：不再覆盖 finalRating，保持原始检查评分\n            // 复查评分将在 reviewInfo 中单独处理\n            \n            // 构建整改任务信息\n            remediationTask = {\n              id: relatedRectification._id,\n              status: relatedRectification.status,\n              assignee: relatedRectification.assigned_to_name || '未分配',\n              deadline: this.formatDateTime(relatedRectification.deadline),\n              // 优先使用 submitted_at，再使用 completion_date\n              completionTime: relatedRectification.submitted_at ? \n                this.formatDateTime(relatedRectification.submitted_at) : \n                (relatedRectification.completion_date ? this.formatDateTime(relatedRectification.completion_date) : null),\n              completionNotes: relatedRectification.completion_description || ''\n            };\n          }\n        }\n\n        this.recordInfo = {\n          id: record._id,\n          areaName: record.area_name || '未知责任区',\n          operatorName: record.inspector_name || '未知',\n          operationTime: this.formatDateTime(record.inspection_date),\n          operationTimestamp: record.inspection_date, // 保存原始时间戳用于API调用\n          status: record.status || 'completed',\n          rating: finalRating, // 使用处理后的最终评分\n          result: record.has_issues ? 'issues' : 'passed',\n          hasIssues: record.has_issues || false,\n          issueType: issueType,\n          issueDescription: issueDescription,\n          summary: record.summary || '',\n          photos: (record.photos || []).map(photo => {\n            // 处理不同的照片数据格式\n            if (typeof photo === 'string') {\n              return {\n                url: photo,\n                name: '检查照片',\n                loadError: false\n              };\n            } else if (typeof photo === 'object' && photo !== null) {\n              return {\n                url: photo.url || photo.src || '',\n                name: photo.name || photo.description || '检查照片',\n                loadError: false\n              };\n            }\n            return {\n              url: '',\n              name: '检查照片',\n              loadError: true\n            };\n          }).filter(photo => photo.url), // 过滤掉空URL的照片\n          remediationTask: remediationTask,\n          history: [\n            {\n              type: 'create',\n              title: '开始检查',\n              description: `${record.inspector_name || '检查员'}开始检查`,\n              time: this.formatDateTime(record.inspection_date)\n            }\n          ]\n        };\n\n        // 如果有问题，添加问题发现历史\n        if (record.has_issues) {\n          this.recordInfo.history.push({\n            type: 'issue',\n            title: '发现问题',\n            description: issueDescription || '检查中发现问题',\n            time: this.formatDateTime(record.inspection_date)\n          });\n          \n          // 添加整改相关的历史记录\n          if (remediationTask) {\n            // 添加整改任务创建记录\n            this.recordInfo.history.push({\n              type: 'task',\n              title: '创建整改任务',\n              description: `已分配给 ${remediationTask.assignee}整改`,\n              time: this.formatDateTime(record.inspection_date)\n            });\n            \n            // 根据整改状态添加相应的历史记录\n            if (remediationTask.completionTime && remediationTask.completionTime !== '--' && remediationTask.completionTime !== 'null') {\n              if (remediationTask.status === 'pending_review') {\n                this.recordInfo.history.push({\n                  type: 'complete',\n                  title: '整改提交',\n                  description: `整改任务已完成并提交复查`,\n                  time: remediationTask.completionTime\n                });\n              } else if (remediationTask.status === 'verified') {\n                this.recordInfo.history.push({\n                  type: 'complete',\n                  title: '整改完成',\n                  description: `整改任务已完成并通过确认`,\n                  time: remediationTask.completionTime\n                });\n              } else if (remediationTask.status === 'rejected') {\n                this.recordInfo.history.push({\n                  type: 'issue',\n                  title: '整改被拒绝',\n                  description: `整改未通过复查，需要重新整改`,\n                  time: remediationTask.completionTime\n                });\n              }\n            }\n          }\n        }\n        \n        // 按时间排序操作历史 - 使用iOS兼容的日期格式\n        this.recordInfo.history.sort((a, b) => {\n          // 解析中文时间格式 \"8月12日 16:23\" 为iOS兼容的日期\n          const parseChineseTime = (timeStr) => {\n            const match = timeStr.match(/(\\d+)月(\\d+)日\\s(\\d+):(\\d+)/);\n            if (match) {\n              const year = new Date().getFullYear();\n              const month = parseInt(match[1]);\n              const day = parseInt(match[2]);\n              const hour = parseInt(match[3]);\n              const minute = parseInt(match[4]);\n              // 使用 Date 构造函数的数字参数，避免字符串解析问题\n              return new Date(year, month - 1, day, hour, minute);\n            }\n            // 如果无法解析，返回当前时间\n            return new Date();\n          };\n          \n          const timeA = parseChineseTime(a.time);\n          const timeB = parseChineseTime(b.time);\n          return timeA - timeB;\n        });\n        \n      } else {\n        throw new Error('获取检查记录详情失败');\n      }\n    },\n\n    // 获取周数\n    getWeekNumber(date) {\n      const onejan = new Date(date.getFullYear(), 0, 1);\n      const millisecsInDay = 86400000;\n      return Math.ceil((((date.getTime() - onejan.getTime()) / millisecsInDay) + onejan.getDay() + 1) / 7);\n    },\n\n    // 获取一周的开始日期（周一）\n    getWeekStart(date) {\n      const d = new Date(date);\n      const day = d.getDay();\n      const diff = d.getDate() - day + (day === 0 ? -6 : 1);\n      const result = new Date(d.setDate(diff));\n      result.setHours(0, 0, 0, 0);\n      return result;\n    },\n\n    // 获取一周的结束日期（周日）\n    getWeekEnd(date) {\n      const d = new Date(date);\n      const day = d.getDay();\n      const diff = d.getDate() - day + (day === 0 ? 0 : 7);\n      const result = new Date(d.setDate(diff));\n      result.setHours(23, 59, 59, 999);\n      return result;\n    },\n\n    // 格式化日期时间\n    formatDateTime(dateString) {\n      if (!dateString || dateString === 'null' || dateString === 'undefined') return '--';\n      \n      try {\n        const date = new Date(dateString);\n        if (isNaN(date.getTime())) {\n          return '--';\n        }\n        \n        return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;\n      } catch (error) {\n        return '--';\n      }\n    },\n\n    // 重试加载\n    retryLoad() {\n      this.dataLoaded = false; // 重置数据加载标记\n      this.loadError = false;\n      this.loadRecordDetail();\n    },\n\n    // 预览照片\n    previewPhoto(index) {\n      const urls = this.recordInfo.photos.map(photo => photo.url);\n      uni.previewImage({\n        urls: urls,\n        current: index\n      });\n    },\n\n    // 照片加载重试\n    retryPhotoLoad(index) {\n      this.$set(this.recordInfo.photos[index], 'loadError', false);\n    },\n\n    // 照片加载失败处理\n    onPhotoError(index) {\n      this.$set(this.recordInfo.photos[index], 'loadError', true);\n    },\n\n    // 检查编辑权限\n    async checkEditPermission() {\n      // 只有清理记录才允许编辑\n      if (this.recordType !== 'cleaning') {\n        this.editPermission = { canEdit: false, message: '', checking: false };\n        return;\n      }\n\n      // 1. 检查是否为历史记录\n      if (this.isHistoricalRecord) {\n        this.editPermission = { \n          canEdit: false, \n          message: '历史记录不允许修改',\n          checking: false\n        };\n        return;\n      }\n\n      // 2. 检查记录时效性（基于清理日期）\n      const timeCheckResult = this.checkRecordTimeValidity();\n      if (!timeCheckResult.canEdit) {\n        this.editPermission = { ...timeCheckResult, checking: false };\n        return;\n      }\n\n      try {\n        // 3. 检查该清理记录是否已经被检查过\n        const areaId = this.recordInfo.areaId;\n        if (!areaId) {\n          this.editPermission = { canEdit: true, message: '', checking: false };\n          return;\n        }\n\n        // 3. 关键检查：该清理记录是否已经被检查员检查过\n        const cleaningRecordTime = new Date(this.recordInfo.operationTimestamp);\n        \n        // 查询该责任区在清理记录之后的检查记录\n        let hasInspectionAfterCleaning = false;\n        \n        try {\n          const inspectionResult = await callCloudFunction('hygiene-inspection', {\n            action: 'getInspectionRecords',\n            data: { \n              area_id: areaId,\n              pageSize: 50\n            }\n          });\n\n          if (inspectionResult && inspectionResult.success && inspectionResult.data) {\n            const records = inspectionResult.data.list;\n            \n            if (Array.isArray(records)) {\n              // 筛选在清理记录之后的检查记录\n              const relatedInspections = records.filter(inspection => {\n                const inspectionTime = new Date(inspection.inspection_date);\n                return inspectionTime > cleaningRecordTime;\n              });\n              \n              hasInspectionAfterCleaning = relatedInspections.length > 0;\n            }\n          }\n        } catch (inspectionError) {\n          console.warn('检查记录查询失败:', inspectionError);\n          // 如果查询失败，为了安全起见，不允许编辑\n          hasInspectionAfterCleaning = true;\n        }\n\n        // 如果清理记录已被检查，则不允许编辑\n        if (hasInspectionAfterCleaning) {\n          this.editPermission = { \n            canEdit: false, \n            message: '该清理记录已被检查员检查，不允许修改',\n            checking: false\n          };\n\n          return;\n        }\n\n        // 4. 检查该责任区是否有相关的整改任务（作为备用检查）\n        const rectificationResult = await callCloudFunction('hygiene-rectification', {\n          action: 'getRectifications',\n          data: { \n            area_id: areaId,\n            pageSize: 10\n          }\n        });\n\n        // 检查是否有进行中的整改任务\n        let hasActiveTasks = false;\n        if (rectificationResult && rectificationResult.success && \n            rectificationResult.data && rectificationResult.data.list) {\n          \n          // 使用已定义的清理记录时间\n          const recordTime = cleaningRecordTime;\n          \n          // 简化逻辑：主要依赖检查记录判断，整改任务作为辅助判断\n          const activeTasks = rectificationResult.data.list.filter(task => {\n            const taskCreatedTime = new Date(task.created_at || task.createdAt);\n            const isTaskAfterRecord = taskCreatedTime > recordTime;\n            \n            // 只检查在清理记录之后的未完成整改任务\n            const blockingStatuses = [\n              'pending_rectification',  // 待整改\n              'pending_assignment',     // 待分配  \n              'in_progress',           // 整改中\n              'pending_review',        // 待复查\n              'rectification_completed' // 整改已完成（等待验证）\n            ];\n            \n            const isBlocking = isTaskAfterRecord && blockingStatuses.includes(task.status);\n            return isBlocking;\n          });\n          hasActiveTasks = activeTasks.length > 0;\n          \n\n        }\n\n        if (hasActiveTasks) {\n          this.editPermission = { \n            canEdit: false, \n            message: '该区域有进行中的整改任务，暂时无法修改清理记录',\n            checking: false\n          };\n        } else {\n          this.editPermission = { canEdit: true, message: '', checking: false };\n        }\n      } catch (error) {\n        // 出错时不允许编辑，避免潜在问题\n        this.editPermission = { \n          canEdit: false, \n          message: '权限检查失败，暂时无法修改',\n          checking: false\n        };\n      }\n    },\n\n    // 检查记录时效性\n    checkRecordTimeValidity() {\n      if (!this.recordInfo.operationTimestamp) {\n        return { canEdit: false, message: '无法确定记录时间，不允许修改' };\n      }\n\n      const now = new Date();\n      const recordTime = new Date(this.recordInfo.operationTimestamp);\n      \n      // 检查是否为本周记录\n      const currentWeekStart = this.getWeekStart(now);\n      const currentWeekEnd = this.getWeekEnd(now);\n      \n      if (recordTime < currentWeekStart || recordTime > currentWeekEnd) {\n        return { \n          canEdit: false, \n          message: '只能修改本周的清理记录' \n        };\n      }\n\n      // 检查是否在24小时内\n      const timeDiff = now.getTime() - recordTime.getTime();\n      const hours24 = 24 * 60 * 60 * 1000;\n      \n      if (timeDiff > hours24) {\n        return { \n          canEdit: false, \n          message: '记录提交超过24小时，不允许修改' \n        };\n      }\n\n      return { canEdit: true, message: '' };\n    },\n\n    // 获取状态文本 - 使用缓存优化\n    getStatusText(status) {\n      if (!this.processCache?.statusMaps) this.initProcessCache();\n      return this.processCache.statusMaps.record[status] || '已完成';\n    },\n\n    // 获取结果文本 - 使用缓存优化\n    getResultText(result) {\n      if (!this.processCache?.statusMaps) this.initProcessCache();\n      return this.processCache.statusMaps.result[result] || result;\n    },\n\n    // 获取任务状态文本 - 使用缓存优化\n    getTaskStatusText(status) {\n      if (!status) return '未知状态';\n      if (!this.processCache?.statusMaps) this.initProcessCache();\n      return this.processCache.statusMaps.task[status] || status;\n    },\n\n    // 获取任务状态图标 - 使用缓存优化\n    getTaskStatusIcon(status) {\n      if (!status) return 'info';\n      if (!this.processCache?.statusMaps) this.initProcessCache();\n      return this.processCache.statusMaps.taskIcon[status] || 'info';\n    },\n\n    // 编辑记录\n    editRecord() {\n      // 跳转到清理提交页面，传入记录ID用于编辑\n      const areaId = this.recordInfo.areaId || this.areaId;\n      const areaType = this.recordInfo.areaType || 'fixed';\n      \n      uni.navigateTo({\n        url: `/pages/6s_pkg/cleaning-upload?mode=edit&recordId=${this.recordId}&areaId=${areaId}&type=${areaType}`\n      });\n    },\n\n    // 获取评分描述 - 使用缓存优化\n    getRatingDescription(rating) {\n      if (!this.processCache?.ratingDescCache) {\n        if (!this.processCache) this.initProcessCache();\n        this.processCache.ratingDescCache = new Map();\n      }\n      \n      // 检查缓存\n      if (this.processCache.ratingDescCache.has(rating)) {\n        return this.processCache.ratingDescCache.get(rating);\n      }\n      \n      // 计算描述\n      let description = '';\n      if (rating === 0) description = '请评分';\n      else if (rating <= 1) description = '较差';\n      else if (rating <= 2) description = '一般';\n      else if (rating <= 3) description = '良好';\n      else if (rating < 5) description = '优秀';  // 4-4.5分都是优秀\n      else if (rating === 5) description = '完美';\n      \n      // 缓存结果\n      this.processCache.ratingDescCache.set(rating, description);\n      return description;\n    },\n\n    // 处理清理记录更新事件\n    handleRecordUpdated(data) {\n      // 如果更新的是当前记录，重新加载数据\n      if (data.recordId === this.recordId) {\n        this.loadRecordDetail();\n      }\n    },\n\n    // 预览整改照片\n    previewRectificationPhoto(index) {\n      const urls = this.recordInfo.remediationTask.completionPhotos.map(photo => photo.url);\n      uni.previewImage({\n        urls: urls,\n        current: index\n      });\n    },\n\n    // 预览复查照片\n    previewReviewPhoto(index) {\n      const urls = this.recordInfo.reviewInfo.reviewPhotos.map(photo => photo.url);\n      uni.previewImage({\n        urls: urls,\n        current: index\n      });\n    },\n\n    // 通用照片错误处理方法\n    handlePhotoError(photoArray, index) {\n      this.$set(photoArray[index], 'loadError', true);\n    },\n\n    // 通用照片重试方法\n    retryPhotoLoad(photoArray, index) {\n      this.$set(photoArray[index], 'loadError', false);\n    },\n\n    // 整改照片加载失败处理\n    onRectificationPhotoError(index) {\n      this.handlePhotoError(this.recordInfo.remediationTask.completionPhotos, index);\n    },\n\n    // 复查照片加载失败处理\n    onReviewPhotoError(index) {\n      this.handlePhotoError(this.recordInfo.reviewInfo.reviewPhotos, index);\n    },\n\n    // 整改照片加载重试\n    retryRectificationPhotoLoad(index) {\n      this.retryPhotoLoad(this.recordInfo.remediationTask.completionPhotos, index);\n    },\n\n    // 复查照片加载重试\n    retryReviewPhotoLoad(index) {\n      this.retryPhotoLoad(this.recordInfo.reviewInfo.reviewPhotos, index);\n    },\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\n\twidth: 100%;\n\toverflow-x: hidden; /* 防止水平滚动 */\n\tbox-sizing: border-box;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;\n}\n\n/* 页面头部 */\n.page-header {\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n  padding: 32rpx 32rpx 40rpx 32rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  color: white;\n  position: relative;\n  z-index: 1000;\n}\n\n.header-content {\n  flex: 1;\n}\n\n.header-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: white;\n  margin-bottom: 8rpx;\n}\n\n.header-subtitle {\n  font-size: 28rpx;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.status-badge-enhanced {\n  display: flex;\n  align-items: center;\n  gap: 6rpx;\n  padding: 6rpx 12rpx;\n  border-radius: 12rpx;\n  font-size: 22rpx;\n  font-weight: 400;\n  background: rgba(255, 255, 255, 0.15);\n  color: rgba(255, 255, 255, 0.95);\n  border: 1rpx solid rgba(255, 255, 255, 0.2);\n\n  &.status-not-cleaned {\n    background: rgba(255, 255, 255, 0.15);\n    color: rgba(255, 255, 255, 0.95);\n    border: 1rpx solid rgba(255, 255, 255, 0.2);\n  }\n  \n  &.status-pending {\n    background: rgba(255, 255, 255, 0.15);\n    color: rgba(255, 255, 255, 0.95);\n    border: 1rpx solid rgba(255, 255, 255, 0.2);\n  }\n  \n  &.status-pending-rectification {\n    background: rgba(255, 255, 255, 0.15);\n    color: rgba(255, 255, 255, 0.95);\n    border: 1rpx solid rgba(255, 255, 255, 0.2);\n  }\n  \n  &.status-completed {\n    background: rgba(255, 255, 255, 0.15);\n    color: rgba(255, 255, 255, 0.95);\n    border: 1rpx solid rgba(255, 255, 255, 0.2);\n  }\n  \n  &.status-missed {\n    background: rgba(255, 255, 255, 0.15);\n    color: rgba(255, 255, 255, 0.95);\n    border: 1rpx solid rgba(255, 255, 255, 0.2);\n  }\n  \n  &.status-cancelled {\n    background: rgba(255, 255, 255, 0.15);\n    color: rgba(255, 255, 255, 0.95);\n    border: 1rpx solid rgba(255, 255, 255, 0.2);\n  }\n}\n\n/* 卡片样式 */\n.card {\n  background: white;\n  border-radius: 16rpx;\n  margin: 24rpx 32rpx 0 32rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.card-header {\n  padding: 32rpx 32rpx 16rpx 32rpx;\n  border-bottom: 1rpx solid #F2F2F7;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 4rpx;\n}\n\n.card-subtitle {\n  font-size: 26rpx;\n  color: #8E8E93;\n}\n\n.card-subtitle-enhanced {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  font-size: 26rpx;\n  color: #8E8E93;\n}\n\n.card-body {\n  padding: 32rpx;\n}\n\n/* 信息列表 */\n.info-list {\n  display: flex;\n  flex-direction: column;\n  gap: 24rpx;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.info-label {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  font-size: 28rpx;\n  color: #1C1C1E;\n}\n\n.info-value {\n  font-size: 28rpx;\n  color: #8E8E93;\n  text-align: right;\n  \n  &.rating-value {\n    display: flex;\n    align-items: center;\n    gap: 4rpx;\n    color: #FFD700;\n  }\n}\n\n/* 增强信息网格 */\n.info-grid-enhanced {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));\n  gap: 24rpx;\n  margin-top: 24rpx;\n}\n\n.info-item-enhanced {\n  display: flex;\n  align-items: center;\n  gap: 16rpx;\n  padding: 20rpx 24rpx;\n  background: #F8F9FA;\n  border-radius: 12rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n}\n\n.info-icon {\n  width: 56rpx;\n  height: 56rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: #007AFF; /* 默认图标背景色 */\n  color: white;\n  font-size: 32rpx;\n\n  &.icon-cleaning {\n    background-color: #34C759; /* 清理图标背景色 */\n  }\n\n  &.icon-inspection {\n    background-color: #FF9500; /* 检查图标背景色 */\n  }\n\n  &.icon-time {\n    background-color: #FF3B30; /* 时间图标背景色 */\n  }\n\n  &.icon-cycle {\n    background-color: #5AC8FA; /* 周期图标背景色 */\n  }\n\n  &.icon-rating {\n    background-color: #FFD700; /* 评分图标背景色 */\n  }\n\n  &.icon-review {\n    background-color: #34C759; /* 复查图标背景色 */\n  }\n}\n\n.info-content {\n  flex: 1;\n}\n\n.info-label-enhanced {\n  font-size: 28rpx;\n  color: #1C1C1E;\n  font-weight: 500;\n  margin-bottom: 4rpx;\n}\n\n.info-value-enhanced {\n  font-size: 28rpx;\n  color: #8E8E93;\n  font-weight: 600;\n}\n\n.rating-item-enhanced {\n  grid-column: span 2; /* 评分项跨两列 */\n}\n\n.period-item-enhanced {\n  grid-column: span 2; /* 所属周期项跨两列 */\n}\n\n.rating-display-enhanced {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  margin-top: 8rpx;\n}\n\n.rating-number {\n  font-size: 36rpx;\n  font-weight: 700;\n  color: #FFD700;\n}\n\n.rating-stars {\n  display: flex;\n  gap: 4rpx;\n}\n\n.rating-desc {\n  font-size: 26rpx;\n  color: #8E8E93;\n  margin-left: 12rpx;\n}\n\n/* 照片网格 */\n.no-photos {\n  padding: 40rpx 0;\n}\n\n.no-photos-enhanced {\n  padding: 40rpx 0;\n}\n\n.photos-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 16rpx;\n}\n\n.photos-grid-enhanced {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr); /* 默认3列布局 */\n  gap: 16rpx;\n}\n\n.photo-item {\n  aspect-ratio: 1;\n  border-radius: 12rpx;\n  overflow: hidden;\n  position: relative;\n  background: #F2F2F7;\n}\n\n.photo-item-enhanced {\n  aspect-ratio: 1;\n  border-radius: 12rpx;\n  overflow: hidden;\n  position: relative;\n  background: #F2F2F7;\n  display: flex; /* 使用flex布局 */\n  flex-direction: column; /* 垂直排列内容 */\n  justify-content: flex-end; /* 底部对齐 */\n  align-items: center; /* 居中 */\n}\n\n.photo-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  border-radius: 12rpx;\n  overflow: hidden;\n}\n\n.photo-image-enhanced {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: opacity 0.3s ease;\n}\n\n.photo-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.3);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.photo-overlay-enhanced {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.3);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n  border-radius: 12rpx; /* 与图片圆角一致 */\n}\n\n.photo-item:active .photo-overlay {\n  opacity: 1;\n}\n\n.photo-item-enhanced:active .photo-overlay-enhanced {\n  opacity: 1;\n}\n\n.overlay-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 4rpx;\n}\n\n.overlay-text {\n  font-size: 24rpx;\n  color: white;\n}\n\n\n\n/* 检查结果 */\n.result-section {\n  display: flex;\n  flex-direction: column;\n  gap: 24rpx;\n}\n\n.result-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.result-label {\n  font-size: 28rpx;\n  color: #1C1C1E;\n  font-weight: 500;\n}\n\n.result-value {\n  font-size: 28rpx;\n  font-weight: 600;\n  \n  &.result-passed {\n    color: #34C759;\n  }\n  \n  &.result-issues {\n    color: #FF3B30;\n  }\n}\n\n/* 问题详情 */\n.issue-details {\n  background: #FFF5F5;\n  border: 1rpx solid #FFE6E6;\n  border-radius: 12rpx;\n  padding: 24rpx;\n}\n\n/* 复查区域样式 */\n.review-section {\n  margin-top: 24rpx;\n  padding-top: 24rpx;\n  border-top: 1rpx solid #E8F5E8;\n  background: #F8FFF8;\n  border-radius: 12rpx;\n  padding: 24rpx;\n}\n\n.review-header {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #34C759;\n  margin-bottom: 16rpx;\n}\n\n/* 复查详情区域 */\n.review-details-section {\n  margin-bottom: 16rpx;\n}\n\n.review-info-row {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n  margin-bottom: 12rpx;\n}\n\n.review-info-item {\n  flex: 1;\n  min-width: 200rpx;\n  display: flex;\n  align-items: center;\n  \n  &.rating-item {\n    flex: 100%;\n  }\n}\n\n.review-label {\n  font-size: 26rpx;\n  color: #8E8E93;\n  font-weight: 500;\n  white-space: nowrap;\n}\n\n.review-value {\n  font-size: 26rpx;\n  color: #1C1C1E;\n  font-weight: 600;\n  margin-left: 8rpx;\n}\n\n.review-rating-display {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  margin-left: 8rpx;\n}\n\n.rating-score {\n  font-size: 28rpx;\n  color: #34C759;\n  font-weight: 700;\n}\n\n.rating-level {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.review-comments {\n  margin-top: 16rpx;\n}\n\n.review-comment-content {\n  font-size: 28rpx;\n  color: #1C1C1E;\n  line-height: 1.5;\n  margin-top: 8rpx;\n  background: white;\n  padding: 16rpx;\n  border-radius: 8rpx;\n  border: 1rpx solid #E8F5E8;\n}\n\n.review-conclusion {\n  text-align: center;\n  padding-top: 16rpx;\n  border-top: 1rpx solid #E8F5E8;\n}\n\n.conclusion-text {\n  font-size: 28rpx;\n  color: #34C759;\n  font-weight: 600;\n}\n\n.review-suffix {\n  font-size: 24rpx;\n  color: #8E8E93;\n  margin-left: 8rpx;\n}\n\n/* 整改任务中的复查信息 */\n.review-details {\n  margin-top: 16rpx;\n  padding-top: 16rpx;\n  border-top: 1rpx solid rgba(52, 199, 89, 0.2);\n}\n\n.review-rating {\n  font-size: 26rpx;\n  color: #34C759;\n  font-weight: 500;\n  margin-bottom: 8rpx;\n}\n\n.review-comments-task {\n  font-size: 26rpx;\n  color: #1C1C1E;\n  line-height: 1.5;\n  margin-bottom: 8rpx;\n}\n\n/* 整改照片和复查照片样式 */\n.rectification-photos,\n.review-photos {\n  margin-top: 24rpx;\n  padding: 24rpx;\n  background: #F8F9FA;\n  border-radius: 12rpx;\n  border: 1rpx solid #E5E7EB;\n}\n\n.photos-header {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  margin-bottom: 16rpx;\n}\n\n.photos-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n}\n\n.rectification-photos .photos-title {\n  color: #34C759;\n}\n\n.review-photos .photos-title {\n  color: #5AC8FA;\n}\n\n/* 独立复查结果区域样式 */\n.review-section-standalone {\n  display: flex;\n  flex-direction: column;\n  gap: 24rpx;\n}\n\n.review-comments-section {\n  margin-top: 24rpx;\n}\n\n.section-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 16rpx;\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n}\n\n.review-conclusion-standalone {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12rpx;\n  padding: 20rpx;\n  background: #F0F9F0;\n  border: 1rpx solid #34C759;\n  border-radius: 12rpx;\n  margin-top: 24rpx;\n}\n\n.conclusion-icon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.review-photos-section {\n  margin-top: 24rpx;\n}\n\n.issue-header {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #FF3B30;\n  margin-bottom: 16rpx;\n}\n\n.issue-time-info {\n  font-size: 26rpx;\n  color: #8E8E93;\n  font-weight: 500;\n  margin-bottom: 4rpx;\n}\n\n.issue-content {\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n}\n\n.issue-type {\n  font-size: 26rpx;\n  color: #FF3B30;\n  font-weight: 500;\n  margin-bottom: 8rpx;\n}\n\n.issue-description {\n  font-size: 26rpx;\n  color: #1C1C1E;\n  line-height: 1.5;\n  font-weight: 400;\n  padding-top: 12rpx;\n  border-top: 1rpx solid #FFE6E6;\n  margin-top: 8rpx;\n  margin-bottom: 4rpx;\n}\n\n.remediation-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-top: 16rpx;\n  border-top: 1rpx solid #FFE6E6;\n}\n\n.remediation-label {\n  font-size: 26rpx;\n  color: #8E8E93;\n}\n\n.remediation-value {\n  font-size: 26rpx;\n  font-weight: 500;\n  \n\n}\n\n/* 说明内容 */\n.notes-content {\n  font-size: 28rpx;\n  color: #1C1C1E;\n  line-height: 1.6;\n  background: #F8F9FA;\n  padding: 24rpx;\n  border-radius: 12rpx;\n}\n\n/* 整改任务 */\n.remediation-task {\n  display: flex;\n  flex-direction: column;\n  gap: 24rpx;\n}\n\n.task-info {\n  display: flex;\n  flex-direction: column;\n  gap: 16rpx;\n}\n\n.task-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.task-label {\n  font-size: 28rpx;\n  color: #1C1C1E;\n}\n\n.task-value {\n  font-size: 28rpx;\n  color: #8E8E93;\n  \n  &.task-status-not-cleaned {\n    color: #8E8E93;\n  }\n  \n  &.task-status-pending {\n    color: #FF9500;\n  }\n  \n  &.task-status-pending-rectification {\n    color: #FF3B30;\n  }\n  \n  &.task-status-completed {\n    color: #34C759;\n  }\n  \n  &.task-status-missed {\n    color: #8B5CF6;\n  }\n}\n\n.task-status-badge {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  font-size: 24rpx;\n  font-weight: 500;\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n  color: white;\n  border: none;\n  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);\n\n  &.task-badge-not-cleaned {\n    background: #8E8E93;\n    color: white;\n    border: none;\n    box-shadow: 0 2rpx 8rpx rgba(142, 142, 147, 0.3);\n  }\n  \n  &.task-badge-pending {\n    background: #FF9500;\n    color: white;\n    border: none;\n    box-shadow: 0 2rpx 8rpx rgba(255, 149, 0, 0.3);\n  }\n  \n  &.task-badge-pending-rectification {\n    background: #FF3B30;\n    color: white;\n    border: none;\n    box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.3);\n  }\n  \n  &.task-badge-completed {\n    background: #34C759;\n    color: white;\n    border: none;\n    box-shadow: 0 2rpx 8rpx rgba(52, 199, 89, 0.3);\n  }\n  \n  &.task-badge-missed {\n    background: #8B5CF6;\n    color: white;\n    border: none;\n    box-shadow: 0 2rpx 8rpx rgba(139, 92, 246, 0.3);\n  }\n}\n\n.completion-info {\n  background: #E8F5E8;\n  border: 1rpx solid #34C759;\n  border-radius: 12rpx;\n  padding: 20rpx;\n}\n\n.completion-header {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  font-size: 30rpx;\n  font-weight: 600;\n  margin-bottom: 12rpx;\n}\n\n/* 根据状态动态设置文字颜色 */\n.completion-header text {\n  color: inherit; /* 继承父元素颜色 */\n}\n\n.completion-details {\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n}\n\n.completion-time,\n.completion-notes {\n  font-size: 26rpx;\n  color: #1C1C1E;\n  line-height: 1.5;\n  margin-bottom: 4rpx;\n}\n\n.completion-time {\n  font-weight: 500;\n  color: #8E8E93;\n}\n\n.completion-notes {\n  color: #1C1C1E;\n  font-weight: 400;\n  padding-top: 12rpx;\n  border-top: 1rpx solid rgba(142, 142, 147, 0.3);\n  margin-top: 8rpx;\n}\n\n/* 时间线 */\n.timeline {\n  position: relative;\n  padding-left: 48rpx;\n}\n\n.timeline-item {\n  position: relative;\n  margin-bottom: 32rpx;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n  \n  &:not(:last-child)::after {\n    content: '';\n    position: absolute;\n    left: -40rpx;\n    top: 32rpx;\n    width: 2rpx;\n    height: calc(100% + 16rpx);\n    background: #E5E5EA;\n  }\n}\n\n.timeline-dot {\n  position: absolute;\n  left: -48rpx;\n  top: 8rpx;\n  width: 16rpx;\n  height: 16rpx;\n  border-radius: 50%;\n  \n  &.dot-create {\n    background: #007AFF;\n  }\n  \n  &.dot-review {\n    background: #34C759;\n  }\n  \n  &.dot-issue {\n    background: #FF3B30;\n  }\n  \n  &.dot-task {\n    background: #FF9500;\n  }\n  \n  &.dot-complete {\n    background: #34C759;\n  }\n  \n  &.dot-review {\n    background: #5AC8FA;\n  }\n}\n\n.timeline-content {\n  background: #F8F9FA;\n  padding: 20rpx;\n  border-radius: 12rpx;\n}\n\n.timeline-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 8rpx;\n}\n\n.timeline-desc {\n  font-size: 26rpx;\n  color: #8E8E93;\n  line-height: 1.4;\n  margin-bottom: 8rpx;\n}\n\n.timeline-time {\n  font-size: 24rpx;\n  color: #C7C7CC;\n}\n\n/* 操作按钮 */\n.action-section {\n  padding: 32rpx;\n  display: flex;\n  flex-direction: column;\n  gap: 16rpx;\n}\n\n.action-btn {\n  width: 100%;\n  height: 88rpx;\n  border-radius: 16rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12rpx;\n  font-size: 28rpx;\n  font-weight: 600;\n  \n  &.warning {\n    background: #FF9500;\n    color: white;\n  }\n  \n  &.primary {\n    background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n    color: white;\n  }\n  \n  &.danger {\n    background: #FF3B30;\n    color: white;\n  }\n  \n  &:active {\n    transform: scale(0.98);\n  }\n\n  &.btn-loading {\n    opacity: 0.7;\n    pointer-events: none;\n  }\n}\n\n/* 编辑禁用提示 */\n.edit-disabled-tip {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n  padding: 20rpx 24rpx;\n  background: #FFF3E0;\n  border: 1rpx solid #FFB74D;\n  border-radius: 12rpx;\n  font-size: 28rpx;\n  color: #F57C00;\n  line-height: 1.4;\n}\n\n/* 检查结果状态徽章 */\n.result-status-badge {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  font-size: 24rpx;\n  font-weight: 500;\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n  color: white;\n  border: none;\n  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);\n\n  &.badge-passed {\n    background: #34C759;\n    color: white;\n    border: none;\n    box-shadow: 0 2rpx 8rpx rgba(52, 199, 89, 0.3);\n  }\n  \n  &.badge-issues {\n    background: #FF3B30;\n    color: white;\n    border: none;\n    box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.3);\n  }\n}\n\n/* 无问题显示 */\n.no-issue-display {\n  display: flex;\n  align-items: center;\n  padding: 32rpx 24rpx;\n  background: #F8F9FA;\n  border-radius: 12rpx;\n  gap: 16rpx;\n}\n\n.no-issue-icon {\n  flex-shrink: 0;\n}\n\n.no-issue-content {\n  flex: 1;\n}\n\n.no-issue-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 4rpx;\n}\n\n.no-issue-desc {\n  font-size: 28rpx;\n  color: #8E8E93;\n  line-height: 1.4;\n  font-weight: 400;\n}\n\n\n\n\n\n/* 错误状态 */\n.error-container {\n  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 32rpx;\n}\n\n.retry-btn {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  background: #007AFF;\n  color: white;\n  padding: 16rpx 32rpx;\n  border-radius: 12rpx;\n  font-size: 28rpx;\n  font-weight: 500;\n  \n  &:active {\n    transform: scale(0.95);\n  }\n}\n\n/* 照片错误状态 */\n.photo-error {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  border-radius: 12rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: 10;\n}\n\n.error-text {\n  color: white;\n  font-size: 24rpx;\n  margin-top: 12rpx;\n}\n\n/* 文本展开/收起 */\n.text-collapsed {\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 3; /* 默认显示3行 */\n  line-clamp: 3; /* 现代浏览器标准属性 */\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.expand-btn {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  font-size: 26rpx;\n  color: #007AFF;\n  margin-top: 12rpx;\n  cursor: pointer;\n}\n\n/* 底部安全间距 */\n.bottom-safe-area {\n  height: 40rpx;\n}\n\n/* 内容区域加载状态 */\n.content-loading {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100vh;\n  background: rgba(255, 255, 255, 0.95);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 999;\n  pointer-events: auto;\n}\n\n.loading-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 16rpx;\n}\n\n.loading-spinner {\n  width: 40rpx;\n  height: 40rpx;\n  border: 4rpx solid #E5E7EB;\n  border-top: 4rpx solid #007AFF;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n  text-align: center;\n  margin-top: 8rpx;\n}\n\n/* 内容区域错误状态 */\n.content-error {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100vh;\n  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 999;\n}\n\n/* H5浏览器端优化 - 条件编译 */\n/* #ifdef H5 */\n@media screen and (min-width: 600px) {\n  .photos-grid-enhanced {\n    max-width: 480px;\n  }\n  \n  .photo-item-enhanced {\n    max-width: 150px;\n    max-height: 150px;\n  }\n}\n/* #endif */\n\n/* 响应式调整 */\n@media (max-width: 600rpx) {\n  .page-header {\n    padding: 24rpx 16rpx;\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 16rpx;\n  }\n  \n  .card {\n    margin: 24rpx 16rpx 0 16rpx;\n  }\n  \n  .card-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 12rpx;\n  }\n  \n  .action-section {\n    padding: 24rpx 16rpx;\n  }\n  \n  .info-grid-enhanced {\n    grid-template-columns: 1fr;\n    gap: 16rpx;\n  }\n  \n  .rating-item-enhanced,\n  .period-item-enhanced {\n    grid-column: span 1;\n  }\n  \n  .photos-grid-enhanced {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 16rpx;\n  }\n}\n\n/* 小屏设备优化 */\n@media (max-width: 400rpx) {\n  .photos-grid-enhanced {\n    grid-template-columns: 1fr;\n  }\n  \n  .info-grid-enhanced {\n    grid-template-columns: 1fr;\n  }\n  \n  .rating-display-enhanced {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8rpx;\n  }\n  \n  .status-badge-enhanced,\n  .result-status-badge,\n  .task-status-badge {\n    font-size: 22rpx;\n    padding: 6rpx 12rpx;\n  }\n}\n\n\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./record-detail.vue?vue&type=style&index=0&id=9f2cd4fe&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./record-detail.vue?vue&type=style&index=0&id=9f2cd4fe&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775845139\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}