{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/patrol_pkg/shift/edit.vue?ca1f", "webpack:///D:/Xwzc/pages/patrol_pkg/shift/edit.vue?3072", "webpack:///D:/Xwzc/pages/patrol_pkg/shift/edit.vue?5215", "webpack:///D:/Xwzc/pages/patrol_pkg/shift/edit.vue?314e", "uni-app:///pages/patrol_pkg/shift/edit.vue", "webpack:///D:/Xwzc/pages/patrol_pkg/shift/edit.vue?5cfe", "webpack:///D:/Xwzc/pages/patrol_pkg/shift/edit.vue?b242"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "shift_id", "formData", "name", "start_time", "end_time", "is_cross_day", "status", "rounds", "rules", "required", "errorMessage", "currentRound", "round", "time", "day_offset", "duration", "dayOptions", "isEditingRound", "editingRoundIndex", "onLoad", "uni", "title", "icon", "setTimeout", "methods", "onStartTimeChange", "onEndTimeChange", "getShiftDetail", "PatrolApi", "res", "console", "addRound", "lastHour", "lastMinute", "newHour", "suggestedTime", "removeRound", "validateRoundDuration", "roundHour", "roundMinute", "startHour", "startMinute", "endHour", "endMinute", "timeErrorMessage", "isTimeInvalid", "content", "showCancel", "confirmText", "success", "isEndTimeInvalid", "endTimeErrorMessage", "message", "onRoundTimeChange", "onDurationChange", "onRoundDayChange", "calculateRoundDayOffset", "recalculateAllRoundOffsets", "editRound", "confirmRound", "onStatusChange", "closeRoundPopup", "validateForm", "reject", "invalidMessage", "hasInvalidRound", "resolve", "handleCancel", "handleSubmit", "i", "isNaN", "getActualDateDisplay", "onCrossDayChange", "submitFormData", "submitData", "pages", "prevPage", "complete"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,8RAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrEA;AAAA;AAAA;AAAA;AAA8lB,CAAgB,wnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACiOlnB;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAN;UACAM,QACA;YACAC;YACAC;UACA;QAEA;QACAP;UACAK,QACA;YACAC;YACAC;UACA;QAEA;QACAN;UACAI,QACA;YACAC;YACAC;UACA;QAEA;MACA;MACA;MACAC;QACAC;QACAV;QACAW;QACAC;QACAC;QAAA;QACAT;MACA;MAEA;MACAU;MAEAC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;MACA;MACA;IACA;MACAC;QACAC;QACAC;MACA;MACAC;QACAH;MACA;IACA;EACA;EACAI;IACA;IACAC;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAP;kBACAC;gBACA;gBAAA;gBAAA;gBAAA,OAGAO;cAAA;gBAAAC;gBAEA;kBACA;kBACA9B,iBAEA;kBACA;oBACAG;oBACA;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;kBACA;;kBAEA;kBACA;oBACA;sBAAA;wBACAK;wBACAV;wBACAW;wBACAC;wBACAC;wBACAT;sBACA;oBAAA;;oBAEA;oBACA;sBAAA;oBAAA;kBACA;;kBAEA;kBACA;oBACA;kBACA;gBACA;kBACAc;oBACAC;oBACAC;kBACA;kBACAC;oBACAH;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAU;gBACAV;kBACAC;kBACAC;gBACA;gBACAC;kBACAH;gBACA;cAAA;gBAAA;gBAEAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAW;MACA;;MAEA;MACA;MAEA;QACA;QACA;QACA;UAAA;UAAAC;UAAAC;;QAEA;QACA;QACA;;QAEA;QACA;UACAC;QACA;QAEAC;MACA;MAEA;QACAvB;QACAV;QACAW;QACAC;QAAA;QACAC;QAAA;QACAT;MACA;;MAEA;MACA;QACA;MACA;MAEA;IACA;IAEA;IACA8B;MACA;;MAEA;MACA;QACAxB;MACA;IACA;IAEA;IACAyB;MACA;;MAEA;MACA;QAAA;QAAAC;QAAAC;MACA;;MAEA;MACA;QAAA;QAAAC;QAAAC;MACA;QAAA;QAAAC;QAAAC;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;MAEA;QACA;QACA;UACA;UACA;YACA;YACA;YACA;YAEAC;YACAA;YACAC;UACA;QACA;UACA;UACA;YACA;YACA;YACA;YAEA;YACA;YACA;YAEAD;YACAA;YACAA;YACAC;UACA;QACA;MACA;QACA;QACA;UACA;UACA;UACA;UAEAD;UACAA;UACAC;QACA;MACA;MAEA;QACAzB;UACAC;UACAyB;UACAC;UACAC;UACAC;YACA;cACA;YAAA;UAEA;QACA;QACA;MACA;;MAEA;MACA;MACA;MAEA;QACA;QACA;UACA;UACA;YACA;YACA;YACA;YAEAC;YACAC;UACA;QACA;UACA;UACA;YACA;YACA;YACA;YAEAD;YACAC;UACA;QACA;MACA;QACA;QACA;UACA;UACA;UACA;UAEAD;UACAC;QACA;MACA;MAEA;QACA;QACA;QACA;QACA;QAEA;QACAC;QACAA;QAEAhC;UACAC;UACAyB;UACAC;UACAC;UACAC;YACA;cACA;YAAA;UAEA;QACA;QACA;MACA;;MAEA;MACA;QACArC;QACA;QACAQ;UACAC;UACAyB;UACAC;UACAC;QACA;MACA;MAEA;IACA;IAEA;IACAK;MACA;;MAEA;MACA;QACA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA5C;QACA;MACA;;MAEA;MACA;QAAA;QAAA4B;QAAAC;MACA;QAAA;QAAAC;QAAAC;MACA;MACA;;MAEA;MACA;QAAA;QAAAL;QAAAC;MACA;;MAEA;MACA;;MAEA;MACA;MAEA;QACA;QACA;UACA;UACA3B;QACA;UACAA;QACA;MACA;QACA;QACA;UACA;UACAA;QACA;UACAA;QACA;MACA;IACA;IAEA;IACA6C;MAAA;MACA;;MAEA;MACA;QACA;UACA7C;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACA8C;MACA;MACA;;MAEA;MACA;MACA,oDACA9C;QACAA;QACAV;QACAY;QACAC;MAAA,EACA;MAEA;IACA;IAEA;IACA4C;MACA;MACA;QACAvC;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;MACA;MAEA;QACA;QACA;MACA;QACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QAAA;MAAA;IACA;IAEA;IACAsC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;UACA;YACA;YACA;cACA1C;gBACAC;gBACAC;cACA;cACAyC;cACA;YACA;;YAEA;YACA;YACA;YAEA;cACA;;cAEA;cACA;gBACAC;gBACAC;gBACA;cACA;;cAEA;cACA;cACA;gBACAD;gBACAC;gBACA;cACA;;cAEA;cACA;gBACAA;gBACA;cACA;YACA;YAEA;cACA;gBACA7C;kBACAC;kBACAC;gBACA;cACA;cACAyC;cACA;YACA;YAEAG;UACA;YACAH;UACA;QACA;UACAA;QACA;MACA;IACA;IAEA;IACAI;MACA/C;IACA;IAEA;IACAgD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,KAGA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAGA;gBACAH;gBACAD;gBAEAK;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACAzD,mCAEA;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAoD;gBACAC;gBAAA;cAAA;gBAIA;gBACAlD;gBAAA,MACAuD;kBAAA;kBAAA;gBAAA;gBACAN;gBACAC;gBAAA;cAAA;gBAAA,IAKA;kBAAA;kBAAA;gBAAA;gBACAA;gBAAA;cAAA;gBApBAI;gBAAA;gBAAA;cAAA;gBAAA,KAyBAJ;kBAAA;kBAAA;gBAAA;gBACA;kBACA7C;oBACAC;oBACAC;kBACA;gBACA;gBAAA;cAAA;gBAIA;gBACA;kBACA;oBACA;sBACA;oBACA;sBACAV;oBACA;kBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAQ;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiD;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;;MAEA;MACA;QACApD;UACAC;UACAC;UACAP;QACA;MACA;IACA;IAEA;IACA0D;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;kBACA1E;kBACAE;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;oBAAA;sBACAK;sBACAV;sBACAW;sBACAC;sBACAC;sBACAT;oBACA;kBAAA;gBACA;gBAEAc;kBACAC;gBACA;gBAAA;gBAAA,OAEAO;cAAA;gBAAAC;gBAEA;kBACA;kBACAT;;kBAEA;kBACAuD;kBACAC;kBACA;oBACAA;kBACA;;kBAEA;kBACAxD;oBACAC;oBACAC;oBACAP;oBACA8D;sBACAtD;wBACAH;sBACA;oBACA;kBACA;gBACA;kBACAA;kBACAA;oBACAC;oBACAC;oBACAP;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAK;gBACAU;gBACA;kBACAV;oBACAC;oBACAC;oBACAP;kBACA;gBACA;kBACAK;oBACAC;oBACAC;oBACAP;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/9BA;AAAA;AAAA;AAAA;AAAyoC,CAAgB,+mCAAG,EAAC,C;;;;;;;;;;;ACA7pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/patrol_pkg/shift/edit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/patrol_pkg/shift/edit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./edit.vue?vue&type=template&id=1f5c9678&\"\nvar renderjs\nimport script from \"./edit.vue?vue&type=script&lang=js&\"\nexport * from \"./edit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./edit.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/patrol_pkg/shift/edit.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=template&id=1f5c9678&\"", "var components\ntry {\n  components = {\n    uniForms: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms/uni-forms\" */ \"@/uni_modules/uni-forms/components/uni-forms/uni-forms.vue\"\n      )\n    },\n    uniFormsItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms-item/uni-forms-item\" */ \"@/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.formData.rounds.length || 0\n  var g1 = _vm.formData.rounds.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function (e) {\n      return (_vm.formData.status = e.detail.value ? 1 : 0)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"shift-edit-container\">\n\t\t<!-- 头部标题区 -->\n\t\t<view class=\"header-section\">\n\t\t\t<view class=\"header-content\">\n\t\t\t\t<view class=\"header-left\">\n\t\t\t\t\t<text class=\"header-title\">编辑班次</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<uni-forms ref=\"form\" :modelValue=\"formData\" :rules=\"rules\">\n\t\t\t<!-- 基本信息卡片 -->\n\t\t\t<view class=\"form-card\">\n\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t<text class=\"section-title\">基本信息</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-content\">\n\t\t\t\t\t<uni-forms-item label=\"班次名称\" name=\"name\" required>\n\t\t\t\t\t\t<uni-easyinput v-model=\"formData.name\" placeholder=\"请输入班次名称\" />\n\t\t\t\t\t</uni-forms-item>\n\t\t\t\t\t\n\t\t\t\t\t<uni-forms-item label=\"开始时间\" name=\"start_time\" required>\n\t\t\t\t\t\t<view class=\"time-picker\">\n\t\t\t\t\t\t\t<picker mode=\"time\" :value=\"formData.start_time\" @change=\"onStartTimeChange\">\n\t\t\t\t\t\t\t\t<view class=\"time-input\">\n\t\t\t\t\t\t\t\t\t<text>{{ formData.start_time || '请选择时间' }}</text>\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"16\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</uni-forms-item>\n\t\t\t\t\t\n\t\t\t\t\t<uni-forms-item label=\"结束时间\" name=\"end_time\" required>\n\t\t\t\t\t\t<view class=\"time-picker\">\n\t\t\t\t\t\t\t<picker mode=\"time\" :value=\"formData.end_time\" @change=\"onEndTimeChange\">\n\t\t\t\t\t\t\t\t<view class=\"time-input\">\n\t\t\t\t\t\t\t\t\t<text>{{ formData.end_time || '请选择时间' }}</text>\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"16\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</uni-forms-item>\n\t\t\t\t\t\n\t\t\t\t\t<uni-forms-item label=\"是否跨天\">\n\t\t\t\t\t\t<view class=\"cross-day-container\">\n\t\t\t\t\t\t\t<view class=\"switch-wrapper\">\n\t\t\t\t\t\t\t\t<switch :checked=\"formData.is_cross_day\" @change=\"onCrossDayChange\" color=\"#1677FF\" style=\"transform: scale(0.8); vertical-align: middle;\" />\n\t\t\t\t\t\t\t\t<text class=\"switch-label\">{{ formData.is_cross_day ? '是' : '否' }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 添加跨天说明 -->\n\t\t\t\t\t\t\t<view class=\"cross-day-info\" v-if=\"formData.is_cross_day\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"info\" size=\"14\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t<text class=\"info-text\">班次跨天时，系统将自动判断每个轮次是否为次日</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</uni-forms-item>\n\t\t\t\t\t\n\t\t\t\t\t<uni-forms-item label=\"班次状态\">\n\t\t\t\t\t\t<view class=\"switch-wrapper\">\n\t\t\t\t\t\t\t<switch :checked=\"formData.status === 1\" @change=\"(e) => formData.status = e.detail.value ? 1 : 0\" color=\"#1677FF\" style=\"transform: scale(0.8); vertical-align: middle;\" />\n\t\t\t\t\t\t\t<text class=\"switch-label\">{{ formData.status === 1 ? '启用' : '停用' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</uni-forms-item>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 轮次管理卡片 -->\n\t\t\t<view class=\"form-card\">\n\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t\t<text class=\"section-title\">轮次管理</text>\n\t\t\t\t\t\t<view class=\"count-badge\">{{ formData.rounds.length || 0 }} 个</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"add-round-btn\" @click=\"addRound\">\n\t\t\t\t\t\t<uni-icons type=\"plusempty\" size=\"16\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t<text>添加轮次</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-content\">\n\t\t\t\t\t<view class=\"empty-rounds\" v-if=\"formData.rounds.length === 0\">\n\t\t\t\t\t\t<uni-icons type=\"info\" size=\"32\" color=\"#8F959E\"></uni-icons>\n\t\t\t\t\t\t<text class=\"empty-tip\">暂无轮次，点击上方按钮添加</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"rounds-list\" v-else>\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tclass=\"round-item\" \n\t\t\t\t\t\t\tv-for=\"(round, index) in formData.rounds\" \n\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t:style=\"{ animationDelay: index * 0.05 + 's' }\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<view class=\"round-header\">\n\t\t\t\t\t\t\t\t<text class=\"round-title\">轮次 {{ round.round }}</text>\n\t\t\t\t\t\t\t\t<view class=\"round-actions\">\n\t\t\t\t\t\t\t\t\t<view class=\"action-edit\" @click=\"editRound(index)\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"compose\" size=\"18\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"action-delete\" @click=\"removeRound(index)\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"trash\" size=\"18\" color=\"#FF3B30\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<view class=\"round-content\">\n\t\t\t\t\t\t\t\t<view class=\"round-form-item\">\n\t\t\t\t\t\t\t\t\t<text class=\"round-label\">名称</text>\n\t\t\t\t\t\t\t\t\t<view class=\"info-value\">{{ round.name || `轮次${round.round}` }}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<view class=\"round-form-item\">\n\t\t\t\t\t\t\t\t\t<text class=\"round-label\">时间</text>\n\t\t\t\t\t\t\t\t\t<view class=\"info-value\">\n\t\t\t\t\t\t\t\t\t\t{{ round.time || '--:--' }}\n\t\t\t\t\t\t\t\t\t\t<text v-if=\"round.day_offset && round.day_offset > 0\" class=\"day-offset-tag\">次日</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<view class=\"round-form-item\">\n\t\t\t\t\t\t\t\t\t<text class=\"round-label\">有效时长</text>\n\t\t\t\t\t\t\t\t\t<view class=\"info-value\">{{ round.duration || 60 }}分钟</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<view class=\"round-form-item\">\n\t\t\t\t\t\t\t\t\t<text class=\"round-label\">状态</text>\n\t\t\t\t\t\t\t\t\t<view class=\"info-value status-text\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"status-dot\" :class=\"{'inactive': round.status === 0}\"></view>\n\t\t\t\t\t\t\t\t\t\t<text>{{ round.status === 1 ? '启用' : '停用' }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-forms>\n\t\t\n\t\t<!-- 底部操作区 -->\n\t\t<view class=\"action-area\">\n\t\t\t<view class=\"action-btns\">\n\t\t\t\t<view class=\"cancel-btn\" @click=\"handleCancel\">取消</view>\n\t\t\t\t<view class=\"submit-btn\" @click=\"handleSubmit\">保存</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 轮次编辑弹出窗口 -->\n\t\t<uni-popup ref=\"roundPopup\" type=\"dialog\">\n\t\t\t<view class=\"round-form\">\n\t\t\t\t<view class=\"form-title\">{{ isEditingRound ? '编辑轮次' : '添加轮次' }}</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">轮次序号</text>\n\t\t\t\t\t<view class=\"input-container\">\n\t\t\t\t\t\t<input class=\"common-input\" type=\"number\" v-model=\"currentRound.round\" placeholder=\"请输入轮次序号\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">轮次名称</text>\n\t\t\t\t\t<view class=\"input-container\">\n\t\t\t\t\t\t<input class=\"common-input\" type=\"text\" v-model=\"currentRound.name\" placeholder=\"请输入轮次名称\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">打卡时间</text>\n\t\t\t\t\t<view class=\"input-container\">\n\t\t\t\t\t\t<picker mode=\"time\" :value=\"currentRound.time\" @change=\"onRoundTimeChange\" class=\"full-width\">\n\t\t\t\t\t\t\t<view class=\"common-input time-input\">\n\t\t\t\t\t\t\t\t<text>{{ currentRound.time || '请选择打卡时间' }}</text>\n\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"16\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">实际时间</text>\n\t\t\t\t\t<view class=\"input-container\">\n\t\t\t\t\t\t<view class=\"common-input actual-time-display\">\n\t\t\t\t\t\t\t<text class=\"time-value\">{{ currentRound.time }}</text>\n\t\t\t\t\t\t\t<text class=\"day-badge\" :class=\"{ 'next-day-badge': currentRound.day_offset === 1 }\">\n\t\t\t\t\t\t\t\t{{ currentRound.day_offset === 1 ? '次日' : '当天' }}\n\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">有效时长(分钟)</text>\n\t\t\t\t\t<view class=\"input-container\">\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"common-input\" \n\t\t\t\t\t\t\ttype=\"number\" \n\t\t\t\t\t\t\tv-model=\"currentRound.duration\" \n\t\t\t\t\t\t\tplaceholder=\"轮次有效时长，默认60分钟\" \n\t\t\t\t\t\t/>\n\t\t\t\t\t\t<text class=\"input-hint\">打卡有效时间窗口，超过此时长不可打卡，最长480分钟时限</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">启用状态</text>\n\t\t\t\t\t<view class=\"input-container\">\n\t\t\t\t\t\t<view class=\"switch-wrapper\">\n\t\t\t\t\t\t\t<switch :checked=\"currentRound.status === 1\" @change=\"onStatusChange\" color=\"#1677FF\" />\n\t\t\t\t\t\t\t<text class=\"switch-label\">{{ currentRound.status === 1 ? '启用' : '停用' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-buttons\">\n\t\t\t\t\t<button class=\"btn btn-cancel\" @click=\"closeRoundPopup\">取消</button>\n\t\t\t\t\t<button class=\"btn btn-primary\" @click=\"confirmRound\">保存</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t</view>\n</template>\n\n<script>\nimport PatrolApi from '@/utils/patrol-api.js';\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tshift_id: '', // 班次ID，用于编辑\n\t\t\tformData: {\n\t\t\t\tname: '',\n\t\t\t\tstart_time: '08:30',\n\t\t\t\tend_time: '17:00',\n\t\t\t\tis_cross_day: false,\n\t\t\t\tstatus: 1,\n\t\t\t\trounds: []\n\t\t\t},\n\t\t\trules: {\n\t\t\t\tname: {\n\t\t\t\t\trules: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\t\terrorMessage: '请输入班次名称'\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t},\n\t\t\t\tstart_time: {\n\t\t\t\t\trules: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\t\terrorMessage: '请选择开始时间'\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t},\n\t\t\t\tend_time: {\n\t\t\t\t\trules: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\t\terrorMessage: '请选择结束时间'\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 默认的轮次对象\n\t\t\tcurrentRound: {\n\t\t\t\tround: 1,\n\t\t\t\tname: '',\n\t\t\t\ttime: '08:30',\n\t\t\t\tday_offset: 0,\n\t\t\t\tduration: 60, // 默认60分钟有效时长\n\t\t\t\tstatus: 1\n\t\t\t},\n\t\t\t\n\t\t\t// 天数偏移选项\n\t\t\tdayOptions: ['当天', '次日'],\n\t\t\t\n\t\t\tisEditingRound: false,\n\t\t\teditingRoundIndex: null\n\t\t}\n\t},\n\tonLoad(options) {\n\t\t// 获取班次ID\n\t\tif (options.id) {\n\t\t\tthis.shift_id = options.id;\n\t\t\t// 加载班次详情\n\t\t\tthis.getShiftDetail();\n\t\t} else {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '参数错误',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.navigateBack();\n\t\t\t}, 1500);\n\t\t}\n\t},\n\tmethods: {\n\t\t// 开始时间变化\n\t\tonStartTimeChange(e) {\n\t\t\tthis.formData.start_time = e.detail.value;\n\t\t\t\n\t\t\t// 如果是跨天班次，重新计算所有轮次的日期偏移\n\t\t\tif (this.formData.is_cross_day) {\n\t\t\t\tthis.recalculateAllRoundOffsets();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 结束时间变化\n\t\tonEndTimeChange(e) {\n\t\t\tthis.formData.end_time = e.detail.value;\n\t\t},\n\t\t\n\t\t// 获取班次详情\n\t\tasync getShiftDetail() {\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '加载中...'\n\t\t\t});\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst res = await PatrolApi.getShiftDetail(this.shift_id);\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\t// 支持处理两种字段格式，兼容驼峰和下划线格式\n\t\t\t\t\tconst data = res.data;\n\t\t\t\t\t\n\t\t\t\t\t// 更新表单数据\n\t\t\t\t\tthis.formData = {\n\t\t\t\t\t\tname: data.name || '',\n\t\t\t\t\t\t// 优先使用下划线格式，如不存在则尝试使用驼峰格式\n\t\t\t\t\t\tstart_time: data.start_time || data.startTime || '08:30',\n\t\t\t\t\t\tend_time: data.end_time || data.endTime || '17:00',\n\t\t\t\t\t\tis_cross_day: data.is_cross_day !== undefined ? data.is_cross_day : (data.isCrossDay || false),\n\t\t\t\t\t\tstatus: data.status !== undefined ? data.status : 1,\n\t\t\t\t\t\trounds: []\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\t// 处理轮次数据，确保有所有必要字段\n\t\t\t\t\tif (data.rounds && Array.isArray(data.rounds)) {\n\t\t\t\t\t\tthis.formData.rounds = data.rounds.map(round => ({\n\t\t\t\t\t\t\tround: round.round || 1,\n\t\t\t\t\t\t\tname: round.name || `轮次${round.round || 1}`,\n\t\t\t\t\t\t\ttime: round.time || '08:30',\n\t\t\t\t\t\t\tday_offset: round.day_offset !== undefined ? round.day_offset : 0,\n\t\t\t\t\t\t\tduration: round.duration || 60,\n\t\t\t\t\t\t\tstatus: round.status !== undefined ? round.status : 1\n\t\t\t\t\t\t}));\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 确保轮次按照序号排序\n\t\t\t\t\t\tthis.formData.rounds.sort((a, b) => a.round - b.round);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 如果没有轮次，添加默认轮次\n\t\t\t\t\tif (this.formData.rounds.length === 0) {\n\t\t\t\t\t\tthis.addRound();\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.message || '获取班次详情失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t}, 1500);\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('获取班次详情错误', e);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '获取班次详情出错',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t}, 1500);\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 添加轮次\n\t\taddRound() {\n\t\t\tthis.isEditingRound = false;\n\t\t\t\n\t\t\t// 获取合理的默认时间\n\t\t\tlet suggestedTime = this.formData.start_time;\n\t\t\t\n\t\t\tif (this.formData.rounds.length > 0) {\n\t\t\t\t// 基于最后一个轮次的时间，加上合理间隔\n\t\t\t\tconst lastRound = this.formData.rounds[this.formData.rounds.length - 1];\n\t\t\t\tconst [lastHour, lastMinute] = lastRound.time.split(':').map(Number);\n\t\t\t\t\n\t\t\t\t// 假设间隔为2小时\n\t\t\t\tlet newHour = lastHour + 2;\n\t\t\t\tlet newMinute = lastMinute;\n\t\t\t\t\n\t\t\t\t// 处理24小时制\n\t\t\t\tif (newHour >= 24) {\n\t\t\t\t\tnewHour = newHour - 24;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tsuggestedTime = `${newHour.toString().padStart(2, '0')}:${newMinute.toString().padStart(2, '0')}`;\n\t\t\t}\n\t\t\t\n\t\t\tthis.currentRound = {\n\t\t\t\tround: this.formData.rounds.length + 1,\n\t\t\t\tname: `轮次${this.formData.rounds.length + 1}`,\n\t\t\t\ttime: suggestedTime,\n\t\t\t\tday_offset: 0, // 初始化为当天，将在下一步自动计算\n\t\t\t\tduration: 60, // 默认60分钟\n\t\t\t\tstatus: 1\n\t\t\t};\n\t\t\t\n\t\t\t// 如果是跨天班次，自动计算day_offset\n\t\t\tif (this.formData.is_cross_day) {\n\t\t\t\tthis.calculateRoundDayOffset(this.currentRound);\n\t\t\t}\n\t\t\t\n\t\t\tthis.$refs.roundPopup.open();\n\t\t},\n\t\t\n\t\t// 删除轮次\n\t\tremoveRound(index) {\n\t\t\tthis.formData.rounds.splice(index, 1);\n\t\t\t\n\t\t\t// 重新排序轮次编号\n\t\t\tthis.formData.rounds.forEach((round, idx) => {\n\t\t\t\tround.round = idx + 1;\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 验证轮次时限的方法\n\t\tvalidateRoundDuration(round) {\n\t\t\tif (!round.time || !round.duration) return true;\n\t\t\t\n\t\t\t// 获取轮次开始时间\n\t\t\tconst [roundHour, roundMinute] = round.time.split(':').map(Number);\n\t\t\tconst roundStartMinutes = roundHour * 60 + roundMinute;\n\t\t\t\n\t\t\t// 获取班次时间\n\t\t\tconst [startHour, startMinute] = this.formData.start_time.split(':').map(Number);\n\t\t\tconst [endHour, endMinute] = this.formData.end_time.split(':').map(Number);\n\t\t\tconst shiftStartMinutes = startHour * 60 + startMinute;\n\t\t\tconst shiftEndMinutes = endHour * 60 + endMinute;\n\t\t\t\n\t\t\t// 计算轮次结束时间\n\t\t\tconst roundEndMinutes = roundStartMinutes + parseInt(round.duration);\n\t\t\t\n\t\t\t// 检查轮次是否跨天\n\t\t\tconst isRoundCrossDay = roundEndMinutes >= 24 * 60;\n\t\t\tconst adjustedRoundEndMinutes = isRoundCrossDay ? roundEndMinutes - 24 * 60 : roundEndMinutes;\n\t\t\t\n\t\t\t// 是否班次本身跨天\n\t\t\tconst isShiftCrossDay = this.formData.is_cross_day;\n\t\t\t\n\t\t\t// 检查轮次时间是否有效\n\t\t\tlet isTimeInvalid = false;\n\t\t\tlet timeErrorMessage = '';\n\t\t\t\n\t\t\tif (isShiftCrossDay) {\n\t\t\t\t// 班次跨天的情况\n\t\t\t\tif (round.day_offset === 0) {\n\t\t\t\t\t// 当天轮次\n\t\t\t\t\tif (roundStartMinutes < shiftStartMinutes) {\n\t\t\t\t\t\tconst diffMinutes = shiftStartMinutes - roundStartMinutes;\n\t\t\t\t\t\tconst diffHours = Math.floor(diffMinutes / 60);\n\t\t\t\t\t\tconst diffMins = diffMinutes % 60;\n\t\t\t\t\t\t\n\t\t\t\t\t\ttimeErrorMessage = `第${round.round}轮开始时间${round.time}早于班次开始时间${this.formData.start_time}，`;\n\t\t\t\t\t\ttimeErrorMessage += `提前了${diffHours}小时${diffMins}分钟`;\n\t\t\t\t\t\tisTimeInvalid = true;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// 次日轮次\n\t\t\t\t\tif (roundStartMinutes > shiftEndMinutes && roundStartMinutes < shiftStartMinutes) {\n\t\t\t\t\t\tconst diffMinutes1 = roundStartMinutes - shiftEndMinutes;\n\t\t\t\t\t\tconst diffHours1 = Math.floor(diffMinutes1 / 60);\n\t\t\t\t\t\tconst diffMins1 = diffMinutes1 % 60;\n\t\t\t\t\t\t\n\t\t\t\t\t\tconst diffMinutes2 = shiftStartMinutes - roundStartMinutes;\n\t\t\t\t\t\tconst diffHours2 = Math.floor(diffMinutes2 / 60);\n\t\t\t\t\t\tconst diffMins2 = diffMinutes2 % 60;\n\t\t\t\t\t\t\n\t\t\t\t\t\ttimeErrorMessage = `第${round.round}轮(次日)开始时间${round.time}在班次下班时间${this.formData.end_time}之后`;\n\t\t\t\t\t\ttimeErrorMessage += `${diffHours1}小时${diffMins1}分钟，且在班次上班时间${this.formData.start_time}之前`;\n\t\t\t\t\t\ttimeErrorMessage += `${diffHours2}小时${diffMins2}分钟，不在班次时间范围内`;\n\t\t\t\t\t\tisTimeInvalid = true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// 班次不跨天的情况\n\t\t\t\tif (roundStartMinutes < shiftStartMinutes) {\n\t\t\t\t\tconst diffMinutes = shiftStartMinutes - roundStartMinutes;\n\t\t\t\t\tconst diffHours = Math.floor(diffMinutes / 60);\n\t\t\t\t\tconst diffMins = diffMinutes % 60;\n\t\t\t\t\t\n\t\t\t\t\ttimeErrorMessage = `第${round.round}轮开始时间早于班次开始时间，`;\n\t\t\t\t\ttimeErrorMessage += `提前了${diffHours}小时${diffMins}分钟`;\n\t\t\t\t\tisTimeInvalid = true;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\tif (isTimeInvalid) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '轮次时间错误提示',\n\t\t\t\t\tcontent: timeErrorMessage,\n\t\t\t\t\tshowCancel: true,\n\t\t\t\t\tconfirmText: '我知道了',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t// 用户确认了解\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\t// 检查轮次结束时间是否超出范围\n\t\t\tlet isEndTimeInvalid = false;\n\t\t\tlet endTimeErrorMessage = '';\n\t\t\t\n\t\t\tif (isShiftCrossDay) {\n\t\t\t\t// 班次跨天的情况\n\t\t\t\tif (round.day_offset === 0) {\n\t\t\t\t\t// 当天轮次\n\t\t\t\t\tif (roundEndMinutes > (shiftEndMinutes + 24 * 60)) {\n\t\t\t\t\t\tconst overMinutes = roundEndMinutes - (shiftEndMinutes + 24 * 60);\n\t\t\t\t\t\tconst overHours = Math.floor(overMinutes / 60);\n\t\t\t\t\t\tconst overMins = overMinutes % 60;\n\t\t\t\t\t\t\n\t\t\t\t\t\tisEndTimeInvalid = true;\n\t\t\t\t\t\tendTimeErrorMessage = `超出次日下班时间${overHours}小时${overMins}分钟`;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// 次日轮次\n\t\t\t\t\tif (adjustedRoundEndMinutes > shiftEndMinutes) {\n\t\t\t\t\t\tconst overMinutes = adjustedRoundEndMinutes - shiftEndMinutes;\n\t\t\t\t\t\tconst overHours = Math.floor(overMinutes / 60);\n\t\t\t\t\t\tconst overMins = overMinutes % 60;\n\t\t\t\t\t\t\n\t\t\t\t\t\tisEndTimeInvalid = true;\n\t\t\t\t\t\tendTimeErrorMessage = `超出次日下班时间${overHours}小时${overMins}分钟`;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// 班次不跨天的情况\n\t\t\t\tif (adjustedRoundEndMinutes > shiftEndMinutes) {\n\t\t\t\t\tconst overMinutes = adjustedRoundEndMinutes - shiftEndMinutes;\n\t\t\t\t\tconst overHours = Math.floor(overMinutes / 60);\n\t\t\t\t\tconst overMins = overMinutes % 60;\n\t\t\t\t\t\n\t\t\t\t\tisEndTimeInvalid = true;\n\t\t\t\t\tendTimeErrorMessage = `超出下班时间${overHours}小时${overMins}分钟`;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\tif (isEndTimeInvalid) {\n\t\t\t\t// 计算轮次结束时间的小时和分钟\n\t\t\t\tlet endTimeHour = Math.floor(adjustedRoundEndMinutes / 60);\n\t\t\t\tlet endTimeMin = adjustedRoundEndMinutes % 60;\n\t\t\t\tconst formattedEndTime = `${endTimeHour.toString().padStart(2,'0')}:${endTimeMin.toString().padStart(2,'0')}`;\n\t\t\t\t\n\t\t\t\tlet message = `第${round.round}轮将在${round.time}${round.day_offset === 1 ? '(次日)' : ''}开始，持续${round.duration}分钟，`;\n\t\t\t\tmessage += `将在${formattedEndTime}${isRoundCrossDay ? '(次日)' : ''}结束，`;\n\t\t\t\tmessage += endTimeErrorMessage;\n\t\t\t\t\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '轮次时限超出提示',\n\t\t\t\t\tcontent: message,\n\t\t\t\t\tshowCancel: true,\n\t\t\t\t\tconfirmText: '我知道了',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t// 用户确认了解\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\t// 显示轮次跨天提示（仅首次显示）\n\t\t\tif (isRoundCrossDay && !round.crossDayNotified) {\n\t\t\t\tround.crossDayNotified = true;\n\t\t\t\tconst endTimeStr = `${Math.floor(adjustedRoundEndMinutes / 60).toString().padStart(2, '0')}:${(adjustedRoundEndMinutes % 60).toString().padStart(2, '0')}`;\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '轮次跨天提示',\n\t\t\t\t\tcontent: `第${round.round}轮将从${round.time}开始，持续${round.duration}分钟，将在次日${endTimeStr}结束`,\n\t\t\t\t\tshowCancel: false,\n\t\t\t\t\tconfirmText: '我知道了'\n\t\t\t\t});\n\t\t\t}\n\t\t\t\n\t\t\treturn true;\n\t\t},\n\t\t\n\t\t// 修改轮次时间变化的处理方法\n\t\tonRoundTimeChange(e) {\n\t\t\tthis.currentRound.time = e.detail.value;\n\t\t\t\n\t\t\t// 如果班次设为跨天，则自动计算该轮次是否应为次日\n\t\t\tif (this.formData.is_cross_day) {\n\t\t\t\tthis.calculateRoundDayOffset(this.currentRound);\n\t\t\t} else {\n\t\t\t\tthis.currentRound.day_offset = 0;\n\t\t\t}\n\t\t\t\n\t\t\t// 验证时限\n\t\t\tif (this.currentRound.duration) {\n\t\t\t\tthis.validateRoundDuration(this.currentRound);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 添加duration变化的监听\n\t\tonDurationChange(e) {\n\t\t\tconst newDuration = parseInt(e.detail.value);\n\t\t\tthis.currentRound.duration = newDuration;\n\t\t\tif (this.currentRound.time) {\n\t\t\t\tthis.validateRoundDuration(this.currentRound);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 轮次日期变化（保留但不再主动调用）\n\t\tonRoundDayChange(e) {\n\t\t\tthis.currentRound.day_offset = parseInt(e.detail.value);\n\t\t},\n\t\t\n\t\t// 计算一个轮次的day_offset\n\t\tcalculateRoundDayOffset(round) {\n\t\t\tif (!this.formData.is_cross_day) {\n\t\t\t\tround.day_offset = 0;\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 获取班次开始时间和结束时间\n\t\t\tconst [startHour, startMinute] = this.formData.start_time.split(':').map(Number);\n\t\t\tconst [endHour, endMinute] = this.formData.end_time.split(':').map(Number);\n\t\t\tconst startTimeMinutes = startHour * 60 + startMinute;\n\t\t\tconst endTimeMinutes = endHour * 60 + endMinute;\n\t\t\t\n\t\t\t// 获取轮次时间\n\t\t\tconst [roundHour, roundMinute] = round.time.split(':').map(Number);\n\t\t\tconst roundTimeMinutes = roundHour * 60 + roundMinute;\n\t\t\t\n\t\t\t// 计算轮次结束时间（考虑持续时间）\n\t\t\tconst roundEndMinutes = roundTimeMinutes + (parseInt(round.duration) || 60);\n\t\t\t\n\t\t\t// 判断班次是否跨天（结束时间小于开始时间）\n\t\t\tconst isShiftCrossDay = endTimeMinutes < startTimeMinutes;\n\t\t\t\n\t\t\tif (isShiftCrossDay) {\n\t\t\t\t// 班次跨天的情况\n\t\t\t\tif (roundTimeMinutes <= endTimeMinutes || roundTimeMinutes >= startTimeMinutes) {\n\t\t\t\t\t// 轮次开始时间在有效范围内\n\t\t\t\t\tround.day_offset = roundTimeMinutes <= endTimeMinutes ? 1 : 0;\n\t\t\t\t} else {\n\t\t\t\t\tround.day_offset = 0;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// 班次不跨天，但需要检查轮次是否因持续时间而跨天\n\t\t\t\tif (roundEndMinutes > 24 * 60) {\n\t\t\t\t\t// 轮次结束时间超过当天24:00，标记为跨天\n\t\t\t\t\tround.day_offset = 1;\n\t\t\t\t} else {\n\t\t\t\t\tround.day_offset = 0;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 重新计算所有轮次的日期偏移\n\t\trecalculateAllRoundOffsets() {\n\t\t\tif (!this.formData.rounds || !this.formData.rounds.length) return;\n\t\t\t\n\t\t\t// 如果班次不跨天，所有轮次都设为当天\n\t\t\tif (!this.formData.is_cross_day) {\n\t\t\t\tthis.formData.rounds.forEach(round => {\n\t\t\t\t\tround.day_offset = 0;\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 处理每个轮次\n\t\t\tthis.formData.rounds.forEach(round => {\n\t\t\t\tthis.calculateRoundDayOffset(round);\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 修改编辑轮次方法\n\t\teditRound(index) {\n\t\t\tthis.isEditingRound = true;\n\t\t\tthis.editingRoundIndex = index;\n\t\t\t\n\t\t\t// 确保轮次有day_offset字段\n\t\t\tconst round = this.formData.rounds[index];\n\t\t\tthis.currentRound = {\n\t\t\t\t...round,\n\t\t\t\tround: round.round,\n\t\t\t\tname: round.name || `轮次${round.round}`,\n\t\t\t\tday_offset: round.day_offset !== undefined ? round.day_offset : 0,\n\t\t\t\tduration: round.duration || 60\n\t\t\t};\n\t\t\t\n\t\t\tthis.$refs.roundPopup.open();\n\t\t},\n\t\t\n\t\t// 确认添加或编辑轮次\n\t\tconfirmRound() {\n\t\t\t// 验证轮次名称和时间\n\t\t\tif (!this.currentRound.name || !this.currentRound.time) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '轮次名称和时间不能为空',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 验证时限\n\t\t\tif (!this.validateRoundDuration(this.currentRound)) {\n\t\t\t\treturn; // 如果验证不通过，不关闭弹窗\n\t\t\t}\n\t\t\t\n\t\t\t// 确保day_offset有值\n\t\t\tif (this.currentRound.day_offset === undefined) {\n\t\t\t\t// 自动计算day_offset\n\t\t\t\tthis.calculateRoundDayOffset(this.currentRound);\n\t\t\t}\n\t\t\t\n\t\t\tif (this.isEditingRound && this.editingRoundIndex !== null) {\n\t\t\t\t// 更新现有轮次\n\t\t\t\tthis.formData.rounds.splice(this.editingRoundIndex, 1, {...this.currentRound});\n\t\t\t} else {\n\t\t\t\t// 添加新轮次\n\t\t\t\tthis.formData.rounds.push({...this.currentRound});\n\t\t\t}\n\t\t\t\n\t\t\t// 关闭弹窗\n\t\t\tthis.$refs.roundPopup.close();\n\t\t\t\n\t\t\t// 重新排序轮次\n\t\t\tthis.formData.rounds.sort((a, b) => a.round - b.round);\n\t\t},\n\t\t\n\t\t// 处理状态变化\n\t\tonStatusChange(e) {\n\t\t\tthis.currentRound.status = e.detail.value ? 1 : 0;\n\t\t},\n\t\t\n\t\t// 关闭轮次编辑弹窗\n\t\tcloseRoundPopup() {\n\t\t\tthis.$refs.roundPopup.close();\n\t\t},\n\t\t\n\t\t// 修改验证表单方法\n\t\tvalidateForm() {\n\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\tthis.$refs.form.validate().then(res => {\n\t\t\t\t\tif (res) {\n\t\t\t\t\t\t// 验证轮次信息\n\t\t\t\t\t\tif (this.formData.rounds.length === 0) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '请至少添加一个轮次',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\treject('请至少添加一个轮次');\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 验证所有轮次的时间和时限\n\t\t\t\t\t\tlet hasInvalidRound = false;\n\t\t\t\t\t\tlet invalidMessage = '';\n\t\t\t\t\t\t\n\t\t\t\t\t\tfor (let i = 0; i < this.formData.rounds.length; i++) {\n\t\t\t\t\t\t\tconst round = this.formData.rounds[i];\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 验证时间是否设置\n\t\t\t\t\t\t\tif (!round.time) {\n\t\t\t\t\t\t\t\tinvalidMessage = `第${round.round}轮时间不完整`;\n\t\t\t\t\t\t\t\thasInvalidRound = true;\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 验证有效时长范围\n\t\t\t\t\t\t\tconst duration = parseInt(round.duration);\n\t\t\t\t\t\t\tif (isNaN(duration) || duration < 1 || duration > 480) {\n\t\t\t\t\t\t\t\tinvalidMessage = `第${round.round}轮有效时长应在1-480分钟之间`;\n\t\t\t\t\t\t\t\thasInvalidRound = true;\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 验证轮次时限\n\t\t\t\t\t\t\tif (!this.validateRoundDuration(round)) {\n\t\t\t\t\t\t\t\thasInvalidRound = true;\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (hasInvalidRound) {\n\t\t\t\t\t\t\tif (invalidMessage) {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: invalidMessage,\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treject('轮次验证未通过');\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tresolve(true);\n\t\t\t\t\t} else {\n\t\t\t\t\t\treject('表单验证不通过');\n\t\t\t\t\t}\n\t\t\t\t}).catch(err => {\n\t\t\t\t\treject(err);\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 取消\n\t\thandleCancel() {\n\t\t\tuni.navigateBack();\n\t\t},\n\t\t\n\t\t// 提交表单\n\t\tasync handleSubmit() {\n\t\t\ttry {\n\t\t\t\t// 先验证基本表单\n\t\t\t\tif (this.$refs.form) {\n\t\t\t\t\tawait this.$refs.form.validate();\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 验证所有轮次\n\t\t\t\tlet hasInvalidRound = false;\n\t\t\t\tlet invalidMessage = '';\n\t\t\t\t\n\t\t\t\tfor (let i = 0; i < this.formData.rounds.length; i++) {\n\t\t\t\t\tconst round = this.formData.rounds[i];\n\t\t\t\t\t\n\t\t\t\t\t// 验证时间是否设置\n\t\t\t\t\tif (!round.time) {\n\t\t\t\t\t\tinvalidMessage = `第${round.round}轮时间不完整`;\n\t\t\t\t\t\thasInvalidRound = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 验证有效时长范围\n\t\t\t\t\tconst duration = parseInt(round.duration);\n\t\t\t\t\t\t\t\t\t\t\t\tif (isNaN(duration) || duration < 1 || duration > 480) {\n\t\t\t\t\t\t\t\tinvalidMessage = `第${round.round}轮有效时长应在1-480分钟之间`;\n\t\t\t\t\t\thasInvalidRound = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 验证轮次时限\n\t\t\t\t\tif (!this.validateRoundDuration(round)) {\n\t\t\t\t\t\thasInvalidRound = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (hasInvalidRound) {\n\t\t\t\t\tif (invalidMessage) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: invalidMessage,\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\treturn; // 验证未通过，直接返回\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 确保所有轮次都有day_offset\n\t\t\t\tthis.formData.rounds.forEach(round => {\n\t\t\t\t\tif (round.day_offset === undefined) {\n\t\t\t\t\t\tif (this.formData.is_cross_day) {\n\t\t\t\t\t\t\tthis.calculateRoundDayOffset(round);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tround.day_offset = 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 提交表单\n\t\t\t\tawait this.submitFormData();\n\t\t\t\t\n\t\t\t} catch (error) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: typeof error === 'string' ? error : '表单验证失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 显示轮次的实际时间\n\t\tgetActualDateDisplay(round) {\n\t\t\tif (!round || !round.time) return '未设置';\n\t\t\t\n\t\t\t// 简化显示，不再添加(次日)，只依靠标签显示\n\t\t\treturn round.time;\n\t\t},\n\t\t\n\t\t// 班次跨天设置变化\n\t\tonCrossDayChange(e) {\n\t\t\tthis.formData.is_cross_day = e.detail.value;\n\t\t\t\n\t\t\t// 自动重新计算所有轮次的day_offset\n\t\t\tthis.recalculateAllRoundOffsets();\n\t\t\t\n\t\t\t// 显示提示\n\t\t\tif (this.formData.is_cross_day) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '已自动调整轮次日期',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 提交表单数据\n\t\tasync submitFormData() {\n\t\t\ttry {\n\t\t\t\t// 准备提交数据\n\t\t\t\tconst submitData = {\n\t\t\t\t\tshift_id: this.shift_id,\n\t\t\t\t\tname: this.formData.name,\n\t\t\t\t\tstart_time: this.formData.start_time,\n\t\t\t\t\tend_time: this.formData.end_time,\n\t\t\t\t\tis_cross_day: this.formData.is_cross_day,\n\t\t\t\t\tstatus: this.formData.status,\n\t\t\t\t\trounds: this.formData.rounds.map(round => ({\n\t\t\t\t\t\tround: round.round,\n\t\t\t\t\t\tname: round.name || `轮次${round.round}`,\n\t\t\t\t\t\ttime: round.time,\n\t\t\t\t\t\tday_offset: parseInt(round.day_offset || 0),\n\t\t\t\t\t\tduration: parseInt(round.duration || 60),\n\t\t\t\t\t\tstatus: round.status\n\t\t\t\t\t}))\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '保存中...'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tconst res = await PatrolApi.updateShift(submitData);\n\t\t\t\t\n\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t// 先隐藏加载中提示\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\n\t\t\t\t\t// 标记列表页需要刷新\n\t\t\t\t\tconst pages = getCurrentPages();\n\t\t\t\t\tconst prevPage = pages[pages.length - 2];\n\t\t\t\t\tif (prevPage && prevPage.$vm) {\n\t\t\t\t\t\tprevPage.$vm.needRefresh = true;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 显示成功提示并返回\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '保存成功',\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 2000,\n\t\t\t\t\t\tcomplete: () => {\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t}, 500);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.message || '保存失败',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tconsole.error('保存班次出错', e);\n\t\t\t\tif (typeof e === 'string') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: e,\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '保存出错',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\">\n/* 主色调变量 */\n$primary-color: #1677FF; // 支付宝蓝\n$primary-light: #E7F1FF;\n$primary-dark: #0E5FD8;\n\n$success-color: #07C160;\n$warning-color: #FFA300;\n$danger-color: #FF3B30;\n$info-color: #8F959E;\n\n$text-primary: #2C3E50;\n$text-secondary: #666666;\n$text-tertiary: #999999;\n$border-color: #EAEAEA;\n$background: #F7F8FA;\n\n$radius-sm: 6rpx;\n$radius-md: 12rpx;\n$radius-lg: 16rpx;\n$radius-xl: 24rpx;\n$radius-full: 999rpx;\n\n$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n$shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\n$shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);\n\n/* 淡入动画 */\n@keyframes fadeIn {\n\tfrom {\n\t\topacity: 0;\n\t\ttransform: translateY(20rpx);\n\t}\n\tto {\n\t\topacity: 1;\n\t\ttransform: translateY(0);\n\t}\n}\n\n/* 脉冲动画 */\n@keyframes pulse {\n\t0% {\n\t\ttransform: scale(1);\n\t}\n\t50% {\n\t\ttransform: scale(1.05);\n\t}\n\t100% {\n\t\ttransform: scale(1);\n\t}\n}\n\n.shift-edit-container {\n\tmin-height: 100vh;\n\tbackground-color: #F5F5F5;\n\tpadding-bottom: 40rpx;\n}\n\n/* 头部区域样式 */\n.header-section {\n\tbackground-color: #FFFFFF;\n\tpadding: 20rpx 30rpx;\n\tborder-bottom: 1rpx solid $border-color;\n\tposition: relative;\n\tbox-shadow: $shadow-sm;\n\tz-index: 5;\n}\n\n.header-content {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.header-title {\n\tfont-size: 36rpx;\n\tfont-weight: 600;\n\tcolor: $text-primary;\n\tposition: relative;\n\tpadding-left: 24rpx;\n\t\n\t&:before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\twidth: 8rpx;\n\t\theight: 32rpx;\n\t\tbackground-color: $primary-color;\n\t\tborder-radius: $radius-sm;\n\t}\n}\n\n/* 表单卡片样式 */\n.form-card {\n\tmargin: 20rpx;\n\tbackground-color: #FFFFFF;\n\tborder-radius: $radius-lg;\n\toverflow: hidden;\n\tbox-shadow: $shadow-sm;\n\tanimation: fadeIn 0.5s ease-out;\n\t\n\t&:nth-child(2) {\n\t\tanimation-delay: 0.1s;\n\t}\n}\n\n.card-header {\n\tpadding: 20rpx 30rpx;\n\tborder-bottom: 1rpx solid $border-color;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.section-header {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.section-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: $text-primary;\n\tposition: relative;\n\tpadding-left: 20rpx;\n\t\n\t&:before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\twidth: 6rpx;\n\t\theight: 28rpx;\n\t\tbackground-color: $primary-color;\n\t\tborder-radius: $radius-sm;\n\t}\n}\n\n.count-badge {\n\tbackground-color: $primary-light;\n\tcolor: $primary-color;\n\tfont-size: 24rpx;\n\tpadding: 4rpx 16rpx;\n\tborder-radius: $radius-full;\n\tfont-weight: 500;\n\tmargin-left: 16rpx;\n}\n\n.form-content {\n\tpadding: 20rpx 30rpx 20rpx 30rpx;\n}\n\n/* 表单元素样式 */\n::v-deep .uni-forms-item__label {\n\tfont-size: 28rpx;\n\tcolor: $text-secondary;\n\tfont-weight: 500;\n\tmargin-bottom: 8rpx;\n\tmin-width: 160rpx;\n\twhite-space: nowrap;\n}\n\n::v-deep .uni-easyinput__content {\n\tbackground-color: $background;\n\tborder-radius: $radius-md;\n\tborder: 1rpx solid $border-color !important;\n\theight: 80rpx;\n\tflex: 1;\n}\n\n::v-deep .uni-forms-item--required .uni-forms-item__label {\n\tmin-width: 170rpx;\n}\n\n::v-deep .uni-forms-item__content {\n\tdisplay: flex;\n\twidth: 100%;\n}\n\n.switch-wrapper {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: flex-start;\n\theight: 60rpx;\n}\n\n.switch-label {\n\tfont-size: 28rpx;\n\tcolor: $text-primary;\n\tmargin-left: 20rpx;\n\tline-height: 1;\n\tvertical-align: middle;\n}\n\n/* 时间选择器专用样式 */\n.time-picker {\n\twidth: 100%;\n\tpadding-right: 35rpx;\n}\n\n.time-input {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\theight: 70rpx;\n\tpadding: 0 20rpx;\n\tbackground-color: #F5F5F5;\n\tborder-radius: 8rpx;\n\tfont-size: 28rpx;\n\tcolor: #333333;\n\twidth: 100%;\n}\n\n/* 添加轮次按钮 */\n.add-round-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tbackground-color: $primary-light;\n\tcolor: $primary-color;\n\tfont-size: 26rpx;\n\tpadding: 8rpx 20rpx;\n\tborder-radius: $radius-full;\n\ttransition: all 0.3s ease;\n\t\n\ttext {\n\t\tmargin-left: 6rpx;\n\t}\n\t\n\t&:active {\n\t\ttransform: scale(0.95);\n\t\topacity: 0.8;\n\t}\n}\n\n/* 空轮次提示 */\n.empty-rounds {\n\tpadding: 40rpx 0;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.empty-tip {\n\tfont-size: 26rpx;\n\tcolor: $text-tertiary;\n\tmargin-top: 20rpx;\n}\n\n/* 轮次列表样式 */\n.rounds-list {\n\tmargin-bottom: 20rpx;\n}\n\n.round-item {\n\tmargin-bottom: 30rpx;\n\tbackground-color: $background;\n\tborder-radius: $radius-md;\n\tpadding: 24rpx;\n\tborder-left: 6rpx solid $primary-color;\n\tbox-shadow: $shadow-sm;\n\ttransition: all 0.3s ease;\n\t\n\t&:hover {\n\t\ttransform: translateX(6rpx);\n\t}\n\t\n\t&:last-child {\n\t\tmargin-bottom: 0;\n\t}\n}\n\n.round-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n\tpadding-bottom: 16rpx;\n\tborder-bottom: 1rpx dashed $border-color;\n}\n\n.round-title {\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tcolor: $primary-color;\n\tbackground-color: $primary-light;\n\tpadding: 6rpx 20rpx;\n\tborder-radius: $radius-full;\n}\n\n.round-actions {\n\tdisplay: flex;\n}\n\n.action-edit, .action-delete {\n\twidth: 64rpx;\n\theight: 64rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\ttransition: all 0.3s ease;\n\t\n\t&:active {\n\t\ttransform: scale(0.9);\n\t\topacity: 0.8;\n\t}\n}\n\n.round-content {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.round-form-item {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 16rpx;\n\tflex-wrap: wrap;\n\t\n\t&:last-child {\n\t\tmargin-bottom: 0;\n\t}\n}\n\n.round-label {\n\twidth: 160rpx;\n\tfont-size: 26rpx;\n\tcolor: $text-secondary;\n\tfont-weight: 500;\n}\n\n.info-value {\n\tflex: 1;\n\tfont-size: 26rpx;\n\tcolor: $text-primary;\n\tpadding: 8rpx 0;\n}\n\n.day-offset-tag {\n\tbackground-color: $primary-light;\n\tcolor: $primary-color;\n\tfont-size: 22rpx;\n\tpadding: 2rpx 8rpx;\n\tborder-radius: 4rpx;\n\tmargin-left: 8rpx;\n}\n\n.status-text {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.status-dot {\n\twidth: 12rpx;\n\theight: 12rpx;\n\tborder-radius: 50%;\n\tmargin-right: 8rpx;\n\tbackground-color: $success-color;\n\t\n\t&.inactive {\n\t\tbackground-color: $danger-color;\n\t}\n}\n\n/* 底部操作区域 */\n.action-area {\n\tposition: relative;\n\tbackground-color: #FFFFFF;\n\tpadding: 20rpx 30rpx;\n\tmargin: 20rpx;\n\tborder-radius: 12rpx;\n\tbox-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.05);\n\tz-index: 10;\n}\n\n.action-btns {\n\tdisplay: flex;\n\tgap: 20rpx;\n}\n\n.cancel-btn, .submit-btn {\n\tflex: 1;\n\theight: 80rpx;\n\tborder-radius: 40rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\ttransition: all 0.3s ease;\n\t\n\t&:active {\n\t\ttransform: scale(0.95);\n\t\topacity: 0.9;\n\t}\n}\n\n.cancel-btn {\n\tbackground-color: #F5F5F5;\n\tcolor: #666666;\n\tborder: 1rpx solid #EEEEEE;\n}\n\n.submit-btn {\n\tbackground-color: #1677FF;\n\tcolor: #FFFFFF;\n}\n\n/* 轮次编辑弹出窗口样式 */\n.round-form {\n\tpadding: 30rpx;\n\twidth: 650rpx;\n\tbackground-color: #FFFFFF;\n\tborder-radius: $radius-lg;\n\tbox-shadow: $shadow-lg;\n\tz-index: 999;\n}\n\n.form-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: $text-primary;\n\ttext-align: center;\n\tmargin-bottom: 30rpx;\n\tposition: relative;\n\t\n\t&:after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 50%;\n\t\tbottom: -10rpx;\n\t\ttransform: translateX(-50%);\n\t\twidth: 60rpx;\n\t\theight: 4rpx;\n\t\tbackground-color: $primary-color;\n\t\tborder-radius: $radius-full;\n\t}\n}\n\n.form-item {\n\tmargin-bottom: 24rpx;\n\t\n\t&:last-child {\n\t\tmargin-bottom: 30rpx;\n\t}\n}\n\n.form-label {\n\tfont-size: 28rpx;\n\tcolor: $text-secondary;\n\tmargin-bottom: 12rpx;\n\tdisplay: block;\n}\n\n.input-container {\n\tflex: 1;\n\twidth: 100%;\n\tpadding-right: 30rpx;\n}\n\n.common-input {\n\theight: 70rpx;\n\tbackground-color: #F5F5F5;\n\tborder-radius: 8rpx;\n\tpadding: 0 20rpx;\n\twidth: 100%;\n\tbox-sizing: border-box;\n\tdisplay: flex;\n\talign-items: center;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.full-width {\n\twidth: 100%;\n}\n\n.form-input {\n\twidth: 100%;\n\theight: 70rpx;\n\tbackground-color: #F5F5F5;\n\tborder-radius: 8rpx;\n\tpadding: 0 20rpx;\n\tbox-sizing: border-box;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.time-input {\n\tjustify-content: space-between;\n\tmargin-right: 30rpx;\n}\n\n.actual-time-display {\n\tjustify-content: space-between;\n\tpadding-right: 10rpx;\n\tmargin-right: 30rpx;\n}\n\n.day-badge {\n\tpadding: 4rpx 12rpx;\n\tborder-radius: 20rpx;\n\tfont-size: 24rpx;\n\tbackground-color: #52C41A;\n\tcolor: #FFFFFF;\n\tmargin-left: auto;\n}\n\n.next-day-badge {\n\tbackground-color: #FA8C16;\n}\n\n/* 添加跨天说明样式 */\n.cross-day-container {\n\tdisplay: flex;\n\tflex-direction: column;\n\twidth: 100%;\n\tpadding-right: 30rpx;\n}\n\n.cross-day-info {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-top: 16rpx;\n\tpadding: 8rpx 16rpx;\n\tbackground-color: #E7F1FF;\n\tborder-radius: 8rpx;\n\twidth: 100%;\n}\n\n.info-text {\n\tfont-size: 24rpx;\n\tcolor: #1677FF;\n\tmargin-left: 8rpx;\n}\n\n/* 添加实际时间显示样式 */\n.actual-time-display {\n\tdisplay: flex;\n\talign-items: center;\n\theight: 70rpx;\n\tpadding: 0 20rpx;\n\tbackground-color: #F5F5F5;\n\tborder-radius: 8rpx;\n\twidth: 100%;\n}\n\n.time-value {\n\tfont-size: 28rpx;\n\tcolor: #333333;\n\tflex: 1;\n}\n\n.day-badge {\n\tpadding: 4rpx 12rpx;\n\tborder-radius: 20rpx;\n\tfont-size: 24rpx;\n\tbackground-color: #52C41A;\n\tcolor: #FFFFFF;\n\tmargin-left: auto;\n}\n\n.next-day-badge {\n\tbackground-color: #FA8C16;\n}\n\n.input-hint {\n\tfont-size: 22rpx;\n\tcolor: #999999;\n\tmargin-top: 8rpx;\n\tdisplay: block;\n}\n\n.form-buttons {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tmargin-top: 40rpx;\n\tgap: 20rpx;\n}\n\n.btn {\n\tflex: 1;\n\theight: 80rpx;\n\tborder-radius: 8rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tline-height: 1;\n\ttransition: all 0.3s ease;\n}\n\n.btn-cancel {\n\tbackground-color: #F5F5F5;\n\tcolor: #666666;\n\tborder: 1px solid #EEEEEE;\n}\n\n.btn-primary {\n\tbackground-color: #1677FF;\n\tcolor: #FFFFFF;\n\tbox-shadow: 0 4rpx 8rpx rgba(22, 119, 255, 0.2);\n}\n\n.btn:active {\n\ttransform: scale(0.98);\n\topacity: 0.9;\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775842513\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}