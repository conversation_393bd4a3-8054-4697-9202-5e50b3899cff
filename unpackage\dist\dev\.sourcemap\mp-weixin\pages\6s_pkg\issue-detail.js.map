{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s_pkg/issue-detail.vue?d7ff", "webpack:///D:/Xwzc/pages/6s_pkg/issue-detail.vue?b384", "webpack:///D:/Xwzc/pages/6s_pkg/issue-detail.vue?04da", "webpack:///D:/Xwzc/pages/6s_pkg/issue-detail.vue?bf21", "uni-app:///pages/6s_pkg/issue-detail.vue", "webpack:///D:/Xwzc/pages/6s_pkg/issue-detail.vue?c014", "webpack:///D:/Xwzc/pages/6s_pkg/issue-detail.vue?504a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "issueId", "issue", "loading", "loadError", "dataLoaded", "unifiedTimeline", "currentUserRole", "currentUserId", "issueSubmittedForInspection", "computed", "shouldShowActionButton", "isButtonDisabled", "deadlineCountdown", "countdownClass", "onLoad", "uni", "onUnload", "methods", "goBack", "initializeUserInfo", "userInfo", "roles", "require", "userRoleStr", "userRole", "console", "loadIssueDetail", "callCloudFunction", "action", "issue_id", "result", "errorMsg", "storageKey", "cachedData", "title", "icon", "duration", "retryLoad", "checkIfResponsible", "checkSubmissionStatus", "item", "formatIssueData", "id", "number", "description", "status", "location", "responsible", "role", "deadline", "priority", "createdAt", "updatedAt", "images", "history", "rectification_description", "rectification_photos", "completed_at", "assigned_to", "responsible_id", "formatDate", "normalizedDateStr", "formatImages", "formatHistory", "time", "operator", "generateUnifiedTimeline", "key", "standardSteps", "stepData", "baseDate", "timeline", "validateTimelineLogic", "targetStep", "currentStep", "step", "generateStepTime", "getDefaultOperator", "getDefaultDescription", "hasHistoryRecord", "getStatusText", "getPriorityText", "getIssueNumber", "getDeadlineCountdown", "deadlineDate", "getCountdownClass", "getButtonText", "handleMainAction", "startRectification", "action_type", "url", "continueRectification", "adminManageIssue", "adminCheckProgress", "itemList", "success", "adminAcceptIssue", "approveIssue", "content", "res", "issueData", "rejectIssue", "changeIssueStatus", "statusOptions", "value", "label", "selectedStatus", "updateIssueStatus", "actualStatus", "newStatus", "goToRectificationPage", "previewImage", "urls", "current", "previewRectificationImage", "reopenIssue", "editIssue", "formatDateTime", "date", "handleIssueUpdated"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/FA;AAAA;AAAA;AAAA;AAAsmB,CAAgB,goBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2M1nB;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;MAEA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;;IAEA;IACAC;EACA;EAEAC;IACA;IACAD;EACA;EACAE;IACAC;MACAH;IACA;IAEA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACAC,8DAEA;kBACAC;kBACA;oBAAA,WACAC;oBACAC;oBACAC;oBAEA;sBACAA;oBACA;sBACAA;oBACA;;oBAEA;oBACA;sBACAH;oBACA;kBACA;oBACA;kBAAA;;kBAGA;kBACA;oBACAA;kBACA;oBACA;oBACAA;kBACA;;kBAEA;;kBAEA;oBACA;;oBAEA;oBACA;sBACA;oBACA;sBACA;oBACA;sBACA;oBACA;sBACA;oBACA;kBAEA;oBACA;oBACA;kBACA;gBAGA;kBACAI;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACA;gBAAA,YAEAJ,iGAEA;gBAAA;gBAAA,OACAK;kBACAC;kBACA7B;oBACA8B;kBACA;gBACA;cAAA;gBALAC;gBAAA,MAOAA;kBAAA;kBAAA;gBAAA;gBAAA,IAEAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGA;gBACA;kBACAL;gBACA;gBAEA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEAM;gBAAA,MACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;gBAOAC;gBACAC;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAEAlB;kBACAmB;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAOA;gBACArB;kBACAmB;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAE;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;UAAA,OACAC;QAAA,EACA;;QAEA;QACA;QAEA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACA;MACA;MAEA;QACAC;QACAC;QACAT;QACAU;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACA;QACAC;QACAC;QACAC;QACA;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;QACA;UACA;UACAC;QACA;QAEA;QACA;UACA;QACA;;QAEA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QAAA;UACAnC;UACAgB;UACAoB;UACAC;QACA;MAAA;IACA;IAEAC;MAAA;MACA;MACA;;MAEA;MACA,qBACA;QAAAC;QAAAjC;QAAAN;MAAA,GACA;QAAAuC;QAAAjC;QAAAN;MAAA,GACA;QAAAuC;QAAAjC;QAAAN;MAAA,GACA;QAAAuC;QAAAjC;QAAAN;MAAA,GACA;QAAAuC;QAAAjC;QAAAN;MAAA,GACA;QAAAuC;QAAAjC;QAAAN;MAAA,EACA;;MAEA;MACAwC;QACA;UACAlC;UACAU;UACAoB;UACAC;UACApB;QACA;;QAEA;QACA;UACA;YAAA;UAAA;UACA;YACAwB;YACAA;YACAA;YACAA;UACA;QACA;;QAEA;QACA;UACA;UACA;YACA;YACAA;YACAA;YACAA;YACA;YACA;YACA;cACAC;YACA;;YACAD;UACA;YACA;cACAA;cACAA;YACA;cACAA;cACAA;YACA;cACA;cACAA;cACAA;YACA;cACAA;cACAA;cACAA;cACAA;YACA;UACA;YACA;cACAA;cACAA;YACA;cACAA;cACAA;YACA;cACA;cACAA;cACAA;YACA;cACAA;cACAA;YACA;cACAA;cACAA;YACA;cACAA;cACAA;cACAA;cACAA;YACA;cACAA;cACAA;cACAA;cACAA;YACA;UACA;YACA;cACAA;cACAA;YACA;cACAA;cACAA;YACA;cACA;cACAA;cACAA;YACA;cACAA;cACAA;YACA;cACAA;cACAA;YACA;cACA;cACAA;cACAA;cACAA;cACAA;YACA;UACA;YACA;cACAA;cACAA;YACA;cACAA;cACAA;YACA;cACA;cACAA;cACAA;YACA;cACAA;cACAA;YACA;cACAA;cACAA;YACA;cACAA;cACAA;cACAA;cACAA;YACA;cACAA;cACAA;cACAA;cACAA;YACA;UACA;QACA;QAEAE;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;;MAEA;MACA;QACA;UAAAC;UAAAC;QAAA;QAAA;QACA;UAAAD;UAAAC;QAAA;QAAA;QACA;UAAAD;UAAAC;QAAA;QAAA;QACA;QACA;UAAAD;UAAAC;QAAA;QAAA;QACA;UAAAD;UAAAC;QAAA;QAAA;QACA;UAAAD;UAAAC;QAAA;QAAA;QACA;UAAAD;UAAAC;QAAA;QAAA;QACA;UAAAD;UAAAC;QAAA;QAAA;QACA;UAAAD;UAAAC;QAAA;QAAA;QACA;UAAAD;UAAAC;QAAA;MACA;;MAEA;QAAAD;QAAAC;MAAA;MAEAH;QACA;UACA;UACAI;UACAA;UACAA;UACAA;QACA;UACA;UACAA;UACAA;UACAA;UACAA;QACA;UACA;UACA;YACAA;YACAA;UACA;YACAA;YACAA;UACA;YACAA;YACAA;UACA;YACAA;YACAA;UACA;QACA;UACA;UACAA;UACAA;UACAA;UACAA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;;MAEA;MACA;MACA;MAEA;QACA;UACA;UACA;UACA;QACA;QACA;UACA;UACA;UACA;QACA;QACA;UACA;UACA;UACA;UACA;UACA;QACA;QACA;UACA;UACA;UACA;QACA;QACA;UACA;UACA;UACA;QACA;QACA;UACA;UACA;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;MACA;MAEA;QAAA;MAAA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEA;QACA;;QAEA;QACA;UACA;UACA;UACA;QACA;;QAEA;QACA;UACA;UACA;UACA;QACA;;QAEA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;QACA;QACA;;QAEA;QACA;UACA;UACA;UACA;YACA;UACA;UACAC;QACA;;QAEA;QACA;QACA;QAEA;QACA;QAEA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;QACA;QACA;;QAEA;QACA;UACA;UACA;YACA;UACA;UACAD;QACA;;QAEA;QACA;QACA;QAEA;QACA;QAEA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAE;MACA;MAEA;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;UACA;QACA;UACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,YAEAlE,iGAEA;gBAAA;gBAAA,OACAK;kBACAC;kBACA7B;oBACA8B;oBACAgB;oBACA4C;kBACA;gBACA;cAAA;gBAPA3D;gBAAA,MASAA;kBAAA;kBAAA;gBAAA;gBACAf;kBACA2E;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA3E;kBACAmB;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAwD;MACA5E;QACA2E;MACA;IACA;IAIA;IACAE;MACA;IACA;IAEA;IACAC;MAAA;MACA9E;QACA+E;QACAC;UACA;YACA;cACA;cACA;YACA;cACA;cACAhF;gBACAmB;gBACAC;cACA;cACA;UAAA;QAEA;MACA;IACA;IAEA;IACA6D;MAAA;MACAjF;QACA+E;QACAC;UACA;YACA;cACA;cACA;YACA;cACA;cACA;UAAA;QAEA;MACA;IACA;IAEA;IACAE;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAlF;kBACAmB;kBACAgE;kBACAH;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAI;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA,YAEA7E;8BAAA;8BAAA,OACAK;gCACAC;gCACA7B;kCACA8B;kCACAgB;kCACA4C;gCACA;8BACA;4BAAA;8BAPA3D;8BASA;gCACA;gCACA;gCACAf;kCACAmB;kCACAC;gCACA;;gCAEA;gCACApB;kCACAa;kCACA5B;kCACAoG;gCACA;8BACA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAEArF;gCACAmB;gCACAC;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAGA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAkE;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAtF;kBACAmB;kBACAgE;kBACAH;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAI;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA,YAEA7E;8BAAA;8BAAA,OACAK;gCACAC;gCACA7B;kCACA8B;kCACAgB;kCACA4C;gCACA;8BACA;4BAAA;8BAPA3D;8BASA;gCACA;gCACA;gCACAf;kCACAmB;kCACAC;gCACA;;gCAEA;gCACApB;kCACAa;kCACA5B;kCACAoG;gCACA;8BACA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAEArF;gCACAmB;gCACAC;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAGA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAmE;MAAA;MACA;MACA;MAEA;MACA;;MAEA;MACA;QACAC,iBACA;UAAAC;UAAAC;QAAA,EACA;MACA;QACA;QACA;UACAF,iBACA;YAAAC;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,EACA;QACA;UACA;UACAF,iBACA;YAAAC;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,EACA;QACA;UACAF,iBACA;YAAAC;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA;UAAA,CACA;QACA;UACAF,iBACA;YAAAC;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,EACA;QACA;UACA;UACAF,iBACA;YAAAC;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,EACA;QACA;UACA;UACAF,iBACA;YAAAC;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA;UACA;UAAA,CACA;QACA;MACA;;MAEA1F;QACA+E;UAAA;QAAA;QACAC;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBACAW;oBAAA,MACAA;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA,OACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAEA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,YAEArF,iGAEA;gBACAsF;gBACA;kBACAA;gBACA;;gBAEA;gBAAA;gBAAA,OACAjF;kBACAC;kBACA7B;oBACA8B;oBACAgB;oBACA4C;kBACA;gBACA;cAAA;gBAPA3D;gBAAA,MASAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACAf;kBACAmB;kBACAC;gBACA;;gBAEA;gBACApB;kBACAa;kBACA5B;kBACA6G;kBACAT;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA3E;gBACAV;kBACAmB;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAIA2E;MACA/F;QACA2E;MACA;IACA;IAIAqB;MACAhG;QACAiG;QACAC;MACA;IACA;IAEA;IACAC;MACA;QAAA;MAAA;MACAnG;QACAiG;QACAC;MACA;IACA;IAEA;IACAE;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACApG;kBACAmB;kBACAgE;kBACAH;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAI;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA,YAEA7E;8BAAA;8BAAA,OAEAK;gCACAC;gCACA7B;kCACA8B;kCACAgB;kCACA4C;gCACA;8BACA;4BAAA;8BAPA3D;8BAAA,MASAA;gCAAA;gCAAA;8BAAA;8BACA;8BACA;8BACAf;gCACAmB;gCACAC;8BACA;;8BAEA;8BACApB;gCACAa;gCACA5B;gCACAoG;8BACA;8BAAA;8BAAA;4BAAA;8BAAA,MAEA;4BAAA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAGArF;gCACAmB;gCACAC;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAGA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAiF;MACA;MACA;QACAlF;QACAU;QACAE;QACAG;QACAF;QACAY;QAAA;QACAT;QACAG;MACA;MAEAtC;QACA2E;MACA;IACA;IAEA2B;MACA;MACA;QACA;QACA;UACA;UACA;UACA;YACA;YACAxD;YACA;cACAA;YACA;UACA;YACA;YACAA;UACA;UACAyD;QACA;UACAA;QACA;;QAEA;QACA;UACA;QACA;QAEA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA9F;;MAEA;MACA;QACAA;;QAEA;QACA;UACA;UACA;YACA;;YAEA;YACA;cACA;YACA;YAEA;cAAA,OACAe;YAAA,EACA;YAEA;cACA;gBACAZ;gBACAgB;gBACAoB;gBACAC;cACA;YACA;YAEA;YACA;;YAEA;YACAlD;cACAmB;cACAC;cACAC;YACA;UACA;QACA;UACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/8CA;AAAA;AAAA;AAAA;AAAyqC,CAAgB,+oCAAG,EAAC,C;;;;;;;;;;;ACA7rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/issue-detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/issue-detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./issue-detail.vue?vue&type=template&id=43779f7e&scoped=true&\"\nvar renderjs\nimport script from \"./issue-detail.vue?vue&type=script&lang=js&\"\nexport * from \"./issue-detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./issue-detail.vue?vue&type=style&index=0&id=43779f7e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"43779f7e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/issue-detail.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./issue-detail.vue?vue&type=template&id=43779f7e&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    !_vm.loading && !_vm.loadError && _vm.dataLoaded && _vm.issue\n      ? _vm.getIssueNumber()\n      : null\n  var m1 =\n    !_vm.loading && !_vm.loadError && _vm.dataLoaded && _vm.issue\n      ? _vm.getStatusText(_vm.issue.status)\n      : null\n  var m2 =\n    !_vm.loading && !_vm.loadError && _vm.dataLoaded && _vm.issue\n      ? _vm.getPriorityText(\n          _vm.issue && _vm.issue.priority ? _vm.issue.priority : \"normal\"\n        )\n      : null\n  var g0 =\n    !_vm.loading && !_vm.loadError && _vm.dataLoaded && _vm.issue\n      ? _vm.issue.images && _vm.issue.images.length > 0\n      : null\n  var g1 =\n    !_vm.loading && !_vm.loadError && _vm.dataLoaded && _vm.issue\n      ? _vm.issue.rectification_description ||\n        (_vm.issue.rectification_photos &&\n          _vm.issue.rectification_photos.length > 0)\n      : null\n  var g2 =\n    !_vm.loading && !_vm.loadError && _vm.dataLoaded && _vm.issue && g1\n      ? _vm.issue.rectification_photos &&\n        _vm.issue.rectification_photos.length > 0\n      : null\n  var m3 =\n    !_vm.loading &&\n    !_vm.loadError &&\n    _vm.dataLoaded &&\n    _vm.issue &&\n    g1 &&\n    _vm.issue.completed_at\n      ? _vm.formatDateTime(_vm.issue.completed_at)\n      : null\n  var m4 =\n    _vm.dataLoaded && _vm.issue && _vm.shouldShowActionButton\n      ? _vm.getButtonText()\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        m3: m3,\n        m4: m4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./issue-detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./issue-detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page-container\">\n    <!-- 内容区域加载状态 -->\n    <view v-if=\"loading\" class=\"content-loading\">\n      <view class=\"loading-content\">\n        <uni-icons type=\"spinner-cycle\" size=\"40\" color=\"#007AFF\"></uni-icons>\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n    </view>\n\n    <!-- 错误状态 -->\n    <view v-else-if=\"loadError\" class=\"content-error\">\n      <p-empty-state \n        type=\"error\"\n        title=\"加载失败\"\n        description=\"网络异常，请检查网络连接\"\n        :show-button=\"true\"\n        button-text=\"重新加载\"\n        @button-click=\"retryLoad\"\n      ></p-empty-state>\n    </view>\n\n    <!-- 正常内容 -->\n    <view v-else-if=\"dataLoaded && issue\" class=\"content\">\n      <!-- 问题状态卡片 -->\n      <view class=\"status-card\">\n        <!-- 顶部状态栏 -->\n        <view class=\"status-bar\">\n          <view class=\"issue-id\">#{{ getIssueNumber() }}</view>\n          <view class=\"status-badge\" :class=\"['status-' + issue.status]\">\n            {{ getStatusText(issue.status) }}\n          </view>\n        </view>\n        \n        <!-- 问题标题 -->\n        <view class=\"issue-title\">{{ issue.title }}</view>\n        \n        <!-- 关键信息网格 -->\n        <view class=\"key-info\">\n          <view class=\"info-item\">\n            <view class=\"info-label\">\n              <uni-icons type=\"person\" size=\"14\" color=\"#007AFF\"></uni-icons>\n              <text>负责人</text>\n            </view>\n            <view class=\"info-value\">\n              <text class=\"main-value\">{{ issue.responsible }}</text>\n            </view>\n          </view>\n          <view class=\"info-item\">\n            <view class=\"info-label\">\n              <uni-icons type=\"calendar\" size=\"14\" color=\"#FF9500\"></uni-icons>\n              <text>截止时间</text>\n            </view>\n            <view class=\"info-value-with-badge\">\n              <text class=\"main-value\">{{ issue.deadline }}</text>\n              <view \n                v-if=\"issue.deadline\" \n                class=\"countdown-badge\" \n                :class=\"countdownClass\"\n              >\n                {{ deadlineCountdown || '计算中...' }}\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 位置信息 -->\n        <view class=\"location-info\">\n          <view class=\"location-section\">\n            <uni-icons type=\"location\" size=\"16\" color=\"#007AFF\"></uni-icons>\n            <text>{{ issue.location }}</text>\n          </view>\n          <view class=\"priority-badge\" :class=\"'priority-' + (issue && issue.priority ? issue.priority : 'normal')\">\n            <view class=\"priority-dot\"></view>\n            <text>{{ getPriorityText(issue && issue.priority ? issue.priority : 'normal') }}</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 问题描述 -->\n      <view class=\"description-card\">\n        <view class=\"card-title\">问题描述</view>\n        <view class=\"description-text\">{{ issue.description }}</view>\n      </view>\n\n      <!-- 问题图片 -->\n      <view v-if=\"issue.images && issue.images.length > 0\" class=\"images-card\">\n        <view class=\"card-title\">问题图片</view>\n        <view class=\"images-grid\">\n          <view \n            v-for=\"(image, index) in issue.images\" \n            :key=\"index\"\n            class=\"image-item\"\n            @click=\"previewImage(index)\"\n          >\n            <image :src=\"image\" mode=\"aspectFill\" class=\"issue-image\" />\n          </view>\n        </view>\n      </view>\n\n      <!-- 整改内容 -->\n      <view v-if=\"issue.rectification_description || (issue.rectification_photos && issue.rectification_photos.length > 0)\" class=\"rectification-card\">\n        <view class=\"card-title\">整改内容</view>\n        \n        <!-- 整改说明 -->\n        <view v-if=\"issue.rectification_description\" class=\"rectification-description\">\n          <view class=\"rectification-label\">整改说明</view>\n          <view class=\"rectification-text\">{{ issue.rectification_description }}</view>\n        </view>\n        \n        <!-- 整改照片 -->\n        <view v-if=\"issue.rectification_photos && issue.rectification_photos.length > 0\" class=\"rectification-images\">\n          <view class=\"rectification-label\">整改照片</view>\n          <view class=\"images-grid\">\n            <view \n              v-for=\"(photo, index) in issue.rectification_photos\" \n              :key=\"index\"\n              class=\"image-item\"\n              @click=\"previewRectificationImage(index)\"\n            >\n              <image :src=\"photo.url || photo\" mode=\"aspectFill\" class=\"rectification-image\" />\n            </view>\n          </view>\n        </view>\n        \n        <!-- 整改完成时间 -->\n        <view v-if=\"issue.completed_at\" class=\"rectification-time\">\n          <view class=\"rectification-label\">完成时间</view>\n          <view class=\"rectification-time-text\">{{ formatDateTime(issue.completed_at) }}</view>\n        </view>\n      </view>\n\n      <!-- 处理进展 -->\n      <view class=\"timeline-card\">\n        <view class=\"card-title\">处理进展</view>\n        <view class=\"unified-timeline\">\n          <view \n            v-for=\"(item, index) in unifiedTimeline\" \n            :key=\"index\"\n            class=\"timeline-item\"\n            :class=\"{ \n              completed: item.status === 'completed', \n              current: item.status === 'current',\n              pending: item.status === 'pending',\n              overdue: item.status === 'overdue'\n            }\"\n          >\n            <view class=\"timeline-dot\">\n              <uni-icons \n                v-if=\"item.status === 'completed'\" \n                type=\"checkmarkempty\" \n                size=\"16\" \n                color=\"#ffffff\"\n              ></uni-icons>\n              <uni-icons \n                v-else-if=\"item.status === 'current'\" \n                type=\"gear\" \n                size=\"16\" \n                color=\"#ffffff\"\n              ></uni-icons>\n              <uni-icons \n                v-else-if=\"item.status === 'overdue'\" \n                type=\"closeempty\" \n                size=\"16\" \n                color=\"#ffffff\"\n              ></uni-icons>\n              <view \n                v-else \n                class=\"dot-placeholder\"\n              ></view>\n            </view>\n            <view class=\"timeline-content\">\n              <view class=\"timeline-header\">\n                <view class=\"step-title\">{{ item.title }}</view>\n                <view class=\"step-time\" v-if=\"item.time\">{{ item.time }}</view>\n              </view>\n              <view class=\"step-desc\" v-if=\"item.description\">{{ item.description }}</view>\n              <view class=\"step-operator\" v-if=\"item.operator\">操作人：{{ item.operator }}</view>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 底部安全间距 -->\n      <view class=\"bottom-safe-area\"></view>\n    </view>\n\n    <!-- 操作按钮 -->\n    <view class=\"action-bar\" v-if=\"dataLoaded && issue && shouldShowActionButton\">\n      <view class=\"action-buttons\">\n        <button \n          class=\"action-btn primary full\" \n          :disabled=\"isButtonDisabled\"\n          @click=\"handleMainAction\"\n        >\n          {{ getButtonText() }}\n        </button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'IssueDetail',\n  data() {\n    return {\n      issueId: null,\n      issue: null,\n      loading: false,\n      loadError: false,\n      dataLoaded: false,\n      unifiedTimeline: [],\n      currentUserRole: null, // 'admin' | 'inspector' | 'responsible' | 'viewer'\n      currentUserId: null, // 当前用户ID\n      issueSubmittedForInspection: false // 整改内容是否已提交检查\n    }\n  },\n  computed: {\n    // 是否显示操作按钮\n    shouldShowActionButton() {\n      // 数据未加载时不显示按钮\n      if (!this.issue || !this.dataLoaded) {\n        return false;\n      }\n      \n      // 只有负责人、检查员或管理员在特定状态下才显示按钮\n      if (this.currentUserRole === 'viewer') {\n        return false;\n      }\n      \n      const status = this.issue.status;\n      \n      if (this.currentUserRole === 'responsible') {\n        // 负责人：已分配、待整改、整改中且未提交检查、被驳回（逾期问题不能操作）\n        return status === 'assigned' || status === 'pending' || status === 'rejected' || (status === 'in_progress' && !this.issueSubmittedForInspection);\n      } else if (this.currentUserRole === 'admin') {\n        // 管理员：检查通过后可以重新打开，其他状态都可以操作\n        return true;\n      } else if (this.currentUserRole === 'inspector') {\n        // 检查员：整改已提交检查 或 待检查状态\n        return this.issueSubmittedForInspection || status === 'pending_review';\n      }\n      \n      return false;\n    },\n    \n    // 按钮是否禁用\n    isButtonDisabled() {\n      return false; // 可以根据具体业务逻辑调整\n    },\n    \n    // 截止时间倒计时文本\n    deadlineCountdown() {\n      if (!this.issue || !this.issue.deadline) return '';\n      return this.getDeadlineCountdown(this.issue.deadline);\n    },\n    \n    // 倒计时样式类\n    countdownClass() {\n      if (!this.issue || !this.issue.deadline) return '';\n      return this.getCountdownClass(this.issue.deadline);\n    }\n  },\n  onLoad(options) {\n    this.issueId = options.id\n    this.initializeUserInfo()\n    this.loadIssueDetail()\n    \n    // 监听数据更新事件\n    uni.$on('monthlyIssueUpdated', this.handleIssueUpdated);\n  },\n  \n  onUnload() {\n    // 移除事件监听\n    uni.$off('monthlyIssueUpdated', this.handleIssueUpdated);\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack()\n    },\n    \n    // 初始化用户信息\n    async initializeUserInfo() {\n      try {\n        // 获取当前用户信息（与其他6S页面保持一致的实现）\n        const userInfo = uni.getStorageSync('uni-id-pages-userInfo') || {};\n        \n        // 尝试获取用户角色信息（从专门的角色缓存中获取）\n        let roles = [];\n        try {\n          const { getCacheKey, CACHE_KEYS } = require('@/utils/cache.js');\n          let userRoleStr = uni.getStorageSync(getCacheKey(CACHE_KEYS.USER_ROLE)) || '{}';\n          let userRole = {};\n          \n          if (typeof userRoleStr === 'string') {\n            userRole = JSON.parse(userRoleStr);\n          } else {\n            userRole = userRoleStr;\n          }\n          \n          // 从userRole.value.userRole获取角色数组\n          if (userRole && userRole.value && userRole.value.userRole) {\n            roles = Array.isArray(userRole.value.userRole) ? userRole.value.userRole : [userRole.value.userRole];\n          }\n        } catch (cacheError) {\n          // 无法获取角色缓存，使用备用方案\n        }\n        \n        // 备用方案：从用户信息中获取角色\n        if (roles.length === 0 && userInfo.role) {\n          roles = Array.isArray(userInfo.role) ? userInfo.role : [userInfo.role];\n        } else if (roles.length === 0 && userInfo.username === 'admin') {\n          // 特殊处理：如果用户名是admin，给予admin角色\n          roles = ['admin'];\n        }\n        \n        // 用户权限初始化\n        \n        if (userInfo && (userInfo._id || userInfo.id)) {\n          this.currentUserId = userInfo._id || userInfo.id;\n          \n          // 根据用户角色确定权限\n          if (roles.includes('admin')) {\n            this.currentUserRole = 'admin';\n          } else if (roles.includes('inspector') || roles.includes('Integrated') || roles.includes('reviser')) {\n            this.currentUserRole = 'inspector';\n          } else if (roles.includes('responsible')) {\n            this.currentUserRole = 'responsible';\n          } else {\n            this.currentUserRole = 'viewer';\n          }          \n\n        } else {\n          this.currentUserRole = 'viewer';\n          this.currentUserId = null;\n        }\n\n        \n      } catch (error) {\n        console.log('初始化用户信息失败:', error);\n        this.currentUserRole = 'viewer';\n      }\n    },\n\n    async loadIssueDetail() {\n      try {\n        this.loading = true;\n        this.loadError = false;\n        \n        const { callCloudFunction } = require('@/utils/auth.js');\n        \n        // 调用真实API获取问题详情\n        const result = await callCloudFunction('hygiene-monthly-inspection', {\n          action: 'getIssueDetail',\n          data: {\n            issue_id: this.issueId\n          }\n        });\n        \n        if (result && result.success) {\n          // 检查返回的数据结构\n          if (!result.data) {\n            throw new Error('未找到对应的问题记录');\n          }\n          \n          // 检查状态值是否有效\n          if (result.data.status && !['pending', 'in_progress', 'pending_review', 'approved', 'overdue', 'rejected', 'open', 'assigned', 'resolved', 'reviewing', 'submitted', 'verified', 'cancelled', 'suspended', 'reopened', 'draft', 'new', 'active', 'inactive', 'expired'].includes(result.data.status)) {\n            console.warn('未识别的状态值:', result.data.status, '完整数据:', result.data);\n          }\n          \n          this.issue = this.formatIssueData(result.data);\n          // 检查是否为负责人\n          this.checkIfResponsible();\n          // 检查整改提交状态\n          this.checkSubmissionStatus();\n          this.generateUnifiedTimeline();\n          this.dataLoaded = true;\n        } else {\n          const errorMsg = result?.message || result?.error || '获取问题详情失败';\n          throw new Error(errorMsg);\n        }\n        \n      } catch (error) {\n        \n        // 如果是网络错误或API不可用，尝试从本地存储获取\n        try {\n          const storageKey = `issue_detail_${this.issueId}`;\n          const cachedData = uni.getStorageSync(storageKey);\n          \n          if (cachedData && typeof cachedData === 'object') {\n            this.issue = this.formatIssueData(cachedData);\n            this.checkIfResponsible();\n            this.checkSubmissionStatus();\n            this.generateUnifiedTimeline();\n            this.dataLoaded = true;\n            \n            uni.showToast({\n              title: '已加载缓存数据',\n              icon: 'none',\n              duration: 2000\n            });\n            return;\n          }\n        } catch (cacheError) {\n          // 缓存数据读取失败\n        }\n        \n        this.loadError = true;\n        uni.showToast({\n          title: error.message || '加载失败，请检查网络连接',\n          icon: 'error'\n        });\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    retryLoad() {\n      this.loadError = false;\n      this.dataLoaded = false;\n      this.loadIssueDetail();\n    },\n    \n    // 检查当前用户是否为负责人\n    checkIfResponsible() {\n      if (this.issue && this.currentUserId) {\n        const assignedToId = this.issue.assigned_to || this.issue.responsible_id;\n        if (assignedToId === this.currentUserId) {\n          this.currentUserRole = 'responsible';\n        }\n      }\n    },\n    \n    // 检查整改提交状态\n    checkSubmissionStatus() {\n      if (this.issue) {\n        // 检查历史记录中是否有提交检查的记录\n        const hasSubmissionRecord = this.issue.history && this.issue.history.some(item => \n          item.action === '提交检查' || item.action === 'submit_for_inspection' || item.action === 'submit_rectification'\n        );\n        \n        // 如果状态是pending_review且没有提交记录，说明刚刚提交\n        const isJustCompleted = this.issue.status === 'pending_review';\n        \n        this.issueSubmittedForInspection = hasSubmissionRecord || isJustCompleted;\n      }\n    },\n    \n    // 格式化API返回的问题数据\n    formatIssueData(data) {\n      // 防护性检查，确保data存在\n      if (!data || typeof data !== 'object') {\n        throw new Error('问题数据格式错误');\n      }\n      \n      return {\n        id: data._id || data.id || this.issueId,\n        number: data.issue_number || data.number || data.issue_id || this.issueId,\n        title: data.title || data.issue_title || data.name || data.subject || '待确认问题',\n        description: data.description || data.issue_description || '',\n        status: data.status || data.issue_status || 'pending',\n        location: data.location_info?.location_name || data.location_info?.name || data.location || data.location_name || '待确认',\n        responsible: data.assigned_to_name || data.responsible || data.responsible_name || '未分配',\n        role: data.assigned_to_role || data.role || data.responsible_role || '负责人',\n        deadline: this.formatDate(data.expected_completion_date || data.deadline || data.due_date || data.target_date),\n        priority: data.priority || data.priority_level || 'normal',\n        createdAt: this.formatDate(data.created_at || data.createdAt || data.create_time),\n        updatedAt: this.formatDate(data.updated_at || data.updatedAt || data.update_time),\n        images: this.formatImages(data.photos || data.images || data.issue_photos || []),\n        history: this.formatHistory(data.history || data.progress_logs || data.logs || []),\n        // 整改相关数据\n        rectification_description: data.rectification_description || '',\n        rectification_photos: this.formatImages(data.rectification_photos || []),\n        completed_at: data.completed_at,\n        // 保存原始数据以备后用\n        assigned_to: data.assigned_to,\n        responsible_id: data.responsible_id\n      };\n    },\n    \n    // 格式化日期\n    formatDate(dateStr) {\n      if (!dateStr) return '';\n      try {\n        // 处理iOS兼容性问题\n        let normalizedDateStr = dateStr;\n        if (typeof dateStr === 'string' && dateStr.includes('-') && !dateStr.includes('T')) {\n          // 将 \"YYYY-MM-DD\" 转换为 \"YYYY/MM/DD\" 以兼容iOS\n          normalizedDateStr = dateStr.replace(/-/g, '/');\n        }\n        \n        const date = new Date(normalizedDateStr);\n        if (isNaN(date.getTime())) {\n          return dateStr;\n        }\n        \n        // 返回 YYYY-MM-DD 格式，便于显示和计算\n        return date.toISOString().split('T')[0];\n      } catch (error) {\n        return dateStr;\n      }\n    },\n    \n    // 格式化图片数据\n    formatImages(images) {\n      if (!Array.isArray(images)) return [];\n      return images.map(img => {\n        if (typeof img === 'string') return img;\n        return img.url || img.cloudPath || img;\n      }).filter(Boolean);\n    },\n    \n    // 格式化历史记录\n    formatHistory(history) {\n      if (!Array.isArray(history)) return [];\n      return history.map(item => ({\n        action: item.action || item.type || '操作',\n        description: item.description || item.content || '',\n        time: this.formatDateTime(item.time || item.timestamp || item.created_at),\n        operator: item.operator || item.user_name || item.created_by || '系统'\n      }));\n    },\n\n    generateUnifiedTimeline() {\n      const timeline = [];\n      const status = this.issue.status;\n      \n      // 定义标准的时间轴步骤\n      const standardSteps = [\n        { key: 'create', title: '创建问题', action: '创建问题' },\n        { key: 'assign', title: '分配负责人', action: '分配负责人' },\n        { key: 'start', title: '开始整改', action: '开始整改' },\n        { key: 'complete', title: '提交整改', action: '整改完成' },\n        { key: 'inspect', title: '检查问题', action: '检查问题' },\n        { key: 'approve', title: '检查通过', action: '检查通过' }\n      ];\n      \n      // 为每个标准步骤生成时间轴项\n      standardSteps.forEach(step => {\n        let stepData = {\n          title: step.title,\n          description: '',\n          time: '',\n          operator: '',\n          status: 'pending'\n        };\n        \n        // 从历史记录中查找对应的步骤\n        if (this.issue.history && this.issue.history.length > 0) {\n          const historyItem = this.issue.history.find(h => h.action === step.action);\n          if (historyItem) {\n            stepData.description = historyItem.description;\n            stepData.time = historyItem.time;\n            stepData.operator = historyItem.operator;\n            stepData.status = 'completed';\n          }\n        }\n        \n         // 根据问题状态确定步骤状态\n         if (stepData.status !== 'completed') {\n           // 如果历史记录中没有找到，根据当前状态推断\n           if (step.key === 'create' || step.key === 'assign') {\n             // 创建问题和分配负责人总是已完成的\n             stepData.status = 'completed';\n             stepData.description = step.key === 'create' ? '问题已创建并记录' : '负责人已分配';\n             stepData.operator = step.key === 'create' ? '检查员' : '管理员';\n             // 生成默认时间\n             const baseDate = new Date(this.issue.createdAt || '2024-01-15');\n             if (step.key === 'assign') {\n               baseDate.setHours(baseDate.getHours() + 1); // 分配在创建1小时后\n             }\n             stepData.time = this.formatDateTime(baseDate);\n           } else if (step.key === 'start') {\n             if (status === 'pending' || status === 'assigned') {\n               stepData.status = 'current';\n               stepData.description = '等待负责人开始整改';\n             } else if (status === 'reopened') {\n               stepData.status = 'current';\n               stepData.description = '问题已重新打开，等待开始整改';\n             } else if (status === 'overdue') {\n               // 逾期：在\"开始整改\"这一步卡住了\n               stepData.status = 'overdue';\n               stepData.description = '未能在截止时间前开始整改，问题已逾期';\n             } else {\n               stepData.status = 'completed';\n               stepData.description = '负责人已开始整改';\n               stepData.operator = this.issue.responsible || '负责人';\n               stepData.time = this.formatDateTime(new Date(2024, 0, 16, 9, 30));\n             }\n           } else if (step.key === 'complete') {\n             if (status === 'pending' || status === 'assigned') {\n               stepData.status = 'pending';\n               stepData.description = '等待开始整改';\n             } else if (status === 'reopened') {\n               stepData.status = 'pending';\n               stepData.description = '等待重新开始整改';\n             } else if (status === 'overdue') {\n               // 逾期：后续步骤都无法执行\n               stepData.status = 'pending';\n               stepData.description = '因问题逾期，此步骤无法执行';\n             } else if (status === 'in_progress' || status === 'active') {\n               stepData.status = 'current';\n               stepData.description = '负责人正在进行整改';\n                         } else if (status === 'rejected') {\n              stepData.status = 'current';\n              stepData.description = '检查未通过，需要重新整改';\n            } else if (status === 'pending_review') {\n              stepData.status = 'completed';\n              stepData.description = '整改工作已完成，等待检查';\n              stepData.operator = this.issue.responsible || '负责人';\n              stepData.time = this.formatDateTime(new Date());\n            } else {\n              stepData.status = 'completed';\n              stepData.description = '整改工作已完成';\n              stepData.operator = this.issue.responsible || '负责人';\n              stepData.time = this.formatDateTime(new Date(2024, 0, 18, 15, 20));\n            }\n                     } else if (step.key === 'inspect') {\n            if (status === 'pending' || status === 'assigned' || status === 'in_progress' || status === 'active') {\n              stepData.status = 'pending';\n              stepData.description = '等待整改完成';\n            } else if (status === 'reopened') {\n              stepData.status = 'pending';\n              stepData.description = '等待重新整改完成';\n            } else if (status === 'overdue') {\n              // 逾期：后续步骤都无法执行\n              stepData.status = 'pending';\n              stepData.description = '因问题逾期，此步骤无法执行';\n            } else if (status === 'pending_review') {\n              stepData.status = 'current';\n              stepData.description = '等待检查员检查整改效果';\n            } else if (status === 'reviewing' || status === 'submitted') {\n              stepData.status = 'current';\n              stepData.description = '检查员正在检查';\n            } else {\n              // approved, resolved, verified等状态：检查已经开始并完成\n              stepData.status = 'completed';\n              stepData.description = '检查员已验收整改效果';\n              stepData.operator = '检查员';\n              stepData.time = this.formatDateTime(new Date(2024, 0, 19, 10, 15));\n            }\n                     } else if (step.key === 'approve') {\n            if (status === 'pending' || status === 'assigned' || status === 'in_progress' || status === 'active') {\n              stepData.status = 'pending';\n              stepData.description = '等待检查完成';\n            } else if (status === 'reopened') {\n              stepData.status = 'pending';\n              stepData.description = '等待重新检查完成';\n            } else if (status === 'overdue') {\n              // 逾期：后续步骤都无法执行\n              stepData.status = 'pending';\n              stepData.description = '因问题逾期，此步骤无法执行';\n            } else if (status === 'pending_review') {\n              stepData.status = 'current';\n              stepData.description = '等待检查员确认';\n            } else if (status === 'reviewing' || status === 'submitted') {\n              stepData.status = 'pending';\n              stepData.description = '等待检查完成';\n            } else if (status === 'approved' || status === 'resolved' || status === 'verified') {\n              stepData.status = 'completed';\n              stepData.description = '检查员确认整改合格';\n              stepData.operator = '检查员';\n              stepData.time = this.formatDateTime(new Date(2024, 0, 19, 14, 30));\n            } else {\n              stepData.status = 'completed';\n              stepData.description = '检查员确认整改合格';\n              stepData.operator = '检查员';\n              stepData.time = this.formatDateTime(new Date(2024, 0, 19, 14, 30));\n            }\n           }\n         }\n        \n        timeline.push(stepData);\n      });\n      \n      // 确保时间轴的逻辑连贯性\n      this.validateTimelineLogic(timeline);\n      this.unifiedTimeline = timeline;\n    },\n\n    // 验证和修正时间轴逻辑\n    validateTimelineLogic(timeline) {\n      const status = this.issue.status;\n      let hasCurrentStep = false;\n      \n      // 根据状态确定应该到达的步骤和当前状态\n      const statusStepMap = {\n        'assigned': { targetStep: 1, currentStep: 1 },     // 已分配：刚完成分配，当前在\"开始整改\"\n        'pending': { targetStep: 2, currentStep: 2 },      // 待整改：分配已完成，当前在\"开始整改\"\n        'in_progress': { targetStep: 3, currentStep: 3 },  // 整改中：当前在\"提交整改\"\n        // 移除 completed 状态，统一使用 pending_review\n        'pending_review': { targetStep: 4, currentStep: 4 }, // 待检查：整改已完成，当前在\"检查问题\"\n        'approved': { targetStep: 6, currentStep: -1 },    // 已通过：流程结束，但可重新打开\n        'overdue': { targetStep: 2, currentStep: 2 },      // 逾期：卡在\"开始整改\"\n        'rejected': { targetStep: 3, currentStep: 3 },     // 已驳回：回到\"提交整改\"\n        'reopened': { targetStep: 2, currentStep: 2 },     // 重新打开：回到\"开始整改\"\n        'reviewing': { targetStep: 4, currentStep: 4 },    // 审核中：当前在\"检查问题\"\n        'submitted': { targetStep: 4, currentStep: 4 }     // 已提交：当前在\"检查问题\"\n      };\n      \n      const statusMapping = statusStepMap[status] || { targetStep: 2, currentStep: 2 };\n      \n      timeline.forEach((step, index) => {\n        if (index < statusMapping.targetStep) {\n          // 前面的步骤应该已完成\n          step.status = 'completed';\n          step.time = step.time || this.generateStepTime(index);\n          step.operator = step.operator || this.getDefaultOperator(index);\n          step.description = step.description || this.getDefaultDescription(index, 'completed');\n        } else if (statusMapping.currentStep === -1) {\n          // 流程已结束（approved），所有步骤都应该完成\n          step.status = 'completed';\n          step.time = step.time || this.generateStepTime(index);\n          step.operator = step.operator || this.getDefaultOperator(index);\n          step.description = step.description || this.getDefaultDescription(index, 'completed');\n        } else if (index === statusMapping.currentStep) {\n          // 当前步骤\n          if (status === 'overdue') {\n            step.status = 'overdue';\n            step.description = '未能在截止时间前完成，问题已逾期';\n          } else if (status === 'rejected') {\n            step.status = 'current';\n            step.description = '检查未通过，需要重新整改';\n          } else if (status === 'reopened') {\n            step.status = 'current';\n            step.description = '问题已重新打开，等待处理';\n          } else {\n            step.status = 'current';\n            step.description = step.description || this.getDefaultDescription(index, 'current');\n          }\n        } else {\n          // 后续步骤应该是待处理\n          step.status = 'pending';\n          step.time = '';\n          step.operator = '';\n          step.description = this.getDefaultDescription(index, 'pending');\n        }\n      });\n    },\n\n    // 生成步骤时间\n    generateStepTime(stepIndex) {\n      const baseTime = new Date(this.issue.createdAt || Date.now());\n      const stepTime = new Date(baseTime.getTime() + stepIndex * 2 * 60 * 60 * 1000); // 每步间隔2小时\n      return this.formatDateTime(stepTime);\n    },\n\n    // 获取默认操作者\n    getDefaultOperator(stepIndex) {\n      const operators = ['检查员', '管理员', this.issue.responsible || '负责人', this.issue.responsible || '负责人', '检查员', '检查员'];\n      return operators[stepIndex] || '系统';\n    },\n\n    // 获取默认描述\n    getDefaultDescription(stepIndex, stepStatus) {\n      const stepKeys = ['create', 'assign', 'start', 'complete', 'inspect', 'approve'];\n      const stepKey = stepKeys[stepIndex];\n      \n      // 获取关键信息用于更详细的描述\n      const responsible = this.issue.responsible || '负责人';\n      const deadline = this.issue.deadline ? `（截止：${this.formatDate(this.issue.deadline)}）` : '';\n      \n      const descriptions = {\n        'create': {\n          'completed': `检查员发现问题并记录在案${deadline ? '，已设定整改期限' : ''}`,\n          'current': '正在创建问题记录',\n          'pending': '等待创建问题'\n        },\n        'assign': {\n          'completed': `已指派${responsible}负责整改${deadline}`,\n          'current': '正在分配负责人',\n          'pending': '等待分配负责人'\n        },\n        'start': {\n          'completed': `${responsible}已接收任务并开始整改`,\n          'current': `等待${responsible}开始整改${deadline}`,\n          'pending': '等待开始整改',\n          'overdue': `整改已逾期${deadline}，需要跟进处理`,\n          'reopened': '问题已重新打开，需要重新开始整改'\n        },\n        'complete': {\n          'completed': `${responsible}已完成整改并提交，等待检查员验收`,\n          'current': `${responsible}正在进行整改工作${deadline}`,\n          'pending': '等待负责人提交整改结果'\n        },\n        'inspect': {\n          'completed': '检查员已开始现场检查整改效果',\n          'current': '等待检查员安排现场检查',\n          'pending': '等待检查验收'\n        },\n        'approve': {\n          'completed': '整改效果符合要求，问题已彻底解决',\n          'current': '检查员正在现场验收整改效果',\n          'pending': '等待检查验收结果'\n        }\n      };\n      \n      return descriptions[stepKey]?.[stepStatus] || '处理中';\n    },\n\n    // 检查是否有历史记录\n    hasHistoryRecord(stepIndex) {\n      const stepKeys = ['create', 'assign', 'start', 'complete', 'inspect', 'approve'];\n      const actions = ['创建问题', '分配负责人', '开始整改', '整改完成', '检查通过'];\n      const targetAction = actions[stepIndex];\n      \n      return this.issue.history && this.issue.history.some(h => h.action === targetAction);\n    },\n\n    getStatusText(status) {\n      const statusMap = {\n        'pending': '待整改',\n        'in_progress': '整改中',\n        'pending_review': '待检查',\n        'approved': '检查通过',\n        'overdue': '逾期',\n        'rejected': '已驳回',\n        // 添加更多可能的状态值\n        'open': '待处理',\n        'assigned': '已分配',\n        'resolved': '已解决',\n        'reviewing': '审核中',\n        'submitted': '已提交',\n        'verified': '已验证',\n        'cancelled': '已取消',\n        'suspended': '已暂停',\n        'reopened': '重新打开',\n        'draft': '草稿',\n        'new': '新建',\n        'active': '进行中',\n        'inactive': '非活跃',\n        'expired': '已过期'\n      }\n      return statusMap[status] || (status ? `${status}(待定义)` : '待确认')\n    },\n\n    getPriorityText(priority) {\n      if (!priority || priority === 'normal') return '一般问题';\n      const priorityMap = {\n        'normal': '一般问题',\n        'urgent': '紧急问题'\n      }\n      return priorityMap[priority] || '一般问题'\n    },\n\n    // 获取问题编号显示\n    getIssueNumber() {\n      const number = this.issue?.number || this.issue?.issue_number || this.issueId;\n      \n      if (number && String(number).length > 0) {\n        const numberStr = String(number);\n        \n        // 处理 YD 开头的长编号，提取后面的简短编号\n        if (numberStr.startsWith('YD') && numberStr.length > 10) {\n          // YD202508025008002 -> 只取最后3位序号\n          const shortNumber = numberStr.slice(-3);\n          return `YD${shortNumber}`;\n        }\n        \n        // 处理其他长编号，保留前缀+后6位\n        if (numberStr.length > 8) {\n          const prefix = numberStr.substring(0, 2); // 取前2位作为前缀\n          const suffix = numberStr.slice(-6); // 取后6位\n          return `${prefix}${suffix}`;\n        }\n        \n        // 短编号直接返回\n        return numberStr;\n      }\n      \n      // 默认编号：使用时间戳生成简单编号\n      return String(Date.now()).slice(-8);\n    },\n\n    // 计算截止时间倒计时\n    getDeadlineCountdown(deadline) {\n      if (!deadline) return '';\n      \n      try {\n        // 直接使用原始日期字符串，Date构造函数会自动处理\n        const deadlineDate = new Date(deadline);\n        const currentDate = new Date();\n        \n        // 检查日期是否有效\n        if (isNaN(deadlineDate.getTime())) {\n          // 尝试替换格式再解析一次\n          const normalizedDate = new Date(deadline.replace(/-/g, '/'));\n          if (isNaN(normalizedDate.getTime())) {\n            return '';\n          }\n          deadlineDate.setTime(normalizedDate.getTime());\n        }\n        \n        // 只比较日期，忽略时间\n        const deadlineDay = new Date(deadlineDate.getFullYear(), deadlineDate.getMonth(), deadlineDate.getDate());\n        const currentDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());\n        \n        const timeDiff = deadlineDay.getTime() - currentDay.getTime();\n        const daysDiff = Math.round(timeDiff / (1000 * 60 * 60 * 24));\n        \n        if (daysDiff < 0) {\n          return `已逾期${Math.abs(daysDiff)}天`;\n        } else if (daysDiff === 0) {\n          return '今日到期';\n        } else if (daysDiff === 1) {\n          return '明日到期';\n        } else {\n          return `${daysDiff}天后到期`;\n        }\n      } catch (error) {\n        return '';\n      }\n    },\n\n    // 获取倒计时样式类\n    getCountdownClass(deadline) {\n      if (!deadline) return '';\n      \n      try {\n        // 使用与 getDeadlineCountdown 相同的逻辑\n        const deadlineDate = new Date(deadline);\n        const currentDate = new Date();\n        \n        // 检查日期是否有效\n        if (isNaN(deadlineDate.getTime())) {\n          const normalizedDate = new Date(deadline.replace(/-/g, '/'));\n          if (isNaN(normalizedDate.getTime())) {\n            return 'normal';\n          }\n          deadlineDate.setTime(normalizedDate.getTime());\n        }\n        \n        // 只比较日期，忽略时间\n        const deadlineDay = new Date(deadlineDate.getFullYear(), deadlineDate.getMonth(), deadlineDate.getDate());\n        const currentDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());\n        \n        const timeDiff = deadlineDay.getTime() - currentDay.getTime();\n        const daysDiff = Math.round(timeDiff / (1000 * 60 * 60 * 24));\n        \n        if (daysDiff < 0) {\n          return 'overdue'; // 已逾期\n        } else if (daysDiff <= 1) {\n          return 'urgent'; // 紧急（今天或明天）\n        } else if (daysDiff <= 3) {\n          return 'warning'; // 警告（3天内）\n        } else {\n          return 'normal'; // 正常\n        }\n      } catch (error) {\n        return 'normal';\n      }\n    },\n\n    // 获取按钮文字\n    getButtonText() {\n      if (!this.issue) return '操作';\n      \n      const status = this.issue.status;\n      if (this.currentUserRole === 'responsible') {\n        if (status === 'assigned' || status === 'pending') {\n          return '开始整改';\n        } else if (status === 'rejected') {\n          return '重新整改';\n        } else if (status === 'in_progress' && !this.issueSubmittedForInspection) {\n          return '继续整改';\n        }\n      } else if (this.currentUserRole === 'admin') {\n        // 管理员根据状态显示不同操作\n        if (status === 'assigned' || status === 'pending') {\n          return '更改状态';\n        } else if (status === 'in_progress') {\n          return '更改状态';\n        } else if (status === 'pending_review') {\n          return '检查问题';\n        } else if (status === 'approved') {\n          return '重新打开';\n        } else if (this.issueSubmittedForInspection) {\n          return '检查问题';\n        } else {\n          return '更改状态';\n        }\n      } else if (this.currentUserRole === 'inspector') {\n        if (this.issueSubmittedForInspection) {\n          return '检查问题';\n        } else if (status === 'pending_review') {\n          return '开始检查';\n        }\n      }\n      return '操作';\n    },\n\n    // 处理主要操作\n    handleMainAction() {\n      if (!this.issue) return;\n      \n      const status = this.issue.status;\n      if (this.currentUserRole === 'responsible') {\n        if (status === 'assigned' || status === 'pending') {\n          this.startRectification();\n        } else if (status === 'rejected') {\n          this.continueRectification(); // 被驳回后继续整改\n        } else if (status === 'in_progress') {\n          this.continueRectification();\n        }\n      } else if (this.currentUserRole === 'admin') {\n        // 管理员操作：根据状态执行不同操作\n        if (status === 'assigned' || status === 'pending') {\n          this.adminManageIssue();\n        } else if (status === 'in_progress') {\n          this.adminManageIssue(); // 整改中状态也用管理问题菜单\n        } else if (status === 'pending_review') {\n          this.adminAcceptIssue();\n        } else if (this.issueSubmittedForInspection) {\n          // 开始检查功能已移除，直接进行状态更改\n          this.changeIssueStatus();\n        } else {\n          this.adminManageIssue();\n        }\n      } else if (this.currentUserRole === 'inspector') {\n        // 开始检查功能已移除，直接进行状态更改\n        this.changeIssueStatus();\n      }\n    },\n\n    // 开始整改\n    async startRectification() {\n      try {\n        const { callCloudFunction } = require('@/utils/auth.js');\n        \n        // 更新问题状态为整改中\n        const result = await callCloudFunction('hygiene-monthly-inspection', {\n          action: 'updateMonthlyIssue',\n          data: {\n            issue_id: this.issueId,\n            status: 'in_progress',\n            action_type: 'start_rectification'\n          }\n        });\n        \n        if (result && result.success) {\n          uni.navigateTo({\n            url: `/pages/6s_pkg/rectification-submit?issueId=${this.issueId}`\n          });\n        } else {\n          throw new Error(result?.message || '开始整改失败');\n        }\n      } catch (error) {\n        uni.showToast({\n          title: error.message || '操作失败',\n          icon: 'error'\n        });\n      }\n    },\n\n    // 继续整改\n    continueRectification() {\n      uni.navigateTo({\n        url: `/pages/6s_pkg/rectification-submit?issueId=${this.issueId}`\n      });\n    },\n\n\n\n    // 管理员管理问题 - 直接进行状态操作\n    adminManageIssue() {\n      this.changeIssueStatus();\n    },\n\n    // 管理员检查进度\n    adminCheckProgress() {\n      uni.showActionSheet({\n        itemList: ['更改状态', '联系负责人'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0:\n              this.changeIssueStatus();\n              break;\n            case 1:\n              // 联系负责人功能\n              uni.showToast({\n                title: '功能开发中',\n                icon: 'none'\n              });\n              break;\n          }\n        }\n      });\n    },\n\n    // 管理员检查问题\n    adminAcceptIssue() {\n      uni.showActionSheet({\n        itemList: ['检查通过', '重新整改'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0:\n              this.approveIssue();\n              break;\n            case 1:\n              this.rejectIssue();\n              break;\n          }\n        }\n      });\n    },\n\n    // 检查通过\n    async approveIssue() {\n      uni.showModal({\n        title: '检查通过',\n        content: '确定检查通过此问题吗？',\n        success: async (res) => {\n          if (res.confirm) {\n            try {\n              const { callCloudFunction } = require('@/utils/auth.js');\n              const result = await callCloudFunction('hygiene-monthly-inspection', {\n                action: 'updateMonthlyIssue',\n                data: {\n                  issue_id: this.issueId,\n                  status: 'approved',\n                  action_type: 'approve'\n                }\n              });\n              \n              if (result && result.success) {\n                this.issue.status = 'approved';\n                this.generateUnifiedTimeline();\n                uni.showToast({\n                  title: '检查通过',\n                  icon: 'success'\n                });\n                \n                // 通知其他页面数据已更新\n                uni.$emit('monthlyIssueUpdated', {\n                  action: 'approve',\n                  issueId: this.issueId,\n                  issueData: this.issue\n                });\n              }\n            } catch (error) {\n              uni.showToast({\n                title: '操作失败',\n                icon: 'error'\n              });\n            }\n          }\n        }\n      });\n    },\n\n    // 重新整改\n    async rejectIssue() {\n      uni.showModal({\n        title: '重新整改',\n        content: '确定要求重新整改此问题吗？',\n        success: async (res) => {\n          if (res.confirm) {\n            try {\n              const { callCloudFunction } = require('@/utils/auth.js');\n              const result = await callCloudFunction('hygiene-monthly-inspection', {\n                action: 'updateMonthlyIssue',\n                data: {\n                  issue_id: this.issueId,\n                  status: 'rejected',\n                  action_type: 'reject'\n                }\n              });\n              \n              if (result && result.success) {\n                this.issue.status = 'rejected';\n                this.generateUnifiedTimeline();\n                uni.showToast({\n                  title: '已要求重新整改',\n                  icon: 'success'\n                });\n                \n                // 通知其他页面数据已更新\n                uni.$emit('monthlyIssueUpdated', {\n                  action: 'reject',\n                  issueId: this.issueId,\n                  issueData: this.issue\n                });\n              }\n            } catch (error) {\n              uni.showToast({\n                title: '操作失败',\n                icon: 'error'\n              });\n            }\n          }\n        }\n      });\n    },\n\n    // 更改问题状态\n    changeIssueStatus() {\n      // 根据当前状态和逻辑确定可选择的状态\n      let statusOptions = [];\n      \n      const currentStatus = this.issue.status;\n      const hasResponsible = this.issue.responsible || this.issue.assigned_to;\n      \n      // 如果没有负责人，只能选择分配相关的状态\n      if (!hasResponsible) {\n        statusOptions = [\n          { value: 'assigned', label: '已分配' }\n        ];\n      } else {\n        // 有负责人的情况下，根据当前状态提供合理的选项\n        if (currentStatus === 'assigned') {\n          statusOptions = [\n            { value: 'pending', label: '待整改' },\n            { value: 'in_progress', label: '整改中' },\n            { value: 'pending_review', label: '待检查' }\n          ];\n        } else if (currentStatus === 'pending') {\n          // 待整改状态，不再提供\"已分配\"选项，因为负责人已经分配了\n          statusOptions = [\n            { value: 'in_progress', label: '整改中' },\n            { value: 'pending_review', label: '待检查' }\n          ];\n        } else if (currentStatus === 'in_progress') {\n          statusOptions = [\n            { value: 'pending_review', label: '待检查' },\n            { value: 'pending', label: '待整改' } // 允许回退\n          ];\n        } else if (currentStatus === 'pending_review') {\n          statusOptions = [\n            { value: 'approved', label: '检查通过' },\n            { value: 'rejected', label: '重新整改' }\n          ];\n        } else if (currentStatus === 'approved') {\n          // 检查通过后，可以重新打开或发现新问题\n          statusOptions = [\n            { value: 'pending', label: '重新打开' },\n            { value: 'rejected', label: '发现新问题，重新整改' }\n          ];\n        } else {\n          // 其他状态提供基础选项（移除已关闭，检查通过就是自然结束）\n          statusOptions = [\n            { value: 'pending', label: '待整改' },\n            { value: 'in_progress', label: '整改中' },\n            { value: 'pending_review', label: '待检查' },\n            { value: 'approved', label: '检查通过' }\n            // 移除 'closed' 和 'completed'，统一使用 pending_review\n          ];\n        }\n      }\n      \n      uni.showActionSheet({\n        itemList: statusOptions.map(opt => opt.label),\n        success: async (res) => {\n          const selectedStatus = statusOptions[res.tapIndex];\n          if (selectedStatus && selectedStatus.value !== this.issue.status) {\n            await this.updateIssueStatus(selectedStatus.value);\n          }\n        }\n      });\n    },\n\n    // 更新问题状态\n    async updateIssueStatus(newStatus) {\n      try {\n        const { callCloudFunction } = require('@/utils/auth.js');\n        \n        // 处理特殊状态映射\n        let actualStatus = newStatus;\n        if (newStatus === 'rejected') {\n          actualStatus = 'rejected'; // 重新整改状态\n        }\n        \n        // 使用updateMonthlyIssue操作来更新状态\n        const result = await callCloudFunction('hygiene-monthly-inspection', {\n          action: 'updateMonthlyIssue',\n          data: {\n            issue_id: this.issueId,\n            status: actualStatus,\n            action_type: 'admin_update'\n          }\n        });\n        \n        if (result && result.success) {\n          this.issue.status = newStatus;\n          this.generateUnifiedTimeline();\n          uni.showToast({\n            title: '状态更新成功',\n            icon: 'success'\n          });\n          \n          // 通知其他页面数据已更新\n          uni.$emit('monthlyIssueUpdated', {\n            action: 'update_status',\n            issueId: this.issueId,\n            newStatus: newStatus,\n            issueData: this.issue\n          });\n        } else {\n          throw new Error(result?.message || result?.error || '状态更新失败');\n        }\n      } catch (error) {\n        console.error('更新问题状态失败:', error);\n        uni.showToast({\n          title: error.message || '更新失败',\n          icon: 'error'\n        });\n      }\n    },\n\n\n\n    goToRectificationPage() {\n      uni.navigateTo({\n        url: `/pages/6s_pkg/rectification-submit?issueId=${this.issueId}`\n      });\n    },\n    \n\n\n    previewImage(index) {\n      uni.previewImage({\n        urls: this.issue.images,\n        current: index\n      })\n    },\n    \n    // 预览整改照片\n    previewRectificationImage(index) {\n      const urls = this.issue.rectification_photos.map(photo => photo.url || photo);\n      uni.previewImage({\n        urls: urls,\n        current: index\n      })\n    },\n\n    // 重新打开问题\n    async reopenIssue() {\n      uni.showModal({\n        title: '确认重新打开',\n        content: '确定要重新打开这个问题吗？',\n        success: async (res) => {\n          if (res.confirm) {\n            try {\n              const { callCloudFunction } = require('@/utils/auth.js');\n              \n              const result = await callCloudFunction('hygiene-monthly-inspection', {\n                action: 'updateMonthlyIssue',\n                data: {\n                  issue_id: this.issueId,\n                  status: 'in_progress',\n                  action_type: 'reopen'\n                }\n              });\n              \n              if (result && result.success) {\n                this.issue.status = 'in_progress';\n                this.generateUnifiedTimeline();\n                uni.showToast({\n                  title: '已重新打开',\n                  icon: 'success'\n                });\n                \n                // 通知其他页面数据已更新\n                uni.$emit('monthlyIssueUpdated', {\n                  action: 'reopen',\n                  issueId: this.issueId,\n                  issueData: this.issue\n                });\n              } else {\n                throw new Error(result?.message || '重新打开失败');\n              }\n            } catch (error) {\n              uni.showToast({\n                title: error.message || '操作失败',\n                icon: 'error'\n              });\n            }\n          }\n        }\n      })\n    },\n\n    editIssue() {\n      // 编辑已发布的问题，传递问题数据到 issue-add 页面\n      const editData = {\n        title: this.issue.title,\n        description: this.issue.description,\n        location: this.issue.location,\n        deadline: this.issue.deadline,\n        responsible: this.issue.responsible,\n        responsible_id: this.issue.assigned_to || this.issue.responsible_id, // 传递负责人ID\n        priority: this.issue.priority,\n        images: this.issue.images || []\n      };\n      \n      uni.navigateTo({\n        url: `/pages/6s_pkg/issue-add?editId=${this.issueId}&editData=${encodeURIComponent(JSON.stringify(editData))}`\n      });\n    },\n\n    formatDateTime(dateInput) {\n      if (!dateInput) return '';\n      try {\n        let date;\n        if (typeof dateInput === 'string') {\n          // 处理iOS兼容性问题：将 \"YYYY-MM-DD HH:mm\" 格式转换为 \"YYYY/MM/DD HH:mm:ss\"\n          let normalizedDateStr = dateInput;\n          if (dateInput.includes('-') && dateInput.includes(' ')) {\n            // \"2025-08-16 09:57\" -> \"2025/08/16 09:57:00\"\n            normalizedDateStr = dateInput.replace(/-/g, '/');\n            if (!normalizedDateStr.includes(':00', normalizedDateStr.length - 3)) {\n              normalizedDateStr += ':00';\n            }\n          } else if (dateInput.includes('-') && !dateInput.includes('T')) {\n            // \"2025-08-16\" -> \"2025/08/16\"\n            normalizedDateStr = dateInput.replace(/-/g, '/');\n          }\n          date = new Date(normalizedDateStr);\n        } else {\n          date = dateInput;\n        }\n        \n        // 检查日期是否有效\n        if (isNaN(date.getTime())) {\n          return dateInput?.toString() || '';\n        }\n        \n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        const hours = String(date.getHours()).padStart(2, '0');\n        const minutes = String(date.getMinutes()).padStart(2, '0');\n        return `${year}-${month}-${day} ${hours}:${minutes}`;\n      } catch (error) {\n        return dateInput?.toString() || '';\n      }\n    },\n    \n    // 处理问题更新事件\n    handleIssueUpdated(eventData) {\n      console.log('issue-detail 收到更新事件:', eventData);\n      \n      // 检查是否是当前问题的更新\n      if (eventData && eventData.issueId && eventData.issueId === this.issueId) {\n        console.log('当前问题需要更新，开始刷新数据...');\n        \n        // 根据不同的操作类型进行相应的处理\n        if (eventData.action === 'submit_rectification') {\n          // 整改提交：更新状态为待检查\n          if (this.issue) {\n            this.issue.status = eventData.status || 'pending_review';\n            \n            // 添加整改提交的历史记录（如果不存在）\n            if (!this.issue.history) {\n              this.issue.history = [];\n            }\n            \n            const hasSubmitRecord = this.issue.history.some(item => \n              item.action === 'submit_rectification' || item.action === '整改完成'\n            );\n            \n            if (!hasSubmitRecord) {\n              this.issue.history.push({\n                action: 'submit_rectification',\n                description: '负责人已完成整改并提交检查',\n                time: this.formatDateTime(new Date()),\n                operator: this.issue.responsible || '负责人'\n              });\n            }\n            \n            this.checkSubmissionStatus(); // 重新检查提交状态\n            this.generateUnifiedTimeline(); // 重新生成时间轴\n            \n            // 显示成功提示\n            uni.showToast({\n              title: '整改已提交',\n              icon: 'success',\n              duration: 2000\n            });\n          }\n        } else {\n          // 其他更新：重新加载完整数据\n          this.loadIssueDetail();\n        }\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);\n\twidth: 100%;\n\toverflow-x: hidden; /* 防止水平滚动 */\n\tbox-sizing: border-box;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;\n  padding-bottom: 160rpx;\n}\n\n.content-loading,\n.content-error {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: #f0f4f8;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 999;\n}\n\n.loading-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 24rpx;\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n}\n\n.content {\n  padding: 32rpx;\n}\n\n/* 卡片通用样式 - 统一卡片样式 */\n.status-card,\n.description-card,\n.images-card,\n.rectification-card,\n.timeline-card {\n  background: #ffffff;\n  border-radius: 24rpx;\n  padding: 32rpx;\n  margin-bottom: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08), \n              0 2rpx 6rpx rgba(0, 0, 0, 0.04);\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\n  /* 性能优化 */\n  transform: translateZ(0);\n  will-change: transform;\n}\n\n/* 状态卡片 */\n.status-card {\n  padding: 0;\n  overflow: hidden;\n}\n\n/* 顶部状态栏 */\n.status-bar {\n  background: #FFFFFF;\n  padding: 24rpx 32rpx 20rpx 32rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1rpx solid #F0F0F0;\n}\n\n.issue-id {\n  font-size: 26rpx;\n  font-weight: 700;\n  color: #007AFF;\n  font-family: 'Monaco', 'Menlo', monospace;\n}\n\n.status-badge {\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  font-size: 22rpx;\n  font-weight: 600;\n  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.08);\n\n  &.status-pending {\n    background: linear-gradient(135deg, #E6F3FF, #F0F7FF);\n    color: #007AFF;\n    border: 2rpx solid rgba(0, 122, 255, 0.2);\n  }\n\n  &.status-in_progress {\n    background: linear-gradient(135deg, #FFF4E6, #FFFBF0);\n    color: #FF9500;\n    border: 2rpx solid rgba(255, 149, 0, 0.2);\n  }\n\n\n\n  &.status-pending_review {\n    background: linear-gradient(135deg, #F0EFFF, #F8F7FF);\n    color: #5856D6;\n    border: 2rpx solid rgba(88, 86, 214, 0.2);\n  }\n\n  &.status-approved {\n    background: linear-gradient(135deg, #E8F5E8, #F0FFF0);\n    color: #34C759;\n    border: 2rpx solid rgba(52, 199, 89, 0.2);\n  }\n\n  &.status-overdue {\n    background: linear-gradient(135deg, #FFE6E6, #FFF0F0);\n    color: #FF3B30;\n    border: 2rpx solid rgba(255, 59, 48, 0.2);\n  }\n\n  &.status-rejected {\n    background: linear-gradient(135deg, #F2F2F7, #F8F8F8);\n    color: #8E8E93;\n    border: 2rpx solid rgba(142, 142, 147, 0.2);\n  }\n\n  // 兼容性状态映射到8色方案\n  &.status-open {\n    background: linear-gradient(135deg, #E6F3FF, #F0F7FF);\n    color: #007AFF;\n    border: 2rpx solid rgba(0, 122, 255, 0.2);\n  }\n\n  &.status-assigned {\n    background: linear-gradient(135deg, #ECFEFF, #F0FDFF);\n    color: #0891B2;\n    border: 2rpx solid rgba(8, 145, 178, 0.2);\n  }\n\n  &.status-resolved {\n    background: linear-gradient(135deg, #DCFCE7, #F0FDF4);\n    color: #16A34A;\n    border: 2rpx solid rgba(22, 163, 74, 0.2);\n  }\n\n  &.status-reviewing {\n    background: linear-gradient(135deg, #FFF4E6, #FFFBF0);\n    color: #FF9500;\n    border: 2rpx solid rgba(255, 149, 0, 0.2);\n  }\n\n  &.status-submitted {\n    background: linear-gradient(135deg, #F0EFFF, #F8F7FF);\n    color: #5856D6;\n    border: 2rpx solid rgba(88, 86, 214, 0.2);\n  }\n\n  &.status-verified {\n    background: linear-gradient(135deg, #DCFCE7, #F0FDF4);\n    color: #16A34A;\n    border: 2rpx solid rgba(22, 163, 74, 0.2);\n  }\n\n  &.status-cancelled {\n    background: linear-gradient(135deg, #F2F2F7, #F8F8F8);\n    color: #8E8E93;\n    border: 2rpx solid rgba(142, 142, 147, 0.2);\n  }\n\n  &.status-suspended {\n    background: linear-gradient(135deg, #F2F2F7, #F8F8F8);\n    color: #8E8E93;\n    border: 2rpx solid rgba(142, 142, 147, 0.2);\n  }\n\n  &.status-reopened {\n    background: linear-gradient(135deg, #FFF4E6, #FFFBF0);\n    color: #FF9500;\n    border: 2rpx solid rgba(255, 149, 0, 0.2);\n  }\n\n  &.status-draft {\n    background: linear-gradient(135deg, #F2F2F7, #F8F8F8);\n    color: #8E8E93;\n    border: 2rpx solid rgba(142, 142, 147, 0.2);\n  }\n\n  &.status-new {\n    background: linear-gradient(135deg, #ECFEFF, #F0FDFF);\n    color: #0891B2;\n    border: 2rpx solid rgba(8, 145, 178, 0.2);\n  }\n\n  &.status-active {\n    background: linear-gradient(135deg, #FFF4E6, #FFFBF0);\n    color: #FF9500;\n    border: 2rpx solid rgba(255, 149, 0, 0.2);\n  }\n\n  &.status-inactive {\n    background: linear-gradient(135deg, #F2F2F7, #F8F8F8);\n    color: #8E8E93;\n    border: 2rpx solid rgba(142, 142, 147, 0.2);\n  }\n\n  &.status-expired {\n    background: linear-gradient(135deg, #FFE6E6, #FFF0F0);\n    color: #FF3B30;\n    border: 2rpx solid rgba(255, 59, 48, 0.2);\n  }\n}\n\n/* 问题标题 */\n.issue-title {\n  font-size: 38rpx;\n  font-weight: 700;\n  color: #1C1C1E;\n  line-height: 1.3;\n  padding: 32rpx 32rpx 24rpx 32rpx;\n}\n\n/* 关键信息网格 */\n.key-info {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 24rpx;\n  padding: 0 32rpx 24rpx 32rpx;\n}\n\n.info-item {\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n}\n\n.info-label {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  font-size: 24rpx;\n  color: #8E8E93;\n  font-weight: 500;\n  margin-bottom: 6rpx;\n}\n\n.info-value-with-badge {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n  min-height: 44rpx;\n}\n\n.main-value {\n  font-size: 28rpx;\n  color: #1C1C1E;\n  font-weight: 600;\n  line-height: 1.2;\n  display: flex;\n  align-items: center;\n}\n\n.role-badge {\n  background: #F0F9FF;\n  color: #0284C7;\n  font-size: 22rpx;\n  font-weight: 500;\n  padding: 8rpx 10rpx;\n  border-radius: 8rpx;\n  line-height: 1;\n  display: flex;\n  align-items: center;\n  height: 32rpx;\n}\n\n.countdown-badge {\n  font-size: 22rpx;\n  font-weight: 500;\n  padding: 8rpx 10rpx;\n  border-radius: 8rpx;\n  line-height: 1;\n  display: flex;\n  align-items: center;\n  height: 32rpx;\n  \n  &.overdue {\n    background: #FEF2F2;\n    color: #DC2626;\n  }\n  \n  &.urgent {\n    background: #FFF7ED;\n    color: #EA580C;\n  }\n  \n  &.warning {\n    background: #FFFBEB;\n    color: #D97706;\n  }\n  \n  &.normal {\n    background: #F0F9FF;\n    color: #0284C7;\n  }\n}\n\n/* 位置信息 */\n.location-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20rpx 32rpx 32rpx 32rpx;\n  background: #FFFFFF;\n  border-top: 1rpx solid #F0F0F0;\n}\n\n.location-section {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  font-size: 26rpx;\n  color: #007AFF;\n}\n\n.priority-badge {\n  display: flex;\n  align-items: center;\n  gap: 4rpx;\n  font-size: 22rpx;\n  font-weight: 500;\n  padding: 6rpx 10rpx;\n  border-radius: 8rpx;\n  \n  &.priority-normal {\n    background: #F8FAFC;\n    color: #64748B;\n  }\n  \n  &.priority-urgent {\n    background: #FFF0F0;\n    color: #DC2626;\n  }\n}\n\n.priority-dot {\n  width: 12rpx;\n  height: 12rpx;\n  border-radius: 50%;\n  background: currentColor;\n  opacity: 0.9;\n}\n\n/* 描述卡片 */\n.description-card {\n  padding: 32rpx;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 24rpx;\n}\n\n.description-text {\n  font-size: 28rpx;\n  color: #1C1C1E;\n  line-height: 1.6;\n}\n\n/* 图片卡片 */\n.images-card {\n  padding: 32rpx;\n}\n\n/* 整改内容卡片 */\n.rectification-card {\n  padding: 32rpx;\n}\n\n.rectification-description,\n.rectification-images,\n.rectification-time {\n  margin-bottom: 24rpx;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.rectification-label {\n  font-size: 26rpx;\n  font-weight: 600;\n  color: #16a34a;\n  margin-bottom: 12rpx;\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  \n  &::before {\n    content: '';\n    width: 6rpx;\n    height: 6rpx;\n    border-radius: 50%;\n    background: #16a34a;\n  }\n}\n\n.rectification-text {\n  font-size: 28rpx;\n  color: #1C1C1E;\n  line-height: 1.6;\n  background: rgba(255, 255, 255, 0.8);\n  padding: 20rpx;\n  border-radius: 12rpx;\n  border: 1rpx solid rgba(34, 197, 94, 0.1);\n}\n\n.rectification-time-text {\n  font-size: 28rpx;\n  color: #1C1C1E;\n  line-height: 1.6;\n  background: rgba(255, 255, 255, 0.8);\n  padding: 20rpx;\n  border-radius: 12rpx;\n  border: 1rpx solid rgba(34, 197, 94, 0.1);\n}\n\n.rectification-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: opacity 0.3s ease;\n}\n\n.images-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 16rpx;\n}\n\n.image-item {\n  aspect-ratio: 1;\n  border-radius: 12rpx;\n  overflow: hidden;\n  background: #F5F5F5;\n  transition: transform 0.2s ease;\n  cursor: pointer;\n  \n  &:active {\n    transform: scale(0.98);\n  }\n}\n\n.issue-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: opacity 0.3s ease;\n}\n\n/* 时间轴卡片 */\n.timeline-card {\n  padding: 32rpx;\n  margin-bottom: 40rpx; /* 增加与底部的间距 */\n}\n\n.unified-timeline {\n  position: relative;\n}\n\n.timeline-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 24rpx;\n  margin-bottom: 40rpx;\n  position: relative;\n  padding: 16rpx 0;\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n\n  &:not(:last-child)::after {\n    content: '';\n    position: absolute;\n    left: 31rpx;\n    top: 72rpx;\n    bottom: -56rpx;\n    width: 3rpx;\n    background: #E5E5E5;\n    border-radius: 2rpx;\n  }\n\n  &.completed::after {\n    background: linear-gradient(180deg, #34C759 0%, #30D158 100%);\n  }\n  \n  &.current::after {\n    background: linear-gradient(180deg, #FF9500 0%, #FF9F0A 100%);\n  }\n  \n  &.overdue::after {\n    background: linear-gradient(180deg, #FF3B30 0%, #FF453A 100%);\n  }\n}\n\n.timeline-dot {\n  width: 56rpx;\n  height: 56rpx;\n  border-radius: 50%;\n  background: #E5E5E5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  z-index: 1;\n  position: relative;\n  border: 4rpx solid #FFFFFF;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);\n}\n\n.timeline-item.completed .timeline-dot {\n  background: linear-gradient(135deg, #34C759 0%, #30D158 100%);\n}\n\n.timeline-item.current .timeline-dot {\n  background: linear-gradient(135deg, #FF9500 0%, #FF9F0A 100%);\n  animation: pulse 2s infinite;\n}\n\n.timeline-item.pending .timeline-dot {\n  background: linear-gradient(135deg, #E5E5E5 0%, #D1D1D6 100%);\n}\n\n.timeline-item.overdue .timeline-dot {\n  background: linear-gradient(135deg, #FF3B30 0%, #FF453A 100%);\n}\n\n.dot-placeholder {\n  width: 12rpx;\n  height: 12rpx;\n  border-radius: 50%;\n  background: #FFFFFF;\n  opacity: 0.8;\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n  50% {\n    transform: scale(1.05);\n    opacity: 0.8;\n  }\n}\n\n.timeline-content {\n  flex: 1;\n  padding: 16rpx 20rpx;\n  background: linear-gradient(135deg, rgba(248, 249, 250, 0.9), rgba(255, 255, 255, 0.8));\n  border-radius: 16rpx;\n  border: 2rpx solid rgba(240, 240, 247, 0.8);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  backdrop-filter: blur(5rpx);\n}\n\n.timeline-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.step-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #1C1C1E;\n  flex: 1;\n}\n\n.timeline-item.pending .step-title {\n  color: #8E8E93;\n}\n\n.timeline-item.completed .timeline-content {\n  background: linear-gradient(135deg, rgba(52, 199, 89, 0.1), rgba(48, 209, 88, 0.08));\n  border-color: rgba(52, 199, 89, 0.3);\n}\n\n.timeline-item.current .timeline-content {\n  background: linear-gradient(135deg, rgba(255, 149, 0, 0.1), rgba(255, 159, 10, 0.08));\n  border-color: rgba(255, 149, 0, 0.3);\n}\n\n.timeline-item.pending .timeline-content {\n  background: linear-gradient(135deg, rgba(229, 229, 229, 0.4), rgba(209, 209, 214, 0.3));\n  border-color: rgba(229, 229, 229, 0.6);\n}\n\n.timeline-item.overdue .timeline-content {\n  background: linear-gradient(135deg, rgba(255, 59, 48, 0.1), rgba(255, 69, 58, 0.08));\n  border-color: rgba(255, 59, 48, 0.3);\n}\n\n.timeline-item.overdue .step-title {\n  color: #FF3B30;\n}\n\n.timeline-item.overdue .step-desc {\n  color: #FF3B30;\n  font-weight: 500;\n}\n\n.step-time {\n  font-size: 20rpx;\n  color: #8E8E93;\n  margin-left: 16rpx;\n  flex-shrink: 0;\n  white-space: nowrap;\n}\n\n.step-desc {\n  font-size: 24rpx;\n  color: #8E8E93;\n  margin-bottom: 4rpx;\n  line-height: 1.4;\n}\n\n.timeline-item.pending .step-desc {\n  color: #C7C7CC;\n}\n\n.step-operator {\n  font-size: 20rpx;\n  color: #007AFF;\n  background: rgba(0, 122, 255, 0.1);\n  padding: 2rpx 8rpx;\n  border-radius: 6rpx;\n  display: inline-block;\n  margin-top: 4rpx;\n}\n\n\n\n/* 操作栏 */\n.action-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  padding: 24rpx 32rpx 32rpx 32rpx;\n  margin: 0rpx 32rpx 32rpx 32rpx;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20rpx);\n  border-top: 1rpx solid rgba(229, 229, 229, 0.5);\n  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);\n  border-radius: 24rpx;\n  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));\n  z-index: 999;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 16rpx;\n}\n\n.action-btn {\n  flex: 1;\n  height: 88rpx;\n  border: none;\n  border-radius: 16rpx;\n  font-size: 32rpx;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  overflow: hidden;\n\n  &.primary {\n    background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n    color: #ffffff;\n    border: 1rpx solid rgba(90, 200, 250, 0.3);\n    box-shadow: 0 12rpx 32rpx rgba(0, 122, 255, 0.4),\n                0 8rpx 16rpx rgba(0, 122, 255, 0.3),\n                0 4rpx 8rpx rgba(0, 122, 255, 0.2),\n                inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);\n  }\n\n  &.primary::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n    transition: left 0.6s;\n  }\n\n  &.primary:active::before {\n    left: 100%;\n  }\n\n  &.primary:active {\n    transform: translateY(4rpx);\n    box-shadow: 0 6rpx 16rpx rgba(0, 122, 255, 0.3),\n                0 3rpx 8rpx rgba(0, 122, 255, 0.2),\n                inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);\n  }\n\n  &.secondary {\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);\n    color: #1C1C1E;\n    border: 2rpx solid rgba(0, 122, 255, 0.25);\n    backdrop-filter: blur(15rpx);\n    box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.15),\n                0 4rpx 12rpx rgba(0, 122, 255, 0.12),\n                0 2rpx 6rpx rgba(0, 122, 255, 0.08),\n                inset 0 2rpx 4rpx rgba(255, 255, 255, 0.8);\n  }\n\n  &.full {\n    flex: none;\n    width: 100%;\n  }\n}\n\n/* 底部安全间距 */\n.bottom-safe-area {\n  height: 40rpx;\n}\n\n/* H5平台适配 - 仅优化图片显示大小 */\n/* #ifdef H5 */\n@media screen and (min-width: 768px) {\n  .image-item {\n    max-width: 200rpx;\n    max-height: 200rpx;\n  }\n  \n  .issue-image {\n    max-width: 200rpx;\n    max-height: 200rpx;\n  }\n}\n/* #endif */\n\n\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./issue-detail.vue?vue&type=style&index=0&id=43779f7e&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./issue-detail.vue?vue&type=style&index=0&id=43779f7e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775845069\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}