require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/feedback_pkg/examine","uni_modules/uni-icons/components/uni-icons/uni-icons"],{"10ef":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){}));var n=function(){var e=this.$createElement;this._self._c},i=[]},1866:function(e,t,a){"use strict";var n=a("bfb5"),i=a.n(n);i.a},"18ad":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("3e22"),i={name:"UniIcons",emits:["click"],props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16},customPrefix:{type:String,default:""},fontFamily:{type:String,default:""}},data:function(){return{icons:n.fontData}},computed:{unicode:function(){var e=this,t=this.icons.find((function(t){return t.font_class===e.type}));return t?t.unicode:""},iconSize:function(){return function(e){return"number"===typeof e||/^[0-9]*$/g.test(e)?e+"px":e}(this.size)},styleObj:function(){return""!==this.fontFamily?"color: ".concat(this.color,"; font-size: ").concat(this.iconSize,"; font-family: ").concat(this.fontFamily,";"):"color: ".concat(this.color,"; font-size: ").concat(this.iconSize,";")}},methods:{_onClick:function(){this.$emit("click")}}};t.default=i},2929:function(e,t,a){"use strict";a.r(t);var n=a("ec09"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(s);t["default"]=i.a},"2e0c":function(e,t,a){},"3d06":function(e,t,a){"use strict";var n=a("2e0c"),i=a.n(n);i.a},"5abf":function(e,t,a){"use strict";(function(e,t){var n=a("47a9");a("357b"),a("861b");n(a("3240"));var i=n(a("a720"));e.__webpack_require_UNI_MP_PLUGIN__=a,t(i.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},"6c67":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return s})),a.d(t,"a",(function(){return n}));var n={uniIcons:function(){return Promise.resolve().then(a.bind(null,"6ddf"))}},i=function(){var e=this,t=e.$createElement,a=(e._self._c,e.hasInitialData?e.__map(e.effectiveSteps,(function(t,a){var n=e.__get_orig(t),i=e.getStepStatus(a),s=e.getStepStatus(a),r=e.getStepStatus(a),o=e.getStepStatus(a),l=e.effectiveSteps.length,u=e.getStepStatus(a),c="pending"!==u?"completed"===e.getStepStatus(a)||"approved"===e.getStepStatus(a):null,d="pending"!==u?e.getStepStatus(a):null,p="pending"!==u?e.getStepStatus(a):null,m="pending"!==u?e.getStepStatus(a):null,f="pending"!==u?e.getRoleTitle(e.getRoleByStep(a)):null,g="pending"!==u?e.getStepStatusText(a):null,h="pending"!==u?e.getStepTime(a):null,D="pending"!==u&&h?e.formatTime(e.getStepTime(a)):null,v="pending"===u&&a===e.currentStep&&a<=2?e.getRoleTitle(e.getRoleByStep(a)):null,b="pending"!==u||a!==e.currentStep||a<=2||3!==a?null:e.getStepStatusText(a),w="pending"!==u||a!==e.currentStep||a<=2||3===a||4!==a?null:e.getStepStatusText(a);return{$orig:n,m0:i,m1:s,m2:r,m3:o,g0:l,m4:u,m5:c,m6:d,m7:p,m8:m,m9:f,m10:g,m11:h,m12:D,m13:v,m14:b,m15:w}})):null),n=e.hasInitialData?e.uniIDHasRole("supervisor")||e.uniIDHasRole("admin"):null,i=e.hasInitialData&&n?e.getSupervisorStatus():null,s=e.hasInitialData&&n?e.getSupervisorStatus():null,r=e.hasInitialData&&n?e.getSupervisorStatus():null,o=e.hasInitialData&&n?e.getSupervisorStatus():null,l=e.hasInitialData&&n?e.getSupervisorStatus():null,u=e.hasInitialData&&n?e.getSupervisorStatus():null,c=e.hasInitialData&&n?e.getSupervisorStatus():null,d=e.hasInitialData&&n?e.getSupervisorStatus():null,p=e.hasInitialData&&n?e.getSupervisorStatus():null,m=e.hasInitialData&&n?e.getSupervisorStatusText():null,f=e.hasInitialData&&n?e.getSupervisorStatus():null,g=e.hasInitialData&&n&&"pending"===f?(e.tempReasonInput.supervisor||"").length:null,h=e.hasInitialData&&n&&"pending"!==f?e.getSupervisorReasonText():null,D=e.hasInitialData?e.uniIDHasRole("PM")||e.uniIDHasRole("admin"):null,v=e.hasInitialData&&D?e.getPMStatus():null,b=e.hasInitialData&&D?e.getPMStatus():null,w=e.hasInitialData&&D?e.getPMStatus():null,S=e.hasInitialData&&D?e.getPMStatus():null,_=e.hasInitialData&&D?e.getPMStatus():null,k=e.hasInitialData&&D?e.getPMStatus():null,T=e.hasInitialData&&D?e.getPMStatus():null,I=e.hasInitialData&&D?e.getPMStatusText():null,x=e.hasInitialData&&D?e.getPMStatus():null,R=e.hasInitialData&&D&&"pending"===x?(e.tempReasonInput.pm||"").length:null,y=e.hasInitialData&&D&&"pending"!==x?e.getPMReasonText():null,M=e.hasInitialData?e.uniIDHasRole("GM")||e.uniIDHasRole("admin"):null,U=e.hasInitialData&&M?e.getGMStatus():null,C=e.hasInitialData&&M?e.getGMStatus():null,P=e.hasInitialData&&M?e.getGMStatus():null,j=e.hasInitialData&&M?e.getGMStatus():null,O=e.hasInitialData&&M?e.getGMStatus():null,$=e.hasInitialData&&M?e.getGMStatus():null,E=e.hasInitialData&&M?e.getGMStatus():null,H=e.hasInitialData&&M?e.getGMStatusText():null,G=e.hasInitialData&&M?e.getGMStatus():null,B=e.hasInitialData&&M&&"pending"===G?(e.tempReasonInput.gm||"").length:null,L=e.hasInitialData&&M&&"pending"!==G?e.getGMReasonText():null,F=e.hasInitialData?(e.uniIDHasRole("GM")||e.uniIDHasRole("admin"))&&"gm_approved_pending_assign"===e.formData.workflowStatus&&!e.formData.responsibleUserId:null,A=e.hasInitialData&&F?e.responsibleUsers.length:null,q=e.hasInitialData&&F?e.getResponsiblePickerDisplayText():null,z=e.hasInitialData&&F?e.assignReason.length:null,N=e.hasInitialData&&e.formData.responsibleUserId&&("assigned_to_responsible"===e.formData.workflowStatus||"completed_by_responsible"===e.formData.workflowStatus||"final_completed"===e.formData.workflowStatus)?e.getResponsibleUserName():null,W=e.hasInitialData&&e.formData.responsibleUserId&&("assigned_to_responsible"===e.formData.workflowStatus||"completed_by_responsible"===e.formData.workflowStatus||"final_completed"===e.formData.workflowStatus)&&e.formData.assignedTime?e.formatTime(e.formData.assignedTime):null,J=e.hasInitialData&&e.formData.responsibleUserId&&("assigned_to_responsible"===e.formData.workflowStatus||"completed_by_responsible"===e.formData.workflowStatus||"final_completed"===e.formData.workflowStatus)&&e.formData.completedByResponsibleTime?e.formatTime(e.formData.completedByResponsibleTime):null,V=e.hasInitialData&&e.formData.responsibleUserId&&("assigned_to_responsible"===e.formData.workflowStatus||"completed_by_responsible"===e.formData.workflowStatus||"final_completed"===e.formData.workflowStatus)&&e.formData.rejectReason&&e.formData.rejectedTime?e.formatTime(e.formData.rejectedTime):null,Y=!e.hasInitialData||!e.formData.responsibleUserId||"assigned_to_responsible"!==e.formData.workflowStatus&&"completed_by_responsible"!==e.formData.workflowStatus&&"final_completed"!==e.formData.workflowStatus||"completed_by_responsible"!==e.formData.workflowStatus&&"final_completed"!==e.formData.workflowStatus?null:e.formData.responsibleCompletionEvidence&&e.formData.responsibleCompletionEvidence.length>0,K=e.hasInitialData&&e.formData.responsibleUserId&&("assigned_to_responsible"===e.formData.workflowStatus||"completed_by_responsible"===e.formData.workflowStatus||"final_completed"===e.formData.workflowStatus)?"assigned_to_responsible"===e.formData.workflowStatus&&(e.isCurrentUserResponsible()||e.uniIDHasRole("admin")):null,Q=e.hasInitialData&&e.formData.responsibleUserId&&("assigned_to_responsible"===e.formData.workflowStatus||"completed_by_responsible"===e.formData.workflowStatus||"final_completed"===e.formData.workflowStatus)&&K?e.completionDescription.length:null,X=e.hasInitialData&&e.formData.responsibleUserId&&("assigned_to_responsible"===e.formData.workflowStatus||"completed_by_responsible"===e.formData.workflowStatus||"final_completed"===e.formData.workflowStatus)&&K?e.completionEvidence.length:null,Z=e.hasInitialData?(e.uniIDHasRole("GM")||e.uniIDHasRole("admin"))&&"completed_by_responsible"===e.formData.workflowStatus:null,ee=e.hasInitialData&&Z?e.getResponsibleUserName():null,te=e.hasInitialData&&Z&&e.formData.completedByResponsibleTime?e.formatTime(e.formData.completedByResponsibleTime):null,ae=e.hasInitialData&&Z?e.finalConfirmReason.length:null,ne=e.hasInitialData&&Z?e.rejectReason.length:null,ie=e.hasInitialData&&"final_completed"===e.formData.workflowStatus&&e.formData.finalCompletedTime?e.formatTime(e.formData.finalCompletedTime):null,se=e.hasInitialData&&"final_completed"===e.formData.workflowStatus?e.getResponsibleUserName():null,re=e.hasInitialData?e.getSupervisorStatus():null,oe=e.hasInitialData?e.getSupervisorStatus():null,le=e.hasInitialData?e.getSupervisorStatus():null,ue=e.hasInitialData?e.getSupervisorStatus():null,ce=e.hasInitialData?e.getSupervisorStatusText():null,de=e.hasInitialData?e.getSupervisorStatus():null,pe=e.hasInitialData?e.getSupervisorStatus():null,me=e.hasInitialData?e.getSupervisorStatus():null,fe=e.hasInitialData?e.getSupervisorStatus():null,ge=e.hasInitialData?e.getSupervisorReasonText():null,he=e.hasInitialData?e.getPMStatus():null,De=e.hasInitialData?e.getPMStatus():null,ve=e.hasInitialData?e.getPMStatus():null,be=e.hasInitialData?e.getPMStatus():null,we=e.hasInitialData?e.getPMStatusText():null,Se=e.hasInitialData?e.getPMStatus():null,_e=e.hasInitialData?e.getPMStatus():null,ke=e.hasInitialData?e.getPMStatus():null,Te=e.hasInitialData?e.getPMReasonText():null,Ie=e.hasInitialData?e.getGMStatus():null,xe=e.hasInitialData?e.getGMStatus():null,Re=e.hasInitialData?e.getGMStatus():null,ye=e.hasInitialData?e.getGMStatus():null,Me=e.hasInitialData?e.getGMStatusText():null,Ue=e.hasInitialData?e.getGMStatus():null,Ce=e.hasInitialData?e.getGMStatus():null,Pe=e.hasInitialData?e.getGMStatus():null,je=e.hasInitialData?e.getGMReasonText():null,Oe=e.hasInitialData?e.uniIDHasRole("GM")||e.uniIDHasRole("admin"):null;e.$mp.data=Object.assign({},{$root:{l0:a,m16:n,m17:i,m18:s,m19:r,m20:o,m21:l,m22:u,m23:c,m24:d,m25:p,m26:m,m27:f,g1:g,m28:h,m29:D,m30:v,m31:b,m32:w,m33:S,m34:_,m35:k,m36:T,m37:I,m38:x,g2:R,m39:y,m40:M,m41:U,m42:C,m43:P,m44:j,m45:O,m46:$,m47:E,m48:H,m49:G,g3:B,m50:L,m51:F,g4:A,m52:q,g5:z,m53:N,m54:W,m55:J,m56:V,g6:Y,m57:K,g7:Q,g8:X,m58:Z,m59:ee,m60:te,g9:ae,g10:ne,m61:ie,m62:se,m63:re,m64:oe,m65:le,m66:ue,m67:ce,m68:de,m69:pe,m70:me,m71:fe,m72:ge,m73:he,m74:De,m75:ve,m76:be,m77:we,m78:Se,m79:_e,m80:ke,m81:Te,m82:Ie,m83:xe,m84:Re,m85:ye,m86:Me,m87:Ue,m88:Ce,m89:Pe,m90:je,m91:Oe}})},s=[]},"6ddf":function(e,t,a){"use strict";a.r(t);var n=a("10ef"),i=a("a0a5");for(var s in i)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(s);a("3d06");var r=a("828b"),o=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);t["default"]=o.exports},a0a5:function(e,t,a){"use strict";a.r(t);var n=a("18ad"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(s);t["default"]=i.a},a720:function(e,t,a){"use strict";a.r(t);var n=a("6c67"),i=a("2929");for(var s in i)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(s);a("1866");var r=a("828b"),o=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);t["default"]=o.exports},bfb5:function(e,t,a){},ec09:function(e,t,a){"use strict";(function(e,n){var i=a("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=i(a("7eb4")),r=i(a("3b2d")),o=i(a("7ca3")),l=i(a("ee10"));function u(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function c(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?u(Object(a),!0).forEach((function(t){(0,o.default)(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function d(e){if("function"!==typeof WeakMap)return null;var t=new WeakMap,a=new WeakMap;return(d=function(e){return e?a:t})(e)}e.database();var p={components:{uniIcons:function(){return Promise.resolve().then((function(){return function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==(0,r.default)(e)&&"function"!==typeof e)return{default:e};var a=d(t);if(a&&a.has(e))return a.get(e);var n={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&Object.prototype.hasOwnProperty.call(e,s)){var o=i?Object.getOwnPropertyDescriptor(e,s):null;o&&(o.get||o.set)?Object.defineProperty(n,s,o):n[s]=e[s]}return n.default=e,a&&a.set(e,n),n}(a("6ddf"))}))}},data:function(){return{formDataId:"",currentStep:0,_isLoading:!1,hasInitialData:!1,isPageVisible:!0,selectedOptions:{supervisor:null,PM:null,GM:null},tempReasonInput:{supervisor:"",pm:"",gm:""},formData:{name:"",project:"",description:"",images:[],createTime:null,createUserId:"",isAdopted:!1,isCompleted:!1,workflowStatus:"pending_supervisor",meetingRequired:!1,terminatedBy:"",terminatedTime:null,lastUpdateTime:null,remark:"",updateTrigger:"",responsibleUserId:null,assignedTime:null,assignReason:"",responsibleCompletionDescription:null,responsibleCompletionEvidence:[],completedByResponsibleTime:null,finalCompletedTime:null,rejectReason:"",rejectedTime:null,actionHistory:[]},responsibleUsers:[],selectedResponsibleIndex:0,selectedResponsibleUser:null,assignReason:"",completionDescription:"",completionEvidence:[],finalConfirmReason:"",rejectReason:"",steps:[{text:"主管审核"},{text:"副厂长审核"},{text:"厂长审核"},{text:"指派负责人"},{text:"执行结果"}]}},computed:{effectiveSteps:function(){return this.steps},isProcessTerminated:function(){return"terminated"===this.formData.workflowStatus},isProcessComplete:function(){return"final_completed"===this.formData.workflowStatus},isLoading:function(){return this._isLoading}},onLoad:function(e){var t=this;return(0,l.default)(s.default.mark((function a(){return s.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!e||!e.id){a.next=5;break}return t.formDataId=e.id,a.next=4,t.loadResponsibleUsers();case 4:t.getDetail(e.id);case 5:case"end":return a.stop()}}),a)})))()},methods:{getRoleByStep:function(e){return["supervisor","PM","GM","assign","final"][e]},getRoleTitle:function(e){return{supervisor:"主管",PM:"副厂长",GM:"厂长",assign:"指派负责人",final:"执行结果"}[e]},getSupervisorStatus:function(){var e=this.formData.workflowStatus,t=this.formData.terminatedBy;if("pending_supervisor"===e)return"pending";if("approved_supervisor"===e||"pending_pm"===e||"approved_pm"===e||"pending_gm"===e||"gm_approved_pending_assign"===e||"assigned_to_responsible"===e||"completed_by_responsible"===e||"final_completed"===e)return this.formData.meetingRequired?"meeting":"approved";if("terminated"===e){if("supervisor"===t)return"rejected";if("PM"===t||"GM"===t)return this.formData.meetingRequired?"meeting":"approved"}return"pending"},getPMStatus:function(){var e=this.formData.workflowStatus,t=this.formData.terminatedBy;return"pending_supervisor"===e||"terminated"===e&&"supervisor"===t?"not_executed":"approved_supervisor"===e||"pending_pm"===e?"pending":"approved_pm"===e||"pending_gm"===e||"gm_approved_pending_assign"===e||"assigned_to_responsible"===e||"completed_by_responsible"===e||"final_completed"===e?"approved":"terminated"===e&&"PM"===t?"rejected":"terminated"===e&&"GM"===t?"approved":"not_executed"},getGMStatus:function(){var e=this.formData.workflowStatus,t=this.formData.terminatedBy;return"pending_supervisor"===e||"approved_supervisor"===e||"pending_pm"===e?"not_executed":"terminated"!==e||"supervisor"!==t&&"PM"!==t?"approved_pm"===e||"pending_gm"===e?"pending":"gm_approved_pending_assign"===e||"assigned_to_responsible"===e||"completed_by_responsible"===e||"final_completed"===e?"approved":"terminated"===e&&"GM"===t?"rejected":"not_executed":"not_executed"},getStepStatus:function(e){var t=["supervisor","PM","GM","assign","final"][e];if("assign"===t)return"gm_approved_pending_assign"===this.formData.workflowStatus?"pending":"assigned_to_responsible"===this.formData.workflowStatus||"completed_by_responsible"===this.formData.workflowStatus||"final_completed"===this.formData.workflowStatus?"completed":(this.formData.workflowStatus,"not_executed");if("final"===t)return"final_completed"===this.formData.workflowStatus?"completed":"completed_by_responsible"===this.formData.workflowStatus?"pending":"terminated"===this.formData.workflowStatus?"rejected":"not_executed";var a=this["get".concat(t.charAt(0).toUpperCase()+t.slice(1),"Status")]();return"approved"===a?"completed":"meeting"===a?"meeting":"rejected"===a?"rejected":"not_executed"===a?"not_executed":"pending"},getSupervisorStatusText:function(){var e=this.getSupervisorStatus();return"pending"===e?"待审核":"approved"===e?"已同意":"rejected"===e?"已拒绝":"meeting"===e?"例会讨论":"not_executed"===e?"未执行":"待审核"},getPMStatusText:function(){var e=this.getPMStatus();return"pending"===e?"待审核":"approved"===e?"已同意":"rejected"===e?"已拒绝":"not_executed"===e?"未执行":"待审核"},getGMStatusText:function(){var e=this.getGMStatus();return"pending"===e?"待审核":"approved"===e?"已同意":"rejected"===e?"已拒绝":"not_executed"===e?"未执行":"待审核"},getStepStatusText:function(e){var t=["supervisor","PM","GM","assign","final"][e];return"assign"===t?"gm_approved_pending_assign"===this.formData.workflowStatus?"待指派负责人":"assigned_to_responsible"===this.formData.workflowStatus?"已指派，待完成":"completed_by_responsible"===this.formData.workflowStatus?"负责人已完成":"final_completed"===this.formData.workflowStatus?"已指派完成":(this.formData.workflowStatus,"未执行"):"final"===t?"final_completed"===this.formData.workflowStatus?"流程完成":"terminated"===this.formData.workflowStatus?"流程终止":"assigned_to_responsible"===this.formData.workflowStatus?"负责人执行中":"completed_by_responsible"===this.formData.workflowStatus?"待厂长确认":"gm_approved_pending_assign"===this.formData.workflowStatus?"待指派负责人":"未执行":this["get".concat(t.charAt(0).toUpperCase()+t.slice(1),"StatusText")]()},getStepTime:function(e){var t=["supervisor","PM","GM","assign","final"][e];if("assign"===t)return this.formData.assignedTime;if("final"===t)return this.formData.finalCompletedTime||this.formData.completedByResponsibleTime||this.formData.terminatedTime;if(this.formData.actionHistory&&this.formData.actionHistory.length>0){var a={supervisor:["supervisor_approve","supervisor_reject","supervisor_meeting"],PM:["pm_approve","pm_reject"],GM:["gm_approve","gm_reject"]}[t]||[],n=this.formData.actionHistory.filter((function(e){return a.includes(e.action)})).sort((function(e,t){return t.timestamp-e.timestamp}))[0];if(n)return n.timestamp}return null},formatTime:function(e){if(!e)return"";var t=new Date(e);return"".concat(t.getFullYear(),"-").concat(String(t.getMonth()+1).padStart(2,"0"),"-").concat(String(t.getDate()).padStart(2,"0")," ").concat(String(t.getHours()).padStart(2,"0"),":").concat(String(t.getMinutes()).padStart(2,"0"))},getSupervisorReasonText:function(){if(this.formData.actionHistory&&this.formData.actionHistory.length>0){var e=this.formData.actionHistory.filter((function(e){return["supervisor_approve","supervisor_reject","supervisor_meeting"].includes(e.action)})).sort((function(e,t){return t.timestamp-e.timestamp}))[0];if(e&&e.reason)return e.reason}return"无"},getPMReasonText:function(){if(this.formData.actionHistory&&this.formData.actionHistory.length>0){var e=this.formData.actionHistory.filter((function(e){return["pm_approve","pm_reject"].includes(e.action)})).sort((function(e,t){return t.timestamp-e.timestamp}))[0];if(e&&e.reason)return e.reason}return"无"},getGMReasonText:function(){if(this.formData.actionHistory&&this.formData.actionHistory.length>0){var e=this.formData.actionHistory.filter((function(e){return["gm_approve","gm_reject"].includes(e.action)})).sort((function(e,t){return t.timestamp-e.timestamp}))[0];if(e&&e.reason)return e.reason}return"无"},getDetail:function(t){var a=this;return(0,l.default)(s.default.mark((function i(){var r,o,l,u,c,d,p;return s.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(i.prev=0,!a._isLoading){i.next=3;break}return i.abrupt("return");case 3:a._isLoading=!0,n.showLoading({title:"加载中...",mask:!0}),r="approval_".concat(t);try{o=n.getStorageSync(r),o&&Date.now()-o.timestamp<6e4&&(Object.assign(a.formData,o.data),o.data&&o.data.description&&(a.formData.description=o.data.description),a.updateSelectedOptions(),a.updateCurrentStep(),a.hasInitialData=!0)}catch(s){}return i.next=9,e.callFunction({name:"feedback-workflow",data:{action:"get_detail",id:t}});case 9:if(l=i.sent,0!==l.result.code||!l.result.data){i.next=19;break}u=l.result.data,a.formData={name:u.name,project:u.project,description:u.description||"",images:u.images||[],createTime:u.createTime,createUserId:u.createUserId,isAdopted:u.isAdopted,isCompleted:u.isCompleted,workflowStatus:u.workflowStatus||"pending_supervisor",meetingRequired:u.meetingRequired||!1,terminatedBy:u.terminatedBy||"",terminatedTime:u.terminatedTime,lastUpdateTime:u.lastUpdateTime,remark:u.remark||"",updateTrigger:u.updateTrigger||"",responsibleUserId:u.responsibleUserId,assignedTime:u.assignedTime,assignReason:u.assignReason||"",responsibleCompletionDescription:u.responsibleCompletionDescription,responsibleCompletionEvidence:u.responsibleCompletionEvidence||[],completedByResponsibleTime:u.completedByResponsibleTime,finalCompletedTime:u.finalCompletedTime,rejectReason:u.rejectReason||"",rejectedTime:u.rejectedTime,actionHistory:u.actionHistory||[]},a.hasInitialData=!0,a.updateSelectedOptions(),a.updateCurrentStep(),a.$nextTick((function(){try{var e="approval_".concat(a.formDataId);n.setStorageSync(e,{timestamp:Date.now(),data:a.formData})}catch(s){}})),i.next=23;break;case 19:if(404!==(null===(c=l.result)||void 0===c?void 0:c.code)||"RECORD_DELETED"!==(null===(d=l.result)||void 0===d?void 0:d.errorType)){i.next=22;break}return a.handleRecordDeleted(),i.abrupt("return");case 22:throw new Error((null===(p=l.result)||void 0===p?void 0:p.message)||"获取详情失败");case 23:i.next=31;break;case 25:if(i.prev=25,i.t0=i["catch"](0),!i.t0.message||!i.t0.message.includes("记录不存在")){i.next=30;break}return a.handleRecordDeleted(),i.abrupt("return");case 30:n.showModal({content:i.t0.message||"请求服务失败",showCancel:!1});case 31:return i.prev=31,n.hideLoading(),a._isLoading=!1,i.finish(31);case 35:case"end":return i.stop()}}),i,null,[[0,25,31,35]])})))()},updateCurrentStep:function(){var e=this.formData.workflowStatus;this.formData.terminatedBy;this.currentStep="pending_supervisor"===e?0:"approved_supervisor"===e||"pending_pm"===e?1:"approved_pm"===e||"pending_gm"===e?2:"gm_approved_pending_assign"===e?3:"assigned_to_responsible"===e||"completed_by_responsible"===e?4:"final_completed"===e||"terminated"===e?-1:0},updateSelectedOptions:function(){var e=this.getSupervisorStatus();"approved"===e?this.$set(this.selectedOptions,"supervisor",!0):"rejected"===e?this.$set(this.selectedOptions,"supervisor",!1):"meeting"===e?this.$set(this.selectedOptions,"supervisor","meeting"):this.$set(this.selectedOptions,"supervisor",null);var t=this.getPMStatus();"approved"===t?this.$set(this.selectedOptions,"PM",!0):"rejected"===t?this.$set(this.selectedOptions,"PM",!1):this.$set(this.selectedOptions,"PM",null);var a=this.getGMStatus();"approved"===a?this.$set(this.selectedOptions,"GM",!0):"rejected"===a?this.$set(this.selectedOptions,"GM",!1):this.$set(this.selectedOptions,"GM",null)},handleApproval:function(t,i){var r=this;return(0,l.default)(s.default.mark((function o(){var l,u,c,d,p,m,f,g,h,D,v,b,w,S,_;return s.default.wrap((function(s){while(1)switch(s.prev=s.next){case 0:if(!r._isProcessing){s.next=2;break}return s.abrupt("return");case 2:if(r._isProcessing=!0,r.$set(r.selectedOptions,t,i),s.prev=4,l=["supervisor","PM","GM"],u=l.indexOf(t),u===r.currentStep){s.next=12;break}return n.showModal({title:"提示",content:"当前不是".concat(t,"审核步骤，请按流程顺序进行审核"),showCancel:!1,confirmText:"确定"}),r._isProcessing=!1,r.$set(r.selectedOptions,t,null),s.abrupt("return");case 12:return!0===i?c="同意":!1===i?c="不同意":"meeting"===i&&(c="提交例会讨论"),s.next=15,n.showModal({title:"确认操作",content:"确定要".concat(c,"吗？"),confirmText:"确定",cancelText:"取消"});case 15:if(d=s.sent,d.confirm){s.next=20;break}return r._isProcessing=!1,r.$set(r.selectedOptions,t,null),s.abrupt("return");case 20:if(n.showLoading({title:"处理中...",mask:!0}),"meeting"!==i){s.next=29;break}return m=r.tempReasonInput.supervisor,m&&""!==m.trim()||(m="需要例会讨论"),s.next=26,e.callFunction({name:"feedback-workflow",data:{action:"supervisor_meeting",id:r.formDataId,reason:m}});case 26:p=s.sent,s.next=36;break;case 29:return"supervisor"===t?f=i?"supervisor_approve":"supervisor_reject":"PM"===t?f=i?"pm_approve":"pm_reject":"GM"===t&&(f=!1===i?"gm_reject":"gm_approve"),g={supervisor:"supervisor",PM:"pm",GM:"gm"},h=r.tempReasonInput[g[t]]||"",h&&""!==h.trim()||(h=!0===i?"同意":"不同意"),s.next=35,e.callFunction({name:"feedback-workflow",data:{action:f,id:r.formDataId,reason:h}});case 35:p=s.sent;case 36:if(!p.result||0!==p.result.code){s.next=60;break}if(p.result.data&&p.result.data.newStatus&&(r.formData.workflowStatus=p.result.data.newStatus,p.result.data.terminatedBy&&r.$set(r.formData,"terminatedBy",p.result.data.terminatedBy)),"meeting"===i&&r.$set(r.formData,"meetingRequired",!0),p.result.data&&(D=p.result.data,D.actionHistory&&(r.formData.actionHistory=D.actionHistory),D.lastUpdateTime&&(r.formData.lastUpdateTime=D.lastUpdateTime),void 0!==D.remark&&(r.formData.remark=D.remark),void 0!==D.isAdopted&&(r.formData.isAdopted=D.isAdopted),void 0!==D.isCompleted&&(r.formData.isCompleted=D.isCompleted),void 0!==D.meetingRequired&&(r.formData.meetingRequired=D.meetingRequired),void 0!==D.responsibleUserId&&(r.formData.responsibleUserId=D.responsibleUserId),void 0!==D.assignedTime&&(r.formData.assignedTime=D.assignedTime),void 0!==D.assignReason&&(r.formData.assignReason=D.assignReason),void 0!==D.completedByResponsibleTime&&(r.formData.completedByResponsibleTime=D.completedByResponsibleTime),void 0!==D.responsibleCompletionDescription&&(r.formData.responsibleCompletionDescription=D.responsibleCompletionDescription),void 0!==D.responsibleCompletionEvidence&&(r.formData.responsibleCompletionEvidence=D.responsibleCompletionEvidence),void 0!==D.finalCompletedTime&&(r.formData.finalCompletedTime=D.finalCompletedTime),void 0!==D.terminatedBy&&(r.formData.terminatedBy=D.terminatedBy),void 0!==D.terminatedTime&&(r.formData.terminatedTime=D.terminatedTime)),v={supervisor:"supervisor",PM:"pm",GM:"gm"},v[t]&&(r.tempReasonInput[v[t]]=""),r.updateCurrentStep(),r.updateSelectedOptions(),r.$forceUpdate(),r.$nextTick((function(){r.$forceUpdate()})),r.$nextTick((function(){try{var e="approval_".concat(r.formDataId);n.setStorageSync(e,{timestamp:Date.now(),data:r.formData})}catch(t){}})),n.hideLoading(),b=a("7e11").default,!b){s.next=53;break}return s.next=52,b.forceRefresh();case 52:setTimeout((function(){b.forceSyncBadge()}),300);case 53:n.$emit("refresh-todo-list"),n.$emit("feedback-updated"),n.$emit("ucenter-need-refresh",{id:r.formDataId}),setTimeout((function(){b&&b.updateTodoCountImmediately()}),500),n.showToast({title:c+"成功",icon:"success",duration:1e3}),s.next=64;break;case 60:if(404!==(null===(w=p.result)||void 0===w?void 0:w.code)||"RECORD_DELETED"!==(null===(S=p.result)||void 0===S?void 0:S.errorType)){s.next=63;break}return r.handleRecordDeleted(),s.abrupt("return");case 63:throw new Error((null===(_=p.result)||void 0===_?void 0:_.message)||"操作失败");case 64:s.next=73;break;case 66:if(s.prev=66,s.t0=s["catch"](4),n.hideLoading(),!s.t0.message||!s.t0.message.includes("记录不存在")){s.next=72;break}return r.handleRecordDeleted(),s.abrupt("return");case 72:n.showModal({content:s.t0.message||"请求服务失败",showCancel:!1});case 73:return s.prev=73,r._isProcessing=!1,s.finish(73);case 76:case"end":return s.stop()}}),o,null,[[4,66,73,76]])})))()},handleReset:function(){var t=this;return(0,l.default)(s.default.mark((function i(){var r,o,l,u,d,p,m,f;return s.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!t._isProcessing){i.next=2;break}return i.abrupt("return");case 2:return t._isProcessing=!0,i.prev=3,i.next=6,n.showModal({title:"确认重置",content:"确定要重置所有审批状态吗？这将清除所有审批记录！",confirmText:"确定",cancelText:"取消"});case 6:if(r=i.sent,r.confirm){i.next=10;break}return t._isProcessing=!1,i.abrupt("return");case 10:return n.showLoading({title:"处理中...",mask:!0}),i.next=13,e.callFunction({name:"feedback-workflow",data:{action:"reset",id:t.formDataId}});case 13:if(o=i.sent,!o.result||0!==o.result.code){i.next=32;break}if(o.result.data&&(l=o.result.data,u={name:t.formData.name,project:t.formData.project,description:t.formData.description,images:t.formData.images||[],createTime:t.formData.createTime,createUserId:t.formData.createUserId},t.formData=c(c({},u),{},{workflowStatus:l.workflowStatus||"pending_supervisor",isAdopted:l.isAdopted||!1,isCompleted:l.isCompleted||!1,meetingRequired:l.meetingRequired||!1,lastUpdateTime:l.lastUpdateTime||Date.now(),remark:l.remark||"",actionHistory:l.actionHistory||[],terminatedBy:"",terminatedTime:null,responsibleUserId:null,assignedTime:null,assignReason:"",responsibleCompletionDescription:null,responsibleCompletionEvidence:[],completedByResponsibleTime:null,finalCompletedTime:null,rejectReason:"",rejectedTime:null,updateTrigger:""})),t.selectedOptions={supervisor:null,PM:null,GM:null},t.updateCurrentStep(),t.$forceUpdate(),t.$nextTick((function(){t.$forceUpdate()})),t.$nextTick((function(){try{var e="approval_".concat(t.formDataId);n.setStorageSync(e,{timestamp:Date.now(),data:t.formData})}catch(a){}})),n.hideLoading(),d=a("7e11").default,!d){i.next=27;break}return i.next=26,d.forceRefresh();case 26:setTimeout((function(){d.forceSyncBadge()}),300);case 27:n.$emit("refresh-todo-list"),n.$emit("feedback-updated"),n.showToast({title:"重置成功",icon:"success",duration:1e3}),i.next=36;break;case 32:if(404!==(null===(p=o.result)||void 0===p?void 0:p.code)||"RECORD_DELETED"!==(null===(m=o.result)||void 0===m?void 0:m.errorType)){i.next=35;break}return t.handleRecordDeleted(),i.abrupt("return");case 35:throw new Error((null===(f=o.result)||void 0===f?void 0:f.message)||"重置失败");case 36:i.next=45;break;case 38:if(i.prev=38,i.t0=i["catch"](3),n.hideLoading(),!i.t0.message||!i.t0.message.includes("记录不存在")){i.next=44;break}return t.handleRecordDeleted(),i.abrupt("return");case 44:n.showModal({content:i.t0.message||"重置失败",showCancel:!1});case 45:return i.prev=45,t._isProcessing=!1,i.finish(45);case 48:case"end":return i.stop()}}),i,null,[[3,38,45,48]])})))()},getFinalStatusText:function(){return"final_completed"===this.formData.workflowStatus?"审核通过":"terminated"===this.formData.workflowStatus?"审核未通过":"审核中"},loadResponsibleUsers:function(){var t=this;return(0,l.default)(s.default.mark((function a(){var n;return s.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,e.callFunction({name:"feedback-workflow",data:{action:"get_responsible_users"}});case 3:n=a.sent,n.result&&0===n.result.code&&(t.responsibleUsers=n.result.data||[],t.responsibleUsers.length>0&&0===t.selectedResponsibleIndex&&(t.selectedResponsibleUser=null)),a.next=10;break;case 7:a.prev=7,a.t0=a["catch"](0),console.warn("加载负责人列表失败:",a.t0);case 10:case"end":return a.stop()}}),a,null,[[0,7]])})))()},getResponsiblePickerDisplayText:function(){return 0===this.responsibleUsers.length?"加载负责人列表中...":this.selectedResponsibleUser?this.selectedResponsibleUser.nickname||this.selectedResponsibleUser.username||"未知用户":"请选择负责人"},onResponsiblePickerChange:function(e){this.selectedResponsibleIndex=e.target.value,this.selectedResponsibleUser=this.responsibleUsers[this.selectedResponsibleIndex]},handleAssignResponsible:function(){var t=this;return(0,l.default)(s.default.mark((function a(){var i,r,o,l,u;return s.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t.selectedResponsibleUser){a.next=3;break}return n.showToast({title:"请选择负责人",icon:"none"}),a.abrupt("return");case 3:return a.prev=3,n.showLoading({title:"指派中...",mask:!0}),i=t.selectedResponsibleUser._id,r=t.assignReason||"无",a.next=9,e.callFunction({name:"feedback-workflow",data:{action:"gm_assign",id:t.formDataId,responsibleUserId:i,reason:r}});case 9:if(o=a.sent,!o.result||0!==o.result.code){a.next=26;break}o.result.data&&(l=o.result.data,l.actionHistory&&(t.formData.actionHistory=l.actionHistory),l.lastUpdateTime&&(t.formData.lastUpdateTime=l.lastUpdateTime),void 0!==l.remark&&(t.formData.remark=l.remark),void 0!==l.workflowStatus&&(t.formData.workflowStatus=l.workflowStatus),void 0!==l.responsibleUserId&&(t.formData.responsibleUserId=l.responsibleUserId),void 0!==l.assignedTime&&(t.formData.assignedTime=l.assignedTime),void 0!==l.assignReason&&(t.formData.assignReason=l.assignReason)),t.updateCurrentStep(),t.$forceUpdate(),t.$nextTick((function(){t.$forceUpdate()})),t.assignReason="",t.selectedResponsibleUser=null,t.selectedResponsibleIndex=0,n.hideLoading(),n.showToast({title:"指派成功",icon:"success"}),n.$emit("task-assigned",{taskId:t.formDataId,responsibleUserId:i,assignedTime:Date.now()}),n.$emit("feedback-updated"),n.$emit("ucenter-need-refresh",{id:t.formDataId}),t.getDetail(t.formDataId),a.next=27;break;case 26:throw new Error((null===(u=o.result)||void 0===u?void 0:u.message)||"指派失败");case 27:a.next=33;break;case 29:a.prev=29,a.t0=a["catch"](3),n.hideLoading(),n.showModal({content:a.t0.message||"指派失败",showCancel:!1});case 33:case"end":return a.stop()}}),a,null,[[3,29]])})))()},isCurrentUserResponsible:function(){var e,t=null===(e=n.getStorageSync("uni-id-token-payload"))||void 0===e?void 0:e.uid;return this.formData.responsibleUserId===t},getResponsibleUserName:function(){var e=this;if(!this.formData.responsibleUserId)return"";var t=this.responsibleUsers.find((function(t){return t._id===e.formData.responsibleUserId}));return t?t.nickname||t.username||"未知用户":this.formData.responsibleUserName||"负责人"},uploadEvidence:function(){var t=this;return(0,l.default)(s.default.mark((function a(){var i,r,o,l,u,c,d,p,m,f,g,h;return s.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,n.chooseImage({count:3-t.completionEvidence.length,sizeType:["compressed"],sourceType:["camera","album"]});case 3:i=a.sent,r=0;case 5:if(!(r<i.tempFilePaths.length)){a.next=23;break}return o=i.tempFilePaths[r],n.showLoading({title:"上传中 (".concat(r+1,"/").concat(i.tempFilePaths.length,")"),mask:!0}),l=new Date,u=l.getFullYear(),c=String(l.getMonth()+1).padStart(2,"0"),d=String(l.getDate()).padStart(2,"0"),p="".concat(u).concat(c).concat(d),m=o.includes(".")?o.substring(o.lastIndexOf(".")):".jpg",f="".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)).concat(m),g="feedback-evidence/".concat(p,"/").concat(f),a.next=18,e.uploadFile({filePath:o,cloudPath:g,cloudPathAsRealPath:!0});case 18:h=a.sent,h.fileID&&t.completionEvidence.push(h.fileID);case 20:r++,a.next=5;break;case 23:n.showToast({title:"上传成功",icon:"success"}),a.next=29;break;case 26:a.prev=26,a.t0=a["catch"](0),n.showToast({title:"上传失败",icon:"none"});case 29:return a.prev=29,n.hideLoading(),a.finish(29);case 32:case"end":return a.stop()}}),a,null,[[0,26,29,32]])})))()},removeImage:function(t){var a=this;n.showModal({title:"确认删除",content:"确定要删除这张图片吗？",confirmColor:"#f44336",success:function(){var i=(0,l.default)(s.default.mark((function i(r){var o;return s.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!r.confirm){i.next=18;break}if(o=a.completionEvidence[t],n.showLoading({title:"删除中...",mask:!0}),i.prev=3,!o||"string"!==typeof o||!o.startsWith("cloud://")&&!o.startsWith("https://")){i.next=7;break}return i.next=7,e.callFunction({name:"delete-file",data:{fileList:[o]}});case 7:a.completionEvidence.splice(t,1),n.showToast({title:"删除成功",icon:"success",duration:1e3}),i.next=15;break;case 11:i.prev=11,i.t0=i["catch"](3),a.completionEvidence.splice(t,1),n.showToast({title:"已从列表移除",icon:"success",duration:1e3});case 15:return i.prev=15,n.hideLoading(),i.finish(15);case 18:case"end":return i.stop()}}),i,null,[[3,11,15,18]])})));return function(e){return i.apply(this,arguments)}}()})},previewImage:function(e){n.previewImage({urls:[e],current:e})},handleResponsibleComplete:function(){var t=this;return(0,l.default)(s.default.mark((function a(){var i,r,o;return s.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t.completionDescription){a.next=3;break}return n.showToast({title:"请输入完成情况说明",icon:"none"}),a.abrupt("return");case 3:return a.prev=3,n.showLoading({title:"提交中...",mask:!0}),a.next=7,e.callFunction({name:"feedback-workflow",data:{action:"responsible_complete",id:t.formDataId,completionDescription:t.completionDescription,completionEvidence:t.completionEvidence}});case 7:if(i=a.sent,!i.result||0!==i.result.code){a.next=22;break}i.result.data&&(r=i.result.data,r.actionHistory&&(t.formData.actionHistory=r.actionHistory),r.lastUpdateTime&&(t.formData.lastUpdateTime=r.lastUpdateTime),void 0!==r.remark&&(t.formData.remark=r.remark),void 0!==r.workflowStatus&&(t.formData.workflowStatus=r.workflowStatus),void 0!==r.completedByResponsibleTime&&(t.formData.completedByResponsibleTime=r.completedByResponsibleTime),void 0!==r.responsibleCompletionDescription&&(t.formData.responsibleCompletionDescription=r.responsibleCompletionDescription),void 0!==r.responsibleCompletionEvidence&&(t.formData.responsibleCompletionEvidence=r.responsibleCompletionEvidence)),t.updateCurrentStep(),t.$forceUpdate(),t.$nextTick((function(){t.$forceUpdate()})),n.hideLoading(),n.showToast({title:"提交成功",icon:"success"}),n.$emit("feedback-updated"),n.$emit("ucenter-need-refresh",{id:t.formDataId}),t.completionDescription="",t.completionEvidence=[],t.getDetail(t.formDataId),a.next=23;break;case 22:throw new Error((null===(o=i.result)||void 0===o?void 0:o.message)||"提交失败");case 23:a.next=29;break;case 25:a.prev=25,a.t0=a["catch"](3),n.hideLoading(),n.showModal({content:a.t0.message||"提交失败",showCancel:!1});case 29:case"end":return a.stop()}}),a,null,[[3,25]])})))()},handleFinalConfirm:function(){var t=this;return(0,l.default)(s.default.mark((function a(){var i,r,o;return s.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t.finalConfirmReason){a.next=3;break}return n.showToast({title:"请输入确认意见",icon:"none"}),a.abrupt("return");case 3:return a.prev=3,n.showLoading({title:"确认中...",mask:!0}),a.next=7,e.callFunction({name:"feedback-workflow",data:{action:"gm_final_confirm",id:t.formDataId,reason:t.finalConfirmReason}});case 7:if(i=a.sent,!i.result||0!==i.result.code){a.next=20;break}i.result.data&&(r=i.result.data,r.actionHistory&&(t.formData.actionHistory=r.actionHistory),r.lastUpdateTime&&(t.formData.lastUpdateTime=r.lastUpdateTime),void 0!==r.remark&&(t.formData.remark=r.remark),void 0!==r.workflowStatus&&(t.formData.workflowStatus=r.workflowStatus),void 0!==r.isCompleted&&(t.formData.isCompleted=r.isCompleted),void 0!==r.finalCompletedTime&&(t.formData.finalCompletedTime=r.finalCompletedTime)),t.updateCurrentStep(),t.$forceUpdate(),t.$nextTick((function(){t.$forceUpdate()})),n.hideLoading(),n.showToast({title:"确认完成",icon:"success"}),n.$emit("feedback-updated"),n.$emit("ucenter-need-refresh",{id:t.formDataId}),t.getDetail(t.formDataId),a.next=21;break;case 20:throw new Error((null===(o=i.result)||void 0===o?void 0:o.message)||"确认失败");case 21:a.next=27;break;case 23:a.prev=23,a.t0=a["catch"](3),n.hideLoading(),n.showModal({content:a.t0.message||"确认失败",showCancel:!1});case 27:case"end":return a.stop()}}),a,null,[[3,23]])})))()},handleRejectCompletion:function(){var t=this;return(0,l.default)(s.default.mark((function a(){var i,r,o,l;return s.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t.rejectReason){a.next=3;break}return n.showToast({title:"请输入退回理由",icon:"none"}),a.abrupt("return");case 3:return a.prev=3,a.next=6,n.showModal({title:"确认退回",content:"确定要退回重做吗？\n退回理由：".concat(t.rejectReason),confirmText:"确定",cancelText:"取消"});case 6:if(i=a.sent,i.confirm){a.next=9;break}return a.abrupt("return");case 9:return n.showLoading({title:"处理中...",mask:!0}),a.next=12,e.callFunction({name:"feedback-workflow",data:{action:"updateWorkflowStatus",id:t.formDataId,workflowStatus:"assigned_to_responsible",rejectReason:t.rejectReason}});case 12:if(r=a.sent,!r.result||0!==r.result.code){a.next=29;break}r.result.data&&(o=r.result.data,o.actionHistory&&(t.formData.actionHistory=o.actionHistory),o.lastUpdateTime&&(t.formData.lastUpdateTime=o.lastUpdateTime),void 0!==o.remark&&(t.formData.remark=o.remark),void 0!==o.workflowStatus&&(t.formData.workflowStatus=o.workflowStatus),void 0!==o.rejectReason&&(t.formData.rejectReason=o.rejectReason),void 0!==o.rejectedTime&&(t.formData.rejectedTime=o.rejectedTime)),t.formData.responsibleCompletionDescription=null,t.formData.responsibleCompletionEvidence=[],t.formData.completedByResponsibleTime=null,t.updateCurrentStep(),t.$forceUpdate(),t.$nextTick((function(){t.$forceUpdate()})),t.rejectReason="",n.hideLoading(),n.showToast({title:"已退回重做",icon:"success"}),n.$emit("feedback-updated"),n.$emit("ucenter-need-refresh",{id:t.formDataId}),t.getDetail(t.formDataId),a.next=30;break;case 29:throw new Error((null===(l=r.result)||void 0===l?void 0:l.message)||"退回失败");case 30:a.next=36;break;case 32:a.prev=32,a.t0=a["catch"](3),n.hideLoading(),n.showModal({content:a.t0.message||"退回失败",showCancel:!1});case 36:case"end":return a.stop()}}),a,null,[[3,32]])})))()},silentRefreshData:function(){var t=this;return(0,l.default)(s.default.mark((function a(){var n,i;return s.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,!t._isLoading){a.next=3;break}return a.abrupt("return");case 3:return a.next=5,e.callFunction({name:"feedback-workflow",data:{action:"get_detail",id:t.formDataId}});case 5:n=a.sent,0===n.result.code&&n.result.data&&(i=n.result.data,t.formData={name:i.name,project:i.project,description:i.description||"",images:i.images||[],createTime:i.createTime,createUserId:i.createUserId,isAdopted:i.isAdopted,isCompleted:i.isCompleted,workflowStatus:i.workflowStatus||"pending_supervisor",meetingRequired:i.meetingRequired||!1,terminatedBy:i.terminatedBy||"",terminatedTime:i.terminatedTime,lastUpdateTime:i.lastUpdateTime,remark:i.remark||"",updateTrigger:i.updateTrigger||"",responsibleUserId:i.responsibleUserId,assignedTime:i.assignedTime,assignReason:i.assignReason||"",responsibleCompletionDescription:i.responsibleCompletionDescription,responsibleCompletionEvidence:i.responsibleCompletionEvidence||[],completedByResponsibleTime:i.completedByResponsibleTime,finalCompletedTime:i.finalCompletedTime,rejectReason:i.rejectReason||"",rejectedTime:i.rejectedTime,actionHistory:i.actionHistory||[]},t.updateSelectedOptions(),t.updateCurrentStep(),t.$forceUpdate()),a.next=12;break;case 9:a.prev=9,a.t0=a["catch"](0),console.log("静默刷新失败:",a.t0);case 12:case"end":return a.stop()}}),a,null,[[0,9]])})))()},shouldRefreshOnCrossDeviceUpdate:function(e){if(!this.formDataId)return!1;if(!this.isPageVisible)return!1;var t=Date.now()-this.lastRefreshTime;return!(t<1e4)&&(!(!e.feedbackIds||!e.feedbackIds.includes(this.formDataId))||!(!e.updateTypes||!e.updateTypes.includes("workflow_status_changed")))},handleRecordDeleted:function(){var e=this;n.hideLoading(),this._isProcessing=!1,n.showModal({title:"记录已删除",content:"您正在操作的反馈记录已被其他用户删除，将返回上一页面。",showCancel:!1,confirmText:"确定",success:function(){try{var t="approval_".concat(e.formDataId);n.removeStorageSync(t)}catch(s){}n.$emit("refresh-todo-list"),n.$emit("feedback-updated"),n.$emit("ucenter-need-refresh");var i=a("7e11").default;i&&i.forceRefresh(),n.navigateBack({delta:1,fail:function(){n.switchTab({url:"/pages/index/index"})}})}})}},created:function(){var e=this;n.$on("cross-device-update-detected",(function(t){if(t.silent){var a=e.shouldRefreshOnCrossDeviceUpdate(t);a&&(console.log("审核页面收到跨设备更新通知，静默刷新数据"),e.silentRefreshData())}}))},beforeDestroy:function(){n.$off("cross-device-update-detected")}};t.default=p}).call(this,a("861b")["uniCloud"],a("df3c")["default"])}},[["5abf","common/runtime","common/vendor"]]]);