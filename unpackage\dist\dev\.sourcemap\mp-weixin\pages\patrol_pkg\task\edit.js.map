{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/patrol_pkg/task/edit.vue?cda3", "webpack:///D:/Xwzc/pages/patrol_pkg/task/edit.vue?f6f3", "webpack:///D:/Xwzc/pages/patrol_pkg/task/edit.vue?8b21", "webpack:///D:/Xwzc/pages/patrol_pkg/task/edit.vue?ce42", "uni-app:///pages/patrol_pkg/task/edit.vue", "webpack:///D:/Xwzc/pages/patrol_pkg/task/edit.vue?ab72", "webpack:///D:/Xwzc/pages/patrol_pkg/task/edit.vue?4d04"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loading", "taskId", "formData", "name", "area", "date", "shift_id", "route_id", "user_id", "remark", "status", "shiftOptions", "shiftIndex", "selectedShift", "routeOptions", "routeIndex", "<PERSON><PERSON><PERSON><PERSON>", "userOptions", "nickname", "userIndex", "selected<PERSON>ser", "statusOptions", "value", "label", "statusIndex", "roleNameMap", "timeZoneIndicator", "STATUS", "NOT_STARTED", "IN_PROGRESS", "COMPLETED", "EXPIRED", "CANCELLED", "originalStatus", "predictedStatus", "statusManually<PERSON><PERSON><PERSON>", "processedRounds", "pointsRefreshed", "searchUserName", "filteredUsers", "showUserSelect", "onLoad", "options", "uni", "title", "icon", "setTimeout", "Promise", "console", "watch", "handler", "deep", "immediate", "methods", "updatePredictedStatus", "calculateTaskStatus", "getStatusIcon", "getStatusText", "getStatusExplanation", "hour", "minute", "getCurrentUserId", "loadTaskData", "currentUserId", "PatrolApi", "action", "id", "userId", "result", "taskData", "enabled_rounds", "rounds_completion", "loadShifts", "params", "with_rounds", "with_detail", "shifts", "shift", "across_day", "rounds", "round", "time", "start_time", "end_time", "day_offset", "duration", "systemInfo", "platform", "isMobile", "loadRoutes", "with_points", "include_point_details", "detailRes", "route", "pointsDetail", "loadUsers", "userid", "pageSize", "field", "filter", "map", "_id", "avatar", "role", "wx_openid", "identities", "loadRoles", "roleMap", "loadShiftDetail", "shiftData", "enabledRounds", "setInitialSelections", "onDateChange", "onShiftChange", "onRouteChange", "res", "routeData", "onUserChange", "onStatusChange", "getRoleText", "getSingleRoleText", "checkDuplicateTask", "queryParams", "patrolDate", "filters", "response", "duplicateTasks", "task", "shiftName", "routeName", "userName", "content", "showCancel", "confirmText", "cancelText", "success", "resolve", "submitForm", "canProceed", "totalPoints", "completedPoints", "abnormalCount", "newOverallStats", "total_points", "completed_points", "missed_points", "completion_rate", "abnormal_count", "last_checkin_time", "taskStatus", "startTime", "originalTask", "dayOffset", "endDateTime", "endTime", "cleanDate", "requestData", "patrol_date", "rounds_detail", "points", "p", "qrcode_enabled", "qrcode_required", "qrcode_version", "overall_stats", "shift_detail", "route_detail", "point_id", "order", "location", "range", "uniCloud", "validateForm", "goBack", "processRoundsData", "taskDate", "originalRound", "originalStats", "roundHours", "roundMinutes", "shiftStartHours", "shiftStartMinutes", "日期", "时间", "偏移", "开始时间", "结束时间", "originalPoint", "checkin_time", "checkin_location", "abnormal", "abnormal_reason", "roundStatus", "check_time", "stats", "refreshRoutePointsData", "force_refresh", "filterUserOptions", "user", "selectUser", "hideUserSelect"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpEA;AAAA;AAAA;AAAA;AAA8lB,CAAgB,wnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;AC+QlnB;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QAAAR;MAAA;MACAS;MACAC;MACAC;QAAAX;MAAA;MACAY;MACAC;MACAC;QAAAC;MAAA;MACAC;MACAC;MACAC,gBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA,KACAC;gBAAA;gBAAA;cAAA;cACA;cAAA;cAAA;YAAA;cAEAC;gBACAC;gBACAC;cACA;cACAC;gBACAH;cACA;cAAA;YAAA;cAIA;;cAEA;cAAA;cAEAA;gBAAAC;cAAA;;cAEA;cAAA;cAAA,OACAG,aACA,oBACA,oBACA,mBACA,mBACA;cAAA,CACA;YAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAC;cACAL;gBACAC;gBACAC;cACA;YAAA;cAAA;cAEAF;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EACAM;IACA;MACA;IACA;IACApC;MACAqC;QACA;MACA;MACAC;IACA;IACA3B;MACA;QACA;QACA;MACA;IACA;IACA;IACAP;MACAiC;QACA;MACA;MACAE;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MAEA;QACA;QACAN;QAEA;QACA;QAEA;QAEA;QAEAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAEA;UACAA;UACA;QACA;UACAA;UACA;QACA;MACA;QACAA;QACA;MACA;IACA;IAEAO;MACA;MACA;QACA;QACA;UAAA;QAAA;QACA;UACAP;UACA;QACA;;QAEA;QACA;UAAA;QAAA;QACA;UACAA;UACA;QACA;;QAEA;QACA;UAAA;QAAA;QACA;UACAA;UACA;QACA;;QAEA;QACA;UAAA;QAAA;QACA;UACAA;UACA;QACA;;QAEA;QACAA;QACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;QACA;QACA;;QAEA;QACAA;QACAA;QACAA;;QAEA;QACA;UACA;UACA;YACA;YACA;YACA;YACA;YACA;;YAEA;YACA;YACA;YACA;UACA;;UAEA;UACA;UACA;UACA;;UAEA;UACA;UAEAA;UACAA;;UAEA;UACA;YACAA;YACA;UACA;QACA;QAEA;UACAA;UACA;QACA;QAEAA;QACA;MACA;QACAA;QACA;MACA;IACA;IAEAQ;MACA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;IAEAC;MACA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;IAEAC;MACA;MACA;QAAAC;QAAAC;MAAA;MAEA;QACA;UACA;YACA;UACA;UACA;QACA;UACA;YACA;UACA;UACA;QACA;UACA;MAAA;IAEA;IAEAC;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAnB;kBACAC;gBACA;gBAEAmB;gBAAA;gBAAA,OAEAC;kBACA7D;kBACA8D;kBACAlE;oBACAmE;oBACAC;kBACA;gBACA;cAAA;gBAPAC;gBAAA,MASAA;kBAAA;kBAAA;gBAAA;gBACAC;gBAEA;kBACAlE;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACA4D;kBACAC;gBACA;gBAEA;gBAEA;kBAAA;gBAAA;gBACA;gBAEAzB;kBACA;gBACA;gBAEAA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAH;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEAF;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA6B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAR;kBACA7D;kBACA8D;kBACAlE;oBACA0E;sBACA/D;sBACAgE;sBACAC;oBACA;kBACA;gBACA;cAAA;gBAVAP;gBAYA;kBACAQ;kBACA;oBACA;sBAAAzE;oBAAA;kBACA;oBACA;oBACAyE;sBAAA,uCACAC;wBACA1E;wBACA2E;wBACAC,sDACAF;0BAAA;wBAAA;0BACA;0BAEA;4BACAG;4BACAC;4BACA9E;4BACA+E;4BACAC;4BACAC;4BACAC;4BACA3E;0BACA;wBACA,KACA;sBAAA;oBAAA,CACA;;oBAEA;oBACA4E;oBACAC;oBACAC,kDAEA;oBACAZ;sBACA;sBACA;;sBAEA;sBACA;sBACA;oBACA;oBAEA;kBACA;kBAEA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAjC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA4C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAzB;kBACA7D;kBACA8D;kBACAlE;oBACA0E;sBACA/D;sBACAgF;sBACAf;sBACAgB;oBACA;kBACA;gBACA;cAAA;gBAXAvB;gBAAA,MAaAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;kBAAAjE;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEA4C;kBAAA;oBAAA;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;4BAAA,OACAiB;8BACA7D;8BACA8D;8BACAlE;gCACA0E;kCACAlE;kCACAmF;kCACAf;gCACA;8BACA;4BACA;0BAAA;4BAVAiB;4BAAA,MAYAA;8BAAA;8BAAA;4BAAA;4BAAA,kEAEAC;8BACAC;4BAAA;0BAAA;4BAAA,kCAGAD;0BAAA;0BAAA;4BAAA;wBAAA;sBAAA;oBAAA;kBAAA,CACA;kBAAA;oBAAA;kBAAA;gBAAA;cAAA;gBApBA;cAAA;gBAuBA;kBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAlD;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAkD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAhC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA,OAGAC;kBACA7D;kBACA8D;kBACAlE;oBACAiG;oBACAvB;sBACAwB;sBACAC;oBACA;kBACA;gBACA;cAAA;gBAVA9B;gBAYA;kBACA,sCACA+B;oBACA;oBACA;sBACA;oBACA;sBACA;oBACA;kBACA,GACAA;oBACA;oBACA;oBACA;kBACA,GACAC;oBAAA;sBACAC;sBACAnF;sBACAoF;sBACAC;sBACAC;sBACAC;oBACA;kBAAA;kBAEA;oBACA;sBAAAvF;oBAAA;kBACA;kBAEA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAyB;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA6D;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA3C;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA,OAGAC;kBACA7D;kBACA8D;kBACAlE;oBACAiG;kBACA;gBACA;cAAA;gBANA5B;gBAQA;kBACAuC;kBACAvC;oBACAuC;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAhE;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA+D;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA5C;kBACA7D;kBACA8D;kBACAlE;oBACA0E;sBACAnE;oBACA;kBACA;gBACA;cAAA;gBARA8D;gBAUA;kBACAyC;kBACA;oBACAC;sBAAA;oBAAA;oBACA,uDACAD;sBACA/B;sBACAC;wBACA;wBAEA;0BACAC;0BACA7E;0BACA8E;0BACAC;0BACAC;0BACAC;0BACAC;0BACA3E;wBACA;sBACA;oBAAA,EACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAsC;gBACAL;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAkE;MAAA;MACA;QACA;UAAA;QAAA;QACA;UACA;UACA;UAEA,qDACAlC;YACAC;YACAC,sDACAF;cAAA;YAAA;cACA;cAEA;gBACAG;gBACA7E;gBACA8E;gBACAC;gBACAC;gBACAC;gBACAC;gBACA3E;cACA;YACA,KACA;UAAA,EACA;QACA;MACA;MAEA;QACA;UAAA;QAAA;QACA;UACA;UACA;UAEA,qDACAmF;YACAC;UAAA,EACA;QACA;MACA;MAEA;QACA;UAAA;QAAA;QACA;UACA;UACA;QACA;MACA;IACA;IACAkB;MACA;MACA;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MAEA;QACA;QAEA;UACA;YAAA;UAAA;UACA,qDACApC;YACAC;YACAC;cACA;cAEA;gBACAC;gBACA7E;gBACA8E;gBACAC;gBACAC;gBACAC;gBACAC;gBACA3E;cACA;YACA;UAAA,EACA;;UAEA;UACA;YACAiC;cACAC;cACAC;cACAwC;YACA;UACA;QACA;UACA;QACA;MACA;QACA;QACA;MACA;IACA;IACA6B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACArB;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGA7B;kBACA7D;kBACA8D;kBACAlE;oBACA0E;sBACAlE;sBACAmF;sBACAf;sBACAgB;oBACA;kBACA;gBACA;cAAA;gBAXAwB;gBAaA;kBACAC;kBACA,uDACAA;oBACAtB;kBAAA,EACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA9C;cAAA;gBAAA;gBAAA;cAAA;gBAGA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAqE;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MAEA;QACA;QACA;UAAA;QAAA;MACA;MAEA;IACA;IACAC;MACA;QACA;MACA;MAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;kBACAC;kBAAA;kBACAC;oBACArH;kBACA;kBACA4D;kBACA7D;gBACA;gBAAA;gBAAA,OAEA0D;cAAA;gBAAA6D;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAC;kBAAA;gBAAA;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACAC;gBACAC;gBACAC;gBACAC;gBAAA,mCAEA;kBACAvF;oBACAC;oBACAuF;oBACAC;oBACAC;oBACAC;oBACAC;sBACAC;oBACA;kBACA;gBACA;cAAA;gBAAA,mCAIA;cAAA;gBAAA;gBAAA;gBAEAxF;gBACAL;kBACAC;kBACAC;gBACA;gBAAA,mCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA4F;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAKA;cAAA;gBAAAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA3D;gBAEA;gBACA;;gBAEA;gBACA4D;kBAAA;gBAAA;gBACAC;kBACA;oBAAA;kBAAA;gBACA;gBACAC;kBACA;oBAAA;kBAAA;gBACA;gBAEAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBAIA;kBACAC;kBACArG;gBACA;kBACAsG;kBAEA;oBACAA;kBACA;kBAEAD;kBACArG;gBACA;gBAEAgF;gBACAC;gBAEAsB;gBAAA;gBAAA;gBAAA,OAEAvF;kBACA7D;kBACA8D;kBACAlE;oBAAAmE;kBAAA;gBACA;cAAA;gBAJAE;gBAMA;kBACAmF;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAvG;cAAA;gBAIA;kBACA;oBACAsG;kBACA;kBAEA;oBAAA,wBACA;oBAAA,yBACA;oBAEAE;oBACA;sBACAA;oBACA;oBAEAC;oBACAC;kBACA;gBACA;;gBAEA;gBACAC;gBAEAC;kBACA1F;kBACA/D;kBACAC;kBACAyJ;kBACAvJ;kBACAC;kBACAC;kBACAE;kBACAD;kBAEAqJ;oBAAA,uCACA9E;sBACA+E;wBAAA;wBAAA,uCACAC;0BACAC;4BAAA;0BAAA;0BACAC;4BAAA;0BAAA;0BACAC;4BAAA;0BAAA;wBAAA;sBAAA,CACA;oBAAA;kBAAA,CACA;kBACA7F;oBAAA;kBAAA;kBACA8F;kBACAC;oBACAlK;oBACA+E;oBACAC;oBACAL;kBACA;kBACAwF;oBACAnK;oBACA4J;sBAAA;wBACAQ;wBACApK;wBACAqK;wBACAC;wBACAC;wBACAT;wBACAC;wBACAC;sBACA;oBAAA;kBACA;gBACA;gBAEA;gBACA;;gBAEA;gBAAA;gBAAA,OAEAQ;kBACAxK;kBACAJ;oBACAkE;oBACAQ;kBACA;gBACA;kBAAA;gBAAA;cAAA;gBANAoD;gBAAA,MAQAA;kBAAA;kBAAA;gBAAA;gBACAlF;kBACAC;kBACAC;gBACA;gBAEAF;gBAEAG;kBACAH;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAK;gBACAL;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA+H;MACA;QACAjI;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;IACA;IACAgI;MACAlI;IACA;IACAmI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA,mCACA;cAAA;gBAGAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEAhB;gBAEAR;gBAAA;gBAAA;gBAAA,OAEAvF;kBACA7D;kBACA8D;kBACAlE;oBAAAmE;kBAAA;gBACA;cAAA;gBAJAE;gBAMA;kBACAmF;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAvG;cAAA;gBAAA,mCAGA;kBAAA;kBACA;kBACA;;kBAEA;kBACA;kBACA;oBACAgI;sBAAA;oBAAA;oBACA;sBACAC;oBACA;kBACA;;kBAEA;kBACA;kBACA;kBAEA;oBACA;sBAAA;sBAAAC;sBAAAC;oBACA;sBAAA;sBAAAC;sBAAAC;oBAEA;oBACA;oBAEA;sBACA7B;oBACA;kBACA;kBAEA;kBACA;kBAEAxG;oBACAsI;oBACAC;oBACAC;oBACAC;oBACAC;kBACA;kBAEA;oBACA;oBACA;sBACAC;wBAAA;sBAAA;oBACA;oBAEA;sBACApB;sBACApK;sBACAqK;sBACA9J;sBACA+J;sBACAC;sBACAT;sBACAC;sBACAC;oBAAA,GACAwB;sBACAC;sBACAC;sBACAC;sBACAC;oBACA;kBAEA;kBAEA;kBACA;oBACAC;kBACA;oBACA;oBACA;sBACAA;oBACA;sBACAA;oBACA;kBACA;;kBAEA;kBACA;oBAAA;kBAAA;kBACA;oBACAjD;oBACAC;oBACAC;oBACAC;oBACAC;sBAAA;oBAAA;oBACAC;kBACA;kBAEA;oBACApE;oBACA7E;oBACA8E;oBACAgH;oBACA/G;oBACAC;oBACAC;oBACAC;oBACA3E;oBACAoE;oBACAiF;oBACAmC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAxJ;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAKAF;kBACAC;gBACA;;gBAEA;gBAAA;gBAAA,OACAoB;kBACA7D;kBACA8D;kBACAlE;oBACA0E;sBACAlE;sBACAmF;sBACAf;sBACAgB;sBACAyG;oBACA;kBACA;gBACA;cAAA;gBAZAjF;gBAAA,MAcAA;kBAAA;kBAAA;gBAAA;gBACAC,sBACA;gBACA,wDACA;kBACAtB;gBAAA,EACA;;gBAEA;gBACA;;gBAEA;gBACAnD;gBACAA;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAG;gBACAL;gBACAA;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAwJ;MACA;MACA;QACA;QACA;MACA;QACA;UAAA,OACAC,qEACA;QAAA;QAAA,CACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;QACA;QACA;UAAA;QAAA;QACA;UACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7hDA;AAAA;AAAA;AAAA;AAAyoC,CAAgB,+mCAAG,EAAC,C;;;;;;;;;;;ACA7pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/patrol_pkg/task/edit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/patrol_pkg/task/edit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./edit.vue?vue&type=template&id=36b84017&\"\nvar renderjs\nimport script from \"./edit.vue?vue&type=script&lang=js&\"\nexport * from \"./edit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./edit.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/patrol_pkg/task/edit.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=template&id=36b84017&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.showUserSelect\n    ? _vm.__map(_vm.filteredUsers, function (user, idx) {\n        var $orig = _vm.__get_orig(user)\n        var m0 = user.role && _vm.getRoleText(user.role)\n        var m1 = m0 ? _vm.getRoleText(user.role) : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n        }\n      })\n    : null\n  var m2 = _vm.selectedUser ? _vm.getRoleText(_vm.selectedUser.role) : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      _vm.showUserSelect = true\n    }\n    _vm.e1 = function ($event) {\n      $event.stopPropagation()\n      _vm.showUserSelect = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.showUserSelect = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"edit-task-container\" @click=\"hideUserSelect\">\n\t\t\n\t\t<view class=\"form-container\">\n\t\t\t<form @submit=\"submitForm\">\n\t\t\t\t<!-- 基本信息 -->\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<text class=\"section-title\">基本信息</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label required\">任务名称</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\ttype=\"text\" \n\t\t\t\t\t\t\tv-model=\"formData.name\"\n\t\t\t\t\t\t\tplaceholder=\"请输入任务名称\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\" v-if=\"false\">\n\t\t\t\t\t\t<text class=\"form-label required\">区域</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t\ttype=\"text\" \n\t\t\t\t\t\t\tv-model=\"formData.area\"\n\t\t\t\t\t\t\tplaceholder=\"请输入巡视区域\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label required\">执行日期</text>\n\t\t\t\t\t\t<picker \n\t\t\t\t\t\t\tmode=\"date\" \n\t\t\t\t\t\t\t:value=\"formData.date\" \n\t\t\t\t\t\t\t@change=\"onDateChange\"\n\t\t\t\t\t\t\tclass=\"form-picker\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<view class=\"picker-value\">\n\t\t\t\t\t\t\t\t<text>{{ formData.date || '请选择执行日期' }}</text>\n\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"20\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 班次与轮次 -->\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<text class=\"section-title\">班次与轮次</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label required\">班次</text>\n\t\t\t\t\t\t<picker \n\t\t\t\t\t\t\t:range=\"shiftOptions\" \n\t\t\t\t\t\t\trange-key=\"name\" \n\t\t\t\t\t\t\t:value=\"shiftIndex\"\n\t\t\t\t\t\t\t@change=\"onShiftChange\"\n\t\t\t\t\t\t\tclass=\"form-picker\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<view class=\"picker-value\">\n\t\t\t\t\t\t\t\t<view class=\"shift-info\">\n\t\t\t\t\t\t\t\t\t<text>{{ shiftOptions[shiftIndex].name || '请选择班次' }}</text>\n\t\t\t\t\t\t\t\t\t<text v-if=\"selectedShift && selectedShift.across_day\" class=\"cross-day-tag\">跨天班次</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"18\" color=\"#999999\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\" v-if=\"selectedShift && selectedShift.rounds\">\n\t\t\t\t\t\t<text class=\"form-label\">轮次信息</text>\n\t\t\t\t\t\t<view class=\"shift-time-info\" v-if=\"selectedShift\">\n\t\t\t\t\t\t\t<view class=\"time-range\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"14\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t<text>{{ selectedShift.start_time }} - {{ selectedShift.end_time }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"round-tags\">\n\t\t\t\t\t\t\t<view class=\"round-tag\" v-for=\"(round, index) in selectedShift.rounds\" :key=\"index\">\n\t\t\t\t\t\t\t\t轮次{{ round.round }}: {{ round.start_time || round.time || '未设置时间' }} \n\t\t\t\t\t\t\t\t<text v-if=\"round.day_offset > 0\" class=\"next-day-badge\">次日</text>\n\t\t\t\t\t\t\t\t<text v-if=\"round.duration\">, 有效时长{{ round.duration }}分钟</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 巡检路线 -->\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<text class=\"section-title\">巡检路线</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label required\">路线</text>\n\t\t\t\t\t\t<picker \n\t\t\t\t\t\t\t:range=\"routeOptions\" \n\t\t\t\t\t\t\trange-key=\"name\" \n\t\t\t\t\t\t\t:value=\"routeIndex\"\n\t\t\t\t\t\t\t@change=\"onRouteChange\"\n\t\t\t\t\t\t\tclass=\"form-picker\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<view class=\"picker-value\">\n\t\t\t\t\t\t\t\t<text>{{ routeOptions[routeIndex].name || '请选择巡检路线' }}</text>\n\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"18\" color=\"#999999\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\" v-if=\"selectedRoute && selectedRoute.pointsDetail\">\n\t\t\t\t\t\t<view class=\"points-header\">\n\t\t\t\t\t\t\t<text class=\"form-label\">点位列表</text>\n\t\t\t\t\t\t\t<view class=\"refresh-points-btn\" @click=\"refreshRoutePointsData\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"refresh\" size=\"16\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t<text>刷新点位数据</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"points-info\" v-if=\"pointsRefreshed\">\n\t\t\t\t\t\t\t<uni-icons type=\"info\" size=\"14\" color=\"#52C41A\"></uni-icons>\n\t\t\t\t\t\t\t<text class=\"info-text success\">点位数据已更新至最新</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"points-list\">\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tclass=\"point-item\" \n\t\t\t\t\t\t\t\tv-for=\"(point, index) in selectedRoute.pointsDetail\" \n\t\t\t\t\t\t\t\t:key=\"point._id\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<text class=\"point-index\">{{ index + 1 }}</text>\n\t\t\t\t\t\t\t\t<text class=\"point-name\">{{ point.name }}</text>\n\t\t\t\t\t\t\t\t<text class=\"point-range\">{{ point.range }}米</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 任务执行人 -->\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<text class=\"section-title\">任务执行人</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label required\">人员选择</text>\n\t\t\t\t\t\t<view class=\"custom-select\" @click.stop=\"showUserSelect = true\">\n\t\t\t\t\t\t\t<view class=\"picker-value\">\n\t\t\t\t\t\t\t\t<text>{{ selectedUser ? selectedUser.nickname : '请选择执行人员' }}</text>\n\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"18\" color=\"#999999\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 自定义下拉选择器 -->\n\t\t\t\t\t\t<view class=\"user-select-modal\" v-if=\"showUserSelect\" @click.stop=\"showUserSelect = false\">\n\t\t\t\t\t\t\t<view class=\"user-select-popup\" @click.stop>\n\t\t\t\t\t\t\t\t<view class=\"user-select-container\">\n\t\t\t\t\t\t\t\t\t<view class=\"user-select-header\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"user-select-title\">请选择执行人员</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"user-search-header\">\n\t\t\t\t\t\t\t\t\t\t<input \n\t\t\t\t\t\t\t\t\t\t\tclass=\"user-search-input\" \n\t\t\t\t\t\t\t\t\t\t\ttype=\"text\" \n\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"输入姓名进行搜索\" \n\t\t\t\t\t\t\t\t\t\t\tv-model=\"searchUserName\"\n\t\t\t\t\t\t\t\t\t\t\t@input=\"filterUserOptions\"\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"search\" size=\"18\" color=\"#999999\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<scroll-view scroll-y class=\"user-select-list\">\n\t\t\t\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\t\t\t\tclass=\"user-select-item\" \n\t\t\t\t\t\t\t\t\t\t\tv-for=\"(user, idx) in filteredUsers\" \n\t\t\t\t\t\t\t\t\t\t\t:key=\"user._id\"\n\t\t\t\t\t\t\t\t\t\t\t@tap=\"selectUser(idx)\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"user-item-content\">\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"user.avatar || '/static/user/default-avatar.png'\" class=\"user-avatar-small\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"user-item-details\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"user-item-name\">{{ user.nickname }}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"user-item-role\" v-if=\"user.role && getRoleText(user.role)\">{{ getRoleText(user.role) }}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"select-radio\" v-if=\"selectedUser && selectedUser._id === user._id\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"checkbox-filled\" size=\"18\" color=\"#1677FF\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t\t\t\t\t<view class=\"user-select-footer\">\n\t\t\t\t\t\t\t\t\t\t<button class=\"user-select-close\" @tap=\"showUserSelect = false\">关闭</button>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\" v-if=\"selectedUser\">\n\t\t\t\t\t\t<text class=\"form-label\">所选人员</text>\n\t\t\t\t\t\t<view class=\"user-info\">\n\t\t\t\t\t\t\t<image \n\t\t\t\t\t\t\t\t:src=\"selectedUser.avatar || '/static/user/default-avatar.png'\" \n\t\t\t\t\t\t\t\tmode=\"aspectFill\" \n\t\t\t\t\t\t\t\tclass=\"user-avatar\"\n\t\t\t\t\t\t\t></image>\n\t\t\t\t\t\t\t<view class=\"user-details\">\n\t\t\t\t\t\t\t\t<text class=\"user-name\">{{ selectedUser.nickname }}</text>\n\t\t\t\t\t\t\t\t<text class=\"user-role\">{{ getRoleText(selectedUser.role) }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 任务状态 -->\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<text class=\"section-title\">任务状态</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">当前状态</text>\n\t\t\t\t\t\t<picker \n\t\t\t\t\t\t\t:range=\"statusOptions\" \n\t\t\t\t\t\t\trange-key=\"label\" \n\t\t\t\t\t\t\t:value=\"statusIndex\"\n\t\t\t\t\t\t\t@change=\"onStatusChange\"\n\t\t\t\t\t\t\tclass=\"form-picker status-picker\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<view class=\"picker-value\">\n\t\t\t\t\t\t\t\t<text :class=\"['status-text', `status-${formData.status}`]\">\n\t\t\t\t\t\t\t\t\t{{ statusOptions[statusIndex].label || '请选择状态' }}\n\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#999999\" class=\"small-icon\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 备注 -->\n\t\t\t\t<!-- <view class=\"form-section\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<text class=\"section-title\">备注信息</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">备注</text>\n\t\t\t\t\t\t<textarea \n\t\t\t\t\t\t\tclass=\"form-textarea\" \n\t\t\t\t\t\t\tv-model=\"formData.remark\"\n\t\t\t\t\t\t\tplaceholder=\"请输入备注信息（选填）\"\n\t\t\t\t\t\t></textarea>\n\t\t\t\t\t</view>\n\t\t\t\t</view> -->\n\t\t\t\t\n\t\t\t\t<!-- 提交按钮 -->\n\t\t\t\t<view class=\"submit-section\">\n\t\t\t\t\t<button class=\"cancel-btn\" @click=\"goBack\">取消操作</button>\n\t\t\t\t\t<button \n\t\t\t\t\t\tclass=\"primary-btn\" \n\t\t\t\t\t\tform-type=\"submit\"\n\t\t\t\t\t\t:loading=\"loading\"\n\t\t\t\t\t\t:disabled=\"loading\"\n\t\t\t\t\t\tstyle=\"background-color: #1677FF !important; color: #FFFFFF !important;\"\n\t\t\t\t\t>{{ loading ? '保存中...' : '保存任务' }}</button>\n\t\t\t\t</view>\n\t\t\t</form>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport PatrolApi from '@/utils/patrol-api.js';\n\timport { calculateRoundTime, calculateEndTime, detectTimeZone, preprocessDates } from '@/utils/date.js';\n\t\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tloading: false,\n\t\t\t\ttaskId: '',\n\t\t\t\tformData: {\n\t\t\t\t\tname: '',\n\t\t\t\t\tarea: '',\n\t\t\t\t\tdate: '',\n\t\t\t\t\tshift_id: '',\n\t\t\t\t\troute_id: '',\n\t\t\t\t\tuser_id: '',\n\t\t\t\t\tremark: '',\n\t\t\t\t\tstatus: 0\n\t\t\t\t},\n\t\t\t\tshiftOptions: [{ name: '加载中...' }],\n\t\t\t\tshiftIndex: 0,\n\t\t\t\tselectedShift: null,\n\t\t\t\trouteOptions: [{ name: '加载中...' }],\n\t\t\t\trouteIndex: 0,\n\t\t\t\tselectedRoute: null,\n\t\t\t\tuserOptions: [{ nickname: '加载中...' }],\n\t\t\t\tuserIndex: 0,\n\t\t\t\tselectedUser: null,\n\t\t\t\tstatusOptions: [\n\t\t\t\t\t{ value: 0, label: '未开始' },\n\t\t\t\t\t{ value: 1, label: '进行中' },\n\t\t\t\t\t{ value: 2, label: '已完成' },\n\t\t\t\t\t{ value: 3, label: '已超时' },\n\t\t\t\t\t{ value: 4, label: '已取消' }\n\t\t\t\t],\n\t\t\t\tstatusIndex: 0,\n\t\t\t\troleNameMap: {},\n\t\t\t\ttimeZoneIndicator: null,\n\t\t\t\tSTATUS: {\n\t\t\t\t\tNOT_STARTED: 0,\n\t\t\t\t\tIN_PROGRESS: 1,\n\t\t\t\t\tCOMPLETED: 2,\n\t\t\t\t\tEXPIRED: 3,\n\t\t\t\t\tCANCELLED: 4\n\t\t\t\t},\n\t\t\t\toriginalStatus: null,\n\t\t\t\tpredictedStatus: null,\n\t\t\t\tstatusManuallyChanged: false,\n\t\t\t\tprocessedRounds: [],\n\t\t\t\tpointsRefreshed: false,\n\t\t\t\tsearchUserName: '',\n\t\t\t\tfilteredUsers: [],\n\t\t\t\tshowUserSelect: false,\n\t\t\t}\n\t\t},\n\t\tasync onLoad(options) {\n\t\t\tif (options.id) {\n\t\t\t\tthis.taskId = options.id;\n\t\t\t} else {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '任务ID不能为空',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t}, 1500);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tthis.timeZoneIndicator = detectTimeZone();\n\t\t\t\n\t\t\t// 🔥 优化：并行加载数据提升用户体验\n\t\t\ttry {\n\t\t\t\tuni.showLoading({ title: '加载中...' });\n\t\t\t\t\n\t\t\t\t// 并行加载减少用户等待时间，包括任务详情\n\t\t\t\tawait Promise.all([\n\t\t\t\t\tthis.loadShifts(),\n\t\t\t\t\tthis.loadRoutes(),\n\t\t\t\t\tthis.loadRoles(),\n\t\t\t\t\tthis.loadUsers(),\n\t\t\t\t\tthis.loadTaskData() // 同时加载任务详情\n\t\t\t\t]);\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('初始化数据加载失败:', e);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '数据加载失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\t'formData.date'() {\n\t\t\t\tthis.updatePredictedStatus();\n\t\t\t},\n\t\t\tselectedShift: {\n\t\t\t\thandler() {\n\t\t\t\t\tthis.updatePredictedStatus();\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t},\n\t\t\tstatusIndex() {\n\t\t\t\tif (this.originalStatus !== null) {\n\t\t\t\t\tconst newStatus = this.statusOptions[this.statusIndex].value;\n\t\t\t\t\tthis.statusManuallyChanged = (newStatus !== this.originalStatus);\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 监听用户列表变化，初始化过滤后的列表\n\t\t\tuserOptions: {\n\t\t\t\thandler(val) {\n\t\t\t\t\tthis.filteredUsers = [...val];\n\t\t\t\t},\n\t\t\t\timmediate: true\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tupdatePredictedStatus() {\n\t\t\t\tif (!this.formData.date || !this.selectedShift || !this.selectedShift.start_time) {\n\t\t\t\t\tthis.predictedStatus = this.STATUS.NOT_STARTED;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconst timezoneOffset = -(new Date().getTimezoneOffset() / 60);\n\t\t\t\t\tconsole.log(`当前时区偏移: UTC${timezoneOffset >= 0 ? '+' : ''}${timezoneOffset}`);\n\t\t\t\t\t\n\t\t\t\t\tconst isAcrossDay = this.selectedShift.across_day;\n\t\t\t\t\tconst shiftStartTime = this.selectedShift.start_time;\n\t\t\t\t\t\n\t\t\t\t\tlet startTime = calculateRoundTime(this.formData.date, shiftStartTime);\n\t\t\t\t\t\n\t\t\t\t\tconst now = new Date();\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('预计任务状态计算:');\n\t\t\t\t\tconsole.log(`- 任务日期: ${this.formData.date}`);\n\t\t\t\t\tconsole.log(`- 班次开始时间: ${shiftStartTime}`);\n\t\t\t\t\tconsole.log(`- 完整开始时间: ${startTime.toISOString()}`);\n\t\t\t\t\tconsole.log(`- 当前时间: ${now.toISOString()}`);\n\t\t\t\t\tconsole.log(`- 是否跨天: ${isAcrossDay}`);\n\t\t\t\t\t\n\t\t\t\t\tif (startTime > now) {\n\t\t\t\t\t\tconsole.log('> 判断结果: 未开始（开始时间在未来）');\n\t\t\t\t\t\tthis.predictedStatus = this.STATUS.NOT_STARTED;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.log('> 判断结果: 进行中（开始时间已过）');\n\t\t\t\t\t\tthis.predictedStatus = this.STATUS.IN_PROGRESS;\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('计算预计状态时出错:', error);\n\t\t\t\t\tthis.predictedStatus = this.STATUS.NOT_STARTED;\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tcalculateTaskStatus(startTimeISOString) {\n\t\t\t\t// 首先获取所有已处理过的轮次（有状态值的轮次）\n\t\t\t\tif (this.processedRounds && this.processedRounds.length > 0) {\n\t\t\t\t\t// 检查是否有进行中的轮次\n\t\t\t\t\tconst hasActiveRounds = this.processedRounds.some(round => round.status === 1);\n\t\t\t\t\tif (hasActiveRounds) {\n\t\t\t\t\t\tconsole.log('> 计算结果: 进行中（有进行中的轮次）');\n\t\t\t\t\t\treturn this.STATUS.IN_PROGRESS;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 检查是否所有轮次都已完成\n\t\t\t\t\tconst allRoundsCompleted = this.processedRounds.every(round => round.status === 2);\n\t\t\t\t\tif (allRoundsCompleted) {\n\t\t\t\t\t\tconsole.log('> 计算结果: 已完成（所有轮次都已完成）');\n\t\t\t\t\t\treturn this.STATUS.COMPLETED;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 检查是否所有轮次都已超时\n\t\t\t\t\tconst allRoundsExpired = this.processedRounds.every(round => round.status === 3);\n\t\t\t\t\tif (allRoundsExpired) {\n\t\t\t\t\t\tconsole.log('> 计算结果: 已超时（所有轮次都已超时）');\n\t\t\t\t\t\treturn this.STATUS.EXPIRED;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 检查是否所有轮次都未开始\n\t\t\t\t\tconst allRoundsNotStarted = this.processedRounds.every(round => round.status === 0);\n\t\t\t\t\tif (allRoundsNotStarted) {\n\t\t\t\t\t\tconsole.log('> 计算结果: 未开始（所有轮次都未开始）');\n\t\t\t\t\t\treturn this.STATUS.NOT_STARTED;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 如果有些轮次已超时，有些未开始，但没有进行中的轮次\n\t\t\t\t\tconsole.log('> 计算结果: 进行中（混合状态）');\n\t\t\t\t\treturn this.STATUS.IN_PROGRESS; // 默认是进行中\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果没有轮次信息，则回退到基于班次时间的判断\n\t\t\t\tif (!startTimeISOString) {\n\t\t\t\t\treturn this.STATUS.NOT_STARTED;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconst now = new Date();\n\t\t\t\t\tconst startTime = new Date(startTimeISOString);\n\t\t\t\t\t\n\t\t\t\t\t// 输出日志\n\t\t\t\t\tconsole.log('计算任务状态:');\n\t\t\t\t\tconsole.log(`- 当前时间: ${now.toISOString()}`);\n\t\t\t\t\tconsole.log(`- 开始时间: ${startTime.toISOString()}`);\n\t\t\t\t\t\n\t\t\t\t\t// 与预测状态相同，使用第一个轮次的开始时间进行验证\n\t\t\t\t\tif (this.selectedShift && this.selectedShift.rounds && this.selectedShift.rounds.length > 0) {\n\t\t\t\t\t\t// 按照时间顺序排序轮次\n\t\t\t\t\t\tconst sortedRounds = [...this.selectedShift.rounds].sort((a, b) => {\n\t\t\t\t\t\t\t// 获取轮次时间并考虑day_offset\n\t\t\t\t\t\t\tconst timeA = a.time.split(':').map(Number);\n\t\t\t\t\t\t\tconst timeB = b.time.split(':').map(Number);\n\t\t\t\t\t\t\tconst offsetA = a.day_offset || 0;\n\t\t\t\t\t\t\tconst offsetB = b.day_offset || 0;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 先比较day_offset，再比较时间\n\t\t\t\t\t\t\tif (offsetA !== offsetB) return offsetA - offsetB;\n\t\t\t\t\t\t\tif (timeA[0] !== timeB[0]) return timeA[0] - timeB[0];\n\t\t\t\t\t\t\treturn timeA[1] - timeB[1];\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 获取第一个轮次的时间\n\t\t\t\t\t\tconst firstRound = sortedRounds[0];\n\t\t\t\t\t\tconst roundTime = firstRound.time;\n\t\t\t\t\t\tconst dayOffset = firstRound.day_offset || 0;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 计算轮次实际开始时间\n\t\t\t\t\t\tconst roundStartTime = calculateRoundTime(this.formData.date, roundTime, dayOffset);\n\t\t\t\t\t\t\n\t\t\t\t\t\tconsole.log(`- 第一个轮次时间: ${roundTime}, 日期偏移: ${dayOffset}`);\n\t\t\t\t\t\tconsole.log(`- 轮次实际开始时间: ${roundStartTime.toISOString()}`);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 如果轮次开始时间尚未到达，则任务未开始\n\t\t\t\t\t\tif (roundStartTime > now) {\n\t\t\t\t\t\t\tconsole.log('> 计算结果: 未开始 (基于轮次时间)');\n\t\t\t\t\t\t\treturn this.STATUS.NOT_STARTED;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tif (startTime > now) {\n\t\t\t\t\t\tconsole.log('> 计算结果: 未开始（开始时间在未来）');\n\t\t\t\t\t\treturn this.STATUS.NOT_STARTED;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('> 计算结果: 进行中');\n\t\t\t\t\treturn this.STATUS.IN_PROGRESS;\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('计算任务状态时出错:', error);\n\t\t\t\t\treturn this.STATUS.NOT_STARTED;\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tgetStatusIcon(status) {\n\t\t\t\tswitch(status) {\n\t\t\t\t\tcase this.STATUS.NOT_STARTED: return 'info-filled';\n\t\t\t\t\tcase this.STATUS.IN_PROGRESS: return 'reload';\n\t\t\t\t\tcase this.STATUS.COMPLETED: return 'checkmarkempty';\n\t\t\t\t\tcase this.STATUS.EXPIRED: return 'closeempty';\n\t\t\t\t\tcase this.STATUS.CANCELLED: return 'minus';\n\t\t\t\t\tdefault: return 'info';\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tgetStatusText(status) {\n\t\t\t\tswitch(status) {\n\t\t\t\t\tcase this.STATUS.NOT_STARTED: return '未开始';\n\t\t\t\t\tcase this.STATUS.IN_PROGRESS: return '进行中';\n\t\t\t\t\tcase this.STATUS.COMPLETED: return '已完成';\n\t\t\t\t\tcase this.STATUS.EXPIRED: return '已超时';\n\t\t\t\t\tcase this.STATUS.CANCELLED: return '已取消';\n\t\t\t\t\tdefault: return '未知状态';\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tgetStatusExplanation(status) {\n\t\t\t\tconst now = new Date();\n\t\t\t\tconst formattedNow = now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });\n\t\t\t\t\n\t\t\t\tswitch(status) {\n\t\t\t\t\tcase this.STATUS.NOT_STARTED:\n\t\t\t\t\t\tif (this.selectedShift && this.selectedShift.start_time) {\n\t\t\t\t\t\t\treturn `当前时间 ${formattedNow}，任务将在 ${this.selectedShift.start_time} 开始`;\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn '任务开始时间尚未到达';\n\t\t\t\t\tcase this.STATUS.IN_PROGRESS:\n\t\t\t\t\t\tif (this.selectedShift && this.selectedShift.start_time) {\n\t\t\t\t\t\t\treturn `当前时间 ${formattedNow}，已超过任务开始时间 ${this.selectedShift.start_time}`;\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn '当前时间已超过任务开始时间';\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tgetCurrentUserId() {\n\t\t\t\ttry {\n\t\t\t\t\tconst userInfo = uni.getStorageSync('uni-id-pages-userInfo');\n\t\t\t\t\treturn userInfo ? userInfo._id || '' : '';\n\t\t\t\t} catch (e) {\n\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tasync loadTaskData() {\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '加载中'\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tconst currentUserId = this.getCurrentUserId();\n\t\t\t\t\t\n\t\t\t\t\tconst result = await PatrolApi.call({\n\t\t\t\t\t\tname: 'patrol-task',\n\t\t\t\t\t\taction: 'getTaskDetail',\n\t\t\t\t\t\tdata: { \n\t\t\t\t\t\t\tid: this.taskId,\n\t\t\t\t\t\t\tuserId: currentUserId\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (result.code === 0 && result.data) {\n\t\t\t\t\t\tconst taskData = result.data.task || result.data;\n\t\t\t\t\t\t\n\t\t\t\t\t\tthis.formData = {\n\t\t\t\t\t\t\tname: taskData.name || '',\n\t\t\t\t\t\t\tarea: taskData.area || '',\n\t\t\t\t\t\t\tdate: taskData.patrol_date || '',\n\t\t\t\t\t\t\tshift_id: taskData.shift_id || '',\n\t\t\t\t\t\t\troute_id: taskData.route_id || '',\n\t\t\t\t\t\t\tuser_id: taskData.user_id || '',\n\t\t\t\t\t\t\tremark: taskData.remark || '',\n\t\t\t\t\t\t\tstatus: taskData.status || 0,\n\t\t\t\t\t\t\tenabled_rounds: taskData.enabled_rounds || [],\n\t\t\t\t\t\t\trounds_completion: taskData.rounds_completion || []\n\t\t\t\t\t\t};\n\t\t\t\t\t\t\n\t\t\t\t\t\tthis.originalStatus = taskData.status;\n\t\t\t\t\t\t\n\t\t\t\t\t\tthis.statusIndex = this.statusOptions.findIndex(item => item.value === taskData.status);\n\t\t\t\t\t\tif (this.statusIndex === -1) this.statusIndex = 0;\n\t\t\t\t\t\t\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.setInitialSelections();\n\t\t\t\t\t\t}, 500);\n\t\t\t\t\t\t\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.updatePredictedStatus();\n\t\t\t\t\t\t}, 800);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(result.message || '获取任务详情失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: e.message || '加载任务数据失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync loadShifts() {\n\t\t\t\ttry {\n\t\t\t\t\tconst result = await PatrolApi.call({\n\t\t\t\t\t\tname: 'patrol-shift',\n\t\t\t\t\t\taction: 'getShiftList',\n\t\t\t\t\t\tdata: { \n\t\t\t\t\t\t\tparams: { \n\t\t\t\t\t\t\t\tstatus: 1,\n\t\t\t\t\t\t\t\twith_rounds: true,\n\t\t\t\t\t\t\t\twith_detail: true\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (result.code === 0 && result.data) {\n\t\t\t\t\t\tlet shifts = result.data.list || [];\n\t\t\t\t\t\tif (shifts.length === 0) {\n\t\t\t\t\t\t\tthis.shiftOptions = [{ name: '暂无可用班次' }];\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 处理班次数据\n\t\t\t\t\t\t\tshifts = shifts.map(shift => ({\n\t\t\t\t\t\t\t\t...shift,\n\t\t\t\t\t\t\t\tname: shift.name,\n\t\t\t\t\t\t\t\tacross_day: shift.across_day || false,\n\t\t\t\t\t\t\t\trounds: shift.rounds && Array.isArray(shift.rounds) \n\t\t\t\t\t\t\t\t\t? shift.rounds.filter(r => r.status !== 0).map(round => {\n\t\t\t\t\t\t\t\t\t\tconst time = round.time || '00:00';\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\t\t\tround: round.round,\n\t\t\t\t\t\t\t\t\t\t\ttime: time,\n\t\t\t\t\t\t\t\t\t\t\tname: round.name || `轮次${round.round}`,\n\t\t\t\t\t\t\t\t\t\t\tstart_time: round.start_time || time,\n\t\t\t\t\t\t\t\t\t\t\tend_time: round.end_time || time,\n\t\t\t\t\t\t\t\t\t\t\tday_offset: round.day_offset || 0,\n\t\t\t\t\t\t\t\t\t\t\tduration: round.duration || 60,\n\t\t\t\t\t\t\t\t\t\t\tstatus: round.status\n\t\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t: []\n\t\t\t\t\t\t\t}));\n\n\t\t\t\t\t\t\t// 按照班次名称字母顺序排序\n\t\t\t\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\t\t\t\t\t\tconst platform = systemInfo.platform;\n\t\t\t\t\t\t\tconst isMobile = ['android', 'ios'].includes(platform);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 对班次进行排序\n\t\t\t\t\t\t\tshifts.sort((a, b) => {\n\t\t\t\t\t\t\t\tconst nameA = String(a.name || '').trim();\n\t\t\t\t\t\t\t\tconst nameB = String(b.name || '').trim();\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 使用 localeCompare 进行中文排序，统一使用 A-Z 排序\n\t\t\t\t\t\t\t\tconst compareResult = nameA.localeCompare(nameB, 'zh-CN');\n\t\t\t\t\t\t\t\treturn isMobile ? -compareResult : compareResult;\n\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\tthis.shiftOptions = shifts;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (this.formData.shift_id) {\n\t\t\t\t\t\t\tthis.setInitialSelections();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '加载班次数据失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync loadRoutes() {\n\t\t\t\ttry {\n\t\t\t\t\tconst result = await PatrolApi.call({\n\t\t\t\t\t\tname: 'patrol-route',\n\t\t\t\t\t\taction: 'getRouteList',\n\t\t\t\t\t\tdata: { \n\t\t\t\t\t\t\tparams: { \n\t\t\t\t\t\t\t\tstatus: 1, \n\t\t\t\t\t\t\t\twith_points: true,\n\t\t\t\t\t\t\t\twith_detail: true,\n\t\t\t\t\t\t\t\tinclude_point_details: true\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (result.code === 0 && result.data) {\n\t\t\t\t\t\tthis.routeOptions = result.data.list || [];\n\t\t\t\t\t\tif (this.routeOptions.length === 0) {\n\t\t\t\t\t\t\tthis.routeOptions = [{ name: '暂无可用路线' }];\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.routeOptions = await Promise.all(this.routeOptions.map(async route => {\n\t\t\t\t\t\t\t\tconst detailRes = await PatrolApi.call({\n\t\t\t\t\t\t\t\t\tname: 'patrol-route',\n\t\t\t\t\t\t\t\t\taction: 'getRouteDetail',\n\t\t\t\t\t\t\t\t\tdata: { \n\t\t\t\t\t\t\t\t\t\tparams: { \n\t\t\t\t\t\t\t\t\t\t\troute_id: route._id,\n\t\t\t\t\t\t\t\t\t\t\twith_points: true,\n\t\t\t\t\t\t\t\t\t\t\twith_detail: true\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tif (detailRes.code === 0 && detailRes.data && detailRes.data.pointsDetail) {\n\t\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\t\t...route,\n\t\t\t\t\t\t\t\t\t\tpointsDetail: detailRes.data.pointsDetail\n\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\treturn route;\n\t\t\t\t\t\t\t}));\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (this.formData.route_id) {\n\t\t\t\t\t\t\tthis.setInitialSelections();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '加载路线数据失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync loadUsers() {\n\t\t\t\ttry {\n\t\t\t\t\tconst currentUserId = this.getCurrentUserId();\n\t\t\t\t\tif (!currentUserId) {\n\t\t\t\t\t\tthrow new Error('未能获取当前用户ID');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconst result = await PatrolApi.call({\n\t\t\t\t\t\tname: 'patrol-user',\n\t\t\t\t\t\taction: 'getUsers',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tuserid: currentUserId,\n\t\t\t\t\t\t\tparams: {\n\t\t\t\t\t\t\t\tpageSize: 100,\n\t\t\t\t\t\t\t\tfield: 'nickname,avatar,role,username,wx_openid,identities' // 请求所需字段\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (result.code === 0 && result.data) {\n\t\t\t\t\t\tthis.userOptions = result.data.list\n\t\t\t\t\t\t\t.filter(user => {\n\t\t\t\t\t\t\t\tif (!user.role) return true;\n\t\t\t\t\t\t\t\tif (Array.isArray(user.role)) {\n\t\t\t\t\t\t\t\t\treturn !user.role.includes('admin');\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\treturn user.role !== 'admin';\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t.filter(user => {\n\t\t\t\t\t\t\t\t// 过滤掉以\"匿名\"开头的用户\n\t\t\t\t\t\t\t\tconst nickname = user.real_name || user.nickname || user.username || '';\n\t\t\t\t\t\t\t\treturn !nickname.startsWith('匿名');\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t.map(user => ({\n\t\t\t\t\t\t\t\t_id: user._id,\n\t\t\t\t\t\t\t\tnickname: user.real_name || user.nickname || user.username || '未命名用户',\n\t\t\t\t\t\t\t\tavatar: user.avatar || '/static/user/default-avatar.png',\n\t\t\t\t\t\t\t\trole: user.role || [],\n\t\t\t\t\t\t\t\twx_openid: user.wx_openid || null,\n\t\t\t\t\t\t\t\tidentities: user.identities || []\n\t\t\t\t\t\t\t}));\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (this.userOptions.length === 0) {\n\t\t\t\t\t\t\tthis.userOptions = [{ nickname: '暂无可用人员' }];\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (this.formData.user_id) {\n\t\t\t\t\t\t\tthis.setInitialSelections();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '加载用户数据失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync loadRoles() {\n\t\t\t\ttry {\n\t\t\t\t\tconst currentUserId = this.getCurrentUserId();\n\t\t\t\t\tif (!currentUserId) {\n\t\t\t\t\t\tthrow new Error('未能获取当前用户ID');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconst result = await PatrolApi.call({\n\t\t\t\t\t\tname: 'patrol-user',\n\t\t\t\t\t\taction: 'getRoleList',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tuserid: currentUserId\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (result.code === 0 && result.data) {\n\t\t\t\t\t\tconst roleMap = {};\n\t\t\t\t\t\tresult.data.forEach(role => {\n\t\t\t\t\t\t\troleMap[role.role_id] = role.role_name;\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.roleNameMap = roleMap;\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '加载角色数据失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync loadShiftDetail(shiftId) {\n\t\t\t\ttry {\n\t\t\t\t\tconst result = await PatrolApi.call({\n\t\t\t\t\t\tname: 'patrol-shift',\n\t\t\t\t\t\taction: 'getShiftDetail',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tparams: {\n\t\t\t\t\t\t\t\tshift_id: shiftId\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (result.code === 0 && result.data) {\n\t\t\t\t\t\tconst shiftData = result.data;\n\t\t\t\t\t\tif (shiftData.rounds && Array.isArray(shiftData.rounds)) {\n\t\t\t\t\t\t\tconst enabledRounds = shiftData.rounds.filter(r => r.status !== 0);\n\t\t\t\t\t\t\tthis.selectedShift = {\n\t\t\t\t\t\t\t\t...shiftData,\n\t\t\t\t\t\t\t\tacross_day: shiftData.across_day || false,\n\t\t\t\t\t\t\t\trounds: enabledRounds.map(round => {\n\t\t\t\t\t\t\t\t\tconst time = round.time || '00:00';\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\t\tround: round.round,\n\t\t\t\t\t\t\t\t\t\tname: round.name || `轮次${round.round}`,\n\t\t\t\t\t\t\t\t\t\ttime: time,\n\t\t\t\t\t\t\t\t\t\tstart_time: round.start_time || time,\n\t\t\t\t\t\t\t\t\t\tend_time: round.end_time || time,\n\t\t\t\t\t\t\t\t\t\tday_offset: round.day_offset || 0,\n\t\t\t\t\t\t\t\t\t\tduration: round.duration || 60,\n\t\t\t\t\t\t\t\t\t\tstatus: round.status\n\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('加载班次详情失败:', e);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '加载班次详情失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tsetInitialSelections() {\n\t\t\t\tif (this.formData.shift_id && this.shiftOptions.length > 0) {\n\t\t\t\t\tconst index = this.shiftOptions.findIndex(item => item._id === this.formData.shift_id);\n\t\t\t\t\tif (index !== -1) {\n\t\t\t\t\t\tthis.shiftIndex = index;\n\t\t\t\t\t\tconst shift = this.shiftOptions[index];\n\t\t\t\t\t\t\n\t\t\t\t\t\tthis.selectedShift = {\n\t\t\t\t\t\t\t...shift,\n\t\t\t\t\t\t\tacross_day: shift.across_day || false,\n\t\t\t\t\t\t\trounds: shift.rounds && Array.isArray(shift.rounds) \n\t\t\t\t\t\t\t\t? shift.rounds.filter(r => r.status !== 0).map(round => {\n\t\t\t\t\t\t\t\t\tconst time = round.time || '00:00';\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\t\tround: round.round,\n\t\t\t\t\t\t\t\t\t\tname: round.name || `轮次${round.round}`,\n\t\t\t\t\t\t\t\t\t\ttime: time,\n\t\t\t\t\t\t\t\t\t\tstart_time: round.start_time || time,\n\t\t\t\t\t\t\t\t\t\tend_time: round.end_time || time,\n\t\t\t\t\t\t\t\t\t\tday_offset: round.day_offset || 0,\n\t\t\t\t\t\t\t\t\t\tduration: round.duration || 60,\n\t\t\t\t\t\t\t\t\t\tstatus: round.status\n\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t: []\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (this.formData.route_id && this.routeOptions.length > 0) {\n\t\t\t\t\tconst index = this.routeOptions.findIndex(item => item._id === this.formData.route_id);\n\t\t\t\t\tif (index !== -1) {\n\t\t\t\t\t\tthis.routeIndex = index;\n\t\t\t\t\t\tconst route = this.routeOptions[index];\n\t\t\t\t\t\t\n\t\t\t\t\t\tthis.selectedRoute = {\n\t\t\t\t\t\t\t...route,\n\t\t\t\t\t\t\tpointsDetail: route.pointsDetail || []\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (this.formData.user_id && this.userOptions.length > 0) {\n\t\t\t\t\tconst index = this.userOptions.findIndex(item => item._id === this.formData.user_id);\n\t\t\t\t\tif (index !== -1) {\n\t\t\t\t\t\tthis.userIndex = index;\n\t\t\t\t\t\tthis.selectedUser = this.userOptions[index];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tonDateChange(e) {\n\t\t\t\t// 确保日期格式一致 - 只保留YYYY-MM-DD部分\n\t\t\t\tconst dateValue = e.detail.value;\n\t\t\t\t// 检查是否需要处理日期格式\n\t\t\t\tif (dateValue.includes('T') || dateValue.includes('Z')) {\n\t\t\t\t\t// 如果有时区信息，提取日期部分\n\t\t\t\t\tthis.formData.date = dateValue.split('T')[0];\n\t\t\t\t} else {\n\t\t\t\t\t// 否则直接使用，这应该已经是YYYY-MM-DD格式\n\t\t\t\t\tthis.formData.date = dateValue;\n\t\t\t\t}\n\t\t\t},\n\t\t\tonShiftChange(e) {\n\t\t\t\tconst index = e.detail.value;\n\t\t\t\tthis.shiftIndex = index;\n\t\t\t\tconst shift = this.shiftOptions[index];\n\t\t\t\t\n\t\t\t\tif (shift && shift._id) {\n\t\t\t\t\tthis.formData.shift_id = shift._id;\n\t\t\t\t\t\n\t\t\t\t\tif (shift.rounds && Array.isArray(shift.rounds)) {\n\t\t\t\t\t\tconst enabledRounds = shift.rounds.filter(r => r.status !== 0);\n\t\t\t\t\t\tthis.selectedShift = {\n\t\t\t\t\t\t\t...shift,\n\t\t\t\t\t\t\tacross_day: shift.across_day || false,\n\t\t\t\t\t\t\trounds: enabledRounds.map(round => {\n\t\t\t\t\t\t\t\tconst time = round.time || '00:00';\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\tround: round.round,\n\t\t\t\t\t\t\t\t\tname: round.name || `轮次${round.round}`,\n\t\t\t\t\t\t\t\t\ttime: time,\n\t\t\t\t\t\t\t\t\tstart_time: round.start_time || time,\n\t\t\t\t\t\t\t\t\tend_time: round.end_time || time,\n\t\t\t\t\t\t\t\t\tday_offset: round.day_offset || 0,\n\t\t\t\t\t\t\t\t\tduration: round.duration || 60,\n\t\t\t\t\t\t\t\t\tstatus: round.status\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t};\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 如果是跨天班次，给用户提示\n\t\t\t\t\t\tif (this.selectedShift.across_day) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '您选择了跨天班次，系统将自动处理次日轮次',\n\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.loadShiftDetail(shift._id);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.formData.shift_id = '';\n\t\t\t\t\tthis.selectedShift = null;\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync onRouteChange(e) {\n\t\t\t\tthis.routeIndex = e.detail.value;\n\t\t\t\tconst route = this.routeOptions[this.routeIndex];\n\t\t\t\t\n\t\t\t\tif (route && route._id) {\n\t\t\t\t\tthis.formData.route_id = route._id;\n\t\t\t\t\tthis.formData.area = route.name || '';\n\t\t\t\t\t\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst res = await PatrolApi.call({\n\t\t\t\t\t\t\tname: 'patrol-route',\n\t\t\t\t\t\t\taction: 'getRouteDetail',\n\t\t\t\t\t\t\tdata: { \n\t\t\t\t\t\t\t\tparams: { \n\t\t\t\t\t\t\t\t\troute_id: route._id,\n\t\t\t\t\t\t\t\t\twith_points: true,\n\t\t\t\t\t\t\t\t\twith_detail: true,\n\t\t\t\t\t\t\t\t\tinclude_point_details: true\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\t\t\tconst routeData = res.data;\n\t\t\t\t\t\t\tthis.selectedRoute = {\n\t\t\t\t\t\t\t\t...routeData,\n\t\t\t\t\t\t\t\tpointsDetail: routeData.pointsDetail || []\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tthis.formData.area = routeData.name || route.name || '';\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error('获取路线详情错误', e);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.formData.route_id = '';\n\t\t\t\t\tthis.formData.area = '';\n\t\t\t\t\tthis.selectedRoute = null;\n\t\t\t\t}\n\t\t\t},\n\t\t\tonUserChange(index) {\n\t\t\t\tthis.userIndex = index;\n\t\t\t\tthis.selectedUser = this.userOptions[index];\n\t\t\t\tthis.formData.user_id = this.selectedUser._id;\n\t\t\t},\n\t\t\tonStatusChange(e) {\n\t\t\t\tconst index = e.detail.value;\n\t\t\t\tthis.statusIndex = index;\n\t\t\t\tthis.formData.status = this.statusOptions[index].value;\n\t\t\t},\n\t\t\tgetRoleText(role) {\n\t\t\t\tif (!role) return '普通员工';\n\t\t\t\t\n\t\t\t\tif (Array.isArray(role)) {\n\t\t\t\t\tif (role.length === 0) return '普通员工';\n\t\t\t\t\treturn role.map(r => this.getSingleRoleText(r)).join(', ');\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn this.getSingleRoleText(role);\n\t\t\t},\n\t\t\tgetSingleRoleText(role) {\n\t\t\t\tif (this.roleNameMap[role]) {\n\t\t\t\t\treturn this.roleNameMap[role];\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst roleMap = {\n\t\t\t\t\t'admin': '管理员',\n\t\t\t\t\t'responsible': '责任人',\n\t\t\t\t\t'reviser': '发布人',\n\t\t\t\t\t'supervisor': '主管',\n\t\t\t\t\t'PM': '副厂长',\n\t\t\t\t\t'GM': '厂长',\n\t\t\t\t\t'logistics': '后勤员',\n\t\t\t\t\t'dispatch': '调度员',\n\t\t\t\t\t'Integrated': '综合员',\n\t\t\t\t\t'operator': '设备员',\n\t\t\t\t\t'technician': '工艺员',\n\t\t\t\t\t'mechanic': '技术员',\n\t\t\t\t\t'user': '普通员工',\n\t\t\t\t\t'manager': '管理人员',\n\t\t\t\t\t'worker': '普通员工'\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\treturn roleMap[role] || '用户 (' + role + ')';\n\t\t\t},\n\t\t\tasync checkDuplicateTask() {\n\t\t\t\ttry {\n\t\t\t\t\tconst queryParams = {\n\t\t\t\t\t\tpatrolDate: this.formData.date.split('T')[0], // 确保只有日期部分\n\t\t\t\t\t\tfilters: {\n\t\t\t\t\t\t\troute_id: this.formData.route_id\n\t\t\t\t\t\t},\n\t\t\t\t\t\tuserId: this.formData.user_id,\n\t\t\t\t\t\tshift_id: this.formData.shift_id\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\tconst response = await PatrolApi.getTaskList(queryParams);\n\t\t\t\t\t\n\t\t\t\t\tif (response && response.code === 0 && response.data && response.data.list) {\n\t\t\t\t\t\t// 过滤掉当前正在编辑的任务\n\t\t\t\t\t\tconst duplicateTasks = response.data.list.filter(task => task._id !== this.taskId);\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (duplicateTasks.length > 0) {\n\t\t\t\t\t\t\tconst task = duplicateTasks[0];\n\t\t\t\t\t\t\tconst shiftName = task.shift_detail?.name || '未知班次';\n\t\t\t\t\t\t\tconst routeName = task.route_detail?.name || '未知路线';\n\t\t\t\t\t\t\tconst userName = task.user_detail?.nickname || '未知用户';\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\treturn new Promise((resolve) => {\n\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\ttitle: '发现重复任务',\n\t\t\t\t\t\t\t\t\tcontent: `已存在相同的任务：\\n日期：${task.patrol_date}\\n班次：${shiftName}\\n路线：${routeName}\\n执行人：${userName}\\n是否继续更新？`,\n\t\t\t\t\t\t\t\t\tshowCancel: true,\n\t\t\t\t\t\t\t\t\tconfirmText: '继续更新',\n\t\t\t\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\t\t\tresolve(res.confirm);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\treturn true;\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('检查重复任务失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '检查重复任务失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync submitForm() {\n\t\t\t\tif (!this.validateForm()) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 检查重复任务\n\t\t\t\tconst canProceed = await this.checkDuplicateTask();\n\t\t\t\tif (!canProceed) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.loading = true;\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconst rounds = await this.processRoundsData();\n\t\t\t\t\t\n\t\t\t\t\t// 保存处理后的轮次数据，以便在计算任务状态时使用\n\t\t\t\t\tthis.processedRounds = rounds;\n\t\t\t\t\t\n\t\t\t\t\t// 计算新的overall_stats\n\t\t\t\t\tconst totalPoints = rounds.reduce((sum, round) => sum + round.points.length, 0);\n\t\t\t\t\tconst completedPoints = rounds.reduce((sum, round) => {\n\t\t\t\t\t\treturn sum + round.points.filter(p => p.status === 1 || p.status === 2).length;\n\t\t\t\t\t}, 0);\n\t\t\t\t\tconst abnormalCount = rounds.reduce((sum, round) => {\n\t\t\t\t\t\treturn sum + round.points.filter(p => p.abnormal).length;\n\t\t\t\t\t}, 0);\n\t\t\t\t\t\n\t\t\t\t\tconst newOverallStats = {\n\t\t\t\t\t\ttotal_points: totalPoints,\n\t\t\t\t\t\tcompleted_points: completedPoints,\n\t\t\t\t\t\tmissed_points: totalPoints - completedPoints,\n\t\t\t\t\t\tcompletion_rate: totalPoints > 0 ? completedPoints / totalPoints : 0,\n\t\t\t\t\t\tabnormal_count: abnormalCount,\n\t\t\t\t\t\tlast_checkin_time: originalTask?.overall_stats?.last_checkin_time || null\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\tlet taskStatus;\n\t\t\t\t\t\n\t\t\t\t\tif (this.statusManuallyChanged) {\n\t\t\t\t\t\ttaskStatus = this.formData.status;\n\t\t\t\t\t\tconsole.log('使用手动选择的状态:', taskStatus);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tlet startTime = null;\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (this.selectedShift && this.selectedShift.start_time) {\n\t\t\t\t\t\t\tstartTime = calculateRoundTime(this.formData.date, this.selectedShift.start_time).toISOString();\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\ttaskStatus = this.calculateTaskStatus(startTime);\n\t\t\t\t\t\tconsole.log('使用自动计算的状态:', taskStatus);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconst shiftName = this.selectedShift ? this.selectedShift.name : '';\n\t\t\t\t\tconst routeName = this.selectedRoute ? this.selectedRoute.name : '';\n\t\t\t\t\t\n\t\t\t\t\tlet originalTask = null;\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst result = await PatrolApi.call({\n\t\t\t\t\t\t\tname: 'patrol-task',\n\t\t\t\t\t\t\taction: 'getTaskDetail',\n\t\t\t\t\t\t\tdata: { id: this.taskId }\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (result.code === 0 && result.data) {\n\t\t\t\t\t\t\toriginalTask = result.data.task || result.data;\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error('获取原始任务数据失败:', e);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tlet startTime, endTime;\n\t\t\t\t\tif (this.selectedShift) {\n\t\t\t\t\t\tif (this.selectedShift.start_time) {\n\t\t\t\t\t\t\tstartTime = calculateRoundTime(this.formData.date, this.selectedShift.start_time).toISOString();\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (this.selectedShift.end_time) {\n\t\t\t\t\t\t\tconst [endHours] = this.selectedShift.end_time.split(':').map(Number);\n\t\t\t\t\t\t\tconst [startHours] = this.selectedShift.start_time.split(':').map(Number);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tlet dayOffset = 0;\n\t\t\t\t\t\t\tif (this.selectedShift.across_day && endHours < startHours) {\n\t\t\t\t\t\t\t\tdayOffset = 1;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tconst endDateTime = calculateRoundTime(this.formData.date, this.selectedShift.end_time, dayOffset);\n\t\t\t\t\t\t\tendTime = endDateTime.toISOString();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 确保patrol_date只有日期部分，没有时间部分\n\t\t\t\t\tconst cleanDate = this.formData.date.split('T')[0]; // 提取YYYY-MM-DD部分，移除可能的时区信息\n\t\t\t\t\t\n\t\t\t\t\tlet requestData = {\n\t\t\t\t\t\tid: this.taskId,\n\t\t\t\t\t\tname: this.formData.name,\n\t\t\t\t\t\tarea: this.formData.area || '默认区域',\n\t\t\t\t\t\tpatrol_date: cleanDate,\n\t\t\t\t\t\tshift_id: this.formData.shift_id,\n\t\t\t\t\t\troute_id: this.formData.route_id,\n\t\t\t\t\t\tuser_id: this.formData.user_id,\n\t\t\t\t\t\tstatus: taskStatus,\n\t\t\t\t\t\tremark: this.formData.remark,\n\t\t\t\t\t\t\n\t\t\t\t\t\trounds_detail: rounds.map(round => ({\n\t\t\t\t\t\t\t...round,\n\t\t\t\t\t\t\tpoints: round.points.map(p => ({\n\t\t\t\t\t\t\t\t...p,\n\t\t\t\t\t\t\t\tqrcode_enabled: this.selectedRoute?.pointsDetail?.find(pd => pd._id === p.point_id)?.qrcode_enabled || false,\n\t\t\t\t\t\t\t\tqrcode_required: this.selectedRoute?.pointsDetail?.find(pd => pd._id === p.point_id)?.qrcode_required || false,\n\t\t\t\t\t\t\t\tqrcode_version: this.selectedRoute?.pointsDetail?.find(pd => pd._id === p.point_id)?.qrcode_version || 0\n\t\t\t\t\t\t\t}))\n\t\t\t\t\t\t})),\n\t\t\t\t\t\tenabled_rounds: originalTask?.enabled_rounds || rounds.map(round => round.round),\n\t\t\t\t\t\toverall_stats: newOverallStats,\n\t\t\t\t\t\tshift_detail: {\n\t\t\t\t\t\t\tname: shiftName,\n\t\t\t\t\t\t\tstart_time: this.selectedShift?.start_time || \"\",\n\t\t\t\t\t\t\tend_time: this.selectedShift?.end_time || \"\",\n\t\t\t\t\t\t\tacross_day: this.selectedShift?.across_day || false\n\t\t\t\t\t\t},\n\t\t\t\t\t\troute_detail: {\n\t\t\t\t\t\t\tname: routeName,\n\t\t\t\t\t\t\tpoints: this.selectedRoute?.pointsDetail?.map(p => ({\n\t\t\t\t\t\t\t\tpoint_id: p._id,\n\t\t\t\t\t\t\t\tname: p.name,\n\t\t\t\t\t\t\t\torder: p.order || 0,\n\t\t\t\t\t\t\t\tlocation: p.location,\n\t\t\t\t\t\t\t\trange: p.range,\n\t\t\t\t\t\t\t\tqrcode_enabled: p.qrcode_enabled || false,\n\t\t\t\t\t\t\t\tqrcode_required: p.qrcode_required || false,\n\t\t\t\t\t\t\t\tqrcode_version: p.qrcode_version || 0\n\t\t\t\t\t\t\t})) || []\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\tif (startTime) requestData.start_time = startTime;\n\t\t\t\t\tif (endTime) requestData.end_time = endTime;\n\t\t\t\t\t\n\t\t\t\t\t// console.log('提交任务数据:', JSON.stringify(requestData, null, 2));\n\t\t\t\t\t\n\t\t\t\t\tconst response = await uniCloud.callFunction({\n\t\t\t\t\t\tname: 'patrol-task',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\taction: 'updateTask',\n\t\t\t\t\t\t\tparams: requestData\n\t\t\t\t\t\t}\n\t\t\t\t\t}).then(res => res.result);\n\t\t\t\t\t\n\t\t\t\t\tif (response && response.code === 0) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '更新成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.$emit('refresh-task-list');\n\t\t\t\t\t\t\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(response.message || '更新失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('更新任务失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: error.message || '更新失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t}\n\t\t\t},\n\t\t\tvalidateForm() {\n\t\t\t\tif (!this.formData.name) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入任务名称',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (!this.formData.date) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请选择执行日期',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (!this.formData.shift_id) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请选择班次',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (!this.formData.route_id) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请选择巡检路线',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (!this.formData.user_id) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请选择执行人员',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn true;\n\t\t\t},\n\t\t\tgoBack() {\n\t\t\t\tuni.navigateBack();\n\t\t\t},\n\t\t\tasync processRoundsData() {\n\t\t\t\tif (!this.selectedShift || !this.selectedShift.rounds || !this.selectedRoute || !this.selectedRoute.pointsDetail) {\n\t\t\t\t\treturn [];\n\t\t\t\t}\n\n\t\t\t\tconst taskDate = this.formData.date;\n\t\t\t\tif (!taskDate) return [];\n\n\t\t\t\tconst points = this.selectedRoute.pointsDetail;\n\n\t\t\t\tlet originalTask = null;\n\t\t\t\ttry {\n\t\t\t\t\tconst result = await PatrolApi.call({\n\t\t\t\t\t\tname: 'patrol-task',\n\t\t\t\t\t\taction: 'getTaskDetail',\n\t\t\t\t\t\tdata: { id: this.taskId }\n\t\t\t\t\t});\n\n\t\t\t\t\tif (result.code === 0 && result.data) {\n\t\t\t\t\t\toriginalTask = result.data.task || result.data;\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('获取原始任务数据失败:', e);\n\t\t\t\t}\n\n\t\t\t\treturn this.selectedShift.rounds.map(round => {\n\t\t\t\t\tlet checkTime = round.time || '00:00';\n\t\t\t\t\tlet dayOffset = round.day_offset || 0;  // 使用班次中的day_offset\n\n\t\t\t\t\tlet originalRound = null;\n\t\t\t\t\tlet originalStats = null;\n\t\t\t\t\tif (originalTask && originalTask.rounds_detail) {\n\t\t\t\t\t\toriginalRound = originalTask.rounds_detail.find(r => r.round === round.round);\n\t\t\t\t\t\tif (originalRound) {\n\t\t\t\t\t\t\toriginalStats = originalRound.stats;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// 始终使用最新的班次时间计算\n\t\t\t\t\tconst isAcrossDay = this.selectedShift.across_day;\n\t\t\t\t\tconst shiftStartTime = this.selectedShift.start_time || '00:00';\n\n\t\t\t\t\tif (isAcrossDay) {\n\t\t\t\t\t\tconst [roundHours, roundMinutes] = checkTime.split(':').map(Number);\n\t\t\t\t\t\tconst [shiftStartHours, shiftStartMinutes] = shiftStartTime.split(':').map(Number);\n\n\t\t\t\t\t\tconst roundTimeMinutes = roundHours * 60 + roundMinutes;\n\t\t\t\t\t\tconst shiftStartTotalMinutes = shiftStartHours * 60 + shiftStartMinutes;\n\n\t\t\t\t\t\tif (roundTimeMinutes < shiftStartTotalMinutes) {\n\t\t\t\t\t\t\tdayOffset = 1;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tconst checkDateTime = calculateRoundTime(taskDate, checkTime, dayOffset);\n\t\t\t\t\tconst endDateTime = calculateEndTime(checkDateTime, round.duration || 60);\n\n\t\t\t\t\tconsole.log(`轮次${round.round}时间计算:`, {\n\t\t\t\t\t\t日期: taskDate,\n\t\t\t\t\t\t时间: checkTime,\n\t\t\t\t\t\t偏移: dayOffset,\n\t\t\t\t\t\t开始时间: checkDateTime.toISOString(),\n\t\t\t\t\t\t结束时间: endDateTime.toISOString()\n\t\t\t\t\t});\n\n\t\t\t\t\tconst roundPoints = points.map(point => {\n\t\t\t\t\t\tlet originalPoint = null;\n\t\t\t\t\t\tif (originalRound && originalRound.points) {\n\t\t\t\t\t\t\toriginalPoint = originalRound.points.find(p => p.point_id === point._id);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tpoint_id: point._id,\n\t\t\t\t\t\t\tname: point.name,\n\t\t\t\t\t\t\torder: point.order || 0,\n\t\t\t\t\t\t\tstatus: originalPoint ? originalPoint.status : 0,\n\t\t\t\t\t\t\tlocation: point.location,\n\t\t\t\t\t\t\trange: point.range,\n\t\t\t\t\t\t\tqrcode_enabled: point.qrcode_enabled || false,\n\t\t\t\t\t\t\tqrcode_required: point.qrcode_required || false,\n\t\t\t\t\t\t\tqrcode_version: point.qrcode_version || 0,\n\t\t\t\t\t\t\t...(originalPoint ? {\n\t\t\t\t\t\t\t\tcheckin_time: originalPoint.checkin_time,\n\t\t\t\t\t\t\t\tcheckin_location: originalPoint.checkin_location,\n\t\t\t\t\t\t\t\tabnormal: originalPoint.abnormal,\n\t\t\t\t\t\t\t\tabnormal_reason: originalPoint.abnormal_reason\n\t\t\t\t\t\t\t} : {})\n\t\t\t\t\t\t};\n\t\t\t\t\t});\n\n\t\t\t\t\tlet roundStatus = 0;\n\t\t\t\t\tif (originalRound) {\n\t\t\t\t\t\troundStatus = originalRound.status;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconst now = new Date();\n\t\t\t\t\t\tif (now >= endDateTime) {\n\t\t\t\t\t\t\troundStatus = 3;\n\t\t\t\t\t\t} else if (now >= checkDateTime) {\n\t\t\t\t\t\t\troundStatus = 1;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// 重新计算统计数据，始终基于当前的点位数量\n\t\t\t\t\tconst completedPoints = roundPoints.filter(p => p.status === 1 || p.status === 2).length;\n\t\t\t\t\tconst stats = {\n\t\t\t\t\t\ttotal_points: roundPoints.length,\n\t\t\t\t\t\tcompleted_points: completedPoints,\n\t\t\t\t\t\tmissed_points: roundPoints.length - completedPoints,\n\t\t\t\t\t\tcompletion_rate: roundPoints.length > 0 ? (completedPoints / roundPoints.length) : 0,\n\t\t\t\t\t\tabnormal_count: roundPoints.filter(p => p.abnormal).length,\n\t\t\t\t\t\tlast_checkin_time: originalStats?.last_checkin_time || null\n\t\t\t\t\t};\n\n\t\t\t\t\treturn {\n\t\t\t\t\t\tround: round.round,\n\t\t\t\t\t\tname: originalRound?.name || round.name || `轮次${round.round}`,\n\t\t\t\t\t\ttime: checkTime,\n\t\t\t\t\t\tcheck_time: checkDateTime.toISOString(),\n\t\t\t\t\t\tstart_time: checkDateTime.toISOString(),\n\t\t\t\t\t\tend_time: endDateTime.toISOString(),\n\t\t\t\t\t\tday_offset: dayOffset,\n\t\t\t\t\t\tduration: round.duration || 60,\n\t\t\t\t\t\tstatus: roundStatus,\n\t\t\t\t\t\tacross_day: this.selectedShift.across_day || false,\n\t\t\t\t\t\tpoints: roundPoints,\n\t\t\t\t\t\tstats: stats\n\t\t\t\t\t};\n\t\t\t\t});\n\t\t\t},\n\t\t\tasync refreshRoutePointsData() {\n\t\t\t\tif (!this.formData.route_id) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请先选择路线',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '刷新中...'\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 获取最新的路线点位数据\n\t\t\t\t\tconst res = await PatrolApi.call({\n\t\t\t\t\t\tname: 'patrol-route',\n\t\t\t\t\t\taction: 'getRouteDetail',\n\t\t\t\t\t\tdata: { \n\t\t\t\t\t\t\tparams: { \n\t\t\t\t\t\t\t\troute_id: this.formData.route_id,\n\t\t\t\t\t\t\t\twith_points: true,\n\t\t\t\t\t\t\t\twith_detail: true,\n\t\t\t\t\t\t\t\tinclude_point_details: true,\n\t\t\t\t\t\t\t\tforce_refresh: true\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\t\tconst routeData = res.data;\n\t\t\t\t\t\t// 更新路线点位数据\n\t\t\t\t\t\tthis.selectedRoute = {\n\t\t\t\t\t\t\t...this.selectedRoute,\n\t\t\t\t\t\t\tpointsDetail: routeData.pointsDetail || []\n\t\t\t\t\t\t};\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 标记点位数据已刷新\n\t\t\t\t\t\tthis.pointsRefreshed = true;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 显示成功提示\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '点位数据已更新',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(res.message || '获取最新点位数据失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('刷新点位数据失败:', e);\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '刷新点位数据失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tfilterUserOptions() {\n\t\t\t\tconst searchText = this.searchUserName.toLowerCase();\n\t\t\t\tif (!searchText) {\n\t\t\t\t\t// 如果搜索框为空，显示所有用户\n\t\t\t\t\tthis.filteredUsers = [...this.userOptions];\n\t\t\t\t} else {\n\t\t\t\t\tthis.filteredUsers = this.userOptions.filter(user => \n\t\t\t\t\t\tuser.nickname && user.nickname.toLowerCase().includes(searchText) && \n\t\t\t\t\t\t!user.nickname.startsWith('匿名') // 确保搜索结果也遵循匿名用户过滤规则\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t},\n\t\t\tselectUser(index) {\n\t\t\t\tconst user = this.filteredUsers[index];\n\t\t\t\tif (user && user._id) {\n\t\t\t\t\tthis.formData.user_id = user._id;\n\t\t\t\t\tthis.selectedUser = user;\n\t\t\t\t\t// 更新userIndex以保持与之前逻辑的兼容性\n\t\t\t\t\tconst originalIndex = this.userOptions.findIndex(u => u._id === user._id);\n\t\t\t\t\tif (originalIndex !== -1) {\n\t\t\t\t\t\tthis.userIndex = originalIndex;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.formData.user_id = '';\n\t\t\t\t\tthis.selectedUser = null;\n\t\t\t\t}\n\t\t\t\tthis.showUserSelect = false;\n\t\t\t},\n\t\t\thideUserSelect() {\n\t\t\t\tthis.showUserSelect = false;\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t.edit-task-container {\n\t\tmin-height: 100vh;\n\t\tbackground-color: #F2F3F5;\n\t\tpadding-bottom: 40rpx;\n\t}\n\t\n\t.header {\n\t\tpadding: 30rpx;\n\t\tbackground-color: #1677FF;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\t}\n\t\n\t.title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #FFFFFF;\n\t}\n\t\n\t.form-container {\n\t\tpadding: 20rpx;\n\t}\n\t\n\t.form-section {\n\t\tmargin: 10rpx;\n\t\tbackground-color: #FFFFFF;\n\t\tborder-radius: 12rpx;\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.card-header {\n\t\tpadding: 20rpx 30rpx;\n\t\tborder-bottom: 1rpx solid #EAEAEA;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t}\n\t\n\t.section-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #2C3E50;\n\t\tposition: relative;\n\t\tpadding-left: 20rpx;\n\t}\n\t\n\t.section-title:before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\twidth: 6rpx;\n\t\theight: 28rpx;\n\t\tbackground-color: #1677FF;\n\t\tborder-radius: 6rpx;\n\t}\n\t\n\t.form-item {\n\t\tpadding: 20rpx;\n\t\tborder-bottom: 1px solid #F5F5F5;\n\t\t\n\t\t&:last-child {\n\t\t\tborder-bottom: none;\n\t\t}\n\t}\n\t\n\t.form-label {\n\t\tdisplay: block;\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t\tmargin-bottom: 16rpx;\n\t\n\t\t&.required::before {\n\t\tcontent: '*';\n\t\tcolor: #FF4D4F;\n\t\t\tmargin-right: 8rpx;\n\t\t}\n\t}\n\t\n\t.required:after {\n\t\tcontent: '';\n\t\tdisplay: none;\n\t}\n\t\n\t.form-input {\n\t\twidth: 100%;\n\t\theight: 80rpx;\n\t\tborder: 1px solid #EEEEEE;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 0 20rpx;\n\t\tfont-size: 28rpx;\n\t\tbox-sizing: border-box;\n\t\tbackground-color: #F9F9F9;\n\t\ttransition: all 0.2s ease;\n\t\t\n\t\t&:focus {\n\t\t\tborder-color: #1677FF;\n\t\t\tbackground-color: #FFFFFF;\n\t\t}\n\t}\n\t\n\t.form-textarea {\n\t\twidth: 100%;\n\t\theight: 200rpx;\n\t\tborder: 1px solid #EEEEEE;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 20rpx;\n\t\tfont-size: 28rpx;\n\t\tbox-sizing: border-box;\n\t\tbackground-color: #F9F9F9;\n\t\ttransition: all 0.2s ease;\n\t\t\n\t\t&:focus {\n\t\t\tborder-color: #1677FF;\n\t\t\tbackground-color: #FFFFFF;\n\t\t}\n\t}\n\t\n\t.form-picker {\n\t\twidth: 100%;\n\t}\n\t\n\t.picker-value {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\twidth: 100%;\n\t\theight: 80rpx;\n\t\tborder: 1px solid #EEEEEE;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 0 20rpx;\n\t\tfont-size: 28rpx;\n\t\tbox-sizing: border-box;\n\t\tbackground-color: #F9F9F9;\n\t\ttransition: all 0.2s ease;\n\t\t\n\t\t&:active {\n\t\t\tborder-color: #1677FF;\n\t\t\tbackground-color: #FFFFFF;\n\t\t}\n\t}\n\t\n\t.shift-info {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 10rpx;\n\t}\n\t\n\t.round-tags {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: 12rpx;\n\t\tmargin-top: 8rpx;\n\t}\n\t\n\t.round-tag {\n\t\tbackground-color: #E6F7FF;\n\t\tcolor: #1677FF;\n\t\tpadding: 8rpx 16rpx;\n\t\tborder-radius: 6rpx;\n\t\tfont-size: 24rpx;\n\t\tmargin-right: 10rpx;\n\t\tmargin-bottom: 10rpx;\n\t\tborder: 1px solid rgba(22, 119, 255, 0.2);\n\t}\n\t\n\t.points-list {\n\t\tborder: 1px solid #EEEEEE;\n\t\tborder-radius: 8rpx;\n\t\tmax-height: 300rpx;\n\t\toverflow-y: auto;\n\t\tbackground-color: #F9F9F9;\n\t}\n\t\n\t.point-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 20rpx;\n\t\tborder-bottom: 1px solid rgba(238, 238, 238, 0.6);\n\t\t\n\t\t&:active {\n\t\t\tbackground-color: #F2F7FF;\n\t\t}\n\t}\n\t\n\t.point-item:last-child {\n\t\tborder-bottom: none;\n\t}\n\t\n\t.point-index {\n\t\twidth: 40rpx;\n\t\theight: 40rpx;\n\t\tborder-radius: 50%;\n\t\tbackground-color: #1677FF;\n\t\tcolor: #FFFFFF;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tfont-size: 24rpx;\n\t\tmargin-right: 20rpx;\n\t\tflex-shrink: 0;\n\t}\n\t\n\t.point-name {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tflex: 1;\n\t\twhite-space: nowrap;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t}\n\t\n\t.point-range {\n\t\tfont-size: 24rpx;\n\t\tcolor: #1677FF;\n\t\tbackground-color: #E6F7FF;\n\t\tpadding: 4rpx 12rpx;\n\t\tborder-radius: 999rpx;\n\t\tmargin-left: 10rpx;\n\t\tflex-shrink: 0;\n\t}\n\t\n\t.user-info {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbackground-color: #F9F9F9;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 16rpx;\n\t\tmargin-top: 8rpx;\n\t\tborder: 1px solid rgba(238, 238, 238, 0.8);\n\t}\n\t\n\t.user-avatar {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\tborder-radius: 50%;\n\t\tmargin-right: 20rpx;\n\t\tbox-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\n\t}\n\t\n\t.user-details {\n\t\tflex: 1;\n\t}\n\t\n\t.user-name {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t\tfont-weight: bold;\n\t\tdisplay: block;\n\t\tmargin-bottom: 8rpx;\n\t}\n\t\n\t.user-role {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999999;\n\t\tdisplay: block;\n\t}\n\t\n\t.status-text {\n\t\tfont-size: 28rpx;\n\t}\n\t\n\t.status-0 {\n\t\tcolor: #FAAD14;\n\t}\n\t\n\t.status-1 {\n\t\tcolor: #1677FF;\n\t}\n\t\n\t.status-2 {\n\t\tcolor: #52C41A;\n\t}\n\t\n\t.status-3 {\n\t\tcolor: #F5222D;\n\t}\n\t\n\t.status-4 {\n\t\tcolor: #999999;\n\t}\n\t\n\t.status-picker {\n\t\twidth: 100%;\n\t}\n\t\n\t.small-icon {\n\t\ttransform: scale(0.8);\n\t}\n\t\n\t.submit-section {\n\t\tmargin: 40rpx 20rpx;\n\t\tpadding: 20rpx 30rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tjustify-content: space-between;\n\t\tgap: 20rpx;\n\t\tbackground-color: #FFFFFF;\n\t\tborder-radius: 12rpx;\n\t\tbox-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.primary-btn, .cancel-btn {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tborder-radius: 40rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 500;\n\t\ttransition: all 0.3s ease;\n\t\t\n\t\t&:after {\n\t\t\tborder: none;\n\t\t}\n\t\t\n\t\t&:active {\n\t\t\ttransform: scale(0.95);\n\t\t\topacity: 0.9;\n\t\t}\n\t}\n\t\n\t.primary-btn {\n\t\tbackground-color: #1677FF;\n\t\tcolor: #FFFFFF !important;\n\t\t\n\t\t&[loading] {\n\t\t\topacity: 0.8;\n\t\t}\n\t}\n\t\n\t.cancel-btn {\n\t\tbackground-color: #F5F5F5;\n\t\tcolor: #666666;\n\t\tborder: 1rpx solid #EEEEEE;\n\t}\n\t\n\t/* 图标字体 */\n\t.iconfont {\n\t\tfont-family: 'iconfont';\n\t}\n\t\n\t.icon-calendar:before {\n\t\tcontent: '\\e6dc';\n\t\tcolor: #1677FF;\n\t}\n\t\n\t.icon-down:before {\n\t\tcontent: '\\e61a';\n\t\tcolor: #999999;\n\t}\n\t\n\t/* 跨天班次标记样式 */\n\t.cross-day-tag {\n\t\tfont-size: 22rpx;\n\t\tcolor: #FA8C16;\n\t\tbackground-color: rgba(250, 140, 22, 0.1);\n\t\tpadding: 2rpx 8rpx;\n\t\tborder-radius: 4rpx;\n\t\tmargin-left: 8rpx;\n\t}\n\t\n\t/* 次日轮次标记样式 */\n\t.next-day-badge {\n\t\tdisplay: inline-block;\n\t\tfont-size: 22rpx;\n\t\tpadding: 2rpx 8rpx;\n\t\tbackground-color: #FA8C16;\n\t\tcolor: #FFFFFF;\n\t\tborder-radius: 20rpx;\n\t\tmargin-left: 6rpx;\n\t}\n\t\n\t.shift-time-info {\n\t\tmargin-bottom: 12rpx;\n\t}\n\t\n\t.time-range {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 8rpx;\n\t\tfont-size: 26rpx;\n\t\tcolor: #666666;\n\t\tpadding: 8rpx 0;\n\t}\n\t\n\t.points-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 10rpx;\n\t}\n\t\n\t.refresh-points-btn {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 8rpx;\n\t\tfont-size: 24rpx;\n\t\tcolor: #1677FF;\n\t}\n\t\n\t.points-info {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 8rpx;\n\t\tmargin-top: 10rpx;\n\t}\n\t\n\t.info-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666666;\n\t}\n\t\n\t.success {\n\t\tcolor: #52C41A;\n\t}\n\t\n\t/* 确保加载状态按钮的文字和图标为白色 */\n\tbutton[loading]::before {\n\t\tcolor: #FFFFFF !important;\n\t}\n\t\n\tbutton[loading]::after {\n\t\tborder-color: #FFFFFF transparent #FFFFFF transparent !important;\n\t}\n\t\n\t.custom-select {\n\t\tposition: relative;\n\t}\n\t\n\t.user-select-modal {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tz-index: 9999;\n\t}\n\t\n\t.user-select-popup {\n\t\twidth: 90%;\n\t\tmax-width: 650rpx;\n\t\tbackground-color: #FFFFFF;\n\t\tborder-radius: 12rpx;\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);\n\t}\n\t\n\t/* #ifdef H5 */\n\t/* H5 端的响应式样式 */\n\t@media screen and (min-width: 1200px) {\n\t\t.user-select-popup {\n\t\t\tmax-width: 600px;\n\t\t}\n\t}\n\n\t@media screen and (min-width: 992px) and (max-width: 1199px) {\n\t\t.user-select-popup {\n\t\t\tmax-width: 560px;\n\t\t}\n\t}\n\n\t@media screen and (min-width: 768px) and (max-width: 991px) {\n\t\t.user-select-popup {\n\t\t\tmax-width: 500px;\n\t\t}\n\t}\n\n\t@media screen and (min-width: 576px) and (max-width: 767px) {\n\t\t.user-select-popup {\n\t\t\tmax-width: 450px;\n\t\t}\n\t}\n\n\t@media screen and (max-width: 575px) {\n\t\t.user-select-popup {\n\t\t\twidth: 92%;\n\t\t\tmax-width: none;\n\t\t}\n\t}\n\t/* #endif */\n\n\t.user-select-container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\theight: 100%;\n\t\tmax-height: 80vh;\n\t}\n\t\n\t.user-select-header {\n\t\tpadding: 20rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #2C3E50;\n\t\tborder-bottom: 1px solid #EEEEEE;\n\t\ttext-align: center;\n\t}\n\t\n\t.user-search-header {\n\t\tpadding: 20rpx;\n\t\tborder-bottom: 1px solid #EEEEEE;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbackground-color: #F9F9F9;\n\t}\n\t\n\t.user-search-input {\n\t\tflex: 1;\n\t\theight: 70rpx;\n\t\tfont-size: 28rpx;\n\t\tpadding: 0 10rpx;\n\t\tbackground-color: transparent;\n\t}\n\t\n\t.user-select-list {\n\t\tflex: 1;\n\t\toverflow-y: auto;\n\t}\n\t\n\t/* #ifdef MP-WEIXIN */\n\t/* 在微信小程序环境下隐藏滚动条 */\n\t.user-select-list::-webkit-scrollbar {\n\t\tdisplay: none;\n\t\twidth: 0;\n\t\theight: 0;\n\t\tbackground: transparent;\n\t}\n\n\t.user-select-list {\n\t\tscrollbar-width: none; /* Firefox */\n\t\t-ms-overflow-style: none; /* IE and Edge */\n\t}\n\t/* #endif */\n\n\t/* #ifndef MP-WEIXIN */\n\t/* 非微信小程序环境下显示滚动条 */\n\t.user-select-list::-webkit-scrollbar {\n\t\twidth: 6px;\n\t\theight: 6px;\n\t}\n\n\t.user-select-list::-webkit-scrollbar-thumb {\n\t\tbackground: rgba(0, 0, 0, 0.2);\n\t\tborder-radius: 3px;\n\t}\n\n\t.user-select-list::-webkit-scrollbar-thumb:hover {\n\t\tbackground: rgba(0, 0, 0, 0.3);\n\t}\n\n\t.user-select-list {\n\t\tscrollbar-width: thin;\n\t\tscrollbar-color: rgba(0, 0, 0, 0.2) transparent;\n\t}\n\t/* #endif */\n\n\t.user-select-item {\n\t\tpadding: 16rpx 20rpx;\n\t\tborder-bottom: 1px solid #F5F5F5;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t}\n\t\n\t.user-select-item:active {\n\t\tbackground-color: #F9F9F9;\n\t}\n\t\n\t.user-item-content {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\twidth: 100%;\n\t}\n\t\n\t.user-avatar-small {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\tborder-radius: 50%;\n\t\tmargin-right: 16rpx;\n\t\tborder: 1px solid #EEEEEE;\n\t}\n\t\n\t.user-item-details {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\t\n\t.user-item-name {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t\tfont-weight: 500;\n\t}\n\t\n\t.user-item-role {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999999;\n\t\tmargin-top: 4rpx;\n\t}\n\t\n\t.select-radio {\n\t\tmargin-left: 10rpx;\n\t}\n\t\n\t.user-select-footer {\n\t\tpadding: 20rpx;\n\t\tborder-top: 1px solid #EEEEEE;\n\t}\n\t\n\t.user-select-close {\n\t\theight: 80rpx;\n\t\tbackground-color: #1677FF;\n\t\tcolor: #FFFFFF;\n\t\tborder-radius: 40rpx;\n\t\tfont-size: 28rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\t\n\t\t&:after {\n\t\t\tborder: none;\n\t\t}\n\t}\n</style>", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775842565\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}