{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s_pkg/public-area-manage.vue?8cbc", "webpack:///D:/Xwzc/pages/6s_pkg/public-area-manage.vue?3e51", "webpack:///D:/Xwzc/pages/6s_pkg/public-area-manage.vue?e814", "webpack:///D:/Xwzc/pages/6s_pkg/public-area-manage.vue?2b35", "uni-app:///pages/6s_pkg/public-area-manage.vue", "webpack:///D:/Xwzc/pages/6s_pkg/public-area-manage.vue?fa3c", "webpack:///D:/Xwzc/pages/6s_pkg/public-area-manage.vue?c8e7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "areaList", "loading", "saving", "scheduleSaving", "isEditing", "currentArea", "currentScheduleArea", "formData", "location", "description", "status", "scheduleData", "scheduled_day", "start_date", "statusOptions", "value", "label", "weekDayOptions", "statusIndex", "scheduleWeekIndex", "computed", "enabled<PERSON><PERSON>s", "scheduledAreas", "todayAreas", "onLoad", "methods", "loadAreaList", "action", "type", "result", "area", "Number", "next_clean_date", "console", "uni", "title", "icon", "duration", "createDefaultAreas", "id", "last_clean_date", "created_at", "updated_at", "calculateNextCleanDate", "nextDate", "showAddArea", "editArea", "deleteArea", "content", "confirmText", "confirmColor", "success", "performDelete", "index", "errorMessage", "showCancel", "mask", "setSchedule", "scheduledDay", "getNextMonday", "nextMonday", "onStatusChange", "onScheduleWeekChange", "onStartDateChange", "submitForm", "requestData", "newArea", "submitSchedule", "isNaN", "updatedArea", "clearSchedule", "validateForm", "resetForm", "closeForm", "closeSchedulePopup", "getStatusText", "hasValidSchedule", "getWeekDayText", "isToday", "formatDate", "tomorrow"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,yBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2I;AAC3I;AACsE;AACL;AACsC;;;AAGvG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,wFAAM;AACR,EAAE,yGAAM;AACR,EAAE,kHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjGA;AAAA;AAAA;AAAA;AAA4mB,CAAgB,soBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACqQhoB;AAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAT;QACAU;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;MACA;MACAC,gBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC,iBACA;QAAAF;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAE;MACAC;IACA;EACA;EAEAC;IACAC;MACA;QAAA;MAAA;IACA;IACAC;MACA;QAAA;MAAA;IACA;IACAC;MACA;MACA;MACA;QAAA;MAAA;IACA;EAEA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;;gBAEA;gBAAA;gBAAA,OACA;kBACAC;kBACA5B;oBACA6B;kBACA;gBACA;cAAA;gBALAC;gBAOA;gBACA;kBAAA,uCACAC;oBACA;oBACAlB,iFACAmB,6BACA;oBACAC,8EACAF;sBACAlB,iFACAmB,6BACA;oBAAA;kBACA;gBAAA,CACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAE;gBACA;gBACA;kBACAC;oBACAC;oBACAC;oBACAC;kBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MACA,aACA;QACAC;QACAzC;QACAU;UAAAsB;QAAA;QACArB;QACAC;QACAkB;QACAhB;QAAA;QACAC;QACA2B;QACAC;QACAC;MACA,GACA;QACAH;QACAzC;QACAU;UAAAsB;QAAA;QACArB;QACAC;QACAkB;QACAhB;QAAA;QACAC;QACA2B;QACAC;QACAC;MACA,GACA;QACAH;QACAzC;QACAU;UAAAsB;QAAA;QACArB;QACAC;QACAkB;QACAhB;QAAA;QACAC;QACA2B;QACAC;QACAC;MACA,GACA;QACAH;QACAzC;QACAU;UAAAsB;QAAA;QACArB;QACAC;QACAkB;QACAhB;QAAA;QACAC;QACA2B;QACAC;QACAC;MACA,EACA;;MAEA;MACA;QAAA,uCACAZ;UACAE;QAAA;MAAA,CACA;IACA;IAEA;IACAW;MACA;QACA;MACA;MAEA;MACA;;MAEA;MACA;MACA;QACAC;MACA;;MAEA;MACA;QACA;UAAA;UACAA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACAhD;QACAU;QACAC;QACAC;MACA;;MAEA;MACA;QAAA;MAAA;MAEA;IACA;IAEA;IACAqC;MAAA;MACAb;QACAC;QACAa;QACAC;QACAC;QACAC;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;kBACAzB;kBACA5B;oBACAwC;kBACA;gBACA;cAAA;gBAEA;gBACAc;kBAAA;gBAAA;gBACA;kBACA;gBACA;gBAEAnB;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAH;gBACA;gBACA;kBACAqB;kBACA;oBACApB;sBACAC;sBACAa;sBACAO;sBACAN;oBACA;kBACA;oBACAf;sBACAC;sBACAC;sBACAC;sBACAmB;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;;MAEA;MACA;MACA;QACAC;QACA;QACA;UACAA;QACA;MACA;MAEA;QACA9C;QACAC;MACA;;MAEA;MACA;QAAA;MAAA;MACA;MAEA;IACA;IAIA;IACA8C;MACA;MACA;MACA;MACAC;MACA;IACA;IAIA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACA;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAKA;gBAEAC;kBACAnE;kBACAU;oBAAAsB;kBAAA;kBACArB;kBACAC;kBACAkB;gBACA;gBAAA,KAGA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAD;kBACA5B;oBACAwC;kBAAA,GACA0B;gBAEA;cAAA;gBANApC;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OASA;kBACAF;kBACA5B;gBACA;cAAA;gBAHA8B;cAAA;gBAMA;kBACA;kBACAwB;oBAAA;kBAAA;kBACA;oBACA,uEACA,yBACAxB;sBACAG;oBAAA,EACA;kBACA;gBACA;kBACA;kBACAkC,0CACArC;oBACAG;kBAAA;kBAEA;gBACA;gBAEAE;kBACAC;kBACAC;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAH;gBACA;gBACA;kBACAqB;kBACA;oBACApB;sBACAC;sBACAa;sBACAO;sBACAN;oBACA;kBACA;oBACAf;sBACAC;sBACAC;sBACAC;sBACAmB;oBACA;kBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAW;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACAjC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBACAsB;gBAAA,MACAU;kBAAA;kBAAA;gBAAA;gBACAlC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAKA;;gBAEA;gBAAA;gBAAA,OACA;kBACAT;kBACA5B;oBACAwC;oBACA3B;oBAAA;oBACAC;kBACA;gBACA;cAAA;gBAEA;gBACAwC;kBAAA;gBAAA;gBACA;kBACAgB,8CACA;oBACAzD;oBAAA;oBACAC;oBACA6B;kBAAA;kBAEA2B;kBACA;gBACA;gBAEAnC;kBACAC;kBACAC;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAH;gBACA;gBACA;kBACAqB;kBACA;oBACApB;sBACAC;sBACAa;sBACAO;sBACAN;oBACA;kBACA;oBACAf;sBACAC;sBACAC;sBACAC;oBACA;kBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;kBACA3C;kBACA5B;oBACAwC;oBACA3B;oBACAC;kBACA;gBACA;cAAA;gBAEA;gBACAwC;kBAAA;gBAAA;gBACA;kBACA,yDACA;oBACAzC;oBACAC;oBACAmB;oBACAU;kBAAA,EACA;gBACA;gBAEAR;kBACAC;kBACAC;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAH;gBACA;gBACA;kBACAqB;kBACA;oBACApB;sBACAC;sBACAa;sBACAO;sBACAN;oBACA;kBACA;oBACAf;sBACAC;sBACAC;sBACAC;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAkC;MACA;QACArC;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MACA;IACA;IAEA;IACAoC;MACA;QACA1E;QACAU;QACAC;QACAC;MACA;MACA;IACA;IAEA;IACA+D;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;QACA9D;QACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACA8D;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA,sCACA9C,oCACA,0CACAA,2BACAA;IACA;IAEA;IACA+C;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACAC;MAEA;MACA;MACA;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACh7BA;AAAA;AAAA;AAAA;AAA+qC,CAAgB,qpCAAG,EAAC,C;;;;;;;;;;;ACAnsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/public-area-manage.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/public-area-manage.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./public-area-manage.vue?vue&type=template&id=2ae3b0bd&scoped=true&\"\nvar renderjs\nimport script from \"./public-area-manage.vue?vue&type=script&lang=js&\"\nexport * from \"./public-area-manage.vue?vue&type=script&lang=js&\"\nimport style0 from \"./public-area-manage.vue?vue&type=style&index=0&id=2ae3b0bd&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2ae3b0bd\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/public-area-manage.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./public-area-manage.vue?vue&type=template&id=2ae3b0bd&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.loading ? _vm.areaList.length : null\n  var g1 = !_vm.loading ? _vm.enabledAreas.length : null\n  var g2 = !_vm.loading ? _vm.scheduledAreas.length : null\n  var g3 = !_vm.loading ? _vm.todayAreas.length : null\n  var g4 = !_vm.loading ? _vm.areaList.length : null\n  var l0 =\n    !_vm.loading && g4 > 0\n      ? _vm.__map(_vm.areaList, function (area, index) {\n          var $orig = _vm.__get_orig(area)\n          var m0 = _vm.getStatusText(area.status)\n          var m1 = _vm.hasValidSchedule(area)\n          var m2 = m1 ? _vm.getWeekDayText(area.scheduled_day) : null\n          var m3 = _vm.hasValidSchedule(area)\n          var m4 = m3 ? _vm.isToday(area.next_clean_date) : null\n          var m5 = m3 ? _vm.formatDate(area.next_clean_date) : null\n          var m6 =\n            m3 && area.last_clean_date\n              ? _vm.formatDate(area.last_clean_date)\n              : null\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            m2: m2,\n            m3: m3,\n            m4: m4,\n            m5: m5,\n            m6: m6,\n          }\n        })\n      : null\n  var g5 =\n    !_vm.loading && !(g4 > 0) ? !_vm.loading && _vm.areaList.length === 0 : null\n  var m7 =\n    _vm.currentScheduleArea &&\n    _vm.scheduleData.scheduled_day !== null &&\n    _vm.scheduleData.start_date\n      ? _vm.getWeekDayText(_vm.scheduleData.scheduled_day)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        l0: l0,\n        g5: g5,\n        m7: m7,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./public-area-manage.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./public-area-manage.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"page-container\">\r\n    <!-- 页面头部 -->\r\n    <view class=\"header\">\r\n      <view class=\"header-title\">公共责任区管理</view>\r\n      <view class=\"header-subtitle\">管理需要轮班清洁的公共区域</view>\r\n    </view>\r\n\r\n    <!-- 统计卡片 -->\r\n    <view class=\"stats-card\">\r\n      <!-- 加载状态 -->\r\n      <view v-if=\"loading\" class=\"stats-loading\">\r\n        <view class=\"loading-content\">\r\n          <uni-icons type=\"spinner-cycle\" size=\"40\" color=\"#007AFF\"></uni-icons>\r\n          <text class=\"loading-text\">加载统计数据中...</text>\r\n        </view>\r\n      </view>\r\n      <!-- 正常统计 -->\r\n      <template v-else>\r\n        <view class=\"stat-item\">\r\n          <view class=\"stat-number\">{{ areaList.length }}</view>\r\n          <view class=\"stat-label\">总公共区</view>\r\n        </view>\r\n        <view class=\"stat-item\">\r\n          <view class=\"stat-number\">{{ enabledAreas.length }}</view>\r\n          <view class=\"stat-label\">启用中</view>\r\n        </view>\r\n        <view class=\"stat-item\">\r\n          <view class=\"stat-number\">{{ scheduledAreas.length }}</view>\r\n          <view class=\"stat-label\">已排班</view>\r\n        </view>\r\n        <view class=\"stat-item\">\r\n          <view class=\"stat-number\">{{ todayAreas.length }}</view>\r\n          <view class=\"stat-label\">今日清扫</view>\r\n        </view>\r\n      </template>\r\n    </view>\r\n\r\n    <!-- 操作按钮组 -->\r\n    <view class=\"action-bar\">\r\n      <button class=\"action-btn primary\" @click=\"showAddArea\">\r\n        <uni-icons type=\"plus\" size=\"16\" color=\"#FFFFFF\" />\r\n        <text>新增公共区</text>\r\n      </button>\r\n    </view>\r\n\r\n    <!-- 公共区列表 -->\r\n    <!-- 加载状态 -->\r\n    <view v-if=\"loading\" class=\"list-loading\">\r\n      <view class=\"loading-content\">\r\n        <uni-icons type=\"spinner-cycle\" size=\"40\" color=\"#007AFF\"></uni-icons>\r\n        <text class=\"loading-text\">加载公共区数据中...</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 正常列表 -->\r\n    <view v-else-if=\"areaList.length > 0\" class=\"area-list\">\r\n      <view \r\n        class=\"area-item\"\r\n        v-for=\"(area, index) in areaList\" \r\n        :key=\"index\"\r\n      >\r\n        <view class=\"area-main\">\r\n          <view class=\"area-header\">\r\n            <view class=\"area-name\">{{ area.name }}</view>\r\n            <view class=\"area-status\" :class=\"[`status-${area.status}`]\">\r\n              {{ getStatusText(area.status) }}\r\n            </view>\r\n          </view>\r\n          <view class=\"area-info\">\r\n            <view class=\"info-item\">\r\n              <uni-icons type=\"location\" size=\"14\" color=\"#8E8E93\" />\r\n              <text>{{ (area.location && area.location.area) || area.location || '未设置位置' }}</text>\r\n            </view>\r\n            <view class=\"info-item\">\r\n              <uni-icons type=\"calendar\" size=\"14\" color=\"#8E8E93\" />\r\n              <text v-if=\"hasValidSchedule(area)\">每{{ getWeekDayText(area.scheduled_day) }}清扫</text>\r\n              <text v-else class=\"unscheduled-text\">未设置清扫日程</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"area-description\" v-if=\"area.description\">\r\n            {{ area.description }}\r\n          </view>\r\n          <view class=\"area-schedule\">\r\n            <view v-if=\"hasValidSchedule(area)\" class=\"schedule-info\">\r\n              <view class=\"next-clean\">\r\n                <text class=\"next-label\">下次清扫：</text>\r\n                <text class=\"next-date\" :class=\"{ 'today': isToday(area.next_clean_date) }\">\r\n                  {{ formatDate(area.next_clean_date) }}\r\n                </text>\r\n              </view>\r\n              <view class=\"last-clean\" v-if=\"area.last_clean_date\">\r\n                <text class=\"last-label\">上次完成：</text>\r\n                <text class=\"last-date\">{{ formatDate(area.last_clean_date) }}</text>\r\n              </view>\r\n            </view>\r\n            <view v-else class=\"no-schedule-info\">\r\n              <uni-icons type=\"calendar\" size=\"16\" color=\"#C7C7CC\" />\r\n              <text>暂未设置清扫日程</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view class=\"area-actions\">\r\n          <button class=\"action-icon-btn schedule\" @click.stop=\"setSchedule(area)\">\r\n            <uni-icons type=\"calendar\" size=\"16\" color=\"#34C759\" />\r\n          </button>\r\n          <button class=\"action-icon-btn edit\" @click.stop=\"editArea(area)\">\r\n            <uni-icons type=\"compose\" size=\"16\" color=\"#007AFF\" />\r\n          </button>\r\n          <button class=\"action-icon-btn delete\" @click.stop=\"deleteArea(area)\">\r\n            <uni-icons type=\"trash\" size=\"16\" color=\"#FF3B30\" />\r\n          </button>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 空状态 -->\r\n    <p-empty-state\r\n      v-else-if=\"!loading && areaList.length === 0\"\r\n      type=\"area\"\r\n      text=\"暂无公共责任区\"\r\n      description=\"点击上方按钮创建第一个公共责任区\"\r\n    ></p-empty-state>\r\n\r\n    <!-- 新增/编辑弹窗 -->\r\n    <uni-popup ref=\"areaFormPopup\" type=\"center\" :mask-click=\"false\">\r\n      <view class=\"form-popup\">\r\n        <view class=\"form-header\">\r\n          <view class=\"form-title\">{{ isEditing ? '编辑' : '新增' }}公共责任区</view>\r\n          <button class=\"close-btn\" @click=\"closeForm\">\r\n            <uni-icons type=\"close\" size=\"20\" color=\"#8E8E93\" />\r\n          </button>\r\n        </view>\r\n        \r\n        <view class=\"form-content\">\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label required\">责任区名称</text>\r\n            <input \r\n              class=\"form-input\" \r\n              type=\"text\" \r\n              v-model=\"formData.name\"\r\n              placeholder=\"请输入公共责任区名称\"\r\n              maxlength=\"50\"\r\n            />\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">所在位置</text>\r\n            <input \r\n              class=\"form-input\" \r\n              type=\"text\" \r\n              v-model=\"formData.location\"\r\n              placeholder=\"请输入具体位置\"\r\n              maxlength=\"100\"\r\n            />\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">责任区描述</text>\r\n            <textarea \r\n              class=\"form-textarea\" \r\n              v-model=\"formData.description\"\r\n              placeholder=\"请输入责任区详细描述\"\r\n              maxlength=\"200\"\r\n              :show-count=\"true\"\r\n            ></textarea>\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">状态</text>\r\n            <picker \r\n              :range=\"statusOptions\" \r\n              range-key=\"label\" \r\n              :value=\"statusIndex\"\r\n              @change=\"onStatusChange\"\r\n              class=\"form-picker\"\r\n            >\r\n              <view class=\"picker-value\">\r\n                <text>{{ statusOptions[statusIndex].label }}</text>\r\n                <uni-icons type=\"right\" size=\"16\" color=\"#C7C7CC\" />\r\n              </view>\r\n            </picker>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-footer\">\r\n          <button class=\"form-btn cancel\" @click=\"closeForm\">取消</button>\r\n          <button class=\"form-btn submit\" @click=\"submitForm\" :loading=\"saving\">\r\n            {{ isEditing ? '保存' : '创建' }}\r\n          </button>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n\r\n    <!-- 排班设置弹窗 -->\r\n    <uni-popup ref=\"schedulePopup\" type=\"center\" :mask-click=\"false\">\r\n      <view class=\"schedule-popup\">\r\n        <view class=\"schedule-header\">\r\n          <view class=\"schedule-title\">设置清扫日程</view>\r\n          <button class=\"close-btn\" @click=\"closeSchedulePopup\">\r\n            <uni-icons type=\"close\" size=\"20\" color=\"#8E8E93\" />\r\n          </button>\r\n        </view>\r\n        \r\n        <view class=\"schedule-content\" v-if=\"currentScheduleArea\">\r\n          <view class=\"area-name-display\">{{ currentScheduleArea.name }}</view>\r\n          \r\n          <view class=\"schedule-item\">\r\n            <text class=\"schedule-label\">清扫日期</text>\r\n            <picker \r\n              :range=\"weekDayOptions\" \r\n              range-key=\"label\" \r\n              :value=\"scheduleWeekIndex\"\r\n              @change=\"onScheduleWeekChange\"\r\n              class=\"schedule-picker\"\r\n            >\r\n              <view class=\"schedule-picker-value\">\r\n                <text>{{ weekDayOptions[scheduleWeekIndex].label }}</text>\r\n                <uni-icons type=\"right\" size=\"16\" color=\"#C7C7CC\" />\r\n              </view>\r\n            </picker>\r\n          </view>\r\n          \r\n          <view class=\"schedule-item\">\r\n            <text class=\"schedule-label\">开始日期</text>\r\n            <picker \r\n              mode=\"date\" \r\n              :value=\"scheduleData.start_date\" \r\n              @change=\"onStartDateChange\"\r\n              class=\"schedule-picker\"\r\n            >\r\n              <view class=\"schedule-picker-value\">\r\n                <text>{{ scheduleData.start_date || '请选择开始日期' }}</text>\r\n                <uni-icons type=\"calendar\" size=\"16\" color=\"#C7C7CC\" />\r\n              </view>\r\n            </picker>\r\n          </view>\r\n          \r\n          <view class=\"schedule-preview\" v-if=\"scheduleData.scheduled_day !== null && scheduleData.start_date\">\r\n            <view class=\"preview-title\">预览</view>\r\n            <view class=\"preview-info\">\r\n              <text>从 {{ scheduleData.start_date }} 开始，每{{ getWeekDayText(scheduleData.scheduled_day) }}进行清扫</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"schedule-footer\">\r\n          <button class=\"schedule-btn cancel\" @click=\"closeSchedulePopup\">取消</button>\r\n          <button class=\"schedule-btn clear\" @click=\"clearSchedule\" v-if=\"currentScheduleArea && currentScheduleArea.scheduled_day !== null\">\r\n            清除排班\r\n          </button>\r\n          <button class=\"schedule-btn submit\" @click=\"submitSchedule\" :loading=\"scheduleSaving\">\r\n            保存排班\r\n          </button>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { callCloudFunction } from '@/utils/auth.js';\r\n\r\nexport default {\r\n  name: 'PublicAreaManage',\r\n  data() {\r\n    return {\r\n      areaList: [],\r\n      loading: false,\r\n      saving: false,\r\n      scheduleSaving: false,\r\n      isEditing: false,\r\n      currentArea: null,\r\n      currentScheduleArea: null,\r\n      formData: {\r\n        name: '',\r\n        location: '',\r\n        description: '',\r\n        status: 'active'\r\n      },\r\n      scheduleData: {\r\n        scheduled_day: null,\r\n        start_date: ''\r\n      },\r\n      statusOptions: [\r\n        { value: 'active', label: '启用' },\r\n        { value: 'inactive', label: '禁用' }\r\n      ],\r\n      weekDayOptions: [\r\n        { value: null, label: '请选择清扫日期' },\r\n        { value: 1, label: '每周一' },\r\n        { value: 2, label: '每周二' },\r\n        { value: 3, label: '每周三' },\r\n        { value: 4, label: '每周四' },\r\n        { value: 5, label: '每周五' },\r\n        { value: 6, label: '每周六' },\r\n        { value: 0, label: '每周日' }\r\n      ],\r\n      statusIndex: 0,\r\n      scheduleWeekIndex: 0\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    enabledAreas() {\r\n      return this.areaList.filter(area => area.status === 'active');\r\n    },\r\n    scheduledAreas() {\r\n      return this.areaList.filter(area => area.scheduled_day !== null);\r\n    },\r\n    todayAreas() {\r\n      const today = new Date();\r\n      const todayWeekDay = today.getDay();\r\n      return this.areaList.filter(area => area.scheduled_day === todayWeekDay);\r\n    },\r\n\r\n  },\r\n  \r\n  onLoad() {\r\n    this.loadAreaList();\r\n  },\r\n  \r\n  methods: {\r\n    // 加载公共责任区列表\r\n    async loadAreaList() {\r\n      try {\r\n        this.loading = true;\r\n        \r\n        // 使用认证工具调用云函数获取公共责任区列表\r\n        const result = await callCloudFunction('hygiene-area-management', {\r\n          action: 'getAreaList',\r\n          data: {\r\n            type: 'public' // 只获取公共责任区\r\n          }\r\n        });\r\n\r\n        // 处理返回的数据，计算下次清扫日期\r\n        this.areaList = (result.data.list || []).map(area => ({\r\n          ...area,\r\n          // 确保 scheduled_day 是正确的数字类型或 null\r\n          scheduled_day: area.scheduled_day !== null && area.scheduled_day !== undefined \r\n            ? Number(area.scheduled_day) \r\n            : null,\r\n          next_clean_date: this.calculateNextCleanDate({\r\n            ...area,\r\n            scheduled_day: area.scheduled_day !== null && area.scheduled_day !== undefined \r\n              ? Number(area.scheduled_day) \r\n              : null\r\n          })\r\n        }));\r\n        \r\n      } catch (error) {\r\n        console.error('加载公共责任区列表失败：', error);\r\n        // 认证工具已经处理了登录相关错误\r\n        if (!error.message.includes('未登录') && !error.message.includes('登录')) {\r\n          uni.showToast({\r\n            title: error.message || '加载失败',\r\n            icon: 'none',\r\n            duration: 3000\r\n          });\r\n        }\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    \r\n    // 创建默认数据 - 保留用于首次使用时的演示\r\n    createDefaultAreas() {\r\n      const areas = [\r\n        {\r\n          id: 'public_001',\r\n          name: '主入口大厅',\r\n          location: { area: '办公楼1层' },\r\n          description: '包括接待台、休息区、展示区域',\r\n          status: 'active',\r\n          type: 'public',\r\n          scheduled_day: 1, // 周一\r\n          start_date: '2024-01-15',\r\n          last_clean_date: '2024-01-15',\r\n          created_at: new Date('2024-01-10').toISOString(),\r\n          updated_at: new Date().toISOString()\r\n        },\r\n        {\r\n          id: 'public_002',\r\n          name: '员工餐厅',\r\n          location: { area: '办公楼2层' },\r\n          description: '用餐区域、后厨清洁区域',\r\n          status: 'active',\r\n          type: 'public',\r\n          scheduled_day: 3, // 周三\r\n          start_date: '2024-01-17',\r\n          last_clean_date: '2024-01-17',\r\n          created_at: new Date('2024-01-12').toISOString(),\r\n          updated_at: new Date().toISOString()\r\n        },\r\n        {\r\n          id: 'public_003',\r\n          name: '停车场区域',\r\n          location: { area: '厂区东侧' },\r\n          description: '室外停车区域和通道',\r\n          status: 'active',\r\n          type: 'public',\r\n          scheduled_day: 5, // 周五\r\n          start_date: '2024-01-19',\r\n          last_clean_date: null,\r\n          created_at: new Date('2024-01-14').toISOString(),\r\n          updated_at: new Date().toISOString()\r\n        },\r\n        {\r\n          id: 'public_004',\r\n          name: '会议室区域',\r\n          location: { area: '办公楼3层' },\r\n          description: '大中小会议室及茶水间',\r\n          status: 'active',\r\n          type: 'public',\r\n          scheduled_day: null, // 未设置\r\n          start_date: null,\r\n          last_clean_date: null,\r\n          created_at: new Date('2024-01-16').toISOString(),\r\n          updated_at: new Date().toISOString()\r\n        }\r\n      ];\r\n      \r\n      // 计算下次清扫日期\r\n      return areas.map(area => ({\r\n        ...area,\r\n        next_clean_date: this.calculateNextCleanDate(area)\r\n      }));\r\n    },\r\n    \r\n    // 计算下次清扫日期\r\n    calculateNextCleanDate(area) {\r\n      if (area.scheduled_day === null || !area.start_date) {\r\n        return null;\r\n      }\r\n      \r\n      const today = new Date();\r\n      const targetWeekDay = area.scheduled_day;\r\n      \r\n      // 从今天开始找下一个目标星期几\r\n      let nextDate = new Date(today);\r\n      while (nextDate.getDay() !== targetWeekDay) {\r\n        nextDate.setDate(nextDate.getDate() + 1);\r\n      }\r\n      \r\n      // 如果今天就是目标星期几，但已经是下午了，则找下周的同一天\r\n      if (nextDate.getDay() === targetWeekDay && nextDate.toDateString() === today.toDateString()) {\r\n        if (today.getHours() >= 12) { // 假设12点后不进行清扫\r\n          nextDate.setDate(nextDate.getDate() + 7);\r\n        }\r\n      }\r\n      \r\n      return nextDate.toISOString().split('T')[0];\r\n    },\r\n    \r\n    // 显示新增表单\r\n    showAddArea() {\r\n      this.isEditing = false;\r\n      this.currentArea = null;\r\n      this.resetForm();\r\n      this.$refs.areaFormPopup.open();\r\n    },\r\n    \r\n    // 编辑公共区\r\n    editArea(area) {\r\n      this.isEditing = true;\r\n      this.currentArea = area;\r\n      this.formData = {\r\n        name: area.name || '',\r\n        location: (area.location && area.location.area) || area.location || '',\r\n        description: area.description || '',\r\n        status: area.status || 'active'\r\n      };\r\n      \r\n      // 设置状态选择器的索引\r\n      this.statusIndex = this.statusOptions.findIndex(option => option.value === area.status) || 0;\r\n      \r\n      this.$refs.areaFormPopup.open();\r\n    },\r\n    \r\n    // 删除公共区\r\n    deleteArea(area) {\r\n      uni.showModal({\r\n        title: '确认删除',\r\n        content: `确定要删除公共责任区\"${area.name}\"吗？删除后不可恢复。`,\r\n        confirmText: '删除',\r\n        confirmColor: '#FF3B30',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            this.performDelete(area);\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 执行删除\r\n    async performDelete(area) {\r\n      try {\r\n        // 使用认证工具调用云函数删除责任区\r\n        await callCloudFunction('hygiene-area-management', {\r\n          action: 'deleteArea',\r\n          data: {\r\n            id: area._id || area.id\r\n          }\r\n        });\r\n\r\n        // 从本地列表中移除\r\n        const index = this.areaList.findIndex(item => (item._id || item.id) === (area._id || area.id));\r\n        if (index > -1) {\r\n          this.areaList.splice(index, 1);\r\n        }\r\n        \r\n        uni.showToast({\r\n          title: '删除成功',\r\n          icon: 'success'\r\n        });\r\n      } catch (error) {\r\n        console.error('删除失败：', error);\r\n        // 认证工具已经处理了登录相关错误\r\n        if (!error.message.includes('未登录') && !error.message.includes('登录')) {\r\n          const errorMessage = error.message || '删除失败';\r\n          if (errorMessage.includes('权限不足')) {\r\n            uni.showModal({\r\n              title: '权限不足',\r\n              content: errorMessage,\r\n              showCancel: false,\r\n              confirmText: '我知道了'\r\n            });\r\n          } else {\r\n            uni.showToast({\r\n              title: errorMessage,\r\n              icon: 'none',\r\n              duration: 3000,\r\n              mask: true\r\n            });\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 设置排班\r\n    setSchedule(area) {\r\n      this.currentScheduleArea = area;\r\n      \r\n      // 确保 scheduled_day 是正确的数据类型\r\n      let scheduledDay = area.scheduled_day;\r\n      if (scheduledDay !== null && scheduledDay !== undefined) {\r\n        scheduledDay = Number(scheduledDay);\r\n        // 如果转换后不是有效数字，则设为null\r\n        if (isNaN(scheduledDay) || scheduledDay < 0 || scheduledDay > 6) {\r\n          scheduledDay = null;\r\n        }\r\n      }\r\n      \r\n      this.scheduleData = {\r\n        scheduled_day: scheduledDay,\r\n        start_date: area.start_date || this.getNextMonday()\r\n      };\r\n      \r\n      // 设置星期选择器的索引\r\n      const foundIndex = this.weekDayOptions.findIndex(option => option.value === scheduledDay);\r\n      this.scheduleWeekIndex = foundIndex >= 0 ? foundIndex : 0;\r\n      \r\n      this.$refs.schedulePopup.open();\r\n    },\r\n    \r\n\r\n    \r\n    // 获取下周一的日期\r\n    getNextMonday() {\r\n      const today = new Date();\r\n      const nextMonday = new Date(today);\r\n      const daysUntilMonday = (8 - today.getDay()) % 7;\r\n      nextMonday.setDate(today.getDate() + (daysUntilMonday === 0 ? 7 : daysUntilMonday));\r\n      return nextMonday.toISOString().split('T')[0];\r\n    },\r\n    \r\n\r\n    \r\n    // 状态变更\r\n    onStatusChange(e) {\r\n      this.statusIndex = e.detail.value;\r\n      this.formData.status = this.statusOptions[this.statusIndex].value;\r\n    },\r\n    \r\n    // 排班星期变更\r\n    onScheduleWeekChange(e) {\r\n      this.scheduleWeekIndex = parseInt(e.detail.value);\r\n      const selectedOption = this.weekDayOptions[this.scheduleWeekIndex];\r\n      if (selectedOption) {\r\n        this.scheduleData.scheduled_day = selectedOption.value;\r\n      } else {\r\n        // 如果选择的索引无效，默认设为null（不设置固定日期）\r\n        this.scheduleData.scheduled_day = null;\r\n        this.scheduleWeekIndex = 0;\r\n      }\r\n    },\r\n    \r\n    // 开始日期变更\r\n    onStartDateChange(e) {\r\n      this.scheduleData.start_date = e.detail.value;\r\n    },\r\n    \r\n    // 提交表单\r\n    async submitForm() {\r\n      if (!this.validateForm()) {\r\n        return;\r\n      }\r\n      \r\n      try {\r\n        this.saving = true;\r\n        \r\n        const requestData = {\r\n          name: this.formData.name,\r\n          location: { area: this.formData.location },\r\n          description: this.formData.description,\r\n          status: this.formData.status,\r\n          type: 'public' // 标记为公共责任区\r\n        };\r\n\r\n        let result;\r\n        if (this.isEditing) {\r\n          // 编辑模式 - 使用认证工具\r\n          result = await callCloudFunction('hygiene-area-management', {\r\n            action: 'updateArea',\r\n            data: {\r\n              id: this.currentArea._id || this.currentArea.id,\r\n              ...requestData\r\n            }\r\n          });\r\n        } else {\r\n          // 新增模式 - 使用认证工具\r\n          result = await callCloudFunction('hygiene-area-management', {\r\n            action: 'createArea',\r\n            data: requestData\r\n          });\r\n        }\r\n\r\n        if (this.isEditing) {\r\n          // 更新本地列表中的数据\r\n          const index = this.areaList.findIndex(item => (item._id || item.id) === (this.currentArea._id || this.currentArea.id));\r\n          if (index > -1) {\r\n            this.areaList[index] = {\r\n              ...this.areaList[index],\r\n              ...(result.data || {}),\r\n              next_clean_date: this.calculateNextCleanDate(result.data || {})\r\n            };\r\n          }\r\n        } else {\r\n          // 添加新数据到列表开头\r\n          const newArea = {\r\n            ...(result.data || {}),\r\n            next_clean_date: this.calculateNextCleanDate(result.data || {})\r\n          };\r\n          this.areaList.unshift(newArea);\r\n        }\r\n        \r\n        uni.showToast({\r\n          title: this.isEditing ? '保存成功' : '创建成功',\r\n          icon: 'success'\r\n        });\r\n        \r\n        this.closeForm();\r\n        \r\n      } catch (error) {\r\n        console.error('保存失败：', error);\r\n        // 认证工具已经处理了登录相关错误\r\n        if (!error.message.includes('未登录') && !error.message.includes('登录')) {\r\n          const errorMessage = error.message || '保存失败';\r\n          if (errorMessage.includes('权限不足')) {\r\n            uni.showModal({\r\n              title: '权限不足',\r\n              content: errorMessage,\r\n              showCancel: false,\r\n              confirmText: '我知道了'\r\n            });\r\n          } else {\r\n            uni.showToast({\r\n              title: errorMessage,\r\n              icon: 'none',\r\n              duration: 3000,\r\n              mask: true\r\n            });\r\n          }\r\n        }\r\n      } finally {\r\n        this.saving = false;\r\n      }\r\n    },\r\n    \r\n    // 提交排班\r\n    async submitSchedule() {\r\n      // 验证输入\r\n      if (this.scheduleData.scheduled_day === null || this.scheduleData.scheduled_day === undefined) {\r\n        uni.showToast({\r\n          title: '请选择清扫日期',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      if (!this.scheduleData.start_date) {\r\n        uni.showToast({\r\n          title: '请选择开始日期',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 确保 scheduled_day 是有效的数字\r\n      const scheduledDay = Number(this.scheduleData.scheduled_day);\r\n      if (isNaN(scheduledDay) || scheduledDay < 0 || scheduledDay > 6) {\r\n        uni.showToast({\r\n          title: '请选择有效的清扫日期',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      try {\r\n        this.scheduleSaving = true;\r\n        \r\n        // 使用认证工具调用云函数更新排班信息\r\n        await callCloudFunction('hygiene-area-management', {\r\n          action: 'updateArea',\r\n          data: {\r\n            id: this.currentScheduleArea._id || this.currentScheduleArea.id,\r\n            scheduled_day: scheduledDay,  // 使用验证过的数值\r\n            start_date: this.scheduleData.start_date\r\n          }\r\n        });\r\n\r\n        // 更新本地列表中的数据\r\n        const index = this.areaList.findIndex(item => (item._id || item.id) === (this.currentScheduleArea._id || this.currentScheduleArea.id));\r\n        if (index > -1) {\r\n          const updatedArea = {\r\n            ...this.areaList[index],\r\n            scheduled_day: scheduledDay,  // 使用验证过的数值\r\n            start_date: this.scheduleData.start_date,\r\n            updated_at: new Date().toISOString()\r\n          };\r\n          updatedArea.next_clean_date = this.calculateNextCleanDate(updatedArea);\r\n          this.areaList[index] = updatedArea;\r\n        }\r\n        \r\n        uni.showToast({\r\n          title: '排班设置成功',\r\n          icon: 'success'\r\n        });\r\n        \r\n        this.closeSchedulePopup();\r\n        \r\n      } catch (error) {\r\n        console.error('排班设置失败：', error);\r\n        // 认证工具已经处理了登录相关错误\r\n        if (!error.message.includes('未登录') && !error.message.includes('登录')) {\r\n          const errorMessage = error.message || '设置失败';\r\n          if (errorMessage.includes('权限不足')) {\r\n            uni.showModal({\r\n              title: '权限不足',\r\n              content: errorMessage,\r\n              showCancel: false,\r\n              confirmText: '我知道了'\r\n            });\r\n          } else {\r\n            uni.showToast({\r\n              title: errorMessage,\r\n              icon: 'none',\r\n              duration: 3000\r\n            });\r\n          }\r\n        }\r\n      } finally {\r\n        this.scheduleSaving = false;\r\n      }\r\n    },\r\n    \r\n    // 清除排班\r\n    async clearSchedule() {\r\n      try {\r\n        // 使用认证工具调用云函数清除排班信息\r\n        await callCloudFunction('hygiene-area-management', {\r\n          action: 'updateArea',\r\n          data: {\r\n            id: this.currentScheduleArea._id || this.currentScheduleArea.id,\r\n            scheduled_day: null,\r\n            start_date: null\r\n          }\r\n        });\r\n\r\n        // 更新本地列表中的数据\r\n        const index = this.areaList.findIndex(item => (item._id || item.id) === (this.currentScheduleArea._id || this.currentScheduleArea.id));\r\n        if (index > -1) {\r\n          this.areaList[index] = {\r\n            ...this.areaList[index],\r\n            scheduled_day: null,\r\n            start_date: null,\r\n            next_clean_date: null,\r\n            updated_at: new Date().toISOString()\r\n          };\r\n        }\r\n        \r\n        uni.showToast({\r\n          title: '排班已清除',\r\n          icon: 'success'\r\n        });\r\n        \r\n        this.closeSchedulePopup();\r\n        \r\n      } catch (error) {\r\n        console.error('清除排班失败：', error);\r\n        // 认证工具已经处理了登录相关错误\r\n        if (!error.message.includes('未登录') && !error.message.includes('登录')) {\r\n          const errorMessage = error.message || '操作失败';\r\n          if (errorMessage.includes('权限不足')) {\r\n            uni.showModal({\r\n              title: '权限不足',\r\n              content: errorMessage,\r\n              showCancel: false,\r\n              confirmText: '我知道了'\r\n            });\r\n          } else {\r\n            uni.showToast({\r\n              title: errorMessage,\r\n              icon: 'none',\r\n              duration: 3000\r\n            });\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 表单验证\r\n    validateForm() {\r\n      if (!this.formData.name.trim()) {\r\n        uni.showToast({\r\n          title: '请输入责任区名称',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      if (this.formData.name.length > 50) {\r\n        uni.showToast({\r\n          title: '责任区名称不能超过50个字符',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    \r\n    // 重置表单\r\n    resetForm() {\r\n      this.formData = {\r\n        name: '',\r\n        location: '',\r\n        description: '',\r\n        status: 'active'\r\n      };\r\n      this.statusIndex = 0;\r\n    },\r\n    \r\n    // 关闭表单\r\n    closeForm() {\r\n      this.$refs.areaFormPopup.close();\r\n      this.resetForm();\r\n    },\r\n    \r\n    // 关闭排班弹窗\r\n    closeSchedulePopup() {\r\n      this.$refs.schedulePopup.close();\r\n      this.currentScheduleArea = null;\r\n      // 重置排班数据\r\n      this.scheduleData = {\r\n        scheduled_day: null,\r\n        start_date: ''\r\n      };\r\n      this.scheduleWeekIndex = 0;\r\n      // 确保保存状态重置\r\n      this.scheduleSaving = false;\r\n    },\r\n    \r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        'active': '启用',\r\n        'inactive': '禁用'\r\n      };\r\n      return statusMap[status] || '未知';\r\n    },\r\n    \r\n    // 判断区域是否有有效的排班设置\r\n    hasValidSchedule(area) {\r\n      return area.scheduled_day !== null && \r\n             area.scheduled_day !== undefined && \r\n             typeof area.scheduled_day === 'number' &&\r\n             area.scheduled_day >= 0 && \r\n             area.scheduled_day <= 6;\r\n    },\r\n    \r\n    // 获取星期文本\r\n    getWeekDayText(weekDay) {\r\n      const weekDayMap = {\r\n        0: '周日',\r\n        1: '周一',\r\n        2: '周二',\r\n        3: '周三',\r\n        4: '周四',\r\n        5: '周五',\r\n        6: '周六'\r\n      };\r\n      return weekDayMap[weekDay] || '';\r\n    },\r\n    \r\n    // 判断是否是今天\r\n    isToday(dateString) {\r\n      if (!dateString) return false;\r\n      const today = new Date().toISOString().split('T')[0];\r\n      const date = typeof dateString === 'string' ? dateString.split('T')[0] : dateString.toISOString().split('T')[0];\r\n      return today === date;\r\n    },\r\n    \r\n    // 格式化日期\r\n    formatDate(dateString) {\r\n      if (!dateString) return '';\r\n      const date = new Date(dateString);\r\n      const today = new Date();\r\n      const tomorrow = new Date(today);\r\n      tomorrow.setDate(today.getDate() + 1);\r\n      \r\n      const dateStr = date.toISOString().split('T')[0];\r\n      const todayStr = today.toISOString().split('T')[0];\r\n      const tomorrowStr = tomorrow.toISOString().split('T')[0];\r\n      \r\n      if (dateStr === todayStr) {\r\n        return '今天';\r\n      } else if (dateStr === tomorrowStr) {\r\n        return '明天';\r\n      } else {\r\n        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-container {\r\n  min-height: 100vh;\r\n  background: #F8F9FA;\r\n  padding-bottom: 40rpx;\r\n}\r\n\r\n.header {\r\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\r\n  padding: 60rpx 32rpx 40rpx;\r\n  color: white;\r\n}\r\n\r\n.header-title {\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.header-subtitle {\r\n  font-size: 24rpx;\r\n  opacity: 0.8;\r\n}\r\n\r\n.stats-card {\r\n  background: white;\r\n  margin: 24rpx 32rpx;\r\n  padding: 32rpx;\r\n  border-radius: 16rpx;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 24rpx;\r\n}\r\n\r\n.stat-item {\r\n  text-align: center;\r\n  padding: 24rpx;\r\n  background: #F8F9FA;\r\n  border-radius: 12rpx;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 48rpx;\r\n  font-weight: 600;\r\n  color: #007AFF;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 24rpx;\r\n  color: #8E8E93;\r\n}\r\n\r\n.action-bar {\r\n  padding: 0 32rpx 24rpx;\r\n  display: flex;\r\n  gap: 16rpx;\r\n}\r\n\r\n.action-btn {\r\n  flex: 1;\r\n  height: 72rpx;\r\n  border-radius: 12rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8rpx;\r\n  font-size: 26rpx;\r\n  border: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn.primary {\r\n  background: #007AFF;\r\n  color: white;\r\n}\r\n\r\n.action-btn.secondary {\r\n  background: white;\r\n  color: #007AFF;\r\n  border: 2rpx solid #007AFF;\r\n}\r\n\r\n.action-btn:active {\r\n  transform: scale(0.95);\r\n}\r\n\r\n.area-list {\r\n  padding: 0 32rpx;\r\n}\r\n\r\n.area-item {\r\n  background: white;\r\n  border-radius: 16rpx;\r\n  margin-bottom: 16rpx;\r\n  padding: 24rpx;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 16rpx;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.area-main {\r\n  flex: 1;\r\n}\r\n\r\n.area-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.area-name {\r\n  font-size: 30rpx;\r\n  font-weight: 600;\r\n  color: #1C1C1E;\r\n}\r\n\r\n.area-status {\r\n  font-size: 22rpx;\r\n  padding: 6rpx 12rpx;\r\n  border-radius: 12rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.area-status.status-active {\r\n  background: rgba(52, 199, 89, 0.1);\r\n  color: #34C759;\r\n}\r\n\r\n.area-status.status-inactive {\r\n  background: rgba(142, 142, 147, 0.1);\r\n  color: #8E8E93;\r\n}\r\n\r\n.area-info {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16rpx;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6rpx;\r\n  font-size: 24rpx;\r\n  color: #8E8E93;\r\n}\r\n\r\n.unscheduled-text {\r\n  color: #C7C7CC;\r\n}\r\n\r\n.area-description {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  line-height: 1.4;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.area-schedule {\r\n  background: #F8F9FA;\r\n  border-radius: 8rpx;\r\n  padding: 12rpx;\r\n}\r\n\r\n.schedule-info {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.next-clean, .last-clean {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6rpx;\r\n}\r\n\r\n.next-label, .last-label {\r\n  font-size: 22rpx;\r\n  color: #8E8E93;\r\n}\r\n\r\n.next-date, .last-date {\r\n  font-size: 22rpx;\r\n  font-weight: 500;\r\n  color: #34C759;\r\n}\r\n\r\n.next-date.today {\r\n  color: #FF3B30;\r\n  font-weight: 600;\r\n}\r\n\r\n.no-schedule-info {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8rpx;\r\n  font-size: 22rpx;\r\n  color: #C7C7CC;\r\n  font-style: italic;\r\n}\r\n\r\n.area-actions {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8rpx;\r\n}\r\n\r\n.action-icon-btn {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border-radius: 12rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-icon-btn.schedule {\r\n  background: rgba(52, 199, 89, 0.1);\r\n}\r\n\r\n.action-icon-btn.edit {\r\n  background: rgba(0, 122, 255, 0.1);\r\n}\r\n\r\n.action-icon-btn.delete {\r\n  background: rgba(255, 59, 48, 0.1);\r\n}\r\n\r\n.action-icon-btn:active {\r\n  transform: scale(0.9);\r\n}\r\n\r\n.form-popup {\r\n  width: 92vw;\r\n  max-width: 550rpx;\r\n  background: white;\r\n  border-radius: 16rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.form-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 32rpx;\r\n  border-bottom: 1rpx solid #F2F2F7;\r\n}\r\n\r\n.form-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #1C1C1E;\r\n  margin-left: 5rpx;\r\n}\r\n\r\n.close-btn {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border-radius: 12rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #F2F2F7;\r\n  border: none;\r\n  margin-right: 5rpx;\r\n}\r\n\r\n.form-content {\r\n  padding: 32rpx;\r\n  max-height: 60vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n/* #ifdef MP-WEIXIN */\r\n/* 微信小程序隐藏滚动条 */\r\n.form-content::-webkit-scrollbar {\r\n  display: none;\r\n  width: 0;\r\n  height: 0;\r\n  background: transparent;\r\n}\r\n\r\n.form-content {\r\n  scrollbar-width: none; /* Firefox */\r\n  -ms-overflow-style: none; /* IE and Edge */\r\n}\r\n/* #endif */\r\n\r\n/* #ifndef MP-WEIXIN */\r\n/* 非微信小程序环境下显示细滚动条 */\r\n.form-content::-webkit-scrollbar {\r\n  width: 4rpx;\r\n  height: 4rpx;\r\n}\r\n\r\n.form-content::-webkit-scrollbar-thumb {\r\n  background: rgba(0, 0, 0, 0.1);\r\n  border-radius: 2rpx;\r\n}\r\n\r\n.form-content::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.form-content {\r\n  scrollbar-width: thin;\r\n  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;\r\n}\r\n/* #endif */\r\n\r\n.form-item {\r\n  margin-bottom: 32rpx;\r\n}\r\n\r\n.form-label {\r\n  display: block;\r\n  font-size: 28rpx;\r\n  color: #1C1C1E;\r\n  margin-bottom: 12rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.form-label.required::before {\r\n  content: '*';\r\n  color: #FF3B30;\r\n  margin-right: 6rpx;\r\n}\r\n\r\n.form-input, .form-textarea {\r\n  width: 100%;\r\n  background: #F2F2F7;\r\n  border: none;\r\n  border-radius: 12rpx;\r\n  font-size: 28rpx;\r\n  color: #1C1C1E;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.form-textarea {\r\n  min-height: 120rpx;\r\n  padding: 16rpx;\r\n  line-height: 1.5;\r\n  resize: none;\r\n}\r\n\r\n.form-input {\r\n  height: 80rpx;\r\n  padding: 0 16rpx;\r\n  line-height: 80rpx;\r\n  text-align: left;\r\n}\r\n\r\n/* 强制占位符样式保持一致 */\r\n.form-input::placeholder {\r\n  line-height: 80rpx;\r\n  color: #C7C7CC;\r\n}\r\n\r\n/* 兼容WebKit浏览器 */\r\n.form-input::-webkit-input-placeholder {\r\n  line-height: 80rpx;\r\n  color: #C7C7CC;\r\n}\r\n\r\n/* 兼容Firefox */\r\n.form-input::-moz-placeholder {\r\n  line-height: 80rpx;\r\n  color: #C7C7CC;\r\n  opacity: 1;\r\n}\r\n\r\n/* 兼容IE/Edge */\r\n.form-input:-ms-input-placeholder {\r\n  line-height: 80rpx;\r\n  color: #C7C7CC;\r\n}\r\n\r\n.form-picker {\r\n  width: 100%;\r\n}\r\n\r\n.picker-value {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 80rpx;\r\n  background: #F2F2F7;\r\n  border-radius: 12rpx;\r\n  padding: 0 16rpx;\r\n  font-size: 28rpx;\r\n  color: #1C1C1E;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.form-footer {\r\n  display: flex;\r\n  gap: 16rpx;\r\n  padding: 32rpx;\r\n  border-top: 1rpx solid #F2F2F7;\r\n}\r\n\r\n.form-btn {\r\n  flex: 1;\r\n  height: 72rpx;\r\n  border-radius: 12rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n  border: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.form-btn.cancel {\r\n  background: #F2F2F7;\r\n  color: #8E8E93;\r\n}\r\n\r\n.form-btn.submit {\r\n  background: #007AFF;\r\n  color: white;\r\n}\r\n\r\n.form-btn:active {\r\n  transform: scale(0.95);\r\n}\r\n\r\n.schedule-popup {\r\n  width: 92vw;\r\n  max-width: 650rpx;\r\n  background: white;\r\n  border-radius: 16rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.schedule-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 32rpx;\r\n  border-bottom: 1rpx solid #F2F2F7;\r\n}\r\n\r\n.schedule-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #1C1C1E;\r\n  margin-left: 5rpx;\r\n}\r\n\r\n.schedule-content {\r\n  padding: 32rpx;\r\n  max-height: 60vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n/* #ifdef MP-WEIXIN */\r\n/* 微信小程序隐藏滚动条 */\r\n.schedule-content::-webkit-scrollbar {\r\n  display: none;\r\n  width: 0;\r\n  height: 0;\r\n  background: transparent;\r\n}\r\n\r\n.schedule-content {\r\n  scrollbar-width: none; /* Firefox */\r\n  -ms-overflow-style: none; /* IE and Edge */\r\n}\r\n/* #endif */\r\n\r\n/* #ifndef MP-WEIXIN */\r\n/* 非微信小程序环境下显示细滚动条 */\r\n.schedule-content::-webkit-scrollbar {\r\n  width: 4rpx;\r\n  height: 4rpx;\r\n}\r\n\r\n.schedule-content::-webkit-scrollbar-thumb {\r\n  background: rgba(0, 0, 0, 0.1);\r\n  border-radius: 2rpx;\r\n}\r\n\r\n.schedule-content::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.schedule-content {\r\n  scrollbar-width: thin;\r\n  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;\r\n}\r\n/* #endif */\r\n\r\n.area-name-display {\r\n  font-size: 30rpx;\r\n  font-weight: 600;\r\n  color: #007AFF;\r\n  text-align: center;\r\n  margin-bottom: 32rpx;\r\n  padding: 16rpx;\r\n  background: rgba(0, 122, 255, 0.1);\r\n  border-radius: 12rpx;\r\n}\r\n\r\n.schedule-item {\r\n  margin-bottom: 24rpx;\r\n}\r\n\r\n.schedule-label {\r\n  display: block;\r\n  font-size: 28rpx;\r\n  color: #1C1C1E;\r\n  margin-bottom: 12rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.schedule-picker {\r\n  width: 100%;\r\n}\r\n\r\n.schedule-picker-value {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 80rpx;\r\n  background: #F2F2F7;\r\n  border-radius: 12rpx;\r\n  padding: 0 16rpx;\r\n  font-size: 28rpx;\r\n  color: #1C1C1E;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.schedule-preview {\r\n  margin-top: 32rpx;\r\n  padding: 24rpx;\r\n  background: #F8F9FA;\r\n  border-radius: 12rpx;\r\n}\r\n\r\n.preview-title {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #1C1C1E;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.preview-info {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.next-dates {\r\n  margin-top: 16rpx;\r\n}\r\n\r\n.dates-label {\r\n  font-size: 24rpx;\r\n  color: #8E8E93;\r\n  display: block;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.date-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8rpx;\r\n}\r\n\r\n.date-item {\r\n  background: #007AFF;\r\n  color: white;\r\n  font-size: 22rpx;\r\n  padding: 6rpx 12rpx;\r\n  border-radius: 12rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.date-item.today {\r\n  background: #FF3B30;\r\n}\r\n\r\n.schedule-footer {\r\n  display: flex;\r\n  gap: 12rpx;\r\n  padding: 32rpx;\r\n  border-top: 1rpx solid #F2F2F7;\r\n}\r\n\r\n.schedule-btn {\r\n  flex: 1;\r\n  height: 72rpx;\r\n  border-radius: 12rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 26rpx;\r\n  border: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.schedule-btn.cancel {\r\n  background: #F2F2F7;\r\n  color: #8E8E93;\r\n}\r\n\r\n.schedule-btn.clear {\r\n  background: #FF3B30;\r\n  color: white;\r\n}\r\n\r\n.schedule-btn.submit {\r\n  background: #007AFF;\r\n  color: white;\r\n}\r\n\r\n.schedule-btn:active {\r\n  transform: scale(0.95);\r\n}\r\n\r\n/* 加载状态样式 */\r\n.stats-loading {\r\n  grid-column: 1 / -1;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 60rpx 0;\r\n}\r\n\r\n.list-loading {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 80rpx 0;\r\n  background: #F8F9FA;\r\n}\r\n\r\n.loading-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16rpx;\r\n}\r\n\r\n.loading-text {\r\n  font-size: 28rpx;\r\n  color: #8E8E93;\r\n}\r\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./public-area-manage.vue?vue&type=style&index=0&id=2ae3b0bd&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./public-area-manage.vue?vue&type=style&index=0&id=2ae3b0bd&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775842473\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}