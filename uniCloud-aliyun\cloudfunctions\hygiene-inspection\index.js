'use strict';

const db = uniCloud.database();
const dbCmd = db.command;

// 6S权限角色常量 - 基于实际业务场景
const HYGIENE_ROLES = {
  // 最高管理权限：系统管理员、厂长（可以创建、修改、删除，但主要看结果）
  ADMIN_ROLES: ['admin', 'GM'],
  // 6S专员权限：综合员、发布人（专门负责6S的核心工作人员）
  SPECIALIST_ROLES: ['Integrated', 'reviser'],
  // 清洁员权限：所有人都有责任区，都需要做清洁工作
  CLEANER_ROLES: ['admin', 'GM', 'supervisor', 'PM', 'responsible', 'logistics', 'dispatch', 'Integrated', 'operator', 'technician', 'mechanic', 'user', 'reviser'],
  // 检查权限：只有6S专员负责检查（包括卫生检查、问题发现、任务指派）
  INSPECTOR_ROLES: ['Integrated', 'reviser'],
  // 数据查看权限：所有角色
  VIEW_ROLES: ['admin', 'GM', 'supervisor', 'PM', 'responsible', 'logistics', 'dispatch', 'Integrated', 'operator', 'technician', 'mechanic', 'user', 'reviser']
};

exports.main = async (event, context) => {
  const { action, data, uniIdToken } = event;
  let uid = '';
  let role = [];
  
  // 获取用户信息
  if (uniIdToken) {
    const { createInstance } = require('uni-id-common');
    const uniID = createInstance({ context });
    const payload = await uniID.checkToken(uniIdToken);
    if (payload.code === 0) {
      uid = payload.uid;
      
      // 从数据库获取用户角色信息
      try {
        const userRes = await db.collection('uni-id-users')
          .doc(uid)
          .field({ role: true })
          .get();
        
        if (userRes.data && userRes.data[0] && userRes.data[0].role) {
          role = Array.isArray(userRes.data[0].role) ? userRes.data[0].role : [userRes.data[0].role];
        } else {
          role = ['user'];
        }
      } catch (error) {
        console.error('获取用户角色失败:', error);
        role = ['user'];
      }
    }
  }
  
  // 权限检查
  if (!uid) {
    return {
      success: false,
      message: '用户未登录'
    };
  }
  
  try {
    switch (action) {
      case 'createInspectionRecord':
        return await createInspectionRecord(data, uid, role);
      case 'updateInspectionRecord':
        return await updateInspectionRecord(data, uid, role);
      case 'deleteInspectionRecord':
        return await deleteInspectionRecord(data, uid, role);
      case 'getInspectionRecords':
        return await getInspectionRecords(data, uid, role);
      case 'getMyInspections':
        return await getMyInspections(data, uid);
      case 'getInspectionDetail':
        return await getInspectionDetail(data, uid, role);
      case 'reviewInspection':
        return await reviewInspection(data, uid, role);
      case 'getInspectionStats':
        return await getInspectionStats(data, uid, role);
      case 'scheduleInspection':
        return await scheduleInspection(data, uid, role);
      case 'getInspectionTasks':
        return await getInspectionTasks(data, uid, role);
      default:
        return {
          success: false,
          message: '未知的操作类型'
        };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return {
      success: false,
      message: '服务器错误',
      error: error.message
    };
  }
};

// 创建检查记录
async function createInspectionRecord(data, uid, role) {
  const {
    area_id,
    inspection_date,
    result,
    score,
    overall_rating,
    issues = [],
    photos = [],
    recommendations = [],
    remarks = ''
  } = data;
  
  // 验证必填字段
  if (!area_id || !inspection_date || !result) {
    return {
      success: false,
      message: '责任区ID、检查日期和检查结果不能为空'
    };
  }
  
  // 权限检查：只有检查员或管理员可以创建检查记录
  if (!role.includes('admin') && !role.includes('inspector') && !role.includes('manager')) {
    return {
      success: false,
      message: '权限不足，只有检查员或管理员可以创建检查记录'
    };
  }
  
  // 获取责任区信息
  const areaResult = await db.collection('hygiene-areas').doc(area_id).get();
  if (!areaResult.data.length) {
    return {
      success: false,
      message: '责任区不存在'
    };
  }
  
  const area = areaResult.data[0];
  
  // 获取检查员信息
  const userResult = await db.collection('uni-id-users').doc(uid).get();
  const userName = userResult.data.length > 0 ? userResult.data[0].nickname || userResult.data[0].username : '';
  
  // 计算周次
  const weekNumber = getWeekNumber(new Date(inspection_date));
  
  // 检查是否有问题
  const hasIssues = issues && issues.length > 0;
  
  const inspectionData = {
    area_id,
    area_name: area.name,
    inspector_id: uid,
    inspector_name: userName,
    inspection_date: new Date(inspection_date),
    week_number: weekNumber,
    result,
    has_issues: hasIssues,
    issues: issues.map(issue => ({
      category: issue.category || 'other',
      description: issue.description,
      severity: issue.severity || 'medium',
      photos: issue.photos || []
    })),
    photos: photos.map(photo => ({
      url: photo.url || photo,
      description: photo.description || '',
      timestamp: new Date()
    })),
    recommendations,
    remarks,
    status: hasIssues ? 'pending_rectification' : 'completed',
    created_at: new Date(),
    updated_at: new Date()
  };
  
  // 可选字段
  if (score !== undefined) inspectionData.score = score;
  if (overall_rating !== undefined) inspectionData.overall_rating = overall_rating;
  
  const result_record = await db.collection('hygiene-inspection-records').add(inspectionData);
  
  // 如果发现问题，自动创建问题记录和整改任务
  if (hasIssues) {
    for (const issue of issues) {
      // 中文问题类型映射到英文
      const categoryMap = {
        '设备问题': 'equipment',
        '清洁问题': 'cleanliness', 
        '整理问题': 'organization',
        '安全问题': 'safety',
        '环境问题': 'environment',
        '标识问题': 'standardization',
        '其他问题': 'other'
      };
      
      // 优先使用type字段（前端发送的），再使用category字段
      const chineseType = issue.type || issue.category;
      const englishCategory = categoryMap[chineseType] || 'cleanliness';
      
      // 创建问题记录
      const issueData = {
        title: `检查发现问题：${issue.description.substring(0, 50)}`,
        description: issue.description,
        category: englishCategory,
        severity: issue.severity || 'medium',
        area_id,
        area_name: area.name,
        reporter_id: uid,
        reporter_name: userName,
        // 只有当有照片时才添加 photos 字段
        ...(issue.photos && issue.photos.length > 0 ? {
          photos: issue.photos.map(url => ({
            url,
            description: '检查发现的问题',
            timestamp: new Date()
          }))
        } : {}),
        status: 'submitted',
        priority: issue.severity === 'critical' ? 'urgent' : 'normal',
        created_at: new Date(),
        updated_at: new Date()
      };
      
      const issueResult = await db.collection('hygiene-issues').add(issueData);
      
      // 自动创建整改任务
      const rectificationData = {
        issue_id: issueResult.id,
        area_id,
        area_name: area.name,
        inspection_record_id: result_record.id,
        title: `整改任务：${issue.description.substring(0, 50)}`,
        description: issue.description,
        category: englishCategory,
        severity: issue.severity || 'medium',
        assigned_to: null, // 将在下面自动分配
        assigned_to_name: null,
        assigned_by: uid,
        assigned_by_name: userName,
        deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 默认7天后
        photos: photos.map(photo => ({
          url: photo.url || photo,
          description: '检查发现的问题',
          timestamp: new Date()
        })),
        status: 'pending_assignment', // 将在下面根据分配情况修改
        priority: issue.severity === 'critical' ? 'urgent' : 'normal',
        created_at: new Date(),
        updated_at: new Date()
      };
      
      // 查询责任区负责人并自动分配
      try {
        let assigned = false;
        
        // 1. 首先尝试从 hygiene-assignments 查找固定分配的负责人
        const assignmentResult = await db.collection('hygiene-assignments')
          .where({
            area_ids: dbCmd.in([area_id]),
            status: 'active'
          })
          .get();
        
        if (assignmentResult.data && assignmentResult.data.length > 0) {
          const assignment = assignmentResult.data[0];
          rectificationData.assigned_to = assignment.employee_id;
          rectificationData.assigned_to_name = assignment.employee_name;
          rectificationData.status = 'pending_rectification';
          assigned = true;
        }
        
        // 2. 如果没有固定分配（公共责任区），查找最近的清理记录来确定负责人
        if (!assigned) {
          const inspectionDate = new Date(inspection_date);
          const startDate = new Date(inspectionDate.getTime() - 7 * 24 * 60 * 60 * 1000); // 前7天
          const endDate = new Date(inspectionDate.getTime() + 1 * 24 * 60 * 60 * 1000);   // 后1天
          
          const cleaningResult = await db.collection('hygiene-cleaning-records')
            .where({
              area_id: area_id,
              cleaning_date: dbCmd.gte(startDate).and(dbCmd.lte(endDate))
            })
            .orderBy('cleaning_date', 'desc')
            .limit(1)
            .get();
          
          if (cleaningResult.data && cleaningResult.data.length > 0) {
            const cleaningRecord = cleaningResult.data[0];
            rectificationData.assigned_to = cleaningRecord.user_id;
            rectificationData.assigned_to_name = cleaningRecord.user_name;
            rectificationData.status = 'pending_rectification';
            assigned = true;
          }
        }
        
        // 3. 如果都没找到，保持待分配状态
        if (!assigned) {
          // 保持默认的 pending_assignment 状态
        }
        
      } catch (error) {
        console.warn('查询责任区负责人失败:', error);
        // 查询失败，保持 pending_assignment 状态
      }
      
      await db.collection('hygiene-rectification-records').add(rectificationData);
    }
  }
  
  return {
    success: true,
    message: hasIssues ? '检查记录创建成功，已自动生成整改任务' : '检查记录创建成功',
    data: {
      id: result_record.id,
      ...inspectionData,
      issues_created: hasIssues ? issues.length : 0,
      rectification_tasks_created: hasIssues ? issues.length : 0
    }
  };
}

// 更新检查记录
async function updateInspectionRecord(data, uid, role) {
  const {
    id,
    result,
    score,
    issues,
    photos,
    recommendations,
    remarks,
    next_inspection_date
  } = data;
  
  if (!id) {
    return {
      success: false,
      message: '检查记录ID不能为空'
    };
  }
  
  // 获取检查记录
  const recordResult = await db.collection('hygiene-inspection-records').doc(id).get();
  if (!recordResult.data.length) {
    return {
      success: false,
      message: '检查记录不存在'
    };
  }
  
  const record = recordResult.data[0];
  
  // 权限检查：只有记录创建者或管理员可以修改
  if (!role.includes('admin') && !role.includes('manager') && record.inspector_id !== uid) {
    return {
      success: false,
      message: '权限不足'
    };
  }
  
  const updateData = {
    updated_at: new Date()
  };
  
  if (result !== undefined) updateData.result = result;
  if (score !== undefined) updateData.score = score;
  if (issues !== undefined) {
    updateData.issues = issues.map(issue => ({
      category: issue.category || 'other',
      description: issue.description,
      severity: issue.severity || 'medium',
      photos: issue.photos || []
    }));
    updateData.has_issues = issues.length > 0;
  }
  if (photos !== undefined) {
    updateData.photos = photos.map(photo => ({
      url: photo.url || photo,
      description: photo.description || '',
      timestamp: new Date()
    }));
  }
  if (recommendations !== undefined) updateData.recommendations = recommendations;
  if (remarks !== undefined) updateData.remarks = remarks;
  if (next_inspection_date !== undefined) updateData.next_inspection_date = new Date(next_inspection_date);
  
  await db.collection('hygiene-inspection-records').doc(id).update(updateData);
  
  return {
    success: true,
    message: '检查记录更新成功'
  };
}

// 删除检查记录
async function deleteInspectionRecord(data, uid, role) {
  const { id } = data;
  
  if (!id) {
    return {
      success: false,
      message: '检查记录ID不能为空'
    };
  }
  
  // 获取检查记录
  const recordResult = await db.collection('hygiene-inspection-records').doc(id).get();
  if (!recordResult.data.length) {
    return {
      success: false,
      message: '检查记录不存在'
    };
  }
  
  const record = recordResult.data[0];
  
  // 权限检查：只有管理员可以删除检查记录
  if (!role.includes('admin')) {
    return {
      success: false,
      message: '只有管理员可以删除检查记录'
    };
  }
  
  await db.collection('hygiene-inspection-records').doc(id).remove();
  
  return {
    success: true,
    message: '检查记录删除成功'
  };
}

// 获取检查记录列表
async function getInspectionRecords(data, uid, role) {
  const {
    area_id,
    inspector_id,
    result,
    start_date,
    end_date,
    page = 1,
    pageSize = 20,
    week_number,
    has_issues
  } = data;
  
  let whereCondition = {};
  
  // 责任区筛选
  if (area_id) {
    whereCondition.area_id = area_id;
  }
  
  // 检查员筛选
  if (inspector_id) {
    whereCondition.inspector_id = inspector_id;
  }
  
  // 结果筛选
  if (result) {
    whereCondition.result = result;
  }
  
  // 是否有问题筛选
  if (has_issues !== undefined) {
    whereCondition.has_issues = has_issues;
  }
  
  // 周次筛选
  if (week_number) {
    whereCondition.week_number = week_number;
  }
  
  // 日期范围筛选
  if (start_date && end_date) {
    whereCondition.inspection_date = dbCmd.gte(new Date(start_date)).and(dbCmd.lte(new Date(end_date)));
  } else if (start_date) {
    whereCondition.inspection_date = dbCmd.gte(new Date(start_date));
  } else if (end_date) {
    whereCondition.inspection_date = dbCmd.lte(new Date(end_date));
  }
  
  const skip = (page - 1) * pageSize;
  
  const [listResult, countResult] = await Promise.all([
    db.collection('hygiene-inspection-records')
      .where(whereCondition)
      .orderBy('inspection_date', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get(),
    db.collection('hygiene-inspection-records')
      .where(whereCondition)
      .count()
  ]);
  
  return {
    success: true,
    data: {
      list: listResult.data,
      total: countResult.total,
      page,
      pageSize
    }
  };
}

// 获取我的检查记录
async function getMyInspections(data, uid) {
  const { status, limit = 10 } = data;
  
  let whereCondition = {
    inspector_id: uid
  };
  
  if (status) {
    whereCondition.status = status;
  }
  
  const result = await db.collection('hygiene-inspection-records')
    .where(whereCondition)
    .orderBy('inspection_date', 'desc')
    .limit(limit)
    .get();
  
  return {
    success: true,
    data: result.data
  };
}

// 获取检查详情
async function getInspectionDetail(data, uid, role) {
  const { id } = data;
  
  if (!id) {
    return {
      success: false,
      message: '检查记录ID不能为空'
    };
  }
  
  const recordResult = await db.collection('hygiene-inspection-records').doc(id).get();
  if (!recordResult.data.length) {
    return {
      success: false,
      message: '检查记录不存在'
    };
  }
  
  const record = recordResult.data[0];
  
  // 获取相关的整改记录
  const rectificationResult = await db.collection('hygiene-rectification-records')
    .where({
      area_id: record.area_id,
      created_at: dbCmd.gte(record.inspection_date)
    })
    .orderBy('created_at', 'desc')
    .limit(5)
    .get();
  
  return {
    success: true,
    data: {
      ...record,
      related_rectifications: rectificationResult.data
    }
  };
}

// 审核检查记录
async function reviewInspection(data, uid, role) {
  const { id, reviewed, review_comments } = data;
  
  if (!id) {
    return {
      success: false,
      message: '检查记录ID不能为空'
    };
  }
  
  // 权限检查：只有管理员可以审核
  if (!role.includes('admin') && !role.includes('manager')) {
    return {
      success: false,
      message: '权限不足，只有管理员可以审核检查记录'
    };
  }
  
  const recordResult = await db.collection('hygiene-inspection-records').doc(id).get();
  if (!recordResult.data.length) {
    return {
      success: false,
      message: '检查记录不存在'
    };
  }
  
  const updateData = {
    reviewed_by: uid,
    reviewed_at: new Date(),
    status: reviewed ? 'reviewed' : 'pending',
    updated_at: new Date()
  };
  
  if (review_comments) updateData.review_comments = review_comments;
  
  await db.collection('hygiene-inspection-records').doc(id).update(updateData);
  
  return {
    success: true,
    message: '检查记录审核成功'
  };
}

// 获取检查统计
async function getInspectionStats(data, uid, role) {
  const { area_id, time_range = 'month' } = data;
  
  let startDate, endDate;
  const now = new Date();
  
  switch (time_range) {
    case 'week':
      startDate = getWeekStart(now);
      endDate = getWeekEnd(now);
      break;
    case 'month':
      startDate = getMonthStart(now);
      endDate = getMonthEnd(now);
      break;
    case 'quarter':
      startDate = getQuarterStart(now);
      endDate = getQuarterEnd(now);
      break;
    default:
      startDate = getMonthStart(now);
      endDate = getMonthEnd(now);
  }
  
  let whereCondition = {
    inspection_date: dbCmd.gte(startDate).and(dbCmd.lte(endDate))
  };
  
  if (area_id) {
    whereCondition.area_id = area_id;
  }
  
  const [
    totalInspections,
    excellentCount,
    goodCount,
    fairCount,
    poorCount,
    failedCount,
    issuesCount
  ] = await Promise.all([
    db.collection('hygiene-inspection-records').where(whereCondition).count(),
    db.collection('hygiene-inspection-records').where({ ...whereCondition, result: 'excellent' }).count(),
    db.collection('hygiene-inspection-records').where({ ...whereCondition, result: 'good' }).count(),
    db.collection('hygiene-inspection-records').where({ ...whereCondition, result: 'fair' }).count(),
    db.collection('hygiene-inspection-records').where({ ...whereCondition, result: 'poor' }).count(),
    db.collection('hygiene-inspection-records').where({ ...whereCondition, result: 'failed' }).count(),
    db.collection('hygiene-inspection-records').where({ ...whereCondition, has_issues: true }).count()
  ]);
  
  return {
    success: true,
    data: {
      timeRange: time_range,
      period: { startDate, endDate },
      total: totalInspections.total,
      results: {
        excellent: excellentCount.total,
        good: goodCount.total,
        fair: fairCount.total,
        poor: poorCount.total,
        failed: failedCount.total
      },
      issuesFound: issuesCount.total,
      passRate: totalInspections.total > 0 
        ? Math.round(((excellentCount.total + goodCount.total + fairCount.total) / totalInspections.total) * 100)
        : 0
    }
  };
}

// 安排检查
async function scheduleInspection(data, uid, role) {
  const { area_id, inspector_id, scheduled_date, priority = 'normal', notes } = data;
  
  if (!area_id || !inspector_id || !scheduled_date) {
    return {
      success: false,
      message: '责任区ID、检查员ID和计划日期不能为空'
    };
  }
  
  // 权限检查：只有管理员可以安排检查
  if (!role.includes('admin') && !role.includes('manager')) {
    return {
      success: false,
      message: '权限不足，只有管理员可以安排检查'
    };
  }
  
  // 获取责任区和检查员信息
  const [areaResult, inspectorResult] = await Promise.all([
    db.collection('hygiene-areas').doc(area_id).get(),
    db.collection('uni-id-users').doc(inspector_id).get()
  ]);
  
  if (!areaResult.data.length) {
    return {
      success: false,
      message: '责任区不存在'
    };
  }
  
  if (!inspectorResult.data.length) {
    return {
      success: false,
      message: '检查员不存在'
    };
  }
  
  const area = areaResult.data[0];
  const inspector = inspectorResult.data[0];
  
  const scheduleData = {
    area_id,
    area_name: area.name,
    inspector_id,
    inspector_name: inspector.nickname || inspector.username,
    scheduled_date: new Date(scheduled_date),
    priority,
    status: 'scheduled',
    assigned_by: uid,
    created_at: new Date(),
    updated_at: new Date()
  };
  
  if (notes) scheduleData.notes = notes;
  
  const result = await db.collection('hygiene-inspection-schedules').add(scheduleData);
  
  return {
    success: true,
    message: '检查安排成功',
    data: {
      _id: result.id,
      ...scheduleData
    }
  };
}

// 获取检查任务
async function getInspectionTasks(data, uid, role) {
  const { inspector_id, status, start_date, end_date } = data;
  
  let targetInspectorId = inspector_id || uid;
  
  // 如果查询其他检查员的任务，需要管理员权限
  if (inspector_id && inspector_id !== uid && !role.includes('admin') && !role.includes('manager')) {
    return {
      success: false,
      message: '权限不足'
    };
  }
  
  let whereCondition = {
    inspector_id: targetInspectorId
  };
  
  if (status) {
    whereCondition.status = status;
  }
  
  // 日期范围筛选
  if (start_date && end_date) {
    whereCondition.scheduled_date = dbCmd.gte(new Date(start_date)).and(dbCmd.lte(new Date(end_date)));
  } else if (start_date) {
    whereCondition.scheduled_date = dbCmd.gte(new Date(start_date));
  } else if (end_date) {
    whereCondition.scheduled_date = dbCmd.lte(new Date(end_date));
  }
  
  const result = await db.collection('hygiene-inspection-schedules')
    .where(whereCondition)
    .orderBy('scheduled_date', 'asc')
    .get();
  
  return {
    success: true,
    data: result.data
  };
}

// 辅助函数：获取周次
function getWeekNumber(date) {
  const year = date.getFullYear();
  const week = getWeek(date);
  return `${year}-W${String(week).padStart(2, '0')}`;
}

// 辅助函数：获取ISO周数
function getWeek(date) {
  const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
  const dayNum = d.getUTCDay() || 7;
  d.setUTCDate(d.getUTCDate() + 4 - dayNum);
  const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
  return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
}

// 辅助函数：获取周开始时间
function getWeekStart(date) {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day + (day === 0 ? -6 : 1);
  return new Date(d.setDate(diff));
}

// 辅助函数：获取周结束时间
function getWeekEnd(date) {
  const d = getWeekStart(date);
  d.setDate(d.getDate() + 6);
  return d;
}

// 辅助函数：获取月开始时间
function getMonthStart(date) {
  return new Date(date.getFullYear(), date.getMonth(), 1);
}

// 辅助函数：获取月结束时间
function getMonthEnd(date) {
  return new Date(date.getFullYear(), date.getMonth() + 1, 0);
}

// 辅助函数：获取季度开始时间
function getQuarterStart(date) {
  const quarter = Math.floor(date.getMonth() / 3);
  return new Date(date.getFullYear(), quarter * 3, 1);
}

// 辅助函数：获取季度结束时间
function getQuarterEnd(date) {
  const quarter = Math.floor(date.getMonth() / 3);
  return new Date(date.getFullYear(), quarter * 3 + 3, 0);
} 