require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/rectification-review"],{"107f":function(e,t,r){"use strict";(function(e,t){var n=r("47a9");r("357b"),r("861b");n(r("3240"));var o=n(r("5a49"));e.__webpack_require_UNI_MP_PLUGIN__=r,t(o.default)}).call(this,r("3223")["default"],r("df3c")["createPage"])},"206d":function(e,t,r){"use strict";var n=r("ddbd"),o=r.n(n);o.a},"5a49":function(e,t,r){"use strict";r.r(t);var n=r("8945"),o=r("c8d9");for(var i in o)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return o[e]}))}(i);r("206d");var a=r("828b"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"0c61d238",null,!1,n["a"],void 0);t["default"]=s.exports},8945:function(e,t,r){"use strict";r.d(t,"b",(function(){return o})),r.d(t,"c",(function(){return i})),r.d(t,"a",(function(){return n}));var n={uniIcons:function(){return Promise.all([r.e("common/vendor"),r.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(r.bind(null,"6ddf"))},pEmptyState:function(){return r.e("components/p-empty-state/p-empty-state").then(r.bind(null,"9b76"))},uniRate:function(){return r.e("uni_modules/uni-rate/components/uni-rate/uni-rate").then(r.bind(null,"897a"))}},o=function(){var e=this,t=e.$createElement,r=(e._self._c,e.loading?null:e.getStatusText(e.taskInfo.status)),n=e.loading||e.loadError||!e.dataLoaded?null:e.formatDateTime(e.taskInfo.issueFoundDate),o=e.loading||e.loadError||!e.dataLoaded?null:e.taskInfo.issuePhotos&&e.taskInfo.issuePhotos.length>0,i=e.loading||e.loadError||!e.dataLoaded?null:e.formatDateTime(e.taskInfo.rectificationSubmitTime),a=!e.loading&&!e.loadError&&e.dataLoaded&&e.dataLoaded?e.taskInfo.rectificationPhotos.length:null,s=e.loading||e.loadError||!e.dataLoaded||"approved"!==e.reviewForm.result?null:e.getRatingDescription(e.reviewForm.rating),u=e.loading||e.loadError||!e.dataLoaded?null:e.__map(e.reviewForm.photos,(function(t,r){var n=e.__get_orig(t),o=e.getPhotoDisplayUrl(t);return{$orig:n,m4:o}})),c=e.loading||e.loadError||!e.dataLoaded?null:e.reviewForm.photos.length;e.$mp.data=Object.assign({},{$root:{m0:r,m1:n,g0:o,m2:i,g1:a,m3:s,l0:u,g2:c}})},i=[]},c128:function(e,t,r){"use strict";(function(e,n){var o=r("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=o(r("7eb4")),a=o(r("ee10")),s=o(r("3b2d")),u=r("882c"),c=o(r("4ea0")),l={name:"RectificationReview",data:function(){return{taskId:"",loading:!1,dataLoaded:!1,loadError:!1,isSubmitting:!1,taskInfo:{id:"",areaName:"",areaType:"",status:"pending_review",issueDescription:"",issueFoundDate:"",inspector:"",issuePhotos:[],rectificationDescription:"",rectificationPhotos:[],rectificationSubmitTime:"",employeeName:""},reviewForm:{result:"",comment:"",photos:[],rating:0},autoUpload:!0,sliderTouching:!1}},computed:{commentLength:function(){return this.reviewForm.comment?this.reviewForm.comment.length:0},canSubmit:function(){if(!this.reviewForm.result)return!1;if("rejected"===this.reviewForm.result&&!this.reviewForm.comment.trim())return!1;if("rejected"===this.reviewForm.result&&0===this.reviewForm.photos.length)return!1;if("approved"===this.reviewForm.result&&!this.reviewForm.rating)return!1;var e=this.reviewForm.photos.some((function(e){return"object"===(0,s.default)(e)&&e.uploading}));return!e&&!this.isSubmitting},commentPlaceholder:function(){return"approved"===this.reviewForm.result?"请对整改效果进行评价（可选）":"rejected"===this.reviewForm.result?"请说明需要重新整改的原因":"请输入复查意见"}},onLoad:function(t){t.taskId?(this.taskId=t.taskId,this.loadTaskInfo()):(this.loadError=!0,e.showModal({title:"参数错误",content:"缺少整改任务ID参数",showCancel:!0,cancelText:"返回",confirmText:"确定",success:function(t){e.navigateBack()}}))},methods:{handleCommentInput:function(t){var r=t.detail.value||"";r.length>200&&(r=r.substring(0,200),e.showToast({title:"复查意见不能超过200个字符",icon:"none",duration:1500})),this.reviewForm.comment=r,this.$forceUpdate()},loadTaskInfo:function(){var t=this;return(0,a.default)(i.default.mark((function r(){var n,o;return i.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(t.taskId){r.next=4;break}return t.loadError=!0,e.showModal({title:"参数错误",content:"整改任务ID不能为空",showCancel:!1,confirmText:"返回",success:function(){e.navigateBack()}}),r.abrupt("return");case 4:return t.loading=!0,t.dataLoaded=!1,t.loadError=!1,r.prev=7,r.next=10,(0,u.callCloudFunction)("hygiene-rectification",{action:"getRectificationDetail",data:{id:t.taskId}});case 10:if(n=r.sent,!(n&&n.success&&n.data)){r.next=16;break}o=n.data,t.taskInfo={id:o._id||o.id,areaName:o.area_name||"未知责任区",areaType:o.area_type||"fixed",status:o.status||"pending_review",issueDescription:o.issue_description||o.description||"无问题描述",issueFoundDate:o.created_at||o.issue_found_date,inspector:o.inspector_name||o.assigned_by_name||o.created_by_name||o.issue&&o.issue.inspector_name||"未知检查员",issuePhotos:t.processPhotos(o.photos||[]),rectificationDescription:o.rectification_description||o.completion_description||"",rectificationPhotos:t.processPhotos(o.completion_photos||[]),rectificationSubmitTime:o.submitted_at||o.updated_at,employeeName:o.assigned_to_name||o.assignee_name||"未分配",assignee_id:o.assignee_id},r.next=17;break;case 16:throw new Error((null===n||void 0===n?void 0:n.message)||"获取整改任务详情失败");case 17:t.dataLoaded=!0,r.next=25;break;case 20:r.prev=20,r.t0=r["catch"](7),console.error("加载整改任务信息失败:",r.t0),t.loadError=!0,e.showModal({title:"加载失败",content:r.t0.message||"网络异常，请稍后重试",showCancel:!0,cancelText:"返回",confirmText:"重试",success:function(r){r.confirm?t.loadTaskInfo():e.navigateBack()}});case 25:return r.prev=25,t.loading=!1,r.finish(25);case 28:case"end":return r.stop()}}),r,null,[[7,20,25,28]])})))()},retryLoad:function(){this.loadTaskInfo()},processPhotos:function(e){return e&&Array.isArray(e)?e.map((function(e){return"string"===typeof e?e:e&&e.url?e.url:""})).filter((function(e){return e})):[]},selectReviewResult:function(e){this.reviewForm.result=e,"approved"===e?((this.reviewForm.comment.includes("需要")||this.reviewForm.comment.includes("不够"))&&(this.reviewForm.comment=""),this.reviewForm.rating||(this.reviewForm.rating=3)):this.reviewForm.rating=0},setRating:function(e){this.reviewForm.rating=e},getRatingDesc:function(e){return{1:"需要改进",2:"基本合格",3:"良好",4:"优秀",5:"卓越"}[e]||"请评分"},getRatingDescription:function(e){return 0===e?"请评分":e<=1?"较差":e<=2?"一般":e<=3?"良好":e<5?"优秀":5===e?"完美":""},onStarRateChange:function(e){this.reviewForm.rating=e.value},setRatingByMark:function(e){this.reviewForm.rating=e},onSliderTouchStart:function(e){this.sliderTouching=!0,this.updateRatingFromTouch(e)},onSliderTouchMove:function(e){this.sliderTouching&&this.updateRatingFromTouch(e)},onSliderTouchEnd:function(e){this.sliderTouching=!1},onSliderMouseDown:function(e){this.sliderTouching=!0,this.updateRatingFromMouse(e)},onSliderMouseMove:function(e){this.sliderTouching&&this.updateRatingFromMouse(e)},onSliderMouseUp:function(e){this.sliderTouching=!1},updateRatingFromTouch:function(t){var r=this,n=t.touches[0]||t.changedTouches[0];e.createSelectorQuery().in(this).select(".custom-slider-container").boundingClientRect((function(e){if(e){var t=n.clientX-e.left,o=Math.max(0,Math.min(1,t/e.width)),i=Math.round(5*o*2)/2;r.reviewForm.rating=Math.max(0,Math.min(5,i))}})).exec()},updateRatingFromMouse:function(t){var r=this;e.createSelectorQuery().in(this).select(".custom-slider-container").boundingClientRect((function(e){if(e){var n=t.clientX-e.left,o=Math.max(0,Math.min(1,n/e.width)),i=Math.round(5*o*2)/2;r.reviewForm.rating=Math.max(0,Math.min(5,i))}})).exec()},getStatusText:function(e){return{pending_review:"待复查",approved:"复查通过",rejected:"整改不达标",completed:"已完成"}[e]||e},formatDateTime:function(e){if(!e)return"--";try{var t;return t="string"===typeof e?e.includes("T")||e.includes("Z")?new Date(e):new Date(e.replace(/-/g,"/")):new Date(e),isNaN(t.getTime())?"--":"".concat(t.getMonth()+1,"月").concat(t.getDate(),"日 ").concat(t.getHours().toString().padStart(2,"0"),":").concat(t.getMinutes().toString().padStart(2,"0"))}catch(r){return"--"}},previewPhoto:function(t){var r=this.taskInfo.rectificationPhotos.map((function(e){return"object"===(0,s.default)(e)?e.url:e}));e.previewImage({urls:r,current:t})},previewIssuePhoto:function(t){var r=this.taskInfo.issuePhotos.map((function(e){return"object"===(0,s.default)(e)?e.url:e}));e.previewImage({urls:r,current:t})},previewReviewPhoto:function(t){var r=this,n=this.reviewForm.photos.map((function(e){return r.getPhotoDisplayUrl(e)}));e.previewImage({urls:n,current:t})},getPhotoDisplayUrl:function(e){return e.url||e},addReviewPhoto:function(){var t=this;return(0,a.default)(i.default.mark((function r(){var n,o;return i.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,new Promise((function(r,n){e.chooseImage({count:6-t.reviewForm.photos.length,sizeType:["compressed"],sourceType:["camera","album"],success:r,fail:n})}));case 3:n=r.sent,o=n.tempFilePaths.map((function(e){return{url:e,uploaded:!1,cloudUrl:"",cloudPath:"",uploading:!1,belongsToRecord:!1}})),t.reviewForm.photos=t.reviewForm.photos.concat(o),t.autoUpload&&t.autoUploadNewPhotos(o),r.next=13;break;case 9:r.prev=9,r.t0=r["catch"](0),console.error("选择照片失败:",r.t0),e.showToast({title:"选择照片失败",icon:"none"});case 13:case"end":return r.stop()}}),r,null,[[0,9]])})))()},deleteReviewPhoto:function(t){var r=this;return(0,a.default)(i.default.mark((function o(){var a;return i.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(!(t<0||t>=r.reviewForm.photos.length)){o.next=2;break}return o.abrupt("return");case 2:if(a=r.reviewForm.photos[t],!a.uploaded||!a.cloudPath){o.next=16;break}return o.prev=4,e.showLoading({title:"删除照片中..."}),o.next=8,n.callFunction({name:"delete-file",data:{fileList:[r.extractFileId(a.cloudPath)]}});case 8:o.next=13;break;case 10:o.prev=10,o.t0=o["catch"](4),e.showToast({title:"删除云端照片失败",icon:"none"});case 13:return o.prev=13,e.hideLoading(),o.finish(13);case 16:r.reviewForm.photos.splice(t,1);case 17:case"end":return o.stop()}}),o,null,[[4,10,13,16]])})))()},extractFileId:function(e){if(e.startsWith("cloud://")){var t=e.split("/");return t[t.length-1]}if(e.includes("tcb-api")){var r=new URL(e);return r.pathname.split("/").pop()}return e},autoUploadNewPhotos:function(t){var r=this;return(0,a.default)(i.default.mark((function n(){var o,a,s;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:o=i.default.mark((function n(o){var a,s,u;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(a=t[o],s=r.reviewForm.photos.findIndex((function(e){return e.url===a.url})),-1!==s){n.next=4;break}return n.abrupt("return","continue");case 4:return n.prev=4,r.$set(r.reviewForm.photos[s],"uploading",!0),n.next=8,r.uploadSinglePhoto(a);case 8:if(u=n.sent,!u.success){n.next=16;break}r.$set(r.reviewForm.photos[s],"uploaded",!0),r.$set(r.reviewForm.photos[s],"cloudUrl",u.url),r.$set(r.reviewForm.photos[s],"cloudPath",u.cloudPath),r.$set(r.reviewForm.photos[s],"uploading",!1),n.next=17;break;case 16:throw new Error(u.error||"上传失败");case 17:n.next=23;break;case 19:n.prev=19,n.t0=n["catch"](4),r.$set(r.reviewForm.photos[s],"uploading",!1),e.showToast({title:"审核照片".concat(o+1,"上传失败"),icon:"none",duration:2e3});case 23:case"end":return n.stop()}}),n,null,[[4,19]])})),a=0;case 2:if(!(a<t.length)){n.next=10;break}return n.delegateYield(o(a),"t0",4);case 4:if(s=n.t0,"continue"!==s){n.next=7;break}return n.abrupt("continue",7);case 7:a++,n.next=2;break;case 10:case"end":return n.stop()}}),n)})))()},uploadReviewPhotos:function(){var t=this;return(0,a.default)(i.default.mark((function r(){var n,o,a,s;return i.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:n=[],o=0;case 2:if(!(o<t.reviewForm.photos.length)){r.next=31;break}if(a=t.reviewForm.photos[o],!a.uploaded){r.next=7;break}return n.push({url:a.cloudPath,description:"审核照片",timestamp:new Date}),r.abrupt("continue",28);case 7:return r.prev=7,t.$set(a,"uploading",!0),r.next=11,t.uploadSinglePhoto(a);case 11:if(s=r.sent,!s.success){r.next=20;break}t.$set(a,"uploaded",!0),t.$set(a,"cloudUrl",s.url),t.$set(a,"cloudPath",s.cloudPath),t.$set(a,"uploading",!1),n.push({url:s.cloudPath,description:"审核照片",timestamp:new Date}),r.next=21;break;case 20:throw new Error(s.error||"上传失败");case 21:r.next=28;break;case 23:throw r.prev=23,r.t0=r["catch"](7),t.$set(a,"uploading",!1),e.showModal({title:"上传失败",content:"审核照片 ".concat(o+1," 上传失败，请重试"),showCancel:!1}),r.t0;case 28:o++,r.next=2;break;case 31:return r.abrupt("return",n);case 32:case"end":return r.stop()}}),r,null,[[7,23]])})))()},uploadSinglePhoto:function(e){var t=this;return(0,a.default)(i.default.mark((function r(){var n,o,a,s,u;return i.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.prev=0,n=Date.now(),o=Math.random().toString(36).substring(2,8),a="6s/review/".concat(t.taskInfo.id||"unknown","/").concat(n,"_").concat(o,".jpg"),r.next=6,c.default.uploadToCloud(e.url,a);case 6:if(s=r.sent,!s||!s.fileID){r.next=14;break}return r.next=10,c.default.getFileInfo(s.fileID);case 10:return u=r.sent,r.abrupt("return",{success:!0,cloudPath:s.fileID,url:u.tempFileURL||s.fileID,size:s.actualSize});case 14:throw new Error("上传返回结果异常");case 15:r.next=20;break;case 17:return r.prev=17,r.t0=r["catch"](0),r.abrupt("return",{success:!1,error:r.t0.message});case 20:case"end":return r.stop()}}),r,null,[[0,17]])})))()},submitReview:function(){var t=this;return(0,a.default)(i.default.mark((function r(){var n;return i.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(t.canSubmit){r.next=2;break}return r.abrupt("return");case 2:n="approved"===t.reviewForm.result?"复查通过":"需重新整改",e.showModal({title:"确认提交",content:'确定审核结果为"'.concat(n,'"吗？'),success:function(){var e=(0,a.default)(i.default.mark((function e(r){return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!r.confirm){e.next=3;break}return e.next=3,t.doSubmit();case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()});case 4:case"end":return r.stop()}}),r)})))()},doSubmit:function(){var t=this;return(0,a.default)(i.default.mark((function r(){var n,o,a,s,c;return i.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(t.isSubmitting=!0,r.prev=1,n=[],!(t.reviewForm.photos.length>0)){r.next=7;break}return r.next=6,t.uploadReviewPhotos();case 6:n=r.sent;case 7:return o={id:t.taskId,review_result:t.reviewForm.result,review_comments:t.reviewForm.comment.trim()||"",review_photos:n,review_date:(new Date).toISOString(),final_rating:"approved"===t.reviewForm.result?t.reviewForm.rating:0},r.next=10,(0,u.callCloudFunction)("hygiene-rectification",{action:"reviewRectification",data:o});case 10:if(a=r.sent,!a||!a.success){r.next=19;break}s="approved"===t.reviewForm.result?"复查通过":"需重新整改",e.showToast({title:"审核".concat(s),icon:"success"}),e.$emit("rectificationReviewUpdated",{taskId:t.taskId,result:t.reviewForm.result,areaId:t.taskInfo.id,areaName:t.taskInfo.areaName}),e.$emit("rectificationRecordUpdated",{taskId:t.taskId,areaId:t.taskInfo.id,mode:"review",result:t.reviewForm.result}),setTimeout((function(){e.navigateBack({delta:1})}),1500),r.next=20;break;case 19:throw new Error((null===a||void 0===a?void 0:a.message)||"提交审核结果失败");case 20:r.next=27;break;case 22:r.prev=22,r.t0=r["catch"](1),c="提交失败，请重试",r.t0.message&&(c=r.t0.message.includes("未登录")?"请先登录":r.t0.message.includes("权限")?"您没有权限审核该任务":r.t0.message),e.showModal({title:"提交失败",content:c,showCancel:!1,confirmText:"知道了"});case 27:return r.prev=27,t.isSubmitting=!1,r.finish(27);case 30:case"end":return r.stop()}}),r,null,[[1,22,27,30]])})))()},onPhotoError:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"rectificationPhotos";t="rectificationPhotos"===r?this.taskInfo.rectificationPhotos:"issuePhotos"===r?this.taskInfo.issuePhotos:this.reviewForm.photos;var n=t[e];if("object"===(0,s.default)(n))this.$set(n,"loadError",!0);else{var o="rectificationPhotos"===r||"issuePhotos"===r?{url:n,loadError:!0}:{url:n,uploaded:!1,cloudUrl:"",cloudPath:"",uploading:!1,belongsToRecord:!1,loadError:!0};this.$set(t,e,o)}},retryPhotoLoad:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"rectificationPhotos";t="rectificationPhotos"===r?this.taskInfo.rectificationPhotos:"issuePhotos"===r?this.taskInfo.issuePhotos:this.reviewForm.photos;var n=t[e];"object"===(0,s.default)(n)&&this.$set(n,"loadError",!1)},toggleAutoUpload:function(){this.autoUpload=!this.autoUpload,e.showToast({title:this.autoUpload?"已开启自动上传":"已关闭自动上传",icon:"none",duration:1500})}}};t.default=l}).call(this,r("df3c")["default"],r("861b")["uniCloud"])},c8d9:function(e,t,r){"use strict";r.r(t);var n=r("c128"),o=r.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(i);t["default"]=o.a},ddbd:function(e,t,r){}},[["107f","common/runtime","common/vendor"]]]);