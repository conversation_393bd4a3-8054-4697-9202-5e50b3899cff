<view class="content"><view class="form-container"><view class="form-item"><text class="label">姓名：</text><input class="input" placeholder="{{ready?'请输入您的姓名':''}}" placeholder-class="ph" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['formData']]]]]}}" value="{{formData.name}}" bindinput="__e"/></view><view class="form-item"><text class="label">找茬项目：</text><picker class="picker" mode="selector" range="{{projectOptions}}" data-event-opts="{{[['change',[['bindProjectChange',['$event']]]]]}}" bindchange="__e"><view class="{{[formData.project?'picker-text-selected':'picker-text']}}">{{formData.project||'请选择找茬项目'}}</view></picker></view><view class="form-item"><text class="label">问题描述：</text><textarea class="textarea" placeholder="{{ready?'请描述您发现的问题':''}}" placeholder-class="ph" data-event-opts="{{[['input',[['__set_model',['$0','description','$event',[]],['formData']]]]]}}" value="{{formData.description}}" bindinput="__e"></textarea></view><view class="form-item"><text class="label">上传图片（最多5张）：</text><view class="image-uploader"><block wx:for="{{images}}" wx:for-item="image" wx:for-index="index" wx:key="index"><view class="image-preview"><image class="preview-image" src="{{image.url||image}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewImages',[index]]]]]}}" bindtap="__e"></image><view data-event-opts="{{[['tap',[['deleteImage',[index]]]]]}}" class="delete-btn" bindtap="__e">×</view><block wx:if="{{image.progress!==undefined&&image.progress<100}}"><view class="upload-progress"><view class="progress-text">{{image.progress+"%"}}</view></view></block></view></block><block wx:if="{{$root.g0<5}}"><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="upload-btn" bindtap="__e"><text class="plus-icon">+</text></view></block></view></view><button data-event-opts="{{[['tap',[['submitForm',['$event']]]]]}}" class="submit-btn" bindtap="__e">提交反馈</button></view></view>