{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/patrol_pkg/record/detail.vue?acd9", "webpack:///D:/Xwzc/pages/patrol_pkg/record/detail.vue?0503", "webpack:///D:/Xwzc/pages/patrol_pkg/record/detail.vue?4a32", "webpack:///D:/Xwzc/pages/patrol_pkg/record/detail.vue?eb70", "uni-app:///pages/patrol_pkg/record/detail.vue", "webpack:///D:/Xwzc/pages/patrol_pkg/record/detail.vue?2c6e", "webpack:///D:/Xwzc/pages/patrol_pkg/record/detail.vue?2dc1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "PEmptyState", "data", "recordId", "record", "loading", "markers", "photoList", "mapCenter", "distanceMsg", "circles", "statusText", "statusColor", "onLoad", "uni", "title", "icon", "setTimeout", "methods", "loadRecordDetail", "PatrolApi", "name", "action", "record_id", "fields", "res", "checkTime", "pointRange", "id", "latitude", "longitude", "callout", "content", "color", "fontSize", "borderRadius", "bgColor", "padding", "display", "iconPath", "width", "height", "fillColor", "radius", "strokeWidth", "distance", "duration", "processPhotoList", "getStatusIcon", "getStatusText", "getStatusDesc", "previewImage", "current", "urls", "navigateBack", "calculateDistance", "Math", "deg2rad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AAAgmB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACsHpnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;MACAC;QACAC;QACAC;MACA;MACAC;QACAH;MACA;IACA;EACA;EACAI;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAIAC;kBACAC;kBACAC;kBACApB;oBACAqB;oBACA;oBACAC;oBACA;oBACA,wFACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;kBAEA;gBACA;cAAA;gBApBAC;gBAsBA;kBACA;kBACA;kBACA;kBACA;;kBAEA;kBACA;oBACA;oBACA;;oBAEA;oBACA;sBACA;sBACA;oBACA;sBACA;sBACA;oBACA;sBACA;sBACA;oBACA;;oBAEA;oBACA;oBAEA;oBACA;;oBAEA;oBACA;sBACA;sBACAC;sBACA;oBACA;sBACA;sBACA;wBACAA;wBACA;0BACA;wBACA;sBACA;wBACA;sBAAA;oBAEA;sBACA;oBACA;oBAEA;oBACA;;oBAEA;oBACA;sBACA,kEACA;oBACA;sBACA;sBACA;oBACA;;oBAEA;oBACA;sBACA;sBACA;oBACA;sBACA;sBACA;oBACA;sBACA;sBACA;oBACA;sBACA;sBACA;oBACA;sBACA;sBACA;oBACA;kBACA;;kBAEA;kBACA;;kBAEA;kBACA;oBACA;;oBAEA;oBACA;sBACA;sBACAC;sBAEA;sBACA;wBACAC;wBACAC;wBACAC;wBACAf;wBACAgB;0BACAC;0BACAC;0BACAC;0BACAC;0BACAC;0BACAC;0BACAC;wBACA;wBACAC;wBACAC;wBACAC;sBACA;;sBAEA;sBACA;wBACAZ;wBACAC;wBACAG;wBAAA;wBACAS;wBAAA;wBACAC;wBACAC;sBACA;oBACA;;oBAEA;oBACA;sBACA;wBACAhB;wBACAC;wBACAC;wBACAf;wBACAwB;wBAAA;wBACAC;wBACAC;wBACAV;0BACAC;0BACAC;0BACAC;0BACAC;0BACAC;0BACAC;0BACAC;wBACA;sBACA;oBACA;;oBAEA;oBACA;sBACA;sBACA;wBACA;0BACAT;0BACAC;wBACA;sBACA;sBACA;sBAAA,KACA;wBACA;0BACAD;0BACAC;wBACA;sBACA;;sBAEA;sBACA;wBACAe,mCACA,gCACA,iCACA,mCACA,mCACA,EAEA;wBACA;0BACA;wBACA;0BACA;wBACA;;wBAEA;wBACAlB;wBACA;0BACA;wBACA;sBACA;oBACA;kBACA;gBACA;kBACA;kBACAb;oBACAC;oBACAC;oBACA8B;kBACA;;kBAEA;kBACA7B;oBACAH;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACAA;kBACAC;kBACAC;kBACA8B;gBACA;;gBAEA;gBACA7B;kBACAH;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiC;MACA;QACA;QACA;UACA;YACA;UACA;YACA;UACA;YACA;YACA;cACA;gBACA;cACA;YACA;YACA;YACA;UACA;YACA;YACA;UACA;QACA;UAAA;QAAA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACArC;UACAsC;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACAxC;IACA;IAEA;IACAyC;MACA;MACA;MACA;MACA,QACAC,0CACAA,8DACAA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9eA;AAAA;AAAA;AAAA;AAA2oC,CAAgB,inCAAG,EAAC,C;;;;;;;;;;;ACA/pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/patrol_pkg/record/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/patrol_pkg/record/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=ede4eedc&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/patrol_pkg/record/detail.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=template&id=ede4eedc&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.record ? _vm.getStatusIcon(_vm.record.status) : null\n  var m1 = _vm.record ? _vm.getStatusText(_vm.record.status) : null\n  var m2 = _vm.record ? _vm.getStatusDesc(_vm.record.status) : null\n  var g0 = _vm.record && _vm.record.photos && _vm.record.photos.length > 0\n  var g1 =\n    _vm.record &&\n    _vm.record.location &&\n    _vm.record.status !== 3 &&\n    _vm.record.status !== 4 &&\n    _vm.markers.length > 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"detail-container\">\n\t\t<!-- 状态展示 -->\n\t\t<view class=\"status-section\" v-if=\"record\">\n\t\t\t<view :class=\"['status-icon', {\n\t\t\t\t'status-0': record.status === 0,\n\t\t\t\t'status-1': record.status === 1,\n\t\t\t\t'status-2': record.status === 2,\n\t\t\t\t'status-3': record.status === 3\n\t\t\t}]\">\n\t\t\t\t<uni-icons :type=\"getStatusIcon(record.status)\" size=\"24\" color=\"#FFFFFF\"></uni-icons>\n\t\t\t</view>\n\t\t\t<view class=\"status-info\">\n\t\t\t\t<view class=\"status-text\">{{ getStatusText(record.status) }}</view>\n\t\t\t\t<view class=\"status-desc\">{{ getStatusDesc(record.status) }}</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 添加缺卡提示 -->\n\t\t<view class=\"status-box\" v-if=\"record && (record.status === 3 || record.status === 4)\">\n\t\t\t<view class=\"card-title\">打卡状态</view>\n\t\t\t<view class=\"miss-card-notice\">\n\t\t\t\t<uni-icons type=\"info\" color=\"#F56C6C\" size=\"24\"></uni-icons>\n\t\t\t\t<text>该记录为缺卡记录，无打卡位置信息</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 基本信息 -->\n\t\t<view class=\"info-card\" v-if=\"record\">\n\t\t\t<view class=\"card-title\">基本信息</view>\n\t\t\t<view class=\"info-list\">\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<view class=\"info-label\">巡视人员</view>\n\t\t\t\t\t<view class=\"info-value\">{{ record.user_name }}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<view class=\"info-label\">巡视班次</view>\n\t\t\t\t\t<view class=\"info-value\">{{ record.shift_name }}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<view class=\"info-label\">点位名称</view>\n\t\t\t\t\t<view class=\"info-value\">{{ record.point_name }}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<view class=\"info-label\">线路名称</view>\n\t\t\t\t\t<view class=\"info-value\">{{ record.route_name }}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<view class=\"info-label\">打卡时间</view>\n\t\t\t\t\t<view class=\"info-value\">{{ record.check_time }}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<view class=\"info-label\">打卡地点</view>\n\t\t\t\t\t<view class=\"info-value\">{{ record.address }}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<view class=\"info-label\">打卡状态</view>\n\t\t\t\t\t<view class=\"info-value\" :style=\"{color: statusColor}\">{{ statusText }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 照片展示 -->\n\t\t<view class=\"photo-card\" v-if=\"record && record.photos && record.photos.length > 0\">\n\t\t\t<view class=\"card-title\">巡视照片</view>\n\t\t\t<view class=\"photo-grid\">\n\t\t\t\t<view class=\"photo-item\" v-for=\"(item, index) in photoList\" :key=\"index\" @click=\"previewImage(index)\">\n\t\t\t\t\t<image class=\"photo-image\" :src=\"item\" mode=\"aspectFill\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 备注信息 -->\n\t\t<view class=\"remark-card\" v-if=\"record && (record.notes || record.remark)\">\n\t\t\t<view class=\"card-title\">备注信息</view>\n\t\t\t<view class=\"remark-content\">\n\t\t\t\t{{ record.notes || record.remark }}\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 异常说明 -->\n\t\t<view class=\"exception-card\" v-if=\"record && (record.exception_reason || (record.status > 1 && record.notes))\">\n\t\t\t<view class=\"card-title\">异常说明</view>\n\t\t\t<view class=\"exception-content\">\n\t\t\t\t{{ record.exception_reason || record.notes || '无异常说明' }}\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 位置信息 -->\n\t\t<view class=\"map-card\" v-if=\"record && record.location && record.status !== 3 && record.status !== 4 && markers.length > 0\">\n\t\t\t<view class=\"card-title\">位置信息</view>\n\t\t\t<map \n\t\t\t\tclass=\"location-map\" \n\t\t\t\t:latitude=\"mapCenter ? mapCenter.latitude : (record.location ? record.location.latitude : 0)\" \n\t\t\t\t:longitude=\"mapCenter ? mapCenter.longitude : (record.location ? record.location.longitude : 0)\"\n\t\t\t\t:markers=\"markers\"\n\t\t\t\t:circles=\"circles\"\n\t\t\t\tscale=\"16\"\n\t\t\t></map>\n\t\t\t\n\t\t\t<!-- 地图下方显示距离信息，不再重复显示 -->\n\t\t\t<view class=\"distance-info\" v-if=\"distanceMsg\">\n\t\t\t\t<text class=\"distance-text\">{{ distanceMsg }}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 加载中 -->\n\t\t<view class=\"loading-container\" v-if=\"loading\">\n\t\t\t<view class=\"loading-spinner\"></view>\n\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t</view>\n\t\t\n\t\t<!-- 数据为空 -->\n\t\t<p-empty-state v-if=\"!loading && !record\" text=\"记录不存在或已被删除\"></p-empty-state>\n\t</view>\n</template>\n\n<script>\nimport PatrolApi from '@/utils/patrol-api.js';\nimport PEmptyState from '@/components/p-empty-state/p-empty-state.vue';\n\nexport default {\n\tcomponents: {\n\t\tPEmptyState\n\t},\n\tdata() {\n\t\treturn {\n\t\t\trecordId: '',\n\t\t\trecord: null,\n\t\t\tloading: true,\n\t\t\tmarkers: [],\n\t\t\tphotoList: [], // 处理后的照片列表\n\t\t\tmapCenter: null,\n\t\t\tdistanceMsg: '',\n\t\t\tcircles: [],\n\t\t\tstatusText: '',\n\t\t\tstatusColor: ''\n\t\t};\n\t},\n\tonLoad(options) {\n\t\tif (options.id) {\n\t\t\tthis.recordId = options.id;\n\t\t\tthis.loadRecordDetail();\n\t\t} else {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '记录ID不存在',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.navigateBack();\n\t\t\t}, 1500);\n\t\t}\n\t},\n\tmethods: {\n\t\t// 加载记录详情\n\t\tasync loadRecordDetail() {\n\t\t\tthis.loading = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 直接调用通用的call方法获取record详情\n\t\t\t\tconst res = await PatrolApi.call({\n\t\t\t\t\tname: 'patrol-record',\n\t\t\t\t\taction: 'getRecordDetail',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\trecord_id: this.recordId,\n\t\t\t\t\t\t// 优化：添加字段过滤，只获取详情页面需要的字段\n\t\t\t\t\t\tfields: [\n\t\t\t\t\t\t\t// 记录基本信息\n\t\t\t\t\t\t\t'_id', 'point_id', 'point_name', 'status', 'round', 'user_id', 'user_name', 'username',\n\t\t\t\t\t\t\t'checkin_time', 'check_time', 'patrol_date', 'task_id', 'shift_name',\n\t\t\t\t\t\t\t// 详情页面需要的字段\n\t\t\t\t\t\t\t'photos', 'location', 'address', 'remarks', 'notes', 'remark',\n\t\t\t\t\t\t\t// 点位信息（只获取必要字段）\n\t\t\t\t\t\t\t'point.name', 'point.location', 'point.range',\n\t\t\t\t\t\t\t// 任务信息（只获取必要字段）\n\t\t\t\t\t\t\t'task.name', 'task.route_name', 'task.shift_name', 'task.user_name', 'task.executor_name', 'task.creator_name',\n\t\t\t\t\t\t\t// 用户信息（如果有）\n\t\t\t\t\t\t\t'user.name', 'user.username', 'user.nickname'\n\t\t\t\t\t\t]\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.code === 0 && res.data) {\n\t\t\t\t\t// 处理记录数据，确保所有关键字段都有值\n\t\t\t\t\tthis.record = res.data.record || {};\n\t\t\t\t\tthis.pointInfo = res.data.point || {};\n\t\t\t\t\tthis.taskInfo = res.data.task || {};\n\t\t\t\t\t\n\t\t\t\t\t// 补充一些可能缺失的字段，确保UI不显示undefined\n\t\t\t\t\tif (this.record) {\n\t\t\t\t\t\tthis.record.point_name = this.record.point_name || this.pointInfo.name || '未知点位';\n\t\t\t\t\t\tthis.record.route_name = this.record.route_name || (this.taskInfo.route_name || this.taskInfo.name) || '未知线路';\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 更全面地尝试获取巡视人员信息\n\t\t\t\t\t\tif (this.record.user) {\n\t\t\t\t\t\t\t// 如果record中有完整的user对象\n\t\t\t\t\t\t\tthis.record.user_name = this.record.user.name || this.record.user.username || this.record.user.nickname || this.record.user_name;\n\t\t\t\t\t\t} else if (this.record.username) {\n\t\t\t\t\t\t\t// 如果有username字段\n\t\t\t\t\t\t\tthis.record.user_name = this.record.username;\n\t\t\t\t\t\t} else if (this.record.user_id && this.taskInfo) {\n\t\t\t\t\t\t\t// 优化：使用已获取的任务信息，避免额外的API调用\n\t\t\t\t\t\t\tthis.record.user_name = this.taskInfo.user_name || this.taskInfo.executor_name || this.taskInfo.creator_name || '未知人员';\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 如果以上方法都失败，使用默认值\n\t\t\t\t\t\tthis.record.user_name = this.record.user_name || '未知人员';\n\t\t\t\t\t\t\n\t\t\t\t\t\tthis.record.shift_name = this.record.shift_name || this.taskInfo.shift_name || '未知班次';\n\t\t\t\t\t\tthis.record.round = this.record.round || 1;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 修正时间显示，确保正确转换时区，兼容check_time和checkin_time\n\t\t\t\t\t\tif (this.record.checkin_time) {\n\t\t\t\t\t\t\t// 使用本地时间格式化，避免时区问题\n\t\t\t\t\t\t\tconst checkTime = new Date(this.record.checkin_time);\n\t\t\t\t\t\t\tthis.record.check_time = `${checkTime.getHours().toString().padStart(2, '0')}:${checkTime.getMinutes().toString().padStart(2, '0')}:${checkTime.getSeconds().toString().padStart(2, '0')}`;\n\t\t\t\t\t\t} else if (this.record.check_time && this.record.check_time !== '未记录') {\n\t\t\t\t\t\t\t// 如果有check_time（缺卡记录通常使用这个字段）\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\tconst checkTime = new Date(this.record.check_time);\n\t\t\t\t\t\t\t\tif (!isNaN(checkTime.getTime())) {\n\t\t\t\t\t\t\t\t\tthis.record.check_time = `${checkTime.getHours().toString().padStart(2, '0')}:${checkTime.getMinutes().toString().padStart(2, '0')}:${checkTime.getSeconds().toString().padStart(2, '0')}`;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\t\t// 错误处理，不打印日志\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.record.check_time = '未记录';\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tthis.record.notes = this.record.notes || this.record.remark || '';\n\t\t\t\t\t\tthis.record.status = this.record.status !== undefined ? this.record.status : 0;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 处理地点信息\n\t\t\t\t\t\tif (this.record.location && !this.record.address) {\n\t\t\t\t\t\t\tthis.record.address = this.record.location.address || \n\t\t\t\t\t\t\t\t`${this.record.location.latitude.toFixed(6)}, ${this.record.location.longitude.toFixed(6)}`;\n\t\t\t\t\t\t} else if (!this.record.location && (this.record.status === 3 || this.record.status === 4)) {\n\t\t\t\t\t\t\t// 缺卡记录没有位置信息\n\t\t\t\t\t\t\tthis.record.address = '无打卡位置信息';\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 优化状态显示\n\t\t\t\t\t\tif (this.record.status === 3) {\n\t\t\t\t\t\t\tthis.statusText = '缺卡';\n\t\t\t\t\t\t\tthis.statusColor = '#F56C6C';\n\t\t\t\t\t\t} else if (this.record.status === 4) {\n\t\t\t\t\t\t\tthis.statusText = '缺卡';\n\t\t\t\t\t\t\tthis.statusColor = '#F56C6C';\n\t\t\t\t\t\t} else if (this.record.status === 1) {\n\t\t\t\t\t\t\tthis.statusText = '正常';\n\t\t\t\t\t\t\tthis.statusColor = '#67C23A';\n\t\t\t\t\t\t} else if (this.record.status === 2) {\n\t\t\t\t\t\t\tthis.statusText = '异常';\n\t\t\t\t\t\t\tthis.statusColor = '#E6A23C';\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.statusText = '未知';\n\t\t\t\t\t\t\tthis.statusColor = '#909399';\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 处理照片列表\n\t\t\t\t\tthis.processPhotoList();\n\t\t\t\t\t\n\t\t\t\t\t// 如果有位置信息，设置地图标记，同时显示点位和打卡位置\n\t\t\t\t\tif (this.record && this.record.status !== 3 && this.record.status !== 4) {\n\t\t\t\t\t\tthis.markers = [];\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 1. 添加点位位置标记（如果有）\n\t\t\t\t\t\tif (this.pointInfo && this.pointInfo.location) {\n\t\t\t\t\t\t\t// 获取点位范围\n\t\t\t\t\t\t\tconst pointRange = this.pointInfo.range || 100; // 默认范围100米\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 添加点位标记\n\t\t\t\t\t\t\tthis.markers.push({\n\t\t\t\t\t\t\t\tid: 1,\n\t\t\t\t\t\t\t\tlatitude: this.pointInfo.location.latitude,\n\t\t\t\t\t\t\t\tlongitude: this.pointInfo.location.longitude,\n\t\t\t\t\t\t\t\ttitle: this.record.point_name,\n\t\t\t\t\t\t\t\tcallout: {\n\t\t\t\t\t\t\t\t\tcontent: this.record.point_name,\n\t\t\t\t\t\t\t\t\tcolor: '#FFFFFF',\n\t\t\t\t\t\t\t\t\tfontSize: 12,\n\t\t\t\t\t\t\t\t\tborderRadius: 4,\n\t\t\t\t\t\t\t\t\tbgColor: '#1677FF',\n\t\t\t\t\t\t\t\t\tpadding: 5,\n\t\t\t\t\t\t\t\t\tdisplay: 'ALWAYS'\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\ticonPath: '/static/map/marker.png',\n\t\t\t\t\t\t\t\twidth: 32,\n\t\t\t\t\t\t\t\theight: 32\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 添加点位范围圆圈\n\t\t\t\t\t\t\tthis.circles = [{\n\t\t\t\t\t\t\t\tlatitude: this.pointInfo.location.latitude,\n\t\t\t\t\t\t\t\tlongitude: this.pointInfo.location.longitude,\n\t\t\t\t\t\t\t\tcolor: '#1677FF33', // 半透明蓝色\n\t\t\t\t\t\t\t\tfillColor: '#1677FF11', // 更透明的填充色\n\t\t\t\t\t\t\t\tradius: pointRange,\n\t\t\t\t\t\t\t\tstrokeWidth: 1\n\t\t\t\t\t\t\t}];\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 2. 添加打卡位置标记（如果有）- 使用默认小点点标记\n\t\t\t\t\t\tif (this.record.location) {\n\t\t\t\t\t\t\tthis.markers.push({\n\t\t\t\t\t\t\t\tid: 2,\n\t\t\t\t\t\t\t\tlatitude: this.record.location.latitude,\n\t\t\t\t\t\t\t\tlongitude: this.record.location.longitude,\n\t\t\t\t\t\t\t\ttitle: \"打卡位置\",\n\t\t\t\t\t\t\t\ticonPath: '/static/map/location.png', // 添加图标路径\n\t\t\t\t\t\t\t\twidth: 24,\n\t\t\t\t\t\t\t\theight: 24,\n\t\t\t\t\t\t\t\tcallout: {\n\t\t\t\t\t\t\t\t\tcontent: '打卡位置',\n\t\t\t\t\t\t\t\t\tcolor: '#FFFFFF',\n\t\t\t\t\t\t\t\t\tfontSize: 12,\n\t\t\t\t\t\t\t\t\tborderRadius: 4,\n\t\t\t\t\t\t\t\t\tbgColor: '#FF5722',\n\t\t\t\t\t\t\t\t\tpadding: 5,\n\t\t\t\t\t\t\t\t\tdisplay: 'ALWAYS'\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 3. 计算中心点，调整地图缩放范围以包含所有标记\n\t\t\t\t\t\tif (this.markers.length > 0) {\n\t\t\t\t\t\t\t// 如果有点位信息，以点位为中心\n\t\t\t\t\t\t\tif (this.pointInfo && this.pointInfo.location) {\n\t\t\t\t\t\t\t\tthis.mapCenter = {\n\t\t\t\t\t\t\t\t\tlatitude: this.pointInfo.location.latitude,\n\t\t\t\t\t\t\t\t\tlongitude: this.pointInfo.location.longitude\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t} \n\t\t\t\t\t\t\t// 否则使用打卡位置\n\t\t\t\t\t\t\telse if (this.record.location) {\n\t\t\t\t\t\t\t\tthis.mapCenter = {\n\t\t\t\t\t\t\t\t\tlatitude: this.record.location.latitude,\n\t\t\t\t\t\t\t\t\tlongitude: this.record.location.longitude\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 计算打卡位置与点位位置的距离\n\t\t\t\t\t\t\tif (this.markers.length === 2 && this.record.location && this.pointInfo && this.pointInfo.location) {\n\t\t\t\t\t\t\t\tconst distance = this.calculateDistance(\n\t\t\t\t\t\t\t\t\tthis.record.location.latitude,\n\t\t\t\t\t\t\t\t\tthis.record.location.longitude,\n\t\t\t\t\t\t\t\t\tthis.pointInfo.location.latitude,\n\t\t\t\t\t\t\t\t\tthis.pointInfo.location.longitude\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 格式化距离显示\n\t\t\t\t\t\t\t\tif (distance > 1000) {\n\t\t\t\t\t\t\t\t\tthis.distanceMsg = `打卡位置距离点位: ${(distance/1000).toFixed(2)}公里`;\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthis.distanceMsg = `打卡位置距离点位: ${Math.round(distance)}米`;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 如果距离超过允许范围，添加提示\n\t\t\t\t\t\t\t\tconst pointRange = this.pointInfo.range || 100; // 默认范围100米\n\t\t\t\t\t\t\t\tif (distance > pointRange) {\n\t\t\t\t\t\t\t\t\tthis.distanceMsg += ` (超出${Math.round(distance - pointRange)}米)`;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// 显示更友好的错误提示\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.message || '获取记录详情失败',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 短暂延迟后返回上一页\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t}, 1500);\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\t// 显示更友好的错误提示\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载记录详情失败，请稍后再试',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 短暂延迟后返回上一页\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t}, 1500);\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 处理照片列表\n\t\tprocessPhotoList() {\n\t\t\tif (this.record && this.record.photos && this.record.photos.length > 0) {\n\t\t\t\t// 确保照片列表中全是字符串\n\t\t\t\tthis.photoList = this.record.photos.map(photo => {\n\t\t\t\t\tif (typeof photo === 'string') {\n\t\t\t\t\t\treturn photo;\n\t\t\t\t\t} else if (photo && photo.url) {\n\t\t\t\t\t\treturn photo.url;\n\t\t\t\t\t} else if (photo && typeof photo === 'object') {\n\t\t\t\t\t\t// 尝试找到对象中的url字段\n\t\t\t\t\t\tfor (const key in photo) {\n\t\t\t\t\t\t\tif (key.includes('url') || key.includes('path')) {\n\t\t\t\t\t\t\t\treturn photo[key];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 无法找到有效的照片URL，返回空字符串\n\t\t\t\t\t\treturn '';\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 无效的照片数据\n\t\t\t\t\t\treturn '';\n\t\t\t\t\t}\n\t\t\t\t}).filter(url => url); // 过滤掉空字符串\n\t\t\t} else {\n\t\t\t\tthis.photoList = [];\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 获取状态图标\n\t\tgetStatusIcon(status) {\n\t\t\tconst iconMap = {\n\t\t\t\t1: 'checkmarkempty',\n\t\t\t\t2: 'info',\n\t\t\t\t3: 'closeempty'\n\t\t\t};\n\t\t\t\n\t\t\treturn iconMap[status] || 'help';\n\t\t},\n\t\t\n\t\t// 获取状态文本\n\t\tgetStatusText(status) {\n\t\t\tconst statusMap = {\n\t\t\t\t0: '未打卡',\n\t\t\t\t1: '已打卡',\n\t\t\t\t2: '超时打卡',\n\t\t\t\t3: '异常打卡',\n\t\t\t\t4: '缺卡'\n\t\t\t};\n\t\t\treturn statusMap[status] || '未知状态';\n\t\t},\n\t\t\n\t\t// 获取状态描述\n\t\tgetStatusDesc(status) {\n\t\t\tconst descMap = {\n\t\t\t\t0: '尚未进行打卡',\n\t\t\t\t1: '已在规定时间内完成打卡',\n\t\t\t\t2: '已超出规定时间完成打卡',\n\t\t\t\t3: '打卡时出现异常情况',\n\t\t\t\t4: '未在规定时间内完成打卡'\n\t\t\t};\n\t\t\treturn descMap[status] || '';\n\t\t},\n\t\t\n\t\t// 预览图片\n\t\tpreviewImage(index) {\n\t\t\tif (this.photoList.length > 0) {\n\t\t\t\tuni.previewImage({\n\t\t\t\t\tcurrent: index,\n\t\t\t\t\turls: this.photoList\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 返回列表\n\t\tnavigateBack() {\n\t\t\tuni.navigateBack();\n\t\t},\n\t\t\n\t\t// 计算两点之间的距离（米）\n\t\tcalculateDistance(lat1, lng1, lat2, lng2) {\n\t\t\tconst R = 6371000; // 地球半径，单位米\n\t\t\tconst dLat = this.deg2rad(lat2 - lat1);\n\t\t\tconst dLng = this.deg2rad(lng2 - lng1);\n\t\t\tconst a = \n\t\t\t\tMath.sin(dLat/2) * Math.sin(dLat/2) +\n\t\t\t\tMath.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) * \n\t\t\t\tMath.sin(dLng/2) * Math.sin(dLng/2);\n\t\t\tconst c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n\t\t\tconst distance = R * c; // 距离，单位米\n\t\t\treturn distance;\n\t\t},\n\t\t\n\t\t// 角度转弧度\n\t\tdeg2rad(deg) {\n\t\t\treturn deg * (Math.PI/180);\n\t\t}\n\t}\n};\n</script>\n\n<style lang=\"scss\">\n.detail-container {\n\tmin-height: 100vh;\n\tbackground-color: #F5F7FA;\n\tpadding: 20rpx;\n\tpadding-bottom: 50rpx; /* 增加底部内边距 */\n}\n\n.status-section {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 30rpx;\n\tbackground-color: #FFFFFF;\n\tborder-radius: 12rpx;\n\tbox-shadow: 0 2rpx 8rpx rgba(22, 119, 255, 0.3);\n\tmargin-bottom: 20rpx;\n}\n\n.status-icon {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tmargin-right: 20rpx;\n}\n\n.status-0 {\n\tbackground-color: #8C8C8C;\n}\n\n.status-1 {\n\tbackground-color: #1677FF;\n}\n\n.status-2 {\n\tbackground-color: #FAAD14;\n}\n\n.status-3 {\n\tbackground-color: #F5222D;\n}\n\n.status-info {\n\tflex: 1;\n}\n\n.status-text {\n\tfont-size: 30rpx;\n\tfont-weight: 600;\n\tcolor: #333333;\n\tmargin-bottom: 8rpx;\n}\n\n.status-desc {\n\tfont-size: 24rpx;\n\tcolor: #8F959E;\n\tline-height: 1.4;\n}\n\n.info-card, .photo-card, .remark-card, .exception-card, .map-card {\n\tmargin-bottom: 20rpx;\n\tbackground-color: #FFFFFF;\n\tborder-radius: 12rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 2rpx 8rpx rgba(22, 119, 255, 0.1);\n}\n\n.card-title {\n\tfont-size: 30rpx; /* 增大标题字体 */\n\tfont-weight: 600; /* 加粗标题 */\n\tcolor: #333333;\n\tpadding: 20rpx 24rpx;\n\tborder-bottom: 1rpx solid #EEEEEE;\n}\n\n.info-list {\n\tpadding: 20rpx 30rpx; /* 增加左右内边距 */\n}\n\n.info-item {\n\tmargin-bottom: 22rpx; /* 调整条目间距 */\n\tpadding-bottom: 22rpx; /* 调整内边距 */\n\tborder-bottom: 1rpx solid #EEEEEE; /* 添加分隔线 */\n\t\n\t&:last-child {\n\t\tmargin-bottom: 0;\n\t\tborder-bottom: none;\n\t\tpadding-bottom: 0;\n\t}\n}\n\n.info-label {\n\tdisplay: block;\n\tfont-size: 26rpx; /* 减小标签字体 */\n\tcolor: #8F959E;\n\tmargin-bottom: 8rpx; /* 减小标签与值的间距 */\n}\n\n.info-value {\n\tdisplay: block;\n\tfont-size: 30rpx;\n\tcolor: #333333;\n\tfont-weight: 400; /* 恢复正常字重 */\n\tline-height: 1.4;\n}\n\n.photo-grid {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tpadding: 20rpx 24rpx;\n}\n\n.photo-item {\n\twidth: calc((100% - 40rpx) / 3);\n\theight: 200rpx;\n\tmargin-right: 20rpx;\n\tmargin-bottom: 20rpx;\n\toverflow: hidden;\n\tborder-radius: 8rpx;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\t\n\t&:nth-child(3n) {\n\t\tmargin-right: 0;\n\t}\n}\n\n.photo-image {\n\twidth: 100%;\n\theight: 100%;\n\tborder-radius: 8rpx;\n}\n\n.remark-content, .exception-content {\n\tpadding: 20rpx 24rpx;\n\tfont-size: 28rpx;\n\tcolor: #333333;\n\tline-height: 1.6;\n}\n\n.location-map {\n\twidth: 100%;\n\theight: 400rpx;\n}\n\n.loading-container {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 100rpx 0;\n}\n\n.loading-spinner {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder: 6rpx solid #F0F0F0;\n\tborder-top: 6rpx solid #1677FF;\n\tborder-radius: 50%;\n\tanimation: spin 1s linear infinite;\n\tmargin-bottom: 20rpx;\n}\n\n@keyframes spin {\n\t0% { transform: rotate(0deg); }\n\t100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n\tfont-size: 28rpx;\n\tcolor: #8F959E;\n}\n\n.distance-info {\n\tpadding: 20rpx 24rpx;\n\tfont-size: 28rpx;\n\tcolor: #333333;\n\tline-height: 1.6;\n\tborder-top: 1rpx solid #F0F0F0;\n}\n\n.distance-text {\n\tdisplay: block;\n\tfont-size: 28rpx;\n\tcolor: #333333; /* 保留更深的颜色 */\n\tfont-weight: 400; /* 恢复正常字重 */\n}\n\n.status-box {\n\tmargin-bottom: 20rpx;\n\tbackground-color: #FFFFFF;\n\tborder-radius: 12rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 2rpx 8rpx rgba(22, 119, 255, 0.1);\n}\n\n.miss-card-notice {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 30rpx;\n\tbackground-color: #FEF0F0;\n\tborder-radius: 8rpx;\n\tmargin: 20rpx 0;\n}\n\n.miss-card-notice text {\n\tmargin-left: 10rpx;\n\tcolor: #F56C6C;\n\tfont-size: 28rpx;\n}\n\n/* 添加底部间距 */\n.map-card {\n\tmargin-bottom: 40rpx;\n\tbackground-color: #FFFFFF;\n\tborder-radius: 12rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 2rpx 8rpx rgba(22, 119, 255, 0.1);\n}\n\n/* 为整个容器添加底部内边距，确保所有内容与手机底部有足够间距 */\n.detail-container {\n\tmin-height: 100vh;\n\tbackground-color: #F5F7FA;\n\tpadding: 20rpx;\n\tpadding-bottom: 50rpx; /* 增加底部内边距 */\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775841884\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}