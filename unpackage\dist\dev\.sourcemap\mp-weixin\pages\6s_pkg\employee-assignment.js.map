{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s_pkg/employee-assignment.vue?5b64", "webpack:///D:/Xwzc/pages/6s_pkg/employee-assignment.vue?e9d8", "webpack:///D:/Xwzc/pages/6s_pkg/employee-assignment.vue?ca80", "webpack:///D:/Xwzc/pages/6s_pkg/employee-assignment.vue?c9ce", "uni-app:///pages/6s_pkg/employee-assignment.vue", "webpack:///D:/Xwzc/pages/6s_pkg/employee-assignment.vue?7196", "webpack:///D:/Xwzc/pages/6s_pkg/employee-assignment.vue?1527"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "assignmentList", "employeeList", "areaList", "loading", "saving", "isEditing", "currentAssignment", "selectedEmployee", "<PERSON><PERSON><PERSON><PERSON>", "assignmentNote", "searchKeyword", "filteredEmployees", "computed", "totalEmployees", "assignedEmployees", "totalAreas", "unassigned<PERSON><PERSON>s", "assignment", "acc", "availableAreas", "filter", "reduce", "currentAreaIds", "onLoad", "methods", "loadData", "Promise", "console", "uni", "title", "icon", "loadEmployees", "action", "pageSize", "result", "getCurrentUserId", "convertRoleToDisplay", "loadAreas", "type", "loadAssignments", "showAssignModal", "editAssignment", "deleteAssignment", "content", "confirmText", "confirmColor", "success", "performDeleteAssignment", "id", "index", "duration", "showEmployeeSelect", "closeEmployeeSelect", "selectEmployee", "filterEmployees", "emp", "toggleAreaSelection", "isAreaSelected", "submitAssignment", "assignmentData", "employee_id", "employee_name", "employee_info", "role", "avatar", "phone", "area_ids", "area_names", "areas_info", "location", "description", "note", "status", "employee", "areas", "newAssignment", "created_at", "validateAssignment", "resetForm", "closeAssignModal", "viewAreaDetail", "showCancel", "getStatusText", "formatDate"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,2BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4I;AAC5I;AACuE;AACL;AACsC;;;AAGxG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,yFAAM;AACR,EAAE,0GAAM;AACR,EAAE,mHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5FA;AAAA;AAAA;AAAA;AAA6mB,CAAgB,uoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC0QjoB;AAAA;AAAA;AACA;AAAA,eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;MAEA;QACAC;UACA;UACA;UACAC;QACA;QACA;MACA;MAEA;QACA;QACA;MACA;MAEA;IACA;IACAC;MAAA;MACA;QACA;QACA;UAAA;QAAA;QACA,0CACAC;UAAA;QAAA,GACAC;UACAJ;YAAA;UAAA;UACA;QACA;QAEA;UAAA,OACAK;QAAA,EACA;MACA;QACA;QACA;UACAL;YAAA;UAAA;UACA;QACA;QACA;UAAA;QAAA;MACA;IACA;EACA;EAEAM;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBAAA;gBAAA,OACAC,aACA,wBACA,oBACA,yBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;kBACAC;kBACAjC;oBACAkC;oBACA;kBACA;gBACA;cAAA;gBANAC;gBAQA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAP;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAQ;MACA;QACA;QACA;QACA;UACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA;QACA;UAAA;QAAA;MACA;MAEA;IACA;IAIA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;kBACAL;kBACAjC;oBACAuC;kBACA;gBACA;cAAA;gBALAJ;gBAOA;kBAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAP;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAY;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;kBACAP;kBACAjC;gBACA;cAAA;gBAHAmC;gBAKA;gBACA;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAP;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAa;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACAd;QACAC;QACAc;QACAC;QACAC;QACAC;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;kBACAf;kBACAjC;oBACAiD;kBACA;gBACA;cAAA;gBAEA;gBACAC;kBAAA;gBAAA;gBACA;kBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAEArB;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAH;gBACAC;kBACAC;kBACAC;kBACAoB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAIA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;QACA;UAAA,OACAC,4CACAA;QAAA,EACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QAAA;MAAA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAKA;;gBAEA;gBAEAC;kBACAC;kBACAC;kBACAC;oBACAhE;oBACAiE;oBACAC;oBACAC;kBACA;kBACAC;oBAAA;kBAAA;kBACAC;oBAAA;kBAAA;kBACAC;oBAAA;sBACApB;sBACAlD;sBACAuE;sBACAC;oBACA;kBAAA;kBACAC;kBACAC;gBACA;gBAAA,KAGA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAxC;kBACAjC;oBACAiD;kBAAA,GACAW;gBAEA;cAAA;gBANAzB;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OASA;kBACAF;kBACAjC;gBACA;cAAA;gBAHAmC;cAAA;gBAMA;kBACA;kBACAe;oBAAA;kBAAA;kBACA;oBACA,+DACAf;sBACAuC;sBACAC;oBAAA,EACA;kBACA;gBACA;kBACA;kBACAC,gDACAzC;oBACAuC;oBACAC;oBACAE;kBAAA;;kBAEA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAEAhD;kBACAC;kBACAC;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAH;gBACAC;kBACAC;kBACAC;kBACAoB;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA2B;MAAA;MACA;QACAjD;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;QAAA,OACAb,0DACA;MAAA,EACA;MAEA;QACAW;UACAC;UACAC;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAgD;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACApD;QACAC;QACAc;QACAsC;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnvBA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,spCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/employee-assignment.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/employee-assignment.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./employee-assignment.vue?vue&type=template&id=6dc3e300&scoped=true&\"\nvar renderjs\nimport script from \"./employee-assignment.vue?vue&type=script&lang=js&\"\nexport * from \"./employee-assignment.vue?vue&type=script&lang=js&\"\nimport style0 from \"./employee-assignment.vue?vue&type=style&index=0&id=6dc3e300&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6dc3e300\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/employee-assignment.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./employee-assignment.vue?vue&type=template&id=6dc3e300&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.loading ? _vm.assignmentList.length : null\n  var l0 =\n    !_vm.loading && g0 > 0\n      ? _vm.__map(_vm.assignmentList, function (assignment, index) {\n          var $orig = _vm.__get_orig(assignment)\n          var g1 = String(index + 1).padStart(3, \"0\")\n          var g2 = assignment.areas.length\n          var m0 = _vm.getStatusText(assignment.status)\n          var m1 =\n            assignment.updated_at &&\n            assignment.updated_at !== assignment.assigned_at\n              ? _vm.formatDate(assignment.updated_at)\n              : null\n          var m2 = !(\n            assignment.updated_at &&\n            assignment.updated_at !== assignment.assigned_at\n          )\n            ? _vm.formatDate(assignment.assigned_at)\n            : null\n          return {\n            $orig: $orig,\n            g1: g1,\n            g2: g2,\n            m0: m0,\n            m1: m1,\n            m2: m2,\n          }\n        })\n      : null\n  var l1 = _vm.__map(_vm.availableAreas, function (area, areaIndex) {\n    var $orig = _vm.__get_orig(area)\n    var m3 = _vm.isAreaSelected(area)\n    var m4 = _vm.isAreaSelected(area)\n    return {\n      $orig: $orig,\n      m3: m3,\n      m4: m4,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./employee-assignment.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./employee-assignment.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page-container\">\n    <!-- 页面头部 -->\n    <view class=\"header\">\n      <view class=\"header-title\">员工责任区分配</view>\n      <view class=\"header-subtitle\">为员工分配固定责任区域</view>\n    </view>\n\n    <!-- 统计概览 -->\n    <view class=\"overview-card\">\n      <view class=\"overview-title\">分配概览</view>\n      \n      <!-- 加载状态 -->\n      <view v-if=\"loading\" class=\"stats-loading\">\n        <view class=\"loading-content\">\n          <uni-icons type=\"spinner-cycle\" size=\"32\" color=\"#007AFF\"></uni-icons>\n          <text class=\"loading-text\">加载统计数据中...</text>\n        </view>\n      </view>\n      \n      <!-- 正常统计数据 -->\n      <view v-else class=\"stats-grid\">\n        <view class=\"stat-item\">\n          <view class=\"stat-number\">{{ totalEmployees }}</view>\n          <view class=\"stat-label\">总员工数</view>\n        </view>\n        <view class=\"stat-item\">\n          <view class=\"stat-number\">{{ assignedEmployees }}</view>\n          <view class=\"stat-label\">已分配</view>\n        </view>\n        <view class=\"stat-item\">\n          <view class=\"stat-number\">{{ totalAreas }}</view>\n          <view class=\"stat-label\">总责任区</view>\n        </view>\n        <view class=\"stat-item\">\n          <view class=\"stat-number\">{{ unassignedAreas }}</view>\n          <view class=\"stat-label\">未分配</view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 操作按钮 -->\n    <view class=\"action-bar\">\n      <button class=\"action-btn primary\" @click=\"showAssignModal\">\n        <uni-icons type=\"plus\" size=\"16\" color=\"#FFFFFF\" />\n        <text>新增分配</text>\n      </button>\n    </view>\n\n    <!-- 分配列表 -->\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"list-loading\">\n      <view class=\"loading-content\">\n        <uni-icons type=\"spinner-cycle\" size=\"40\" color=\"#007AFF\"></uni-icons>\n        <text class=\"loading-text\">加载分配数据中...</text>\n      </view>\n    </view>\n    \n    <!-- 正常分配列表 -->\n    <view v-else-if=\"assignmentList.length > 0\" class=\"assignment-list\">\n      <view \n        class=\"assignment-card-modern\"\n        v-for=\"(assignment, index) in assignmentList\" \n        :key=\"index\"\n      >\n        <!-- 移除左侧装饰条，统一为极简卡片 -->\n        <!-- no accent bar -->\n        \n        <!-- 主要内容区域 -->\n        <view class=\"card-content\">\n          <!-- 顶部员工信息行 -->\n          <view class=\"employee-header\">\n            <view class=\"employee-name-section\">\n              <view class=\"employee-icon-wrapper\">\n                <uni-icons type=\"person-filled\" color=\"#FFFFFF\" size=\"18\"></uni-icons>\n              </view>\n              <text class=\"employee-name-large\">{{ assignment.employee.name }}</text>\n              <view class=\"employee-role-chip\">{{ assignment.employee.role || '员工' }}</view>\n            </view>\n            <view class=\"assignment-id\">ID: {{ String(index + 1).padStart(3, '0') }}</view>\n          </view>\n          \n          <!-- 区域信息展示 -->\n          <view class=\"areas-section\">\n            <view class=\"section-header\">\n              <view class=\"section-title-row\">\n                <view class=\"section-title\">责任区域</view>\n                <view class=\"area-count-badge\">{{ assignment.areas.length }}</view>\n              </view>\n              <view class=\"assignment-status-modern\" :class=\"[`status-${assignment.status}`]\">\n                {{ getStatusText(assignment.status) }}\n              </view>\n            </view>\n            \n            <view class=\"areas-grid\">\n              <view \n                class=\"area-tag-modern\"\n                v-for=\"(area, areaIndex) in assignment.areas\" \n                :key=\"areaIndex\"\n                @click=\"viewAreaDetail(area)\"\n              >\n                <text class=\"area-name\">{{ area.name }}</text>\n              </view>\n            </view>\n          </view>\n          \n          <!-- 时间信息和操作 -->\n          <view class=\"card-footer\">\n            <view class=\"time-info\">\n              <template v-if=\"assignment.updated_at && assignment.updated_at !== assignment.assigned_at\">\n                <text class=\"time-label\">更新于:</text>\n                <text class=\"time-value\">{{ formatDate(assignment.updated_at) }}</text>\n              </template>\n              <template v-else>\n                <text class=\"time-label\">分配于:</text>\n                <text class=\"time-value\">{{ formatDate(assignment.assigned_at) }}</text>\n              </template>\n            </view>\n            \n            <view class=\"action-buttons\">\n              <view class=\"action-btn edit-btn\" @click=\"editAssignment(assignment)\" role=\"button\">\n                <uni-icons type=\"compose\" size=\"16\" color=\"#007AFF\" />\n                <text class=\"btn-text\">编辑</text>\n              </view>\n              <view class=\"action-btn delete-btn\" @click=\"deleteAssignment(assignment)\" role=\"button\">\n                <uni-icons type=\"trash\" size=\"16\" color=\"#FF3B30\" />\n                <text class=\"btn-text\">删除</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 空状态 -->\n    <p-empty-state\n      v-else\n      type=\"assignment\"\n      text=\"暂无分配记录\"\n      description=\"点击上方按钮开始分配责任区\"\n    ></p-empty-state>\n\n    <!-- 分配弹窗 -->\n    <uni-popup ref=\"assignPopup\" type=\"center\" :mask-click=\"false\">\n      <view class=\"assign-popup\">\n        <view class=\"popup-header\">\n          <view class=\"popup-title\">{{ isEditing ? '编辑' : '新增' }}分配</view>\n          <button class=\"close-btn\" @click=\"closeAssignModal\">\n            <uni-icons type=\"close\" size=\"20\" color=\"#8E8E93\" />\n          </button>\n        </view>\n        \n        <view class=\"popup-content\">\n          <!-- 员工选择 -->\n          <view class=\"form-section\">\n            <view class=\"section-title\">选择员工</view>\n            <view class=\"employee-selector\" @click=\"showEmployeeSelect\">\n              <view class=\"selected-employee\" v-if=\"selectedEmployee\">\n                <image \n                  :src=\"selectedEmployee.avatar || '/static/user/default-avatar.png'\" \n                  class=\"selected-avatar\" \n                  mode=\"aspectFill\"\n                />\n                <view class=\"selected-info\">\n                  <text class=\"selected-name\">{{ selectedEmployee.name }}</text>\n                  <text class=\"selected-role\">{{ selectedEmployee.role || '员工' }}</text>\n                </view>\n              </view>\n              <view class=\"placeholder\" v-else>\n                <uni-icons type=\"person-filled\" size=\"20\" color=\"#C7C7CC\" />\n                <text>点击选择员工</text>\n              </view>\n              <uni-icons type=\"right\" size=\"16\" color=\"#C7C7CC\" />\n            </view>\n          </view>\n          \n          <!-- 责任区选择 -->\n          <view class=\"form-section\">\n            <view class=\"section-title\">分配责任区</view>\n            <view class=\"areas-selector\">\n              <view \n                class=\"area-option\"\n                v-for=\"(area, areaIndex) in availableAreas\" \n                :key=\"areaIndex\"\n                @click=\"toggleAreaSelection(area)\"\n                :class=\"{ 'selected': isAreaSelected(area) }\"\n              >\n                <view class=\"area-option-main\">\n                  <view class=\"area-option-name\">{{ area.name }}</view>\n                  <view class=\"area-option-location\">{{ (area.location && area.location.area) || area.location || '未设置位置' }}</view>\n                </view>\n                <view class=\"area-option-check\" v-if=\"isAreaSelected(area)\">\n                  <uni-icons type=\"checkmarkempty\" size=\"18\" color=\"#007AFF\" />\n                </view>\n              </view>\n            </view>\n          </view>\n          \n          <!-- 分配说明 -->\n          <view class=\"form-section\">\n            <view class=\"section-title\">分配说明</view>\n            <textarea \n              class=\"assign-note\" \n              v-model=\"assignmentNote\"\n              placeholder=\"请输入分配说明（可选）\"\n              maxlength=\"200\"\n            ></textarea>\n          </view>\n        </view>\n        \n        <view class=\"popup-footer\">\n          <button class=\"popup-btn cancel\" @click=\"closeAssignModal\">取消</button>\n          <button class=\"popup-btn submit\" @click=\"submitAssignment\" :loading=\"saving\">\n            {{ isEditing ? '保存' : '分配' }}\n          </button>\n        </view>\n      </view>\n    </uni-popup>\n\n    <!-- 员工选择弹窗 -->\n    <uni-popup ref=\"employeeSelectPopup\" type=\"center\">\n      <view class=\"employee-select-popup\">\n        <view class=\"select-header\">\n          <text class=\"select-title\">选择员工</text>\n          <button class=\"close-btn\" @click=\"closeEmployeeSelect\">\n            <uni-icons type=\"close\" size=\"20\" color=\"#8E8E93\" />\n          </button>\n        </view>\n        <view class=\"search-bar\">\n          <input \n            class=\"search-input\" \n            type=\"text\" \n            placeholder=\"搜索员工姓名\" \n            v-model=\"searchKeyword\"\n            @input=\"filterEmployees\"\n          />\n          <uni-icons type=\"search\" size=\"18\" color=\"#8E8E93\" />\n        </view>\n        <scroll-view scroll-y class=\"employee-list\">\n          <view \n            class=\"employee-option\"\n            v-for=\"(employee, empIndex) in filteredEmployees\" \n            :key=\"empIndex\"\n            @click=\"selectEmployee(employee)\"\n            :class=\"{ 'selected': selectedEmployee && selectedEmployee.id === employee.id }\"\n          >\n            <image \n              :src=\"employee.avatar || '/static/user/default-avatar.png'\" \n              class=\"option-avatar\" \n              mode=\"aspectFill\"\n            />\n            <view class=\"option-info\">\n              <text class=\"option-name\">{{ employee.name }}</text>\n              <text class=\"option-role\">{{ employee.role || '员工' }}</text>\n            </view>\n            <view class=\"option-check\" v-if=\"selectedEmployee && selectedEmployee.id === employee.id\">\n              <uni-icons type=\"checkmarkempty\" size=\"18\" color=\"#007AFF\" />\n            </view>\n          </view>\n        </scroll-view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\nimport { callCloudFunction } from '@/utils/auth.js';\n// 使用统一的认证工具，不需要额外导入\n\nexport default {\n  name: 'EmployeeAssignment',\n  data() {\n    return {\n      assignmentList: [],\n      employeeList: [],\n      areaList: [],\n      loading: false,\n      saving: false,\n      isEditing: false,\n      currentAssignment: null,\n      selectedEmployee: null,\n      selectedAreas: [],\n      assignmentNote: '',\n      searchKeyword: '',\n      filteredEmployees: []\n    }\n  },\n  \n  computed: {\n    totalEmployees() {\n      return this.employeeList.length;\n    },\n    assignedEmployees() {\n      return this.assignmentList.length;\n    },\n    totalAreas() {\n      return this.areaList.length;\n    },\n    unassignedAreas() {\n      // 确保数据都加载完成后再计算\n      if (this.loading || this.areaList.length === 0) {\n        return 0;\n      }\n      \n      const assignedAreaIds = this.assignmentList.reduce((acc, assignment) => {\n        assignment.areas.forEach(area => {\n          // 兼容 _id 和 id 两种格式\n          const areaId = area._id || area.id;\n          acc.add(areaId);\n        });\n        return acc;\n      }, new Set());\n      \n      const unassignedCount = this.areaList.filter(area => {\n        const areaId = area._id || area.id;\n        return !assignedAreaIds.has(areaId);\n      }).length;\n      \n      return unassignedCount;\n    },\n    availableAreas() {\n      if (this.isEditing && this.currentAssignment) {\n        // 编辑模式：显示当前分配的区域 + 未分配的区域\n        const currentAreaIds = new Set(this.currentAssignment.areas.map(area => area.id));\n        const assignedAreaIds = this.assignmentList\n          .filter(assignment => assignment.id !== this.currentAssignment.id)\n          .reduce((acc, assignment) => {\n            assignment.areas.forEach(area => acc.add(area.id));\n            return acc;\n          }, new Set());\n        \n        return this.areaList.filter(area => \n          currentAreaIds.has(area.id) || !assignedAreaIds.has(area.id)\n        );\n      } else {\n        // 新增模式：只显示未分配的区域\n        const assignedAreaIds = this.assignmentList.reduce((acc, assignment) => {\n          assignment.areas.forEach(area => acc.add(area.id));\n          return acc;\n        }, new Set());\n        return this.areaList.filter(area => !assignedAreaIds.has(area.id));\n      }\n    }\n  },\n  \n  onLoad() {\n    this.loadData();\n  },\n  \n  methods: {\n    // 加载数据\n    async loadData() {\n      try {\n        this.loading = true;\n        await Promise.all([\n          this.loadEmployees(),\n          this.loadAreas(),\n          this.loadAssignments()\n        ]);\n      } catch (error) {\n        console.error('加载数据失败：', error);\n        uni.showToast({\n          title: '加载失败',\n          icon: 'none'\n        });\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    // 加载员工列表\n    async loadEmployees() {\n      try {\n        // 使用6S模块自己的云函数获取员工列表\n        const result = await callCloudFunction('hygiene-assignments', {\n          action: 'getEmployeeList',\n          data: {\n            pageSize: 200\n            // 不传status参数，这样云函数就不会添加状态过滤条件\n          }\n        });\n\n        this.employeeList = result.data.list || [];\n        this.filteredEmployees = [...this.employeeList];\n      } catch (error) {\n        console.error('加载员工数据失败：', error);\n        this.employeeList = [];\n        this.filteredEmployees = [];\n      }\n    },\n    \n    // 获取当前用户ID\n    getCurrentUserId() {\n      try {\n        // 尝试从本地存储获取用户信息\n        const userInfo = uni.getStorageSync('uni-id-pages-userInfo');\n        if (userInfo) {\n          return userInfo._id || '';\n        }\n        return '';\n      } catch (e) {\n        return '';\n      }\n    },\n    \n    // 转换角色为显示文本（与用户中心保持一致）\n    convertRoleToDisplay(role) {\n      const roleNames = {\n        'admin': '管理员',\n        'responsible': '负责人',\n        'reviser': '发布人',\n        'supervisor': '主管',\n        'PM': '副厂长',\n        'GM': '厂长',\n        'logistics': '后勤员',\n        'dispatch': '调度员',\n        'Integrated': '综合员',\n        'operator': '设备员',\n        'technician': '工艺员',\n        'mechanic': '技术员',\n        'user': '普通员工'\n      };\n      \n      if (Array.isArray(role)) {\n        return role.map(r => roleNames[r] || r).join('、');\n      }\n      \n      return roleNames[role] || role || '普通员工';\n    },\n    \n\n    \n    // 加载责任区列表\n    async loadAreas() {\n      try {\n        // 使用认证工具调用云函数获取固定责任区列表\n        const result = await callCloudFunction('hygiene-area-management', {\n          action: 'getAreaList',\n          data: {\n            type: 'fixed' // 只获取固定责任区\n          }\n        });\n\n        this.areaList = (result.data.list || []).filter(area => area.status === 'active');\n      } catch (error) {\n        console.error('加载责任区数据失败：', error);\n        this.areaList = [];\n      }\n    },\n    \n    // 加载分配记录\n    async loadAssignments() {\n      try {\n        // 使用认证工具调用云函数获取分配记录\n        const result = await callCloudFunction('hygiene-assignments', {\n          action: 'getAssignmentList',\n          data: {}\n        });\n\n        // 按创建时间正序排列，确保最早创建的记录编号最小\n        this.assignmentList = (result.data || []).sort((a, b) => {\n          const dateA = new Date(a.created_at || a.assigned_at || 0);\n          const dateB = new Date(b.created_at || b.assigned_at || 0);\n          return dateA - dateB; // 升序排列\n        });\n      } catch (error) {\n        console.error('加载分配记录失败：', error);\n        this.assignmentList = [];\n      }\n    },\n    \n    // 显示分配弹窗\n    showAssignModal() {\n      this.isEditing = false;\n      this.currentAssignment = null;\n      this.resetForm();\n      this.$refs.assignPopup.open();\n    },\n    \n    // 编辑分配\n    editAssignment(assignment) {\n      this.isEditing = true;\n      this.currentAssignment = assignment;\n      this.selectedEmployee = assignment.employee;\n      this.selectedAreas = [...assignment.areas];\n      this.assignmentNote = assignment.note || '';\n      this.$refs.assignPopup.open();\n    },\n    \n    // 删除分配\n    deleteAssignment(assignment) {\n      uni.showModal({\n        title: '确认删除',\n        content: `确定要删除 ${assignment.employee.name} 的责任区分配吗？`,\n        confirmText: '删除',\n        confirmColor: '#FF3B30',\n        success: (res) => {\n          if (res.confirm) {\n            this.performDeleteAssignment(assignment);\n          }\n        }\n      });\n    },\n    \n    // 执行删除分配\n    async performDeleteAssignment(assignment) {\n      try {\n        // 使用认证工具调用云函数删除分配记录\n        await callCloudFunction('hygiene-assignments', {\n          action: 'deleteAssignment',\n          data: {\n            id: assignment._id || assignment.id\n          }\n        });\n\n        // 从本地列表中移除\n        const index = this.assignmentList.findIndex(item => (item._id || item.id) === (assignment._id || assignment.id));\n        if (index > -1) {\n          this.assignmentList.splice(index, 1);\n        }\n        \n        // 重新加载责任区数据以更新分配状态\n        await this.loadAreas();\n        \n        uni.showToast({\n          title: '删除成功',\n          icon: 'success'\n        });\n      } catch (error) {\n        console.error('删除失败：', error);\n        uni.showToast({\n          title: error.message || '删除失败',\n          icon: 'none',\n          duration: 3000\n        });\n      }\n    },\n    \n\n    \n    // 显示员工选择\n    showEmployeeSelect() {\n      this.$refs.employeeSelectPopup.open();\n    },\n    \n    // 关闭员工选择\n    closeEmployeeSelect() {\n      this.$refs.employeeSelectPopup.close();\n    },\n    \n    // 选择员工\n    selectEmployee(employee) {\n      this.selectedEmployee = employee;\n      this.closeEmployeeSelect();\n    },\n    \n    // 过滤员工\n    filterEmployees() {\n      const keyword = this.searchKeyword.toLowerCase();\n      if (!keyword) {\n        this.filteredEmployees = [...this.employeeList];\n      } else {\n        this.filteredEmployees = this.employeeList.filter(emp => \n          emp.name.toLowerCase().includes(keyword) ||\n          (emp.role && emp.role.toLowerCase().includes(keyword))\n        );\n      }\n    },\n    \n    // 切换责任区选择\n    toggleAreaSelection(area) {\n      const areaId = area._id || area.id;\n      const index = this.selectedAreas.findIndex(a => (a._id || a.id) === areaId);\n      if (index > -1) {\n        this.selectedAreas.splice(index, 1);\n      } else {\n        this.selectedAreas.push(area);\n      }\n    },\n    \n    // 检查责任区是否已选择\n    isAreaSelected(area) {\n      const areaId = area._id || area.id;\n      return this.selectedAreas.some(a => (a._id || a.id) === areaId);\n    },\n    \n    // 提交分配\n    async submitAssignment() {\n      if (!this.validateAssignment()) {\n        return;\n      }\n      \n      try {\n        this.saving = true;\n        \n        // 使用认证工具处理，无需手动检查token\n\n        const assignmentData = {\n          employee_id: this.selectedEmployee.id,\n          employee_name: this.selectedEmployee.name,\n          employee_info: {\n            name: this.selectedEmployee.name,\n            role: this.selectedEmployee.role,\n            avatar: this.selectedEmployee.avatar,\n            phone: this.selectedEmployee.phone\n          },\n          area_ids: this.selectedAreas.map(area => area._id || area.id),\n          area_names: this.selectedAreas.map(area => area.name),\n          areas_info: this.selectedAreas.map(area => ({\n            id: area._id || area.id,\n            name: area.name,\n            location: area.location,\n            description: area.description\n          })),\n          note: this.assignmentNote,\n          status: 'active'\n        };\n\n        let result;\n        if (this.isEditing) {\n          // 编辑模式\n          result = await callCloudFunction('hygiene-assignments', {\n            action: 'updateAssignment',\n            data: {\n              id: this.currentAssignment._id || this.currentAssignment.id,\n              ...assignmentData\n            }\n          });\n        } else {\n          // 新增模式\n          result = await callCloudFunction('hygiene-assignments', {\n            action: 'createAssignment',\n            data: assignmentData\n          });\n        }\n\n        if (this.isEditing) {\n          // 更新本地列表中的数据\n          const index = this.assignmentList.findIndex(item => (item._id || item.id) === (this.currentAssignment._id || this.currentAssignment.id));\n          if (index > -1) {\n            this.assignmentList[index] = {\n              ...(result.data || {}),\n              employee: result.data?.employee_info || this.selectedEmployee,\n              areas: result.data?.areas_info || this.selectedAreas\n            };\n          }\n        } else {\n          // 添加新数据到列表末尾（因为新记录创建时间最晚）\n          const newAssignment = {\n            ...(result.data || {}),\n            employee: result.data?.employee_info || this.selectedEmployee,\n            areas: result.data?.areas_info || this.selectedAreas,\n            created_at: result.data?.created_at || new Date().toISOString() // 确保有创建时间\n          };\n          this.assignmentList.push(newAssignment);\n        }\n        \n        // 重新加载责任区数据以更新分配状态\n        await this.loadAreas();\n        \n        uni.showToast({\n          title: this.isEditing ? '保存成功' : '分配成功',\n          icon: 'success'\n        });\n        \n        this.closeAssignModal();\n        \n      } catch (error) {\n        console.error('保存失败：', error);\n        uni.showToast({\n          title: error.message || '保存失败',\n          icon: 'none',\n          duration: 3000\n        });\n      } finally {\n        this.saving = false;\n      }\n    },\n    \n    // 验证分配\n    validateAssignment() {\n      if (!this.selectedEmployee) {\n        uni.showToast({\n          title: '请选择员工',\n          icon: 'none'\n        });\n        return false;\n      }\n      \n      if (this.selectedAreas.length === 0) {\n        uni.showToast({\n          title: '请选择至少一个责任区',\n          icon: 'none'\n        });\n        return false;\n      }\n      \n      // 检查员工是否已有分配（编辑模式下排除当前记录）\n      const existingAssignment = this.assignmentList.find(assignment => \n        assignment.employee.id === this.selectedEmployee.id &&\n        (!this.isEditing || assignment.id !== this.currentAssignment.id)\n      );\n      \n      if (existingAssignment) {\n        uni.showToast({\n          title: '该员工已有责任区分配',\n          icon: 'none'\n        });\n        return false;\n      }\n      \n      return true;\n    },\n    \n    // 重置表单\n    resetForm() {\n      this.selectedEmployee = null;\n      this.selectedAreas = [];\n      this.assignmentNote = '';\n      this.searchKeyword = '';\n      this.filteredEmployees = [...this.employeeList];\n    },\n    \n    // 关闭分配弹窗\n    closeAssignModal() {\n      this.$refs.assignPopup.close();\n      this.resetForm();\n    },\n    \n    // 查看责任区详情\n    viewAreaDetail(area) {\n      uni.showModal({\n        title: area.name,\n        content: `位置：${(area.location && area.location.area) || area.location || '未设置'}\\n描述：${area.description || '无'}`,\n        showCancel: false\n      });\n    },\n    \n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        'active': '正常',\n        'inactive': '暂停',\n        'expired': '过期'\n      };\n      return statusMap[status] || '未知';\n    },\n    \n    // 格式化日期\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n  min-height: 100vh;\n  background: #F8F9FA;\n  padding-bottom: 40rpx;\n}\n\n.header {\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n  padding: 60rpx 32rpx 40rpx;\n  color: white;\n}\n\n.header-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  margin-bottom: 8rpx;\n}\n\n.header-subtitle {\n  font-size: 24rpx;\n  opacity: 0.8;\n}\n\n.overview-card {\n  background: white;\n  margin: 24rpx 32rpx;\n  padding: 32rpx;\n  border-radius: 16rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.overview-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 24rpx;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 24rpx;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 24rpx;\n  background: #F8F9FA;\n  border-radius: 12rpx;\n}\n\n.stat-number {\n  font-size: 48rpx;\n  font-weight: 600;\n  color: #007AFF;\n  margin-bottom: 8rpx;\n}\n\n.stat-label {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.action-bar {\n  padding: 0 32rpx 24rpx;\n  display: flex;\n  gap: 16rpx;\n}\n\n.action-btn {\n  flex: 1;\n  height: 72rpx;\n  border-radius: 12rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8rpx;\n  font-size: 26rpx;\n  border: none;\n  transition: all 0.3s ease;\n}\n\n.action-btn.primary {\n  background: #007AFF;\n  color: white;\n}\n\n.action-btn.secondary {\n  background: white;\n  color: #007AFF;\n  border: 2rpx solid #007AFF;\n}\n\n.action-btn:active {\n  transform: scale(0.95);\n}\n\n.assignment-list {\n  padding: 0 32rpx;\n}\n\n/* 统一为极简风格卡片 */\n.assignment-card-modern {\n  background: #FFFFFF;\n  border: 1rpx solid rgba(0, 0, 0, 0.06);\n  border-radius: 16rpx;\n  margin-bottom: 24rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);\n  overflow: hidden;\n}\n\n/* 移除装饰条 */\n.card-accent-bar { display: none; }\n\n/* 内容区域边距与 index 风格统一 */\n.card-content {\n  padding: 24rpx 24rpx 12rpx;\n}\n\n/* 顶部员工信息 */\n.employee-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n  padding-bottom: 16rpx;\n  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);\n}\n\n.employee-name-section {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n}\n\n.employee-icon-wrapper {\n  width: 48rpx;\n  height: 48rpx;\n  background-color: #007AFF;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n\n.employee-name-large {\n  font-size: 32rpx;\n  font-weight: 700;\n  color: #1D1D1F;\n  line-height: 1;\n  display: flex;\n  align-items: center;\n}\n\n.employee-role-chip {\n  padding: 6rpx 12rpx;\n  background: rgba(0, 122, 255, 0.12);\n  color: #007AFF;\n  font-size: 20rpx;\n  font-weight: 600;\n  border-radius: 12rpx;\n}\n\n.assignment-id {\n  font-size: 22rpx;\n  font-weight: 600;\n  color: #8E8E93;\n  background: #F5F5F7;\n  padding: 6rpx 12rpx;\n  border-radius: 10rpx;\n}\n\n/* 区域与状态 */\n.areas-section { margin-bottom: 12rpx; }\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12rpx;\n}\n\n.section-title-row { display: flex; align-items: baseline; gap: 10rpx; }\n\n.section-title { font-size: 28rpx; font-weight: 600; color: #1D1D1F; }\n\n.area-count-badge {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 149, 0, 0.12);\n  color: #FF9500;\n  font-size: 18rpx;\n  font-weight: 700;\n  padding: 4rpx 10rpx;\n  border-radius: 16rpx;\n  min-width: 28rpx;\n}\n\n.assignment-status-modern {\n  padding: 6rpx 12rpx;\n  border-radius: 12rpx;\n  font-size: 20rpx;\n  font-weight: 600;\n}\n\n.assignment-status-modern.status-active {\n  background: rgba(52, 199, 89, 0.12);\n  color: #34C759;\n}\n\n.assignment-status-modern.status-inactive {\n  background: rgba(142, 142, 147, 0.12);\n  color: #8E8E93;\n}\n\n.assignment-status-modern.status-expired {\n  background: rgba(255, 59, 48, 0.12);\n  color: #FF3B30;\n}\n\n/* 区域标签栅格 */\n.areas-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10rpx;\n}\n\n.area-tag-modern {\n  background: #F8FAFC;\n  border: 1rpx solid #E5E7EB;\n  padding: 10rpx 16rpx;\n  border-radius: 12rpx;\n  transition: background 0.2s ease;\n}\n\n.area-name {\n  font-size: 24rpx;\n  font-weight: 500;\n  color: #1D1D1F;\n}\n\n/* 底部信息与操作 */\n.card-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-top: 12rpx;\n  border-top: 1rpx solid rgba(0, 0, 0, 0.06);\n}\n\n.time-info { \n  display: flex;\n  align-items: baseline;\n  gap: 8rpx;\n}\n\n.time-label { \n  font-size: 22rpx; \n  color: #8E8E93; \n}\n\n.time-value { \n  font-size: 22rpx;\n  font-weight: 500;\n  color: #1D1D1F;\n}\n\n.action-buttons { display: flex; gap: 12rpx; }\n\n/* 统一按钮为极简描边/文本按钮风格（仅作用于卡片内按钮） */\n.assignment-card-modern .action-btn {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  padding: 2rpx 12rpx;\n  border-radius: 8rpx;\n  border: none;\n  background-color: transparent;\n  font-size: 22rpx;\n  font-weight: 500;\n  transition: background-color 0.2s ease;\n}\n\n.assignment-card-modern .edit-btn { color: #007AFF; }\n.assignment-card-modern .delete-btn { color: #FF3B30; }\n\n.assignment-card-modern .action-btn:active { background-color: #F8F9FA; }\n\n.assign-popup {\n  width: 92vw;\n  max-width: 650rpx;\n  background: white;\n  border-radius: 16rpx;\n  overflow: hidden;\n  max-height: 80vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 32rpx;\n  border-bottom: 1rpx solid #F2F2F7;\n}\n\n.popup-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-left: 5rpx;\n}\n\n.close-btn {\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 12rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n    background: #F2F2F7;\n  border: none;\n  margin-right: 5rpx;\n}\n\n.popup-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 32rpx;\n}\n\n/* #ifdef MP-WEIXIN */\n/* 微信小程序隐藏滚动条 */\n.popup-content::-webkit-scrollbar {\n  display: none;\n  width: 0;\n  height: 0;\n  background: transparent;\n}\n\n.popup-content {\n  scrollbar-width: none; /* Firefox */\n  -ms-overflow-style: none; /* IE and Edge */\n}\n/* #endif */\n\n/* #ifndef MP-WEIXIN */\n/* 非微信小程序环境下也隐藏滚动条 */\n.popup-content::-webkit-scrollbar {\n  display: none;\n  width: 0;\n  height: 0;\n  background: transparent;\n}\n\n.popup-content {\n  scrollbar-width: none; /* Firefox */\n  -ms-overflow-style: none; /* IE and Edge */\n}\n/* #endif */\n\n.form-section {\n  margin-bottom: 32rpx;\n}\n\n.section-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 16rpx;\n}\n\n.employee-selector {\n  background: #F2F2F7;\n  border-radius: 12rpx;\n  padding: 16rpx;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.selected-employee {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n  flex: 1;\n}\n\n.selected-avatar {\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 50%;\n}\n\n.selected-info {\n  flex: 1;\n}\n\n.selected-name {\n  font-size: 26rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  display: block;\n  margin-bottom: 4rpx;\n}\n\n.selected-role {\n  font-size: 22rpx;\n  color: #8E8E93;\n}\n\n.placeholder {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n  color: #C7C7CC;\n  font-size: 26rpx;\n  flex: 1;\n}\n\n.areas-selector {\n  max-height: 300rpx;\n  overflow-y: auto;\n}\n\n/* 隐藏责任区选择器滚动条 */\n.areas-selector::-webkit-scrollbar {\n  display: none;\n  width: 0;\n  height: 0;\n  background: transparent;\n}\n\n.areas-selector {\n  scrollbar-width: none; /* Firefox */\n  -ms-overflow-style: none; /* IE and Edge */\n}\n\n.area-option {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 16rpx;\n  border: 2rpx solid #F2F2F7;\n  border-radius: 12rpx;\n  margin-bottom: 12rpx;\n  transition: all 0.3s ease;\n}\n\n.area-option.selected {\n  border-color: #007AFF;\n  background: rgba(0, 122, 255, 0.05);\n}\n\n.area-option-main {\n  flex: 1;\n}\n\n.area-option-name {\n  font-size: 26rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-bottom: 4rpx;\n}\n\n.area-option-location {\n  font-size: 22rpx;\n  color: #8E8E93;\n}\n\n.area-option-check {\n  width: 40rpx;\n  height: 40rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.assign-note {\n  width: 100%;\n  background: #F2F2F7;\n  border: none;\n  border-radius: 12rpx;\n  padding: 16rpx;\n  font-size: 26rpx;\n  color: #1C1C1E;\n  min-height: 120rpx;\n  box-sizing: border-box;\n}\n\n.popup-footer {\n  display: flex;\n  gap: 16rpx;\n  padding: 32rpx;\n  border-top: 1rpx solid #F2F2F7;\n}\n\n.popup-btn {\n  flex: 1;\n  height: 72rpx;\n  border-radius: 12rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  border: none;\n  transition: all 0.3s ease;\n}\n\n.popup-btn.cancel {\n  background: #F2F2F7;\n  color: #8E8E93;\n}\n\n.popup-btn.submit {\n  background: #007AFF;\n  color: white;\n}\n\n.popup-btn:active {\n  transform: scale(0.95);\n}\n\n.employee-select-popup {\n  width: 92vw;\n  max-width: 550rpx;\n  background: white;\n  border-radius: 16rpx;\n  overflow: hidden;\n  max-height: 80vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.select-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 32rpx;\n  border-bottom: 1rpx solid #F2F2F7;\n}\n\n.select-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-left: 5rpx;\n}\n\n.search-bar {\n  display: flex;\n  align-items: center;\n  padding: 24rpx 32rpx;\n  background: #F8F9FA;\n  border-bottom: 1rpx solid #F2F2F7;\n}\n\n.search-input {\n  flex: 1;\n  height: 60rpx;\n  font-size: 26rpx;\n  color: #1C1C1E;\n}\n\n.employee-list {\n  flex: 1;\n  overflow-y: auto;\n}\n\n/* #ifdef MP-WEIXIN */\n/* 微信小程序隐藏滚动条 */\n.employee-list::-webkit-scrollbar {\n  display: none;\n  width: 0;\n  height: 0;\n  background: transparent;\n}\n\n.employee-list {\n  scrollbar-width: none; /* Firefox */\n  -ms-overflow-style: none; /* IE and Edge */\n}\n/* #endif */\n\n/* #ifndef MP-WEIXIN */\n/* 非微信小程序环境下也隐藏滚动条 */\n.employee-list::-webkit-scrollbar {\n  display: none;\n  width: 0;\n  height: 0;\n  background: transparent;\n}\n\n.employee-list {\n  scrollbar-width: none; /* Firefox */\n  -ms-overflow-style: none; /* IE and Edge */\n}\n/* #endif */\n\n.employee-option {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n  padding: 20rpx 32rpx;\n  border-bottom: 1rpx solid #F2F2F7;\n  transition: all 0.3s ease;\n}\n\n.employee-option.selected {\n  background: rgba(0, 122, 255, 0.05);\n}\n\n.employee-option:active {\n  background: #F8F9FA;\n}\n\n.option-avatar {\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 50%;\n  border: 2rpx solid #F2F2F7;\n}\n\n.option-info {\n  flex: 1;\n}\n\n.option-name {\n  font-size: 26rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  display: block;\n  margin-bottom: 4rpx;\n}\n\n.option-role {\n  font-size: 22rpx;\n  color: #8E8E93;\n}\n\n.option-check {\n  width: 40rpx;\n  height: 40rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 加载状态样式 */\n.stats-loading {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 60rpx 0;\n}\n\n.list-loading {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 120rpx 32rpx;\n}\n\n.loading-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 16rpx;\n}\n\n.loading-text {\n  font-size: 26rpx;\n  color: #8E8E93;\n  text-align: center;\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./employee-assignment.vue?vue&type=style&index=0&id=6dc3e300&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./employee-assignment.vue?vue&type=style&index=0&id=6dc3e300&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775844844\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}