<view class="page-container data-v-1ef03a4e"><view class="content data-v-1ef03a4e"><view class="person-card data-v-1ef03a4e"><view class="person-header data-v-1ef03a4e"><view class="person-avatar data-v-1ef03a4e"><uni-icons vue-id="2074f94d-1" type="person" size="32" color="#007AFF" class="data-v-1ef03a4e" bind:__l="__l"></uni-icons></view><view class="person-info data-v-1ef03a4e"><view class="person-name data-v-1ef03a4e">{{personData.name}}</view><view class="person-role data-v-1ef03a4e">{{personData.role}}</view></view></view><view class="progress-summary data-v-1ef03a4e"><view class="summary-item data-v-1ef03a4e"><view class="summary-number primary data-v-1ef03a4e">{{personData.total}}</view><view class="summary-label data-v-1ef03a4e">总问题</view></view><view class="summary-item data-v-1ef03a4e"><view class="summary-number success data-v-1ef03a4e">{{personData.completed}}</view><view class="summary-label data-v-1ef03a4e">已完成</view></view><view class="summary-item data-v-1ef03a4e"><view class="summary-number warning data-v-1ef03a4e">{{personData.inProgress}}</view><view class="summary-label data-v-1ef03a4e">进行中</view></view><view class="summary-item data-v-1ef03a4e"><view class="summary-number danger data-v-1ef03a4e">{{personData.overdue}}</view><view class="summary-label data-v-1ef03a4e">已逾期</view></view></view><view class="completion-rate data-v-1ef03a4e"><view class="rate-info data-v-1ef03a4e"><text class="rate-label data-v-1ef03a4e">完成率</text><text class="rate-value data-v-1ef03a4e">{{completionRate+"%"}}</text></view><view class="rate-bar data-v-1ef03a4e"><view class="rate-fill data-v-1ef03a4e" style="{{'width:'+(completionRate+'%')+';'}}"></view></view></view></view><view class="filter-card data-v-1ef03a4e"><view class="card-header data-v-1ef03a4e"><scroll-view class="filter-tabs data-v-1ef03a4e" scroll-x="true" show-scrollbar="false"><block wx:for="{{statusFilters}}" wx:for-item="filter" wx:for-index="__i0__" wx:key="value"><view data-event-opts="{{[['tap',[['changeFilter',['$0'],[[['statusFilters','value',filter.value,'value']]]]]]]}}" class="{{['filter-tab','data-v-1ef03a4e',(currentFilter===filter.value)?'active':'']}}" bindtap="__e">{{''+filter.label+''}}</view></block></scroll-view></view></view><view class="issues-card data-v-1ef03a4e"><view class="card-header data-v-1ef03a4e"><view class="card-title data-v-1ef03a4e">{{$root.m0}}</view><view class="card-subtitle data-v-1ef03a4e">{{"共"+$root.g0+"个问题"}}</view></view><block wx:if="{{$root.g1>0}}"><view class="issues-list data-v-1ef03a4e"><block wx:for="{{$root.l0}}" wx:for-item="issue" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['viewIssueDetail',['$0'],[[['displayedIssues','',index]]]]]]]}}" class="issue-item data-v-1ef03a4e" bindtap="__e"><view class="{{['issue-icon','data-v-1ef03a4e','icon-bg-'+issue.$orig.status]}}"><text class="issue-number data-v-1ef03a4e">{{issue.g2}}</text></view><view class="issue-content data-v-1ef03a4e"><view class="issue-title data-v-1ef03a4e">{{issue.$orig.title}}</view><view class="issue-location data-v-1ef03a4e"><uni-icons vue-id="{{'2074f94d-2-'+index}}" type="location" size="14" color="#8E8E93" class="data-v-1ef03a4e" bind:__l="__l"></uni-icons><text class="data-v-1ef03a4e">{{issue.$orig.location}}</text></view><view class="issue-deadline data-v-1ef03a4e"><uni-icons vue-id="{{'2074f94d-3-'+index}}" type="calendar" size="14" color="#8E8E93" class="data-v-1ef03a4e" bind:__l="__l"></uni-icons><text class="data-v-1ef03a4e">{{"截止："+issue.$orig.deadline}}</text></view></view><view class="issue-right data-v-1ef03a4e"><view class="{{['issue-status','data-v-1ef03a4e','status-'+issue.$orig.status]}}">{{''+issue.m1+''}}</view><uni-icons vue-id="{{'2074f94d-4-'+index}}" type="right" size="14" color="#C7C7CC" class="data-v-1ef03a4e" bind:__l="__l"></uni-icons></view></view></block></view></block><block wx:else><p-empty-state vue-id="2074f94d-5" useIcon="{{true}}" iconName="info" iconColor="#C7C7CC" size="large" text="{{'暂无'+$root.m2}}" class="data-v-1ef03a4e" bind:__l="__l"></p-empty-state></block><block wx:if="{{hasMoreIssues}}"><view class="more-section data-v-1ef03a4e"><view data-event-opts="{{[['tap',[['showMoreIssues',['$event']]]]]}}" class="more-btn data-v-1ef03a4e" bindtap="__e"><uni-icons vue-id="2074f94d-6" type="down" size="16" color="#007AFF" class="data-v-1ef03a4e" bind:__l="__l"></uni-icons><text class="data-v-1ef03a4e">{{"查看更多问题 ("+remainingIssuesCount+")"}}</text></view></view></block></view><view class="bottom-safe-area data-v-1ef03a4e"></view></view><block wx:if="{{loading}}"><view class="custom-loading-mask data-v-1ef03a4e"><view class="loading-container-enhanced data-v-1ef03a4e"><uni-icons vue-id="2074f94d-7" type="spinner-cycle" size="48" color="#007AFF" class="data-v-1ef03a4e" bind:__l="__l"></uni-icons><text class="loading-text-enhanced data-v-1ef03a4e">{{loadingText}}</text></view></view></block></view>