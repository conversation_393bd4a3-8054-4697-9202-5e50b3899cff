{"bsonType": "object", "required": ["area_id", "user_id", "cleaning_date"], "permission": {"read": true, "create": "doc.user_id == auth.uid || auth.role.includes('admin')", "update": "doc.user_id == auth.uid || auth.role.includes('admin')", "delete": "auth.role.includes('admin')"}, "properties": {"_id": {"description": "ID，系统自动生成"}, "area_id": {"bsonType": "string", "title": "责任区ID", "description": "关联的责任区ID", "foreignKey": "hygiene-areas._id", "errorMessage": {"required": "责任区ID不能为空"}}, "area_name": {"bsonType": "string", "title": "责任区名称", "description": "冗余存储的责任区名称，方便查询显示", "maxLength": 50}, "user_id": {"bsonType": "string", "title": "清洁人员", "description": "执行清洁的用户ID", "foreignKey": "uni-id-users._id", "errorMessage": {"required": "清洁人员不能为空"}}, "user_name": {"bsonType": "string", "title": "清洁人员姓名", "description": "冗余存储的用户姓名", "maxLength": 50}, "cleaning_date": {"bsonType": "timestamp", "title": "清洁日期", "description": "执行清洁的日期", "errorMessage": {"required": "清洁日期不能为空"}}, "week_number": {"bsonType": "string", "title": "周次", "description": "清洁所属的周次，格式如'2025-W03'", "pattern": "^\\d{4}-W\\d{2}$", "maxLength": 8}, "photos": {"bsonType": "array", "title": "清洁照片", "description": "清洁前后的照片列表", "items": {"bsonType": "object", "properties": {"url": {"bsonType": "string", "title": "图片URL", "pattern": "^cloud://"}, "type": {"bsonType": "string", "title": "照片类型", "enum": ["before", "after", "during"], "default": "after"}, "description": {"bsonType": "string", "title": "照片说明", "maxLength": 100}}}, "maxItems": 10}, "remark": {"bsonType": "string", "title": "备注说明", "description": "清洁过程中的备注信息", "maxLength": 500}, "completion_status": {"bsonType": "string", "title": "完成状态", "description": "清洁任务的完成状态", "enum": ["completed", "partial", "delayed", "skipped"], "default": "completed"}, "quality_score": {"bsonType": "number", "title": "质量评分", "description": "清洁质量评分（1-10分）", "minimum": 1, "maximum": 10}, "duration_minutes": {"bsonType": "number", "title": "清洁用时", "description": "完成清洁所用的时间（分钟）", "minimum": 1, "maximum": 480}, "verified_by": {"bsonType": "string", "title": "验证人", "description": "验收清洁结果的人员ID", "foreignKey": "uni-id-users._id"}, "verified_at": {"bsonType": "timestamp", "title": "验证时间", "description": "验收的时间"}, "issues_found": {"bsonType": "array", "title": "发现的问题", "description": "清洁过程中发现的需要整改的问题", "items": {"bsonType": "string", "maxLength": 200}, "maxItems": 10}, "created_at": {"bsonType": "timestamp", "title": "创建时间", "description": "记录创建时间", "forceDefaultValue": {"$env": "now"}}, "updated_at": {"bsonType": "timestamp", "title": "更新时间", "description": "记录最后更新时间", "forceDefaultValue": {"$env": "now"}}}}