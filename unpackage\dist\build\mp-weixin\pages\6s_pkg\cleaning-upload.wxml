<view class="page-container data-v-372fbbc5"><view class="card data-v-372fbbc5"><view class="card-header data-v-372fbbc5"><view class="header-content data-v-372fbbc5"><view class="card-title data-v-372fbbc5">{{areaInfo.name||'责任区清理'}}</view><view class="card-subtitle data-v-372fbbc5">{{mode==='edit'?'修改清理记录':$root.m0}}</view></view></view><view class="card-body data-v-372fbbc5"><view class="upload-section data-v-372fbbc5"><view class="section-header data-v-372fbbc5"><view class="section-title data-v-372fbbc5">上传清理照片</view><view data-event-opts="{{[['tap',[['toggleAutoUpload',['$event']]]]]}}" class="auto-upload-toggle data-v-372fbbc5" bindtap="__e"><view class="toggle-label data-v-372fbbc5">自动上传</view><view class="{{['toggle-switch','data-v-372fbbc5',(autoUpload)?'active':'']}}"><view class="toggle-circle data-v-372fbbc5"></view></view></view></view><view class="photo-grid data-v-372fbbc5"><block wx:for="{{$root.l0}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view class="photo-item data-v-372fbbc5"><image src="{{photo.m1}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewPhoto',[index]]]]]}}" bindtap="__e" class="data-v-372fbbc5"></image><block wx:if="{{photo.$orig.uploading}}"><view class="photo-uploading data-v-372fbbc5"><view class="upload-spinner data-v-372fbbc5"></view></view></block><block wx:else><block wx:if="{{photo.$orig.uploaded}}"><view class="photo-uploaded data-v-372fbbc5"><uni-icons vue-id="{{'6ddd939d-1-'+index}}" type="checkmarkempty" size="16" color="white" class="data-v-372fbbc5" bind:__l="__l"></uni-icons></view></block></block><view data-event-opts="{{[['tap',[['deletePhoto',[index]]]]]}}" class="photo-delete data-v-372fbbc5" bindtap="__e"><uni-icons vue-id="{{'6ddd939d-2-'+index}}" type="close" size="18" color="white" class="data-v-372fbbc5" bind:__l="__l"></uni-icons></view></view></block><block wx:if="{{$root.g0<12}}"><view data-event-opts="{{[['tap',[['addPhoto',['$event']]]]]}}" class="{{['photo-add','data-v-372fbbc5',(uploading)?'disabled':'']}}" bindtap="__e"><uni-icons vue-id="6ddd939d-3" type="camera" size="32" color="#8E8E93" class="data-v-372fbbc5" bind:__l="__l"></uni-icons><text class="data-v-372fbbc5">添加照片</text></view></block></view><block wx:if="{{uploading}}"><view class="upload-progress data-v-372fbbc5"><view class="progress-bar data-v-372fbbc5"><view class="progress-fill data-v-372fbbc5" style="{{'width:'+(uploadProgress+'%')+';'}}"></view></view><text class="progress-text data-v-372fbbc5">{{"正在上传照片... "+uploadProgress+"%"}}</text></view></block><view class="photo-tip data-v-372fbbc5">最多可上传12张照片，建议拍摄清理前后对比照片</view></view><view class="remarks-section data-v-372fbbc5"><view class="section-title data-v-372fbbc5">清理备注（可选）</view><view class="remarks-input-container data-v-372fbbc5"><textarea class="remarks-input data-v-372fbbc5" placeholder="请描述清理过程中发现的问题或需要注意的事项..." maxlength="200" data-event-opts="{{[['input',[['__set_model',['','remarks','$event',[]]],['handleRemarksInput',['$event']]]]]}}" value="{{remarks}}" bindinput="__e"></textarea><view class="char-count-overlay data-v-372fbbc5">{{remarksLength+"/200"}}</view></view></view></view></view><view class="button-container data-v-372fbbc5"><button class="{{['primary-button','data-v-372fbbc5',(loading||uploading)?'loading':'']}}" disabled="{{$root.g1}}" data-event-opts="{{[['tap',[['submitCleaning',['$event']]]]]}}" bindtap="__e"><block wx:if="{{loading||uploading}}"><view class="button-loading data-v-372fbbc5"><view class="loading-spinner data-v-372fbbc5"></view><text class="data-v-372fbbc5">{{uploading?'上传中...':'提交中...'}}</text></view></block><block wx:else><text class="data-v-372fbbc5">{{mode==='edit'?'保存修改':'提交清理记录'}}</text></block></button></view></view>