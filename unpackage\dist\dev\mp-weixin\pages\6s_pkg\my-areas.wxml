<view class="page-container data-v-4c3d3ebe"><view class="card data-v-4c3d3ebe"><view class="card-header data-v-4c3d3ebe"><view class="header-content data-v-4c3d3ebe"><view class="card-title data-v-4c3d3ebe">{{$root.m0}}</view><view data-event-opts="{{[['tap',[['showTimeSelector',['$event']]]]]}}" class="time-selector data-v-4c3d3ebe" bindtap="__e"><text class="time-text data-v-4c3d3ebe">{{$root.m1}}</text><uni-icons vue-id="624b3485-1" type="down" size="12" color="#007AFF" class="data-v-4c3d3ebe" bind:__l="__l"></uni-icons></view></view></view><view class="card-body data-v-4c3d3ebe"><block wx:if="{{loading}}"><view class="stats-loading data-v-4c3d3ebe"><view class="loading-content data-v-4c3d3ebe"><view class="loading-spinner data-v-4c3d3ebe"></view><text class="loading-text data-v-4c3d3ebe">统计数据加载中...</text></view></view></block><block wx:else><view class="stats-grid data-v-4c3d3ebe"><view class="stats-item data-v-4c3d3ebe"><view class="stats-number success data-v-4c3d3ebe">{{completedCount}}</view><view class="stats-label data-v-4c3d3ebe">{{selectedQuickFilter==='week'?'已完成':'已完成整改'}}</view></view><view class="stats-item data-v-4c3d3ebe"><view class="stats-number warning data-v-4c3d3ebe">{{pendingCount}}</view><view class="stats-label data-v-4c3d3ebe">{{selectedQuickFilter==='week'?'待处理':'待整改'}}</view></view><view class="stats-item data-v-4c3d3ebe"><view class="stats-number danger data-v-4c3d3ebe">{{overdueCount}}</view><view class="stats-label data-v-4c3d3ebe">已逾期</view></view></view></block></view></view><view class="card data-v-4c3d3ebe"><view class="card-header data-v-4c3d3ebe"><view class="header-content data-v-4c3d3ebe"><view class="card-title data-v-4c3d3ebe">固定责任区</view><view class="card-subtitle data-v-4c3d3ebe">每人分配固定责任区进行清理</view></view></view><block wx:if="{{loading}}"><view class="list-loading data-v-4c3d3ebe"><view class="loading-content data-v-4c3d3ebe"><view class="loading-spinner data-v-4c3d3ebe"></view><text class="loading-text data-v-4c3d3ebe">加载责任区数据中...</text></view></view></block><block wx:else><block wx:if="{{$root.g0===0}}"><p-empty-state vue-id="624b3485-2" useIcon="{{true}}" iconName="info" iconColor="#8E8E93" text="暂未分配固定责任区" description="请联系管理员在责任区检查中进行分配" class="data-v-4c3d3ebe" bind:__l="__l"></p-empty-state></block><block wx:for="{{$root.l0}}" wx:for-item="area" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['openAreaDetail',['$0'],[[['myFixedAreas','',index]]]]]]]}}" class="list-item data-v-4c3d3ebe" bindtap="__e"><view class="{{['list-item-icon','data-v-4c3d3ebe','icon-bg-'+area.$orig.status]}}"><uni-icons vue-id="{{'624b3485-3-'+index}}" type="{{area.$orig.icon}}" size="18" color="{{area.m2}}" class="data-v-4c3d3ebe" bind:__l="__l"></uni-icons></view><view class="list-item-content data-v-4c3d3ebe"><view class="list-item-title data-v-4c3d3ebe">{{''+area.$orig.name+''}}<text class="fixed-tag data-v-4c3d3ebe">固定</text></view><view class="list-item-subtitle data-v-4c3d3ebe">{{area.$orig.subtitle}}</view></view><view class="list-item-right data-v-4c3d3ebe"><view class="{{['status-badge','data-v-4c3d3ebe','status-'+area.$orig.status]}}">{{area.m3}}</view><uni-icons vue-id="{{'624b3485-4-'+index}}" type="right" size="14" color="#C7C7CC" class="data-v-4c3d3ebe" bind:__l="__l"></uni-icons></view></view></block></block></view><block wx:if="{{$root.g1}}"><view class="card data-v-4c3d3ebe"><view class="card-header data-v-4c3d3ebe"><view class="header-content data-v-4c3d3ebe"><view class="card-title data-v-4c3d3ebe">公共责任区</view><view class="card-subtitle data-v-4c3d3ebe">每周轮班公共责任区进行清理</view></view></view><block wx:if="{{loading}}"><view class="list-loading data-v-4c3d3ebe"><view class="loading-content data-v-4c3d3ebe"><view class="loading-spinner data-v-4c3d3ebe"></view><text class="loading-text data-v-4c3d3ebe">加载责任区数据中...</text></view></view></block><block wx:else><block wx:if="{{$root.g2===0}}"><p-empty-state vue-id="624b3485-5" useIcon="{{true}}" iconName="home" iconColor="#8E8E93" text="暂无公共责任区" description="管理员可在责任区检查中添加公共责任区" class="data-v-4c3d3ebe" bind:__l="__l"></p-empty-state></block><block wx:for="{{$root.l1}}" wx:for-item="area" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['openPublicAreaDetail',['$0'],[[['myPublicAreas','',index]]]]]]]}}" class="list-item data-v-4c3d3ebe" bindtap="__e"><view class="{{['list-item-icon','data-v-4c3d3ebe','icon-bg-'+area.$orig.status]}}"><uni-icons vue-id="{{'624b3485-6-'+index}}" type="{{area.$orig.icon}}" size="18" color="{{area.m4}}" class="data-v-4c3d3ebe" bind:__l="__l"></uni-icons></view><view class="list-item-content data-v-4c3d3ebe"><view class="list-item-title data-v-4c3d3ebe">{{''+area.$orig.name+''}}<text class="public-tag data-v-4c3d3ebe">公共</text></view><view class="list-item-subtitle data-v-4c3d3ebe">{{area.$orig.subtitle}}</view></view><view class="list-item-right data-v-4c3d3ebe"><view class="{{['status-badge','data-v-4c3d3ebe','status-'+area.$orig.status]}}">{{area.m5}}</view><uni-icons vue-id="{{'624b3485-7-'+index}}" type="right" size="14" color="#C7C7CC" class="data-v-4c3d3ebe" bind:__l="__l"></uni-icons></view></view></block></block></view></block><view class="card data-v-4c3d3ebe"><view class="card-header data-v-4c3d3ebe"><view class="header-content data-v-4c3d3ebe"><view class="card-title data-v-4c3d3ebe">我的整改记录</view><view class="card-subtitle data-v-4c3d3ebe">我的责任区整改记录</view></view></view><block wx:if="{{loading}}"><view class="list-loading data-v-4c3d3ebe"><view class="loading-content data-v-4c3d3ebe"><view class="loading-spinner data-v-4c3d3ebe"></view><text class="loading-text data-v-4c3d3ebe">加载整改记录中...</text></view></view></block><block wx:else><block wx:if="{{$root.g3===0}}"><p-empty-state vue-id="624b3485-8" useIcon="{{true}}" iconName="checkmarkempty" iconColor="#34C759" text="暂无整改任务" description="当责任区检查发现问题时，会在此显示需要整改的任务" class="data-v-4c3d3ebe" bind:__l="__l"></p-empty-state></block><block wx:for="{{$root.l3}}" wx:for-item="group" wx:for-index="__i0__" wx:key="weekKey"><view class="time-group data-v-4c3d3ebe"><view data-event-opts="{{[['tap',[['toggleRectificationWeek',['$0'],[[['groupedRectificationTasks','weekKey',group.$orig.weekKey,'weekKey']]]]]]]}}" class="time-group-header data-v-4c3d3ebe" bindtap="__e"><view class="time-group-title data-v-4c3d3ebe"><uni-icons vue-id="{{'624b3485-9-'+__i0__}}" type="{{group.$orig.expanded?'down':'right'}}" size="16" color="#007AFF" class="data-v-4c3d3ebe" bind:__l="__l"></uni-icons><text class="time-title data-v-4c3d3ebe">{{group.$orig.title}}</text><view class="time-count data-v-4c3d3ebe">{{group.g4+"条"}}</view></view></view><block wx:if="{{group.$orig.expanded}}"><view class="time-group-content data-v-4c3d3ebe"><block wx:for="{{group.l2}}" wx:for-item="task" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['openRectificationDetail',['$0'],[[['groupedRectificationTasks','weekKey',group.$orig.weekKey],['tasks','',index]]]]]]]}}" class="list-item data-v-4c3d3ebe" bindtap="__e"><view class="{{['list-item-icon','data-v-4c3d3ebe','icon-bg-'+task.$orig.status]}}"><uni-icons vue-id="{{'624b3485-10-'+__i0__+'-'+index}}" type="{{task.$orig.icon}}" size="18" color="{{task.m6}}" class="data-v-4c3d3ebe" bind:__l="__l"></uni-icons></view><view class="list-item-content data-v-4c3d3ebe"><view class="list-item-title data-v-4c3d3ebe">{{''+task.$orig.areaName+''}}<block wx:if="{{task.$orig.areaType==='public'}}"><text class="public-tag data-v-4c3d3ebe">公共</text></block><block wx:else><text class="fixed-tag data-v-4c3d3ebe">固定</text></block></view><view class="list-item-subtitle data-v-4c3d3ebe">{{task.$orig.subtitle}}</view><view class="task-time-info data-v-4c3d3ebe"><text class="time-label data-v-4c3d3ebe">发现时间：</text><text class="time-value data-v-4c3d3ebe">{{task.$orig.issueFoundDate}}</text><block wx:if="{{task.$orig.completedDate}}"><text class="time-label data-v-4c3d3ebe">· 完成时间：</text></block><block wx:if="{{task.$orig.completedDate}}"><text class="time-value data-v-4c3d3ebe">{{task.$orig.completedDate}}</text></block></view></view><view class="list-item-right data-v-4c3d3ebe"><view class="{{['status-badge','data-v-4c3d3ebe','status-'+task.$orig.status]}}">{{task.m7}}</view><uni-icons vue-id="{{'624b3485-11-'+__i0__+'-'+index}}" type="right" size="14" color="#C7C7CC" class="data-v-4c3d3ebe" bind:__l="__l"></uni-icons></view></view></block></view></block></view></block></block></view><view class="card data-v-4c3d3ebe"><view class="card-header data-v-4c3d3ebe"><view class="header-content data-v-4c3d3ebe"><view class="card-title data-v-4c3d3ebe">我的清理记录</view><view class="card-subtitle data-v-4c3d3ebe">最近的清理工作记录</view></view></view><block wx:if="{{loading}}"><view class="loading-container data-v-4c3d3ebe"><view class="loading-content data-v-4c3d3ebe"><view class="loading-spinner data-v-4c3d3ebe"></view><text class="loading-text data-v-4c3d3ebe">加载清理记录中...</text></view></view></block><block wx:else><block wx:if="{{$root.g5===0}}"><p-empty-state vue-id="624b3485-12" useIcon="{{true}}" iconName="checkmarkempty" iconColor="#34C759" text="暂无清理记录" description="完成清理任务后，记录会在此显示" class="data-v-4c3d3ebe" bind:__l="__l"></p-empty-state></block><block wx:for="{{$root.l4}}" wx:for-item="record" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['openCleaningRecordDetail',['$0'],[[['cleaningRecords.list','',index]]]]]]]}}" class="list-item data-v-4c3d3ebe" bindtap="__e"><view class="{{['list-item-icon','data-v-4c3d3ebe','icon-bg-'+record.$orig.status]}}"><uni-icons vue-id="{{'624b3485-13-'+index}}" type="{{record.$orig.icon}}" size="18" color="{{record.m8}}" class="data-v-4c3d3ebe" bind:__l="__l"></uni-icons></view><view class="list-item-content data-v-4c3d3ebe"><view class="list-item-title data-v-4c3d3ebe">{{''+record.$orig.areaName+''}}<block wx:if="{{record.$orig.areaType==='public'}}"><text class="public-tag data-v-4c3d3ebe">公共</text></block><block wx:else><text class="fixed-tag data-v-4c3d3ebe">固定</text></block></view><view class="list-item-subtitle data-v-4c3d3ebe">{{record.$orig.formattedDate}}</view><view class="cleaning-record-meta data-v-4c3d3ebe"><text class="meta-label data-v-4c3d3ebe">照片：</text><text class="meta-value data-v-4c3d3ebe">{{record.$orig.photos+"张"}}</text><block wx:if="{{record.$orig.remark}}"><text class="meta-label data-v-4c3d3ebe">· 备注：</text></block><block wx:if="{{record.$orig.remark}}"><text class="meta-value meta-remark data-v-4c3d3ebe">{{record.g6+(record.g7>15?'...':'')}}</text></block></view></view><view class="list-item-right data-v-4c3d3ebe"><view class="{{['status-badge','data-v-4c3d3ebe','status-'+record.$orig.status]}}">{{record.m9}}</view><uni-icons vue-id="{{'624b3485-14-'+index}}" type="right" size="14" color="#C7C7CC" class="data-v-4c3d3ebe" bind:__l="__l"></uni-icons></view></view></block><block wx:if="{{cleaningRecords.hasMore}}"><view class="load-more-section data-v-4c3d3ebe"><button class="load-more-btn data-v-4c3d3ebe" disabled="{{cleaningRecords.loading}}" data-event-opts="{{[['tap',[['loadMoreCleaningRecords',['$event']]]]]}}" bindtap="__e"><block wx:if="{{cleaningRecords.loading}}"><view class="btn-loading-spinner data-v-4c3d3ebe"></view></block><text class="data-v-4c3d3ebe">{{cleaningRecords.loading?'加载中...':'加载更多'}}</text></button></view></block></block></view><view class="bottom-safe-area data-v-4c3d3ebe"></view><uni-popup vue-id="624b3485-15" type="bottom" border-radius="16rpx 16rpx 0 0" data-ref="timePopup" class="data-v-4c3d3ebe vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="date-picker-popup data-v-4c3d3ebe"><view class="popup-header data-v-4c3d3ebe"><text class="popup-title data-v-4c3d3ebe">筛选时间</text><view data-event-opts="{{[['tap',[['closeDatePicker',['$event']]]]]}}" class="popup-close data-v-4c3d3ebe" bindtap="__e"><uni-icons vue-id="{{('624b3485-16')+','+('624b3485-15')}}" type="close" size="18" color="#8E8E93" class="data-v-4c3d3ebe" bind:__l="__l"></uni-icons></view></view><block wx:if="{{dateFilterMode==='quick'}}"><view class="quick-date-section data-v-4c3d3ebe"><view class="section-title data-v-4c3d3ebe">快捷选择</view><view class="quick-options data-v-4c3d3ebe"><block wx:for="{{quickDateOptions}}" wx:for-item="option" wx:for-index="__i1__" wx:key="value"><view data-event-opts="{{[['tap',[['selectQuickDateOption',['$0'],[[['quickDateOptions','value',option.value]]]]]]]}}" class="{{['quick-option','data-v-4c3d3ebe',(selectedQuickFilter===option.value)?'active':'']}}" bindtap="__e"><text class="quick-text data-v-4c3d3ebe">{{option.label}}</text></view></block></view></view></block><block wx:else><view class="range-date-section data-v-4c3d3ebe"><view class="calendar-section data-v-4c3d3ebe"><view class="calendar-header data-v-4c3d3ebe"><text class="calendar-tip data-v-4c3d3ebe">在日历上点击选择开始和结束日期</text></view><uni-calendar vue-id="{{('624b3485-17')+','+('624b3485-15')}}" range="{{true}}" date="{{calendarDate}}" start-date="{{calendarStartDate}}" end-date="{{calendarEndDate}}" data-ref="calendar" data-event-opts="{{[['^change',[['onCalendarChange']]],['^monthSwitch',[['onMonthSwitch']]]]}}" bind:change="__e" bind:monthSwitch="__e" class="data-v-4c3d3ebe vue-ref" bind:__l="__l"></uni-calendar><block wx:if="{{customDateRange.startDate&&customDateRange.endDate}}"><view class="selected-range data-v-4c3d3ebe"><view class="range-item data-v-4c3d3ebe"><text class="range-label data-v-4c3d3ebe">开始日期</text><text class="range-value data-v-4c3d3ebe">{{$root.m10}}</text></view><view class="range-separator data-v-4c3d3ebe">→</view><view class="range-item data-v-4c3d3ebe"><text class="range-label data-v-4c3d3ebe">结束日期</text><text class="range-value data-v-4c3d3ebe">{{$root.m11}}</text></view></view></block></view></view></block></view></uni-popup></view>