require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/issue-add"],{"130b":function(t,e,n){"use strict";(function(t,e){var i=n("47a9");n("357b"),n("861b");i(n("3240"));var o=i(n("b519"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"26e1":function(t,e,n){"use strict";n.r(e);var i=n("fd38"),o=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=o.a},"8ff7":function(t,e,n){"use strict";var i=n("9432"),o=n.n(i);o.a},9432:function(t,e,n){},b519:function(t,e,n){"use strict";n.r(e);var i=n("d9ab"),o=n("26e1");for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);n("8ff7");var r=n("828b"),s=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,"d4c57b42",null,!1,i["a"],void 0);e["default"]=s.exports},d9ab:function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return i}));var i={uniIcons:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(n.bind(null,"6ddf"))}},o=function(){var t=this,e=t.$createElement,n=(t._self._c,t.commonLocations.length),i=t.showLocationSuggestions?t.commonLocations.length:null,o=t.showLocationSuggestions?t.filteredCategorizedSuggestions.length:null,a=t.showLocationSuggestions&&o>0?t.__map(t.filteredCategorizedSuggestions,(function(e,n){var i=t.__get_orig(e),o=t.__map(e.items,(function(e,n){var i=t.__get_orig(e),o=t.isFavorite(e),a=t.isFavorite(e);return{$orig:i,m0:o,m1:a}}));return{$orig:i,l0:o}})):null,r=t.showLocationSuggestions?!t.commonLocations.length&&!t.filteredCategorizedSuggestions.length:null,s=t.__map(t.formData.images,(function(e,n){var i=t.__get_orig(e),o=t.getPhotoDisplayUrl(e);return{$orig:i,m2:o}})),c=t.formData.images.length;t.$mp.data=Object.assign({},{$root:{g0:n,g1:i,g2:o,l1:a,g3:r,l2:s,g4:c}})},a=[]},fd38:function(t,e,n){"use strict";(function(t,i){var o=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=o(n("7eb4")),r=o(n("af34")),s=o(n("ee10")),c=o(n("7ca3")),u=o(n("4ea0"));function l(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function d(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?l(Object(n),!0).forEach((function(e){(0,c.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var f={name:"IssueAdd",data:function(){return{submitting:!1,draftSaving:!1,isEditingDraft:!1,currentDraftId:null,isEditingPublished:!1,currentEditId:null,pendingEditData:null,showLocationSuggestions:!1,uploading:!1,uploadProgress:0,autoUpload:!0,formData:{title:"",description:"",location:"",responsible:"",deadline:"",priority:"normal",images:[]},selectedResponsibleIndex:0,selectedPriorityIndex:0,responsiblePersons:[],priorityOptions:[{value:"normal",text:"一般问题"},{value:"urgent",text:"紧急问题"}],commonLocations:[],allLocationSuggestions:[],locationCategories:[],locationConfigLoading:!1}},onLoad:function(e){this.isEditingPublished=!1,this.isEditingDraft=!1,this.currentEditId=null,this.currentDraftId=null,this.pendingEditData=null,e.draftId&&(this.loadDraft(e.draftId),t.setNavigationBarTitle({title:"编辑草稿"})),e.editId&&e.editData&&(this.loadEditData(e.editId,e.editData),t.setNavigationBarTitle({title:"编辑问题"})),this.loadLocationConfig(),this.loadCommonLocations(),this.loadResponsiblePersons()},onShow:function(){this.loadCommonLocations();var t=this.currentEditId||this.currentDraftId||this.isEditingPublished||this.isEditingDraft;this.responsiblePersons.length<=1&&!t&&this.loadResponsiblePersons()},computed:{todayDate:function(){var t=new Date,e=t.getFullYear(),n=String(t.getMonth()+1).padStart(2,"0"),i=String(t.getDate()).padStart(2,"0");return"".concat(e,"-").concat(n,"-").concat(i)},canSubmit:function(){return!this.submitting&&this.formData.title.trim()&&this.formData.description.trim()&&this.formData.location.trim()&&this.formData.responsible&&this.formData.deadline},canSaveDraft:function(){return!this.draftSaving&&this.formData.title.trim()},responsibleDisplayName:function(){return this.selectedResponsibleIndex>0&&this.responsiblePersons[this.selectedResponsibleIndex]?this.responsiblePersons[this.selectedResponsibleIndex].text:"请选择负责人"},filteredCategorizedSuggestions:function(){var t=this;if(!this.locationCategories||0===this.locationCategories.length)return[];var e=this.formData.location.toLowerCase().trim();return this.locationCategories.map((function(n){var i=n.items||[];return e&&(i=i.filter((function(t){return t.toLowerCase().includes(e)}))),i=i.filter((function(e){return!t.commonLocations.includes(e)})),d(d({},n),{},{items:i.slice(0,5)})})).filter((function(t){return t.items.length>0}))}},methods:{goBack:function(){t.navigateBack()},loadLocationConfig:function(){var t=this;return(0,s.default)(a.default.mark((function e(){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,t.locationConfigLoading=!0,e.next=4,t.fetchLocationConfigFromServer();case 4:0===t.allLocationSuggestions.length&&t.loadLocationConfigFromCache(),0===t.allLocationSuggestions.length&&t.showLocationConfigError(),e.next=12;break;case 8:e.prev=8,e.t0=e["catch"](0),t.loadLocationConfigFromCache(),0===t.allLocationSuggestions.length&&t.showLocationConfigError();case 12:return e.prev=12,t.locationConfigLoading=!1,e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,8,12,15]])})))()},fetchLocationConfigFromServer:function(){var e=this;return(0,s.default)(a.default.mark((function i(){var o,r,s,c;return a.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.prev=0,o=n("882c"),r=o.callCloudFunction,i.next=4,r("hygiene-location-management",{action:"getLocationConfig"});case 4:if(s=i.sent,!(s&&s.success&&s.data&&s.data.locations)){i.next=13;break}c={version:"1.0.0",lastUpdated:(new Date).toISOString(),locations:s.data.locations},e.locationCategories=c.locations,e.allLocationSuggestions=c.locations.reduce((function(t,e){return t.concat(e.items||[])}),[]),t.setStorageSync("location_config",c),t.setStorageSync("location_config_timestamp",Date.now()),i.next=14;break;case 13:throw new Error("获取位置配置失败");case 14:i.next=19;break;case 16:throw i.prev=16,i.t0=i["catch"](0),i.t0;case 19:case"end":return i.stop()}}),i,null,[[0,16]])})))()},loadLocationConfigFromCache:function(){try{var e=t.getStorageSync("location_config"),n=t.getStorageSync("location_config_timestamp"),i=Date.now()-(n||0),o=i>6048e5;e&&!o&&(this.locationCategories=e.locations,this.allLocationSuggestions=e.locations.reduce((function(t,e){return t.concat(e.items)}),[]))}catch(a){}},showLocationConfigError:function(){var e=this;t.showModal({title:"位置配置加载失败",content:"无法获取位置配置数据，请检查网络连接或联系管理员。",showCancel:!1,confirmText:"重试",success:function(t){t.confirm&&e.loadLocationConfig()}}),this.locationCategories=[],this.allLocationSuggestions=[]},loadCommonLocations:function(){try{var e=t.getStorageSync("favorite_locations")||[];this.commonLocations=e}catch(n){}},selectLocation:function(t){this.formData.location=t,this.showLocationSuggestions=!1},clearLocation:function(){this.formData.location="",this.showLocationSuggestions=!1},onLocationInput:function(){this.showLocationSuggestions=!0},onLocationFocus:function(){this.showLocationSuggestions=!0},isFavorite:function(t){return this.commonLocations.includes(t)},toggleFavorite:function(e){try{var n=t.getStorageSync("favorite_locations")||[],i=n.indexOf(e);i>-1?(n.splice(i,1),t.showToast({title:"已取消收藏",icon:"none",duration:1500})):(n.unshift(e),t.showToast({title:"已添加到常用位置",icon:"success",duration:1500})),t.setStorageSync("favorite_locations",n),this.loadCommonLocations()}catch(o){t.showToast({title:"操作失败",icon:"none"})}},removeFromCommon:function(e,n){var i=this;t.showModal({title:"删除常用位置",content:'确定要从常用位置中删除"'.concat(e,'"吗？'),success:function(o){if(o.confirm)try{var a=t.getStorageSync("favorite_locations")||[],r=a.indexOf(e);r>-1&&(a.splice(r,1),t.setStorageSync("favorite_locations",a)),i.commonLocations.splice(n,1),t.showToast({title:"已删除",icon:"success",duration:1500})}catch(s){t.showToast({title:"删除失败",icon:"none"})}}})},loadResponsiblePersons:function(){var e=this;return(0,s.default)(a.default.mark((function i(){var o,s,c,u,l;return a.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.prev=0,o=n("882c"),s=o.callCloudFunction,i.next=4,s("hygiene-monthly-inspection",{action:"getResponsibleUsers"});case 4:c=i.sent,c&&c.success&&c.data?(u=c.data.filter((function(t){var e=Array.isArray(t.role)?t.role:[t.role];if(e.includes("admin"))return!1;if(!e.includes("responsible"))return!1;var n=t.nickname||t.username;return!(!n||""===n.trim())&&!n.startsWith("匿名")})).map((function(t){return{value:t._id,text:t.nickname||t.username}})).filter((function(t){return t.value&&t.text})),l=[{value:"",text:"请选择负责人"}].concat((0,r.default)(u)).filter((function(t,e){return 0===e||t&&t.text&&""!==t.text.trim()})),e.responsiblePersons=l,e.pendingEditData?(e.setEditSelectors(e.pendingEditData),e.pendingEditData=null):e.selectedResponsibleIndex=0):(t.showToast({title:"加载负责人列表失败",icon:"none"}),e.responsiblePersons=[{value:"",text:"请选择负责人"}],e.pendingEditData||(e.selectedResponsibleIndex=0)),i.next=14;break;case 8:i.prev=8,i.t0=i["catch"](0),t.showToast({title:"网络错误，请稍后重试",icon:"none"}),e.responsiblePersons=[{value:"",text:"请选择负责人"}],e.pendingEditData||(e.selectedResponsibleIndex=0),e.pendingEditData&&(e.setEditSelectors(e.pendingEditData),e.pendingEditData=null);case 14:case"end":return i.stop()}}),i,null,[[0,8]])})))()},onResponsibleChange:function(t){var e=t.detail.value;this.selectedResponsibleIndex=e,e>=0&&e<this.responsiblePersons.length&&this.responsiblePersons[e]?this.formData.responsible=0===e?"":this.responsiblePersons[e].value:(this.formData.responsible="",this.selectedResponsibleIndex=0)},onDeadlineChange:function(t){this.formData.deadline=t.detail.value},selectPriority:function(t,e){this.formData.priority=t,this.selectedPriorityIndex=e},onPriorityChange:function(t){var e=t.detail.value;this.selectedPriorityIndex=e,this.formData.priority=this.priorityOptions[e].value},getPriorityText:function(){var t=this,e=this.priorityOptions.find((function(e){return e.value===t.formData.priority}));return e?e.text:"一般问题"},chooseImage:function(){var e=this;this.formData.images.length>=6?t.showToast({title:"最多只能选择6张图片",icon:"none"}):t.chooseImage({count:6-this.formData.images.length,sizeType:["compressed"],sourceType:["camera","album"],success:function(t){var n,i=e.processNewPhotos(t.tempFilePaths);(n=e.formData.images).push.apply(n,(0,r.default)(i)),e.autoUpload&&e.autoUploadNewPhotos(i)},fail:function(){t.showToast({title:"选择照片失败",icon:"none"})}})},processNewPhotos:function(t){return t.map((function(t){return{url:t,uploaded:!1,cloudUrl:"",cloudPath:"",uploading:!1}}))},autoUploadNewPhotos:function(e){var n=this;return(0,s.default)(a.default.mark((function i(){var o,r,c;return a.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return o=e.map(function(){var t=(0,s.default)(a.default.mark((function t(e,i){var o,r;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(o=n.formData.images.findIndex((function(t){return t.url===e.url})),-1!==o){t.next=3;break}return t.abrupt("return",{success:!1,index:i});case 3:return t.prev=3,n.$set(n.formData.images[o],"uploading",!0),t.next=7,n.uploadSinglePhoto(e);case 7:if(r=t.sent,!r.success){t.next=14;break}return Object.assign(n.formData.images[o],{uploaded:!0,cloudUrl:r.url,cloudPath:r.cloudPath,uploading:!1,cloudReady:!1}),n.preloadCloudImage(o,r.url),t.abrupt("return",{success:!0,index:i});case 14:throw new Error(r.error||"上传失败");case 15:t.next=21;break;case 17:return t.prev=17,t.t0=t["catch"](3),n.$set(n.formData.images[o],"uploading",!1),t.abrupt("return",{success:!1,index:i,error:t.t0.message});case 21:case"end":return t.stop()}}),t,null,[[3,17]])})));return function(e,n){return t.apply(this,arguments)}}()),i.next=3,Promise.allSettled(o);case 3:r=i.sent,c=r.filter((function(t){return"fulfilled"===t.status&&!t.value.success})).map((function(t){return t.value})),c.length>0&&t.showToast({title:"".concat(c.length,"张照片上传失败"),icon:"none",duration:2e3});case 6:case"end":return i.stop()}}),i)})))()},uploadSinglePhoto:function(t){var e=this;return(0,s.default)(a.default.mark((function n(){var i,o,r;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,i=e.generateCloudPath(),n.next=4,u.default.uploadToCloud(t.url,i);case 4:if(o=n.sent,null===o||void 0===o||!o.fileID){n.next=12;break}return n.next=8,u.default.getFileInfo(o.fileID);case 8:return r=n.sent,n.abrupt("return",{success:!0,cloudPath:o.fileID,url:r.tempFileURL||o.fileID,size:o.actualSize});case 12:throw new Error("上传返回结果异常");case 13:n.next=18;break;case 15:return n.prev=15,n.t0=n["catch"](0),n.abrupt("return",{success:!1,error:n.t0.message});case 18:case"end":return n.stop()}}),n,null,[[0,15]])})))()},generateCloudPath:function(){var t=Date.now(),e=Math.random().toString(36).substring(2,8);return"6s/issues/".concat(t,"_").concat(e,".jpg")},deleteImage:function(e){var n=this;return(0,s.default)(a.default.mark((function o(){var r;return a.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(!(e<0||e>=n.formData.images.length)){o.next=2;break}return o.abrupt("return");case 2:if(r=n.formData.images[e],!r.uploaded||!r.cloudPath){o.next=16;break}return o.prev=4,t.showLoading({title:"删除照片中..."}),o.next=8,i.callFunction({name:"delete-file",data:{fileList:[n.extractFileId(r.cloudPath)]}});case 8:o.next=13;break;case 10:o.prev=10,o.t0=o["catch"](4),t.showToast({title:"删除云端照片失败",icon:"none"});case 13:return o.prev=13,t.hideLoading(),o.finish(13);case 16:n.formData.images.splice(e,1);case 17:case"end":return o.stop()}}),o,null,[[4,10,13,16]])})))()},extractFileId:function(t){if(t.startsWith("cloud://")){var e=t.split("/");return e[e.length-1]}if(t.includes("tcb-api")){var n=new URL(t);return n.pathname.split("/").pop()}return t},previewPhoto:function(e){var n=this,i=this.formData.images.map((function(t){return n.getPhotoDisplayUrl(t)}));t.previewImage({urls:i,current:e})},getPhotoDisplayUrl:function(t){return"string"===typeof t?t:t.uploaded&&t.cloudUrl&&t.cloudReady?t.cloudUrl:t.url||t.cloudUrl||t},preloadCloudImage:function(e,n){var i=this;t.getImageInfo({src:n,success:function(){i.formData.images[e]&&i.$set(i.formData.images[e],"cloudReady",!0)},fail:function(){i.formData.images[e]&&i.$set(i.formData.images[e],"cloudReady",!1)}})},validateForm:function(){return this.formData.title.trim()?this.formData.title.length<2||this.formData.title.length>50?(t.showToast({title:"标题长度在2-50个字符之间",icon:"none"}),!1):this.formData.description.trim()?this.formData.description.length<5||this.formData.description.length>200?(t.showToast({title:"描述长度在5-200个字符之间",icon:"none"}),!1):this.formData.location.trim()?this.formData.responsible?!!this.formData.deadline||(t.showToast({title:"请选择整改期限",icon:"none"}),!1):(t.showToast({title:"请选择负责人",icon:"none"}),!1):(t.showToast({title:"请输入问题位置",icon:"none"}),!1):(t.showToast({title:"请输入问题描述",icon:"none"}),!1):(t.showToast({title:"请输入问题标题",icon:"none"}),!1)},submitForm:function(){var e=this;return(0,s.default)(a.default.mark((function i(){var o,r,s,c,u,l,f,h,g,p;return a.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(e.validateForm()){i.next=2;break}return i.abrupt("return");case 2:return i.prev=2,e.submitting=!0,o=n("882c"),r=o.callCloudFunction,s={title:e.formData.title.trim(),description:e.formData.description.trim(),location_info:{location_type:"custom",location_name:e.formData.location.trim(),location_description:""},inspection_info:{inspection_date:(new Date).toISOString(),inspection_type:"monthly_routine"},category:"safety",severity:e.getPriorityToSeverity(e.formData.priority),priority:e.formData.priority,assigned_to:e.getResponsibleId(),assigned_to_name:e.getResponsibleName(),photos:e.preparePhotosForSubmit(),tags:[],expected_completion_date:e.formData.deadline},c=e.isEditingPublished?"updateMonthlyIssue":"createMonthlyIssue",u=e.isEditingPublished?d({issue_id:e.currentEditId},s):s,i.next=10,r("hygiene-monthly-inspection",{action:c,data:u});case 10:if(l=i.sent,!l||!l.success){i.next=20;break}t.showToast({title:e.isEditingPublished?"问题更新成功":"问题发布成功",icon:"success"}),p={action:e.isEditingPublished?"update":"create",issueId:e.isEditingPublished?e.currentEditId:(null===(f=l.data)||void 0===f?void 0:f.id)||(null===(h=l.data)||void 0===h?void 0:h._id),issueNumber:null===(g=l.data)||void 0===g?void 0:g.issue_number,issueData:s,fromDraft:e.isEditingDraft},t.$emit("monthlyIssueUpdated",p),e.clearDraftIfExists(),e.isEditingDraft&&e.currentDraftId&&setTimeout((function(){var n={action:"delete",draftId:e.currentDraftId};t.$emit("issueDraftUpdated",n)}),100),setTimeout((function(){t.navigateBack()}),1500),i.next=21;break;case 20:throw new Error(l.message||"发布失败");case 21:i.next=26;break;case 23:i.prev=23,i.t0=i["catch"](2),t.showToast({title:i.t0.message||"发布失败，请重试",icon:"error"});case 26:return i.prev=26,e.submitting=!1,i.finish(26);case 29:case"end":return i.stop()}}),i,null,[[2,23,26,29]])})))()},preparePhotosForSubmit:function(){return this.formData.images.filter((function(t){return t.uploaded&&t.cloudPath})).map((function(t){return{url:t.cloudPath,description:"月度检查发现的问题",timestamp:new Date}}))},getPriorityToSeverity:function(t){return{normal:"medium",urgent:"high"}[t]||"medium"},getResponsibleId:function(){if(this.selectedResponsibleIndex>0&&this.responsiblePersons[this.selectedResponsibleIndex]){var t=this.responsiblePersons[this.selectedResponsibleIndex].value;return t||null}return null},getResponsibleName:function(){if(this.selectedResponsibleIndex>0&&this.responsiblePersons[this.selectedResponsibleIndex]){var t=this.responsiblePersons[this.selectedResponsibleIndex].text;return"请选择负责人"!==t?t:null}return null},clearDraftIfExists:function(){var e=this;if(this.isEditingDraft&&this.currentDraftId)try{var n=t.getStorageSync("issue_drafts")||[],i=n.filter((function(t){return t.id!==e.currentDraftId}));t.setStorageSync("issue_drafts",i)}catch(o){}},saveDraft:function(){var e=this;return(0,s.default)(a.default.mark((function n(){var i;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(e.canSaveDraft){n.next=2;break}return n.abrupt("return");case 2:try{e.draftSaving=!0,i=d(d({},e.formData),{},{images:e.formData.images.map((function(t){return"string"===typeof t?t:t.uploaded&&t.cloudUrl?d(d({},t),{},{url:t.cloudUrl,displayUrl:t.cloudUrl}):t})),createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),status:"draft",id:Date.now()}),e.saveDraftToLocal(i),t.showToast({title:"草稿保存成功",icon:"success"}),t.$emit("issueDraftUpdated",{action:e.isEditingDraft?"update":"create",draftId:e.isEditingDraft?e.currentDraftId:i.id,draftData:i}),setTimeout((function(){t.navigateBack()}),1500)}catch(o){t.showToast({title:"保存失败，请重试",icon:"error"})}finally{e.draftSaving=!1}case 3:case"end":return n.stop()}}),n)})))()},saveDraftToLocal:function(e){var n=this;try{var i=t.getStorageSync("issue_drafts")||[];if(this.isEditingDraft){var o=i.findIndex((function(t){return t.id===n.currentDraftId}));-1!==o&&(i[o]=d(d({},e),{},{id:this.currentDraftId}))}else i.push(e);t.setStorageSync("issue_drafts",i)}catch(a){}},loadDraft:function(e){try{var n=t.getStorageSync("issue_drafts")||[],i=n.find((function(t){return t.id==e}));i&&(this.isEditingDraft=!0,this.currentDraftId=i.id,this.formData=d({},i),this.pendingEditData={responsible_id:i.responsible,priority:i.priority})}catch(o){}},loadEditData:function(e,n){try{var i=JSON.parse(decodeURIComponent(n));this.isEditingPublished=!0,this.currentEditId=e,this.formData={title:i.title||"",description:i.description||"",location:i.location||"",responsible:i.responsible_id||"",deadline:i.deadline||"",priority:i.priority||"normal",images:this.processEditImages(i.images||[])},this.pendingEditData=i}catch(o){t.showToast({title:"加载编辑数据失败",icon:"error"})}},setEditSelectors:function(t){if(t.responsible_id&&this.responsiblePersons.length>0){var e=this.responsiblePersons.findIndex((function(e){return e.value===t.responsible_id}));e>-1&&(this.selectedResponsibleIndex=e,this.formData.responsible=t.responsible_id)}if(t.priority){var n=this.priorityOptions.findIndex((function(e){return e.value===t.priority}));n>-1&&(this.selectedPriorityIndex=n)}},processEditImages:function(t){return Array.isArray(t)?t.map((function(t){return"string"===typeof t?{url:t,cloudUrl:t,cloudPath:t,uploaded:!0,uploading:!1}:t&&t.url?{url:t.url,cloudUrl:t.url,cloudPath:t.url,uploaded:!0,uploading:!1}:t})).filter(Boolean):[]},toggleAutoUpload:function(){this.autoUpload=!this.autoUpload,t.showToast({title:this.autoUpload?"已开启自动上传":"已关闭自动上传",icon:"none"})},hideSuggestions:function(){this.showLocationSuggestions=!1}}};e.default=f}).call(this,n("df3c")["default"],n("861b")["uniCloud"])}},[["130b","common/runtime","common/vendor"]]]);