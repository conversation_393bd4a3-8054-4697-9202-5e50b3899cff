{"bsonType": "object", "required": ["name", "category"], "permission": {"read": true, "create": "auth.role.includes('admin') || auth.role.includes('GM') || auth.role.includes('Integrated') || auth.role.includes('reviser')", "update": "auth.role.includes('admin') || auth.role.includes('GM') || auth.role.includes('Integrated') || auth.role.includes('reviser')", "delete": "auth.role.includes('admin')"}, "properties": {"_id": {"description": "ID，系统自动生成"}, "name": {"bsonType": "string", "title": "位置名称", "description": "位置的名称", "minLength": 2, "maxLength": 100, "errorMessage": {"required": "位置名称不能为空", "minLength": "位置名称长度不能小于 {minLength} 个字符", "maxLength": "位置名称长度不能大于 {maxLength} 个字符"}}, "category": {"bsonType": "string", "title": "位置类别", "description": "位置的分类", "enum": ["building", "floor", "workshop", "office", "warehouse", "outdoor", "facility", "equipment"], "errorMessage": {"required": "位置类别不能为空", "enum": "请选择有效的位置类别"}}, "parent_id": {"bsonType": "string", "title": "父级位置ID", "description": "上级位置的ID，用于构建层级关系", "foreignKey": "hygiene-locations._id"}, "level": {"bsonType": "number", "title": "层级", "description": "在位置层级中的级别（1为顶级）", "minimum": 1, "maximum": 10, "default": 1}, "code": {"bsonType": "string", "title": "位置编码", "description": "位置的唯一编码", "maxLength": 50}, "description": {"bsonType": "string", "title": "位置描述", "description": "位置的详细描述", "maxLength": 500}, "coordinates": {"bsonType": "object", "title": "坐标信息", "description": "位置的地理坐标", "properties": {"latitude": {"bsonType": "double", "title": "纬度", "minimum": -90, "maximum": 90}, "longitude": {"bsonType": "double", "title": "经度", "minimum": -180, "maximum": 180}, "altitude": {"bsonType": "double", "title": "海拔", "minimum": -1000, "maximum": 10000}}}, "area_size": {"bsonType": "object", "title": "区域大小", "description": "区域的尺寸信息", "properties": {"length": {"bsonType": "double", "title": "长度（米）", "minimum": 0}, "width": {"bsonType": "double", "title": "宽度（米）", "minimum": 0}, "height": {"bsonType": "double", "title": "高度（米）", "minimum": 0}, "total_area": {"bsonType": "double", "title": "总面积（平方米）", "minimum": 0}}}, "properties": {"bsonType": "object", "title": "位置属性", "description": "位置的特殊属性", "properties": {"access_level": {"bsonType": "string", "title": "访问级别", "enum": ["public", "restricted", "private", "confidential"], "default": "public"}, "safety_level": {"bsonType": "string", "title": "安全级别", "enum": ["low", "medium", "high", "critical"], "default": "medium"}, "cleaning_difficulty": {"bsonType": "string", "title": "清洁难度", "enum": ["easy", "medium", "hard", "very_hard"], "default": "medium"}, "required_tools": {"bsonType": "array", "title": "所需工具", "description": "清洁该位置所需的工具清单", "items": {"bsonType": "string", "maxLength": 50}, "maxItems": 20}, "special_requirements": {"bsonType": "array", "title": "特殊要求", "description": "清洁该位置的特殊要求", "items": {"bsonType": "string", "maxLength": 200}, "maxItems": 10}}}, "contact_info": {"bsonType": "object", "title": "联系信息", "description": "该位置的相关联系信息", "properties": {"responsible_person": {"bsonType": "string", "title": "负责人", "maxLength": 50}, "phone": {"bsonType": "string", "title": "联系电话", "maxLength": 20}, "extension": {"bsonType": "string", "title": "分机号", "maxLength": 10}}}, "operating_hours": {"bsonType": "object", "title": "运营时间", "description": "该位置的运营时间", "properties": {"start_time": {"bsonType": "string", "title": "开始时间", "pattern": "^\\d{2}:\\d{2}$"}, "end_time": {"bsonType": "string", "title": "结束时间", "pattern": "^\\d{2}:\\d{2}$"}, "days_of_week": {"bsonType": "array", "title": "工作日", "items": {"bsonType": "number", "minimum": 1, "maximum": 7}, "maxItems": 7}, "is_24_hours": {"bsonType": "bool", "title": "是否24小时", "default": false}}}, "maintenance_info": {"bsonType": "object", "title": "维护信息", "description": "位置的维护相关信息", "properties": {"last_maintenance": {"bsonType": "timestamp", "title": "最后维护时间"}, "next_maintenance": {"bsonType": "timestamp", "title": "下次维护时间"}, "maintenance_frequency": {"bsonType": "string", "title": "维护频率", "enum": ["daily", "weekly", "monthly", "quarterly", "yearly"]}, "maintenance_notes": {"bsonType": "string", "title": "维护备注", "maxLength": 500}}}, "is_active": {"bsonType": "bool", "title": "是否有效", "description": "该位置是否当前有效", "default": true}, "sort_order": {"bsonType": "number", "title": "排序", "description": "显示时的排序顺序", "minimum": 0, "default": 0}, "tags": {"bsonType": "array", "title": "标签", "description": "位置的标签，便于分类和搜索", "items": {"bsonType": "string", "maxLength": 20}, "maxItems": 10}, "images": {"bsonType": "array", "title": "位置图片", "description": "该位置的图片", "items": {"bsonType": "string", "pattern": "^cloud://"}, "maxItems": 10}, "creator_id": {"bsonType": "string", "title": "创建人", "description": "创建该位置记录的用户ID", "foreignKey": "uni-id-users._id"}, "created_at": {"bsonType": "timestamp", "title": "创建时间", "description": "记录创建时间", "forceDefaultValue": {"$env": "now"}}, "updated_at": {"bsonType": "timestamp", "title": "更新时间", "description": "记录最后更新时间", "forceDefaultValue": {"$env": "now"}}}}