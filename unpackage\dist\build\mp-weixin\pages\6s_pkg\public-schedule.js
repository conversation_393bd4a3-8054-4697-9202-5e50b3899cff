require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/public-schedule"],{"12c5":function(e,t,a){"use strict";(function(e,t){var n=a("47a9");a("357b"),a("861b");n(a("3240"));var r=n(a("4ab5"));e.__webpack_require_UNI_MP_PLUGIN__=a,t(r.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},"2e66":function(e,t,a){"use strict";var n=a("de80"),r=a.n(n);r.a},"3c6d":function(e,t,a){"use strict";(function(e){var n=a("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("7eb4")),u=n(a("7ca3")),l=n(a("ee10")),s=a("882c");function i(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function c(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?i(Object(a),!0).forEach((function(t){(0,u.default)(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):i(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}var d={name:"PublicSchedule",data:function(){return{areaList:[],loading:!1,saving:!1,currentArea:null,scheduleData:{scheduled_day:null,start_date:""},filterOptions:[{value:"all",label:"全部区域"},{value:"scheduled",label:"已排班"},{value:"unscheduled",label:"未排班"},{value:"today",label:"今日清扫"}],weekDayOptions:[{value:null,label:"请选择清扫日期"},{value:1,label:"每周一"},{value:2,label:"每周二"},{value:3,label:"每周三"},{value:4,label:"每周四"},{value:5,label:"每周五"},{value:6,label:"每周六"},{value:0,label:"每周日"}],filterIndex:0,scheduleWeekIndex:0}},computed:{filteredAreas:function(){var e=this.filterOptions[this.filterIndex].value;switch(e){case"scheduled":return this.areaList.filter((function(e){return null!==e.scheduled_day}));case"unscheduled":return this.areaList.filter((function(e){return null===e.scheduled_day}));case"today":var t=(new Date).getDay();return this.areaList.filter((function(e){return e.scheduled_day===t}));default:return this.areaList}},weekDays:function(){var e=this,t=new Date,a=[],n=new Date(t),r=t.getDay(),u=0===r?-6:1-r;n.setDate(t.getDate()+u);for(var l=["周日","周一","周二","周三","周四","周五","周六"],s=function(r){var u=new Date(n);u.setDate(n.getDate()+r);var s=u.getDay();a.push({name:l[s],date:u.getDate(),isToday:u.toDateString()===t.toDateString(),areas:e.areaList.filter((function(e){return e.scheduled_day===s}))})},i=0;i<7;i++)s(i);return a}},onLoad:function(){this.loadData()},methods:{getPreviewState:function(){var e=null!==this.scheduleData.scheduled_day&&void 0!==this.scheduleData.scheduled_day&&"number"===typeof this.scheduleData.scheduled_day,t=this.scheduleData.start_date&&this.scheduleData.start_date.trim();return e&&t?"complete":"incomplete"},hasValidSchedule:function(e){return null!==e.scheduled_day&&void 0!==e.scheduled_day&&"number"===typeof e.scheduled_day&&e.scheduled_day>=0&&e.scheduled_day<=6},loadData:function(){var t=this;return(0,l.default)(r.default.mark((function a(){var n;return r.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,t.loading=!0,a.next=4,(0,s.callCloudFunction)("hygiene-area-management",{action:"getAreaList",data:{type:"public"}});case 4:n=a.sent,t.areaList=(n.data.list||[]).filter((function(e){return"active"===e.status})).map((function(e){return c(c({},e),{},{next_clean_date:t.calculateNextCleanDate(e)})})),a.next=12;break;case 8:a.prev=8,a.t0=a["catch"](0),console.error("加载数据失败：",a.t0),e.showToast({title:"加载失败",icon:"none"});case 12:return a.prev=12,t.loading=!1,a.finish(12);case 15:case"end":return a.stop()}}),a,null,[[0,8,12,15]])})))()},calculateNextCleanDate:function(e){if(null===e.scheduled_day||void 0===e.scheduled_day)return null;var t=new Date,a=e.scheduled_day,n=e.start_date?new Date(e.start_date):new Date(t);if(e.start_date&&n>t){while(n.getDay()!==a)n.setDate(n.getDate()+1);return n.toISOString().split("T")[0]}var r=new Date(t);while(r.getDay()!==a)r.setDate(r.getDate()+1);return r.getDay()===a&&r.toDateString()===t.toDateString()&&t.getHours()>=12&&r.setDate(r.getDate()+7),r.toISOString().split("T")[0]},getNextMonday:function(){var e=new Date,t=new Date(e),a=(8-e.getDay())%7;return t.setDate(e.getDate()+(0===a?7:a)),t.toISOString().split("T")[0]},onFilterChange:function(e){this.filterIndex=e.detail.value},setSchedule:function(e){this.currentArea=e,this.scheduleData={scheduled_day:e.scheduled_day,start_date:e.start_date||this.getNextMonday()};var t=this.weekDayOptions.findIndex((function(t){return t.value===e.scheduled_day}));this.scheduleWeekIndex=t>=0?t:0,this.$refs.schedulePopup.open()},clearSchedule:function(t){var a=this;e.showModal({title:"确认清除",content:'确定要清除"'.concat(t.name,'"的排班设置吗？'),confirmText:"确定",confirmColor:"#FF3B30",success:function(e){e.confirm&&a.performClearSchedule(t)}})},performClearSchedule:function(t){var a=this;return(0,l.default)(r.default.mark((function n(){var u,l;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,(0,s.callCloudFunction)("hygiene-area-management",{action:"updateArea",data:{id:t._id||t.id,scheduled_day:null,start_date:null}});case 3:u=a.areaList.findIndex((function(e){return(e._id||e.id)===(t._id||t.id)})),u>-1&&(l=c(c({},a.areaList[u]),{},{scheduled_day:null,start_date:null,next_clean_date:null,updated_at:(new Date).toISOString()}),a.$set(a.areaList,u,l)),e.showToast({title:"排班已清除",icon:"success"}),n.next=12;break;case 8:n.prev=8,n.t0=n["catch"](0),console.error("清除排班失败：",n.t0),e.showToast({title:n.t0.message||"操作失败",icon:"none"});case 12:case"end":return n.stop()}}),n,null,[[0,8]])})))()},onScheduleWeekChange:function(e){this.scheduleWeekIndex=parseInt(e.detail.value);var t=this.weekDayOptions[this.scheduleWeekIndex];t?this.scheduleData.scheduled_day=t.value:(this.scheduleWeekIndex=0,this.scheduleData.scheduled_day=null)},onStartDateChange:function(e){this.scheduleData.start_date=e.detail.value},submitSchedule:function(){var t=this;return(0,l.default)(r.default.mark((function a(){var n,u;return r.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(null!==t.scheduleData.scheduled_day&&void 0!==t.scheduleData.scheduled_day){a.next=3;break}return e.showToast({title:"请选择清扫日期",icon:"none"}),a.abrupt("return");case 3:if(t.scheduleData.start_date){a.next=6;break}return e.showToast({title:"请选择开始日期",icon:"none"}),a.abrupt("return");case 6:return a.prev=6,t.saving=!0,a.next=10,(0,s.callCloudFunction)("hygiene-area-management",{action:"updateArea",data:{id:t.currentArea._id||t.currentArea.id,scheduled_day:t.scheduleData.scheduled_day,start_date:t.scheduleData.start_date}});case 10:n=t.areaList.findIndex((function(e){return(e._id||e.id)===(t.currentArea._id||t.currentArea.id)})),n>-1&&(u=c(c({},t.areaList[n]),{},{scheduled_day:t.scheduleData.scheduled_day,start_date:t.scheduleData.start_date,next_clean_date:t.calculateNextCleanDate(c(c({},t.areaList[n]),{},{scheduled_day:t.scheduleData.scheduled_day,start_date:t.scheduleData.start_date})),updated_at:(new Date).toISOString()}),t.$set(t.areaList,n,u)),"排班设置成功",e.showToast({title:"排班设置成功",icon:"success"}),t.closeSchedulePopup(),a.next=21;break;case 17:a.prev=17,a.t0=a["catch"](6),console.error("排班设置失败：",a.t0),e.showToast({title:a.t0.message||"设置失败",icon:"none"});case 21:return a.prev=21,t.saving=!1,a.finish(21);case 24:case"end":return a.stop()}}),a,null,[[6,17,21,24]])})))()},closeSchedulePopup:function(){var e=this;this.$refs.schedulePopup.close(),setTimeout((function(){e.resetPopupState()}),350)},resetPopupState:function(){this.currentArea=null,this.scheduleData={scheduled_day:null,start_date:""},this.scheduleWeekIndex=0},getWeekDayText:function(e){if(null===e||void 0===e||"number"!==typeof e)return"未知";return{0:"周日",1:"周一",2:"周二",3:"周三",4:"周四",5:"周五",6:"周六"}[e]||"未知"},isToday:function(e){if(!e)return!1;var t=(new Date).toISOString().split("T")[0],a="string"===typeof e?e.split("T")[0]:e.toISOString().split("T")[0];return t===a},formatDate:function(e){if(!e)return"";try{var t=new Date(e);if(isNaN(t.getTime()))return"";var a=new Date,n=new Date(a);n.setDate(a.getDate()+1);var r=t.toISOString().split("T")[0],u=a.toISOString().split("T")[0],l=n.toISOString().split("T")[0];return r===u?"今天":r===l?"明天":"".concat(t.getMonth()+1,"-").concat(String(t.getDate()).padStart(2,"0"))}catch(s){return console.error("日期格式化错误：",s),""}},formatFullDate:function(e){if(!e)return"";var t=new Date(e);return"".concat(t.getFullYear(),"年").concat(t.getMonth()+1,"月").concat(t.getDate(),"日")},getEmptyText:function(){var e=this.filterOptions[this.filterIndex].value;switch(e){case"scheduled":return"暂无已排班区域";case"unscheduled":return"暂无未排班区域";case"today":return"今日无清扫安排";default:return"暂无公共责任区"}},getEmptyDesc:function(){var e=this.filterOptions[this.filterIndex].value;switch(e){case"scheduled":return'可以切换到"未排班"查看需要设置的区域';case"unscheduled":return"所有公共责任区都已完成排班设置";case"today":return"可以查看其他日期的清扫安排";default:return"请先在公共责任区管理中创建区域"}}}};t.default=d}).call(this,a("df3c")["default"])},"4ab5":function(e,t,a){"use strict";a.r(t);var n=a("d3eb"),r=a("b2ff");for(var u in r)["default"].indexOf(u)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(u);a("2e66");var l=a("828b"),s=Object(l["a"])(r["default"],n["b"],n["c"],!1,null,"23f29288",null,!1,n["a"],void 0);t["default"]=s.exports},b2ff:function(e,t,a){"use strict";a.r(t);var n=a("3c6d"),r=a.n(n);for(var u in n)["default"].indexOf(u)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(u);t["default"]=r.a},d3eb:function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return u})),a.d(t,"a",(function(){return n}));var n={uniIcons:function(){return Promise.all([a.e("common/vendor"),a.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(a.bind(null,"6ddf"))},pEmptyState:function(){return a.e("components/p-empty-state/p-empty-state").then(a.bind(null,"9b76"))},uniPopup:function(){return a.e("uni_modules/uni-popup/components/uni-popup/uni-popup").then(a.bind(null,"a2b7"))}},r=function(){var e=this,t=e.$createElement,a=(e._self._c,e.__map(e.weekDays,(function(t,a){var n=e.__get_orig(t),r=t.areas.length,u=t.areas.length,l=u>0?t.areas.length:null;return{$orig:n,g0:r,g1:u,g2:l}}))),n=e.loading?null:e.filteredAreas.length,r=!e.loading&&n>0?e.__map(e.filteredAreas,(function(t,a){var n=e.__get_orig(t),r=e.hasValidSchedule(t),u=r?e.getWeekDayText(t.scheduled_day):null,l=e.hasValidSchedule(t),s=l?e.isToday(t.next_clean_date):null,i=l?e.formatDate(t.next_clean_date):null;return{$orig:n,m0:r,m1:u,m2:l,m3:s,m4:i}})):null,u=e.loading||n>0?null:!e.loading&&e.areaList.length>0&&0===e.filteredAreas.length,l=e.loading||n>0||u?null:!e.loading&&0===e.areaList.length,s=e.loading||n>0||u||!l?null:e.getEmptyText(),i=e.loading||n>0||u||!l?null:e.getEmptyDesc(),c=e.currentArea?e.getPreviewState():null,d=e.currentArea&&"complete"===c?e.formatFullDate(e.scheduleData.start_date):null,o=e.currentArea&&"complete"===c?e.getWeekDayText(e.scheduleData.scheduled_day):null,h=e.currentArea&&"complete"!==c?e.getPreviewState():null,f=e.currentArea&&"complete"!==c&&"clear"!==h&&e.scheduleData.start_date?e.formatFullDate(e.scheduleData.start_date):null;e.$mp.data=Object.assign({},{$root:{l0:a,g3:n,l1:r,g4:u,g5:l,m5:s,m6:i,m7:c,m8:d,m9:o,m10:h,m11:f}})},u=[]},de80:function(e,t,a){}},[["12c5","common/runtime","common/vendor"]]]);