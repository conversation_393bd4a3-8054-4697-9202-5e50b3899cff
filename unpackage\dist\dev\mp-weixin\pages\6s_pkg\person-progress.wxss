@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container.data-v-1ef03a4e {
  min-height: 100vh;
  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
  width: 100%;
  overflow-x: hidden;
  /* 防止水平滚动 */
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
}
.content.data-v-1ef03a4e {
  padding: 32rpx;
  padding-bottom: 120rpx;
}
/* 卡片通用样式 */
.person-card.data-v-1ef03a4e,
.issues-card.data-v-1ef03a4e,
.chart-card.data-v-1ef03a4e {
  background: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}
/* 筛选卡片特殊样式 - 控制溢出行为 */
.filter-card.data-v-1ef03a4e {
  background: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}
/* 负责人信息卡片 */
.person-card.data-v-1ef03a4e {
  padding: 32rpx;
}
.person-header.data-v-1ef03a4e {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 32rpx;
}
.person-avatar.data-v-1ef03a4e {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: rgba(0, 122, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}
.person-info.data-v-1ef03a4e {
  flex: 1;
}
.person-name.data-v-1ef03a4e {
  font-size: 36rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 8rpx;
}
.person-role.data-v-1ef03a4e {
  font-size: 26rpx;
  color: #8E8E93;
}
.progress-summary.data-v-1ef03a4e {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;
  margin-bottom: 32rpx;
  text-align: center;
}
.summary-item.data-v-1ef03a4e {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.summary-number.data-v-1ef03a4e {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.summary-number.primary.data-v-1ef03a4e {
  color: #007AFF;
}
.summary-number.success.data-v-1ef03a4e {
  color: #34C759;
}
.summary-number.warning.data-v-1ef03a4e {
  color: #FF9500;
}
.summary-number.danger.data-v-1ef03a4e {
  color: #FF3B30;
}
.summary-label.data-v-1ef03a4e {
  font-size: 24rpx;
  color: #8E8E93;
}
.completion-rate.data-v-1ef03a4e {
  padding-top: 32rpx;
  border-top: 1rpx solid #F0F0F0;
}
.rate-info.data-v-1ef03a4e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.rate-label.data-v-1ef03a4e {
  font-size: 28rpx;
  color: #1C1C1E;
  font-weight: 500;
}
.rate-value.data-v-1ef03a4e {
  font-size: 32rpx;
  font-weight: bold;
  color: #007AFF;
}
.rate-bar.data-v-1ef03a4e {
  height: 12rpx;
  background: #F0F0F0;
  border-radius: 6rpx;
  overflow: hidden;
}
.rate-fill.data-v-1ef03a4e {
  height: 100%;
  background: linear-gradient(90deg, #007AFF 0%, #34C759 100%);
  border-radius: 6rpx;
  transition: width 0.5s ease;
}
/* 筛选卡片 */
.filter-card.data-v-1ef03a4e {
  overflow: hidden;
}
.filter-card .card-header.data-v-1ef03a4e {
  padding: 32rpx 32rpx 24rpx 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}
.filter-tabs.data-v-1ef03a4e {
  white-space: nowrap;
  padding: 12rpx 0 0 0;
  margin-top: 0rpx;
}
.filter-tabs.data-v-1ef03a4e::-webkit-scrollbar {
  display: none;
}
.filter-tab.data-v-1ef03a4e {
  display: inline-block;
  padding: 12rpx 20rpx;
  border: 2rpx solid #E5E5E5;
  background: #ffffff;
  border-radius: 24rpx;
  font-size: 24rpx;
  color: #8E8E93;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  letter-spacing: 0.3rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  white-space: nowrap;
  margin: 4rpx 12rpx 4rpx 0;
}
.filter-tab.active.data-v-1ef03a4e {
  border-color: #007AFF;
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  color: #ffffff;
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(0, 122, 255, 0.3);
}
.filter-tab.data-v-1ef03a4e:hover:not(.active) {
  border-color: #B3D9FF;
  background: #F8FBFF;
  -webkit-transform: translateY(-1rpx);
          transform: translateY(-1rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}
/* 问题列表卡片 */
.issues-card.data-v-1ef03a4e {
  overflow: hidden;
}
.card-header.data-v-1ef03a4e {
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}
.card-title.data-v-1ef03a4e {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 4rpx;
}
.card-subtitle.data-v-1ef03a4e {
  font-size: 24rpx;
  color: #8E8E93;
}
.issues-list.data-v-1ef03a4e {
  padding: 0;
}
.issue-item.data-v-1ef03a4e {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}
.issue-item.data-v-1ef03a4e:last-child {
  border-bottom: none;
}
.issue-icon.data-v-1ef03a4e {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}
.issue-icon.icon-bg-approved.data-v-1ef03a4e {
  background: rgba(52, 199, 89, 0.1);
}
.issue-icon.icon-bg-in_progress.data-v-1ef03a4e {
  background: rgba(255, 149, 0, 0.1);
}
.issue-icon.icon-bg-overdue.data-v-1ef03a4e {
  background: rgba(255, 59, 48, 0.1);
}
.issue-icon.icon-bg-pending.data-v-1ef03a4e {
  background: rgba(0, 122, 255, 0.1);
}
.issue-icon.icon-bg-assigned.data-v-1ef03a4e {
  background: rgba(0, 122, 255, 0.1);
}
.issue-icon.icon-bg-pending_review.data-v-1ef03a4e {
  background: rgba(88, 86, 214, 0.1);
}
.issue-icon.icon-bg-approved.data-v-1ef03a4e {
  background: rgba(52, 199, 89, 0.1);
}
.issue-icon.icon-bg-rejected.data-v-1ef03a4e {
  background: rgba(255, 149, 0, 0.1);
}
.issue-number.data-v-1ef03a4e {
  font-size: 28rpx;
  font-weight: bold;
  color: #1C1C1E;
}
.issue-icon.icon-bg-approved .issue-number.data-v-1ef03a4e {
  color: #34C759;
}
.issue-icon.icon-bg-in_progress .issue-number.data-v-1ef03a4e {
  color: #FF9500;
}
.issue-icon.icon-bg-overdue .issue-number.data-v-1ef03a4e {
  color: #FF3B30;
}
.issue-icon.icon-bg-pending .issue-number.data-v-1ef03a4e {
  color: #007AFF;
}
.issue-icon.icon-bg-assigned .issue-number.data-v-1ef03a4e {
  color: #007AFF;
}
.issue-icon.icon-bg-pending_review .issue-number.data-v-1ef03a4e {
  color: #5856D6;
}
.issue-icon.icon-bg-approved .issue-number.data-v-1ef03a4e {
  color: #34C759;
}
.issue-icon.icon-bg-rejected .issue-number.data-v-1ef03a4e {
  color: #FF9500;
}
.issue-content.data-v-1ef03a4e {
  flex: 1;
}
.issue-title.data-v-1ef03a4e {
  font-size: 30rpx;
  font-weight: 500;
  color: #1C1C1E;
  margin-bottom: 8rpx;
}
.issue-location.data-v-1ef03a4e,
.issue-deadline.data-v-1ef03a4e {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 4rpx;
}
.issue-right.data-v-1ef03a4e {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.issue-status.data-v-1ef03a4e {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 600;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.08);
}
.issue-status.status-approved.data-v-1ef03a4e {
  background: linear-gradient(135deg, #E8F5E8, #F0FFF0);
  color: #34C759;
  border: 2rpx solid rgba(52, 199, 89, 0.2);
}
.issue-status.status-in_progress.data-v-1ef03a4e {
  background: linear-gradient(135deg, #FFF4E6, #FFFBF0);
  color: #FF9500;
  border: 2rpx solid rgba(255, 149, 0, 0.2);
}
.issue-status.status-overdue.data-v-1ef03a4e {
  background: linear-gradient(135deg, #FFE6E6, #FFF0F0);
  color: #FF3B30;
  border: 2rpx solid rgba(255, 59, 48, 0.2);
}
.issue-status.status-pending.data-v-1ef03a4e {
  background: linear-gradient(135deg, #E6F3FF, #F0F7FF);
  color: #007AFF;
  border: 2rpx solid rgba(0, 122, 255, 0.2);
}
.issue-status.status-assigned.data-v-1ef03a4e {
  background: linear-gradient(135deg, #ECFEFF, #F0FDFF);
  color: #0891B2;
  border: 2rpx solid rgba(8, 145, 178, 0.2);
}
.issue-status.status-pending_review.data-v-1ef03a4e {
  background: linear-gradient(135deg, #F0EFFF, #F8F7FF);
  color: #5856D6;
  border: 2rpx solid rgba(88, 86, 214, 0.2);
}
.issue-status.status-approved.data-v-1ef03a4e {
  background: linear-gradient(135deg, #E8F5E8, #F0FFF0);
  color: #34C759;
  border: 2rpx solid rgba(52, 199, 89, 0.2);
}
.issue-status.status-rejected.data-v-1ef03a4e {
  background: linear-gradient(135deg, #F2F2F7, #F8F8F8);
  color: #8E8E93;
  border: 2rpx solid rgba(142, 142, 147, 0.2);
}
/* 查看更多按钮样式 */
.more-section.data-v-1ef03a4e {
  padding: 28rpx;
  border-top: 1rpx solid #F0F0F0;
}
.more-btn.data-v-1ef03a4e {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 28rpx;
  font-size: 30rpx;
  color: #007AFF;
  border: 2rpx solid rgba(0, 122, 255, 0.2);
  border-radius: 16rpx;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(255, 255, 255, 0.9));
  font-weight: 600;
  letter-spacing: 0.5rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.more-btn.data-v-1ef03a4e:active {
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
  background: linear-gradient(135deg, rgba(230, 243, 255, 0.9), rgba(240, 247, 255, 0.95));
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.15);
  transition: all 0.1s ease;
}
.more-btn.data-v-1ef03a4e:not(:active) {
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
/* 底部安全间距 */
.bottom-safe-area.data-v-1ef03a4e {
  height: 40rpx;
}
/* 加载状态 - 自定义遮罩 */
.custom-loading-mask.data-v-1ef03a4e {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  -webkit-backdrop-filter: blur(8rpx);
          backdrop-filter: blur(8rpx);
  z-index: 9999;
  -webkit-animation: maskFadeIn-data-v-1ef03a4e 0.3s ease-out;
          animation: maskFadeIn-data-v-1ef03a4e 0.3s ease-out;
  /* 确保相对于视口定位，而不是父容器 */
  margin: 0;
  padding: 0;
}
@-webkit-keyframes maskFadeIn-data-v-1ef03a4e {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes maskFadeIn-data-v-1ef03a4e {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.loading-container-enhanced.data-v-1ef03a4e {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 32rpx;
  padding: 80rpx 60rpx;
  background: rgba(255, 255, 255, 0.98);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  min-width: 320rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.25), 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
  -webkit-animation: containerSlideIn-data-v-1ef03a4e 0.4s ease-out 0.1s both;
          animation: containerSlideIn-data-v-1ef03a4e 0.4s ease-out 0.1s both;
}
@-webkit-keyframes containerSlideIn-data-v-1ef03a4e {
from {
    opacity: 0;
    -webkit-transform: translateY(20rpx) scale(0.95);
            transform: translateY(20rpx) scale(0.95);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0) scale(1);
            transform: translateY(0) scale(1);
}
}
@keyframes containerSlideIn-data-v-1ef03a4e {
from {
    opacity: 0;
    -webkit-transform: translateY(20rpx) scale(0.95);
            transform: translateY(20rpx) scale(0.95);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0) scale(1);
            transform: translateY(0) scale(1);
}
}
.loading-text-enhanced.data-v-1ef03a4e {
  font-size: 32rpx;
  color: #1C1C1E;
  font-weight: 600;
  margin-top: 8rpx;
  text-align: center;
  line-height: 1.3;
}
