{"version": 3, "sources": ["webpack:///D:/Xwzc/uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.vue?c6de", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.vue?3043", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.vue?f1ef", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.vue?9efd", "uni-app:///uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.vue", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.vue?1b62", "webpack:///D:/Xwzc/uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.vue?9899"], "names": ["computed", "agreements", "config", "serviceUrl", "privacyUrl", "url", "title", "agree", "get", "set", "data", "servicesList", "univerifyStyle", "watch", "created", "loginTypes", "id", "logo", "path", "methods", "getParentComponent", "setUserInfo", "getRoute", "toPage", "uni", "animationType", "complete", "login_before", "navigateBack", "options", "icon", "duration", "needAgreements", "type", "agreementsRef", "mask", "closeUniverify", "univerifyManager", "clickAnotherButtons", "onButtonsClickFn", "checkBoxState", "setTimeout", "success", "fail", "phoneCode", "provider", "res", "Object", "code", "console", "content", "confirmText", "showCancel", "login", "customUI", "uniIdCo", "mutations", "catch", "finally", "getUserInfo", "e", "resolve", "reject"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC0E;AACL;AACc;;;AAGnF;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,4FAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAgnB,CAAgB,0oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACapoB;AAEA;AAAA;AAAA;AACA;AAAA,eACA;EACAA;IACAC;MACA;QACA;MACA;MACA,yBAGAC;QAFAC;QACAC;MAEA;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;IACA;IACAC;MACAC;QACA;MACA;MACAC;QACA;MACA;IACA;EACA;EACAC;IACA;MACAC;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;MACA,EAgDA;MACAC;QAAA;QACA;QAAA;QACA;QAAA;QACA;UAAA;UACA;UAAA;UACA;QACA;QACA;UACA;UAAA;UACA;UAAA;UACA;UAAA;UACA;UAAA;UACA;UAAA;UACA;QACA;MACA;IACA;EACA;EACAC;IACAN;MACA;IACA;EACA;EACAO;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACAH;cACAI;cAEAJ;gBAGA;gBACA;kBACA;gBACA;gBAUA;cACA;cACA;cACA;gBACA;gBACA;gBACAA,qCAIA;kBAAA,IAHAK;oBACAC;oBACAC;kBAEA;oBACA;sBACA;sBACA;sBACAA;oBACA;kBACA;gBACA;cACA;;cAGA;cACA;gBACA;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IACAC;MAEA;IAMA;IACAC,sCAEA;IACAC;MAAA;MACA;MACA;QACA;MACA;MACA;IACA;IACAC;MAAA;MAEA,kEACA;QACA;QACA;QACAC;MACA;QAAA;QACAA;MACA;QACA;UACAA;YACAnB;YACAoB;YACAC,gCAEA;UACA;QACA;UACAF;YACAnB;YACAoB;YACAC,gCAEA;UACA;QACA;MACA,QAEA;IACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAAC;gBAAA,KAGA,OACA,UACA,aACA,UACA,YACA,UACA,UACA,SACA;kBAAA;kBAAA;gBAAA;gBAAA,kCACAL;kBACAlB;kBACAwB;kBACAC;gBACA;cAAA;gBAAA;gBAqCA;;gBAEA;kBAAA;kBAAA;gBAAA;gBAAA,kCAIAP;kBACAlB;kBACAwB;kBACAC;gBACA;cAAA;gBAEA;gBACAC;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBACAC;gBAAA,kCACAA;kBACA;gBACA;cAAA;gBA2CAV;kBAIAW;gBACA;gBAAA,MAEAF;kBAAA;kBAAA;gBAAA;gBAuCAG;kBACAZ;kBACAa;kBACA;kBACAA;gBACA,GACA;gBA5CAA;gBACAC;gBACAC;kBAAA;oBAAA;oBAAA;sBAAA;wBAAA;0BAAA;4BAEAD;4BAAA;4BAAA,OACAd;0BAAA;4BAAAgB;4BACA;;4BAEA;4BAMAtB,OACA,8CADAA;4BAEA;8BACA;gCACA;8BACA;8BACA;8BACAkB;4BACA;8BACA;gCACAA;gCACAK;kCACA;gCACA;8BACA;gCACAjB;kCACAlB;kCACAwB;kCACAC;gCACA;8BACA;4BACA;0BAAA;0BAAA;4BAAA;wBAAA;sBAAA;oBAAA;kBAAA,CACA;kBAAA,gBAlCAQ;oBAAA;kBAAA;gBAAA;gBA2CAF;gBACA;gBAAA,kCACAA;kBACA;kBACAK;oBACA;kBACA;kBACAC;oBAEA;sBACAnB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;kBACA;;kBACAE;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BACAF;8BACA;8BACA;8BACA;8BACAa;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CACA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;gBAAA,MAGAJ;kBAAA;kBAAA;gBAAA;gBAAA,kCACA;kBACAW;gBACA;cAAA;gBAGApB;kBACA;kBACA;kBAIAkB;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,MACAT;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA,OACA;gCACAY;8BACA;4BAAA;8BAFAC;8BAGAC;8BACAvB;4BAAA;8BAGA;gCACAwB;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CACA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;kBACAL;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BACAM;8BACAzB;gCACA0B;gCACAC;gCACAC;8BACA;8BACA5B;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CACA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA6B;MAAA;;MAGA;MACA;MACA;QACAC;MACA;MACAC;QACA/B;UACAlB;UACAwB;UACAC;QACA;QAIAyB;MACA,GACAC;QACAjC;UACA0B;UACAC;UACAC;QACA;MACA,GACAM;QACA;UACAlC;QACA;QACAA;MACA;IACA;IACAmC;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;kBACAnC,gDACAoC;oBACAlB;sBACAmB;oBACA;oBACAlB;sBACAnB;wBACA0B;wBACAE;sBACA;sBACAU;oBACA;kBAAA,GACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjhBA;AAAA;AAAA;AAAA;AAA2pC,CAAgB,ioCAAG,EAAC,C;;;;;;;;;;;ACA/qC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-id-pages-fab-login.vue?vue&type=template&id=5810e2be&\"\nvar renderjs\nimport script from \"./uni-id-pages-fab-login.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-id-pages-fab-login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-id-pages-fab-login.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-id-pages-fab-login.vue?vue&type=template&id=5810e2be&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, item) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item\n      var _temp, _temp2\n      item.path ? _vm.toPage(item.path) : _vm.login_before(item.id, false)\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-id-pages-fab-login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-id-pages-fab-login.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<view class=\"fab-login-box\">\n\t\t\t<view class=\"item\" v-for=\"(item,index) in servicesList\" :key=\"index\"\n\t\t\t\t@click=\"item.path?toPage(item.path):login_before(item.id,false)\"\n\t\t\t\thover-class=\"none\">\n\t\t\t\t<image class=\"logo\" :src=\"item.logo\" mode=\"scaleToFill\"></image>\n\t\t\t\t<text class=\"login-title\">{{item.text}}</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n<script>\n\timport config from '@/uni_modules/uni-id-pages/config.js'\n\t//前一个窗口的页面地址。控制点击切换快捷登录方式是创建还是返回\n\timport {store,mutations} from '@/uni_modules/uni-id-pages/common/store.js'\n\tlet allServicesList = []\n\texport default {\n\t\tcomputed: {\n\t\t\tagreements() {\n\t\t\t\tif (!config.agreements) {\n\t\t\t\t\treturn []\n\t\t\t\t}\n\t\t\t\tlet {\n\t\t\t\t\tserviceUrl,\n\t\t\t\t\tprivacyUrl\n\t\t\t\t} = config.agreements\n\t\t\t\treturn [{\n\t\t\t\t\t\turl: serviceUrl,\n\t\t\t\t\t\ttitle: \"用户服务协议\"\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\turl: privacyUrl,\n\t\t\t\t\t\ttitle: \"隐私政策条款\"\n\t\t\t\t\t}\n\t\t\t\t]\n\t\t\t},\n\t\t\tagree: {\n\t\t\t\tget() {\n\t\t\t\t\treturn this.getParentComponent().agree\n\t\t\t\t},\n\t\t\t\tset(agree) {\n\t\t\t\t\treturn this.getParentComponent().agree = agree\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tservicesList: [{\n\t\t\t\t\t\t\"id\": \"username\",\n\t\t\t\t\t\t\"text\": \"账号登录\",\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/login/uni-fab-login/user.png\",\n\t\t\t\t\t\t\"path\": \"/uni_modules/uni-id-pages/pages/login/login-withpwd\"\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\t\"id\": \"smsCode\",\n\t\t\t\t\t\t\"text\": \"短信验证码\",\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/login/uni-fab-login/sms.png\",\n\t\t\t\t\t\t\"path\": \"/uni_modules/uni-id-pages/pages/login/login-withoutpwd?type=smsCode\"\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\t\"id\": \"weixin\",\n\t\t\t\t\t\t\"text\": \"微信登录\",\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/login/uni-fab-login/weixin.png\",\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\t\"id\": \"huawei\",\n\t\t\t\t\t\t\"text\": \"华为登录\",\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/login/uni-fab-login/huawei.png\",\n\t\t\t\t\t\t\"path\": \"/uni_modules/uni-id-pages/pages/login/login-withoutpwd?type=huawei\"\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\t\"id\": \"huaweiMobile\",\n\t\t\t\t\t\t\"text\": \"华为账号一键登录\",\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/login/uni-fab-login/huawei.png\",\n\t\t\t\t\t\t\"path\": \"/uni_modules/uni-id-pages/pages/login/login-withoutpwd?type=huaweiMobile\"\n\t\t\t\t\t},\n\t\t\t\t\t// #ifndef MP-WEIXIN\n\t\t\t\t\t{\n\t\t\t\t\t\t\"id\": \"apple\",\n\t\t\t\t\t\t\"text\": \"苹果登录\",\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/uni-fab-login/apple.png\",\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\t\"id\": \"univerify\",\n\t\t\t\t\t\t\"text\": \"一键登录\",\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/app/uni-fab-login/univerify.png\",\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\t\"id\": \"taobao\",\n\t\t\t\t\t\t\"text\": \"淘宝登录\", //暂未提供该登录方式的接口示例\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/app/uni-fab-login/taobao.png\",\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\t\"id\": \"facebook\",\n\t\t\t\t\t\t\"text\": \"脸书登录\", //暂未提供该登录方式的接口示例\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/app/uni-fab-login/facebook.png\",\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\t\"id\": \"alipay\",\n\t\t\t\t\t\t\"text\": \"支付宝登录\", //暂未提供该登录方式的接口示例\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/app/uni-fab-login/alipay.png\",\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\t\"id\": \"qq\",\n\t\t\t\t\t\t\"text\": \"QQ登录\", //暂未提供该登录方式的接口示例\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/app/uni-fab-login/qq.png\",\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\t\"id\": \"google\",\n\t\t\t\t\t\t\"text\": \"谷歌登录\", //暂未提供该登录方式的接口示例\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/app/uni-fab-login/google.png\",\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\t\"id\": \"douyin\",\n\t\t\t\t\t\t\"text\": \"抖音登录\", //暂未提供该登录方式的接口示例\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/app/uni-fab-login/douyin.png\",\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\t\"id\": \"sinaweibo\",\n\t\t\t\t\t\t\"text\": \"新浪微博\", //暂未提供该登录方式的接口示例\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/app/uni-fab-login/sinaweibo.png\",\n\t\t\t\t\t}\n\t\t\t\t\t// #endif\n\t\t\t\t],\n\t\t\t\tuniverifyStyle: { //一键登录弹出窗的样式配置参数\n\t\t\t\t\t\"fullScreen\": true, // 是否全屏显示，true表示全屏模式，false表示非全屏模式，默认值为false。\n\t\t\t\t\t\"backgroundColor\": \"#ffffff\", // 授权页面背景颜色，默认值：#ffffff\n\t\t\t\t\t\"buttons\": { // 自定义登录按钮\n\t\t\t\t\t\t\"iconWidth\": \"45px\", // 图标宽度（高度等比例缩放） 默认值：45px\n\t\t\t\t\t\t\"list\": []\n\t\t\t\t\t},\n\t\t\t\t\t\"privacyTerms\": {\n\t\t\t\t\t\t\"defaultCheckBoxState\": false, // 条款勾选框初始状态 默认值： true\n\t\t\t\t\t\t\"textColor\": \"#BBBBBB\", // 文字颜色 默认值：#BBBBBB\n\t\t\t\t\t\t\"termsColor\": \"#5496E3\", //  协议文字颜色 默认值： #5496E3\n\t\t\t\t\t\t\"prefix\": \"我已阅读并同意\", // 条款前的文案 默认值：“我已阅读并同意”\n\t\t\t\t\t\t\"suffix\": \"并使用本机号码登录\", // 条款后的文案 默认值：“并使用本机号码登录”\n\t\t\t\t\t\t\"privacyItems\": []\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tagree(agree) {\n\t\t\t\tthis.univerifyStyle.privacyTerms.defaultCheckBoxState = agree\n\t\t\t}\n\t\t},\n\t\tasync created() {\n\t\t\tlet servicesList = this.servicesList\n\t\t\tlet loginTypes = config.loginTypes\n\n\t\t\tservicesList = servicesList.filter(item => {\n\n\t\t\t\t// #ifndef APP\n\t\t\t\t//非app端去掉apple登录\n\t\t\t\tif (item.id == 'apple') {\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t\t// #endif\n\n\t\t\t\t// #ifdef APP\n\t\t\t\t//去掉非ios系统上的apple登录\n\t\t\t\tif (item.id == 'apple' && uni.getSystemInfoSync().osName != 'ios') {\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t\t// #endif\n\n\t\t\t\treturn loginTypes.includes(item.id)\n\t\t\t})\n\t\t\t//处理一键登录\n\t\t\tif (loginTypes.includes('univerify')) {\n\t\t\t\tthis.univerifyStyle.privacyTerms.privacyItems = this.agreements\n\t\t\t\t//设置一键登录功能底下的快捷登录按钮\n\t\t\t\tservicesList.forEach(({\n\t\t\t\t\tid,\n\t\t\t\t\tlogo,\n\t\t\t\t\tpath\n\t\t\t\t}) => {\n\t\t\t\t\tif (id != 'univerify') {\n\t\t\t\t\t\tthis.univerifyStyle.buttons.list.push({\n\t\t\t\t\t\t\t\"iconPath\": logo,\n\t\t\t\t\t\t\t\"provider\": id,\n\t\t\t\t\t\t\tpath //路径用于点击快捷按钮时判断是跳转页面\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\n\n\t\t\t//去掉当前页面对应的登录选项\n\t\t\tthis.servicesList = servicesList.filter(item => {\n\t\t\t\tlet path = item.path ? item.path.split('?')[0] : '';\n\t\t\t\treturn path != this.getRoute(1)\n\t\t\t})\n\t\t},\n\t\tmethods: {\n\t\t\tgetParentComponent(){\n\t\t\t\t// #ifndef H5\n\t\t\t\treturn this.$parent;\n\t\t\t\t// #endif\n\n\t\t\t\t// #ifdef H5\n\t\t\t\treturn this.$parent.$parent;\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tsetUserInfo(e) {\n\n\t\t\t},\n\t\t\tgetRoute(n = 0) {\n\t\t\t\tlet pages = getCurrentPages();\n\t\t\t\tif (n > pages.length) {\n\t\t\t\t\treturn ''\n\t\t\t\t}\n\t\t\t\treturn '/' + pages[pages.length - n].route\n\t\t\t},\n\t\t\ttoPage(path,index = 0) {\n\n\t\t\t\tif (this.getRoute(1) == path.split('?')[0] && this.getRoute(1) ==\n\t\t\t\t\t'/uni_modules/uni-id-pages/pages/login/login-withoutpwd') {\n\t\t\t\t\t//如果要被打开的页面已经打开，且这个页面是 /uni_modules/uni-id-pages/pages/index/index 则把类型参数传给他\n\t\t\t\t\tlet loginType = path.split('?')[1].split('=')[1]\n\t\t\t\t\tuni.$emit('uni-id-pages-setLoginType', loginType)\n\t\t\t\t} else if (this.getRoute(2) == path) { // 如果上一个页面就是，马上要打开的页面，直接返回。防止重复开启\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t} else if (this.getRoute(1) != path) {\n\t\t\t\t\tif(index === 0){\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: path,\n\t\t\t\t\t\t\tanimationType: 'slide-in-left',\n\t\t\t\t\t\t\tcomplete(e) {\n\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}else{\n\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\turl: path,\n\t\t\t\t\t\t\tanimationType: 'slide-in-left',\n\t\t\t\t\t\t\tcomplete(e) {\n\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t} else {\n\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync login_before(type, navigateBack = true, options = {}) {\n\n\t\t\t\t//提示空实现\n\t\t\t\tif ([\"qq\",\n\t\t\t\t\t\t\"xiaomi\",\n\t\t\t\t\t\t\"sinaweibo\",\n\t\t\t\t\t\t\"taobao\",\n\t\t\t\t\t\t\"facebook\",\n\t\t\t\t\t\t\"google\",\n\t\t\t\t\t\t\"alipay\",\n\t\t\t\t\t\t\"douyin\",\n\t\t\t\t\t].includes(type)) {\n\t\t\t\t\treturn uni.showToast({\n\t\t\t\t\t\ttitle: '该登录方式暂未实现，欢迎提交pr',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t});\n\t\t\t\t}\n\n\n\t\t\t\t//检查当前环境是否支持这种登录方式\n\t\t\t\t// #ifdef APP\n\t\t\t\tlet isAppExist = true\n\t\t\t\tawait new Promise((callback) => {\n\n\t\t\t\t\tuni.getProvider({\n\t\t\t\t\t\tservice: 'oauth',\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tconst provider = res.providers.find(item => item.id === type)\n\n\t\t\t\t\t\t\tif (provider) {\n\t\t\t\t\t\t\t\tisAppExist = provider?.isAppExist ?? true\n\t\t\t\t\t\t\t\tcallback()\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\treturn uni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '当前设备不支持此登录，请选择其他登录方式',\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: () => {\n\t\t\t\t\t\t\tthrow new Error('获取服务供应商失败：' + JSON.stringify(err))\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t\t// #endif\n\n\t\t\t\tif (\n\t\t\t\t\t// #ifdef APP\n\t\t\t\t\t!isAppExist\n\t\t\t\t\t// #endif\n\n\t\t\t\t\t//非app端使用了，app特有登录方式\n\t\t\t\t\t// #ifndef APP\n\t\t\t\t\t[\"univerify\",\"apple\"].includes(type)\n\t\t\t\t\t// #endif\n\n\t\t\t\t) {\n\t\t\t\t\treturn uni.showToast({\n\t\t\t\t\t\ttitle: '当前设备不支持此登录，请选择其他登录方式',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t//判断是否需要弹出隐私协议授权框\n\t\t\t\tlet needAgreements = (config?.agreements?.scope || []).includes('register')\n\t\t\t\tif (type != 'univerify' && needAgreements && !this.agree) {\n\t\t\t\t\tlet agreementsRef = this.getParentComponent().$refs.agreements\n\t\t\t\t\treturn agreementsRef.popup(() => {\n\t\t\t\t\t\tthis.login_before(type, navigateBack, options)\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\t// #ifdef H5\n\t\t\t\t\tif(type == 'weixin'){\n\n\t\t\t\t\t\t// let redirectUrl = location.protocol +'//'+\n\t\t\t\t\t\t// \t\t\t\tdocument.domain +\n\t\t\t\t\t\t// \t\t\t\t(window.location.href.includes('#')?'/#':'') +\n\t\t\t\t\t\t// \t\t\t\t'/uni_modules/uni-id-pages/pages/login/login-withoutpwd?is_weixin_redirect=true&type=weixin'\n            // #ifdef VUE2\n            const baseUrl = process.env.BASE_URL\n            // #endif\n            // #ifdef VUE3\n            const baseUrl = import.meta.env.BASE_URL\n            // #endif\n\n            let redirectUrl = location.protocol +\n                '//' +\n                location.host +\n                baseUrl.replace(/\\/$/, '') +\n                (window.location.href.includes('#')?'/#':'') +\n                '/uni_modules/uni-id-pages/pages/login/login-withoutpwd?is_weixin_redirect=true&type=weixin'\n\n\n\t\t\t\t\t\tlet ua = window.navigator.userAgent.toLowerCase();\n\t\t\t\t\t\tif (ua.match(/MicroMessenger/i) == 'micromessenger'){\n\n\t\t\t\t\t\t\treturn window.open(`https://open.weixin.qq.com/connect/oauth2/authorize?\n\t\t\t\t\t\t\t\t\t\tappid=${config.appid.weixin.h5}\n\t\t\t\t\t\t\t\t\t\t&redirect_uri=${encodeURIComponent(redirectUrl)}\n\t\t\t\t\t\t\t\t\t\t&response_type=code\n\t\t\t\t\t\t\t\t\t\t&scope=snsapi_userinfo\n\t\t\t\t\t\t\t\t\t\t&state=STATE&connect_redirect=1#wechat_redirect`);\n\n\t\t\t\t\t\t}else{\n\n\t\t\t\t\t\t\treturn location.href = `https://open.weixin.qq.com/connect/qrconnect?appid=${config.appid.weixin.web}\n\t\t\t\t\t\t\t\t\t\t\t&redirect_uri=${encodeURIComponent(redirectUrl)}\n\t\t\t\t\t\t\t\t\t\t\t&response_type=code&scope=snsapi_login&state=STATE#wechat_redirect`\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t// #endif\n\n\t\t\t\tuni.showLoading({\n\t\t\t\t\t// #ifdef MP-HARMONY\n\t\t\t\t\ttitle: \"正在登录\",\n\t\t\t\t\t// #endif\n\t\t\t\t\tmask: true\n\t\t\t\t})\n\n\t\t\t\tif (type == 'univerify') {\n\t\t\t\t\tlet univerifyManager = uni.getUniverifyManager()\n\t\t\t\t\tlet clickAnotherButtons = false\n\t\t\t\t\tlet onButtonsClickFn = async res => {\n\n\t\t\t\t\t\tclickAnotherButtons = true\n\t\t\t\t\t\tlet checkBoxState = await uni.getCheckBoxState();\n\t\t\t\t\t\t// 同步一键登录弹出层隐私协议框是否打勾\n\t\t\t\t\t\t// #ifdef VUE2\n\t\t\t\t\t\tthis.agree = checkBoxState[1].state\n\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t// #ifdef VUE3\n\t\t\t\t\t\tthis.agree = checkBoxState.state\n\t\t\t\t\t\t// #endif\n\t\t\t\t\t\tlet {\n\t\t\t\t\t\t\tpath\n\t\t\t\t\t\t} = this.univerifyStyle.buttons.list[res.index]\n\t\t\t\t\t\tif (path) {\n\t\t\t\t\t\t\tif( this.getRoute(1).includes('login-withoutpwd') && path.includes('login-withoutpwd') ){\n\t\t\t\t\t\t\t\tthis.getParentComponent().showCurrentWebview()\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.toPage(path,1)\n\t\t\t\t\t\t\tcloseUniverify()\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (this.agree) {\n\t\t\t\t\t\t\t\tcloseUniverify()\n\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\tthis.login_before(res.provider)\n\t\t\t\t\t\t\t\t}, 500)\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: \"你未同意隐私政策协议\",\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tfunction closeUniverify() {\n\t\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\t\tuniverifyManager.close()\n\t\t\t\t\t\t// 取消订阅自定义按钮点击事件\n\t\t\t\t\t\tuniverifyManager.offButtonsClick(onButtonsClickFn)\n\t\t\t\t\t}\n\t\t\t\t\t// 订阅自定义按钮点击事件\n\t\t\t\t\tuniverifyManager.onButtonsClick(onButtonsClickFn)\n\t\t\t\t\t// 调用一键登录弹框\n\t\t\t\t\treturn univerifyManager.login({\n\t\t\t\t\t\t\"univerifyStyle\": this.univerifyStyle,\n\t\t\t\t\t\tsuccess: res => {\n\t\t\t\t\t\t\tthis.login(res.authResult, 'univerify')\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail(err) {\n\n\t\t\t\t\t\t\tif(!clickAnotherButtons){\n\t\t\t\t\t\t\t\tuni.navigateBack()\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// uni.showToast({\n\t\t\t\t\t\t\t// \ttitle: JSON.stringify(err),\n\t\t\t\t\t\t\t// \ticon: 'none',\n\t\t\t\t\t\t\t// \tduration: 3000\n\t\t\t\t\t\t\t// });\n\t\t\t\t\t\t},\n\t\t\t\t\t\tcomplete: async e => {\n\t\t\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\t\t\t//同步一键登录弹出层隐私协议框是否打勾\n\t\t\t\t\t\t\t// this.agree = (await uni.getCheckBoxState())[1].state\n\t\t\t\t\t\t\t// 取消订阅自定义按钮点击事件\n\t\t\t\t\t\t\tuniverifyManager.offButtonsClick(onButtonsClickFn)\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\n\t\t\t\tif (type === 'weixinMobile' || type === 'huaweiMobile') {\n\t\t\t\t\treturn this.login({\n\t\t\t\t\t\tphoneCode: options.phoneNumberCode\n\t\t\t\t\t}, type)\n\t\t\t\t}\n\n\t\t\t\tuni.login({\n\t\t\t\t\t\"provider\": type,\n\t\t\t\t\t\"onlyAuthorize\": true,\n\t\t\t\t\t// #ifdef APP\n\t\t\t\t\t\"univerifyStyle\": this.univerifyStyle,\n\t\t\t\t\t// #endif\n\t\t\t\t\tsuccess: async e => {\n\t\t\t\t\t\tif (type == 'apple') {\n\t\t\t\t\t\t\tlet res = await this.getUserInfo({\n\t\t\t\t\t\t\t\tprovider: \"apple\"\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\tObject.assign(e.authResult, res.userInfo)\n\t\t\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tthis.login(['huawei', 'weixin'].includes(type) ? {\n\t\t\t\t\t\t\tcode: e.code\n\t\t\t\t\t\t} : e.authResult, type)\n\t\t\t\t\t},\n\t\t\t\t\tfail: async (err) => {\n\t\t\t\t\t\tconsole.error(JSON.stringify(err));\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\tcontent: `登录失败; code: ${err.errCode || -1}`,\n\t\t\t\t\t\t\tconfirmText: \"知道了\",\n\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t});\n\t\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tlogin(params, type) { //联网验证登录\n\n\n\t\t\t\t//toLowerCase\n\t\t\t\tlet action = 'loginBy' + type.trim().replace(type[0], type[0].toUpperCase())\n\t\t\t\tconst uniIdCo = uniCloud.importObject(\"uni-id-co\",{\n\t\t\t\t\tcustomUI:true\n\t\t\t\t})\n\t\t\t\tuniIdCo[action](params).then(result => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '登录成功',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\t// #ifdef H5\n\t\t\t\t\tresult.loginType = type\n\t\t\t\t\t// #endif\n\t\t\t\t\tmutations.loginSuccess(result)\n\t\t\t\t})\n\t\t\t\t.catch(e=>{\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\tcontent: e.message,\n\t\t\t\t\t\tconfirmText:\"知道了\",\n\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t});\n\t\t\t\t})\n\t\t\t\t.finally(e => {\n\t\t\t\t\tif (type == 'univerify') {\n\t\t\t\t\t\tuni.closeAuthView()\n\t\t\t\t\t}\n\t\t\t\t\tuni.hideLoading()\n\t\t\t\t})\n\t\t\t},\n\t\t\tasync getUserInfo(e) {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\tuni.getUserInfo({\n\t\t\t\t\t\t...e,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tresolve(res);\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\tcontent: JSON.stringify(err),\n\t\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\treject(err);\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t/* #ifndef APP-NVUE */\n\t.fab-login-box,\n\t.item {\n\t\tdisplay: flex;\n\t\tbox-sizing: border-box;\n\t\tflex-direction: column;\n\t}\n\t/* #endif */\n\n\t.fab-login-box {\n\t\tflex-direction: row;\n\t\tflex-wrap: wrap;\n\t\twidth: 750rpx;\n\t\tjustify-content: space-around;\n\t\tposition: fixed;\n\t\tleft: 0;\n\t}\n\n\t.item {\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\theight: 200rpx;\n\t\tcursor: pointer;\n\t\t\n\t\t/* #ifdef MP-WEIXIN */\n\t\t/* 微信小程序中移除点击高亮效果 */\n\t\t-webkit-tap-highlight-color: transparent;\n\t\tbackground-color: transparent;\n\t\t/* #endif */\n\t}\n\n\t/* #ifndef APP-NVUE */\n\t@media screen and (min-width: 690px) {\n\t\t.fab-login-box {\n\t\t\tmax-width: 500px;\n\t\t\tmargin-left: calc(50% - 250px);\n\t\t}\n\t\t.item {\n\t\t\theight: 160rpx;\n\t\t}\n\t}\n\n\t@media screen and (max-width: 690px) {\n\t\t.fab-login-box {\n\t\t\tbottom: 10rpx;\n\t\t}\n\t}\n\n\t/* #endif */\n\n\t.logo {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tmax-width: 40px;\n\t\tmax-height: 40px;\n\t\tborder-radius: 100%;\n\t\tborder: solid 1px #F6F6F6;\n\t}\n\n\t.login-title {\n\t\ttext-align: center;\n\t\tmargin-top: 6px;\n\t\tcolor: #999;\n\t\tfont-size: 12px;\n\t\twidth: 80px;\n\t}\n</style>\n", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-id-pages-fab-login.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-id-pages-fab-login.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775853436\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}