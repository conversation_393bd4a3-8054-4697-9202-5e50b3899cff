{"id": "uni-id-pages", "displayName": "uni-id-pages", "version": "1.1.24", "description": "云端一体简单、统一、可扩展的用户中心页面模版", "keywords": ["用户管理", "用户中心", "短信验证码", "login", "登录"], "repository": "https://gitcode.net/dcloud/hello_uni-id-pages", "engines": {"HBuilderX": "^4.15", "uni-app": "^3.1.0", "uni-app-x": "^3.1.0"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "", "type": "unicloud-template-page", "darkmode": "-", "i18n": "-", "widescreen": "-"}, "uni_modules": {"dependencies": ["uni-captcha", "uni-config-center", "uni-data-checkbox", "uni-easyinput", "uni-forms", "uni-icons", "uni-id-common", "uni-list", "uni-load-more", "uni-popup", "uni-scss", "uni-transition", "uni-open-bridge-common", "uni-cloud-s2s"], "encrypt": [], "platforms": {"cloud": {"tcb": "√", "aliyun": "√", "alipay": "√"}, "client": {"uni-app": {"vue": {"vue2": "-", "vue3": "-"}, "web": {"safari": "-", "chrome": "-"}, "app": {"vue": "-", "nvue": "-", "android": "-", "ios": "-", "harmony": "-"}, "mp": {"weixin": "-", "alipay": "-", "toutiao": "-", "baidu": "-", "kuaishou": "-", "jd": "-", "harmony": "-", "qq": "-", "lark": "-"}, "quickapp": {"huawei": "-", "union": "-"}}, "uni-app-x": {"web": {"safari": "-", "chrome": "-"}, "app": {"android": "-", "ios": "-", "harmony": "-"}, "mp": {"weixin": "-"}}}}}, "dependencies": {}}