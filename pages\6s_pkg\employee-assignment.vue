<template>
  <view class="page-container">
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-title">员工责任区分配</view>
      <view class="header-subtitle">为员工分配固定责任区域</view>
    </view>

    <!-- 统计概览 -->
    <view class="overview-card">
      <view class="overview-title">分配概览</view>
      
      <!-- 加载状态 -->
      <view v-if="loading" class="stats-loading">
        <view class="loading-content">
          <uni-icons type="spinner-cycle" size="32" color="#007AFF"></uni-icons>
          <text class="loading-text">加载统计数据中...</text>
        </view>
      </view>
      
      <!-- 正常统计数据 -->
      <view v-else class="stats-grid">
        <view class="stat-item">
          <view class="stat-number">{{ totalEmployees }}</view>
          <view class="stat-label">总员工数</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{ assignedEmployees }}</view>
          <view class="stat-label">已分配</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{ totalAreas }}</view>
          <view class="stat-label">总责任区</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{ unassignedAreas }}</view>
          <view class="stat-label">未分配</view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-bar">
      <button class="action-btn primary" @click="showAssignModal">
        <uni-icons type="plus" size="16" color="#FFFFFF" />
        <text>新增分配</text>
      </button>
    </view>

    <!-- 分配列表 -->
    <!-- 加载状态 -->
    <view v-if="loading" class="list-loading">
      <view class="loading-content">
        <uni-icons type="spinner-cycle" size="40" color="#007AFF"></uni-icons>
        <text class="loading-text">加载分配数据中...</text>
      </view>
    </view>
    
    <!-- 正常分配列表 -->
    <view v-else-if="assignmentList.length > 0" class="assignment-list">
      <view 
        class="assignment-card-modern"
        v-for="(assignment, index) in assignmentList" 
        :key="index"
      >
        <!-- 移除左侧装饰条，统一为极简卡片 -->
        <!-- no accent bar -->
        
        <!-- 主要内容区域 -->
        <view class="card-content">
          <!-- 顶部员工信息行 -->
          <view class="employee-header">
            <view class="employee-name-section">
              <view class="employee-icon-wrapper">
                <uni-icons type="person-filled" color="#FFFFFF" size="18"></uni-icons>
              </view>
              <text class="employee-name-large">{{ assignment.employee.name }}</text>
              <view class="employee-role-chip">{{ assignment.employee.role || '员工' }}</view>
            </view>
            <view class="assignment-id">ID: {{ String(index + 1).padStart(3, '0') }}</view>
          </view>
          
          <!-- 区域信息展示 -->
          <view class="areas-section">
            <view class="section-header">
              <view class="section-title-row">
                <view class="section-title">责任区域</view>
                <view class="area-count-badge">{{ assignment.areas.length }}</view>
              </view>
              <view class="assignment-status-modern" :class="[`status-${assignment.status}`]">
                {{ getStatusText(assignment.status) }}
              </view>
            </view>
            
            <view class="areas-grid">
              <view 
                class="area-tag-modern"
                v-for="(area, areaIndex) in assignment.areas" 
                :key="areaIndex"
                @click="viewAreaDetail(area)"
              >
                <text class="area-name">{{ area.name }}</text>
              </view>
            </view>
          </view>
          
          <!-- 时间信息和操作 -->
          <view class="card-footer">
            <view class="time-info">
              <template v-if="assignment.updated_at && assignment.updated_at !== assignment.assigned_at">
                <text class="time-label">更新于:</text>
                <text class="time-value">{{ formatDate(assignment.updated_at) }}</text>
              </template>
              <template v-else>
                <text class="time-label">分配于:</text>
                <text class="time-value">{{ formatDate(assignment.assigned_at) }}</text>
              </template>
            </view>
            
            <view class="action-buttons">
              <view class="action-btn edit-btn" @click="editAssignment(assignment)" role="button">
                <uni-icons type="compose" size="16" color="#007AFF" />
                <text class="btn-text">编辑</text>
              </view>
              <view class="action-btn delete-btn" @click="deleteAssignment(assignment)" role="button">
                <uni-icons type="trash" size="16" color="#FF3B30" />
                <text class="btn-text">删除</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <p-empty-state
      v-else
      type="assignment"
      text="暂无分配记录"
      description="点击上方按钮开始分配责任区"
    ></p-empty-state>

    <!-- 分配弹窗 -->
    <uni-popup ref="assignPopup" type="center" :mask-click="false">
      <view class="assign-popup">
        <view class="popup-header">
          <view class="popup-title">{{ isEditing ? '编辑' : '新增' }}分配</view>
          <button class="close-btn" @click="closeAssignModal">
            <uni-icons type="close" size="20" color="#8E8E93" />
          </button>
        </view>
        
        <view class="popup-content">
          <!-- 员工选择 -->
          <view class="form-section">
            <view class="section-title">选择员工</view>
            <view class="employee-selector" @click="showEmployeeSelect">
              <view class="selected-employee" v-if="selectedEmployee">
                <image 
                  :src="selectedEmployee.avatar || '/static/user/default-avatar.png'" 
                  class="selected-avatar" 
                  mode="aspectFill"
                />
                <view class="selected-info">
                  <text class="selected-name">{{ selectedEmployee.name }}</text>
                  <text class="selected-role">{{ selectedEmployee.role || '员工' }}</text>
                </view>
              </view>
              <view class="placeholder" v-else>
                <uni-icons type="person-filled" size="20" color="#C7C7CC" />
                <text>点击选择员工</text>
              </view>
              <uni-icons type="right" size="16" color="#C7C7CC" />
            </view>
          </view>
          
          <!-- 责任区选择 -->
          <view class="form-section">
            <view class="section-title">分配责任区</view>
            <view class="areas-selector">
              <view 
                class="area-option"
                v-for="(area, areaIndex) in availableAreas" 
                :key="areaIndex"
                @click="toggleAreaSelection(area)"
                :class="{ 'selected': isAreaSelected(area) }"
              >
                <view class="area-option-main">
                  <view class="area-option-name">{{ area.name }}</view>
                  <view class="area-option-location">{{ (area.location && area.location.area) || area.location || '未设置位置' }}</view>
                </view>
                <view class="area-option-check" v-if="isAreaSelected(area)">
                  <uni-icons type="checkmarkempty" size="18" color="#007AFF" />
                </view>
              </view>
            </view>
          </view>
          
          <!-- 分配说明 -->
          <view class="form-section">
            <view class="section-title">分配说明</view>
            <textarea 
              class="assign-note" 
              v-model="assignmentNote"
              placeholder="请输入分配说明（可选）"
              maxlength="200"
            ></textarea>
          </view>
        </view>
        
        <view class="popup-footer">
          <button class="popup-btn cancel" @click="closeAssignModal">取消</button>
          <button class="popup-btn submit" @click="submitAssignment" :loading="saving">
            {{ isEditing ? '保存' : '分配' }}
          </button>
        </view>
      </view>
    </uni-popup>

    <!-- 员工选择弹窗 -->
    <uni-popup ref="employeeSelectPopup" type="center">
      <view class="employee-select-popup">
        <view class="select-header">
          <text class="select-title">选择员工</text>
          <button class="close-btn" @click="closeEmployeeSelect">
            <uni-icons type="close" size="20" color="#8E8E93" />
          </button>
        </view>
        <view class="search-bar">
          <input 
            class="search-input" 
            type="text" 
            placeholder="搜索员工姓名" 
            v-model="searchKeyword"
            @input="filterEmployees"
          />
          <uni-icons type="search" size="18" color="#8E8E93" />
        </view>
        <scroll-view scroll-y class="employee-list">
          <view 
            class="employee-option"
            v-for="(employee, empIndex) in filteredEmployees" 
            :key="empIndex"
            @click="selectEmployee(employee)"
            :class="{ 'selected': selectedEmployee && selectedEmployee.id === employee.id }"
          >
            <image 
              :src="employee.avatar || '/static/user/default-avatar.png'" 
              class="option-avatar" 
              mode="aspectFill"
            />
            <view class="option-info">
              <text class="option-name">{{ employee.name }}</text>
              <text class="option-role">{{ employee.role || '员工' }}</text>
            </view>
            <view class="option-check" v-if="selectedEmployee && selectedEmployee.id === employee.id">
              <uni-icons type="checkmarkempty" size="18" color="#007AFF" />
            </view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { callCloudFunction } from '@/utils/auth.js';
// 使用统一的认证工具，不需要额外导入

export default {
  name: 'EmployeeAssignment',
  data() {
    return {
      assignmentList: [],
      employeeList: [],
      areaList: [],
      loading: false,
      saving: false,
      isEditing: false,
      currentAssignment: null,
      selectedEmployee: null,
      selectedAreas: [],
      assignmentNote: '',
      searchKeyword: '',
      filteredEmployees: []
    }
  },
  
  computed: {
    totalEmployees() {
      return this.employeeList.length;
    },
    assignedEmployees() {
      return this.assignmentList.length;
    },
    totalAreas() {
      return this.areaList.length;
    },
    unassignedAreas() {
      // 确保数据都加载完成后再计算
      if (this.loading || this.areaList.length === 0) {
        return 0;
      }
      
      const assignedAreaIds = this.assignmentList.reduce((acc, assignment) => {
        assignment.areas.forEach(area => {
          // 兼容 _id 和 id 两种格式
          const areaId = area._id || area.id;
          acc.add(areaId);
        });
        return acc;
      }, new Set());
      
      const unassignedCount = this.areaList.filter(area => {
        const areaId = area._id || area.id;
        return !assignedAreaIds.has(areaId);
      }).length;
      
      return unassignedCount;
    },
    availableAreas() {
      if (this.isEditing && this.currentAssignment) {
        // 编辑模式：显示当前分配的区域 + 未分配的区域
        const currentAreaIds = new Set(this.currentAssignment.areas.map(area => area.id));
        const assignedAreaIds = this.assignmentList
          .filter(assignment => assignment.id !== this.currentAssignment.id)
          .reduce((acc, assignment) => {
            assignment.areas.forEach(area => acc.add(area.id));
            return acc;
          }, new Set());
        
        return this.areaList.filter(area => 
          currentAreaIds.has(area.id) || !assignedAreaIds.has(area.id)
        );
      } else {
        // 新增模式：只显示未分配的区域
        const assignedAreaIds = this.assignmentList.reduce((acc, assignment) => {
          assignment.areas.forEach(area => acc.add(area.id));
          return acc;
        }, new Set());
        return this.areaList.filter(area => !assignedAreaIds.has(area.id));
      }
    }
  },
  
  onLoad() {
    this.loadData();
  },
  
  methods: {
    // 加载数据
    async loadData() {
      try {
        this.loading = true;
        await Promise.all([
          this.loadEmployees(),
          this.loadAreas(),
          this.loadAssignments()
        ]);
      } catch (error) {
        console.error('加载数据失败：', error);
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    
    // 加载员工列表
    async loadEmployees() {
      try {
        // 使用6S模块自己的云函数获取员工列表
        const result = await callCloudFunction('hygiene-assignments', {
          action: 'getEmployeeList',
          data: {
            pageSize: 200
            // 不传status参数，这样云函数就不会添加状态过滤条件
          }
        });

        this.employeeList = result.data.list || [];
        this.filteredEmployees = [...this.employeeList];
      } catch (error) {
        console.error('加载员工数据失败：', error);
        this.employeeList = [];
        this.filteredEmployees = [];
      }
    },
    
    // 获取当前用户ID
    getCurrentUserId() {
      try {
        // 尝试从本地存储获取用户信息
        const userInfo = uni.getStorageSync('uni-id-pages-userInfo');
        if (userInfo) {
          return userInfo._id || '';
        }
        return '';
      } catch (e) {
        return '';
      }
    },
    
    // 转换角色为显示文本（与用户中心保持一致）
    convertRoleToDisplay(role) {
      const roleNames = {
        'admin': '管理员',
        'responsible': '负责人',
        'reviser': '发布人',
        'supervisor': '主管',
        'PM': '副厂长',
        'GM': '厂长',
        'logistics': '后勤员',
        'dispatch': '调度员',
        'Integrated': '综合员',
        'operator': '设备员',
        'technician': '工艺员',
        'mechanic': '技术员',
        'user': '普通员工'
      };
      
      if (Array.isArray(role)) {
        return role.map(r => roleNames[r] || r).join('、');
      }
      
      return roleNames[role] || role || '普通员工';
    },
    

    
    // 加载责任区列表
    async loadAreas() {
      try {
        // 使用认证工具调用云函数获取固定责任区列表
        const result = await callCloudFunction('hygiene-area-management', {
          action: 'getAreaList',
          data: {
            type: 'fixed' // 只获取固定责任区
          }
        });

        this.areaList = (result.data.list || []).filter(area => area.status === 'active');
      } catch (error) {
        console.error('加载责任区数据失败：', error);
        this.areaList = [];
      }
    },
    
    // 加载分配记录
    async loadAssignments() {
      try {
        // 使用认证工具调用云函数获取分配记录
        const result = await callCloudFunction('hygiene-assignments', {
          action: 'getAssignmentList',
          data: {}
        });

        // 按创建时间正序排列，确保最早创建的记录编号最小
        this.assignmentList = (result.data || []).sort((a, b) => {
          const dateA = new Date(a.created_at || a.assigned_at || 0);
          const dateB = new Date(b.created_at || b.assigned_at || 0);
          return dateA - dateB; // 升序排列
        });
      } catch (error) {
        console.error('加载分配记录失败：', error);
        this.assignmentList = [];
      }
    },
    
    // 显示分配弹窗
    showAssignModal() {
      this.isEditing = false;
      this.currentAssignment = null;
      this.resetForm();
      this.$refs.assignPopup.open();
    },
    
    // 编辑分配
    editAssignment(assignment) {
      this.isEditing = true;
      this.currentAssignment = assignment;
      this.selectedEmployee = assignment.employee;
      this.selectedAreas = [...assignment.areas];
      this.assignmentNote = assignment.note || '';
      this.$refs.assignPopup.open();
    },
    
    // 删除分配
    deleteAssignment(assignment) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除 ${assignment.employee.name} 的责任区分配吗？`,
        confirmText: '删除',
        confirmColor: '#FF3B30',
        success: (res) => {
          if (res.confirm) {
            this.performDeleteAssignment(assignment);
          }
        }
      });
    },
    
    // 执行删除分配
    async performDeleteAssignment(assignment) {
      try {
        // 使用认证工具调用云函数删除分配记录
        await callCloudFunction('hygiene-assignments', {
          action: 'deleteAssignment',
          data: {
            id: assignment._id || assignment.id
          }
        });

        // 从本地列表中移除
        const index = this.assignmentList.findIndex(item => (item._id || item.id) === (assignment._id || assignment.id));
        if (index > -1) {
          this.assignmentList.splice(index, 1);
        }
        
        // 重新加载责任区数据以更新分配状态
        await this.loadAreas();
        
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('删除失败：', error);
        uni.showToast({
          title: error.message || '删除失败',
          icon: 'none',
          duration: 3000
        });
      }
    },
    

    
    // 显示员工选择
    showEmployeeSelect() {
      this.$refs.employeeSelectPopup.open();
    },
    
    // 关闭员工选择
    closeEmployeeSelect() {
      this.$refs.employeeSelectPopup.close();
    },
    
    // 选择员工
    selectEmployee(employee) {
      this.selectedEmployee = employee;
      this.closeEmployeeSelect();
    },
    
    // 过滤员工
    filterEmployees() {
      const keyword = this.searchKeyword.toLowerCase();
      if (!keyword) {
        this.filteredEmployees = [...this.employeeList];
      } else {
        this.filteredEmployees = this.employeeList.filter(emp => 
          emp.name.toLowerCase().includes(keyword) ||
          (emp.role && emp.role.toLowerCase().includes(keyword))
        );
      }
    },
    
    // 切换责任区选择
    toggleAreaSelection(area) {
      const areaId = area._id || area.id;
      const index = this.selectedAreas.findIndex(a => (a._id || a.id) === areaId);
      if (index > -1) {
        this.selectedAreas.splice(index, 1);
      } else {
        this.selectedAreas.push(area);
      }
    },
    
    // 检查责任区是否已选择
    isAreaSelected(area) {
      const areaId = area._id || area.id;
      return this.selectedAreas.some(a => (a._id || a.id) === areaId);
    },
    
    // 提交分配
    async submitAssignment() {
      if (!this.validateAssignment()) {
        return;
      }
      
      try {
        this.saving = true;
        
        // 使用认证工具处理，无需手动检查token

        const assignmentData = {
          employee_id: this.selectedEmployee.id,
          employee_name: this.selectedEmployee.name,
          employee_info: {
            name: this.selectedEmployee.name,
            role: this.selectedEmployee.role,
            avatar: this.selectedEmployee.avatar,
            phone: this.selectedEmployee.phone
          },
          area_ids: this.selectedAreas.map(area => area._id || area.id),
          area_names: this.selectedAreas.map(area => area.name),
          areas_info: this.selectedAreas.map(area => ({
            id: area._id || area.id,
            name: area.name,
            location: area.location,
            description: area.description
          })),
          note: this.assignmentNote,
          status: 'active'
        };

        let result;
        if (this.isEditing) {
          // 编辑模式
          result = await callCloudFunction('hygiene-assignments', {
            action: 'updateAssignment',
            data: {
              id: this.currentAssignment._id || this.currentAssignment.id,
              ...assignmentData
            }
          });
        } else {
          // 新增模式
          result = await callCloudFunction('hygiene-assignments', {
            action: 'createAssignment',
            data: assignmentData
          });
        }

        if (this.isEditing) {
          // 更新本地列表中的数据
          const index = this.assignmentList.findIndex(item => (item._id || item.id) === (this.currentAssignment._id || this.currentAssignment.id));
          if (index > -1) {
            this.assignmentList[index] = {
              ...(result.data || {}),
              employee: result.data?.employee_info || this.selectedEmployee,
              areas: result.data?.areas_info || this.selectedAreas
            };
          }
        } else {
          // 添加新数据到列表末尾（因为新记录创建时间最晚）
          const newAssignment = {
            ...(result.data || {}),
            employee: result.data?.employee_info || this.selectedEmployee,
            areas: result.data?.areas_info || this.selectedAreas,
            created_at: result.data?.created_at || new Date().toISOString() // 确保有创建时间
          };
          this.assignmentList.push(newAssignment);
        }
        
        // 重新加载责任区数据以更新分配状态
        await this.loadAreas();
        
        uni.showToast({
          title: this.isEditing ? '保存成功' : '分配成功',
          icon: 'success'
        });
        
        this.closeAssignModal();
        
      } catch (error) {
        console.error('保存失败：', error);
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'none',
          duration: 3000
        });
      } finally {
        this.saving = false;
      }
    },
    
    // 验证分配
    validateAssignment() {
      if (!this.selectedEmployee) {
        uni.showToast({
          title: '请选择员工',
          icon: 'none'
        });
        return false;
      }
      
      if (this.selectedAreas.length === 0) {
        uni.showToast({
          title: '请选择至少一个责任区',
          icon: 'none'
        });
        return false;
      }
      
      // 检查员工是否已有分配（编辑模式下排除当前记录）
      const existingAssignment = this.assignmentList.find(assignment => 
        assignment.employee.id === this.selectedEmployee.id &&
        (!this.isEditing || assignment.id !== this.currentAssignment.id)
      );
      
      if (existingAssignment) {
        uni.showToast({
          title: '该员工已有责任区分配',
          icon: 'none'
        });
        return false;
      }
      
      return true;
    },
    
    // 重置表单
    resetForm() {
      this.selectedEmployee = null;
      this.selectedAreas = [];
      this.assignmentNote = '';
      this.searchKeyword = '';
      this.filteredEmployees = [...this.employeeList];
    },
    
    // 关闭分配弹窗
    closeAssignModal() {
      this.$refs.assignPopup.close();
      this.resetForm();
    },
    
    // 查看责任区详情
    viewAreaDetail(area) {
      uni.showModal({
        title: area.name,
        content: `位置：${(area.location && area.location.area) || area.location || '未设置'}\n描述：${area.description || '无'}`,
        showCancel: false
      });
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'active': '正常',
        'inactive': '暂停',
        'expired': '过期'
      };
      return statusMap[status] || '未知';
    },
    
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: #F8F9FA;
  padding-bottom: 40rpx;
}

.header {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  padding: 60rpx 32rpx 40rpx;
  color: white;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 24rpx;
  opacity: 0.8;
}

.overview-card {
  background: white;
  margin: 24rpx 32rpx;
  padding: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.overview-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 24rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.stat-item {
  text-align: center;
  padding: 24rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 600;
  color: #007AFF;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #8E8E93;
}

.action-bar {
  padding: 0 32rpx 24rpx;
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 26rpx;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: #007AFF;
  color: white;
}

.action-btn.secondary {
  background: white;
  color: #007AFF;
  border: 2rpx solid #007AFF;
}

.action-btn:active {
  transform: scale(0.95);
}

.assignment-list {
  padding: 0 32rpx;
}

/* 统一为极简风格卡片 */
.assignment-card-modern {
  background: #FFFFFF;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

/* 移除装饰条 */
.card-accent-bar { display: none; }

/* 内容区域边距与 index 风格统一 */
.card-content {
  padding: 24rpx 24rpx 12rpx;
}

/* 顶部员工信息 */
.employee-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
}

.employee-name-section {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.employee-icon-wrapper {
  width: 48rpx;
  height: 48rpx;
  background-color: #007AFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.employee-name-large {
  font-size: 32rpx;
  font-weight: 700;
  color: #1D1D1F;
  line-height: 1;
  display: flex;
  align-items: center;
}

.employee-role-chip {
  padding: 6rpx 12rpx;
  background: rgba(0, 122, 255, 0.12);
  color: #007AFF;
  font-size: 20rpx;
  font-weight: 600;
  border-radius: 12rpx;
}

.assignment-id {
  font-size: 22rpx;
  font-weight: 600;
  color: #8E8E93;
  background: #F5F5F7;
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
}

/* 区域与状态 */
.areas-section { margin-bottom: 12rpx; }

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.section-title-row { display: flex; align-items: baseline; gap: 10rpx; }

.section-title { font-size: 28rpx; font-weight: 600; color: #1D1D1F; }

.area-count-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 149, 0, 0.12);
  color: #FF9500;
  font-size: 18rpx;
  font-weight: 700;
  padding: 4rpx 10rpx;
  border-radius: 16rpx;
  min-width: 28rpx;
}

.assignment-status-modern {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 600;
}

.assignment-status-modern.status-active {
  background: rgba(52, 199, 89, 0.12);
  color: #34C759;
}

.assignment-status-modern.status-inactive {
  background: rgba(142, 142, 147, 0.12);
  color: #8E8E93;
}

.assignment-status-modern.status-expired {
  background: rgba(255, 59, 48, 0.12);
  color: #FF3B30;
}

/* 区域标签栅格 */
.areas-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.area-tag-modern {
  background: #F8FAFC;
  border: 1rpx solid #E5E7EB;
  padding: 10rpx 16rpx;
  border-radius: 12rpx;
  transition: background 0.2s ease;
}

.area-name {
  font-size: 24rpx;
  font-weight: 500;
  color: #1D1D1F;
}

/* 底部信息与操作 */
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
}

.time-info { 
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.time-label { 
  font-size: 22rpx; 
  color: #8E8E93; 
}

.time-value { 
  font-size: 22rpx;
  font-weight: 500;
  color: #1D1D1F;
}

.action-buttons { display: flex; gap: 12rpx; }

/* 统一按钮为极简描边/文本按钮风格（仅作用于卡片内按钮） */
.assignment-card-modern .action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 2rpx 12rpx;
  border-radius: 8rpx;
  border: none;
  background-color: transparent;
  font-size: 22rpx;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.assignment-card-modern .edit-btn { color: #007AFF; }
.assignment-card-modern .delete-btn { color: #FF3B30; }

.assignment-card-modern .action-btn:active { background-color: #F8F9FA; }

.assign-popup {
  width: 92vw;
  max-width: 650rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-left: 5rpx;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
    background: #F2F2F7;
  border: none;
  margin-right: 5rpx;
}

.popup-content {
  flex: 1;
  overflow-y: auto;
  padding: 32rpx;
}

/* #ifdef MP-WEIXIN */
/* 微信小程序隐藏滚动条 */
.popup-content::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  background: transparent;
}

.popup-content {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
/* #endif */

/* #ifndef MP-WEIXIN */
/* 非微信小程序环境下也隐藏滚动条 */
.popup-content::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  background: transparent;
}

.popup-content {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
/* #endif */

.form-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 16rpx;
}

.employee-selector {
  background: #F2F2F7;
  border-radius: 12rpx;
  padding: 16rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.selected-employee {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
}

.selected-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
}

.selected-info {
  flex: 1;
}

.selected-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #1C1C1E;
  display: block;
  margin-bottom: 4rpx;
}

.selected-role {
  font-size: 22rpx;
  color: #8E8E93;
}

.placeholder {
  display: flex;
  align-items: center;
  gap: 12rpx;
  color: #C7C7CC;
  font-size: 26rpx;
  flex: 1;
}

.areas-selector {
  max-height: 300rpx;
  overflow-y: auto;
}

/* 隐藏责任区选择器滚动条 */
.areas-selector::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  background: transparent;
}

.areas-selector {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.area-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx;
  border: 2rpx solid #F2F2F7;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
  transition: all 0.3s ease;
}

.area-option.selected {
  border-color: #007AFF;
  background: rgba(0, 122, 255, 0.05);
}

.area-option-main {
  flex: 1;
}

.area-option-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 4rpx;
}

.area-option-location {
  font-size: 22rpx;
  color: #8E8E93;
}

.area-option-check {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.assign-note {
  width: 100%;
  background: #F2F2F7;
  border: none;
  border-radius: 12rpx;
  padding: 16rpx;
  font-size: 26rpx;
  color: #1C1C1E;
  min-height: 120rpx;
  box-sizing: border-box;
}

.popup-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #F2F2F7;
}

.popup-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  border: none;
  transition: all 0.3s ease;
}

.popup-btn.cancel {
  background: #F2F2F7;
  color: #8E8E93;
}

.popup-btn.submit {
  background: #007AFF;
  color: white;
}

.popup-btn:active {
  transform: scale(0.95);
}

.employee-select-popup {
  width: 92vw;
  max-width: 550rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.select-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
}

.select-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-left: 5rpx;
}

.search-bar {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background: #F8F9FA;
  border-bottom: 1rpx solid #F2F2F7;
}

.search-input {
  flex: 1;
  height: 60rpx;
  font-size: 26rpx;
  color: #1C1C1E;
}

.employee-list {
  flex: 1;
  overflow-y: auto;
}

/* #ifdef MP-WEIXIN */
/* 微信小程序隐藏滚动条 */
.employee-list::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  background: transparent;
}

.employee-list {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
/* #endif */

/* #ifndef MP-WEIXIN */
/* 非微信小程序环境下也隐藏滚动条 */
.employee-list::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  background: transparent;
}

.employee-list {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
/* #endif */

.employee-option {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 32rpx;
  border-bottom: 1rpx solid #F2F2F7;
  transition: all 0.3s ease;
}

.employee-option.selected {
  background: rgba(0, 122, 255, 0.05);
}

.employee-option:active {
  background: #F8F9FA;
}

.option-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: 2rpx solid #F2F2F7;
}

.option-info {
  flex: 1;
}

.option-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #1C1C1E;
  display: block;
  margin-bottom: 4rpx;
}

.option-role {
  font-size: 22rpx;
  color: #8E8E93;
}

.option-check {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 加载状态样式 */
.stats-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}

.list-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120rpx 32rpx;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #8E8E93;
  text-align: center;
}
</style> 