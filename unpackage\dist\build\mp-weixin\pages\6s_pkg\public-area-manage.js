require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/public-area-manage"],{"0cab":function(e,t,a){"use strict";var n=a("e442"),r=a.n(n);r.a},"12cd":function(e,t,a){"use strict";(function(e){var n=a("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("7eb4")),i=n(a("7ca3")),u=n(a("ee10")),s=a("882c");function l(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function c(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?l(Object(a),!0).forEach((function(t){(0,i.default)(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):l(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}var d={name:"PublicAreaManage",data:function(){return{areaList:[],loading:!1,saving:!1,scheduleSaving:!1,isEditing:!1,currentArea:null,currentScheduleArea:null,formData:{name:"",location:"",description:"",status:"active"},scheduleData:{scheduled_day:null,start_date:""},statusOptions:[{value:"active",label:"启用"},{value:"inactive",label:"禁用"}],weekDayOptions:[{value:null,label:"请选择清扫日期"},{value:1,label:"每周一"},{value:2,label:"每周二"},{value:3,label:"每周三"},{value:4,label:"每周四"},{value:5,label:"每周五"},{value:6,label:"每周六"},{value:0,label:"每周日"}],statusIndex:0,scheduleWeekIndex:0}},computed:{enabledAreas:function(){return this.areaList.filter((function(e){return"active"===e.status}))},scheduledAreas:function(){return this.areaList.filter((function(e){return null!==e.scheduled_day}))},todayAreas:function(){var e=new Date,t=e.getDay();return this.areaList.filter((function(e){return e.scheduled_day===t}))}},onLoad:function(){this.loadAreaList()},methods:{loadAreaList:function(){var t=this;return(0,u.default)(r.default.mark((function a(){var n;return r.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,t.loading=!0,a.next=4,(0,s.callCloudFunction)("hygiene-area-management",{action:"getAreaList",data:{type:"public"}});case 4:n=a.sent,t.areaList=(n.data.list||[]).map((function(e){return c(c({},e),{},{scheduled_day:null!==e.scheduled_day&&void 0!==e.scheduled_day?Number(e.scheduled_day):null,next_clean_date:t.calculateNextCleanDate(c(c({},e),{},{scheduled_day:null!==e.scheduled_day&&void 0!==e.scheduled_day?Number(e.scheduled_day):null}))})})),a.next=12;break;case 8:a.prev=8,a.t0=a["catch"](0),console.error("加载公共责任区列表失败：",a.t0),a.t0.message.includes("未登录")||a.t0.message.includes("登录")||e.showToast({title:a.t0.message||"加载失败",icon:"none",duration:3e3});case 12:return a.prev=12,t.loading=!1,a.finish(12);case 15:case"end":return a.stop()}}),a,null,[[0,8,12,15]])})))()},createDefaultAreas:function(){var e=this,t=[{id:"public_001",name:"主入口大厅",location:{area:"办公楼1层"},description:"包括接待台、休息区、展示区域",status:"active",type:"public",scheduled_day:1,start_date:"2024-01-15",last_clean_date:"2024-01-15",created_at:new Date("2024-01-10").toISOString(),updated_at:(new Date).toISOString()},{id:"public_002",name:"员工餐厅",location:{area:"办公楼2层"},description:"用餐区域、后厨清洁区域",status:"active",type:"public",scheduled_day:3,start_date:"2024-01-17",last_clean_date:"2024-01-17",created_at:new Date("2024-01-12").toISOString(),updated_at:(new Date).toISOString()},{id:"public_003",name:"停车场区域",location:{area:"厂区东侧"},description:"室外停车区域和通道",status:"active",type:"public",scheduled_day:5,start_date:"2024-01-19",last_clean_date:null,created_at:new Date("2024-01-14").toISOString(),updated_at:(new Date).toISOString()},{id:"public_004",name:"会议室区域",location:{area:"办公楼3层"},description:"大中小会议室及茶水间",status:"active",type:"public",scheduled_day:null,start_date:null,last_clean_date:null,created_at:new Date("2024-01-16").toISOString(),updated_at:(new Date).toISOString()}];return t.map((function(t){return c(c({},t),{},{next_clean_date:e.calculateNextCleanDate(t)})}))},calculateNextCleanDate:function(e){if(null===e.scheduled_day||!e.start_date)return null;var t=new Date,a=e.scheduled_day,n=new Date(t);while(n.getDay()!==a)n.setDate(n.getDate()+1);return n.getDay()===a&&n.toDateString()===t.toDateString()&&t.getHours()>=12&&n.setDate(n.getDate()+7),n.toISOString().split("T")[0]},showAddArea:function(){this.isEditing=!1,this.currentArea=null,this.resetForm(),this.$refs.areaFormPopup.open()},editArea:function(e){this.isEditing=!0,this.currentArea=e,this.formData={name:e.name||"",location:e.location&&e.location.area||e.location||"",description:e.description||"",status:e.status||"active"},this.statusIndex=this.statusOptions.findIndex((function(t){return t.value===e.status}))||0,this.$refs.areaFormPopup.open()},deleteArea:function(t){var a=this;e.showModal({title:"确认删除",content:'确定要删除公共责任区"'.concat(t.name,'"吗？删除后不可恢复。'),confirmText:"删除",confirmColor:"#FF3B30",success:function(e){e.confirm&&a.performDelete(t)}})},performDelete:function(t){var a=this;return(0,u.default)(r.default.mark((function n(){var i,u;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,(0,s.callCloudFunction)("hygiene-area-management",{action:"deleteArea",data:{id:t._id||t.id}});case 3:i=a.areaList.findIndex((function(e){return(e._id||e.id)===(t._id||t.id)})),i>-1&&a.areaList.splice(i,1),e.showToast({title:"删除成功",icon:"success"}),n.next=12;break;case 8:n.prev=8,n.t0=n["catch"](0),console.error("删除失败：",n.t0),n.t0.message.includes("未登录")||n.t0.message.includes("登录")||(u=n.t0.message||"删除失败",u.includes("权限不足")?e.showModal({title:"权限不足",content:u,showCancel:!1,confirmText:"我知道了"}):e.showToast({title:u,icon:"none",duration:3e3,mask:!0}));case 12:case"end":return n.stop()}}),n,null,[[0,8]])})))()},setSchedule:function(e){this.currentScheduleArea=e;var t=e.scheduled_day;null!==t&&void 0!==t&&(t=Number(t),(isNaN(t)||t<0||t>6)&&(t=null)),this.scheduleData={scheduled_day:t,start_date:e.start_date||this.getNextMonday()};var a=this.weekDayOptions.findIndex((function(e){return e.value===t}));this.scheduleWeekIndex=a>=0?a:0,this.$refs.schedulePopup.open()},getNextMonday:function(){var e=new Date,t=new Date(e),a=(8-e.getDay())%7;return t.setDate(e.getDate()+(0===a?7:a)),t.toISOString().split("T")[0]},onStatusChange:function(e){this.statusIndex=e.detail.value,this.formData.status=this.statusOptions[this.statusIndex].value},onScheduleWeekChange:function(e){this.scheduleWeekIndex=parseInt(e.detail.value);var t=this.weekDayOptions[this.scheduleWeekIndex];t?this.scheduleData.scheduled_day=t.value:(this.scheduleData.scheduled_day=null,this.scheduleWeekIndex=0)},onStartDateChange:function(e){this.scheduleData.start_date=e.detail.value},submitForm:function(){var t=this;return(0,u.default)(r.default.mark((function a(){var n,i,u,l,d;return r.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t.validateForm()){a.next=2;break}return a.abrupt("return");case 2:if(a.prev=2,t.saving=!0,n={name:t.formData.name,location:{area:t.formData.location},description:t.formData.description,status:t.formData.status,type:"public"},!t.isEditing){a.next=11;break}return a.next=8,(0,s.callCloudFunction)("hygiene-area-management",{action:"updateArea",data:c({id:t.currentArea._id||t.currentArea.id},n)});case 8:i=a.sent,a.next=14;break;case 11:return a.next=13,(0,s.callCloudFunction)("hygiene-area-management",{action:"createArea",data:n});case 13:i=a.sent;case 14:t.isEditing?(u=t.areaList.findIndex((function(e){return(e._id||e.id)===(t.currentArea._id||t.currentArea.id)})),u>-1&&(t.areaList[u]=c(c(c({},t.areaList[u]),i.data||{}),{},{next_clean_date:t.calculateNextCleanDate(i.data||{})}))):(l=c(c({},i.data||{}),{},{next_clean_date:t.calculateNextCleanDate(i.data||{})}),t.areaList.unshift(l)),e.showToast({title:t.isEditing?"保存成功":"创建成功",icon:"success"}),t.closeForm(),a.next=23;break;case 19:a.prev=19,a.t0=a["catch"](2),console.error("保存失败：",a.t0),a.t0.message.includes("未登录")||a.t0.message.includes("登录")||(d=a.t0.message||"保存失败",d.includes("权限不足")?e.showModal({title:"权限不足",content:d,showCancel:!1,confirmText:"我知道了"}):e.showToast({title:d,icon:"none",duration:3e3,mask:!0}));case 23:return a.prev=23,t.saving=!1,a.finish(23);case 26:case"end":return a.stop()}}),a,null,[[2,19,23,26]])})))()},submitSchedule:function(){var t=this;return(0,u.default)(r.default.mark((function a(){var n,i,u,l;return r.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(null!==t.scheduleData.scheduled_day&&void 0!==t.scheduleData.scheduled_day){a.next=3;break}return e.showToast({title:"请选择清扫日期",icon:"none"}),a.abrupt("return");case 3:if(t.scheduleData.start_date){a.next=6;break}return e.showToast({title:"请选择开始日期",icon:"none"}),a.abrupt("return");case 6:if(n=Number(t.scheduleData.scheduled_day),!(isNaN(n)||n<0||n>6)){a.next=10;break}return e.showToast({title:"请选择有效的清扫日期",icon:"none"}),a.abrupt("return");case 10:return a.prev=10,t.scheduleSaving=!0,a.next=14,(0,s.callCloudFunction)("hygiene-area-management",{action:"updateArea",data:{id:t.currentScheduleArea._id||t.currentScheduleArea.id,scheduled_day:n,start_date:t.scheduleData.start_date}});case 14:i=t.areaList.findIndex((function(e){return(e._id||e.id)===(t.currentScheduleArea._id||t.currentScheduleArea.id)})),i>-1&&(u=c(c({},t.areaList[i]),{},{scheduled_day:n,start_date:t.scheduleData.start_date,updated_at:(new Date).toISOString()}),u.next_clean_date=t.calculateNextCleanDate(u),t.areaList[i]=u),e.showToast({title:"排班设置成功",icon:"success"}),t.closeSchedulePopup(),a.next=24;break;case 20:a.prev=20,a.t0=a["catch"](10),console.error("排班设置失败：",a.t0),a.t0.message.includes("未登录")||a.t0.message.includes("登录")||(l=a.t0.message||"设置失败",l.includes("权限不足")?e.showModal({title:"权限不足",content:l,showCancel:!1,confirmText:"我知道了"}):e.showToast({title:l,icon:"none",duration:3e3}));case 24:return a.prev=24,t.scheduleSaving=!1,a.finish(24);case 27:case"end":return a.stop()}}),a,null,[[10,20,24,27]])})))()},clearSchedule:function(){var t=this;return(0,u.default)(r.default.mark((function a(){var n,i;return r.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,(0,s.callCloudFunction)("hygiene-area-management",{action:"updateArea",data:{id:t.currentScheduleArea._id||t.currentScheduleArea.id,scheduled_day:null,start_date:null}});case 3:n=t.areaList.findIndex((function(e){return(e._id||e.id)===(t.currentScheduleArea._id||t.currentScheduleArea.id)})),n>-1&&(t.areaList[n]=c(c({},t.areaList[n]),{},{scheduled_day:null,start_date:null,next_clean_date:null,updated_at:(new Date).toISOString()})),e.showToast({title:"排班已清除",icon:"success"}),t.closeSchedulePopup(),a.next=13;break;case 9:a.prev=9,a.t0=a["catch"](0),console.error("清除排班失败：",a.t0),a.t0.message.includes("未登录")||a.t0.message.includes("登录")||(i=a.t0.message||"操作失败",i.includes("权限不足")?e.showModal({title:"权限不足",content:i,showCancel:!1,confirmText:"我知道了"}):e.showToast({title:i,icon:"none",duration:3e3}));case 13:case"end":return a.stop()}}),a,null,[[0,9]])})))()},validateForm:function(){return this.formData.name.trim()?!(this.formData.name.length>50)||(e.showToast({title:"责任区名称不能超过50个字符",icon:"none"}),!1):(e.showToast({title:"请输入责任区名称",icon:"none"}),!1)},resetForm:function(){this.formData={name:"",location:"",description:"",status:"active"},this.statusIndex=0},closeForm:function(){this.$refs.areaFormPopup.close(),this.resetForm()},closeSchedulePopup:function(){this.$refs.schedulePopup.close(),this.currentScheduleArea=null,this.scheduleData={scheduled_day:null,start_date:""},this.scheduleWeekIndex=0,this.scheduleSaving=!1},getStatusText:function(e){return{active:"启用",inactive:"禁用"}[e]||"未知"},hasValidSchedule:function(e){return null!==e.scheduled_day&&void 0!==e.scheduled_day&&"number"===typeof e.scheduled_day&&e.scheduled_day>=0&&e.scheduled_day<=6},getWeekDayText:function(e){return{0:"周日",1:"周一",2:"周二",3:"周三",4:"周四",5:"周五",6:"周六"}[e]||""},isToday:function(e){if(!e)return!1;var t=(new Date).toISOString().split("T")[0],a="string"===typeof e?e.split("T")[0]:e.toISOString().split("T")[0];return t===a},formatDate:function(e){if(!e)return"";var t=new Date(e),a=new Date,n=new Date(a);n.setDate(a.getDate()+1);var r=t.toISOString().split("T")[0],i=a.toISOString().split("T")[0],u=n.toISOString().split("T")[0];return r===i?"今天":r===u?"明天":"".concat(t.getFullYear(),"-").concat(String(t.getMonth()+1).padStart(2,"0"),"-").concat(String(t.getDate()).padStart(2,"0"))}}};t.default=d}).call(this,a("df3c")["default"])},"69f2":function(e,t,a){"use strict";(function(e,t){var n=a("47a9");a("357b"),a("861b");n(a("3240"));var r=n(a("f72f"));e.__webpack_require_UNI_MP_PLUGIN__=a,t(r.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},"92eb":function(e,t,a){"use strict";a.r(t);var n=a("12cd"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},bc83:function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return n}));var n={uniIcons:function(){return Promise.all([a.e("common/vendor"),a.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(a.bind(null,"6ddf"))},pEmptyState:function(){return a.e("components/p-empty-state/p-empty-state").then(a.bind(null,"9b76"))},uniPopup:function(){return a.e("uni_modules/uni-popup/components/uni-popup/uni-popup").then(a.bind(null,"a2b7"))}},r=function(){var e=this,t=e.$createElement,a=(e._self._c,e.loading?null:e.areaList.length),n=e.loading?null:e.enabledAreas.length,r=e.loading?null:e.scheduledAreas.length,i=e.loading?null:e.todayAreas.length,u=e.loading?null:e.areaList.length,s=!e.loading&&u>0?e.__map(e.areaList,(function(t,a){var n=e.__get_orig(t),r=e.getStatusText(t.status),i=e.hasValidSchedule(t),u=i?e.getWeekDayText(t.scheduled_day):null,s=e.hasValidSchedule(t),l=s?e.isToday(t.next_clean_date):null,c=s?e.formatDate(t.next_clean_date):null,d=s&&t.last_clean_date?e.formatDate(t.last_clean_date):null;return{$orig:n,m0:r,m1:i,m2:u,m3:s,m4:l,m5:c,m6:d}})):null,l=e.loading||u>0?null:!e.loading&&0===e.areaList.length,c=e.currentScheduleArea&&null!==e.scheduleData.scheduled_day&&e.scheduleData.start_date?e.getWeekDayText(e.scheduleData.scheduled_day):null;e.$mp.data=Object.assign({},{$root:{g0:a,g1:n,g2:r,g3:i,g4:u,l0:s,g5:l,m7:c}})},i=[]},e442:function(e,t,a){},f72f:function(e,t,a){"use strict";a.r(t);var n=a("bc83"),r=a("92eb");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("0cab");var u=a("828b"),s=Object(u["a"])(r["default"],n["b"],n["c"],!1,null,"6c335195",null,!1,n["a"],void 0);t["default"]=s.exports}},[["69f2","common/runtime","common/vendor"]]]);