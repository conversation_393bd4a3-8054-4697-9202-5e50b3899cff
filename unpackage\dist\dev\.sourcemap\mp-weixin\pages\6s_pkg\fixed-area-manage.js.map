{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Xwzc/pages/6s_pkg/fixed-area-manage.vue?3512", "webpack:///D:/Xwzc/pages/6s_pkg/fixed-area-manage.vue?8ad5", "webpack:///D:/Xwzc/pages/6s_pkg/fixed-area-manage.vue?acc0", "webpack:///D:/Xwzc/pages/6s_pkg/fixed-area-manage.vue?aaf2", "uni-app:///pages/6s_pkg/fixed-area-manage.vue", "webpack:///D:/Xwzc/pages/6s_pkg/fixed-area-manage.vue?e945", "webpack:///D:/Xwzc/pages/6s_pkg/fixed-area-manage.vue?8344"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "areaList", "loading", "saving", "isEditing", "currentArea", "formData", "location", "description", "status", "statusOptions", "value", "label", "statusIndex", "computed", "enabled<PERSON><PERSON>s", "<PERSON><PERSON><PERSON><PERSON>", "onLoad", "methods", "loadAreaList", "action", "type", "page", "pageSize", "result", "console", "uni", "title", "icon", "showAddArea", "editArea", "deleteArea", "content", "confirmText", "confirmColor", "success", "performDelete", "areaId", "id", "index", "errorMessage", "showCancel", "duration", "viewAreaDetail", "onStatusChange", "submitForm", "area", "updated_at", "mask", "validateForm", "resetForm", "closeForm", "getStatusText", "formatDate", "date"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0I;AAC1I;AACqE;AACL;AACsC;;;AAGtG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,wGAAM;AACR,EAAE,iHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5EA;AAAA;AAAA;AAAA;AAA2mB,CAAgB,qoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC+K/nB;AAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAP;QACAQ;QACAC;QACAC;MACA;MACAC,gBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;IACA;EACA;EAEAC;IACAC;MACA;QAAA;MAAA;IACA;IACAC;MACA;QAAA;MAAA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;;gBAEA;gBAAA;gBAAA,OACA;kBACAC;kBACApB;oBACAqB;oBACAZ;oBACAa;oBACAC;kBACA;gBACA;cAAA;gBARAC;gBASA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;;gBAEA;gBACA;kBACAC;oBACAC;oBACAC;kBACA;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAIA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACA/B;QACAQ;QACAC;QACAC;MACA;;MAEA;MACA;QAAA;MAAA;MAEA;IACA;IAEA;IACAsB;MAAA;MACAL;QACAC;QACAK;QACAC;QACAC;QACAC;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA,OAIA;kBACAjB;kBACApB;oBACAsC;kBACA;gBACA;cAAA;gBAEA;gBACAC;kBAAA;gBAAA;gBACA;kBACA;gBACA;gBAEAb;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAH;gBACAe,+CAEA;gBACA;kBACAd;oBACAC;oBACAK;oBACAS;oBACAR;kBACA;gBACA;kBACAP;oBACAC;oBACAC;oBACAc;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;MACA;MAEAjB;QACAC;QACAK;QACAS;MACA;IACA;IAIA;IACAG;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAKA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAzB;kBACApB;oBACAsC;oBACAvC;oBACAsB;oBACAb;oBACAD;sBACAuC;oBACA;oBACArC;kBACA;gBACA;cAAA;gBAZAe;gBAcA;gBACAe;kBAAA;gBAAA;gBACA;kBACA,uEACA,yBACA;oBACAQ;kBAAA,EACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;kBACA3B;kBACApB;oBACAD;oBACAsB;oBACAb;oBACAD;sBACAuC;oBACA;oBACArC;kBACA;gBACA;cAAA;gBAXAe;gBAaA;gBACA;cAAA;gBAGAE;kBACAC;kBACAC;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAH;gBACA;gBACAe,+CAEA;gBACA;kBACAd;oBACAC;oBACAK;oBACAS;oBACAR;kBACA;gBACA;kBACAP;oBACAC;oBACAC;oBACAc;oBACAM;kBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACAvB;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAsB;MACA;QACAnD;QACAQ;QACAC;QACAC;MACA;MACA;IACA;IAEA;IACA0C;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;QACAC;MACA;QACAA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxfA;AAAA;AAAA;AAAA;AAA8qC,CAAgB,opCAAG,EAAC,C;;;;;;;;;;;ACAlsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/6s_pkg/fixed-area-manage.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/6s_pkg/fixed-area-manage.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./fixed-area-manage.vue?vue&type=template&id=a4db3f00&scoped=true&\"\nvar renderjs\nimport script from \"./fixed-area-manage.vue?vue&type=script&lang=js&\"\nexport * from \"./fixed-area-manage.vue?vue&type=script&lang=js&\"\nimport style0 from \"./fixed-area-manage.vue?vue&type=style&index=0&id=a4db3f00&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a4db3f00\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/6s_pkg/fixed-area-manage.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fixed-area-manage.vue?vue&type=template&id=a4db3f00&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    pEmptyState: function () {\n      return import(\n        /* webpackChunkName: \"components/p-empty-state/p-empty-state\" */ \"@/components/p-empty-state/p-empty-state.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.loading ? _vm.areaList.length : null\n  var g1 = !_vm.loading ? _vm.enabledAreas.length : null\n  var g2 = !_vm.loading ? _vm.assignedAreas.length : null\n  var g3 = !_vm.loading ? _vm.areaList.length : null\n  var l0 =\n    !_vm.loading && g3 > 0\n      ? _vm.__map(_vm.areaList, function (area, index) {\n          var $orig = _vm.__get_orig(area)\n          var m0 = _vm.getStatusText(area.status)\n          var g4 = area.assigned_users && area.assigned_users.length > 0\n          var g5 = g4 ? area.assigned_users.length : null\n          var m1 = _vm.formatDate(area.created_at)\n          return {\n            $orig: $orig,\n            m0: m0,\n            g4: g4,\n            g5: g5,\n            m1: m1,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fixed-area-manage.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fixed-area-manage.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page-container\">\n    <!-- 页面头部 -->\n    <view class=\"header\">\n      <view class=\"header-title\">固定责任区管理</view>\n      <view class=\"header-subtitle\">管理各部门固定负责的区域</view>\n    </view>\n\n    <!-- 统计卡片 -->\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"stats-loading\">\n      <view class=\"loading-content\">\n        <uni-icons type=\"spinner-cycle\" size=\"32\" color=\"#007AFF\"></uni-icons>\n        <text class=\"loading-text\">加载统计数据中...</text>\n      </view>\n    </view>\n    \n    <!-- 正常统计数据 -->\n    <view v-else class=\"stats-card\">\n      <view class=\"stat-item\">\n        <view class=\"stat-number\">{{ areaList.length }}</view>\n        <view class=\"stat-label\">总责任区</view>\n      </view>\n      <view class=\"stat-item\">\n        <view class=\"stat-number\">{{ enabledAreas.length }}</view>\n        <view class=\"stat-label\">启用中</view>\n      </view>\n      <view class=\"stat-item\">\n        <view class=\"stat-number\">{{ assignedAreas.length }}</view>\n        <view class=\"stat-label\">已分配</view>\n      </view>\n    </view>\n\n    <!-- 操作按钮组 -->\n    <view class=\"action-bar\">\n      <button class=\"action-btn primary\" @click=\"showAddArea\">\n        <uni-icons type=\"plus\" size=\"16\" color=\"#FFFFFF\" />\n        <text>新增责任区</text>\n      </button>\n    </view>\n\n    <!-- 责任区列表 -->\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"list-loading\">\n      <view class=\"loading-content\">\n        <uni-icons type=\"spinner-cycle\" size=\"40\" color=\"#007AFF\"></uni-icons>\n        <text class=\"loading-text\">加载责任区数据中...</text>\n      </view>\n    </view>\n    \n    <!-- 正常列表 -->\n    <view v-else-if=\"areaList.length > 0\" class=\"area-list\">\n      <view \n        class=\"area-item\"\n        v-for=\"(area, index) in areaList\" \n        :key=\"index\"\n        @click=\"viewAreaDetail(area)\"\n      >\n        <view class=\"area-main\">\n          <view class=\"area-header\">\n            <view class=\"area-name\">{{ area.name }}</view>\n            <view class=\"area-status\" :class=\"[`status-${area.status}`]\">\n              {{ getStatusText(area.status) }}\n            </view>\n          </view>\n          <view class=\"area-info\">\n            <view class=\"info-item\">\n              <uni-icons type=\"location\" size=\"14\" color=\"#8E8E93\" />\n              <text>{{ (area.location && area.location.area) || area.location || '未设置位置' }}</text>\n            </view>\n            <view class=\"info-item\" v-if=\"area.assigned_users && area.assigned_users.length > 0\">\n              <uni-icons type=\"person\" size=\"14\" color=\"#8E8E93\" />\n              <text>已分配{{ area.assigned_users.length }}人</text>\n            </view>\n            <view class=\"info-item\">\n              <uni-icons type=\"calendar\" size=\"14\" color=\"#8E8E93\" />\n              <text>{{ formatDate(area.created_at) }}</text>\n            </view>\n          </view>\n          <view class=\"area-description\" v-if=\"area.description\">\n            {{ area.description }}\n          </view>\n        </view>\n        <view class=\"area-actions\">\n          <button class=\"action-icon-btn edit\" @click.stop=\"editArea(area)\">\n            <uni-icons type=\"compose\" size=\"16\" color=\"#007AFF\" />\n          </button>\n          <button class=\"action-icon-btn delete\" @click.stop=\"deleteArea(area)\">\n            <uni-icons type=\"trash\" size=\"16\" color=\"#FF3B30\" />\n          </button>\n        </view>\n      </view>\n    </view>\n\n    <!-- 空状态 -->\n    <p-empty-state\n      v-else\n      type=\"area\"\n      text=\"暂无固定责任区\"\n      description=\"点击上方按钮创建第一个固定责任区\"\n    ></p-empty-state>\n\n    <!-- 新增/编辑弹窗 -->\n    <uni-popup ref=\"areaFormPopup\" type=\"center\" :mask-click=\"false\">\n      <view class=\"form-popup\">\n        <view class=\"form-header\">\n          <view class=\"form-title\">{{ isEditing ? '编辑' : '新增' }}固定责任区</view>\n          <button class=\"close-btn\" @click=\"closeForm\">\n            <uni-icons type=\"close\" size=\"20\" color=\"#8E8E93\" />\n          </button>\n        </view>\n        \n        <view class=\"form-content\">\n          <view class=\"form-item\">\n            <text class=\"form-label required\">责任区名称</text>\n            <input \n              class=\"form-input\" \n              type=\"text\" \n              v-model=\"formData.name\"\n              placeholder=\"请输入责任区名称\"\n              maxlength=\"50\"\n            />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">所在位置</text>\n            <input \n              class=\"form-input\" \n              type=\"text\" \n              v-model=\"formData.location\"\n              placeholder=\"请输入具体位置\"\n              maxlength=\"100\"\n            />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">责任区描述</text>\n            <textarea \n              class=\"form-textarea\" \n              v-model=\"formData.description\"\n              placeholder=\"请输入责任区详细描述\"\n              maxlength=\"200\"\n              :show-count=\"true\"\n            ></textarea>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">状态</text>\n            <picker \n              :range=\"statusOptions\" \n              range-key=\"label\" \n              :value=\"statusIndex\"\n              @change=\"onStatusChange\"\n              class=\"form-picker\"\n            >\n              <view class=\"picker-value\">\n                <text>{{ statusOptions[statusIndex].label }}</text>\n                <uni-icons type=\"right\" size=\"16\" color=\"#C7C7CC\" />\n              </view>\n            </picker>\n          </view>\n        </view>\n        \n        <view class=\"form-footer\">\n          <button class=\"form-btn cancel\" @click=\"closeForm\">取消</button>\n          <button class=\"form-btn submit\" @click=\"submitForm\" :loading=\"saving\">\n            {{ isEditing ? '保存' : '创建' }}\n          </button>\n        </view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\nimport { callCloudFunction } from '@/utils/auth.js';\n\nexport default {\n  name: 'FixedAreaManage',\n  data() {\n    return {\n      areaList: [],\n      loading: false,\n      saving: false,\n      isEditing: false,\n      currentArea: null,\n      formData: {\n        name: '',\n        location: '',\n        description: '',\n        status: 'active'\n      },\n      statusOptions: [\n        { value: 'active', label: '启用' },\n        { value: 'inactive', label: '禁用' }\n      ],\n      statusIndex: 0\n    }\n  },\n  \n  computed: {\n    enabledAreas() {\n      return this.areaList.filter(area => area.status === 'active');\n    },\n    assignedAreas() {\n      return this.areaList.filter(area => area.assigned_users && area.assigned_users.length > 0);\n    }\n  },\n  \n      onLoad() {\n      this.loadAreaList();\n    },\n  \n  methods: {\n    // 加载责任区列表\n    async loadAreaList() {\n      try {\n        this.loading = true;\n        \n        // 使用认证工具调用云函数\n        const result = await callCloudFunction('hygiene-area-management', {\n          action: 'getAreaList',\n          data: {\n            type: 'fixed',\n            status: 'active',\n            page: 1,\n            pageSize: 100\n          }\n        });\n        this.areaList = (result.data && result.data.list) || [];\n        \n      } catch (error) {\n        console.error('加载责任区列表失败：', error);\n        \n        // 认证工具已经处理了登录相关错误，这里只处理业务错误\n        if (!error.message.includes('未登录') && !error.message.includes('登录')) {\n          uni.showToast({\n            title: error.message || '加载失败',\n            icon: 'none'\n          });\n        }\n        this.areaList = [];\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n\n    \n    // 显示新增表单\n    showAddArea() {\n      this.isEditing = false;\n      this.currentArea = null;\n      this.resetForm();\n      this.$refs.areaFormPopup.open();\n    },\n    \n    // 编辑责任区\n    editArea(area) {\n      this.isEditing = true;\n      this.currentArea = area;\n      this.formData = {\n        name: area.name || '',\n        location: (area.location && area.location.area) || area.location || '',\n        description: area.description || '',\n        status: area.status || 'active'\n      };\n      \n      // 设置状态选择器的索引\n      this.statusIndex = this.statusOptions.findIndex(option => option.value === area.status) || 0;\n      \n      this.$refs.areaFormPopup.open();\n    },\n    \n    // 删除责任区\n    deleteArea(area) {\n      uni.showModal({\n        title: '确认删除',\n        content: `确定要删除责任区\"${area.name}\"吗？删除后不可恢复。`,\n        confirmText: '删除',\n        confirmColor: '#FF3B30',\n        success: (res) => {\n          if (res.confirm) {\n            this.performDelete(area);\n          }\n        }\n      });\n    },\n    \n    // 执行删除\n    async performDelete(area) {\n      try {\n        // 获取区域ID\n        const areaId = area._id;\n        if (!areaId) {\n          throw new Error('责任区ID不存在，数据可能异常，请刷新页面重试');\n        }\n        \n        // 使用认证工具调用云函数删除责任区\n        await callCloudFunction('hygiene-area-management', {\n          action: 'deleteArea',\n          data: {\n            id: areaId\n          }\n        });\n        \n        // 从本地列表中移除\n        const index = this.areaList.findIndex(item => (item._id || item.id) === areaId);\n        if (index > -1) {\n          this.areaList.splice(index, 1);\n        }\n        \n        uni.showToast({\n          title: '删除成功',\n          icon: 'success'\n        });\n      } catch (error) {\n        console.error('删除失败：', error);\n        const errorMessage = error.message || '删除失败';\n        \n        // 对权限错误进行特殊处理\n        if (errorMessage.includes('权限不足')) {\n          uni.showModal({\n            title: '权限不足',\n            content: errorMessage,\n            showCancel: false,\n            confirmText: '我知道了'\n          });\n        } else {\n          uni.showToast({\n            title: errorMessage,\n            icon: 'none',\n            duration: 3000\n          });\n        }\n      }\n    },\n    \n    // 查看详情\n    viewAreaDetail(area) {\n      const location = (area.location && area.location.area) || area.location || '未设置';\n      const assignedCount = (area.assigned_users && area.assigned_users.length) || 0;\n      \n      uni.showModal({\n        title: area.name,\n        content: `位置：${location}\\n分配人员：${assignedCount}人\\n描述：${area.description || '无描述'}`,\n        showCancel: false\n      });\n    },\n    \n\n    \n    // 状态变更\n    onStatusChange(e) {\n      this.statusIndex = e.detail.value;\n      this.formData.status = this.statusOptions[this.statusIndex].value;\n    },\n    \n    // 提交表单\n    async submitForm() {\n      if (!this.validateForm()) {\n        return;\n      }\n      \n      try {\n        this.saving = true;\n        \n        if (this.isEditing) {\n          // 编辑模式 - 使用认证工具调用云函数更新\n          const result = await callCloudFunction('hygiene-area-management', {\n            action: 'updateArea',\n            data: {\n              id: this.currentArea._id,\n              name: this.formData.name,\n              type: 'fixed',\n              description: this.formData.description,\n              location: {\n                area: this.formData.location\n              },\n              status: this.formData.status\n            }\n          });\n          \n          // 更新本地列表\n          const index = this.areaList.findIndex(item => item._id === this.currentArea._id);\n          if (index > -1) {\n            this.areaList[index] = {\n              ...this.areaList[index],\n              ...this.formData,\n              updated_at: new Date().toISOString()\n            };\n          }\n        } else {\n          // 新增模式 - 使用认证工具调用云函数创建\n          const result = await callCloudFunction('hygiene-area-management', {\n            action: 'createArea',\n            data: {\n              name: this.formData.name,\n              type: 'fixed',\n              description: this.formData.description,\n              location: {\n                area: this.formData.location\n              },\n              status: this.formData.status\n            }\n          });\n          \n          // 添加到本地列表\n          this.areaList.unshift(result.data || {});\n        }\n        \n        uni.showToast({\n          title: this.isEditing ? '保存成功' : '创建成功',\n          icon: 'success'\n        });\n        \n        this.closeForm();\n        \n      } catch (error) {\n        console.error('保存失败：', error);\n        // 显示详细的错误信息\n        const errorMessage = error.message || '保存失败';\n        \n        // 对权限错误进行特殊处理\n        if (errorMessage.includes('权限不足')) {\n          uni.showModal({\n            title: '权限不足',\n            content: errorMessage,\n            showCancel: false,\n            confirmText: '我知道了'\n          });\n        } else {\n          uni.showToast({\n            title: errorMessage,\n            icon: 'none',\n            duration: 3000,\n            mask: true\n          });\n        }\n      } finally {\n        this.saving = false;\n      }\n    },\n    \n    // 表单验证\n    validateForm() {\n      if (!this.formData.name.trim()) {\n        uni.showToast({\n          title: '请输入责任区名称',\n          icon: 'none'\n        });\n        return false;\n      }\n      \n      if (this.formData.name.length > 50) {\n        uni.showToast({\n          title: '责任区名称不能超过50个字符',\n          icon: 'none'\n        });\n        return false;\n      }\n      \n      return true;\n    },\n    \n    // 重置表单\n    resetForm() {\n      this.formData = {\n        name: '',\n        location: '',\n        description: '',\n        status: 'active'\n      };\n      this.statusIndex = 0;\n    },\n    \n    // 关闭表单\n    closeForm() {\n      this.$refs.areaFormPopup.close();\n      this.resetForm();\n    },\n    \n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        'active': '启用',\n        'inactive': '禁用'\n      };\n      return statusMap[status] || '未知';\n    },\n    \n    // 格式化日期\n    formatDate(dateString) {\n      if (!dateString) return '';\n      // 处理云数据库的时间格式\n      let date;\n      if (typeof dateString === 'object' && dateString.$date) {\n        date = new Date(dateString.$date);\n      } else {\n        date = new Date(dateString);\n      }\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n  min-height: 100vh;\n  background: #F8F9FA;\n  padding-bottom: 40rpx;\n}\n\n.header {\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n  padding: 60rpx 32rpx 40rpx;\n  color: white;\n}\n\n.header-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  margin-bottom: 8rpx;\n}\n\n.header-subtitle {\n  font-size: 24rpx;\n  opacity: 0.8;\n}\n\n.stats-card {\n  background: white;\n  margin: 24rpx 32rpx;\n  padding: 32rpx;\n  border-radius: 16rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n  display: flex;\n  justify-content: space-around;\n}\n\n.stat-item {\n  text-align: center;\n}\n\n.stat-number {\n  font-size: 48rpx;\n  font-weight: 600;\n  color: #007AFF;\n  margin-bottom: 8rpx;\n}\n\n.stat-label {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.action-bar {\n  padding: 0 32rpx 24rpx;\n  display: flex;\n  gap: 16rpx;\n}\n\n.action-btn {\n  flex: 1;\n  height: 72rpx;\n  border-radius: 12rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8rpx;\n  font-size: 26rpx;\n  border: none;\n  transition: all 0.3s ease;\n}\n\n.action-btn.primary {\n  background: #007AFF;\n  color: white;\n}\n\n.action-btn.secondary {\n  background: white;\n  color: #007AFF;\n  border: 2rpx solid #007AFF;\n}\n\n.action-btn:active {\n  transform: scale(0.95);\n}\n\n.area-list {\n  padding: 0 32rpx;\n}\n\n.area-item {\n  background: white;\n  border-radius: 16rpx;\n  margin-bottom: 16rpx;\n  padding: 24rpx;\n  display: flex;\n  align-items: flex-start;\n  gap: 16rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n}\n\n.area-item:active {\n  transform: scale(0.98);\n}\n\n.area-main {\n  flex: 1;\n}\n\n.area-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12rpx;\n}\n\n.area-name {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n}\n\n.area-status {\n  font-size: 22rpx;\n  padding: 6rpx 12rpx;\n  border-radius: 12rpx;\n  font-weight: 500;\n}\n\n.area-status.status-active {\n  background: rgba(52, 199, 89, 0.1);\n  color: #34C759;\n}\n\n.area-status.status-inactive {\n  background: rgba(142, 142, 147, 0.1);\n  color: #8E8E93;\n}\n\n\n\n.area-info {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n  margin-bottom: 12rpx;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  gap: 6rpx;\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.area-description {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.4;\n}\n\n.area-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n}\n\n.action-icon-btn {\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 12rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: none;\n  transition: all 0.3s ease;\n}\n\n.action-icon-btn.edit {\n  background: rgba(0, 122, 255, 0.1);\n}\n\n.action-icon-btn.delete {\n  background: rgba(255, 59, 48, 0.1);\n}\n\n.action-icon-btn:active {\n  transform: scale(0.9);\n}\n\n.form-popup {\n  width: 92vw;\n  max-width: 550rpx;\n  background: white;\n  border-radius: 16rpx;\n  overflow: hidden;\n}\n\n.form-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 32rpx;\n  border-bottom: 1rpx solid #F2F2F7;\n}\n\n.form-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #1C1C1E;\n  margin-left: 5rpx;\n}\n\n.close-btn {\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 12rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #F2F2F7;\n  border: none;\n  margin-right: 5rpx;\n}\n\n.form-content {\n  padding: 32rpx;\n  max-height: 60vh;\n  overflow-y: auto;\n}\n\n/* #ifdef MP-WEIXIN */\n/* 微信小程序隐藏滚动条 */\n.form-content::-webkit-scrollbar {\n  display: none;\n  width: 0;\n  height: 0;\n  background: transparent;\n}\n\n.form-content {\n  scrollbar-width: none; /* Firefox */\n  -ms-overflow-style: none; /* IE and Edge */\n}\n/* #endif */\n\n/* #ifndef MP-WEIXIN */\n/* 非微信小程序环境下显示细滚动条 */\n.form-content::-webkit-scrollbar {\n  width: 4rpx;\n  height: 4rpx;\n}\n\n.form-content::-webkit-scrollbar-thumb {\n  background: rgba(0, 0, 0, 0.1);\n  border-radius: 2rpx;\n}\n\n.form-content::-webkit-scrollbar-thumb:hover {\n  background: rgba(0, 0, 0, 0.2);\n}\n\n.form-content {\n  scrollbar-width: thin;\n  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;\n}\n/* #endif */\n\n.form-item {\n  margin-bottom: 32rpx;\n}\n\n.form-label {\n  display: block;\n  font-size: 28rpx;\n  color: #1C1C1E;\n  margin-bottom: 12rpx;\n  font-weight: 500;\n}\n\n.form-label.required::before {\n  content: '*';\n  color: #FF3B30;\n  margin-right: 6rpx;\n}\n\n.form-input, .form-textarea {\n  width: 100%;\n  background: #F2F2F7;\n  border: none;\n  border-radius: 12rpx;\n  font-size: 28rpx;\n  color: #1C1C1E;\n  box-sizing: border-box;\n}\n\n.form-textarea {\n  min-height: 120rpx;\n  padding: 16rpx;\n  line-height: 1.5;\n  resize: none;\n}\n\n.form-input {\n  height: 80rpx;\n  padding: 0 16rpx;\n  line-height: 80rpx;\n  text-align: left;\n}\n\n/* 强制占位符样式保持一致 */\n.form-input::placeholder {\n  line-height: 80rpx;\n  color: #C7C7CC;\n}\n\n/* 兼容WebKit浏览器 */\n.form-input::-webkit-input-placeholder {\n  line-height: 80rpx;\n  color: #C7C7CC;\n}\n\n/* 兼容Firefox */\n.form-input::-moz-placeholder {\n  line-height: 80rpx;\n  color: #C7C7CC;\n  opacity: 1;\n}\n\n/* 兼容IE/Edge */\n.form-input:-ms-input-placeholder {\n  line-height: 80rpx;\n  color: #C7C7CC;\n}\n\n.form-picker {\n  width: 100%;\n}\n\n.picker-value {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n  height: 80rpx;\n  background: #F2F2F7;\n  border-radius: 12rpx;\n  padding: 0 16rpx;\n  font-size: 28rpx;\n  color: #1C1C1E;\n  box-sizing: border-box;\n}\n\n.form-footer {\n  display: flex;\n  gap: 16rpx;\n  padding: 32rpx;\n  border-top: 1rpx solid #F2F2F7;\n}\n\n.form-btn {\n  flex: 1;\n  height: 72rpx;\n  border-radius: 12rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  border: none;\n  transition: all 0.3s ease;\n}\n\n.form-btn.cancel {\n  background: #F2F2F7;\n  color: #8E8E93;\n}\n\n.form-btn.submit {\n  background: #007AFF;\n  color: white;\n}\n\n.form-btn:active {\n  transform: scale(0.95);\n}\n\n/* 加载状态样式 */\n.stats-loading {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 60rpx 0;\n  margin: 24rpx 32rpx;\n  background: white;\n  border-radius: 16rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.list-loading {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 120rpx 32rpx;\n}\n\n.loading-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 16rpx;\n}\n\n.loading-text {\n  font-size: 26rpx;\n  color: #8E8E93;\n  text-align: center;\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fixed-area-manage.vue?vue&type=style&index=0&id=a4db3f00&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fixed-area-manage.vue?vue&type=style&index=0&id=a4db3f00&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775842161\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}