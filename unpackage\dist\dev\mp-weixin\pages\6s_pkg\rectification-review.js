require('./common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/6s_pkg/rectification-review"],{

/***/ 621:
/*!************************************************************************!*\
  !*** D:/Xwzc/main.js?{"page":"pages%2F6s_pkg%2Frectification-review"} ***!
  \************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _rectificationReview = _interopRequireDefault(__webpack_require__(/*! ./pages/6s_pkg/rectification-review.vue */ 622));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_rectificationReview.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 622:
/*!*****************************************************!*\
  !*** D:/Xwzc/pages/6s_pkg/rectification-review.vue ***!
  \*****************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _rectification_review_vue_vue_type_template_id_665ecd07_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./rectification-review.vue?vue&type=template&id=665ecd07&scoped=true& */ 623);
/* harmony import */ var _rectification_review_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rectification-review.vue?vue&type=script&lang=js& */ 625);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _rectification_review_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _rectification_review_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _rectification_review_vue_vue_type_style_index_0_id_665ecd07_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./rectification-review.vue?vue&type=style&index=0&id=665ecd07&lang=scss&scoped=true& */ 627);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 50);

var renderjs





/* normalize component */

var component = Object(_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _rectification_review_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _rectification_review_vue_vue_type_template_id_665ecd07_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _rectification_review_vue_vue_type_template_id_665ecd07_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "665ecd07",
  null,
  false,
  _rectification_review_vue_vue_type_template_id_665ecd07_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/6s_pkg/rectification-review.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 623:
/*!************************************************************************************************!*\
  !*** D:/Xwzc/pages/6s_pkg/rectification-review.vue?vue&type=template&id=665ecd07&scoped=true& ***!
  \************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_review_vue_vue_type_template_id_665ecd07_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./rectification-review.vue?vue&type=template&id=665ecd07&scoped=true& */ 624);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_review_vue_vue_type_template_id_665ecd07_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_review_vue_vue_type_template_id_665ecd07_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_review_vue_vue_type_template_id_665ecd07_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_review_vue_vue_type_template_id_665ecd07_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 624:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/6s_pkg/rectification-review.vue?vue&type=template&id=665ecd07&scoped=true& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 118))
    },
    pEmptyState: function () {
      return __webpack_require__.e(/*! import() | components/p-empty-state/p-empty-state */ "components/p-empty-state/p-empty-state").then(__webpack_require__.bind(null, /*! @/components/p-empty-state/p-empty-state.vue */ 677))
    },
    uniRate: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-rate/components/uni-rate/uni-rate */ "uni_modules/uni-rate/components/uni-rate/uni-rate").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-rate/components/uni-rate/uni-rate.vue */ 971))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = !_vm.loading ? _vm.getStatusText(_vm.taskInfo.status) : null
  var m1 =
    !_vm.loading && !_vm.loadError && _vm.dataLoaded
      ? _vm.formatDateTime(_vm.taskInfo.issueFoundDate)
      : null
  var g0 =
    !_vm.loading && !_vm.loadError && _vm.dataLoaded
      ? _vm.taskInfo.issuePhotos && _vm.taskInfo.issuePhotos.length > 0
      : null
  var m2 =
    !_vm.loading && !_vm.loadError && _vm.dataLoaded
      ? _vm.formatDateTime(_vm.taskInfo.rectificationSubmitTime)
      : null
  var g1 =
    !_vm.loading && !_vm.loadError && _vm.dataLoaded && !!_vm.dataLoaded
      ? _vm.taskInfo.rectificationPhotos.length
      : null
  var m3 =
    !_vm.loading &&
    !_vm.loadError &&
    _vm.dataLoaded &&
    _vm.reviewForm.result === "approved"
      ? _vm.getRatingDescription(_vm.reviewForm.rating)
      : null
  var l0 =
    !_vm.loading && !_vm.loadError && _vm.dataLoaded
      ? _vm.__map(_vm.reviewForm.photos, function (photo, index) {
          var $orig = _vm.__get_orig(photo)
          var m4 = _vm.getPhotoDisplayUrl(photo)
          return {
            $orig: $orig,
            m4: m4,
          }
        })
      : null
  var g2 =
    !_vm.loading && !_vm.loadError && _vm.dataLoaded
      ? _vm.reviewForm.photos.length
      : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        m1: m1,
        g0: g0,
        m2: m2,
        g1: g1,
        m3: m3,
        l0: l0,
        g2: g2,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 625:
/*!******************************************************************************!*\
  !*** D:/Xwzc/pages/6s_pkg/rectification-review.vue?vue&type=script&lang=js& ***!
  \******************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_review_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./rectification-review.vue?vue&type=script&lang=js& */ 626);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_review_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_review_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_review_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_review_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_review_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 626:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/6s_pkg/rectification-review.vue?vue&type=script&lang=js& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni, uniCloud) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 28));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 31));
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
var _auth = __webpack_require__(/*! @/utils/auth.js */ 91);
var _uploadUtils = _interopRequireDefault(__webpack_require__(/*! @/utils/upload-utils.js */ 275));
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  name: 'RectificationReview',
  data: function data() {
    return {
      taskId: '',
      loading: false,
      dataLoaded: false,
      loadError: false,
      isSubmitting: false,
      taskInfo: {
        id: '',
        areaName: '',
        areaType: '',
        status: 'pending_review',
        issueDescription: '',
        issueFoundDate: '',
        inspector: '',
        issuePhotos: [],
        rectificationDescription: '',
        rectificationPhotos: [],
        rectificationSubmitTime: '',
        employeeName: ''
      },
      reviewForm: {
        result: '',
        // 'approved' | 'rejected'
        comment: '',
        photos: [],
        rating: 0 // 整改后评分 (1-5)
      },

      autoUpload: true,
      // 自动上传开关（默认开启）
      sliderTouching: false // 滑动条触摸状态
    };
  },

  computed: {
    // 计算评论长度，确保响应式更新
    commentLength: function commentLength() {
      return this.reviewForm.comment ? this.reviewForm.comment.length : 0;
    },
    canSubmit: function canSubmit() {
      if (!this.reviewForm.result) return false;
      if (this.reviewForm.result === 'rejected' && !this.reviewForm.comment.trim()) return false;
      if (this.reviewForm.result === 'rejected' && this.reviewForm.photos.length === 0) return false;
      if (this.reviewForm.result === 'approved' && !this.reviewForm.rating) return false;

      // 检查是否有照片正在上传
      var hasUploadingPhotos = this.reviewForm.photos.some(function (photo) {
        return (0, _typeof2.default)(photo) === 'object' && photo.uploading;
      });
      if (hasUploadingPhotos) return false;
      return !this.isSubmitting;
    },
    commentPlaceholder: function commentPlaceholder() {
      if (this.reviewForm.result === 'approved') {
        return '请对整改效果进行评价（可选）';
      } else if (this.reviewForm.result === 'rejected') {
        return '请说明需要重新整改的原因';
      }
      return '请输入复查意见';
    }
  },
  onLoad: function onLoad(options) {
    if (options.taskId) {
      this.taskId = options.taskId;
      this.loadTaskInfo();
    } else {
      this.loadError = true;
      uni.showModal({
        title: '参数错误',
        content: '缺少整改任务ID参数',
        showCancel: true,
        cancelText: '返回',
        confirmText: '确定',
        success: function success(res) {
          uni.navigateBack();
        }
      });
    }
  },
  methods: {
    // 处理评论输入，确保字符限制和响应式更新
    handleCommentInput: function handleCommentInput(e) {
      var value = e.detail.value || '';

      // 强制限制字符数量
      if (value.length > 200) {
        value = value.substring(0, 200);
        // 如果超出限制，显示提示
        uni.showToast({
          title: '复查意见不能超过200个字符',
          icon: 'none',
          duration: 1500
        });
      }

      // 更新数据
      this.reviewForm.comment = value;

      // 强制触发视图更新
      this.$forceUpdate();
    },
    // 加载整改任务信息
    loadTaskInfo: function loadTaskInfo() {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var result, task;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                if (_this.taskId) {
                  _context.next = 4;
                  break;
                }
                _this.loadError = true;
                uni.showModal({
                  title: '参数错误',
                  content: '整改任务ID不能为空',
                  showCancel: false,
                  confirmText: '返回',
                  success: function success() {
                    uni.navigateBack();
                  }
                });
                return _context.abrupt("return");
              case 4:
                _this.loading = true;
                _this.dataLoaded = false;
                _this.loadError = false;
                _context.prev = 7;
                _context.next = 10;
                return (0, _auth.callCloudFunction)('hygiene-rectification', {
                  action: 'getRectificationDetail',
                  data: {
                    id: _this.taskId
                  }
                });
              case 10:
                result = _context.sent;
                if (!(result && result.success && result.data)) {
                  _context.next = 16;
                  break;
                }
                task = result.data;
                _this.taskInfo = {
                  id: task._id || task.id,
                  areaName: task.area_name || '未知责任区',
                  areaType: task.area_type || 'fixed',
                  status: task.status || 'pending_review',
                  issueDescription: task.issue_description || task.description || '无问题描述',
                  issueFoundDate: task.created_at || task.issue_found_date,
                  inspector: task.inspector_name || task.assigned_by_name || task.created_by_name || task.issue && task.issue.inspector_name || '未知检查员',
                  issuePhotos: _this.processPhotos(task.photos || []),
                  rectificationDescription: task.rectification_description || task.completion_description || '',
                  rectificationPhotos: _this.processPhotos(task.completion_photos || []),
                  rectificationSubmitTime: task.submitted_at || task.updated_at,
                  employeeName: task.assigned_to_name || task.assignee_name || '未分配',
                  assignee_id: task.assignee_id
                };
                _context.next = 17;
                break;
              case 16:
                throw new Error((result === null || result === void 0 ? void 0 : result.message) || '获取整改任务详情失败');
              case 17:
                _this.dataLoaded = true;
                _context.next = 25;
                break;
              case 20:
                _context.prev = 20;
                _context.t0 = _context["catch"](7);
                console.error('加载整改任务信息失败:', _context.t0);
                _this.loadError = true;
                uni.showModal({
                  title: '加载失败',
                  content: _context.t0.message || '网络异常，请稍后重试',
                  showCancel: true,
                  cancelText: '返回',
                  confirmText: '重试',
                  success: function success(res) {
                    if (res.confirm) {
                      _this.loadTaskInfo();
                    } else {
                      uni.navigateBack();
                    }
                  }
                });
              case 25:
                _context.prev = 25;
                _this.loading = false;
                return _context.finish(25);
              case 28:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[7, 20, 25, 28]]);
      }))();
    },
    // 重新加载数据
    retryLoad: function retryLoad() {
      this.loadTaskInfo();
    },
    // 处理照片数据
    processPhotos: function processPhotos(photos) {
      if (!photos || !Array.isArray(photos)) return [];
      return photos.map(function (photo) {
        if (typeof photo === 'string') {
          return photo;
        } else if (photo && photo.url) {
          return photo.url;
        }
        return '';
      }).filter(function (url) {
        return url;
      });
    },
    // 选择审核结果
    selectReviewResult: function selectReviewResult(result) {
      this.reviewForm.result = result;
      if (result === 'approved') {
        // 通过时清空之前可能的拒绝原因
        if (this.reviewForm.comment.includes('需要') || this.reviewForm.comment.includes('不够')) {
          this.reviewForm.comment = '';
        }
        // 设置默认评分
        if (!this.reviewForm.rating) {
          this.reviewForm.rating = 3; // 默认3分
        }
      } else {
        // 拒绝时清空评分
        this.reviewForm.rating = 0;
      }
    },
    // 设置评分
    setRating: function setRating(rating) {
      this.reviewForm.rating = rating;
    },
    // 获取评分描述
    getRatingDesc: function getRatingDesc(rating) {
      var descriptions = {
        1: '需要改进',
        2: '基本合格',
        3: '良好',
        4: '优秀',
        5: '卓越'
      };
      return descriptions[rating] || '请评分';
    },
    // 获取评分描述（更详细版本，与inspection-detail.vue保持一致）
    getRatingDescription: function getRatingDescription(rating) {
      if (rating === 0) return '请评分';
      if (rating <= 1) return '较差';
      if (rating <= 2) return '一般';
      if (rating <= 3) return '良好';
      if (rating < 5) return '优秀'; // 4-4.5分都是优秀
      if (rating === 5) return '完美';
      return '';
    },
    // 星星评分变化
    onStarRateChange: function onStarRateChange(e) {
      this.reviewForm.rating = e.value;
    },
    // 点击刻度设置评分
    setRatingByMark: function setRatingByMark(rating) {
      this.reviewForm.rating = rating;
    },
    // 自定义滑动条触摸开始
    onSliderTouchStart: function onSliderTouchStart(e) {
      this.sliderTouching = true;
      this.updateRatingFromTouch(e);
    },
    // 自定义滑动条触摸移动
    onSliderTouchMove: function onSliderTouchMove(e) {
      if (this.sliderTouching) {
        this.updateRatingFromTouch(e);
      }
    },
    // 自定义滑动条触摸结束
    onSliderTouchEnd: function onSliderTouchEnd(e) {
      this.sliderTouching = false;
    },
    // 鼠标事件处理（H5端支持）
    onSliderMouseDown: function onSliderMouseDown(e) {
      this.sliderTouching = true;
      this.updateRatingFromMouse(e);
    },
    onSliderMouseMove: function onSliderMouseMove(e) {
      if (this.sliderTouching) {
        this.updateRatingFromMouse(e);
      }
    },
    onSliderMouseUp: function onSliderMouseUp(e) {
      this.sliderTouching = false;
    },
    // 根据触摸位置更新评分
    updateRatingFromTouch: function updateRatingFromTouch(e) {
      var _this2 = this;
      var touch = e.touches[0] || e.changedTouches[0];
      // 获取滑动条容器的位置信息
      uni.createSelectorQuery().in(this).select('.custom-slider-container').boundingClientRect(function (rect) {
        if (rect) {
          var x = touch.clientX - rect.left;
          var percentage = Math.max(0, Math.min(1, x / rect.width));
          var rating = Math.round(percentage * 5 * 2) / 2; // 精确到0.5
          _this2.reviewForm.rating = Math.max(0, Math.min(5, rating));
        }
      }).exec();
    },
    // 根据鼠标位置更新评分（H5端）
    updateRatingFromMouse: function updateRatingFromMouse(e) {
      var _this3 = this;
      // 获取滑动条容器的位置信息
      uni.createSelectorQuery().in(this).select('.custom-slider-container').boundingClientRect(function (rect) {
        if (rect) {
          var x = e.clientX - rect.left;
          var percentage = Math.max(0, Math.min(1, x / rect.width));
          var rating = Math.round(percentage * 5 * 2) / 2; // 精确到0.5
          _this3.reviewForm.rating = Math.max(0, Math.min(5, rating));
        }
      }).exec();
    },
    // 获取状态文本
    getStatusText: function getStatusText(status) {
      var statusMap = {
        'pending_review': '待复查',
        'approved': '复查通过',
        'rejected': '整改不达标',
        'completed': '已完成'
      };
      return statusMap[status] || status;
    },
    // 格式化日期时间
    formatDateTime: function formatDateTime(dateString) {
      if (!dateString) return '--';
      try {
        var date;
        if (typeof dateString === 'string') {
          if (dateString.includes('T') || dateString.includes('Z')) {
            // ISO 8601 格式
            date = new Date(dateString);
          } else {
            // 传统格式
            date = new Date(dateString.replace(/-/g, '/'));
          }
        } else {
          date = new Date(dateString);
        }
        if (isNaN(date.getTime())) {
          return '--';
        }
        return "".concat(date.getMonth() + 1, "\u6708").concat(date.getDate(), "\u65E5 ").concat(date.getHours().toString().padStart(2, '0'), ":").concat(date.getMinutes().toString().padStart(2, '0'));
      } catch (error) {
        return '--';
      }
    },
    // 预览整改照片
    previewPhoto: function previewPhoto(index) {
      var urls = this.taskInfo.rectificationPhotos.map(function (photo) {
        return (0, _typeof2.default)(photo) === 'object' ? photo.url : photo;
      });
      uni.previewImage({
        urls: urls,
        current: index
      });
    },
    // 预览问题照片
    previewIssuePhoto: function previewIssuePhoto(index) {
      var urls = this.taskInfo.issuePhotos.map(function (photo) {
        return (0, _typeof2.default)(photo) === 'object' ? photo.url : photo;
      });
      uni.previewImage({
        urls: urls,
        current: index
      });
    },
    // 预览审核照片
    previewReviewPhoto: function previewReviewPhoto(index) {
      var _this4 = this;
      var urls = this.reviewForm.photos.map(function (photo) {
        return _this4.getPhotoDisplayUrl(photo);
      });
      uni.previewImage({
        urls: urls,
        current: index
      });
    },
    // 获取照片显示URL
    getPhotoDisplayUrl: function getPhotoDisplayUrl(photo) {
      return photo.url || photo;
    },
    // 添加审核照片
    addReviewPhoto: function addReviewPhoto() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var res, newPhotos;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                _context2.next = 3;
                return new Promise(function (resolve, reject) {
                  uni.chooseImage({
                    count: 6 - _this5.reviewForm.photos.length,
                    sizeType: ['compressed'],
                    sourceType: ['camera', 'album'],
                    success: resolve,
                    fail: reject
                  });
                });
              case 3:
                res = _context2.sent;
                // 添加临时路径到照片列表
                newPhotos = res.tempFilePaths.map(function (path) {
                  return {
                    url: path,
                    uploaded: false,
                    cloudUrl: '',
                    cloudPath: '',
                    uploading: false,
                    belongsToRecord: false // 标记为新上传的照片
                  };
                }); // 添加到照片列表

                _this5.reviewForm.photos = _this5.reviewForm.photos.concat(newPhotos);

                // 如果开启自动上传，立即上传新选择的照片
                if (_this5.autoUpload) {
                  _this5.autoUploadNewPhotos(newPhotos);
                }
                _context2.next = 13;
                break;
              case 9:
                _context2.prev = 9;
                _context2.t0 = _context2["catch"](0);
                console.error('选择照片失败:', _context2.t0);
                uni.showToast({
                  title: '选择照片失败',
                  icon: 'none'
                });
              case 13:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 9]]);
      }))();
    },
    // 删除审核照片
    deleteReviewPhoto: function deleteReviewPhoto(index) {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var photo;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                if (!(index < 0 || index >= _this6.reviewForm.photos.length)) {
                  _context3.next = 2;
                  break;
                }
                return _context3.abrupt("return");
              case 2:
                photo = _this6.reviewForm.photos[index]; // 如果照片已经上传到云端，需要删除云端文件
                if (!(photo.uploaded && photo.cloudPath)) {
                  _context3.next = 16;
                  break;
                }
                _context3.prev = 4;
                uni.showLoading({
                  title: '删除照片中...'
                });

                // 审核照片都是新上传的，直接调用删除云文件的云函数
                _context3.next = 8;
                return uniCloud.callFunction({
                  name: 'delete-file',
                  data: {
                    fileList: [_this6.extractFileId(photo.cloudPath)]
                  }
                });
              case 8:
                _context3.next = 13;
                break;
              case 10:
                _context3.prev = 10;
                _context3.t0 = _context3["catch"](4);
                uni.showToast({
                  title: '删除云端照片失败',
                  icon: 'none'
                });
              case 13:
                _context3.prev = 13;
                uni.hideLoading();
                return _context3.finish(13);
              case 16:
                // 从本地数组中移除
                _this6.reviewForm.photos.splice(index, 1);
              case 17:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[4, 10, 13, 16]]);
      }))();
    },
    // 从URL中提取文件ID（与cleaning-upload.vue保持一致）
    extractFileId: function extractFileId(url) {
      if (url.startsWith('cloud://')) {
        var parts = url.split('/');
        return parts[parts.length - 1];
      } else if (url.includes('tcb-api')) {
        var urlObj = new URL(url);
        return urlObj.pathname.split('/').pop();
      }
      return url;
    },
    // 自动上传新选择的照片（与cleaning-upload.vue保持一致）
    autoUploadNewPhotos: function autoUploadNewPhotos(newPhotos) {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var _loop, i, _ret;
        return _regenerator.default.wrap(function _callee4$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _loop = /*#__PURE__*/_regenerator.default.mark(function _loop(i) {
                  var photo, photoIndex, uploadResult;
                  return _regenerator.default.wrap(function _loop$(_context4) {
                    while (1) {
                      switch (_context4.prev = _context4.next) {
                        case 0:
                          photo = newPhotos[i];
                          photoIndex = _this7.reviewForm.photos.findIndex(function (p) {
                            return p.url === photo.url;
                          });
                          if (!(photoIndex === -1)) {
                            _context4.next = 4;
                            break;
                          }
                          return _context4.abrupt("return", "continue");
                        case 4:
                          _context4.prev = 4;
                          // 标记为正在上传
                          _this7.$set(_this7.reviewForm.photos[photoIndex], 'uploading', true);

                          // 单张照片上传
                          _context4.next = 8;
                          return _this7.uploadSinglePhoto(photo);
                        case 8:
                          uploadResult = _context4.sent;
                          if (!uploadResult.success) {
                            _context4.next = 16;
                            break;
                          }
                          // 更新照片信息（与cleaning-upload.vue保持一致，不更新photo.url）
                          _this7.$set(_this7.reviewForm.photos[photoIndex], 'uploaded', true);
                          _this7.$set(_this7.reviewForm.photos[photoIndex], 'cloudUrl', uploadResult.url);
                          _this7.$set(_this7.reviewForm.photos[photoIndex], 'cloudPath', uploadResult.cloudPath);
                          _this7.$set(_this7.reviewForm.photos[photoIndex], 'uploading', false);
                          _context4.next = 17;
                          break;
                        case 16:
                          throw new Error(uploadResult.error || '上传失败');
                        case 17:
                          _context4.next = 23;
                          break;
                        case 19:
                          _context4.prev = 19;
                          _context4.t0 = _context4["catch"](4);
                          _this7.$set(_this7.reviewForm.photos[photoIndex], 'uploading', false);

                          // 显示上传失败提示
                          uni.showToast({
                            title: "\u5BA1\u6838\u7167\u7247".concat(i + 1, "\u4E0A\u4F20\u5931\u8D25"),
                            icon: 'none',
                            duration: 2000
                          });
                        case 23:
                        case "end":
                          return _context4.stop();
                      }
                    }
                  }, _loop, null, [[4, 19]]);
                });
                i = 0;
              case 2:
                if (!(i < newPhotos.length)) {
                  _context5.next = 10;
                  break;
                }
                return _context5.delegateYield(_loop(i), "t0", 4);
              case 4:
                _ret = _context5.t0;
                if (!(_ret === "continue")) {
                  _context5.next = 7;
                  break;
                }
                return _context5.abrupt("continue", 7);
              case 7:
                i++;
                _context5.next = 2;
                break;
              case 10:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee4);
      }))();
    },
    // 上传审核照片
    uploadReviewPhotos: function uploadReviewPhotos() {
      var _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var uploadedPhotos, i, photo, uploadResult;
        return _regenerator.default.wrap(function _callee5$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                uploadedPhotos = [];
                i = 0;
              case 2:
                if (!(i < _this8.reviewForm.photos.length)) {
                  _context6.next = 31;
                  break;
                }
                photo = _this8.reviewForm.photos[i];
                if (!photo.uploaded) {
                  _context6.next = 7;
                  break;
                }
                // 已上传的照片，直接使用云端URL
                uploadedPhotos.push({
                  url: photo.cloudPath,
                  description: '审核照片',
                  timestamp: new Date()
                });
                return _context6.abrupt("continue", 28);
              case 7:
                _context6.prev = 7;
                // 设置上传状态
                _this8.$set(photo, 'uploading', true);

                // 使用统一的上传工具上传单张照片
                _context6.next = 11;
                return _this8.uploadSinglePhoto(photo);
              case 11:
                uploadResult = _context6.sent;
                if (!uploadResult.success) {
                  _context6.next = 20;
                  break;
                }
                // 更新照片状态（与cleaning-upload.vue保持一致，不更新photo.url）
                _this8.$set(photo, 'uploaded', true);
                _this8.$set(photo, 'cloudUrl', uploadResult.url);
                _this8.$set(photo, 'cloudPath', uploadResult.cloudPath);
                _this8.$set(photo, 'uploading', false);
                uploadedPhotos.push({
                  url: uploadResult.cloudPath,
                  description: '审核照片',
                  timestamp: new Date()
                });
                _context6.next = 21;
                break;
              case 20:
                throw new Error(uploadResult.error || '上传失败');
              case 21:
                _context6.next = 28;
                break;
              case 23:
                _context6.prev = 23;
                _context6.t0 = _context6["catch"](7);
                _this8.$set(photo, 'uploading', false);
                uni.showModal({
                  title: '上传失败',
                  content: "\u5BA1\u6838\u7167\u7247 ".concat(i + 1, " \u4E0A\u4F20\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"),
                  showCancel: false
                });
                throw _context6.t0;
              case 28:
                i++;
                _context6.next = 2;
                break;
              case 31:
                return _context6.abrupt("return", uploadedPhotos);
              case 32:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee5, null, [[7, 23]]);
      }))();
    },
    // 单张照片上传（统一方法）
    uploadSinglePhoto: function uploadSinglePhoto(photo) {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var timestamp, random, cloudPath, uploadResult, fileInfo;
        return _regenerator.default.wrap(function _callee6$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                _context7.prev = 0;
                timestamp = Date.now();
                random = Math.random().toString(36).substring(2, 8);
                cloudPath = "6s/review/".concat(_this9.taskInfo.id || 'unknown', "/").concat(timestamp, "_").concat(random, ".jpg"); // 使用统一的 uploadToCloud 方法
                _context7.next = 6;
                return _uploadUtils.default.uploadToCloud(photo.url, cloudPath);
              case 6:
                uploadResult = _context7.sent;
                if (!(uploadResult && uploadResult.fileID)) {
                  _context7.next = 14;
                  break;
                }
                _context7.next = 10;
                return _uploadUtils.default.getFileInfo(uploadResult.fileID);
              case 10:
                fileInfo = _context7.sent;
                return _context7.abrupt("return", {
                  success: true,
                  cloudPath: uploadResult.fileID,
                  url: fileInfo.tempFileURL || uploadResult.fileID,
                  size: uploadResult.actualSize
                });
              case 14:
                throw new Error('上传返回结果异常');
              case 15:
                _context7.next = 20;
                break;
              case 17:
                _context7.prev = 17;
                _context7.t0 = _context7["catch"](0);
                return _context7.abrupt("return", {
                  success: false,
                  error: _context7.t0.message
                });
              case 20:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee6, null, [[0, 17]]);
      }))();
    },
    // 提交审核结果
    submitReview: function submitReview() {
      var _this10 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        var resultText;
        return _regenerator.default.wrap(function _callee8$(_context9) {
          while (1) {
            switch (_context9.prev = _context9.next) {
              case 0:
                if (_this10.canSubmit) {
                  _context9.next = 2;
                  break;
                }
                return _context9.abrupt("return");
              case 2:
                resultText = _this10.reviewForm.result === 'approved' ? '复查通过' : '需重新整改';
                uni.showModal({
                  title: '确认提交',
                  content: "\u786E\u5B9A\u5BA1\u6838\u7ED3\u679C\u4E3A\"".concat(resultText, "\"\u5417\uFF1F"),
                  success: function () {
                    var _success = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7(res) {
                      return _regenerator.default.wrap(function _callee7$(_context8) {
                        while (1) {
                          switch (_context8.prev = _context8.next) {
                            case 0:
                              if (!res.confirm) {
                                _context8.next = 3;
                                break;
                              }
                              _context8.next = 3;
                              return _this10.doSubmit();
                            case 3:
                            case "end":
                              return _context8.stop();
                          }
                        }
                      }, _callee7);
                    }));
                    function success(_x) {
                      return _success.apply(this, arguments);
                    }
                    return success;
                  }()
                });
              case 4:
              case "end":
                return _context9.stop();
            }
          }
        }, _callee8);
      }))();
    },
    // 执行提交
    doSubmit: function doSubmit() {
      var _this11 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
        var uploadedPhotos, reviewData, result, resultText, errorMessage;
        return _regenerator.default.wrap(function _callee9$(_context10) {
          while (1) {
            switch (_context10.prev = _context10.next) {
              case 0:
                _this11.isSubmitting = true;
                _context10.prev = 1;
                // 上传审核照片
                uploadedPhotos = [];
                if (!(_this11.reviewForm.photos.length > 0)) {
                  _context10.next = 7;
                  break;
                }
                _context10.next = 6;
                return _this11.uploadReviewPhotos();
              case 6:
                uploadedPhotos = _context10.sent;
              case 7:
                reviewData = {
                  id: _this11.taskId,
                  // 修正参数名
                  review_result: _this11.reviewForm.result,
                  // 'approved' 或 'rejected'
                  review_comments: _this11.reviewForm.comment.trim() || '',
                  review_photos: uploadedPhotos,
                  // 添加审核照片
                  review_date: new Date().toISOString(),
                  final_rating: _this11.reviewForm.result === 'approved' ? _this11.reviewForm.rating : 0 // 最终评分
                }; // 调用云函数提交审核结果
                _context10.next = 10;
                return (0, _auth.callCloudFunction)('hygiene-rectification', {
                  action: 'reviewRectification',
                  data: reviewData
                });
              case 10:
                result = _context10.sent;
                if (!(result && result.success)) {
                  _context10.next = 19;
                  break;
                }
                resultText = _this11.reviewForm.result === 'approved' ? '复查通过' : '需重新整改';
                uni.showToast({
                  title: "\u5BA1\u6838".concat(resultText),
                  icon: 'success'
                });

                // 通知其他页面数据已更新
                uni.$emit('rectificationReviewUpdated', {
                  taskId: _this11.taskId,
                  result: _this11.reviewForm.result,
                  areaId: _this11.taskInfo.id,
                  areaName: _this11.taskInfo.areaName
                });

                // 同时触发整改记录更新事件，供其他页面监听
                uni.$emit('rectificationRecordUpdated', {
                  taskId: _this11.taskId,
                  areaId: _this11.taskInfo.id,
                  mode: 'review',
                  result: _this11.reviewForm.result
                });

                // 返回上一页并刷新
                setTimeout(function () {
                  uni.navigateBack({
                    delta: 1
                  });
                }, 1500);
                _context10.next = 20;
                break;
              case 19:
                throw new Error((result === null || result === void 0 ? void 0 : result.message) || '提交审核结果失败');
              case 20:
                _context10.next = 27;
                break;
              case 22:
                _context10.prev = 22;
                _context10.t0 = _context10["catch"](1);
                errorMessage = '提交失败，请重试';
                if (_context10.t0.message) {
                  if (_context10.t0.message.includes('未登录')) {
                    errorMessage = '请先登录';
                  } else if (_context10.t0.message.includes('权限')) {
                    errorMessage = '您没有权限审核该任务';
                  } else {
                    errorMessage = _context10.t0.message;
                  }
                }
                uni.showModal({
                  title: '提交失败',
                  content: errorMessage,
                  showCancel: false,
                  confirmText: '知道了'
                });
              case 27:
                _context10.prev = 27;
                _this11.isSubmitting = false;
                return _context10.finish(27);
              case 30:
              case "end":
                return _context10.stop();
            }
          }
        }, _callee9, null, [[1, 22, 27, 30]]);
      }))();
    },
    // 处理照片加载错误（通用方法）
    onPhotoError: function onPhotoError(index) {
      var photoArray = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'rectificationPhotos';
      var photos;
      if (photoArray === 'rectificationPhotos') {
        photos = this.taskInfo.rectificationPhotos;
      } else if (photoArray === 'issuePhotos') {
        photos = this.taskInfo.issuePhotos;
      } else {
        photos = this.reviewForm.photos;
      }
      var photo = photos[index];
      if ((0, _typeof2.default)(photo) === 'object') {
        this.$set(photo, 'loadError', true);
      } else {
        var photoObj = photoArray === 'rectificationPhotos' || photoArray === 'issuePhotos' ? {
          url: photo,
          loadError: true
        } : {
          url: photo,
          uploaded: false,
          cloudUrl: '',
          cloudPath: '',
          uploading: false,
          belongsToRecord: false,
          loadError: true
        };
        this.$set(photos, index, photoObj);
      }
    },
    // 重试加载照片（通用方法）
    retryPhotoLoad: function retryPhotoLoad(index) {
      var photoArray = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'rectificationPhotos';
      var photos;
      if (photoArray === 'rectificationPhotos') {
        photos = this.taskInfo.rectificationPhotos;
      } else if (photoArray === 'issuePhotos') {
        photos = this.taskInfo.issuePhotos;
      } else {
        photos = this.reviewForm.photos;
      }
      var photo = photos[index];
      if ((0, _typeof2.default)(photo) === 'object') {
        this.$set(photo, 'loadError', false);
      }
    },
    // 切换自动上传状态
    toggleAutoUpload: function toggleAutoUpload() {
      this.autoUpload = !this.autoUpload;
      uni.showToast({
        title: this.autoUpload ? '已开启自动上传' : '已关闭自动上传',
        icon: 'none',
        duration: 1500
      });
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 27)["uniCloud"]))

/***/ }),

/***/ 627:
/*!***************************************************************************************************************!*\
  !*** D:/Xwzc/pages/6s_pkg/rectification-review.vue?vue&type=style&index=0&id=665ecd07&lang=scss&scoped=true& ***!
  \***************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_review_vue_vue_type_style_index_0_id_665ecd07_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./rectification-review.vue?vue&type=style&index=0&id=665ecd07&lang=scss&scoped=true& */ 628);
/* harmony import */ var _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_review_vue_vue_type_style_index_0_id_665ecd07_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_review_vue_vue_type_style_index_0_id_665ecd07_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_review_vue_vue_type_style_index_0_id_665ecd07_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_review_vue_vue_type_style_index_0_id_665ecd07_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_rectification_review_vue_vue_type_style_index_0_id_665ecd07_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 628:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Xwzc/pages/6s_pkg/rectification-review.vue?vue&type=style&index=0&id=665ecd07&lang=scss&scoped=true& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[621,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/6s_pkg/rectification-review.js.map