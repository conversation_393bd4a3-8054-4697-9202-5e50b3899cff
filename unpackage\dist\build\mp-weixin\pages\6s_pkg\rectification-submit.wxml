<view class="page-container data-v-16fef611"><view class="issue-card data-v-16fef611"><view class="issue-header data-v-16fef611"><view class="issue-status data-v-16fef611">整改中</view><view class="issue-title-header data-v-16fef611">{{taskInfo.title}}</view></view><view class="issue-meta data-v-16fef611"><view class="meta-item data-v-16fef611"><uni-icons vue-id="179d841f-1" type="location" size="16" color="#007AFF" class="data-v-16fef611" bind:__l="__l"></uni-icons><text class="data-v-16fef611">{{taskInfo.areaName}}</text></view><view class="meta-item data-v-16fef611"><uni-icons vue-id="179d841f-2" type="calendar" size="16" color="#FF9500" class="data-v-16fef611" bind:__l="__l"></uni-icons><text class="data-v-16fef611">{{"截止："+taskInfo.deadline}}</text></view></view><block wx:if="{{taskInfo.description}}"><view class="issue-description data-v-16fef611"><text class="desc-label data-v-16fef611">问题描述：</text><text class="desc-content data-v-16fef611">{{taskInfo.description}}</text></view></block></view><view class="section-card data-v-16fef611"><view class="section-header data-v-16fef611"><view class="section-title data-v-16fef611">问题图片</view><view class="section-subtitle data-v-16fef611">发现时拍摄</view></view><block wx:if="{{$root.g0===0}}"><p-empty-state vue-id="179d841f-3" type="data" text="暂无问题图片" description="检查员未上传问题照片" class="data-v-16fef611" bind:__l="__l"></p-empty-state></block><block wx:else><view class="image-grid original-images data-v-16fef611"><block wx:for="{{taskInfo.issuePhotos}}" wx:for-item="image" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['previewOriginalImage',[index]]]]]}}" class="image-item data-v-16fef611" bindtap="__e"><image class="issue-image data-v-16fef611" src="{{image}}" mode="aspectFill"></image><view class="image-label data-v-16fef611">问题图片</view></view></block></view></block></view><view class="section-card data-v-16fef611"><view class="section-header data-v-16fef611"><view class="section-header-main data-v-16fef611"><view class="section-title data-v-16fef611">整改照片</view><view class="section-subtitle data-v-16fef611">请上传整改后的照片</view></view><view data-event-opts="{{[['tap',[['toggleAutoUpload',['$event']]]]]}}" class="auto-upload-toggle data-v-16fef611" bindtap="__e"><view class="toggle-label data-v-16fef611">自动上传</view><view class="{{['toggle-switch','data-v-16fef611',(autoUpload)?'active':'']}}"><view class="toggle-circle data-v-16fef611"></view></view></view></view><view class="upload-area data-v-16fef611"><view class="image-grid data-v-16fef611"><block wx:for="{{rectificationPhotos}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view class="image-item photo-item data-v-16fef611"><image class="uploaded-image data-v-16fef611" src="{{photo.url}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewPhoto',[index]]]]]}}" bindtap="__e"></image><block wx:if="{{photo.uploading}}"><view class="photo-uploading data-v-16fef611"><view class="upload-spinner data-v-16fef611"></view></view></block><block wx:else><block wx:if="{{photo.uploaded}}"><view class="photo-uploaded data-v-16fef611"><uni-icons vue-id="{{'179d841f-4-'+index}}" type="checkmarkempty" size="16" color="white" class="data-v-16fef611" bind:__l="__l"></uni-icons></view></block><block wx:else><view data-event-opts="{{[['tap',[['uploadSinglePhoto',['$0'],[[['rectificationPhotos','',index]]]]]]]}}" class="photo-failed data-v-16fef611" bindtap="__e"><uni-icons vue-id="{{'179d841f-5-'+index}}" type="refreshempty" size="16" color="white" class="data-v-16fef611" bind:__l="__l"></uni-icons></view></block></block><view data-event-opts="{{[['tap',[['deletePhoto',[index]]]]]}}" class="photo-delete data-v-16fef611" bindtap="__e"><uni-icons vue-id="{{'179d841f-6-'+index}}" type="close" size="18" color="white" class="data-v-16fef611" bind:__l="__l"></uni-icons></view></view></block><block wx:if="{{$root.g1<maxPhotos}}"><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="image-item add-photo data-v-16fef611" bindtap="__e"><view class="add-icon data-v-16fef611"><uni-icons vue-id="179d841f-7" type="camera-filled" size="32" color="#8E8E93" class="data-v-16fef611" bind:__l="__l"></uni-icons></view><text class="add-text data-v-16fef611">添加照片</text><text class="add-count data-v-16fef611">{{$root.g2+"/"+maxPhotos}}</text></view></block></view><view class="upload-tips data-v-16fef611"><text class="tip-text data-v-16fef611">• 建议拍摄整改后的实际效果照片</text><text class="tip-text data-v-16fef611">{{"• 可上传 "+maxPhotos+" 张照片，支持多角度拍摄"}}</text></view></view></view><view class="section-card data-v-16fef611"><view class="section-header data-v-16fef611"><view class="section-title data-v-16fef611">整改说明</view><view class="section-subtitle data-v-16fef611">详细描述整改措施和效果</view></view><view class="textarea-container data-v-16fef611"><view class="description-input-container data-v-16fef611"><textarea class="description-input data-v-16fef611" placeholder="请详细说明采取的整改措施、解决方案和预期效果" maxlength="{{200}}" auto-height="{{true}}" data-event-opts="{{[['input',[['__set_model',['','rectificationDescription','$event',[]]],['handleDescriptionInput',['$event']]]]]}}" value="{{rectificationDescription}}" bindinput="__e"></textarea><view class="char-count-overlay data-v-16fef611">{{descriptionLength+"/200"}}</view></view></view></view><view class="button-container data-v-16fef611"><button class="{{['primary-button','data-v-16fef611',(submitting)?'loading':'']}}" disabled="{{submitting}}" data-event-opts="{{[['tap',[['submitRectification',['$event']]]]]}}" bindtap="__e"><block wx:if="{{submitting}}"><view class="button-loading data-v-16fef611"><view class="loading-spinner data-v-16fef611"></view><text class="data-v-16fef611">提交中...</text></view></block><block wx:else><text class="data-v-16fef611">提交整改</text></block></button></view><block wx:if="{{loading}}"><view class="loading-mask data-v-16fef611"><view class="loading-content data-v-16fef611"><uni-icons vue-id="179d841f-8" type="spinner-cycle" size="32" color="#007AFF" class="data-v-16fef611" bind:__l="__l"></uni-icons><text class="loading-text data-v-16fef611">{{loadingText}}</text></view></view></block></view>