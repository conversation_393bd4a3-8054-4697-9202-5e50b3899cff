@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container.data-v-5b99b5c7 {
  min-height: 100vh;
  background: linear-gradient(145deg, #f0f4f8 0%, #d9e2ec 100%);
  width: 100%;
  overflow-x: hidden;
  /* 防止水平滚动 */
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
}
/* 问题信息卡片 */
.issue-card.data-v-5b99b5c7 {
  background: #FFFFFF;
  margin: 24rpx 32rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}
.issue-header.data-v-5b99b5c7 {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 20rpx;
}
.issue-status.data-v-5b99b5c7 {
  background: #FF9500;
  color: #FFFFFF;
  font-size: 22rpx;
  font-weight: 600;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  white-space: nowrap;
}
.issue-title-header.data-v-5b99b5c7 {
  flex: 1;
  font-size: 28rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-left: 16rpx;
  line-height: 1.4;
  word-wrap: break-word;
}
.issue-meta.data-v-5b99b5c7 {
  display: flex;
  gap: 24rpx;
  margin-bottom: 24rpx;
}
.meta-item.data-v-5b99b5c7 {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  color: #8E8E93;
}
.issue-description.data-v-5b99b5c7 {
  background: #F8F9FA;
  padding: 20rpx;
  border-radius: 12rpx;
}
.desc-label.data-v-5b99b5c7 {
  font-size: 24rpx;
  color: #8E8E93;
  margin-right: 8rpx;
}
.desc-content.data-v-5b99b5c7 {
  font-size: 26rpx;
  color: #1C1C1E;
  line-height: 1.5;
}
/* 通用卡片样式 */
.section-card.data-v-5b99b5c7 {
  background: #FFFFFF;
  margin: 24rpx 32rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}
.section-header.data-v-5b99b5c7 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}
.section-header-main.data-v-5b99b5c7 {
  flex: 1;
}
.section-title.data-v-5b99b5c7 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 8rpx;
}
.section-subtitle.data-v-5b99b5c7 {
  font-size: 24rpx;
  color: #8E8E93;
}
/* 自动上传开关 */
.auto-upload-toggle.data-v-5b99b5c7 {
  display: flex;
  align-items: center;
  gap: 12rpx;
  cursor: pointer;
}
.toggle-label.data-v-5b99b5c7 {
  font-size: 24rpx;
  color: #1C1C1E;
  font-weight: 500;
}
.toggle-switch.data-v-5b99b5c7 {
  width: 80rpx;
  height: 48rpx;
  background: #E5E5EA;
  border-radius: 24rpx;
  position: relative;
  transition: background-color 0.3s ease;
}
.toggle-switch.active.data-v-5b99b5c7 {
  background: #34C759;
}
.toggle-circle.data-v-5b99b5c7 {
  width: 40rpx;
  height: 40rpx;
  background: #FFFFFF;
  border-radius: 50%;
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}
.toggle-switch.active .toggle-circle.data-v-5b99b5c7 {
  -webkit-transform: translateX(32rpx);
          transform: translateX(32rpx);
}
/* 图片网格 */
.image-grid.data-v-5b99b5c7 {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}
.image-item.data-v-5b99b5c7 {
  width: 170rpx;
  height: 170rpx;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  background: #F5F5F5;
}
.issue-image.data-v-5b99b5c7, .uploaded-image.data-v-5b99b5c7 {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.image-label.data-v-5b99b5c7 {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  color: #FFFFFF;
  font-size: 20rpx;
  text-align: center;
  padding: 6rpx;
}
/* 照片状态指示器 */
.photo-uploading.data-v-5b99b5c7,
.photo-uploaded.data-v-5b99b5c7,
.photo-failed.data-v-5b99b5c7 {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
}
.photo-uploading.data-v-5b99b5c7 {
  background: rgba(0, 0, 0, 0.6);
}
.photo-uploaded.data-v-5b99b5c7 {
  background: #34C759;
}
.photo-failed.data-v-5b99b5c7 {
  background: rgba(255, 59, 48, 0.9);
  cursor: pointer;
}
.upload-spinner.data-v-5b99b5c7 {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-top: 2rpx solid white;
  border-radius: 50%;
  -webkit-animation: spin-data-v-5b99b5c7 1s linear infinite;
          animation: spin-data-v-5b99b5c7 1s linear infinite;
}
@-webkit-keyframes spin-data-v-5b99b5c7 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin-data-v-5b99b5c7 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.photo-delete.data-v-5b99b5c7 {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
}
.add-photo.data-v-5b99b5c7 {
  width: 170rpx;
  height: 170rpx;
  border: 2rpx dashed #D1D1D6;
  background: #FAFBFC;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}
.add-icon.data-v-5b99b5c7 {
  margin-bottom: 8rpx;
}
.add-text.data-v-5b99b5c7 {
  font-size: 24rpx;
  color: #8E8E93;
  font-weight: 500;
}
.add-count.data-v-5b99b5c7 {
  font-size: 20rpx;
  color: #8E8E93;
}
.upload-tips.data-v-5b99b5c7 {
  margin-top: 16rpx;
  padding: 16rpx;
  background: #F8F9FA;
  border-radius: 8rpx;
}
.tip-text.data-v-5b99b5c7 {
  display: block;
  font-size: 22rpx;
  color: #8E8E93;
  line-height: 1.5;
  margin-bottom: 4rpx;
}
/* 文本输入 */
.textarea-container.data-v-5b99b5c7 {
  position: relative;
}
.description-input-container.data-v-5b99b5c7 {
  position: relative;
  width: 100%;
}
.description-input.data-v-5b99b5c7 {
  width: 100%;
  min-height: 180rpx;
  padding: 20rpx 16rpx 40rpx 16rpx;
  border: 1rpx solid #E5E5EA;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1C1C1E;
  background: #F8F9FA;
  box-sizing: border-box;
  resize: none;
  line-height: 1.6;
}
.char-count-overlay.data-v-5b99b5c7 {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  font-size: 22rpx;
  color: #8E8E93;
  background: rgba(248, 249, 250, 0.9);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  pointer-events: none;
  z-index: 2;
  -webkit-backdrop-filter: blur(4rpx);
          backdrop-filter: blur(4rpx);
}
/* 按钮容器 */
.button-container.data-v-5b99b5c7 {
  padding: 32rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
}
.primary-button.data-v-5b99b5c7 {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 88rpx;
}
.primary-button.data-v-5b99b5c7:disabled {
  background: #C7C7CC;
  color: #8E8E93;
  opacity: 0.7;
}
.primary-button.loading.data-v-5b99b5c7 {
  background: #0056D6;
  opacity: 0.9;
}
.button-loading.data-v-5b99b5c7 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  color: white;
}
.loading-spinner.data-v-5b99b5c7 {
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid white;
  border-radius: 50%;
  -webkit-animation: spin-data-v-5b99b5c7 1s linear infinite;
          animation: spin-data-v-5b99b5c7 1s linear infinite;
}
/* 加载遮罩 */
.loading-mask.data-v-5b99b5c7 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.loading-content.data-v-5b99b5c7 {
  background: #FFFFFF;
  padding: 48rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}
.loading-text.data-v-5b99b5c7 {
  font-size: 28rpx;
  color: #1C1C1E;
}
/* H5平台图片尺寸优化 */
