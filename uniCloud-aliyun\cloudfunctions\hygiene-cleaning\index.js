'use strict';

const db = uniCloud.database();
const dbCmd = db.command;

// 6S权限角色常量 - 基于实际业务场景
const HYGIENE_ROLES = {
  // 最高管理权限：系统管理员、厂长（可以创建、修改、删除，但主要看结果）
  ADMIN_ROLES: ['admin', 'GM'],
  // 6S专员权限：综合员、发布人（专门负责6S的核心工作人员）
  SPECIALIST_ROLES: ['Integrated', 'reviser'],
  // 清洁员权限：所有人都有责任区，都需要做清洁工作
  CLEANER_ROLES: ['admin', 'GM', 'supervisor', 'PM', 'responsible', 'logistics', 'dispatch', 'Integrated', 'operator', 'technician', 'mechanic', 'user', 'reviser'],
  // 检查权限：只有6S专员负责检查（包括卫生检查、问题发现、任务指派）
  INSPECTOR_ROLES: ['Integrated', 'reviser'],
  // 数据查看权限：所有角色
  VIEW_ROLES: ['admin', 'GM', 'supervisor', 'PM', 'responsible', 'logistics', 'dispatch', 'Integrated', 'operator', 'technician', 'mechanic', 'user', 'reviser']
};

exports.main = async (event, context) => {
  const { action, data, uniIdToken } = event;
  let uid = '';
  let role = [];
  
  // 获取用户信息
  if (uniIdToken) {
    const { createInstance } = require('uni-id-common');
    const uniID = createInstance({ context });
    const payload = await uniID.checkToken(uniIdToken);
    if (payload.code === 0) {
      uid = payload.uid;
      
      // 从数据库获取用户角色信息
      try {
        const userRes = await db.collection('uni-id-users')
          .doc(uid)
          .field({ role: true })
          .get();
        
        if (userRes.data && userRes.data[0] && userRes.data[0].role) {
          role = Array.isArray(userRes.data[0].role) ? userRes.data[0].role : [userRes.data[0].role];
        } else {
          role = ['user'];
        }
      } catch (error) {
        console.error('获取用户角色失败:', error);
        role = ['user'];
      }
    }
  }
  
  // 权限检查
  if (!uid) {
    return {
      success: false,
      message: '用户未登录'
    };
  }
  
  try {
    switch (action) {
      case 'createCleaningRecord':
        return await createCleaningRecord(data, uid, role);
      case 'updateCleaningRecord':
        return await updateCleaningRecord(data, uid, role);
      case 'deleteCleaningRecord':
        return await deleteCleaningRecord(data, uid, role);
      case 'getCleaningRecords':
        return await getCleaningRecords(data, uid, role);
      case 'getCleaningRecordDetail':
        return await getCleaningRecordDetail(data, uid, role);
      case 'getMyCleaningRecords':
        return await getMyCleaningRecords(data, uid);
      case 'getCleaningHistory':
        return await getCleaningHistory(data, uid, role);
      case 'updateCleaningProgress':
        return await updateCleaningProgress(data, uid, role);
      case 'verifyCleaningResult':
        return await verifyCleaningResult(data, uid, role);
      case 'getWeeklySchedule':
        return await getWeeklySchedule(data, uid, role);
      case 'uploadCleaningPhotos':
        return await uploadCleaningPhotos(data, uid, role);
      case 'deleteCleaningPhotos':
        return await deleteCleaningPhotos(data, uid, role);
      case 'getBatchCleaningRecords':
        return await getBatchCleaningRecords(data, uid, role);
      default:
        return {
          success: false,
          message: '未知的操作类型'
        };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return {
      success: false,
      message: '服务器错误',
      error: error.message
    };
  }
};

// 创建清洁记录
async function createCleaningRecord(data, uid, role) {
  const { 
    area_id, 
    cleaning_date, 
    photos = [], 
    remark = '', 
    duration_minutes,
    quality_score,
    completion_status = 'completed'
  } = data;
  
  // 验证必填字段
  if (!area_id || !cleaning_date) {
    return {
      success: false,
      message: '责任区ID和清洁日期不能为空'
    };
  }
  
  // 获取责任区信息
  const areaResult = await db.collection('hygiene-areas').doc(area_id).get();
  if (!areaResult.data.length) {
    return {
      success: false,
      message: '责任区不存在'
    };
  }
  
  const area = areaResult.data[0];
  
  // 权限检查：管理员、6S专员或分配到该责任区的用户可以创建记录
  const hasAdminPermission = HYGIENE_ROLES.ADMIN_ROLES.some(r => role.includes(r));
  const hasSpecialistPermission = HYGIENE_ROLES.SPECIALIST_ROLES.some(r => role.includes(r));
  const isAssignedToArea = area.assigned_users && area.assigned_users.includes(uid);
  
  // 检查hygiene-assignments表中的分配关系
  let hasAssignmentPermission = false;
  try {
    const assignmentResult = await db.collection('hygiene-assignments')
      .where({
        employee_id: uid,  // 使用employee_id字段
        area_ids: dbCmd.in([area_id]),  // area_ids是数组，需要用in查询
        status: 'active'  // 检查状态字段而不是is_active
      })
      .get();
    hasAssignmentPermission = assignmentResult.data.length > 0;
  } catch (error) {
    console.error('检查分配权限失败:', error);
  }
  
  // 公共责任区权限：任何有清洁权限的用户都可以创建清洁记录
  const isPublicArea = area.type === 'public';
  const hasCleanerRole = HYGIENE_ROLES.CLEANER_ROLES.some(r => role.includes(r));
  const hasPublicAreaPermission = isPublicArea && hasCleanerRole;
  
  if (!hasAdminPermission && !hasSpecialistPermission && !isAssignedToArea && !hasAssignmentPermission && !hasPublicAreaPermission) {
    return {
      success: false,
      message: '您没有权限为该责任区创建清洁记录'
    };
  }
  
  // 获取用户信息
  const userResult = await db.collection('uni-id-users').doc(uid).get();
  const userName = userResult.data.length > 0 ? userResult.data[0].nickname || userResult.data[0].username : '';
  
  // 计算周次
  const weekNumber = getWeekNumber(new Date(cleaning_date));
  
  const cleaningData = {
    area_id,
    area_name: area.name,
    user_id: uid,
    user_name: userName,
    cleaning_date: new Date(cleaning_date),
    week_number: weekNumber,
    photos: photos.map(photo => ({
      url: photo.url || photo,
      type: photo.type || 'after',
      description: photo.description || ''
    })),
    remark,
    completion_status,
    created_at: new Date(),
    updated_at: new Date()
  };
  
  // 可选字段
  if (duration_minutes) cleaningData.duration_minutes = duration_minutes;
  if (quality_score) cleaningData.quality_score = quality_score;
  
  const result = await db.collection('hygiene-cleaning-records').add(cleaningData);
  
  return {
    success: true,
    message: '清洁记录创建成功',
    data: {
      _id: result.id,
      ...cleaningData
    }
  };
}

// 更新清洁记录
async function updateCleaningRecord(data, uid, role) {
  const { 
    id, 
    photos, 
    remark, 
    duration_minutes, 
    quality_score, 
    completion_status,
    issues_found
  } = data;
  
  if (!id) {
    return {
      success: false,
      message: '清洁记录ID不能为空'
    };
  }
  
  // 获取清洁记录
  const recordResult = await db.collection('hygiene-cleaning-records').doc(id).get();
  if (!recordResult.data.length) {
    return {
      success: false,
      message: '清洁记录不存在'
    };
  }
  
  const record = recordResult.data[0];
  
  // 权限检查：只有记录创建者、管理员或6S专员可以修改
  const hasAdminPermission = HYGIENE_ROLES.ADMIN_ROLES.some(r => role.includes(r));
  const hasSpecialistPermission = HYGIENE_ROLES.SPECIALIST_ROLES.some(r => role.includes(r));
  const isCreator = record.user_id === uid;
  
  if (!hasAdminPermission && !hasSpecialistPermission && !isCreator) {
    return {
      success: false,
      message: '您只能修改自己创建的清洁记录'
    };
  }
  
  const updateData = {
    updated_at: new Date()
  };
  
  if (photos !== undefined) {
    updateData.photos = photos.map(photo => ({
      url: photo.url || photo,
      type: photo.type || 'after',
      description: photo.description || ''
    }));
  }
  if (remark !== undefined) updateData.remark = remark;
  if (duration_minutes !== undefined) updateData.duration_minutes = duration_minutes;
  if (quality_score !== undefined) updateData.quality_score = quality_score;
  if (completion_status !== undefined) updateData.completion_status = completion_status;
  if (issues_found !== undefined) updateData.issues_found = issues_found;
  
  await db.collection('hygiene-cleaning-records').doc(id).update(updateData);
  
  return {
    success: true,
    message: '清洁记录更新成功'
  };
}

// 删除清洁记录
async function deleteCleaningRecord(data, uid, role) {
  const { id } = data;
  
  if (!id) {
    return {
      success: false,
      message: '清洁记录ID不能为空'
    };
  }
  
  // 获取清洁记录
  const recordResult = await db.collection('hygiene-cleaning-records').doc(id).get();
  if (!recordResult.data.length) {
    return {
      success: false,
      message: '清洁记录不存在'
    };
  }
  
  const record = recordResult.data[0];
  
  // 权限检查：只有记录创建者、管理员或6S专员可以删除
  const hasAdminPermission = HYGIENE_ROLES.ADMIN_ROLES.some(r => role.includes(r));
  const hasSpecialistPermission = HYGIENE_ROLES.SPECIALIST_ROLES.some(r => role.includes(r));
  const isCreator = record.user_id === uid;
  
  if (!hasAdminPermission && !hasSpecialistPermission && !isCreator) {
    return {
      success: false,
      message: '只有管理员或记录创建者可以删除清洁记录'
    };
  }
  
  // 删除记录前先删除相关的照片
  const photos = record.photos || [];
  if (photos.length > 0) {
    try {
      const photoUrls = photos.map(photo => photo.url);
      const fileIds = photoUrls.map(url => {
        // 从URL中提取文件ID
        if (url.startsWith('cloud://')) {
          const parts = url.split('/');
          return parts[parts.length - 1];
        } else if (url.includes('tcb-api')) {
          const urlObj = new URL(url);
          return urlObj.pathname.split('/').pop();
        }
        return url;
      });
      
      // 调用删除云存储文件的云函数
      await uniCloud.callFunction({
        name: 'delete-file',
        data: {
          fileList: fileIds
        }
      });
      
      console.log(`已删除清洁记录 ${id} 的 ${photos.length} 张照片`);
    } catch (error) {
      console.error('删除记录关联照片失败:', error);
      // 不阻止记录删除，只记录错误
    }
  }
  
  await db.collection('hygiene-cleaning-records').doc(id).remove();
  
  return {
    success: true,
    message: '清洁记录删除成功'
  };
}

// 获取清洁记录列表
async function getCleaningRecords(data, uid, role) {
  const { 
    area_id, 
    user_id, 
    status, 
    start_date, 
    end_date, 
    page = 1, 
    pageSize = 20,
    week_number
  } = data;
  
  let whereCondition = {};
  
  // 责任区筛选
  if (area_id) {
    whereCondition.area_id = area_id;
  }
  
  // 用户筛选
  if (user_id) {
    whereCondition.user_id = user_id;
  }
  
  // 状态筛选
  if (status) {
    whereCondition.completion_status = status;
  }
  
  // 周次筛选
  if (week_number) {
    whereCondition.week_number = week_number;
  }
  
  // 日期范围筛选
  if (start_date && end_date) {
    whereCondition.cleaning_date = dbCmd.gte(new Date(start_date)).and(dbCmd.lte(new Date(end_date)));
  } else if (start_date) {
    whereCondition.cleaning_date = dbCmd.gte(new Date(start_date));
  } else if (end_date) {
    whereCondition.cleaning_date = dbCmd.lte(new Date(end_date));
  }
  
  const skip = (page - 1) * pageSize;
  
  const [listResult, countResult] = await Promise.all([
    db.collection('hygiene-cleaning-records')
      .where(whereCondition)
      .orderBy('cleaning_date', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get(),
    db.collection('hygiene-cleaning-records')
      .where(whereCondition)
      .count()
  ]);
  
  return {
    success: true,
    data: {
      list: listResult.data,
      total: countResult.total,
      page,
      pageSize
    }
  };
}

// 获取清洁记录详情
async function getCleaningRecordDetail(data, uid, role) {
  const { record_id } = data;
  
  if (!record_id) {
    return {
      success: false,
      message: '记录ID不能为空'
    };
  }
  
  try {
    const result = await db.collection('hygiene-cleaning-records')
      .doc(record_id)
      .get();
    
    if (!result.data || result.data.length === 0) {
      return {
        success: false,
        message: '记录不存在'
      };
    }
    
    const record = result.data[0];
    
    // 权限检查：只有管理员、记录创建者或责任区负责人可以查看详情
    const hasAdminPermission = HYGIENE_ROLES.ADMIN_ROLES.some(r => role.includes(r)) || 
                               HYGIENE_ROLES.SPECIALIST_ROLES.some(r => role.includes(r));
    const isRecordOwner = record.user_id === uid;
    
    if (!hasAdminPermission && !isRecordOwner) {
      return {
        success: false,
        message: '权限不足，无法查看此记录详情'
      };
    }
    
    return {
      success: true,
      data: record
    };
  } catch (error) {
    console.error('获取清洁记录详情失败:', error);
    return {
      success: false,
      message: '获取记录详情失败',
      error: error.message
    };
  }
}

// 获取我的清洁记录
async function getMyCleaningRecords(data, uid) {
  const { status, limit = 10 } = data;
  
  let whereCondition = {
    user_id: uid
  };
  
  if (status) {
    whereCondition.completion_status = status;
  }
  
  const result = await db.collection('hygiene-cleaning-records')
    .where(whereCondition)
    .orderBy('cleaning_date', 'desc')
    .limit(limit)
    .get();
  
  // 获取责任区信息以补充 area_type
  const records = result.data;
  if (records.length > 0) {
    // 收集所有责任区ID
    const areaIds = [...new Set(records.map(record => record.area_id))];
    
    // 批量查询责任区信息
    const areasResult = await db.collection('hygiene-areas')
      .where({
        _id: dbCmd.in(areaIds)
      })
      .field({
        _id: true,
        type: true
      })
      .get();
    
    // 创建责任区类型映射
    const areaTypeMap = {};
    areasResult.data.forEach(area => {
      areaTypeMap[area._id] = area.type || 'fixed';
    });
    
    // 为每条记录添加 area_type
    records.forEach(record => {
      record.area_type = areaTypeMap[record.area_id] || 'fixed';
    });
  }
  
  return {
    success: true,
    data: records
  };
}

// 获取清洁历史
async function getCleaningHistory(data, uid, role) {
  const { area_id, months = 6 } = data;
  
  if (!area_id) {
    return {
      success: false,
      message: '责任区ID不能为空'
    };
  }
  
  // 计算开始日期
  const startDate = new Date();
  startDate.setMonth(startDate.getMonth() - months);
  
  const result = await db.collection('hygiene-cleaning-records')
    .where({
      area_id,
      cleaning_date: dbCmd.gte(startDate)
    })
    .orderBy('cleaning_date', 'desc')
    .get();
  
  // 按月分组
  const historyByMonth = {};
  result.data.forEach(record => {
    const monthKey = record.cleaning_date.toISOString().substring(0, 7); // YYYY-MM
    if (!historyByMonth[monthKey]) {
      historyByMonth[monthKey] = [];
    }
    historyByMonth[monthKey].push(record);
  });
  
  return {
    success: true,
    data: {
      total: result.data.length,
      historyByMonth,
      records: result.data
    }
  };
}

// 更新清洁进度
async function updateCleaningProgress(data, uid, role) {
  const { id, progress_percentage, progress_notes, progress_photos } = data;
  
  if (!id) {
    return {
      success: false,
      message: '清洁记录ID不能为空'
    };
  }
  
  const recordResult = await db.collection('hygiene-cleaning-records').doc(id).get();
  if (!recordResult.data.length) {
    return {
      success: false,
      message: '清洁记录不存在'
    };
  }
  
  const record = recordResult.data[0];
  
  // 权限检查：只有记录创建者、管理员或6S专员可以更新进度
  const hasAdminPermission = HYGIENE_ROLES.ADMIN_ROLES.some(r => role.includes(r));
  const hasSpecialistPermission = HYGIENE_ROLES.SPECIALIST_ROLES.some(r => role.includes(r));
  const isCreator = record.user_id === uid;
  
  if (!hasAdminPermission && !hasSpecialistPermission && !isCreator) {
    return {
      success: false,
      message: '您只能更新自己的清洁进度'
    };
  }
  
  const updateData = {
    updated_at: new Date()
  };
  
  if (progress_percentage !== undefined) {
    updateData.progress_percentage = progress_percentage;
    
    // 根据进度自动更新状态
    if (progress_percentage === 100) {
      updateData.completion_status = 'completed';
    } else if (progress_percentage > 0) {
      updateData.completion_status = 'in_progress';
    }
  }
  
  if (progress_notes) updateData.progress_notes = progress_notes;
  if (progress_photos) updateData.progress_photos = progress_photos;
  
  await db.collection('hygiene-cleaning-records').doc(id).update(updateData);
  
  return {
    success: true,
    message: '清洁进度更新成功'
  };
}

// 验证清洁结果
async function verifyCleaningResult(data, uid, role) {
  const { id, verified, verify_comments, quality_score } = data;
  
  if (!id) {
    return {
      success: false,
      message: '清洁记录ID不能为空'
    };
  }
  
  // 权限检查：只有管理员或6S专员可以验证
  const hasAdminPermission = HYGIENE_ROLES.ADMIN_ROLES.some(r => role.includes(r));
  const hasSpecialistPermission = HYGIENE_ROLES.SPECIALIST_ROLES.some(r => role.includes(r));
  
  if (!hasAdminPermission && !hasSpecialistPermission) {
    return {
      success: false,
      message: '您当前没有验证清洁结果的权限'
    };
  }
  
  const recordResult = await db.collection('hygiene-cleaning-records').doc(id).get();
  if (!recordResult.data.length) {
    return {
      success: false,
      message: '清洁记录不存在'
    };
  }
  
  const updateData = {
    verified_by: uid,
    verified_at: new Date(),
    updated_at: new Date()
  };
  
  if (verified !== undefined) updateData.verified = verified;
  if (verify_comments) updateData.verify_comments = verify_comments;
  if (quality_score !== undefined) updateData.quality_score = quality_score;
  
  await db.collection('hygiene-cleaning-records').doc(id).update(updateData);
  
  return {
    success: true,
    message: '清洁结果验证成功'
  };
}

// 获取周清洁计划
async function getWeeklySchedule(data, uid, role) {
  const { week_start, user_id } = data;
  
  let targetUserId = user_id || uid;
  
  // 如果查询其他用户的计划，需要管理员或6S专员权限
  if (user_id && user_id !== uid) {
    const hasAdminPermission = HYGIENE_ROLES.ADMIN_ROLES.some(r => role.includes(r));
    const hasSpecialistPermission = HYGIENE_ROLES.SPECIALIST_ROLES.some(r => role.includes(r));
    
    if (!hasAdminPermission && !hasSpecialistPermission) {
      return {
        success: false,
        message: '您只能查看自己的周计划'
      };
    }
  }
  
  const weekStart = week_start ? new Date(week_start) : getWeekStart(new Date());
  const weekEnd = getWeekEnd(weekStart);
  const weekNumber = getWeekNumber(weekStart);
  
  // 获取用户分配的责任区
  const assignmentResult = await db.collection('hygiene-assignments')
    .where({
      user_id: targetUserId,
      is_active: true
    })
    .get();
  
  const areaIds = assignmentResult.data.map(assignment => assignment.area_id);
  
  if (areaIds.length === 0) {
    return {
      success: true,
      data: {
        weekNumber,
        weekStart,
        weekEnd,
        areas: [],
        completed: [],
        pending: []
      }
    };
  }
  
  // 获取责任区详情
  const areasResult = await db.collection('hygiene-areas')
    .where({
      _id: dbCmd.in(areaIds),
      status: 'active'
    })
    .get();
  
  // 获取本周已完成的清洁记录
  const completedResult = await db.collection('hygiene-cleaning-records')
    .where({
      user_id: targetUserId,
      area_id: dbCmd.in(areaIds),
      cleaning_date: dbCmd.gte(weekStart).and(dbCmd.lte(weekEnd))
    })
    .get();
  
  const completedAreaIds = completedResult.data.map(record => record.area_id);
  
  // 分类责任区
  const completed = areasResult.data.filter(area => completedAreaIds.includes(area._id));
  const pending = areasResult.data.filter(area => !completedAreaIds.includes(area._id));
  
  return {
    success: true,
    data: {
      weekNumber,
      weekStart,
      weekEnd,
      areas: areasResult.data,
      completed: completed.map(area => ({
        ...area,
        cleaning_record: completedResult.data.find(record => record.area_id === area._id)
      })),
      pending
    }
  };
}

// 上传清洁照片
async function uploadCleaningPhotos(data, uid, role) {
  const { cleaning_record_id, photos } = data;
  
  if (!cleaning_record_id || !photos || !Array.isArray(photos)) {
    return {
      success: false,
      message: '清洁记录ID和照片不能为空'
    };
  }
  
  const recordResult = await db.collection('hygiene-cleaning-records').doc(cleaning_record_id).get();
  if (!recordResult.data.length) {
    return {
      success: false,
      message: '清洁记录不存在'
    };
  }
  
  const record = recordResult.data[0];
  
  // 权限检查：只有记录创建者、管理员或6S专员可以上传照片
  const hasAdminPermission = HYGIENE_ROLES.ADMIN_ROLES.some(r => role.includes(r));
  const hasSpecialistPermission = HYGIENE_ROLES.SPECIALIST_ROLES.some(r => role.includes(r));
  const isCreator = record.user_id === uid;
  
  if (!hasAdminPermission && !hasSpecialistPermission && !isCreator) {
    return {
      success: false,
      message: '您只能上传自己清洁记录的照片'
    };
  }
  
  // 合并现有照片和新照片
  const existingPhotos = record.photos || [];
  const newPhotos = photos.map(photo => ({
    url: photo.url,
    type: photo.type || 'after',
    description: photo.description || '',
    upload_time: new Date()
  }));
  
  const allPhotos = [...existingPhotos, ...newPhotos];
  
  await db.collection('hygiene-cleaning-records').doc(cleaning_record_id).update({
    photos: allPhotos,
    updated_at: new Date()
  });
  
  return {
    success: true,
    message: '照片上传成功',
    data: {
      photos: allPhotos
    }
  };
}

// 辅助函数：获取周次
function getWeekNumber(date) {
  const year = date.getFullYear();
  const week = getWeek(date);
  return `${year}-W${String(week).padStart(2, '0')}`;
}

// 辅助函数：获取ISO周数
function getWeek(date) {
  const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
  const dayNum = d.getUTCDay() || 7;
  d.setUTCDate(d.getUTCDate() + 4 - dayNum);
  const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
  return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
}

// 辅助函数：获取周开始时间
function getWeekStart(date) {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day + (day === 0 ? -6 : 1);
  return new Date(d.setDate(diff));
}

// 删除清洁照片
async function deleteCleaningPhotos(data, uid, role) {
  const { cleaning_record_id, photo_urls } = data;
  
  if (!cleaning_record_id || !photo_urls || !Array.isArray(photo_urls) || photo_urls.length === 0) {
    return {
      success: false,
      message: '清洁记录ID和要删除的照片URL不能为空'
    };
  }
  
  // 获取清洁记录
  const recordResult = await db.collection('hygiene-cleaning-records').doc(cleaning_record_id).get();
  if (!recordResult.data.length) {
    return {
      success: false,
      message: '清洁记录不存在'
    };
  }
  
  const record = recordResult.data[0];
  
  // 权限检查：只有记录创建者、管理员或6S专员可以删除照片
  const hasAdminPermission = HYGIENE_ROLES.ADMIN_ROLES.some(r => role.includes(r));
  const hasSpecialistPermission = HYGIENE_ROLES.SPECIALIST_ROLES.some(r => role.includes(r));
  const isCreator = record.user_id === uid;
  
  if (!hasAdminPermission && !hasSpecialistPermission && !isCreator) {
    return {
      success: false,
      message: '您只能删除自己清洁记录的照片'
    };
  }
  
  // 验证要删除的照片是否属于该记录
  const existingPhotos = record.photos || [];
  const existingPhotoUrls = existingPhotos.map(photo => photo.url);
  const invalidUrls = photo_urls.filter(url => !existingPhotoUrls.includes(url));
  
  if (invalidUrls.length > 0) {
    return {
      success: false,
      message: `以下照片不属于该清洁记录：${invalidUrls.join(', ')}`
    };
  }
  
  try {
    // 从URL中提取文件ID列表
    const fileIds = photo_urls.map(url => {
      // 如果是云存储URL，提取文件ID
      if (url.startsWith('cloud://')) {
        // cloud://env-id.xxx/xxx/file-id 格式
        const parts = url.split('/');
        return parts[parts.length - 1]; // 最后一部分是文件ID
      } else if (url.includes('tcb-api')) {
        // 如果是CDN URL，需要从URL参数中提取文件ID
        const urlObj = new URL(url);
        return urlObj.pathname.split('/').pop();
      }
      return url; // 如果已经是文件ID，直接返回
    });
    
    // 调用删除云存储文件的云函数
    const deleteResult = await uniCloud.callFunction({
      name: 'delete-file',
      data: {
        fileList: fileIds
      }
    });
    
    if (deleteResult.result.code !== 0) {
      console.error('删除云存储文件失败:', deleteResult.result);
      return {
        success: false,
        message: `删除云存储文件失败: ${deleteResult.result.message}`
      };
    }
    
    // 从记录中移除已删除的照片
    const remainingPhotos = existingPhotos.filter(photo => !photo_urls.includes(photo.url));
    
    // 更新清洁记录
    await db.collection('hygiene-cleaning-records').doc(cleaning_record_id).update({
      photos: remainingPhotos,
      updated_at: new Date()
    });
    
    return {
      success: true,
      message: '照片删除成功',
      data: {
        deleted_photos: photo_urls,
        remaining_photos: remainingPhotos,
        cloud_deletion_result: deleteResult.result
      }
    };
    
  } catch (error) {
    console.error('删除照片失败:', error);
    return {
      success: false,
      message: '删除照片失败: ' + error.message
    };
  }
}

// 批量获取清洁记录（性能优化）
async function getBatchCleaningRecords(data, uid, role) {
  const { area_ids, start_date, end_date, latest_only = false } = data;
  
  // 验证参数
  if (!area_ids || !Array.isArray(area_ids) || area_ids.length === 0) {
    return {
      success: false,
      message: '责任区ID列表不能为空'
    };
  }
  
  try {
    let whereCondition = {
      area_id: dbCmd.in(area_ids)
    };
    
    // 日期范围筛选
    if (start_date && end_date) {
      whereCondition.cleaning_date = dbCmd.gte(new Date(start_date)).and(dbCmd.lte(new Date(end_date)));
    } else if (start_date) {
      whereCondition.cleaning_date = dbCmd.gte(new Date(start_date));
    } else if (end_date) {
      whereCondition.cleaning_date = dbCmd.lte(new Date(end_date));
    }
    
    if (latest_only) {
      // 获取每个责任区的最新记录
      const results = [];
      
      // 为每个责任区查询最新记录
      for (const areaId of area_ids) {
        const areaCondition = {
          area_id: areaId
        };
        
        // 添加日期条件
        if (whereCondition.cleaning_date) {
          areaCondition.cleaning_date = whereCondition.cleaning_date;
        }
        
        const areaResult = await db.collection('hygiene-cleaning-records')
          .where(areaCondition)
          .orderBy('cleaning_date', 'desc')
          .limit(1)
          .get();
        
        if (areaResult.data.length > 0) {
          results.push(areaResult.data[0]);
        }
      }
      
      return {
        success: true,
        data: results
      };
    } else {
      // 获取所有匹配的记录
      const result = await db.collection('hygiene-cleaning-records')
        .where(whereCondition)
        .orderBy('cleaning_date', 'desc')
        .get();
      
      return {
        success: true,
        data: result.data
      };
    }
  } catch (error) {
    console.error('批量获取清洁记录失败:', error);
    return {
      success: false,
      message: '批量获取清洁记录失败',
      error: error.message
    };
  }
}

// 辅助函数：获取周结束时间
function getWeekEnd(date) {
  const d = getWeekStart(date);
  d.setDate(d.getDate() + 6);
  return d;
} 