<template>
  <view class="qrcode-batch-page">  
    <!-- 点位列表 -->
    <scroll-view 
      class="point-list" 
      scroll-y 
      @scrolltolower="loadMore"
      :style="{ height: 'calc(100vh - 120rpx)' }"
      show-scrollbar="false"
      enable-back-to-top
    >
      <view class="point-list-content">
        <view class="point-item" v-for="point in points" :key="point._id">
          <view class="point-info">
            <text class="point-name">{{point.name || '未命名点位'}}</text>
            <view class="point-detail">
              <text class="point-address">{{point.location && point.location.address || point.address || '无地址信息'}}</text>
              <text class="point-coordinates" v-if="point.location">经度: {{point.location.longitude.toFixed(6)}} | 纬度: {{point.location.latitude.toFixed(6)}}</text>
              <text class="point-coordinates">ID: {{point._id}}</text>
            </view>
          </view>
          
          <view class="qrcode-preview" v-if="point.qrcode_enabled">
            <view class="qrcode-wrapper">
              <uqrcode
                v-if="point.qrcodeContent"
                :ref="'qrcode-' + point._id"
                :canvas-id="'qrcode-' + point._id"
                class="qrcode-canvas"
                :value="point.qrcodeContent"
                :options="{
                  size: 200,
                  margin: 10,
                  backgroundColor: '#ffffff',
                  foregroundColor: '#000000',
                  errorCorrectLevel: 'H',
                  type: 'image'
                }"
                @complete="(res) => onQRCodeComplete(res, point)"
              />
            </view>
            <view class="qrcode-meta">
              <text class="version">版本: {{point.qrcode_version || 1}}</text>
              <text class="status" :class="{'status-success': point.generated}">
                {{point.generated ? '已生成' : '未生成'}}
              </text>
            </view>
          </view>
          
          <view class="point-actions">
            <button 
              class="btn-generate" 
              v-if="!point.qrcode_enabled" 
              @click="enableQRCode(point)"
            >
              <uni-icons type="scan" size="16" color="#FFFFFF"></uni-icons>
              <text>启用二维码</text>
            </button>
            <button 
              class="btn-regenerate" 
              v-else-if="!point.generated" 
              @click="generateQRCode(point)"
            >
              <uni-icons type="refresh" size="16" color="#FFFFFF"></uni-icons>
              <text>生成二维码</text>
            </button>
            <button 
              class="btn-save" 
              v-else 
              @click="saveQRCode(point)"
            >
              <uni-icons type="download" size="16" color="#FFFFFF"></uni-icons>
              <text>保存图片</text>
            </button>
            <button 
              class="btn-disable" 
              v-if="point.qrcode_enabled" 
              @click="disableQRCode(point)"
            >
              <uni-icons type="closeempty" size="16" color="#FFFFFF"></uni-icons>
              <text>关闭二维码</text>
            </button>
          </view>
        </view>
        
        <!-- 加载更多提示 -->
        <view class="loading-more" v-if="loadMoreStatus === 'loading'">
          <uni-icons type="spinner-cycle" size="20" color="#666666"></uni-icons>
          <text>加载中...</text>
        </view>
        
        <!-- 没有更多数据提示 -->
        <view class="no-more" v-if="loadMoreStatus === 'noMore' && points.length > 0">
          <text>没有更多数据了</text>
        </view>
        
        <!-- 空状态 -->
        <p-empty-state 
          v-if="!loading && points.length === 0"
          text="暂无点位数据"
          image="/static/empty/empty_data.png"
        ></p-empty-state>
      </view>
    </scroll-view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <button 
        :class="['btn-batch-operation', allPointsEnabled ? 'btn-disable-all' : 'btn-generate-all']" 
        @click="handleBatchOperation"
      >
        <uni-icons :type="allPointsEnabled ? 'closeempty' : 'scan'" size="18" color="#FFFFFF"></uni-icons>
        <text>{{allPointsEnabled ? '一键关闭全部' : '一键启用全部'}}</text>
      </button>
      <button class="btn-save-all" @click="saveAllQRCodes">
        <uni-icons type="download" size="18" color="#FFFFFF"></uni-icons>
        <text>保存全部</text>
      </button>
    </view>
  </view>
</template>

<script>
import PatrolApi from '@/utils/patrol-api.js';
import QRCodeUtil from '@/utils/qrcode-utils.js';
import PEmptyState from '@/components/p-empty-state/p-empty-state.vue';

export default {
  components: {
    PEmptyState
  },
  
  data() {
    return {
      points: [], // 点位列表
      loading: false,
      hasGeneratedCodes: false,
      pagination: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      loadMoreStatus: 'more',
      isLoadingMore: false
    }
  },
  
  onLoad() {
    this.loadPoints(true);
  },

  // 添加页面生命周期方法
  onReachBottom() {
    this.loadMore();
  },

  // 添加下拉刷新
  onPullDownRefresh() {
    this.loadPoints(true).then(() => {
      uni.stopPullDownRefresh();
    });
  },

  computed: {
    // 判断是否所有点位都已启用并生成二维码
    allPointsEnabled() {
      return this.points.length > 0 && this.points.every(p => p.qrcode_enabled && p.generated);
    }
  },

  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    // 加载更多
    async loadMore() {
      // 避免重复加载和已经没有更多数据的情况
      if (this.loadMoreStatus === 'loading' || this.loadMoreStatus === 'noMore' || this.isLoadingMore) {
        return;
      }

      this.isLoadingMore = true;
      this.loadMoreStatus = 'loading';
      this.pagination.page++;
      await this.loadPoints();
    },

    // 加载点位列表
    async loadPoints(reset = false) {
      if (reset) {
        this.pagination.page = 1;
        this.points = [];
        this.loadMoreStatus = 'more';
      }

      try {
        if (reset) {
          this.loading = true;
          uni.showLoading({ title: '加载中...' });
        }

        const res = await PatrolApi.callPointFunction('getPointList', {
          params: {
            keyword: '',
            page: this.pagination.page,
            pageSize: this.pagination.pageSize,
            status: 1
          }
        });
        
        if (res.code === 0 && res.data) {
          const newList = res.data.list || [];
          
          // 处理新的点位数据
          const newPoints = newList.map(point => ({
            ...point,
            generated: !!point.qrcode_content,  // 如果已有二维码内容则标记为已生成
            qrcodeContent: point.qrcode_content || null  // 使用数据库中的二维码内容
          }));
          
          // 更新列表
          if (reset) {
            this.points = newPoints;
          } else {
            this.points = [...this.points, ...newPoints];
          }
          
          // 确保total是数字
          if (reset) {
            let total;
            if (typeof res.data.total === 'number' && res.data.total > 0) {
              total = res.data.total;
            } else if (typeof res.data.total === 'string' && parseInt(res.data.total) > 0) {
              total = parseInt(res.data.total);
            } else if (newList.length >= this.pagination.pageSize) {
              total = newList.length + this.pagination.pageSize;
            } else {
              total = newList.length;
            }
            this.pagination.total = total;
          }
          
          // 更新加载状态
          if (newList.length === 0) {
            this.loadMoreStatus = 'noMore';
          } else if (newList.length < this.pagination.pageSize) {
            this.loadMoreStatus = 'noMore';
          } else {
            this.loadMoreStatus = 'more';
          }
          

        }
      } catch (e) {

        uni.showToast({
          title: '加载点位失败',
          icon: 'none'
        });
      } finally {
        if (reset) {
          this.loading = false;
          uni.hideLoading();
        }
        this.isLoadingMore = false;
      }
    },
    
    // 启用点位二维码
    async enableQRCode(point) {
      try {
        uni.showLoading({ title: '启用中...' });
        const res = await PatrolApi.callPointFunction('updatePoint', {
          data: {
            id: point._id,
            qrcode_enabled: true,
            qrcode_version: 1
          }
        });
        
        if (res.code === 0) {
          // 更新本地数据
          const index = this.points.findIndex(p => p._id === point._id);
          if (index !== -1) {
            this.$set(this.points[index], 'qrcode_enabled', true);
            this.$set(this.points[index], 'qrcode_version', 1);
            // 自动生成二维码
            this.$nextTick(() => {
              this.generateQRCode(this.points[index]);
            });
          }
          
          uni.showToast({
            title: '启用成功',
            icon: 'success'
          });
        } else {
          throw new Error(res.message || '启用失败');
        }
      } catch (e) {

        uni.showToast({
          title: e.message || '启用失败',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },
    
    // 关闭点位二维码
    async disableQRCode(point) {
      try {
        uni.showModal({
          title: '确认关闭',
          content: '关闭二维码后，该点位的所有二维码数据将被清除，是否继续？',
          success: async (res) => {
            if (res.confirm) {
              uni.showLoading({ title: '关闭中...' });
              const res = await PatrolApi.callPointFunction('updatePoint', {
                data: {
                  id: point._id,
                  qrcode_enabled: false,
                  qrcode_content: null,        // 清除二维码内容
                  qrcode_version: null,        // 清除版本号
                  qrcode_hash_key: null,       // 清除哈希密钥
                  qrcode_generated_time: null  // 清除生成时间
                }
              });
              
              if (res.code === 0) {
                // 更新本地数据
                const index = this.points.findIndex(p => p._id === point._id);
                if (index !== -1) {
                  this.$set(this.points[index], 'qrcode_enabled', false);
                  this.$set(this.points[index], 'qrcode_content', null);
                  this.$set(this.points[index], 'qrcode_version', null);
                  this.$set(this.points[index], 'qrcode_hash_key', null);
                  this.$set(this.points[index], 'qrcode_generated_time', null);
                  this.$set(this.points[index], 'generated', false);
                  this.$set(this.points[index], 'qrcodeContent', null);
                }
                
                uni.showToast({
                  title: '已关闭二维码',
                  icon: 'success'
                });
              } else {
                throw new Error(res.message || '关闭失败');
              }
            }
          }
        });
      } catch (e) {

        uni.showToast({
          title: e.message || '关闭失败',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },
    
    // 生成单个点位的二维码
    async generateQRCode(point) {
      try {
        // 如果点位未启用二维码，先启用并获取新的hash_key
        if (!point.qrcode_enabled) {
          const enableRes = await PatrolApi.callPointFunction('updatePoint', {
            data: {
              id: point._id,
              qrcode_enabled: true,
              qrcode_version: 1
            }
          });
          
          if (enableRes.code !== 0) {
            throw new Error(enableRes.message || '启用二维码失败');
          }
          
          // 重新获取点位信息以获取新生成的hash_key
          const detailRes = await PatrolApi.callPointFunction('getPointDetail', {
            point_id: point._id
          });
          
          if (detailRes.code === 0 && detailRes.data) {
            // 更新点位信息，特别是hash_key
            Object.assign(point, detailRes.data);
          } else {
            throw new Error('获取点位详情失败');
          }
          
          // 更新本地数据
          const index = this.points.findIndex(p => p._id === point._id);
          if (index !== -1) {
            this.$set(this.points[index], 'qrcode_enabled', true);
            this.$set(this.points[index], 'qrcode_version', 1);
            this.$set(this.points[index], 'qrcode_hash_key', point.qrcode_hash_key);
          }
        }
        
        // 确保有hash_key
        if (!point.qrcode_hash_key) {
          const detailRes = await PatrolApi.callPointFunction('getPointDetail', {
            point_id: point._id
          });
          
          if (detailRes.code === 0 && detailRes.data) {
            point.qrcode_hash_key = detailRes.data.qrcode_hash_key;
          } else {
            throw new Error('获取点位hash_key失败');
          }
        }
        
        // 生成新的二维码内容
        const qrContent = QRCodeUtil.getQRCodeData({
          ...point,
          qrcode_content: null  // 强制生成新内容
        }, {
          includeTimestamp: false  // 不包含时间戳
        });
        
        // 保存到数据库
        const updateResult = await PatrolApi.callPointFunction('updatePoint', {
          data: {
            id: point._id,
            qrcode_content: qrContent,
            qrcode_generated_time: new Date().toISOString(),
            qrcode_enabled: true,
            qrcode_version: point.qrcode_version || 1
          }
        });
        
        if (updateResult.code !== 0) {
          throw new Error(updateResult.message || '生成二维码失败');
        }
        
        // 更新本地数据
        const index = this.points.findIndex(p => p._id === point._id);
        if (index !== -1) {
          this.$set(this.points[index], 'qrcodeContent', qrContent);
          this.$set(this.points[index], 'qrcode_content', qrContent);
          this.$set(this.points[index], 'generated', true);
          this.$set(this.points[index], 'qrcode_enabled', true);
          this.$set(this.points[index], 'qrcode_version', point.qrcode_version || 1);
          this.$set(this.points[index], 'qrcode_generated_time', new Date().toISOString());
        }
        
        // 显示成功提示
        uni.showToast({
          title: '生成成功',
          icon: 'success'
        });
      } catch (err) {

        uni.showToast({
          title: err.message || '生成失败',
          icon: 'none'
        });
        throw err;
      }
    },
    
    // 二维码生成完成回调
    onQRCodeComplete(res, point) {
      if (res.success) {
        const index = this.points.findIndex(p => p._id === point._id);
        if (index !== -1) {
          this.$set(this.points[index], 'generated', true);
          this.hasGeneratedCodes = true;
        }
      }
    },
    
    // 处理批量操作（生成或关闭）
    async handleBatchOperation() {
      if (this.allPointsEnabled) {
        await this.disableAllQRCodes();
      } else {
        await this.generateAllQRCodes();
      }
    },

    // 保存单个二维码
    async saveQRCode(point) {
      try {
        uni.showLoading({ title: '保存中...' });
        
        const qrcodeRef = this.$refs['qrcode-' + point._id];
        if (!qrcodeRef) {
          throw new Error('二维码组件未找到');
        }
        
        const qrcode = Array.isArray(qrcodeRef) ? qrcodeRef[0] : qrcodeRef;
        
        // #ifdef H5
        // H5平台：直接获取canvas并触发下载
        try {
          // 获取二维码的canvas元素
          const canvas = qrcode.$el.querySelector('canvas');
          if (!canvas) {
            throw new Error('未找到二维码canvas');
          }
          
          // 将canvas转换为blob
          canvas.toBlob((blob) => {
            if (!blob) {
              uni.showToast({ title: '保存失败', icon: 'none' });
              return;
            }
            
            // 创建下载链接
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${point.name || point._id}.png`;
            
            // 触发下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // 清理URL对象
            setTimeout(() => {
              URL.revokeObjectURL(url);
            }, 100);
            
            uni.showToast({
              title: '保存成功',
              icon: 'success'
            });
          }, 'image/png', 1.0);
          
        } catch (canvasError) {
          // 回退到原始的save方法
          await qrcode.save({
            success: () => {
              uni.showToast({
                title: '保存成功',
                icon: 'success'
              });
            },
            fail: (error) => {
              uni.showToast({
                title: '保存失败',
                icon: 'none'
              });
            }
          });
        }
        // #endif
        
        // #ifndef H5
        // 非H5平台使用原始方法
        await qrcode.save({
          success: () => {
            uni.showToast({
              title: '保存成功',
              icon: 'success'
            });
          },
          fail: (error) => {
            uni.showToast({
              title: '保存失败',
              icon: 'none'
            });
          }
        });
        // #endif
      } catch (e) {
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },
    
    // 批量生成二维码
    async generateAllQRCodes() {
      // 过滤出需要生成二维码的点位
      const pointsToGenerate = this.points.filter(point => 
        !point.qrcode_content || !point.generated
      );

      if (pointsToGenerate.length === 0) {
        uni.showToast({
          title: '没有需要生成的二维码',
          icon: 'none'
        });
        return;
      }

      // 显示确认框
      try {
        const confirmRes = await new Promise((resolve, reject) => {
          uni.showModal({
            title: '确认生成',
            content: `确定要为${pointsToGenerate.length}个点位启用并生成二维码吗？`,
            confirmText: '确定',
            cancelText: '取消',
            success: resolve,
            fail: reject
          });
        });

        if (!confirmRes.confirm) {
          return;
        }

        let successCount = 0;
        let failCount = 0;

        // 显示初始进度
        uni.showLoading({
          title: `处理中(0/${pointsToGenerate.length})`,
          mask: true
        });

        // 逐个生成二维码
        for (let i = 0; i < pointsToGenerate.length; i++) {
          const point = pointsToGenerate[i];
          try {
            await this.generateQRCode(point);
            successCount++;
            // 更新进度
            uni.showLoading({
              title: `处理中(${successCount}/${pointsToGenerate.length})`,
              mask: true
            });
          } catch (err) {

            failCount++;
          }
        }

        // 隐藏加载框
        uni.hideLoading();

        // 显示最终结果
        uni.showModal({
          title: '处理完成',
          content: `生成完成\n成功：${successCount}个\n失败：${failCount}个`,
          showCancel: false
        });
      } catch (err) {

        uni.hideLoading();
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    },
    
    // 一键关闭所有二维码
    async disableAllQRCodes() {
      // 获取所有已启用二维码的点位
      const enabledPoints = this.points.filter(p => p.qrcode_enabled);
      
      if (enabledPoints.length === 0) {
        uni.showToast({
          title: '没有已启用的二维码',
          icon: 'none'
        });
        return;
      }

      // 弹窗确认
      const confirmRes = await new Promise(resolve => {
        uni.showModal({
          title: '确认关闭',
          content: `将关闭${enabledPoints.length}个点位的二维码功能，关闭后将清除所有二维码数据，是否继续？`,
          success: resolve
        });
      });

      if (!confirmRes.confirm) return;

      try {
        uni.showLoading({
          title: '处理中(0/' + enabledPoints.length + ')',
          mask: true
        });

        let successCount = 0;
        let failCount = 0;

        for (let i = 0; i < enabledPoints.length; i++) {
          const point = enabledPoints[i];
          try {
            const res = await PatrolApi.callPointFunction('updatePoint', {
              data: {
                id: point._id,
                qrcode_enabled: false,
                qrcode_content: null,        // 清除二维码内容
                qrcode_version: null,        // 清除版本号
                qrcode_hash_key: null,       // 清除哈希密钥
                qrcode_generated_time: null  // 清除生成时间
              }
            });
            
            if (res.code === 0) {
              // 更新本地数据
              const index = this.points.findIndex(p => p._id === point._id);
              if (index !== -1) {
                this.$set(this.points[index], 'qrcode_enabled', false);
                this.$set(this.points[index], 'qrcode_content', null);
                this.$set(this.points[index], 'qrcode_version', null);
                this.$set(this.points[index], 'qrcode_hash_key', null);
                this.$set(this.points[index], 'qrcode_generated_time', null);
                this.$set(this.points[index], 'generated', false);
                this.$set(this.points[index], 'qrcodeContent', null);
              }
              successCount++;
            } else {
              throw new Error(res.message || '关闭失败');
            }
          } catch (e) {

            failCount++;
          }

          // 更新进度
          uni.showLoading({
            title: `处理中(${successCount}/${enabledPoints.length})`,
            mask: true
          });
        }

        uni.hideLoading();
        uni.showModal({
          title: '处理完成',
          content: `成功：${successCount}个\n失败：${failCount}个\n所有二维码数据已清除`,
          showCancel: false
        });
      } catch (e) {

        uni.showToast({
          title: '批量关闭失败',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },
    
    /**
     * @description 保存全部已生成的二维码图片
     */
    async saveAllQRCodes() {
      // 筛选出已生成二维码的点位
      const pointsToSave = this.points.filter(p => p.generated);
      
      // 如果没有可保存的二维码，则提示并返回
      if (pointsToSave.length === 0) {
        uni.showToast({
          title: '没有已生成的二维码可保存',
          icon: 'none'
        });
        return;
      }
      
      // 确认是否开始保存
      const confirmRes = await new Promise(resolve => {
        uni.showModal({
          title: '确认保存',
          content: `即将保存 ${pointsToSave.length} 个二维码图片到系统相册，是否继续？`,
          success: resolve
        });
      });

      if (!confirmRes.confirm) return;

      let successCount = 0;
      let failCount = 0;
      const totalCount = pointsToSave.length;

      // 显示初始进度
      uni.showLoading({ 
        title: `保存中(0/${totalCount})`, 
        mask: true 
      });
      
      // 逐个保存二维码
      for (let i = 0; i < totalCount; i++) {
        const point = pointsToSave[i];
        const currentProgress = i + 1;
        
        // 更新加载提示，显示当前点位名称和进度
        uni.showLoading({ 
          title: `保存 ${currentProgress}/${totalCount}: ${point.name || '未命名点位'}...`, 
          mask: true 
        });

        try {
          // 获取对应的 uqrcode 组件实例
          const qrcodeRef = this.$refs['qrcode-' + point._id];
          if (!qrcodeRef) {
            failCount++;
            continue; // 跳过当前点位
          }
          
          // 确保获取到的是单个组件实例
          const qrcode = Array.isArray(qrcodeRef) ? qrcodeRef[0] : qrcodeRef;
          
          // #ifdef H5
          // H5平台使用原生下载
          await new Promise((resolve) => {
            try {
              // 获取二维码的canvas元素
              const canvas = qrcode.$el.querySelector('canvas');
              if (!canvas) {
                failCount++;
                resolve();
                return;
              }
              
              // 添加延迟，避免浏览器下载限制
              setTimeout(() => {
                // 将canvas转换为blob并下载
                canvas.toBlob((blob) => {
                  if (!blob) {
                    failCount++;
                    resolve();
                    return;
                  }
                  
                  // 创建下载链接
                  const url = URL.createObjectURL(blob);
                  const link = document.createElement('a');
                  link.href = url;
                  link.download = `${point.name || point._id}.png`;
                  
                  // 触发下载
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                  
                  // 清理URL对象
                  setTimeout(() => {
                    URL.revokeObjectURL(url);
                  }, 100);
                  
                  successCount++;
                  resolve();
                }, 'image/png', 1.0);
              }, currentProgress * 100); // 每个下载间隔100ms
              
            } catch (canvasError) {
              // 回退到原始方法
              setTimeout(() => {
                qrcode.save({
                  success: () => {
                    successCount++;
                    resolve();
                  },
                  fail: (err) => {
                    failCount++;
                    resolve();
                  }
                });
              }, currentProgress * 100);
            }
          });
          // #endif
          
          // #ifndef H5
          // 非H5平台使用原始方法
          await new Promise((resolve, reject) => {
            qrcode.save({
              success: () => {
                successCount++;
                resolve();
              },
              fail: (err) => {
                failCount++;
                // 即使失败也 resolve，以便继续处理下一个
                resolve(); 
              }
            });
          });
          // #endif

        } catch (e) {
          // 捕获预料之外的错误
          failCount++;
        }
      }
      
      // 隐藏加载提示
      uni.hideLoading();
      
      // 显示最终结果
      uni.showModal({
        title: '保存完成',
        // 根据成功和失败数量显示不同的提示信息
        content: `共处理 ${totalCount} 个二维码。\n成功保存 ${successCount} 个，失败 ${failCount} 个。`,
        showCancel: false
      });
    }
  }
}
</script>

<style lang="scss">
.qrcode-batch-page {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  position: relative;
}

.point-list {
  flex: 1;
  position: relative;
  padding-bottom: 120rpx; // 为底部按钮留出空间
}

.point-list-content {
  padding: 20rpx;
}

.point-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  position: relative;
  z-index: 1;
  
  &:active {
    transform: scale(0.99);
    transition: transform 0.2s;
  }
}

.point-info {
  margin-bottom: 20rpx;
  
  .point-name {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 12rpx;
    display: block;
  }
  
  .point-detail {
    .point-address {
      font-size: 26rpx;
      color: #666;
      margin-bottom: 8rpx;
      display: block;
      word-break: break-all;
      line-height: 1.4;
    }
    
    .point-coordinates, .point-id {
      font-size: 24rpx;
      color: #999;
      display: block;
      margin-top: 4rpx;
    }
  }
}

.qrcode-preview {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 24rpx 0;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  
  .qrcode-wrapper {
    margin-bottom: 16rpx;
  }
  
  .qrcode-canvas {
    width: 200px !important;
    height: 200px !important;
    background: #fff;
    margin-bottom: 16rpx;
    border-radius: 8rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  }
  
  .qrcode-meta {
    display: flex;
    align-items: center;
    gap: 16rpx;
    
    text {
      font-size: 24rpx;
      background: #fff;
      padding: 4rpx 12rpx;
      border-radius: 4rpx;
    }
    
    .version {
      color: #666;
    }
    
    .status {
      color: #ff9500;
      
      &.status-success {
        color: #34c759;
      }
    }
  }
}

.point-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 20rpx;
  
  button {
    flex: 1;
    min-width: 180rpx;
    margin: 0;
    height: 72rpx;
    font-size: 28rpx;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    color: #fff;
    
    text {
      margin-left: 4rpx;
    }
  }
  
  .btn-generate {
    background: linear-gradient(135deg, #3a86ff, #2563eb);
  }
  
  .btn-regenerate {
    background: linear-gradient(135deg, #ff9500, #ff8000);
  }
  
  .btn-save {
    background: linear-gradient(135deg, #34c759, #2fb344);
  }
  
  .btn-disable {
    background: linear-gradient(135deg, #ff3b30, #ff2d20);
  }
}

.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 24rpx;
  background: #fff;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-around;
  gap: 20rpx;
  z-index: 99;
  
  button {
    flex: 1;
    margin: 0;
    font-size: 28rpx;
    border-radius: 8rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    color: #fff;
    
    text {
      margin-left: 4rpx;
    }
  }
  
  .btn-batch-operation {
    &.btn-generate-all {
      background: linear-gradient(135deg, #3a86ff, #2563eb);
    }
    
    &.btn-disable-all {
      background: linear-gradient(135deg, #ff3b30, #ff2d20);
    }
  }
  
  .btn-save-all {
    background: linear-gradient(135deg, #34c759, #2fb344);
  }
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  color: #666;
  font-size: 26rpx;
  
  text {
    margin-left: 8rpx;
  }
}

.no-more {
  text-align: center;
  padding: 20rpx;
  color: #999;
  font-size: 26rpx;
  background: #f5f5f5;
}

/* #ifdef H5 */
/* 简单修复H5平台弹窗层级问题 */
.uqrcode-h5-save {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 99999 !important;
  background: rgba(0, 0, 0, 0.8) !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
}
/* #endif */
</style>