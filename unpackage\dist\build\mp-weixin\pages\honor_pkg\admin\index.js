(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/honor_pkg/admin/index"],{3787:function(e,t,a){"use strict";var r=a("d8af"),n=a.n(r);n.a},"405d":function(e,t,a){"use strict";(function(e,r){var n=a("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("7eb4")),o=n(a("af34")),c=n(a("7ca3")),s=n(a("ee10"));function u(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function d(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?u(Object(a),!0).forEach((function(t){(0,c.default)(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}var l={name:"HonorAdmin",data:function(){return{loading:!1,refreshing:!1,loadingText:"加载中...",activeModule:null,stats:{totalHonors:0,monthlyHonors:0,featuredHonors:0,activeTypes:0},overviewCards:[{label:"总表彰数",value:0,icon:"medal-filled",color:"#3a86ff"},{label:"本月新增",value:0,icon:"calendar-filled",color:"#10b981"},{label:"精选表彰",value:0,icon:"star-filled",color:"#f59e0b"},{label:"荣誉类型",value:0,icon:"gear-filled",color:"#8b5cf6"}],functionModules:[{id:"records",name:"表彰记录",description:"添加、编辑、管理表彰记录",icon:"medal-filled",color:"#3a86ff",count:"加载中..."},{id:"batch",name:"智能批次",description:"表彰批次与周期管理",icon:"calendar-filled",color:"#10b981",count:"加载中..."},{id:"featured",name:"精选管理",description:"批量设置精选表彰",icon:"star-filled",color:"#f59e0b",count:"加载中..."},{id:"types",name:"荣誉类型",description:"表彰类型配置管理",icon:"gear-filled",color:"#8b5cf6",count:"加载中..."}],recentActivities:[],recordSearch:"",recordList:[],recordPage:1,recordSize:10,recordTotal:0,recordLoading:!1,featuredList:[],selectedFeaturedIds:[],featuredPage:1,featuredSize:15,featuredTotal:0,featuredLoading:!1,featuredSearch:"",scrollDisabled:!1,scrollTop:0,loadingStates:{stats:!0,modules:!0,activities:!1,records:!0,featured:!0},cacheData:{stats:null,statsTime:0,activities:null,activitiesTime:0},cacheExpiry:3e5}},onLoad:function(){var e=this;return(0,s.default)(i.default.mark((function t(){return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.checkPermission();case 2:return t.next=4,e.initializeData();case 4:case"end":return t.stop()}}),t)})))()},methods:{checkPermission:function(){var t=this;return(0,s.default)(i.default.mark((function a(){var n,o,c,s,u,d,l,f;return i.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,n=e.getStorageSync("uni_id_token"),n){a.next=5;break}return e.showModal({title:"权限不足",content:"请先登录系统",showCancel:!1,success:function(){e.navigateBack()}}),a.abrupt("return",!1);case 5:if(o=["admin","supervisor","PM","GM","reviser"],c=o.some((function(e){return t.uniIDHasRole(e)})),!c){a.next=9;break}return a.abrupt("return",!0);case 9:return s=r.database(),a.next=12,s.collection("uni-id-users").where("'_id' == $cloudEnv_uid").field("role").get();case 12:if(u=a.sent,d=u.result,d.data&&0!==d.data.length){a.next=17;break}return e.showModal({title:"权限不足",content:"无法获取用户角色信息",showCancel:!1,success:function(){e.navigateBack()}}),a.abrupt("return",!1);case 17:if(l=d.data[0].role||[],f=l.some((function(e){return o.includes(e)})),f){a.next=22;break}return e.showModal({title:"权限不足",content:"需要管理员权限才能访问此页面",showCancel:!1,success:function(){e.navigateBack()}}),a.abrupt("return",!1);case 22:return a.abrupt("return",!0);case 25:return a.prev=25,a.t0=a["catch"](0),console.error("权限检查异常:",a.t0),e.showModal({title:"权限检查失败",content:"请重新登录后再试",showCancel:!1,success:function(){e.navigateBack()}}),a.abrupt("return",!1);case 30:case"end":return a.stop()}}),a,null,[[0,25]])})))()},initializeData:function(){var t=this;return(0,s.default)(i.default.mark((function a(){var r,n;return i.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return r=Date.now(),a.prev=1,a.next=4,Promise.all([t.loadStats(),t.loadModuleCounts(),t.loadRecentActivities()]);case 4:n=Date.now()-r,t.recordLoadTime("initialization",n),t.preloadData(),a.next=12;break;case 9:a.prev=9,a.t0=a["catch"](1),e.showToast({title:"数据加载失败，请下拉刷新",icon:"none",duration:3e3});case 12:case"end":return a.stop()}}),a,null,[[1,9]])})))()},loadStats:function(){var t=arguments,a=this;return(0,s.default)(i.default.mark((function n(){var o,c,s,u,d,l;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(o=t.length>0&&void 0!==t[0]&&t[0],c=Date.now(),!(!o&&a.cacheData.stats&&c-a.cacheData.statsTime<a.cacheExpiry)){n.next=6;break}return a.updateStatsUI(a.cacheData.stats),a.loadingStates.stats=!1,n.abrupt("return");case 6:return a.loadingStates.stats=!0,n.prev=7,s=Date.now(),n.next=11,r.callFunction({name:"honor-admin",data:{action:"getStatistics"}});case 11:if(u=n.sent,d=Date.now()-s,0!==u.result.code){n.next=21;break}l=u.result.data,a.cacheData.stats=l,a.cacheData.statsTime=c,a.updateStatsUI(l),d>1e3&&e.showToast({title:"数据已更新",icon:"success",duration:1e3}),n.next=22;break;case 21:throw new Error(u.result.message||"获取统计数据失败");case 22:n.next=27;break;case 24:n.prev=24,n.t0=n["catch"](7),a.handleStatsError(n.t0);case 27:return n.prev=27,a.loadingStates.stats=!1,n.finish(27);case 30:case"end":return n.stop()}}),n,null,[[7,24,27,30]])})))()},updateStatsUI:function(e){this.stats={totalHonors:e.totalHonors||0,monthlyHonors:e.currentMonthHonors||0,featuredHonors:e.featuredHonors||0,activeTypes:e.activeTypes||0},this.overviewCards[0].value=this.stats.totalHonors,this.overviewCards[1].value=this.stats.monthlyHonors,this.overviewCards[2].value=this.stats.featuredHonors,this.overviewCards[3].value=this.stats.activeTypes,this.overviewCards[0].trend=this.calculateTrend(e.totalHonors,e.lastMonthTotal),this.overviewCards[1].trend=this.calculateTrend(e.currentMonthHonors,e.lastMonthHonors),this.overviewCards[2].trend=this.calculateTrend(e.featuredHonors,e.lastMonthFeatured),this.overviewCards[3].trend=this.calculateTrend(e.activeTypes,e.lastMonthTypes)},handleStatsError:function(t){this.overviewCards.forEach((function(e,t){e.value="---",e.trend=null})),t.message&&t.message.includes("网络")?e.showToast({title:"网络连接异常",icon:"none",duration:2e3}):e.showToast({title:"数据加载失败",icon:"none",duration:2e3})},loadModuleCounts:function(){var e=this;return(0,s.default)(i.default.mark((function t(){var a,n;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loadingStates.modules=!0,t.prev=1,t.next=4,r.callFunction({name:"honor-admin",data:{action:"getStatistics"}});case 4:a=t.sent,0===a.result.code&&(n=a.result.data,e.functionModules[0].count="".concat(n.totalHonors||0," 条记录"),e.functionModules[1].count="".concat(n.totalBatches||0," 个批次"),e.functionModules[2].count="".concat(n.featuredHonors||0," 条精选"),e.functionModules[3].count="".concat(n.activeTypes||0," 种类型")),t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](1),e.functionModules.forEach((function(e){e.count="加载失败"}));case 11:return t.prev=11,e.loadingStates.modules=!1,t.finish(11);case 14:case"end":return t.stop()}}),t,null,[[1,8,11,14]])})))()},calculateTrend:function(e,t){return t&&0!==t?Math.round((e-t)/t*100):null},preloadData:function(){var e=this;return(0,s.default)(i.default.mark((function t(){var a;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getNetworkType();case 2:if(a=t.sent,"none"!==a){t.next=5;break}return t.abrupt("return");case 5:"wifi"!==a&&"4g"!==a||setTimeout((function(){e.recordList.length||e.loadRecordList()}),2e3);case 6:case"end":return t.stop()}}),t)})))()},getNetworkType:function(){return(0,s.default)(i.default.mark((function t(){return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise((function(t){e.getNetworkType({success:function(e){return t(e.networkType)},fail:function(){return t("unknown")}})})));case 1:case"end":return t.stop()}}),t)})))()},recordLoadTime:function(t,a){var r=e.getStorageSync("admin_performance")||{};r[t]||(r[t]=[]),r[t].push({time:a,timestamp:Date.now()}),r[t].length>10&&(r[t]=r[t].slice(-10)),e.setStorageSync("admin_performance",r);r[t].reduce((function(e,t){return e+t.time}),0),r[t].length},isDataFresh:function(e){return Date.now()-e<this.cacheExpiry},retryOperation:function(e){var t=arguments,a=this;return(0,s.default)(i.default.mark((function r(){var n,o,c;return i.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:n=t.length>1&&void 0!==t[1]?t[1]:3,o=t.length>2&&void 0!==t[2]?t[2]:1e3,c=0;case 3:if(!(c<n)){r.next=19;break}return r.prev=4,r.next=7,e();case 7:return r.abrupt("return",r.sent);case 10:if(r.prev=10,r.t0=r["catch"](4),c!==n-1){r.next=14;break}throw r.t0;case 14:return r.next=16,a.sleep(o*(c+1));case 16:c++,r.next=3;break;case 19:case"end":return r.stop()}}),r,null,[[4,10]])})))()},sleep:function(e){return new Promise((function(t){return setTimeout(t,e)}))},loadRecentActivities:function(){var t=arguments,a=this;return(0,s.default)(i.default.mark((function n(){var o,c,s,u,l;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(o=t.length>0&&void 0!==t[0]&&t[0],c=Date.now(),!(!o&&a.cacheData.activities&&c-a.cacheData.activitiesTime<a.cacheExpiry)){n.next=6;break}return a.recentActivities=a.cacheData.activities,a.loadingStates.activities=!1,n.abrupt("return");case 6:return a.loadingStates.activities=!0,n.prev=7,s=Date.now(),n.next=11,r.callFunction({name:"honor-admin",data:{action:"getRecentActivities",data:{limit:10}}});case 11:if(u=n.sent,Date.now()-s,0!==u.result.code){n.next=20;break}l=(u.result.data||[]).map((function(e){return d(d({},e),{},{icon:a.getOperationIcon(e.title||e.action),color:a.getOperationColor(e.title||e.action)})})),a.cacheData.activities=l,a.cacheData.activitiesTime=c,a.recentActivities=l,n.next=21;break;case 20:throw new Error(u.result.message||"获取最近操作失败");case 21:n.next=27;break;case 23:n.prev=23,n.t0=n["catch"](7),a.recentActivities=[],n.t0.message&&n.t0.message.includes("网络")&&e.showToast({title:"网络异常，请稍后重试",icon:"none",duration:2e3});case 27:return n.prev=27,a.loadingStates.activities=!1,n.finish(27);case 30:case"end":return n.stop()}}),n,null,[[7,23,27,30]])})))()},refreshRecentActivities:function(){var t=this;return(0,s.default)(i.default.mark((function a(){return i.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!t.loadingStates.activities){a.next=2;break}return a.abrupt("return");case 2:return a.prev=2,t.cacheData.activities=null,t.cacheData.activitiesTime=0,a.next=7,t.loadRecentActivities(!0);case 7:e.showToast({title:"最近操作已更新",icon:"success",duration:1500}),a.next=13;break;case 10:a.prev=10,a.t0=a["catch"](2),e.showToast({title:"刷新失败",icon:"none",duration:2e3});case 13:case"end":return a.stop()}}),a,null,[[2,10]])})))()},getOperationIcon:function(e){var t={"添加表彰记录":"plus-filled","创建表彰记录":"plus-filled","编辑表彰记录":"compose","删除表彰记录":"trash-filled","发布批次":"upload-filled","创建批次":"calendar","删除批次":"trash-filled","批量精选":"star-filled","创建荣誉类型":"gear-filled","智能创建":"gear-filled"};for(var a in t)if(e&&e.includes(a.replace(/表彰|记录/g,"")))return t[a];return"plus-filled"},getOperationColor:function(e){var t={"添加":"#10b981","创建":"#10b981","编辑":"#f59e0b","删除":"#ef4444","发布":"#3a86ff","批量":"#8b5cf6","智能":"#06b6d4"};for(var a in t)if(e&&e.includes(a))return t[a];return"#10b981"},onRefresh:function(){var t=this;return(0,s.default)(i.default.mark((function a(){var r;return i.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.refreshing=!0,a.prev=1,t.clearCache(),r=Date.now(),a.next=6,Promise.all([t.loadStats(!0),t.loadModuleCounts(),t.loadRecentActivities(!0)]);case 6:Date.now()-r,e.showToast({title:"刷新成功",icon:"success",duration:1500}),a.next=13;break;case 10:a.prev=10,a.t0=a["catch"](1),e.showToast({title:"刷新失败，请稍后重试",icon:"none",duration:2e3});case 13:return a.prev=13,t.refreshing=!1,a.finish(13);case 16:case"end":return a.stop()}}),a,null,[[1,10,13,16]])})))()},clearCache:function(){this.cacheData={stats:null,statsTime:0,activities:null,activitiesTime:0}},openModule:function(t){var a=this;return(0,s.default)(i.default.mark((function r(){return i.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:r.t0=t.id,r.next="records"===r.t0?3:"batch"===r.t0?8:"featured"===r.t0?10:"types"===r.t0?15:17;break;case 3:return a.activeModule=t.id,a.preventScroll(),r.next=7,a.loadRecordList();case 7:return r.abrupt("break",17);case 8:return e.navigateTo({url:"/pages/honor_pkg/admin/batch-manager"}),r.abrupt("break",17);case 10:return a.activeModule=t.id,a.preventScroll(),r.next=14,a.loadFeaturedList();case 14:return r.abrupt("break",17);case 15:return e.navigateTo({url:"/pages/honor_pkg/admin/type-manager"}),r.abrupt("break",17);case 17:case"end":return r.stop()}}),r)})))()},closeModule:function(){this.activeModule=null,this.allowScroll()},loadRecordList:function(){var e=arguments,t=this;return(0,s.default)(i.default.mark((function a(){var n,c,s,u;return i.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(n=e.length>0&&void 0!==e[0]&&e[0],!t.recordLoading){a.next=3;break}return a.abrupt("return");case 3:return n||(t.loadingStates.records=!0),t.recordLoading=!0,a.prev=5,c=n?t.recordPage+1:1,a.next=9,r.callFunction({name:"honor-admin",data:{action:"getHonorList",data:{page:c,size:t.recordSize,orderBy:"createTime",orderDirection:"desc"}}});case 9:s=a.sent,0===s.result.code&&(u=s.result.data,n?(t.recordList=[].concat((0,o.default)(t.recordList),(0,o.default)(u.list||[])),t.recordPage=c):(t.recordList=u.list||[],t.recordPage=1),t.recordTotal=u.total||0),a.next=15;break;case 13:a.prev=13,a.t0=a["catch"](5);case 15:return a.prev=15,t.recordLoading=!1,n||(t.loadingStates.records=!1),a.finish(15);case 19:case"end":return a.stop()}}),a,null,[[5,13,15,19]])})))()},loadBatchList:function(){},loadFeaturedList:function(){var e=arguments,t=this;return(0,s.default)(i.default.mark((function a(){var n,c,s,u;return i.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(n=e.length>0&&void 0!==e[0]&&e[0],!t.featuredLoading){a.next=3;break}return a.abrupt("return");case 3:return t.featuredLoading=!0,a.prev=4,c=n?t.featuredPage+1:1,a.next=8,r.callFunction({name:"honor-admin",data:{action:"getHonorList",data:{page:c,size:t.featuredSize,orderBy:"createTime",orderDirection:"desc",search:t.featuredSearch.trim()}}});case 8:s=a.sent,0===s.result.code&&(u=s.result.data,n?(t.featuredList=[].concat((0,o.default)(t.featuredList),(0,o.default)(u.list||[])),t.featuredPage=c):(t.featuredList=u.list||[],t.featuredPage=1,t.selectedFeaturedIds=[]),t.featuredTotal=u.total||0),a.next=14;break;case 12:a.prev=12,a.t0=a["catch"](4);case 14:return a.prev=14,t.featuredLoading=!1,t.loadingStates.featured=!1,a.finish(14);case 18:case"end":return a.stop()}}),a,null,[[4,12,14,18]])})))()},loadTypeList:function(){},openAddRecord:function(){e.navigateTo({url:"/pages/honor_pkg/admin/add-record"})},openBatchImport:function(){e.showToast({title:"批量导入功能开发中",icon:"none"})},openBatchFeatured:function(){this.activeModule="featured",this.loadingStates.featured=!0,this.loadFeaturedList()},editRecord:function(t){var a=encodeURIComponent(JSON.stringify({id:t._id,userName:t.userName,department:t.department,userAvatar:t.userAvatar,honorTypeId:t.honorTypeId,batchId:t.batchId,reason:t.reason,isFeatured:t.isFeatured,images:t.images||[]}));e.navigateTo({url:"/pages/honor_pkg/admin/add-record?mode=edit&data=".concat(a)})},deleteRecord:function(t){var a=this;e.showModal({title:"确认删除",content:"确定要删除 ".concat(t.userName," 的表彰记录吗？"),success:function(){var n=(0,s.default)(i.default.mark((function n(o){return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!o.confirm){n.next=11;break}return n.prev=1,n.next=4,r.callFunction({name:"honor-admin",data:{action:"deleteHonor",data:{honorId:t._id}}});case 4:e.showToast({title:"删除成功",icon:"success"}),a.loadRecordList(),n.next=11;break;case 8:n.prev=8,n.t0=n["catch"](1),e.showToast({title:"删除失败",icon:"none"});case 11:case"end":return n.stop()}}),n,null,[[1,8]])})));return function(e){return n.apply(this,arguments)}}()})},searchRecords:function(){var t=this;return(0,s.default)(i.default.mark((function a(){var n;return i.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t.recordSearch.trim()){a.next=4;break}return a.next=3,t.loadRecordList();case 3:return a.abrupt("return");case 4:return a.prev=4,a.next=7,r.callFunction({name:"honor-admin",data:{action:"getHonorList",data:{page:1,size:50,orderBy:"createTime",orderDirection:"desc",search:t.recordSearch.trim()}}});case 7:n=a.sent,0===n.result.code&&(t.recordList=n.result.data.list||[],0===t.recordList.length&&e.showToast({title:"未找到匹配记录",icon:"none"})),a.next=14;break;case 11:a.prev=11,a.t0=a["catch"](4),e.showToast({title:"搜索失败",icon:"none"});case 14:case"end":return a.stop()}}),a,null,[[4,11]])})))()},clearSearch:function(){this.recordSearch="",this.loadRecordList()},loadMoreRecords:function(){this.recordList.length<this.recordTotal&&!this.recordLoading&&this.loadRecordList(!0)},toggleFeaturedSelection:function(e){var t=this.selectedFeaturedIds.indexOf(e);t>-1?this.selectedFeaturedIds.splice(t,1):this.selectedFeaturedIds.push(e)},selectAllFeatured:function(){this.selectedFeaturedIds.length===this.featuredList.length?this.selectedFeaturedIds=[]:this.selectedFeaturedIds=this.featuredList.map((function(e){return e._id}))},batchSetFeatured:function(t){var a=this;return(0,s.default)(i.default.mark((function n(){var o;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(0!==a.selectedFeaturedIds.length){n.next=3;break}return e.showToast({title:"请先选择记录",icon:"none"}),n.abrupt("return");case 3:o=t?"设为精选":"取消精选",e.showModal({title:"批量操作确认",content:"确定要将 ".concat(a.selectedFeaturedIds.length," 条记录").concat(o,"吗？"),success:function(){var n=(0,s.default)(i.default.mark((function n(c){var s;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!c.confirm){n.next=20;break}return n.prev=1,n.next=4,r.callFunction({name:"honor-admin",data:{action:"batchSetFeatured",data:{honorIds:a.selectedFeaturedIds,isFeatured:t}}});case 4:if(s=n.sent,0!==s.result.code){n.next=14;break}return e.showToast({title:"".concat(o,"成功"),icon:"success"}),a.loadingStates.featured=!0,n.next=10,a.loadFeaturedList();case 10:return n.next=12,a.loadStats();case 12:n.next=15;break;case 14:throw new Error(s.result.message||"".concat(o,"失败"));case 15:n.next=20;break;case 17:n.prev=17,n.t0=n["catch"](1),e.showToast({title:n.t0.message||"".concat(o,"失败"),icon:"none"});case 20:case"end":return n.stop()}}),n,null,[[1,17]])})));return function(e){return n.apply(this,arguments)}}()});case 5:case"end":return n.stop()}}),n)})))()},goBack:function(){e.navigateBack({fail:function(){e.redirectTo({url:"/pages/honor_pkg/gallery/index"})}})},formatTime:function(e){try{var t=new Date(e),a=new Date,r=a-t;return r<6e4?"刚刚":r<36e5?"".concat(Math.floor(r/6e4),"分钟前"):r<864e5?"".concat(Math.floor(r/36e5),"小时前"):"".concat(Math.floor(r/864e5),"天前")}catch(n){return"时间未知"}},preventScroll:function(){this.scrollDisabled=!0},allowScroll:function(){this.scrollDisabled=!1},formatDate:function(e){try{var t=new Date(e);return"".concat(t.getFullYear(),"-").concat(String(t.getMonth()+1).padStart(2,"0"),"-").concat(String(t.getDate()).padStart(2,"0"))}catch(a){return"日期未知"}},loadMoreFeatured:function(){this.featuredList.length<this.featuredTotal&&!this.featuredLoading&&this.loadFeaturedList(!0)},searchFeatured:function(){this.loadingStates.featured=!0,this.loadFeaturedList()},clearFeaturedSearch:function(){this.featuredSearch="",this.loadingStates.featured=!0,this.loadFeaturedList()}}};t.default=l}).call(this,a("df3c")["default"],a("861b")["uniCloud"])},b286:function(e,t,a){"use strict";a.r(t);var r=a("405d"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},d8af:function(e,t,a){},e3be:function(e,t,a){"use strict";(function(e,t){var r=a("47a9");a("357b"),a("861b");r(a("3240"));var n=r(a("e7c8"));e.__webpack_require_UNI_MP_PLUGIN__=a,t(n.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},e7c8:function(e,t,a){"use strict";a.r(t);var r=a("fb0e"),n=a("b286");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("3787");var o=a("828b"),c=Object(o["a"])(n["default"],r["b"],r["c"],!1,null,"73814dd0",null,!1,r["a"],void 0);t["default"]=c.exports},fb0e:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return r}));var r={uniIcons:function(){return Promise.all([a.e("common/vendor"),a.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(a.bind(null,"6ddf"))},uniEasyinput:function(){return a.e("uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput").then(a.bind(null,"6cf4"))}},n=function(){var e=this,t=e.$createElement,a=(e._self._c,e.loadingStates.activities?null:e.__map(e.recentActivities,(function(t,a){var r=e.__get_orig(t),n=e.formatTime(t.time);return{$orig:r,m0:n}}))),r=e.loadingStates.activities?null:e.recentActivities.length,n="records"!==e.activeModule||e.loadingStates.records?null:e.__map(e.recordList,(function(t,a){var r=e.__get_orig(t),n=e.formatDate(t.createTime);return{$orig:r,m1:n}})),i="records"!==e.activeModule||e.loadingStates.records?null:e.recordList.length>0&&e.recordList.length<e.recordTotal,o="records"!==e.activeModule||e.loadingStates.records||!i||e.recordLoading?null:e.recordList.length,c="records"!==e.activeModule||e.loadingStates.records?null:e.recordList.length,s="featured"===e.activeModule?e.selectedFeaturedIds.length:null,u="featured"===e.activeModule?e.selectedFeaturedIds.length:null,d="featured"===e.activeModule?e.selectedFeaturedIds.length:null,l="featured"===e.activeModule?e.selectedFeaturedIds.length:null,f="featured"===e.activeModule?e.selectedFeaturedIds.length:null,h="featured"===e.activeModule?e.selectedFeaturedIds.length:null,p="featured"===e.activeModule?e.selectedFeaturedIds.length===e.featuredList.length&&e.featuredList.length>0:null,v="featured"===e.activeModule?e.selectedFeaturedIds.length===e.featuredList.length&&e.featuredList.length>0:null,g="featured"!==e.activeModule||e.loadingStates.featured?null:e.__map(e.featuredList,(function(t,a){var r=e.__get_orig(t),n=e.selectedFeaturedIds.includes(t._id),i=e.selectedFeaturedIds.includes(t._id),o=e.selectedFeaturedIds.includes(t._id),c=e.formatDate(t.createTime);return{$orig:r,g12:n,g13:i,g14:o,m2:c}})),m="featured"!==e.activeModule||e.loadingStates.featured?null:e.featuredList.length>0&&e.featuredList.length<e.featuredTotal,w="featured"!==e.activeModule||e.loadingStates.featured||!m||e.featuredLoading?null:e.featuredList.length,b="featured"!==e.activeModule||e.loadingStates.featured?null:0===e.featuredList.length&&!e.featuredLoading;e._isMounted||(e.e0=function(t){e.refreshing=!1}),e.$mp.data=Object.assign({},{$root:{l0:a,g0:r,l1:n,g1:i,g2:o,g3:c,g4:s,g5:u,g6:d,g7:l,g8:f,g9:h,g10:p,g11:v,l2:g,g15:m,g16:w,g17:b}})},i=[]}},[["e3be","common/runtime","common/vendor"]]]);