<view class="page-container data-v-ca5ce792"><view class="header data-v-ca5ce792"><view class="header-title data-v-ca5ce792">员工责任区分配</view><view class="header-subtitle data-v-ca5ce792">为员工分配固定责任区域</view></view><view class="overview-card data-v-ca5ce792"><view class="overview-title data-v-ca5ce792">分配概览</view><block wx:if="{{loading}}"><view class="stats-loading data-v-ca5ce792"><view class="loading-content data-v-ca5ce792"><uni-icons vue-id="4ab8fe06-1" type="spinner-cycle" size="32" color="#007AFF" class="data-v-ca5ce792" bind:__l="__l"></uni-icons><text class="loading-text data-v-ca5ce792">加载统计数据中...</text></view></view></block><block wx:else><view class="stats-grid data-v-ca5ce792"><view class="stat-item data-v-ca5ce792"><view class="stat-number data-v-ca5ce792">{{totalEmployees}}</view><view class="stat-label data-v-ca5ce792">总员工数</view></view><view class="stat-item data-v-ca5ce792"><view class="stat-number data-v-ca5ce792">{{assignedEmployees}}</view><view class="stat-label data-v-ca5ce792">已分配</view></view><view class="stat-item data-v-ca5ce792"><view class="stat-number data-v-ca5ce792">{{totalAreas}}</view><view class="stat-label data-v-ca5ce792">总责任区</view></view><view class="stat-item data-v-ca5ce792"><view class="stat-number data-v-ca5ce792">{{unassignedAreas}}</view><view class="stat-label data-v-ca5ce792">未分配</view></view></view></block></view><view class="action-bar data-v-ca5ce792"><button data-event-opts="{{[['tap',[['showAssignModal',['$event']]]]]}}" class="action-btn primary data-v-ca5ce792" bindtap="__e"><uni-icons vue-id="4ab8fe06-2" type="plus" size="16" color="#FFFFFF" class="data-v-ca5ce792" bind:__l="__l"></uni-icons><text class="data-v-ca5ce792">新增分配</text></button></view><block wx:if="{{loading}}"><view class="list-loading data-v-ca5ce792"><view class="loading-content data-v-ca5ce792"><uni-icons vue-id="4ab8fe06-3" type="spinner-cycle" size="40" color="#007AFF" class="data-v-ca5ce792" bind:__l="__l"></uni-icons><text class="loading-text data-v-ca5ce792">加载分配数据中...</text></view></view></block><block wx:else><block wx:if="{{$root.g0>0}}"><view class="assignment-list data-v-ca5ce792"><block wx:for="{{$root.l0}}" wx:for-item="assignment" wx:for-index="index" wx:key="index"><view class="assignment-card-modern data-v-ca5ce792"><view class="card-content data-v-ca5ce792"><view class="employee-header data-v-ca5ce792"><view class="employee-name-section data-v-ca5ce792"><view class="employee-icon-wrapper data-v-ca5ce792"><uni-icons vue-id="{{'4ab8fe06-4-'+index}}" type="person-filled" color="#FFFFFF" size="18" class="data-v-ca5ce792" bind:__l="__l"></uni-icons></view><text class="employee-name-large data-v-ca5ce792">{{assignment.$orig.employee.name}}</text><view class="employee-role-chip data-v-ca5ce792">{{assignment.$orig.employee.role||'员工'}}</view></view><view class="assignment-id data-v-ca5ce792">{{"ID: "+assignment.g1}}</view></view><view class="areas-section data-v-ca5ce792"><view class="section-header data-v-ca5ce792"><view class="section-title-row data-v-ca5ce792"><view class="section-title data-v-ca5ce792">责任区域</view><view class="area-count-badge data-v-ca5ce792">{{assignment.g2}}</view></view><view class="{{['assignment-status-modern','data-v-ca5ce792','status-'+assignment.$orig.status]}}">{{''+assignment.m0+''}}</view></view><view class="areas-grid data-v-ca5ce792"><block wx:for="{{assignment.$orig.areas}}" wx:for-item="area" wx:for-index="areaIndex" wx:key="areaIndex"><view data-event-opts="{{[['tap',[['viewAreaDetail',['$0'],[[['assignmentList','',index],['areas','',areaIndex]]]]]]]}}" class="area-tag-modern data-v-ca5ce792" bindtap="__e"><text class="area-name data-v-ca5ce792">{{area.name}}</text></view></block></view></view><view class="card-footer data-v-ca5ce792"><view class="time-info data-v-ca5ce792"><block wx:if="{{assignment.$orig.updated_at&&assignment.$orig.updated_at!==assignment.$orig.assigned_at}}"><text class="time-label data-v-ca5ce792">更新于:</text><text class="time-value data-v-ca5ce792">{{assignment.m1}}</text></block><block wx:else><text class="time-label data-v-ca5ce792">分配于:</text><text class="time-value data-v-ca5ce792">{{assignment.m2}}</text></block></view><view class="action-buttons data-v-ca5ce792"><view class="action-btn edit-btn data-v-ca5ce792" role="button" data-event-opts="{{[['tap',[['editAssignment',['$0'],[[['assignmentList','',index]]]]]]]}}" bindtap="__e"><uni-icons vue-id="{{'4ab8fe06-5-'+index}}" type="compose" size="16" color="#007AFF" class="data-v-ca5ce792" bind:__l="__l"></uni-icons><text class="btn-text data-v-ca5ce792">编辑</text></view><view class="action-btn delete-btn data-v-ca5ce792" role="button" data-event-opts="{{[['tap',[['deleteAssignment',['$0'],[[['assignmentList','',index]]]]]]]}}" bindtap="__e"><uni-icons vue-id="{{'4ab8fe06-6-'+index}}" type="trash" size="16" color="#FF3B30" class="data-v-ca5ce792" bind:__l="__l"></uni-icons><text class="btn-text data-v-ca5ce792">删除</text></view></view></view></view></view></block></view></block><block wx:else><p-empty-state vue-id="4ab8fe06-7" type="assignment" text="暂无分配记录" description="点击上方按钮开始分配责任区" class="data-v-ca5ce792" bind:__l="__l"></p-empty-state></block></block><uni-popup vue-id="4ab8fe06-8" type="center" mask-click="{{false}}" data-ref="assignPopup" class="data-v-ca5ce792 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="assign-popup data-v-ca5ce792"><view class="popup-header data-v-ca5ce792"><view class="popup-title data-v-ca5ce792">{{(isEditing?'编辑':'新增')+"分配"}}</view><button data-event-opts="{{[['tap',[['closeAssignModal',['$event']]]]]}}" class="close-btn data-v-ca5ce792" bindtap="__e"><uni-icons vue-id="{{('4ab8fe06-9')+','+('4ab8fe06-8')}}" type="close" size="20" color="#8E8E93" class="data-v-ca5ce792" bind:__l="__l"></uni-icons></button></view><view class="popup-content data-v-ca5ce792"><view class="form-section data-v-ca5ce792"><view class="section-title data-v-ca5ce792">选择员工</view><view data-event-opts="{{[['tap',[['showEmployeeSelect',['$event']]]]]}}" class="employee-selector data-v-ca5ce792" bindtap="__e"><block wx:if="{{selectedEmployee}}"><view class="selected-employee data-v-ca5ce792"><image class="selected-avatar data-v-ca5ce792" src="{{selectedEmployee.avatar||'/static/user/default-avatar.png'}}" mode="aspectFill"></image><view class="selected-info data-v-ca5ce792"><text class="selected-name data-v-ca5ce792">{{selectedEmployee.name}}</text><text class="selected-role data-v-ca5ce792">{{selectedEmployee.role||'员工'}}</text></view></view></block><block wx:else><view class="placeholder data-v-ca5ce792"><uni-icons vue-id="{{('4ab8fe06-10')+','+('4ab8fe06-8')}}" type="person-filled" size="20" color="#C7C7CC" class="data-v-ca5ce792" bind:__l="__l"></uni-icons><text class="data-v-ca5ce792">点击选择员工</text></view></block><uni-icons vue-id="{{('4ab8fe06-11')+','+('4ab8fe06-8')}}" type="right" size="16" color="#C7C7CC" class="data-v-ca5ce792" bind:__l="__l"></uni-icons></view></view><view class="form-section data-v-ca5ce792"><view class="section-title data-v-ca5ce792">分配责任区</view><view class="areas-selector data-v-ca5ce792"><block wx:for="{{$root.l1}}" wx:for-item="area" wx:for-index="areaIndex" wx:key="areaIndex"><view data-event-opts="{{[['tap',[['toggleAreaSelection',['$0'],[[['availableAreas','',areaIndex]]]]]]]}}" class="{{['area-option','data-v-ca5ce792',(area.m3)?'selected':'']}}" bindtap="__e"><view class="area-option-main data-v-ca5ce792"><view class="area-option-name data-v-ca5ce792">{{area.$orig.name}}</view><view class="area-option-location data-v-ca5ce792">{{area.$orig.location&&area.$orig.location.area||area.$orig.location||'未设置位置'}}</view></view><block wx:if="{{area.m4}}"><view class="area-option-check data-v-ca5ce792"><uni-icons vue-id="{{('4ab8fe06-12-'+areaIndex)+','+('4ab8fe06-8')}}" type="checkmarkempty" size="18" color="#007AFF" class="data-v-ca5ce792" bind:__l="__l"></uni-icons></view></block></view></block></view></view><view class="form-section data-v-ca5ce792"><view class="section-title data-v-ca5ce792">分配说明</view><textarea class="assign-note data-v-ca5ce792" placeholder="请输入分配说明（可选）" maxlength="200" data-event-opts="{{[['input',[['__set_model',['','assignmentNote','$event',[]]]]]]}}" value="{{assignmentNote}}" bindinput="__e"></textarea></view></view><view class="popup-footer data-v-ca5ce792"><button data-event-opts="{{[['tap',[['closeAssignModal',['$event']]]]]}}" class="popup-btn cancel data-v-ca5ce792" bindtap="__e">取消</button><button class="popup-btn submit data-v-ca5ce792" loading="{{saving}}" data-event-opts="{{[['tap',[['submitAssignment',['$event']]]]]}}" bindtap="__e">{{''+(isEditing?'保存':'分配')+''}}</button></view></view></uni-popup><uni-popup vue-id="4ab8fe06-13" type="center" data-ref="employeeSelectPopup" class="data-v-ca5ce792 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="employee-select-popup data-v-ca5ce792"><view class="select-header data-v-ca5ce792"><text class="select-title data-v-ca5ce792">选择员工</text><button data-event-opts="{{[['tap',[['closeEmployeeSelect',['$event']]]]]}}" class="close-btn data-v-ca5ce792" bindtap="__e"><uni-icons vue-id="{{('4ab8fe06-14')+','+('4ab8fe06-13')}}" type="close" size="20" color="#8E8E93" class="data-v-ca5ce792" bind:__l="__l"></uni-icons></button></view><view class="search-bar data-v-ca5ce792"><input class="search-input data-v-ca5ce792" type="text" placeholder="搜索员工姓名" data-event-opts="{{[['input',[['__set_model',['','searchKeyword','$event',[]]],['filterEmployees',['$event']]]]]}}" value="{{searchKeyword}}" bindinput="__e"/><uni-icons vue-id="{{('4ab8fe06-15')+','+('4ab8fe06-13')}}" type="search" size="18" color="#8E8E93" class="data-v-ca5ce792" bind:__l="__l"></uni-icons></view><scroll-view class="employee-list data-v-ca5ce792" scroll-y="{{true}}"><block wx:for="{{filteredEmployees}}" wx:for-item="employee" wx:for-index="empIndex" wx:key="empIndex"><view data-event-opts="{{[['tap',[['selectEmployee',['$0'],[[['filteredEmployees','',empIndex]]]]]]]}}" class="{{['employee-option','data-v-ca5ce792',(selectedEmployee&&selectedEmployee.id===employee.id)?'selected':'']}}" bindtap="__e"><image class="option-avatar data-v-ca5ce792" src="{{employee.avatar||'/static/user/default-avatar.png'}}" mode="aspectFill"></image><view class="option-info data-v-ca5ce792"><text class="option-name data-v-ca5ce792">{{employee.name}}</text><text class="option-role data-v-ca5ce792">{{employee.role||'员工'}}</text></view><block wx:if="{{selectedEmployee&&selectedEmployee.id===employee.id}}"><view class="option-check data-v-ca5ce792"><uni-icons vue-id="{{('4ab8fe06-16-'+empIndex)+','+('4ab8fe06-13')}}" type="checkmarkempty" size="18" color="#007AFF" class="data-v-ca5ce792" bind:__l="__l"></uni-icons></view></block></view></block></scroll-view></view></uni-popup></view>