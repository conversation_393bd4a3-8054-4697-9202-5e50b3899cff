<view class="page-container data-v-1bc1a9f8"><view class="stats-section data-v-1bc1a9f8"><view class="stats-card data-v-1bc1a9f8"><view class="stats-item data-v-1bc1a9f8"><view class="stats-number data-v-1bc1a9f8">{{publishedCount}}</view><view class="stats-label data-v-1bc1a9f8">已发布</view></view><view class="stats-item data-v-1bc1a9f8"><view class="stats-number data-v-1bc1a9f8">{{draftCount}}</view><view class="stats-label data-v-1bc1a9f8">草稿</view></view><view class="stats-item data-v-1bc1a9f8"><view class="stats-number data-v-1bc1a9f8">{{totalCount}}</view><view class="stats-label data-v-1bc1a9f8">总计</view></view></view></view><view class="tab-section data-v-1bc1a9f8"><view class="tab-container data-v-1bc1a9f8"><view data-event-opts="{{[['tap',[['switchTab',['published']]]]]}}" class="{{['tab-item','data-v-1bc1a9f8',(currentTab==='published')?'active':'']}}" bindtap="__e"><text class="data-v-1bc1a9f8">{{"已发布 ("+publishedCount+")"}}</text></view><view data-event-opts="{{[['tap',[['switchTab',['draft']]]]]}}" class="{{['tab-item','data-v-1bc1a9f8',(currentTab==='draft')?'active':'']}}" bindtap="__e"><text class="data-v-1bc1a9f8">{{"草稿 ("+draftCount+")"}}</text></view></view></view><view class="list-section data-v-1bc1a9f8"><block wx:if="{{loading}}"><view class="loading-container data-v-1bc1a9f8"><uni-icons vue-id="b685da90-1" type="spinner-cycle" size="32" color="#007AFF" class="data-v-1bc1a9f8" bind:__l="__l"></uni-icons><text class="loading-text data-v-1bc1a9f8">加载中...</text></view></block><block wx:else><block wx:if="{{$root.g0>0}}"><block wx:for="{{$root.l0}}" wx:for-item="issue" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['handleItemClick',['$0'],[[['currentList','',index]]]]]]]}}" class="issue-item data-v-1bc1a9f8" bindtap="__e"><view class="issue-header data-v-1bc1a9f8"><view class="issue-title-section data-v-1bc1a9f8"><block wx:if="{{issue.$orig.number}}"><view class="issue-number data-v-1bc1a9f8">{{"#"+issue.m0}}</view></block><view class="issue-title data-v-1bc1a9f8">{{issue.$orig.title}}</view></view><view class="{{['issue-status','data-v-1bc1a9f8','status-'+issue.$orig.status]}}">{{''+issue.m1+''}}</view></view><view class="issue-content data-v-1bc1a9f8"><view class="issue-info data-v-1bc1a9f8"><view class="info-item data-v-1bc1a9f8"><uni-icons vue-id="{{'b685da90-2-'+index}}" type="location" size="14" color="#8E8E93" class="data-v-1bc1a9f8" bind:__l="__l"></uni-icons><text class="data-v-1bc1a9f8">{{issue.$orig.location||'未设置'}}</text></view><view class="info-item data-v-1bc1a9f8"><uni-icons vue-id="{{'b685da90-3-'+index}}" type="person" size="14" color="#8E8E93" class="data-v-1bc1a9f8" bind:__l="__l"></uni-icons><text class="data-v-1bc1a9f8">{{issue.$orig.responsible||'未指定'}}</text></view></view><view class="issue-time data-v-1bc1a9f8">{{''+issue.m2+''}}</view></view><block wx:if="{{issue.$orig.status==='draft'}}"><view class="issue-actions data-v-1bc1a9f8"><view data-event-opts="{{[['tap',[['editDraft',['$0'],[[['currentList','',index]]]]]]]}}" class="action-btn edit data-v-1bc1a9f8" catchtap="__e"><uni-icons vue-id="{{'b685da90-4-'+index}}" type="compose" size="14" color="#007AFF" class="data-v-1bc1a9f8" bind:__l="__l"></uni-icons><text class="data-v-1bc1a9f8">编辑</text></view><view data-event-opts="{{[['tap',[['publishDraft',['$0'],[[['currentList','',index]]]]]]]}}" class="action-btn publish data-v-1bc1a9f8" catchtap="__e"><uni-icons vue-id="{{'b685da90-5-'+index}}" type="upload" size="14" color="#34C759" class="data-v-1bc1a9f8" bind:__l="__l"></uni-icons><text class="data-v-1bc1a9f8">发布</text></view><view data-event-opts="{{[['tap',[['deleteDraft',['$0'],[[['currentList','',index]]]]]]]}}" class="action-btn delete data-v-1bc1a9f8" catchtap="__e"><uni-icons vue-id="{{'b685da90-6-'+index}}" type="trash" size="14" color="#FF3B30" class="data-v-1bc1a9f8" bind:__l="__l"></uni-icons><text class="data-v-1bc1a9f8">删除</text></view></view></block><block wx:else><view class="issue-actions data-v-1bc1a9f8"><view data-event-opts="{{[['tap',[['viewIssueDetail',['$0'],[[['currentList','',index]]]]]]]}}" class="action-btn view data-v-1bc1a9f8" catchtap="__e"><uni-icons vue-id="{{'b685da90-7-'+index}}" type="eye" size="14" color="#007AFF" class="data-v-1bc1a9f8" bind:__l="__l"></uni-icons><text class="data-v-1bc1a9f8">查看详情</text></view><block wx:if="{{issue.$orig.status!=='approved'}}"><view data-event-opts="{{[['tap',[['editPublishedIssue',['$0'],[[['currentList','',index]]]]]]]}}" class="action-btn edit data-v-1bc1a9f8" catchtap="__e"><uni-icons vue-id="{{'b685da90-8-'+index}}" type="compose" size="14" color="#34C759" class="data-v-1bc1a9f8" bind:__l="__l"></uni-icons><text class="data-v-1bc1a9f8">编辑</text></view></block><view data-event-opts="{{[['tap',[['changeIssueStatus',['$0'],[[['currentList','',index]]]]]]]}}" class="action-btn status data-v-1bc1a9f8" catchtap="__e"><uni-icons vue-id="{{'b685da90-9-'+index}}" type="loop" size="14" color="#FF9500" class="data-v-1bc1a9f8" bind:__l="__l"></uni-icons><text class="data-v-1bc1a9f8">{{issue.$orig.status==='approved'?'重新打开':'状态'}}</text></view></view></block></view></block></block><block wx:else><p-empty-state vue-id="b685da90-10" type="data" text="{{currentTab==='draft'?'暂无草稿':'暂无已发布问题'}}" description="{{currentTab==='draft'?'在问题录入时点击保存草稿即可保存':'点击右下角按钮新增问题'}}" class="data-v-1bc1a9f8" bind:__l="__l"></p-empty-state></block></block></view><view class="fab-container data-v-1bc1a9f8"><view data-event-opts="{{[['tap',[['addIssue',['$event']]]]]}}" class="fab-btn data-v-1bc1a9f8" bindtap="__e"><uni-icons vue-id="b685da90-11" type="plus" size="32" color="#ffffff" class="data-v-1bc1a9f8" bind:__l="__l"></uni-icons></view></view></view>