{"version": 3, "sources": ["webpack:///D:/Xwzc/components/patrol/p-task-card.vue?adba", "webpack:///D:/Xwzc/components/patrol/p-task-card.vue?1e88", "webpack:///D:/Xwzc/components/patrol/p-task-card.vue?5621", "webpack:///D:/Xwzc/components/patrol/p-task-card.vue?f411", "uni-app:///components/patrol/p-task-card.vue", "webpack:///D:/Xwzc/components/patrol/p-task-card.vue?895a", "webpack:///D:/Xwzc/components/patrol/p-task-card.vue?07ce"], "names": ["name", "props", "task", "type", "required", "userMap", "default", "shift", "roundInfo", "active", "selectedRoundNumber", "data", "countdownTimer", "currentTime", "refreshedRounds", "countdownCache", "eventThrottle", "lastEmitTime", "computed", "statusClass", "statusText", "isActive", "isExpired", "isCompleted", "canContinue", "canStartPatrol", "completedPoints", "totalPoints", "progressRate", "isAcrossDay", "hours", "minutes", "hasRounds", "sortedRounds", "hasActiveRound", "methods", "formatProgress", "progress", "onRoundClick", "round", "index", "taskId", "isRoundSelected", "getRoundStatusText", "getCurrentActiveRound", "getNextRound", "getTotalMissedPoints", "onCardClick", "onContinueTask", "selectedRound", "onViewDetail", "getCurrentRoundNumber", "getTaskStatusText", "getUserName", "getRoundTimeRemaining", "now", "result", "console", "value", "timestamp", "getRoundTimeUntilStart", "parseISOTime", "year", "month", "day", "hour", "minute", "second", "millisecond", "parseInt", "formatSimpleCountdown", "getCurrentRoundMissedPoints", "hasRealCheckInData", "record", "isPointCompleted", "getNextRoundNumber", "startCountdownTimer", "stopCountdownTimer", "clearInterval", "throttledEmit", "uni", "setTimeout", "checkCountdownExpired", "needRefresh", "expiredRounds", "reason", "details", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "onShow", "onHide"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9KA;AAAA;AAAA;AAAA;AAAqmB,CAAgB,+nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACyUznB;AAAA;AAAA;AAAA;AAAA,gBAEA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAG;QAAA;MAAA;IACA;IACAC;MACAJ;MACAG;IACA;IACAE;MACAL;MACAG;IACA;IACAG;MACAN;MACAG;IACA;IACA;IACAI;MACAP;MACAG;IACA;EACA;EACAK;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;QACA;UACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;UACA;QACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;UACA;UACA;UACA;;UAEA;UACA;YACA;YACA;cAAA;cAAAC;cAAAC;YACA;UACA;;UAEA;UACA;;UAEA;UACA;QACA;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA,oBACA,2BACA;QAAA;MAAA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACAC;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;QACA;QACA;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MACA;;MAEA;MACA;QACA9C;QACA+C,iDACA;UAAA;QAAA,KACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA;;MAEA;MACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MAEA;MACA;;MAEA;MACA,qCACAC;QACA;MACA;MAEA;MAEA;QACA;QACA;UACA;UACA;YACAC;UACA;QACA;QACA;QAAA,KACA;UACA;UACA;UACA;UACA;UACA;UACA;UAEA;YACAA;UACA;QACA;MACA;QACAC;MACA;;MAEA;MACA;QACAC;QACAC;MACA;MAEA;IACA;IAEA;IACAC;MACA;MAEA;MACA;;MAEA;MACA,qCACAL;QACA;MACA;MAEA;MAEA;QACA;QACA;UACA;UACA;YACAC;UACA;QACA;QACA;QAAA,KACA;UACA;UACA;UACA;UACA;UAEA;YACAA;UACA;QACA;MACA;QACAC;MACA;;MAEA;MACA;QACAC;QACAC;MACA;MAEA;IACA;IAEA;IACAE;MACA;MAEA;QACA;QACA;QACA;UACA;YAAAC;YAAAC;YAAAC;YAAAC;YAAAC;YAAAC;YAAAC;UACA;UACA,yBACAC,gBACAA;UAAA;UACAA,eACAA,gBACAA,kBACAA,kBACAA,2BACA;QACA;;QAEA;QACA;QACA;MACA;QACAZ;QACA;MACA;IACA;IAEA;IACAa;MACA;MAEA;QACA;QACA;QACA;QACA;;QAEA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;QACAb;QACA;MACA;IACA;IAEA;IACAc;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;UAAA,OACAC;QAAA,EACA;QACA;MACA;;MAEA;MACA;QAAA,2CACA;UAAA;QAAA;UAAA;YAAA;YACA;cACA;cACA;gBACA;cACA;YACA;UACA;QAAA;UAAA;QAAA;UAAA;QAAA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEA;MACA;;MAEA;MACA;QAAA;MAAA;MACA;;MAEA;MACA;MACA;MACA;MAEA;IACA;IAEAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;;MAEA;MACA;QACA;;QAEA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACAC;QACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;MACA;MACA;MAEA;QACAtB;QACA;MACA;MAEA;MACA;;MAEA;MACA;QACA;QACA;UACA;UACAuB;UAEAC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEA;MACA;MAEA;QACA;QAEA;;QAEA;QACA;UACA;UACA;YACAC;YACAC;YACA;UACA;QACA;;QAEA;QACA;UACA;UACA;YACAD;YACAC;YACA;UACA;QACA;MACA;;MAEA;MACA;QACA3B;;QAEA;QACA;UACAhB;UACAvC;UACAkF;QACA;;QAEA;QACA;UACA3C;UACA4C;UACAC;QACA;;QAEA;QACA;UACA;UACA;YACA;cACA7C;cACA2C;YACA;UACA;;UAEA;UACA;YACAJ;cACAvC;cACA2C;YACA;;YAEA;YACAJ;UACA;QACA;;QAEA;QACAC;UACAG;YAAA;YACA;YACA;cACA;cACA;cACA;YACA;UACA;QACA;MACA;IACA;EACA;EACAG;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACA;EACAC;IACA;EACA;EAEAC;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACzgCA;AAAA;AAAA;AAAA;AAAgpC,CAAgB,snCAAG,EAAC,C;;;;;;;;;;;ACApqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/patrol/p-task-card.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./p-task-card.vue?vue&type=template&id=23915e73&\"\nvar renderjs\nimport script from \"./p-task-card.vue?vue&type=script&lang=js&\"\nexport * from \"./p-task-card.vue?vue&type=script&lang=js&\"\nimport style0 from \"./p-task-card.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/patrol/p-task-card.vue\"\nexport default component.exports", "export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./p-task-card.vue?vue&type=template&id=23915e73&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getUserName()\n  var m1 =\n    _vm.task.patrol_date || _vm.formatDate(_vm.task.create_date, \"YYYY-MM-DD\")\n  var g0 = _vm.hasRounds\n    ? _vm.task.rounds_detail && _vm.task.rounds_detail.length > 0\n    : null\n  var g1 = _vm.hasRounds && g0 ? _vm.task.rounds_detail.length : null\n  var m2 =\n    _vm.hasRounds && !(_vm.task.status === 2)\n      ? _vm.getCurrentActiveRound()\n      : null\n  var m3 =\n    _vm.hasRounds && !(_vm.task.status === 2) && m2\n      ? _vm.getCurrentActiveRound().round || \"?\"\n      : null\n  var m4 =\n    _vm.hasRounds && !(_vm.task.status === 2) && !m2 ? _vm.getNextRound() : null\n  var m5 =\n    _vm.hasRounds && !(_vm.task.status === 2) && !m2 && m4\n      ? _vm.getNextRound().round || \"?\"\n      : null\n  var g2 = _vm.hasRounds\n    ? !_vm.task.rounds_detail || _vm.task.rounds_detail.length === 0\n    : null\n  var l0 =\n    _vm.hasRounds && !g2\n      ? _vm.__map(_vm.sortedRounds, function (round, index) {\n          var $orig = _vm.__get_orig(round)\n          var m6 = _vm.isRoundSelected(round)\n          var m7 = _vm.getRoundStatusText(round)\n          var m8 =\n            round.status === 1\n              ? _vm.formatSimpleCountdown(_vm.getRoundTimeRemaining(round))\n              : null\n          var m9 =\n            round.status === 0\n              ? _vm.formatSimpleCountdown(_vm.getRoundTimeUntilStart(round))\n              : null\n          return {\n            $orig: $orig,\n            m6: m6,\n            m7: m7,\n            m8: m8,\n            m9: m9,\n          }\n        })\n      : null\n  var m10 =\n    _vm.hasRounds && _vm.task.status === 2 ? _vm.getTotalMissedPoints() : null\n  var m11 =\n    _vm.hasRounds && _vm.task.status === 2 && m10 > 0\n      ? _vm.getTotalMissedPoints()\n      : null\n  var m12 =\n    _vm.hasRounds &&\n    !(_vm.task.status === 2) &&\n    !(_vm.task.status === 4) &&\n    _vm.task.status === 3\n      ? _vm.getTotalMissedPoints()\n      : null\n  var m13 =\n    _vm.hasRounds &&\n    !(_vm.task.status === 2) &&\n    !(_vm.task.status === 4) &&\n    _vm.task.status === 3 &&\n    m12 > 0\n      ? _vm.getTotalMissedPoints()\n      : null\n  var m14 =\n    _vm.hasRounds &&\n    !(_vm.task.status === 2) &&\n    !(_vm.task.status === 4) &&\n    !(_vm.task.status === 3) &&\n    _vm.task.status === 1 &&\n    _vm.hasActiveRound\n      ? _vm.getCurrentRoundNumber()\n      : null\n  var m15 =\n    _vm.hasRounds &&\n    !(_vm.task.status === 2) &&\n    !(_vm.task.status === 4) &&\n    !(_vm.task.status === 3) &&\n    _vm.task.status === 1 &&\n    _vm.hasActiveRound\n      ? _vm.getCurrentRoundMissedPoints()\n      : null\n  var m16 =\n    _vm.hasRounds &&\n    !(_vm.task.status === 2) &&\n    !(_vm.task.status === 4) &&\n    !(_vm.task.status === 3) &&\n    _vm.task.status === 1 &&\n    _vm.hasActiveRound &&\n    m15 > 0\n      ? _vm.getCurrentRoundMissedPoints()\n      : null\n  var m17 =\n    _vm.hasRounds &&\n    !(_vm.task.status === 2) &&\n    !(_vm.task.status === 4) &&\n    !(_vm.task.status === 3) &&\n    !(_vm.task.status === 1 && _vm.hasActiveRound)\n      ? _vm.getNextRoundNumber()\n      : null\n  var m18 = _vm.formatProgress(_vm.progressRate)\n  var m19 =\n    _vm.canContinue && _vm.canStartPatrol ? _vm.getCurrentActiveRound() : null\n  var m20 =\n    !(_vm.canContinue && _vm.canStartPatrol) &&\n    _vm.canContinue &&\n    !_vm.canStartPatrol\n      ? _vm.getCurrentActiveRound()\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        g1: g1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        g2: g2,\n        l0: l0,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./p-task-card.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./p-task-card.vue?vue&type=script&lang=js&\"", "<template>\n  <view \n    class=\"p-task-card\" \n    :class=\"{\n      'p-task-card--active': isActive, \n      'p-task-card--expired': isExpired,\n      'p-task-card--completed': isCompleted,\n      'p-task-card--selected': active\n    }\"\n    :active=\"active ? true : false\"\n    @click=\"onCardClick\"\n  >\n    <!-- 状态标识和标题 -->\n    <view class=\"p-task-card__header\">\n      <view class=\"p-task-card__title-container\">\n        <text class=\"p-task-card__title\">{{ task.name || task.route_name || '未命名任务' }}</text>\n      </view>\n      \n      <!-- 操作按钮 - 置于右上角，提高点击区域 -->\n      <view class=\"p-task-card__quick-actions\">\n        <text class=\"p-task-card__status\" :class=\"{\n          'p-task-card__status--pending': statusClass === 'pending',\n          'p-task-card__status--active': statusClass === 'active',\n          'p-task-card__status--completed': statusClass === 'completed',\n          'p-task-card__status--expired': statusClass === 'expired',\n          'p-task-card__status--canceled': statusClass === 'canceled'\n        }\">\n          {{ statusText }}\n        </text>\n        <view \n          v-if=\"task.status !== 4\" \n          class=\"p-task-card__view-btn\" \n          @click.stop=\"onViewDetail\"\n        >\n          <uni-icons type=\"info\" size=\"18\" color=\"#1677FF\"></uni-icons>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 班次和时间信息 - 使用图标提升视觉识别 -->\n    <view class=\"p-task-card__info-group\">\n      <!-- 第一行：区域/路线信息和班次信息 -->\n      <view class=\"p-task-card__info-row\">\n        <!-- 区域/路线信息 -->\n        <view class=\"p-task-card__info-item\">\n          <view class=\"p-task-card__info-icon\">\n            <uni-icons type=\"location-filled\" size=\"20\" color=\"#1677FF\"></uni-icons>\n          </view>\n          <view class=\"p-task-card__info-content\">\n          <text class=\"p-task-card__label\">线路区域:</text>\n          <text class=\"p-task-card__value\">{{ task.area || task.route_name || '未指定区域' }}</text>\n          </view>\n        </view>\n        \n        <!-- 班次信息 -->\n        <view class=\"p-task-card__info-item\">\n          <view class=\"p-task-card__info-icon\">\n            <uni-icons type=\"flag-filled\" size=\"20\" color=\"#1677FF\"></uni-icons>\n          </view>\n          <view class=\"p-task-card__info-content\">\n          <text class=\"p-task-card__label\">执行班次:</text>\n          <text class=\"p-task-card__value\">\n            {{ task.shift_name || (shift ? shift.name : '未指定班次') }}\n            <text v-if=\"isAcrossDay\" class=\"p-task-card__tag\">跨天</text>\n          </text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 第二行：执行人员和执行时间 -->\n      <view class=\"p-task-card__info-row\">\n        <!-- 执行人员信息 -->\n        <view class=\"p-task-card__info-item\">\n          <view class=\"p-task-card__info-icon\">\n            <uni-icons type=\"person-filled\" size=\"20\" color=\"#1677FF\"></uni-icons>\n          </view>\n          <view class=\"p-task-card__info-content\">\n          <text class=\"p-task-card__label\">执行人员:</text>\n          <text class=\"p-task-card__value\">{{ getUserName() }}</text>\n          </view>\n        </view>\n        \n        <!-- 执行时间信息 -->\n        <view class=\"p-task-card__info-item\">\n          <view class=\"p-task-card__info-icon\">\n            <uni-icons type=\"calendar-filled\" size=\"20\" color=\"#1677FF\"></uni-icons>\n          </view>\n          <view class=\"p-task-card__info-content\">\n          <text class=\"p-task-card__label\">执行时间:</text>\n            <text class=\"p-task-card__value\">{{ task.patrol_date || formatDate(task.create_date, 'YYYY-MM-DD') }}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 信息组与轮次信息之间的分隔线 -->\n    <view class=\"p-task-card__divider\"></view>\n    \n    <!-- 轮次信息 - 突出显示，增加未打卡统计 -->\n    <view v-if=\"hasRounds\" class=\"p-task-card__round-info\">\n      <view class=\"p-task-card__round-header\">\n        <uni-icons type=\"loop\" size=\"18\" color=\"#1677FF\"></uni-icons>\n        <text class=\"p-task-card__round-title\">轮次信息</text>\n      </view>\n      \n      <!-- 轮次概要信息 -->\n      <view class=\"p-task-card__round-summary\">\n        <text class=\"p-task-card__round-summary-text\">\n          共{{ task.rounds_detail && task.rounds_detail.length > 0 ? task.rounds_detail.length : 0 }}轮巡检\n        </text>\n        <text v-if=\"task.status === 2\" class=\"p-task-card__round-summary-text p-task-card__round-current--completed\">\n          所有轮次已完成\n        </text>\n        <text v-else-if=\"getCurrentActiveRound()\" class=\"p-task-card__round-current\">\n          当前第{{ getCurrentActiveRound().round || '?' }}轮\n        </text>\n        <text v-else-if=\"getNextRound()\" class=\"p-task-card__round-current\">\n          等待第{{ getNextRound().round || '?' }}轮开始\n        </text>\n      </view>\n      \n      <!-- 所有轮次状态列表 -->\n      <view class=\"p-task-card__rounds-list\">\n        <!-- 如果没有轮次数据，手动创建空白提示 -->\n        <template v-if=\"!task.rounds_detail || task.rounds_detail.length === 0\">\n          <view class=\"p-task-card__round-item\">\n            <text class=\"p-task-card__empty-rounds\">暂无轮次数据</text>\n          </view>\n        </template>\n        \n        <!-- 如果有轮次数据，正常展示 -->\n        <template v-else>\n          <view \n            v-for=\"(round, index) in sortedRounds\" \n            :key=\"index\" \n            class=\"p-task-card__round-item\"\n            :class=\"{\n              'p-task-card__round-item--active': round.status === 1,\n              'p-task-card__round-item--completed': round.status === 2 || task.status === 2,\n              'p-task-card__round-item--expired': round.status === 3,\n              'p-task-card__round-item--waiting': round.status === 0,\n              'p-task-card__round-item--selected': isRoundSelected(round)\n            }\"\n            @click.stop=\"onRoundClick(round, index)\"\n          >\n            <view class=\"p-task-card__round-item-header\">\n              <text class=\"p-task-card__round-number\">{{ round.name || `第${round.round}轮` }}</text>\n              <text v-if=\"round.stats && round.stats.missed_points > 0\" class=\"p-task-card__round-number-missed\">缺{{ round.stats.missed_points }}张打卡</text>\n              <text \n                class=\"p-task-card__round-status-badge\"\n                :class=\"{\n                  'p-task-card__round-status-badge--active': round.status === 1,\n                  'p-task-card__round-status-badge--completed': round.status === 2 || task.status === 2,\n                  'p-task-card__round-status-badge--expired': round.status === 3,\n                  'p-task-card__round-status-badge--waiting': round.status === 0\n                }\"\n              >{{ getRoundStatusText(round) }}</text>\n            </view>\n            \n            <view class=\"p-task-card__round-item-time\">\n              <!-- 倒计时显示 -->\n              <view v-if=\"round.status === 1\" class=\"p-task-card__round-countdown\">\n                <text>剩余{{ formatSimpleCountdown(getRoundTimeRemaining(round)) }}</text>\n              </view>\n              \n              <!-- 等待开始倒计时 -->\n              <view v-if=\"round.status === 0\" class=\"p-task-card__round-countdown\">\n                <text>{{ formatSimpleCountdown(getRoundTimeUntilStart(round)) }}后开始</text>\n              </view>\n            </view>\n          </view>\n        </template>\n      </view>\n      \n      <!-- 实时状态 -->\n      <view class=\"p-task-card__round-status\">\n        <!-- 任务已完成 -->\n        <view v-if=\"task.status === 2\" class=\"p-task-card__completed-rounds\">\n          <view class=\"p-task-card__status-row\">\n            <text class=\"p-task-card__round-badge\" :class=\"{\n              'p-task-card__round-badge--completed': true\n            }\">\n              所有轮次已完成\n            </text>\n            \n            <!-- 完成情况摘要 -->\n            <text v-if=\"getTotalMissedPoints() > 0\" class=\"p-task-card__missed-badge\">\n              共缺{{ getTotalMissedPoints() }}张打卡\n            </text>\n          </view>\n        </view>\n        \n        <!-- 任务已取消 -->\n        <view v-else-if=\"task.status === 4\" class=\"p-task-card__canceled-rounds\">\n          <view class=\"p-task-card__status-row\">\n            <text class=\"p-task-card__round-badge\" :class=\"{\n              'p-task-card__round-badge--canceled': true\n            }\">\n              任务已取消\n            </text>\n          </view>\n        </view>\n        \n        <!-- 任务已超时 -->\n        <view v-else-if=\"task.status === 3\" class=\"p-task-card__expired-rounds\">\n          <view class=\"p-task-card__status-row\">\n            <text class=\"p-task-card__round-badge\" :class=\"{\n              'p-task-card__round-badge--expired': true\n            }\">\n              任务已超时\n            </text>\n            <text v-if=\"getTotalMissedPoints() > 0\" class=\"p-task-card__missed-badge\">\n              共缺{{ getTotalMissedPoints() }}张打卡\n            </text>\n          </view>\n        </view>\n        \n        <!-- 进行中且有活跃轮次 -->\n        <view v-else-if=\"task.status === 1 && hasActiveRound\" class=\"p-task-card__active-task\">\n          <view class=\"p-task-card__status-row\">\n            <text class=\"p-task-card__round-badge\" :class=\"{\n              'p-task-card__round-badge--active': true\n            }\">\n              正在进行第{{ getCurrentRoundNumber() }}轮巡检\n            </text>\n            \n            <text v-if=\"getCurrentRoundMissedPoints() > 0\" class=\"p-task-card__missed-badge\">\n              当前轮次缺{{ getCurrentRoundMissedPoints() }}张打卡\n            </text>\n          </view>\n        </view>\n        \n        <!-- 等待开始状态(包括未开始和进行中但轮次未开始) -->\n        <view v-else class=\"p-task-card__other-status\">\n          <view class=\"p-task-card__status-row\">\n            <text class=\"p-task-card__round-badge\" :class=\"{'p-task-card__round-badge--waiting': true}\">\n              等待第{{ getNextRoundNumber() }}轮开始\n            </text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 轮次信息与进度条之间的分隔线 -->\n    <view v-if=\"hasRounds\" class=\"p-task-card__divider\"></view>\n    \n    <!-- 进度条 - 更直观的视觉设计 -->\n    <view class=\"p-task-card__progress-container\">\n      <view class=\"p-task-card__progress-header\">\n        <text class=\"p-task-card__progress-label\">巡视进度</text>\n        <text class=\"p-task-card__progress-text\">\n          {{ formatProgress(progressRate) }}\n        </text>\n      </view>\n      \n      <view class=\"p-task-card__progress-bar\">\n        <view \n          class=\"p-task-card__progress-inner\" \n          :class=\"{'p-task-card__progress-inner--completed': isCompleted}\"\n          :style=\"{\n            width: progressRate * 100 + '%'\n          }\" \n        ></view>\n      </view>\n      \n      <view class=\"p-task-card__points-info\">\n        <text class=\"p-task-card__points-completed\">已巡视 {{ completedPoints }} 个点位</text>\n        <text class=\"p-task-card__points-total\">共 {{ totalPoints }} 个点位</text>\n      </view>\n    </view>\n    \n    <!-- 进度条与底部按钮之间的分隔线 -->\n    <view class=\"p-task-card__divider p-task-card__divider--bottom\"></view>\n    \n    <!-- 底部操作按钮 - 更醒目的主操作按钮 -->\n    <view class=\"p-task-card__actions\">\n      <view v-if=\"canContinue && canStartPatrol\" class=\"p-task-card__action-primary\">\n        <button \n          class=\"p-task-card__btn p-task-card__btn--primary\"\n          @click.stop=\"onContinueTask\"\n        >\n          <uni-icons type=\"checkmarkempty\" size=\"18\" color=\"#FFFFFF\"></uni-icons>\n          {{ getCurrentActiveRound() ? '继续巡检' : '开始巡检' }}\n        </button>\n      </view>\n      \n      <view v-else-if=\"canContinue && !canStartPatrol\" class=\"p-task-card__action-waiting\">\n        <text class=\"p-task-card__waiting-text\">等待开始时间</text>\n        <button \n          class=\"p-task-card__btn p-task-card__btn--disabled\"\n          disabled\n        >\n          {{ getCurrentActiveRound() ? '继续巡检' : '开始巡检' }}\n        </button>\n      </view>\n      \n      <view v-else-if=\"isExpired\" class=\"p-task-card__action-expired\">\n        <text class=\"p-task-card__expired-text\">班次已结束</text>\n        <button \n          class=\"p-task-card__btn p-task-card__btn--secondary\"\n          @click.stop=\"onViewDetail\"\n        >\n          查看详情\n        </button>\n      </view>\n      \n      <view v-else-if=\"isCompleted\" class=\"p-task-card__action-completed\">\n        <text class=\"p-task-card__completed-text\">任务已完成</text>\n        <button \n          class=\"p-task-card__btn p-task-card__btn--secondary\"\n          @click.stop=\"onViewDetail\"\n        >\n          查看详情\n        </button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\n/**\n * 任务卡片组件\n * 显示巡视任务信息和状态\n * 依赖父组件提供:\n * 1. 任务数据(task)\n * 2. 完整的用户映射(userMap)\n * 3. 可选的实时轮次信息(roundInfo) - 若不提供，组件将自行计算时间\n */\n// 导入日期工具函数\nimport { formatDate, calculateRoundTime, isToday, calculateEndTime } from '@/utils/date.js';\n\nexport default {\n  name: 'p-task-card',\n  props: {\n    task: {\n      type: Object,\n      required: true\n    },\n    userMap: {\n      type: Object,\n      default: () => ({})\n    },\n    shift: {\n      type: Object,\n      default: null\n    },\n    roundInfo: {\n      type: Object,\n      default: null\n    },\n    active: {\n      type: Boolean,\n      default: false\n    },\n    // 新增：当前选中的轮次\n    selectedRoundNumber: {\n      type: Number,\n      default: -1\n    }\n  },\n  data() {\n    return {\n      // 简化定时器\n      countdownTimer: null,\n      // 当前时间（用于计算倒计时）\n      currentTime: new Date(),\n      // 防抖：记录已经触发过刷新的轮次\n      refreshedRounds: new Set(),\n      // 缓存倒计时结果，减少闪烁\n      countdownCache: {},\n      // 🔥 新增：事件节流机制\n      eventThrottle: {},\n      lastEmitTime: {}\n    }\n  },\n  computed: {\n    // 状态样式类 - 直接使用任务状态，不再重新计算\n    statusClass() {\n      const status = this.task ? this.task.status : 0;\n      if (status === 0) return 'pending';\n      if (status === 1) return 'active';\n      if (status === 2) return 'completed';\n      if (status === 3) return 'expired';\n      if (status === 4) return 'canceled';\n      return 'pending';\n    },\n    \n    // 状态文本 - 直接使用任务状态，不再重新计算\n    statusText() {\n      const status = this.task ? this.task.status : 0;\n      if (status === 0) return '未开始';\n      if (status === 1) return '进行中';\n      if (status === 2) return '已完成';\n      if (status === 3) return '已超时';\n      if (status === 4) return '已取消';\n      return '未开始';\n    },\n    \n    // 是否处于激活状态 - 直接使用任务状态\n    isActive() {\n      return this.task && this.task.status === 1;\n    },\n    \n    // 是否已超时 - 直接使用任务状态\n    isExpired() {\n      return this.task && this.task.status === 3;\n    },\n    \n    // 是否已完成 - 直接使用任务状态\n    isCompleted() {\n      return this.task && this.task.status === 2;\n    },\n    \n    // 是否可以继续执行 - 简化逻辑\n    canContinue() {\n      return this.task && (this.task.status === 0 || this.task.status === 1);\n    },\n    \n    // 是否可以开始巡视 - 简化逻辑，依赖父组件传入的状态\n    canStartPatrol() {\n      if (!this.task) return false;\n      return this.task.status === 1 || this.task.status === 0;\n    },\n    \n    // 已完成点位数量\n    completedPoints() {\n      // 优先使用overall_stats\n      if (this.task && this.task.overall_stats && this.task.overall_stats.completed_points !== undefined) {\n        return this.task.overall_stats.completed_points;\n      }\n      return 0;\n    },\n    \n    // 总点位数量\n    totalPoints() {\n      // 优先使用overall_stats\n      if (this.task && this.task.overall_stats && this.task.overall_stats.total_points !== undefined) {\n        return this.task.overall_stats.total_points;\n      }\n      return 0;\n    },\n    \n    // 进度比例计算属性\n    progressRate() {\n      // 1. 优先使用overall_stats的completion_rate\n      if (this.task && this.task.overall_stats && this.task.overall_stats.completion_rate !== undefined) {\n        // 确保completion_rate是有效数值且在0-1范围内\n        const rate = parseFloat(this.task.overall_stats.completion_rate);\n        if (!isNaN(rate)) {\n          return rate > 1 ? rate / 100 : rate; // 处理可能的百分比值\n        }\n      }\n      \n      // 2. 其次使用task的completion_rate\n      if (this.task && this.task.completion_rate !== undefined) {\n        // 确保completion_rate是有效数值且在0-1范围内\n        const rate = parseFloat(this.task.completion_rate);\n        if (!isNaN(rate)) {\n          return rate > 1 ? rate / 100 : rate; // 处理可能的百分比值\n        }\n      }\n      \n      // 3. 最后计算比例，确保完成点位数大于0且总点位数大于0\n      if (this.completedPoints > 0 && this.totalPoints > 0) {\n        return this.completedPoints / this.totalPoints;\n      }\n      \n      return 0; // 默认返回0表示无进度\n    },\n    \n    // 是否为通宵/跨天班次\n    isAcrossDay() {\n      // 1. 首先检查task.shift_detail中的across_day属性\n      if (this.task && this.task.shift_detail && this.task.shift_detail.across_day === true) {\n        return true;\n      }\n      \n      // 2. 如果直接从shift中获取时间\n      if (this.shift && this.shift.start_time && this.shift.end_time) {\n        try {\n          // 如果开始时间大于结束时间，说明是跨天班次\n          const startTime = this.shift.start_time;\n          const endTime = this.shift.end_time;\n          \n          // 解析纯时间格式，例如 \"18:00\"\n          const parseTimeStr = (timeStr) => {\n            if (!timeStr || !timeStr.includes(':')) return 0;\n            const [hours, minutes] = timeStr.split(':').map(Number);\n            return hours * 60 + minutes; // 转换为分钟数便于比较\n          };\n          \n          const startMinutes = parseTimeStr(startTime);\n          const endMinutes = parseTimeStr(endTime);\n          \n          // 如果结束时间小于开始时间，通常是跨天的情况\n          return startMinutes > endMinutes;\n        } catch (e) {\n          return false;\n        }\n      }\n      \n      return false;\n    },\n    \n    // 是否有轮次\n    hasRounds() {\n      return this.task && this.task.rounds_detail && this.task.rounds_detail.length > 0;\n    },\n    \n    // 排序轮次计算属性 - 直接使用传入的轮次数据，不再重新排序\n    sortedRounds() {\n      if (!this.task || !this.task.rounds_detail || !Array.isArray(this.task.rounds_detail)) {\n        return [];\n      }\n      return this.task.rounds_detail;\n    },\n    \n    // 是否有活跃轮次 - 直接使用轮次状态\n    hasActiveRound() {\n      return this.task && \n             this.task.rounds_detail && \n             this.task.rounds_detail.some(round => round.status === 1);\n    }\n  },\n  methods: {\n    // 格式化进度\n    formatProgress(progress) {\n      // 确保progress是有效数字且在0-1之间\n      if (typeof progress !== 'number' || isNaN(progress)) {\n        progress = 0;\n      }\n      \n      // 如果已经是百分比形式(0-100)\n      if (progress > 1) {\n        return `${Math.min(100, Math.floor(progress))}%`;\n      }\n      \n      // 如果是小数形式(0-1)\n      return `${Math.floor(progress * 100)}%`;\n    },\n    \n    // 新增：处理轮次点击事件\n    onRoundClick(round, index) {\n      if (!round) return;\n      \n      // 向父组件发送select-round事件，传递轮次信息\n      this.$emit('select-round', {\n        round,\n        index,\n        taskId: this.task ? this.task._id : null\n      });\n    },\n    \n    // 新增：检查轮次是否被选中\n    isRoundSelected(round) {\n      if (!round) return false;\n      return this.selectedRoundNumber === round.round;\n    },\n    \n    // 获取轮次状态文本\n    getRoundStatusText(round) {\n      if (!round) return '未知';\n      \n      // 从轮次状态status获取\n      const statusMap = {\n        0: '未开始',\n        1: '进行中',\n        2: '已完成',\n        3: '已超时'\n      };\n      \n      return statusMap[round.status] || '未知';\n    },\n    \n    // 获取当前活跃轮次 - 简化逻辑\n    getCurrentActiveRound() {\n      if (!this.task || !this.task.rounds_detail) return null;\n      return this.task.rounds_detail.find(round => round.status === 1);\n    },\n    \n    // 获取下一个轮次 - 简化逻辑\n    getNextRound() {\n      if (!this.task || !this.task.rounds_detail) return null;\n      return this.task.rounds_detail.find(round => round.status === 0);\n    },\n    \n    // 获取总缺卡点数\n    getTotalMissedPoints() {\n      // 从overall_stats获取缺卡点数\n      if (this.task && this.task.overall_stats && this.task.overall_stats.missed_points !== undefined) {\n        return this.task.overall_stats.missed_points;\n      }\n      \n      return 0;\n    },\n    \n    // 点击卡片\n    onCardClick() {\n      if (!this.task) return;\n      \n      // 点击卡片时触发select事件，传递任务ID\n      this.$emit('click', this.task);\n    },\n    \n    // 继续/开始巡检按钮点击事件\n    onContinueTask() {\n      if (!this.task) return;\n      \n      // 直接触发continue事件，让父组件处理轮次选择和状态判断\n      this.$emit('continue', {\n        task: this.task,\n        selectedRound: this.selectedRoundNumber !== -1 ? \n          this.task.rounds_detail.find(r => r.round === this.selectedRoundNumber) : \n          null\n      });\n    },\n    \n    // 查看详情按钮点击事件\n    onViewDetail() {\n      if (!this.task) return;\n      \n      this.$emit('view-detail', this.task);\n    },\n    \n    // 获取当前轮次编号\n    getCurrentRoundNumber() {\n      const currentRound = this.getCurrentActiveRound();\n      return currentRound ? currentRound.round || 1 : 1;\n    },\n    \n    // 获取任务状态文本\n    getTaskStatusText() {\n      if (!this.task) return '';\n      \n      // 任务状态: 0=未开始, 1=进行中, 2=已完成, 3=已超时\n      switch (this.task.status) {\n        case 0:\n          return '等待开始';\n        case 1:\n          return `进行第${this.getCurrentRoundNumber()}轮巡检`;\n        case 2:\n          return '已完成';\n        case 3:\n          return '已超时';\n        default:\n          return '未知状态';\n      }\n    },\n    \n    // 获取用户名\n    getUserName() {\n      if(!this.task) return '';\n      \n      // 仅从userMap获取用户名\n      if(this.userMap && this.task.user_id && this.userMap[this.task.user_id]) {\n        return this.userMap[this.task.user_id].name || this.userMap[this.task.user_id].username || '未知用户';\n      }\n      \n      return '未指定人员';\n    },\n    \n    // 获取轮次剩余时间（毫秒）\n    getRoundTimeRemaining(round) {\n      if (!round || round.status !== 1) return 0;\n      \n      const cacheKey = `remaining_${round.round}`;\n      const now = Date.now();\n      \n      // 使用缓存减少闪烁，每500ms更新一次\n      if (this.countdownCache[cacheKey] && \n          (now - this.countdownCache[cacheKey].timestamp) < 500) {\n        return this.countdownCache[cacheKey].value;\n      }\n      \n      let result = 0;\n      \n      try {\n        // 方式1：直接使用ISO格式的end_time（如果可用）\n        if (round.end_time) {\n          const endTime = this.parseISOTime(round.end_time);\n          if (endTime && this.currentTime < endTime) {\n            result = Math.max(0, endTime - this.currentTime);\n          }\n        }\n        // 方式2：使用工具函数从time和day_offset计算\n        else if (round.time && this.task) {\n          // 获取基准日期（优先使用任务日期，其次使用当天日期）\n          const baseDate = this.task.patrol_date || formatDate(new Date(), 'YYYY-MM-DD');\n          // 使用工具方法计算开始时间，考虑日期偏移\n          const startTime = calculateRoundTime(baseDate, round.time, round.day_offset || 0);\n          // 使用工具方法计算结束时间，考虑持续时间\n          const endTime = calculateEndTime(startTime, round.duration || 60);\n          \n          if (this.currentTime < endTime) {\n            result = Math.max(0, endTime - this.currentTime);\n          }\n        }\n      } catch (e) {\n        console.error('计算轮次剩余时间出错:', e, round);\n      }\n      \n      // 缓存结果\n      this.countdownCache[cacheKey] = {\n        value: result,\n        timestamp: now\n      };\n      \n      return result;\n    },\n    \n    // 获取轮次等待开始时间（毫秒）\n    getRoundTimeUntilStart(round) {\n      if (!round || round.status !== 0) return 0;\n      \n      const cacheKey = `waiting_${round.round}`;\n      const now = Date.now();\n      \n      // 使用缓存减少闪烁，每500ms更新一次\n      if (this.countdownCache[cacheKey] && \n          (now - this.countdownCache[cacheKey].timestamp) < 500) {\n        return this.countdownCache[cacheKey].value;\n      }\n      \n      let result = 0;\n      \n      try {\n        // 方式1：直接使用ISO格式的start_time（如果可用）\n        if (round.start_time) {\n          const startTime = this.parseISOTime(round.start_time);\n          if (startTime && this.currentTime < startTime) {\n            result = Math.max(0, startTime - this.currentTime);\n          }\n        }\n        // 方式2：使用工具函数从time和day_offset计算\n        else if (round.time && this.task) {\n          // 获取基准日期（优先使用任务日期，其次使用当天日期）\n          const baseDate = this.task.patrol_date || formatDate(new Date(), 'YYYY-MM-DD');\n          // 使用工具方法计算时间，考虑日期偏移\n          const startTime = calculateRoundTime(baseDate, round.time, round.day_offset || 0);\n          \n          if (this.currentTime < startTime) {\n            result = Math.max(0, startTime - this.currentTime);\n          }\n        }\n      } catch (e) {\n        console.error('计算轮次等待时间出错:', e, round);\n      }\n      \n      // 缓存结果\n      this.countdownCache[cacheKey] = {\n        value: result,\n        timestamp: now\n      };\n      \n      return result;\n    },\n    \n    // 解析ISO时间字符串\n    parseISOTime(isoString) {\n      if (!isoString) return null;\n      \n      try {\n        // 手动解析ISO格式时间字符串，如 \"2025-05-27T12:00:00.000Z\"\n        const match = isoString.match(/^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2})(?:\\.(\\d{3}))?Z?$/);\n        if (match) {\n          const [, year, month, day, hour, minute, second, millisecond] = match;\n          // 注意：JavaScript的月份是从0开始的\n          return new Date(Date.UTC(\n            parseInt(year),\n            parseInt(month) - 1, // 月份需要减1\n            parseInt(day),\n            parseInt(hour),\n            parseInt(minute),\n            parseInt(second),\n            parseInt(millisecond || 0)\n          ));\n        }\n        \n        // 如果正则匹配失败，尝试直接解析\n        const date = new Date(isoString);\n        return isNaN(date.getTime()) ? null : date;\n      } catch (e) {\n        console.error('解析ISO时间出错:', e, isoString);\n        return null;\n      }\n    },\n\n    // 格式化倒计时显示\n    formatSimpleCountdown(milliseconds) {\n      if (!milliseconds || milliseconds <= 0) return '0秒';\n      \n      try {\n        const totalSeconds = Math.floor(milliseconds / 1000);\n        const hours = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor((totalSeconds % 3600) / 60);\n        const seconds = totalSeconds % 60;\n        \n        // 轮次时间倒计时显示\n        if (totalSeconds < 60) {\n          // 小于1分钟，显示秒数\n          return `${seconds}秒`;\n        } else if (totalSeconds < 3600) {\n          // 小于1小时，显示分钟和秒数\n          return `${minutes}分${seconds}秒`;\n        } else {\n          // 大于等于1小时，显示小时和分钟\n          return `${hours}小时${minutes}分钟`;\n        }\n      } catch (e) {\n        console.error('格式化倒计时出错:', e);\n        return '0秒';\n      }\n    },\n    \n    // 获取当前轮次缺卡数 - 简化逻辑\n    getCurrentRoundMissedPoints() {\n      const currentRound = this.getCurrentActiveRound();\n      if (!currentRound || !currentRound.stats) return 0;\n      return currentRound.stats.missed_points || 0;\n    },\n    \n    // 检查轮次是否有真实的打卡数据\n    hasRealCheckInData() {\n      if (!this.task) return false;\n      \n      // 如果任务状态是已完成且有进度指标，那么可能已有数据\n      if (this.task.status === 2 && this.progressRate > 0) {\n        return true;\n      }\n      \n      // 检查current_round_records是否有真实数据\n      if (this.task.current_round_records && Array.isArray(this.task.current_round_records)) {\n        // 过滤有效记录 - 必须有check_time\n        const validRecords = this.task.current_round_records.filter(record => \n          record && typeof record === 'object' && record.check_time\n        );\n        return validRecords.length > 0;\n      }\n      \n      // 检查所有轮次是否有打卡记录\n      if (this.task.rounds_detail && Array.isArray(this.task.rounds_detail)) {\n        for (const round of this.task.rounds_detail) {\n          if (round && round.stats) {\n            // 只有当completed_points大于0时才认为有真实打卡\n            if (round.stats.completed_points > 0) {\n              return true;\n            }\n          }\n        }\n      }\n      \n      // 没有找到有效打卡记录\n      return false;\n    },\n    \n    // 添加判断点位是否完成的辅助方法\n    isPointCompleted(point, round) {\n      if (!point || !round) return false;\n      \n      const pointId = point.point_id || point._id;\n      if (!pointId) return false;\n      \n      // 获取轮次记录\n      const roundRecord = this.task.round_records?.find(r => r.round === round.round);\n      const completedPoints = roundRecord?.completed_points || [];\n      \n      // 检查完成状态 - 与index.vue保持一致的判断标准\n      const statusCompleted = point.status === 1 || point.status === 'completed';\n      const hasValidCheckIn = !!point.checkin_time;\n      const inCompletedArray = completedPoints.includes(pointId);\n      \n      return statusCompleted || hasValidCheckIn || inCompletedArray;\n    },\n    \n    getNextRoundNumber() {\n      const nextRound = this.getNextRound();\n      return nextRound ? nextRound.round : 1;\n    },\n\n    // 启动倒计时定时器\n    startCountdownTimer() {\n      // 先清除已有的定时器\n      this.stopCountdownTimer();\n      \n      // 每秒更新一次当前时间\n      this.countdownTimer = setInterval(() => {\n        this.currentTime = new Date();\n        \n        // 检查是否有倒计时结束，需要刷新数据\n        this.checkCountdownExpired();\n      }, 1000);\n    },\n\n    // 停止倒计时定时器\n    stopCountdownTimer() {\n      if (this.countdownTimer) {\n        clearInterval(this.countdownTimer);\n        this.countdownTimer = null;\n      }\n      // 清理缓存\n      this.countdownCache = {};\n    },\n\n    // 🔥 新增：节流发送事件，避免短时间内重复触发\n    throttledEmit(eventName, data, throttleTime = 5000) {\n      const now = Date.now();\n      const key = `${eventName}_${this.task._id}`;\n      \n      if (this.lastEmitTime[key] && (now - this.lastEmitTime[key]) < throttleTime) {\n        console.log(`事件${eventName}被节流，跳过重复触发`);\n        return;\n      }\n      \n      this.lastEmitTime[key] = now;\n      this.$emit(eventName, data);\n      \n      // 只在必要时发送全局事件\n      if (eventName === 'refresh-task' || eventName === 'countdown-expired') {\n        // 确保全局事件不重复\n        if (!this.eventThrottle[key]) {\n          this.eventThrottle[key] = true;\n          uni.$emit('refresh-task-list');\n          \n          setTimeout(() => {\n            this.eventThrottle[key] = false;\n          }, throttleTime);\n        }\n      }\n    },\n\n    // 检查倒计时是否结束，需要刷新数据\n    checkCountdownExpired() {\n      if (!this.task || !this.task.rounds_detail) return;\n      \n      let needRefresh = false;\n      let expiredRounds = [];\n      \n      this.task.rounds_detail.forEach(round => {\n        if (!round) return;\n        \n        const roundKey = `${this.task._id}_${round.round}`;\n        \n        // 检查进行中的轮次是否已超时\n        if (round.status === 1) {\n          const remaining = this.getRoundTimeRemaining(round);\n          if (remaining <= 0 && !this.refreshedRounds.has(roundKey + '_expired')) {\n            needRefresh = true;\n            expiredRounds.push(`轮次${round.round}已超时`);\n            this.refreshedRounds.add(roundKey + '_expired');\n          }\n        }\n        \n        // 检查未开始的轮次是否应该开始了\n        if (round.status === 0) {\n          const timeUntilStart = this.getRoundTimeUntilStart(round);\n          if (timeUntilStart <= 0 && !this.refreshedRounds.has(roundKey + '_start')) {\n            needRefresh = true;\n            expiredRounds.push(`轮次${round.round}应该开始`);\n            this.refreshedRounds.add(roundKey + '_start');\n          }\n        }\n      });\n      \n      // 如果需要刷新，通知父组件\n      if (needRefresh) {\n        console.log('倒计时结束，需要刷新任务:', this.task._id, expiredRounds);\n        \n        // 发送事件给父组件\n        this.throttledEmit('countdown-expired', {\n          taskId: this.task._id,\n          task: this.task,\n          expiredRounds: expiredRounds\n        });\n        \n        // 同时发送刷新事件（更通用的事件名）\n        this.throttledEmit('refresh-task', {\n          taskId: this.task._id,\n          reason: 'countdown-expired',\n          details: expiredRounds\n        });\n        \n        // 如果父组件没有监听事件，尝试其他方式通知刷新\n        this.$nextTick(() => {\n          // 通过全局事件总线通知\n          if (this.$root && this.$root.$emit) {\n            this.$root.$emit('task-countdown-expired', {\n              taskId: this.task._id,\n              expiredRounds: expiredRounds\n            });\n          }\n          \n          // 通过uni.$emit通知（如果是uni-app环境）\n          if (typeof uni !== 'undefined' && uni.$emit) {\n            uni.$emit('task-countdown-expired', {\n              taskId: this.task._id,\n              expiredRounds: expiredRounds\n            });\n            \n            // 发送父组件已经监听的刷新事件\n            uni.$emit('refresh-task-list');\n          }\n        });\n        \n        // 延迟一段时间后清除防抖记录，允许再次触发\n        setTimeout(() => {\n          expiredRounds.forEach(msg => {\n            const roundNum = msg.match(/轮次(\\d+)/)?.[1];\n            if (roundNum) {\n              const roundKey = `${this.task._id}_${roundNum}`;\n              this.refreshedRounds.delete(roundKey + '_expired');\n              this.refreshedRounds.delete(roundKey + '_start');\n            }\n          });\n        }, 30000); // 30秒后允许再次触发\n      }\n    }\n  },\n  mounted() {\n    // 启动倒计时定时器\n    this.startCountdownTimer();\n  },\n  beforeDestroy() {\n    // 清理定时器\n    this.stopCountdownTimer();\n  },\n  // 添加页面生命周期管理\n  onShow() {\n    this.startCountdownTimer();\n  },\n\n  onHide() {\n    this.stopCountdownTimer();\n  }\n};\n</script>\n\n<style lang=\"scss\">\n.p-task-card {\n  position: relative;\n  background-color: #fff;\n  border-radius: 16rpx;\n  padding: 30rpx 24rpx 20rpx 24rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  overflow: hidden;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    left: 0;\n    top: 0;\n    bottom: 0;\n    width: 8rpx;\n    background-color: #ddd;\n  }\n  \n  &--active {\n    border-left: none;\n    box-shadow: 0 8rpx 24rpx rgba(22, 119, 255, 0.15);\n    \n    &::before {\n      background-color: #1677FF;\n    }\n  }\n  \n  &--selected {\n    background-color: inherit !important;\n    border: none !important;\n    box-shadow: 0 6rpx 20rpx rgba(22, 119, 255, 0.25) !important;\n    transform: translateY(-2rpx);\n  }\n  \n  // 已添加的active样式\n  &[active=\"true\"] {\n    border: none !important;\n    background-color: inherit !important;\n    box-shadow: 0 6rpx 20rpx rgba(22, 119, 255, 0.25) !important;\n    transform: translateY(-2rpx);\n  }\n  \n  &--expired {\n    border-left: none;\n    \n    &::before {\n      background-color: #F27D42;\n    }\n  }\n  \n  &--completed {\n    border-left: none;\n    \n    &::before {\n      background-color: #4CBBCE;\n    }\n  }\n\n  &__header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 24rpx;\n    padding: 0 10rpx;\n  }\n  \n  &__title-container {\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    flex: 1;\n    padding-left: 8rpx;\n  }\n  \n  &__title {\n    font-size: 34rpx;\n    font-weight: bold;\n    color: #333;\n    padding-right: 10rpx;\n  }\n  \n  &__quick-actions {\n    display: flex;\n    align-items: center;\n    margin-left: 10rpx;\n  }\n  \n  &__status {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 22rpx;\n    padding: 2rpx 10rpx;\n    height: 28rpx;\n    border-radius: 4rpx;\n    font-weight: 500;\n    white-space: nowrap;\n    line-height: 1;\n    margin-right: 0;\n    margin-left: 8rpx;\n    \n    &--pending {\n      background-color: #f2f2f2;\n      color: #666;\n    }\n    \n    &--active {\n      background-color: #e6f0ff;\n      color: #0055cc;\n    }\n    \n    &--completed {\n      background-color: #e6f9f7;\n      color: #0b9d89;\n    }\n    \n    &--expired {\n      background-color: #fdf1e6;\n      color: #d85614;\n    }\n    \n    &--canceled {\n      background-color: #f2f2f2;\n      color: #999;\n    }\n  }\n  \n  &__view-btn {\n    padding: 8rpx;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    background-color: transparent;\n    border: none;\n    border-radius: 50%;\n    margin-left: 8rpx;\n    width: 36rpx;\n    height: 36rpx;\n    transition: all 0.2s ease;\n    \n    &:active {\n      opacity: 0.7;\n    }\n  }\n  \n  &__info-group {\n    padding: 24rpx 30rpx;\n    background-color: #f8fafe;\n    margin-bottom: 20rpx;\n    border-radius: 12rpx;\n    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n    border: 1px solid rgba(22, 119, 255, 0.1);\n  }\n  \n  &__divider {\n    height: 2rpx;\n    background: linear-gradient(to right, rgba(22, 119, 255, 0.05), rgba(22, 119, 255, 0.3), rgba(22, 119, 255, 0.05));\n    margin: 10rpx 30rpx 30rpx;\n    border-radius: 2rpx;\n  }\n  \n  &__divider--bottom {\n    margin-bottom: 16rpx;\n    background: linear-gradient(to right, rgba(22, 119, 255, 0.02), rgba(22, 119, 255, 0.15), rgba(22, 119, 255, 0.02));\n  }\n  \n  &__info-row {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 20rpx;\n    \n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n  \n  &__info-item {\n    display: flex;\n    align-items: flex-start;\n    flex: 1;\n    max-width: 48%; // 确保两个item之间有间距\n  }\n  \n  &__info-icon {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    width: 48rpx;\n    height: 48rpx;\n    background-color: rgba(22, 119, 255, 0.1);\n    border-radius: 8rpx;\n    margin-right: 12rpx;\n  }\n  \n  &__info-content {\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n  }\n  \n  &__label {\n    font-size: 24rpx;\n    color: #666666;\n    margin-bottom: 4rpx;\n  }\n  \n  &__value {\n    font-size: 28rpx;\n    color: #222222;\n    font-weight: 600;\n    line-height: 1.3;\n    word-break: break-all;\n  }\n  \n  &__tag {\n    display: inline-block;\n    font-size: 20rpx;\n    background-color: #ff9500;\n    color: #fff;\n    padding: 0 8rpx;\n    border-radius: 6rpx;\n    margin-left: 8rpx;\n    font-weight: bold;\n    vertical-align: middle;\n    transform: translateY(-2rpx);\n  }\n  \n  &__round-info {\n    background-color: #f0f7ff;\n    border-radius: 12rpx;\n    padding: 16rpx;\n    margin-bottom: 24rpx;\n    border: 1px solid rgba(22, 119, 255, 0.1);\n    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n  }\n  \n  &__round-header {\n    display: flex;\n    align-items: center;\n    margin-bottom: 12rpx;\n  }\n  \n  &__round-title {\n    font-size: 28rpx;\n    font-weight: bold;\n    color: #1677FF;\n    margin-left: 10rpx;\n  }\n  \n  &__round-summary {\n    margin-bottom: 12rpx;\n  }\n  \n  &__round-summary-text {\n    font-size: 26rpx;\n    color: #666;\n  }\n  \n  &__round-current {\n    font-size: 26rpx;\n    color: #1677FF;\n    font-weight: bold;\n  }\n  \n  &__round-current--completed {\n    color: #4CBBCE;\n  }\n  \n  &__rounds-list {\n    margin-bottom: 12rpx;\n  }\n  \n  &__round-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 10rpx 12rpx;\n    border-radius: 8rpx;\n    background-color: #fff;\n    margin-bottom: 6rpx;\n    transition: all 0.3s;\n    cursor: pointer;\n    border-left: 4rpx solid transparent;\n    \n    /* 轮次项悬停效果 */\n    &:hover, &:active {\n      background-color: #f0f0f0;\n      border-left-color: #AAD6FF;\n    }\n    \n    /* 选中的轮次项样式 */\n    &--selected {\n      background-color: #e6f0ff !important;\n      border-left: 4rpx solid #1677FF !important;\n      box-shadow: 0 2rpx 10rpx rgba(22, 119, 255, 0.15);\n    }\n  }\n  \n  &__round-item-header {\n    display: flex;\n    align-items: center;\n    flex-wrap: wrap;\n    max-width: 65%;\n  }\n  \n  &__round-number {\n    font-size: 26rpx;\n    color: #333;\n    font-weight: bold;\n  }\n  \n  &__round-number-missed {\n    font-size: 24rpx;\n    color: #e53935;\n    margin-left: 8rpx;\n    font-weight: 500;\n    background-color: rgba(255, 255, 255, 0.7);\n    padding: 0 6rpx;\n    border-radius: 4rpx;\n  }\n  \n  &__round-status-badge {\n    display: inline-block;\n    font-size: 22rpx;\n    padding: 2rpx 12rpx;\n    border-radius: 16rpx;\n    font-weight: 500;\n    background-color: #e6e6e6;\n    color: #666;\n    margin-left: 8rpx;\n    \n    &--active {\n      background-color: #e6f0ff;\n      color: #0055cc;\n      font-weight: bold;\n    }\n    \n    &--completed {\n      background-color: #e6f9f7;\n      color: #0b9d89;\n      font-weight: bold;\n    }\n    \n    &--expired {\n      background-color: #fdf1e6;\n      color: #d85614;\n      font-weight: bold;\n    }\n    \n    &--waiting {\n      background-color: #f0f7ff;\n      color: #0055cc;\n      font-weight: bold;\n    }\n  }\n  \n  &__round-item-time {\n    margin-left: 12rpx;\n    display: flex;\n    align-items: center;\n    \n    text {\n      font-size: 24rpx;\n      color: #444;\n      font-weight: 500;\n      background-color: rgba(255, 255, 255, 0.7);\n      padding: 2rpx 8rpx;\n      border-radius: 4rpx;\n      margin-left: 4rpx;\n    }\n  }\n  \n    &__round-countdown {\n    display: flex;\n    align-items: center;\n    margin-left: 10rpx;\n    font-size: 24rpx;\n    color: #333; // 使用默认的深灰色，更清晰易读\n  }\n  \n  &__round-status {\n    margin-top: 12rpx;\n  }\n  \n  &__active-task,\n  &__completed-rounds,\n  &__other-status {\n    padding: 8rpx;\n    border-radius: 8rpx;\n    background-color: rgba(246, 250, 255, 0.8); // 浅色背景，提高可读性\n  }\n  \n  &__status-row {\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    justify-content: space-between;\n  }\n  \n  &__missed-badge {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 22rpx;\n    height: 28rpx;\n    background-color: #ffeeed;\n    color: #f5222d;\n    border-radius: 4rpx;\n    padding: 2rpx 12rpx;\n    margin-left: 8rpx;\n    white-space: nowrap;\n  }\n  \n  &__round-badge {\n    display: inline-block;\n    font-size: 24rpx;\n    padding: 4rpx 16rpx;\n    border-radius: 20rpx;\n    font-weight: 500;\n    background-color: #e6e6e6;\n    color: #666;\n    \n    &--waiting {\n      background-color: #e6f0ff; // 略微调亮背景\n      color: #0055cc; // 更深的蓝色，增加对比度\n      font-weight: bold; // 加粗\n    }\n    \n    &--active {\n      background-color: #e6f0ff; // 略微调亮背景\n      color: #0055cc; // 更深的蓝色，增加对比度\n      font-weight: bold; // 加粗\n    }\n    \n    &--completed {\n      background-color: #e6f9f7;\n      color: #0b9d89; // 更深的绿色，增加对比度\n      font-weight: bold; // 加粗\n    }\n    \n    &--expired {\n      background-color: #fdf1e6;\n      color: #d85614; // 更深的橙色，增加对比度\n      font-weight: bold; // 加粗\n    }\n  }\n  \n  &__progress-container {\n    margin-bottom: 20rpx;\n    background-color: #f8fafe;\n    border-radius: 12rpx;\n    padding: 16rpx;\n    border: 1px solid rgba(22, 119, 255, 0.1);\n    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n  }\n  \n  &__progress-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 8rpx;\n  }\n  \n  &__progress-label {\n    font-size: 26rpx;\n    color: #333;\n    font-weight: bold;\n  }\n  \n  &__progress-text {\n    font-size: 26rpx;\n    color: #1677FF;\n    font-weight: bold;\n  }\n  \n  &__progress-bar {\n    height: 16rpx;\n    background-color: #f2f2f2;\n    border-radius: 8rpx;\n    overflow: hidden;\n    margin-bottom: 8rpx;\n  }\n  \n  &__progress-inner {\n    height: 100%;\n    background-color: #1677FF;\n    border-radius: 8rpx;\n    transition: width 0.3s;\n    \n    &--completed {\n      background-color: #4CBBCE;\n    }\n  }\n  \n  &__points-info {\n    display: flex;\n    justify-content: space-between;\n    font-size: 24rpx;\n    color: #666;\n  }\n  \n  &__points-completed {\n    color: #1677FF;\n  }\n  \n  &__actions {\n    display: flex;\n    justify-content: center;\n  }\n  \n  &__action-primary,\n  &__action-expired,\n  &__action-completed {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 100%;\n  }\n  \n  &__action-expired,\n  &__action-completed {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 10rpx 20rpx;\n    background-color: #f9f9f9;\n    border-radius: 12rpx;\n    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n  }\n  \n  &__action-waiting {\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    justify-content: space-between;\n    width: 100%;\n    padding: 10rpx 20rpx;\n    background-color: #f9f9f9;\n    border-radius: 12rpx;\n    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n  }\n  \n  &__expired-text,\n  &__completed-text {\n    font-size: 28rpx;\n    color: #555;\n    font-weight: 500;\n    background: linear-gradient(to right, #ff9c5a, #ff7b2e);\n    background-clip: text;\n    -webkit-background-clip: text;\n    color: transparent;\n    padding: 6rpx 0;\n    flex: 1; /* 添加flex: 1使文本占据左侧空间 */\n  }\n  \n  &__waiting-text {\n    font-size: 28rpx;\n    color: #555;\n    font-weight: 500;\n    background: linear-gradient(to right, #52a0ff, #1677FF);\n    background-clip: text;\n    -webkit-background-clip: text;\n    color: transparent;\n    padding: 6rpx 0;\n    flex: 1; /* 添加flex: 1使文本占据左侧空间 */\n  }\n  \n  &__btn {\n    border: none;\n    border-radius: 8rpx;\n    font-size: 28rpx;\n    font-weight: 500;\n    padding: 0 40rpx;\n    height: 72rpx;\n    line-height: 72rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    \n    &--primary {\n      background-color: #1677FF;\n      color: #fff;\n      \n      uni-icons {\n        margin-right: 6rpx;\n      }\n    }\n    \n    &--secondary {\n      background-color: #f2f8ff;\n      color: #1677FF;\n      border: 1rpx solid #d9e8ff;\n      box-shadow: 0 4rpx 8rpx rgba(22, 119, 255, 0.1);\n      transition: all 0.3s ease;\n      margin-left: auto; /* 确保按钮靠最右侧 */\n      \n      &:active {\n        transform: translateY(2rpx);\n        box-shadow: 0 2rpx 4rpx rgba(22, 119, 255, 0.1);\n      }\n    }\n    \n    &--disabled {\n      background-color: #e9f0fa;\n      color: #a0b8d8;\n      border: 1rpx solid #d9e8ff;\n      cursor: not-allowed;\n      margin-left: auto; /* 确保按钮靠最右侧 */\n    }\n  }\n}\n\n\n\n@keyframes pulse {\n  0% {\n    transform: scale(1);\n    opacity: 1;\n  }\n  50% {\n    transform: scale(1.1);\n    opacity: 0.7;\n  }\n  100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n</style> ", "import mod from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./p-task-card.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./p-task-card.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755775844900\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}