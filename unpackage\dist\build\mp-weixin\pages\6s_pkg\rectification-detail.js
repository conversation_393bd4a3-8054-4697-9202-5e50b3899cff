require('./common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/6s_pkg/rectification-detail"],{"0969":function(e,t,a){"use strict";var n=a("7335"),r=a.n(n);r.a},6055:function(e,t,a){"use strict";a.r(t);var n=a("c52f"),r=a("cfcf");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("0969");var o=a("828b"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"5453c2e4",null,!1,n["a"],void 0);t["default"]=s.exports},7335:function(e,t,a){},b087:function(e,t,a){"use strict";(function(e,t){var n=a("47a9");a("357b"),a("861b");n(a("3240"));var r=n(a("6055"));e.__webpack_require_UNI_MP_PLUGIN__=a,t(r.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},c52f:function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return n}));var n={uniIcons:function(){return Promise.all([a.e("common/vendor"),a.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(a.bind(null,"6ddf"))}},r=function(){var e=this,t=e.$createElement,a=(e._self._c,e.loading||e.loadError?null:e.formatDateTimeOptimized(e.taskInfo.issueFoundDate)),n=!e.loading&&!e.loadError&&e.computedData.isCompleted&&e.taskInfo.completedDate?e.formatDateTimeOptimized(e.taskInfo.completedDate):null,r=e.loading||e.loadError||e.computedData.isCompleted&&e.taskInfo.completedDate||e.computedData.isCompleted||!e.taskInfo.deadline?null:e.formatDateTimeOptimized(e.taskInfo.deadline),i=e.loading||e.loadError||"in_progress"!==e.taskInfo.status||!e.taskInfo.startDate?null:e.formatDateTimeOptimized(e.taskInfo.startDate),o=e.loading||e.loadError?null:e.taskInfo.photos&&e.taskInfo.photos.length>0,s=e.loading||e.loadError||!o?null:e.taskInfo.photos.length,c=e.loading||e.loadError?null:e.taskInfo.completionPhotos&&e.taskInfo.completionPhotos.length>0,d=e.loading||e.loadError||!c?null:e.taskInfo.completionPhotos.length,u=e.loading||e.loadError||!e.taskInfo.reviewResult&&!e.taskInfo.reviewComments||!e.taskInfo.reviewDate?null:e.formatDateTimeOptimized(e.taskInfo.reviewDate),l=e.loading||e.loadError||!e.taskInfo.reviewResult&&!e.taskInfo.reviewComments?null:e.taskInfo.reviewPhotos&&e.taskInfo.reviewPhotos.length>0,p=e.loading||e.loadError||!e.taskInfo.reviewResult&&!e.taskInfo.reviewComments||!l?null:e.taskInfo.reviewPhotos.length;e.$mp.data=Object.assign({},{$root:{m0:a,m1:n,m2:r,m3:i,g0:o,g1:s,g2:c,g3:d,m4:u,g4:l,g5:p}})},i=[]},c9a8:function(e,t,a){"use strict";(function(e){var n=a("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("7eb4")),i=n(a("3b2d")),o=n(a("ee10")),s=a("882c"),c={name:"RectificationDetail",data:function(){return{taskInfo:{},loading:!1,loadError:"",taskId:"",processCache:null,dataLoaded:!1,computedData:{isCompleted:!1,statusText:"",categoryText:"",reviewResultText:""}}},onLoad:function(t){this.taskId=t.taskId,this.loadTaskDetail(t.taskId),e.$on("rectificationRecordUpdated",this.handleRecordUpdated)},onUnload:function(){e.$off("rectificationRecordUpdated",this.handleRecordUpdated)},methods:{initProcessCache:function(){this.processCache||(this.processCache={statusMap:{pending:"待整改",pending_rectification:"待整改",pending_review:"待复查",completed:"已完成",overdue:"已逾期",in_progress:"整改中",rejected:"整改不达标",verified:"整改合格"},categoryMap:{cleanliness:"清洁问题",safety:"安全问题",equipment:"设备问题",environment:"环境问题",organization:"整理问题",standardization:"标识问题",other:"其他问题"},reviewResultMap:{approved:"复查通过",rejected:"需重新整改",needs_improvement:"需改进"},completedStatuses:["completed","verified","approved"],dateFormatter:this.createOptimizedDateFormatter()})},createOptimizedDateFormatter:function(){return function(e){if(!e)return"--";try{var t;return t="string"===typeof e?e.includes("T")||e.includes("Z")?new Date(e):new Date(e.replace(/-/g,"/")):new Date(e),isNaN(t.getTime())?"--":"".concat(t.getMonth()+1,"月").concat(t.getDate(),"日 ").concat(t.getHours().toString().padStart(2,"0"),":").concat(t.getMinutes().toString().padStart(2,"0"))}catch(a){return"--"}}},loadTaskDetailOptimized:function(e){var t=this;return(0,o.default)(r.default.mark((function a(){var n,i;return r.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e){a.next=3;break}return t.loadError="整改任务ID不能为空",a.abrupt("return");case 3:return t.loading=!0,t.loadError="",a.prev=5,t.initProcessCache(),a.next=9,(0,s.callCloudFunction)("hygiene-rectification",{action:"getRectificationDetail",data:{id:e}});case 9:if(n=a.sent,!(n&&n.success&&n.data)){a.next=17;break}i=n.data,t.taskInfo=t.processTaskDataOptimized(i),t.precomputeTaskStates(),t.dataLoaded=!0,a.next=18;break;case 17:throw new Error((null===n||void 0===n?void 0:n.message)||"获取整改任务详情失败");case 18:a.next=23;break;case 20:a.prev=20,a.t0=a["catch"](5),t.loadError=a.t0.message||"加载失败，请稍后重试";case 23:return a.prev=23,t.loading=!1,a.finish(23);case 26:case"end":return a.stop()}}),a,null,[[5,20,23,26]])})))()},processTaskDataOptimized:function(e){this.processCache.dateFormatter;return{id:e._id||e.id,area:e.area_name||"未知责任区",areaName:e.area_name||"未知责任区",isPublic:"public"===e.area_type,status:e.status||"pending",problemDescription:e.issue_description||e.description||"无问题描述",issueFoundDate:e.created_at||e.issue_found_date,inspector:e.inspector_name||e.issue&&e.issue.inspector_name||e.created_by_name||e.assigned_by_name||"未知检查员",rectificationDescription:e.rectification_description||e.completion_description||"",photos:this.processPhotos(e.photos||[]),completionPhotos:this.processPhotos(e.completion_photos||[]),completedDate:e.completed_at,rectificationSubmitTime:e.submitted_at||e.updated_at,assignee_name:e.assigned_to_name||e.cleaner_name||e.area_assignee_name||"未分配",category:e.category||"",deadline:e.deadline,startDate:e.start_date,reviewResult:e.review_result||"",reviewComments:e.review_comments||"",reviewDate:e.review_date,reviewerName:e.reviewer_name||"",reviewPhotos:this.processPhotos(e.review_photos||[])}},processPhotos:function(e){return Array.isArray(e)?e.map((function(e){return"string"===typeof e?{url:e}:e&&"object"===(0,i.default)(e)?{url:e.url||e}:{url:""}})).filter((function(e){return e.url})):[]},precomputeTaskStates:function(){var e=this.processCache;this.computedData={isCompleted:e.completedStatuses.includes(this.taskInfo.status),statusText:e.statusMap[this.taskInfo.status]||"未知状态",categoryText:e.categoryMap[this.taskInfo.category]||this.taskInfo.category||"其他",reviewResultText:e.reviewResultMap[this.taskInfo.reviewResult]||this.taskInfo.reviewResult}},loadTaskDetail:function(e){var t=this;return(0,o.default)(r.default.mark((function a(){return r.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.abrupt("return",t.loadTaskDetailOptimized(e));case 1:case"end":return a.stop()}}),a)})))()},loadTaskDetailOriginal:function(e){var t=this;return(0,o.default)(r.default.mark((function a(){var n,i;return r.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e){a.next=3;break}return t.loadError="整改任务ID不能为空",a.abrupt("return");case 3:return t.loading=!0,t.loadError="",a.prev=5,a.next=8,(0,s.callCloudFunction)("hygiene-rectification",{action:"getRectificationDetail",data:{id:e}});case 8:if(n=a.sent,!(n&&n.success&&n.data)){a.next=14;break}i=n.data,t.taskInfo={id:i._id||i.id,area:i.area_name||"未知责任区",areaName:i.area_name||"未知责任区",isPublic:"public"===i.area_type,status:i.status||"pending",problemDescription:i.issue_description||i.description||"无问题描述",issueFoundDate:i.created_at||i.issue_found_date,inspector:i.inspector_name||i.issue&&i.issue.inspector_name||i.created_by_name||i.assigned_by_name||"未知检查员",rectificationDescription:i.rectification_description||i.completion_description||"",photos:i.photos||[],completionPhotos:i.completion_photos||[],completedDate:i.completed_at,rectificationSubmitTime:i.submitted_at||i.updated_at,assignee_name:i.assigned_to_name||i.cleaner_name||i.area_assignee_name||"未分配",category:i.category||"",deadline:i.deadline,startDate:i.start_date,reviewResult:i.review_result||"",reviewComments:i.review_comments||"",reviewDate:i.review_date,reviewerName:i.reviewer_name||""},a.next=15;break;case 14:throw new Error((null===n||void 0===n?void 0:n.message)||"获取整改任务详情失败");case 15:a.next=20;break;case 17:a.prev=17,a.t0=a["catch"](5),t.loadError=a.t0.message||"加载失败，请稍后重试";case 20:return a.prev=20,t.loading=!1,a.finish(20);case 23:case"end":return a.stop()}}),a,null,[[5,17,20,23]])})))()},retryLoad:function(){this.loadTaskDetail(this.taskId)},formatDateTimeOptimized:function(e){return this.processCache&&this.processCache.dateFormatter?this.processCache.dateFormatter(e):this.formatDateTime(e)},formatDateTime:function(e){if(!e)return"--";try{var t;return t="string"===typeof e?e.includes("T")||e.includes("Z")?new Date(e):new Date(e.replace(/-/g,"/")):new Date(e),isNaN(t.getTime())?"--":"".concat(t.getMonth()+1,"月").concat(t.getDate(),"日 ").concat(t.getHours().toString().padStart(2,"0"),":").concat(t.getMinutes().toString().padStart(2,"0"))}catch(a){return"--"}},getStatusText:function(e){if(this.processCache&&this.processCache.statusMap)return this.processCache.statusMap[e]||"未知状态";return{pending:"待整改",pending_rectification:"待整改",pending_review:"待复查",completed:"已完成",overdue:"已逾期",in_progress:"整改中",rejected:"整改不达标",verified:"整改合格"}[e]||"未知状态"},getCategoryText:function(e){if(this.processCache&&this.processCache.categoryMap)return this.processCache.categoryMap[e]||e||"其他";return{cleanliness:"清洁问题",safety:"安全问题",equipment:"设备问题",environment:"环境问题",organization:"整理问题",standardization:"标识问题",other:"其他问题"}[e]||e||"其他"},isTaskCompleted:function(){if(this.computedData&&"boolean"===typeof this.computedData.isCompleted)return this.computedData.isCompleted;return["completed","verified","approved"].includes(this.taskInfo.status)},getReviewResultText:function(e){if(this.processCache&&this.processCache.reviewResultMap)return this.processCache.reviewResultMap[e]||e;return{approved:"复查通过",rejected:"需重新整改",needs_improvement:"需改进"}[e]||e},previewPhoto:function(t){var a,n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"problem";if("problem"===r)a=this.taskInfo.photos||[];else if("completion"===r)a=this.taskInfo.completionPhotos||[];else{if("review"!==r)return;a=this.taskInfo.reviewPhotos||[]}a._cachedUrls?n=a._cachedUrls:(n=a.map((function(e){return e.url||e})).filter((function(e){return e})),a._cachedUrls=n),0!==n.length&&e.previewImage({urls:n,current:Math.min(t,n.length-1)})},goBack:function(){e.navigateBack()},handleRecordUpdated:function(e){e.taskId===this.taskId&&this.loadTaskDetail(this.taskId)}}};t.default=c}).call(this,a("df3c")["default"])},cfcf:function(e,t,a){"use strict";a.r(t);var n=a("c9a8"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a}},[["b087","common/runtime","common/vendor"]]]);